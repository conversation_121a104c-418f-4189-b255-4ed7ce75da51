#!/bin/bash
set -xeo pipefail

. ${HOME}/.nix-profile/etc/profile.d/nix.sh
[[ -s "/Users/<USER>/.rvm/scripts/rvm" ]] && source "/Users/<USER>/.rvm/scripts/rvm"
export PATH="${PATH:+${PATH}:}/usr/local/bin:/opt/homebrew/bin"
cd $DESTINATION_PATH
bundle_path="$(/usr/bin/which bundle)"
$bundle_path _1.17.3_ install

##############################
###  Install nix packages  ###
##############################

start_time_nix_install="$(bro metrics time-now)"
echo $start_time_nix_install > /tmp/start_time_nix_install

cp packages-${IOS_PLATFORM_VERSION}.nix default.nix
bro packages install 2>&1 | tee -a /var/log/browserstack/nix_packages_installer.log

nix_install_time=`bro metrics time-since ${start_time_nix_install}`
echo $nix_install_time > /tmp/nix_install_time

bro metrics push \
    --kind nix_install_stats \
    --category ios \
    --os $OS_NAME \
    --os-version $OS_VERSION \
    --environment production \
    --region $SUB_REGION --data success:true installTime:$nix_install_time release:$RELEASE
    # TODO add error and session_id

if [ ! -e $PRIVOXY_BLOCKING_PATH ] ; then
    touch $PRIVOXY_BLOCKING_PATH
fi

echo "{\"tag\": \"$RELEASE\"}" > "$DESTINATION_PATH/.browserstack_build_version.json"
