#!/bin/bash
set -xeo pipefail

. ${HOME}/.nix-profile/etc/profile.d/nix.sh
[[ -s "/Users/<USER>/.rvm/scripts/rvm" ]] && source "/Users/<USER>/.rvm/scripts/rvm"

if [ $CODE_UPDATE_STATUS -eq 0 ]; then
  allocation_success_status="true"
  error_type="none"
else
  allocation_success_status="false"
  error_type="failure"
fi
allocation_run_time=`bro metrics time-since ${start_time}`

bro metrics push \
  --kind deploy-metrics \
  --category deploy-ios \
  --os $OS_NAME \
  --os-version $OS_VERSION \
  --environment $DEPLOY_ENV \
  --region $SUB_REGION \
  --error $error_type \
  --data success:$allocation_success_status install-time:$allocation_run_time release:$RELEASE

if [[ ! -f "/tmp/nix_install_time" ]]; then
  start_time_nix_install=`cat /tmp/start_time_nix_install`
  nix_install_time=`bro metrics time-since ${start_time_nix_install}`

  bro metrics push \
    --kind nix_install_stats \
    --category ios \
    --os $OS_NAME \
    --os-version $OS_VERSION \
    --environment $DEPLOY_ENV \
    --region $SUB_REGION \
    --error $error_type \
    --data success:false installTime:$nix_install_time release:$RELEASE
    # TODO add session_id
else
  rm /tmp/nix_install_time
fi
rm /tmp/start_time_nix_install
