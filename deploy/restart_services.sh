#!/bin/bash
set -xeo pipefail

[[ -s "/Users/<USER>/.rvm/scripts/rvm" ]] && source "/Users/<USER>/.rvm/scripts/rvm"

BROWSERSTACK_DIR=/usr/local/.browserstack
REPO=realmobile
STATE_FILES_DIR=/usr/local/.browserstack/state_files
bash /usr/local/.browserstack/bshelper.sh restart_servers

# restart file converter and uploader services
sudo launchctl kickstart -k system/ruby_image_uploader || true
sudo launchctl kickstart -k system/ruby_video_uploader || true
sudo launchctl kickstart -k system/ruby_file_converter || true
sudo launchctl kickstart -k system/ruby_other_files_uploader || true
sudo launchctl kickstart -k system/ruby_network_files_uploader || true
sudo launchctl kickstart -k system/ruby_screenshot_instrumentation || true


if [ "$IOS_PLATFORM_VERSION" = "ios-gen-6" ]; then
 mkdir -p $(dirname $INSECURE_WEBSOCKET_PROXY_PATH)
 touch $INSECURE_WEBSOCKET_PROXY_PATH
fi


mkdir -p "$STATE_FILES_DIR"

for device_id in $(bash /usr/local/.browserstack/bshelper.sh devices); do
    touch "$STATE_FILES_DIR/install_mdm_profiles_${device_id}"
done


mkdir -p $BROWSERSTACK_DIR/metrics/
echo "deploy_tag{component=\"$REPO\",tag=\"$RELEASE\"} 1" > $BROWSERSTACK_DIR/metrics/$REPO.prom.$$
mv $BROWSERSTACK_DIR/metrics/$REPO.prom.$$ $BROWSERSTACK_DIR/metrics/$REPO.prom
