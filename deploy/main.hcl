# Variables will be substituted:
#
#   [[RELEASE_TAG]] - a release tag chosen when the deploy submitted
#   [[ENVIRONMENT]] - environment where to deploy. Warning. It's here
#                     for compatibility only. Don't use it for new deploys
#   [[PARALLELS_PER_REGION]]
#   [[DEPLOY_INTERVAL]]

job "ios_platform" {
  type = "system"

  meta {
    release                  = "[[RELEASE_TAG]]"
    specific_machines_deploy = "[[SPECIFIC_MACHINES_DEPLOY]]"
    PARALLELS_PER_REGION     = "[[PARALLELS_PER_REGION]]"
    DEPLOY_INTERVAL          = "[[DEPLOY_INTERVAL]]"
  }

  constraint {
    attribute = "${meta.mobile-platform}"
    value     = "ios"
  }

  group "platform" {

    vault {
      policies = ["kv-all"]
    }

    # Saves the code update script to the machine
    task "code_update" {
      driver = "raw_exec"

      lifecycle {
        hook = "prestart"
      }

      env {
        DESTINATION_PATH      = "/usr/local/.browserstack/realmobile"
        PRIVOXY_BLOCKING_PATH = "/usr/local/.browserstack/privoxy/privoxy_domain_blocking.conf"
        GITHUB_KEY            = "${NOMAD_SECRETS_DIR}/github"
        GIT_SSH_COMMAND       = "$(nix-build --no-out-link '<nixpkgs>' -I nixpkgs=https://github.com/NixOS/nixpkgs/archive/nixos-24.05.tar.gz -A openssh)/bin/ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no -i $GITHUB_KEY -F /dev/null"
        IOS_PLATFORM_VERSION  = "${meta.ios-platform-version}"
        HOME                  = "/Users/<USER>"
        RELEASE               = "${NOMAD_META_release}"
        S3_BUCKET             = "bs-mobile"
        NIX_CONF_DIR          = "${NOMAD_TASK_DIR}"
        OS_NAME               = "${attr.os.name}"
        OS_VERSION            = "${attr.os.version}"
        ios_platform_version  = "${meta.ios-platform-version}"
        TEMP_BUILD_DIR        = "${NOMAD_TASK_DIR}"
        PARALLELS_PER_REGION  = "${NOMAD_META_PARALLELS_PER_REGION}"
        DEPLOY_INTERVAL       = "${NOMAD_META_DEPLOY_INTERVAL}"
        GITHUB_REPO           = "realmobile"
      }

      template {
        data        = <<-EOH
        {{ with secret "/kv/github/ssh-key" }}{{ .Data.data.value }}{{ end }}
        EOH
        destination = "secrets/github"
        perms       = "600"
      }

      template {
        data        = <<-EOH
        CONSUL_LOCK_TOKEN={{ with secret "/kv/consul-lock" }}{{ .Data.data.acl_token }}{{ end }}
        EOH
        destination = "secrets/consul_lock_acl_token.env"
        env         = true
      }

      template {
        data        = <<-EOH
        extra-substituters = http://caching-proxy-server.service.prod.mobile.browserstack.com:3128/package-storage
        require-sigs = false
        extra-platforms = x86_64-darwin aarch64-darwin
        EOH
        destination = "local/nix.conf"
      }

      template {
        data = <<-EOH
        #!/bin/bash
        set -xeo pipefail

        TASK_PATH=$PWD
        sudo chown app $GITHUB_KEY

        /usr/bin/perl -MPOSIX -e "sleep(${DEPLOY_INTERVAL})"

        # Add nix to environment
        . ${HOME}/.nix-profile/etc/profile.d/nix.sh

        export SUB_REGION=$NOMAD_DC
        export start_time="$(bro metrics time-now)"
        [[ -s "/Users/<USER>/.rvm/scripts/rvm" ]] && source "/Users/<USER>/.rvm/scripts/rvm"

        ###########################
        ### Push deploy metrics ###
        ###########################

        function log_metrics_on_exit {
          export CODE_UPDATE_STATUS=$?
          export DEPLOY_ENV=production
          bash $DESTINATION_PATH/deploy/push_metrics.sh
        }
        trap log_metrics_on_exit EXIT

        #######################
        ####   Pre Update  ####
        #######################

        cd $TEMP_BUILD_DIR
        if [ ! -d ${GITHUB_REPO} ]; then
            <NAME_EMAIL>:browserstack/${GITHUB_REPO}.git --depth 1 -b $RELEASE
        fi
        cd $GITHUB_REPO
        export PATH="$PATH:/usr/local/bin:/opt/homebrew/bin/"
        bundle_path="$(/usr/bin/which bundle)"
        $bundle_path _1.17.3_ install || { rm -rf /Users/<USER>/.rvm/gems/ruby-2.7.2/cache/bundler/git/{dwh_ruby,mobile-common,ruby-hoothoot}*; $bundle_path _1.17.3_ install; }
        bro packages build -v packages-${ios_platform_version}.nix 2>&1 | tee -a /var/log/browserstack/nix_packages_installer.log

        #######################
        ####  Code Update  ####
        #######################

        if [ ! -d "$DESTINATION_PATH" ]; then
            cd $(dirname $DESTINATION_PATH)
            <NAME_EMAIL>:browserstack/${GITHUB_REPO}.git --depth 1 -b $RELEASE
        fi

        cd $DESTINATION_PATH
        git reset --hard || rm .git/index
        git clean -f -d
        git remote update origin

        git fetch --tags -f
        git fetch origin 'refs/tags/*:refs/tags/*'
        git checkout tags/$RELEASE

        #######################*/
        ####  Post Update  ####*/
        #######################*/
        bash $DESTINATION_PATH/deploy/post_update.sh

        # need to set trap again as RVM removes traps
        # https://github.com/rvm/rvm/issues/3540
        trap log_metrics_on_exit EXIT

        ####################
        ####  Clean up  ####
        ####################

        rm $GITHUB_KEY
        EOH

        destination = "local/update.sh"
      }

      template {
        data        = "/usr/bin/sudo -E -u app /usr/local/bin/consul lock -verbose -child-exit-code -token=$CONSUL_LOCK_TOKEN -n $PARALLELS_PER_REGION ${NOMAD_JOB_NAME}_${PARALLELS_PER_REGION}_lock '/bin/bash local/update.sh'"
        destination = "local/code_update.sh"
      }

      config {
        command = "/bin/bash"
        args    = ["local/code_update.sh"]
      }

      restart {
        interval = "1h"
        attempts = 5
        delay    = "2m"
        mode     = "fail"
      }

    }

    # Dummy process used to allow for system type nomad job
    task "dormant" {
      driver = "raw_exec"

      env {
        HOME                                      = "/Users/<USER>"
        DESTINATION_PATH                          = "/usr/local/.browserstack/realmobile"
        IOS_PLATFORM_VERSION                      = "${meta.ios-platform-version}"
        RELEASE                                   = "${NOMAD_META_release}"
        STATIC_CONFIG_PATH                        = "/usr/local/.browserstack/config/static_conf.json"
        CONFIG_PATH                               = "/usr/local/.browserstack/realmobile/config/config.yml"
        KEYS_PATH                                 = "/usr/local/.browserstack/realmobile/keys/keys.yml"
        REALMOBILE_CERTIFICATES_PRIVATE_KEY_PATH  = "/Users/<USER>/.ssh/id_rsa_mobilegithub"
        PERCY_GCLOUD_JSON_PATH                    = "/usr/local/.browserstack/realmobile/keys/percy/gcloud.json"
        PERCY_GCLOUD_STAGING_JSON_PATH            = "/usr/local/.browserstack/realmobile/keys/percy/gcloud_staging.json"
        SPECIFIC_MACHINES_DEPLOY                  = "${NOMAD_META_specific_machines_deploy}"
        PUBLIC_SIM_DEVICES_JSON_PATH              = "/usr/local/.browserstack/realmobile/config/custom_devices/public_sim_devices.json"
        PRIVATE_SIM_DEVICES_JSON_PATH             = "/usr/local/.browserstack/realmobile/config/custom_devices/private_sim_devices.json"
        HIGHER_MEMORY_DEVICES_JSON_PATH           = "/usr/local/.browserstack/realmobile/config/custom_devices/high_memory_devices.json"
        APPLE_PAY_DEVICES_PATH                    = "/usr/local/.browserstack/realmobile/config/custom_devices/apple_pay_devices"
        DEDICATED_CLOUD_APPLE_PAY_DEVICES_PATH    = "/usr/local/.browserstack/realmobile/config/custom_devices/dedicated_cloud_apple_pay_devices"
        APPLE_PAY_SANDBOX_MAPPING_JSON_PATH       = "/usr/local/.browserstack/realmobile/config/custom_devices/apple_pay_sandbox_mapping.json"
        SANDBOX_CARDS_JSON_PATH                   = "/usr/local/.browserstack/realmobile/config/custom_devices/sandbox_cards.json"
        CSPT_ADMIN_AUTH_JSON_PATH                 = "/usr/local/.browserstack/realmobile/keys/cspt/admin_auth.json"
        VOICEOVER_DEVICES_JSON_PATH               = "/usr/local/.browserstack/realmobile/config/custom_devices/voiceover_devices.json"
        PROVISIONING_SERVER_AUTH_CONFIG_JSON_PATH = "/usr/local/.browserstack/realmobile/keys/provisioning_server/auth_config.json"
        FEATURE_FLAGS_PATH                        = "/usr/local/.browserstack/realmobile/config/feature_flags.json"
        INSECURE_WEBSOCKET_PROXY_PATH             = "/usr/local/.browserstack/realmobile/scripts/insecure_websocket_proxy/tmp/restart.txt"
        VPP_TOKEN_PATH                            = "/usr/local/.browserstack/realmobile/config/vpp_token"
      }

      template {
        source      = "/usr/local/.browserstack/realmobile/config/static_conf.json.template"
        destination = "local/static_conf.json"
      }

      template {
        source      = "/usr/local/.browserstack/realmobile/config/config-${IOS_PLATFORM_VERSION}.yml.template"
        destination = "local/config.yml"
      }

      template {
        source      = "/usr/local/.browserstack/realmobile/keys/keys.yml.template"
        destination = "local/keys.yml"
      }

      template {
        data        = <<-EOH
        {{ with secret "/kv/ssh-keys/github/deploy/realmobile-certificates" }}{{ .Data.data.private_key }}{{ end }}
        EOH
        destination = "local/id_rsa_mobilegithub"
      }

      template {
        source      = "/usr/local/.browserstack/realmobile/keys/percy/gcloud.json.template"
        destination = "local/gcloud.json"
      }

      template {
        source      = "/usr/local/.browserstack/realmobile/keys/cspt/admin_auth.json.template"
        destination = "local/admin_auth.json"
      }

      template {
        source      = "/usr/local/.browserstack/realmobile/config/vpp_token.template"
        destination = "local/vpp_token"
      }

      template {
        source      = "/usr/local/.browserstack/realmobile/keys/percy/gcloud.json.staging.template"
        destination = "local/gcloud_staging.json"
      }

      template {
        source      = "/usr/local/.browserstack/realmobile/keys/provisioning_server/auth_config.json.template"
        destination = "local/auth_config.json"
      }

      template {
        source      = "/usr/local/.browserstack/realmobile/config/custom_devices/public_sim_devices.json.template"
        destination = "local/public_sim_devices.json"
      }

      template {
        data        = "{{ key \"ios/private-sim-devices\" }}"
        destination = "local/private_sim_devices.json"
      }

      template {
        source      = "/usr/local/.browserstack/realmobile/config/custom_devices/apple_pay_devices.template"
        destination = "local/apple_pay_devices"
      }

      template {
        source      = "/usr/local/.browserstack/realmobile/config/custom_devices/sandbox_cards.json.template"
        destination = "local/sandbox_cards.json"
      }

      template {
        source      = "/usr/local/.browserstack/realmobile/config/custom_devices/apple_pay_sandbox_mapping.json.template"
        destination = "local/apple_pay_sandbox_mapping.json"
      }

      template {
        source      = "/usr/local/.browserstack/realmobile/config/custom_devices/high_memory_devices.json.template"
        destination = "local/high_memory_devices.json"
      }

      template {
        source      = "/usr/local/.browserstack/realmobile/config/custom_devices/voiceover_devices.json.template"
        destination = "local/voiceover_devices.json"
      }

      template {
        source      = "/usr/local/.browserstack/realmobile/config/feature_flags.json.template"
        destination = "local/feature_flags.json"
      }

      template {
        data        = "{{ key \"ios/dedicated-cloud-apple-pay-devices\" }}"
        destination = "local/dedicated_cloud_apple_pay_devices"
      }

      template {
        data        = <<-EOH
        #!/bin/bash
        set -xe

        grep -F "$RELEASE" $DESTINATION_PATH/.browserstack_build_version.json

        [[ -s "/Users/<USER>/.rvm/scripts/rvm" ]] && source "/Users/<USER>/.rvm/scripts/rvm"

        cat local/keys.yml > $KEYS_PATH
        cat local/config.yml > $CONFIG_PATH
        cat local/admin_auth.json > $CSPT_ADMIN_AUTH_JSON_PATH
        cat local/gcloud.json > $PERCY_GCLOUD_JSON_PATH
        cat local/admin_auth.json > $CSPT_ADMIN_AUTH_JSON_PATH
        cat local/vpp_token > $VPP_TOKEN_PATH
        cat local/auth_config.json > $PROVISIONING_SERVER_AUTH_CONFIG_JSON_PATH
        cat local/gcloud_staging.json > $PERCY_GCLOUD_STAGING_JSON_PATH
        cat local/public_sim_devices.json > $PUBLIC_SIM_DEVICES_JSON_PATH
        cat local/private_sim_devices.json > $PRIVATE_SIM_DEVICES_JSON_PATH
        cat local/apple_pay_devices > $APPLE_PAY_DEVICES_PATH
        cat local/dedicated_cloud_apple_pay_devices > $DEDICATED_CLOUD_APPLE_PAY_DEVICES_PATH
        cat local/sandbox_cards.json > $SANDBOX_CARDS_JSON_PATH
        cat local/voiceover_devices.json > $VOICEOVER_DEVICES_JSON_PATH
        cat local/high_memory_devices.json > $HIGHER_MEMORY_DEVICES_JSON_PATH
        cat local/apple_pay_sandbox_mapping.json > $APPLE_PAY_SANDBOX_MAPPING_JSON_PATH
        cat local/feature_flags.json > $FEATURE_FLAGS_PATH

        if ! cmp --silent $STATIC_CONFIG_PATH local/static_conf.json; then
          chmod 0755 $STATIC_CONFIG_PATH
          cat local/static_conf.json > $STATIC_CONFIG_PATH
          chmod 0440 $STATIC_CONFIG_PATH
        fi

        # Copy id_rsa_mobilegithub to .ssh directory and update permissions
        cat local/id_rsa_mobilegithub > $REALMOBILE_CERTIFICATES_PRIVATE_KEY_PATH
        sudo chown app:staff $REALMOBILE_CERTIFICATES_PRIVATE_KEY_PATH
        sudo chmod 600 $REALMOBILE_CERTIFICATES_PRIVATE_KEY_PATH

        ############################
        ####  Services restart  ####
        ############################

        bash $DESTINATION_PATH/deploy/restart_services.sh

        if [[ "$SPECIFIC_MACHINES_DEPLOY" != "true" ]]; then
          # Sleep 24 hours
          /usr/bin/perl -MPOSIX -e "sleep(86400)"
        fi
        EOH
        destination = "local/refresh.sh"
      }

      template {
        data        = "/usr/bin/sudo -E -u app /bin/bash local/refresh.sh"
        destination = "local/wait.sh"
      }

      config {
        command = "/bin/bash"
        args    = ["local/wait.sh"]
      }

      restart {
        attempts = 0
        mode     = fail
      }

      service {
        tags = ["http"]

        name = "ios-platform-server"

        port = "server"

        check {
          type     = "http"
          path     = "/"
          interval = "60s"
          timeout  = "5s"
        }
      }

      service {
        tags = ["http"]

        name = "ios-platform-cleanup-server"

        port = "cleanup_server"

        check {
          type     = "http"
          path     = "/"
          interval = "60s"
          timeout  = "5s"
        }
      }

      service {
        tags = ["http"]

        name = "ios-platform-proxy"

        port = "proxy"

        check {
          type     = "http"
          path     = "/"
          interval = "60s"
          timeout  = "5s"
        }
      }

      service {
        tags = ["http"]

        name = "ios-platform-devtools-proxy"

        port = "devtools_proxy"

        check {
          type     = "tcp"
          path     = "/"
          interval = "60s"
          timeout  = "5s"
        }
      }

      service {
        tags = ["launchd"]
        name = "ios-platform-services"

        check {
          type     = "script"
          name     = "ruby-file-converter"
          command  = "/bin/bash"
          args     = ["-c", "/usr/local/.browserstack/realmobile/deploy/health_checks/launchd_service.sh", "ruby_file_converter"]
          interval = "300s"
          timeout  = "10s"
        }

        check {
          type     = "script"
          name     = "ruby-image-uploader"
          command  = "/bin/bash"
          args     = ["-c", "/usr/local/.browserstack/realmobile/deploy/health_checks/launchd_service.sh", "ruby_image_uploader"]
          interval = "300s"
          timeout  = "10s"
        }


        check {
          type     = "script"
          name     = "ruby-video-uploader"
          command  = "/bin/bash"
          args     = ["-c", "/usr/local/.browserstack/realmobile/deploy/health_checks/launchd_service.sh", "ruby_video_uploader"]
          interval = "300s"
          timeout  = "10s"
        }

        check {
          type     = "script"
          name     = "ruby-other-files-uploader"
          command  = "/bin/bash"
          args     = ["/usr/local/.browserstack/realmobile/deploy/health_checks/launchd_service.sh", "ruby_other_files_uploader"]
          interval = "300s"
          timeout  = "10s"
        }

        check {
          type     = "script"
          name     = "ruby-network-files-uploader"
          command  = "/bin/bash"
          args     = ["/usr/local/.browserstack/realmobile/deploy/health_checks/launchd_service.sh", "ruby_network_files_uploader"]
          interval = "300s"
          timeout  = "10s"
        }

        check {
          type     = "script"
          name     = "ruby-screenshot-instrumentation"
          command  = "/bin/bash"
          args     = ["/usr/local/.browserstack/realmobile/deploy/health_checks/launchd_service.sh", "ruby_screenshot_instrumentation"]
          interval = "300s"
          timeout  = "10s"
        }
      }

      resources {
        cpu    = 20
        memory = 32

        network {
          port "server" {
            static = 45671
          }

          port "cleanup_server" {
            static = 45680
          }

          port "proxy" {
            static = 45691
          }

          port "devtools_proxy" {
            static = 443
          }
        }
      }
    }
  }
}
