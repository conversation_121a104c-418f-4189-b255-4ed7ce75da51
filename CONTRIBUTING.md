# Contributing

This document highlights the contributing process for this repository.

Index:
* [Contribution Process](#contribution-process)
* [Coding Guidelines](#coding-guidelines)
  * [New functionality to be unit tested](#all-new-functionality-should-be-unit-tested)
  * [Do not use Process.fork without exec](#prefer-not-to-use-fork-with-exec)
* [Directory Structure](#directory-structure)



<hr>


## Contribution Process
1. Create a new branch off of `master` with the format `<description>-<JIRA-ID>`, example "fix-device-check-AA-666"
1. Refer to the [coding guidelines](#coding-guidelines) when writing code. *Code without specs will not be considered ready for merge*
1. Open new PR and fill the information available in the PR template.
1. For product teams, add a label to you PR with the product. Ex: `app-automate`, `automate`
1. If the PR is not yet ready to review apply the label: `draft`
1. If applicable the branch/PR will undergo testing.
1. After testing, the PR should be reviewed by someone from the product team, i.e. if PR belongs to `app-automate` a review should be present by someone from the `app-automate team`
1. Request a review from browserstack/mobile-platform on Github. It will assign two members of the mobile team using Github code review assignment. The mobile team members assigned will try to review the PR within 48 hours. Frankly, the time needed for review depends on the size of the PR. *PR's should do just one change*, if your PR is doing multiple things (e.g. refactoring a database and adding a row, or adding a feature for a new phone and doing that code more modular), raise multiple PR's.
1. As mentioned in the PR template, After all the above are done, open a deploy ticket on the [Mobile Ops board] and add the `deploy` label to the JIRA ticket. We will assume you want, besides a review, to get it deployed. If this is not the case, make sure to warn us.

## Exceptions
1. For P0 issues, some of the steps from [process](#contribution-process) can be skipped, otherwise all things are strict requirements.


## All new functionality should be unit tested
If your code is doing something new you need to write rspecs for it. Even if you are modifying code that was already written and didn't have specs, you should add the specs.


## Do not use Process.fork without exec

* If using Process.fork or fork we should be using exec to not share any resources from the parent
* If not using exec inside the forked processes, the socket connection is held by the forked process and case cause packet drops and various issues
* for doing short lived async work you can use Thread.new, else long-asyc work should be done with fork+exec
* for more details check [MOBPL-1570]

``` ruby
    # This is the correct way to spawn processes
    def fork_process_exec
      pid = fork do
        log "Starting "
        exec("sleep 60")
        log "Sleep done"
      end

      Process.detach(pid)
    end

    # AVOID THIS
    def fork_process_system
      pid = fork do
        log "Starting "
        system("sleep 60")
        log "Sleep done"
      end

      Process.detach(pid)
    end

    get '/fork_exec' do
      fork_process_exec
    end

    get '/fork_system' do
      fork_process_system
    end
```

## Do not use lsof to kill processes, use ps aux
There’s a problem with lsof, `lsof -i $PORT` could also list the processes that are not actually using the given `$PORT` on our machine. These happened to be privoxy processes sometimes.
So, using `ps aux` would be a good idea.
<hr>

[MOBPL-1570](https://browserstack.atlassian.net/browse/MOBPL-1570)
[Mobile Ops board](https://browserstack.atlassian.net/secure/RapidBoard.jspa?rapidView=63)

## Working with MDM / CFGUTIL Profiles
### General Guidance
- When it comes to managing profiles, we have two categories of device, first which uses MDM for managing profiles and others which uses CFGUTIL.
- Any things related to profile (CRUD) should happen in `ConfigurationProfilesManager`, `ConfigurationProfilesGenerator` classes for all types of device.
- Changes in `lib/utils/mdm_profile_restrictions.rb` should be done instead in `lib/utils/configuration_profiles_generator.rb` - `MdmProfileRestrictions` is now deprecated.
- Don't use `IosMdmServiceClient` classes directly for installing, updating or removing the profiles, all this is now migrated to `ConfigurationProfilesManager`.
- Any changes done in `CheckDevice#check_mdm_settings` should also be ideally replicated in `ConfigurationProfilesEnforcer#enforce_configuration_profiles` ( Depends on type of change being done )
- Overall be cognizant of the fact that your changes should also work correctly on all types of devices ( CFGUTIL and MDM ).

### How to update profiles ?
Let's assume that you have made changes in `restrictions.erb` and now you want to update `restrictions.erb` on all the devices.
- Update `mdm_restrictions_profile_payload_id` in `config/constants.yml` to any value other than the current one ( Generally prefer to increment last and / or second last digits ).
- Increment `latest_version` for `:restrictions` inside `cfgutil_profiles_required` in `config/constants.yml`.
- After deployment, this will update restrictions profile on all the devices.
- Currently profile update happens in Device Check after every few intervals for MDM managed devices, and once a day in Cleanup for CFGUTIL Managed devices.

### References:
- [Workings of MDM and CFGUTIL profiles + Migration Strategy](https://browserstack.atlassian.net/wiki/spaces/ENG/pages/4102259601/iOS+MDM+to+CFGUTIL+Migration+strategy)
- [CFGUTIL Migration Release Plan](https://browserstack.atlassian.net/wiki/spaces/ENG/pages/3984364370/iOS+Migrating+configuration+profiles+to+cfgutil+from+MDM+Release+Plan)
- [Reducing MDM Usage](https://browserstack.atlassian.net/wiki/spaces/~pulkit/pages/3976856066/Reducing+MDM+usage)
