#!groovy
@Library('jen<PERSON>-library@master') _
pipeline {
    agent {
        kubernetes {
            yaml libraryResource('RealMobilePodSpec.yaml')
        }
    }
    options {
        timestamps ()
        timeout(time: 1, unit: 'HOURS')
    }
    stages {
        stage("Run Tests") {
            steps {
                container('mobile-slave') {
                    script {
                        sh 'cd /usr/local/.browserstack/realmobile && make'
                    }
                }
            }
        }
    }
}
