# To build docker image:
# docker build -t ruby-realmobile --build-arg ssh_prv_key="$(cat ~/.ssh/id_rsa)" . --no-cache
# To run tests:
# docker run -v $(pwd):/usr/local/.browserstack/realmobile  -a stdin -a stdout -i -t ruby-realmobile make
# the -v option only work if your project folder is not a linked folder
FROM ruby:2.7.2

###### ADD MOBILE RSA PRIVATE KEY TO THE MACHINE
ARG ssh_prv_key

# Authorize SSH Host
RUN mkdir -p /root/.ssh && \
    chmod 0700 /root/.ssh && \
    ssh-keyscan github.com > /root/.ssh/known_hosts

# Add the keys and set permissions
RUN echo "$ssh_prv_key" > /root/.ssh/id_rsa && \
    chmod 600 /root/.ssh/id_rsa

WORKDIR /usr/local/.browserstack/
RUN mkdir /usr/local/.browserstack/state_files
COPY Gemfile /tmp/Gemfile
COPY Gemfile.lock /tmp/Gemfile.lock
RUN gem install bundler:1.17.3
RUN bundle config force_ruby_platform true
COPY dependencies /tmp/dependencies
RUN bundle _1.17.3_ install --gemfile=/tmp/Gemfile

# The mobile-common repo is required and isn't installable as a gem.
# The path for importing stuff from it is hardcoded.
ARG CACHEBUST=1
RUN <NAME_EMAIL>:browserstack/mobile-common.git
RUN mkdir config
RUN echo '*******' > whatsmyip

COPY . realmobile
WORKDIR /usr/local/.browserstack/realmobile
COPY config/config.yml.sample config/config.yml
COPY config/config.json.sample ../config/config.json
COPY config/static_conf.json.sample ../config/static_conf.json
COPY keys/keys.yml.sample keys/keys.yml

CMD rspec
