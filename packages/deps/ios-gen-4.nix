{
  buildEnv
, hello
, callPackage
, darwin
, idevicebookmark
, idevicecontacts
, idevicecrashreport
, ideviceinstaller
, idevicelocation
, libirecovery
, libusbmuxd
, multitail
, uhubctl
, pymobiledevice3
}:

let
  npm_modules = callPackage ../npm_modules {};
  realmobileBin = buildEnv {
    name = "realmobile-binaries-only";
    paths = [ npm_modules.percy-cli ];
    pathsToLink = [ "/bin" ];
  };
in
[{
  package = buildEnv {
    name = "realmobile-libraries-headers-binaries";
    paths = [
      realmobileBin
      darwin.ios-deploy
      idevicebookmark
      idevicecontacts
      idevicecrashreport
      ideviceinstaller
      idevicelocation
      libirecovery
      libusbmuxd
      multitail
      uhubctl
      pymobiledevice3
    ];
  };
  destination = "/usr/local/.browserstack/realmobile/deps";
}]
