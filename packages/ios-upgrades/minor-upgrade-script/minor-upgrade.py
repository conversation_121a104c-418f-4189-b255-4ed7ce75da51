import requests
from socket import timeout
import os
import sys
import json

# Aliases
config_summary_cmd = 'ruby /usr/local/.browserstack/helpers/config_summary.rb'
devices_cmd = 'bash /usr/local/.browserstack/bshelper.sh devices'

# Global variables
rails_un = 'admin'
rails_pw = os.popen('cat /usr/local/.browserstack/config/static_conf.json | grep admin_terminals_pass').read()
rails_pw = rails_pw[rails_pw.rfind(':')+3:-3]
ipsw_files_dir = '/usr/local/.browserstack/ios-upgrades/firmware-files/'
logs_dir = '/var/log/browserstack/'


def fetch_available_ios_versions(device_type):

    """
      Returns available firmeware from ipsw.me API

      Args:
       - device_type: The model of the
         device to find firmware for
      Returns:
       - String of JSON data of available
         firmware if the model was found
       - Prints to screen and exits if it
         could not find the model
    """

    request_url = 'https://api.ipsw.me/v4/device/' + device_type + '?type=ipsw'
    try:
        ios_versions_request = requests.get(request_url, timeout=5)
        if ios_versions_request.status_code != requests.codes.ok:
            print('Error: The request to retrieve available iOS versions for', device_type, 'returned', ios_versions_request.status_code)
            sys.exit(2)
        else:
            return ios_versions_request.text
    except timeout:
        print('Error: The request to retrieve available iOS versions for', device_type,'timed out')
        sys.exit(2)


def validate_ios_version_availability(uuid, target_ios_version):

    """
      Checks the ipsw.me API to see if the requested
      iOS version is available for the device and if
      it is signed.

      Args:
       - uuid: The UUID of the device to
         upgrade. Used to get the device type
       - target_ios_version: The iOS version
         to check availability for given
         the device type
      Returns:
       - If the iOS version is found
         and is signed, nothing is returned.
       - If either the iOS version is not found
         or if it is found but not signed the
         script exits.
    """

    device_type = get_device_type(uuid)
    available_firmware = json.loads(fetch_available_ios_versions(device_type))["firmwares"]
    for firmware in available_firmware:
        if target_ios_version in firmware.values():
            if firmware['signed'] == False:
                print('Error:', target_ios_version,'is not signed for this device type.')
                sys.exit(1)
            else:
                return
    print('Error:', target_ios_version, 'is not available for this device type.')


def get_device_type(uuid):

    """
      Determines the device type given the UUID 

      Args:
       - uuid: The UUID of the device to
         find the device type of
      Returns:
       - A string containing the device type
    """

    configs = os.popen(config_summary_cmd).read() 
    for line in configs.split('\n'):
        if uuid in line:
            return line.split(' ')[1] 


def get_current_ios_version(uuid):

    """
      Get the iOS version that a device is currently
      on given its UUID

      Args:
       - uuid: The UUID of the device to
         check the iOS version of
      Returns:
       - A string containing the iOS version
         that the device is on
    """

    configs = os.popen(config_summary_cmd).read()
    for line in configs.split('\n'):
        if uuid in line:
            current_ios_version = line.split(' ')[2]
            return current_ios_version[1:-1]  # trim off the parenthesis


def get_rails_info(uuid):

    """
      Retrieve info about a device from rails

      Args:
       - uuid: The UUID of the device to
         get info about from rails
      Returns:
       - JSON containing the response from rails
       - If there was an error while retrieving
         data the script exits
    """

    request_url = 'https://www.browserstack.com/admin/mobile_terminals.json?' \
                  'filters[instance_id]=' + uuid
    try:
        rails_request = requests.get(request_url, auth=(rails_un, rails_pw), timeout=10)
        if rails_request.status_code != requests.codes.ok:
            print('Error: The request to retrieve info for', uuid, 'from rails returned', rails_request.status_code)
            sys.exit(2) 
        return json.loads(rails_request.text)
    except timeout:
        # Log to log file that the request to rails timedout
        sys.exit(1)


def check_usb_status(uuid):

    """
      Checks if the device is on usb

      Args:
       - uuid: The UUID of the device to
         check the usb status for
      Returns:
       - 1: The device is on usb
       - 0: The device is not on usb
    """

    devices_on_usb = os.popen(devices_cmd).read()
    for device in devices_on_usb.split('\n'):
        if uuid == device:
            return 1
    return 0 


def get_build_id(uuid, target_ios_version):

    """
      Returns the build id of the iOS version for a
      given device

      Args:
       - uuid: The UUID of the device that
         we are finding the build id for
       - target_ios_version: The iOS version
         that we are finding the build id for
      Returns:
       - String containing the build id value
    """

    device_type = get_device_type(uuid)
    available_firmware = json.loads(fetch_available_ios_versions(device_type))['firmwares']
    for firmware in available_firmware:
        if target_ios_version in firmware.values():
            return firmware['buildid']


def get_ipsw_md5(uuid, buildid):

    """
      Checks the ipsw.me API to retrieve the md5 sum
      of an ipsw given the UUID and build id

      Args:
       - uuid: The UUID of the device that
         is associated with the ipsw file
        - buildid: The build id associated
          with the ipsw file that we want
          the md5 sum of
      Returns:
       - String containing the md5sum value
    """

    device_type = get_device_type(uuid)
    available_firmware = json.loads(fetch_available_ios_versions(device_type))['firmwares']
    for firmware in available_firmware:
        if buildid in firmware.values():
            return firmware['md5sum']


def download_ipsw(uuid, buildid, ipsw_location):

    """
      Downloads the ipsw file from the download
      endpoint. The user specifies the ipsw location
      which includes the download endpoint IP and
      file name

      Args:
       - uuid: The UUID of the device that the user
         wants to upgrade
       - buildid: The build id of the iOS version
       - ipsw_location: The IP of the download
         download and the ipsw file name
      Returns:
       - None. If the download was successful the
         script continues. If not it logs the error
         and exits.
    """

    file_name = ipsw_location[ipsw_location.rfind('/') + 1 :]
    remote_ipsw_location = 'http://' + ipsw_location
    local_ipsw_location = ipsw_files_dir + file_name
    if verify_remote_ipsw_exists(ipsw_location) == requests.codes.ok:
        ipsw_request = requests.get(remote_ipsw_location, stream = True)
        with open(local_ipsw_location, 'wb') as firmware:
            for chunk in ipsw_request.iter_content(chunk_size=1024):
                firmware.write(chunk)
    print('done downloading')
    if verify_downloaded_ipsw_md5(uuid, buildid, local_ipsw_location) != 0:
        # log error and exit
        print('MD5 of downloaded ipsw is not correct')
    else:
        print('md5 is good')


def verify_downloaded_ipsw_md5(uuid, buildid, local_ipsw_location):

    """
      Get the md5 of the ipsw file passed in and checks it
      against the md5 value in ipsw.me

      Args:
       - uuid: The UUID of the device that the user
         wants to upgrade
       - buildid: The build id of the iOS version
       - local_ipsw_location: The location of the local
         ipsw file to check the md5sum for
      Returns:
       - 0: The md5 is correct
       - 1: The md5 is not correct
    """

    os_md5_cmd = 'md5 ' + local_ipsw_location
    remote_md5 = get_ipsw_md5(uuid, buildid)
    local_md5 = os.popen(os_md5_cmd).read()
    local_md5 = local_md5.split()
    if remote_md5 == local_md5[3]:
        return 0
    return 1


def verify_remote_ipsw_exists(ipsw_location):

    """
      Checks for the ipsw file on the download endpoint

      Args:
       - ipsw_location: String containing the download
         endpoint IP and the ipsw file name
      Returns:
       - String containing the HHTP response code received
         when checking if the file exists
    """

    request_url = "http://" + ipsw_location
    ipsw_exists_request = requests.head(request_url)
    return ipsw_exists_request.status_code


def verify_cli_args():

    """
      Checks multiple things about the arguments passed in on 
      the command line when the script is run. The arguments 
      passed in should be in this order: UUID of the device to 
      upgrade, the iOS version to upgrade to, and the location
      of the ipsw file to use for the upgrade(download endpoint 
      IP & filename: **************/firmware.ipsw).

      Checks:
       1: Were exactly three parameters passed in?
       2: Is the given UUID in the machine config?
       3: Is the device marked as dead in Rails?
       4: Does the current iOS version match the target iOS version?
       5: Is the user attempting to upgrade to a different major iOS version?
       6: Is the target iOS version available for the device type?
       7: Is the ipsw on the download endpoint accessible?

      If any of the above checks fail the script prints to the screen
      and exits.

      Args: - None passed into the function but the function does make use
              the arguments passed in on the command line (sys.argv)
      Returns: - None
    """

    # Create the firmware-files directory if it doesn't exist
    if not os.path.isdir(ipsw_files_dir):
      os.makedirs(ipsw_files_dir)

    # Check 1: Were exactly three parameters passed in?
    if len(sys.argv) != 4:
        print('Error: expected 3 arguments, received', len(sys.argv) - 1)
        print('\tPass in the following arguments in this order: <Device UUID> <Target iOS> <ipsw_file_location>')
        print('\tEx: python', sys.argv[0], '00008030-000A65C00187802E 14.4 **************/firmware_file.ipsw')
        sys.exit(1)

    # Store the UUID and target iOS version
    device_id = sys.argv[1]
    target_ios_version = sys.argv[2]

    # Check 2: Is the given UUID in the machine config?
    configs = os.popen(config_summary_cmd).read()
    found_uuid = False
    for config in configs.split('\n'):
        if device_id in config:
            found_uuid = True
            break
    if not found_uuid:
        print('Error:', device_id, 'not found in the config of this machine')
        sys.exit(1)

    # Check 3: Is the device marked as dead in Rails?
    if get_rails_info(device_id)['dead_count'] != 0:
        print('Error:', device_id, 'is marked as dead in rails.')
        sys.exit(1)

    current_ios_version = get_current_ios_version(device_id)

    # Check 4: Does the current iOS version match the target iOS version?
    if current_ios_version == target_ios_version :
        print('Error:', device_id, 'is already on', target_ios_version)
        sys.exit(1)

    # Check 5: Is the user attempting to upgrade to a different major iOS version?
    current_major_ios_version = current_ios_version[:current_ios_version.find('.')]
    target_major_ios_version = target_ios_version[:target_ios_version.find('.')]
    if current_major_ios_version != target_major_ios_version:
        print('Error: Upgrading to a different major iOS version is not permitted.')
        print('Current iOS version:', current_ios_version)
        print('Target iOS version:', target_ios_version)
        sys.exit(1)

    # Check 6: Is the target iOS version available for the device type?
    validate_ios_version_availability(device_id, target_ios_version)   

    # Check 7: Is the ipsw file on the download endpoint accessible?
    ipsw_location = sys.argv[3]
    if verify_remote_ipsw_exists(ipsw_location) != 200:
        print('Error: Could not find the ipsw file. You provided:', ipsw_location)
        print('\tPlease check the following:')
        print('\t - The machine specified is a download endpoint in your DC')
        print('\t - The ipsw file is on the machine and located in the home directory of ritesharora')
        print('\t - There is a sym link created for the file in /var/www/html')
        sys.exit(1)

def upgrade_device(uuid, local_ipsw_location):

    """
      Upgrades the device specified

      Args: - uuid: The UUID of the device to upgrade
            - local_ipsw_location: The location of the ipsw file that was retrieved

      Returns: - None
    """
    
    # Pull data from rails about the device to see if it can be upgraded now
    rails_device_data = get_rails_info(uuid)
    if rails_device_data["blocked_no_users_count"] == 0:
        if rails_device_data["offline_count"] == 0:
            print("The device is not blocked or offline. Exiting the script")
            sys.exit(1)
    print("upgrading now")
    # go ahead with the upgrade
    upgrade_command = "/usr/local/.browserstack/ios-upgrades/dependencies/idevicerestore/bin/idevicerestore -u " + uuid + " " + local_ipsw_location
    os.system(upgrade_command)

def main():
    verify_cli_args()

    # Initial checks are good, set variables
    uuid = sys.argv[1]
    target_ios_version = sys.argv[2]
    remote_ipsw_location = sys.argv[3]
    ipsw_file_name = remote_ipsw_location[remote_ipsw_location.rfind('/') + 1 :]
    local_ipsw_location = ipsw_files_dir + ipsw_file_name
    device_type = get_device_type(uuid)
    buildid = get_build_id(uuid, target_ios_version)

    # Download the ipsw file to the machine
    download_ipsw(uuid, buildid, remote_ipsw_location)
   
    # Upgrade the device
    upgrade_device(uuid, local_ipsw_location)

main()
