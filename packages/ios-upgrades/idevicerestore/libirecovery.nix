with import (builtins.fetchTarball {
  url = "https://releases.nixos.org/nixpkgs/21.05-darwin/nixpkgs-darwin-21.05pre294215.9d6766c0f0b/nixexprs.tar.xz";
  sha256 = "1ly26r0ryv7c2z68ckxmqzvk0sb2jdnc2nqyrql7v1n5w57ypl5p";
}) { };
stdenv.mkDerivation rec {
  pname = "libirecovery";
  version = "1.0.0";

  src = fetchFromGitHub {
    owner = "libimobiledevice";
    repo = pname;
    rev = "3dda9d2701a34f02058425eea25431122283177c";
    sha256 = "1gyb462i89x4dr73ahb8kvjv9lbx3p01qq61m58li73f1bahhvwc";
  };

  outputs = [ "out" "dev" ];

  nativeBuildInputs = [ autoconf automake libtool pkg-config ];

  buildInputs = [ libusb1 readline ];

  preConfigure = "NOCONFIGURE=1 ./autogen.sh";

  # Packager note: Not clear whether this needs a NixOS configuration,
  # as only the `idevicerestore` binary was tested so far (which worked
  # without further configuration).
  configureFlags = [
    "--with-udevrulesdir=${placeholder "out"}/lib/udev/rules.d"
    ''--with-udevrule="OWNER=\"root\", GROUP=\"myusergroup\", MODE=\"0660\""''
  ];

  meta = with lib; {
    homepage = "https://github.com/libimobiledevice/libirecovery";
    description =
      "Library and utility to talk to iBoot/iBSS via USB on Mac OS X, Windows, and Linux";
    longDescription = ''
      libirecovery is a cross-platform library which implements communication to
      iBoot/iBSS found on Apple's iOS devices via USB. A command-line utility is also
      provided.
    '';
    license = licenses.lgpl21;
    platforms = platforms.linux ++ platforms.darwin;
  };
}
