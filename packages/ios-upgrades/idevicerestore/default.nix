{ pkgs }:
with pkgs;
let
  libirecovery = import ./libirecovery.nix;
  IOKit = darwin.apple_sdk.frameworks.IOKit;
in stdenv.mkDerivation rec {
  pname = "idevicerestore";
  version = "1.0.0";

  src = fetchFromGitHub {
    owner = "libimobiledevice";
    repo = pname;
    rev = "9a9ad5dab9a88705bb0b3261220ba2a52acc31dc";
    sha256 = "091jbkgnimdfgrlimarw060c62i9idlyw86q9x1yj9bd4dk59al4";
  };

  nativeBuildInputs = [ autoreconfHook pkg-config ];

  buildInputs = [
    curl
    libimobiledevice
    libirecovery
    libzip
    libusbmuxd
    # Not listing other dependencies specified in
    # https://github.com/libimobiledevice/idevicerestore/blob/8a882038b2b1e022fbd19eaf8bea51006a373c06/README#L20
    # because they are inherited `libimobiledevice`.
  ] ++ lib.optionals stdenv.isDarwin [ IOKit ];

  meta = with lib; {
    homepage = "https://github.com/libimobiledevice/idevicerestore";
    description = "Restore/upgrade firmware of iOS devices";
    longDescription = ''
      The idevicerestore tool allows to restore firmware files to iOS devices.

      It is a full reimplementation of all granular steps which are performed during
      restore of a firmware to a device.

      In general, upgrades and downgrades are possible, however subject to
      availability of SHSH blobs from Apple for signing the firmare files.

      To restore a device to some firmware, simply run the following:
      $ sudo idevicerestore -l

      This will download and restore a device to the latest firmware available.
    '';
    license = licenses.lgpl21Plus;
    platforms = platforms.linux ++ platforms.darwin;
  };
}
