import requests
from socket import timeout
import os
import sys
import json

# Aliases
config_summary_cmd = 'ruby /usr/local/.browserstack/helpers/config_summary.rb'
devices_cmd = 'bash /usr/local/.browserstack/bshelper.sh devices'

# Global variables
rails_un = 'admin'
rails_pw = os.popen('cat /usr/local/.browserstack/config/static_conf.json | grep admin_terminals_pass').read()
rails_pw = rails_pw[rails_pw.rfind(':')+3:-3]
ipsw_files_dir = '/usr/local/.browserstack/ios-upgrades/firmware-files/'
logs_dir = '/var/log/browserstack/'


def get_device_type(uuid):

    """
      Determines the device type given the UUID 

      Args:
       - uuid: The UUID of the device to
         find the device type of
      Returns:
       - A string containing the device type
    """

    configs = os.popen(config_summary_cmd).read() 
    for line in configs.split('\n'):
        if uuid in line:
            return line.split(' ')[1] 


def get_current_ios_version(uuid):

    """
      Get the iOS version that a device is currently
      on given its UUID

      Args:
       - uuid: The UUID of the device to
         check the iOS version of
      Returns:
       - A string containing the iOS version
         that the device is on
    """

    configs = os.popen(config_summary_cmd).read()
    for line in configs.split('\n'):
        if uuid in line:
            current_ios_version = line.split(' ')[2]
            return current_ios_version[1:-1]  # trim off the parenthesis


def get_rails_info(uuid):

    """
      Retrieve info about a device from rails

      Args:
       - uuid: The UUID of the device to
         get info about from rails
      Returns:
       - JSON containing the response from rails
       - If there was an error while retrieving
         data the script exits
    """

    request_url = 'https://www.browserstack.com/admin/mobile_terminals.json?' \
                  'filters[instance_id]=' + uuid
    try:
        rails_request = requests.get(request_url, auth=(rails_un, rails_pw), timeout=10)
        if rails_request.status_code != requests.codes.ok:
            print('Error: The request to retrieve info for', uuid, 'from rails returned', rails_request.status_code)
            sys.exit(2) 
        return json.loads(rails_request.text)
    except timeout:
        # Log to log file that the request to rails timedout
        sys.exit(1)


def check_usb_status(uuid):

    """
      Checks if the device is on usb

      Args:
       - uuid: The UUID of the device to
         check the usb status for
      Returns:
       - 1: The device is on usb
       - 0: The device is not on usb
    """

    devices_on_usb = os.popen(devices_cmd).read()
    for device in devices_on_usb.split('\n'):
        if uuid == device:
            return 1
    return 0 


def download_ipsw(ipsw_location):

    """
      Downloads the ipsw file from the download
      endpoint. The user specifies the ipsw location
      which includes the download endpoint IP and
      file name

      Args:
       - ipsw_location: The IP of the download
         download and the ipsw file name
      Returns:
       - None. If the download was successful the
         script continues. If not it logs the error
         and exits.
    """

    print('Downloading ipsw file')
    file_name = ipsw_location[ipsw_location.rfind('/') + 1 :]
    remote_ipsw_location = 'http://' + ipsw_location
    local_ipsw_location = ipsw_files_dir + file_name
    if verify_remote_ipsw_exists(ipsw_location) == requests.codes.ok:
        ipsw_request = requests.get(remote_ipsw_location, stream = True)
        with open(local_ipsw_location, 'wb') as firmware:
            for chunk in ipsw_request.iter_content(chunk_size=1024):
                firmware.write(chunk)
    print('Done downloading')


def verify_remote_ipsw_exists(ipsw_location):

    """
      Checks for the ipsw file on the download endpoint

      Args:
       - ipsw_location: String containing the download
         endpoint IP and the ipsw file name
      Returns:
       - String containing the HHTP response code received
         when checking if the file exists
    """

    request_url = "http://" + ipsw_location
    ipsw_exists_request = requests.head(request_url)
    return ipsw_exists_request.status_code


def verify_cli_args():

    """
      Checks multiple things about the arguments passed in on 
      the command line when the script is run. The arguments 
      passed in should be in this order: UUID of the device to 
      upgrade, the iOS version to upgrade to, and the location
      of the ipsw file to use for the upgrade(download endpoint 
      IP & filename: **************/firmware.ipsw).

      Checks:
       1: Were exactly three parameters passed in?
       2: Is the given UUID in the machine config?
       3: Is the device marked as dead in Rails?
       4: Does the current iOS version match the target iOS version?
       5: Is the ipsw on the download endpoint accessible?

      If any of the above checks fail the script prints to the screen
      and exits.

      Args: - None passed into the function but the function does make use
              the arguments passed in on the command line (sys.argv)
      Returns: - None
    """

    # Create the firmware-files directory if it doesn't exist
    if not os.path.isdir(ipsw_files_dir):
      os.makedirs(ipsw_files_dir)

    # Check 1: Were exactly three parameters passed in?
    if len(sys.argv) != 4:
        print('Error: expected 3 arguments, received', len(sys.argv) - 1)
        print('\tPass in the following arguments in this order: <Device UUID> <Target iOS> <ipsw_file_location>')
        print('\tEx: python', sys.argv[0], '00008030-000A65C00187802E 14.4 **************/firmware_file.ipsw')
        sys.exit(1)

    # Store the UUID and target iOS version
    device_id = sys.argv[1]
    target_ios_version = sys.argv[2]

    # Check 2: Is the given UUID in the machine config?
    configs = os.popen(config_summary_cmd).read()
    found_uuid = False
    for config in configs.split('\n'):
        if device_id in config:
            found_uuid = True
            break
    if not found_uuid:
        print('Error:', device_id, 'not found in the config of this machine')
        sys.exit(1)

    # Check 3: Is the device marked as dead in Rails?
    if get_rails_info(device_id)['dead_count'] != 0:
        print('Error:', device_id, 'is marked as dead in rails.')
        sys.exit(1)

    current_ios_version = get_current_ios_version(device_id)

    # Check 4: Does the current iOS version match the target iOS version?
    if current_ios_version == target_ios_version :
        print('Error:', device_id, 'is already on', target_ios_version)
        sys.exit(1)

    # Check 5: Is the ipsw file on the download endpoint accessible?
    ipsw_location = sys.argv[3]
    if verify_remote_ipsw_exists(ipsw_location) != 200:
        print('Error: Could not find the ipsw file. You provided:', ipsw_location)
        print('\tPlease check the following:')
        print('\t - The machine specified is a download endpoint in your DC')
        print('\t - The ipsw file is on the machine and located in the home directory of ritesharora')
        print('\t - There is a sym link created for the file in /var/www/html')
        sys.exit(1)

    print('All initial checks passed, you can exit this session now')

def upgrade_device(uuid, local_ipsw_location):

    """
      Upgrades the device specified

      Args: - uuid: The UUID of the device to upgrade
            - local_ipsw_location: The location of the ipsw file that was retrieved

      Returns: - None
    """
    
    # Pull data from rails about the device to see if it can be upgraded now
    rails_device_data = get_rails_info(uuid)
    if rails_device_data["blocked_no_users_count"] == 0:
        if rails_device_data["offline_count"] == 0:
            print("The device is not blocked or offline. Exiting the script")
            sys.exit(1)
    print("Upgrading now")
    # Go ahead with the upgrade
    upgrade_command = "/usr/local/.browserstack/ios-upgrades/dependencies/idevicerestore/bin/idevicerestore -u " + uuid + " " + local_ipsw_location
    os.system(upgrade_command)

def main():
    verify_cli_args()

    # Initial checks are good, set variables
    uuid = sys.argv[1]
    target_ios_version = sys.argv[2]
    remote_ipsw_location = sys.argv[3]
    ipsw_file_name = remote_ipsw_location[remote_ipsw_location.rfind('/') + 1 :]
    local_ipsw_location = ipsw_files_dir + ipsw_file_name
    device_type = get_device_type(uuid)

    # Download the ipsw file to the machine
    download_ipsw(remote_ipsw_location)
   
    # Upgrade the device
    upgrade_device(uuid, local_ipsw_location)

main()
