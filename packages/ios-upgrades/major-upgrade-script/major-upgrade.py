import requests
from socket import timeout
import os
import sys
import json

### FYI ###
# sys.argv[0] = the script name
# sys.argv[1] = UUID
# sys.argv[2] = ECID
# sys.argv[3] = Target iOS
# sys.argv[4] = IPSW name


###########
# ALIASES #
###########

config_summary_cmd = 'ruby /usr/local/.browserstack/helpers/config_summary.rb'
devices_cmd = 'bash /usr/local/.browserstack/bshelper.sh devices'

####################
# GLOBAL VARIABLES #
#################### 

rails_un = 'admin'
rails_pw = os.popen('cat /usr/local/.browserstack/config/static_conf.json | grep admin_terminals_pass').read()
rails_pw = rails_pw[rails_pw.rfind(':')+3:-3]
local_ipsw_dir = '/usr/local/.browserstack/ios-upgrades/firmware-files/'
logs_dir = '/var/log/browserstack/'


##################
# MAIN FUNCTIONS #
##################

### MAIN FNC 1: DOES APPLE CONFIGURATOR EXIST? ###
def apple_config_check():
    if not os.path.exists('/Applications/Apple Configurator 2.app'):
        print("Apple configurator isn't installed. \n Check if OS is Monterey or Big Sur Apple Configurator is installed. \n Exiting program.")
        sys.exit(1)


### MAIN FNC 2: ARE ALL ARGUMENTS PASSED? ###
def verify_cli_args():
    """
      Checks multiple things about the arguments passed in on
      the command line when the script is run. The arguments
      passed in should be in this order: UUID of the device to
      upgrade, the iOS version to upgrade to, and the location
      of the ipsw file to use for the upgrade.

      Checks:
       1: Were exactly four parameters passed in?
       2: Is the given UUID in the machine config?
       3: Is the device marked as dead in Rails?
       4: Does the current iOS version match the target iOS version?
       5: Is the target iOS version available for the device type?
       6: Is the ipsw on the download endpoint accessible?

      If any of the above checks fail the script prints to the screen
      and exits.

      Args: - None passed into the function but the function does make use
              the arguments passed in on the command line (sys.argv)
      Returns: - None
    """
    # Create the firmware-files directory if it doesn't exist
    if not os.path.isdir(local_ipsw_dir):
      os.makedirs(local_ipsw_dir)

    # Check 1: Were exactly three parameters passed in?
    if len(sys.argv) != 5:
        print('Error: expected 4 arguments, received', len(sys.argv) - 1)
        print('\tPass in the following arguments in this order: <Device UUID> <Device ECID> <Target iOS> <ipsw_name>')
        print('\tEx: python3', sys.argv[0], '00008030-001D6816023A802E 0x1D6816023A802E 14.4 firmware_file.ipsw')
        sys.exit(1)

    # Store the UUID and target iOS version
    uuid = sys.argv[1]
    target_ios_version = sys.argv[3]

    # Check 2: Is the given UUID in the machine config?
    configs = os.popen(config_summary_cmd).read()
    found_uuid = False
    for config in configs.split('\n'):
        if uuid in config:
            found_uuid = True
            break
    if not found_uuid:
        print('Error:', uuid, 'not found in the config of this machine')
        sys.exit(1)

    # Check 3: Is the device marked as dead in Rails?
    if get_rails_info(uuid)['dead_count'] != 0:
        print('Error:', uuid, 'is marked as dead in rails.')
        sys.exit(1)

    current_ios_version = get_current_ios_version(uuid)

    # Check 4: Does the current iOS version match the target iOS version?
    if current_ios_version == target_ios_version :
        print('Error:', uuid, 'is already on', target_ios_version)
        sys.exit(1)

    # Check 5: Is the target iOS version available for the device type?
    validate_ios_version_availability(uuid, target_ios_version)

    # Check 6: Is the ipsw file accessible?
    ipsw_name = sys.argv[4]
    if verify_remote_ipsw_exists(ipsw_name) != 200:
        print('Error: Could not find the ipsw file. You provided:', ipsw_name)
        print('\tPlease check the following:')
        print('\t - If the ipsw has been uploaded to the DCs NAS')
        sys.exit(1)


### MAIN FNC 3: DOWNLOAD IPSW ###
def download_ipsw(uuid, buildid, ipsw_name):
    """
      Downloads the ipsw file from the download
      endpoint. The user specifies the ipsw location
      which includes the download endpoint IP and
      file name

      Args:
       - uuid: The UUID of the device that the user
         wants to upgrade
       - buildid: The build id of the iOS version
       - ipsw_name: The IP of the download
         download and the ipsw file name
      Returns:
       - None. If the download was successful the
         script continues. If not it logs the error
         and exits.
    """
    print('Checking if file is already downloaded:\n')
    local_ipsw_location = local_ipsw_dir + ipsw_name

    if os.path.exists(local_ipsw_location):
        print('IPSW already downloaded. Proceeding with upgrade...\n')
    else:
        print('Starting download...\n')
        curl_IPSW = "curl http://dc-file-server.service.prod.mobile.browserstack.com:3131/" + ipsw_name + " -o /usr/local/.browserstack/ios-upgrades/firmware-files/" + ipsw_name
        os.system(curl_IPSW)
        print('...done downloading')
    if verify_downloaded_ipsw_md5(uuid, buildid, local_ipsw_location) != 0:
        # log error and exit
        print('MD5 of downloaded ipsw is not correct')
    else:
        print('md5 is good')


### MAIN FNC 4: UPGRADE THE DEVICE ###
def upgrade_device(ecid, uuid, local_ipsw_location):

    """
      Upgrades the device specified

      Args: - uuid: The UUID of the device to upgrade
            - local_ipsw_location: The location of the ipsw file that was retrieved

      Returns: - None
    """

    # Pull data from rails about the device to see if it can be upgraded now
    rails_device_data = get_rails_info(uuid)
    if rails_device_data["blocked_no_users_count"] == 0:
        if rails_device_data["offline_count"] == 0:
            print("The device is not blocked or offline. Exiting the script")
            sys.exit(1)
    print("upgrading now")
    # go ahead with the upgrade
    upgrade_command = "cfgutil -e " + ecid + " update -I " + local_ipsw_location
    os.system(upgrade_command)


#######################
# AUXILIARY FUNCTIONS #
#######################

def fetch_available_ios_versions(device_type):

    """
      Returns available firmeware from ipsw.me API

      Args:
       - device_type: The model of the
         device to find firmware for
      Returns:
       - String of JSON data of available
         firmware if the model was found
       - Prints to screen and exits if it
         could not find the model
    """

    request_url = 'https://api.ipsw.me/v4/device/' + device_type + '?type=ipsw'
    try:
        ios_versions_request = requests.get(request_url, timeout=5)
        if ios_versions_request.status_code != requests.codes.ok:
            print('Error: The request to retrieve available iOS versions for', device_type, 'returned', ios_versions_request.status_code)
            sys.exit(2)
        else:
            return ios_versions_request.text
    except timeout:
        print('Error: The request to retrieve available iOS versions for', device_type,'timed out')
        sys.exit(2)


def validate_ios_version_availability(uuid, target_ios_version):

    """
      Checks the ipsw.me API to see if the requested
      iOS version is available for the device and if
      it is signed.

      Args:
       - uuid: The UUID of the device to
         upgrade. Used to get the device type
       - target_ios_version: The iOS version
         to check availability for given
         the device type
      Returns:
       - If the iOS version is found
         and is signed, nothing is returned.
       - If either the iOS version is not found
         or if it is found but not signed the
         script exits.
    """

    device_type = get_device_type(uuid)
    available_firmware = json.loads(fetch_available_ios_versions(device_type))["firmwares"]
    for firmware in available_firmware:
        if target_ios_version in firmware.values():
            if firmware['signed'] == False:
                print('Error:', target_ios_version,'is not signed for this device type.')
                sys.exit(1)
            else:
                return
    print('Error:', target_ios_version, 'is not available for this device type.')


def get_device_type(uuid):

    """
      Determines the device type given the UUID

      Args:
       - uuid: The UUID of the device to
         find the device type of
      Returns:
       - A string containing the device type
    """

    configs = os.popen(config_summary_cmd).read()
    for line in configs.split('\n'):
        if uuid in line:
            return line.split(' ')[1]


def get_current_ios_version(uuid):

    """
      Get the iOS version that a device is currently
      on given its UUID

      Args:
       - uuid: The UUID of the device to
         check the iOS version of
      Returns:
       - A string containing the iOS version
         that the device is on
    """

    configs = os.popen(config_summary_cmd).read()
    for line in configs.split('\n'):
        if uuid in line:
            current_ios_version = line.split(' ')[2]
            return current_ios_version[1:-1]  # trim off the parenthesis


def get_rails_info(uuid):

    """
      Retrieve info about a device from rails

      Args:
       - uuid: The UUID of the device to
         get info about from rails
      Returns:
       - JSON containing the response from rails
       - If there was an error while retrieving
         data the script exits
    """

    request_url = 'https://www.browserstack.com/admin/mobile_terminals.json?' \
                  'filters[instance_id]=' + uuid
    try:
        rails_request = requests.get(request_url, auth=(rails_un, rails_pw), timeout=10)
        if rails_request.status_code != requests.codes.ok:
            print('Error: The request to retrieve info for', uuid, 'from rails returned', rails_request.status_code)
            sys.exit(2)
        return json.loads(rails_request.text)
    except timeout:
        # Log to log file that the request to rails timedout
        sys.exit(1)


def check_usb_status(uuid):

    """
      Checks if the device is on usb

      Args:
       - uuid: The UUID of the device to
         check the usb status for
      Returns:
       - 1: The device is on usb
       - 0: The device is not on usb
    """

    devices_on_usb = os.popen(devices_cmd).read()
    for device in devices_on_usb.split('\n'):
        if uuid == device:
            return 1
    return 0


def get_build_id(uuid, target_ios_version):

    """
      Returns the build id of the iOS version for a
      given device

      Args:
       - uuid: The UUID of the device that
         we are finding the build id for
       - target_ios_version: The iOS version
         that we are finding the build id for
      Returns:
       - String containing the build id value
    """

    device_type = get_device_type(uuid)
    available_firmware = json.loads(fetch_available_ios_versions(device_type))['firmwares']
    for firmware in available_firmware:
        if target_ios_version in firmware.values():
            return firmware['buildid']


def get_ipsw_md5(uuid, buildid):

    """
      Checks the ipsw.me API to retrieve the md5 sum
      of an ipsw given the UUID and build id

      Args:
       - uuid: The UUID of the device that
         is associated with the ipsw file
        - buildid: The build id associated
          with the ipsw file that we want
          the md5 sum of
      Returns:
       - String containing the md5sum value
    """

    device_type = get_device_type(uuid)
    available_firmware = json.loads(fetch_available_ios_versions(device_type))['firmwares']
    for firmware in available_firmware:
        if buildid in firmware.values():
            return firmware['md5sum']



def verify_downloaded_ipsw_md5(uuid, buildid, local_ipsw_location):

    """
      Get the md5 of the ipsw file passed in and checks it
      against the md5 value in ipsw.me

      Args:
       - uuid: The UUID of the device that the user
         wants to upgrade
       - buildid: The build id of the iOS version
       - local_ipsw_location: The location of the local
         ipsw file to check the md5sum for
      Returns:
       - 0: The md5 is correct
       - 1: The md5 is not correct
    """

    os_md5_cmd = 'md5 ' + local_ipsw_location
    remote_md5 = get_ipsw_md5(uuid, buildid)
    local_md5 = os.popen(os_md5_cmd).read()
    local_md5 = local_md5.split()
    if remote_md5 == local_md5[3]:
        return 0
    return 1

def verify_remote_ipsw_exists(ipsw_name):

    """
      Checks for the ipsw file on the download endpoint

      Args:
       - ipsw_name: String containing the download
         endpoint IP and the ipsw file name
      Returns:
       - String containing the HHTP response code received
         when checking if the file exists
    """

    request_url = "http://dc-file-server.service.prod.mobile.browserstack.com:3131/" + ipsw_name
    ipsw_exists_request = requests.head(request_url)
    return ipsw_exists_request.status_code


def main():

    apple_config_check()
    verify_cli_args()
    #  have to change ecid and expected arguments for the program...
    # Initial checks are good, set variables

    ecid = sys.argv[2]
    uuid = sys.argv[1]
    target_ios_version = sys.argv[3]
    ipsw_name = sys.argv[4] 
    local_ipsw_location = local_ipsw_dir + ipsw_name
    device_type = get_device_type(uuid)
    buildid = get_build_id(uuid, target_ios_version)

    # Download the ipsw file to the machine
    download_ipsw(uuid, buildid, ipsw_name)

    # Upgrade the device
    upgrade_device(ecid, uuid, local_ipsw_location)

main()
