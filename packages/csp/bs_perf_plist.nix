{ pkgs }:

pkgs.writeTextFile {
    name = "mobilecspt-plist";
    executable = true;
    text = ''
    <?xml version="1.0" encoding="UTF-8"?>
    <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
    <plist version="1.0">
        <dict>
            <key>EnvironmentVariables</key>
            <dict>
                <key>PATH</key>
                <string>/bin:/usr/bin:/usr/local/bin:/opt/homebrew/bin</string>
                <key>rvmsudo_secure_path</key>
                <string>0</string>
                <key>CSPT_INFRA</key>
                <string>BS</string>
            </dict>
            <key>UserName</key>
            <string>root</string>
            <key>Label</key>
            <string>com.browserstack.mobilecspt</string>
            <key>ProgramArguments</key>
            <array>
                <string>/usr/local/.browserstack/csp_bin/bs-perf-tool-macos</string>
                <string>server</string>
                <string>-p</string>
                <string>45700</string>
                <string>-pi</string>
                <string>45701</string>
            </array>
            <key>RunAtLoad</key>
            <true/>
            <key>KeepAlive</key>
            <true/>
            <key>StandardOutPath</key>
            <string>/var/log/browserstack/csp/bsperf.log</string>
            <key>StandardErrorPath</key>
            <string>/var/log/browserstack/csp/bsperf.log</string>
            <key>WorkingDirectory</key>
            <string>/usr/local/.browserstack/csp_bin/</string>
        </dict>
    </plist>
    '';
}
