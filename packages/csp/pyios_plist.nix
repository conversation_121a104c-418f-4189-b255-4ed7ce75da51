{ pkgs }:

pkgs.writeTextFile {
    name = "pyios-plist";
    executable = true;
    text = ''
    <?xml version="1.0" encoding="UTF-8"?>
    <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
    <plist version="1.0">
        <dict>
            <key>EnvironmentVariables</key>
            <dict>
                <key>PATH</key>
                <string>/bin:/usr/bin:/usr/local/bin:/opt/homebrew/bin</string>
                <key>rvmsudo_secure_path</key>
                <string>0</string>
                <key>CSPT_INFRA</key>
                <string>BS</string>
            </dict>
            <key>UserName</key>
            <string>root</string>
            <key>Label</key>
            <string>com.browserstack.pyios</string>
            <key>ProgramArguments</key>
            <array>
                <string>/usr/local/.browserstack/csp_bin/py-ios/server</string>
                <string>45701</string>
            </array>
            <key>RunAtLoad</key>
            <true/>
            <key>KeepAlive</key>
            <true/>
            <key>StandardOutPath</key>
            <string>/var/log/browserstack/csp/py-ios.log</string>
            <key>StandardErrorPath</key>
            <string>/var/log/browserstack/csp/py-ios.log</string>
            <key>WorkingDirectory</key>
            <string>/usr/local/.browserstack/csp_bin/</string>
        </dict>
    </plist>
    '';
}
