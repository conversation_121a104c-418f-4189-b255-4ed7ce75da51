{ pkgs }:
with pkgs;
let
  bshelperAliasesScript = writeScript "bshlper-aliases-script" ''
    export BSHELPERSDIR="/usr/local/.browserstack/helpers"
    export BSHELPERS="/usr/local/.browserstack/bshelper.sh"
    alias devices="bash $BSHELPERS devices"
    alias clean="bash $BSHELPERS clean"
    alias config="bash $BSHELPERS config"
    alias cleanuplogs="bash $BSHELPERS cleanuplogs"
    alias install_phase_logs="bash $BSHELPERS install_phase_logs"
    alias serverlogs="bash $BSHELPERS serverlogs"
    alias dclogs="bash $BSHELPERS dclogs"
    alias mchecklogs="bash $BSHELPERS mchecklogs"
    alias config_summary="ruby $BSHELPERSDIR/config_summary.rb"
    alias cs="ruby $BSHELPERSDIR/config_summary.rb"
    alias ms="bro machine info"
    alias removedevice="bash $BSHELPERS removedevice"
    alias restart_servers="bash $BSHELPERS restart_servers"
    alias go="cd /usr/local/.browserstack"
    alias logs="f() { if [[ -z "\$1" ]]; then cd "/var/log/browserstack"; else tail -100f "/var/log/browserstack/\$1"; fi };f"
    alias unlock_screen="bash $BSHELPERS unlock_screen"
    alias run_machine_check="bash $BSHELPERS run_machine_check"
    alias app_version="cd /usr/local/.browserstack/realmobile; bundle exec ruby $BSHELPERSDIR/app_version.rb run"
    alias disable_device_restrictions="bash $BSHELPERS disable_device_restrictions"
    alias static_conf="bash $BSHELPERS static_conf"
    alias enable_autocompletions="source /usr/local/.browserstack/realmobile/scripts/auto_completion/init"
    alias build_and_install_bs_app="bash $BSHELPERS build_and_install_bs_app"
    alias build_and_install_wda="bash $BSHELPERS build_and_install_wda"
    alias launch_wda="bash $BSHELPERS launch_wda"
    alias multilogs="cd /usr/local/.browserstack/realmobile; bundle exec ruby scripts/multi_logs.rb"
    alias reboot_device="bash $BSHELPERS reboot_device"
    alias touch_dedicated_file="bash $BSHELPERS touch_dedicated_file"
    alias mip="bash $BSHELPERS mip"
    alias flush_nomad_and_consul="bash $BSHELPERS flush_nomad_and_consul"
    alias retrigger_nomad_allocations="bash $BSHELPERS retrigger_nomad_allocations"
  '';
in [
  {
    package = bshelperAliasesScript;
    destination = "/usr/local/.browserstack/bshelper_aliases.sh";
  }
  {
    package = writeScript "bshelper-sh" (builtins.readFile ./bshelper);
    destination = "/usr/local/.browserstack/bshelper.sh";
  }
]
