#!/bin/bash
#
# bshelper.sh --- An attempt to make life easier!
#
# set -x

PROG="$0"
COMMAND="$1"
CONFIG="/usr/local/.browserstack/config/config.json"
STATIC_CONF="/usr/local/.browserstack/config/static_conf.json"
LOGS="/var/log/browserstack"
DEVICEID=
NUM_LINES=
SEARCH_STRING=
DEVICE_INFO=false
FULL_CLEANUP=false
SHELL_COMMAND=
JQ="/usr/local/bin/jq"

function usage {
cat <<EOF
Usage: $PROG [command] [-afhs] [-d device_id] [-n num]
    Options:
        -h      display this help and exit

    Commands:

        devices [-a] [-d deviceid]      Show info of all connected devices.
                                        -a displays additional info corresponsing to each device in the format [model][port][online_status]
                                        -d display deviceid specific info.

        clean deviceid          Run cleanup of device.
                                        -f specifies fullcleanup, no option defaults to quick cleanup.

        config [-d deviceid [key]]      Pretty print config.json.
                                        -d prints config of deviceid only
                                        if key is specified, only prints key of device's config

        cleanuplogs [-ns] -d deviceid   Tail cleauplogs of deviceid.
                                        -n specifies number of lines to tail.Default is 100.
                                        -s greps logs with string specified by this flag

        install_phase_logs [-ns] -d deviceid        Tail install phase of deviceid.
                                        -n specifies number of lines to tail.Default is 100.
                                        -s greps logs with string specified by this flag

        serverlogs [-ns]                Tail server logs.
                                        -n specifies number of lines to tail.Default is 100.
                                        -s greps logs with string specified by this flag

        dclogs [-n]                     Tail device_check logs.
                                        -n specifies number of lines to tail.Default is 100.

        mchecklogs [-n]                 Tail device_check logs.
                                        -n specifies number of lines to tail.Default is 100.

        removedevice deviceid [flags]   Calls /remove_device endpoint to remove deviceid from config
                                        if --nowait is specified, removedevice won't keep retrying if device
                                        check is running.
                                        if --noclean is specified, removedevice will not send the phone to
                                        cleanup after it goes online.

        restart_servers                 Do a pumactl phased-restart on all pumas

        run_machine_check               Run machine check and tail output logs.

        unlock_screen device            Attempt to unlock screen using instruments

        disable_device_restrictions     Kill idevicesyslog so that settings & other restricted apps can be
                                        opened for debugging

        static_conf                     Pretty print static_conf.json

        reboot_device -d deviceid       Reboot the device

        touch_dedicated_file deviceId   Touch dedicated cloud state file for the device

        mip                              Get "." separated IP address of machine

        flush_nomad_and_consul          Stops the Consul And Nomad Clients. Deletes all data. Then starts them.

        retrigger_nomad_allocations     Drains Nomad Allocations And Re Triggers them.
EOF
}

#=============================================Methods==========================================

list_devices_with_info(){
  devices=""
  devices=$(/usr/local/bin/idevice_id -l )
  for dev in $devices;do
    key=".devices[\"$dev\"]"
    echo "$dev" $(cat $CONFIG | $JQ $key | $JQ '"\(.device_name) \(.port) \(.online) \(.device_version)"') | tr -d '"'
  done
}

list_devices(){
    echo $( /usr/local/bin/idevice_id -l ) | tr ' ' '\n'
}

list_device(){
  if is_device_connected; then
    key=".devices[\"$DEVICEID\"]"
    echo "$DEVICEID" $(cat $CONFIG | $JQ $key | $JQ '"\(.device_name) \(.port) \(.online)"') | tr -d '"'
  fi
}

is_device_connected(){
  connected=""
  connected=`/usr/local/bin/idevice_id -l | grep -wc $DEVICEID`
  if [ "$connected" == "1" ];then
    return 0
  fi
  echo "Device not found"
  return 1
}

cleanup_logs(){
  CLEANUP_LOGS=$LOGS/cleanup_"$DEVICEID".log
  if [[ -z $SEARCH_STRING ]]; then
    tail -"$NUM_LINES"f $CLEANUP_LOGS
  else
    zgrep --no-filename $SEARCH_STRING $CLEANUP_LOGS*
  fi
}

install_phase_logs(){
  INSTALL_PHASE_LOGS=$LOGS/install_phase_"$DEVICEID".log
  if [[ -z $SEARCH_STRING ]]; then
    tail -"$NUM_LINES"f $INSTALL_PHASE_LOGS
  else
    zgrep --no-filename $SEARCH_STRING $INSTALL_PHASE_LOGS*
  fi
}

device_check_logs(){
  tail -"$NUM_LINES"f $LOGS/mobile_poll.log
}

machine_check_logs(){
  tail -"$NUM_LINES"f $LOGS/device_poll.log
}

server_logs(){
  env=$(cat /usr/local/.browserstack/env)
  SERVER_LOGS=$LOGS/"$env".log
  if [[ -z $SEARCH_STRING ]]; then
    tail -"$NUM_LINES"f $SERVER_LOGS
  else
    zgrep --no-filename $SEARCH_STRING $SERVER_LOGS*
  fi
}

remove_device() {
  while true; do
    echo 'Testing....'
    res=$(curl -s "http://localhost:45671/remove_device?device=$DEVICEID");
    echo $res;
    if [[ "$res" != "Device check is running" ]] || [[ ! -z "$NOWAIT" ]]; then
      break
    else
      sleep 1;
    fi
  done;
  if [[ -z "$NOCLEAN" ]]; then
    touch "/usr/local/.browserstack/state_files/cleanup_requested_$DEVICEID"
  fi
}

build_and_install_bs_app() {
  cd /usr/local/.browserstack/realmobile/lib/helpers/ && bundle exec ruby browserstack_app_helper.rb build_and_install "$DEVICEID"
}

build_and_install_wda() {
  bash /usr/local/.browserstack/realmobile/scripts/build_and_install_wda.sh $WDA_BSTACK_UTILITIES_BRANCH
}

launch_wda(){
  cd /usr/local/.browserstack/realmobile || { echo "Failed to enter 'realmobile' directory"; exit 1; }
  bundle exec ruby lib/appium_server.rb start_driver $DEVICEID
}

#=============================================Main==========================================

if [[ ( -z "$1") || ( "$1" == "--help") ]]
then
    usage
    exit 0
fi

shift

while getopts "ad:fhn:s:" opt; do

  case $opt in
    a ) DEVICE_INFO=true;
        ;;
    d ) DEVICEID=$OPTARG;
        ;;
    f ) FULL_CLEANUP=true;
        ;;
    h ) usage; exit 0;
        ;;
    n ) NUM_LINES=$OPTARG;
        ;;
    s ) SEARCH_STRING=$OPTARG;
        ;;
    \?) usage
        ;;
  esac
done


case $COMMAND in

    devices )
      if [[ ! ( "$1" == "-a") ]];then
        DEVICEID=${DEVICEID:-$1}
      fi
      if [[ -z "$DEVICEID" ]];then
        if "$DEVICE_INFO";then
          list_devices_with_info
        else
          list_devices
        fi
      else
        list_device
      fi
      ;;

    clean )
      if [[ -z "$1" ]];then
        usage;
        exit 0;
      fi
      DEVICEID=${DEVICEID:-${@:(-1):1}}
      if $FULL_CLEANUP; then
        curl "http://localhost:45671/cleanup?device=$DEVICEID&retry_cleanup=true&full_cleanup=true&manual_cleanup=true"
      else
        curl "http://localhost:45671/cleanup?device=$DEVICEID&retry_cleanup=true&manual_cleanup=true"
      fi
      ;;

    config )
      DEVICEID=${DEVICEID:-$1}
      KEY=$2
      if [[ -z "$DEVICEID" ]];then
        key="."
      else
        if [[ -z "$KEY" ]]; then
          key=".devices[\"$DEVICEID\"]"
        else
          key=".devices[\"$DEVICEID\"][\"$KEY\"]"
        fi

      fi
      cat $CONFIG | $JQ $key
      ;;

    cleanuplogs )
      if [[ -z "$1" ]];then
        usage;
        exit 0;
      fi
      DEVICEID=${DEVICEID:-${@:(-1):1}}
      NUM_LINES=${NUM_LINES:-100}
      cleanup_logs
      ;;

    install_phase_logs )
      if [[ -z "$1" ]];then
        usage;
        exit 0;
      fi
      DEVICEID=${DEVICEID:-${@:(-1):1}}
      NUM_LINES=${NUM_LINES:-100}
      install_phase_logs
      ;;

    dclogs )
      NUM_LINES=${NUM_LINES:-100}
      device_check_logs
      ;;

    serverlogs )
      NUM_LINES=${NUM_LINES:-100}
      server_logs
      ;;

    mchecklogs )
      NUM_LINES=${NUM_LINES:-100}
      machine_check_logs
      ;;

    removedevice )
      if [[ -z "$1" ]];then
        usage;
        exit 0;
      fi
      DEVICEID=$1
      NOWAIT=$(echo $@ | grep -- '--nowait')
      NOCLEAN=$(echo $@ | grep -- '--noclean')
      remove_device
      ;;

    unlock_screen )
      if [[ -z "$1" ]];then
        usage;
        exit 0;
      fi
      /usr/bin/instruments -v -t "Activity Monitor" -l 100 -D kill_iphone -w $1 com.apple.Preferences
      ;;

    restart_servers )
      for pid in `ps aux | grep puma | grep tcp | awk '{print $2}'`; do
          echo "restarting $pid"
          pumactl -p $pid phased-restart
      done
      ;;

    run_machine_check )
      sudo launchctl unload -w /Library/LaunchDaemons/com.browserstack.machine_check.plist
      sudo launchctl load -w /Library/LaunchDaemons/com.browserstack.machine_check.plist
      tail -f /var/log/browserstack/machine_check_plist.log
      ;;

    disable_device_restrictions )
      DEVICEID=$1

      STATE_DIR="/usr/local/.browserstack/state_files"
      DEDICATED_FILE="$STATE_DIR/dedicated_device_$DEVICEID"
      CUSTOM_MDM_FILE="$STATE_DIR/custom_mdm_$DEVICEID"

      # Determine values based on file presence
      is_dedicated_device=false
      is_custom_mdm_device=false

      if [ -f "$DEDICATED_FILE" ]; then
        is_dedicated_device=true
      fi

      if [ -f "$CUSTOM_MDM_FILE" ]; then
        is_custom_mdm_device=true
      fi

      ps -ef | grep $DEVICEID | grep idevicesyslog | awk '{print $2}' | xargs -I{} kill {}
      cd /usr/local/.browserstack/realmobile/lib/helpers/ && bundle exec ruby wda_client.rb $DEVICEID settings_access true $is_dedicated_device $is_custom_mdm_device true
      ;;

    static_conf )
      KEY=$1
      if [[ -z "$KEY" ]];then
        KEY="."
      fi
      cat $STATIC_CONF |$JQ $KEY
      ;;

    build_and_install_bs_app )
      DEVICEID=$1
      build_and_install_bs_app
      ;;

    build_and_install_wda )
      WDA_BSTACK_UTILITIES_BRANCH=$1
      build_and_install_wda
      ;;

    launch_wda )
      DEVICEID=$1
      launch_wda
      ;;

    reboot_device )
      if [[ -z "$1" ]];then
        usage;
        exit 0;
      fi
      DEVICEID=${DEVICEID:-$1}
      /usr/local/bin/idevicediagnostics -u $DEVICEID restart
      ;;

    touch_dedicated_file )
      if [[ -z "$1" ]];then
        usage;
        exit 0;
      fi
      DEVICEID=${DEVICEID:-$1}
      touch "/usr/local/.browserstack/state_files/dedicated_device_$DEVICEID"
      ;;

    mip )
      curl ifconfig.me
      ;;

    flush_nomad_and_consul )
      sudo launchctl unload /Library/LaunchDaemons/com.hashicorp.consul.plist
      sudo rm -rf /opt/consul/data/*
      sudo launchctl load -w /Library/LaunchDaemons/com.hashicorp.consul.plist

      sudo launchctl unload /Library/LaunchDaemons/com.hashicorp.nomad.plist
      sudo rm -rf /opt/nomad/data/*
      sudo launchctl load -w /Library/LaunchDaemons/com.hashicorp.nomad.plist
      ;;

    retrigger_nomad_allocations )
      nomad_drain_token=$($JQ -r '.nomad_drain_token' "$STATIC_CONF")
      export NOMAD_TOKEN="$nomad_drain_token"
      nomad node drain -self -enable
      nomad node eligibility -enable -self
      ;;
esac
