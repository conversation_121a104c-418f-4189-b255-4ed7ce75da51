# nix packages

This directory contains files for installing dependencies through nix. [See this guide on confluence for more info](https://browserstack.atlassian.net/wiki/spaces/ENG/pages/3093464238/Deploying+files+using+Nix+Nomad+from+S3).

## Snippets
### Generate SHA

```shell
nix-hash --flat --base32 --type sha256 your-file.zip
```

# device-packages.nix

## com.browserstack.Launcher

> When altering the version, the `Info.plist` inside the app package must correspond the version in `constant.yml`.
This is the the `.app` package of [ios-browser-launcher](https://github.com/browserstack/ios-browser-launcher), see the readme for info on building.

### Exporting

After building the application and testing through Xcode, we extract the .app bundle and deploy it as a tar file.

This file will typically be located somewhere like here: `~/Library/Developer/Xcode/DerivedData/Launcher-ckdcjxcbmefzeiduhcladduijtdi/Build/Products/Debug-iphoneos/Launcher.app`

```shell
version="14"
cp -r ~/Library/Developer/Xcode/DerivedData/Launcher-alsftpdjcnjygxabwfcdhszwnsiu/Build/Products/Debug-iphoneos/Launcher.app "/tmp/com.browserstack.Launcher-${version}.app"
```

Create IPA file
```shell
mkdir /tmp/Payload
cp -r "/tmp/com.browserstack.Launcher-${version}.app" /tmp/Payload
zip -r com.browserstack.Launcher.ipa /tmp/Payload
```

Create tar.gz for nix
```shell
tar cvzf "/tmp/com.browserstack.Launcher-${version}.tar.gz" "/tmp/com.browserstack.Launcher-${version}.app"
```

### Testing

To test the portable app on other machines/devices using realmobile you can use `ideviceinstaller`.

> If using the `tar.gz`, you need to extract it first.
#### Signing
To install the package, it will need to be signed.

[See this snippet for using realmobile to sign the app.](../lib/readme.md)

#### Installing

```shell
# Uninstall app first
ideviceinstaller -u $did --uninstall com.browserstack.Launcher
ideviceinstaller -u $did -i "Launcher.ipa"
```

Installing typically takes a few seconds, if it seems to be stuck then something might have gone wrong with signing. 