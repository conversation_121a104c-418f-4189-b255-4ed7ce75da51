{ pkgs }:

with pkgs;
let
  nixPlistPath = "/usr/local/.browserstack/nix/services/Library/LaunchDaemons";
in
[
  # csp 
  {
    package = callPackage ../csp {};
    destination = "/usr/local/.browserstack/csp_bin";
  }
  # csp bs-perf plist
  {
    package = callPackage ../csp/bs_perf_plist.nix {};
    destination = "${nixPlistPath}/com.browserstack.mobilecspt.plist";
  }
  # csp pyios plist
  {
    package = callPackage ../csp/pyios_plist.nix {};
    destination = "${nixPlistPath}/com.browserstack.pyios.plist";
  }
]
