# This file has been generated by node2nix 1.11.0. Do not edit!

{nodeEnv, fetchurl, fetchgit, nix-gitignore, stdenv, lib, globalBuildInputs ? []}:

let
  sources = {
    "@babel/code-frame-7.25.7" = {
      name = "_at_babel_slash_code-frame";
      packageName = "@babel/code-frame";
      version = "7.25.7";
      src = fetchurl {
        url = "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.25.7.tgz";
        sha512 = "0xZJFNE5XMpENsgfHYTw8FbX4kv53mFLn2i3XPoq69LyhYSCBJtitaHx9QnsVTrsogI4Z3+HtEfZ2/GFPOtf5g==";
      };
    };
    "@babel/helper-validator-identifier-7.25.7" = {
      name = "_at_babel_slash_helper-validator-identifier";
      packageName = "@babel/helper-validator-identifier";
      version = "7.25.7";
      src = fetchurl {
        url = "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.25.7.tgz";
        sha512 = "AM6TzwYqGChO45oiuPqwL2t20/HdMC1rTPAesnBCgPCSF1x3oN9MVUwQV2iyz4xqWrctwK5RNC8LV22kaQCNYg==";
      };
    };
    "@babel/highlight-7.25.7" = {
      name = "_at_babel_slash_highlight";
      packageName = "@babel/highlight";
      version = "7.25.7";
      src = fetchurl {
        url = "https://registry.npmjs.org/@babel/highlight/-/highlight-7.25.7.tgz";
        sha512 = "iYyACpW3iW8Fw+ZybQK+drQre+ns/tKpXbNESfrhNnPLIklLbXr7MYJ6gPEd0iETGLOK+SxMjVvKb/ffmk+FEw==";
      };
    };
    "@nodelib/fs.scandir-2.1.5" = {
      name = "_at_nodelib_slash_fs.scandir";
      packageName = "@nodelib/fs.scandir";
      version = "2.1.5";
      src = fetchurl {
        url = "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz";
        sha512 = "vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==";
      };
    };
    "@nodelib/fs.stat-2.0.5" = {
      name = "_at_nodelib_slash_fs.stat";
      packageName = "@nodelib/fs.stat";
      version = "2.0.5";
      src = fetchurl {
        url = "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz";
        sha512 = "RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==";
      };
    };
    "@nodelib/fs.walk-1.2.8" = {
      name = "_at_nodelib_slash_fs.walk";
      packageName = "@nodelib/fs.walk";
      version = "1.2.8";
      src = fetchurl {
        url = "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz";
        sha512 = "oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==";
      };
    };
    "@percy/cli-1.30.0" = {
      name = "_at_percy_slash_cli";
      packageName = "@percy/cli";
      version = "1.30.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/@percy/cli/-/cli-1.30.0.tgz";
        sha512 = "d5mJQkhYJdL/fOZGuk2sBymYSYCncM+adpOrI3kINVLSXH7CAvYfrnJNLZ5Ib2xQDS85QeCFIb9noivPqXtkPw==";
      };
    };
    "@percy/cli-app-1.30.0" = {
      name = "_at_percy_slash_cli-app";
      packageName = "@percy/cli-app";
      version = "1.30.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/@percy/cli-app/-/cli-app-1.30.0.tgz";
        sha512 = "omBN3mwZVlOv06pIL1sHfN18iQ6fJcyJtWVtPS82idSHBm0xWe4ulcAAXAJAI31opKUIh1dM1LDxFDJBQEA1Tw==";
      };
    };
    "@percy/cli-build-1.30.0" = {
      name = "_at_percy_slash_cli-build";
      packageName = "@percy/cli-build";
      version = "1.30.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/@percy/cli-build/-/cli-build-1.30.0.tgz";
        sha512 = "VEdKQQCdfdjZHLBjQMXILSeiCEwuKaGvfi9TQqE+4ZPWzOUorjf/Knpnm5+FvvpJYcB3TgNIZ7H/jQE9LokChg==";
      };
    };
    "@percy/cli-command-1.30.0" = {
      name = "_at_percy_slash_cli-command";
      packageName = "@percy/cli-command";
      version = "1.30.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/@percy/cli-command/-/cli-command-1.30.0.tgz";
        sha512 = "5gUZfUaYY4O4UdS4yAKCkVuEQov8ZmBRez2oE9wZti1kCavcRJ3cps31qMiXWyRBzAru+MdDxpoDD40KGK5m5g==";
      };
    };
    "@percy/cli-config-1.30.0" = {
      name = "_at_percy_slash_cli-config";
      packageName = "@percy/cli-config";
      version = "1.30.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/@percy/cli-config/-/cli-config-1.30.0.tgz";
        sha512 = "LUVrQZMjswTS5bifrpbU/vL/fFCx+Pl8FG+rRA5f8tnVBnLxnMpGeOFhKf8mmkg32YJxGhmmXn//pR4aupN4oA==";
      };
    };
    "@percy/cli-exec-1.30.0" = {
      name = "_at_percy_slash_cli-exec";
      packageName = "@percy/cli-exec";
      version = "1.30.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/@percy/cli-exec/-/cli-exec-1.30.0.tgz";
        sha512 = "9uSrPY+VhRXmuIiPO0+XoZNJcwrfdZqmCcR8nZdSlLvgoGbm/4/gzMNnlazzgY+NHkMSVpZyE6tTiAePjweuIA==";
      };
    };
    "@percy/cli-snapshot-1.30.0" = {
      name = "_at_percy_slash_cli-snapshot";
      packageName = "@percy/cli-snapshot";
      version = "1.30.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/@percy/cli-snapshot/-/cli-snapshot-1.30.0.tgz";
        sha512 = "EdCnZUcq7O5SDymmzzdJSuoAvWa25fZtAn7ji4W7moWDAY3r62Q4S8ZC4NfMbvihbBa1FJTzbgOHbfS3gSgbkQ==";
      };
    };
    "@percy/cli-upload-1.30.0" = {
      name = "_at_percy_slash_cli-upload";
      packageName = "@percy/cli-upload";
      version = "1.30.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/@percy/cli-upload/-/cli-upload-1.30.0.tgz";
        sha512 = "vtgQU6JbnDUUe/+8teN2E0vfHS/NeRRe0n9Nc/e8MgMXWXhgZEWWPC8jlwwIliCFgkiWl+zWXD6OAbSlyoBtYw==";
      };
    };
    "@percy/client-1.30.0" = {
      name = "_at_percy_slash_client";
      packageName = "@percy/client";
      version = "1.30.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/@percy/client/-/client-1.30.0.tgz";
        sha512 = "kNAoa07QtlIHvail0+vjd0xfvO4OxI97fV73pefe6PtJ1u3Cze+q4DEFPWxcJ39wjUD4C2f3TgJ2CzhEm53yiw==";
      };
    };
    "@percy/config-1.30.0" = {
      name = "_at_percy_slash_config";
      packageName = "@percy/config";
      version = "1.30.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/@percy/config/-/config-1.30.0.tgz";
        sha512 = "mOhLn9fqQTMgXA8Li1UxjZCcLQcokeQtsedlCXdzzt2QbS9DFcV3koMyooabOO6MsCNh4wI7J/zcXu08c4H9Vw==";
      };
    };
    "@percy/core-1.30.0" = {
      name = "_at_percy_slash_core";
      packageName = "@percy/core";
      version = "1.30.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/@percy/core/-/core-1.30.0.tgz";
        sha512 = "MeAhFWCf97xVvx8c6XsSpC+uWRbM0RE5llXmC7U5JH2cYZXoyzul2eZacYUmQJPVzEaWduXLDCukYIa0nDo1+A==";
      };
    };
    "@percy/dom-1.30.0" = {
      name = "_at_percy_slash_dom";
      packageName = "@percy/dom";
      version = "1.30.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/@percy/dom/-/dom-1.30.0.tgz";
        sha512 = "i3uHFDAzXCLAeI0oTDDWIaRq1wiGwoRknS1QfIfy7usAia+ipeWhWb+m3sXtfg/g6WyBzcg3OtG1tIB8TO7/4g==";
      };
    };
    "@percy/env-1.30.0" = {
      name = "_at_percy_slash_env";
      packageName = "@percy/env";
      version = "1.30.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/@percy/env/-/env-1.30.0.tgz";
        sha512 = "+h7c0lxB01bnymTZfM24RrjMIL28rQWeT4aQ/BJVlvAELstZaYwl0vv8RrAtBUKnSZaXUkNIOWZtgFiuer/y0g==";
      };
    };
    "@percy/logger-1.30.0" = {
      name = "_at_percy_slash_logger";
      packageName = "@percy/logger";
      version = "1.30.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/@percy/logger/-/logger-1.30.0.tgz";
        sha512 = "p5ZxD9fFRjCiMF630xIDRv2hPH70TI20r9yIyHgjmCM4ES0Cfu+61gaRpiSaay0DWKfG6iwpmUhZsKafRMSYCw==";
      };
    };
    "@percy/sdk-utils-1.30.0" = {
      name = "_at_percy_slash_sdk-utils";
      packageName = "@percy/sdk-utils";
      version = "1.30.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/@percy/sdk-utils/-/sdk-utils-1.30.0.tgz";
        sha512 = "GPEexJ0gxiFXqjFpL8EtILZo/arrwdz96oyZMsCeRu9bVSQ8ZvptFlwO8hYKtXvZcY7CJfGnUyOcza8CYPxM3g==";
      };
    };
    "@percy/webdriver-utils-1.30.0" = {
      name = "_at_percy_slash_webdriver-utils";
      packageName = "@percy/webdriver-utils";
      version = "1.30.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/@percy/webdriver-utils/-/webdriver-utils-1.30.0.tgz";
        sha512 = "93S0U1enM9mHR99iEjPSekrZc3/DI+GYYXicvM8nFD9FHlXuMbykHCU0hKJAZF/IvlucVnH04s3yomn/58cXAw==";
      };
    };
    "@types/node-22.7.5" = {
      name = "_at_types_slash_node";
      packageName = "@types/node";
      version = "22.7.5";
      src = fetchurl {
        url = "https://registry.npmjs.org/@types/node/-/node-22.7.5.tgz";
        sha512 = "jML7s2NAzMWc//QSJ1a3prpk78cOPchGvXJsC3C6R6PSMoooztvRVQEz89gmBTBY1SPMaqo5teB4uNHPdetShQ==";
      };
    };
    "@types/yauzl-2.10.3" = {
      name = "_at_types_slash_yauzl";
      packageName = "@types/yauzl";
      version = "2.10.3";
      src = fetchurl {
        url = "https://registry.npmjs.org/@types/yauzl/-/yauzl-2.10.3.tgz";
        sha512 = "oJoftv0LSuaDZE3Le4DbKX+KS9G36NzOeSap90UIK0yMA/NhKJhqlSGtNDORNRaIbQfzjXDrQa0ytJ6mNRGz/Q==";
      };
    };
    "ajv-8.17.1" = {
      name = "ajv";
      packageName = "ajv";
      version = "8.17.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/ajv/-/ajv-8.17.1.tgz";
        sha512 = "B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g==";
      };
    };
    "ansi-styles-3.2.1" = {
      name = "ansi-styles";
      packageName = "ansi-styles";
      version = "3.2.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz";
        sha512 = "VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==";
      };
    };
    "argparse-2.0.1" = {
      name = "argparse";
      packageName = "argparse";
      version = "2.0.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz";
        sha512 = "8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==";
      };
    };
    "balanced-match-1.0.2" = {
      name = "balanced-match";
      packageName = "balanced-match";
      version = "1.0.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz";
        sha512 = "3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==";
      };
    };
    "brace-expansion-1.1.11" = {
      name = "brace-expansion";
      packageName = "brace-expansion";
      version = "1.1.11";
      src = fetchurl {
        url = "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz";
        sha512 = "iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==";
      };
    };
    "braces-3.0.3" = {
      name = "braces";
      packageName = "braces";
      version = "3.0.3";
      src = fetchurl {
        url = "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz";
        sha512 = "yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==";
      };
    };
    "buffer-crc32-0.2.13" = {
      name = "buffer-crc32";
      packageName = "buffer-crc32";
      version = "0.2.13";
      src = fetchurl {
        url = "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.13.tgz";
        sha512 = "VO9Ht/+p3SN7SKWqcrgEzjGbRSJYTx+Q1pTQC0wrWqHx0vpJraQ6GtHx8tvcg1rlK1byhU5gccxgOgj7B0TDkQ==";
      };
    };
    "callsites-3.1.0" = {
      name = "callsites";
      packageName = "callsites";
      version = "3.1.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz";
        sha512 = "P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==";
      };
    };
    "chalk-2.4.2" = {
      name = "chalk";
      packageName = "chalk";
      version = "2.4.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz";
        sha512 = "Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==";
      };
    };
    "color-convert-1.9.3" = {
      name = "color-convert";
      packageName = "color-convert";
      version = "1.9.3";
      src = fetchurl {
        url = "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz";
        sha512 = "QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==";
      };
    };
    "color-name-1.1.3" = {
      name = "color-name";
      packageName = "color-name";
      version = "1.1.3";
      src = fetchurl {
        url = "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz";
        sha512 = "72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==";
      };
    };
    "concat-map-0.0.1" = {
      name = "concat-map";
      packageName = "concat-map";
      version = "0.0.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz";
        sha512 = "/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==";
      };
    };
    "content-disposition-0.5.4" = {
      name = "content-disposition";
      packageName = "content-disposition";
      version = "0.5.4";
      src = fetchurl {
        url = "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.4.tgz";
        sha512 = "FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==";
      };
    };
    "cosmiconfig-8.3.6" = {
      name = "cosmiconfig";
      packageName = "cosmiconfig";
      version = "8.3.6";
      src = fetchurl {
        url = "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-8.3.6.tgz";
        sha512 = "kcZ6+W5QzcJ3P1Mt+83OUv/oHFqZHIx8DuxG6eZ5RGMERoLqp4BuGjhHLYGK+Kf5XVkQvqBSmAy/nGWN3qDgEA==";
      };
    };
    "cross-spawn-7.0.3" = {
      name = "cross-spawn";
      packageName = "cross-spawn";
      version = "7.0.3";
      src = fetchurl {
        url = "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.3.tgz";
        sha512 = "iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==";
      };
    };
    "debug-4.3.7" = {
      name = "debug";
      packageName = "debug";
      version = "4.3.7";
      src = fetchurl {
        url = "https://registry.npmjs.org/debug/-/debug-4.3.7.tgz";
        sha512 = "Er2nc/H7RrMXZBFCEim6TCmMk02Z8vLC2Rbi1KEBggpo0fS6l0S1nnapwmIi3yW/+GOJap1Krg4w0Hg80oCqgQ==";
      };
    };
    "end-of-stream-1.4.4" = {
      name = "end-of-stream";
      packageName = "end-of-stream";
      version = "1.4.4";
      src = fetchurl {
        url = "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.4.tgz";
        sha512 = "+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==";
      };
    };
    "error-ex-1.3.2" = {
      name = "error-ex";
      packageName = "error-ex";
      version = "1.3.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz";
        sha512 = "7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==";
      };
    };
    "escape-string-regexp-1.0.5" = {
      name = "escape-string-regexp";
      packageName = "escape-string-regexp";
      version = "1.0.5";
      src = fetchurl {
        url = "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz";
        sha512 = "vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==";
      };
    };
    "extract-zip-2.0.1" = {
      name = "extract-zip";
      packageName = "extract-zip";
      version = "2.0.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/extract-zip/-/extract-zip-2.0.1.tgz";
        sha512 = "GDhU9ntwuKyGXdZBUgTIe+vXnWj0fppUEtMDL0+idd5Sta8TGpHssn/eusA9mrPr9qNDym6SxAYZjNvCn/9RBg==";
      };
    };
    "fast-deep-equal-3.1.3" = {
      name = "fast-deep-equal";
      packageName = "fast-deep-equal";
      version = "3.1.3";
      src = fetchurl {
        url = "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz";
        sha512 = "f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==";
      };
    };
    "fast-glob-3.3.2" = {
      name = "fast-glob";
      packageName = "fast-glob";
      version = "3.3.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.2.tgz";
        sha512 = "oX2ruAFQwf/Orj8m737Y5adxDQO0LAB7/S5MnxCdTNDd4p6BsyIVsv9JQsATbTSq8KHRpLwIHbVlUNatxd+1Ow==";
      };
    };
    "fast-uri-3.0.2" = {
      name = "fast-uri";
      packageName = "fast-uri";
      version = "3.0.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/fast-uri/-/fast-uri-3.0.2.tgz";
        sha512 = "GR6f0hD7XXyNJa25Tb9BuIdN0tdr+0BMi6/CJPH3wJO1JjNG3n/VsSw38AwRdKZABm8lGbPfakLRkYzx2V9row==";
      };
    };
    "fastq-1.17.1" = {
      name = "fastq";
      packageName = "fastq";
      version = "1.17.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/fastq/-/fastq-1.17.1.tgz";
        sha512 = "sRVD3lWVIXWg6By68ZN7vho9a1pQcN/WBFaAAsDDFzlJjvoGx0P8z7V1t72grFJfJhu3YPZBuu25f7Kaw2jN1w==";
      };
    };
    "fd-slicer-1.1.0" = {
      name = "fd-slicer";
      packageName = "fd-slicer";
      version = "1.1.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/fd-slicer/-/fd-slicer-1.1.0.tgz";
        sha512 = "cE1qsB/VwyQozZ+q1dGxR8LBYNZeofhEdUNGSMbQD3Gw2lAzX9Zb3uIU6Ebc/Fmyjo9AWWfnn0AUCHqtevs/8g==";
      };
    };
    "fill-range-7.1.1" = {
      name = "fill-range";
      packageName = "fill-range";
      version = "7.1.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz";
        sha512 = "YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==";
      };
    };
    "fs.realpath-1.0.0" = {
      name = "fs.realpath";
      packageName = "fs.realpath";
      version = "1.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz";
        sha512 = "OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==";
      };
    };
    "get-stream-5.2.0" = {
      name = "get-stream";
      packageName = "get-stream";
      version = "5.2.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/get-stream/-/get-stream-5.2.0.tgz";
        sha512 = "nBF+F1rAZVCu/p7rjzgA+Yb4lfYXrpl7a6VmJrU8wF9I1CKvP/QwPNZHnOlwbTkY6dvtFIzFMSyQXbLoTQPRpA==";
      };
    };
    "glob-7.2.3" = {
      name = "glob";
      packageName = "glob";
      version = "7.2.3";
      src = fetchurl {
        url = "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz";
        sha512 = "nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==";
      };
    };
    "glob-parent-5.1.2" = {
      name = "glob-parent";
      packageName = "glob-parent";
      version = "5.1.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz";
        sha512 = "AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==";
      };
    };
    "has-flag-3.0.0" = {
      name = "has-flag";
      packageName = "has-flag";
      version = "3.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz";
        sha512 = "sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==";
      };
    };
    "image-size-1.1.1" = {
      name = "image-size";
      packageName = "image-size";
      version = "1.1.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/image-size/-/image-size-1.1.1.tgz";
        sha512 = "541xKlUw6jr/6gGuk92F+mYM5zaFAc5ahphvkqvNe2bQ6gVBkd6bfrmVJ2t4KDAfikAYZyIqTnktX3i6/aQDrQ==";
      };
    };
    "import-fresh-3.3.0" = {
      name = "import-fresh";
      packageName = "import-fresh";
      version = "3.3.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.0.tgz";
        sha512 = "veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==";
      };
    };
    "inflight-1.0.6" = {
      name = "inflight";
      packageName = "inflight";
      version = "1.0.6";
      src = fetchurl {
        url = "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz";
        sha512 = "k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==";
      };
    };
    "inherits-2.0.4" = {
      name = "inherits";
      packageName = "inherits";
      version = "2.0.4";
      src = fetchurl {
        url = "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz";
        sha512 = "k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==";
      };
    };
    "is-arrayish-0.2.1" = {
      name = "is-arrayish";
      packageName = "is-arrayish";
      version = "0.2.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz";
        sha512 = "zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==";
      };
    };
    "is-extglob-2.1.1" = {
      name = "is-extglob";
      packageName = "is-extglob";
      version = "2.1.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz";
        sha512 = "SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==";
      };
    };
    "is-glob-4.0.3" = {
      name = "is-glob";
      packageName = "is-glob";
      version = "4.0.3";
      src = fetchurl {
        url = "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz";
        sha512 = "xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==";
      };
    };
    "is-number-7.0.0" = {
      name = "is-number";
      packageName = "is-number";
      version = "7.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz";
        sha512 = "41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==";
      };
    };
    "isexe-2.0.0" = {
      name = "isexe";
      packageName = "isexe";
      version = "2.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz";
        sha512 = "RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==";
      };
    };
    "js-tokens-4.0.0" = {
      name = "js-tokens";
      packageName = "js-tokens";
      version = "4.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz";
        sha512 = "RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==";
      };
    };
    "js-yaml-4.1.0" = {
      name = "js-yaml";
      packageName = "js-yaml";
      version = "4.1.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz";
        sha512 = "wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==";
      };
    };
    "json-parse-even-better-errors-2.3.1" = {
      name = "json-parse-even-better-errors";
      packageName = "json-parse-even-better-errors";
      version = "2.3.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz";
        sha512 = "xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==";
      };
    };
    "json-schema-traverse-1.0.0" = {
      name = "json-schema-traverse";
      packageName = "json-schema-traverse";
      version = "1.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz";
        sha512 = "NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==";
      };
    };
    "lines-and-columns-1.2.4" = {
      name = "lines-and-columns";
      packageName = "lines-and-columns";
      version = "1.2.4";
      src = fetchurl {
        url = "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz";
        sha512 = "7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==";
      };
    };
    "merge2-1.4.1" = {
      name = "merge2";
      packageName = "merge2";
      version = "1.4.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz";
        sha512 = "8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==";
      };
    };
    "micromatch-4.0.8" = {
      name = "micromatch";
      packageName = "micromatch";
      version = "4.0.8";
      src = fetchurl {
        url = "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz";
        sha512 = "PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==";
      };
    };
    "mime-db-1.52.0" = {
      name = "mime-db";
      packageName = "mime-db";
      version = "1.52.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz";
        sha512 = "sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==";
      };
    };
    "mime-types-2.1.35" = {
      name = "mime-types";
      packageName = "mime-types";
      version = "2.1.35";
      src = fetchurl {
        url = "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz";
        sha512 = "ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==";
      };
    };
    "minimatch-3.1.2" = {
      name = "minimatch";
      packageName = "minimatch";
      version = "3.1.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz";
        sha512 = "J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==";
      };
    };
    "ms-2.1.3" = {
      name = "ms";
      packageName = "ms";
      version = "2.1.3";
      src = fetchurl {
        url = "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz";
        sha512 = "6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==";
      };
    };
    "once-1.4.0" = {
      name = "once";
      packageName = "once";
      version = "1.4.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/once/-/once-1.4.0.tgz";
        sha512 = "lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==";
      };
    };
    "pako-2.1.0" = {
      name = "pako";
      packageName = "pako";
      version = "2.1.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/pako/-/pako-2.1.0.tgz";
        sha512 = "w+eufiZ1WuJYgPXbV/PO3NCMEc3xqylkKHzp8bxp1uW4qaSNQUkwmLLEc3kKsfz8lpV1F8Ht3U1Cm+9Srog2ug==";
      };
    };
    "parent-module-1.0.1" = {
      name = "parent-module";
      packageName = "parent-module";
      version = "1.0.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz";
        sha512 = "GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==";
      };
    };
    "parse-json-5.2.0" = {
      name = "parse-json";
      packageName = "parse-json";
      version = "5.2.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz";
        sha512 = "ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==";
      };
    };
    "path-is-absolute-1.0.1" = {
      name = "path-is-absolute";
      packageName = "path-is-absolute";
      version = "1.0.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz";
        sha512 = "AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==";
      };
    };
    "path-key-3.1.1" = {
      name = "path-key";
      packageName = "path-key";
      version = "3.1.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz";
        sha512 = "ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==";
      };
    };
    "path-to-regexp-6.3.0" = {
      name = "path-to-regexp";
      packageName = "path-to-regexp";
      version = "6.3.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-6.3.0.tgz";
        sha512 = "Yhpw4T9C6hPpgPeA28us07OJeqZ5EzQTkbfwuhsUg0c237RomFoETJgmp2sa3F/41gfLE6G5cqcYwznmeEeOlQ==";
      };
    };
    "path-type-4.0.0" = {
      name = "path-type";
      packageName = "path-type";
      version = "4.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz";
        sha512 = "gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==";
      };
    };
    "pend-1.2.0" = {
      name = "pend";
      packageName = "pend";
      version = "1.2.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/pend/-/pend-1.2.0.tgz";
        sha512 = "F3asv42UuXchdzt+xXqfW1OGlVBe+mxa2mqI0pg5yAHZPvFmY3Y6drSf/GQ1A86WgWEN9Kzh/WrgKa6iGcHXLg==";
      };
    };
    "picocolors-1.1.0" = {
      name = "picocolors";
      packageName = "picocolors";
      version = "1.1.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/picocolors/-/picocolors-1.1.0.tgz";
        sha512 = "TQ92mBOW0l3LeMeyLV6mzy/kWr8lkd/hp3mTg7wYK7zJhuBStmGMBG0BdeDZS/dZx1IukaX6Bk11zcln25o1Aw==";
      };
    };
    "picomatch-2.3.1" = {
      name = "picomatch";
      packageName = "picomatch";
      version = "2.3.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz";
        sha512 = "JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==";
      };
    };
    "pump-3.0.2" = {
      name = "pump";
      packageName = "pump";
      version = "3.0.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/pump/-/pump-3.0.2.tgz";
        sha512 = "tUPXtzlGM8FE3P0ZL6DVs/3P58k9nk8/jZeQCurTJylQA8qFYzHFfhBJkuqyE0FifOsQ0uKWekiZ5g8wtr28cw==";
      };
    };
    "queue-6.0.2" = {
      name = "queue";
      packageName = "queue";
      version = "6.0.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/queue/-/queue-6.0.2.tgz";
        sha512 = "iHZWu+q3IdFZFX36ro/lKBkSvfkztY5Y7HMiPlOUjhupPcG2JMfst2KKEpu5XndviX/3UhFbRngUPNKtgvtZiA==";
      };
    };
    "queue-microtask-1.2.3" = {
      name = "queue-microtask";
      packageName = "queue-microtask";
      version = "1.2.3";
      src = fetchurl {
        url = "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz";
        sha512 = "NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==";
      };
    };
    "require-from-string-2.0.2" = {
      name = "require-from-string";
      packageName = "require-from-string";
      version = "2.0.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.2.tgz";
        sha512 = "Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==";
      };
    };
    "resolve-from-4.0.0" = {
      name = "resolve-from";
      packageName = "resolve-from";
      version = "4.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz";
        sha512 = "pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==";
      };
    };
    "reusify-1.0.4" = {
      name = "reusify";
      packageName = "reusify";
      version = "1.0.4";
      src = fetchurl {
        url = "https://registry.npmjs.org/reusify/-/reusify-1.0.4.tgz";
        sha512 = "U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==";
      };
    };
    "rimraf-3.0.2" = {
      name = "rimraf";
      packageName = "rimraf";
      version = "3.0.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz";
        sha512 = "JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==";
      };
    };
    "run-parallel-1.2.0" = {
      name = "run-parallel";
      packageName = "run-parallel";
      version = "1.2.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz";
        sha512 = "5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==";
      };
    };
    "safe-buffer-5.2.1" = {
      name = "safe-buffer";
      packageName = "safe-buffer";
      version = "5.2.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz";
        sha512 = "rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==";
      };
    };
    "shebang-command-2.0.0" = {
      name = "shebang-command";
      packageName = "shebang-command";
      version = "2.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz";
        sha512 = "kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==";
      };
    };
    "shebang-regex-3.0.0" = {
      name = "shebang-regex";
      packageName = "shebang-regex";
      version = "3.0.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz";
        sha512 = "7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==";
      };
    };
    "supports-color-5.5.0" = {
      name = "supports-color";
      packageName = "supports-color";
      version = "5.5.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz";
        sha512 = "QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==";
      };
    };
    "to-regex-range-5.0.1" = {
      name = "to-regex-range";
      packageName = "to-regex-range";
      version = "5.0.1";
      src = fetchurl {
        url = "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz";
        sha512 = "65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==";
      };
    };
    "undici-types-6.19.8" = {
      name = "undici-types";
      packageName = "undici-types";
      version = "6.19.8";
      src = fetchurl {
        url = "https://registry.npmjs.org/undici-types/-/undici-types-6.19.8.tgz";
        sha512 = "ve2KP6f/JnbPBFyobGHuerC9g1FYGn/F8n1LWTwNxCEzd6IfqTwUQcNXgEtmmQ6DlRrC1hrSrBnCZPokRrDHjw==";
      };
    };
    "which-2.0.2" = {
      name = "which";
      packageName = "which";
      version = "2.0.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/which/-/which-2.0.2.tgz";
        sha512 = "BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==";
      };
    };
    "wrappy-1.0.2" = {
      name = "wrappy";
      packageName = "wrappy";
      version = "1.0.2";
      src = fetchurl {
        url = "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz";
        sha512 = "l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==";
      };
    };
    "ws-8.18.0" = {
      name = "ws";
      packageName = "ws";
      version = "8.18.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/ws/-/ws-8.18.0.tgz";
        sha512 = "8VbfWfHLbbwu3+N6OKsOMpBdT4kXPDDB9cJk2bJ6mh9ucxdlnNvH1e+roYkKmN9Nxw2yjz7VzeO9oOz2zJ04Pw==";
      };
    };
    "yaml-2.6.0" = {
      name = "yaml";
      packageName = "yaml";
      version = "2.6.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/yaml/-/yaml-2.6.0.tgz";
        sha512 = "a6ae//JvKDEra2kdi1qzCyrJW/WZCgFi8ydDV+eXExl95t+5R+ijnqHJbz9tmMh8FUjx3iv2fCQ4dclAQlO2UQ==";
      };
    };
    "yauzl-2.10.0" = {
      name = "yauzl";
      packageName = "yauzl";
      version = "2.10.0";
      src = fetchurl {
        url = "https://registry.npmjs.org/yauzl/-/yauzl-2.10.0.tgz";
        sha512 = "p4a9I6X6nu6IhoGmBqAcbJy1mlC4j27vEPZX9F4L4/vZT3Lyq1VkFHw/V/PUcB9Buo+DG3iHkT0x3Qya58zc3g==";
      };
    };
  };
  args = {
    name = "percy-cli";
    packageName = "percy-cli";
    version = "1.30.0";
    src = ./.;
    dependencies = [
      sources."@babel/code-frame-7.25.7"
      sources."@babel/helper-validator-identifier-7.25.7"
      sources."@babel/highlight-7.25.7"
      sources."@nodelib/fs.scandir-2.1.5"
      sources."@nodelib/fs.stat-2.0.5"
      sources."@nodelib/fs.walk-1.2.8"
      sources."@percy/cli-1.30.0"
      sources."@percy/cli-app-1.30.0"
      sources."@percy/cli-build-1.30.0"
      sources."@percy/cli-command-1.30.0"
      sources."@percy/cli-config-1.30.0"
      sources."@percy/cli-exec-1.30.0"
      sources."@percy/cli-snapshot-1.30.0"
      sources."@percy/cli-upload-1.30.0"
      sources."@percy/client-1.30.0"
      sources."@percy/config-1.30.0"
      sources."@percy/core-1.30.0"
      sources."@percy/dom-1.30.0"
      sources."@percy/env-1.30.0"
      sources."@percy/logger-1.30.0"
      sources."@percy/sdk-utils-1.30.0"
      sources."@percy/webdriver-utils-1.30.0"
      sources."@types/node-22.7.5"
      sources."@types/yauzl-2.10.3"
      sources."ajv-8.17.1"
      sources."ansi-styles-3.2.1"
      sources."argparse-2.0.1"
      sources."balanced-match-1.0.2"
      sources."brace-expansion-1.1.11"
      sources."braces-3.0.3"
      sources."buffer-crc32-0.2.13"
      sources."callsites-3.1.0"
      sources."chalk-2.4.2"
      sources."color-convert-1.9.3"
      sources."color-name-1.1.3"
      sources."concat-map-0.0.1"
      sources."content-disposition-0.5.4"
      sources."cosmiconfig-8.3.6"
      sources."cross-spawn-7.0.3"
      sources."debug-4.3.7"
      sources."end-of-stream-1.4.4"
      sources."error-ex-1.3.2"
      sources."escape-string-regexp-1.0.5"
      sources."extract-zip-2.0.1"
      sources."fast-deep-equal-3.1.3"
      sources."fast-glob-3.3.2"
      sources."fast-uri-3.0.2"
      sources."fastq-1.17.1"
      sources."fd-slicer-1.1.0"
      sources."fill-range-7.1.1"
      sources."fs.realpath-1.0.0"
      sources."get-stream-5.2.0"
      sources."glob-7.2.3"
      sources."glob-parent-5.1.2"
      sources."has-flag-3.0.0"
      sources."image-size-1.1.1"
      sources."import-fresh-3.3.0"
      sources."inflight-1.0.6"
      sources."inherits-2.0.4"
      sources."is-arrayish-0.2.1"
      sources."is-extglob-2.1.1"
      sources."is-glob-4.0.3"
      sources."is-number-7.0.0"
      sources."isexe-2.0.0"
      sources."js-tokens-4.0.0"
      sources."js-yaml-4.1.0"
      sources."json-parse-even-better-errors-2.3.1"
      sources."json-schema-traverse-1.0.0"
      sources."lines-and-columns-1.2.4"
      sources."merge2-1.4.1"
      sources."micromatch-4.0.8"
      sources."mime-db-1.52.0"
      sources."mime-types-2.1.35"
      sources."minimatch-3.1.2"
      sources."ms-2.1.3"
      sources."once-1.4.0"
      sources."pako-2.1.0"
      sources."parent-module-1.0.1"
      sources."parse-json-5.2.0"
      sources."path-is-absolute-1.0.1"
      sources."path-key-3.1.1"
      sources."path-to-regexp-6.3.0"
      sources."path-type-4.0.0"
      sources."pend-1.2.0"
      sources."picocolors-1.1.0"
      sources."picomatch-2.3.1"
      sources."pump-3.0.2"
      sources."queue-6.0.2"
      sources."queue-microtask-1.2.3"
      sources."require-from-string-2.0.2"
      sources."resolve-from-4.0.0"
      sources."reusify-1.0.4"
      sources."rimraf-3.0.2"
      sources."run-parallel-1.2.0"
      sources."safe-buffer-5.2.1"
      sources."shebang-command-2.0.0"
      sources."shebang-regex-3.0.0"
      sources."supports-color-5.5.0"
      sources."to-regex-range-5.0.1"
      sources."undici-types-6.19.8"
      sources."which-2.0.2"
      sources."wrappy-1.0.2"
      sources."ws-8.18.0"
      sources."yaml-2.6.0"
      sources."yauzl-2.10.0"
    ];
    buildInputs = globalBuildInputs;
    meta = {
      description = "";
      license = "ISC";
    };
    production = true;
    bypassCache = true;
    reconstructLock = true;
  };
in
{
  args = args;
  sources = sources;
  tarball = nodeEnv.buildNodeSourceDist args;
  package = nodeEnv.buildNodePackage args;
  shell = nodeEnv.buildNodeShell args;
  nodeDependencies = nodeEnv.buildNodeDependencies (lib.overrideExisting args {
    src = stdenv.mkDerivation {
      name = args.name + "-package-json";
      src = nix-gitignore.gitignoreSourcePure [
        "*"
        "!package.json"
        "!package-lock.json"
      ] args.src;
      dontBuild = true;
      installPhase = "mkdir -p $out; cp -r ./* $out;";
    };
  });
}
