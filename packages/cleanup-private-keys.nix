{ pkgs }:
with pkgs;
let
  GITHUB_TOKEN = builtins.getEnv "GITHUB_TOKEN";
  revision = "****************************************";
  mobileToolsRepo = fetchzip rec {
    url = "https://github.com/browserstack/mobile-tools/archive/${revision}.tar.gz";
    curlOpts = [ "-H @${writeText "headers.txt" "Authorization: token ${GITHUB_TOKEN}"}" ];
    sha256 = "0lkdyfxchxdk66cd2a7i570nlxxrd6hn049wfgb4xfln62ig5rz9";
  };
in
  [
  {
    package = runCommand "cleanup_private_keys" { inherit mobileToolsRepo; } ''
      cp $mobileToolsRepo/cleanup-private-keys/key-cleaner $out
    '';
    destination = "/usr/local/.browserstack/deps/cleanup_private_keys";
  }
]
