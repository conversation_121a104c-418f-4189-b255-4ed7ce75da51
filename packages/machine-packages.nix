{ pkgs }:
with pkgs;
[
  # libimobiledevice_arm64
  {
    package = fetchs3Archive rec {
      pname = "libimobiledevice_arm64";
      s3url = "s3://bs-mobile/ios/libimobiledevice-arm64-${version}.tar.gz";
      sha256 = "1x3nhjmrj1vryzfg32aqpml4xr72hvh6jw390rw4wmzngk4z08f6";
      version = "1";
    };
    destination = "/usr/local/.browserstack/deps/libimobiledevice_arm64";
  }

  # libimobiledevice_x86_64
  {
    package = fetchs3Archive rec {
      pname = "libimobiledevice_x86_64";
      s3url = "s3://bs-mobile/ios/libimobiledevice-x86_64-${version}.tar.gz";
      sha256 = "0yyhrk6cl52xsyszyi2p02q95bzazq8rz1d95402wpv7ih24zymr";
      version = "1";
    };
    destination = "/usr/local/.browserstack/deps/libimobiledevice_x86_64";
  }

  {
    package = fetchs3Archive rec {
      pname = "mitm_ca";
      s3url = "s3://bs-mobile/ios/mitm_ca_ver_${version}.tar.gz";
      sha256 = "0ysg188ldhmvbdaly31xs61qi97jb1mpb3mn4hss30smm1z3c6b3";
      version = "1";
    };
    destination = "/Users/<USER>/.mitmproxy";
  }

  {
    package = fetchs3Archive rec {
      pname = "mitm_5_ca";
      s3url = "s3://bs-mobile/ios/mitm_5_ca_ver_${version}.tar.gz";
      sha256 = "1wkf8f603z6v8mg7lga78p0ch3566vj6v7j43lp2mlybx5ws4pli";
      version = "1";
    };
    destination = "/Users/<USER>/.mitmproxy_5";
  }

  {
    package = fetchs3Archive rec {
      pname = "mitm_5_ca";
      s3url = "s3://bs-mobile/ios/mitm_5_ca_ver_${version}.tar.gz";
      sha256 = "016ria2m6da9ib3l1wp1x2pprs0ml4x2nprmkdb3n9p3knjlfl0f";
      version = "2";
    };
    destination = "/Users/<USER>/.mitmproxy_5_new";
  }
]
