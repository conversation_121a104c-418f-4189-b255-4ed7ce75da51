{ pkgs }:
with pkgs;
[
  # ios_chrome_app
  {
    package = fetchs3Archive rec {
      pname = "ios_chrome_app";
      s3url = "s3://bs-mobile/ios/com.google.chrome.ios-${version}.tar.gz";
      sha256 = "1yfz2jvz1xwxjv95xvjjz18vsw61nln2nwqs9bfh03pp2qmf34z5";
      version = "92";
    };
    destination = "/usr/local/.browserstack/config/apps/cache/com.google.chrome.ios-92.0.4515.90.app";
  }
  {
    package = fetchs3Archive rec {
      pname = "ios_chrome_app";
      s3url = "s3://bs-mobile/ios/com.google.chrome.ios-${version}.tar.gz";
      sha256 = "1fiasgyagj0qy7ychis5v7a7vf6m30snnzx2n088a88bcw73cq2y";
      version = "86";
    };
    destination = "/usr/local/.browserstack/config/apps/cache/com.google.chrome.ios-86.0.4240.93.app";
  }
  # ios_launcher_app
  {
    package = fetchs3Archive rec {
      pname = "ios_launcher_app";
      s3url = "s3://bs-mobile/ios/com.browserstack.Launcher-${version}.tar.gz";
      sha256 = "0s0qkyznj2bn4pl5x2vlanqv57fwacwgvs8hhjm75r4n3ja4i63k";
      version = "1";
    };
    destination = "/usr/local/.browserstack/config/apps/cache/com.browserstack.Launcher-1.app";
  }
  {
    package = fetchs3Archive rec {
      pname = "ios_launcher_app";
      s3url = "s3://bs-mobile/ios/com.browserstack.Launcher-${version}.tar.gz";
      sha256 = "16sd555xzvfny2j7syp2dakrpadw320i5yqka2mizm4ixmfy92yf";
      version = "15";
    };
    destination = "/usr/local/.browserstack/config/apps/cache/com.browserstack.Launcher-15.app";
  }
  {
    package = fetchs3Archive rec {
      pname = "ios_launcher_app";
      s3url = "s3://bs-mobile/ios/com.browserstack.Launcher-${version}.tar.gz";
      sha256 = "04fhg70lzxqijyb6hvrj2y0xnpzdn7phnnrhzsg7mpirb821d1zg";
      version = "17";
    };
    destination = "/usr/local/.browserstack/config/apps/cache/com.browserstack.Launcher-17.app";
  }
  {
    package = fetchs3Archive rec {
      pname = "ios_redirect_extension";
      s3url = "s3://bstack-local-prod/mobile-extensions/com.browserstack.Redirect-${version}.tar.gz";
      sha256 = "2223a440ef7cb0aba8435544f3afb81569b9080428c08d58fcad1e821c603081";
      version = "2";
    };
    destination = "/usr/local/.browserstack/config/apps/cache/com.browserstack.Redirect-2.app";
  }
  # ios_media
  {
    package = fetchs3Archive rec {
      pname = "ios_media";
      s3url = "s3://bs-mobile/ios/media_ios_multiversion_${version}.tar.gz";
      sha256 = "1gdbbr53iskpmdwphawja9nydrwml877s420v6aqprfpf8kvybsv";
      version = "15.6";
    };
    destination = "/usr/local/.browserstack/deps/media_ios_multiversion";
  }

  # sample media for injection
  {
    package = fetchs3Archive rec {
      pname = "sample_media";
      s3url = "s3://bs-mobile/mediainjection/sample_media_${version}.tar.gz";
      sha256 = "18cx3sa44f1ak4kdcmgj92racm0dzjjqbffixywad2jj43zcmpyq";
      version = "1";
    };
    destination = "/usr/local/.browserstack/deps/sample_media/v1";
  }

  # Automate AI Proxy
  {
    package = fetchs3Archive rec {
      pname = "automate-ai-proxy";
      s3url = "s3://bs-mobile/automate-ai-proxy/automate-ai-proxy-v${version}.tar.gz";
      sha256 = "1sxz2lmga55b7ywxs5157nwag0gqyl4gvlynn11jjwvwgnsaby9v";
      version = "1";
    };
    destination = "/usr/local/.browserstack/deps/automate-ai-proxy/v1";
  }

  # Playwright iOS Proxy
  {
    package = fetchs3Archive rec {
      pname = "automate-pwios-proxy";
      s3url = "s3://bs-mobile/automate-pwios-proxy/automate-pwios-proxy-v${version}.tar.gz";
      sha256 = "9bf6a91dfc2649fe7d4fb75ff29ec594d348b5006f9d3bd5431787602e2531c0";
      version = "1";
    };
    destination = "/usr/local/.browserstack/deps/automate-pwios-proxy/v1";
  }
  # Maestro UI Runner
  {
    package = fetchs3Archive rec {
      pname = "maestro_ui_runner_app";
      s3url = "s3://bs-mobile/ios/dev.mobile.maestro-driver-iosUITests-${version}.tar.gz";
      sha256 = "0chdsb9hayw1g7a1l1dcpy928qlfdc9nppy367zzb4j1gwa4387w";
      version = "1";
    };
    destination = "/usr/local/.browserstack/config/apps/cache/dev.mobile.maestro-driver-iosUITests.xctrunner-1.app";
  }

  # Maestro CLI
  {
    package = fetchs3Archive rec {
      pname = "maestro-cli";
      s3url = "s3://bs-mobile/ios/maestro-cli-${version}.tar.gz";
      sha256 = "04sf8jqc0hxhx5lw3q2fgankbpv21s3by4g47rc6g7gpyn6j77b4";
      version = "1";
    };
    destination = "/usr/local/.browserstack/deps/maestro-cli/v1";
  }
  {
    package = fetchs3Archive rec {
      pname = "maestro-cli";
      s3url = "s3://bs-mobile/ios/maestro-cli-${version}.tar.gz";
      sha256 = "1dd6h4nc187jk2j7rd5przbjfxmi863syqgv8g74z1p5apan2wdk";
      version = "2";
    };
    destination = "/usr/local/.browserstack/deps/maestro-cli/v2";
  }
]
