{ pkgs }:
with pkgs;
[
  # browserup_jar
  {
    package = fetchs3 rec {
      name = "browserup_jar";
      s3url = "s3://bs-mobile/ios/percy/browserup-percy-1.0-SNAPSHOT.jar";
      sha256 = "242d0b02065848d646870acd71859ab4cb1932ad874d92976d063f10522fccc5";
    };
    destination = "/usr/local/.browserstack/deps/browserup_jar/browserup.jar";
  }

  # jp keys
  {
    package = fetchs3 rec {
      name = "jackproxy_server_pem";
      s3url = "s3://bs-mobile/ios/percy/jackproxy/server.pem";
      sha256 = "7b5a9c1cd34f85720e914e0c6105acc271e27fca9f82434fba8435bb7ca98b35";
    };
    destination = "/usr/local/.browserstack/deps/percy/jackproxy/keys/server.pem";
  }

  {
    package = fetchs3 rec {
      name = "jackproxy_server_key";
      s3url = "s3://bs-mobile/ios/percy/jackproxy/server.key";
      sha256 = "4848ef75e408c3ab08d9670dd046c97379c3b36684888735ff92c5fa28994be3";
    };
    destination = "/usr/local/.browserstack/deps/percy/jackproxy/keys/server.key";
  }
]
