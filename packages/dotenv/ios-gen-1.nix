{ pkgs, rosetta_pkgs }:

with pkgs;
[
  {
    package = writeTextFile {
      name = ".env";
      text = ''
        PRIVOXY="${privoxy_3_0_29}/bin/privoxy"
        IMAGEMAGICK_CONVERT="${imagemagick}/bin/convert"
        NODE_10="${nodejs-10_x}/bin/node"
        NODE_14="${nodejs-14_x}/bin/node"
        NODE_20="${pkgs.nodejs-20_x}/bin/node"
        DEVICE_LOGGER_NODE="${pkgs.nodejs-14_x}/bin/node"
        PSTREE="${pstree}/bin/pstree"
        FFMPEG="${ffmpeg}/bin/ffmpeg"
        FFPROBE="${ffmpeg}/bin/ffprobe"
        IOS_DEPLOY="${darwin.ios-deploy}/bin/ios-deploy"
      '';
    };
    # to install relative to where `bro packages install` is invoked
    # also can be a full path
    destination = ".env";
  }
]
