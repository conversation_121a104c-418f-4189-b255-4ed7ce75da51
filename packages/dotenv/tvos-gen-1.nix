{ pkgs, rosetta_pkgs }:

with pkgs;

[
  {
    package = writeTextFile {
      name = ".env";
      text = ''
        JAVA_ZULU_16="${jdk}/bin/java"
        PRIVOXY="${privoxy_3_0_29}/bin/privoxy"
        MITMDUMP_5="${rosetta_pkgs.mitmproxy_5}/bin/mitmdump"
        IMAGEMAGICK_CONVERT="${pkgs.imagemagick}/bin/convert"
        NODE_14="${rosetta_pkgs.nodejs-14_x}/bin/node"
        DEVICE_LOGGER_NODE="${pkgs.nodejs-14_x}/bin/node"
        PSTREE="${pstree}/bin/pstree"
        FFMPEG="${ffmpeg}/bin/ffmpeg"
        GO_IOS="${pkgs.go-ios}/bin/go-ios"
      '';
    };
    # to install relative to where `bro install` is invoked
    # also can be a full path
    destination = ".env";
  }
]
