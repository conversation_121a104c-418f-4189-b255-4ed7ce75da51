{ pkgs, rosetta_pkgs }:

with pkgs;
[
  {
    package = writeTextFile {
      name = ".env";
      text = ''
        JAVA_ZULU_16="${jdk}/bin/java"
        PRIVOXY="${privoxy_3_0_29}/bin/privoxy"
        MITMDUMP_5="${rosetta_pkgs.mitmproxy_5}/bin/mitmdump"
        MITMDUMP_10="${mitmproxy_10}/bin/mitmdump"
        SOCAT="${pkgs.socat}/bin/socat"
        IMAGEMAGICK_CONVERT="${pkgs.imagemagick}/bin/convert"
        NODE_20="${pkgs.nodejs-20_x}/bin/node"
        NODE_16="${rosetta_pkgs.nodejs-16_x}/bin/node"
        NODE_14="${rosetta_pkgs.nodejs-14_x}/bin/node"
        DEVICE_LOGGER_NODE="${pkgs.nodejs-14_x}/bin/node"
        PSTREE="${pstree}/bin/pstree"
        FFMPEG="${ffmpeg}/bin/ffmpeg"
        FFPROBE="${ffmpeg}/bin/ffprobe"
        IOS_DEPLOY="${darwin.ios-deploy}/bin/ios-deploy"
      '';
    };
    # to install relative to where `bro install` is invoked
    # also can be a full path
    destination = ".env";
  }
]
