{ pkgs }:
{
  revision,
  sha256,
}:

with pkgs;
let
  GITHUB_TOKEN = builtins.getEnv "GITHUB_TOKEN";
in
stdenv.mkDerivation {
  name = "privoxy";
  src = fetchzip {
    inherit sha256;
    url = "https://github.com/browserstack/terminalDeploy/archive/${revision}.tar.gz";
    curlOpts = [ ''-H @${writeText "headers.txt" "Authorization: token ${GITHUB_TOKEN}"}'' ];
  };
  phases = [ "installPhase" ];
  installPhase = ''
    mkdir -p $out
    echo "BROWSERSTACK DEFAULT TEMPLATE FILE" > $out/default
    cp $src/browserProxyForTunnel/privoxy/templates/forwarding-failed_non_local $out/forwarding-failed_non_local
    for template in blocked connect-failed forwarding-failed no-server-data no-such-domain
    do
      cp $src/browserProxyForTunnel/privoxy/templates/forwarding-failed_default $out/$template
    done
  '';
  inherit revision;
}
