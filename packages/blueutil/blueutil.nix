{ pkgs }:
with pkgs;
{
  revision,
  sha256
}:
let
  GITHUB_TOKEN = builtins.getEnv "GITHUB_TOKEN";
in
stdenv.mkDerivation rec {
  name = "blueutil";
  src = fetchzip {
    inherit sha256;
    url = "https://github.com/browserstack/ios-browser-launcher/archive/${revision}.tar.gz";
    curlOpts = [ ''-H @${writeText "headers.txt" "Authorization: token ${GITHUB_TOKEN}"}'' ];
  };
  sourceRoot="source/iOSInteractionServerRefactored/blueutil";
  buildInputs = (with darwin.apple_sdk.frameworks; [ IOBluetooth Foundation ]);
  nativeBuildInputs = [ xcbuild6Hook ];
  installPhase = ''
    for f in Products/Release/*; do
      if [ -f $f ]; then
        install -D $f $out/bin/$(basename $f)
      fi
    done
  '';
  inherit revision;
}
