{ pkgs }:
with pkgs;

let
  old_pkgs = import (fetchzip {
      url = "https://releases.nixos.org/nixpkgs/20.09-darwin/nixpkgs-darwin-20.09pre246842.c5429e84111/nixexprs.tar.xz";
      sha256 = "0xccyirqas1pbl9h0mhb3nbz8yds2f4ijhy7kh0i0bh0hb0z2c46";
  }) {};
in
stdenv.mkDerivation rec {
  version = "5.6.0";
  pname = "opencv";
  phases = [ "installPhase" ];
  buildInputs = [ python nodejs-14_x ];
  propagatedBuildInputs = [ old_pkgs.opencv4 ];
  OPENCV4NODEJS_DISABLE_AUTOBUILD="1";
  OPENCV_LIB_DIR="${old_pkgs.opencv4}/lib";
  OPENCV_INCLUDE_DIR="${old_pkgs.opencv4}/include/opencv4/";
  installPhase = ''
    mkdir -p $out
    cd $out
    chmod -R +w .
    export HOME=$TMPDIR
    npm i opencv4nodejs@${version}
  '';
}
