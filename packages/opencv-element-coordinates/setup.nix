{ pkgs }:

with pkgs;
let
  opencv = callPackage ./opencv.nix {};
in
stdenv.mkDerivation {
  name = "opencv_element_coordinates";
  src = ../../scripts/opencv-element-coordinates;
  phases = [ "installPhase" ];
  installPhase = ''
    mkdir -p $out
    mkdir -p $out/node_modules
    cp -R $src/* $out
    chmod +w $out/node_modules
    ln -sf ${opencv}/node_modules/opencv4nodejs $out/node_modules/opencv4nodejs
  '';
}
