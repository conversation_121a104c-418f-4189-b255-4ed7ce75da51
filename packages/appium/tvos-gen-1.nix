{ pkgs, native_pkgs }:
let
  appium = pkgs.callPackage ./appium.nix { inherit native_pkgs; };
  node10 = pkgs.nodejs-10_x;
  node14 = pkgs.nodejs-14_x;
in [
  {
    package = appium {
      revision = "b9161986f98af0a8caaeb606af1cc5c8b6d0b5f9";
      sha256 = "17hhkdmzj6cn16bidkdqf4iswkdr78szf359kmyzd172kgzsa1zg";
      version = "2.0.0";
      nodeVersion = node14;
    };
    destination = "/usr/local/.browserstack/appium_2.0.0_bstack";
  }
]
