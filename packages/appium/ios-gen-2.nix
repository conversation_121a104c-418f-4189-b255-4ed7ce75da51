{ pkgs, native_pkgs }:
let
  appium = pkgs.callPackage ./appium.nix { inherit native_pkgs; };
  node10 = pkgs.nodejs-10_x;
  node14 = pkgs.nodejs-14_x;
in [
  {
    package = appium {
      revision = "0832891bf881c95193698dbd2447eebefc4f6d74";
      sha256 = "1afck6iq8f6qzkz6ypnvygkfjwq3i9wfh7n3wjwv7fsfik2y9fij";
      version = "1.9.1";
      nodeVersion = node10;
    };
    destination = "/usr/local/.browserstack/appium_1.9.1_bstack";
  }
  {
    package = appium {
      revision = "c1a0bb59d6816a70a1410195e1b9f454c3e09a73";
      sha256 = "1lgg3c572m6pr0bf71029xw8q9p830272i2gdvkw741ag5bayf68";
      version = "1.10.1";
      nodeVersion = node10;
    };
    destination = "/usr/local/.browserstack/appium_1.10.1_bstack";
  }
  {
    package = appium {
      revision = "28b3d6d158fb0a6d2f0f64fe5fb77391a323b0d6";
      sha256 = "0cd9x4ir3h200604mpjsdjrb3fnx4jxwprxrq0wjqb8z2dn8nh6m";
      version = "1.11.1";
      nodeVersion = node10;
    };
    destination = "/usr/local/.browserstack/appium_1.11.1_bstack";
  }
  {
    package = appium {
      revision = "bf400db9923a51c3532db6d81ba4903bf5f4e792";
      sha256 = "0klri2wlrwfc3hqh3mii59cq9l715mj7sdiq99mab26bvzcq0kqs";
      version = "1.12.1";
      nodeVersion = node10;
    };
    destination = "/usr/local/.browserstack/appium_1.12.1_bstack";
  }
  {
    package = appium {
      revision = "d05d7c2ff26cd3a200f14224c15f499218542a52";
      sha256 = "12ga819c0pjj8gd9a6gc6sbqnlamgn3i8zbnz7vsvm0vq4ryjdl7";
      version = "1.13.0";
      nodeVersion = node10;
    };
    destination = "/usr/local/.browserstack/appium_1.13.0_bstack";
  }
  {
    package = appium {
      revision = "9d082fcce4a531ac1c0600b90266bfcf5cb73df1";
      sha256 = "1gfn6q1fqb2l1wszikh6ryiw6srwws629q0iwhdsad3llbbc1vn1";
      version = "1.14.0";
      nodeVersion = node10;
    };
    destination = "/usr/local/.browserstack/appium_1.14.0_bstack";
  }
  {
    package = appium {
      revision = "9b978f60fcbfe7d1c16de3b4d7e7081b361a02f2";
      sha256 = "0kmbyqsiqc46749ddwxndv99cgkf9kwxwddji91kjybc5qfyn712";
      version = "1.15.0";
      nodeVersion = node10;
    };
    destination = "/usr/local/.browserstack/appium_1.15.0_bstack";
  }
  {
    package = appium {
      revision = "f99e5fafbb66dad7d4394cc6b9e9762929960497";
      sha256 = "1p37kacxlizpnxcsbxmdbv47al7rz1r9d0vq2885lnh4rf74biy8";
      version = "1.16.0";
      nodeVersion = node10;
    };
    destination = "/usr/local/.browserstack/appium_1.16.0_bstack";
  }
  {
    package = appium {
      revision = "e6955d34022de2bc6f91c96de78bd7d355daf94b";
      sha256 = "0yywp7cwqrg8qn5k4l6hrvfrsv3i6xay0khbchfrlmvfh0k2f5ds";
      version = "1.17.0";
      nodeVersion = node10;
    };
    destination = "/usr/local/.browserstack/appium_1.17.0_bstack";
  }
  {
    package = appium {
      revision = "49b5e4567599e1c6d94c5c7f72527704687987ca";
      sha256 = "1cry2zrk34w3hhi0m13a55x3shgm34q2jhb3s4s6iyx238ih2fr0";
      version = "1.18.0";
      nodeVersion = node10;
    };
    destination = "/usr/local/.browserstack/appium_1.18.0_bstack";
  }
  {
    package = appium {
      revision = "c2023139e2f07ee97ef304beb5b6ab2c3c4f521a";
      sha256 = "0d2dlxkdcz0w2lks1mqcsgnc8praxngab4bzk3ialq7j4ib0k8vj";
      version = "1.19.1";
      nodeVersion = node10;
    };
    destination = "/usr/local/.browserstack/appium_1.19.1_bstack";
  }
  {
    package = appium {
      revision = "684f48e5dc5a81aa1b302e4a187a72da7bbaa732";
      sha256 = "01qvgyjynv266063g4pwwg4azasxglwj4amn1a6kf6n7z737jcsk";
      version = "1.20.2";
      nodeVersion = node10;
    };
    destination = "/usr/local/.browserstack/appium_1.20.2_bstack";
  }
  {
    package = appium {
      revision = "c30653be533e5d95b99939956f7f40b6f28bd0d6";
      sha256 = "1k8mi75vx0wrlmwb3jrv149qn3rlmakn8ykxr09673kallxjbj2g";
      version = "1.21.0";
      nodeVersion = node14;
    };
    destination = "/usr/local/.browserstack/appium_1.21.0_bstack";
  }
  {
    package = appium {
      revision = "551c3897a6fe19067fcb7996b021aca370ba26a0";
      sha256 = "0miimnxayq87my86mvj2q2wr6m0c0xy9hcc9vyl9jssickj0jj7c";
      version = "1.22.0";
      nodeVersion = node14;
    };
    destination = "/usr/local/.browserstack/appium_1.22.0_bstack";
  }
]
