{ pkgs, native_pkgs }:
let
  appium = pkgs.callPackage ./appium.nix { inherit native_pkgs; };
  node10 = pkgs.nodejs-10_x;
  node14 = pkgs.nodejs-14_x;
  node16 = pkgs.nodejs-16_x;
in [
  {
    package = appium {
      revision = "2a28281e2c2eff52a89b8d5870fc056669427b61";
      sha256 = "0yz7d3n8dymdwcss8dbnysa1bgp0c2yzcw0gdc9r5n1vqhv9m37s";
      version = "1.17.0";
      nodeVersion = node10;
    };
    destination = "/usr/local/.browserstack/appium_1.17.0_bstack";
  }
  {
    package = appium {
      revision = "49b5e4567599e1c6d94c5c7f72527704687987ca";
      sha256 = "1cry2zrk34w3hhi0m13a55x3shgm34q2jhb3s4s6iyx238ih2fr0";
      version = "1.18.0";
      nodeVersion = node10;
    };
    destination = "/usr/local/.browserstack/appium_1.18.0_bstack";
  }
  {
    package = appium {
      revision = "c2023139e2f07ee97ef304beb5b6ab2c3c4f521a";
      sha256 = "0d2dlxkdcz0w2lks1mqcsgnc8praxngab4bzk3ialq7j4ib0k8vj";
      version = "1.19.1";
      nodeVersion = node10;
    };
    destination = "/usr/local/.browserstack/appium_1.19.1_bstack";
  }
  {
    package = appium {
      revision = "684f48e5dc5a81aa1b302e4a187a72da7bbaa732";
      sha256 = "01qvgyjynv266063g4pwwg4azasxglwj4amn1a6kf6n7z737jcsk";
      version = "1.20.2";
      nodeVersion = node10;
    };
    destination = "/usr/local/.browserstack/appium_1.20.2_bstack";
  }
  {
    package = appium {
      revision = "0f30b640da2054515e49623b1013a9f022b15cc3";
      sha256 = "0834kkg5fb7b0ifax4bpm9xfkn3v2cqz5vy116797ym6nb0cfv2r";
      version = "1.21.0";
      nodeVersion = node14;
    };
    destination = "/usr/local/.browserstack/appium_1.21.0_bstack";
  }
  {
    package = appium {
      revision = "551c3897a6fe19067fcb7996b021aca370ba26a0";
      sha256 = "0miimnxayq87my86mvj2q2wr6m0c0xy9hcc9vyl9jssickj0jj7c";
      version = "1.22.0";
      nodeVersion = node14;
    };
    destination = "/usr/local/.browserstack/appium_1.22.0_bstack";
  }
  {
    package = appium {
      revision = "aef2769baf20a2a3384b2d9da8af7a8678b9c212";
      sha256 = "015wmvxwsski1kwazj8l9cp14b0lnwc9pp18jcjna6lkamcnnbw3";

      version = "2.0.0";
      nodeVersion = node14;
    };
    destination = "/usr/local/.browserstack/appium_2.0.0_bstack";
  }
  {
    package = appium {
      revision = "c1c1fff4672dfecadf8ff64d1df1ab8cc4e1d51e";
      sha256 = "1cym8f6gw6jlqjkv32d3mvkrw28zy0336q90z5zq0h08liv40q67";

      version = "2.0.1";
      nodeVersion = node16;
    };
    destination = "/usr/local/.browserstack/appium_2.0.1_bstack";
  }
]
