{ pkgs, native_pkgs }:
let
  appium = pkgs.callPackage ./appium.nix { inherit native_pkgs; };
  node10 = pkgs.nodejs-10_x;
  node14 = pkgs.nodejs-14_x;
in [
  {
    package = appium {
      revision = "a167655c71ecb95862793a7adee9cbc6bec99c4e";
      sha256 = "1g6j1k54j27nnmki4siw4f1r8h4rmxg267fgbk3jncc5mg0cs340";
      version = "1.6.5";
      nodeVersion = node10;
    };
    destination = "/usr/local/.browserstack/appium_1.6.5_bstack";
  }
  {
    package = appium {
      revision = "461d92379bf76f3572598ea0883e92498a73dfc8";
      sha256 = "1fk6m4271345cjdlxy8hh5id2nn968vmx86y5yv3gpga2sqw93xy";
      version = "1.7.0";
      nodeVersion = node10;
    };
    destination = "/usr/local/.browserstack/appium_1.7.0_bstack";
  }
  {
    package = appium {
      revision = "fc567d2b6d866771bb30ac7a36c132d0f4a8526b";
      sha256 = "0vf1gdavrqcl7wj513v60nk40cijiqvl4fc9vgbfjvzyn4rl4mbc";
      version = "1.7.1";
      nodeVersion = node10;
    };
    destination = "/usr/local/.browserstack/appium_1.7.1_bstack";
  }
  {
    package = appium {
      revision = "641f068218b47dcb85d6dd6ecd3b99801dd098ed";
      sha256 = "06x6429pi07k7aisn31qs6v2p0zbpxpq31kmnnvdvqy1vndsxdqf";
      version = "1.7.2";
      nodeVersion = node10;
    };
    destination = "/usr/local/.browserstack/appium_1.7.2_bstack";
  }
  {
    package = appium {
      revision = "1ab6269d3f695109e44dcd49b0d4b2fc815386bf";
      sha256 = "126gdf24sz9y9vxizblw4w2m2ji7sz3n4rpi2dslpy7997384igk";
      version = "1.8.0";
      nodeVersion = node10;
    };
    destination = "/usr/local/.browserstack/appium_1.8.0_bstack";
  }
  {
    package = appium {
      revision = "75b76dd807d158577055f5a276026537df571df9";
      sha256 = "1s59gk9aqdcfa6crm6zz7kqrrjwyrkrdyvv0f8k9sxdpxv22g0js";
      version = "1.9.1";
      nodeVersion = node10;
    };
    destination = "/usr/local/.browserstack/appium_1.9.1_bstack";
  }
  {
    package = appium {
      revision = "c1a0bb59d6816a70a1410195e1b9f454c3e09a73";
      sha256 = "1lgg3c572m6pr0bf71029xw8q9p830272i2gdvkw741ag5bayf68";
      version = "1.10.1";
      nodeVersion = node10;
    };
    destination = "/usr/local/.browserstack/appium_1.10.1_bstack";
  }
  {
    package = appium {
      revision = "28b3d6d158fb0a6d2f0f64fe5fb77391a323b0d6";
      sha256 = "0cd9x4ir3h200604mpjsdjrb3fnx4jxwprxrq0wjqb8z2dn8nh6m";
      version = "1.11.1";
      nodeVersion = node10;
    };
    destination = "/usr/local/.browserstack/appium_1.11.1_bstack";
  }
  {
    package = appium {
      revision = "bf400db9923a51c3532db6d81ba4903bf5f4e792";
      sha256 = "0klri2wlrwfc3hqh3mii59cq9l715mj7sdiq99mab26bvzcq0kqs";
      version = "1.12.1";
      nodeVersion = node10;
    };
    destination = "/usr/local/.browserstack/appium_1.12.1_bstack";
  }
  {
    package = appium {
      revision = "d05d7c2ff26cd3a200f14224c15f499218542a52";
      sha256 = "12ga819c0pjj8gd9a6gc6sbqnlamgn3i8zbnz7vsvm0vq4ryjdl7";
      version = "1.13.0";
      nodeVersion = node10;
    };
    destination = "/usr/local/.browserstack/appium_1.13.0_bstack";
  }
  {
    package = appium {
      revision = "f1cd5d185ea17184aac4693591a280afddb18f94";
      sha256 = "0i4bdb2r02g5ks6mkxm6v5lgfa23vm5r8bwyvxkv58m3d62yfkz2";
      version = "1.14.0";
      nodeVersion = node10;
    };
    destination = "/usr/local/.browserstack/appium_1.14.0_bstack";
  }
  {
    package = appium {
      revision = "9b978f60fcbfe7d1c16de3b4d7e7081b361a02f2";
      sha256 = "0kmbyqsiqc46749ddwxndv99cgkf9kwxwddji91kjybc5qfyn712";
      version = "1.15.0";
      nodeVersion = node10;
    };
    destination = "/usr/local/.browserstack/appium_1.15.0_bstack";
  }
  {
    package = appium {
      revision = "f99e5fafbb66dad7d4394cc6b9e9762929960497";
      sha256 = "1p37kacxlizpnxcsbxmdbv47al7rz1r9d0vq2885lnh4rf74biy8";
      version = "1.16.0";
      nodeVersion = node10;
    };
    destination = "/usr/local/.browserstack/appium_1.16.0_bstack";
  }
]
