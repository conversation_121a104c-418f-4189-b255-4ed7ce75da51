{ pkgs, native_pkgs }:
let
  appium = pkgs.callPackage ./appium.nix { inherit native_pkgs; };
  node10 = pkgs.nodejs-10_x;
  node14 = pkgs.nodejs-14_x;
  node16 = pkgs.nodejs-16_x;
  node20 = pkgs.nodejs-20_x;
in [
  {
    package = appium {
      revision = "4f5787c1e984fe7b1c5c5d6d4e84e9019d4135bb";
      sha256 = "01214irj31dz2553vd2w774cqgk2asskal57cqjsypzmablpx1r2";
      version = "2.15.0.8.3.3";
      nodeVersion = node14;
    };
    destination = "/usr/local/.browserstack/appium_2.15.0_bstack";
  }
]
