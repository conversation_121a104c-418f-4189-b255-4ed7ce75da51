{ pkgs, native_pkgs }:
let
  appium = pkgs.callPackage ./appium.nix { inherit native_pkgs; };
  node10 = pkgs.nodejs-10_x;
  node14 = pkgs.nodejs-14_x;
  node16 = pkgs.nodejs-16_x;
  node20 = pkgs.nodejs-20_x;
in [
  {
    package = appium {
      revision = "0f30b640da2054515e49623b1013a9f022b15cc3";
      sha256 = "0834kkg5fb7b0ifax4bpm9xfkn3v2cqz5vy116797ym6nb0cfv2r";
      version = "1.21.0";
      nodeVersion = node14;
    };
    destination = "/usr/local/.browserstack/appium_1.21.0_bstack";
  }
  {
    package = appium {
      revision = "f8341d12a6e26f55cdec0613ef5cc3945afa492c";
      sha256 = "0mlpv4vrik0zkw8gn52751p376ga5i5lzn7xylzkkag1q115yc0q";
      version = "1.22.0";
      nodeVersion = node14;
    };
    destination = "/usr/local/.browserstack/appium_1.22.0_bstack";
  }
  {
    package = appium {
      revision = "aef2769baf20a2a3384b2d9da8af7a8678b9c212";
      sha256 = "015wmvxwsski1kwazj8l9cp14b0lnwc9pp18jcjna6lkamcnnbw3";
      version = "2.0.0";
      nodeVersion = node14;
    };
    destination = "/usr/local/.browserstack/appium_2.0.0_bstack";
  }
  {
    package = appium {
      revision = "c1c1fff4672dfecadf8ff64d1df1ab8cc4e1d51e";
      sha256 = "1cym8f6gw6jlqjkv32d3mvkrw28zy0336q90z5zq0h08liv40q67";

      version = "2.0.1";
      nodeVersion = node16;
    };
    destination = "/usr/local/.browserstack/appium_2.0.1_bstack";
  }
  {
    package = appium {
      revision = "7a1e9c6b6282d32a0fbd134060ebc6ce2e27e2e3";
      sha256 = "0gyhsi76xqwnr11d7y2glcnf5zpy3i64g7bml2m4lzhan7dcybd1";
      version = "2.4.1";
      nodeVersion = node16;
    };
    destination = "/usr/local/.browserstack/appium_2.4.1_bstack";
  }
  {
    package = appium {
      revision = "c8d30c748dd73e60e1da5842fe7924e027aca14d";
      sha256 = "0qk6fsm42pw0jf8r4czcmvmn157rnqi04gwy0iv07qbrdshb1570";

      version = "2.6.0";
      nodeVersion = node20;
    };
    destination = "/usr/local/.browserstack/appium_2.6.0_bstack";
  }
  {
    package = appium {
      revision = "c620b9a511b990f44b4161343baa4dae0693f353";
      sha256 = "0nha6ikjzkxbisg0bk9qqfaa64ajir2ibddfk4lkrsh5wbb3f8y5";
      version = "2.12.1";
      nodeVersion = node20;
    };
    destination = "/usr/local/.browserstack/appium_2.12.1_bstack";
  }
  {
    package = appium {
      revision = "7e5d3c7b1f3e1324b67e8a25e67e9978cfeafadf";
      sha256 = "0xs6mm0cxx2ginzp88r75348dm49gd29wgp4mzva8dqx26hm1p8z";
      version = "2.15.0";
      nodeVersion = node20;
    };
    destination = "/usr/local/.browserstack/appium_2.15.0_bstack";
  }
]
