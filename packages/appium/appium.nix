{ pkgs, native_pkgs }:
with pkgs;
{
  revision,
  sha256,
  version,
  nodeVersion
}:
let
  GITHUB_TOKEN = builtins.getEnv "GITHUB_TOKEN";
  opencv = callPackage ./opencv.nix { } { inherit nodeVersion; };
in
native_pkgs.stdenv.mkDerivation {
  pname = "appium";
  src = fetchzip {
    inherit sha256;
    url = "https://github.com/browserstack/appium-njb/archive/${revision}.tar.gz";
    curlOpts = [ ''-H @${writeText "headers.txt" "Authorization: token ${GITHUB_TOKEN}"}'' ];
  };
  phases = [ "installPhase" ];
  installPhase = ''
    mkdir -p $out
    cp -R $src/* $out
    chmod +w $out/node_modules
    echo ${revision} > $out/commit_id
  ''+ lib.optionalString (builtins.compareVersions version "1.14.0" == 1) ''
    ln -sf ${opencv}/node_modules/opencv4nodejs $out/node_modules/opencv4nodejs
  '';
  inherit version;
}
