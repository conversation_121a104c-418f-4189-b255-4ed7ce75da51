# This configuration was generated by
# `rubocop --auto-gen-config`
# on 2022-10-11 06:22:09 UTC using RuboCop version 1.22.2.
# The point is for the user to remove these configuration records
# one by one as the offenses are removed from the code base.
# Note that changes in the inspected code, or installation of new
# versions of RuboCop, may require this file to be generated again.

# Offense count: 3
# Configuration parameters: AllowedMethods.
# AllowedMethods: enums
Lint/ConstantDefinitionInBlock:
  Exclude:
    - 'spec/lib/utils/http_utils_spec.rb'
    - 'spec/lib/utils/time_recorder_spec.rb'

# Offense count: 1
Lint/ImplicitStringConcatenation:
  Exclude:
    - 'lib/video_utility.rb'

# Offense count: 27
Lint/RescueException:
  Exclude:
    - 'lib/apps/chrome.rb'
    - 'lib/checks/check_device.rb'
    - 'lib/checks/machine.rb'
    - 'lib/cleanup_telephony.rb'
    - 'lib/dummy_app_trust.rb'
    - 'lib/session/app_live_session.rb'
    - 'lib/session/xctest_session.rb'
    - 'lib/utils/utils.rb'
    - 'scripts/inventory_push.rb'
    - 'scripts/privoxy_push.rb'
    - 'server/device_manager.rb'
    - 'server/server.rb'

# Offense count: 13
Lint/SelfAssignment:
  Exclude:
    - 'lib/utils/utils.rb'
    - 'spec/lib/utils/utils_spec.rb'

# Offense count: 155
Lint/UselessAssignment:
  Enabled: false

# Offense count: 4
# Configuration parameters: CheckForMethodsWithNoSideEffects.
Lint/Void:
  Exclude:
    - 'lib/video_utility.rb'

# Offense count: 10
# Configuration parameters: CountBlocks.
Metrics/BlockNesting:
  Max: 4

# Offense count: 47
# Configuration parameters: CountKeywordArgs.
Metrics/ParameterLists:
  MaxOptionalParameters: 6
  Max: 22

# Offense count: 33
Naming/AccessorMethodName:
  Exclude:
    - 'lib/checks/machine.rb'
    - 'lib/cleanup_iphone.rb'
    - 'lib/erb_binding.rb'
    - 'lib/helpers/wda_client.rb'
    - 'lib/ios_vid_capturer.rb'
    - 'lib/network_simulator.rb'
    - 'lib/session/xctest_session.rb'
    - 'lib/utils/osutils.rb'
    - 'lib/utils/xcode_utils.rb'
    - 'lib/video_utility.rb'
    - 'scripts/device_thread.rb'
    - 'scripts/proxy_check.rb'
    - 'server/iphone.rb'

# Offense count: 25
# Configuration parameters: MinNameLength, AllowNamesEndingInNumbers, AllowedNames, ForbiddenNames.
# AllowedNames: at, by, db, id, in, io, ip, of, on, os, pp, to
Naming/MethodParameterName:
  Exclude:
    - 'lib/helpers/wda_client.rb'
    - 'lib/session/xctest_session.rb'
    - 'lib/utils/app_settings_util.rb'
    - 'lib/utils/xcuitest_summaries_parser.rb'
    - 'server/iphone.rb'
    - 'spec/helpers/mock_ios_vid_capturer.rb'
    - 'spec/helpers/mock_logger.rb'

# Offense count: 22
# Configuration parameters: NamePrefix, ForbiddenPrefixes, AllowedMethods, MethodDefinitionMacros.
# NamePrefix: is_, has_, have_
# ForbiddenPrefixes: is_, has_, have_
# AllowedMethods: is_a?
# MethodDefinitionMacros: define_method, define_singleton_method
Naming/PredicateName:
  Exclude:
    - 'spec/**/*'
    - 'lib/dummy_app_trust.rb'
    - 'lib/network_simulator.rb'
    - 'lib/session/xctest_session.rb'
    - 'lib/utils/http_utils.rb'
    - 'lib/utils/idevice_utils.rb'
    - 'lib/utils/ios_mdm_service_client.rb'
    - 'lib/utils/osutils.rb'
    - 'lib/utils/utils.rb'
    - 'server/device_manager.rb'
    - 'server/iphone.rb'

# Offense count: 240
# Configuration parameters: EnforcedStyle, AllowedIdentifiers.
# SupportedStyles: snake_case, camelCase
Naming/VariableName:
  Enabled: false

# Offense count: 14
# Configuration parameters: EnforcedStyle, CheckMethodNames, CheckSymbols, AllowedIdentifiers.
# SupportedStyles: snake_case, normalcase, non_integer
# AllowedIdentifiers: capture3, iso8601, rfc1123_date, rfc822, rfc2822, rfc3339
Naming/VariableNumber:
  Exclude:
    - 'lib/mitm_proxy.rb'
    - 'lib/utils/idevice_utils.rb'
    - 'lib/utils/utils.rb'
    - 'spec/lib/device_setup/install_check_spec.rb'
    - 'spec/lib/utils/osutils_spec.rb'

# Offense count: 1
Style/CombinableLoops:
  Exclude:
    - 'lib/checks/machine.rb'

# Offense count: 137
# Configuration parameters: AllowedConstants.
Style/Documentation:
  Enabled: false

# Offense count: 34
# Configuration parameters: AllowedVariables.
Style/GlobalVars:
  Exclude:
    - 'lib/utils/helpers.rb'
    - 'lib/utils/hooter.rb'
    - 'scripts/file_converter_process.rb'

# Offense count: 114
# Configuration parameters: MinBodyLength.
Style/GuardClause:
  Enabled: false

# Offense count: 14
Style/MixinUsage:
  Exclude:
    - 'lib/privoxy_push_repeater.rb'
    - 'scripts/file_converter_process.rb'
    - 'server/device_manager.rb'
    - 'server/device_server.rb'
    - 'server/server.rb'
    - 'spec/helpers/mock_logger.rb'
    - 'spec/server/device_manager_spec.rb'
    - 'spec/server/server_spec.rb'

# Offense count: 55
# Configuration parameters: AllowedMethods.
# AllowedMethods: respond_to_missing?
Style/OptionalBooleanParameter:
  Enabled: false

# Offense count: 2185
# Cop supports --auto-correct.
# Configuration parameters: AllowHeredoc, AllowURI, URISchemes, IgnoreCopDirectives, IgnoredPatterns.
# URISchemes: http, https
Layout/LineLength:
  Max: 465
