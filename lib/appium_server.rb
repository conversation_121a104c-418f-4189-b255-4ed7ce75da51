require 'appium_lib'
require_relative './configuration'
require_relative './checks/check_plist'
require_relative './utils/utils'
require_relative './utils/osutils'
require_relative './utils/http_utils'
require_relative './utils/iproxy'
require_relative './utils/idevice_utils'
require_relative './utils/web_driver_agent'
require_relative './utils/zombie'
require_relative './custom_exceptions'
require_relative '../server/device_manager'
require_relative './utils/time_recorder'
require_relative '../config/constants'
require_relative './models/device_state'
require_relative './helpers/wda_client'
require_relative './utils/apple_tv_utils'

# This class takes care of everything around Appium server for one device from starting it
# and keeping it alive, to creating a driver for it and also killing it, etc...
# --
# This class is NOT responsible for the UI Automation commands sent through Appium to the device
module BrowserStack
  class AppiumServer # rubocop:todo Metrics/ClassLength
    include BrowserStack::TimeRecorder
    time_methods :driver
    time_methods :start_server_for_version, threshold: 0.4

    attr_reader :wda_port

    def initialize(device_id, device_config = nil, check_running: true)
      device_config ||= DeviceManager.device_configuration_check(device_id)
      BrowserStack::AppiumServer.verify_server_args(device_id, device_config)
      @device_id = device_id
      @appium_version = device_config['current_appium_version']
      @device_name = device_config['device_name']
      @device_version = device_config['device_version']
      @appium_port = device_config['selenium_port']
      @wda_port = device_config['webdriver_port']
      @webkit_port = device_config['debugger_port']
      @node_path = device_config['node_path']
      @platform_version = device_config['platform_version']
      @config = BrowserStack::Configuration.new.all
      @server_url = "http://127.0.0.1:#{@appium_port}/wd/hub"
      @device_state = DeviceState.new(@device_id)
      @platform = IdeviceUtils.os(@device_id) == 'tvos' ? "tvOS" : "iOS"
      @default_caps = {
        platformName: @platform,
        url: @server_url,
        deviceName: @device_name,
        automationName: 'XCUITest',
        orientation: 'PORTRAIT',
        showXcodeLog: false,
        wdaStartupRetries: 2 # For older appium versions, as this is the default for newer versions.
      }

      return unless check_running

      begin
        running?
      rescue AppiumServerError
        BrowserStack.logger.warn "AppiumServer not running on port #{@appium_port} on AppiumServer.new, starting now."
        start_server_for_version(@appium_version)
      end
    end

    # Verifies the arguments passed to .new
    def self.verify_server_args(device_id, device_config)
      raise ArgumentError, "device_id must be a non-empty string" if device_id.nil? || device_id.empty?

      raise ArgumentError, "device_config must be a non-empty hash" if device_config.nil? || device_config.empty?

      required_keys = ["current_appium_version",
                       "device_name",
                       "device_version",
                       "selenium_port",
                       "webdriver_port",
                       "debugger_port"]
      missing_keys = required_keys.reject { |key| device_config.key?(key) && !(device_config[key].nil? || device_config[key].to_s.empty?) }
      raise ArgumentError, "device_config is missing the keys #{missing_keys}" unless missing_keys.empty?
    end

    # This checks if Appium server is running on port, retries multiple times if necessary
    def running?(max_attempts = 10)
      BrowserStack.logger.info "Checking if Appium server is running on port #{@appium_port}"
      running = false
      attempts = 0
      until running
        status_response = BrowserStack::HttpUtils.make_get_request("#{@server_url}/status", 2)
        running = status_response.success? && status_response.status == 200

        next if running
        raise AppiumServerError, "Appium server not running after #{max_attempts} retries" if attempts > max_attempts

        attempts += 1
        sleep 2
      end

      AppleTVUtils.verify_wda_running(@device_id) if apple_tv_device?
      true
    end

    # This deletes the xctestrun file previously created by appium if it's empty
    # Note: This is done as a preventive measure, we still need to understand why the file is getting empty
    def ensure_xctestrun_file_not_empty
      wda_version = @config['default_wda_version'] || @appium_version
      wda_xctestrun_folder = BrowserStack::WebDriverAgent.xctestrun_file_path(@device_id, wda_version, @device_version)
      possible_versions = ['null'] # TODO: Understand why Appium uses null and remove this
      platform_version = @platform_version || XcodeUtils.get_platform_version
      possible_versions << platform_version
      possible_versions.each do |version|
        filename = "#{wda_xctestrun_folder}/#{@device_id}_#{version}.xctestrun"
        next if !File.exists?(filename) || File.size(filename) > 0

        BrowserStack.logger.warn "#{filename} is empty, deleting."
        BrowserStack::Zombie.push_logs("empty-xctestrun-file", filename, { "version" => @device_version })
        File.delete(filename)
      end
    end

    # This returns a driver or raise the proper error why it wasn't able to give one
    # By default the driver will be for settings app
    # rubocop:disable Metrics/AbcSize            # (and MethodLength if it fires)
    def driver(caps = { app: 'settings' }, timeout = 300)
      caps[:app] ||= 'com.apple.TVPhotos' if apple_tv_device? && @device_version.to_i >= 18
      BrowserStack.logger.info "Getting a driver for appium server on port #{@appium_port}"

      options = {
        caps: @default_caps.merge(caps),
        appium_lib: {
          port: @appium_port
        }
      }

      BrowserStack.logger.info "Options required for driver: #{caps}"
      driver = Appium::Driver.new(options, false) # Drivers cannot be global because we're using them in different threads.
      begin
        BrowserStack.logger.info "Starting driver with timeout #{timeout}s"
        ensure_xctestrun_file_not_empty
        driver.start_driver(http_client: ::Appium::Http::Default.new(read_timeout: timeout))
        BrowserStack.logger.info "Succesfully started driver for appium server on port #{@appium_port}"
        @device_state.remove_resign_wda_file
      rescue Net::ReadTimeout
        BrowserStack.logger.error "Starting driver timed out after #{timeout}s"
        raise AppiumServerError, "start driver timed out"
      rescue Selenium::WebDriver::Error::UnknownError, EOFError, RuntimeError => e
        BrowserStack.logger.error "Starting driver failed with known error: #{e.class} - #{e.backtrace.join("\n")}"
        better_error_message = transform_error_message(e.message)
        BrowserStack.logger.error "Appium Server Error: #{better_error_message}"

        case better_error_message
        when /unable to upgrade/
          BrowserStack.logger.error "Uninstalling WDA to fix issue"
          IdeviceUtils.uninstall_app(@device_id, "com.facebook.WebDriverAgentRunner.xctrunner")
        when /app signed with invalid profile/, /xctestrun/, /xcodebuild/
          BrowserStack.logger.error "Will Resign WDA"
          @device_state.touch_resign_wda_file # Touch this file to resign, might fix xcodebuild 65 errors
        else
          BrowserStack.logger.error "Not sure how to fix the problem"
        end

        raise AppiumServerError, "start driver failed: #{better_error_message}"
      rescue => e
        app = ", app:#{caps[:app]}" if caps[:app]
        raise AppiumServerError, "#{e.class} for appium server start_driver#{app}"
      end
      driver
    end
    # rubocop:enable Metrics/AbcSize

    # Transform .start_driver error messages into more intelligible messages
    def transform_error_message(error_message) # rubocop:disable Metrics/MethodLength
      error_message = "server not running or unreachable" if error_message =~ /unable to connect to appium/i
      error_message = "server stopped unexpectedly" if error_message =~ /end of file reached/i
      error_message = "ios-deploy not installed" if error_message =~ /could not initialize ios-deploy/i
      error_message = "xctestrun file not found" if error_message =~ /you need to have a xctestrun file/i
      error_message = "Cannot parse xctestrun file" if error_message =~ /Could not parse plist file/i
      error_message = "Unknown device, is it on usb?" if error_message =~ /unknown device or simulator/i
      error_message = "Failed to unlock keychain" if error_message =~ /command 'security -v unlock-keychain -p .*' exited with code 181/i

      if error_message =~ /entitlement does not match that of the installed application/
        # Return here instead of checking other error messages, as this error
        # also has `xcodebuild failed with code 65`
        return "unable to upgrade"
      end

      if error_message =~ /A valid provisioning profile for this executable was not found/
        # Return here instead of checking other error messages, as this error
        # also has `xcodebuild failed with code 65`
        return "app signed with invalid profile"
      end

      # Something is interrupting the UITest launch on the screen
      # Usually happens because of 'No SIM card Installed' popup
      if error_message =~ /Failed to background test runner/
        # Return here instead of checking other error messages, as this error
        # also has `xcodebuild failed with code 65`
        return "possible no sim installed popup"
      end

      provisioning_profiles_error = error_message.match(/Xcode couldn't find any provisioning profiles matching (.*)'\./)
      if provisioning_profiles_error
        # Return here instead of checking other error messages, as this error
        # also has `xcodebuild failed with code 65`
        return "app incorrectly signed with #{provisioning_profiles_error[1]}"
      end

      identifier_error = error_message.match(/'(.*)' identifier didn't match any elements/i)
      if identifier_error
        identifier = identifier_error.captures[0]
        error_message = "#{identifier} identifier didn't match any elements"
      end

      xcodebuild_error = error_message.match(/xcodebuild failed with code (\d+)/i)
      if xcodebuild_error
        error_code = xcodebuild_error.captures[0]
        error_message = "xcodebuild failure code #{error_code}"
      end

      app_not_running_error = error_message.match(/the application under test with bundle id '(.*)' is not running/i)
      if app_not_running_error
        app_bundle = app_not_running_error.captures[0]
        error_message = "#{app_bundle} not running, probably crashed"
      end

      app_bundle_error = error_message.match(/app with bundle identifier '(.*)' unknown/i)
      if app_bundle_error
        app_bundle = app_bundle_error.captures[0]
        error_message = if app_bundle == 'com.apple.Preferences'
                          "Unknown settings app, is device trusted?"
                        else
                          "Unknown app bundle #{app_bundle}"
                        end
      end

      (error_message.length > 80 ? "#{error_message[0..80]}..." : error_message).to_s
    end

    # Kills the process, and also reloads the service if not coming back up after killing
    def restart
      BrowserStack.logger.info "Restarting Appium server on port #{@appium_port}"
      OSUtils.kill_process("appium", @appium_port.to_s)
      AppleTVUtils.restart_wda(@device_id) if apple_tv_device? && @device_version.to_i < 18
      begin
        restart_retries ||= 0
        running? # Waiting for LaunchDaemon to restart
      rescue AppiumServerError => e
        BrowserStack.logger.info("Reloading appium_#{@appium_port} service because it's not coming back up")
        username = @config['user']
        OSUtils.kill_process("appium", @appium_port.to_s)
        CheckPlist.unload_service("appium_#{@appium_port}", ServiceType.UserService, username)
        CheckPlist.load_service("appium_#{@appium_port}", ServiceType.UserService, username, 0)
        raise AppiumServerError, "Failed to restart Appium server on port #{@appium_port}" if restart_retries > 3

        restart_retries += 1
        sleep 5 * restart_retries
        retry
      end
      BrowserStack.logger.info "Succesfully restarted Appium server on port #{@appium_port}"
    end

    # rubocop:disable Metrics/AbcSize
    # This is basically updating the plist to use the given version
    # and waits for the service to come back up
    def start_server_for_version(appium_version, force_update = false, max_retries = nil, log_level = 'debug', automation_name = default_automation_name, automation_version = default_automation_version, skip_one_wda = false)
      # We need to start iproxy only for older versions of appium
      # and kill it if it's running for recent versions
      start_time = Time.now.to_f
      if (Gem::Version.new(appium_version) >= Gem::Version.new("1.14") && !apple_tv_device?) || (apple_tv_device? && @device_version.to_i >= 18)
        BrowserStack.logger.info "Killing all iProxy processes as a part of Appium start"
        BrowserStack::OSUtils.kill_all_processes("iproxy", @device_id)
      else
        ensure_iproxy_running
      end
      BrowserStack.logger.info("iproxy kill time: #{Time.now.to_f - start_time}")

      # WDA is requried to be launched with go-ios for Apple TV
      AppleTVUtils.update_wda_launch_agent(@device_id, appium_version) if apple_tv_device? && @device_version.to_i < 18

      start_time = Time.now.to_f
      @appium_version = appium_version
      ensure_xctestrun_file_not_empty

      @is_appium2 = Gem::Version.new(appium_version) >= Gem::Version.new("2.0")
      if @is_appium2
        update_plist(appium_version, log_level, force_update, automation_name, automation_version, skip_one_wda)
      else
        update_plist(appium_version, log_level, force_update, nil, nil, skip_one_wda)
      end
      BrowserStack.logger.info("appium plist update time: #{Time.now.to_f - start_time} ")
      start_time = Time.now.to_f
      max_retries.nil? ? running? : running?(max_retries)

      BrowserStack.logger.info("appium running check time: #{Time.now.to_f - start_time}")
      BrowserStack.logger.info "Appium server succesfully started on port #{@appium_port} for appium v#{appium_version}"
    end
    # rubocop:enable Metrics/AbcSize

    def get_node_path(appium_version)
      should_use_node20 = Gem::Version.new(appium_version) >= Gem::Version.new("2.6.0")
      should_use_node16 = Gem::Version.new(appium_version) >= Gem::Version.new("2.0.1")
      should_use_node14 = OSUtils.macos_version.to_i >= 11 || Gem::Version.new(appium_version) >= Gem::Version.new("1.21")
      node_env = if should_use_node20
                   NODE_20
                 elsif should_use_node16
                   NODE_16
                 elsif should_use_node14
                   NODE_14
                 else
                   NODE_10
                 end
      raise NameError, "Missing NODE_ENV" if node_env.nil?

      node_env
    end

    def get_memory_limits_for_device(device_id)
      HIGHER_MEMORY_DEVICES.key?(device_id.to_s) ? HIGHER_MEMORY_DEVICES[device_id.to_s]["memory"] : 2048
    end

    # This will update the plist runnning, and reload the service
    def update_plist(appium_version, log_level, force_update = false, automation_name = nil, automation_version = nil, skip_one_wda = false) # rubocop:todo Metrics/MethodLength, Metrics/AbcSize
      appium_dir = @config['appium_roots'][appium_version]
      username = @config['user']
      logging_root = @config['logging_root']
      appium_keychain = @config['appium_keychain']
      appium_keychain_password = @config['appium_keychain_password']
      platform_version = @platform_version || XcodeUtils.get_platform_version
      wda_version = @appium_version
      wda_version = @config['default_wda_version'] || wda_version unless skip_one_wda
      wda_xctestrun_folder = BrowserStack::WebDriverAgent.xctestrun_file_path(@device_id, wda_version, @device_version)

      desired_capabilites = []
      desired_capabilites << "\"keychainPath\":\"#{appium_keychain}\""
      desired_capabilites << "\"keychainPassword\":\"#{appium_keychain_password}\""
      desired_capabilites << "\"platformVersion\":\"#{platform_version}\""
      desired_capabilites << "\"useXctestrunFile\":true" unless apple_tv_device? && @device_version.to_i < 18
      desired_capabilites << "\"bootstrapPath\":\"#{wda_xctestrun_folder}\"" unless apple_tv_device? &&  @device_version.to_i < 18
      desired_capabilites << %("webDriverAgentUrl":"http://127.0.0.1:#{@wda_port}") if apple_tv_device? && @device_version.to_i < 18
      desired_capabilites << "\"orientation\":\"PORTRAIT\""
      desired_capabilites << "\"browserstack.isTargetBased\": \"#{@device_version.to_f >= 12.2}\""
      desired_capabilites << "\"newCommandTimeout\": 0"
      desired_capabilites << "\"wdaLocalPort\": #{@wda_port}" if @is_appium2 && ["flutter", "flutterintegration"].include?(automation_name.to_s.downcase)
      # TODO: Implement a debug mode that would run for us with the following cap but not for users
      # desired_capabilites << "\"showXcodeLog\":true"
      # start the server with various node versions as some appium commands don't work with lower node versions
      node_path = get_node_path(appium_version)

      appium_build_path = "#{appium_dir}/build/lib/main.js"

      if @is_appium2
        appium_build_path = "#{appium_dir}/packages/appium/build/lib/main.js"
        appium_dir = "#{appium_dir}/packages/appium"
        desired_capabilites << "\"udid\": \"#{@device_id}\""
      end

      if !File.exist?(node_path)
        raise "node version #{node_path} doesn't exist"
      elsif !File.exist?(appium_build_path)
        raise "File #{appium_dir}/build/lib/main.js doesn't exist"
      end

      memory_limit = get_memory_limits_for_device(@device_id)

      plist_args = [
        node_path,
        "--max-old-space-size=#{memory_limit}",
        appium_build_path,
        '--log-level',
        log_level,
        '-p',
        @appium_port,
        '-dc',
        "{#{desired_capabilites.join(', ')}}",
        "--log-timestamp",
        "--log-no-colors"
      ]

      appium_home = false
      if @is_appium2
        plist_args.concat(['--driver-xcuitest-webkit-debug-proxy-port', @webkit_port, '--driver-xcuitest-webdriveragent-port', @wda_port, "--base-path", "/wd/hub", "--use-plugins", "all"])
        appium_home = "#{appium_dir}/#{automation_name}/#{automation_version}"
      else
        plist_args.concat(['-U', @device_id, '--webkit-debug-proxy-port', @webkit_port, '--webdriveragent-port', @wda_port])
      end
      cp = CheckPlist.new(
        "appium_#{@appium_port}",
        ServiceType.UserService,
        plist_args,
        "#{logging_root}/appium_#{@appium_port}.log",
        "#{logging_root}/appium_#{@appium_port}.log",
        username,
        nil,
        { WorkingDirectory: appium_dir },
        true,
        nil,
        false,
        false,
        'generic_plist.erb',
        '/usr/local/.browserstack/realmobile/deps/bin',
        appium_home
      )

      cp.update(force_update, 0)
    rescue => e
      BrowserStack.logger.error "Failed to update plist for Appium server on port #{@appium_port}: #{e.message}"
      raise AppiumServerError, "Appium plist setup failed: #{e.message}"
    end

    # This will start iproxy on the wda_port if it's not already running
    def ensure_iproxy_running
      retries = 0
      while retries < 3 && !Iproxy.running?(@device_id, @wda_port)
        stale_iproxy_process_pid = BrowserStack::OSUtils.get_process_pid('iproxy', @wda_port).strip
        BrowserStack::OSUtils.kill_pid(stale_iproxy_process_pid.to_i) unless stale_iproxy_process_pid.empty?
        Iproxy.start(@device_id, @wda_port, '8100')
        sleep 1 # waiting for iproxy to boot
        retries += 1
      end
      unless Iproxy.running?(@device_id, @wda_port)
        Iproxy.start(@device_id, @wda_port, '8100')
        sleep 1 # waiting for iproxy to boot
      end

      raise AppiumServerError, "Couldn't start iproxy" unless Iproxy.running?(@device_id, @wda_port)
    end
  end

  def default_automation_name
    apple_tv_device? || @device_version.to_i >= 17 ? "xcuitest" : nil
  end

  def default_automation_version
    return '8.3.3'  if apple_tv_device? && @device_version.to_i >= 18
    return '4.12.2' if apple_tv_device?
    return '4.21.6' if @device_version.to_i >= 17

    nil
  end

  def apple_tv_device?
    IdeviceUtils.os(@device_id) == 'tvos'
  end
end

# It can take a few seconds for the file requirements to load.
if $PROGRAM_NAME == __FILE__
  puts "File requirements loaded. Beginning script..."

  method = ARGV[0]
  device = ARGV[1]

  # Sample command:
    # did=<device-id>
    # bundle exec ruby appium_server.rb start_driver $did
  case method
  when 'start_driver'
    BrowserStack.logger.info "Starting WDA and iproxy for device #{device}"

    appium_server = BrowserStack::AppiumServer.new(device)
    appium_server.driver
    appium_server.ensure_iproxy_running

    BrowserStack.logger.info "WDA and iproxy is now started for device #{device}"
  else
    BrowserStack.logger.error "Method #{method}, is not listed."
  end
end
