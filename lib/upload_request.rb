require 'base64'
require 'json'
require 'fileutils'
require 'digest'
require 'securerandom'

class UploadRequest
  def initialize(folder)
    @request_folder = folder
    raise 'Uninitialized image uploader folder' if @request_folder.nil? || @request_folder.empty?
  end

  def create_request(dest_filename, json_data)
    dir_name = File.dirname(dest_filename)
    FileUtils.mkdir_p(dir_name) unless Dir.exist?(dir_name)

    if File.exist?(dest_filename)
      BrowserStack.logger.info("Not generating request as already created !!.")
    else
      Utils.write_to_file(dest_filename, json_data.to_json)
      BrowserStack.logger.info("Upload request created #{dest_filename} : #{json_data}")
    end
  end

  def start(img_file_name, s3_file_name, screenshot_lock_file, session_id, start_time, key_id = nil, secret_key = nil, keep_screenshot = false, check_black_screenshot = false)

    json_data = {
      file_name: img_file_name,
      dest: s3_file_name,
      upload_type: 'screenshot',
      key_id: key_id,
      secret_key: secret_key,
      session_id: session_id,
      start_time: start_time,
      screenshot_lock_file: screenshot_lock_file,
      keep_screenshot: keep_screenshot,
      check_black_screenshot: check_black_screenshot
    }

    dest_filename = "#{@request_folder}/#{SecureRandom.uuid}.json"

    create_request(dest_filename, json_data)
    BrowserStack.logger.info("creating #{dest_filename} with data: #{json_data}")
  rescue => e
    BrowserStack.logger.info("Something went wrong in sending this request: file: #{img_file_name} : #{e.message}" + e.backtrace.join("\n"))

  end
end
