require 'fileutils'
require_relative './models/device_state'
require_relative '../config/constants'
require_relative './helpers/testflight'

# This class has been created to perform tasks on an upgraded device to ensure it undergoes cleanup properly.
# It would mostly involve removal/addition of state files.
# Any other step like installation/uninstallation of any app on the upgraded device
# should also be performed here.

module BrowserStack
  class UpgradedDeviceCheckState #rubocop:todo Metrics/ClassLength

    attr_reader :device_state

    def initialize(device)
      @device = device
      @device_state = DeviceState.new(@device)
    end

    def remove_state_files_after_erase
      public_methods = DeviceState.instance_methods(false)
      private_methods = DeviceState.private_instance_methods(false)
      all_methods = public_methods + private_methods
      all_methods.select do |m|
        if m.to_s.end_with?('_file') && !["session_start_file", "session_file", "ppuid_file", "cleanup_completed_steps_file", "last_camera_check_file", "manual_cleanup_file", "full_cleanup_file", "mdm_full_cleanup_file"].include?(m.to_s)
          BrowserStack.logger.info "Removing state file: #{m}"
          @device_state.send(:"remove_#{m}")
        end
      end
    end

    def touch_periodic_cleanup_related_frequency_files
      periodic_cleanup_related_frequency_files.each do |file|
        BrowserStack.logger.info "Touching state file: #{file}"
        @device_state.send(:"touch_#{file}")
      end
    end

    def periodic_cleanup_related_frequency_files
      %I[
        disable_dark_mode_file
        enabled_wifi_file
        force_clean_safari_file
        force_clean_apple_id_file
        force_clean_sandbox_account_file
        force_clean_safari_bookmarks_file
        force_clean_safari_favorites_file
        force_clean_files_app_file
        force_kill_apps_file
        reset_view_to_standard_file
        reset_keyboard_settings_file
        remove_extra_keyboards_file
        clean_preloaded_media_with_reboot_file
        force_clean_apple_wallet_file
        force_clean_testflight_file
        force_clean_safari_tab_groups_file
        set_default_font_size_file
        force_clean_chromium_file
        clean_stored_password_file
        provisioning_profile_cleaned_file
        clean_sms_file
        disable_stage_manager_file
        disable_apple_intelligence_file
        force_clean_history_from_safari_app_file
        restart_webkit_proxy_file
      ]
    end

    def remove_bluetooth_network_files
      device_state.remove_disabled_bluetooth_file
      device_state.remove_check_global_proxy_file
      device_state.remove_nat_setup_complete_file
    end

    def remove_apple_id_files
      device_state.remove_force_clean_apple_id_file
      device_state.remove_pwa_enabled_file
    end

    def remove_session_reboot_photos_files
      device_state.remove_photos_permission_file
      device_state.remove_reboot_file
      device_state.remove_webdriveragent_file
      device_state.remove_session_info_file
      device_state.remove_session_start_file
      device_state.remove_session_lock_file
      device_state.remove_session_file
    end

    def remove_location_files
      device_state.remove_location_services_enabled_file
      device_state.remove_appstore_location_services_popup_file
      device_state.remove_location_service_for_app_session_file
      device_state.remove_current_locale_app_live_file
    end

    def remove_camera_files
      device_state.remove_back_camera_file
      device_state.remove_front_camera_file
      device_state.remove_last_camera_check_file
    end

    def remove_orientation_files
      device_state.remove_force_check_orientation_lock_file
    end

    def remove_safari_state_files
      # Trigger Safari Web Inspector Cleanup
      device_state.remove_enable_safari_web_inspector_file

      # Trigger Safari Full cleanup
      device_state.remove_force_clean_safari_file

      # Trigger Bookmarks and Favorites cleanup
      device_state.remove_force_clean_safari_bookmarks_file
      device_state.remove_force_clean_safari_favorites_file
    end

    def remove_testflight_files
      device_state.remove_disabled_testflight_notifs_file
      device_state.remove_installed_testflight_file
      device_state.remove_installed_com_apple_testflight_installed_file
      device_state.remove_com_apple_testflight_file
    end

    def remove_siri_files
      device_state.remove_disable_siri_contact_suggestions_file
      device_state.remove_siri_search_cleaned_file
    end

    def remove_auto_lock_files
      device_state.remove_disable_auto_lock_file
    end

    def remove_government_notifications_file
      device_state.remove_government_notifications_file
    end

    def remove_update_failed_file
      device_state.remove_update_failed_file
    end

    def remove_wda_files
      Dir.glob("#{TMP_DIR_PATH}/#{@device}_*_wda.zip*").each { |file| FileUtils.rm_f(file) }
    end

    def remove_apps_files
      device_state.remove_browserstack_app_built_version_file

      return if device_state.dedicated_cleanup_file_present?

      Dir.glob("#{TMP_DIR_PATH}/#{@device}_installed_apps/*").each { |file| FileUtils.rm_f(file) }
    end

    def remove_device_logger_files
      # device_logger_pid file is removed after cleanup, but removing for fail-safe
      device_state.remove_device_logger_pid_file # absence of this file will make device logger appear unreliable
      device_state.remove_device_logger_session_end_pid_file
    end

    def discard_old_states

      Automation::TestFlight.new(@device).uninstall
      remove_location_files
      remove_camera_files
      remove_orientation_files
      remove_safari_state_files
      remove_auto_lock_files
      remove_government_notifications_file
      remove_wda_files
      remove_apps_files
      remove_device_logger_files
      remove_update_failed_file

    rescue => e
      BrowserStack.logger.error("Error during file cleanup of upgraded device-#{@device}: with #{e.message}:\n #{e.backtrace}")
      raise e

    end

  end
end
