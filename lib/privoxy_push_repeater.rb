MOBILE_COMMON_ROOT = "/usr/local/.browserstack/mobile-common"
require "#{MOBILE_COMMON_ROOT}/utils/log_parse_util"
require_relative "../lib/utils/utils"
require_relative './utils/helpers'
include LogParseUtil

class PrivoxyP<PERSON><PERSON><PERSON><PERSON><PERSON>
  def self.push_privoxy_logs(params) # rubocop:todo Metrics/MethodLength, Metrics/AbcSize
    device_identifier = params[:device] || params["device"]
    genre = params[:genre]
    session_id = nil
    session_type = nil
    case genre
    when "live_testing"
      session_id = params[:live_session_id]
      session_type = "summary_logs"
      eds_constant = EdsConstants::LIVE_TEST_SESSIONS
    when "selenium"
      session_id = params[:automate_session_id]
      session_type = "automate_logs"
      eds_constant = EdsConstants::AUTOMATE_TEST_SESSIONS
    when "js_testing"
      session_id = params[:automate_session_id]
      session_type = "js_testing_logs"
      eds_constant = EdsConstants::AUTOMATE_TEST_SESSIONS
    when "app_live_testing"
      session_id = params[:app_live_session_id]
      session_type = "app_summary_logs"
      eds_constant = EdsConstants::APP_LIVE_TEST_SESSIONS
    when "app_automate"
      session_id = params[:automate_session_id]
      session_type = "app_automate_logs"
      eds_constant = EdsConstants::APP_AUTOMATE_TEST_SESSIONS
    else
      return
    end

    privoxy_log_path = "/var/log/browserstack/privoxy_#{device_identifier}.log"
    unless File.file?(privoxy_log_path)
      BrowserStack.logger.info "privoxy_stats - #{privoxy_log_path} file does not exist"
      return
    end
    privoxy_stats = parse_privoxy_logs(session_id, privoxy_log_path)
    counters = begin
      privoxy_stats["counters"]
    rescue
      {}
    end

    all_host_requests = privoxy_stats.nil? ? {} : privoxy_stats["all_host_requests"]

    privoxy_stats["session_type"] = session_type
    BrowserStack.logger.info "Privoxy!! Params #{params} "
    ## pushing to cls
    push_to_cls(params, "local-privoxy-stats", "", privoxy_stats)
    BrowserStack.logger.info "Privoxy!! --pushing to eds #{privoxy_stats} and #{eds_constant}"
    Utils.send_privoxy_data_to_eds({
      "session_id" => session_id,
      "local" => {
        "local_address" => {
          "num_privoxy_pages_shown" => privoxy_stats["counters"]["privoxy_error_page"] || 0,
          "num_privoxy_requests" => privoxy_stats["counters"]["requests"] || 0,
          "num_privoxy_responses" => privoxy_stats["counters"]["responses"] || 0
        }.to_json
      }
    }, eds_constant)

    BrowserStack.logger.info "Starting to send network logs to EDS"

    # TODO: Need to move the string to EdsConstants::SESSION_NETWORK_LOGS after bundle update
    Utils.send_network_logs_to_eds(session_id, genre, all_host_requests, "session_network_logs")

    BrowserStack.logger.info "Completed send network logs to EDS"

    ## Log Rotate for Privoxy
    Utils.truncate_log_file(privoxy_log_path)
  end
end
