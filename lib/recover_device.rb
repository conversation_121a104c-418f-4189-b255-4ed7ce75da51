require 'English'
require_relative 'custom_exceptions'
require 'browserstack_logger'
require_relative 'utils/idevice_utils'
require_relative 'device_setup/recovery_mode/check_and_exit'
require_relative '../config/constants'
require_relative '../server/iphone'
require_relative 'device_setup/full_cleanup/full_cleanup_utils'
require_relative 'helpers/pairing_helper'
require_relative 'helpers/automation'

module BrowserStack
  # Doesn't guarantee recovery

  class RecoverDevice # rubocop:todo Metrics/ClassLength

    # device is on usb but not online on idevice_id
    class DeviceNotOnIdeviceInfo < MobileCheckException
    end

    # device is in lockdown
    class DeviceInLockdown < MobileCheckException
    end

    # device is not on usb
    class DeviceNotOnUSB < MobileCheckException
    end

    # device is not on usb
    class DeviceCtlError < MobileCheckException
    end

    # recover device timed out
    class RecoverDeviceTimedOut < MobileCheckException
    end

    attr_reader :device_id

    def initialize(device_id, ecid = nil)
      @device_id = device_id
      @ecid = ecid
      @server_config = Configuration.new.all

      # Device Configuration
      begin
        device_config = DeviceManager.device_configuration_check @device_id
        @device_name = device_config['device_name']
        @device_os_version = device_config['device_version'].to_f
      rescue => e
        @device_name = ''
        @device_os_version = 0.0
      end

      # Devicectl
      @use_devicectl = false
      @use_devicectl_computed = false
    end

    def full_cleanup_utils
      @full_cleanup_utils ||= FullCleanup::FullCleanupUtils.new(@device_id, BrowserStack.logger)
    end

    def recover_devicectl_with_reboot
      DeviceManager.reboot_and_wait(@device_id)
      full_cleanup_utils.wait_until_device(:on_devicectl)
    rescue => e
      device_state = devicectl_state
      raise DeviceCtlError, OFFLINE_REASON_NOT_ON_DEVICECTL if device_state.include? "No devices found"

      raise DeviceCtlError, e.message
    end

    def check_and_recover_device
      begin
        log "Checking if device is on idevice_id"
        full_cleanup_utils.check_and_recover_device_on_idevice_id
      rescue => e
        log "Device is not on idevice_id, error: #{e.message}"
        raise DeviceNotOnUSB, "manual fix required: device is not on usb"
      end

      begin
        log "Device is on idevice_id, checking if device is on devicectl"
        full_cleanup_utils.wait_until_device(:on_devicectl)
      rescue => e
        check_and_clear_passcode
        recover_devicectl_with_reboot
      end

      log "Device is on devicectl, lockdown pairing the device"
      full_cleanup_utils.wait_until_device(:lockdown_paired)

      begin
        log "Checking if device if on ideviceinfo"
        full_cleanup_utils.wait_until_device(:on_ideviceinfo)
      rescue => e
        raise DeviceNotOnIdeviceInfo, "device is on usb but not on ideviceinfo with error: #{e.message}"
      end

      log "Checking if developer mode is enabled or not"
      full_cleanup_utils.check_and_enable_developer_mode

      begin
        log "Checking if device is connected"
        full_cleanup_utils.wait_until_device(:connected, accept_no_ddi: false, accept_available_paired: true, accept_available: false)
      rescue => e
        log "Device is not connected, attempting to get it connected"
        pairing_helper = PairingHelper.new(@device_id, BrowserStack.logger)
        pairing_helper.ensure_pair

        device_state = devicectl_state
        DeviceCtl::Device.apps(@device_id, timeout: 30) if device_state.include?("connected (no DDI)")
        # Check if device is connected
        full_cleanup_utils.wait_until_device(:connected, accept_no_ddi: false, accept_available_paired: true, accept_available: false)
      end
    end

    def attempt
      if connected?
        log "Device is connected, nothing to do."
        return
      end

      if @device_os_version >= 18.0
        # Using it just for ios 18.0 and above
        check_and_recover_device
        return
      end
      pre_recover_checks

      log "Attempting to recover"
      recover

      if connected?
        log "Device recovered!!"
        return
      end

      post_recover_checks
    end

    private

    def connected?
      idevice_info = IdeviceUtils.ideviceinfo(@device_id, "ProductType").first

      return false unless $CHILD_STATUS.exitstatus == 0 # If ideviceinfo command fails

      return false unless device_on_idevice_info?(idevice_info)

      return false if use_devicectl? && !devicectl_connected?

      true
    end

    def pre_recover_checks
      raise DeviceNotOnUSB, "manual fix required: device is not on usb" unless device_on_usb?

      if use_devicectl?
        device_state = devicectl_state
        raise DeviceCtlError, OFFLINE_REASON_NOT_ON_DEVICECTL if device_state.include? "No devices found"
        raise DeviceCtlError, OFFLINE_REASON_DEVICE_NOT_PAIRED if device_state.include? "pairing"
      end
    end

    def post_recover_checks
      pre_recover_checks

      idevice_info = IdeviceUtils.ideviceinfo(@device_id, "ProductType").first
      lockdown_code_value = lockdown_code(idevice_info)
      raise DeviceInLockdown, lockdown_reason(lockdown_code_value) if lockdown_code_present?(lockdown_code_value)

      raise DeviceNotOnIdeviceInfo, "device is on usb but not on ideviceinfo" if device_on_usb?

      raise DeviceNotOnUSB, "manual fix required: device is not on usb"
    end

    def recover
      # check if device is in recovery mode and exit using irecovery
      RecoveryMode.check_and_exit(@ecid) unless @ecid.nil?
      # Reset USB on device's address
      device_address = device_address_from_ioreg
      reset_usb_with_address(device_address) if device_on_usb?(device_address)

      if use_devicectl?
        device_state = devicectl_state
        recover_devicectl(device_state) unless devicectl_connected?(device_state)
      end
    rescue OSUtilsError => e
      log("recover device timed out: #{e.message} #{e.backtrace}")
      raise RecoverDeviceTimedOut, "recover devices timed out"
    end

    def devicectl_state
      DeviceCtl::List.device_state(@device_id, timeout: 10)
    end

    def devicectl_connected?(device_state = nil)
      device_state = devicectl_state if device_state.nil?
      ["connected", "available (paired)"].include? device_state
    end

    def recover_devicectl(device_state)
      if device_state.include?("connected (no DDI)")
        begin
          BrowserStack::OSUtils.execute("xcrun devmodectl single #{@device_id}", timeout: 10)
          return if devicectl_connected?
        rescue
          DeviceManager.reboot_and_wait(@device_id)
          raise DeviceCtlError, OFFLINE_REASON_NO_DDI unless devicectl_connected?
        end
      end

      check_and_clear_passcode
      return if devicectl_connected?

      # retry to get re-pair and get apps, sometimes device comes back
      counter = 0
      while counter < 30 && devicectl_state == 'connecting'
        # wait for it to connect
        sleep 2
      end
      DeviceCtl::Manage.pair(@device_id, timeout: 10)
      DeviceCtl::Device.apps(@device_id, timeout: 10)

      # if device is supervised using managed identity this will trust it without a popup
      # requires crt and der files to be passed as params used to supervise devices
      # stored in config directory
      cfgutil = CFGUtil.new(udid: @device_id)
      identities_path = "#{CONFIG_ROOT}/supervision_identities"
      region = @server_config['static_conf']['region']
      index = region.length - 3
      filename = region[0..index]
      path = "#{identities_path}/#{filename}"

      cfgutil.pair("#{path}.crt", "#{path}.der") if ensure_files_exist_and_device_supervised(identities_path, path)
      raise DeviceCtlError, OFFLINE_REASON_DEVICE_NOT_TRUSTED unless devicectl_connected?
    end

    def ensure_files_exist_and_device_supervised(folder_path, file_path)
      return false unless Dir.exists?(folder_path)
      return false if !File.exists?("#{file_path}.crt") || !File.exists?("#{file_path}.der")

      cfgutil = CFGUtil.new(udid: @device_id)

      return false unless cfgutil.installed?
      return false if cfgutil.device_type.nil?

      org_name = cfgutil.get_property('organizationName', "#{file_path}.crt", "#{file_path}.der")
      return false if !org_name.nil? && org_name.split.length == 1

      true
    end

    def use_devicectl?
      return @use_devicectl if @use_devicectl_computed

      @use_devicectl = IdeviceUtils.use_devicectl(@device_id)
      @use_devicectl_computed = true
      @use_devicectl
    rescue
      false
    end

    def device_in_lockdown?(idevice_info)
      idevice_info.include?("Could not connect to lockdownd")
    end

    def lockdown_code_present?(lockdown_code_value)
      lockdown_code_value != false
    end

    def lockdown_code(idevice_info)
      device_in_lockdown?(idevice_info) ? idevice_info.split.last : false
    end

    def lockdown_reason(lockdown_code_value)

      return "I am not in lockdown" unless lockdown_code_present?(lockdown_code_value)

      return "I have trust issue # #{lockdown_code_value}" if ['-18', '-19', '-5'].include?(lockdown_code_value)

      "Lockdown issue # #{lockdown_code_value}"
    end

    def device_on_usb?(device_address = nil)
      device_address = device_address_from_ioreg if device_address.nil?
      !device_address.empty?
    end

    def device_on_idevice_info?(idevice_info)
      !(idevice_info.include?("No device found with udid") || idevice_info.include?("Device #{@device_id} not found!"))
    end

    def device_address_from_ioreg
      BrowserStack::OSUtils.execute(device_address_command, timeout: 10)
    end

    def reset_usb_with_address(address)
      return if address.empty?

      Timeout.timeout(30) do
        BrowserStack::OSUtils.execute(usb_reset_command(address))
      end
    rescue => e
      log("Exception in reset_usb:  #{e.message}")
    end

    def device_address_command
      "ioreg -p IOUSB -l -b | grep -E '@|kUSBSerialNumberString|USB Address|USB Serial Number' | grep -C1 #{@device_id.tr('-', '')} | grep Address | awk '{print $NF}'"
    end

    def usb_reset_command(address)
      "/usr/local/.browserstack/deps/usb_reset #{address}"
    end

    def check_and_clear_passcode
      device_config = DeviceManager.device_configuration_check @device_id
      iphone = BrowserStack::IPhone.new(device_config, @device_id)
      iphone.check_and_clear_passcode
    end

    def log(msg)
      BrowserStack.logger.info("#{@device_id} #{msg}")
    end
  end
end
