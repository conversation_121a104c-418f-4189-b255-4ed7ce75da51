require 'json'
require_relative './configuration'
require_relative './utils/utils'

module BrowserStack
  class DeviceConf
    class << self
      def [](device_id)
        return Utils.deep_copy(Thread.current[device_id]) if Thread.current[device_id]

        old[device_id]
      end

      def old
        return {} unless File.exist?(Configuration['config_json_file'])
        return {} if File.size(Configuration['config_json_file']) == 0

        JSON.parse(File.read(Configuration['config_json_file']))['devices']
      end
    end
  end
end
