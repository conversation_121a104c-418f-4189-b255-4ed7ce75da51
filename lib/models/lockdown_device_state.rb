require 'json'
require_relative '../utils/pymobiledevice'

module B<PERSON>erStack
  class LockdownDeviceState
    DOMAIN = "com.apple.browserstack.device_state"

    attr_reader :udid

    def initialize(udid)
      @udid = udid
    end

    def respond_to_missing?(method, *)
      method_name = method.to_s
      method_name.end_with?('_key_present?') ||
        (method_name.start_with?('set_') && method_name.end_with?('_key')) ||
        (method_name.start_with?('get_') && method_name.end_with?('_key')) ||
        (method_name.start_with?('remove_') && method_name.end_with?('_key'))
      super
    end

    def method_missing(method, *args)
      method_name = method.to_s
      if method_name.end_with?('_key_present?')
        !get_key(send(method_name.sub('_present?', '').to_sym)).nil?
      elsif method_name.start_with?('remove_') && method_name.end_with?('_key')
        remove_key(send(method_name.sub('remove_', '').to_sym))
      elsif method_name.start_with?('set_') && method_name.end_with?('_key')
        key_value = {
          value: args[0],
          last_updated_at: Time.now.to_i
        }
        set_key(send(method_name.sub('set_', '').to_sym), key_value.to_json)
      elsif method_name.start_with?('get_') && method_name.end_with?('_key')
        key_value = get_key(send(method_name.sub('get_', '').to_sym))
        return JSON.parse(key_value)&.dig("value") unless key_value.nil?

        nil
      else
        super
      end
    end

    def first_cleanup_done_key
      "first_cleanup_done"
    end

    def apple_pay_assistive_touch_key
      "apple_pay_assistive_touch"
    end

    def assistive_touch_configuration_key
      "assistive_touch_configuration"
    end

    private

    def set_key(key, value)
      PyMobileDevice::Lockdown.set_key(@udid, DOMAIN, key, value)
    end

    def get_key(key)
      PyMobileDevice::Lockdown.get_key(@udid, DOMAIN, key)
    end

    def remove_key(key)
      PyMobileDevice::Lockdown.remove_key(@udid, DOMAIN, key)
    end
  end
end
