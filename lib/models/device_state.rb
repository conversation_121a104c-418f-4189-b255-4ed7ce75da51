require 'fileutils'
require_relative '../../config/constants'
require_relative '../utils/utils'
require_relative '../../lib/configuration'

# This class just holds boilerplate so we can put all the state files in a single class.
# It would also make it easier to move to a DB in future if we ever want to.
# Try exposing only convenience methods like `rotate_certificate_file_present?`
# instead of exposing `rotate_certificate_file`.
#
# Ideally, this class shouldn't have any class methods and its object will be light
# weight so anywhere just pass in the device id and call the method like:
# DeviceState.new("<udid>").rotate_certificate_file_present?
#
# After defining a method (say my_file) that returns a
# file path (like /usr/local/.browserstack/config/my_file_udid), you can use:
# 1. touch_my_file
# 2. remove_my_file
# 3. my_file_to_array
# 4. write_to_my_file(string)
# 5. my_file_present?
# 6. my_file_older_than_days?(days)
# 7. my_file_older_than_minutes?(minutes)
# 8. read_my_file
# 9. write_array_to_my_file
# 10. write_with_lock_to_my_file
# TODO: Move required files from config & states_files directories here.
class DeviceState # rubocop:todo Metrics/ClassLength
  attr_reader :udid

  def initialize(udid)
    @udid = udid
  end

  def respond_to_missing?(method, *)
    method_name = method.to_s
    method_name.end_with?('_file_present?', '_file_to_array', '_file_older_than_days?', '_file_older_than_minutes?', '_file_clean_on_weekend?') ||
      (method_name.start_with?('remove_') && method_name.end_with?('_file')) ||
      (method_name.start_with?('touch_') && method_name.end_with?('_file')) ||
      (method_name.start_with?('read_') && method_name.end_with?('_file')) ||
      (method_name.start_with?('write_array_to_') && method_name.end_with?('_file')) ||
      (method_name.start_with?('write_with_lock_to_') && method_name.end_with?('_file')) ||
      (method_name.start_with?('write_to_') && method_name.end_with?('_file')) ||
      super
  end

  def method_missing(method, *args) # rubocop:todo Metrics/AbcSize, Metrics/PerceivedComplexity, Metrics/CyclomaticComplexity, Metrics/MethodLength
    method_name = method.to_s
    if method_name.end_with?('_file_present?')
      File.exist?(send(method_name.sub('_present?', '').to_sym))
    elsif method_name.end_with?('_file_to_array')
      Utils.read_file_in_array(send(method_name.sub('_to_array', '').to_sym))
    elsif method_name.start_with?('write_array_to_') && method_name.end_with?('_file')
      array = args[0]
      Utils.write_array_to_file!(send(method_name.sub('write_array_to_', '').to_sym), array)
    elsif method_name.start_with?('remove_') && method_name.end_with?('_file')
      FileUtils.rm_f(send(method_name.sub('remove_', '').to_sym))
    elsif method_name.start_with?('touch_') && method_name.end_with?('_file')
      Utils.touch_file_and_create_parents(send(method_name.sub('touch_', '').to_sym))
    elsif method_name.start_with?('write_to_') && method_name.end_with?('_file')
      data = args[0]
      Utils.write_to_file(send(method_name.sub('write_to_', '').to_sym), data)
    elsif method_name.start_with?('write_with_lock_to_') && method_name.end_with?('_file')
      data = args[0]
      Utils.write_to_file_with_lock(send(method_name.sub('write_with_lock_to_', '').to_sym), data)
    elsif method_name.end_with?('_file_older_than_days?')
      days = args[0]
      file_path = send(method_name.sub('_older_than_days?', '').to_sym)
      !File.exist?(file_path) || (File.mtime(file_path) < (Time.now - (60 * 60 * 24 * days)))
    elsif method_name.end_with?('_file_older_than_minutes?')
      minutes = args[0]
      file_path = send(method_name.sub('_older_than_minutes?', '').to_sym)
      !File.exist?(file_path) || (File.mtime(file_path) < (Time.now - (60 * minutes)))
    elsif method_name.end_with?('_file_clean_on_weekend?')
      days = args[0]
      file_path = send(method_name.sub('_clean_on_weekend?', '').to_sym)
      !File.exist?(file_path) || check_weekend_or_file_older?(file_path)
    elsif method_name.end_with?('_created_at')
      file_path = send(method_name.sub('_created_at', '').to_sym)
      File.birthtime(file_path)
    elsif method_name.end_with?('_updated_at')
      file_path = send(method_name.sub('_updated_at', '').to_sym)
      File.mtime(file_path)
    elsif method_name.start_with?('read_') && method_name.end_with?('_file')
      File.read(send(method_name.sub('read_', '').to_sym))
    else
      super
    end
  end

  def skip_enterprise_dummy_automation_file
    File.join(STATE_FILES_DIR, "skip_enterprise_dummy_automation_#{udid}")
  end

  def enable_safari_web_inspector_file
    File.join(STATE_FILES_DIR, "enable_safari_web_inspector_#{udid}")
  end

  def proxy_pac_url(ip)
    "http://#{ip}:45691/pacfile?device=#{udid}"
  end

  def xcodebuild_retry_count
    xcodebuild_retry_count_file_present? ? read_xcodebuild_retry_count_file.to_i : 0
  end

  def retry_appstore_login_file
    File.join(STATE_FILES_DIR, "retry_appstore_login_#{udid}")
  end

  def allow_settings_app_count
    allow_settings_app_count_file_present? ? read_allow_settings_app_count_file.to_i : 0
  end

  def installed_apps_file
    File.join(CONFIG_ROOT, "installed_apps_#{udid}.json")
  end

  def first_cleanup_file
    File.join(STATE_FILES_DIR, "first_cleanup_#{udid}")
  end

  def first_cleanup_completed_file
    # if this file exists, then the device has completed atleast 1 cleanup, otherwise the device is freshly hosted
    File.join(STATE_FILES_DIR, "first_cleanup_completed_#{udid}")
  end

  def first_device_thread_done_file
    File.join(STATE_FILES_DIR, "first_device_thread_done_#{udid}")
  end

  private

  def custom_media_cleanup_file
    File.join(STATE_FILES_DIR, "custom_media_cleanup_file_#{udid}")
  end

  def minimized_cleanup_reserved_file
    File.join(STATE_FILES_DIR, "minimized_cleanup_reserved_file_#{udid}")
  end

  def preserve_app_state_reserved_file
    File.join(STATE_FILES_DIR, "preserve_app_state_reserved_file_#{udid}")
  end

  def minimized_cleanup_unreserved_file
    File.join(STATE_FILES_DIR, "minimized_cleanup_unreserved_file_#{udid}")
  end

  def safari_live_session_file
    File.join(STATE_FILES_DIR, "safari_live_session_file_#{udid}")
  end

  def replay_kit_running_file
    File.join(STATE_FILES_DIR, "replay_kit_running_file_#{udid}")
  end

  def paint_timing_enabled_file
    File.join(STATE_FILES_DIR, "paint_timing_enabled_#{udid}")
  end

  def safari_websocket_file
    File.join(STATE_FILES_DIR, "safari_websocket_#{udid}")
  end

  def standby_mode_file
    File.join(STATE_FILES_DIR, "standby_mode_disabled_#{udid}")
  end

  def device_logger_pid_file
    File.join(STATE_FILES_DIR, "device_logger_pid_#{udid}")
  end

  def device_logger_session_end_pid_file
    File.join(STATE_FILES_DIR, "device_logger_session_end_pid_#{udid}")
  end

  def watcher_unreliable_file
    File.join(STATE_FILES_DIR, "watcher_unreliable_#{udid}")
  end

  def watcher_unreliable_off_usb_file
    File.join(STATE_FILES_DIR, "watcher_unreliable_off_usb_#{udid}")
  end

  def watcher_start_time_file
    File.join(STATE_FILES_DIR, "watcher_start_time_#{udid}")
  end

  def video_logger_pid_file
    File.join(STATE_FILES_DIR, "video_logger_pid_#{udid}")
  end

  def manual_cleanup_file
    File.join(STATE_FILES_DIR, "manual_cleanupdone_#{udid}")
  end

  def check_global_proxy_file
    File.join(STATE_FILES_DIR, "check_global_proxy_#{udid}")
  end

  def disable_dark_mode_file
    File.join(STATE_FILES_DIR, "disable_dark_mode/disabled_#{udid}")
  end

  def safari_remote_automation_file
    File.join(STATE_FILES_DIR, "safari_remote_automation_#{udid}")
  end

  def enabled_wifi_file
    File.join(STATE_FILES_DIR, "enabled_wifi/enabled_#{udid}")
  end

  def keep_wifi_enabled_file
    File.join(STATE_FILES_DIR, "enabled_wifi/keep_wifi_enabled_#{udid}")
  end

  def reset_keyboard_settings_file
    File.join(STATE_FILES_DIR, "reset_keyboard_settings_#{udid}")
  end

  def force_install_mdm_profiles_file
    File.join(STATE_FILES_DIR, "force_install_mdm_profiles_#{udid}")
  end

  def install_mdm_profiles_file
    File.join(STATE_FILES_DIR, "install_mdm_profiles_#{udid}")
  end

  def unlock_token_file
    File.join(STATE_FILES_DIR, "unlock_token_#{udid}")
  end

  def government_notifications_file
    File.join(CONFIG_ROOT, "government_notifications/disabled_#{udid}")
  end

  def mobile_config_installed_file
    File.join(STATE_FILES_DIR, "mobile_config_installed_#{udid}")
  end

  def sim_info_file
    File.join(STATE_FILES_DIR, "sim_info_#{udid}")
  end

  def sim_conflict_file
    File.join(STATE_FILES_DIR, "sim_conflict_#{udid}")
  end

  # If a physical sim is present
  def physical_sim_file
    File.join(STATE_FILES_DIR, "physical_sim_#{udid}")
  end

  # If an e-sim is present
  def esim_file
    File.join(STATE_FILES_DIR, "esim_#{udid}")
  end

  # If sim card is enabled
  def sim_enabled_file
    File.join(STATE_FILES_DIR, "sim_enabled_#{udid}")
  end

  # If esim modifications have been enabled/allowed
  def esim_modification_file
    File.join(STATE_FILES_DIR, "esim_modification_file_#{udid}")
  end

  # If sim card signal strength is updated
  def sim_signal_strength_file
    File.join(STATE_FILES_DIR, "sim_signal_strength_#{udid}")
  end

  def sim_validation_check_file
    File.join(STATE_FILES_DIR, "sim_validation_check_#{udid}")
  end

  def sim_config_file
    File.join(STATE_FILES_DIR, "sim_config_#{udid}")
  end

  def device_logger_file
    File.join(STATE_FILES_DIR, "device_logger_#{udid}")
  end

  # If call forwarding setup is required
  def call_forwarding_file
    File.join(STATE_FILES_DIR, "call_forwarding_#{udid}")
  end

  def user_entitlement_automation_file
    "/tmp/#{udid}_automation_running"
  end

  def al_user_settings_launch_file
    "/tmp/#{udid}_al_user_settings_launch_running"
  end

  def xcuitest_result_bundle_zip_file
    "/tmp/#{udid}_result-bundle.xcresult.zip"
  end

  def call_logs_file
    File.join(STATE_FILES_DIR, "phone_call_opened_#{udid}")
  end

  def contacts_modified_file
    File.join(STATE_FILES_DIR, "contacts_modified_#{udid}")
  end

  def language_file
    File.join(STATE_FILES_DIR, "add_new_languages_#{udid}")
  end

  def date_time_automatic_file
    File.join(STATE_FILES_DIR, "date_time_automatic_#{udid}")
  end

  def custom_time_file
    File.join(STATE_FILES_DIR, "custom_time_#{udid}")
  end

  def custom_date_file
    File.join(STATE_FILES_DIR, "custom_date_#{udid}")
  end

  def custom_time_format_set_file
    File.join(STATE_FILES_DIR, "custom_time_format_set_#{udid}")
  end

  def force_automatic_date_time_file
    File.join(STATE_FILES_DIR, "force_automatic_date_time_#{udid}")
  end

  def ppuid_file
    File.join(CONFIG_ROOT, "ppuid_#{udid}")
  end

  # This file is for dev use only. It forces the device to update its
  # provisioning profile on the next device check.
  def rotate_certificate_file
    File.join(CONFIG_ROOT, "rotate_certificate_#{udid}")
  end

  # This file causes the device to update its provisioning profile
  # next time it holds @device_check_lock.
  def update_provisioning_profile_file
    File.join(STATE_FILES_DIR, "update_provisioning_profile_#{udid}")
  end

  # This file causes the device to push provisioning profile
  # metrics in Big query
  def update_provisioning_profile_metrics_file
    File.join(STATE_FILES_DIR, "update_provisioning_profile_metrics_#{udid}")
  end

  def needs_provisioning_file
    File.join(STATE_FILES_DIR, "needs_provisioning_#{udid}")
  end

  # Can be used to trigger fix xcodebuild errors with
  # Appium (WDA)
  def resign_wda_file
    File.join(STATE_FILES_DIR, "resign_wda_#{udid}")
  end

  def appstore_login_failure_file
    File.join(STATE_FILES_DIR, "appstore_login_failure_#{udid}")
  end

  def location_services_enabled_file
    File.join(CONFIG_ROOT, "location_services_enabled_#{udid}")
  end

  def photos_permission_file
    File.join(STATE_FILES_DIR, "need_permission_to_access_photos_#{udid}")
  end

  def launcher_photos_permission_file
    File.join(STATE_FILES_DIR, "launcher_need_permission_to_access_photos_#{udid}")
  end

  def v2_video_needs_phased_reboot_file
    File.join(STATE_FILES_DIR, "v2_video_needs_phased_reboot_#{udid}")
  end

  def day_wise_phased_reboot_file
    File.join(STATE_FILES_DIR, "day_wise_phased_reboot_#{udid}")
  end

  def wifi_enabled_file
    File.join(STATE_FILES_DIR, "wifi_enabled_#{udid}")
  end

  def apple_id_sign_in_requested_file
    File.join(STATE_FILES_DIR, "apple_id_sign_in_requested_#{udid}")
  end

  def siri_contacts_cleanup_file
    File.join(STATE_FILES_DIR, "siri_contacts_cleanup_#{udid}")
  end

  def network_simulation_file
    File.join(STATE_FILES_DIR, "network_simulation_#{udid}")
  end

  def device_logger_detected_orientation_lock_changed_file
    File.join(STATE_FILES_DIR, "orientation_lock_changed_#{udid}")
  end

  def device_logger_detected_apple_id_signed_in_file
    File.join(STATE_FILES_DIR, "apple_sign_in_#{udid}")
  end

  def device_logger_detected_sandbox_signed_in_file
    File.join(STATE_FILES_DIR, "sandbox_sign_in_#{udid}")
  end

  def device_logger_detected_testflight_opened_file
    File.join(TMP_DIR_PATH, "testflight_opened_#{udid}")
  end

  def remove_extra_keyboards_file
    File.join(STATE_FILES_DIR, "remove_extra_keyboards_#{udid}")
  end

  def set_default_font_size_file
    File.join(STATE_FILES_DIR, "set_default_font_size_#{udid}")
  end

  def device_logger_detected_safari_launched_file
    File.join(STATE_FILES_DIR, "safari_launched_#{udid}")
  end

  def device_logger_detected_chromium_launched_file
    File.join(STATE_FILES_DIR, "chromium_launched_#{udid}")
  end

  def device_logger_detected_apple_wallet_launched_file
    File.join(STATE_FILES_DIR, "apple_wallet_#{udid}")
  end

  def device_logger_detected_file_download_triggered_file
    File.join(STATE_FILES_DIR, "file_download_triggered_#{udid}")
  end

  def device_logger_detected_safari_favorites_launched_file
    File.join(STATE_FILES_DIR, "safari_add_favorites_#{udid}")
  end

  def device_logger_detected_safari_bookmarks_launched_file
    File.join(STATE_FILES_DIR, "safari_add_bookmark_#{udid}")
  end

  def device_logger_detected_safari_url_bar_changed_file
    File.join(STATE_FILES_DIR, "safari_url_bar_changed_#{udid}")
  end

  def device_logger_detected_safari_tab_groups_opened_file
    File.join(STATE_FILES_DIR, "safari_new_tab_group_opened_#{udid}")
  end

  def device_logger_detected_sandbox_signin_logline_file
    File.join(STATE_FILES_DIR, "sandbox_sign_in_improved_#{udid}")
  end

  def device_logger_detected_sandbox_signin_popup_file
    File.join(STATE_FILES_DIR, "sandbox_sign_in_popup_#{udid}")
  end

  def force_kill_apps_file
    File.join(STATE_FILES_DIR, "force_kill_apps_#{udid}")
  end

  def force_clean_files_app_file
    File.join(STATE_FILES_DIR, "force_clean_files_app_#{udid}")
  end

  def force_clean_safari_file
    File.join(STATE_FILES_DIR, "force_clean_safari_#{udid}")
  end

  def force_clean_chromium_file
    File.join(STATE_FILES_DIR, "force_clean_chromium_#{udid}")
  end

  def force_clean_apple_wallet_file
    File.join(STATE_FILES_DIR, "force_clean_apple_wallet_#{udid}")
  end

  def apple_wallet_double_click_side_button_enabled_file
    File.join(STATE_FILES_DIR, "apple_wallet_double_click_side_button_enabled_#{udid}")
  end

  def force_clean_apple_id_file
    File.join(STATE_FILES_DIR, "force_clean_apple_id_#{udid}")
  end

  def force_clean_sandbox_account_file
    File.join(STATE_FILES_DIR, "force_clean_sandbox_account_#{udid}")
  end

  def force_clean_testflight_file
    File.join(STATE_FILES_DIR, "force_clean_testflight_#{udid}")
  end

  def force_clean_safari_tab_groups_file
    File.join(STATE_FILES_DIR, "force_clean_safari_tab_groups_#{udid}")
  end

  def force_clean_history_from_safari_app_file
    File.join(STATE_FILES_DIR, "force_clean_history_from_safari_app_#{udid}")
  end

  def force_check_orientation_lock_file
    File.join(STATE_FILES_DIR, "force_check_orientation_lock_#{udid}")
  end

  def install_phase_failed_file
    File.join(STATE_FILES_DIR, "install_phase_failed_#{udid}")
  end

  def installations_required_file
    File.join(STATE_FILES_DIR, "installations_required_#{udid}")
  end

  def install_phase_log_file
    File.join(BrowserStack::Configuration['logging_root'], "install_phase_#{udid}.log")
  end

  def force_clean_safari_bookmarks_file
    File.join(STATE_FILES_DIR, "force_clean_safari_bookmarks_#{udid}")
  end

  def force_clean_safari_favorites_file
    File.join(STATE_FILES_DIR, "force_clean_safari_favorites_#{udid}")
  end

  def set_time_to_utc_file
    File.join(STATE_FILES_DIR, "set_time_to_utc_#{udid}")
  end

  def xcodebuild_retry_count_file
    "/tmp/xcodebuild_retry_count_#{udid}"
  end

  def xctest_session_timedout_file
    "/tmp/#{udid}_xctest_timeout"
  end

  def maestro_session_timedout_file
    "/tmp/#{udid}_maestro_timeout"
  end

  def xctest_v2_video_file
    File.join(STATE_FILES_DIR, "xctest_v2_video#{udid}")
  end

  def disable_auto_lock_file
    File.join(STATE_FILES_DIR, "disable_auto_lock_#{udid}")
  end

  def enable_redirect_extension_file
    File.join(STATE_FILES_DIR, "enable_redirect_extension_#{udid}")
  end

  def cleanup_requested_file
    File.join(STATE_FILES_DIR, "cleanup_requested_#{udid}")
  end

  def update_app_settings_file
    File.join(STATE_FILES_DIR, "update_app_settings_#{udid}")
  end

  def device_location_off_file
    File.join(STATE_FILES_DIR, "device_location_off_file_#{udid}")
  end

  def allow_settings_app_file
    # Device logger watches this file to enable disable settings app.
    File.join(STATE_FILES_DIR, "allow_settings_app_#{udid}")
  end

  def allow_settings_app_count_file
    # Device logger watches this file to enable disable settings app based on the count.
    File.join(STATE_FILES_DIR, "allow_settings_app_count_#{udid}")
  end

  def disabled_bluetooth_file
    File.join(STATE_FILES_DIR, "disabled_bluetooth_#{udid}")
  end

  def third_party_account_sign_in_file
    File.join(STATE_FILES_DIR, "third_party_account_sign_in_#{udid}")
  end

  def pwa_enabled_file
    File.join(STATE_FILES_DIR, "pwa_enabled_#{udid}")
  end

  def pwa_cleanup_failure_count_file
    File.join(STATE_FILES_DIR, "pwa_cleanup_failure_count_#{udid}")
  end

  def reset_view_to_standard_file
    File.join(STATE_FILES_DIR, "reset_view_to_standard_#{udid}")
  end

  def assistive_touch_opened_file
    File.join(TMP_DIR_PATH, "assistive_touch_opened_#{udid}")
  end

  def favorite_contact_file
    File.join(TMP_DIR_PATH, "favorite_contact_opened_#{udid}")
  end

  def dark_mode_file
    File.join(STATE_FILES_DIR, "dark_mode_#{udid}")
  end

  def prevent_cross_site_tracking_disabled_file
    File.join(STATE_FILES_DIR, "prevent_cross_site_tracking_disabled_file_#{udid}")
  end

  def prevent_cross_site_tracking_automation_running_file
    File.join(STATE_FILES_DIR, "prevent_cross_site_tracking_automation_running_file_#{udid}")
  end

  def ios_voiceover_automation_running_file
    File.join(STATE_FILES_DIR, "ios_voiceover_automation_running_file_#{udid}")
  end

  def safari_settings_file
    File.join(STATE_FILES_DIR, "safari_settings_file_#{udid}")
  end

  def settings_automation_executing_file
    File.join(STATE_FILES_DIR, "settings_automation_executing_file#{udid}")
  end

  def appstore_location_services_popup_file
    File.join(ALREADY_DISMISSED_DIR_PATH, "already_dismissed_#{udid}")
  end

  def location_service_for_app_session_file
    File.join(CONFIG_DIR_PATH, "location_service_for_app_#{udid}")
  end

  def current_locale_app_live_file
    File.join(TMP_DIR_PATH, "#{udid}_current_locale")
  end

  def back_camera_file
    File.join(TMP_DIR_PATH, "back_camera__#{udid}.png")
  end

  def front_camera_file
    File.join(TMP_DIR_PATH, "front_camera__#{udid}.png")
  end

  def last_camera_check_file
    File.join(CONFIG_DIR_PATH, "last_camera_check_#{udid}")
  end

  def browserstack_app_built_version_file
    File.join(STATE_FILES_DIR, "browserstack_app_built_#{udid}")
  end

  def rebuild_browserstack_app_file
    File.join(STATE_FILES_DIR, "rebuild_browserstack_app_#{udid}")
  end

  def reinstall_browserstack_app_file
    File.join(STATE_FILES_DIR, "reinstall_browserstack_apps_#{udid}")
  end

  def reinstall_browserstack_test_suite_file
    File.join(STATE_FILES_DIR, "reinstall_browserstack_test_suite_#{udid}")
  end

  def disable_siri_contact_suggestions_file
    File.join(STATE_FILES_DIR, "disable_siri_contact_suggestions_#{udid}")
  end

  def siri_search_cleaned_file
    File.join(CONFIG_DIR_PATH, "siri_search_cleaned/siri_search_cleaned_#{udid}")
  end

  def disabled_testflight_notifs_file
    File.join(CONFIG_DIR_PATH, "installed_TestFlight/disabled_TestFlight_notifs_#{udid}")
  end

  def installed_testflight_file
    File.join(CONFIG_DIR_PATH, "installed_TestFlight/installed_TestFlight_#{udid}")
  end

  def installed_com_apple_testflight_installed_file
    File.join(CONFIG_DIR_PATH, "installed_TestFlight/installed_com.apple.TestFlight/installed_#{udid}")
  end

  def com_apple_testflight_file
    File.join(CONFIG_DIR_PATH, "com.apple.TestFlight_#{udid}")
  end

  def cleanup_completed_steps_file
    File.join(STATE_FILES_DIR, "cleanup_completed_steps_#{udid}")
  end

  def cleanupdone_file
    File.join(STATE_FILES_DIR, "cleanupdone_#{udid}")
  end

  def last_successful_cleanup_file
    File.join(STATE_FILES_DIR, "last_successful_cleanup_#{udid}")
  end

  def nat_setup_complete_file
    File.join(STATE_FILES_DIR, "nat_setup_complete_#{udid}")
  end

  def needs_phased_reboot_file
    File.join(TMP_DIR_PATH, "need_phased_reboot_#{udid}")
  end

  def reboot_file
    File.join(TMP_DIR_PATH, "reboot_#{udid}")
  end

  def webdriveragent_file
    File.join(TMP_DIR_PATH, "webdriveragent_#{udid}")
  end

  def session_info_file
    File.join(TMP_DIR_PATH, "_mobile/session/#{udid}/session_info.json")
  end

  def session_start_file
    File.join(TMP_DIR_PATH, "session_start/#{udid}")
  end

  def session_lock_file
    File.join(STATE_FILES_DIR, "#{udid}_session.lock")
  end

  def session_file
    File.join(STATE_FILES_DIR, "#{udid}_session")
  end

  def offline_mode_file
    File.join(STATE_FILES_DIR, "offline_mode_#{udid}")
  end

  def re_mdm_file
    File.join(STATE_FILES_DIR, "re_mdm_#{udid}")
  end

  def re_mdm_requested_file
    File.join(STATE_FILES_DIR, "re_mdm_requested_#{udid}")
  end

  def app_store_popup_handled_file
    File.join(STATE_FILES_DIR, "app_store_popup_handled_#{udid}")
  end

  def force_backup_redownload_file
    File.join(STATE_FILES_DIR, "force_backup_redownload_#{udid}")
  end

  def passcode_file
    File.join(STATE_FILES_DIR, "passcode_#{udid}")
  end

  def voiceover_used_file
    File.join(STATE_FILES_DIR, "voiceover_used_#{udid}")
  end

  def local_chrome_extension_file
    File.join(STATE_FILES_DIR, "local_chrome_extension_#{udid}")
  end

  def dedicated_cleanup_file
    File.join(STATE_FILES_DIR, "dedicated_cleanup_#{udid}")
  end

  def dedicated_device_file
    File.join(STATE_FILES_DIR, "dedicated_device_#{udid}")
  end

  def dedicated_video_state_file
    File.join(STATE_FILES_DIR, "dedicated_video_state_#{udid}")
  end

  def custom_mdm_file
    File.join(STATE_FILES_DIR, "custom_mdm_#{udid}")
  end

  def custom_mdm_perform_setup_file
    File.join(STATE_FILES_DIR, "custom_mdm_perform_setup_#{udid}")
  end

  def custom_mdm_remove_setup_file
    File.join(STATE_FILES_DIR, "custom_mdm_remove_setup_#{udid}")
  end

  def re_mdm_periodic_check_file
    File.join(STATE_FILES_DIR, "re_mdm_periodic_check_#{udid}")
  end

  def clean_preloaded_media_with_reboot_file
    File.join(STATE_FILES_DIR, "clean_preloaded_media_with_reboot_#{udid}")
  end

  def needs_preload_media_file
    "#{STATE_FILES_DIR}/needs_preload_media_#{udid}"
  end

  def download_files_setup_done_file
    "#{STATE_FILES_DIR}/download_file_setup_done_#{udid}"
  end

  def fallback_to_v1_file
    File.join(TMP_DIR_PATH, "fallback_to_v1_#{udid}")
  end

  def install_cfgutil_restrictions_profile_file
    "#{STATE_FILES_DIR}/install_cfgutil_restrictions_profile_#{udid}"
  end

  def app_store_login_file
    "#{STATE_FILES_DIR}/app_store_login_#{udid}"
  end

  def bs_launcher_install_file
    "#{STATE_FILES_DIR}/bs_launcher_install_#{udid}"
  end

  def incompatible_machine_for_update_file
    "#{STATE_FILES_DIR}/incompatible_machine_for_update_#{udid}"
  end

  def update_manager_log_file
    File.join(BrowserStack::Configuration['logging_root'], "device_update_#{udid}.log")
  end

  def undergoing_update_file
    "#{STATE_FILES_DIR}/undergoing_update_#{udid}"
  end

  def update_failed_file
    "#{STATE_FILES_DIR}/update_failed_#{udid}"
  end

  def low_power_mode_file
    File.join(STATE_FILES_DIR, "low_power_mode_#{udid}")
  end

  def icloud_access_file
    "#{STATE_FILES_DIR}/icloud_access_#{udid}"
  end

  def full_cleanup_file
    "#{STATE_FILES_DIR}/full_cleanup_#{udid}"
  end

  def mdm_full_cleanup_file
    "#{STATE_FILES_DIR}/mdm_full_cleanup_#{udid}"
  end

  def disable_full_cleanup_file
    "#{STATE_FILES_DIR}/disable_full_cleanup_#{udid}"
  end

  def assistive_touch_configuration_file
    File.join(STATE_FILES_DIR, "assistive_touch_configuration_#{udid}")
  end

  def device_logger_detected_contacts_app_opened_file
    "#{STATE_FILES_DIR}/contacts_app_opened_#{udid}"
  end

  def device_logger_apple_id_present_file
    "#{STATE_FILES_DIR}/apple_id_present_#{udid}"
  end

  def set_safari_default_browser_in_eu_file
    "#{STATE_FILES_DIR}/set_safari_default_browser_in_eu_#{udid}"
  end

  def force_set_safari_default_browser_in_eu_file
    "#{STATE_FILES_DIR}/force_set_safari_default_browser_in_eu_#{udid}"
  end

  def accessibility_settings_file
    File.join(STATE_FILES_DIR, "accessibility_settings_#{udid}")
  end

  def apple_pay_data_file
    File.join(STATE_FILES_DIR, "apple_pay_data_#{udid}")
  end

  def apple_pay_assistive_touch_file
    File.join(STATE_FILES_DIR, "apple_pay_assistive_touch_#{udid}")
  end

  def session_start_indicator_file
    File.join(STATE_FILES_DIR, "#{udid}_session_start_indicator")
  end

  def apple_pay_sensitive_data_cleanup_failure_file
    File.join(STATE_FILES_DIR, "apple_pay_sensitive_data_cleanup_failure_#{udid}")
  end

  def aa_mcspt_measurement_enabled_file
    File.join(STATE_FILES_DIR, "aa_mcspt_measurement_enabled_#{udid}")
  end

  def apple_pay_configuration_file
    File.join(STATE_FILES_DIR, "apple_pay_configuration_#{udid}")
  end

  def cfgutil_profiles_cache_file
    File.join(STATE_FILES_DIR, "cfgutil_profiles_cached_#{udid}.json")
  end

  def configuration_profile_periodic_check_file
    File.join(STATE_FILES_DIR, "configuration_profile_period_check_#{udid}")
  end

  def supervision_identities_checked_file
    File.join(STATE_FILES_DIR, "supervision_identities_checked_#{udid}")
  end

  def supervision_identity_details_fetched_file
    File.join(STATE_FILES_DIR, "supervision_identity_details_fetched_#{udid}")
  end

  def appstore_automation_file
    File.join(TMP_DIR_PATH, "automation_#{udid}")
  end

  def geoguard_cleanup_file
    File.join(STATE_FILES_DIR, "geoguard_cleanup_#{udid}")
  end

  def geoguard_failed_file
    File.join(STATE_FILES_DIR, "geoguard_failed_#{udid}")
  end

  def disable_geoguard_notifications_file
    File.join(STATE_FILES_DIR, "disable_geoguard_notifications_#{udid}")
  end

  def enable_message_notification_failure_file
    File.join(STATE_FILES_DIR, "enable_message_notification_failure_#{udid}")
  end

  def chrome_cleanup_required_file
    File.join(STATE_FILES_DIR, "chrome_cleanup_required_#{udid}")
  end

  def tmp_use_path_without_date_file
    File.join(STATE_FILES_DIR, "tmp_use_path_without_date")
  end

  def multitasking_view_opened_file
    File.join(STATE_FILES_DIR, "multitasking_view_opened_#{udid}")
  end

  def app_a11y_app_details_file
    File.join(STATE_FILES_DIR, "app_a11y_app_details_#{udid}")
  end

  def app_a11y_deduplication_hash_file
    File.join(STATE_FILES_DIR, "app_a11y_session_hash_#{udid}.json")
  end

  def rebooted_recently_file
    File.join(STATE_FILES_DIR, "rebooted_recently_#{udid}")
  end

  def enable_restriction_feature_file
    File.join(TMP_DIR_PATH, "enable_restriction_#{udid}")
  end

  def disable_testflight_background_refresh_file
    File.join(STATE_FILES_DIR, "disable_testflight_background_refresh_#{udid}")
  end

  def sign_in_to_app_store_popup_detected_file
    File.join(STATE_FILES_DIR, "sign_in_to_app_store_popup_detected_#{udid}")
  end

  def clean_stored_password_file
    File.join(STATE_FILES_DIR, "clean_saved_password_#{udid}")
  end

  def device_logger_detected_password_added_file
    File.join(STATE_FILES_DIR, "password_added_#{udid}")
  end

  def cleanup_count_file
    File.join(STATE_FILES_DIR, "cleanup_count_#{udid}")
  end

  def wda_uninstalled_file
    File.join(STATE_FILES_DIR, "wda_uninstalled_#{udid}")
  end

  def custom_certificate_installed_file
    File.join(STATE_FILES_DIR, "custom_certificate_installed_#{udid}")
  end

  def clean_provisioning_profile_file
    File.join(STATE_FILES_DIR, "clean_provisioning_profile_#{udid}")
  end

  def provisioning_profile_cleaned_file
    File.join(STATE_FILES_DIR, "provisioning_profile_cleaned_#{udid}")
  end

  def clean_sms_file
    File.join(STATE_FILES_DIR, "clean_sms_file_#{udid}")
  end

  def theme_change_attempted_file
    File.join(STATE_FILES_DIR, "theme_change_attempted_#{udid}")
  end

  def theme_change_cleaned_file
    File.join(STATE_FILES_DIR, "theme_change_cleaned_#{udid}")
  end

  def disable_apple_intelligence_file
    File.join(STATE_FILES_DIR, "disable_apple_intelligence_#{udid}")
  end

  def ios_sms_app_opened_file
    File.join(STATE_FILES_DIR, "sms_app_opened_#{udid}")
  end

  def device_logger_detected_stage_manager_enabled_file
    File.join(STATE_FILES_DIR, "stage_manager_enabled_#{udid}")
  end

  def disable_stage_manager_file
    File.join(STATE_FILES_DIR, "disable_stage_manager_#{udid}")
  end

  def check_weekend_or_file_older?(file_path)
    return false unless [6, 7].include?(Date.today.cwday)

    File.mtime(file_path) < (Time.now - (60 * 60 * 24 * 3))
  end

  def device_logger_detected_safari_website_settings_opened_file
    File.join(STATE_FILES_DIR, "safari_website_settings_opened_#{udid}")
  end

  def device_logger_detected_safari_vfx_used_file
    File.join(STATE_FILES_DIR, "safari_vfx_used_#{udid}")
  end

  def restart_webkit_proxy_file
    File.join(STATE_FILES_DIR, "restart_webkit_proxy_#{udid}")
  end

  def session_bm_file
    File.join(STATE_FILES_DIR, "session_bm_#{udid}")
  end

  def end_call_optimised_file
    File.join(STATE_FILES_DIR, "end_call_optimised_#{udid}")
  end
end
