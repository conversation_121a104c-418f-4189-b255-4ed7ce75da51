require 'pry'

require_relative '../utils/idevice_ffi/idevice_require'
require_relative '../utils/idevice_ffi/assistive_touch_util'
require_relative '../utils/idevice_ffi/manage_calendar_util'
require_relative '../utils/idevice_ffi/icon_layout_util'
require_relative '../utils/idevice_ffi/screen_shot_util'
require_relative '../utils/idevice_utils'
require_relative '../utils/idevice_ffi/accessibility_settings_util'
require_relative '../utils/pymobiledevice'
require_relative '../configuration'
require_relative '../../lib/wrappers/cfg_util'
require_relative '../../lib/utils/devicectl'
require_relative './device_state'
require 'json'
require 'openssl'

module BrowserStack
  class IosDevice # rubocop:todo Metrics/ClassLength

    def self.run_from_bash
      device_id = ARGV[0]
      function_to_call = ARGV[1].to_s.strip.downcase.to_sym

      logger = Logger.new($stdout)

      device = BrowserStack::IosDevice.new(device_id, "shell", logger)
      case function_to_call
      when :console
        binding.pry # rubocop:todo Lint/Debugger
      else
        device.send(function_to_call)
      end
    end

    attr_reader :device_id, :logger

    # <device-id> String, The device id
    #
    # <init_from> String, Name of the component owning this device object, i.e.
    # "Cleanup", "DeviceCheck", etc
    #
    def initialize(device_id, init_from, logger, logger_params = {})
      raise ArgumentError, "Invalid device id: #{device_id}" if device_id.to_s == ""

      conf = BrowserStack::Configuration.new
      @server_config = conf.all
      IdeviceUtils.configure(@server_config)

      logger_params[:component] = self.class.to_s
      logger_params[:device] = device_id

      @device_state_file = DeviceState.new(device_id)
      @device_id = device_id
      @init_from = init_from
      @logger = logger
      @logger_params = logger_params

      @logger.info "IosDevice Object Initialized"
    end

    def idevice_ffi
      @idevice_ffi ||= Idevice::Idevice.attach(udid: device_id)
    rescue => e
      # Idevice gem won't load in tests, hence ignoring these errors for now
      raise e if ENV['RACK_ENV'] != 'test'
    end

    def proxy_pac_url
      "http://#{device_config['ip']}:45691/pacfile?device=#{device_id}"
    end

    # [path_to_crt, path_to_der]
    def supervision_certs_base_path
      return @supervision_certs_base_path if @supervision_certs_base_path

      identities_path = "#{CONFIG_ROOT}/supervision_identities"
      region = @server_config['static_conf']['region']
      index = region.length - 3
      filename = region[0..index]
      @supervision_certs_base_path = "#{identities_path}/#{filename}"

      @supervision_certs_base_path
    end

    def supervision_crt
      @supervision_crt ||= "#{supervision_certs_base_path}.crt"
    end

    def supervision_der
      @supervision_der ||= "#{supervision_certs_base_path}.der"
    end

    def mdm_server_url
      @server_config["mdm_server_url"]
    end

    def device_state
      @device_state_file
    end

    def device_config
      @device_config ||= JSON.parse(File.read(CONFIG_JSON_FILE))["devices"][@device_id]
      raise "device not found in config" if @device_config.nil?

      @device_config
    rescue => e
      log "Unable to load device_config #{e.message} #{e.backtrace.join("\n")}"
      raise "Unable to load device_config for device: #{@device_id}"
    end

    def device_version
      @device_version ||= Gem::Version.new(device_config["device_version"])
    end

    def device_name
      @device_name ||= device_config["device_name"]
    end

    def device_type
      device_name.match(/iPad/i).nil? ? "iPhone" : "iPad"
    end

    def device_ecid
      @device_ecid ||= device_config["device_ecid"]

      if @device_ecid.to_s.empty?
        nil
      else
        @device_ecid
      end
    end

    def cfgutil
      @cfgutil ||= CFGUtil.new(udid: device_id, ecid: device_ecid)
    end

    def sub_region
      @sub_region ||= Gem::Version.new(device_config["sub_region"])
    end
    alias datacenter sub_region

    def enable_assistive_touch
      assistive_touch_util = AssistiveTouchUtil.new(idevice_ffi: idevice_ffi)
      assistive_touch_util.enable
    end

    def reincarnate_assistive_touch
      assistive_touch_util = AssistiveTouchUtil.new(idevice_ffi: idevice_ffi)
      assistive_touch_util.reincarnate
    end

    def disable_assistive_touch
      assistive_touch_util = AssistiveTouchUtil.new(idevice_ffi: idevice_ffi)
      assistive_touch_util.disable
    end

    def toggle_assistive_touch
      assistive_touch_util = AssistiveTouchUtil.new(idevice_ffi: idevice_ffi)
      if assistive_touch_util.enabled?
        assistive_touch_util.disable
      else
        assistive_touch_util.enable
      end
    end

    def clear_local_calendar_events
      manage_calendar_util = ManageCalendarUtil.new(idevice_ffi: idevice_ffi)
      manage_calendar_util.delete_all
    end

    def take_screenshot
      screenshot_util = ScreenShotUtil.new(idevice_ffi: idevice_ffi)
      screenshot_util.take_screenshot
    end

    def accessibility_settings
      AccessibilitySettingsUtil.new(idevice_ffi: idevice_ffi)
    end

    def run_idevice_utils(command, *args)
      IdeviceUtils.send(command.to_sym, @device_id, *args)
    end

    def devicectl_device_state
      DeviceCtl::List.device_state(@device_id)
    end

    # Returns an array of profiles
    # [
    #    {
    #      "identifier": "Diontec-MacBook-Pro.14826579-EFD7-41F1-958C-88407DE7F7C2",
    #      "version": 1,
    #      "displayName": "Wifi",
    #      "uuid": "XYZ"
    #    },
    #    {
    #      "identifier": "com.github.micromdm.micromdm.enroll",
    #      "version": 1,
    #      "displayName": "Enrollment Profile",
    #      "uuid": "XYZ"
    #    }
    # ]
    def installed_configuration_profiles(cached: false)
      if cached && @device_state_file.cfgutil_profiles_cache_file_present?
        logger.info("Using cached installed profiles")
        return JSON.parse(@device_state_file.read_cfgutil_profiles_cache_file)
      end

      profiles_json = PyMobileDevice::Profile.list_profiles(@device_id)
      profiles = profiles_json["ProfileMetadata"].map do |key, data|
        profile_hash = {}
        profile_hash["identifier"] = key
        profile_hash["version"] = data["PayloadVersion"]
        profile_hash["displayName"] = data["PayloadDisplayName"]
        profile_hash["uuid"] = data["PayloadUUID"]
        profile_hash
      end
      # update cache
      @device_state_file.write_to_cfgutil_profiles_cache_file(profiles.to_json)
      logger.info("Installed profiles on the device: #{profiles}")

      profiles
    end

    #  Example identifier: "com.github.micromdm.micromdm.enroll"
    #  This will only fetch profiles installed via cfgutil (Apple Configurator 2)
    #
    #  Returns:
    #   {
    #     "identifier": "com.github.micromdm.micromdm.enroll",
    #     "version": 1,
    #     "displayName": "Enrollment Profile"
    #   }
    def fetch_installed_profile(identifier, cached: false)
      logger.info("Checking if #{identifier} is installed on the device")
      profile_data = installed_configuration_profiles(cached: cached).select do |profile|
        profile["identifier"] == identifier
      end
      logger.info("#{identifier} profile details : #{profile_data.first}")

      profile_data.first
    end

    #  Example identifier: "com.github.micromdm.micromdm.enroll"
    def remove_profile(identifier)
      logger.info("Removing #{identifier} profile from the device")
      cfgutil.remove_profile(profile_payload_id: identifier)
    end

    #  Example identifier: "com.github.micromdm.micromdm.enroll"
    def install_profile(identifier, mobileconfig_path)
      logger.info("Installing #{identifier} profile on the device")
      cfgutil.install_profile_supervised(mobileconfig_path, supervision_crt, supervision_der)
      logger.info("#{identifier} succesfully installed on the device !")
    end

    def supervised_pair
      logger.info("Supervised pairing")
      cfgutil.pair(supervision_crt, supervision_der)
    end

    def erase
      cfgutil.erase
    end

    def supervise_device
      raise "CFGUTIL or Supervision certificate does not exist to supervise the device" unless supervision_identities_exist_and_cfgutil_installed?

      supervision_crt_details = OpenSSL::X509::Certificate.new(File.read(supervision_crt))
      organization_detail = supervision_crt_details.issuer.to_a.detect { |detail| detail[0] == 'O' }

      cfgutil.supervise(supervision_crt, organization_detail[1].to_s)
    end

    # Don't remove instance ids or device from this list, as it won't be possible to revert profiles if required later.
    def enable_new_provisioning_flow?
      return true if device_version >= Gem::Version.new('18.0')

      device_version >= Gem::Version.new('17.0') &&
      (
        device_name == "iPhone16,1" || # iPhone 15 Pro
        device_name == "iPhone16,2" || # iPhone 15 Pro Max
        (device_name == "iPhone15,4" && device_version >= Gem::Version.new('17.4')) || # iPhone 15
        device_name == "iPhone15,5" || # iPhone 15 Plus
        device_name == "iPad16,3" || # iPad Pro 11 inch, 2024
        device_name == "iPad14,10" || # iPad Air 13.0 2024
        device_name == "iPad16,5"  # iPad Pro 13 inch, 2024
      )
    end

    def device_eligible_for_optimised_flow?
      is_valid_version = device_version >= Gem::Version.new(OPTIMISED_PRELOAD_MEDIA_SUPPORT_VERSION[:min_os_version].to_s)

      BrowserStack.logger.info "device with os_ver #{device_version} valid for optimised flow" if is_valid_version

      is_valid_version
    end

    def enable_new_notifications_profile_flow?
      device_version >= Gem::Version.new('17.0')
    end

    def supervision_identities_exist_and_cfgutil_installed?
      return false if !File.exists?(supervision_crt) || !File.exists?(supervision_der)
      return false unless cfgutil.installed?

      true
    end

    def cfgutil_installed?
      cfgutil.installed?
    end

    def supervision_identities_correct?
      retries_left = 3
      begin
        org_name = cfgutil.get_property('organizationName', supervision_crt, supervision_der)
        return false if org_name.nil?

        supervision_crt_details = OpenSSL::X509::Certificate.new(File.read(supervision_crt))
        organization_detail = supervision_crt_details.issuer.to_a.detect { |detail| detail[0] == 'O' }
        @logger.info "Supervision certs: #{supervision_crt_details}"
        @logger.info "Organization Detail: #{organization_detail}"
        return false if organization_detail.nil?

        # Here we are assuming that if the organization name in the device matches the supervision identities organization name
        # then we assume that DC team has supervised device using the same supervision identity that is present on the machine.
        # However, we had seen that sometimes organization name is different even when supervised with same certificate.
        # Example: 00008130-001C509421E2001C this device, has organizationName as Browserstack instead of BrowserStack USE even when supervised with same identity.
        org_name.to_s.downcase == organization_detail[1].to_s.downcase || org_name.to_s.downcase == "BrowserStack_USW_new".to_s.downcase
      rescue => e
        retries_left -= 1
        retry if retries_left > 0
        raise e
      end
    end

    def supervision_identity_details
      retries_left = 3
      begin
        cfgutil.get_property_without_crt_files('organizationName')

      rescue => e
        retries_left -= 1
        retry if retries_left > 0
        raise e
      end
    end

    def device_provisioning_profile
      ppuid_file = "#{@server_config['config_root']}/ppuid_#{@device_id}"
      ppuid_config = File.read(ppuid_file)
      ppuid_config.lines[2].strip
    end
  end
end

# Called from bash
BrowserStack::IosDevice.run_from_bash if $PROGRAM_NAME == __FILE__
