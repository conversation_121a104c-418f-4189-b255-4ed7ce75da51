require_relative '../../server/device_manager'
require_relative '../utils/screenshot_util'
require_relative './gcs_manager'
require_relative './renderer_key_check'
require_relative './util'

module Percy
  Tile = Struct.new(:path, :build_id, :url, :error, :seq_no)

  # In this class we capture screenshot via appium, use convert to strip headers/footers and
  # upload them to our gcp bucket using gcs_manager class. A Tile is a screenshot taken via appium.
  class TilesManager # rubocop:todo Metrics/ClassLength
    DEFAULT_TEMP_IMAGE_DIR = '/tmp/'
    MAX_UPLOAD_RETRY_COUNT = 30 # => 15 sec right now. Its fine so far but might have to increase

    attr_accessor :device_id, :renderer_key_check

    def initialize(device_id)
      @device_id = device_id
      @renderer_key_check = RendererKeyCheck.new(device_id)
    end

    def capture_tile(appium_session_id, build_id, seq_no, strip, bucket, renderer_key: nil)
      renderer_key_check.validate_renderer_key(renderer_key: renderer_key)
      # init state if seq_no is 0 as else it will be nil
      init_state if seq_no == 0

      # capture
      image_content = ScreenshotsUtil.capture_screenshot_via_appium(device_id, appium_session_id)

      tile = Tile.new
      tile.build_id = build_id
      tile.seq_no = seq_no
      save_tile(tile)
      Thread.bs_run { capture_tile_bg(tile, image_content, strip, bucket) }
    end

    def capture_tile_bg(tile, image_content, strip, bucket)
      # store and strip
      tile.path = image_file_path(tile.build_id, tile.seq_no)
      save_image(tile.path, image_content)
      strip_image(tile.path, strip)

      # upload
      upload_tile(bucket, tile)
    rescue => e
      tile.error = e.message
    ensure
      save_tile(tile)
    end

    def finalize(expected_tiles, renderer_key: nil)
      renderer_key_check.validate_renderer_key(renderer_key: renderer_key)

      i = 0
      until upload_done?(expected_tiles)
        sleep(0.5)
        i += 1
        raise "upload not done, #{tiles}" if i == MAX_UPLOAD_RETRY_COUNT
      end
      tiles_list = tiles
      # post cleanup tiles cant be read
      # TODO: maybe remove it from there and only keep in cleanup
      cleanup
      tiles_list
    end

    def cleanup
      delete_tmp_images_folder
    end

    def tiles
      tiles_list = []
      Dir.glob("#{tmp_images_dir}/*_meta").each do |meta_file|
        tiles_list << load_tile(meta_file)
      end
      tiles_list.sort_by(&:seq_no)
    end

    private

    # We should always cleanup and init states correctly to maintain consistency and
    # to not take the risk of mixing multiple snapshot tiles
    def init_state
      delete_tmp_images_folder
      create_tmp_images_folder
    end

    def save_image(path, base64content)
      File.open(path, 'wb+') do |f|
        f.puts(Base64.decode64(base64content))
      end
    end

    def save_tile(tile)
      Percy::PercyUtil.write_to_file_with_lock(tile_meta_file_path(tile.build_id, tile.seq_no), tile.to_h.to_json)
    end

    def load_tile(meta_path)
      file_content = Percy::PercyUtil.read_file_with_lock(meta_path)
      tile_hash = JSON.parse(file_content)
      tile = Tile.new
      tile.path = tile_hash["path"]
      tile.build_id = tile_hash["build_id"]
      tile.url = tile_hash["url"]
      tile.error = tile_hash["error"]
      tile.seq_no = tile_hash['seq_no']
      tile
    end

    def strip_image(path, strip)
      return if strip['header_height'].nil? && strip['footer_height'].nil? && strip['left_offset'].nil? && strip['right_offset'].nil?

      strip['header_height'] ||= 0
      strip['footer_height'] ||= 0
      strip['right_offset'] ||= 0
      strip['left_offset'] ||= 0

      command = [
        (ENV['IMAGEMAGICK_CONVERT']).to_s,
        path,
        '-strip -define png:include-chunk=none',
        "-gravity North -chop 0x#{strip['header_height']}",
        "-gravity South -chop 0x#{strip['footer_height']}"
      ]

      command << "-gravity East -chop #{strip['right_offset']}x0" if strip['right_offset'] != 0
      command << "-gravity West -chop #{strip['left_offset']}x0" if strip['left_offset'] != 0

      command << path
      res = system(command.join(' '))
      return if res

      raise "Stripping header/footer failed."
    end

    def image_file_path(build_id, seq_no)
      "#{tmp_images_dir}/#{build_id}_#{seq_no}"
    end

    def tile_meta_file_path(build_id, seq_no)
      "#{image_file_path(build_id, seq_no)}_meta"
    end

    def tmp_images_dir
      "/tmp/#{device_id}/tiles"
    end

    def create_tmp_images_folder
      system("mkdir -p #{tmp_images_dir}")
    end

    def delete_tmp_images_folder
      system("rm -rf #{tmp_images_dir}")
    end

    def upload_file_name(tile)
      content = File.read(tile.path)
      image_sha = Digest::SHA256.hexdigest(content)
      "#{image_sha}-#{tile.build_id}"
    end

    def upload_tile(bucket, tile)
      tile.url = GCSManager.upload(
        bucket, upload_file_name(tile), tile.path, device_id, cert_delete_access: false
      )
    rescue => e
      tile.error = e.message
    end

    def upload_done?(expected_tiles)
      tiles_list = tiles
      raise "Incorrect number of tiles" if tiles_list.length != expected_tiles

      tiles_list.each do |tile|
        return false if tile.url.nil? && tile.error.nil?
      end

      true
    end
  end
end
