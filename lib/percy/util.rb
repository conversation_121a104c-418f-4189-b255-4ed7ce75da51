# frozen_string_literal: true

require 'socket'
require 'date'
require 'timeout'
require_relative '../utils/zombie'
require_relative '../utils/helpers'
require_relative '../../server/device_manager'

module Percy
  # This module contains util functions for files under /percy folder
  module PercyUtil # rubocop:todo Metrics/ModuleLength
    @environment = 'staging' # or production if set

    class << self
      attr_accessor :environment
    end

    def self.logit(event_name)
      event_data = {
        product: 'percy',
        success: false,
        message: '',
        abort: false
      }

      start_time = Time.now
      begin
        yield event_data
      rescue => e
        event_data[:success] = false
        event_data[:message] = e.message
        event_data[:stacktrace] = e.backtrace
        raise e # we raise it again so that caller can handle it
      end
    ensure # this is added so that we send instrumentation even if user returns in block
      end_time = Time.now
      event_data[:duration] = ((end_time - start_time) * 1000).to_i
      send_instrumentation(*make_event_data(event_name, event_data)) unless event_data[:abort]
    end

    def self.make_event_data(event_name, event_data)
      begin
        device_id = event_data[:device_id]
        unless event_data[:automation_session_id]
          sess_data = JSON.parse(File.read(DeviceManager.session_file(device_id)))
          event_name = "#{sess_data['env']['PLATFORM']}_#{event_name}"
          @environment = sess_data['env']['environment'] == 'production' ? 'production' : 'staging'
        end
      rescue => e
        event_data[:i_success] = false
        event_data[:i_message] = e.message
        event_data[:i_stacktrace] = e.backtrace
        event_name = "unknown_#{event_name}"
      end

      [event_name, event_data]
    end

    def self.send_instrumentation(event_name, data)
      has_comparison_id = data.key?(:comparison_id)

      if has_comparison_id # cls data
        send_to_cls(event_name, data)
      else
        send_to_pager(event_name, data)
      end
    end
    # private

    def self.send_to_cls(event_name, data)
      data['app'] = 'ios_safari'
      params = {
        'genre' => 'percy',
        'percy_comparison_id' => data[:comparison_id].to_s
      }
      # send
      begin
        push_to_cls(params, event_name, "", data, nil, @environment)
        BrowserStack.logger.info("Data sent to cls")
      rescue => e
        BrowserStack.logger.info("failed to send to cls: #{e.message}")
      end

      send_to_pager(event_name, data) unless data[:success]
    end

    def self.send_to_pager(event_name, data)
      data = data.clone
      event_name += (data[:success] ? '_1' : '_0')

      pager_data = {
        'category' => 'percy-platform:ios',
        'kind' => event_name,
        'session_id' => data.delete(:comparison_id),
        'error' => data.delete(:message),
        'browser' => 'ios-safari',
        'terminal_type' => 'ios-njb',
        'browser_version' => data.delete(:browser_version),
        'team' => 'percy-platform'
      }
      # this is all remaining data
      pager_data['data'] = data

      # Handling pager for Prod iPhones blocked for staging
      pager_data['data']['platform'] = 'iphone_staging' if @environment == 'staging'

      # send
      begin
        BrowserStack::Zombie.push_logs(event_name , '', pager_data, @environment)
        BrowserStack.logger.info("Data sent to pager")
      rescue => e
        BrowserStack.logger.info("failed to send to pager: #{e.message}")
      end
    end

    def self.write_to_file_with_lock(file_name, data, lock_timeout: 5)
      File.open(file_name, File::RDWR | File::CREAT, 0o644) do |f|
        Timeout.timeout(lock_timeout) { f.flock(File::LOCK_EX) }
        f.rewind
        f.write(data)
        f.flush
        f.truncate(f.pos)
      rescue Timeout::Error => e
        BrowserStack.logger.info("Timeout Error, #{e.message}")
      end
    end

    def self.read_file_with_lock(file_name, lock_timeout: 5)
      File.open(file_name, 'r') do |f|
        Timeout.timeout(lock_timeout) { f.flock(File::LOCK_SH) }
        f.read
      rescue Timeout::Error => e
        BrowserStack.logger.info("Timeout Error while acquiring lock on file: #{file_name}, #{e.message}")
      end
    end
  end
end
