# frozen_string_literal: true

require_relative '../utils/osutils'

module Percy
  class JackproxyManager

    JACKPROXY_DEPS = '/usr/local/.browserstack/deps/percy/jackproxy'
    JACKPROXY_FOLDER = '/usr/local/.browserstack/realmobile/lib/percy/jackproxy'

    attr_reader :device, :jp_executable

    def initialize(device, jp_executable, binaries_bucket = nil)
      @device = device
      @jp_executable = jp_executable
      @binaries_bucket = "https://storage.googleapis.com/#{
        !binaries_bucket.nil? ? binaries_bucket : 'percy-dev-binaries'
      }"
    end

    def setup_jackproxy
      return system("curl -o #{jp_executable_path} --create-dirs #{@binaries_bucket}/jackproxy/#{jp_executable} && chmod +x #{jp_executable_path}") unless File.file?(jp_executable_path)

      true
    end

    def start_jackproxy(params, proxy_map_file_path, env_vars)
      raise "Could not find with JP version to use" if @jp_executable.nil?

      jackproxy_start_command =
        "#{jp_env(env_vars)} "\
        "#{jp_executable_path} "\
        "--key #{JACKPROXY_DEPS}/keys/server.key " \
        "--pem #{JACKPROXY_DEPS}/keys/server.pem " \
        "--proxymap #{proxy_map_file_path} " \
        "--port #{jackproxy_port} " \
        "--https-port #{jackproxy_https_port} " \
        "#{params} " \
        " > #{jackproxy_log_file_path} 2>&1"

      Thread.bs_run { system(jackproxy_start_command) }
    end

    def stop_jackproxy?
      BrowserStack::OSUtils.kill_process("jackproxy", "'port #{jackproxy_port}'")
      !jackproxy_running?
    end

    def jp_env(env_vars)
      vars = ['HONEYCOMB_WRITE_KEY', 'HONEYCOMB_DATASET', 'PLATFORM']
      env = ""
      vars.each do |var|
        env += "#{var}=#{env_vars[var]} " if env_vars[var]
      end
      env
    end

    def jp_executable_path
      "#{JACKPROXY_DEPS}/binaries/#{jp_executable}" if jp_executable
    end

    def jackproxy_log_file_path
      "#{JACKPROXY_FOLDER}/logs/jackproxy.#{jackproxy_port}.log"
    end

    def proxy_map_file_path
      "#{JACKPROXY_FOLDER}/proxymapdata/#{proxy_map_file_name}"
    end

    def jackproxy_running?
      BrowserStack::OSUtils.is_process_running?("jackproxy", "'port #{jackproxy_port}'")
    end

    private

    def jackproxy_port
      "2#{device['port']}"
    end

    def jackproxy_https_port
      "3#{device['port']}"
    end

    def proxy_map_file_name
      "proxy_map.#{jackproxy_port}.json"
    end
  end
end
