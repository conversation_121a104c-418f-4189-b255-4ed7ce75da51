# frozen_string_literal: true

require 'json'
require 'fileutils'
require 'net/http'

require_relative '../../server/device_manager'
require_relative '../privoxy_manager'
require_relative './browserup_manager'
require_relative './jackproxy_manager'
require_relative './util'
require_relative '../utils/osutils'
require_relative '../utils/idevice_utils'
require_relative '../session/session'
require_relative './tiles_manager'
require_relative './renderer_key_check'
require_relative '../utils/device_logger'
require_relative '../models/device_state'

require 'percy_on_automate_common'

module Percy
  class PercySession # rubocop:todo Metrics/ClassLength

    JACKPROXY_DEPS = '/usr/local/.browserstack/deps/percy/jackproxy'
    JACKPROXY_FOLDER = '/usr/local/.browserstack/realmobile/lib/percy/jackproxy'
    APPIUM_VERSION = '1.22.0'
    SERVICE_ACCOUNT_PROD_FILE_PATH_DEF = '/usr/local/.browserstack/realmobile/keys/percy/gcloud.json'
    SERVICE_ACCOUNT_STAGING_FILE_PATH = '/usr/local/.browserstack/realmobile/keys/percy/gcloud_staging.json'
    DEFAULT_SOURCE_HOSTNAME = 'render.percy.local'
    attr_reader :device_id, :device, :sess, :browserup_manager, :jp_executable
    attr_accessor :tiles_manager, :jp_manager, :renderer_key_check

    def self.default_percy_tmp_dir(device_id)
      "/tmp/percy/#{device_id}/certs"
    end

    def self.create_tmp_cert_folder(device_id)
      FileUtils.mkdir_p [
        PercySession.default_percy_tmp_dir(device_id).to_s
      ]
    end

    def self.gcp_tmp_file_path(device_id)
      "#{PercySession.default_percy_tmp_dir(device_id)}/gcloud.json"
    end

    def initialize(device_id, docker_image=nil, cleanup=false)
      @device_id = device_id
      @device = DeviceManager.device_configuration_check(device_id)
      @browserup_manager = Percy::BrowserupManager.new(device, device_id)
      @tiles_manager = TilesManager.new(device_id)
      @renderer_key_check = RendererKeyCheck.new(device_id)
      @jp_manager = setup_jp_manager(docker_image)
      PercySession.create_tmp_cert_folder(device_id)
      BrowserStack::DeviceLogger.initialize(device_id, "percy_session")
    end

    def start_server(env_vars) # rubocop:todo Metrics/AbcSize
      Percy::PercyUtil.logit('start_server_api') do |event_data|
        event_data ||= {}
        # Lets save session so that cleanup works correctly later
        BrowserStack.logger.info('Starting server')
        BrowserStack::DeviceLogger.clean_workspace(device_id)
        BrowserStack::DeviceLogger.start(device_id)
        BrowserStack.logger.info('Started device logger for percy')
        event_data[:device_id] = device_id
        if env_vars['FETCH_CERTS']
          gcloud_cert = fetch_certs_from_torch(env_vars)
          File.open(PercySession.gcp_tmp_file_path(device_id), 'w') { |file| file.write(gcloud_cert) }
        end
        # Uninstall local redirect extension
        uninstall_app_thread = IdeviceUtils.uninstall_redirect_app(device_id)

        # First we setup privoxy, the port is default privoxy port from the device config
        PrivoxyManager.setup_privoxy(device_id, device, { percy_forwarding: browserup_manager.browserup_proxy_port })
        # Setup browserup
        browserup_manager.setup_browserup
        # switch to correct appium version
        DeviceManager.switch_appium({}, device_id, device, APPIUM_VERSION, 'error')

        if env_vars['DISABLE_SMS_NOTIFICATION']
          bundle_ids = [MESSAGE_APP_BUNDLE_ID]
          bundle_ids.push(GEOGUARD_BUNDLE_ID) if IdeviceUtils.check_app_with_bundle_id_exists(device_id, GEOGUARD_BUNDLE_ID)
          result = Utils.change_notifications_profile_mdm(bundle_ids, device_id, true)
          event_data[:message_notification_disabled] = result["result"] == "success"
          env_vars['DISABLE_SMS_NOTIFICATION'] = result["result"] == "success" # if disabling notfication fails no need to renable in stop server
        end
        save_session(env_vars)

        # Confirm that extension was uninstalled
        if BrowserStack::Session.extension_uninstalled?({ genre: "percy" }, uninstall_app_thread).to_s != "true"
          msg = "Failed to uninstall safari redirect extension"
          event_data[:message] = msg
          raise StandardError, msg
        end

        event_data[:message] = enable_focus_mode(device_id) if env_vars['ENABLE_DND_MODE']
        event_data[:success] = true
      end
    end

    def stop_server
      Percy::PercyUtil.logit('stop_server_api') do |event_data|
        event_data ||= {}
        sess_data = get_session_data(device_id)
        env_vars = sess_data['env'] || {}
        event_data[:message] = disable_focus_mode(device_id) if env_vars['ENABLE_DND_MODE']
        BrowserStack.logger.info('Stopping server')
        BrowserStack::DeviceLogger.destroy(device_id)
        BrowserStack.logger.info('Stopped device logger for percy')
        event_data[:device_id] = device_id
        PrivoxyManager.reset_proxy(device_id, device)
        browserup_manager.clean_browserup
        sess_data['renderer_key'] = nil
        Utils.write_to_file_with_lock(DeviceManager.session_file(device_id), sess_data.to_json)
        if env_vars['DISABLE_SMS_NOTIFICATION']
          begin
            Utils.enable_notifications(device_id)
            event_data[:message_notification_enabled] = true
          rescue
            # we handle this in cleanup if mdm call fails
            device_state = DeviceState.new(device_id)
            device_state.touch_enable_message_notification_failure_file
            event_data[:message_notification_enabled] = false
          end
        end
        event_data[:success] = true
      end
    end

    def start_jackproxy(params, proxy_map, debug_data={}, proxy_map_url=nil, url_suffix=nil, renderer_key: nil)
      Percy::PercyUtil.logit('start_jackproxy_api') do |event_data|
        session_file = DeviceManager.session_file(device_id)
        sess_data = JSON.parse(File.read(session_file))
        renderer_key_check.validate_renderer_key_exists

        stop_jackproxy(renderer_key: 'cleanup')
        event_data ||= {}
        event_data[:device_id] = device_id
        event_data[:comparison_id] = debug_data['comparison_id']

        # get JP executable
        raise "JP executable setup failed" unless jp_manager.setup_jackproxy

        handle_proxy_map(proxy_map, proxy_map_url, url_suffix)
        # NOTE: This will start process with env of current process
        params = refactor_params(params)
        env_vars = sess_data['env'] || {}
        jp_manager.start_jackproxy(params, jp_manager.proxy_map_file_path, env_vars)
        proxy_started = false
        5.times do
          sleep(1)
          proxy_started = jp_manager.jackproxy_running?
          break if proxy_started
        end
        unless proxy_started
          event_data[:success] = false
          event_data[:message] = 'Jackproxy start failed'
          return false
        end
        sess_data['renderer_key'] = renderer_key
        sess_data['jp_executable'] = jp_executable
        Utils.write_to_file_with_lock(session_file, sess_data.to_json)
        event_data[:success] = true
        proxy_started
      end
    end

    def handle_proxy_map(proxy_map, proxy_map_url=nil, url_suffix=nil)
      proxy_map = get_proxy_map(proxy_map_url) unless proxy_map_url.nil?
      proxy_map = randomize_proxy_map(proxy_map, url_suffix) unless url_suffix.nil? || proxy_map_url.nil?
      save_proxy_map(proxy_map)
    end

    def stop_jackproxy(debug_data={}, renderer_key: nil)
      Percy::PercyUtil.logit('stop_jackproxy_api') do |event_data|
        renderer_key_check.validate_renderer_key(renderer_key: renderer_key)

        event_data ||= {}
        event_data[:device_id] = device_id
        event_data[:comparison_id] = debug_data['comparison_id']
        output = jp_manager.stop_jackproxy?
        unless output
          event_data[:success] = false
          event_data[:message] = 'Jackproxy stop failed'
          return false
        end
        delete_proxy_map
        BrowserStack.logger.info('Jackproxy stopped')
        event_data[:success] = true
        output
      end
    end

    def clean_mobile(renderer_key: nil)
      Percy::PercyUtil.logit('clean_realmobile_api') do |event_data|
        renderer_key_check.validate_renderer_key(renderer_key: renderer_key)
        sess_data = get_session_data(device_id)
        sess_data['renderer_key'] = nil
        Utils.write_to_file_with_lock(DeviceManager.session_file(device_id), sess_data.to_json)
        event_data[:device_id] = device_id
        event_data[:success] = true
      end
    end

    def cleanup
      Percy::PercyUtil.logit('cleanup_api') do |event_data|
        browserup_manager.clean_browserup
        BrowserStack::DeviceLogger.destroy(device_id)
        stop_jackproxy(renderer_key: 'cleanup')
        delete_extra_files
        clean_jp_executables

        event_data[:device_id] = device_id
        event_data[:success] = true
      end
    end

    def refactor_params(params)
      params = params.join(' ')
      params.gsub('"', '\\"').gsub('{', '"{').gsub('}', '}"')
    end

    def health_check?
      Percy::PercyUtil.logit('health_check_api') do |event_data|
        status = browserup_manager.browserup_running? &&
                PrivoxyManager.percy_config_set?(device, browserup_manager.browserup_proxy_port)
        BrowserStack.logger.info("Percy health check status at #{Time.now}: Status: #{status}")
        event_data[:device_id] = device_id
        event_data[:success] = status
        status
      end
    end

    def self.running?(device_id)
      session_file = MobileSessionInfo.file_path(device_id)
      status = File.file?(session_file) && Time.now - File.mtime(session_file) < PERCY_SESSION_TIMEOUT
      { "status" => status }.to_json
    end

    def self.keep_alive(device_id)
      session_file = MobileSessionInfo.file_path(device_id)
      return false unless File.file?(session_file)

      FileUtils.touch(session_file)
    end

    def google_cert_path
      "#{PercyOnAutomate::DEFAULT_TEMP_CERTS_DIR(device_id)}/#{PercyOnAutomate::GOOGLE_CLOUD_CREDENTIALS}"
    end

    def create_tmp_gcp_folder
      FileUtils.mkdir_p(PercyOnAutomate::DEFAULT_TEMP_CERTS_DIR(device_id))
    end

    def copy_gcp_certs(project_id)
      create_tmp_gcp_folder
      sess_data = get_session_data(device_id)
      env_vars = sess_data['env'] || {}
      prod_path = (
        if env_vars['FETCH_CERTS']
          PercySession.gcp_tmp_file_path(device_id)
        else
          SERVICE_ACCOUNT_PROD_FILE_PATH_DEF
        end
      )
      if project_id == 'percy-prod'
        system("cp #{prod_path} #{google_cert_path}")
      else
        path = env_vars['FETCH_CERTS'] ? PercySession.gcp_tmp_file_path(device_id) : SERVICE_ACCOUNT_STAGING_FILE_PATH
        system("cp #{path} #{google_cert_path}")
      end
    end

    def percy_screenshot_handler(data, devices_json: {})
      Percy::PercyUtil.logit('poa_screenshot_api') do |event_data|
        data['device_id'] = device_id
        Percy::PercyUtil.environment = 'production' if data['project_id'] == 'percy-prod'
        screenshot_manager = PercyOnAutomate::PoaScreenshotManager.new(data, devices_json: devices_json)
        screenshot_hash = screenshot_manager.capture_screenshot
        event_data[:device_id] = device_id
        event_data[:automation_session_id] = data['automation_session_id']
        event_data[:hub_session_id] = data['hub_session_id']
        event_data[:success] = true
        return screenshot_hash
      rescue => e
        event_data[:success] = false
        event_data[:message] = e.message
        BrowserStack.logger.info("percy_screenshot failed: , #{e.message}")
        BrowserStack.logger.info("percy_screenshot failed: , #{e.backtrace}")
        return 500, e.message
      end
    end

    def percy_setup_automate_session_handler(data)
      Percy::PercyUtil.logit('poa_setup_automate_session_api') do |event_data|
        data['device_id'] = device_id
        Percy::PercyUtil.environment = 'production' if data['project_id'] == 'percy-prod'
        setup_automate_session = PercyOnAutomate::SetupAutomateSession.new(device_id)
        setup_automate_session.setup(data)
        event_data[:device_id] = device_id
        event_data[:automation_session_id] = data['automation_session_id']
        event_data[:hub_session_id] = data['hub_session_id']
        event_data[:success] = true
        return "Completed setting up Percy On Automate Session"
      rescue => e
        event_data[:success] = false
        event_data[:message] = e.message
        BrowserStack.logger.info("percy_screenshot failed: , #{e.message}")
        BrowserStack.logger.info("percy_screenshot failed: , #{e.backtrace}")
        return 500, e.message
      end
    end

    def percy_dom_metadata_finalize_handler(data)
      Percy::PercyUtil.logit('percy_dom_metadata_finalize_handler') do |event_data|
        Percy::PercyUtil.environment = 'production' if data['project_id'] == 'percy-prod'
        data['device_id'] = device_id
        dom_meta_data_handler = PercyOnAutomate::DomMetaDataHandler.new(data)
        metadata_info = dom_meta_data_handler.dom_metadata_finalize
        event_data[:device_id] = device_id
        if metadata_info['url']
          event_data[:success] = true
          return metadata_info['url']
        end
        raise "Error occured while DOM finalize"
      rescue => e
        event_data[:success] = false
        event_data[:message] = e.message
        BrowserStack.logger.info("dom_metadata_finalize failed: , #{e.message}")
        BrowserStack.logger.info("dom_metadata_finalize failed: , #{e.backtrace}")
        return 500, e.message
      end
    end

    def percy_dom_metadata_upload_handler(data)
      Percy::PercyUtil.logit('percy_dom_metadata_upload_handler') do |event_data|
        Percy::PercyUtil.environment = 'production' if data['project_id'] == 'percy-prod'
        data['device_id'] = device_id
        copy_gcp_certs(data['project_id'])
        screenshot_manager = PercyOnAutomate::PoaScreenshotManager.new(data)
        metadata_hash = screenshot_manager.download_and_execute_metadata_script(data['feature_flags'])
        event_data[:device_id] = device_id
        if metadata_hash
          event_data[:success] = true
          return metadata_hash
        end
        raise "Error occured while DOM metadata upload"
      rescue => e
        event_data[:success] = false
        event_data[:message] = e.message
        BrowserStack.logger.info("dom_metadata_upload failed: , #{e.message}")
        BrowserStack.logger.info("dom_metadata_upload failed: , #{e.backtrace}")
        return 500, e.message
      end
    end

    private

    def fetch_certs_from_torch(env_vars)
      torch_api = env_vars['TORCH_API']
      machine_key = env_vars['REMOTE_AUTH_KEY']
      terminal_id = env_vars['TERMINAL_ID']
      platform = env_vars['PLATFORM']
      url = URI.parse("#{torch_api}/v1/certificates?id=#{terminal_id}&platform=#{platform}")
      BrowserStack.logger.info("fetch_certs torch api, #{url}")
      begin
        req = Net::HTTP.new(url.host, url.port)
        req.use_ssl = true
        headers = {
          'Authorization' => "Bearer #{machine_key}"
        }
        res = req.get(url, headers)
        certs = JSON.parse(res.body)
        certs['google_cloud_credentials']
      rescue => e
        BrowserStack.logger.info("fetch_certs_from_torch error: #{e.message}")
        raise e
      end
    end

    def setup_jp_manager(docker_image)
      begin
        # handle session file not existing/empty
        sess_data = get_session_data(device_id)
      rescue
        sess_data = nil
        BrowserStack.logger.warn("session file does not exist or has bad json")
      end

      if docker_image
        jp_tag = docker_image[docker_image.index("renderer") + 9..]
        @jp_executable = BrowserStack::OSUtils.is_arm? ? "#{jp_tag}-darwin-arm64" : "#{jp_tag}-darwin-amd64"
      elsif sess_data
        @jp_executable = sess_data.key?("jp_executable") ? sess_data["jp_executable"] : nil
      end

      bin_bucket = sess_data['env']['PERCY_BINARIES_BUCKET'] if sess_data && sess_data['env']
      Percy::JackproxyManager.new(device, jp_executable, bin_bucket)
    end

    def save_proxy_map(proxy_map)
      proxy_map ||= {}
      File.write(jp_manager.proxy_map_file_path, proxy_map)
    end

    # This method replaces the URLs present in proxy map with randomize proxy map URLs
    # on Renderer Side with the URL_SUFFIX passed by renderer.
    # exp -> (render.percy.local -> render.percy.qwerty) -> URL SUFFIX here is qwerty
    def randomize_proxy_map(proxy_map, url_suffix)
      BrowserStack.logger.info("Randomizing the proxy_map with suffix : #{url_suffix}")
      randomized_proxy_map = {}
      default_hostname = DEFAULT_SOURCE_HOSTNAME
      new_domain_name = default_hostname.gsub('local', url_suffix)
      proxy_map = JSON.parse(proxy_map)
      proxy_map.each do |key, val|
        key = key.gsub(default_hostname, new_domain_name)
        randomized_proxy_map[key] = val.clone
      end
      JSON.generate(randomized_proxy_map)
    end

    def get_proxy_map(url)
      BrowserStack.logger.info("Downloading the proxy_map from : #{url}")
      url = URI.parse(url)
      Net::HTTP.get(url)
    rescue => e
      BrowserStack.logger.warn("Could not download the proxy_map : #{e.message}")
      raise e
    end

    def delete_proxy_map
      File.delete(jp_manager.proxy_map_file_path) if File.exist?(jp_manager.proxy_map_file_path)
    end

    def save_session(env_vars)
      params = {
        # NOTE: right now we dont have a separate session id so we are using utc timestamp
        # we are not storing it in a var because we dont care about it as much and also
        # because a new instance of this class it created everytime an endpoint is called
        # and this is used for multiple devices ( we could maintain a static hash but we
        # haven't so far)
        # multiple comparisons are processed on a terminal in a single terminal session.
        # Because we dont know this comparison_id when we start percy session, we are using a
        # timestamp here as session id.
        device: device_id, # we need the key name to be device so that device-logger can catch this and start logging
        session_id: Time.now.utc.iso8601,
        genre: PERCY,
        session_subtype: 'ios',
        jp_executable: jp_executable,
        env: env_vars || {}
      }
      @sess = MobileSessionInfo.new(device_id, params)
      sess.save

      # also write to session file for device manager, not sure why both are different
      Utils.write_to_file_with_lock(DeviceManager.session_file(device_id), params.to_json)
    end

    def delete_extra_files
      [jp_manager.proxy_map_file_path, jp_manager.jackproxy_log_file_path,
       browserup_manager.browserup_log_file_path, PercySession.gcp_tmp_file_path(device_id)].each do |path|
        File.delete(path) if File.exist?(path)
      end
    end

    def clean_jp_executables
      Percy::PercyUtil.logit('clean_jackproxy') do |event_data|
        Dir.glob(File.join("#{JACKPROXY_DEPS}/binaries", '*'))
           .sort_by { |f| File.mtime(f) }[0..-6]
           .each { |f| File.delete(f) unless f == jp_manager.jp_executable_path }
        event_data[:device_id] = device_id
        event_data[:success] = true
      end
    end

    def enable_focus_mode(device_id)
      message = ''
      begin
        BrowserStackAppHelper.check_and_install_browserstack_test_suite(device_id)
        BrowserStackAppHelper.run_ui_test(device_id, :enable_focus_mode)
      rescue BrowserStackTestExecutionError => e
        # Instrument how many failure we get even after 3 retry
        # Not failing since session can run without DND
        message = "Failed to enable dnd #{e.message}"
      rescue => e
        # Resuce in case of app installation failure
        message = "dnd failure Reason: #{e.message}"
      end
      message
    end

    def disable_focus_mode(device_id)
      message = ''
      begin
        BrowserStackAppHelper.run_ui_test(device_id, :disable_focus_mode)
      rescue BrowserStackTestExecutionError => e
        # Instrument how many failure we get even after 3 retry
        # Not failing since session can run without DND
        message = "Failed to disable dnd #{e.message}"
      end
      message
    end

    def get_session_data(device_id)
      session_file = DeviceManager.session_file(device_id)
      JSON.parse(File.read(session_file))
    end
  end
end
