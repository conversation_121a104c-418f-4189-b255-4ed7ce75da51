# frozen_string_literal: true

require 'dotenv'
Dotenv.load('/usr/local/.browserstack/realmobile/.env')

module Percy
  class BrowserupManager

    BROWSERUP_DIR = "/usr/local/.browserstack/deps/browserup_jar"
    BROWSERUP_JAR = "#{BROWSERUP_DIR}/browserup.jar"
    MITM_CA_CERT_FILE = "#{MITM_PROXIES[:mitm_5_new][:conf]}/mitmproxy-ca.p12"

    attr_reader :device, :device_id

    def initialize(device, device_id)
      @device = device
      @device_id = device_id
    end

    def setup_browserup
      return true if browserup_running?

      start_browserup

      # setup a new proxy for current session
      proxy_started = false
      10.times do
        sleep(1)
        proxy_started = File.exist?(browserup_pid_file)
        break if proxy_started
      end
      raise "Browserup setup failed" unless proxy_started

      proxy_started
    end

    def browserup_proxy_port
      "5#{device['port']}"
    end

    def browserup_log_file_path
      "/var/log/browserstack/browserup_#{device_id}.log"
    end

    def clean_browserup
      # Force kill browserup and delete pid file
      system("#{browserup_process_cmd} | awk '{print $2}' | xargs kill -9")
      sleep(2)
      if File.exist?(browserup_pid_file)
        File.delete(browserup_pid_file)
        BrowserStack.logger.info("PID file wasn't deleted on killing browserup")
      end
    end

    def browserup_running?
      status = `#{browserup_process_cmd} | wc -l`.strip == '1'
      BrowserStack.logger.info('Browserup is not running') unless status
      status
    end

    private

    def browserup_pid_file
      "#{BROWSERUP_DIR}/browserup_proxy_#{browserup_proxy_port}_pid"
    end

    def browserup_process_cmd
      "ps aux | grep -i browserup | grep -- '-port #{browserup_proxy_port}' | grep -v grep "
    end

    def start_browserup
      pid_file = browserup_pid_file
      Thread.bs_run do
        system("#{ENV['JAVA_ZULU_16']} -jar #{BROWSERUP_JAR} -keyFilePath #{MITM_CA_CERT_FILE} -keyAlias 1 -upstreamProxyURI" \
          " localhost:2#{device['port']} -port #{browserup_proxy_port} -pid #{pid_file}" \
          " -log #{browserup_log_file_path}")
      end
    end
  end
end
