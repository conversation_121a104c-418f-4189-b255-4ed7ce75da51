require 'google/cloud/storage'

module GCSManager
  # TODO: change the path
  SERVICE_ACCOUNT_PROD_FILE_PATH = '/usr/local/.browserstack/realmobile/keys/percy/gcloud.json'
  SERVICE_ACCOUNT_STAGING_FILE_PATH = '/usr/local/.browserstack/realmobile/keys/percy/gcloud_staging.json'
  NUM_FILE_UPLOAD_RETRIES = 20
  class << self
    attr_accessor :project_id

    @@storage = nil
    @@storage_staging = nil

    def upload(bucket_name, filename, local_file_path, device_id, cert_delete_access: true)
      retries = NUM_FILE_UPLOAD_RETRIES
      begin
        sess_data = JSON.parse(File.read(DeviceManager.session_file(device_id)))
        env_vars = sess_data['env'] || {}
        prod_path = (
          if env_vars['FETCH_CERTS']
            Percy::PercySession.gcp_tmp_file_path(device_id)
          else
            SERVICE_ACCOUNT_PROD_FILE_PATH
          end
        )
        init_storage(prod_path, bucket_name, fetch_certs: env_vars['FETCH_CERTS'])
        bucket = bucket_name == "percy-screenshot-tiles" ? @@storage.bucket(bucket_name) : @@storage_staging.bucket(bucket_name)
        # check if exists
        file = bucket.find_file(filename)

        return file.public_url if file

        # else upload and return url
        file = bucket.create_file(local_file_path, filename)
        file.public_url
      rescue Google::Cloud::NotFoundError, Google::Cloud::PermissionDeniedError, Google::Cloud::UnauthenticatedError
        @@storage = nil
        @@storage_staging = nil
        # if file is already present GCS tries to replace it, by deleting and adding again
        # if a cert does not have delete access, then create the GCS link and return the same
        if !cert_delete_access && e.message.include?(
          "does not have storage.objects.delete access to the Google Cloud Storage"
        )
          return "https://storage.googleapis.com/#{bucket_name}/#{filename}"
        end

        sleep(rand(0.1..1))
        retry unless (retries -= 1) < 0
        raise
      end
    end

    private

    def init_storage(prod_path, bucket_name, fetch_certs: false)
      if bucket_name == "percy-screenshot-tiles"
        return unless @@storage.nil?

        gcloud = Google::Cloud.new(project_id, prod_path)
        @@storage = gcloud.storage
      else
        return unless @@storage_staging.nil?

        staging_path = fetch_certs ? prod_path : SERVICE_ACCOUNT_STAGING_FILE_PATH
        gcloud_staging = Google::Cloud.new(project_id, staging_path)
        @@storage_staging = gcloud_staging.storage
      end
    end
  end
end
