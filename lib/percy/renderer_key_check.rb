require 'json'
require 'fileutils'
require_relative './util'
require_relative '../utils/osutils'
require_relative '../../server/device_manager'

module Percy
  class RendererKeyCheck
    attr_accessor :device_id

    def initialize(device_id)
      @device_id = device_id
    end

    def validate_renderer_key(renderer_key: nil)
      return if renderer_key == 'cleanup'

      # below check currently will run only if it is an async_renderer
      sess_data = JSON.parse(File.read(DeviceManager.session_file(device_id)))
      # check if `renderer_key` is null / was removed by stop_server
      raise RendererKeyError, 'Renderer key is empty' unless sess_data['renderer_key']
      # validate `renderer_key`
      raise RendererKeyError, 'Incorrect renderer key' if renderer_key != sess_data['renderer_key']
    end

    def validate_renderer_key_exists
      sess_data = JSON.parse(File.read(DeviceManager.session_file(device_id)))
      # check if `renderer_key` is present
      raise RendererKeyError, 'renderer key is already present' if sess_data['renderer_key']
    end

  end
end
