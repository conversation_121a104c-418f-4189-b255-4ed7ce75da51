require_relative 'utils/utils'
require_relative './helpers/automation'

class HandleCrossSiteTracking
  class << self
    def get_element(driver)
      driver.find_element(:xpath, '//XCUIElementTypeSwitch[@name="Prevent Cross-Site Tracking"]')
    rescue => e
      BrowserStack.logger.info "Element unable to find. #{e.message}"
      nil
    end

    def handle_switch_ios18(driver, to_enable: false)
      from_state, to_state = to_enable ? ['0', '1'] : ['1', '0']

      cross_tracking_switch = Automation.manual_scroll_to_element_for_ios18(driver, "Prevent Cross-Site Tracking")
      success = false

      if cross_tracking_switch
        cross_tracking_switch.click if cross_tracking_switch.attribute('value') == from_state

        # This was done because after click the attribute `value` on simulator is
        # returning nil, so wrote this safety check to fetch the attribute value after click
        cross_tracking_switch = get_element(driver)
        success = cross_tracking_switch.attribute('value') == to_state if cross_tracking_switch
      end

      success
    end

    def handle_switch(driver, to_enable: false)
      from_state, to_state = to_enable ? ['0', '1'] : ['1', '0']

      cross_tracking_switch = get_element(driver)
      success = false

      if cross_tracking_switch
        cross_tracking_switch.click if cross_tracking_switch.attribute('value') == from_state

        # This was done because after click the attribute `value` on simulator is
        # returning nil, so wrote this safety check to fetch the attribute value after click
        cross_tracking_switch = get_element(driver)
        success = cross_tracking_switch.attribute('value') == to_state if cross_tracking_switch
      end

      success
    end

    def do(device_id, device_version)

      driver = Gem::Version.new(device_version) >= Gem::Version.new('18') ? Automation.open_safari_settings_for_ios18(device_id, device_version) : Automation.open_safari_settings(device_id, device_version)

      success = Gem::Version.new(device_version) >= Gem::Version.new('18') ? handle_switch_ios18(driver) : handle_switch(driver)
    rescue => e
      BrowserStack.logger.info "Failed To Enable Cross-Site Tracking. #{e.message}"
      success = false
    ensure
      driver&.driver_quit
      success

    end
  end
end
