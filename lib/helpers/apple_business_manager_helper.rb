require_relative '../custom_exceptions'
require_relative './wda_client'
require_relative '../models/device_state'
require_relative '../configuration'
require_relative '../utils/zombie'
require_relative '../utils/idevice_utils'
require_relative '../utils/http_utils'
require_relative '../utils/ios_mdm_service_client'
require_relative '../utils/vpp_utils'

module BrowserStack
  class AppleBusinessManagerHelper # rubocop:todo Metrics/ClassLength
    LOG_IDENTIFIER = "[AppleBusinessManagerHelper]"

    def initialize(device_id, app_name)
      @device_state = DeviceState.new(device_id)
      @uuid = device_id
      @app_name = app_name
      @device_id = device_id
      @device_config = device_config
      @ios_version = @device_config["device_version"]
      @serial_number = @device_config["device_serial"]
      @device_name = @device_config["device_name"]
      @mobileprovision_branch = @device_config["mobileprovision_branch"]
      BrowserStack::Zombie.configure
      IosMdmServiceClient.configure
    end

    def device_config
      config = BrowserStack::Configuration.new.all
      devices = begin
        JSON.parse(File.read(config['config_json_file']))["devices"]
      rescue
        {}
      end
      devices[@uuid]
    end

    # method list in this file
    # install_app_via_mdm_using_vpp
    # device_eligible?(region)
    # assign_license_to_device(serial_number, app_name)
    # license_assigned_to_device?(serial_number, app_name)
    # license_available_for_app?(serial_number, app_name)
    # app_installed_with_retries?(device_id, app_name, max_retries = 4, wait_seconds = 5)
    # vpp_token_valid?
    # VPPUtils.asset_information(adamId)
    # VPPUtils.token_expiry
    # VPPUtils.fetch_assignments
    # VPPUtils.assignment_status(event_id)
    # VPPUtils.associate_license(app_name, serial_number)
    def ensure_license_and_install_app(app_install_wait_seconds: 5)
      is_license_assigned_to_device = license_assigned_to_device?

      BrowserStack.logger.info "#{LOG_IDENTIFIER} license assigned to device with serial number #{@serial_number}? #{is_license_assigned_to_device}"

      unless is_license_assigned_to_device
        return false unless license_available_for_app?

        # check if license is available or not
        # call method to assign a the device a license
        # check if license is successfully assigned or not

        BrowserStack.logger.info "#{LOG_IDENTIFIER} license is available for app"

        assign_license_to_device
      end

      # go ahead and call MDM to install testflight
      mdm_response = send_mdm_install_app_request
      BrowserStack.logger.info "#{LOG_IDENTIFIER} MDM final install app respnse? #{mdm_response}"

      # check if app is installed or not,
      is_app_installed = app_installed_with_retries?(5, app_install_wait_seconds)
      BrowserStack.logger.info "#{LOG_IDENTIFIER} is app installed? #{is_app_installed}"
      is_app_installed
    end

    def install_app_via_mdm_using_vpp
      start_time = Time.now.to_i
      mdm_response = nil

      mdm_response = send_mdm_install_app_request

      BrowserStack.logger.info "#{LOG_IDENTIFIER} MDM install app respnse? #{mdm_response}"
      license_not_found = mdm_response.include?("No license was found for app") ||
                          mdm_response.include?("No licence was found for app") ||
                          mdm_response.include?("License not found")

      unless license_not_found
        is_app_installed = app_installed_with_retries?
        BrowserStack.logger.info "#{LOG_IDENTIFIER} is testflight installed? #{is_app_installed}"
        event_logger("install_app_via_mdm_using_vpp", { "status" => "success", "message" => "Testflight installed via MDM", "device" => @device_id, "serial_number" => @serial_number })
        return is_app_installed
      end

      # following code is just a fail safe mechanism in case anything goes wrong in machine check or install phase
      # there's very less chances of this code getting called

      return false unless vpp_token_valid?

      BrowserStack.logger.info "#{LOG_IDENTIFIER} Vpp token is valid"

      is_license_assigned_to_device = license_assigned_to_device?

      BrowserStack.logger.info "#{LOG_IDENTIFIER} license assigned to device with serial number #{@serial_number}? #{is_license_assigned_to_device}"

      unless is_license_assigned_to_device
        return false unless license_available_for_app?

        # check if license is available or not
        # call method to assign a the device a license
        # check if license is successfully assigned or not

        BrowserStack.logger.info "#{LOG_IDENTIFIER} license is available for app"

        assign_license_to_device
      end

      # go ahead and call MDM to install testflight
      mdm_response = send_mdm_install_app_request
      BrowserStack.logger.info "#{LOG_IDENTIFIER} MDM final install app respnse? #{mdm_response}"

      # check if app is installed of not,
      is_app_installed = app_installed_with_retries?
      BrowserStack.logger.info "#{LOG_IDENTIFIER} is testflight installed? #{is_app_installed}"
      event_logger("install_app_via_mdm_using_vpp", { "status" => "success", "message" => "Testflight installed via MDM", "device" => @device_id, "serial_number" => @serial_number })
      is_app_installed
    rescue => e
      handle_error("install_testflight_via_mdm", start_time, e, mdm_response)
      raise e
    end

    def device_eligible?(region)
      is_vpp_token_valid = VPP_TOKEN != "" && VPP_TOKEN != "<no value>"
      is_region_valid = VPP_CHECKS[:regions].include?(region)
      is_version_valid = @ios_version.to_i >= VPP_CHECKS[:min_os_version]
      is_high_cap_device = VPP_CHECKS[:mobileprovision_branches].include?(@mobileprovision_branch)

      is_vpp_token_valid && is_region_valid && is_version_valid && is_high_cap_device
    end

    def send_mdm_install_app_request
      IosMdmServiceClient.send(:make_request_for_install_app, {
        "request_type" => "InstallApplication",
        "udid" => @device_id,
        "itunes_store_id" => APP_DETAILS[@app_name.to_sym][:itunes_store_id],
        "options" => {
          "purchase_method" => 1
        }
      }, REDIS_CLIENT)
    rescue => e
      BrowserStack.logger.info "#{LOG_IDENTIFIER} MDM install app failed due to error: #{e.message}"
      e.message
    end

    def assign_license_to_device
      start_time = Time.now.to_i
      response = nil
      response = VPPUtils.associate_license(@app_name, @serial_number)
      BrowserStack.logger.info "#{LOG_IDENTIFIER} Associate license response: #{response}"
      event_id = response["eventId"]
      response = VPPUtils.assignment_status(event_id)
      BrowserStack.logger.info "#{LOG_IDENTIFIER} Assignment status response: #{response}"

      # Pending means, the device is enrolled, but it is taking some time to update the state. It is as good as success.
      # If we try to install the app after receiving state as pending, then also app installation works and thus we are not counting this as offline.
      if response["eventStatus"] == "COMPLETE" || response["eventStatus"] == "PENDING"
        update_vpp_enrolled_device_list_file(@serial_number, APP_DETAILS[@app_name.to_sym][:itunes_store_id])
        return true
      end

      raise AppleBusinessManagerError, "License assignment failed"
    rescue => e
      handle_error("assign_license_to_device", start_time, e, response)
      false
    end

    def license_assigned_to_device?
      start_time = Time.now.to_i
      assignments = nil
      cached_assignments_data = nil
      assignment_found = false
      adam_id = APP_DETAILS[@app_name.to_sym][:itunes_store_id]

      #improve hashing , add data for the entried this is available
      cached_assignments_data = begin
        JSON.parse(File.read(VPP_ENROLLED_DEVICE_LIST_FILE))
      rescue
        { "assignments" => [] }
      end
      cached_assignments_data["assignments"].each do |assignment|
        assignment_found = true if assignment["serialNumber"] == @serial_number && assignment["adamId"].to_s == adam_id.to_s
      end
      return true if assignment_found

      # fetches assignment based on serial number & adam_id
      assignments = VPPUtils.fetch_assignments( adam_id, @serial_number)["assignments"]
      BrowserStack.logger.info("#{LOG_IDENTIFIER} Assignments recieved from fetch_assignments: #{assignments}")

      # append the data in cached_assignments_data
      assignments.each do |assignment|
        cached_assignments_data["assignments"] << assignment
      end

      File.open(VPP_ENROLLED_DEVICE_LIST_FILE, 'w') do |file|
        file.write(cached_assignments_data.to_json)
      end

      # Check if the recent fetched assignments have expected data / not
      # No need to iterate across all the cached assignment data since that's already done above
      assignments.each do |assignment|
        return true if assignment["serialNumber"] == @serial_number && assignment["adamId"] == adam_id.to_s
      end
      false
    rescue => e
      handle_error("license_assigned_to_device?", start_time, e, assignments)
      false
    end

    def license_available_for_app?
      start_time = Time.now.to_i
      response = nil
      response = VPPUtils.asset_information(APP_DETAILS[@app_name.to_sym][:itunes_store_id])
      return true if !response["availableCount"].nil? && response["availableCount"].to_i > 0

      # no more licenses available, please purchase more
      raise AppleBusinessManagerError, "Purchase more licenses"
    rescue => e
      handle_error("license_available_for_app?", start_time, e, response)
      false
    end

    def app_installed_with_retries?(max_retries = 4, wait_seconds = 5)
      start_time = Time.now.to_i
      retries = 0

      while retries < max_retries
        if IdeviceUtils.check_app_with_bundle_id_exists(@device_id, APP_DETAILS[@app_name.to_sym][:bundle_id])
          BrowserStack.logger.info "#{LOG_IDENTIFIER} App is installed."
          return true
        else
          retries += 1
          if retries < max_retries
            BrowserStack.logger.info "#{LOG_IDENTIFIER} Retry #{retries}: App is not installed. Retrying in #{wait_seconds} seconds..."
            sleep wait_seconds
          end
        end
      end

      BrowserStack.logger.info "#{LOG_IDENTIFIER} All retries failed. App is not installed."
      event_logger("app_installed_with_retries", { "status" => "fail", "message" => "Testflight is not installed on this device currently", "device" => @device_id, "serial_number" => @serial_number })
      false
    rescue => e
      handle_error("app_installed_with_retries?", start_time, e)
      false
    end

    def vpp_token_valid?
      start_time = Time.now.to_i
      expiry_date_str = File.exist?(VPP_TOKEN_EXPIRY_FILE) ? File.read(VPP_TOKEN_EXPIRY_FILE) : VPPUtils.token_expiry
      expiry_date = DateTime.parse(expiry_date_str)

      current_date = DateTime.now

      # Compare the current date with the expiry date
      if current_date < expiry_date
        if expiry_date - current_date <= 15
          remaining_days = expiry_date - current_date
          message = "VPP Token is valid for only #{remaining_days} days, please renew"
          BrowserStack.logger.info(message)
          BrowserStack::Zombie.push_logs("vpp-token-expiring", "update_vpp_token_expiry_file" , { "data" => message })
        end
        BrowserStack.logger.info "#{LOG_IDENTIFIER} VPP Token is still valid."
        true
      else
        raise AppleBusinessManagerError, "Vpp token expired"
      end
    rescue => e
      handle_error("vpp_token_valid", start_time, e)
      false
    end

    private

    def update_vpp_enrolled_device_list_file(serial_number, adam_id)
      assignments = begin
        JSON.parse(File.read(VPP_ENROLLED_DEVICE_LIST_FILE))
      rescue
        { "assignments" => [] }
      end
      assignment_array = assignments["assignments"]
      assignment_array.push({ "adamId" => adam_id.to_s, "serialNumber" => serial_number.to_s })
      assignments["assignments"] = assignment_array
      File.write(VPP_ENROLLED_DEVICE_LIST_FILE, assignments.to_json)
    end

    def handle_error(method_name, start_time, error, data = nil)
      BrowserStack.logger.info "#{LOG_IDENTIFIER}[handle_error] #{method_name} failed due to error - #{error}, backtrace: #{error.backtrace} and data #{data}"
      failure_data(method_name, start_time, data, error.message)
    end

    def event_logger(method_name, data)
      event_name = "web_events"
      data_to_push = {
        event_name: "VPPInstallation",
        os: IdeviceUtils.os(@device_id),
        os_version: @ios_version,
        team: "device_features"
      }
      event_json = {
        app_name: @app_name
      }
      event_json.merge!(data)
      data_to_push.merge!({ event_json: event_json })
      Utils.send_to_eds(data_to_push, event_name, true)
      if data[:status] == 'fail'
        BrowserStack::Zombie.push_logs("vpp-installation-failure",
                                       "AppleBusinessManagerError",
                                       { "device" => @device_id,
                                         "os_version" => @ios_version,
                                         "data" => data[:error_message],
                                         "url" => method_name,
                                         "error" => data[:error_reason] })
      end
    end

    def failure_data(method_name, start_time, error_object, error_message)
      BrowserStack.logger.error "#{LOG_IDENTIFIER} Error - #{method_name} failed due to error - #{error_object} - #{error_message}"
      end_time = Time.now.to_i
      f_data = {
        status: "fail",
        error_reason: error_object,
        error_message: error_message,
        time_taken: end_time - start_time
      }
      event_logger(method_name, f_data)
    end
  end
end
