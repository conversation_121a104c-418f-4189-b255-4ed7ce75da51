require_relative '../configuration'
require_relative '../utils/zombie'
require_relative '../utils/utils'
require_relative '../custom_exceptions'
require_relative './browserstack_app_helper'
require_relative './wda_client'
require_relative '../models/device_state'
require_relative '../../config/constants'
require_relative '../utils/ios_mdm_service_client'
require_relative '../utils/idevice_utils'
require_relative '../utils/custom_mdm_manager'

module BrowserStack
  class TimezoneHelper # rubocop:todo Metrics/ClassLength
    @@errors_classification = {
      "Descendants matching type SearchField" => "RetryError",
      "Early unexpected exit, operation never finished bootstrapping" => "XCUITestBootstrapError"
    }
    @@config = Configuration.new.all
    def initialize(uuid, session_id, product, device_config)
      @device_state = DeviceState.new(uuid)
      @uuid = uuid
      @device_config = device_config
      @device_version = device_config['device_version']
      @session_id = session_id
      @product = product
      @event_name = "web_events"
      Zombie.configure
    end

    def check_device_version_for_timezone
      device_version = @device_config["device_version"]
      if device_version.to_i < TIMEZONE_MIN_OS_VERSION
        BrowserStack.logger.info("[ios-timezone] The device version is not supported for timezone : #{device_version.to_i}")
        raise TimezoneNotSupportedException
      end
    end

    def call_automation_with_wda(timezone)
      start_time = Time.now.to_i
      wda_client = WdaClient.new(@device_config['webdriver_port'])
      res = wda_client.change_time_zone(timezone)
      response_message = res['value'] ? res['value']['message'] : "Unknown Error"
      total_time = Time.now.to_i - start_time
      raise "WDA automation failure. Falling back to XCUI" if !response_message.include?("success") && !response_message.include?("search results")

      [response_message, total_time]
    end

    def check_response_and_log_errors(start_time, end_time, mdm_executed, mdm_response_message, mdm_execution_time, wda_executed, wda_response_message, wda_execution_time, response_status) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
      data_to_push = { event_name: "UpdateTimezone", product: @product, os: IdeviceUtils.os(@uuid), team: "device_features" }
      event_json = { session_id: @session_id }
      event_json.merge!({ fallback: "true", fallback_reason: mdm_response_message }) if mdm_executed && wda_executed
      if !response_status
        BrowserStack.logger.error("Error - Failed to change timezone of device mdm response: #{mdm_response_message}, wda response: #{wda_response_message} ")
        f_data = { time_taken: end_time - start_time, timezone_updated: "failed" }
        f_data.merge!({ "mdm-response": mdm_response_message, "mdm-total-time": mdm_execution_time }) if mdm_executed
        f_data.merge!({ "wda-response": wda_response_message, "wda-total-time": wda_execution_time }) if wda_executed
        if wda_response_message.include?("No search results")
          f_data.merge!({ error_reason: "no_search_results" })
          event_json.merge!(f_data)
          data_to_push.merge!({ event_json: event_json })
          Utils.send_to_eds(data_to_push, @event_name, true)
          BrowserStack::Zombie.push_logs("timezone-change-failure", "NoTimezonesException", { "device" => @uuid, "product" => @product, "session_id" => @session_id, "data" => { "reason" => "No Timezone", "mdm-response" => mdm_response_message } })
          raise NoTimezonesException
        elsif wda_response_message.include?("Multiple search results")
          f_data.merge!({ error_reason: "multiple_search_results" })
          event_json.merge!(f_data)
          data_to_push.merge!({ event_json: event_json })
          Utils.send_to_eds(data_to_push, @event_name, true)
          BrowserStack::Zombie.push_logs("timezone-change-failure", "MultipleTimezonesException", { "device" => @uuid, "product" => @product, "session_id" => @session_id, "data" => { "reason" => "Multiple Timezones", "mdm-response" => mdm_response_message } })
          raise MultipleTimezonesException
        elsif wda_response_message == "" && @device_version.to_i >= 18
          f_data.merge!({ error_reason: "mdm_error" })
          event_json.merge!(f_data)
          data_to_push.merge!({ event_json: event_json })
          Utils.send_to_eds(data_to_push, @event_name, true)
          BrowserStack::Zombie.push_logs("timezone-change-failure", "MDMTimezoneFailureException", { "device" => @uuid, "product" => @product, "session_id" => @session_id, "data" => { "reason" => "MDM Timezone Error", "mdm-response" => mdm_response_message } })
          raise MDMTimezoneFailureException
        else
          error_msg = wda_response_message
          f_data.merge!({ error_reason: "automation_error" })
          event_json.merge!(f_data)
          data_to_push.merge!({ event_json: event_json })
          Utils.send_to_eds(data_to_push, @event_name, true)
          BrowserStack::Zombie.push_logs("timezone-change-failure", error_msg, { "device" => @uuid, "product" => @product, "session_id" => @session_id, "data" => { "reason" => wda_response_message, "mdm-response" => mdm_response_message } })
          raise TimezoneFailureException
        end
      else
        s_data = { timezone_updated: "success", time_taken: end_time - start_time }
        s_data.merge!({ "mdm-response": mdm_response_message, "mdm-total-time": mdm_execution_time }) if mdm_executed
        s_data.merge!({ "wda-response": wda_response_message, "wda-total-time": wda_execution_time }) if wda_executed
        event_json.merge!(s_data)
        data_to_push.merge!({ event_json: event_json })
        Utils.send_to_eds(data_to_push, @event_name, true)
        BrowserStack::Zombie.push_logs("timezone-change-success", "", { "device" => @uuid, "product" => @product, "session_id" => @session_id, "mdm-response" => mdm_response_message, "wda-response" => wda_response_message })
      end
    end

    # Exposed functions

    def self.get_error_type(test_output)
      @@errors_classification.each_key do |error_msg|
        return @@errors_classification[error_msg] if test_output.include?(error_msg)
      end
      "OtherError"
    end

    def change_time_zone(timezone, timezone_mdm = nil )
      BrowserStack.logger.info("[ios-timezone] Session id : #{@session_id}, timezone: #{timezone}, mdm param: #{timezone_mdm}" )
      check_device_version_for_timezone
      mdm_executed = false
      wda_executed = false
      mdm_response_message = ""
      wda_response_message = ""
      response_status = false
      start_time = Time.now.to_i
      mdm_execution_time = nil
      wda_execution_time = nil
      begin
        if @device_version.to_i >= TIMEZONE_MDM_MIN_VERSION && !timezone_mdm.to_s.empty? && !CustomMDMManager.is_custom_mdm_device?(@uuid)
          mdm_executed = true
          BrowserStack.logger.info("[ios-timezone] Timezone change via MDM with param #{timezone_mdm}")
          response_status = BrowserStack::IosMdmServiceClient.set_timezone(@uuid, timezone_mdm, redis_client = REDIS_CLIENT)
          mdm_execution_time = Time.now.to_i - start_time
          mdm_response_message = response_status ? "Timezone change success." : "Timezone change failed."
        end
        if !response_status && (@device_version.to_i < 18 || CustomMDMManager.is_custom_mdm_device?(@uuid))
          wda_executed = true
          @device_state.touch_settings_automation_executing_file
          BrowserStack.logger.info("[ios-timezone] Timezone change via WDA with param #{timezone}")
          wda_response_message, wda_execution_time = call_automation_with_wda(timezone)
          response_status = true unless wda_response_message.include?("search results")
        end
      rescue => e
        BrowserStack.logger.info("[ios-timezone] Failed to change timezone with param: #{timezone} for device: #{@uuid}, session_id: #{@session_id}")
      end
      end_time = Time.now.to_i
      @device_state.remove_settings_automation_executing_file
      check_response_and_log_errors(start_time, end_time, mdm_executed, mdm_response_message, mdm_execution_time, wda_executed, wda_response_message, wda_execution_time, response_status)
      response_status
    end

  end
end
