require 'appium_lib'
require_relative './browserstack_app_helper'
require_relative '../custom_exceptions'
require_relative '../models/device_state'
require_relative '../configuration'
require_relative '../utils/zombie'
require_relative './automation'
require_relative '../utils/idevice_utils'

class CallLogsHelper
  def initialize(device_id, session_id, product = nil)
    @device_state = DeviceState.new(device_id)
    @uuid = device_id
    @product = product
    @session_id = session_id
    @device_config = device_config
    @ios_version = @device_config["device_version"]
    BrowserStack::Zombie.configure
  end

  def clear_call_logs
    BrowserStack.logger.info("Running XCUI automation to clear call logs on device : #{@uuid} for session ID : #{@session_id}")
    start_time = Time.now.to_i
    error_reason = ""
    error_message = ""
    status = "failed"
    begin
      BrowserStackAppHelper.run_ui_test(@uuid, :call_logs_cleanup, session_id: @session_id)
      @device_state.remove_call_logs_file
      status = "success"
      BrowserStack.logger.info("Successfully cleared call logs of device: #{@uuid} for session ID : #{@session_id}")
    rescue BrowserStackTestExecutionError => e
      error_reason = "BrowserStackTestExecutionError"
      error_message = e.message
    rescue => e
      error_reason = "UnknownError"
      error_message = e.message
    ensure
      data = prepare_data(status, start_time, error_reason, error_message)
      event_logger(data)
    end
    status == "success"
  end

  def event_logger(data)
    event_name = "web_events"
    data_to_push = {
      event_name: "ios-contacts-call-logs",
      product: @product,
      os: IdeviceUtils.os(@uuid),
      os_version: @ios_version,
      team: "device_features"
    }
    event_json = {
      session_id: @session_id
    }
    event_json.merge!(data)
    data_to_push.merge!({ event_json: event_json })
    Utils.send_to_eds(data_to_push, event_name, true)

    if data[:result] == 'failed'
      BrowserStack::Zombie.push_logs("ios-contacts-call-logs",
                                     data[:error_reason],
                                     {
                                       "result" => "failed",
                                       "device" => @uuid,
                                       "product" => @product,
                                       "session_id" => @session_id,
                                       "os_version" => @ios_version,
                                       "error_reason" => data[:error_reason],
                                       "error_message" => data[:error_message]
                                     })
    end
  end

  private

  def device_config
    config = BrowserStack::Configuration.new.all
    devices = begin
      JSON.parse(File.read(config['config_json_file']))["devices"]
    rescue
      {}
    end
    devices[@uuid]
  end

  def prepare_data(result, start_time, error_reason = nil, error_message = nil)
    end_time = Time.now.to_i
    data = {
      result: result,
      time_taken: end_time - start_time
    }
    if result == "failed"
      data[:error_reason] = error_reason
      data[:error_message] = error_message
    end

    data
  end
end
