require_relative '../custom_exceptions'
require_relative './wda_client'
require_relative '../models/device_state'
require_relative '../configuration'
require_relative '../utils/zombie'
require_relative './data_report_helper'
require_relative '../../server/device_manager'

class SafariSettings # rubocop:todo Metrics/ClassLength
  ENABLE = "enable"
  DISABLE = "disable"
  DEFAULT_STATE = {
    prevent_cross_site_tracking: ENABLE,
    block_popups: ENABLE
  }

  def initialize(device_id, session_id, product = "")
    @device_state = DeviceState.new(device_id)
    @uuid = device_id
    @product = product
    @session_id = session_id
    @device_config = DeviceManager.device_configuration_check(@uuid)
    @ios_version = !@device_config.nil? && @device_config["device_version"]
    @reporter = DataReportHelper.settings_reporter(session_id: @session_id, device: @uuid, product: @product, ios_version: @ios_version)
  end

  def switch(safari_settings) # rubocop:todo Metrics/AbcSize
    parsed_settings = safari_settings.is_a?(String) ? JSON.parse(safari_settings.gsub('=>', ':')) : safari_settings
    safari_settings = {
      prevent_cross_site_tracking: parsed_settings["prevent_cross_site_tracking"] || parsed_settings[:prevent_cross_site_tracking],
      block_popups: parsed_settings["block_popups"] || parsed_settings[:block_popups]
    }
    @device_state.touch_settings_automation_executing_file
    BrowserStack.logger.info("Applying safari settings #{safari_settings} via WDA on device : #{@uuid} for session ID : #{@session_id}" )
    start_time = Time.now.to_i
    begin
      client = WdaClient.new(@device_config['webdriver_port'])
      response = client.set_safari_settings(safari_settings)  # returns a hash

      !response['value']['error'].include?(true) && report_data(success: true, complete_failure: false, wda_response: response['value'], interval: Time.now.to_i - start_time)
      return validate_response(response, safari_settings, start_time)
    rescue WdaAutomationError => e
      end_time = Time.now.to_i
      if !response['value']['error'].include?(false)
        report_data(success: false, complete_failure: true, wda_response: response['value'], interval: Time.now.to_i - start_time)
      else
        report_data(success: false, complete_failure: false, wda_response: response['value'], interval: Time.now.to_i - start_time)
      end
      return {
        prevent_cross_site_tracking: response['value']['error'][0] ? "fail" : "pass",
        block_popups: response['value']['error'][1] ? "fail" : "pass"
      }
    rescue WdaClientError => e
      end_time = Time.now.to_i
      report_data(success: false, complete_failure: true, wda_response: { "error" => e.message }, additional_data: { "wda-client-status" => client.running? ? "running" : "non running" }, interval: Time.now.to_i - start_time)
    rescue => e
      BrowserStack.logger.info("Error while #{safari_settings} prevent cross-site tracking -#{e.message} #{e.backtrace}")
      end_time = Time.now.to_i
      report_data(success: false, complete_failure: true, wda_response: { "error" => e.message }, additional_data: { "exception" => e.message }, interval: Time.now.to_i - start_time)
    ensure
      @device_state.remove_settings_automation_executing_file
    end
    { prevent_cross_site_tracking: "fail", block_popups: "fail" }
  end

  def validate_response(response, safari_settings, start_time)
    state = {
      prevent_cross_site_tracking: "pass",
      block_popups: "pass"
    }
    if !response['value']['error'].include?(true)
      update_current_state(safari_settings)
    else
      prev_state = SafariSettings.get_current_state(@device_state)
      updated_state = {
        prevent_cross_site_tracking: response['value']['error'][0] ? prev_state[:prevent_cross_site_tracking] : safari_settings[:prevent_cross_site_tracking],
        block_popups: response['value']['error'][1] ? prev_state[:block_popups] : safari_settings[:block_popups]
      }
      update_current_state(updated_state)
      raise WdaAutomationError.new(response['value']['error']), response['value']['message']
    end
    state
  end

  def self.get_current_state(device_state)
    if device_state.safari_settings_file_present?
      curr_state = device_state.read_safari_settings_file.split("\n")
      return {
        prevent_cross_site_tracking: curr_state[0],
        block_popups: curr_state[1]
      }
    end
    DEFAULT_STATE
  end

  def update_current_state(state)
    updated_state = "#{state[:prevent_cross_site_tracking]}\n#{state[:block_popups]}"
    @device_state.remove_safari_settings_file
    @device_state.write_to_safari_settings_file(updated_state)
  end

  def report_data(success: false, complete_failure: false, wda_response: {}, additional_data: {}, interval: 0)
    settings_list = ["prevent_cross_site_tracking", "block_popups"]
    curr_state = SafariSettings.get_current_state(@device_state)
    if success || complete_failure
      @reporter.report_settings_data(
        settings_list,
        values: [curr_state[:prevent_cross_site_tracking], curr_state[:block_popups]],
        zombie_error: complete_failure,
        extra_data: [{
          time_taken: interval,
          error: complete_failure ? wda_response : "",
          additional_data: additional_data
        }]
      )
      return
    end
    failed_settings = []
    updated_settings = []
    if wda_response['error'][0]
      failed_settings.append(settings_list[0])
      updated_settings.append(settings_list[1])
    else
      failed_settings.append(settings_list[1])
      updated_settings.append(settings_list[0])
    end
    @reporter.report_settings_data(
      updated_settings,
      values: [curr_state[updated_settings[0].to_sym]],
      zombie_error: false,
      extra_data: [{ time_taken: interval, error: wda_response["message"] }]
    )
    @reporter.report_settings_data(
      failed_settings,
      values: [curr_state[failed_settings[0].to_sym]],
      zombie_error: true,
      extra_data: [{ time_taken: interval, error: wda_response["message"] }]
    )
  end
end
