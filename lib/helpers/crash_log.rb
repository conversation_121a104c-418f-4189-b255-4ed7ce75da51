require_relative '../utils/utils'
require_relative '../utils/idevice_utils'
require_relative '../utils/zombie'
require 'fileutils'
require_relative '../../config/constants'

class CrashLog
  class << self

    def generate_and_upload(device, params, server_config)
      log_info("Called generate_and_upload method with params:#{params} device: #{device}")
      data_to_push = { event_name: "CrashLogUpload", product: params[:product], os: IdeviceUtils.os(device), team: "device_features" }
      start_time = Time.now.to_i
      crash_zip_path = "/tmp/crash_report_#{params[:session_id]}.zip"
      crash_reports = generate(device, server_config, crash_zip_path, params[:session_id])
      num_crash_reports = crash_reports.length

      log_info("Count of crash reports #{num_crash_reports} with params:#{params} device: #{device}")
      return { num_crash_reports: num_crash_reports } unless num_crash_reports > 0

      metadata = { num_crash_reports: num_crash_reports.to_s } #uploading integer was a problem in metadata to S3

      upload(params, crash_zip_path, metadata)
      end_time = Time.now.to_i
      event_json = { session_id: params[:session_id], crash_logs_upload: "success", time_taken: end_time - start_time }
      data_to_push.merge!({ event_json: event_json })
      Utils.send_to_eds(data_to_push, "web_events", true)
      log_info("Finished executing generate_and_upload method with params:#{params} device: #{device}")
      { num_crash_reports: num_crash_reports }
    rescue => e
      log_error("Failed to upload crash report #{e.message} #{e.backtrace.join("\n")}")
      end_time = Time.now.to_i
      event_json = { session_id: params[:session_id], crash_logs_upload: "failed", error: e.message, time_taken: end_time - start_time }
      data_to_push.merge!({ event_json: event_json })
      Utils.send_to_eds(data_to_push, "web_events", true)
      BrowserStack::Zombie.push_logs("crash-log-upload-failure", e.message.to_s, { "session_id" => params[:session_id], "device" => device, "product" => params[:product] })
      raise e
    ensure
      cleanup(device, crash_zip_path)
    end

    def generate(device, server_config, crash_zip_path, session_id = "")
      log_info("Called generate method with device: #{device} and session_id: #{session_id}")
      crash_reports = []
      IdeviceUtils.configure(server_config)
      user_installed_apps = IdeviceUtils.list_user_installed_apps_details(device)
      user_installed_apps.each do |user_app_detail|
        bundle_executable = user_app_detail[:bundle_executable]
        crash_reports.concat(IdeviceUtils.get_crash_report_from_device(device, [bundle_executable], session_id, false))
      end
      log_info("Successfully fetched the crash reports with device: #{device} and session_id: #{session_id}")

      IdeviceUtils.create_zip_of_crash_report(crash_reports, session_id, device, crash_zip_path) unless crash_reports.empty?
      log_info("Successfully zipped the crash reports with device: #{device} and session_id: #{session_id}")
      crash_reports
    end

    def upload(params, crash_zip_path, metadata)
      log_info("Called upload method with params:#{params}")
      session_id = params[:session_id]
      params['s3_config'] = JSON.parse(params['s3_config'])
      aws_region = params['s3_config']['s3_bucket'] == "bs-stag" || params['s3_config']['s3_region'] == "us-east-1" ? "" : "-#{params['s3_config']['s3_region']}"
      s3_url = "https://s3#{aws_region}.amazonaws.com/#{params[:s3_config]['s3_bucket']}/data/#{session_id}/#{session_id}-crash_reports.zip"

      region = params['s3_config']['s3_bucket'] == "bs-stag" || params['s3_config']['s3_region'] == "us-east-1" ? nil : (params['s3_config']['s3_region']).to_s
      log_info("Region is #{region}")
      ret, error = Utils.upload_file_to_s3(         #The crash logs would get deleted after 24 hrs from s3
        params['s3_config']['s3_access_keyId'],
        params['s3_config']['s3_secret_access_key'],
        "application/zip",
        crash_zip_path,
        "public-read",
        s3_url,
        params['session_id'],
        params['genre'],
        region,
        300,
        metadata,
        metadata
      )

      unless ret || error.empty?
        log_error("Failed to upload file to s3 for params: #{params}, error: #{error}")
        raise "Error while uploading crash reports to S3: #{error}"
      end
      log_info("Successfully uploaded crash reports to s3  with params:#{params}")
      true
    end

    def cleanup(device, crash_zip_path)
      log_info("Called cleanup method with device: #{device}")
      FileUtils.rm_rf(Utils.get_crash_reports_dir_path(device))
      FileUtils.rm_rf(crash_zip_path)
      log_info("Successfully deleted the crash report folders with device: #{device}")
      true
    end

    def log_info(message)
      BrowserStack.logger.info("#{CRASH_LOG_TAG} #{message}")
    end

    def log_error(message)
      BrowserStack.logger.error("#{CRASH_LOG_TAG} #{message}")
    end
  end
end
