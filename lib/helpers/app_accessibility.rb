require_relative '../utils/utils'
require_relative '../wrappers/cfg_util'
require_relative '../../server/device_manager'
require_relative '../utils/plist_buddy'
require_relative '../utils/pymobiledevice'
require_relative '../../lib/ifuse'
require_relative '../utils/osutils'

require 'aws-sdk-s3'
require 'base64'

class AppAccessibility # rubocop:todo Metrics/ClassLength
  TEAM = APP_A11Y_TEAM
  APP_A11Y_AUTOMATE_URL = 'https://app-accessibility-service.browserstack.com/automate/api/v1/scan/notify-results'

  def initialize(device_id, session_id, product = "", first_scan = false, focus_order_timeout = 5, automate_obj = {})
    @udid = device_id
    @session_id = session_id
    @product = product
    @first_scan = first_scan
    @focus_order_timeout = focus_order_timeout
    @device_config = DeviceManager.device_configuration_check(@udid)
    @app_accesibility_data_reporter = DataReportHelper.new('accessibility-info',
                                                           session_id: session_id,
                                                           product: product,
                                                           ios_version: @device_config["device_version"],
                                                           device: device_id)
    @session_info = DeviceManager.session_file_contents(@udid)
    @app_bundle_id = @session_info['app_testing_bundle_id']
    @app_details_file = "#{STATE_FILES_DIR}/app_a11y_app_details_#{@udid}"
    @de_duplication_hash_file = "#{STATE_FILES_DIR}/app_a11y_session_hash_#{@udid}.json"
    @device_version = @device_config['device_version']
    @async_mode = automate_obj['async_mode']
    @automate_obj = automate_obj
  end

  def self.fetch_app_ipa_details(app_path, udid)
    session_info = DeviceManager.session_file_contents(udid)
    app_name = session_info['app_display_name']
    app_details_file = "#{STATE_FILES_DIR}/app_a11y_app_details_#{udid}"
    user_plist_list = Dir["#{app_path}/Payload/*/Info.plist"]
    BrowserStack.logger.info("#{APP_A11Y_TAG} Fetching app ipa details #{app_name} Info.plist = #{user_plist_list}")
    info_plist_file = user_plist_list.max.to_s
    if File.exists?(info_plist_file)
      interface_style = app_user_interface_style(info_plist_file)
      supported_orientations = supported_orientations(info_plist_file)
      app_version = fetch_app_version(info_plist_file)
      File.open(app_details_file, 'w') do |file|
        file.write(
          "#{app_name}<|>#{interface_style}<|>#{supported_orientations}<|>#{app_version}"
        )
      end
    end
  rescue => e
    BrowserStack.logger.error("#{APP_A11Y_TAG} Error in fetching app details from ipa file: #{e.message}")
  end

  # This helps to know the enabled mode Dark mode or Light mode
  def self.app_user_interface_style(info_plist_file)
    PlistBuddy.get_value_of_key(info_plist_file, "UIUserInterfaceStyle")
  end

  def self.supported_orientations(info_plist_file)
    output = PlistBuddy.get_value_of_key(info_plist_file, "UISupportedInterfaceOrientations")
    output.scan(/UIInterfaceOrientation\w+/).join(',')
  end

  def self.fetch_app_version(info_plist_file)
    PlistBuddy.get_value_of_key(info_plist_file, "CFBundleVersion")
  end

  def base64_encoded_app_icon
    app_icon_filename = "#{@app_bundle_id}.png"
    CFGUtil.new(udid: @udid).get_app_icon(@app_bundle_id)
    Base64.strict_encode64(File.read(app_icon_filename))
  rescue => e
    BrowserStack.logger.error("Failed to get app icon for #{@app_bundle_id} due to #{e}")
    ""
  ensure
    FileUtils.rm_f(app_icon_filename)
  end

  #
  # Used for app accessibility rule engine
  #
  # This method uses webdriveragent to fetch accessibility tree, measures the time taken,
  # and reports the results using data reporter.
  #
  # @return [JSON] Returns accessibility tree using webdriveragent with added attributes. if the webdriveragent fails, return {}.
  def resolution
    screen_width_command = "ideviceinfo --udid #{@udid} --domain com.apple.mobile.iTunes | grep ScreenWidth"
    screen_width = BrowserStack::OSUtils.execute(screen_width_command).strip.split(':').last.strip
    screen_height_command = "ideviceinfo --udid #{@udid} --domain com.apple.mobile.iTunes | grep ScreenHeight"
    screen_height = BrowserStack::OSUtils.execute(screen_height_command).strip.split(':').last.strip
    if screen_width.empty? || screen_height.empty?
      BrowserStack.logger.error("Failed to fetch resolution.")
      return nil
    end
    "#{screen_width}x#{screen_height}"
  rescue => e
    BrowserStack.logger.error("Failed to fetch resolution: #{e.message}")
    nil
  end

  def write_hash_to_file(hash_list)
    content = {}
    content["hash_list"] = hash_list
    File.open(@de_duplication_hash_file, 'w') do |file|
      file.write(
        content.to_json.to_s
      )
    end
  end

  def fetch_hash_list
    if File.exists?(@de_duplication_hash_file)
      content = File.read(@de_duplication_hash_file)
      return JSON.parse(content)["hash_list"]
    end
    []
  end

  def fetch_accessibility_info # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
    hash_list = fetch_hash_list
    req_json = {}
    req_json["hash_list"] = hash_list
    req_json["is_automate"] = @async_mode
    start_time = Time.now.to_i
    client = WdaClient.new(@device_config['webdriver_port'].to_i)
    failure_data = {}
    success_data = {}
    additional_data = {
      "os" => "ios",
      "session_id" => @session_id
    }
    begin
      BrowserStack.logger.info("#{APP_A11Y_TAG} Getting accessibility info using webdriver agent")
      res = client.post_accessibility_info(req_json)
      raise WdaAutomationError.new("unknown error"), "No payload in response" unless res['value']

      BrowserStack.logger.info("#{APP_A11Y_TAG} Received response form wda client : response -  #{res}")
      end_time = Time.now.to_i

      if res['value']['status'] == "duplicate"
        additional_data["success"] = false
        return { 'duplicate': true } if @async_mode

        return res['value'].merge!(additional_data)
      end
      write_hash_to_file(res['value']['hash_list'])

      if File.exists?(@app_details_file)
        app_name, interface_style, orientations_value, app_version = File.read(@app_details_file).strip.split('<|>')
      else
        app_name = @session_info["app_display_name"]
        interface_style = nil
        orientations_value = nil
        app_version = nil
      end

      BrowserStack.logger.info("#{APP_A11Y_TAG} Getting accessibility info using pymobiledevice3")
      # pymobiledevice3 audit api used with test types for color contrast and text truncation
      # pymobiledevice3 list-items api used to obtain focus order and spoken description of elements
      # Order is important here, DO NOT execute list-items before WDA or audit command for that matter as list-items can scroll a view leading to different snapshot than user intended to capture
      test_type = @device_version.to_i < 15 ? "AXAuditTextElementInfo::auditCheckApplicationsAuditIssues AXAuditCategoryVisual::auditTextContrast" : "testTypeDynamicText testTypeContrast testTypeTextClipped"
      audit_start_time = Time.now.to_i
      audit_result = PyMobileDevice::Developer::Accessibility.run_audit(@udid, test_type)
      audit_end_time = Time.now.to_i

      focus_order_caption_data_size = nil
      focus_order_caption_start_time = nil
      focus_order_caption_end_time = nil
      unless @async_mode
        focus_order_caption_start_time = Time.now.to_i
        focus_order_caption_result = PyMobileDevice::Developer::Accessibility.list_items(@udid, @focus_order_timeout)
        focus_order_caption_data_size = focus_order_caption_result.length
        focus_order_caption_end_time = Time.now.to_i

        additional_data["focus_order_caption_data"] = focus_order_caption_result
      end

      BrowserStack.logger.info(
        "#{APP_A11Y_TAG} App data - Name: #{app_name}, Interface style: #{interface_style}, Orientations: #{orientations_value}, Version: #{app_version}"
      )
      additional_data["supported_orientations"] = orientations_value
      additional_data["user_interface_style"] = interface_style
      additional_data["app_name"] = app_name
      additional_data["bundle_id"] = @app_bundle_id
      additional_data["app_version"] = app_version
      additional_data["accessibility_audit_data"] = audit_result
      app_icon_start_time = Time.now.to_i
      additional_data["app_icon"] = base64_encoded_app_icon if @first_scan
      app_icon_end_time = Time.now.to_i
      if res['value']['status'] == "pass"
        report_additional_data = {
          "accessibility_audit_time_taken" => audit_end_time - audit_start_time,
          "is_first_scan" => @first_scan,
          "app_icon_extract_time_taken" => app_icon_end_time - app_icon_start_time
        }

        unless @async_mode
          report_additional_data.merge!({
            "focus_order_caption_data_size" => focus_order_caption_data_size,
            "focus_order_caption_time_taken" => focus_order_caption_end_time - focus_order_caption_start_time
          })
        end
        success_data = @app_accesibility_data_reporter.populate_data_hash(wda_response: { "status" => res['value']['status'] }, interval: end_time - start_time,
                                                                          additional_data: report_additional_data)
        additional_data.merge!(res['value'])
        if @async_mode
          notify_results(APP_A11Y_AUTOMATE_URL, additional_data, @automate_obj, @session_id)
          { 'success': true }
        else
          additional_data.merge!(res['value'])
        end
      else
        failure_data = @app_accesibility_data_reporter.populate_data_hash(wda_response: { "error" => res['value']['error'] }, additional_data: { "error" => "failure response from curl request" }, interval: end_time - start_time)
        raise WdaAutomationError.new(res['value']['error']), "Internal Error"
      end

    rescue WdaAutomationError => e
      BrowserStack.logger.info("#{APP_A11Y_TAG} wda endpoint failure - #{e.message}")
      {}

    rescue WdaClientError => e
      end_time = Time.now.to_i
      BrowserStack.logger.info("#{APP_A11Y_TAG} wda endpoint failure - #{e.message}")
      failure_data = @app_accesibility_data_reporter.populate_data_hash(wda_response: { "error" => e.message[0..1000] }, additional_data: { "wda-client-status" => client.running? ? "running" : "non running" }, interval: end_time - start_time)
      {}

    rescue => e
      end_time = Time.now.to_i
      failure_data = @app_accesibility_data_reporter.populate_data_hash(wda_response: { "error" => e.message }, additional_data: { "backtrace" => e.backtrace }, interval: end_time - start_time)
      BrowserStack.logger.info("#{APP_A11Y_TAG} Standard error while fetching accessibility info : error -  #{e.message}, #{e.backtrace}")
      {}
    ensure
      data = success_data.merge!(failure_data)
      @app_accesibility_data_reporter.report(data, team: AppAccessibility::TEAM)
    end
  end

  def upload_to_s3(screenshot_key, screenshot, automate_obj)
    s3 = Aws::S3::Client.new(
      region: automate_obj['region'],
      access_key_id: automate_obj['s3_access_key'],
      secret_access_key: automate_obj['s3_secret_key']
    )
    begin
      s3.put_object(
        bucket: automate_obj['s3_bucket'],
        key: screenshot_key,
        body: screenshot,
        content_type: 'image/jpeg',
        acl: 'public-read' # Set the ACL to public-read
      )
      log("File uploaded successfully with public-read access!")
    rescue Aws::S3::Errors::ServiceError => e
      log("Failed to upload file: #{e.message}")
    end
  end

  def notify_results(url, res_json, automate_obj, session_id)
    pid = Process.fork do
      start_time = (Time.now.to_f * 1000).to_i
      BrowserStack.logger.info("#{APP_A11Y_TAG} notify_results process forked")

      # Process the results and prepare data
      updated_res_json = prepare_device_info(res_json)
      screenshot_key, app_icon_key = handle_images(updated_res_json, automate_obj)

      # Send the results to the server
      send_results_to_server(url, updated_res_json, automate_obj, session_id, screenshot_key, app_icon_key)

      end_time = (Time.now.to_f * 1000).to_i
    end
    Process.detach(pid)
  end

  def make_request(url, headers, params)
    log("Making a call to external url === #{url} with params ====")
    conn = Faraday.new(
      url: url,
      headers: headers,
      ssl: { verify: false }
    )
    response = conn.post do |req|
      req.body = params.to_json
    end
    log("status of notify_results #{response.status.to_i} == #{response.body}")
  rescue => e
    log("Exception while making request to === #{params[:url]} with params ==== #{params},
          Exception : #{e.inspect}")
    error_message = e.message
  end

  def broadcast_continuous_scanning_session(pusher, rule_engine_callback, mode)
    app_details = load_app_details
    ifuse = Ifuse.new(@udid)
    app_mount_point = ifuse.mount_app_point(@app_bundle_id)
    if mode == 'on'
      enable_continuous_scanning(ifuse, app_mount_point, pusher, rule_engine_callback, app_details)
    else
      disable_continuous_scanning(ifuse, app_mount_point)
    end
  rescue => e
    handle_broadcast_error(e)
  end

  private

  def load_app_details
    app_name, interface_style, orientations_value, app_version = File.read(@app_details_file).strip.split('<|>')
    {
      "app_name" => app_name,
      "interface_style" => interface_style,
      "supported_orientations" => orientations_value,
      "app_version" => app_version,
      "bundle_id" => @app_bundle_id
    }
  end

  def enable_continuous_scanning(ifuse, app_mount_point, pusher, rule_engine_callback, app_details)
    # Create JSON content for session_details.json
    session_info = {
      "pusher" => pusher,
      "rule_engine_callback" => rule_engine_callback
    }
    json_content = JSON.generate(session_info)
    ifuse.mount_root_directory_of_app(@app_bundle_id)
    cmd = "mkdir -p #{app_mount_point}/Documents && " \
      "echo '#{json_content}' > #{app_mount_point}/Documents/session_details.json"
    msg, error_msg, status = OSUtils.execute2(cmd)
    if !error_msg.empty? && error_msg.include?('is itself on a macFUSE volume')
      BrowserStack.logger.error "ifuse failed, error: #{error_msg}"
      BrowserStack.logger.info "umounting #{app_mount_point}"
      OSUtils.execute("sudo umount -f #{app_mount_point}")
      msg, error_msg, status = OSUtils.execute2(cmd)
    end
    BrowserStack.logger.error "ifuse failed, error: #{error_msg}, msg: #{msg}" unless error_msg.empty?
    raise "ifuse failed" unless error_msg.empty?

    {
      continuous_scanning: true,
      app_details: app_details,
      broadcast_success: true
    }
  ensure
    ifuse.unmount_root_directory_of_app(@app_bundle_id)
  end

  def disable_continuous_scanning(ifuse, app_mount_point)
    ifuse.mount_root_directory_of_app(@app_bundle_id)
    cmd = "rm -f #{app_mount_point}/Documents/session_details.json"
    msg, error_msg, status = OSUtils.execute2(cmd)
    if !error_msg.empty? && error_msg.include?('is itself on a macFUSE volume')
      BrowserStack.logger.error "ifuse failed, error: #{error_msg}"
      BrowserStack.logger.info "umounting #{app_mount_point}"
      OSUtils.execute("sudo umount -f #{app_mount_point}")
      msg, error_msg, status = OSUtils.execute2(cmd)
    end
    BrowserStack.logger.error "ifuse failed, error: #{error_msg}, msg: #{msg}" unless error_msg.empty?
    raise "ifuse failed" unless error_msg.empty?

    {
      continuous_scanning: false,
      app_details: {},
      broadcast_success: true
    }
  ensure
    ifuse.unmount_root_directory_of_app(@app_bundle_id)
  end

  def handle_broadcast_error(error)
    BrowserStack.logger.error("#{APP_A11Y_TAG} Error in broadcasting continuous scanning session: #{error.message}")
    {
      continuous_scanning: false,
      app_details: {},
      broadcast_success: false
    }
  end

  def prepare_device_info(res_json)
    result = res_json.dup
    result['deviceInfo']['resolution'] = resolution
    result['deviceInfo']['os_version'] = @device_config["device_version"]
    result['deviceInfo']['device_name'] = @device_config["device_name"]
    result
  end

  def handle_images(res_json, automate_obj)
    th_build_uuid = automate_obj['th_build_uuid']
    test_run_uuid = automate_obj['test_run_uuid']

    # Handle screenshot
    screenshot_key = "#{th_build_uuid}/#{test_run_uuid}/screenshot-#{SecureRandom.hex(4)}.jpeg"
    upload_to_s3(screenshot_key, Base64.decode64(res_json['screenshot']), automate_obj)
    res_json.delete('screenshot')

    # Handle app icon if present
    app_icon_key = nil
    if res_json.key?('app_icon')
      app_icon_key = "#{th_build_uuid}/#{test_run_uuid}/app-icon-#{SecureRandom.hex(4)}.jpeg"
      upload_to_s3(app_icon_key, Base64.decode64(res_json['app_icon']), automate_obj)
      res_json.delete('app_icon')
    end

    [screenshot_key, app_icon_key]
  end

  def send_results_to_server(url, res_json, automate_obj, session_id, screenshot_key, app_icon_key)
    headers = {
      "Authorization" => "Bearer #{automate_obj['auth_token']}",
      "Content-Type" => "application/octet-stream"
    }

    params = {
      snapshot_data: res_json,
      test_run_uuid: automate_obj['test_run_uuid'],
      session_id: session_id,
      screenshot_key: screenshot_key,
      th_build_uuid: automate_obj['th_build_uuid'],
      scan_timestamp: automate_obj['scan_timestamp']
    }

    # Add app_icon_key to params if it exists
    params[:app_icon_key] = app_icon_key if app_icon_key

    BrowserStack.logger.info("screenshot key #{screenshot_key}")
    make_request(url, headers, params)
  end
end
