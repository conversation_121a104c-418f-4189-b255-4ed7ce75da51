require_relative '../configuration'
require_relative '../device_conf'
require_relative '../utils/utils'
require_relative '../../config/constants'
require_relative '../utils/pymobiledevice'

class LocationSimulator
  def initialize(device)
    @device = device
  end

  def simulate(latitude, longitude)
    log(:info, "Simulating location to latitude: #{latitude} and longitude: #{longitude}")
    latitude = 0.001 if latitude == 0
    longitude = 0.001 if longitude == 0

    if device_config['device_version'].to_f >= 17.0
      simulate_greater_than_equal_to17(latitude, longitude)
    else
      simulate_less_than17(latitude, longitude)
    end
    log(:info, "Simulated location to latitude: #{latitude} and longitude: #{longitude}")
  rescue => e
    log(:error, "Failed to simulate location to latitude: #{latitude} and longitude: #{longitude}")
    raise e
  end

  def reset
    log(:info, "Resetting location simulation")
    lat = config["dc_gps_location"]["latitude"]
    long = config["dc_gps_location"]["longitude"]
    simulate(lat, long)
    kill_existing_location_simulation_greater_than_equal_to17 if device_config['device_version'].to_f >= 17.0
    log(:info, "Resetted location simulation")
  rescue => e
    log(:error, "Failed to reset location, error: #{e.message}, #{e.backtrace.join("\n")}")
    raise e
  end

  private

  def config
    @config ||= BrowserStack::Configuration.new.all
  end

  def device_config
    @device_config ||= BrowserStack::DeviceConf[@device]
  end

  def simulate_gps_location_file
    "/tmp/simulate_gps_location_#{@device}.json"
  end

  def simulate_less_than17(latitude, longitude)
    log(:info, "Simulating location to latitude: #{latitude} and longitude: #{longitude} for gps location less than 17")
    set_gps_callback = proc { |_stdout, stderr, _status, _args|
      log(:error, "Setting gps location failed, lockdownd error") if stderr.include? "Could not connect to lockdownd"
    }
    cmd = "gtimeout 10 #{IDEVICELOCATION} -u #{@device} -- #{latitude} #{longitude}"
    Utils.fork_process(cmd, process_name: "mock geolocation for #{@device}", callback: { block: set_gps_callback })
    log(:info, "Simulated location to latitude: #{latitude} and longitude: #{longitude} for gps location less than 17")
  end

  def simulate_greater_than_equal_to17(latitude, longitude)
    log(:info, "Simulating location to latitude: #{latitude} and longitude: #{longitude} for gps location greater than equal to 17")
    kill_existing_location_simulation_greater_than_equal_to17
    pid = PyMobileDevice::Developer.simulate_location(@device, latitude, longitude)
    File.write(simulate_gps_location_file, { pid: pid }.to_json)
    log(:info, "Simulated location to latitude: #{latitude} and longitude: #{longitude} for gps location greater than equal to 17")
  end

  def kill_existing_location_simulation_greater_than_equal_to17
    log(:info, "Killing existing location simulation for ios version greater than equal to 17")
    return unless File.exists?(simulate_gps_location_file)

    simulate_location_file = JSON.parse(File.read(simulate_gps_location_file))
    pid = simulate_location_file["pid"]
    Process.kill('KILL', pid.to_i) if !pid.nil? && Utils.process_running?(pid)
    File.delete(simulate_gps_location_file)
    log(:info, "Killed existing location simulation for ios version greater than equal to 17")
  end

  def log(level, message)
    params = { subcomponent: self.class.to_s, device: @device }
    BrowserStack.logger.send(level.to_sym, message, params)
  end
end
