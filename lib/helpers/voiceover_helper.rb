require_relative '../../config/constants'
require_relative 'blueutil_helper'
require 'fileutils'
require_relative '../../lib/configuration'
require_relative '../utils/utils'
require_relative '../utils/osutils'
require_relative '../utils/http_utils'
require_relative '../utils/configuration_profiles_manager'
require_relative '../../server/device_manager'
require_relative '../overridden/thread'
require_relative '../models/device_state'
require_relative '../utils/custom_mdm_manager'

module BrowserStack
  class VoiceoverHelper # rubocop:todo Metrics/ClassLength

    VOICEOVER_CLEANUP_DONE_FILE = "#{STATE_FILES_DIR}/voiceover_cleanup_done"
    VOICEOVER_CONFIG_FILE_PATH = "/usr/local/.browserstack/realmobile/config/custom_devices/voiceover_devices.json"
    VOICEOVER_RESERVED_DEVICES = ["iPhone14,3", "iPhone14,5", "iPhone16,1", "iPhone17,1", "iPhone15,5", "iPhone17,4"] # Model Identifiers are used i.e iPhone14,3 (iPhone 13 Pro), iPhone14,5 (iPhone 13), iPhone16,1 (iPhone 15 Pro), iPhone17,1 (iPhone 16 Pro), iPhone15,5 (iPhone 15 Plus), iPhone17,4 (iPhone 16 Plus) -> these devices will be reserved exclusively for voiceover sessions

    def initialize
      @server_config = Configuration.new.all
      @blueutil_helper = BrowserStack::BlueutilHelper.new
      @machine_version = @server_config["platform_category"].sub('ios_njb_', '')
      @is_monterey_or_higher = Gem::Version.new(@machine_version) >= Gem::Version.new('16.0')
      @bluetooth_server_mappings = bluetooth_server_mappings
      @bluetooth_server_ip = bluetooth_server_ip
    end

    def self.voiceover_device?(device_id)
      VOICEOVER_DEVICES.key?("devices") ? VOICEOVER_DEVICES["devices"].include?(device_id.to_s) : false
    end

    def self.reserved_voiceover_device?(device_name)
      VOICEOVER_RESERVED_DEVICES.include?(device_name)
    end

    def self.pre_voiceover_setup(device, params)
      # @param lseIOSBluetoothVoiceover is for private bridgecloud voiceover access
      # @param is_ios_screen_reader_public_cloud_device is for semi public cloud voiceover access
      # Return if none of these are true
      return unless Utils.bridgecloud_ios_voiceover_session?(params) || params["is_ios_screen_reader_public_cloud_device"].to_s == "true"

      enable_bluetooth_modification_via_mdm(device)

      # Give full settings to private bridgecloud voiceover sessions
      # For semi public cloud we cannot give settings access since device will be shared
      if Utils.bridgecloud_ios_voiceover_session?(params)
        current_device_config = DeviceManager.device_configuration_check(device)
        wda_port = current_device_config["webdriver_port"].to_i
        wda_client = WdaClient.new(wda_port)

        device_state = DeviceState.new(device)
        dedicated_device = device_state.dedicated_device_file_present?.to_s
        custom_mdm = CustomMDMManager.is_custom_mdm_device?(device).to_s
        wda_client.settings_access("true", dedicated_device, custom_mdm, "true")

        BrowserStack.logger.info("Enabled Settings Access : for #{device}")
      end

      # Push to EDS for semi public cloud voiceover sessions
      if params["is_ios_screen_reader_public_cloud_device"].to_s == "true"
        product = params["genre"]
        session_id = product == "live_testing" ? params["live_session_id"] : params["app_live_session_id"]
        BrowserStack.logger.info("pre_voiceover_setup send_to_eds product: #{product} , session_id: #{session_id}")
        eds_table = product == "live_testing" ? EdsConstants::LIVE_TEST_SESSIONS : EdsConstants::APP_LIVE_TEST_SESSIONS
        Utils.send_to_eds({
          session_id: session_id,
          product: {
            ios_voiceover_semi_public_cloud_enabled: true
          }
        }, eds_table, true)
      end

      voiceover_helper = BrowserStack::VoiceoverHelper.new
      voiceover_helper.mark_voiceover_used(device, params) # For cleanup purposes
    rescue => e
      BrowserStack.logger.error("pre_voiceover_setup call failed with error : #{e.message}")
    end

    # Enables bluetooth modification access via MDM
    def self.enable_bluetooth_modification_via_mdm(device)
      Thread.bs_run do
        config_profiles_manager = ConfigurationProfilesManager.new(device, BrowserStack.logger)
        # In some cases, the first MDM call succeeds, but bluetooth modification is still blocked by MDM
        # Hence, we are adding an extra retry, to fix the flaky behaviour
        mdmMaxRetries = 1
        retryCount = 0
        while retryCount <= mdmMaxRetries
          BrowserStack.logger.info("Applying MDM profile to allow bluetooth modification for : #{device}, Try number: #{retryCount}")
          config_profiles_manager.install_profile(:restrictions, { flags: ['enable_bluetooth_modification'] }, install_via: :automatic)
          retryCount += 1
          sleep 1
        end
        BrowserStack.logger.info("[enable_bluetooth_modification_via_mdm] Successfully enabled MDM bluetooth modification on device : #{device}")
      rescue => e
        BrowserStack.logger.error("[enable_bluetooth_modification_via_mdm] Error while enabling MDM bluetooth modification on device #{device} due to error -#{e.message} #{e.backtrace}")
      end
    end

    # Returns the discoverable blueooth name of the mac mini, which iOS devices connect to
    def mini_bluetooth_name
      cmd = "system_profiler SPBluetoothDataType -json"
      result = BrowserStack::OSUtils.execute(cmd)
      bluetoothDetails = JSON.parse(result)["SPBluetoothDataType"][0]
      bluetoothDetails["local_device_title"]["general_name"]
    rescue => e
      BrowserStack.logger.error("Coud not fetch mini bluetooth name, error : #{e.message}")
      raise "Could not find mini's bluetooth name using system_profiler command"
    end

    # starts iOSInteractionServer responsible for handling voiceover gestures, if not running
    def start_interaction_server
      interaction_server_process_count = BrowserStack::OSUtils.execute("lsof -i:9001 | grep -v PID | wc -l").strip
      if interaction_server_process_count.to_i == 0
        is_interaction_server_already_running = false
        interactions_server_logfile = "#{LOGGING_DIR}/iOSInteractionServer.log"
        cmd = "#{BS_DIR}/deps/iOSInteractionServer/iOSInteractionServer >> #{interactions_server_logfile} 2>&1"

        set_interactionserver_callback = proc { |stdout, stderr, status, _args|
          File.write(interactions_server_logfile, "[set_interactionserver_callback] iOSInteractionServer process exited - timestamp #{Time.now.utc} - stdout: #{stdout} - stderr: #{stderr} - status: #{status} - #{status.success?}", mode: 'a')
        }
        Utils.fork_process(cmd, {
          process_name: "iOSInteractionServer",
          callback: {
            block: set_interactionserver_callback
          }
        })
        BrowserStack.logger.info("[VOICEOVER] /setup_voiceover Forked interactionServer process")
      end
    end

    # kills the iOSInteractionServer responsible for handling voiceover gestures
    def kill_interaction_server
      BrowserStack::OSUtils.execute("lsof -i:9001 | grep -v PID | awk '{print $2}' | xargs kill")
    end

    # Returns concurrent active running voiceover sessions
    def active_voiceover_sessions
      BrowserStack::OSUtils.execute("ls #{STATE_FILES_DIR} | grep voiceover_used_ | wc -l").strip.to_i
    end

    def delete_stale_voiceover_used_files
      files = BrowserStack::OSUtils.execute("ls #{STATE_FILES_DIR} | grep voiceover_used_*").split("\n")
      files.each do |filename|
        file = "#{STATE_FILES_DIR}/#{filename}"
        FileUtils.rm_f(file) if File.exist?(file) && Time.now - File.ctime(file) >= 4 * 60 * 60
      end
    end

    def machine_ip
      ip = File.read(@server_config['ip_file']).strip
    rescue
      raise "machine_ip failed"
    end

    # If bluetooth server mapping exists in config, return that. Else, return localhost
    def bluetooth_server_ip
      @bluetooth_server_mappings.key?(machine_ip) ? @bluetooth_server_mappings[machine_ip] : "localhost"
    end

    def bluetooth_server_mappings
      cmd = "jq .bluetooth_server_mappings #{VOICEOVER_CONFIG_FILE_PATH}"
      result = BrowserStack::OSUtils.execute(cmd)
      JSON.parse(result)
    rescue
      {}
    end

    # Gets called at the voiceover host machine (bluetooth client)
    # Returns true if given device address is not connected to the mini via bluetooth
    def device_address_disconnected?(address)
      endpoint = "http://#{@bluetooth_server_ip}:45671/voiceover_bluetooth_connection_status?address=#{address}"
      response = BrowserStack::HttpUtils.make_get_request(endpoint, 15)
      unless response.status.to_i == 200
        message = "API call to endpoint : #{endpoint} failed"
        BrowserStack.logger.error(message)
        raise message
      end
      begin
        BrowserStack.logger.info("API success :: endpoint: #{endpoint} :: response: #{response.body} :: address: #{address}")
        JSON.parse(response.body)['disconnected'] == "true"
      rescue => e
        message = "JSON parse failed on response.body: #{response.body}, error message: #{e.message}"
        BrowserStack.logger.error(message)
        raise message
      end
    end

    def device_disconnected_after_voiceover_setup?(address)
      isConnectedMaxRetries = 5
      retryCount = 1
      while retryCount <= isConnectedMaxRetries && device_address_disconnected?(address)
        BrowserStack.logger.info("Bluetooth for device: #{@device} is not yet connected, will sleep 1 second and retry. Try number: #{retryCount}")
        retryCount += 1
        sleep 1
      end
      device_address_disconnected?(address)
    end

    # Gets called at the voiceover host machine (bluetooth client)
    # It triggers voiceover_init API on the bluetooth server mini (which could be localhost as well)
    def setup(device_id, session_id)
      BrowserStack.logger.info("[VOICEOVER] Got voiceover setup request for session_id: #{session_id}, device_id: #{device_id}")
      raise "Monterey and higher macOS versions cannot act as a bluetooth server, please add a proper bluetooth server in config" if @is_monterey_or_higher && @bluetooth_server_ip == "localhost"

      endpoint = "http://#{@bluetooth_server_ip}:45671/voiceover_init?session_id=#{session_id}&device_id=#{device_id}"
      response = BrowserStack::HttpUtils.make_get_request(endpoint, 15)
      unless response.status.to_i == 200
        message = "API call to endpoint : #{endpoint} failed"
        BrowserStack.logger.error(message)
        raise message
      end
      begin
        BrowserStack.logger.info("API success :: endpoint: #{endpoint} :: response: #{JSON.parse(response.body)}")
        JSON.parse(response.body)['mini_bluetooth_name']
      rescue => e
        message = "JSON parse failed at setup phase on response.body: #{response.body}, error message: #{e.message}"
        BrowserStack.logger.error(message)
        raise message
      end
    end

    # Writes required params to the voiceover used file to make /push_to_cls calls work
    def write_session_params_to_voiceover_used_file(device_id, params)
      voiceover_used_file_path = "#{STATE_FILES_DIR}/voiceover_used_#{device_id}"
      params_needed_for_cls = params.slice(:genre, :live_session_id, :app_live_session_id)
      File.open(voiceover_used_file_path, 'w') { |f| f.write(params_needed_for_cls.to_json) }
      BrowserStack.logger.info("[VOICEOVER] Wrote params: #{params_needed_for_cls.to_json} to file: #{voiceover_used_file_path}")
    rescue => e
      BrowserStack.logger.error("Could not write params to used file, error : #{e.message}")
    end

    # Gets called at the voiceover host machine (bluetooth client)
    # It creates state files on both bluetooth client and server
    # Used for cleanup purposes
    def mark_voiceover_used(device_id, params)
      # First, create the used file on bluetooth client machine
      device_state = DeviceState.new(device_id)
      device_state.touch_voiceover_used_file
      write_session_params_to_voiceover_used_file(device_id, params)

      endpoint = "http://#{@bluetooth_server_ip}:45671/voiceover_mark_device_used?device_id=#{device_id}"
      endpoint += "&genre=#{params[:genre]}&live_session_id=#{params[:live_session_id]}" if params[:genre].to_s.eql?(LIVE_TESTING)
      endpoint += "&genre=#{params[:genre]}&app_live_session_id=#{params[:app_live_session_id]}" if params[:genre].to_s.eql?(APP_LIVE_TESTING)
      response = BrowserStack::HttpUtils.make_get_request(endpoint, 15)
      unless response.status.to_i == 200
        message = "API call to endpoint : #{endpoint} failed"
        BrowserStack.logger.error(message)
        raise message
      end
      begin
        BrowserStack.logger.info("API success :: endpoint: #{endpoint} :: response: #{response.body}")
        JSON.parse(response.body)['success'] == "true"
      rescue => e
        message = "JSON parse failed on response.body: #{response.body}, error message: #{e.message}"
        BrowserStack.logger.error(message)
        raise message
      end
    end

    # Gets called at the voiceover host machine (bluetooth client)
    # It calls API endpoint to bluetooth server, which performs the actual cleanup
    def trigger_cleanup_on_bluetooth_server(device_id)
      endpoint = "http://#{@bluetooth_server_ip}:45671/voiceover_cleanup?device_id=#{device_id}"
      response = BrowserStack::HttpUtils.make_get_request(endpoint, 15)
      unless response.status.to_i == 200
        message = "API call to endpoint : #{endpoint} failed"
        BrowserStack.logger.error(message)
        raise message
      end
      begin
        BrowserStack.logger.info("API success :: endpoint: #{endpoint} :: response: #{response.body}")
        JSON.parse(response.body)['success'] == "true"
      rescue => e
        message = "JSON parse failed on response.body: #{response.body}, error message: #{e.message}"
        BrowserStack.logger.error(message)
        raise message
      end
    end

    # Gets called at the bluetooth server machine
    # Disables mini bluetooth and kills interaction server if bluetooth is not serving any devices
    def cleanup(device_id)
      # If, for some reason, cleanup of a device got stuck and its corresponding used file could not be deleted
      delete_stale_voiceover_used_files

      # Delete used file of current device
      voiceover_used_file = "#{STATE_FILES_DIR}/voiceover_used_#{device_id}"
      FileUtils.rm_f(voiceover_used_file) if File.exist?(voiceover_used_file)

      session_count = active_voiceover_sessions
      # Do cleanup in case of no active running sessions
      if session_count == 0
        BrowserStack.logger.info("Performing voiceover cleanup...")
        kill_interaction_server
        @blueutil_helper.disable_bluetooth
        FileUtils.touch(VOICEOVER_CLEANUP_DONE_FILE)
      else
        BrowserStack.logger.info("active voiceover sessions = #{session_count}, not performing cleanup")
      end
    end

    # Gets called at the bluetooth server machine
    # Starts bluetooth & interaction server
    def start_bluetooth_and_interaction_server
      @blueutil_helper.enable_bluetooth
      start_interaction_server
      mini_bluetooth_name
    end
  end
end
