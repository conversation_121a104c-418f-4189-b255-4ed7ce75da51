require_relative '../../config/constants'
require_relative '../utils/osutils'
require_relative '../utils/utils'
require_relative '../custom_exceptions'
require_relative '../utils/idevice_utils'
require 'json'
require 'vpim'

class CustomContacts
  class << self

    def load_custom_contacts(device, file)
      # taking vcf file to convert to 2 json files and saving them
      contact_list_path, contact_metadata_path = vcf_to_json_conversion(file, device)

      tmp_path = custom_media_folder(device)
      contact_list_plist = "#{tmp_path}/contact_list.plist"
      contact_metadata_plist = "#{tmp_path}/contact_metadata.plist"
      FileUtils.touch([contact_list_plist, contact_metadata_plist])

      # converting above 2 json files to binary plist files
      convert_json_to_plist(contact_list_path, contact_list_plist)
      convert_json_to_plist(contact_metadata_path, contact_metadata_plist)

      # loading contacts using plists generated above using idevicecontacts utility
      OSUtils.execute_retry IdeviceUtils.method(:load_contacts), device, contact_list_plist, contact_metadata_plist, retries: 2
    rescue CustomContactsError => e
      raise e
    rescue => e
      BrowserStack.logger.info "load_custom_contacts : Parsing vcf file failed with error message : #{e.message} #{e.backtrace.join("\n")}"
      raise CustomContactsError.new("Load Custom Contacts Failed", BROWSERSTACK_ERROR_STRING, "custom_contacts_preload_failed")
    end

    def convert_json_to_plist(json_file, plist_file)
      BrowserStack.logger.info "convert_json_to_plist json_file #{json_file}, plist_file #{plist_file}"
      `plutil -convert binary1 \"#{json_file}\" -o \"#{plist_file}\"`
    end

    def vcf_to_json_conversion(file, device)
      vcards = []
      begin
        vcards = Vpim::Vcard.decode(File.read(file))
      rescue => e
        BrowserStack.logger.info "vcf_to_json_conversion failed"
        raise CustomContactsError.new("Invalid vcf file", USER_ERROR_STRING, "load_custom_contacts_failed")
      end
      contacts_list = {}
      contacts_metadata = {}
      id = FIRST_CUSTOM_CONTACT_ID
      vcards.each do |vcard|
        contacts_list[id.to_s] = get_contact_details(vcard)
        contacts_metadata[id.to_s] = {}
        contacts_metadata[id.to_s]["phone"] = get_phone_details(vcard)
        contacts_metadata[id.to_s]["email"] = get_email_details(vcard)
        contacts_metadata[id.to_s]["address"] = get_address_details(vcard)
        id += 1
      end
      tmp_path = custom_media_folder(device)
      contacts_list_file = "#{tmp_path}/contacts_list_file.json"
      contacts_metadata_file = "#{tmp_path}/contacts_metadata_file.json"
      Utils.write_to_file(contacts_list_file, JSON.dump(contacts_list))
      Utils.write_to_file(contacts_metadata_file, JSON.dump(contacts_metadata))

      [contacts_list_file, contacts_metadata_file]
    end

    def get_contact_details(vcard)
      ret = {}
      ret["first name"] = vcard.name.given
      ret["last name"] = vcard.name.family
      ret["company name"] = vcard.org.fetch(0) unless vcard.org.nil?
      ret["middle name"] = vcard.name.additional
      ret["com.apple.syncservices.RecordEntityName"] = "com.apple.contacts.Contact"
      ret
    end

    def get_phone_details(vcard)
      ret = {}
      # 51 is hardcoded value which is required in the idevicecontacts utility, it can't be obtained from the standard vcf format
      ret["contact"] = ["51"]
      ret["value"] = (vcard.telephone.nil? ? "" : vcard.telephones.first.to_s)
      ret["type"] = vcard.telephone.location.first unless vcard.telephone.nil?
      ret["type"] = "mobile" unless ["mobile", "home", "work", "school", "iPhone", "main", "pager", "other"].include? ret["type"]
      ret["com.apple.syncservices.RecordEntityName"] = "com.apple.contacts.Phone Number"
      ret
    end

    def get_email_details(vcard)
      ret = {}
      ret["contact"] = ["51"]
      ret["type"] = vcard.email.location.first unless vcard.email.nil?
      ret["type"] = "home" unless ["home", "work", "school", "iCloud", "other"].include? ret["type"]
      ret["value"] = vcard.email.to_s unless vcard.email.nil?
      ret["com.apple.syncservices.RecordEntityName"] = "com.apple.contacts.Email Address"
      ret
    end

    def get_address_details(vcard) # rubocop:todo Metrics/AbcSize
      ret = {}
      ret["contact"] = ["51"]
      ret["type"] = vcard.address.location.first unless vcard.address.nil?
      ret["type"] = "home" unless ["home", "work", "school", "other"].include? ret["type"]
      # 7 is hardcoded value which is required in the idevicecontacts utility, it can't be obtained from the standard vcf format
      ret["label"] = "7"
      ret["street"] = vcard.address.street unless vcard.address.nil?
      ret["city"] = vcard.address.region unless vcard.address.nil?
      ret["state"] = vcard.address.locality unless vcard.address.nil?
      ret["postal code"] = vcard.address.postalcode unless vcard.address.nil?
      ret["country code"] = "IN"
      ret["country"] = vcard.address.country unless vcard.address.nil?
      ret["com.apple.syncservices.RecordEntityName"] = "com.apple.contacts.Street Address"
      ret
    end

    def custom_media_folder(device)
      "/tmp/custom_media_#{device}"
    end
  end
end
