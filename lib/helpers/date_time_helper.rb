require_relative '../models/device_state'
require_relative 'data_report_helper'
require_relative 'wda_client'
require_relative 'xcui_test_helper'
require_relative '../utils/configuration_profiles_manager'
require_relative '../utils/ios_mdm_service_client'
require_relative '../utils/idevice_utils'
require_relative '../utils/custom_mdm_manager'
require 'date'
require_relative '../utils/idevice_ffi/time_format_util'
require_relative '../utils/utils'
require_relative '../../config/constants'

class DateTimeHelper  # rubocop:todo Metrics/ClassLength
  DATE_TIME_TAG = "DATE_TIME"

  def initialize(device_id, params)
    @device = device_id
    @session_id = params['session_id'] || params['app_live_session_id'] || params['live_session_id']
    @product = params['product']
    @user_id = params["user_id"]
    @params = params

    device_config = DeviceManager.device_configuration_check(@device)
    @wda_client = WdaClient.new(device_config['webdriver_port'])
    @device_state = DeviceState.new(device_id)
    @server_config = BrowserStack::Configuration.new.all
    @reporter = DataReportHelper.settings_reporter(session_id: @session_id, device: @device, product: @product, ios_version: device_config["device_version"], user_id: @user_id)
  end

  def automatic_via_mdm
    log "Setting automatic true via MDM"

    if @device_state.passcode_file_present?
      log "Passcode enabled, can't set automatic via MDM"
      raise "passcode_enabled"
    end

    begin
      config_profiles_manager = ConfigurationProfilesManager.new(@device, BrowserStack.logger)

      # Install Restrictions with forceAutomaticDateAndTime set to true
      @device_state.touch_force_automatic_date_time_file
      config_profiles_manager.install_profile(:restrictions, install_via: :automatic)

      log "Installed restrictions with force automatic enabled"
      # Install Restrictions with forceAutomaticDateAndTime set to false
      @device_state.remove_force_automatic_date_time_file
      config_profiles_manager.install_profile(:restrictions, install_via: :automatic)
      log "Installed restrictions with force automatic disabled"

      sleep 5 # Wait for the popup to appear
      if CustomMDMManager.is_custom_mdm_device?(@device)
        log "locking device using WDA"
        device_config = DeviceManager.device_configuration_check(@device)
        @wda_client.lock_device(device_config["device_version"])
      else
        log "locking device using MDM"
        BrowserStack::IosMdmServiceClient.lock_device(@device)
      end
      log "locked device, unlocking.."
      @wda_client.unlock_device
      log "unlocked device"
    rescue => e
      log "Error in setting automatic via mdm: #{e.message}, backtrace: #{e.backtrace.join("\n")}"
      unless CustomMDMManager.is_custom_mdm_device?(@device)
        @device_state.remove_force_automatic_date_time_file
        raise "Date Time automatic failed: #{e}"
      end
    end
  end

  #TODO: Make this method DF generic to handle wda response
  def call_wda_method_with_args(method, *args)
    log "Calling WDA method #{method}"

    result = "failed"
    error = {}

    begin
      @device_state.touch_settings_automation_executing_file
      response = @wda_client.send(method, *args)
      wda_error = response["value"]["error"]
      if wda_error == "no error"
        result = "success"
      else
        error = { "type" => "automation_error", "message" => wda_error }
      end
    rescue WdaClientError => e
      error = { "type" => e.response_object, "message" => e.message }
    end
    @device_state.remove_settings_automation_executing_file
    save_screenshot(response["value"]["screenshot"], method) if response["value"]["screenshot"]
    { "result" => result, "error" => error }
  end

  def call_wda_method_with_args_wrapper(method, *args)
    call_wda_method_with_args(method, *args)
  rescue => e
    error = { "type" => "StandardError", "message" => e.message }
  end

  def push_to_pusher(event)
    if @product == "app_live_testing"
      Utils.notify_pusher(event, @params, @device)
    else
      Utils.notify_pusher_for_live(event, @params, @device)
    end
  end

  def automatic(enabled, send_to_pusher: false)
    log "Request for setting automatic #{enabled} "
    return true if enabled && @device_state.date_time_automatic_file_present? || !(enabled || @device_state.date_time_automatic_file_present?)

    # Result code specifies by which method does the request for enabling true got satisfied
    # 0: Success through MDM
    # 1: Failed through MDM and success through WDA
    # 2: Failed through MDM and WDA
    result_code = 0
    success = false
    start = Time.now.to_i
    zombie_error = nil
    error = {}
    if enabled
      begin
        automatic_via_mdm
        success = true
      rescue => e
        result_code += 1
        zombie_error = "Setting automatic true via MDM failed" unless e.message == "passcode_enabled"
        error["mdm"] = e.message
      end
    end

    unless success
      response = call_wda_method_with_args(:set_date_time_automatic, enabled)
      success = response["result"] == "success"
      unless success
        result_code += 1
        error["wda"] = error
        zombie_error = "Both MDM and WDA failed to set automatic"
      end
    end

    send_to_pusher && push_to_pusher(success ? "DateTimeAutomaticSuccess" : "DateTimeAutomaticFailure")

    @reporter.report_settings_data(
      ['autosetdatetime'],
      values: [enabled],
      zombie_error: zombie_error,
      extra_data: [{
        time_taken: Time.now.to_i - start,
        result_code: result_code,
        error: error
      }]
    )
    enabled = !enabled unless success
    enabled ? @device_state.touch_date_time_automatic_file : @device_state.remove_date_time_automatic_file
    success
  end

  def time_format(new_time_format = TWENTY_FOUR_HOUR_TIME_FORMAT, send_to_pusher: false)
    log "Request for setting time format #{new_time_format}"
    start = Time.now.to_i
    @device_state.touch_custom_time_format_set_file
    idevice_result, idevice_error = update_time_format_via_idevice(new_time_format)
    unless idevice_result
      Utils.enable_settings_app(@device, { automate_session_id: @session_id })
      foreground_app_bundle_id = foreground_app
      wda_result, wda_error = update_time_format_via_wda(new_time_format)
    end

    result = idevice_result || wda_result
    log "Result for setting time format: #{result}"

    result
  ensure
    Utils.disable_settings_app(@device, { automate_session_id: @session_id })

    zombie_error = nil
    if idevice_error && wda_error
      zombie_error = {
        idevice_error: idevice_error,
        wda_error: wda_error
      }
    end

    @reporter.report_settings_data(
      ['TimeFormat'],
      values: [new_time_format],
      zombie_error: zombie_error,
      extra_data: [{
        time_taken: Time.now.to_i - start,
        idevice_result: idevice_result,
        wda_result: wda_result,
        idevice_error: idevice_error,
        wda_error: wda_error
      }]
    )

    @wda_client.set_foreground(foreground_app_bundle_id, "app_name", {}, @session_id) unless foreground_app_bundle_id.nil?
  end

  def log(line)
    self.class.log(line)
  end

  def self.log(line)
    BrowserStack.logger.info "#{DATE_TIME_TAG}: #{line}"
  end

  def time(new_time, send_to_pusher: false)
    log "Request for changing time to #{new_time}"
    start = Time.now.to_i

    data = call_wda_method_with_args(:set_device_time, new_time)
    success = data["result"] == "success"
    if success
      @device_state.remove_date_time_automatic_file
      @device_state.touch_custom_time_file
    end

    @reporter.report_settings_data(
      ['time'],
      values: [new_time],
      zombie_error: data["error"]["type"],
      extra_data: [{
        time_taken: Time.now.to_i - start,
        error: data["error"]
      }]
    )

    send_to_pusher && push_to_pusher(success ? "SetExactTimeSuccess" : "SetExactTimeFailure")
    success
  end

  def change_date(new_date, send_to_pusher: false) # rubocop:todo Metrics/MethodLength
    log "Request for changing date to #{new_date}"
    start = Time.now.to_i

    set_device_date_success = false
    trust_popup_success = true
    success = false
    error_code = nil
    data = { "error" => { "type" => "Date not valid" } }

    foreground_app_bundle_id = nil

    if validate_date(new_date)
      foreground_app_bundle_id = current_foreground_app
      data = call_wda_method_with_args_wrapper(:set_device_date_upto_7days, new_date)
      set_device_date_success = data["result"] == "success"

      validate_response = IdeviceUtils.idevicepair(@device, "validate")
      unless validate_response.start_with?("SUCCESS")
        log "Device not paired, pairing it and clicking on trust popup."
        pair_response = IdeviceUtils.idevicepair(@device, "pair")
        unless pair_response.start_with?("SUCCESS")
          data = call_wda_method_with_args_wrapper(:trust_popup)
          trust_popup_success = data["result"] == "success"
        end
      end
      success = set_device_date_success && trust_popup_success
    else
      error_code = "CD_0001"
    end

    @device_state.remove_date_time_automatic_file if set_device_date_success

    @reporter.report_settings_data(
      ['date'],
      values: [new_date],
      zombie_error: data["error"]&.fetch("type", nil),
      extra_data: [{
        time_taken: Time.now.to_i - start,
        error: data["error"],
        validate_response: validate_response,
        pair_response: pair_response,
        trust_popup_response: trust_popup_success
      }]
    )

    # Need to add sleep to give enough time to wda for pressing home screen
    # TODO: Instead of sleep, use some other approach to open settings access
    sleep(3)
    launch_foreground_app(foreground_app_bundle_id)

    send_to_pusher && push_to_pusher(success ? "SetExactDateSuccess" : "SetExactDateFailure")
    [success, error_code]
  end

  def validate_date(new_date)
    new_day = new_date["day"].to_i
    new_month = new_date["month"].to_i
    new_year = new_date["year"].to_i
    new_date = Date.new(new_year, new_month, new_day)

    initial_date = initial_date_on_device

    # Check if new_date is in the future also if it is within 7 days timeframe of initial_date
    if new_date >= initial_date
      days_diff = (new_date - initial_date).to_i
      return days_diff <= 7
    end

    false
  end

  def current_date_on_device(should_touch_state_file: true)
    time_since1970 = IdeviceUtils.ideviceinfo(@device, "TimeIntervalSince1970").first
    time_zone_offset_from_utc = IdeviceUtils.ideviceinfo(@device, "TimeZoneOffsetFromUTC").first
    time = Time.at(time_since1970.to_f + time_zone_offset_from_utc.to_f)

    year = time.year
    month = time.month
    day = time.day

    should_touch_state_file &&
    @device_state.write_to_custom_date_file({ date: { day: day, month: month, year: year } }.to_json)

    Date.new(year, month, day)
  end

  def initial_date_on_device(should_touch_state_file: true)
    if @device_state.custom_date_file_present?
      begin
        date = JSON.parse(@device_state.read_custom_date_file, symbolize_names: true)[:date]
        return Date.new(date[:year], date[:month], date[:day])
      rescue => e
        log "Error occured while parsing custom date file, falling back to ideviceinfo. Error: #{e.message}"
      end
    end
    current_date_on_device(should_touch_state_file: should_touch_state_file)
  end

  def current_date_on_device_hash
    date = current_date_on_device(should_touch_state_file: false)
    {
      day: date.day,
      month: date.month,
      year: date.year
    }
  end

  def initial_date_on_device_hash
    date = initial_date_on_device(should_touch_state_file: false)
    {
      day: date.day,
      month: date.month,
      year: date.year
    }
  end

  def current_foreground_app
    @wda_client.foreground
  rescue => e
    log "Error occured while fetching foreground app bundle id: #{e.message}"
    nil
  end

  def launch_foreground_app(foreground_app_bundle_id)
    @wda_client.set_foreground(foreground_app_bundle_id, "app_name", {}, @session_id) unless foreground_app_bundle_id.nil?
  rescue => e
    log "Error occured while setting foreground app: #{e.message}"
    nil
  end

  def save_screenshot(screenshot, name)
    image_data = Base64.decode64(screenshot)
    output_filename = File.join(@server_config['screenshot_dir'], "#{name}_#{@udid}.png")
    File.open(output_filename, 'wb') do |file|
      file.write(image_data)
    end
  end

  def cleanup_date
    log "Request for cleaning date on #{@device}"
    start_time = Time.now.to_i

    automatic_via_mdm

    @device_state.remove_custom_date_file
    @device_state.touch_date_time_automatic_file
    @reporter.report({ "message" => "automatic enabled via mdm", "status" => "pass", "time_taken" => Time.now.to_i - start_time }, event_name: "change-date-cleanup")
  rescue => e
    @reporter.report({ "error" => e.message, "status" => "fail", "time_taken" => Time.now.to_i - start_time }, event_name: "change-date-cleanup", zombie_kind: "change-date-cleanup-failure")
    log "Some error occured while resetting date #{@device}. Error: #{e.message}, backtrace: #{e.backtrace}"
    raise e
  end

  def cleanup
    log "Request for cleaning date & time on #{@device}"
    test_runner = XCUITestHelper.new(@device, @session_id)
    response = test_runner.run_test(:set_time_to_utc)
    success = response["result"] == "success"
    unless success
      @reporter.report({ "error" => response["error_message"] }, zombie_only: true, zombie_kind: "date-time-cleanup-fail")
      raise DateTimeException, "Error in resetting time: #{response['error']}, #{response['error_message']}"
    end

    @device_state.remove_date_time_automatic_file
    @device_state.remove_custom_time_file
    @device_state.remove_custom_date_file
    @device_state.remove_custom_time_format_set_file
  end

  private

  def time_format_util
    @time_format_util ||= TimeFormatUtil.new({ device_id: @device })
  end

  def update_time_format_via_idevice(new_time_format)
    time_format_util.set(new_time_format)
    current_time_format = @wda_client.device_info["value"]["timeFormat"]
    [current_time_format == new_time_format, nil]
  rescue => e
    log "Failed to set time format via idevice, error: #{e.message}, #{e.backtrace}"
    [false, e.message]
  end

  def update_time_format_via_wda(new_time_format)
    wda_response = @wda_client.set_device_time_format(new_time_format)
    raise wda_response['value']['message'] unless wda_response['value']['status'] == 'pass'

    [true, nil]
  rescue => e
    log "Failed to set time format via WDA, error: #{e.message}, #{e.backtrace}"
    [false, e.message]
  end

  def foreground_app
    @wda_client.foreground
  rescue => e
    log "Failed to get foreground app, error: #{e.message}, #{e.backtrace}"
    nil
  end
end
