require_relative '../utils/utils'
require_relative '../../server/device_manager'
require_relative './apple_card'
require_relative '../utils/socks5_forwarder'
require 'base64'

module Secure
  class AppleWallet # rubocop:todo Metrics/ClassLength
    WALLET_TAG = "[WALLET]"
    MAX_ATTEMPTS = 3

    def initialize(device_id, session_id, product = "")
      @device_id = device_id
      @session_id = session_id
      @product = product
      @server_config = BrowserStack::Configuration.new.all
      @device_config = DeviceManager.device_configuration_check(@device_id)
      @wallet_add_card_data_reporter = DataReportHelper.new(
        'add-card',
        session_id: session_id,
        product: product,
        ios_version: @device_config["device_version"],
        device: device_id
      )
      @wda_client = WdaClient.new(@device_config['webdriver_port'])
      conf = BrowserStack::Configuration.new
      @static_conf = conf.all
      @custom_rails_endpoint = @static_conf['static_conf'].device_rails_endpoint(device_id)
      @name = "Browserstack"
      @attempted_cards = []
    end

    def add_sandbox_card(cards = nil, network = nil) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
      @start_time = Time.now.to_i
      @failure_data = {}
      @success_data = {}

      begin
        @attempts ||= 1
        setup_environment if @device_config["device_version"].to_i >= 18

        @most_stable_card = if cards.nil? || cards.empty? || @attempts > cards.length
                              get_card_using_network(network)
                            else
                              get_most_stable_card(cards)
                            end

        log_add_card_attempt

        res = @wda_client.add_card(
          @name,
          @most_stable_card.number,
          @most_stable_card.date,
          @most_stable_card.year,
          @most_stable_card.cvv
        )

        log_response_received(res)

        if res['value']['status'] == "pass"
          report_successfull_card_to_zombie(res['value'])
          @most_stable_card.mark_success
          @attempted_cards.append(@most_stable_card) unless cards.nil?

          true
        else
          report_failed_card_to_zombie(res['value'])
          save_failed_card_screenshot(res['value']['screenshot']) if res['value']['screenshot']

          #Card is marked as failed only when the automation couldn't complete due to the provider issue.
          @most_stable_card.mark_fail if res['value']['message'].include?('Done')
          @attempted_cards.append(@most_stable_card) unless cards.nil?

          raise WdaAutomationError.new(res['value']['message']), res['value']['debugDescription']
        end

      rescue WdaAutomationError => e
        log_wda_automation_failure(e)

        if (@attempts += 1) <= MAX_ATTEMPTS
          log_reattempt_card
          retry
        end

        false
      rescue WdaClientError => e
        report_wda_client_error_to_zombie(e)

        if (@attempts += 1) <= MAX_ATTEMPTS
          log_reattempt_card
          retry
        end
        false

      rescue => e
        report_standard_error_to_zombie(e)
        log_standard_error(e)

        if (@attempts += 1) <= MAX_ATTEMPTS
          log_reattempt_card
          retry
        end
        false
      ensure
        reset_environment if @device_config["device_version"].to_i >= 18
        metrics = @success_data.merge!(@failure_data)

        # Reports card addition metrics to zombie.
        @wallet_add_card_data_reporter.report(metrics)

        # Reports cards success rate to rails.
        report_card_success_to_rails unless cards.nil? || cards.empty?
      end
    end

    private

    def get_card_using_network(card_network)
      if SANDBOX_CARDS.keys.include?(card_network)
        number, date, year, cvv = SANDBOX_CARDS[card_network].sample
      elsif SANDBOX_CARDS.keys.include?("0")
        number, date, year, cvv = SANDBOX_CARDS.values.sample
      else
        sample_network = SANDBOX_CARDS.keys.sample
        number, date, year, cvv = SANDBOX_CARDS[sample_network].sample
      end

      Secure::AppleCard.new(sample_network, number, date, year, cvv)
    end

    def get_most_stable_card(cards)
      # Sequentially falls back to lesser stable cards if preferred candidate fails.
      # Ref: https://browserstack.atlassian.net/wiki/spaces/ENG/pages/**********/WIP+Apple+Pay+card+stability+improvement#Discussions%3A
      Secure::AppleCard.new(
        cards[@attempts - 1]['provider'],
        cards[@attempts - 1]['number'],
        cards[@attempts - 1]['date'],
        cards[@attempts - 1]['year'],
        cards[@attempts - 1]['cvv']
      )
    end

    def setup_environment
      configure_socks5_forwarder
    end

    def reset_environment
      @socks5_forwarder.reset_privoxy
    end

    def configure_socks5_forwarder
      @socks5_forwarder ||= Privoxy::Socks5Forwarder.new(@device_id)
      @socks5_forwarder.forward_traffic(["*.icloud.com"])
    end

    def report_successfull_card_to_zombie(response_value)
      end_time = Time.now.to_i
      @success_data = @wallet_add_card_data_reporter.populate_data_hash(
        wda_response: response_value,
        additional_data: {
          "sandbox_card_used" => [
            @most_stable_card.number,
            @most_stable_card.date,
            @most_stable_card.year,
            @most_stable_card.cvv,
            @most_stable_card.provider
          ]
        },
        interval: end_time - @start_time,
        retries: @attempts
      )
    end

    def report_card_success_to_rails
      rails_endpoint = "#{@custom_rails_endpoint}/admin/update_card_stability"

      req_body = {
        "cards" => []
      }

      rails_request_required = false

      @attempted_cards.each do |card|
        next unless card.stability_is_reported

        rails_request_required = true

        req_body["cards"].append({
          "provider": card.provider,
          "number": card.number,
          "status": card.status
        })
      end

      if rails_request_required
        res = BrowserStack::HttpUtils.send_post(rails_endpoint, req_body)

        BrowserStack.logger.info("#{WALLET_TAG} Request body for rails: #{req_body}")
        BrowserStack.logger.info("#{WALLET_TAG} Recieved response from rails: #{res}")
        return true if res.code.to_i == 200
      end

      false
    end

    def report_failed_card_to_zombie(response_value)
      end_time = Time.now.to_i
      failed_sandbox_cards = []

      failed_sandbox_cards.append([
        @most_stable_card.number,
        @most_stable_card.date,
        @most_stable_card.year,
        @most_stable_card.cvv,
        @most_stable_card.provider
      ])
      @failure_data = @wallet_add_card_data_reporter.populate_data_hash(
        wda_response: { "status" => "fail" },
        additional_data: {
          "error" => "failure response from curl request",
          "sandbox_card_used" => failed_sandbox_cards,
          "screenshot_dir" => (@server_config['screenshot_dir']).to_s +
            "apple_pay_wallet_#{@udid}_*"
        },
        interval: end_time - @start_time, retries: @attempts
      )
    end

    def save_failed_card_screenshot(screenshot)
      image_data = Base64.decode64(screenshot)
      output_filename = File.join(
        @server_config['screenshot_dir'],
        "apple_pay_wallet_#{@udid}_#{@attempts}.png"
      )
      File.open(output_filename, 'wb') do |file|
        file.write(image_data)
      end
    end

    def report_wda_client_error_to_zombie(error)
      end_time = Time.now.to_i
      @failure_data = @wallet_add_card_data_reporter.populate_data_hash(
        wda_response: { "error" => error.message },
        additional_data: { "@wda-client-status" => @wda_client.running? ? "running" : "non running" },
        interval: end_time - @start_time,
        retries: @attempts
      )
    end

    def report_standard_error_to_zombie(error)
      end_time = Time.now.to_i
      @failure_data = @wallet_add_card_data_reporter.populate_data_hash(
        wda_response: { "error" => error.message },
        additional_data: { "exception" => error.message },
        interval: end_time - @start_time,
        retries: @attempts
      )
    end

    def log_add_card_attempt
      BrowserStack.logger.info(
        "#{WALLET_TAG} Attempting to add card to wallet app with " \
        "details : number -  #{@most_stable_card.number}, date- #{@most_stable_card.date}, " \
        "year - #{@most_stable_card.year}, cvv - #{@most_stable_card.cvv}"
      )
    end

    def log_response_received(res)
      BrowserStack.logger.info(
        "#{WALLET_TAG} Received response form wda @wda_client : response -  #{res}"
      )
    end

    def log_wda_automation_failure(error)
      BrowserStack.logger.info(
        "#{WALLET_TAG} automation failure - #{error}; element tree - #{error.message}"
      )
    end

    def log_reattempt_card
      BrowserStack.logger.info(
        "#{WALLET_TAG} Re-attempting to add card to wallet app : attempt count -  #{@attempts}"
      )
    end

    def log_standard_error(error)
      BrowserStack.logger.info(
        "#{WALLET_TAG} Standard error while adding card to wallet app : error -  #{error.message}"
      )
    end
  end
end
