require 'socket'
require 'base64'
require 'digest/sha1'
require 'fileutils'
require 'json'
require_relative '../utils/utils'

class NetworkLogsUtil # rubocop:todo Metrics/ClassLength

  MITM_WS_READY = 'MITM_WS_READINESS_CHECK_PASS'.freeze
  MITM_WS_FAILED = 'MITM_WS_READINESS_CHECK_FAIL'.freeze

  def initialize(device, params)
    @device = device
    @params = params
    @event_hash = {}
    @network_logs_event_hash = {}
    begin
      @state_session_data = JSON.parse(File.read("#{STATE_FILES_DIR}/#{@device}_session"))
    rescue => e
      @state_session_data = {}
    end

    # Store all the event for network logs flow and will store in EDS at the end of the session.
    @network_logs_event_hash_file = "#{STATE_FILES_DIR}/al_network_logs_hash_"\
            "#{@params['app_live_session_id']}"
    FileUtils.touch(@network_logs_event_hash_file)
  end

  def run
    proxy_setup_pid = fork do
      safe_execute(
        :check_mitm_ws_readiness, @params[:mitm_ws_readiness_timeout].to_i, @params[:mitm_ws_readiness_interval].to_i
      )
      safe_execute(:persist_network_logs_hash, @network_logs_event_hash)
    end
    Process.detach(proxy_setup_pid)
  end

  def set_local_params
    @params['hosts'] ||= @state_session_data['hosts']
    @params['proxy_type'] ||= @state_session_data['proxy_type']
    @params['tunnelPorts'] ||= @state_session_data['tunnelPorts']
    @params['tunnelHostServer'] ||= @state_session_data['tunnelHostServer']
    @params['force_local'] ||= @state_session_data['force_local']
    if @state_session_data['startElement'] == 'local'
      @params['resolve_common_hosts_on_device'] ||=
        @state_session_data['resolve_common_hosts_on_device']
    end
  end

  def safe_execute(method, *args)
    start_time = Time.now
    BrowserStack.logger.info "#{self.class}##{__method__} calling #{method}"
    send(method, *args)
  rescue => e
    BrowserStack.logger.error("#{self.class}##{method} Exception: #{e.message}, stacktrace : #{e.backtrace}")
  ensure
    @network_logs_event_hash["#{method}_time_taken"] = Time.now - start_time
  end

  def self.perform_clean_up(session_id)
    network_logs_hash = {}
    begin
      network_logs_event_hash_file = "#{STATE_FILES_DIR}/al_network_logs_hash_#{session_id}"
      if File.exist?(network_logs_event_hash_file)
        File.open(network_logs_event_hash_file, 'r') do |file|
          network_logs_hash = JSON.parse(file.read)
        end
        network_logs_hash.merge!(
          {
            event_name: 'network_logs_2_0_json_event', session_id: session_id,
            genre: 'app_live_testing', product: 'app_live',
            team: 'app_live', platform: 'ios'
          }
        )
        FileUtils.rm_f(network_logs_event_hash_file)
      end
    rescue => e
      network_logs_hash[:error] = "NETWORK_LOGS_HASH_COLLECTION_FAILED"
      BrowserStack.logger.error("#{self.class}##{__method__} Exception: #{e.message}, stacktrace : #{e.backtrace}")
    ensure
      Utils.send_to_eds(eds_hash, EdsConstants::APP_LIVE_WEB_EVENTS, true, req_params: @params)
    end
  end

  private

  def check_mitm_ws_readiness(timeout = 30, check_interval = 1)
    start_time = Time.now
    @network_logs_event_hash[:tunnel_setup_start] = start_time
    event_hash = {
      mitm_ws_readiness_time_taken: 0, mitm_ws_readiness_timeout_value: timeout,
      mitm_ws_readiness_interval: check_interval , mitm_ws_readiness_status: false
    }

    while Time.now - start_time < timeout
      break if (event_hash[:mitm_ws_readiness_status] = mitm_ready_for_ws?('localhost', @state_session_data["network_logs_port"]))

      sleep check_interval
      event_hash[:mitm_ws_readiness_time_taken] += check_interval
    end

    notify_pusher(event_hash[:mitm_ws_readiness_status] ? MITM_WS_READY : MITM_WS_FAILED)
    @network_logs_event_hash[:tunnel_setup_time_taken] = Time.now - @network_logs_event_hash[:tunnel_setup_start]
    @network_logs_event_hash.delete(:tunnel_setup_start)
    persist_network_logs_hash(event_hash)
  end

  def mitm_ready_for_ws?(host, port , path = '/updates', timeout = 1)
    key = Base64.strict_encode64(Random.new.bytes(16))
    request = <<~HTTP
      GET #{path} HTTP/1.1
      Host: #{host}:#{port}
      Upgrade: websocket
      Connection: Upgrade
      Sec-WebSocket-Key: #{key}
      Sec-WebSocket-Version: 13

    HTTP

    socket = nil
    begin
      socket = Socket.tcp(host, port, connect_timeout: timeout)
      socket.write(request)
      response = socket.readpartial(1024)

      response.include?("101 Switching Protocols")
    rescue Errno::ECONNREFUSED, Errno::ETIMEDOUT
      false
    ensure
      socket&.close
    end
  end

  def persist_network_logs_hash(hash)
    File.open(@network_logs_event_hash_file, 'r+') do |file|
      file.flock(File::LOCK_EX)
      begin
        existing_data = JSON.parse(file.read)
      rescue => e
        existing_data = {}
      end
      merged_data = existing_data.merge(hash)
      file.rewind
      file.truncate(0)
      file.write(JSON.pretty_generate(merged_data))
      file.flock(File::LOCK_UN)
    end
  end

  def notify_pusher(message, extra_hash = {})
    BrowserStack.logger.info("#{self.class}##{__method__} Notifying app live pusher message : #{message}")
    Utils.notify_pusher(
      message,
      {
        "pusher_auth" => @state_session_data["pusher_auth"],
        "pusher_channel" => @state_session_data["pusher_channel"],
        "session_id" => @params["app_live_session_id"],
        "pusher_url" => @state_session_data["pusher_url"]
      }.merge!(@params),
      @device,
      extra_hash
    )
  end

end
