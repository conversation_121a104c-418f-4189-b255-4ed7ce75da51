# frozen_string_literal: true

require_relative '../../server/device_manager'
require_relative '../utils/idevice_utils'
require_relative '../models/device_state'
require_relative 'xcui_test_helper'
require_relative '../utils/utils'
require_relative 'browserstack_app_helper'
require_relative 'passcode_helper'
require_relative 'apple_wallet'
require_relative '../utils/custom_mdm_manager'

require 'benchmark'

module Secure
  class ApplePaySessionData # rubocop:todo Metrics/ClassLength
    MAX_RETRY_ATTEMPTS = 3
    def initialize(session_id, device_id, product = "")
      @product = product
      @session_id = session_id
      @udid = device_id
      @tags = ["ApplePaySessionData"]
    end

    def log_info(message)
      BrowserStack.logger.info("[#{@tags.join('][')}] #{message}")
    end

    def log_error(message)
      BrowserStack.logger.error("[#{@tags.join('][')}] #{message}")
    end

    def run_and_report_cleanup(app, type = "apple-pay", should_set_passcode: false, should_add_card_to_wallet: false, skip_ios_njb: false, invoked_from: nil)
      @tags += [__method__.to_s]
      event_name = type == "apple-pay" ? "apple-pay-session-data-cleanup" : "non-apple-pay-session-data-cleanup"
      @cleanup_reporter = DataReportHelper.new(
        event_name,
        session_id: @session_id,
        product: @product,
        ios_version: IdeviceUtils.os(@udid),
        device: @udid
      )

      data = {}
      log_info("Cleanup started for #{app}")
      time = Benchmark.realtime do
        data = cleanup(app, should_set_passcode: should_set_passcode, should_add_card_to_wallet: should_add_card_to_wallet, skip_ios_njb: skip_ios_njb)
      end

      data.merge!({ "app" => app, "time_taken" => time })
      data.merge!({ "invoked_from" => invoked_from }) unless invoked_from.nil?
      @cleanup_reporter.report(data)

      raise OFFLINE_REASON_APPLE_PAY_SENSITIVE_DATA unless data["status"] == "pass"

      true
    end

    def cleanup(app, should_set_passcode: false, should_add_card_to_wallet: false, skip_ios_njb: false) # rubocop:todo Metrics/MethodLength
      @tags += [__method__.to_s]
      device_state = DeviceState.new(@udid)

      return { "status" => "pass" } if app == "settings" && !device_state.apple_pay_data_file_present?

      wda_cleanup_status = false
      wda_response_data = ""
      ios_njb_status = false
      ios_njb_data = ""
      error = nil

      result = {}

      begin
        log_info("Running cleanup for #{app}")
        if should_set_passcode
          set_passcode_response, set_passcode_error = set_passcode
          raise "Failed to set passcode #{set_passcode_error}" unless set_passcode_response
        end

        if should_add_card_to_wallet
          add_card_to_wallet_response, add_card_to_wallet_error = add_card_to_wallet
          raise "Failed to add card to wallet #{add_card_to_wallet_error}" unless add_card_to_wallet_response
        end

        wda_client = prepare_wda_client
        wda_cleanup_status, wda_response_data = cleanup_using_wda_client(app, device_state, wda_client)

        ios_njb_status, ios_njb_data = cleanup_using_ios_njb_client(app, device_state, wda_client) if !wda_cleanup_status && !skip_ios_njb
      rescue => e
        error = e
        log_error("Error while running cleanup - #{e.message} #{e.backtrace}")
      end

      result.merge!(generate_and_handle_cleanup_result(app, wda_cleanup_status, wda_response_data, ios_njb_status, ios_njb_data))

      result.merge!({ "status" => "fail" }) unless error.nil?

      if should_set_passcode
        result.merge!({
          "set_passcode_response" => set_passcode_response,
          "set_passcode_error" => set_passcode_error
        })
      end

      if should_add_card_to_wallet
        result.merge!({
          "add_card_to_wallet_response" => add_card_to_wallet_response,
          "add_card_to_wallet_error" => add_card_to_wallet_error
        })
      end

      result
    end

    def prepare_wda_client
      device_config = DeviceManager.device_configuration_check(@udid)
      WdaClient.new(device_config['webdriver_port'])
    end

    def generate_and_handle_cleanup_result(app, wda_cleanup_status, wda_response_data, ios_njb_status, ios_njb_data)
      log_info("Generating data for cleanup run using wda client and ios_njb_app's XCTest suite for #{app}")
      DataReportHelper.generate_wda_xcui_fallback_reporting_data(wda_cleanup_status, wda_response_data, ios_njb_status, ios_njb_data)

    end

    def cleanup_using_wda_client(app, device_state, wda_client)
      @tags += [__method__.to_s]
      if app == "appstore"
        resp = wda_client.reset_logins
        response_handler(app, device_state, resp)
      else
        return true unless device_state.apple_pay_data_file_present?

        resp = wda_client.clear_transaction_defaults
        response_handler(app, device_state, resp)
      end
    rescue => e
      [false, e.message]
    end

    def response_handler(app, device_state, resp)
      @tags += [__method__.to_s]
      if resp['value']['status'] == "pass"
        log_info("Got cleanup success response - #{resp['value']} from wda for #{app}")
        [true, resp['value']]
      else
        [false, resp['value']['message']]
      end
    end

    def cleanup_using_ios_njb_client(app, device_state, wda_client)
      @tags += [__method__.to_s]
      test_runner = XCUITestHelper.new(@udid, @session_id)

      if wda_client.running?
        custom_mdm = CustomMDMManager.is_custom_mdm_device?(@udid).to_s
        dedicated_device = device_state.dedicated_device_file_present?.to_s
        res = wda_client.settings_access("true", dedicated_device, custom_mdm, "true")

        if res['value']['global_settings_access_enabled'].eql?("true")
          result = run_xcui_test(app, test_runner, device_state)
          wda_client.settings_access("false", dedicated_device, custom_mdm, "false")
        else
          log_info("Unable to obtain settings access to run XCUI test")
          raise "Failed to enable settings access"
        end
      else
        result = run_xcui_test(app, test_runner, device_state)
      end

      result
    rescue => e
      [false, e.message]
    end

    def run_xcui_test(app, test_runner, device_state)
      attempts = 0
      result = {}

      while attempts < MAX_RETRY_ATTEMPTS
        BrowserStackAppHelper.build_and_install_browserstack_app(@udid)
        if app == "appstore"
          result = test_runner.run_test(:sign_out_of_appstore)
        else
          result = test_runner.run_test(:clear_transaction_defaults)
          device_state.remove_apple_pay_data_file if result["result"] == "success"
        end

        break if result["result"] == "success"

        attempts += 1

        log_info("Test failed. Retrying... (Attempt #{attempts})")
      end

      log_info("XCTest response - #{result}.")
      [result["result"] == "success", result]
    end

    private

    def passcode_helper
      @passcode_helper ||= Secure::Passcode.new(@udid, @session_id, @product)
    end

    def wallet_helper
      @wallet_helper ||= Secure::AppleWallet.new(@udid, @session_id, @product)
    end

    def set_passcode
      passcode_helper.passcode_settings(REDIS_CLIENT)
      set_passcode_response = passcode_helper.set_passcode
      [set_passcode_response, nil]
    rescue => e
      [false, e.message]
    end

    def clear_passcode
      passcode_helper.clear_passcode(REDIS_CLIENT)
      [true, nil]
    rescue => e
      [false, e.message]
    end

    def add_card_to_wallet
      add_card_to_wallet_response = wallet_helper.add_sandbox_card
      [add_card_to_wallet_response, nil]
    rescue => e
      [false, e.message]
    end
  end
end
