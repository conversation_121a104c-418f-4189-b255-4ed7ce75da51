class XCUITestHelper
  def initialize(device, session_id)
    @device = device
    @session_id = session_id
  end

  def parse_test_output_for_error(err_object)
    error_message = err_object.test_output
    err_object.test_output.split("\n").each do |test_log_line|
      next unless test_log_line.include?("XCTAssertTrue failed") || test_log_line.include?("XCTAssertFalse failed")

      # Eg of test_output_split:  t = 4.29s Assertion Failure: MessagesUITests.swift:36: XCTAssertFalse failed - Some element not found
      error_message = test_log_line.split("-").last.strip
      return error_message
    end
    error_message
  end

  def run_test(test, timeout: 60, env_params: {})
    start_time = Time.now.to_i
    result = "failed"
    error = nil
    error_message = nil

    begin
      BrowserStackAppHelper.run_ui_test(@device, test, timeout, session_id: @session_id, environment_variables: env_params)
      result = "success"
    rescue BrowserStackTestExecutionError => e
      error = "BrowserStackTestExecutionError"
      error_message = parse_test_output_for_error(e)
    rescue => e
      error = "UnknownError"
      error_message = e.message
    ensure
      BrowserStack.logger.error("Failed to run #{test} for session due to error - #{error}: #{error_message}.") if error
      time_taken = Time.now.to_i - start_time
    end
    {
      "result" => result,
      "time_taken" => time_taken,
      "error" => error,
      "error_message" => error_message
    }
  end
end
