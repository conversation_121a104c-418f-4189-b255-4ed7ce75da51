require_relative '../../lib/configuration'
require_relative '../../lib/utils/http_utils'

module Secure
  class AppleCard
    attr_reader :provider, :number, :date, :year, :cvv, :status
    attr_accessor :stability_is_reported

    def initialize(provider, number, date, year, cvv)
      @provider = provider
      @number = number
      @date = date
      @year = year
      @cvv = cvv
      @stability_is_reported = false
      @status = nil
    end

    def mark_success
      @stability_is_reported = true
      @status = true
    end

    def mark_fail
      @stability_is_reported = true
      @status = false
    end
  end
end