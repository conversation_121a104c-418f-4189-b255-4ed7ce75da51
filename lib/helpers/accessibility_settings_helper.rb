require_relative '../models/device_state'
require_relative '../models/ios_device'
require_relative '../configuration'
require_relative '../../server/device_manager'
require_relative '../utils/zombie'
require_relative './data_report_helper'
require_relative './wda_client'
require_relative '../utils/utils'
require_relative '../utils/pymobiledevice'
require_relative '../utils/ios_mdm_service_client'

module Secure
  # This class handles Accessibility Settings Operations.
  class AccessibilitySettingsHelper # rubocop:todo Metrics/ClassLength
    TAG = "[ACCESSIBILITY_SETTINGS]"
    ENABLE = "enable"
    DISABLE = "disable"
    #Keys to Change Values of Accessibility Settings through idevice
    #Abstraction between product and the actual idevice keys is maintained in the hash
    #which will also help further in instrumentation and populating stats for BQ.
    ACCESSIBILITY_SETTING_KEYS = {
      reduce_motion: "ReduceMotionEnabled",
      speaking_rate: "VoiceOverTouchSpeakingRate",
      increase_contrast: "DarkenSystemColors"
    }

    DEFAULT_STATE = {
      motion: {
        reduce_motion: "disable"
      },
      voice_over: {
        speaking_rate: 50,
        captions_panel: "disable",
        navigation_style: "flat"
      },
      display_and_textsize: {
        increase_contrast: "disable",
        larger_accessibility_sizes: "disable",
        text_size: 3
      }
    }

    def initialize(device_id, session_id, product = "")
      @start_time = @end_time = Time.now.to_i
      @device_state = DeviceState.new(device_id)
      @uuid = device_id
      @product = product
      @session_id = session_id
      @ios_device = BrowserStack::IosDevice.new(device_id, self.class.to_s, BrowserStack.logger)
      @device_config = DeviceManager.device_configuration_check(@uuid)
      @os_version = @device_config["device_version"].to_f
      @wda_client = WdaClient.new(@device_config['webdriver_port'])
      BrowserStack::Zombie.configure
    end

    #This method contains the delegation to other methods on the basis of settings updated
    #Also updates the current state of settings
    def update_accessibility_settings(setting_items) # rubocop:todo Metrics/MethodLength, Metrics/AbcSize
      accessibility_settings_state = load_current_state
      prev_state = Marshal.load(Marshal.dump(accessibility_settings_state))
      log("info", "Updating #{setting_items} for session: #{@session_id}")
      setting_items = JSON.parse(setting_items)
      settings_to_update = validate_settings_to_update(setting_items, accessibility_settings_state)
      setting_items.each do |key, value|
        case key
        when "reduce_motion"
          if toggle_accessibility_setting(ACCESSIBILITY_SETTING_KEYS[key.to_sym], value)
            accessibility_settings_state[:motion][:reduce_motion_enabled] = value == ENABLE #Updating the state
          end
        when "speaking_rate"
          value = value.to_i # Speaking Rate min is 0 and max is 1
          if update_accessibility_setting(ACCESSIBILITY_SETTING_KEYS[key.to_sym], value / 100.0)
            accessibility_settings_state[:voice_over][:speaking_rate] = value #Updating the state
          end
        when "increase_contrast"
          if toggle_accessibility_setting(ACCESSIBILITY_SETTING_KEYS[key.to_sym], value)
            accessibility_settings_state[:display_and_textsize][:increase_contrast_enabled] = value == ENABLE #Updating the state
          end
        when "larger_accessibility_sizes", "text_size"
          payload = { larger_accessibility_sizes: setting_items["larger_accessibility_sizes"], text_size: setting_items["text_size"].to_i }
          setting_items.delete("larger_accessibility_sizes") # Delete the keys so that UI Automation will not be triggered again
          setting_items.delete("text_size")
          automation_state = check_and_run_automation("accessibility_setting_display_and_textsize", accessibility_settings_state, payload)
          accessibility_settings_state[:display_and_textsize][:larger_accessibility_sizes_enabled] = automation_state[:larger_accessibility_sizes_enabled]
          accessibility_settings_state[:display_and_textsize][:text_size] = automation_state[:text_size].to_i
        when "captions_panel", "navigation_style"
          payload = { captions_panel: setting_items["captions_panel"] }
          payload[:navigation_style] = setting_items["navigation_style"] if @os_version >= 15

          setting_items.delete("captions_panel") # Delete the keys so that UI Automation will not be triggered again
          setting_items.delete("navigation_style")
          automation_state = check_and_run_automation("accessibility_setting_voice_over", accessibility_settings_state, payload)
          accessibility_settings_state[:voice_over][:captions_panel_enabled] = automation_state[:captions_panel_enabled]
          accessibility_settings_state[:voice_over][:navigation_style] = automation_state[:navigation_style] if @os_version >= 15
        end
      end
      log("info", "Saving current state for session: #{@session_id} with #{setting_items}")
      save_current_state(accessibility_settings_state)
      send_usage_to_eds(settings_to_update, prev_state, accessibility_settings_state) unless @product == "cleanup"
      pusher_msg = pusher_message(settings_to_update, prev_state, accessibility_settings_state) unless @product == "cleanup"
      accessibility_settings_state[:pusher_message] = pusher_msg unless @product == "cleanup"
      accessibility_settings_state
    rescue => e
      log("error", "Failed to update accessibility settings #{settings_to_update} due to #{e.message}. Trace: #{e.backtrace}")
      raise e
    end

    def load_current_state
      if @device_state.accessibility_settings_file_present?
        JSON.parse(@device_state.read_accessibility_settings_file, symbolize_names: true)
      else
        #Generate Default States of Settings
        generate_accessibility_states(reduce_motion_enabled: false, speaking_rate: 50, increase_contrast_enabled: false)
      end
    end

    def reset_text_size_and_larger_accessibility_sizes
      return unless @device_state.accessibility_settings_file_present?

      current_state = load_current_state
      log("info", "current_state: #{current_state}")
      default_state_text_size = DEFAULT_STATE.clone
      log("info", "default_state_text_size: #{default_state_text_size}")
      settings_to_update = validate_settings_to_update(JSON.parse(default_state_text_size[:display_and_textsize].to_json), current_state)
      log("info", "settings_to_update #{settings_to_update}")
      if settings_to_update.include?("larger_accessibility_sizes") || settings_to_update.include?("text_size")
        if @os_version >= 17
          change_font_size_programatically(false, default_state_text_size[:display_and_textsize][:text_size])
        else
          BrowserStackAppHelper.run_ui_test(@uuid, :set_default_font_size, 120)
        end

        @device_state.touch_set_default_font_size_file
        current_state[:display_and_textsize][:larger_accessibility_sizes_enabled] = false
        current_state[:display_and_textsize][:text_size] = 3
        log("info", "updated current state: #{current_state}")
        save_current_state(current_state)
      end

    end

    #Cleanup. To update the setting values to their defaults
    def reset_accessibility_settings
      default_state = DEFAULT_STATE.clone
      default_state.each_key do |key|
        next if @device_state.dedicated_cleanup_file_present? && key == :voice_over

        update_accessibility_settings(default_state[key.to_sym].to_json)
      end
    ensure
      @device_state.remove_accessibility_settings_file
    end

    def change_font_size_programatically(larger_accessibility_sizes, text_size)
      start_time = Time.now.to_i
      result = "success"
      log("info", "Changing font size programatically to larger_accessibility_sizes: #{larger_accessibility_sizes}, size: #{text_size}")

      pymobiledevice3_start_time = Time.now.to_i
      changed_via_pymobiledevice3, pymobiledevice3_error = change_font_size_via_pymobiledevice3(text_size)
      pymobiledevice3_time_taken = Time.now.to_i - pymobiledevice3_start_time

      unless changed_via_pymobiledevice3
        mdm_start_time = Time.now.to_i
        changed_via_mdm, mdm_error = change_font_size_via_mdm(text_size)
        mdm_time_taken = Time.now.to_i - mdm_start_time
      end

      raise "Failed to change font size programatically on device: #{@uuid}" unless changed_via_pymobiledevice3 || changed_via_mdm

      log("info", "Changed font size programatically to larger_accessibility_sizes: #{larger_accessibility_sizes}, size: #{text_size}")
    rescue => e
      result = "failed"
      error = e.message
      log("error", "Failed to change font size programatically, to larger_accessibility_sizes: #{larger_accessibility_sizes}, size: #{text_size}, error: #{e.message}, #{e.backtrace.join("\n")}")
      raise e
    ensure
      font_size_programatically_data_report_helper.report({
        "result" => result,
        "time_taken" => Time.now.to_i - start_time,
        "changed_via_pymobiledevice3" => changed_via_pymobiledevice3,
        "changed_via_mdm" => changed_via_mdm,
        "pymobiledevice3_error" => pymobiledevice3_error,
        "mdm_error" => mdm_error,
        "pymobiledevice3_time_taken" => pymobiledevice3_time_taken,
        "mdm_time_taken" => mdm_time_taken,
        "error" => error
      })
    end

    def self.default_font_size
      DEFAULT_STATE[:display_and_textsize][:text_size]
    end

    private

    def accessibility_setting_enabled?(setting)
      log("info", "Checking whether #{ACCESSIBILITY_SETTING_KEYS[setting]} is enabled for session: #{@session_id}")
      @ios_device.accessibility_settings.enabled?(ACCESSIBILITY_SETTING_KEYS[setting])
    end

    #To toggle a single accessibility setting among the Keys mentioned
    def toggle_accessibility_setting(setting, toggle)
      case toggle
      when ENABLE
        @ios_device.accessibility_settings.enable(setting)
      when DISABLE
        @ios_device.accessibility_settings.disable(setting)
      end
      log("info", "Successfully #{toggle}d #{setting} for session: #{@session_id}")
      true
    rescue => e
      log("info", "Failed to #{toggle} #{setting} for session: #{@session_id} with message #{e.message} and trace #{e.backtrace}")
      false
    end

    #To update a single accessibility setting among the Keys mentioned
    def update_accessibility_setting(setting, value)
      @ios_device.accessibility_settings.update(setting, value)
      log("info", "Successfully updated #{setting} to #{value} for session: #{@session_id}")
      true
    rescue => e
      log("info", "Failed to update #{setting} to #{value} for session: #{@session_id} with message #{e.message} and trace #{e.backtrace}")
      false
    end

    def save_current_state(accessibility_settings_state)
      @device_state.touch_accessibility_settings_file
      @device_state.write_to_accessibility_settings_file(accessibility_settings_state.to_json)
    end

    def generate_accessibility_states(reduce_motion_enabled: false,
                                      speaking_rate: 50,
                                      increase_contrast_enabled: false,
                                      captions_panel_enabled: false,
                                      navigation_style: "flat",
                                      larger_accessibility_sizes_enabled: false,
                                      text_size: 3)
      #Default States of the setting items for the category they belong.
      {
        motion: {
          reduce_motion_enabled: reduce_motion_enabled
        },
        voice_over: {
          speaking_rate: speaking_rate,
          captions_panel_enabled: captions_panel_enabled,
          navigation_style: @os_version < 15 ? 'disabled' : navigation_style
        },
        display_and_textsize: {
          increase_contrast_enabled: increase_contrast_enabled,
          larger_accessibility_sizes_enabled: larger_accessibility_sizes_enabled,
          text_size: text_size
        }
      }
    end

    #Get the list of settings which are supposed to be updated. Used for populating feature usage
    def validate_settings_to_update(settings, state)
      requested_states = {
        enable: true,
        disable: false
      }
      settings_to_update = []
      settings.each do |key, value|
        case key
        when "reduce_motion"
          settings_to_update << key unless state[:motion][:reduce_motion_enabled] == requested_states[value.to_sym]
        when "speaking_rate"
          value = value.to_i
          settings_to_update << key unless state[:voice_over][:speaking_rate] == value
        when "increase_contrast"
          settings_to_update << key unless state[:display_and_textsize][:increase_contrast_enabled] == requested_states[value.to_sym]
        when "larger_accessibility_sizes"
          settings_to_update << key unless state[:display_and_textsize][:larger_accessibility_sizes_enabled] == requested_states[value.to_sym]
        when "text_size"
          value = value.to_i
          settings_to_update << key unless state[:display_and_textsize][:text_size] == value
        when "captions_panel"
          settings_to_update << key unless state[:voice_over][:captions_panel_enabled] == requested_states[value.to_sym]
        when "navigation_style"
          settings_to_update << key unless state[:voice_over][:navigation_style] == value
        end
      end
      settings_to_update
    end

    def check_and_run_automation(setting, state, payload) # rubocop:todo Metrics/AbcSize
      @device_state.touch_settings_automation_executing_file
      case setting
      when "accessibility_setting_display_and_textsize"
        #Return if the requested state is same as the current state
        current_state = { larger_accessibility_sizes_enabled: state[:display_and_textsize][:larger_accessibility_sizes_enabled], text_size: state[:display_and_textsize][:text_size] }
        log("info", "Current Display Settings State #{current_state} and complete state #{state}")
        return current_state if state[:display_and_textsize][:larger_accessibility_sizes_enabled] == (payload[:larger_accessibility_sizes] == ENABLE) && state[:display_and_textsize][:text_size] == payload[:text_size]

        if @os_version >= 17
          change_font_size_programatically(payload[:larger_accessibility_sizes] == ENABLE, payload[:text_size])

          current_state[:larger_accessibility_sizes_enabled] = payload[:larger_accessibility_sizes] == ENABLE
          current_state[:text_size] = payload[:text_size]
        else
          response = @wda_client.set_display_settings(payload)
          raise WdaAutomationError.new("unknown error"), "No payload in response" unless response['value']

          log("info", "Display Settings WDA response #{response}")
          current_state[:larger_accessibility_sizes_enabled] = payload[:larger_accessibility_sizes] == ENABLE if response["value"]["success"].to_s == "true"
          current_state[:text_size] = payload[:text_size] if response["value"]["success"].to_s == "true"
          log("info", "Current Display Settings State after WDA response #{current_state} and complete state #{state}")
        end

        current_state
      when "accessibility_setting_voice_over"
        #Return if the requested state is same as the current state
        current_state = { captions_panel_enabled: state[:voice_over][:captions_panel_enabled] }
        return current_state if @os_version < 15 && state[:voice_over][:captions_panel_enabled] == payload[:captions_panel]

        current_state[:navigation_style] = state[:voice_over][:navigation_style] if @os_version >= 15
        return current_state if state[:voice_over][:captions_panel_enabled] == (payload[:captions_panel] == ENABLE) && state[:voice_over][:navigation_style] == payload[:navigation_style]

        response = @wda_client.set_voice_over_settings(payload)
        raise WdaAutomationError.new("unknown error"), "No payload in response" unless response['value']

        current_state[:captions_panel_enabled] = payload[:captions_panel] == ENABLE if response["value"]["captions_panel"]["success"]
        current_state[:navigation_style] = payload[:navigation_style] if @os_version >= 15 && response["value"]["navigation_style"]["success"]
        current_state
      end
    rescue WdaClientError => e
      log("error", "Failed to automate #{payload} Accessibility Settings through WDA due to #{e.message}. Trace #{e.backtrace}")
      push_to_zombie("iOSSettingsUpdateFailed", "WdaClientError", e.message, payload)
      current_state
    rescue => e
      log("error", "Failed to automate #{payload} Accessibility Settings through WDA due to #{e.message}. Trace #{e.backtrace}")
      push_to_zombie("iOSSettingsUpdateFailed", "AutomationError", e.message, payload)
      current_state
    ensure
      @device_state.remove_settings_automation_executing_file
    end

    def change_font_size_programatically_file
      "/tmp/change_font_size_programatically_file_#{@uuid}.json"
    end

    def change_font_size_via_pymobiledevice3(text_size)
      log("info", "Changing font size via pymobiledevice3 to size: #{text_size}")
      kill_existing_change_font_size_programatically

      adjusted_text_size = text_size / 11.0
      if @product == "cleanup"
        PyMobileDevice::Developer::Accessibility.reset_accessibility_settings(@uuid)
      else
        pid = PyMobileDevice::Developer::Accessibility.set_setting(@uuid, "DYNAMIC_TYPE", adjusted_text_size)
        File.write(change_font_size_programatically_file, { pid: pid }.to_json)
      end

      count = 0
      changed = false
      while count < 3
        current_settings = PyMobileDevice::Developer::Accessibility.show_settings(@uuid)
        if current_settings["DYNAMIC_TYPE"].to_f.round(2) == adjusted_text_size.round(2)
          changed = true
          break
        end
        sleep 1
        count += 1
      end

      raise "Failed to change font size via pymobiledevice3 on device: #{@uuid}" unless changed

      log("info", "Changed font size via pymobiledevice3 to size: #{text_size}")
      [true, nil]
    rescue => e
      log("error", "Failed changing font size via pymobiledevice3 to size: #{text_size}, error: #{e.message}, #{e.backtrace.join("\n")}")
      kill_existing_change_font_size_programatically
      [false, e.message]
    end

    def change_font_size_via_mdm(text_size)
      log("info", "Changing font size via MDM to size: #{text_size}")
      BrowserStack::IosMdmServiceClient.configure
      response = BrowserStack::IosMdmServiceClient.set_text_size(@uuid, text_size)

      log("info", "Changed font size via MDM to size: #{text_size}")
      [true, nil]
    rescue => e
      log("error", "Failed to change text size via MDM to size: #{text_size}, error: #{e.message}, #{e.backtrace.join("\n")}")
      [false, e.message]
    end

    def kill_existing_change_font_size_programatically
      return unless File.exists?(change_font_size_programatically_file)

      font_size_programatically_file = JSON.parse(File.read(change_font_size_programatically_file))
      pid = font_size_programatically_file["pid"]
      Process.kill('KILL', pid.to_i) if !pid.nil? && Utils.process_running?(pid)
      File.delete(change_font_size_programatically_file)
    end

    def send_usage_to_eds(settings_to_update, prev_state, state)
      iOSSettingsUpdate = {
        session_id: @session_id,
        settings_updated: [],
        values_updated: [],
        values_old: []
      }
      iOSSettingsUpdateFailed = {
        session_id: @session_id,
        settings_update_failed: [],
        values_update_failed: [],
        values_existing: []
      }
      settings_to_update.each do |setting|
        parent = parent_setting(setting)
        #For Toggles, _enabled is appended in the state.
        new_state = state[parent][setting.to_sym] || state[parent]["#{setting}_enabled".to_sym]
        old_state = prev_state[parent][setting.to_sym] || prev_state[parent]["#{setting}_enabled".to_sym]
        if new_state != old_state # Setting updated
          iOSSettingsUpdate[:settings_updated] << setting
          iOSSettingsUpdate[:values_updated] << new_state
          iOSSettingsUpdate[:values_old] << old_state
        else
          iOSSettingsUpdateFailed[:settings_update_failed] << setting
          iOSSettingsUpdateFailed[:values_update_failed] << new_state
          iOSSettingsUpdateFailed[:values_existing] << old_state
        end
      end
      success_data_reporter = DataReportHelper.new('iOSSettingsUpdate', session_id: @session_id, product: @product, ios_version: @device_config["device_version"], device: @uuid)
      success_data_reporter.report(iOSSettingsUpdate) unless iOSSettingsUpdate[:settings_updated].empty?
      failure_data_reporter = DataReportHelper.new('iOSSettingsUpdateFailed', session_id: @session_id, product: @product, ios_version: @device_config["device_version"], device: @uuid)
      failure_data_reporter.report(iOSSettingsUpdateFailed) unless iOSSettingsUpdateFailed[:settings_update_failed].empty?
    end

    def push_to_zombie(kind, error, error_message, payload)
      BrowserStack::Zombie.push_logs( kind,
                                      error,
                                      { "device" => @uuid,
                                        "product" => @product,
                                        "session_id" => @session_id,
                                        "os_version" => @ios_version,
                                        "data" => {
                                          "error_message" => error_message,
                                          "payload" => payload
                                        } })
    end

    def pusher_message(settings_to_update, prev_state, state)
      settings_updated = []
      settings_failed = []
      parent = ""
      settings_to_update.each do |setting|
        parent = parent_setting(setting)
        #For Toggles, _enabled is appended in the state.
        new_state = state[parent][setting.to_sym] || state[parent]["#{setting}_enabled".to_sym]
        old_state = prev_state[parent][setting.to_sym] || prev_state[parent]["#{setting}_enabled".to_sym]
        if new_state != old_state # Setting updated
          settings_updated << setting
        else
          settings_failed << setting
        end
      end
      settings_updated.sort!
      settings_failed.sort!
      parent = parent.to_s.capitalize
      return "#{parent}-Successful" if settings_failed.empty? && !settings_updated.empty?
      return "#{parent}-Failed" if !settings_failed.empty? && settings_updated.empty?

      "#{parent}-Successful:#{settings_updated.join('|')}-Failed:#{settings_failed.join('|')}"
    end

    #Utility method to get the parent category of a setting.
    def parent_setting(setting_item)
      case setting_item
      when "reduce_motion"
        :motion
      when "speaking_rate", "navigation_style", "captions_panel"
        :voice_over
      when "increase_contrast", "larger_accessibility_sizes", "text_size"
        :display_and_textsize
      end
    end

    def font_size_programatically_data_report_helper
      @font_size_programatically_data_report_helper ||= DataReportHelper.new('font-size-programatically',
                                                                             session_id: @session_id,
                                                                             product: @product,
                                                                             ios_version: @os_version,
                                                                             device: @uuid)
    end

    def log(level, message)
      params = { subcomponent: TAG, device: @uuid }
      BrowserStack.logger.send(level.to_sym, message, params)
    end

  end
end
