require 'browserstack_logger'
require 'fileutils'
require 'sequel'
require 'sqlite3'

require_relative '../utils/zombie'

class IosBackupSqliteHelper
  def initialize(udid)
    @udid = udid
  end

  def retrieve_messages
    db = execute_sms_db
    return "error" if db.nil?

    chat_message_join = db[:chat_message_join]
    chat = db[:chat]
    message = db[:message]
    message.join(:chat_message_join, message_id: :ROWID).join(:chat, ROWID: :chat_id).select(:chat_identifier, :text, :destination_caller_id ).all

  end

  private

  def execute_sms_db
    sms_db_path = "#{IOS_BACKUP_DIR}/#{@udid}/3d/3d0d7e5fb2ce288813306e4d4636395e047a3d28"
    db = nil
    db = Sequel.connect("sqlite://#{sms_db_path}") if File.exist?(sms_db_path)
    db
  ensure
    db&.disconnect
  end
end
