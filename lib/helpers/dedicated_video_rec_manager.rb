require 'browserstack_logger'
require_relative '../utils/zombie'
require_relative '../utils/osutils'
require_relative '../models/device_state'
require_relative '../custom_exceptions'
require_relative '../../config/constants'

class DedicatedVideoRecManager

  def initialize(device)
    @device = device
    @device_state = DeviceState.new(device)
  end

  def get_new_video_files(video_files, session_id)
    diff_files = []
    stored_video_files = []
    begin
      stored_video_files = @device_state.dedicated_video_state_file_to_array
      @device_state.write_array_to_dedicated_video_state_file(video_files.to_a)
      diff_files = video_files - stored_video_files
      BrowserStack.logger.info("[Dedicated] Existing Video Count: #{stored_video_files.size}; New Videos Count: #{diff_files.size}; New video files: #{diff_files}")
      BrowserStack::Zombie.push_logs("dedicated-device-events", nil, { "device" => @device, "data" => { "message" => 'Write Session Video File State', "component" => 'DEDICATED_CLEANUP', "existing_videos_count": stored_video_files.size, "new_videos_count": diff_files.size }, "session_id" => session_id })
      raise VideoFileFetchException, "[Dedicated] No video files found" if diff_files.empty?
    rescue => e
      BrowserStack.logger.error("[Dedicated] Error in get_new_video_files: #{e.message}")
      raise VideoFileFetchException, "[Dedicated] Error in get_new_video_files"
    end
    diff_files
  end

  def write_session_video_file(component, session_id=nil)
    return if @device_state.dedicated_video_state_file_present?

    begin
      retry_count ||= 0
      BrowserStack.logger.info("Attempting to unmount device #{@device}, retry_count: #{retry_count}") if retry_count != 0
      BrowserStack::OSUtils.execute("sudo umount -f /Users/<USER>/#{@device}", timeout: 10) if retry_count != 0
      BrowserStack::OSUtils.execute("sudo su -l #{USER} -c 'mkdir -p /Users/<USER>/#{@device}'", timeout: 10)
      BrowserStack::OSUtils.execute("#{IFUSE} -o allow_other,umask=0000,default_permissions -u #{@device} /Users/<USER>/#{@device}", timeout: 10)
      device_video_files = Dir.glob("/Users/<USER>/#{@device}/DCIM/*/*.MP4")
      @device_state.write_array_to_dedicated_video_state_file(device_video_files.to_a)
      BrowserStack::Zombie.push_logs("dedicated-device-events", nil, { "device" => @device, "data" => { "message" => 'Write Session Video File State', "component" => component, "new_videos_count": device_video_files.size }, "session_id" => session_id })
      BrowserStack.logger.info("Success : Video files fetched for #{@device}; Video Count: #{device_video_files.size}")
    rescue OSUtilsError => e
      BrowserStack.logger.error("Failed : Timeout, #{e.message} #{e.backtrace} Retry #{retry_count}")
      retry_count += 1
      retry if retry_count < 2
      error = e.message
      BrowserStack::Zombie.push_logs("dedicated-device-events", e.message, { "device" => @device, "data" => { "message" => 'Write Session Video File State', "component" => component }, "session_id" => session_id })
    rescue => e
      error = e.message
      BrowserStack.logger.error("Failed : Error, #{e.message} #{e.backtrace}")
      BrowserStack::Zombie.push_logs("dedicated-device-events", e.message, { "device" => @device, "data" => { "message" => 'Write Session Video File State', "component" => component }, "session_id" => session_id })
    ensure
      BrowserStack::OSUtils.execute("sudo umount -f /Users/<USER>/#{@device}", timeout: 10)
    end
  end
end
