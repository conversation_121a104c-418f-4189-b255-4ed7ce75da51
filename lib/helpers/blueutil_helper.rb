require_relative '../../config/constants'

module BrowserStack
  class BlueutilHelper

    BLUEUTIL_BINARY_PATH = "#{BS_DIR}/deps/blueutil/bin/blueutil".freeze

    # Enable the mini's bluetooth using blueutil binary
    def enable_bluetooth
      cmd = "/usr/local/bin/gtimeout -s KILL 25 #{BLUEUTIL_BINARY_PATH} -p 1 -d 1"
      t1 = Time.now.to_f
      BrowserStack::OSUtils.execute(cmd)
      t2 = Time.now.to_f
      BrowserStack.logger.info("enable_bluetooth step took #{t2 - t1} seconds")
    end

    # Disable the mini's bluetooth using blueutil binary
    def disable_bluetooth
      cmd = "BLUEUTIL_ALLOW_ROOT=1 /usr/local/bin/gtimeout -s KILL 5 #{BLUEUTIL_BINARY_PATH} -p 0 -d 0"
      t1 = Time.now.to_f
      BrowserStack::OSUtils.execute(cmd)
      t2 = Time.now.to_f
      BrowserStack.logger.info("disable_bluetooth step took #{t2 - t1} seconds")
    end

    # Returns true if given device address is not connected to the mini via bluetooth
    def device_address_disconnected?(address)
      cmd = "#{BLUEUTIL_BINARY_PATH} --connected | grep \"#{address}\" | wc -l"
      connected_count = BrowserStack::OSUtils.execute(cmd).strip
      connected_count.to_i == 0
    end
  end
end

if __FILE__ == $PROGRAM_NAME
  case ARGV[0]
  when 'enable_bluetooth'
    blueutil_helper = BrowserStack::BlueutilHelper.new
    blueutil_helper.enable_bluetooth
  when 'disable_bluetooth'
    blueutil_helper = BrowserStack::BlueutilHelper.new
    blueutil_helper.disable_bluetooth
  else
    BrowserStack.logger.error "Method #{ARGV[0]}, is not listed."
  end
end
