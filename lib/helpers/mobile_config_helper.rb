require 'json'
require_relative '../configuration'
require_relative '../utils/zombie'
require_relative '../custom_exceptions'
require_relative '../utils/ios_mdm_service_client'
require_relative '../models/device_state'
require_relative '../utils/plist_buddy'
require_relative '../../server/device_manager'

# Does not seem to be used anywhere hence not modified for CFGUTIL migration
module BrowserStack
  class MobileConfigHelper # rubocop:todo Metrics/ClassLength

    @@config = Configuration.new.all

    def initialize(uuid, device_config)
      @uuid = uuid
      @session_file = "#{@@config['state_files_dir']}/#{uuid}_session"
      @device_state = DeviceState.new(uuid)
      @device_config = device_config
      @eds_event_type = "web_events"
      @eds_event_name_install = "InstallMobileConfig"
      @eds_event_name_remove = "RemoveMobileConfig"
      Zombie.configure
      IosMdmServiceClient.configure
    end

    def session_params
      begin
        @session_params ||= Utils.read_json_file(@session_file)
      rescue Errno::ENOENT => e
        BrowserStack.logger.info("No session file found, Product specific tasks won't run! #{e.message}")
        @session_params ||= {}
      end
      @session_params
    end

    def session_id
      @session_id ||= (session_params["live_session_id"] || session_params["automate_session_id"] || session_params["app_live_session_id"] || session_params["session_id"]).to_s
    end

    def device_version
      @device_config["device_version"]
    end

    def install_failure_zombie_handler(error, product, time_taken)
      if error.message.include?("PayloadContent") && error.message.include?("invalid")
        BrowserStack::Zombie.push_logs("ssl-user-failure", error.message, { "device" => @uuid, "product" => product, "session_id" => session_id, "data" => { "time_taken" => time_taken, "os_version" => device_version } })
      else
        BrowserStack::Zombie.push_logs("ssl-install-failure", error.message, { "device" => @uuid, "product" => product, "session_id" => session_id, "data" => { "time_taken" => time_taken, "os_version" => device_version } })
      end
    end

    def preprocess_mobile_config_data(path_to_config_file)
      @device_state.touch_mobile_config_installed_file unless @device_state.mobile_config_installed_file_present?

      mobile_config_data_raw = @device_state.read_mobile_config_installed_file
      mobile_config_data = mobile_config_data_raw == '' ? {} : JSON.parse(mobile_config_data_raw)
      payload_id = @@config["mobile_config_ssl_prefix"] + (Digest::MD5.hexdigest String rand)
      mobile_config_data[payload_id] = 0
      PlistBuddy.set_key_in_plist(path_to_config_file, @@config["mobile_config_ID_name"], payload_id)
      BrowserStack.logger.info("Changes the Payload ID of #{path_to_config_file} to #{payload_id}")
      mobile_config_data
    end

    def handle_remove_failure(mobile_config_data, identifier, time_start, event_json)
      BrowserStack.logger.info("Failed to remove certificate with ID : #{identifier}. Saving it back into state file for retry in next cleanup.")
      eds_data = { "event_name" => @eds_event_name_remove, "team" => "device_features", "product" => "device_features" }
      mobile_config_data[identifier] = mobile_config_data[identifier].to_i + 1
      if mobile_config_data[identifier] > @@config['max_retries_ssl_cert']
        mobile_config_data.delete(identifier)
        time_taken = Time.now.to_i - time_start
        event_json.merge!({ "time_taken" => time_taken, "success" => "false" })
        Utils.send_to_eds(eds_data.merge!({ "event_json" => event_json }), @eds_event_type, true)
        BrowserStack::Zombie.push_logs("ssl-remove-failure", "Retries Exceeded", { "device" => @uuid, "session_id" => session_id, "data" => { "certificate_id" => identifier, "time_taken" => time_taken, "os_version" => device_version } })
      else
        time_taken = Time.now.to_i - time_start
        event_json.merge!({ "time_taken" => time_taken, "success" => "false", "retry_attempt" => mobile_config_data[identifier] })
        Utils.send_to_eds(event_json, @eds_event_type, true)
        BrowserStack::Zombie.push_logs("ssl-remove-failure", "Removal Failure", { "device" => @uuid, "session_id" => session_id, "data" => { "certificate_id" => identifier, "time_taken" => time_taken, "os_version" => device_version, "retry_count" => mobile_config_data[identifier] } })
      end
    end

    def check_and_remove_mobile_config_payload(identifier, mobile_config_data)
      begin
        time_start = Time.now.to_i
        eds_data = { "event_name" => @eds_event_name_remove, "team" => "device_features", "product" => "device_features" }
        event_json = { "device" => @uuid, "session_id" => session_id, "identifier" => identifier, "os_version" => device_version }
        BrowserStack.logger.info("Removing certificate IDs: #{identifier}")
        IosMdmServiceClient.send(:make_request, { "request_type" => "RemoveProfile", "udid" => @uuid, "Identifier" => identifier }, REDIS_CLIENT)
        mobile_config_data.delete(identifier)
        event_json.merge!({ "time_taken" => Time.now.to_i - time_start, "success" => "true" })
        Utils.send_to_eds(eds_data.merge!({ "event_json" => event_json }), @eds_event_type, true)
      rescue => e
        handle_remove_failure(mobile_config_data, identifier, time_start, event_json)
      end
      mobile_config_data
    end

    def remove_mobile_configs
      mobile_config_data = JSON.parse(@device_state.read_mobile_config_installed_file)
      payload_ids = mobile_config_data.keys
      BrowserStack.logger.info("Removing certificates with IDs: #{payload_ids}")
      payload_ids.each do |identifier|
        mobile_config_data = check_and_remove_mobile_config_payload(identifier, mobile_config_data)
      end
      if mobile_config_data.keys.empty?
        BrowserStack.logger.info("Removed certificates for device : #{@uuid}. Certificate clenup done completely.")
        @device_state.remove_mobile_config_installed_file
        BrowserStack.logger.info("State file for mobile configs installed delete successfully.")
      else
        BrowserStack.logger.info("Removed certificates for device : #{@uuid}. Certificate clenup done partially. Will retry for cleanup in the next time.")
        @device_state.write_to_mobile_config_installed_file(mobile_config_data.to_json)
      end
    end

    def install_mobile_config_profile(path_to_config_file, product)

      BrowserStack.logger.info("Starting install SSL certificate for device: #{@uuid}")
      time_start = Time.now.to_i
      eds_data = { "event_name" => @eds_event_name_install, "team" => "device_features", "product" => "device_features" }
      event_json = { "device" => @uuid, "session_id" => session_id, "os_version" => device_version }

      mobile_config_data = preprocess_mobile_config_data(path_to_config_file)
      base64_encoded_mobile_config_payload = Base64.encode64(File.read(path_to_config_file))

      IosMdmServiceClient.send(:make_request, { "request_type" => "InstallProfile", "udid" => @uuid, "payload" => base64_encoded_mobile_config_payload }, REDIS_CLIENT)
      BrowserStack.logger.info("SSL certificate installed succesfully on the device : #{@uuid}")
      @device_state.write_to_mobile_config_installed_file(mobile_config_data.to_json)

      event_json.merge!({ "time_taken" => Time.now.to_i - time_start, "success" => "true" })
      eds_data.merge!({ "event_json" => event_json })
      Utils.send_to_eds(eds_data, @eds_event_type, true)
    rescue => e
      BrowserStack.logger.error("Error while installing mobileconfig through MDM : #{e.message}")
      time_taken = Time.now.to_i - time_start
      event_json.merge!({ "time_taken" => time_taken, "success" => "false", "error" => e.message })
      eds_data.merge!({ "event_json" => event_json })
      Utils.send_to_eds(eds_data, @eds_event_type, true)
      install_failure_zombie_handler(e, product, time_taken)
      raise SSLCertificateNotInstalledException

    end
  end
end
