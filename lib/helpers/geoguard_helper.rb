require_relative '../configuration'
require_relative '../models/device_state'
require_relative './data_report_helper'
require_relative './apple_business_manager_helper'
require_relative '../utils/utils'
require_relative './browserstack_app_helper'
require_relative './wda_client'
require 'json'

class GeoguardHelper #rubocop:todo Metrics/ClassLength
  def initialize(device_id, session_id, logger)
    @device_id = device_id
    @session_id = session_id
    @logger = logger
  end

  def geoguard_enabled?
    is_geoguard_enabled = false
    log(:info, "Is Geoguard Enabled: #{is_geoguard_enabled}")
    is_geoguard_enabled
  end

  def setup
    log(:info, "Started setting up Geoguard")

    start_time = Time.now.to_i

    geoguard_app_installed = IdeviceUtils.check_app_with_bundle_id_exists(@device_id, GEOGUARD_BUNDLE_ID)

    allow_geoguard_notifications

    if geoguard_app_installed
      wda_client.launch_app_with_bundle_id(GEOGUARD_BUNDLE_ID)
    else
      BrowserStackAppHelper.check_and_install_browserstack_test_suite(@device_id)
      install_geoguard_app
      allow_geoguard_location_permissions
    end

    device_state.touch_geoguard_cleanup_file
    result = "success"
    log(:info, "Successfully setup Geoguard")
    true
  rescue => e
    log(:error, "Failed to setup Geoguard, error: #{e.message}, #{e.backtrace.join("\n")}")
    device_state.touch_geoguard_failed_file
    error = e.message
    result = "failed"
    raise e
  ensure
    time_taken = Time.now.to_i - start_time
    data_reporter.report({
      "action" => "setup",
      "result" => result,
      "time_taken" => time_taken,
      "install_via" => "mdm",
      "app_already_installed" => geoguard_app_installed,
      "error" => error
    })
  end

  def cleanup
    log(:info, "Started GeoGuard cleanup")

    start_time = Time.now.to_i
    geoguard_app_installed = IdeviceUtils.check_app_with_bundle_id_exists(@device_id, GEOGUARD_BUNDLE_ID)
    return unless geoguard_app_installed

    IdeviceUtils.uninstall_app(@device_id, GEOGUARD_BUNDLE_ID) if device_state.geoguard_failed_file_present?

    if device_state.geoguard_cleanup_file_present?
      disallow_geoguard_notifications
      wda_client.kill_apps([GEOGUARD_BUNDLE_ID])
    end

    result = "success"
    log(:info, "Completed GeoGuard Cleanup")
    true
  rescue => e
    log(:error, "Failed to cleanup GeoGuard, error: #{e.message}, #{e.backtrace.join("\n")}")
    error = e.message
    result = "failed"
    raise e
  ensure
    if error.nil?
      device_state.remove_geoguard_failed_file
      device_state.remove_geoguard_cleanup_file
    end
    time_taken = Time.now.to_i - start_time
    if geoguard_app_installed || !error.nil?
      data_reporter.report({
        "action" => "cleanup",
        "result" => result,
        "time_taken" => time_taken,
        "error" => error
      })
    end
  end

  private

  def install_geoguard_app
    raise "Failed to install Geoguard App" unless apple_business_manager.ensure_license_and_install_app(app_install_wait_seconds: 30)
  end

  def allow_geoguard_location_permissions
    BrowserStackAppHelper.run_ui_test(@device_id, :enable_geoguard)
  end

  def allow_geoguard_notifications
    device_state.remove_disable_geoguard_notifications_file
    Utils.change_notifications_profile_mdm([], @device_id, false)
  end

  def disallow_geoguard_notifications
    device_state.touch_disable_geoguard_notifications_file
    response_mdm = Utils.change_notifications_profile_mdm([], @device_id, true)
    raise "Failed to disallow Geoguard Notification" unless response_mdm["result"] == "success"

    true
  end

  def data_reporter
    @data_reporter ||= DataReportHelper.new("geo-session-stats", session_id: @session_id, device: @device_id)
  end

  def server_config
    @server_config ||= BrowserStack::Configuration.new.all
  end

  def device_config
    @device_config ||= JSON.parse(File.read(server_config['config_json_file']))["devices"][@device_id]
  end

  def device_state
    @device_state ||= DeviceState.new(@device_id)
  end

  def apple_business_manager
    @apple_business_manager ||= AppleBusinessManagerHelper.new(@device_id, "geoguard")
  end

  def wda_client
    @wda_client ||= WdaClient.new(device_config['webdriver_port'].to_i)
  end

  def log(level, message)
    @logger.send(level.to_sym, "[GeoGuard][#{@session_id}][#{@device_id}] #{message}")
  end
end
