require_relative "../../config/constants"
require_relative "../utils/utils"
require_relative "../../server/device_manager"
require "fileutils"

class PFCTLHelper # rubocop:todo Metrics/ClassLength
  @@rule = "\nblock return in on bridge100 proto tcp from %{device_ip} to any no state\n"\
            "block return out on bridge100 proto tcp from any to %{device_ip} no state\n"\
            "pass in on bridge100 from %{device_ip} to <app_live_whitelist> no state\n"\
            "pass out on bridge100 from <app_live_whitelist> to %{device_ip} no state\n"\
            "pass in on bridge100 proto tcp from %{device_ip} to %{terminal_ip} port 48080 <> 48179 no state\n"\
            "pass out on bridge100 proto tcp from %{terminal_ip} port 48080 <> 48179 to %{device_ip} no state\n"
  def initialize(ip, uuid, session_id, product = nil)
    @terminal_ip = ip
    @uuid = uuid
    @device_state = DeviceState.new(uuid)
    @product = product
    @session_id = session_id
  end

  def setup
    Dir.mkdir(PF_CONFIGURATION_DIR) unless Dir.exists?(PF_CONFIGURATION_DIR)
    FileUtils.touch(OFFLINE_MODE_DEVICES_CONF) unless File.exists?(OFFLINE_MODE_DEVICES_CONF)
  end

  def fetch_ip_address
    device_config = DeviceManager.device_configuration_check(@uuid)
    wda = WdaClient.new(device_config["webdriver_port"].to_i)
    response = wda.device_ip
    response["value"] || ""
  end

  def add_offline_mode_rules(device_ip)
    ##
    # Adds Rules to device_conf.json. Sample device_conf.json after a rule is added would be
    # {"00008030-001524C90CF3802E":"\nblock return in on bridge100 from ************ to any no state\nblock return out on bridge100 from any to ************ no state\n"}

    Utils.with_lock(OFFLINE_MODE_DEVICES_CONF) do
      rules_hash = begin
        Utils.read_json_file(OFFLINE_MODE_DEVICES_CONF)
      rescue => e
        BrowserStack.logger.error("[ios-offline-mode] unable to parse OFFLINE_MODE_DEVICES_CONF on #{@uuid} during enabling. Error: #{e.message}")
        {}
      end
      rules_hash[@uuid] = format(@@rule, device_ip: device_ip.to_s, terminal_ip: @terminal_ip)
      Utils.write_to_file(OFFLINE_MODE_DEVICES_CONF, rules_hash.to_json)
    end
  end

  def remove_offline_mode_rules
    # Removes the json entry for the device from device_conf.json file

    Utils.with_lock(OFFLINE_MODE_DEVICES_CONF) do
      rules_hash = begin
        Utils.read_json_file(OFFLINE_MODE_DEVICES_CONF)
      rescue => e
        BrowserStack.logger.error("[ios-offline-mode] unable to parse OFFLINE_MODE_DEVICES_CONF on #{@uuid} during disabling. Error: #{e.message}")
        {}
      end
      rules_hash.delete(@uuid)
      Utils.write_to_file(OFFLINE_MODE_DEVICES_CONF, rules_hash.to_json)
    end
  end

  def enable_offline_mode
    return if @device_state.offline_mode_file_present?

    setup
    ip = fetch_ip_address
    if !ip.empty?
      BrowserStack.logger.info("[ios-offline-mode] Enabling Offline Mode for #{@session_id} on #{@uuid}")
      kill_states(ip)
      add_offline_mode_rules(ip)
      reload_pf_conf
      @device_state.touch_offline_mode_file
    else
      raise OfflineModeFailureException, "WDA client returned no ip"
    end
  rescue => e
    BrowserStack.logger.error("[ios-offline-mode] Failed to enable offline mode for #{@session_id} on #{@uuid}. Error: #{e.message}")
    raise OfflineModeFailureException, "Failed to enable offline mode. Error: #{e.message}"
  end

  def disable_offline_mode
    return unless @device_state.offline_mode_file_present?

    BrowserStack.logger.info("[ios-offline-mode] Disabling Offline Mode for #{@session_id} on #{@uuid}")
    remove_offline_mode_rules
    reload_pf_conf
    @device_state.remove_offline_mode_file
  rescue => e
    BrowserStack.logger.error("[ios-offline-mode] Failed to disable offline mode for #{@session_id} on #{@uuid}. Error: #{e.message}")
    raise OfflineModeFailureException, "Failed to disable offline mode. Error: #{e.message}"
  end

  def validate_custom_conf
    # Validates Custom conf file and raises errors
    stdout, stderr, status = BrowserStack::OSUtils.execute2("sudo pfctl -q -nf #{CUSTOM_PF_CONF}")
    raise OfflineModeFailureException, "PF Conf Validation Failed. Error: #{stderr}" unless status.success?
  end

  def kill_states(ip)
    BrowserStack.logger.info("[ios-offline-mode] Killing States for #{ip} in #{@session_id} on #{@uuid}")
    stdout, stderr, status = BrowserStack::OSUtils.execute2("sudo pfctl -q -k #{ip} -k #{@terminal_ip}")
    raise OfflineModeFailureException, "Killing States for ip:#{ip} failed. Error: #{stderr}" unless status.success?
  end

  def reload_pf_conf
    # Copies /etc/pf.conf to custom_pf.conf, adds rules from device_conf.json and loads custom_pf.conf if validation is successful, else loads /etc/pf.conf

    rules_hash = {}
    Utils.with_lock(CUSTOM_PF_CONF) do
      rules_hash = begin
        Utils.read_json_file(OFFLINE_MODE_DEVICES_CONF)
      rescue => e
        BrowserStack.logger.error("[ios-offline-mode] unable to parse OFFLINE_MODE_DEVICES_CONF on #{@uuid} during reloading. Error: #{e.message}")
        {}
      end

      BrowserStack.logger.info("[ios-offline-mode] Offline Mode Rules Hash being appended is #{rules_hash}")
      FileUtils.cp(PF_ORIGINAL_CONF, CUSTOM_PF_CONF)
      custom_pf_conf_file = File.open(CUSTOM_PF_CONF, "a")
      custom_pf_conf_file.write("\ntable <app_live_whitelist> persist file \"#{CONFIG_PATH}/app_live_ios_offline_whitelist.table\"\n")
      rules_hash.each do |_uuid, rules|
        custom_pf_conf_file.write(rules)
      end
      custom_pf_conf_file.close
    end
    return restore_config if rules_hash.empty?

    validate_custom_conf
    BrowserStack.logger.info("[ios-offline-mode] PF conf validation Successful")
    BrowserStack::OSUtils.execute2("sudo pfctl -q -f #{CUSTOM_PF_CONF}")
  rescue OfflineModeFailureException => e
    # Restoring config if PF Conf validation fails and raises an error
    BrowserStack.logger.error("[ios-offline-mode] Reload PF config failed. Error: #{e.message}")
    remove_offline_mode_rules
    restore_config
    raise OfflineModeFailureException, e.message
  end

  def restore_config
    BrowserStack.logger.info("[ios-offline-mode] Restoring original pf config file")
    BrowserStack::OSUtils.execute2("sudo pfctl -q -f #{PF_ORIGINAL_CONF}")
  end

  def delete_state_file
    BrowserStack.logger.info("[ios-offline-mode] Removing Offline Mode state file for #{@uuid}")
    @device_state.remove_offline_mode_file
  end
end
