# rubocop:disable all
require 'browserstack_logger'
require 'json'
require 'websocket-client-simple'

require_relative '../../config/constants'
require_relative '../utils/utils'
require_relative "../utils/osutils"

# currently supports device browser chrome and safari
class SessionSavePolling

  @@target_ids = {}

  def initialize(session_id, device_id, debugger_port, genre, setting_cookies)
    @session_id = session_id
    @device_id = device_id
    @debugger_port = debugger_port
    @genre = genre
    @history = {}
    @setting_cookies = setting_cookies
    @ws = nil
    @current_target_id = @@target_ids[@device_id]
    @cookie_file_path = "/usr/local/.browserstack/state_files/#{@device_id}_cookie_data_from_s3.json"
    BrowserStack.logger.info("[SessionSavePolling] initialize #{@session_id}, #{@device_id}, #{@debugger_port}")
  end

  def setup_ws_connection
    begin
      max_attempts = 60
      attempts = 0
      page_tab = nil

      # Loop until we find a valid page tab or reach max attempts
      while page_tab.nil? && attempts < max_attempts
        attempts += 1
        BrowserStack.logger.info("[SessionSavePolling] fetching tabs attempt #{attempts}/#{max_attempts}")

        json_list = JSON.parse(OSUtils.execute("curl http://localhost:#{@debugger_port}/json/list"))
        BrowserStack.logger.info("[SessionSavePolling] fetching tabs: #{json_list.to_json}")

        if json_list.empty?
          BrowserStack.logger.info("[SessionSavePolling] empty tab list, retrying in 1 second")
          sleep(1)
          next
        end

        # For iOS, find a page with a non-empty URL that's not a ServiceWorker or JSContext
        page_tab = json_list.find do |tab| 
          !tab["url"].to_s.empty? && 
          !tab["title"].to_s.include?("ServiceWorker") && 
          !tab["title"].to_s.include?("JSContext") &&
          !tab["url"].to_s.include?("about://newtab/") 
        end

        if page_tab.nil?
          BrowserStack.logger.info("[SessionSavePolling] no valid page tab found, retrying in 1 second")
          sleep(1)
        end
      end

      # Return early if we couldn't find a valid page tab
      unless page_tab && page_tab["webSocketDebuggerUrl"]
        BrowserStack.logger.error("[SessionSavePolling] could not find a valid page tab after #{max_attempts} attempts")
        return
      end

      BrowserStack.logger.info("[SessionSavePolling] found valid page tab url: #{page_tab['url']}")
      BrowserStack.logger.info("[SessionSavePolling] found valid page tab webSocketDebuggerUrl: #{page_tab['webSocketDebuggerUrl']}")
      BrowserStack.logger.info("[SessionSavePolling] found valid page tab FE URL: #{page_tab['devtoolsFrontendUrl']}")

      page_id = page_tab["webSocketDebuggerUrl"].split("/").last
      ws_url = "http://localhost:#{@debugger_port}/devtools/page/#{page_id}"
      BrowserStack.logger.info("[SessionSavePolling] setting up WebSocket connection to #{ws_url}")

      connection_established = false
      connection_error = nil
      that = self

      WebSocket::Client::Simple.connect(ws_url) do |ws|
        @ws = ws

        ws.on :open do |_event|
          connection_established = true
          BrowserStack.logger.info("[SessionSavePolling] WS connection established for tab: #{page_tab["url"]}")
        end

        ws.on :error do |error|
          connection_error = error
          BrowserStack.logger.error("[SessionSavePolling] WS connection error: #{error.message}")
        end

        ws.on :close do |_event|
          BrowserStack.logger.info("[SessionSavePolling] WS connection closed")
        end

        ws.on :message do |msg|
          begin
            data = JSON.parse(msg.data)

            # Store target ID when it's created or changed
            if data['method'] == 'Target.targetCreated'
              that.instance_variable_set(:@current_target_id, data['params']['targetInfo']['targetId'])
              @@target_ids[that.instance_variable_get(:@device_id)] = that.instance_variable_get(:@current_target_id)
              BrowserStack.logger.info("[SessionSavePolling] new target created: #{that.instance_variable_get(:@current_target_id)}")
            elsif data['method'] == 'Target.targetInfoChanged'
              that.instance_variable_set(:@current_target_id, data['params']['targetInfo']['targetId'])
              @@target_ids[that.instance_variable_get(:@device_id)] = that.instance_variable_get(:@current_target_id)
              BrowserStack.logger.info("[SessionSavePolling] target info changed: #{that.instance_variable_get(:@current_target_id)}")
            end
          rescue JSON::ParserError => e
            BrowserStack.logger.error("[SessionSavePolling] error parsing message: #{e.message}")
          end
        end
      end

      # Wait for connection to be established
      timeout = Time.now + 5  # 5 second timeout
      until connection_established || connection_error
        sleep 0.1
        if Time.now > timeout
          raise "WebSocket connection timeout after 5 seconds"
        end
      end

      if connection_error
        raise "WebSocket connection failed: #{connection_error.message}"
      end

      raise "WebSocket connection not established" unless @ws

    rescue StandardError => e
      BrowserStack.logger.error("[SessionSavePolling] setup_ws_connection failed: #{e.message}")
      raise
    end
  end  

  def start
    # this is a safe check
    Timeout.timeout(MAX_TERMINAL_BLOCK_TIME) do
      Thread.bs_run do
        start_file = self.class.start_file(@device_id)
        FileUtils.touch(start_file)

        setup_ws_connection

        if @ws && @ws.open? && @setting_cookies
          set_cookies
        end

        while File.exist?(start_file)
          begin
            get_cookies
          rescue => e
            BrowserStack.logger.error("[SessionSavePolling] exception: #{e.message}, #{e.backtrace.join("\n")}")
            break
          end
          sleep(10)
        end

      ensure
        BrowserStack.logger.info("[SessionSavePolling] start ensure block")
        File.delete(start_file) if File.exist?(start_file)
      end
    end
  end

  class << self
    def start_file(device_id)
      "#{STATE_FILES_DIR}/start_save_session_#{device_id}"
    end

    def running?(device_id)
      # check for start file, if exists then already running
      File.exist?(start_file(device_id))
    end
  end

  private

  def with_ws_connection
    if @ws.nil? || !@ws.open?
      BrowserStack.logger.info("[SessionSavePolling] WebSocket connection not active, reconnecting...")
      setup_ws_connection
    end

    if @ws && @ws.open?
      yield @ws
    else
      BrowserStack.logger.error("[SessionSavePolling] WebSocket connection not available")
      raise "WebSocket connection not available"
    end
  rescue StandardError => e
    BrowserStack.logger.error("[SessionSavePolling] WebSocket operation failed: #{e.message}")
    raise
  end

  def get_cookies
    begin

      BrowserStack.logger.info("[SessionSavePolling] getting cookies with setup_ws_connection")
      setup_ws_connection

      # If we still don't have a valid connection, log and return
      if @ws.nil? || !@ws.open?
        BrowserStack.logger.error("[SessionSavePolling] cannot get cookies: WebSocket connection not available")
        return
      end

      if @current_target_id.nil? || @current_target_id.empty?
        @current_target_id = @@target_ids[@device_id]
        BrowserStack.logger.info("[SessionSavePolling] loaded target ID from class variable: #{@current_target_id}")
      end

      # Define the strip_url_to_domain method in the local scope
      local_strip_url_to_domain = lambda do |url|
        begin
          uri = URI.parse(url)
          "#{uri.scheme}://#{uri.host}"
        rescue URI::InvalidURIError => e
          BrowserStack.logger.error("[SessionSavePolling] error parsing URL: #{e.message}")
          url # Return original URL if parsing fails
        end
      end

      BrowserStack.logger.info("[SessionSavePolling] setting up message handlers, target id: #{@current_target_id}")

      with_ws_connection do |ws|
        url_received = false
        cookies_received = false
        current_url = nil
        that = self
        cookie_path = @cookie_file_path

        BrowserStack.logger.info("[SessionSavePolling] setting up message handlers")
        BrowserStack.logger.info("[SessionSavePolling] inside with_ws_connection current target id: #{@current_target_id}")

        # Set up message handlers before sending requests
        ws.on :message do |msg|
          begin
            response = JSON.parse(msg.data)
            # BrowserStack.logger.info("BrowserActivityMonitoring inside ws.on :message current target id: #{@current_target_id}")
            # Handle Target.dispatchMessageFromTarget responses
            if response['method'] == 'Target.dispatchMessageFromTarget'
              inner_message = JSON.parse(response['params']['message'])

              # Handle URL response using Page.getFrameTree
              if inner_message['id'] == 9002 && inner_message['result'] && inner_message['result']['frameTree']
                full_url = inner_message['result']['frameTree']['frame']['url']
                current_url = local_strip_url_to_domain.call(full_url)
                BrowserStack.logger.info("[SessionSavePolling] current URL from DevTools: #{full_url}")
                BrowserStack.logger.info("[SessionSavePolling] stripped URL: #{current_url}")
                url_received = true
              end

              # Handle cookies response from the target
              if inner_message['id'] == 9001 && inner_message['result'] && inner_message['result']['cookies']
                if current_url
                  cookies = inner_message['result']['cookies']
                  BrowserStack.logger.info("[SessionSavePolling] successfully retrieved #{cookies.length} cookies")

                  existing_cookies = if File.exist?(cookie_path)
                    JSON.parse(File.read(cookie_path))
                  else
                    {}
                  end

                  existing_cookies[current_url] = cookies

                  FileUtils.mkdir_p(File.dirname(cookie_path))
                  File.write(cookie_path, JSON.pretty_generate(existing_cookies))
                  BrowserStack.logger.info("[SessionSavePolling] cookies saved to #{cookie_path} for URL: #{current_url}")
                end
                cookies_received = true
              end
            end
          rescue JSON::ParserError => e
            BrowserStack.logger.error("[SessionSavePolling] error parsing response: #{e.message}")
          end
        end
        BrowserStack.logger.info("[SessionSavePolling] current target id: #{@current_target_id}")
        return unless @current_target_id # Don't proceed if we don't have a target ID
        BrowserStack.logger.info("[SessionSavePolling] proceeding with get_url_command")
        # First get the current URL using Page.getResourceTree
        get_resource_tree_command = {
          id: 9000,
          method: "Target.sendMessageToTarget",
          params: {
            targetId: @current_target_id,
            message: {
              id: 9002,
              method: "Page.getResourceTree"
            }.to_json
          }
        }.to_json

        BrowserStack.logger.info("[SessionSavePolling] sending Page.getResourceTree request via Target API")
        ws.send(get_resource_tree_command)
        BrowserStack.logger.info("[SessionSavePolling] sent Page.getResourceTree request")

        # Wait for URL with timeout
        timeout = Time.now + 5
        until url_received
          sleep 0.1
          if Time.now > timeout
            # Fallback to using json/list if getFrameTree fails
            json_list = JSON.parse(OSUtils.execute("curl http://localhost:#{@debugger_port}/json/list"))
            unless json_list.empty?
              page_tab = json_list.find do |tab| 
                !tab["url"].to_s.empty? && 
                !tab["title"].to_s.include?("ServiceWorker") && 
                !tab["title"].to_s.include?("JSContext")
              end

              if page_tab
                full_url = page_tab["url"]
                current_url = local_strip_url_to_domain.call(full_url)
                BrowserStack.logger.info("[SessionSavePolling] current URL from json/list fallback: #{full_url}")
                BrowserStack.logger.info("[SessionSavePolling] stripped URL: #{current_url}")
                url_received = true
              end
            end
            break
          end
        end

        BrowserStack.logger.info("[SessionSavePolling] url_received: #{url_received}")
        return unless url_received # Don't proceed if we couldn't get the URL
        BrowserStack.logger.info("[SessionSavePolling] proceeding with get_cookies_command")

        # Then get the cookies using Target API
        get_cookies_command = {
          id: 9000,
          method: "Target.sendMessageToTarget",
          params: {
            targetId: @current_target_id,
            message: {
              id: 9001,
              method: "Page.getCookies"
            }.to_json
          }
        }.to_json

        BrowserStack.logger.info("[SessionSavePolling] sending getCookies request via Target API")
        ws.send(get_cookies_command)
        BrowserStack.logger.info("[SessionSavePolling] sent getCookies request")
        # Wait for cookies with timeout
        timeout = Time.now + 5
        until cookies_received
          sleep 0.1
          if Time.now > timeout
            BrowserStack.logger.error("[SessionSavePolling] timeout waiting for cookies response")
            break
          end
        end
      end
    rescue StandardError => e
      BrowserStack.logger.error("[SessionSavePolling] get_cookies error: #{e.message}")
    end
  end

  def set_cookies
    begin
      cookie_path = @cookie_file_path
      # Check if the cookie file exists
      unless File.exist?(cookie_path)
        BrowserStack.logger.info("[SessionSavePolling] no cookies file found at #{cookie_path}")
        return
      end

      # Load all cookies from the file
      all_cookies_by_url = JSON.parse(File.read(cookie_path))
      BrowserStack.logger.info("[SessionSavePolling] loaded cookies for #{all_cookies_by_url.keys.length} URLs and path: #{cookie_path}")

      # Flatten all cookies into a single array
      all_cookies = []
      all_cookies_by_url.each do |url, cookies|
        all_cookies.concat(cookies)
      end

      # Remove duplicate cookies (same name and domain)
      unique_cookies = {}
      all_cookies.each do |cookie|
        key = "#{cookie['name']}_#{cookie['domain']}"
        unique_cookies[key] = cookie
      end

      cookies_to_set = unique_cookies.values
      BrowserStack.logger.info("[SessionSavePolling] setting #{cookies_to_set.length} unique cookies")

      # Check if we have a valid WebSocket connection
      if @ws.nil? || !@ws.open?
        BrowserStack.logger.info("[SessionSavePolling] WebSocket connection not active, reconnecting...")
        setup_ws_connection
      end

      # If we still don't have a valid connection, log and return
      if @ws.nil? || !@ws.open?
        BrowserStack.logger.error("[SessionSavePolling] cannot set cookies: WebSocket connection not available")
        return
      end

      # If current_target_id is not set, try to get it from the class variable
      if @current_target_id.nil? || @current_target_id.empty?
        @current_target_id = @@target_ids[@device_id]
        BrowserStack.logger.info("[SessionSavePolling] loaded target ID from class variable: #{@current_target_id}")
      end

      # If we still don't have a target ID, log and return
      if @current_target_id.nil? || @current_target_id.empty?
        BrowserStack.logger.error("[SessionSavePolling] cannot set cookies: No target ID available")
        return
      end

      cookies_set = 0

      # Send all cookie set requests
      cookies_to_set.each_with_index do |cookie, index|
        # Ensure required fields are present
        next unless cookie['name'] && cookie['value'] && (cookie['domain'] || cookie['url'])

        # Prepare the inner message for Page.setCookie
        inner_message = {
          id: 6000 + index,
          method: "Page.setCookie",
          params: {
            cookie: {
              name: cookie['name'],
              value: cookie['value'],
              domain: cookie['domain'],
              path: cookie['path'] || '/',
              secure: cookie['secure'] || false,
              httpOnly: cookie['httpOnly'] || false,
              sameSite: cookie['sameSite'] || 'None',
              expires: cookie['expires'] ? cookie['expires'] * 1000 : (Time.now.to_i + 365 * 24 * 60 * 60)
            }
          }
        }.to_json

        # Prepare the Target API command
        set_cookie_command = {
          id: 6000 + index,
          method: "Target.sendMessageToTarget",
          params: {
            targetId: @current_target_id,
            message: inner_message
          }
        }.to_json

        BrowserStack.logger.info("[SessionSavePolling] setting cookie: #{cookie['name']} (#{index + 1}/#{cookies_to_set.length})")

        begin
          @ws.send(set_cookie_command)
          cookies_set += 1
          # sleep 0.1  # Small delay to avoid overwhelming the connection
        rescue StandardError => e
          BrowserStack.logger.error("[SessionSavePolling] error sending cookie command: #{e.message}")
          BrowserStack.logger.error("[SessionSavePolling] cookie data: #{cookie.inspect}")
        end
      end

      BrowserStack.logger.info("[SessionSavePolling] successfully set #{cookies_set} out of #{cookies_to_set.length} cookies")

      # Refresh the page after setting cookies
      if cookies_set > 0
        refresh_command = {
          id: 7000,
          method: "Target.sendMessageToTarget",
          params: {
            targetId: @current_target_id,
            message: {
              id: 7000,
              method: "Page.reload",
              params: { ignoreCache: false }
            }.to_json
          }
        }.to_json

        BrowserStack.logger.info("[SessionSavePolling] refreshing page after setting cookies")
        @ws.send(refresh_command)
      end

    rescue StandardError => e
      BrowserStack.logger.error("[SessionSavePolling] set_cookies error: #{e.message}")
    end
  end

end
# rubocop:enable all