require 'json'
require 'net/http'
require 'timeout'
require 'uri'
require 'fileutils'

require_relative '../models/device_state'
require_relative '../../config/constants' # For STATE_FILES_DIR, PRODUCTION_SITE

module BrowserStack
  module FirstDeviceCheckHelper
    class << self
      def first_device_check?(device_id)
        !File.exist?(File.join(STATE_FILES_DIR, "first_device_thread_done_#{device_id}"))
      rescue => e
        BrowserStack.logger.error("Error checking first run for device #{device_id}: #{e.message} #{e.backtrace}")
        false
      end

      def fetch_device_details_from_terminal_details_api(device_id, static_conf)
        return {} if static_conf.nil?

        rails_endpoint = static_conf.device_rails_endpoint(device_id)
        return {} if rails_endpoint.nil?

        url = construct_terminal_details_url(device_id, rails_endpoint)
        return {} if url.nil?

        credentials = get_api_credentials(rails_endpoint, static_conf)
        return {} if credentials.nil?

        make_api_request(url, credentials.slice(:username, :password))
      rescue => e
        BrowserStack.logger.error("Error in fetch_device_details for #{device_id}: #{e.message}")
        {}
      end

      def assign_device_details_attributes(device_details)
        @sim_details = device_details['terminal_sim_details']
        @dedicated_device = device_details['private_terminal_mapping']&.any?
        @dedicated_cleanup = @dedicated_device && device_details['private_terminal_mapping']['cleanup_type'] == 'dedicated'
      end

      def setup_sim_details_for_device
        @device_state.write_to_sim_info_file(JSON.generate(@sim_details)) if @sim_details&.any? && @dedicated_device
      rescue => e
        BrowserStack.logger.error("Error setting up sim details for #{@device_id}: #{e.message} #{e.backtrace.join("\n")}")
      end

      def setup_dedicated_device_state_for_device
        @device_state.remove_dedicated_device_file if @device_state.dedicated_device_file_present? && !@dedicated_device
        @device_state.remove_dedicated_cleanup_file if @device_state.dedicated_cleanup_file_present? && !@dedicated_cleanup
        @device_state.touch_dedicated_device_file if !@device_state.dedicated_device_file_present? && @dedicated_device
        @device_state.touch_dedicated_cleanup_file if !@device_state.dedicated_cleanup_file_present? && @dedicated_cleanup
      rescue => e
        BrowserStack.logger.error("Error setting up dedicated state for #{@device_id}: #{e.message} #{e.backtrace.join("\n")}")
      end

      def perform_first_device_check_setup(device_id, device_config, static_conf)

        device_details_response = fetch_device_details_from_terminal_details_api(device_id, static_conf)

        device_specific_details = device_details_response[device_id]

        if device_specific_details.nil? || device_specific_details.empty?
          BrowserStack.logger.warn("No device details found from API for #{@device_id}: First run setup may be incomplete.")
          return
        end

        @device_state = DeviceState.new(device_id)

        assign_device_details_attributes(device_specific_details)

        setup_sim_details_for_device
        setup_dedicated_device_state_for_device

      rescue => e
        BrowserStack.logger.error("Error during first run setup for #{@device_id}: #{e.message} #{e.backtrace.join("\n")}")
      end

      private

      def construct_terminal_details_url(device_id, rails_endpoint)
        rails_endpoint = "#{rails_endpoint}.bsstag.com" unless
          rails_endpoint =~ /^localhost(:[0-9]+)?$/ || rails_endpoint.include?('.') || rails_endpoint.include?(PRODUCTION_SITE)

        rails_endpoint = rails_endpoint.sub(%r{^http:[/\\]+}, '')
        url = URI("https://#{rails_endpoint}/admin/mobile_terminals/details")
        url.query = URI.encode_www_form({ instance_ids: [device_id] })
        url.to_s
      rescue => e
        BrowserStack.logger.error("Error constructing URL for #{device_id}: #{e.message}")
        nil
      end

      def get_api_credentials(rails_endpoint, static_conf)
        is_staging = !rails_endpoint.include?(PRODUCTION_SITE)
        username_key = is_staging ? "staging_rails_user" : "admin_terminals_user"
        password_key = is_staging ? "staging_rails_pass" : "admin_terminals_pass"

        unless static_conf[username_key] && static_conf[password_key]
          BrowserStack.logger.error("Missing credentials in static_conf for #{@device_id}: Required: #{username_key}, #{password_key}")
          return nil
        end

        { username: static_conf[username_key], password: static_conf[password_key], is_staging: is_staging }
      rescue => e
        BrowserStack.logger.error("Error getting API credentials for #{@device_id}: #{e.message}")
        nil
      end

      def make_api_request(url, auth)
        response_body = {}
        response = HttpUtils.get_response(url, auth)

        return {} unless response.code == "200"

        parsed_response = JSON.parse(response.body)
        return parsed_response['details'] if parsed_response['success'] && parsed_response['details']

        error_message = "Response from terminal details API was not successful for #{@device_id}: #{parsed_response['message']}"
        log_and_push_error("terminal_api_request_failed", error_message, { device_id: @device_id, url: url })
        {}
      rescue => e
        error_message = "Error making API request for #{@device_id}: #{e.message}"
        log_and_push_error("terminal_api_request_failed", error_message, { device_id: @device_id, url: url })
        {}
      end

      def log_and_push_error(kind, message, data)
        BrowserStack.logger.error(message)
        BrowserStack::Zombie.push_logs(kind, message, data)
      end
    end
  end
end
