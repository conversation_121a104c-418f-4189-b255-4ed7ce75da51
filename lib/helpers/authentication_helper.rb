require_relative '../../config/constants'
require_relative "../utils/utils"

require "#{MOBILE_COMMON_ROOT}/utils/auth_key_util"

class AuthenticationHelper
  include Auth<PERSON><PERSON>Util

  def self.auth_key_valid?(device_info, device)
    device_name = device_info.nil? ? '[device name not found]' : device_info['device_name']
    return true if device_name != "iPhone14,5"

    session_params = Utils.read_json_file("#{STATE_FILES_DIR}/#{device}_session")
    session_id = session_params['app_live_session_id']
    return true if session_id.nil?

    auth_key = read_data_from_auth_key_state_file(session_id, device)
    validate_auth_key(session_id, device, auth_key)
  rescue => e
    BrowserStack.logger.info("auth_key_valid?: Some error occured: #{e}")
    true
  end
end
