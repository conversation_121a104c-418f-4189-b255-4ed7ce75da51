require_relative '../utils/utils'
require_relative '../models/device_state'
require_relative '../../server/device_manager'

class Battery
  BATTERY_TAG = "[BATTERY]"

  def self.low_power_mode_enabled?(device)
    device_config = DeviceManager.device_configuration_check(device)
    client = WdaClient.new(device_config["webdriver_port"].to_i)
    mode = client.low_power_mode_enabled?["value"] && client.low_power_mode_enabled?["value"]["status"]
    mode.eql?("enabled")
  end

  def initialize(device_id, session_id, product = "")
    @udid = device_id
    @session_id = session_id
    @product = product
    @device_state = DeviceState.new(device_id)
    @device_config = DeviceManager.device_configuration_check(@udid)
    @low_power_mode_data_reporter = DataReportHelper.new('low-power-mode',
                                                         session_id: session_id,
                                                         product: product,
                                                         ios_version: @device_config["device_version"],
                                                         device: device_id)
  end

  def change_low_power_mode(state = "Off") # rubocop:todo Metrics/AbcSize
    return if @device_state.low_power_mode_file_present? && state == "On"

    start_time = Time.now.to_i
    client = WdaClient.new(@device_config['webdriver_port'].to_i)
    failure_data = {}
    success_data = {}
    begin
      BrowserStack.logger.info("#{BATTERY_TAG} Turning Low Power Mode to - #{state} state")
      res = client.low_power_mode(state)
      BrowserStack.logger.info("#{BATTERY_TAG} Received response form wda client : response -  #{res}")

      if res['value']['status'] == "pass"
        @device_state.remove_low_power_mode_file if state == "Off"
        end_time = Time.now.to_i
        success_data = @low_power_mode_data_reporter.populate_data_hash(wda_response: res['value'], interval: end_time - start_time)
        true

      else
        end_time = Time.now.to_i
        failure_data = @low_power_mode_data_reporter.populate_data_hash(wda_response: res['value'], additional_data: { "error" => "failure response from curl request" } , interval: end_time - start_time )
        raise WdaAutomationError.new(res['value']['message']), res['value']['debugDescription']
      end

    rescue WdaAutomationError => e
      BrowserStack.logger.info("#{BATTERY_TAG} automation failure - #{e}; element tree - #{e.message}")
      false

    rescue WdaClientError => e
      end_time = Time.now.to_i
      failure_data = @low_power_mode_data_reporter.populate_data_hash(wda_response: { "error" => e.message }, additional_data: { "wda-client-status" => client.running? ? "running" : "non running" } , interval: end_time - start_time )
      false

    rescue => e
      end_time = Time.now.to_i
      failure_data = @low_power_mode_data_reporter.populate_data_hash(wda_response: { "error" => e.message }, additional_data: { "exception" => e.message } , interval: end_time - start_time )
      BrowserStack.logger.info("#{BATTERY_TAG} Standard error while turning on low power mode : error -  #{e.message}")
      false
    ensure
      data = success_data.merge!(failure_data)
      @low_power_mode_data_reporter.report(data)
      @device_state.touch_low_power_mode_file if state == "On"
    end
  end
end
