require_relative '../../config/constants'
require_relative '../utils/idevice_ffi/icon_layout_util'

class ContactsAppHelper
  def initialize(device_id, session_id, product = "")
    @device_id = device_id
    @session_id = session_id
    @product = product
    @icon_layout_util = IconLayoutUtil.new(device_id: device_id)
  end

  def add_icon_to_home_screen
    log(:info, "Adding Contacts App icon to home screen")
    @icon_layout_util.icon_state = [
      [],
      [[{ "bundleIdentifier": "com.apple.MobileAddressBook" }]]
    ]
    log(:info, "Added Contacts App icon to home screen")
    true
  rescue => e
    log(:error, "Failed to add Contacts App icon to home scren, error: #{e.message}, #{e.backtrace.join("\n")}")
    false
  end

  private

  def log(level, message)
    params = { subcomponent: "[contacts_app_helper]", device: @device_id, session: @session_id }
    BrowserStack.logger.send(level.to_sym, message, params)
  end
end
