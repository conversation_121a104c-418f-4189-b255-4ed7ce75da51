require_relative '../configuration'
require_relative '../utils/zombie'
require_relative './data_report_helper'
require_relative '../../server/device_manager'
require 'fileutils'

class CameraInjectionHelper

  STATE_FILES_DIR = "/usr/local/.browserstack/state_files".freeze

  def initialize(device_id, session_id, product = "")
    @uuid = device_id
    @product = product
    @session_id = session_id
    @device_config = DeviceManager.device_configuration_check(@uuid)
    @ios_version = !@device_config.nil? && @device_config["device_version"]
    @reporter = DataReportHelper.new("camera_injection", session_id: @session_id, device: @uuid, product: @product, ios_version: @ios_version)
    @camera_injection_extension_sample_script_path = File.join(DIR_HOME, "scripts/camera_injection/extension/")
    @camera_injection_extension_script_path = File.join(STATE_FILES_DIR, "camera_injection/")
  end

  def update_camera_url(camera_injection_url)
    BrowserStack.logger.info("Update camera URL request for #{@uuid}, #{@session_id}, #{camera_injection_url}")
    type = 'url-update'
    begin
      f = File.open("#{@camera_injection_extension_sample_script_path}sample_injection_script.js", "r")
      data = f.read
      f.close
      data = data.sub("some_replacable_url", camera_injection_url)
      FileUtils.mkdir_p @camera_injection_extension_script_path
      f = File.open("#{@camera_injection_extension_script_path}injection_script.js", "w")
      f.write(data)
      f.close
      @reporter.report({ status: "pass", type: "camera_injection", message: "Camera URL successfully updated" })
    rescue => e
      BrowserStack.logger.info("Unknown error in update camera URL: #{e.message} \n backtrace: #{e.backtrace}")
      @reporter.report({ status: "fail", type: "camera_injection", message: e.message })
    end
  end
end
