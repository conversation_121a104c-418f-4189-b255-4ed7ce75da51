require_relative '../utils/zombie'
require "#{MOBILE_COMMON_ROOT}/utils/log_parse_util"

module BrowserStack
  class HostStats # rubocop:disable Metrics/ClassLength

    def initialize
      @page_size = page_size
    end

    def perform
      Zombie.push_logs("ios_host_stats", '', { "data" => host_stats })
      instrument_puma_server
    rescue => e
      Zombie.push_logs("ios_host_stats_failed", e.message, { "data" => e.backtrace })
    end

    def instrument_puma_server
      push_puma_server_stats
    rescue => e
      Zombie.push_logs("puma_server_stats_failed", e.message, { "data" => e.backtrace })
    end

    def host_stats
      stats = {}

      stats["swap_memory"] = swap_memory_stats
      stats["disk_usage"] = disk_stats
      stats["memory"] = memory_stats
      stats["cpu"] = cpu_stats
      stats["ports"] = port_stats
      stats["file_descriptors"] = file_descriptors
      stats["os_version"] = os_version

      stats
    end

    # Swap memory in MB
    def swap_memory_stats
      stats = {}

      swap_data = swap_stats.scan(/\d+\.\d+/)
      stats["swap_total"] = swap_data[0].to_i
      stats["swap_used"] = swap_data[1].to_i
      stats["swap_free"] = swap_data[2].to_i

      # To avoid divide by 0 error as swap_total can be 0
      stats["swap_used_percentage"] = stats["swap_total"] == 0 ? 0 : (stats["swap_used"].to_f / stats["swap_total"] ) * 100

      stats
    end

    # displays in Mbytes
    def memory_stats
      stats = {}

      stats["memory_free"] = ((pages_free.to_f * @page_size) / 1_000_000).to_i
      stats["memory_active"] = ((pages_active.to_f * @page_size) / 1_000_000).to_i
      stats["memory_inactive"] = ((pages_inactive.to_f * @page_size) / 1_000_000).to_i
      # useful if you compare amount of faults with previous machine check push to show memory pressure
      stats["translation_faults"] = translation_faults

      stats
    end

    # displays in Mbytes
    def disk_stats
      stats = {}

      stats["disk_space_used"] = disk_space_used
      stats["disk_space_available"] = disk_space_available
      stats["disk_space_percentage"] = ((stats["disk_space_used"].to_f / (stats["disk_space_used"].to_f + stats["disk_space_available"].to_f)) * 100).round(2)

      stats
    end

    def cpu_stats
      stats = {}

      stats["total_process_count"] = process_count
      stats["cpu_usage_percentage"] = cpu_usage
      stats["average_load"] = average_load

      stats
    end

    def port_stats
      stats = {}

      ["45671", "45680"].each do |port|
        stats[port] = {}
        stats[port]["count"] = port_count(port)
      end
      stats
    end

    private

    def swap_stats
      `sysctl vm.swapusage`.strip
    end

    def page_size
      `vm_stat | sed -n 1p | awk -F ' ' '{print $8}'`.strip.to_i
    end

    def pages_free
      `vm_stat | sed -n 2p | awk -F ' ' '{print $3}'| sed 's/.$//g'`.strip.to_i
    end

    def pages_active
      `vm_stat | sed -n 3p | awk -F ' ' '{print $3}'| sed 's/.$//g'`.strip.to_i
    end

    def pages_inactive
      `vm_stat | sed -n 4p | awk -F ' ' '{print $3}'| sed 's/.$//g'`.strip.to_i
    end

    def translation_faults
      `vm_stat | sed -n 9p | awk -F ' ' '{print $3}'| sed 's/.$//g'`.strip.to_i
    end

    def disk_space_used
      `df -m | awk '{s+=$3} END {print s}'`.strip.to_i
    end

    def disk_space_available
      `df -m | awk '{s+=$4} END {print s}'`.strip.to_i
    end

    def os_version
      `sw_vers | sed -n 2p | awk -F ' ' '{print $2}'`.strip
    end

    def process_count
      `ps aux | wc -l`.strip.to_i
    end

    def cpu_usage
      `top -l  2 | grep -E "^CPU" | tail -1 | awk '{ print $3 + $5"%" }'`.strip.to_i
    end

    def average_load
      `uptime | cut -d: -f4 | awk '{print $1}'`.strip.to_i
    end

    def port_count(port)
      `lsof -i :#{port} | wc -l`.strip.to_i
    end

    def file_descriptors
      `sudo lsof | wc -l`.strip.to_i
    end
  end
end
