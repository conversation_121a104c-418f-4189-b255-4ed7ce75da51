require_relative '../../config/constants'
require_relative '../utils/zombie'

require 'time'
require 'json'

class CryptoMiningDetectionHelper
  # Long list of crypto mining pool domain names
  COINBLOCKERLIST_URL = 'https://gitlab.com/ZeroDot1/CoinBlockerLists/-/raw/master/list.txt'
  COINBLOCKERLIST_FILE = "#{CONFIG_ROOT}/coin_blocker_list.txt"

  DNS_LOGS = "#{LOGGING_DIR}/dns_requests.log"

  def instrument_crypto_mining
    crypto_domains_set = coin_blocker_list
    domain_names_set, domain_timestamps = parse_dns_logs
    File.truncate(DNS_LOGS, 0) # delete log file contents to limit size

    flagged_domains = crypto_domains_set.intersection(domain_names_set)
    return if flagged_domains.empty?

    BrowserStack::Zombie.configure
    flagged_domains.each do |domain|
      data = {
        "domain" => domain,
        "access_times" => domain_timestamps[domain].to_a
      }
      log(:info, "Crypto mining detected - domain: #{data}")
      BrowserStack::Zombie.push_logs('crypto-mining-detected', '', { 'data': data })
    end
  end

  def parse_dns_logs
    log(:info, "Parsing #{DNS_LOGS}")
    domain_names_set = Set[]
    domain_timestamps = {}

    # Using File.foreach to avoid reading the entire log file into memory at once.
    File.foreach(DNS_LOGS) do |line|
      output = parse_log_line(line)
      next if output.nil?

      timestamp, domains = output

      # Record the timestamp each domain name was requested at
      domains.each do |d|
        if domain_timestamps.key?(d)
          domain_timestamps[d].merge([timestamp])
        else
          domain_timestamps[d] = Set[timestamp]
        end
      end

      domain_names_set.merge(domains)
    end

    [domain_names_set, domain_timestamps]
  end

  # Sample log lines:
  # 18:40:43.422800 IP ***************.63370 > ***************.53: 54429+ A? www.google.com. (32)
  # 18:42:12.729558 IP ***************.53 > ***************.50505: 12983 3/1/0 CNAME weather-data.apple.com.akadns.net., CNAME a2047.dscapi9.akamai.net. (233)
  def parse_log_line(line)
    words = line.to_s.split(' ')

    time_str = words[0]
    return if time_str.nil?

    timestamp = convert_to_timestamp(time_str)
    return if timestamp.nil?

    domains = words[6..-2]
    return if domains.nil?

    # Clean up domain names a bit
    domains -= ['A', 'A?', 'AAAA?', 'Type65?', 'CNAME']
    domains = domains.uniq.map { |d| d.chomp(',').chomp('.') }
    domains.reject! { |d| valid_ip_address?(d) || d.match(%r{\d/\d/\d}) }

    [timestamp, domains]
  end

  def convert_to_timestamp(time_string)
    Time.parse(time_string.to_s).strftime('%Y-%m-%d %H:%M:%S')
  rescue ArgumentError
    nil
  end

  def valid_ip_address?(ip)
    IPAddr.new(ip.to_s)
    true
  rescue IPAddr::InvalidAddressError
    false
  end

  def coin_blocker_list
    download_coin_blocker_list if coin_blocker_list_download_required?

    log(:info, "Reading coin blocker list")
    File.read(COINBLOCKERLIST_FILE).split("\n").to_set
  end

  # True if file doesn't exist or is older than 7 days
  def coin_blocker_list_download_required?
    !File.exist?(COINBLOCKERLIST_FILE) ||
      (File.mtime(COINBLOCKERLIST_FILE) < (Time.now - (60 * 60 * 24 * 7)))
  end

  def download_coin_blocker_list
    log(:info, "Downloading coin blocker list: #{COINBLOCKERLIST_URL}")

    uri = URI(COINBLOCKERLIST_URL)
    request = Net::HTTP::Get.new(uri)
    response = Net::HTTP.get_response(uri)
    File.open(COINBLOCKERLIST_FILE, 'w') do |file|
      file.write(response.body)
    end
  end

  def log(level, msg)
    params = { subcomponent: self.class.to_s }
    BrowserStack.logger.send(level.to_sym, msg, params)
  end
end
