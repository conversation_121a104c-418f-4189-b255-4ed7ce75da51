require 'timeout'

require_relative '../utils/osutils'
require_relative '../models/device_state'
require_relative '../../config/constants'
require_relative '../../lib/configuration'

class VideoRecProcessLogger
  PROCESS_NAMES = [
    "Launcher",
    "live_streaming",
    "WebDriverAgentRunner-Runner"
  ]
  def initialize(device, session_id, is_start = true)
    @device_id = device
    @session_id = session_id
    @device_state = DeviceState.new(device)
    @marker = is_start ? "Start Recording" : "Stop Recording"
    @server_config = Configuration.new.all
  end

  def start
    truncate_logs
    write_log_head
    logger_pid = Process.spawn(syslog_command, { pgroup: true })
    Process.detach(logger_pid)
    @device_state.write_to_video_logger_pid_file(logger_pid)
  end

  def check_and_start
    check_and_kill_running_process
    start
  end

  def stop
    write_log_head(false)
    check_and_kill_running_process
    truncate_logs
  end

  private

  def check_and_kill_running_process
    logger_pid = @device_state.read_video_logger_pid_file.to_i if @device_state.video_logger_pid_file_present?
    # We want to kill the process group https://unix.stackexchange.com/a/14853
    raise "logger pid file is empty" if logger_pid.nil? || logger_pid == 0

    OSUtils.kill_pid(-Process.getpgid(logger_pid))
  rescue => e
    BrowserStack.logger.info("unable to kill idevicesyslog process gracefully #{e.message}")
  ensure
    @device_state.remove_video_logger_pid_file
  end

  def syslog_command
    "#{IdeviceUtils.idevicesyslog_bin_path} -u #{@device_id} #{process_filter} >> #{log_file}"
  end

  def process_filter
    filter_string = ""
    PROCESS_NAMES.each do |process_name|
      filter_string += " -p #{process_name}"
    end
    filter_string
  end

  def write_log_head(start = true)
    phase = start ? "Starting" : "Ending"
    File.open(log_file, "a") { |f| f.write("\n>>>>>>>>>>>>>>>>>>\n#{DateTime.now} [#{@marker}] #{phase} video recording logs for #{@device_id} for session_id: #{@session_id}\n>>>>>>>>>>>>>>>>>>\n") }
  end

  def log_file
    "/var/log/browserstack/video_rec_process_#{@device_id}.log"
  end

  def truncate_logs
    return unless File.file?(log_file)

    archive_log_file = File.open("#{log_file}.archive", 'a')
    IO.foreach(log_file) do |log|
      archive_log_file << log
    end
    archive_log_file.close
    File.truncate(log_file, 0)
  end
end
