require 'browserstack_logger'
require 'fileutils'
require 'sequel'
require 'sqlite3'

require_relative '../ifuse'
require_relative '../utils/zombie'

class PhotosSqliteHelper # rubocop:todo Metrics/ClassLength
  def initialize(udid, device_version)
    @udid = udid
    @device_version = device_version.to_f
    @ifuse = Ifuse.new(udid)
    @mount_point = @ifuse.mount_point
  end

  def all_media_count
    execute_photos_db { |db| db[asset_table].count }
  end

  def hidden_media_count
    execute_photos_db { |db| db[asset_table].where(ZHIDDEN: 1).count }
  end

  def marked_favorite_count
    execute_photos_db { |db| db[asset_table].where(ZFAVORITE: 1).count }
  end

  def trashed_media_count
    execute_photos_db { |db| db[asset_table].where(ZTRASHEDSTATE: 1).count }
  end

  def video_count
    execute_photos_db { |db| db[asset_table].exclude(ZDURATION: 0).count }
  end

  def photos_count
    execute_photos_db { |db| db[asset_table].where(ZDURATION: 0).count }
  end

  def albums_count
    sql_query = @device_version < 15.0 ? { ZKIND: 2 } : { ZIMPORTEDBYBUNDLEIDENTIFIER: "com.apple.mobileslideshow" }
    sql_query[:ZTRASHEDSTATE] = 0
    execute_photos_db { |db| db[:ZGENERICALBUM].exclude(ZTITLE: nil).where(sql_query).count }
  end

  def folder_count
    sql_query = @device_version < 15.0 ? { ZKIND: 4000 } : { ZIMPORTEDBYBUNDLEIDENTIFIER: nil }
    sql_query[:ZTRASHEDSTATE] = 0
    count = execute_photos_db { |db| db[:ZGENERICALBUM].exclude(ZTITLE: nil).where(sql_query).count }

    # 3 predefined folders by apple: progress-ota-restore, progress-fs-import, progress-sync, therefore count - 3
    count -= 3 unless @device_version < 15.0
    count
  end

  def ios_media_state_consistent?(push_to_zombie: false, check_file_size: false)
    # backup sqlite files for these versions contains 1 deleted item, hence check fails
    # https://browserstack.atlassian.net/browse/MOBFR-468?focusedCommentId=423650
    return true if @device_version >= 13.4 && @device_version <= 13.7

    return true if @device_version < 13.0 # we do not use sqlite preload method for them, hence skipping for now

    pull_photos_sqlite_from_device
    current_media_state = {
      all_media_count: all_media_count,
      hidden_media_count: hidden_media_count,
      trashed_media_count: trashed_media_count,
      video_count: video_count,
      photos_count: photos_count,
      albums_count: albums_count,
      folder_count: folder_count,
      favorites_count: marked_favorite_count
    }
    # we don't clean favorites or folders in new preload media currently, hence not checking them
    media_consistent = current_media_state[:all_media_count] == 9 &&
                       current_media_state[:hidden_media_count].zero? &&
                       current_media_state[:trashed_media_count].zero? &&
                       current_media_state[:video_count] == 3 &&
                       current_media_state[:photos_count] == 6 &&
                       current_media_state[:albums_count].zero?
    BrowserStack.logger.info("Is preload media consistent? #{media_consistent}")
    # log the state of preload media if it is not consistent
    BrowserStack.logger.info("State of preload media: #{current_media_state}") unless media_consistent

    if !media_consistent && push_to_zombie
      zombie_data = {
        "data": current_media_state,
        "os_version": @device_version,
        "device": @udid
      }
      current_folder_count = current_media_state[:folder_count]
      BrowserStack::Zombie.push_logs("ios_sqlite_media_state", "media_state_consistent: #{media_consistent}", zombie_data)
      # logging that user has created folder, might require cleaning in future in alternate preload media
      BrowserStack::Zombie.push_logs("ios_media_folder_created", "folder count #{current_folder_count}", { "device": @udid, "os_version": @device_version } ) if current_folder_count > 0
    end

    delete_tmp_sqlite_files
    check_file_size_and_clean if check_file_size
    media_consistent
  rescue Sequel::DatabaseError => e
    BrowserStack.logger.error("Sequel::DatabaseError error #{e.message} #{e.backtrace.join("\n")}")
    false
  end

  private

  def delete_tmp_sqlite_files
    FileUtils.rm_f(tmp_photos_sqlite_file_path)
    FileUtils.rm_f("#{tmp_photos_sqlite_file_path}-shm")
    FileUtils.rm_f("#{tmp_photos_sqlite_file_path}-wal")
  end

  def copy_sqlite_files_to_tmp
    FileUtils.cp("#{@mount_point}/PhotoData/Photos.sqlite", tmp_photos_sqlite_file_path)
    FileUtils.cp("#{@mount_point}/PhotoData/Photos.sqlite-shm", "#{tmp_photos_sqlite_file_path}-shm")
    FileUtils.cp("#{@mount_point}/PhotoData/Photos.sqlite-wal", "#{tmp_photos_sqlite_file_path}-wal")
  end

  def pull_photos_sqlite_from_device
    delete_tmp_sqlite_files
    if File.exists?(@mount_point) # don't mount again, just pull it
      copy_sqlite_files_to_tmp
    else
      @ifuse.run do # mount and pull
        copy_sqlite_files_to_tmp
      end
    end
  end

  def check_file_size_and_clean
    threshold = 15.0  # Take time to copy files greater than 15MB, hence deleting the file and rebooting the device so that a small file gets regenerated
    file_name = "#{@mount_point}/PhotoData/Photos.sqlite"
    file_size = File.size(file_name) / (1024.0 * 1024.0)
    BrowserStack.logger.info "Size of #{file_name} is #{file_size}Mb"
    zombie_data = {
      "data": file_size,
      "os_version": @device_version,
      "device": @udid
    }
    BrowserStack::Zombie.push_logs("ios_photos_sqlite_size", "ios_photos_sqlite_size", zombie_data)
    if file_size > threshold
      BrowserStack.logger.info "File Size is greater than #{threshold}Mb. Need to delete and reboot"
      FileUtils.rm_f(file_name)
      FileUtils.rm_f("#{file_name}-shm")
      FileUtils.rm_f("#{file_name}-wal")
      DeviceManager.reboot_and_wait(@udid)
    end
  rescue => e
    BrowserStack.logger.error "Failed in check_file_size_and_clean message : #{e.message} backtrace : #{e.backtrace}"
  end

  def execute_photos_db
    pull_photos_sqlite_from_device unless File.file?(tmp_photos_sqlite_file_path)
    db = Sequel.connect("sqlite://#{tmp_photos_sqlite_file_path}")
    yield(db)
  ensure
    db.disconnect
  end

  def tmp_photos_sqlite_file_path
    "/tmp/Photos-#{@udid}.sqlite"
  end

  def asset_table
    @device_version < 14.0 ? :ZGENERICASSET : :ZASSET
  end
end
