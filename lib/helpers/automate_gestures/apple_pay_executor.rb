require_relative '../../custom_exceptions'
require_relative '../../configuration'
require_relative '../wda_client'
require_relative '../../utils/utils'
require_relative '../../../server/device_manager'
require_relative '../../models/ios_device'
require_relative '../../utils/osutils'
require_relative '../../../config/constants'

# Executes Apple pay payment using Confirm with Assistive Touch from assistive touch menu on device using image recognition on otherwise inaccessible XCUI elements.
# Instantiated upon platform call from custom executor to confirm payment when using apple pay on device.
class ApplePayExecutor #rubocop:todo Metrics/ClassLength

  APPLE_PAY_EXECUTOR = "[APPLE_PAY_EXECUTOR]"
  TIMEOUT_CMD = "/usr/local/bin/gtimeout"
  DP_DENSITY_MAP = { "iPhone12,1" => 2, "iPhone11,8" => 2, "iPhone15,4" => 1.5 }

  def initialize(device_id, session_id, product = "")
    @uuid = device_id
    @session_id = session_id
    @product = product
    @ios_device = BrowserStack::IosDevice.new(device_id, self.class.to_s, BrowserStack.logger)
    @server_config = BrowserStack::Configuration.new.all
    @device_config = DeviceManager.device_configuration_check(@uuid)
    @wda_client = WdaClient.new(@device_config['webdriver_port'])
    @trigger_apple_pay_data_reporter = DataReportHelper.new('trigger-apple-pay',
                                                            session_id: session_id,
                                                            product: product,
                                                            ios_version: @device_config["device_version"],
                                                            device: device_id)

    @reincarnate_required = true
    @dp_density = DP_DENSITY_MAP.key?(@device_config["device_name"]) ? DP_DENSITY_MAP[@device_config["device_name"]] : 3
    @is_low_res_image_required = DP_DENSITY_MAP.key?(@device_config["device_name"]) ? true : false
  end

  # Triggers assistive touch's apple pay, instruments errors and report data
  def trigger_apple_pay # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
    device_state = DeviceState.new(@uuid)
    success_data = {}
    failure_data = {}
    start_time = Time.now.to_i
    begin
      retries ||= 0
      log("Triggering Apple Pay retry count: #{retries}")
      if @device_config["device_version"].to_i >= 18
        execute_apple_pay_automation_optimized
      else
        execute_apple_pay_automation
      end

      log("Triggering apple pay successful.")
      success_data = @trigger_apple_pay_data_reporter.populate_data_hash(
        additional_data: { "status" => "pass" },
        interval: Time.now.to_i - start_time
      )
      return true # device apple pay success

    rescue ScriptExecutionError, UnsupportedElementError => e
      @reincarnate_required = !@reincarnate_required
      retry if (retries += 1) < 2
      failure_data = @trigger_apple_pay_data_reporter.populate_data_hash(
        additional_data: { "status" => "fail", "error" => "#{e.response_object} - #{e.message}" },
        interval: Time.now.to_i - start_time
      )

    rescue WdaClientError => e
      failure_data = @trigger_apple_pay_data_reporter.populate_data_hash(
        wda_response: { "status" => "fail", "error" => "#{e.response_object.to_s[0...50]} - #{e.message.to_s[0...50]}" },
        additional_data: { "wda-client-status" => @wda_client.running? ? "running" : "non running" },
        interval: Time.now.to_i - start_time
      )

    rescue => e
      failure_data = @trigger_apple_pay_data_reporter.populate_data_hash(
        additional_data: { "status" => "fail", "error" => e.message[0...100] },
        interval: Time.now.to_i - start_time
      )

    ensure
      `"rm  #{screenshot_file_path})` # remove the screenshot file
      data = success_data.merge!(failure_data)
      @trigger_apple_pay_data_reporter.report(data) if device_state.apple_pay_configuration_file_present?  # send event for enabled devices only, as we're making this executor GA on dedicated cloud
    end
    log("Triggering apple pay failed due to reason: #{failure_data}")
    false # device apple pay failure
  end

  # @return [TrueClass]
  def execute_apple_pay_automation
    log("Executing Apple Pay Automation")
    apple_pay_template_file = "#{@server_config['assistive_touch_gesture_button_templates']}/apple_pay_button.png"
    confirm_apple_pay_template_file = @is_low_res_image_required ? "#{@server_config['assistive_touch_gesture_button_templates']}/confirm_apple_pay_button_low_res.png" : "#{@server_config['assistive_touch_gesture_button_templates']}/confirm_apple_pay_button_high_res.png"

    # This code darkens the assistive touch bubble to increase detection
    @reincarnate_required && @ios_device.reincarnate_assistive_touch

    log("Triggering Assistive touch")
    screenshot_file = save_screenshot_to_file # saves screenshot image containing assistive touch bubble
    x, y = coordinates("assistive_touch_bubble", screenshot_file) # coordinates of assistive touch bubble
    @wda_client.tap(x, y) # tap on assistive touch bubble

    sleep(1) # avoid race-around of screenshot taken before assistive touch menu appeared on screen
    log("Clicking on Apple Pay button")
    screenshot_file = save_screenshot_to_file # saves screenshot image containing assistive touch menu options
    x, y = coordinates("apple_pay_button", screenshot_file, apple_pay_template_file) # coordinates of apple pay button in assistive touch menu
    @wda_client.tap(x, y) # tap on apple pay option

    sleep(1) # avoid race-around of screenshot taken before assistive touch menu appeared on screen
    log("Clicking on Confirm Apple Pay button")
    screenshot_file = save_screenshot_to_file # saves screenshot image containing assistive touch menu options
    x, y = coordinates("confirm_apple_pay_button", screenshot_file, confirm_apple_pay_template_file) # coordinates of confirm apple pay button in assistive touch menu
    @wda_client.tap(x, y) # tap on shake option
    true # device confirm with apple pay button success
  end

  def execute_apple_pay_automation_optimized
    log("Executing Optimized Apple Pay Automation")
    @reincarnate_required && @ios_device.reincarnate_assistive_touch
    sleep(1)
    wda_response = @wda_client.tap_assistive_touch_element("Pay")
    raise wda_response['value']['message'] unless wda_response['value']['status'] == "pass"

    wda_response = @wda_client.tap_assistive_touch_element("Confirm Pay", doNotTapAssistiveTouch: "true")
    raise wda_response['value']['message'] unless wda_response['value']['status'] == "pass"

    log("Executed Optimized Apple Pay Automation")
    true
  end

  # @param [String] element
  # @param [String] screenshot_path
  # @param [String] template_file_path
  # @return [Array]
  def coordinates(element, screenshot_path, template_file_path = "something")
    # executes the script present at /usr/local/.browserstack/deps/opencv_element_coordinates/app.js packaged using
    # nix from source present at ../../scripts/opencv-element-coordinates/app.js to return screen coordinates of the passed element.
    value = OSUtils.execute("#{TIMEOUT_CMD} 60 #{NODE_14} #{BS_DIR_PATH}/deps/opencv_element_coordinates/app.js #{element} #{screenshot_path} #{template_file_path} #{@dp_density}")
    log("Coordinates recieved for #{element} are #{value.chomp.split}")
    if value.start_with?("Error")
      raise ScriptExecutionError.new("ScriptExecutionError"), value
    elsif value.chomp.eql?("unsupported element")
      raise UnsupportedElementError.new("UnsupportedElementError"), element
    end

    value.chomp.split
  end

  # @return [String]
  def screenshot_file_path
    File.join(@server_config['screenshot_dir'], "/screen_#{@uuid}.png")
  end

  # @return [String]
  def save_screenshot_to_file
    # TODO: move this to screenshot utility
    # writes to screenshot_file with device screenshot image data
    # obtains image data using idevice ScreenShotUtils primarily with fallbacks -
    # Fallback 1 - Uses idevicescreenshot when ScreenShotUtils's take_screenshot does not return image data.
    # Fallback 2 - WDA is used in rescue.
    screenshot_file = screenshot_file_path
    begin
      image_data = @ios_device.take_screenshot
      if image_data
        File.open(screenshot_file, 'wb') do |f|
          f.write image_data
        end
      else
        IdeviceUtils.screenshot(@uuid, screenshot_file)
      end
      screenshot_file

    rescue
      response = @wda_client.get_scaled_screenshot
      base_64_encoded_data = response["value"]
      File.open(screenshot_file, 'wb') do |f|
        f.write(Base64.decode64(base_64_encoded_data))
      end
      screenshot_file
    end
  end

  def log(log_line)
    BrowserStack.logger.info("#{APPLE_PAY_EXECUTOR} #{log_line}")
  end
end
