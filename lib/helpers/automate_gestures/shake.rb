require_relative '../../custom_exceptions'
require_relative '../../configuration'
require_relative '../wda_client'
require_relative '../../utils/utils'
require_relative '../../../server/device_manager'
require_relative '../../models/ios_device'
require_relative '../../utils/osutils'
require_relative '../../../config/constants'

# Executes Shake gesture event on device with the help of assistive touch.
# Instantiated upon platform call from custom executor to perform shake gesture on device.
class Shake
  def initialize(device_id, session_id, product = "")
    @uuid = device_id
    @session_id = session_id
    @product = product
    @ios_device = BrowserStack::IosDevice.new(device_id, self.class.to_s, BrowserStack.logger)
    @server_config = BrowserStack::Configuration.new.all
    @device_config = DeviceManager.device_configuration_check(@uuid)
    @wda_client = WdaClient.new(@device_config['webdriver_port'])
    @shake_gesture_data_reporter = DataReportHelper.new('shake-device',
                                                        session_id: session_id,
                                                        product: product,
                                                        ios_version: @device_config["device_version"],
                                                        device: device_id)
    # TODO: Pass from railsapp
    @devices_with_lower_dp_density = ["iPad8,12", "iPad14,6", "iPad13,19", "iPad14,4", "iPad8,10", "iPad11,6", "iPhone12,8"]
    @dp_density = @devices_with_lower_dp_density.include?(@device_config["device_name"]) ? 2 : 3
  end

  # Triggers assistive touch's shake, instruments errors and report data
  def shake_device
    success_data = {}
    failure_data = {}
    start_time = Time.now.to_i
    begin
      assistive_touch_shake_optimized
      success_data = @shake_gesture_data_reporter.populate_data_hash(
        additional_data: { "status" => "pass" },
        interval: Time.now.to_i - start_time
      )
      true # device shake gesture success
    rescue WdaClientError => e
      failure_data = @shake_gesture_data_reporter.populate_data_hash(
        wda_response: { "status" => "fail", "error" => "#{e.response_object} - #{e.message}" },
        additional_data: { "wda-client-status" => @wda_client.running? ? "running" : "non running" },
        interval: Time.now.to_i - start_time
      )
      false # device shake gesture failure
    rescue => e
      failure_data = @shake_gesture_data_reporter.populate_data_hash(
        additional_data: { "status" => "fail", "error" => "#{e.message} #{e.backtrace&.join("\n")}" },
        interval: Time.now.to_i - start_time
      )
      false # device shake gesture failure
    ensure
      @ios_device.disable_assistive_touch # disable assistive touch after shake
      data = success_data.merge!(failure_data)
      @shake_gesture_data_reporter.report(data)
    end
  end

  # @return [TrueClass]
  def assistive_touch_shake
    shake_template_file = "#{@server_config['assistive_touch_gesture_button_templates']}/shake_button.png"
    @ios_device.enable_assistive_touch # enable assistive touch using lockdown service before taking screenshot
    sleep(1) # avoid race-around of screenshot taken before assistive touch bubble appeared on screen
    @ios_device.reincarnate_assistive_touch # darkens the assistive touch bubble to improve detection
    sleep(1)
    screenshot_file = save_screenshot_to_file # saves screenshot image containing assistive touch bubble
    x, y = coordinates("assistive_touch_bubble", screenshot_file) # coordinates of assistive touch bubble
    @wda_client.tap(x, y) # tap on assistive touch bubble
    screenshot_file = save_screenshot_to_file # saves screenshot image containing assistive touch menu options
    x, y = coordinates("shake_button", screenshot_file, shake_template_file) # coordinates of shake button in assistive touch menu
    @wda_client.tap(x, y) # tap on shake option
    true # device shake gesture success
  end

  def assistive_touch_shake_optimized
    @ios_device.enable_assistive_touch # enable assistive touch using lockdown service before taking screenshot
    sleep(1)
    wda_response = @wda_client.tap_assistive_touch_element("Shake")
    raise wda_response['value']['message'] unless wda_response['value']['status'] == "pass"

    true
  end

  # @param [String] element
  # @param [String] screenshot_path
  # @param [String] template_file_path
  # @return [Array]
  def coordinates(element, screenshot_path, template_file_path = "something")
    # executes the script present at /usr/local/.browserstack/deps/opencv_element_coordinates/app.js packaged using
    # nix from source present at ../../scripts/opencv-element-coordinates/app.js to return screen coordinates of the passed element.
    value = OSUtils.execute("#{NODE_14} #{BS_DIR_PATH}/deps/opencv_element_coordinates/app.js #{element} #{screenshot_path} #{template_file_path} #{@dp_density}")
    if value.start_with?("Error")
      raise ScriptExecutionError.new("ScriptExecutionError"), value
    elsif value.chomp.eql?("unsupported element")
      raise UnsupportedElementError.new("UnsupportedElementError"), element
    end

    value.chomp.split
  end

  # @return [String]
  def screenshot_dir_path
    @server_config['screenshot_dir'] + "/screen_#{@uuid}.png"
  end

  # @return [String]
  def save_screenshot_to_file
    # TODO: move this to screenshot utility
    # writes to screenshot_file with device screenshot image data
    # obtains image data using idevice ScreenShotUtils primarily with fallbacks -
    # Fallback 1 - Uses idevicescreenshot when ScreenShotUtils's take_screenshot does not return image data.
    # Fallback 2 - WDA is used in rescue.
    screenshot_file = screenshot_dir_path
    begin
      image_data = @ios_device.take_screenshot
      if image_data
        File.open(screenshot_file, 'wb') do |f|
          f.write image_data
        end
      else
        IdeviceUtils.screenshot(@uuid, screenshot_file)
      end
      screenshot_file

    rescue
      response = @wda_client.get_scaled_screenshot
      base_64_encoded_data = response["value"]
      File.open(screenshot_file, 'wb') do |f|
        f.write(Base64.decode64(base_64_encoded_data))
      end
      screenshot_file
    end
  end
end
