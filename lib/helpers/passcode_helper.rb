require 'base64'
require_relative '../utils/ios_mdm_service_client'
require_relative '../utils/configuration_profiles_manager'
require_relative '../helpers/wda_client'
require_relative '../utils/zombie'
require_relative '../configuration'
require_relative '../custom_exceptions'
require_relative 'experiments_helper'
require_relative '../erb_binding'
require_relative '../../config/constants'
require_relative '../models/device_state'
require_relative '../utils/idevice_utils'
require_relative '../utils/custom_mdm_manager'

module Secure
  BrowserStack::Zombie.configure
  # This class includes methods to modify mdm restriction allowing passcode to be set on a device, to set the passcode itself
  # and to clear the passcode.
  class Passcode # rubocop:todo Metrics/ClassLength
    PASSCODE_MODULE_TAG = "[Passcode]"
    def initialize(device_id, session_id, product = "", feature = "")
      @udid = device_id
      @session_id = session_id
      @product = product
      @feature = feature
      @device_state = DeviceState.new(device_id)
      @server_config = BrowserStack::Configuration.new.all
      @device_config = begin
        JSON.parse(File.read(@server_config['config_json_file']))["devices"][device_id]
      rescue
        {}
      end
    end

    # It modifies the restrictions profile to allow passcode and set these restrictions using mdm command.
    # ensures state file for mdm restriction restoration is touched.
    def passcode_settings(redis_client)
      start_time = Time.now.to_i
      begin
        BrowserStack.logger.info("#{PASSCODE_MODULE_TAG} Modifying mdm restrictions to enable passcode menu in settings for device : #{@uuid} and session ID : #{@session_id}" )
        restrictions = ConfigurationProfilesManager.new(@udid, BrowserStack.logger)
        BrowserStack.logger.info("#{PASSCODE_MODULE_TAG} Composing and applying restrictions" )
        restrictions.install_profile(:restrictions, { flags: ['enable_passcode_settings'] }, install_via: :automatic)
        end_time = Time.now.to_i
        s_data = {
          status: "pass",
          time_taken: end_time - start_time
        }
        event_logger("On", "mdm-passcode-settings", s_data)
        BrowserStack.logger.info("#{PASSCODE_MODULE_TAG} Successfully enabled device Passcode settings menu on device : #{@udid} for session ID: #{@session_id}")
      rescue => e
        BrowserStack.logger.error("#{PASSCODE_MODULE_TAG} Error while removing passcode restrictions via mdm command on device- #{@udid} for session ID: #{@session_id} due to error -#{e.message} #{e.backtrace}")
        failure_data("On", "mdm-passcode-settings", start_time, "mdm-command-execution-error", e.message)
      ensure
        @device_state.touch_force_install_mdm_profiles_file
        BrowserStack.logger.info("#{PASSCODE_MODULE_TAG} Successfully touched install_mdm_profiles state file for mdm profile restoration on device : #{@udid} for session ID: #{@session_id}")
      end
    end

    # Runs XCUI automation through WDA and logs corresponding success and failure events of WDA automation.
    # and also logs this method's own execution failure event as well.
    # returns false in case of any error in method execution, true otherwise.
    # ensures state file for passcode cleanup is touched.
    def set_passcode #rubocop:todo Metrics/AbcSize
      start_time = Time.now.to_i
      @device_state.touch_passcode_file # ensures passcode cleanup using mdm command
      begin
        client = WdaClient.new(@device_config['webdriver_port'])
        res = client.set_passcode(DEVICE_PASSCODE) # returns a hash
        raise WdaAutomationError.new("unknown error"), "No payload in response" unless res['value']

        if res['value']['error'] == "no error"
          end_time = Time.now.to_i
          s_data = {
            status: "pass",
            error_reason: res['value']['error'],
            error_message: res['value']['message'],
            time_taken: end_time - start_time
          }
          event_logger("On", "wda-automation", s_data)
          BrowserStack.logger.info("#{PASSCODE_MODULE_TAG} Successfully set device passcode on device : #{@udid} for session ID: #{@session_id} to #{DEVICE_PASSCODE}")
        else
          raise WdaAutomationError.new(res['value']['error']), res['value']['message']
        end
        true
      rescue WdaAutomationError => e
        BrowserStack.logger.error("#{PASSCODE_MODULE_TAG} WDA Automation Failure while setting device Passcode on device : #{@udid} for session ID: #{@session_id} to #{DEVICE_PASSCODE}")
        failure_data("On", "wda-automation", start_time, e.response_object, e.message)
        save_screenshot(res)
        BrowserStack.logger.error("Debug Description: #{res&.dig('value', 'debugDescription')}")
        false
      rescue => e
        BrowserStack.logger.error("#{PASSCODE_MODULE_TAG} Execution Failure while setting passcode on the device- #{@udid} for session ID: #{@session_id} due to error -#{e.message} #{e.backtrace}")
        failure_data("On", "set-passcode-helper", start_time, "realmobile-method-execution-error", e.message)
        false
      end
    end

    # Clears passcode for device id using mdm command
    def clear_passcode(redis_client)
      cleanup_method = CustomMDMManager.is_custom_mdm_device?(@udid) ? "cfgutil" : "mdm"
      BrowserStack.logger.info("Starting clear device Passcode via #{cleanup_method.upcase} on device : #{@udid} for session ID : #{@session_id}")
      start_time = Time.now.to_i
      begin
        if cleanup_method == "cfgutil"
          CustomMDMManager.new(@udid, BrowserStack.logger).clear_passcode_via_unlock_token
        else
          BrowserStack::IosMdmServiceClient.clear_passcode(@udid, redis_client)
        end
        end_time = Time.now.to_i
        s_data = {
          status: "pass",
          time_taken: end_time - start_time
        }
        event_logger("Off", "#{cleanup_method}-clear-passcode", s_data)
        BrowserStack.logger.info("#{PASSCODE_MODULE_TAG} Successfully cleared device Passcode via #{cleanup_method.upcase} on device : #{@udid} for session ID: #{@session_id}")
        @device_state.remove_passcode_file
      rescue => e
        BrowserStack.logger.error("#{PASSCODE_MODULE_TAG} Error while clearing passcode via #{cleanup_method} command on device- #{@udid} for session ID: #{@session_id} due to error -#{e.message} #{e.backtrace}")
        failure_data("Off", "#{cleanup_method}-clear-passcode", start_time, "#{cleanup_method}-command-execution-error", e.message)
        raise "#{cleanup_method.upcase} Clear passcode failed"
      end
    end

    # Utility function to log events to eds and zombie for success and failure cases resp.
    def event_logger(mode, kind, data)
      event_name = "web_events"
      data_to_push = {
        event_name: "passcode-state-change",
        product: @product,
        os: IdeviceUtils.os(@udid),
        os_version: @device_config["device_version"],
        team: "device_features",
        feature: @feature
      }
      event_json = {
        session_id: @session_id,
        passcode_state_change_to: mode
      }
      event_json.merge!(data)
      data_to_push.merge!({ event_json: event_json })
      Utils.send_to_eds(data_to_push, event_name, true)
      if data[:status] == 'fail'
        BrowserStack::Zombie.push_logs("passcode-state-change",
                                       kind,
                                       { "device" => @udid,
                                         "product" => @product,
                                         "session_id" => @session_id,
                                         "os_version" => @device_config["device_version"],
                                         "data" => data[:error_message],
                                         "url" => mode,
                                         "error" => data[:error_reason] })
      end
    end

    # Utility function to compose failure data
    def failure_data(mode, kind, start_time, error_object, error_message)
      end_time = Time.now.to_i
      f_data = {
        status: "fail",
        error_reason: error_object,
        error_message: error_message,
        time_taken: end_time - start_time
      }
      event_logger(mode, kind, f_data)
    end

    def save_screenshot(response)
      screenshot_data = response&.dig('value', 'screenshot')
      return if screenshot_data.nil?

      image_data = Base64.decode64(screenshot_data)

      output_filename = File.join(@server_config['screenshot_dir'], "set_passcode_#{@udid}.png")
      File.open(output_filename, 'wb') { |file| file.write(image_data) }
    end
  end
end
