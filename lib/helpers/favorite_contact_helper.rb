require 'appium_lib'
require_relative '../custom_exceptions'
require_relative '../configuration'
require_relative 'automation'
require_relative 'data_report_helper'
require_relative 'xcui_test_helper'
require_relative '../utils/idevice_utils'
require_relative '../models/device_state'

class FavoriteContactHelper < XCUITestHelper
  def initialize(device_id, session_id, product = nil)
    super(device_id, session_id)
    @device_state = DeviceState.new(device_id)
    @uuid = device_id
    @product = product
    @session_id = session_id
    @favorite_contact_cleanup_reporter = DataReportHelper.new('fav-contacts-cleanup', session_id: session_id, product: product, device: device_id)
  end

  def cleanup
    BrowserStack.logger.info "Request for favorite Contacts cleanup"
    response = run_test(:favorite_contact_cleanup)
    @favorite_contact_cleanup_reporter.report(response)
    result = response["result"] == "success"
    @device_state.remove_favorite_contact_file if result
    result
  end
end
