require_relative '../custom_exceptions'
require_relative './wda_client'
require_relative '../models/device_state'
require_relative '../configuration'
require_relative '../utils/zombie'
require_relative '../utils/idevice_utils'

module BrowserStack
# This class change appearance to dark or light mode.
  class SwitchMode
    DARK_MODE = "Dark"
    LIGHT_MODE = "Light"

    def initialize(device_id, session_id, product = "")
      @device_state = DeviceState.new(device_id)
      @uuid = device_id
      @product = product
      @session_id = session_id
      @device_config = device_config
      @ios_version = @device_config["device_version"]
      BrowserStack::Zombie.configure
    end

    def device_config
      config = BrowserStack::Configuration.new.all
      devices = begin
        JSON.parse(File.read(config['config_json_file']))["devices"]
      rescue
        {}
      end
      devices[@uuid]
    end

    def event_logger(mode, data)
      event_name = "web_events"
      data_to_push = {
        event_name: "AppearanceChange",
        product: @product,
        os: IdeviceUtils.os(@uuid),
        os_version: @ios_version,
        team: "device_features"
      }
      event_json = {
        session_id: @session_id,
        appearance_change_to: mode
      }
      event_json.merge!(data)
      data_to_push.merge!({ event_json: event_json })
      Utils.send_to_eds(data_to_push, event_name, true)
      if data[:status] == 'fail'
        BrowserStack::Zombie.push_logs("appearance-change-failure",
                                       "WdaAutomationError",
                                       { "device" => @uuid,
                                         "product" => @product,
                                         "session_id" => @session_id,
                                         "os_version" => @ios_version,
                                         "data" => data[:error_message],
                                         "url" => mode,
                                         "error" => data[:error_reason] })
      end
    end

    def failure_data(mode, start_time, error_object, error_message)
      BrowserStack.logger.error("Error - Failed to change system appearance to #{mode} mode for session- #{@session_id} due to error - #{error_object} - #{error_message}")
      end_time = Time.now.to_i
      f_data = {
        status: "fail",
        error_reason: error_object,
        error_message: error_message,
        time_taken: end_time - start_time
      }
      event_logger(mode, f_data)
    end

    def change_appearance_to(mode) # rubocop:todo Metrics/AbcSize
      BrowserStack.logger.info("Switch to #{mode} mode via WDA on device : #{@uuid} for session ID : #{@session_id}" )
      start_time = Time.now.to_i
      begin
        @device_state.touch_settings_automation_executing_file
        client = WdaClient.new(@device_config['webdriver_port'])
        if @ios_version.to_f >= 17.0
          res = client.set_device_appearance(mode)
          raise WdaAutomationError.new(res[:body]['value']['error']), res[:body]['value']['message'] unless res[:status] == 200
        else
          res = client.switch_mode(mode)  # returns a hash
          raise WdaAutomationError.new("unknown error"), "No payload in response" unless res['value']
          raise WdaAutomationError.new(res['value']['error']), res['value']['message'] unless res['value']['error'] == "no error"
        end

        @device_state.remove_dark_mode_file if mode == LIGHT_MODE
        s_data = {
          status: "pass",
          time_taken: Time.now.to_i - start_time
        }
        event_logger(mode, s_data)

        return true
      rescue WdaAutomationError => e
        failure_data(mode, start_time, e.response_object, e.message)
        false
      rescue => e
        BrowserStack.logger.info("Error while changing appearance mode of device -#{e.message} #{e.backtrace}")
        failure_data(mode, start_time, "unknown code error", e.message)
        false
      ensure
        @device_state.remove_settings_automation_executing_file
        @device_state.touch_dark_mode_file if mode == DARK_MODE
      end
      false
    end
  end
end
