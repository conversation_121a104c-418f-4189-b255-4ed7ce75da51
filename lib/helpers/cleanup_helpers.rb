require_relative '../../config/constants'
require_relative '../../server/device_manager'
require_relative '../configuration'
require_relative '../models/device_state'
require_relative '../provisioning/provisioning_manager'
require_relative '../provisioning/ppuid_file'

# Helper methods for cleanup script
module CleanupHelpers
  def self.abort_and_request_setup!(device, cleanup_not_possible_reason = "unknown")
    DeviceManager.request_setup(device)
    DeviceManager.request_cleanup(device)
    error = "requesting device check, reason: #{cleanup_not_possible_reason}"
    DeviceManager.write_cleanup_failure_reason(device, "", error)
    exit! 0
  end

  # Cleanup is not possible when:
  # - The default xctestrun file is missing: wda/appium will fail to start
  # - The device is not supervised: any MDM command will fail
  # - The rotate certificate file exists:
  #     The device's provisioning certificate needs to be rotated
  #     and wda/ios-njb-app resigned, or they will fail to launch.
  def self.cleanup_possible?(device)
    provisioning_manager = ProvisioningManager.new(device)
    device_state = DeviceState.new(device)
    ppuid_file = PpuidFile.new(device)

    if provisioning_manager.problem_detected?

      # This file is used to trigger a provisioning profile update during
      # the high cap cert migration (see MOBPL-1109). During migration period,
      # both provisioning profiles remain valid, so we can allow the device to proceed
      # through cleanup rather than marking it offline. This helps smooth the deploy.
      if device_state.update_provisioning_profile_file_present? && ppuid_file.branch_name == 'production_60_02072022'
        BrowserStack.logger.warn('Ignoring required provisioning profile update')
        [true, '']
      else
        BrowserStack.logger.error('Cleanup not possible: provisioning profile issue detected')
        device_state.touch_needs_provisioning_file
        [false, 'provisioning profile issue detected, waiting for device check']
      end
    else
      [true, '']
    end
  end

  def self.default_xctestrun_present?(device)
    default_xctestrun = DeviceManager.default_xctestrun_file(device)
    default_xctestrun && File.exist?(default_xctestrun)
  end

  def self.offline_reason(device)
    device_config = DeviceManager.device_configuration_check(device)
    device_config["offline_reason"]
  end
end
