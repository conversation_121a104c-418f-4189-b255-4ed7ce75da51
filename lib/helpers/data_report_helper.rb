require_relative '../utils/zombie'
require_relative '../utils/idevice_utils'

# DataReportHelper is an helper for device features to send data to eds and zombie.
# For EDS: This will send data to web_events table
# For Zombie: This will send data to Pager
class DataReportHelper

  # event_name: The event name with which this is send to eds.
  # zombie_kind: The kind with which data is sent to zombie. By default this will append -failure to eds event_name
  def initialize(event_name, session_id: nil, product: nil, ios_version: nil, device: nil, zombie_kind: nil, user_id: nil)
    @event_name = event_name
    @failed_event_name = "#{@event_name}Failed"
    @zombie_kind = zombie_kind || "#{@event_name}-failure"
    @product = product
    @device = device
    @ios_version = ios_version
    @session_id = session_id
    @user_id = user_id
    BrowserStack::Zombie.configure
  end

  def self.settings_reporter(**args)
    DataReportHelper.new("iOSSettingsUpdate", zombie_kind: "iOSSettingsUpdateFailed", **args)
  end

  def self.generate_wda_xcui_fallback_reporting_data(wda_cleanup_status, wda_response_data, ios_njb_status, ios_njb_data)
    return { "status" => "fail" } if [wda_cleanup_status.to_s, wda_response_data.to_s, ios_njb_status.to_s, ios_njb_data.to_s].all?(&:empty?)

    res = {
      "status" => wda_cleanup_status || ios_njb_status ? "pass" : "fail",
      "wda_cleanup_status" => wda_cleanup_status,
      "wda_response_data" => wda_response_data,
      "ios_njb_cleanup_status" => ios_njb_status,
      "ios_njb_response_data" => ios_njb_data
    }

    BrowserStack.logger.info "[generate_wda_xcui_fallback_reporting_data] - Returning Response - #{res}"

    res
  end

  def setting_structure(settings, values: nil, extra_data: nil, failed: false)
    setting_field = failed ? :settings_update_failed : :settings_updated
    values_field = failed ? :values_update_failed : :values_updated

    {
      setting_field => settings,
      values_field => values,
      data: extra_data
    }
  end

  # report_setting_data: Report the setting data
  # settings: Array of settings which have changed
  # values: Array of values which the settings have changed to
  # extra_data: Any extra data that you want to send, specific to feature such as time_taken, errors etc..
  # zombie_error: Zombie error column. Give this non nil to send data to zombie
  # Also you can specify other args which gets directly passed to report
  def report_settings_data(settings, values: nil, extra_data: nil, zombie_error: nil, **args)
    failed = !zombie_error.nil? && zombie_error
    data = setting_structure(settings, values: values, extra_data: extra_data, failed: failed)
    failed ? report(data, event_name: "iOSSettingsUpdateFailed", zombie_error: zombie_error, **args) : report(data, **args)
  end

  # This method will send data to EDS and Zombie based of result or status variable in data
  # data: Data that needs to sent to EDS or Zombie
  # eds_only: Send only to EDS. Don't send the data to zombie irrespective of result
  # zombie_only: Send data only to zombies. Don't send to EDS
  # (DEPRECATED) force_zombie: Send data to zombies irrespective of result check. (DON'T USE THIS)
  # zombies_data: Use this to send your extra data in zombies
  # event_name: Use this if you want to override the event name
  # zombie_error: Use this to specify zombie error in cases whether data variable doesn't comply with fail check
  # zombie_kind: Pass this to overwrite existing existing zombie kind
  def report(data, eds_only: false, zombie_only: false, force_zombie: false, zombies_data: {}, event_name: nil, zombie_error: nil, zombie_kind: nil, team: "device-features")
    eds_data = {
      event_name: event_name || @event_name,
      product: @product,
      os: IdeviceUtils.os(@device),
      team: team,
      user: {
        user_id: @user_id
      },
      event_json: {
        session_id: @session_id,
        device: @device,
        **data
      }
    }
    Utils.send_to_eds(eds_data, "web_events", true) unless zombie_only

    zombies_data = zombies_data.empty? ? data : zombies_data
    result = zombies_data["result"] || zombies_data["status"]
    if zombie_error || force_zombie || (!eds_only && result.to_s.start_with?('fail'))
      BrowserStack::Zombie.push_logs(
        zombie_kind || @zombie_kind,
        zombie_error || zombies_data["error"] || data["error"], {
          "device" => @device,
          "product" => @product,
          "session_id" => @session_id,
          "os_version" => @ios_version,
          "user_id" => @user_id,
          "team" => team,
          "data" => zombies_data
        }
      )
    end
  end

  def populate_data_hash(interval:, wda_response: {}, additional_data: {}, retries: 0)
    common_data = { "time_taken" => interval, "retries" => retries }
    wda_response.merge!(additional_data).merge!(common_data)
  end

end
