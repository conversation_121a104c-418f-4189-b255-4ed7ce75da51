require_relative './automation'
require_relative '../../server/device_manager'
require_relative '../appium_server'
require_relative '../configuration'
require_relative '../custom_exceptions'
require_relative '../utils/idevice_utils'

module Automation
  class TestFlight # rubocop:todo Metrics/ClassLength
    BUNDLE_ID = "com.apple.TestFlight"

    def initialize(device_id, session_id = "")
      @device_id = device_id
      @session_id = session_id
      @config = BrowserStack::Configuration.new.all
      @appium_server = BrowserStack::AppiumServer.new(device_id)
    end

    def handle_popup_if_any(driver)
      3.times do
        alert = driver.find_element(:class, 'XCUIElementTypeAlert')

        case alert.text
        when /Would like to send you notifications/i
          driver.find_element(:name, "Don’t Allow").click # Don't is different from Don’t
        when /Apple ID Verification/
          raise AppleIDVerificationException
        else
          # Sometimes the following alert occurs on logging in:
          #   "This Apple ID is only valid for purchases in the U.S. iTunes Store. You will be switched to that Store."
          # Need to click the 'OK' button in this case.
          driver.find_element(:name, "OK").click
        end

        sleep 2
      end
    rescue Selenium::WebDriver::Error::NoSuchElementError
      log "No alert found, all good !"
      nil
    end

    # Raises an exception if login fails for any reason
    # We login into TestFlight by opening the TestFlight application and signing in into
    # the apple account using the creds present in keys.yml
    def ensure_login(expected_login = false) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
      driver = @appium_server.driver({ app: BUNDLE_ID })

      handle_popup_if_any(driver)

      begin
        driver.find_element(:name, "Sign in")
        testflight_logged_in = false
      rescue Selenium::WebDriver::Error::NoSuchElementError
        testflight_logged_in = true
      end

      if testflight_logged_in
        #TODO: Send Alert,Zombie, as in session start this should be logged out.
        log "TestFlight is already logged in, Expected Login? = #{expected_login}"

        raise "TestFlight is already logged in, was expecting it to be logged out" unless expected_login

        return
      else
        log "TestFlight is logged out, proceeding to log in."
      end

      driver.find_element(:name, "Sign in").click
      sleep 1

      begin
        driver.find_element(:name, "Use Existing Apple ID").click
        sleep 1.5
      rescue Selenium::WebDriver::Error::NoSuchElementError
        log "Existing Apple ID not found, proceeding to login."
      end

      driver.find_element(:class, "XCUIElementTypeTextField").send_keys(@config["apple_id"])
      driver.find_element(:class, "XCUIElementTypeSecureTextField").send_keys(@config["apple_password"])

      begin
        driver.find_element(:name, "Sign In").click
      rescue Selenium::WebDriver::Error::NoSuchElementError
        driver.find_element(:name, "OK").click
      end

      # Waiting for Login to finish.
      # TODO: this can be made more robust by polling for elements, but there is certain scenarios where popups can occur
      sleep 15

      handle_popup_if_any(driver)

      sleep 1

      log "Checking if terms and conditions page is visible"

      begin
        terms_and_conditions = driver.find_element(:name, "TestFlight Terms and Conditions")
        log "Found terms and conditions page"
        driver.find_element(:name, 'Accept Button').click
        sleep 2
      rescue Selenium::WebDriver::Error::NoSuchElementError
        log "Terms and conditions page not found, all good !"
      rescue
        log "Unhandled exception occurred while dealing with terms and conditions page"
      end

      log "Looking for the Redeem button"

      begin
        driver.find_element(:name, "Redeem")
        log "Found the Redeem button"
      # Login Success if Redeem button is visible
      rescue Selenium::WebDriver::Error::NoSuchElementError
        # Login Failed
        log "Unable to find the Redeem button"
        raise "Unable to find the Redeem button"
      end
    end

    # Just open the Application upon installation and handle the Notifications Popup to see the notification related settings
    def initial_launch
      driver = @appium_server.driver({ app: BUNDLE_ID })
      handle_popup_if_any(driver)
    end

    # Download the App from the <testflight_public_link>, which looks like:  https://testflight.apple.com/join/svD0JS3Z
    # Preliminary checks are done in Rails, so we can assume here that the link the valid.
    def install_app_via_public_link(testflight_public_link) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
      # Converting the link to a hard-link, so that we can skip the step of clicking in Safari.
      # Opening this hard link in Safari, will open TestFlight for downloading user's App.
      testflight_app_hardlink = testflight_public_link.gsub("https", "itms-beta")

      begin
        ensure_login
      rescue => e
        log "TestFlight login failed, cannot proceed. Reason: #{e.message}"
        raise "TestFlight login failed, cannot proceed. Reason: #{e.message}"
      end

      log "Starting Safari with Appium Driver to trigger App download in TestFlight."

      caps = {
        browserName: 'Safari',
        safariInitialUrl: 'http://mobile-internet-check.browserstack.com',
        webkitResponseTimeout: 30000
      }

      driver = @appium_server.driver(caps)
      safari_driver = driver.start_driver

      sleep 1

      safari_driver.get(testflight_app_hardlink)

      # Change context to interact with the App.
      driver.set_context("NATIVE_APP")

      # Sleep to ensure transition from Safari -> TestFlight completes.
      sleep 1

      # Now the TestFlight App displays a page for the App with various details.
      # We need to click on the install button to trigger download.
      install_button = driver.find_elements(:class, "XCUIElementTypeButton")[2]

      if install_button.nil?
        log "Unable to find the button to accept/install/open App, aborting. Check if TestFlight is logged in or not."
        raise "Unable to find the button to accept/install/open App, aborting. Check if TestFlight is logged in or not."
      end

      # Cache this location because we need to repeatedly poll it to figure out if download has completed or not.
      install_button_location = install_button.location

      # Cache a click action (does not actually perform it)
      click_action = Appium::TouchAction.new(driver).press(x: install_button_location.x, y: install_button_location.y).wait(1).release

      # If any of these buttons are present, click it
      if ["INSTALL", "Accept invite"].include?(install_button.text)
        # Click INSTALL/ACCEPT button
        click_action.perform
        sleep 1
      end

      # Intentionally written again
      if ["INSTALL", "Accept invite"].include?(install_button.text)
        # Click INSTALL/ACCEPT button
        click_action.perform
        sleep 1
      end

      log "Downloading and Installing the App."

      polls = 1

      # Poll for App download to complete.
      # TODO:
      # Move poll times to config.
      100.times do
        # The text field has the download progress. i.e. something like '56% downloaded'
        current_download_state = install_button.text

        # If button displays "OPEN", that means that the download (+installation) is finished.
        case current_download_state
        when "OPEN"
          break
        when /^90/
          # Once download progress reaches 90%, TestFlight proceeds to installing the App. There is no way to get the installation progress.
          log "Download completed, waiting for installation to finish."
        else
          log "Download Progress: #{current_download_state}"
        end

        polls += 1
        sleep 1
      end

      # Ensure that 'OPEN' is displayed
      if install_button.text == "OPEN"
        log "App Installed Successfully, number of polls: #{polls}"
      else
        log "App Install Failed, current button state is '#{install_button.text}', was expecting it to be 'OPEN'"
        raise "App Install Failed, current button state is '#{install_button.text}', was expecting it to be 'OPEN'."
      end

      driver.driver_quit
    rescue => e
      log "App Installation Failed, Uncaught Exception: #{e.message} #{e.backtrace.join("\n")}"
      # Bubble the exception.
      raise e
    end

    def disable_notifications
      log "Removing lock screen TestFlight notifications in settings"

      driver = Automation.open_settings_app(@device_id)
      handle_popup_if_any(driver)

      log "Scroll to TestFlight and click"
      element = driver.find_element(:name, "TestFlight")
      driver.execute_script('mobile: scroll', { "element": element.ref, "toVisible": true })
      element.click

      log "Clicking on TestFlight notifications"

      elements = driver.find_elements(:xpath, "//*[contains(@name,'Notifications')]")

      if elements.empty?
        log "Notifications option not available"
        return
      else
        elements[-1].click
      end

      toggle = driver.find_element(:name, "Allow Notifications")
      if toggle.value == "1"
        log "Disabling notifications"
        toggle.click
        sleep 2
      else
        log "Notifications already disabled"
      end
    rescue => e
      CleanupAutomationError.new(e.message)
    end

    def uninstall
      IdeviceUtils.uninstall_app(@device_id, BUNDLE_ID)
    end

    def log(message)
      log_message = if @session_id.empty?
                      "TestFlight-Automation: #{message}"
                    else
                      "SessionID: #{@session_id} - #{message}"
                    end

      if defined?(BrowserStack.logger.info)
        BrowserStack.logger.info(log_message)
      else
        puts log_message
      end
    end
  end
end
