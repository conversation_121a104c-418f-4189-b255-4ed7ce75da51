require_relative "../utils/utils"
require_relative "../utils/idevice_file_utils"
require_relative "../utils/osutils"
require_relative "xcui_test_helper"
require "fileutils"

DOWNLOAD_FILES_MESSAGES = {
  success: "DOWNLOAD_FILES_SUCCESS",
  max_size_limit_error: "DOWNLOAD_LIMIT_EXCEEDED",
  files_not_available: "DOWNLOAD_FILES_NOT_AVAILABLE",
  failed: "DOWNLOAD_FILES_FAILED"
}.freeze

class DownloadFileHelper < XCUITestHelper # rubocop:todo Metrics/ClassLength
  def initialize(device_id, session_id, product = "")
    super(device_id, session_id)
    @uuid = device_id
    @product = product
    @session_id = session_id
    @device_config = DeviceManager.device_configuration_check(@uuid)
    @ios_version = @device_config["device_version"]
    @device_state = DeviceState.new(device_id)
    @server_config = Configuration.new.all
    @download_file_data_reporter = DataReportHelper.new("download-file", session_id: session_id, product: product, ios_version: @ios_version, device: device_id)
    @download_files_zip_path = Utils.get_download_files_zip_path(@uuid)
    @download_files_dir_path = Utils.get_download_files_dir_path(@uuid)
    @preloaded_media_list_file_path = Utils.get_list_of_preloaded_media_path(@uuid)
  end

  def generate(download_file_path, params)
    log(:info, "Called generate method with params: #{params}")

    IdeviceFileUtils.pull_folder(@uuid, "/Documents", CHROME_BUNDLE_ID, "#{@download_files_dir_path}/chrome")
    log(:info, "Successfully fetched chrome download files")

    IdeviceFileUtils.pull_all_photos(@uuid, @download_files_dir_path)
    log(:info, "Successfully fetched all photos")

    download_folders = %w[chrome DCIM]
    Utils.create_zip(@download_files_zip_path, @download_files_dir_path, download_folders, list_preloaded_media)
    log(:info, "Successfully zipped downloaded files")

    files_size = begin
      OSUtils.execute("unzip -l #{@download_files_zip_path} | tail -1").split(" ")[0].to_i
    rescue
      nil
    end

    if files_size.nil? || files_size == 0
      log(:info, "No downloaded user files available at #{@download_files_zip_path}.")
      return { status: "failed", message: DOWNLOAD_FILES_MESSAGES[:files_not_available], file_size: 0 }
    end

    # check for file size
    max_files_size = params["max_files_size"] || DEFAULT_DOWNLOAD_FILE_SIZE_LIMIT
    log(:info, "Checking if file size is greater than #{max_files_size}")
    if files_size.to_f / 1000000.0 > max_files_size.to_f
      log(:info, "File size of downloaded files (#{files_size}) is greater than allowed file size #{max_files_size}")
      { status: "failed", message: DOWNLOAD_FILES_MESSAGES[:max_size_limit_error], file_size: files_size }
    else
      log(:info, "Downloaded user files available at #{@download_files_zip_path}.")
      { status: "success", message: DOWNLOAD_FILES_MESSAGES[:success], file_size: files_size }
    end
  rescue => e
    log(:error, "Failed to generate download file zip #{e.message} #{e.backtrace.join("\n")}")
    { status: "failed", message: DOWNLOAD_FILES_MESSAGES[:failed], file_size: 0 }
  end

  def upload(params, download_zip_path, metadata = {})
    log(:info, "Called upload method with params:#{params}")
    # get s3 config from params
    params["s3_config"] = JSON.parse(params["s3_config"])

    if params["should_use_presigned_url"].to_s == 'true'
      # upload to s3 via presigned url
      Utils.upload_zip_to_s3_via_presigned_url(download_zip_path, params["s3_config"]["upload_url"])

      # return the get presigned url
      return params["s3_config"]["download_url"]
    end

    aws_region = "-#{params['s3_config']['s3_region']}"
    region = (params["s3_config"]["s3_region"]).to_s
    bucket = params[:s3_config]['s3_bucket']

    if bucket == "bs-stag" || region == "us-east-1"
      aws_region = ""
      region = nil
    end
    resource_path = params[:s3bucket_endpoint] || "data/#{@session_id}/#{@session_id}-download_files.zip"
    s3_url = "https://s3#{aws_region}.amazonaws.com/#{params[:s3_config]['s3_bucket']}/#{resource_path}"
    log(:info, "Region is #{region}")
    params["s3_config"]["s3_region"] = region
    # The downloaded files would get deleted after 24 hrs from s3
    ret, error = Utils.upload_file_to_s3(params["s3_config"]["s3_access_keyId"], params["s3_config"]["s3_secret_access_key"], "application/zip", download_zip_path, "private", s3_url, @session_id, params["genre"], region, 300, metadata, metadata)
    unless ret || error.empty?
      log(:error, "Failed to upload file to s3 for params: #{params}, error: #{error}")
      raise "Error while uploading downloaded file zip to S3: #{error}"
    end
    log(:info, "Successfully uploaded downloaded file zip to s3 with params:#{params}")
    Utils.get_presigned_url(resource_path, params["s3_config"], 604800)
  end

  def one_time_setup
    start_time = Time.now.to_i
    result = "success"
    # Run change default download location perform only for version >=13, as for versions below it, there is no option to set a default downloads location
    if @ios_version.to_f >= 13
      FileUtils.mkdir_p(@download_files_dir_path)
      FileUtils.touch("#{@download_files_dir_path}/sample")
      IdeviceFileUtils.add_file(@uuid, "#{@download_files_dir_path}/sample", "Documents/sample", CHROME_BUNDLE_ID)
      run_test_result = run_test(:change_safari_default_download_location)
      if run_test_result["result"] != "success"
        result = 'failed'
        error = "SafariDefaultDownloadSetupFailed-#{run_test_result['error']}"
        error_message = run_test_result["error_message"]
        log(:error, "Failed to run one time setup: #{error} #{error_message}")
      end
    else
      log(:info, "Changing safari default download location is not run for version < 13")
    end
    @device_state.touch_download_files_setup_done_file if result == "success"
    result == "success"
  rescue => e
    result = 'failed'
    log(:error, "Failed to run one time setup: #{e.message} #{e.backtrace.join("\n")}")
    error = "UnknownError"
    error_message = e.message
    false
  ensure
    time_taken = Time.now.to_i - start_time
    # we send only the first 100 characters of the error message to zombies
    @download_file_data_reporter.report({ "result" => result, "time_taken" => time_taken, "error" => error, "error_message" => error_message ? error_message[0, 100] : nil })
    FileUtils.rm_rf(@download_files_dir_path)
  end

  private

  def list_preloaded_media
    log(:info, "Checking the list of preloaded media at: #{@preloaded_media_list_file_path}")
    preloaded_media_list = File.read(@preloaded_media_list_file_path).split(" ")
    preloaded_media_list << "/chrome/Documents/sample"
    log(:info, "Found the following preloaded media: #{preloaded_media_list}")
    preloaded_media_list
  end

  def log(level, message)
    logger_params = { subcomponent: DOWNLOAD_FILE_TAG, device: @uuid, session_id: @session_id }
    BrowserStack.logger.send(level.to_sym, message, logger_params)
  end
end
