require_relative '../custom_exceptions'
require_relative './wda_client'
require_relative '../models/device_state'
require_relative '../configuration'
require_relative '../utils/zombie'
require_relative '../utils/idevice_utils'

module BrowserStack
# This class toggles prevent cross-site tracking to enable or disable.
  class PreventCrossSiteTracking
    ENABLE = "enable"
    DISABLE = "disable"

    def initialize(device_id, session_id, product = "")
      @device_state = DeviceState.new(device_id)
      @uuid = device_id
      @product = product
      @session_id = session_id
      @device_config = device_config
      @ios_version = @device_config["device_version"]
      BrowserStack::Zombie.configure
    end

    def device_config
      config = BrowserStack::Configuration.new.all
      devices = begin
        JSON.parse(File.read(config['config_json_file']))["devices"]
      rescue
        BrowserStack.logger.info("Failed to parse config_json_file on device : #{@uuid} for session ID : #{@session_id}, thus returning empty json" )
        {}
      end
      devices[@uuid]
    end

    def event_logger(toggle, data)
      event_name = "web_events"
      data_to_push = {
        event_name: "PreventCrossSiteTrackingToggle",
        product: @product,
        os: IdeviceUtils.os(@uuid),
        os_version: @ios_version,
        team: "device_features"
      }
      event_json = {
        session_id: @session_id,
        appearance_change_to: toggle
      }
      event_json.merge!(data)
      data_to_push.merge!({ event_json: event_json })
      Utils.send_to_eds(data_to_push, event_name, true)
      if data[:status] == 'fail'
        BrowserStack::Zombie.push_logs("prevent-cross-site-tracking-toggle-failure",
                                       "WdaAutomationError",
                                       { "device" => @uuid,
                                         "product" => @product,
                                         "session_id" => @session_id,
                                         "os_version" => @ios_version,
                                         "data" => data[:error_message],
                                         "url" => toggle,
                                         "error" => data[:error_reason] })
      end
    end

    def failure_data(toggle, start_time, error_object, error_message)
      BrowserStack.logger.error("Error - Failed to toggle prevent cross-site tracking to #{toggle} toggle for session- #{@session_id} due to error - #{error_object} - #{error_message}")
      end_time = Time.now.to_i
      f_data = {
        status: "fail",
        error_reason: error_object,
        error_message: error_message,
        time_taken: end_time - start_time
      }
      event_logger(toggle, f_data)
    end

    def switch(value)
      toggle = value
      @device_state.touch_prevent_cross_site_tracking_automation_running_file
      BrowserStack.logger.info("Switch to #{toggle} toggle via WDA on device : #{@uuid} for session ID : #{@session_id}" )
      start_time = Time.now.to_i
      begin
        client = WdaClient.new(@device_config['webdriver_port'])
        res = client.switch_prevent_cross_site_tracking(toggle)  # returns a hash
        raise WdaAutomationError.new("unknown error"), "No payload in response" unless res['value']

        @device_state.remove_prevent_cross_site_tracking_automation_running_file
        return validate_response(res, toggle, start_time)
      rescue WdaAutomationError => e
        failure_data(toggle, start_time, e.response_object, e.message)
      rescue => e
        BrowserStack.logger.info("Error while #{toggle} prevent cross-site tracking -#{e.message} #{e.backtrace}")
        failure_data(toggle, start_time, "unknown code error", e.message)
      end
      @device_state.remove_prevent_cross_site_tracking_automation_running_file
      false
    end

    def validate_response(res, toggle, start_time)
      prevent_cross_site_tracking_disabled_file(toggle, res['value']['error'])
      if res['value']['error'].nil?
        end_time = Time.now.to_i
        s_data = {
          status: "pass",
          error_reason: res['value']['error'],
          error_message: res['value']['message'],
          time_taken: end_time - start_time
        }
        event_logger(toggle, s_data)
      else
        raise WdaAutomationError.new(res['value']['error']), res['value']['message']
      end
      true
    end

    def prevent_cross_site_tracking_disabled_file(toggle, error)
      @device_state.touch_prevent_cross_site_tracking_disabled_file if toggle == DISABLE && (error.nil? || error == 'already disabled')
      @device_state.remove_prevent_cross_site_tracking_disabled_file if toggle == ENABLE && (error.nil? || error == 'already enabled')
    end
  end
end
