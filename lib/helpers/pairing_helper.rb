require_relative '../configuration'
require_relative '../device_conf'
require_relative '../utils/pymobiledevice'
require_relative '../models/ios_device'
require_relative '../utils/idevice_utils'
require_relative '../utils/devicectl'
require_relative '../utils/wda_version'
require_relative '../utils/osutils'
require_relative '../utils/iproxy'
require_relative '../utils/utils'
require_relative 'data_report_helper'
require_relative 'wda_client'

class PairingHelper # rubocop:todo Metrics/ClassLength
  def initialize(device_id, logger)
    @device_id = device_id
    @logger = logger
  end

  def ensure_pair
    start_time = Time.now.to_i
    retries ||= 0
    log(:info, "Ensure Pairing device, retries: #{retries} ...")

    was_lockdown_pairing_required = ensure_lockdown_pairing

    return if device_config['device_version'].to_i < 17

    was_remoted_pairing_required = ensure_remoted_pairing

    log(:info, "Ensured pairing device, retries: #{retries} ...")
    result = "success"
    true
  rescue => e
    log(:error, "Failed to ensure pairing, error: #{e.message}, #{e.backtrace.join("\n")}")
    retries += 1
    retry if retries < 3
    error = e
    result = "failed"
    raise e
  ensure
    if was_lockdown_pairing_required || was_remoted_pairing_required || error
      data_reporter.report({
        "result" => result,
        "time_taken" => Time.now.to_i - start_time,
        "was_lockdown_pairing_required" => was_lockdown_pairing_required,
        "was_remoted_pairing_required" => was_remoted_pairing_required,
        "error" => error&.message
      })
    end
  end

  def ensure_lockdown_pairing
    return false if IdeviceUtils.idevicepair(@device_id, "validate").start_with?("SUCCESS")

    log(:info, "Device not paired on lockdown, checking if device is supervised with known supervision identities")
    ios_device.supervised_pair

    raise "Failed to pair device on lockdown" unless IdeviceUtils.idevicepair(@device_id, "validate").start_with?("SUCCESS")

    true
  end

  def ensure_remoted_pairing
    already_paired = DeviceCtl::Device.paired?(@device_id)
    return false if already_paired

    log(:info, "Device not paired on remoted, checking if device is >= 17.4 and can use lockdown service to create remotexpc tunnel")
    raise "Can't pair the device, since iOS version < 17.4" unless device_config["device_version"].to_f >= 17.4

    # Enable Developer Mode
    enable_developer_mode
    # Mount Developer Disk Image
    auto_mount_developer_image
    # Start Lockdown Tunnel
    rsd_address, rsd_port = start_lockdown_tunnel
    # Install WDA
    install_wda
    # kill_xcodebuild_and_iproxy
    kill_iproxy
    # Launch WDA
    launch_wda(rsd_address, rsd_port)
    # Run iproxy
    start_iproxy
    # Ensure WDA running
    ensure_wda_running
    # Unlock device
    unlock_device
    # Launch sample app to ensure unlocking the device
    PyMobileDevice::Developer.launch(@device_id, "com.apple.Preferences", rsd_address, rsd_port)
    # Start pairing
    pairing_pid = trigger_pairing
    # Handle trust popup
    handle_trust_popup

    raise "Failed to trust remoted popup" unless DeviceCtl::Device.paired?(@device_id)

    true
  rescue => e
    log(:error, "error: #{e.message}, #{e.backtrace.join("\n")}")
    raise e
  ensure
    unless already_paired
      # kill_tunnel
      stop_lockdown_tunnel
      # kill iproxy
      kill_iproxy
      # kill xcuitest
      stop_xcuitest(rsd_address, rsd_port)
      # kill pairing process
      Process.kill('KILL', pairing_pid.to_i) if !pairing_pid.nil? && Utils.process_running?(pairing_pid)
    end
  end

  private

  def log(level, message)
    @logger.send(level.to_sym, "[PairingHelper] #{message}")
  end

  def data_reporter
    @data_reporter ||= DataReportHelper.new("pairing-helper", device: @device_id)
  end

  def server_config
    @server_config ||= BrowserStack::Configuration.new.all
  end

  def device_config
    @device_config ||= BrowserStack::DeviceConf[@device_id]
  end

  def ios_device
    @ios_device ||= BrowserStack::IosDevice.new(@device_id, self.class.to_s, BrowserStack.logger)
  end

  def lockdown_tunnel_file_path
    "/tmp/lockdown_tunnel_#{@device_id}.json"
  end

  def enable_developer_mode
    result, status = BrowserStack::OSUtils.execute("xcrun devmodectl single #{@device_id}", true)
    raise "Failed to enable developer mode" unless status == 0
  end

  def auto_mount_developer_image
    PyMobileDevice::Mounter.auto_mount(@device_id)
  end

  def start_lockdown_tunnel
    log(:info, "starting lockdown tunnel")
    stdin, stdout = PyMobileDevice::Lockdown.start_tunnel(@device_id)
    log(:info, "started, #{stdout}")
    stdin.close

    pid = Process.fork do
      tunnel_created = false
      stdout.each_line do |line|
        if tunnel_created
          log(:info, "Tunnel created")
          rsd_address, rsd_port = line.split
          log(:info, "RSD details: #{rsd_address}, #{rsd_port}")
          Utils.write_to_file(lockdown_tunnel_file_path, { rsd_address: rsd_address, rsd_port: rsd_port }.to_json)
          break
        end
        tunnel_created = true if line.include?("tunnel created")
      end
    end
    Process.detach(pid)

    rsd_address = nil
    rsd_port = nil
    retry_count = 1
    loop do
      if File.exists?(lockdown_tunnel_file_path)
        rsd_json = Utils.read_json_file(lockdown_tunnel_file_path)
        rsd_address = rsd_json["rsd_address"]
        rsd_port = rsd_json["rsd_port"]
      end
      break if rsd_address && rsd_port
      break if retry_count > 2

      sleep 5
      retry_count += 1
    end
    log(:info, "RSD: #{rsd_address}, #{rsd_port}")
    raise "Lockdown tunnel can't be created" if rsd_address.nil? || rsd_port.nil?

    [rsd_address, rsd_port]
  ensure
    FileUtils.rm_rf(lockdown_tunnel_file_path)
    Process.kill('KILL', pid.to_i) if !pid.nil? && Utils.process_running?(pid)
  end

  def stop_lockdown_tunnel
    `ps aux | grep pymobiledevice3 | grep 'lockdown start-tunnel' | grep #{@device_id} | awk '{print $2}' | xargs sudo kill`
  end

  def stop_xcuitest(rsd_address, rsd_port)
    `ps aux | grep pymobiledevice3 | grep 'developer dvt xcuitest' | grep #{rsd_address} | grep #{rsd_port} | awk '{print $2}' | xargs sudo kill`
  end

  def install_wda
    wda_app_folder = wda_version.wda_app_folder
    log(:info, "WDA app folder: #{wda_app_folder}")
    wda_app_name = wda_app_folder.split("/").last
    tmp_wda_app_folder = "/tmp/#{@device_id}/"
    FileUtils.mkdir_p(tmp_wda_app_folder)
    FileUtils.cp_r(wda_app_folder, tmp_wda_app_folder)
    directory = File.join(tmp_wda_app_folder, "#{wda_app_name}/Frameworks/")
    pattern = "*"
    files = Dir.glob(File.join(directory, pattern))

    # Iterate over the files and delete each one
    files.each do |file|
      FileUtils.rm_rf(file)
      log(:info, "Deleted: #{file}")
    end

    IdeviceUtils.install_app(@device_id, File.join(tmp_wda_app_folder, wda_app_name), avoid_devicectl: true)
    raise "Failed to install WDA" unless $CHILD_STATUS.exitstatus == 0
  end

  def uninstall_wda
    IdeviceUtils.uninstall_app(@device_id,  WDAVersion::BUNDLE_ID, force_skip_device_ctl: true)
  end

  def launch_wda(rsd_address, rsd_port)
    stdin, stdout = PyMobileDevice::Developer.run_xcui_test(@device_id, WDAVersion::BUNDLE_ID, rsd_address, rsd_port)
    stdin.close

    log(:info, "Launching WDA stdout: #{stdout}")

    pid = Process.fork do
      Timeout.timeout(30) do
        log(:info, "Launching WDA")
        stdout.each_line do |line|
          log(:info, line)
        end
      end
    rescue Timeout::Error
      log(:warn, "WDA launch timeout - Expected")
    end
    Process.detach(pid)
  end

  def kill_iproxy
    BrowserStack::OSUtils.kill_process("iproxy", @device_id.to_s)
  end

  def start_iproxy
    Iproxy.start(@device_id, device_config['webdriver_port'].to_i, '8100')
  end

  def ensure_wda_running
    retries ||= 0
    raise "WDA not running" unless wda_client.running?
  rescue => e
    retries += 1
    if retries < 5
      sleep 3
      retry
    end
    raise e
  end

  def unlock_device
    wda_client.unlock_device
  end

  def trigger_pairing
    pid = Process.fork do
      DeviceCtl::Manage.pair(@device_id)
    end
    Process.detach(pid)
    pid
  end

  def handle_trust_popup
    response = wda_client.trust_popup
    save_screenshot(response["value"]["screenshot"], "handle_trust_popup_ss") if response["value"]["status"] != "pass" && response["value"]["screenshot"]
    raise "Failed to handle trust popup" unless response["value"]["status"] == "pass"
  end

  def wda_version
    @wda_version ||= WDAVersion.new(@device_id, server_config['default_wda_version'], device_config['device_version'].to_f)
  end

  def wda_client
    @wda_client ||= WdaClient.new(device_config["webdriver_port"].to_i)
  end

  def save_screenshot(screenshot, name)
    image_data = Base64.decode64(screenshot)
    output_filename = File.join(server_config['screenshot_dir'], "#{name}_#{@device_id}.png")
    File.open(output_filename, 'wb') do |file|
      file.write(image_data)
    end
  rescue => e
    log(:error, "Unable to save screenshot. Error: #{e}")
  end
end
