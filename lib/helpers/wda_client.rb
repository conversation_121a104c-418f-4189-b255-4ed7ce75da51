require 'json'
require 'faraday'
require_relative '../ios_influxdb_client'
require_relative '../utils/utils'
require_relative '../utils/time_recorder'
require_relative '../utils/http_utils'
require_relative '../../config/constants'
require_relative '../custom_exceptions'

# This class wraps calls to WDA and abstracts the endpoints to functions
class WdaClient # rubocop:todo Metrics/ClassLength
  include BrowserStack::TimeRecorder

  WDA_AUTH_PASS = begin
    JSON.parse(File.read(STATIC_CONF_PATH))["wda_auth_pass"]
  rescue
    ""
  end
  LOG_REDACTED_ENDPOINTS = ["get_snapshot_details", "scaledDownScreenshot"]

  time_methods :check_orientation,
               :clean_keychain,
               :connect_to_peer,
               :drag,
               :drag_with_tap_duration,
               :homescreen,
               :kill_apps,
               :launch_app,
               :launch_app_with_app_display_name,
               :launch_app_with_bundle_id,
               :launch_apps_with_locale,
               :launch_browser_with_url,
               :lock_device,
               :set_orientation,
               :set_rtc_data_and_start,
               :stop_live_session,
               :snapshot_details,
               :stop_live_session,
               :tap,
               :unlock_device,
               :open_settings,
               :trust_popup

  def initialize(wda_port, device_id = Thread.current[:device_id])
    @wda_port = wda_port
    @device_id = device_id
    @influxdb_client = BrowserStack::IosInfluxdbClient.new
  end

  def status(timeout: 5)
    endpoint = "/status"
    make_request("GET", endpoint, {}, timeout)
  end

  def running?
    begin
      wda_response = status
    rescue => e
      log :error, "WDA not running. Error getting response: #{e.message}"
      return false
    end

    begin
      wda_response['value']['state'] == 'success'
    rescue => e
      log :error, "WDA not running. WDA status response: #{wda_response}"
      false
    end
  end

  def cached_running?
    status_cache_file = "/tmp/wda_status_#{@device_id}.txt"
    last_updated_time_raw, wda_running_raw = (File.exists?(status_cache_file) ? File.open(status_cache_file, &:readline) : "0,false").split(",")
    last_updated_time = last_updated_time_raw.to_i  # to_i returns 0 for inavlid integers
    wda_running = wda_running_raw.to_s == "true"
    if Time.now.to_i - last_updated_time > 10 # Get & set current status if cached status is 10 seconds old
      wda_running = running?
      File.open(status_cache_file, "w") { |f| f.write "#{Time.now.to_i},#{wda_running}" }
    end
    wda_running
  rescue => e
    BrowserStack.logger.error("Exception in /cached_running? for #{@device_id}: #{e.message} \n#{e.backtrace.join("\n")}")
    running?
  end

  def describe_running
    begin
      wda_response = status
    rescue => e
      return false, "WDA not running. Error getting response: #{e.message}"
    end

    begin
      wda_running = wda_response['value']['state'] == 'success'
      [wda_running, wda_running == true ? "wda success state" : "wda failed state"]
    rescue => e
      [false, "WDA not running. WDA status response: #{wda_response}"]
    end
  end

  def redact_endpoint?(endpoint)
    LOG_REDACTED_ENDPOINTS.any? { |redacted_endpoint| endpoint.include?(redacted_endpoint) }
  end

  def tap(x, y)
    endpoint = '/bs/tap'
    params = { x: x, y: y }
    response = make_request('POST', endpoint, params)
    raise "Failed to Tap at #{x} #{y}" if (response.key?('status') && response['status'] != 0) || (!response.key?('status') && !response['value'].nil?)
  end

  def lock_device(ios_version)
    # NJB:  iOS < 12 uses appium 1.7.0, responds to endpoint GET /lock
    #       iOS >= 12 uses appium 1.9.1, responds to endpoint POST :session_id: /wda/lock

    case ios_version.to_i
    when 10, 11
      make_request('GET', '/lock')
    else
      make_request('POST', '/wda/lock')
    end

    log :info, "Locking, via wda_client.rb - lock_device"
  end

  def clean_keychain
    endpoint = '/bs/clean_keychain'
    make_request('GET', endpoint)
  end

  def unlock_device
    endpoint = '/bs/unlock_device'
    log :info, "Unlocking, via wda_client.rb - unlock_device"
    make_request('GET', endpoint)
  end

  def trust_popup
    endpoint = '/bs/trust_popup'
    log :info, "Trusting popup, via wda_client.rb"
    make_request('POST', endpoint)
  end

  def open_settings(launch_url, app_display_name, app_live_settings_v3="false")
    endpoint = '/bs/open_settings'
    request = if launch_url.nil?
                { "app_name": app_display_name, "app_live_settings_v3": app_live_settings_v3 }
              else
                { "settings_bundle_url": launch_url, "app_name": app_display_name, "app_live_settings_v3": app_live_settings_v3 }
              end
    log :info, "opening settings, via wda_client.rb - open_settings for url: #{launch_url} app:#{app_display_name}"
    make_request('POST', endpoint, request)
  end

  def get_settings_pages_visited
    endpoint = '/bs/get_all_settings_page_visited'
    log :info, "get_settings_pages_visited, via wda_client.rb - Settings page for url"
    make_request('GET', endpoint)
  end

  # Scrolls through homescreen until it finds the application and then taps on it.
  def launch_app_with_app_display_name(app_display_name)
    endpoint = '/bs/launchapp'
    make_request('POST', endpoint, { displayName: app_display_name, strictLaunch: true })
  end

  # Launches app directly by bundle id.
  # This endpoint does not exist on bstack_1.7.0_njb (default appium for ios 10, 11)
  def launch_app_with_bundle_id(bundle_id)
    endpoint = '/bs/live/launch_app_from_bundle_id'
    make_request('POST', endpoint, { app_bundle_id: bundle_id })
  end

  def homescreen(timeout: 5)
    endpoint = '/wda/homescreen'
    make_request('POST', endpoint, timeout: timeout)
  end

  def drag(fromX, fromY, toX, toY)
    endpoint = '/bs/dragFromTo'
    make_request('POST', endpoint, { fromX: fromX, fromY: fromY, toX: toX, toY: toY })
  end

  def open_control_centre(duration)
    endpoint = "/bs/dragControlCenterDuration"
    make_request('POST', endpoint, { duration: duration })
  end

  def dismiss_control_centre
    endpoint = '/bs/ensure_homescreen'
    make_request('POST', endpoint, {})
  end

  def drag_with_tap_duration(from_x, from_y, to_x, to_y, duration)
    # Tap for the duration specified and then start dragging
    endpoint = '/bs/dragCoordinateDuration'
    make_request('POST', endpoint, { fromX: from_x, fromY: from_y, toX: to_x, toY: to_y, duration: duration })
  end

  def check_orientation(lockValue)
    endpoint = '/bs/orientationchecker'
    make_request('POST', endpoint, { lockValue: lockValue })
  end

  def kill_apps(app_list)
    endpoint = '/bs/killapps'
    make_request('POST', endpoint, { apps: app_list })
  end

  def device_ip(timeout: 60)
    endpoint = '/bs/get_device_ip'
    make_request('GET', endpoint, {}, timeout)
  end

  # Get app currently in foreground
  def foreground
    endpoint = '/bs/foreground'
    wda_response = make_request('GET', endpoint)
    wda_response['value']['app']
  end

  def clear_clipboard
    endpoint = '/bs/clear_clipboard'
    wda_response = make_request('POST', endpoint, {})
  end

  def get_clipboard
    endpoint = '/wda/getPasteboard'
    make_request('POST', endpoint, {})
  end

  # Set app in foreground
  def set_foreground(bundle_id, app_name, dsl, session_id)
    endpoint = '/bs/forgroundApp'
    make_request('POST', endpoint, { "app_bundle_id" => bundle_id, "session_id" => session_id,
                                     "app_name" => app_name, "DSL" => dsl })
  end

  # Enables interactions in the settings app
  # If not enabled, any interaction will press the home button instead.
  def settings_access(enable = false, dedicated_cloud_device = false, custom_mdm_enabled = false, force_access = false)
    endpoint = '/bs/settings'
    make_request('POST', endpoint, { enabled: enable, dedicated_cloud_device: dedicated_cloud_device, custom_mdm_enabled: custom_mdm_enabled, force_access: force_access, auth: WdaClient::WDA_AUTH_PASS })
  end

  def get_wifi_status(timeout: 60)
    endpoint = '/bs/get_wifi_status'
    make_request('GET', endpoint, {}, timeout)
  end

  def internet_check(timeout)
    endpoint = '/bs/internet_check'
    make_request('GET', endpoint, {}, timeout)
  end

  def launch_browser_with_url(url, browser, ios_version)
    endpoint = '/bs/live/launch_browser_with_url'
    make_request('POST', endpoint, { launch_url: url, device_browser: browser, ios_version: ios_version })
  end

  def snapshot_details(inner_timeout, request_timeout)
    endpoint = "/bs/get_snapshot_details?timeout=#{inner_timeout}"
    make_request('GET', endpoint, {}, request_timeout)
  end

  def set_orientation(orientation)
    endpoint = '/bs/setOrientation'
    make_request('POST', endpoint, { orientation: orientation })
  end

  def get_orientation
    endpoint = '/bs/getOrientation'
    make_request('GET', endpoint)
  end

  def set_date_time_automatic(enabled)
    value = enabled ? "ON" : "OFF"
    endpoint = "/bs/setAutomaticDateTime"
    make_request('POST', endpoint, { toggle: value })
  end

  def set_device_time(time)
    endpoint = "/bs/change_time"
    make_request('POST', endpoint, { time: time })
  end

  def set_device_time_format(time_format = TWENTY_FOUR_HOUR_TIME_FORMAT)
    endpoint = "/bs/time_format"
    make_request('POST', endpoint, { time_format: time_format })
  end

  def set_device_date_upto_7days(date)
    endpoint = "/bs/change_date_upto_7days"
    make_request('POST', endpoint, { day: date["day"], month: date["month"], year: date["year"] })
  end

  def launch_apps_with_locale(list_apps, locale, region, pause="no", set_browserstack_env="no", timeout = 300)
    endpoint = '/bs/launchapps_with_locale'
    make_request('POST', endpoint, { apps: list_apps, locale: locale, region: region, pause: pause, set_browserstack_env: set_browserstack_env }, timeout)
  end

  def launch_app(display_name)
    endpoint = '/bs/launchapp'
    make_request('POST', endpoint, { displayName: display_name })
  end

  def set_rtc_data_and_start(rtc_data)
    endpoint = '/bs/set_rtcdata_and_start'
    make_request('POST', endpoint, { rtc_data: rtc_data })
  end

  def set_rtc_data_and_start_replay_kit(rtc_data, url, browser, genre, use_v2_endpoint)
    endpoint = use_v2_endpoint ? '/bs/set_rtcdata_and_start_v2' : '/bs/set_rtcdata_and_start'
    make_request('POST', endpoint, { rtc_data: rtc_data, url: url, browser: browser, genre: genre })
  end

  def connect_to_peer(params)
    endpoint = '/bs/connect_to_peer'
    make_request('POST', endpoint, params)
  end

  def stop_live_session(params={})
    endpoint = '/bs/live/stop'
    # make_request GET request doesn't send parameters so adding parameter to the URL
    endpoint += "?is_voiceover_enabled=#{params[:is_voiceover_enabled]}" unless params[:is_voiceover_enabled].nil?
    make_request('GET', endpoint, params, 100)
  end

  def trigger_local_network_access_popup
    endpoint = '/bs/live/trigger_local_privacy_alert'
    make_request('GET', endpoint, {}, 100)
  end

  def get_scaled_screenshot(screenshot_quality = DEBUG_SCREENSHOTS_COMPRESSION_QUALITY, screenshot_scale = DEBUG_SCREENSHOTS_SCALEFACTOR)
    endpoint = "/bs/scaledDownScreenshot?quality=#{screenshot_quality}&scale=#{screenshot_scale}"
    make_request('GET', endpoint, {}, 2)
  end

  def switch_mode(mode = "Dark")
    endpoint = "/bs/switch_mode?mode=#{mode}"
    make_request('GET', endpoint)
  end

  def set_device_appearance(mode = "Dark")
    endpoint = "/wda/device/appearance"
    make_request('POST', endpoint, { name: mode.downcase }, 300, false, true)
  end

  def setup_voiceover(mini_bluetooth_name)
    endpoint = "/bs/setup_voiceover"
    make_request('POST', endpoint, { "miniBluetoothName": mini_bluetooth_name })
  end

  def teardown_voiceover
    endpoint = "/bs/teardown_voiceover"
    make_request('POST', endpoint)
  end

  def voiceover_reconnect_bluetooth(mini_bluetooth_name)
    endpoint = "/bs/voiceover_reconnect_bluetooth"
    make_request('POST', endpoint, { "miniBluetoothName": mini_bluetooth_name })
  end

  def set_passcode(passcode)
    endpoint = "/bs/set_passcode?passcode=#{passcode}"
    make_request('GET', endpoint)
  end

  def change_sim_state(state, flow="default_flow", sim_network_index = "")
    endpoint = "/bs/change_sim_state?state=#{state}&flow=#{flow}&sim_network_index=#{sim_network_index}"
    make_request("GET", endpoint)
  end

  def change_esim_state(state, flow="default_flow")
    endpoint = "/bs/change_esim_state?state=#{state}&flow=#{flow}"
    make_request("POST", endpoint)
  end

  def add_card(name, number, date, year, cvv)
    endpoint = '/bs/add_card'
    make_request('POST', endpoint, { name: name, number: number, date: date, year: year, cvv: cvv })
  end

  def low_power_mode_enabled?
    endpoint = "/bs/is_low_power_mode_enabled"
    make_request('GET', endpoint)
  end

  def low_power_mode(state)
    endpoint = '/bs/low_power_mode'
    make_request('POST', endpoint, { state: state })
  end

  def reset_logins
    endpoint = '/bs/reset_logins'
    make_request('POST', endpoint, {}, 15)
  end

  def apple_id_signout
    endpoint = '/bs/apple_id_signout'
    make_request('POST', endpoint)
  end

  def check_apple_id_popup
    endpoint = '/bs/check_apple_id_popup'
    make_request('GET', endpoint)
  end

  def clear_transaction_defaults
    endpoint = '/bs/clear_transaction_defaults'
    make_request('POST', endpoint, {})
  end

  def switch_prevent_cross_site_tracking(toggle = "enable")
    endpoint = "/bs/switch_prevent_cross_site_tracking?toggle=#{toggle}"
    make_request('GET', endpoint)
  end

  def post_accessibility_info(req_json)
    endpoint = "/bs/accesibility_info"
    make_request('POST', endpoint, req_json)
  end

  def accessibility_info
    endpoint = "/bs/accesibility_info"
    make_request('GET', endpoint)
  end

  def set_safari_settings(safari_settings)
    endpoint = "/bs/set_safari_settings"
    make_request('POST', endpoint, safari_settings)
  end

  def start_replay_kit_recording(is_v2_performance_enabled = false)
    endpoint = is_v2_performance_enabled ? '/bs/startRecordingOptimized' : '/bs/startRecording'
    make_request('GET', endpoint, {}, 30)
  end

  def stop_replay_kit_recording(is_v2_performance_enabled = false, stop_video_experiment = false)
    endpoint = is_v2_performance_enabled ? '/bs/stopRecordingOptimized' : '/bs/stopRecording'
    endpoint = '/bs/lockUnlockDevice' if stop_video_experiment
    make_request('GET', endpoint, {}, 30)
  end

  def play_with_assistive_touch(operation = "disable")
    endpoint = "/bs/play_with_assistive_touch?operation=#{operation}"
    make_request('GET', endpoint)
  end

  def end_call(ios_watcher_flow = false)
    endpoint = "/bs/end_call?ios_watcher_flow=#{ios_watcher_flow}"
    make_request('GET', endpoint)
  end

  def change_time_zone(timezone = "utc")
    endpoint = "/bs/change_time_zone?timezone=#{timezone}"
    make_request('GET', endpoint, {}, 15)
  end

  def set_voice_over_settings(payload)
    endpoint = "/bs/set_voice_over_settings"
    make_request('POST', endpoint, payload)
  end

  def set_voiceover_persistent_settings
    endpoint = "/bs/set_voice_over_persistent_settings"
    response = make_request('POST', endpoint)

    raise "Failed to set voiceover persistent settings" unless response['value']['result'] && response['value']['result'] == "success"
  end

  def set_display_settings(payload)
    endpoint = "/bs/set_display_settings"
    make_request('POST', endpoint, payload)
  end

  def update_device_settings(params)
    endpoint = "/bs/updateSettings"
    payload = {
      "session_id" => params['automate_session_id'],
      "global_setting_name" => params['global_setting_name'],
      "DSL" => params['device_settings_DSL']
    }
    payload['special_post_action'] = params['special_post_action'] unless params['special_post_action'].nil?

    response = make_request('POST', endpoint, payload)

    if response['value']['status'] == 'error'
      error_obj = response['value']['error']

      raise DeviceSettingsError.new(error_obj['message'],
                                    BROWSERSTACK_ERROR_STRING,
                                    'setting_automation_error', error_obj['meta_data'])
    end

    response
  end

  # Apple Pay related. Not Integrated
  def icloud_login_status
    endpoint = "/bs/ubiquity_identity_token"
    make_request('GET', endpoint)
  end

  # Apple Pay related. Not Integrated
  def get_sandbox_id
    endpoint = "/bs/sandbox_account_details"
    make_request('POST', endpoint)
  end

  def apple_id_signed_in
    endpoint = "/bs/apple_id_signed_in"
    make_request('GET', endpoint, {}, 300, true)
  end

  def sign_in(sandbox_account)
    endpoint = "/bs/sign_in"
    make_request('POST', endpoint, { email: sandbox_account })
  end

  # Apple Pay related. Not Integrated
  def sign_out
    endpoint = "/bs/sign_out"
    make_request('POST', endpoint)
  end

  def add_to_keychain(keychain_item)
    endpoint = "/bs/add_to_keychain"
    make_request('POST', endpoint, { key: keychain_item })
  end

  def search_keychain(keychain_item)
    endpoint = "/bs/search_keychain?key=#{keychain_item}"
    make_request('GET', endpoint)
  end

  def disable_safari_sync
    endpoint = "/bs/disable_safari_sync"
    make_request('POST', endpoint, {})
  end

  def prefill_apple_pay_detail(apple_pay_details)
    endpoint = "/bs/apple_pay_prefill_payment_sheet"
    make_request('POST', endpoint, apple_pay_details, 150)
  end

  def tap_assistive_touch_element(icon, doNotTapAssistiveTouch: "false")
    endpoint = "/bs/tap_assistive_touch_element"
    make_request('POST', endpoint, { "icon": icon, "doNotTapAssistiveTouch": doNotTapAssistiveTouch })
  end

  def device_info
    endpoint = "/bs/device_info"
    make_request('GET', endpoint)
  end

  def get_metrics
    start_time = Time.now.to_f
    endpoint = "/bs/wda/metrics"
    response = make_request('GET', endpoint)
    end_time = Time.now.to_f
    if response.is_a?(Hash) && response['value']
      memory_usage = response['value']['memoryUsageMB']
      cpu_usage = response['value']['cpuUsagePercentage']
      { process_name: WDA, memory_usage: memory_usage, cpu_usage: cpu_usage, time_consumed: (end_time - start_time) }
    else
      BrowserStack.logger.error("Invalid WDA response from endpoint: #{endpoint}")
    end
  end

  private

  # Useful for testing purposes
  def log(level, message)
    # When returning debugDescription(XCUI element tree trace) from wda automation endpoints as response
    # in case of automation failure, the log writing fails when the debugDescription string contains improperly encoded
    # strings example exception - 'log writing failed. "\xE2" from ASCII-8BIT to UTF-8'
    # this is added to replace such improperly encoded strings with '_' from message for logging
    # reference - https://makandracards.com/makandra/498508-ruby-fixing-strings-with-invalid-encoding-and-converting-to-utf-8
    message = message.encode('utf-8', invalid: :replace, undef: :replace, replace: '_')
    if defined?(BrowserStack.logger.info)
      BrowserStack.logger.send(level.to_sym, message)
    else
      puts message
    end
  end

  # TODO: check why timeout is so large and set more realistic default value if we can
  def make_request(verb, endpoint, params = {}, timeout = 300, send_error_response = false, send_status_code = false) # rubocop:todo Metrics/AbcSize
    url = "http://127.0.0.1:#{@wda_port}#{endpoint}"
    log :info, "WDA Request - URL: #{url} Params: #{params}"
    subcomponent = endpoint.tr('/', '_')

    case verb
    when "GET"
      response = BrowserStack::HttpUtils.make_get_request(url, timeout)
      log :info, "WDA Response - success: #{response.success?} code: #{response.status} body: #{response.body}" unless redact_endpoint?(endpoint) && response.success?
      @influxdb_client.event(@device_id, 'wda-request-timeout', subcomponent: subcomponent, is_error: true) unless response.success?
      # TODO: Remove this - GET requests should not swallow timeout/nil response errors
      return {} if (!response.success? && !send_error_response) || response.body.nil? || response.body.empty?
    when "POST"
      response = BrowserStack::HttpUtils.send_post_raw(url, params.to_json, basic_auth = nil, timeout)
      log :info, "WDA Response - success: #{response.success?} code: #{response.status} body: #{response.body} "
      @influxdb_client.event(@device_id, 'wda-request-timeout', subcomponent: subcomponent, is_error: true) unless response.success?
    else
      raise WdaClientError.new("invalid-request"), "request type #{verb} is not supported"
    end

    unless response.success?
      if response.body
        raise WdaClientError.new("curl-error"), "WDA #{endpoint} request failed: #{response.body}"
      else
        raise WdaClientError.new("curl-error"), "WDA #{endpoint} request failed"
      end
    end

    begin
      # TODO: Investigate json parse failures because of empty body
      # Return status code if requested
      return { "status": response.status , "body": JSON.parse(response.body, max_nesting: 400) } if send_status_code

      JSON.parse(response.body, max_nesting: 400)
    rescue
      raise WdaClientError.new("parse-error"), "Cannot parse WDA response \"#{response.body}\" from endpoint #{endpoint} as json"
    end
  end
end

# It can take a few seconds for the file requirements to load.
if $PROGRAM_NAME == __FILE__
  puts "File requirements loaded. Beginning script..."

  device = ARGV[0]
  method = ARGV[1]
  args = ARGV[2..]

  raise 'No device given' if device.nil?
  raise 'No method given' if method.nil?

  require 'browserstack_logger'

  # Mock logger - output to stdout
  BrowserStack.init_logger('/dev/stdout')

  require_relative '../utils/zombie'
  BrowserStack::Zombie.configure({ zombie_url: nil, ip: nil })

  # Get appium port for device
  device_config = JSON.parse(File.read(CONFIG_JSON_FILE))["devices"][device]
  wda_port = device_config["webdriver_port"]

  puts "Making request..."

  client = WdaClient.new(wda_port, device)
  response = client.send(method.to_sym, *args)

  puts "Response:\n"
  puts JSON.pretty_generate(response)
end
