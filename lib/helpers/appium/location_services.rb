require 'browserstack_logger'

module Automation
  LOCATION_POOPUP_OPTIONS = {
    once: 'Allow Once',
    always: 'Allow While Using App',
    never: 'Don’t Allow'
  }

  def self.location_permissions_popup(driver, permission: :once)
    begin
      driver.find_element(:xpath, "//*[contains(@label,'to use your location?')]")
    rescue Selenium::WebDriver::Error::NoSuchElementError
      BrowserStack.logger.info "No location permission popup detected"
      return
    end

    text_to_click = LOCATION_POOPUP_OPTIONS.fetch(permission, LOCATION_POOPUP_OPTIONS[:always])

    driver.find_element(name: text_to_click).click
  end
end