require 'appium_lib'
require_relative '../custom_exceptions'
require_relative '../configuration'
require_relative 'automation'
require_relative 'data_report_helper'
require_relative 'xcui_test_helper'
require_relative 'sms_helper'
require_relative 'call_logs_helper'
require_relative '../utils/idevice_utils'
require_relative '../models/device_state'
require_relative "../utils/utils"
require_relative '../utils/configuration_profiles_manager'
require_relative 'ios_backup_sqlite_helper'
require_relative '../cleanup_iphone'
require_relative '../../config/constants'

class DeviceSIMHelper < XCUITestHelper # rubocop:todo Metrics/ClassLength

  PUBLIC_SIM_DEVICES_FILE = "/usr/local/.browserstack/realmobile/config/custom_devices/public_sim_devices.json"
  PRIVATE_SIM_DEVICES_FILE = "/usr/local/.browserstack/realmobile/config/custom_devices/private_sim_devices.json"
  SIM_HELPER_TAG = "[SIM_HELPER]"
  MAX_RETRY_ATTEMPTS = 3

  def initialize(device_id, session_id = "", product = "", user_id: nil)
    super(device_id, session_id)
    @uuid = device_id
    @product = product
    @session_id = session_id
    @user_id = user_id
    @device_config = self.class.device_config(@uuid)
    @ios_version = @device_config["device_version"]
    @device_name = @device_config["device_name"]
    @device_state = self.class.device_state(@uuid)
    @wda_client = WdaClient.new(@device_config['webdriver_port'])
    @server_config = Configuration.new.all
    @sim_details = nil
    @sms_cleaner = SMSHelper.new(@device, @session_id)
    @sim_data_reporter = DataReportHelper.new('sim-session-stats', session_id: session_id, product: product, device: device_id, ios_version: @ios_version, user_id: user_id)
    @call_forwarding_data_reporter = DataReportHelper.new('call-forwarding', session_id: session_id, product: product, device: device_id, ios_version: @ios_version, user_id: user_id)
    @clear_sms_notifications_reporter = DataReportHelper.new('sms-notification', session_id: session_id, product: product, device: device_id, ios_version: @ios_version, user_id: user_id)
    @sim_state_change_reporter = DataReportHelper.new('sim-state-change', session_id: session_id, product: product, device: device_id, ios_version: @ios_version, user_id: user_id)
    @sim_setup_reporter = DataReportHelper.new('sim-configuration', session_id: session_id, product: product, device: device_id, ios_version: @ios_version, user_id: user_id)
    @end_call_reporter = DataReportHelper.new('sim-end-call', session_id: session_id, product: product, device: device_id, ios_version: @ios_version, user_id: user_id)
  end

  def self.device_state(device)
    DeviceState.new(device)
  end

  def self.device_config(device)
    DeviceManager.device_configuration_check(device)
  end

  def self.private_sim_config(device)
    get_sim_data_from_statefile(device)
  rescue
    {}
  end

  def self.public_sim_config(device)
    JSON.parse(File.read(PUBLIC_SIM_DEVICES_FILE))[device] || {}
  rescue
    {}
  end

  def self.sim_config(device)
    public_sim_config(device).merge(private_sim_config(device))
  end

  def self.private_sim?(device)
    device_state(device).sim_info_file_present? && dedicated_device?(device)
  end

  def self.dedicated_device?(device)
    device_state(device).dedicated_device_file_present?
  end

  def self.public_sim?(device)
    !public_sim_config(device)["phone_number"].nil?
  end

  def sim_config_present?
    self.class.sim_config_present?(@uuid)
  end

  def self.sim_config_present?(device)
    private_sim?(device) || public_sim?(device)
  end

  def sim?
    self.class.sim?(@device)
  end

  def self.sim?(device)
    physical_sim?(device) || esim?(device)
  end

  def esim?
    self.class.esim?(@uuid)
  end

  def self.esim?(device)
    device_state(device).esim_file_present?
  end

  def physical_sim?
    self.class.physical_sim?(@uuid)
  end

  def self.physical_sim?(device)
    device_state(device).physical_sim_file_present?
  end

  def call_forwarding_number
    "9999999999"
  end

  def send_sim_stats(event, result, time_taken, is_sim_session = false, is_esim_cleanup_required = false)
    data = {
      "event" => event,
      "result" => result,
      "time_taken" => time_taken,
      "platform" => 'iOS',
      "os_version" => @ios_version,
      "device_name" => @device_config["device_name"],
      "device_id" => @uuid,
      "region" => @device_config["region"],
      "sub_region" => @device_config["sub_region"],
      "is_sim_session" => is_sim_session,
      "is_esim_cleanup_required" => is_esim_cleanup_required
    }
    @sim_data_reporter.report(data, eds_only: true)
  end

  def self.refresh_state_files(device)
    is_sim_config_present = sim_config_present?(device)
    is_physical_sim = physical_sim?(device)
    is_esim = esim?(device)

    device_state = device_state(device)
    # remove state files if sim config is not present.
    unless is_sim_config_present
      device_state.remove_physical_sim_file
      device_state.remove_esim_file
      device_state.remove_sim_enabled_file
      device_state.remove_sim_validation_check_file
      device_state.remove_sim_config_file
      return
    end

    # return if sim config is present and physical/esim is already identified
    return if is_sim_config_present && (is_physical_sim || is_esim)

    # return if no sim inserted without touching physical/esim file
    return unless sim_inserted?(device)

    # Check if sim is esim the first time
    if esim_enabled?(device)
      device_state.touch_esim_file
    else
      device_state.touch_physical_sim_file
    end
  end

  def esim_enabled?
    self.class.esim_enabled?(@uuid)
  end

  def self.esim_enabled?(device, validate_output: false)
    # Check for SIM1IsEmbedded value from ideviceinfo, which is false for physical sim devices.
    # SIM1IsEmbedded is true when esim is in sim 1 slot of device and is not disabled.
    # SIM1IsEmbedded is empty when esim is disabled or not in SIM 1 slot.
    # So based on the above conditions,
    #   if SIM1IsEmbedded is false then it is a physical sim.
    #   else it is a eSIM
    cmd_output = IdeviceUtils.ideviceinfo(device).grep(/^SIM[1|2]IsEmbedded/)
    embedded_value = cmd_output[0]

    raise IdeviceinfoOutputFieldMissing, "Failed to determine SIM type: SIM1IsEmbedded field not found in ideviceinfo output." if validate_output && cmd_output.empty?

    log "esim_enabled: Got the embedded value as #{embedded_value}"
    !embedded_value.nil? && embedded_value.include?("true")
  end

  def self.sim_enabled?(device)
    device_state(device).sim_enabled_file_present?
  end

  # Verifies presence of physical/e-sim
  def self.sim_inserted?(device_id)
    sim_status = IdeviceUtils.ideviceinfo(device_id, 'CarrierBundleInfoArray')
    log "SIM Inserted status for device #{device_id} is #{sim_status}"
    !sim_status.empty?
  end

  # Physical sim is inserted in the device and can be enabled if not already
  def self.physical_sim_ready?(device)
    physical_sim?(device) && sim_inserted?(device)
  end

  def log_sms_sim_sessions
    start = Time.now.to_i
    data = nil
    backup_cmd = nil
    error_msg = nil
    FileUtils.mkdir_p(IOS_BACKUP_DIR) unless File.exists?(IOS_BACKUP_DIR)
    ios_db_backup_command = "idevicebackup2 backup -u #{@uuid} --full #{IOS_BACKUP_DIR}"
    backup_cmd = BrowserStack::OSUtils.execute(ios_db_backup_command, true)
    sqlite_backup_object = IosBackupSqliteHelper.new(@uuid)
    data = sqlite_backup_object.retrieve_messages
  rescue => e
    error_msg = e.message
    log "[ios_sim_logging] iOS sms logging failed with error: #{e.message}, backup_cmd: #{backup_cmd} || #{e.backtrace.join("\n")}"
  ensure
    BrowserStack::Zombie.push_logs("ios_sms_logging", error_msg, { "device" => @uuid, "session_id" => @session_id, "product" => @product, "user_id" => @user_id, "data" => data, "time_taken" => Time.now.to_i - start })
  end

  def cleanup
    log "Started SIM cleanup flow"
    is_sim_session = nil
    start = Time.now.to_i
    result = true

    is_esim_cleanup_required = esim?
    is_sim_session = self.class.sim_enabled?(@uuid)
    return true unless is_sim_session

    whitelisted_terminals = ["**************", "*************", "*************"]
    is_msg_logging_enabled = whitelisted_terminals.include?(@device_config["ip"]) && @device_state.ios_sms_app_opened_file_present? && (is_esim_cleanup_required || is_sim_session)
    log "[ios_sim_logging] flag: #{@device_state.ios_sms_app_opened_file_present?}, is_esim_cleanup_required: #{is_esim_cleanup_required}, is_sim_session: #{is_sim_session}"
    log_sms_sim_sessions if is_msg_logging_enabled

    result = enable_call_forwarding && change_sim_state("disable", is_esim_cleanup_required, use_wda: false) && @sms_cleaner.sms_cleanup && clear_sms_notifications
    raise "Failed to clean SIM" unless result

    true
  rescue => e
    log "Failed in SIM cleanup flow, error: #{e.message}, #{e.backtrace.join("\n")}"
    result = false
    false
  ensure
    @device_state.remove_ios_sms_app_opened_file
    send_sim_stats('cleanup', result, Time.now.to_i - start, is_sim_session, is_esim_cleanup_required)
  end

  def session_setup
    log "Started SIM session setup flow"
    if self.class.sim_enabled?(@uuid)
      BrowserStack.logger.info "SIM has already been setup, not needed"
      return true
    end

    result = nil
    begin
      start = Time.now.to_i
      result = enable_sim
      raise "Failed to setup SIM session" unless result

      true
    rescue => e
      result = false
      log "Failed to setup SIM session: #{e.message}, #{e.backtrace.join("\n")}"
      false
    ensure
      send_sim_stats('session_start', result, Time.now.to_i - start)
    end
  end

  def enable_call_forwarding
    return true unless @device_state.call_forwarding_file_present?

    cf_number = call_forwarding_number
    log "Setting call forwarding number #{cf_number} for region #{@device_config['region']}"
    result = run_test(:enable_call_forwarding, env_params: { 'call_forwarding_number': cf_number })
    @call_forwarding_data_reporter.report(result)
    success = result["result"] == "success"
    @device_state.remove_call_forwarding_file if success
    success
  end

  def change_sim_state_through_wda(state, is_esim, sim_network_index = "")
    log "Request for changing SIM state to #{state} through WDA"

    flow = "default_flow"
    prefs = SETTINGS_PREFS[:change_sim_state_prefs]
    prefs_output = CleanupIphone.get_prefs_for_version(@ios_version, @device_name, prefs)

    if prefs_output
      flow = "optimised_flow"
      launch_app_output = DeviceCtl::Device.launch_app_with_prefs(@uuid, "com.apple.Preferences", prefs_output)
      raise "Failed to launch Settings app" unless launch_app_output.include? "Launched application"
    end

    response = is_esim ? @wda_client.change_esim_state(state, flow) : @wda_client.change_sim_state(state, flow, sim_network_index)
    BrowserStack.logger.info "Response from WDA for changing sim state: #{response}"

    raise WdaAutomationError.new("unknown error"), "No payload in response" unless response["value"]

    data = response["value"]
    data["expected_flow"] = flow
    data
  end

  def change_sim_state_through_xcui(state, is_esim, options: {}, max_retry_attempts: 1)
    log "Request for changing SIM state to #{state} through ios-njb-app with options: #{options}"
    attempts = 0
    response = {}

    test_type = "#{state}_#{is_esim ? 'esim' : 'sim'}"
    options["confirmDisable"] = true if test_type == "disable_esim"

    prefs = SETTINGS_PREFS[:change_sim_state_prefs]
    prefs_output = CleanupIphone.get_prefs_for_version(@ios_version, @device_name, prefs)

    if prefs_output
      options["flow"] = "optimised_flow"
      launch_app_output = DeviceCtl::Device.launch_app_with_prefs(@uuid, "com.apple.Preferences", prefs_output)
      raise "Failed to launch Settings app" unless launch_app_output.include? "Launched application"
    end

    while attempts < max_retry_attempts
      BrowserStackAppHelper.check_and_install_browserstack_test_suite(@device)
      response = run_test(test_type.to_sym, env_params: options)

      break if response["result"] == "success"

      attempts += 1

      log "Change SIM state test failed. Retrying... (Attempt #{attempts})" if attempts < max_retry_attempts
    end

    unless response["result"] == "success"
      raise GenericException.new("Failed to change SIM state through XCUI", {
        "response" => response
      })
    end
  end

  def change_sim_state(state, is_esim, use_wda: true, use_xcui: true, max_retry_attempts: 1) # rubocop:todo Metrics/MethodLength, Metrics/AbcSize
    log "Request for changing SIM state to #{state}, use_wda: #{use_wda}, use_xcui: #{use_xcui}"

    result = nil
    data = {}
    error = {}
    start_time = Time.now.to_i
    sim_network_index = fetch_sim_network_index(state: state)
    error_message = ""
    wda_actual_flow = ""
    wda_expected_flow = ""
    automation_via = ""
    if use_wda
      begin
        output = change_sim_state_through_wda(state, is_esim, sim_network_index)
        wda_actual_flow = output["flow"]
        wda_expected_flow = output["expected_flow"]
        automation_via = "wda"
        failed = is_esim ? output["result"] == "failed" : output["error"] != "no error"
        raise WdaAutomationError.new("automation error"), output["error"] if failed

        result = "success"
      rescue WdaAutomationError => e
        # Handling sim signal issue separately.
        if e.message == "Didn't get expected signal check"
          use_xcui = false
          error_message = e.message
        end

        error["wda"] = {
          "reason" => e.response_object,
          "message" => e.message
        }
      end
    end

    if use_xcui && result != "success"
      begin
        options = { "sim_network_index_choice" => sim_network_index }
        options["carrier"] = "T-Mobile, Sprint, 311 490" if state == "disable" && tmobile_carrier?
        outputs = change_sim_state_through_xcui(state, is_esim, options: options, max_retry_attempts: max_retry_attempts)
        automation_via = "xcui"
        result = "success"
      rescue GenericException => e
        error["inb"] = {
          "reason" => e.meta_data["response"]["error"],
          "message" => e.meta_data["response"]["error_message"]
        }
        error_message = e.meta_data["response"]["error_message"]
      end
    end

    success = result == "success"
    log "Touching/removing sim_enabled state file based on state: #{state}, success: #{success}"
    @device_state.touch_sim_enabled_file if success && state == "enable"
    @device_state.remove_sim_enabled_file if success && state == "disable"

    [success, error_message]
  rescue => e
    log "Failed to change SIM state: #{e.message}, #{e.backtrace.join("\n")}"
    error["unknown"] = {
      "reason" => "unknown",
      "message" => e.message
    }
    [false, error_message]
  ensure
    result ||= "failed"
    data = {
      "result" => result,
      "state" => state,
      "time_taken" => Time.now.to_i - start_time,
      "error" => error,
      "wda_actual_flow" => wda_actual_flow,
      "wda_expected_flow" => wda_expected_flow,
      "automation_via" => automation_via
    }
    @sim_state_change_reporter.report(data)
  end

  def fetch_sim_network_index(state: "enable")
    carrier1 = carrier
    return SIM_CARRIER_MAPPING[carrier1.to_sym][:label_for_enabling].to_s if state == "enable"

    SIM_CARRIER_MAPPING[carrier1.to_sym][:label_for_disabling].to_s if state == "disable"
  rescue
    ""
  end

  def enable_sim
    log "Request for enabling SIM"

    result = false
    if enable_sim_modification(true)
      result = change_sim_state("enable", esim?)[0]
      result &&= enable_sim_modification(false)
    end
    @device_state.touch_call_forwarding_file if result && needs_call_forwarding?
    result
  end

  def enable_sim_modification(enable)
    return true unless esim?

    sim_modification_reporter = DataReportHelper.new('esim-modification', session_id: @session_id, product: @product, device: @uuid, ios_version: @ios_version, user_id: @user_id)
    start_time = Time.now.to_i
    result = "failed"
    error = nil
    begin
      config_profiles_manager = ConfigurationProfilesManager.new(@uuid, BrowserStack.logger)
      enable ? @device_state.touch_esim_modification_file : @device_state.remove_esim_modification_file
      config_profiles_manager.install_profile(:restrictions, install_via: :automatic)
      result = "success"
      log "Installed restrictions with esim modification #{enable}"
    rescue MdmApiFatalException => e
      error = e.message
      log "Failed to set enable_sim_modification to #{enable}, due to #{e.message}"
      @device_state.touch_force_install_mdm_profiles_file unless enable
    ensure
      @device_state.remove_esim_modification_file
      sim_modification_reporter.report({
        "enable" => enable,
        "result" => result,
        "time_taken" => Time.now.to_i - start_time,
        "error" => error
      })
    end
    result == "success"
  end

  def toggle_notifications_xcui
    log "Request for toggling SMS notifications through XCUI"
    run_test(:clear_sms_notifications)
  end

  def needs_call_forwarding?
    !tmobile_carrier? || esim?
  end

  def tmobile_carrier?
    log "Request for checking if carrier is t-mobile"
    carrier = self.carrier
    carrier == "com.apple.TMobile_US"
  end

  def carrier
    @sim_details ||= self.class.sim_info(@device)

    @sim_details ||= self.class.sim_info(@device, force_check: true) if @sim_details.nil? || @sim_details.empty?

    begin
      @sim_details[0]['carrier']
    rescue => e
      log "Error in getting sim carrier: #{e.message} #{e.backtrace.join("\n")}"
      @sim_setup_reporter.report({ "status" => "failed", "error" => "SIM inactive", "device_name" => @device_config["device_name"], "ip" => @device_config["ip"] })
      @sim_details = nil
      nil
    end
  end

  def clear_sms_notifications
    log "Request for clearing SMS notifications"
    result_code = 0
    error = {}
    result = "success"
    time_taken = 0

    response_mdm = Utils.toggle_notifications_mdm(["com.apple.MobileSMS"], @device)
    if response_mdm["result"] == "failed"
      result_code = 1
      error["mdm"] = response_mdm["error"]

      response_xcui = toggle_notifications_xcui
      if response_xcui["result"] == "failed"
        result = "failed"
        result_code = 2
        error["xcui"] = {
          "reason" => response_xcui["error"],
          "message" => response_xcui["error_message"]
        }
      end

      time_taken = response_mdm["time_taken"] + response_xcui["time_taken"]
    else
      time_taken = response_mdm["time_taken"]
    end

    # Result Structure
    # result_code -> Enum of 0, 1, 2
    #   0 = success through ndn
    #   1 = mdm failed but xcui passed
    #   2 = both mdm and xcui failed
    response = {
      "result" => result,
      "time_taken" => time_taken,
      "result_code" => result_code,
      "error" => error
    }
    zombie_data = {
      "result_code" => result_code,
      "message" => result_code == 1 ? "mdm failed but xcui passed" : "both mdm and xcui failed"
    }
    @clear_sms_notifications_reporter.report(response, force_zombie: result_code != 0, zombies_data: zombie_data)
    response["result"] == "success"
  end

  def end_call(ios_watcher_flow = false)
    log "End call got called"
    start_time = Time.now.to_i
    error = {}
    result = ""
    response = {}
    response = @wda_client.end_call(ios_watcher_flow)
    BrowserStack.logger.info "Response from WDA for ending call: #{response}"

    raise WdaAutomationError.new("unknown error"), "No payload in response" unless response["value"]

    result = "pass"
  rescue => e
    log "Failed to end call"
    result = "fail"
    error = { "message" => e.message }
  ensure
    data = {
      "result" => result,
      "response" => response,
      "time_taken" => Time.now.to_i - start_time,
      "error" => error
    }
    @end_call_reporter.report(data)
  end

  def log(line)
    self.class.log(line)
  end

  def self.update_existing_sim_details(sim_info_file, sim_details, device)
    existing_content = File.read(sim_info_file)
    existing_sim_details = JSON.parse(existing_content)

    sim_details.each do |sim_detail|
      sim_slot = sim_detail["sim_slot"]
      existing_sim_detail = existing_sim_details.find { |detail| detail["sim_slot"] == sim_slot }

      if existing_sim_detail
        existing_sim_detail.merge!(sim_detail)
      else
        existing_sim_details << sim_detail
      end
    end

    File.write(sim_info_file, existing_sim_details.to_json)
    BrowserStack.logger.info("Sim details updated for device #{device}.")
  end

  def self.create_new_sim_details(sim_info_file, sim_details, device)
    FileUtils.touch(sim_info_file)
    File.write(sim_info_file, sim_details.to_json)
    BrowserStack.logger.info("Sim details added for device #{device} with details: #{sim_details}")
  end

  def self.validate_sim_details(sim_details)
    required_fields = ["sim_slot", "sim_id", "carrier", "imei", "sim_type", "phone_number"]
    sim_details.each do |sim_detail|
      raise SimValidationError, 'Invalid sim_slot. It must be 1 or 2.' unless %w[1 2].include?(sim_detail["sim_slot"].to_s)

      missing_fields = required_fields - sim_detail.keys
      raise SimValidationError, "Some mandatory fields are missing: #{missing_fields.join(', ')}." unless missing_fields.empty?
    end
  end

  def self.log(line)
    BrowserStack.logger.info "#{SIM_HELPER_TAG}: #{line}"
  end

  def self.sim_info(device, force_check: false)
    log "sim_info called with device : #{device} and force_check : #{force_check}"
    unless sim_config_present?(device)
      refresh_state_files(device)
      return []
    end

    unless force_check
      sim_details = []
      begin
        return JSON.parse(device_state(device).read_sim_config_file) if sim?(device) && device_state(device).sim_config_file_present?
      rescue JSON::ParserError => e
        BrowserStack.logger.error "Error parsing sim config JSON file: #{e.message}"
      end

      sim_details = device_config(device)["sim_details"]
      return sim_details if sim_details.is_a?(Array)

      return []
    end

    sim_details = device_config(device)["sim_details"]

    # Runs every device check
    device_sim_config = sim_config(device)
    return sim_details if !DeviceSIMHelper.check_force_read_config_required?(sim_details, device_sim_config) && sim?(device)

    # Runs in case (first time sim is inserted/sim changed)
    refresh_state_files(device)
    DeviceSIMHelper.retrieve_sim_details_from_device(device, device_sim_config["phone_number"])
  rescue => e
    BrowserStack.logger.error "sim_info has ran into error for Device #{device} with error #{
      e.message} error_trace: #{e.backtrace.join("\n")}"
    begin
      return DeviceSIMHelper.retrieve_sim_details_from_device(device, "")
    rescue
      []
    end
    []
  end

  def self.get_sim_details_from_statefile(device)
    JSON.parse(device_state(device).read_sim_info_file)
  rescue
    []
  end

  def self.dedicated_device_sim_info(device)
    return [] unless sim?(device)

    get_sim_details_from_statefile(device)
  rescue => e
    BrowserStack.logger.error "dedicated_device_sim_info has ran into error for Device #{device} with error #{
      e.message} error_trace: #{e.backtrace.join("\n")}"
    []
  end

  def self.compare_sim_details(device)
    device_detail = retrieve_sim_details_from_device(device, '')
    state_detail = get_sim_details_from_statefile(device)

    comparison = {}

    device_detail.each do |device_sim|
      state_detail.each do |state_sim|
        next if state_sim['sim_slot'] != device_sim['sim_slot']

        ['phone_number', 'imei', 'sim_id'].each do |field|
          # Normalize phone numbers for comparison to avoid issues with formatting
          if field == 'phone_number'
            state_sim[field] = state_sim[field].gsub(/[^0-9]/, '')
            device_sim[field] = device_sim[field].gsub(/[^0-9]/, '')
          end

          next if state_sim[field] == device_sim[field]

          # Skip if the field is nil or empty
          next if state_sim[field].nil? || device_sim[field].nil?
          next if state_sim[field] == "" || device_sim[field] == ""

          comparison["sim#{state_sim['sim_slot']}"] ||= {}
          comparison["sim#{state_sim['sim_slot']}"][field] = {
            'railsDB' => state_sim[field],
            'device' => device_sim[field]
          }
        end
      end
    end

    BrowserStack.logger.info "Finished comparing sim details with actual device info"
    comparison
  rescue => e
    BrowserStack.logger.error "Failed to compare sim details, error: #{e.message}"
    {}
  end

  def self.check_force_read_config_required?(sim_details_json, device_sim_config)
    keys_to_check = %w[carrier phone_number sim_slot sim_type imsi iccid imei]
    return true if sim_details_json.nil? || sim_details_json.empty?

    sim_detail_json = sim_details_json.find { |h| h["sim_slot"] == 1 }
    return true if sim_detail_json.nil? || sim_detail_json.empty?

    keys_to_check.each do |key|
      return true if sim_detail_json[key].to_s.empty?
      return true if key.to_s == 'phone_number' && sim_detail_json['phone_number'].to_s != device_sim_config['phone_number'].to_s
    end

    false
  end

  def self.retrieve_sim_details_from_device(device, phone_number)
    BrowserStack.logger.info "Retrieving Sim Details From Device #{device} And Phone Number #{phone_number}"
    device_info = IdeviceUtils.ideviceinfo(device, 'CarrierBundleInfoArray')
    sim_details = []

    device_info.each do |info|
      key, val = info.split(':', 2).map(&:strip)

      case key
      when '0', '1'
        sim_details.push({})
      when 'CFBundleIdentifier'
        sim_details.last['carrier'] = val
      when 'IntegratedCircuitCardIdentity'
        sim_details.last['iccid'] = val
      when 'InternationalMobileSubscriberIdentity'
        sim_details.last['imsi'] = val
      when 'Slot'
        sim_details.last['sim_type'] = DeviceSIMHelper.esim?(device) ? 'esim' : 'physical'
        sim_details.last['sim_slot'] = val == 'kOne' ? 1 : 2
        sim_details.last['imei'] = IdeviceUtils.ideviceinfo(device, val == 'kOne' ? 'InternationalMobileEquipmentIdentity' : 'InternationalMobileEquipmentIdentity2')[0].strip
        sim_details.last['phone_number'] = phone_number
      end
    end
    BrowserStack.logger.info "Device Info : #{device_info}, Sim Details : #{sim_details}"

    sim_details
  end
end
