# This file contains class and functions related to handling of browserstack app and test suite, which are currently
# being used for preloading of images and adding custom media to photos app, but with the help of browserstack test suite,
# we can automate other things too in cleanup or setup, which are currently being done via appium.
require_relative '../../config/constants'
require_relative '../../server/iphone'
require_relative '../apps/installed_app'
require_relative '../configuration'
require_relative '../custom_exceptions'
require_relative '../provisioning/ppuid_file'
require_relative '../utils/idevice_utils'
require_relative '../utils/plist_buddy'
require_relative '../utils/time_recorder'
require_relative '../utils/utils'
require_relative '../utils/devicectl'
require_relative '../utils/zombie'
require_relative './xcparse'
require_relative '../apps/chromium'
require_relative '../utils/helpers'

# FIXME: The methods in this class should be instance methods instead of class methods.
# This should be an instantiated object with uuid as an instance variable.

XCParse.xcparse_path = XCPARSE_PATH

class BrowserStackAppHelper # rubocop:todo Metrics/ClassLength
  include BrowserStack::TimeRecorder

  time_class_methods :handle_cleanup_tasks

  @@config = BrowserStack::Configuration.conf
  XCUI_TESTS = @@config['xcui_class_function_map']
  XCUI_CLEANUP_TESTS_MAP = @@config['xcui_cleanup_tests_class_function_map']
  XCUI_CLEANUP_POPULATION_TESTS_MAP = @@config['xcui_cleanup_population_tests_class_function_map']
  INSTALL_PHASE_IDENTIFIER = "browserstack_app".freeze
  MAX_ATTEMPTS_XCUI = 3

  def self.bstack_test_app_bundle_id
    return @bstack_test_suite_bundle_id unless @bstack_test_suite_bundle_id.nil?

    xcode_version = BrowserStack::OSUtils.check_xcode
    @bstack_test_suite_bundle_id = if Gem::Version.new(xcode_version.split[1]) >= Gem::Version.new(11)
      # Example of split: xcode_version.split => ["Xcode", "10.1", "Build", "version", "10B61"]
                                     "com.browserstack.BrowserStackUITests.xctrunner"
                                   else
                                     "com.apple.test.BrowserStackUITests-Runner"
                                   end
  end

  def self.ppuid(uuid)
    PpuidFile.new(uuid).ppuid
  end

  def self.provision_profile_identifier(uuid)
    BrowserStack::WebDriverAgent.provision_profile_identifier(uuid)
  end

  def self.device_state(uuid)
    DeviceState.new(uuid)
  end

  def self.device_version(uuid)
    DeviceManager.device_configuration_check(uuid)['device_version'].to_f
  end

  def self.device_type(uuid)
    DeviceManager.device_configuration_check(uuid)["device_name"].match(/iPad/i).nil? ? "iPhone" : "iPad"
  end

  def self.device_config(uuid)
    DeviceManager.device_configuration_check(uuid)
  end

  # Tasks to be done in cleanup of a device.
  def self.handle_cleanup_tasks(uuid)
    BrowserStackAppHelper.check_and_build_and_install_browserstack_app(uuid)
    BrowserStackAppHelper.check_and_install_browserstack_apps(uuid)
  end

  # Check if browserstack app is built for a given uuid
  def self.browserstack_app_built?(uuid)
    browserstack_app_path = browserstack_app_path(uuid)
    browserstack_test_suite_path = browserstack_test_suite_path(uuid)
    rebuild_browserstack_path = REBUILD_BROWSERSTACK_APP_PATH_PREFIX + uuid

    # Touched when deploying ios-njb-app
    if File.exist?(rebuild_browserstack_path)
      BrowserStack.logger.warn("Rebuild browserstack app file exists: #{rebuild_browserstack_path}")
      return false
    end

    unless File.exist?(browserstack_app_path)
      BrowserStack.logger.warn("Browserstack app not found at path: #{browserstack_app_path}")
      return false
    end

    unless File.exist?(browserstack_app_info_plist(uuid))
      BrowserStack.logger.warn("Browserstack app info plist not found")
      return false
    end

    unless File.exist?(browserstack_test_suite_path)
      BrowserStack.logger.warn("Browserstack test suite not found at path: #{browserstack_test_suite_path}")
      return false
    end

    unless File.exist?(test_suite_info_plist(uuid))
      BrowserStack.logger.warn("Browserstack test suite info plist not found")
      return false
    end

    BrowserStack.logger.info("Browserstack app already built and ready to install !")
    true
  end

  # <built_for_prov_id_path> file is created after a successful build
  # Since multiple devices can be under one prov_id, we can ignore the
  # rebuild file for those as the app has already been built
  def self.already_built?(uuid)
    rebuild_browserstack_path = REBUILD_BROWSERSTACK_APP_PATH_PREFIX + uuid
    built_for_prov_id_path = BrowserStackAppHelper.browserstack_app_built_for_prov_id_path(uuid)
    if File.exist?(built_for_prov_id_path) &&
        File.exist?(rebuild_browserstack_path) &&
        File.mtime(built_for_prov_id_path) > File.mtime(rebuild_browserstack_path)
      BrowserStack.logger.warn("BrowserStack App has been rebuilt already after touching rebuild file, no need to build again")
      true
    else
      false
    end
  end

  def self.build_browserstack_app(uuid)
    rebuild_browserstack_path = REBUILD_BROWSERSTACK_APP_PATH_PREFIX + uuid
    if already_built?(uuid)
      FileUtils.rm_f(rebuild_browserstack_path)
      return
    end

    begin
      # Lock to ensure only one build is running at a time
      # Helps to deal with cases where multiple devices are on the same
      # provisioning profile. This will stagger the build process, so hopefully
      # only a single actual build is run and the others can reuse that app
      OSUtils.lock_keychain_access do
        BrowserStack::WebDriverAgent.build_browserstack_app(uuid)
      end
      FileUtils.rm_f(rebuild_browserstack_path)
    rescue => e
      BrowserStack.logger.error("Build BrowserStack App Failed : #{e.message} #{e.backtrace}")
      raise BrowserStackAppBuildError, e.message
    end
  end

  # Builds or rebuilds the browserstack app's code and installs them
  def self.check_and_build_and_install_browserstack_app(uuid)
    # adding below line to execute idevice uninstall command when this method is called individually
    IdeviceUtils.configure(BrowserStack::Configuration.conf)

    app_built = browserstack_app_built?(uuid)
    app_installed = !browserstack_app_install_required?(uuid)

    # Don't do anything if already built and installed
    if app_built && app_installed
      BrowserStack.logger.info("BrowserStack App is built and already installed, nothing to do")
      return
    end

    unless app_built
      BrowserStack.logger.info("Building BrowserStack app")
      build_browserstack_app(uuid)
    end

    # Always reinstall apps
    # Uninstall then reinstall the browserstack app and test suite
    IdeviceUtils.uninstall_app(uuid, BROWSERSTACK_APP_BUNDLE_ID)
    IdeviceUtils.uninstall_app(uuid, bstack_test_app_bundle_id)
    install_browserstack_app(uuid)
    install_browserstack_test_suite(uuid)
  end

  def self.trust_client_certificate(uuid)
    BrowserStack.logger.info "Going to trust Client Cert for device: #{uuid}"
    begin
      run_ui_test(uuid, :trust_enterprise_cert)
      BrowserStack.logger.info("browserstack app launched for device #{uuid}")
      true
    rescue => e
      BrowserStack.logger.error("Failure in trusting client certificate via browserstack app: #{uuid} #{e.message} #{e.backtrace}")
      raise e
    end
  end

  # This function can be used to trust BinaryLife Cert on device using XCUI automation
  # But it isn't currently used anywhere. Please do not remove
  def self.trust_dummy_app_certificate(uuid)
    BrowserStack.logger.info "Going to trust Client Cert for device: #{uuid}"
    begin
      run_ui_test(uuid, :trust_dummy_app_cert)
      BrowserStack.logger.info("browserstack app launched for device #{uuid}")
    rescue => e
      BrowserStack.logger.error("Failure in trusting dummy app certificate via browserstack app: #{uuid} #{e.message} #{e.backtrace}")
      raise e
    end
  end

  # calls xcuitest for launching browserstack app
  def self.launch_browserstack_app(uuid, session_id = "")
    BrowserStack.logger.info "launching browserstack app for device #{uuid}"
    begin
      run_ui_test(uuid, :launch_browserstack_app)
      BrowserStack.logger.info("browserstack app launched for device #{uuid}")
      Utils.send_general_feature_usage_data_to_eds(session_id, FEATURE_CUSTOM_MEDIA, true, GENRE_APP_AUTOMATE, "")
    rescue => e
      BrowserStack.logger.error("Failure in launching browserstack app: #{uuid} #{e.message} #{e.backtrace}")
      BrowserStack::Zombie.push_logs("aa-bstack-launch-failure", e.message.to_s[0..150], { "device" => uuid, "session_id" => session_id })
      Utils.send_general_feature_usage_data_to_eds(session_id, FEATURE_CUSTOM_MEDIA, false, GENRE_APP_AUTOMATE, "App launch instrumentation failed")
      raise LaunchBrowserStackError, "browserstack app launch failed"
    end
  end

  def self.check_and_install_browserstack_test_suite(uuid)
    return unless test_suite_install_required?(uuid)

    log(:info, "Installing BrowserStack TestSuite", uuid: uuid)
    BrowserStackAppHelper.install_browserstack_test_suite(uuid)
    log(:info, "BrowserStack TestSuite installed", uuid: uuid)
    device_state(uuid).remove_reinstall_browserstack_test_suite_file
  end

  def self.test_suite_install_required?(uuid)
    if device_state(uuid).reinstall_browserstack_test_suite_file_present?
      log(:info, "BrowserStack TestSuite reinstall file found", uuid: uuid)
      return true
    end

    installed_app = InstalledApp.new(uuid, bstack_test_app_bundle_id)
    if installed_app.reinstall?(latest_version: test_suite_version(uuid))
      log(:info, 'BrowserStack TestSuite install required', uuid: uuid)
      return true
    end

    unless browserstack_test_suite_present?(uuid)
      log(:info, "BrowserStack TestSuite not present on device", uuid: uuid)
      return true
    end

    log(:info, "BrowserStack TestSuite is already installed.", uuid: uuid)
    false
  end

  def self.check_and_install_browserstack_app(uuid)
    return unless browserstack_app_install_required?(uuid)

    log(:info, 'Installing BrowserStack App', uuid: uuid)
    BrowserStackAppHelper.install_browserstack_app(uuid)
    log(:info, 'BrowserStack App installed', uuid: uuid)
    device_state(uuid).remove_reinstall_browserstack_app_file
  end

  def self.browserstack_app_install_required?(uuid)
    if device_state(uuid).reinstall_browserstack_app_file_present?
      log(:info, 'BrowserStack App reinstall file found', uuid: uuid)
      return true
    end

    latest_version = browserstack_app_version(uuid)
    if latest_version.match(/File Doesn't Exist/)
      log(:info, 'BrowserStack App install required as it is not built yet', uuid: uuid)
      return true
    end

    installed_app = InstalledApp.new(uuid, BROWSERSTACK_APP_BUNDLE_ID)
    if installed_app.reinstall?(latest_version: latest_version)
      log(:info, 'BrowserStack App install required', uuid: uuid)
      return true
    end

    unless BrowserStackAppHelper.browserstack_app_present?(uuid)
      log(:info, 'BrowserStack App not present on device', uuid: uuid)
      return true
    end

    log(:info, 'BrowserStack App is already installed.', uuid: uuid)
    false
  end

  # checks and installs browserstack app and test suite
  def self.check_and_install_browserstack_apps(uuid)
    BrowserStackAppHelper.check_and_install_browserstack_app(uuid)
    BrowserStackAppHelper.check_and_install_browserstack_test_suite(uuid)
  end

  def self.browserstack_app_path(uuid)
    File.join(@@config["browserstack_app_dir"], provision_profile_identifier(uuid), "/Build/Products/Debug-iphoneos/BrowserStack.app")
  end

  def self.browserstack_test_suite_path(uuid)
    File.join(@@config["browserstack_app_dir"], provision_profile_identifier(uuid), "/Build/Products/Debug-iphoneos/BrowserStackUITests-Runner.app")
  end

  def self.browserstack_app_built_for_prov_id_path(uuid)
    File.join(@@config["state_files_dir"], "browserstack_app_built_for_prov_id_#{provision_profile_identifier(uuid)}")
  end

  def self.browserstack_app_info_plist(uuid)
    File.join(browserstack_app_path(uuid), "Info.plist")
  end

  def self.test_suite_info_plist(uuid)
    File.join(browserstack_test_suite_path(uuid), "PlugIns/BrowserStackUITests.xctest/Info.plist")
  end

  # Will return: "File Doesn't Exist, Will Create....."
  # when called when app is not yet built
  # Returns the version of the app built on the host
  def self.browserstack_app_version(uuid)
    PlistBuddy.get_value_of_key(browserstack_app_info_plist(uuid), "CFBundleVersion").strip
  end

  # Will return: "File Doesn't Exist, Will Create....."
  # when called when app is not yet built
  # Returns the version of the app built on the host
  def self.test_suite_version(uuid)
    PlistBuddy.get_value_of_key(test_suite_info_plist(uuid), "CFBundleVersion").strip
  end

  # This function launches browserstack app for syncing custom media to photos app
  def self.refresh_gallery(uuid, session_id = "")
    #sync images/videos to device by launching BrowserStack.ipa

    BrowserStack.logger.info("Refreshing gallery #{uuid}")
    BrowserStackAppHelper.check_and_install_browserstack_apps(uuid)
    BrowserStackAppHelper.launch_browserstack_app(uuid, session_id)
    BrowserStack.logger.info("Custom Media Sync worked successfully for device #{uuid} and session_id : #{session_id}!")
  rescue => e
    BrowserStack.logger.error("Custom Media Sync failed for device #{uuid} and session_id : #{session_id} with exception #{e.message} #{e.backtrace}!")
    raise e
  end

  # install test suite and check if it has been installed
  def self.install_browserstack_test_suite(uuid)
    browserstack_test_suite_path = browserstack_test_suite_path(uuid)
    unless File.exist?(browserstack_test_suite_path)
      log(:error, "#{browserstack_test_suite_path} file not found.", uuid: uuid)
      raise BrowserStackInstallError, "BrowserStack TestSuite file not found"
    end

    log(:info, "starting browserstack test suite installation", uuid: uuid)
    IdeviceUtils.install_app(uuid, browserstack_test_suite_path)

    unless browserstack_test_suite_present?(uuid)
      log(:error, "BrowserStack test suite installation failed.", uuid: uuid)
      raise BrowserStackInstallError, "BrowserStack TestSuite install failed."
    end

    # Record installation details in device installed app config
    installed_app = InstalledApp.new(uuid, bstack_test_app_bundle_id)
    installed_app.update_config(test_suite_version(uuid), ppuid(uuid))
  end

  # install browserstack app and create a permission popup file to let cleanup know that it needs to be handled
  def self.install_browserstack_app(uuid, avoid_devicectl: false)
    browserstack_app_path = browserstack_app_path(uuid)

    unless File.exist?(browserstack_app_path)
      log(:error, "#{browserstack_app_path} file not found.", uuid: uuid)
      raise BrowserStackInstallError, "BrowserStack App installation failed, directory not found."
    end

    log(:info, "starting browserstack app installation", uuid: uuid)
    IdeviceUtils.install_app(uuid, browserstack_app_path, avoid_devicectl: avoid_devicectl)

    unless browserstack_app_present?(uuid)
      log(:error, "BrowserStack app installation failed.", uuid: uuid)
      raise BrowserStackInstallError, "BrowserStackApp install failed."
    end

    # Record installation details in device installed app config
    installed_app = InstalledApp.new(uuid, BROWSERSTACK_APP_BUNDLE_ID)
    installed_app.update_config(browserstack_app_version(uuid), ppuid(uuid))

    # touching this as need to handle the allow photos popup every new installation of this app
    log(:info, "Creating state file: need_permission_to_access_photos", uuid: uuid)
    device_state(uuid).touch_photos_permission_file
  end

  # Reinstall browserstack app with option to remove existing app. In iOS 18+, installing the same app on top of an existing app
  # not clearing the permissions.
  def self.reinstall_browserstack_app(uuid, remove_existing_app: false)
    return unless browserstack_app_install_required?(uuid)

    if remove_existing_app
      log(:info, 'Uninstalling existing BrowserStack App', uuid: uuid)
      IdeviceUtils.uninstall_app(uuid, BROWSERSTACK_APP_BUNDLE_ID)
    end

    log(:info, 'Installing BrowserStack App', uuid: uuid)
    install_browserstack_app(uuid)
    log(:info, 'BrowserStack App installed', uuid: uuid)

    device_state(uuid).remove_reinstall_browserstack_app_file
  end

  def self.browserstack_app_present?(uuid)
    result = IdeviceUtils.check_app_with_bundle_id_exists(uuid, BROWSERSTACK_APP_BUNDLE_ID, avoid_devicectl: true)
    log(:info, "browserstack app present?: #{result}", uuid: uuid)
    result
  end

  def self.browserstack_test_suite_present?(uuid)
    result = IdeviceUtils.check_app_with_bundle_id_exists(uuid, bstack_test_app_bundle_id, avoid_devicectl: true)
    log(:info, "browserstack test suite present?: #{result}", uuid: uuid)
    result
  end

  def self.test_meta_data(test_to_execute)
    test_meta_data = XCUI_TESTS[test_to_execute]

    cls, func = test_meta_data ? [test_meta_data[:class], test_meta_data[:function]] : raise("Unknown XCUITest: #{test_to_execute}")

    return cls, func if cls && func

    raise "test: #{test_to_execute} is not properly specified"
  end

  def self.xctestrun_command(uuid, test_class, test_func, stdout_file, capture_screenshots, environment_variables)
    # splitting to different function to remove lint error
    build_time = Time.now.strftime("%Y.%m.%d.%k.%M.%S")
    xcresult_path = "/tmp/xcresults/#{uuid}_#{build_time}_#{test_func}@#{test_class}.xcresult"
    xcodebuild_partial = capture_screenshots ? "-resultBundlePath '#{xcresult_path}' " : ''
    if device_version(uuid) >= 17 && device_version(uuid) < 18
      BrowserStack.logger.info("Using non xctestrun method for device #{uuid}")
      xctestrun_cmd = "xcodebuild -destination 'platform=iOS,id=#{uuid}' test-without-building -only-testing:'BrowserStackUITests/#{test_class}/#{test_func}' #{xcodebuild_partial}"
      xctestrun_cmd += " -scheme BrowserStack -project /usr/local/.browserstack/ios-njb-app/BrowserStack.xcodeproj -derivedDataPath #{@@config['browserstack_app_dir']}/#{provision_profile_identifier(uuid)} "
      xctestrun_env_variables = ''
      environment_variables.each do |key, value|
        xctestrun_env_variables += "#{key}=#{value};"
      end
      xctestrun_cmd += " BS_ENV_VARIABLES='#{xctestrun_env_variables}' " unless xctestrun_env_variables.empty?
    else
      xctestrun_cmd = "xcodebuild #{xcodebuild_partial}-xctestrun #{@xctest_xmlfile} -destination \"id=#{uuid}\" test-without-building -only-testing:\"BrowserStackUITests/#{test_class}/#{test_func}\" "
    end
    xctestrun_cmd += "2>&1 | tee -a #{stdout_file}"
    xctestrun_cmd
  end

  def self.run_ui_test(uuid, test_to_execute, timeout = 60, session_id: nil, environment_variables: {}, capture_syslog: false, capture_screenshots: false) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
    attempts = 0
    test_class, test_func = test_meta_data(test_to_execute)
    output = nil
    status = nil

    IdeviceUtils.start_syslog_capture(uuid, session_id, "xcuitest_syslog_#{test_to_execute}") if capture_syslog

    environment_variables['BS_ENV'] = 'debug' if capture_screenshots

    device_info = device_config(uuid)
    environment_variables['MACHINE'] = device_info['ip']
    environment_variables['REGION'] = device_info['sub_region']
    environment_variables['DEVICE_NAME'] = device_info['device_name']
    environment_variables['OS_VERSION'] = device_info['device_version']
    environment_variables['DEPLOY_ENV'] = deploy_env
    environment_variables['DEVICE'] = uuid
    environment_variables['SESSION_ID'] = session_id
    environment_variables['TEST_NAME'] = test_to_execute

    generate_bstack_xctestrun_file(uuid, environment_variables: environment_variables)
    stdout_file = "#{@@config['logging_root']}/browserstack_xcuitest_#{uuid}_#{test_to_execute}.log"

    xctestrun_cmd = xctestrun_command(uuid, test_class, test_func, stdout_file, capture_screenshots, environment_variables)
    loop do
      start_banner = "\n\n#{'*' * 20} #{Time.now.utc} Starting new execution for session_id: #{session_id} #{'*' * 20}\n\n"
      File.write(stdout_file, start_banner, mode: 'a')
      output, status = BrowserStack::OSUtils.execute(xctestrun_cmd, true, timeout: timeout)

      end_banner = "\n\n#{'*' * 20} #{Time.now.utc} Completed execution for session_id: #{session_id} #{'*' * 20}\n\n"
      File.write(stdout_file, end_banner, mode: 'a')

      IdeviceUtils.stop_syslog_capture(uuid, session_id, "xcuitest_syslog_#{test_to_execute}") if capture_syslog

      bluetooth_status = output[/bluetooth status: (?<bluetoothStatus>\w+)/, :bluetoothStatus]
      if bluetooth_status
        BrowserStack.logger.debug("Device #{uuid} bluetooth status: #{bluetooth_status}")
        File.write("/tmp/bluetooth_#{uuid}", bluetooth_status)
        BrowserStack::Zombie.push_logs('iphone-bluetooth-status', '', "device" => uuid, "session_id" => session_id, "data" => bluetooth_status)
      end

      # Test if XCUITest execution was successful and atleast 1 test was executed
      test_success = output.include?("TEST EXECUTE SUCCEEDED") && !output.include?("Executed 0 test")
      return output if test_success

      attempts += 1
      BrowserStack.logger.info("Attempt: #{attempts}")
      break if attempts >= MAX_ATTEMPTS_XCUI || !output.include?("No installed application found")
    end
    BrowserStack.logger.error("Test #{test_func} @ #{test_class} failed with code #{status}")
    BrowserStack.logger.error(output)

    cert_error = output.include?("Verify that the Developer App certificate for your account is trusted on your device") && device_version(uuid) >= 16
    if cert_error
      interfaces = Utils.get_working_interfaces(uuid, true)
      interface_not_in_bridge = false
      interfaces.each do |interface|
        unless IdeviceUtils.is_network_interface_in_bridge?(interface)
          interface_not_in_bridge = true
          IdeviceUtils.add_network_interface_to_bridge(interface)
        end
      end
      if interface_not_in_bridge
        zombie_data = { "device" => uuid, "session_id" => session_id, "os_version" => device_version(uuid) }
        BrowserStack::Zombie.push_logs("ios-interface-not-in-bridge", uuid, zombie_data)
      end
      raise 'cannot trust developer cert'
    end

    extract_screenshots(xcresult_path, "/tmp/failed_ui_tests/#{uuid}/") if capture_screenshots

    raise BrowserStackTestExecutionError.new(test_class, test_func, output)
  end

  def self.extract_screenshots(xctestrun_path, output_dir, remove_autogenerated: true)
    FileUtils.mkdir_p(output_dir) unless File.directory?(output_dir)

    BrowserStack.logger.info "Extracting screenshots from '#{xctestrun_path}'"

    XCParse.screenshot(xctestrun_path, output_dir)

    FileUtils.rm(Dir.glob("#{output_dir}*.heic")) if remove_autogenerated

    BrowserStack.logger.info "Screenshots available at '#{output_dir}'"
  end

  def self.build_and_install_browserstack_app(device_id)
    IdeviceUtils.configure(BrowserStack::Configuration.conf)

    rebuild_browserstack_path = REBUILD_BROWSERSTACK_APP_PATH_PREFIX + device_id
    FileUtils.touch(rebuild_browserstack_path)

    BrowserStack.logger.info "Building..."
    check_and_build_and_install_browserstack_app(device_id)

    BrowserStack.logger.info "Installing..."
    check_and_install_browserstack_apps(device_id)

    BrowserStack.logger.info "Generating xctestrun file..."
    generate_bstack_xctestrun_file(device_id)
  end

  def self.build_and_install_launcher_app(device_id)
    BrowserStack.logger.info "build_and_install_launcher_app: device - #{device_id}"
    device_state(device_id).touch_launcher_photos_permission_file
    #TODO: launcher app dynamic version
    launcher_app = "com.browserstack.Launcher-15.app"
    # Launcher 17 is installed on iOS version 18.0 & iPad OS 18.0
    launcher_app = "com.browserstack.Launcher-17.app" if device_version(device_id) >= 18
    ppuid = ppuid(device_id)
    signed_app_path = "/usr/local/.browserstack/config/apps/signed/#{ppuid}/#{launcher_app}"
    cmd = "#{IOS_DEPLOY} --id #{device_id} -r --bundle #{signed_app_path}"
    BrowserStack.logger.info "launcher-app deploy cmd #{cmd}"
    system(cmd)
  end

  # Start recording process in the background
  def self.start_recording(device_udid, screenshot_dir)
    BrowserStack.logger.info("Debug: Starting recording...")

    Thread.new do
      screenshot_index = 0

      if device_version(device_udid) >= 17
        wda_port = DeviceManager.device_configuration_check(device_udid)['webdriver_port']

        loop do
          screenshot_path_jpg = "#{screenshot_dir}/screenshot_#{screenshot_index}.jpg"
          screenshot_path_png = "#{screenshot_dir}/screenshot_#{screenshot_index}.png"

          ScreenshotsUtil.capture_screenshot_via_wda(screenshot_path_jpg, device_udid, wda_port)
          BrowserStack::OSUtils.execute("#{IMAGEMAGICK_CONVERT} #{screenshot_path_jpg} #{screenshot_path_png}") # convert jpg to png

          FileUtils.rm_f(screenshot_path_png) if File.size(screenshot_path_png) == 0 # remove corrupt files
          screenshot_index += 1
          sleep(1)  # Capture every second
        end

      else
        loop do
          screenshot_path = "#{screenshot_dir}/screenshot_#{screenshot_index}.png"
          command = "idevicescreenshot -u #{device_udid} #{screenshot_path}"
          system(command)
          screenshot_index += 1
        end
      end
    end
  end

  # Stop recording process gracefully
  def self.stop_recording(recording_thread)
    BrowserStack.logger.info("Debug: Stopping recording...")
    Thread.kill(recording_thread)
  end

  # Combine screenshots into a video using FFmpeg
  def self.generate_video(screenshot_dir, session_id)
    output_video = "#{screenshot_dir}/#{session_id}.mp4"
    ffmpeg_command = "#{FFMPEG} -r 1 -f image2 -s 1920x1080 -i #{screenshot_dir}/screenshot_%d.png -vf \"scale=trunc(iw/2)*2:trunc(ih/2)*2\" #{output_video}"
    BrowserStack.logger.info("Debug: Combining screenshots into video...")
    output, status = BrowserStack::OSUtils.execute(ffmpeg_command, true, timeout: 300)
    BrowserStack.logger.info("Debug: Video created successfully as #{output_video}")
  end

  def self.run_cleanup_tests(device_id, timeout = 60, suite_type, subsuite_type, session_id) # rubocop:todo Metrics/AbcSize
    cleanup_tests_map = suite_type.eql?('validation') ? XCUI_CLEANUP_TESTS_MAP : XCUI_CLEANUP_POPULATION_TESTS_MAP
    cleanup_tests_map = cleanup_tests_map.select { |key, _| key.to_s.include?(subsuite_type) } unless subsuite_type == "all"
    all_cleanup_tests = cleanup_tests_map.keys
    passed_tests = []
    failed_tests = environment_variables = {}
    if device_version(device_id) >= 14
      chromium_bundle_id = Chromium.new(device_id).bundle_id
      environment_variables = { 'chromium_package': chromium_bundle_id }
      generate_bstack_xctestrun_file(device_id, environment_variables: environment_variables)
    else
      generate_bstack_xctestrun_file(device_id)
    end
    screenshot_dir = "#{TMP_DIR_PATH}/#{device_id}"
    FileUtils.mkdir_p(screenshot_dir)
    # Start recording
    recording_thread = start_recording(device_id, screenshot_dir)

    all_cleanup_tests.each do |test|
      stdout_file = "#{@@config['logging_root']}/browserstack_cleanup_test_#{device_id}_#{test}.log"
      start_banner = "\n\n#{'*' * 15} #{Time.now.utc} Starting Cleanup Test #{'*' * 15}\n\n"
      File.write(stdout_file, start_banner, mode: 'a')

      test_class_name = cleanup_tests_map[test][:class]
      test_function_name = cleanup_tests_map[test][:function]
      xctestrun_cmd = xctestrun_command(device_id, test_class_name, test_function_name, stdout_file, false, environment_variables)
      # xctestrun_cmd = "xcodebuild -xctestrun #{@xctest_xmlfile} -destination \"id=#{device_id}\" test-without-building -only-testing:\"BrowserStackUITests/#{test_class_name}/#{test_function_name}\" | tee -a #{stdout_file}"
      output, status = BrowserStack::OSUtils.execute(xctestrun_cmd, true, timeout: timeout)

      if output.include?("SUCCEEDED") && !output.include?("Executed 0 test")
        passed_tests << test_function_name
        BrowserStack.logger.info("Test #{test_class_name} @ #{test_function_name} PASSED with code #{status}")
        BrowserStack.logger.info(output)
      else
        failed_tests[test_function_name] = output
        BrowserStack.logger.error("Test #{test_class_name} @ #{test_function_name} FAILED with code #{status}")
        BrowserStack.logger.error(output)
      end

      end_banner = "\n\n#{'*' * 15} #{Time.now.utc} Completed Cleanup Test Execution #{'*' * 15}\n\n"
      File.write(stdout_file, end_banner, mode: 'a')
    end
    # Stop recording and generate video
    stop_recording(recording_thread)
    generate_video(screenshot_dir, session_id)
    s3_url = Utils.upload_qa_test_video_to_s3(screenshot_dir, device_id, session_id)
    FileUtils.rm_rf(screenshot_dir)

    { passed: passed_tests, passed_count: passed_tests.size, failed: failed_tests, failed_count: failed_tests.size, s3_url: s3_url }.to_json
  end

  def self.run_xcui_test(device_id, timeout = 60, test_class_name, test_function_name, environment_variables: {})
    BrowserStackAppHelper.check_and_install_browserstack_test_suite(device_id)
    generate_bstack_xctestrun_file(device_id, environment_variables: environment_variables)
    stdout_file = "#{@@config['logging_root']}/xcuitest_#{device_id}_#{test_function_name}.log"
    xctestrun_cmd = "xcodebuild -xctestrun #{@xctest_xmlfile} -destination \"id=#{device_id}\" test-without-building -only-testing:\"BrowserStackUITests/#{test_class_name}/#{test_function_name}\" | tee -a #{stdout_file}"
    output, status = BrowserStack::OSUtils.execute(xctestrun_cmd, true, timeout: timeout)
    { result: output, status: status }.to_json
  end

  def self.generate_bstack_xctestrun_file(uuid, xctestrun_file_name: "bstack_test_suite_xctestrun", environment_variables: {})
    @xctest_xmlfile = "/tmp/#{xctestrun_file_name}_#{uuid}.xml"
    @xctest_xmlfile = "/tmp/#{xctestrun_file_name}_#{uuid}.xctestrun" if device_version(uuid) >= 18

    data = {
      xctestname: "BrowserStackUITests",
      xctest_identifier: bstack_test_app_bundle_id,
      product_module_name: "BrowserStackUITests",
      xcui_app_identifier: "com.browserstack.app",
      environment_variables: environment_variables,
      language: nil,
      locale: nil
    }

    xctestrun_contents = ERB.new(File.open("#{__dir__}/../../templates/xcuitest_xctestrun.erb").read).result(ErbBinding.new(data).get_binding)
    Utils.write_to_file(@xctest_xmlfile, xctestrun_contents)
  end

  def self.cache_browserstack_app(uuid)
    return unless device_version(uuid) >= 17
    return unless BrowserStack::OSUtils.booted_in_last_n_minutes?(120) # last 2 hours

    temp_state_file_for_cached_browserstack_app = "/tmp/devicectl_browserstack_app_cache_#{uuid}"
    return if File.file?(temp_state_file_for_cached_browserstack_app)

    BrowserStack.logger.info "Caching browserstack app on reboot of host machine to cache it"

    begin
      device_state(uuid).touch_reinstall_browserstack_app_file
      check_and_build_and_install_browserstack_app(uuid)
      FileUtils.touch(temp_state_file_for_cached_browserstack_app)
      BrowserStack::Zombie.push_logs("ios-17-browserstack-app-cache-success", "", { "device" => uuid })
    rescue => e
      BrowserStack.logger.error("Failed to install browserstack app after reboot of host machine: #{e}")
      BrowserStack::Zombie.push_logs("ios-17-browserstack-app-cache-failure", e.message.to_s, { "device" => uuid })
    ensure
      device_state(uuid).remove_reinstall_browserstack_app_file
    end
  end

  def self.cache_as_test_app(uuid)
    # Only needed for iOS 17 that are booted in last 15 mins and don't have the test app cached in xcode already

    return unless device_version(uuid) >= 17
    return unless BrowserStack::OSUtils.booted_in_last_n_minutes?(120) # last 2 hours

    temp_state_file_for_cache = "/tmp/devicectl_cache_#{uuid}"
    return if File.file?(temp_state_file_for_cache)

    BrowserStack.logger.info "Copying browserstack app from signed folder to test app folder"

    begin
      # copy the browserstack app from signed folder
      # to /usr/local/.browserstack/xctest_session_apps_ios17/device_id/test_app.app
      # mkdir_p creates intermediate folders without error
      FileUtils.mkdir_p("#{XCTEST_TEST_APP_PATH_IOS17}/#{uuid}")
      path_to_test_app_bundle = "#{XCTEST_TEST_APP_PATH_IOS17}/#{uuid}/#{XCTEST_TEST_APP_NAME_IOS17}"
      FileUtils.cp_r(browserstack_app_path(uuid), path_to_test_app_bundle)

      BrowserStack.logger.info "Installing browserstack app as test_app.app using devicectl"
      msg, exit_status = DeviceCtl::Device.install(uuid, path_to_test_app_bundle)
      raise msg if exit_status != 0

      FileUtils.touch(temp_state_file_for_cache)
      BrowserStack::Zombie.push_logs("ios-17-test-app-cache-success", path_to_test_app_bundle, { "device" => uuid })
    rescue => e
      # sometimes this fails to install due to conflicting profile, use the old method in that case
      BrowserStack.logger.error("Failed to install app using devicectl: #{e}")
      BrowserStack::Zombie.push_logs("ios-17-test-app-cache-failure", e.message.to_s, { "device" => uuid })
    ensure
      FileUtils.rm_rf(path_to_test_app_bundle) if File.directory?(path_to_test_app_bundle)
    end
  end

  def self.log(level, message, uuid: nil)
    params = { subcomponent: 'BrowserStackAppHelper' }
    params[:device] = uuid unless uuid.nil?

    BrowserStack.logger.send(level.to_sym, message, params)
  end
end

if $PROGRAM_NAME == __FILE__
  # Mock logger - output to stdout
  require 'browserstack_logger'
  BrowserStack.init_logger('/dev/stdout')
  BrowserStack::Zombie.configure
  IdeviceUtils.configure(Configuration.conf)

  uuid = ARGV[1]
  case ARGV[0]

    # Sample command:
    # did=<device-id>
    # bundle exec ruby browserstack_app_helper.rb build_and_install $did
  when 'build_and_install'
    BrowserStack.logger.info "Building and installing for device #{uuid}"
    BrowserStack.logger.info "Ensure cleanup not running for device, or build may fail."

    BrowserStackAppHelper.build_and_install_browserstack_app(uuid)

    # Sample command:
    # did=<device-id>
    # bundle exec ruby browserstack_app_helper.rb run $did SettingsUITests testEnableLocationService
    # bundle exec ruby browserstack_app_helper.rb run $did SettingsUITests testGameCenterCleanup --screenshots
  when 'run'
    clazz = ARGV[2]
    function = ARGV[3]
    options = ARGV[4..]

    options = [] if options.nil?

    BrowserStack.logger.info("Running XCUITest: #{clazz}/#{function} on #{uuid}")
    BrowserStackAppHelper::XCUI_TESTS[function] = {
      class: clazz,
      function: function
    }

    BrowserStack.logger.info("Killing idevicesyslogs to disable device restrictions like opening settings")
    `ps -ef | grep #{uuid} | grep idevicesyslog | awk '{print $2}' | xargs -I{} kill {}`

    BrowserStackAppHelper.run_ui_test(uuid, function, capture_screenshots: options.include?('--screenshots'))

    # In case you want to capture syslogs as well while the test runs:
    # BrowserStackAppHelper.run_ui_test(uuid, function, capture_syslog: true)
  else
    BrowserStack.logger.error "Method #{ARGV[0]}, is not listed."
  end
end
