require_relative './passcode_helper'
require_relative './apple_wallet'
require_relative './sandbox_account'
require_relative '../../config/constants'
require_relative './assistive_touch_helper'
require_relative '../utils/utils'
require_relative '../models/device_state'
require_relative '../custom_exceptions'
require_relative '../utils/idevice_utils'

module Secure
  class ApplePay # rubocop:todo Metrics/ClassLength
    APPLE_PAY_MODULE_TAG = "[ApplePay]"
    KEYCHAIN_KEY_FOR_CONFIGURATION = "apple-pay-configured"

    # rubocop:disable Metrics/AbcSize
    def self.setup(device, params)
      BrowserStack.logger.info "#{APPLE_PAY_MODULE_TAG} - Setup"
      device_state = DeviceState.new(device)
      raise "Apple Pay is not configured on this device" unless Secure::ApplePay.apple_pay_configured?(device) || Secure::ApplePay.dedicated_cloud_apple_pay_device?(device)

      if device_state.passcode_file_present?
        # Using this as check for apple pay setup existing on device if mid session restart is done. Scenarios such as network logs.
        custom_pusher("ENABLING_APPLE_PAY_SUCCESSFUL", params, device) # to remove load screen in FE
        return
      end

      device_state.touch_settings_automation_executing_file

      custom_pusher("APPLE_PAY_INITIATED", params, device)

      product = params['product'] || params['genre']
      session_id = product == "live_testing" ? params["live_session_id"] : params["app_live_session_id"]
      session_id = params["session_id"] if session_id.nil?
      apple_pay_helper = ApplePay.new(device, session_id, product)

      steps = nil
      steps = ["enable_passcode"] if params["uses_new_apple_pay_flow"].to_s == "true"
      result = apple_pay_helper.prepare_device(REDIS_CLIENT, params, steps)

      unless result
        sleep 5
        raise BrowserStack::SessionException, "Can't initiate session on device #{device} with APPLE PAY"
      end

      custom_pusher("ENABLING_APPLE_PAY_SUCCESSFUL", params, device)
    rescue => e
      error = e.message
      BrowserStack.logger.error "#{APPLE_PAY_MODULE_TAG} Error in Apple Pay Setup : #{e.message}"
      custom_pusher("ENABLING_APPLE_PAY_UNSUCCESSFUL", params, device)
      raise e
    ensure
      if error.nil? && params['live_apple_pay_session'].to_s == "true"
        BrowserStack::WebDriverAgent.launch_browser_with_url(
          (params["use_blank_url"] ? '' : params["url"]),
          params["device_browser"],
          params["iproxyPort"],
          params["device_version"].to_f
        )
      end
      device_state.remove_settings_automation_executing_file
    end
    # rubocop:enable Metrics/AbcSize

    def self.setup_wallet_and_assistive_touch(device, params)
      BrowserStack.logger.info "#{APPLE_PAY_MODULE_TAG} - Setting Up Wallet & Assistive Touch"
      device_state = DeviceState.new(device)
      return unless Secure::ApplePay.apple_pay_configured?(device)

      product = params['product']
      session_id = product == "live_testing" ? params["live_session_id"] : params["app_live_session_id"]

      apple_pay_helper = ApplePay.new(device, session_id, product)

      steps = []
      steps += ["add_card"] unless device_state.apple_pay_data_file_present?
      steps += ["enable_assistive_touch"] unless device_state.apple_pay_assistive_touch_file_present?

      device_state.touch_settings_automation_executing_file

      params['reopen_foreground_app'] = true
      result = apple_pay_helper.prepare_device(REDIS_CLIENT, params, steps)
      raise "Failed to setup Wallet and/or Assistive Touch" unless result

      Secure::ApplePay.custom_pusher("APPLE_PAY_WALLET_ASSISTIVE_TOUCH_SUCCESSFULL", params, device)
    ensure
      device_state.remove_settings_automation_executing_file
    end

    def initialize(device_id, session_id, product = "")
      BrowserStack.logger.info "#{APPLE_PAY_MODULE_TAG} - Initialize"
      @udid = device_id
      @session_id = session_id
      @product = product
      @server_config = BrowserStack::Configuration.new.all
      @device_config = DeviceManager.device_configuration_check(@udid)
      @apple_pay_setup_reporter = DataReportHelper.new('apple-pay-setup',
                                                       session_id: session_id,
                                                       product: product,
                                                       ios_version: @device_config["device_version"],
                                                       device: device_id)
      @configuration_reporter = DataReportHelper.new('apple-pay-configuration',
                                                     session_id: session_id,
                                                     product: product,
                                                     ios_version: @device_config["device_version"],
                                                     device: device_id)
      @device_state = DeviceState.new(device_id)
      @client = WdaClient.new(@device_config['webdriver_port'])
    end

    def prepare_device(redis_client, params, steps = nil) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
      BrowserStack.logger.info "#{APPLE_PAY_MODULE_TAG} - prepare_device - #{steps}"

      steps ||= ["enable_passcode", "add_card", "enable_assistive_touch"]
      steps += ["add_apple_pay_details"] if params["applePayDetails"]

      result = {}

      start_time = Time.now.to_i

      foreground_app_bundle_id = current_foreground_app if params['reopen_foreground_app'].to_s == 'true' && !steps.empty?

      if steps.include?("enable_passcode")
        BrowserStack.logger.info "#{APPLE_PAY_MODULE_TAG} - Enabling Passcode"
        Secure::ApplePay.custom_pusher("START_ENABLING_DEVICE_PASSCODE", params, @udid)
        result["enable_passcode"] = enable_passcode(redis_client, params)
        unless result["enable_passcode"]
          Secure::ApplePay.custom_pusher("ENABLING_DEVICE_PASSCODE_UNSUCCESSFUL", params, @udid)
          raise WdaAutomationError, "Passcode setup Failed"
        end
      end
      if steps.include?("add_card")
        BrowserStack.logger.info "#{APPLE_PAY_MODULE_TAG} - Adding Card"
        Secure::ApplePay.custom_pusher("START_ADDING_CARD_TO_WALLET", params, @udid)
        result["add_card"] = setup_wallet(params)
        unless result["add_card"]
          Secure::ApplePay.custom_pusher("ADDING_CARD_TO_WALLET_UNSUCCESSFUL", params, @udid)
          raise WdaAutomationError, "Wallet setup Failed"
        end
      end
      if steps.include?("enable_assistive_touch")
        BrowserStack.logger.info "#{APPLE_PAY_MODULE_TAG} - Enabling Assistive Touch"
        Secure::ApplePay.custom_pusher("START_ENABLING_ASSISTIVE_TOUCH", params, @udid)
        result["enable_assistive_touch"] = enable_assistive_touch(params)
        unless result["enable_assistive_touch"]
          Secure::ApplePay.custom_pusher("ENABLING_ASSISTIVE_TOUCH_UNSUCCESSFUL", params, @udid)
          raise WdaAutomationError, "Assitive touch setup Failed"
        end
      end

      if steps.include?("add_apple_pay_details")
        log_info "Adding apple_pay_details"
        apple_pay_prefill_detail_helper = ApplePayPrefillDetailHelper.new(@udid, { product: @product, session_id: @session_id })
        res, code = apple_pay_prefill_detail_helper.execute(params['applePayDetails'])
        result["add_apple_pay_details"] = res
        raise WdaAutomationError, "Apple Pay setup Failed" unless res
      end
      Secure::ApplePay.custom_pusher("START_ENABLING_APPLE_PAY", params, @udid)
      result.values.all?
    rescue => e
      BrowserStack.logger.info "#{APPLE_PAY_MODULE_TAG} - prepare_device failed, steps: #{steps}, result: #{result}, error: #{e.message}, #{e.backtrace.join("\n")}"
      false
    ensure
      launch_foreground_app(foreground_app_bundle_id)
      if result["add_card"] && !@session_id&.include?("cleanup_onboarding")
        @device_state.touch_apple_pay_data_file
        @device_state.write_to_apple_pay_data_file(params["card_network"]) unless params["card_network"].nil?
      end
      @device_state.touch_apple_pay_assistive_touch_file if result["enable_assistive_touch"]
      data = @apple_pay_setup_reporter.populate_data_hash(additional_data: { "steps" => steps, "passcode_response" => result["enable_passcode"], "wallet_response" => result["add_card"], "assistive_touch_response" => result["enable_assistive_touch"], "status" => result.values.all? }, interval: Time.now.to_i - start_time)
      @apple_pay_setup_reporter.report(data)
    end

    def self.dedicated_cloud_apple_pay_device?(device)
      BrowserStack.logger.info "#{APPLE_PAY_MODULE_TAG} - Checking if Private Cloud Apple Pay Device"
      device_state = DeviceState.new(device)
      is_dedicated_cloud_device = device_state.dedicated_device_file_present?
      is_dedicated_cloud_apple_pay_device = DEDICATED_CLOUD_APPLE_PAY_DEVICES.include? [device]

      BrowserStack.logger.info "Dedicated Cloud Apple Pay devices count: #{DEDICATED_CLOUD_APPLE_PAY_DEVICES.length}, Is Dedicated Cloud Apple Pay Device: #{is_dedicated_cloud_apple_pay_device}, Is Dedicated Device, #{is_dedicated_cloud_device}, Is Dedicated Cloud and Apple Pay Device: #{is_dedicated_cloud_device && is_dedicated_cloud_apple_pay_device}"

      result = is_dedicated_cloud_device && is_dedicated_cloud_apple_pay_device
      BrowserStack.logger.info "#{APPLE_PAY_MODULE_TAG} - Device is #{result ? '' : 'NOT'} A Private Cloud Apple Pay Device"
      result
    end

    def self.apple_pay_device?(device)
      BrowserStack.logger.info "#{APPLE_PAY_MODULE_TAG} - Checking if Public Cloud Apple Pay Device"
      is_apple_pay_device = APPLE_PAY_DEVICES.include? [device]
      BrowserStack.logger.info "Apple Pay devices count: #{APPLE_PAY_DEVICES.length}, Is Device, #{device}, Apple Pay Device: #{is_apple_pay_device}"
      BrowserStack.logger.info "#{APPLE_PAY_MODULE_TAG} - Device is #{is_apple_pay_device ? '' : 'NOT'} A Public Cloud Apple Pay Device"
      is_apple_pay_device
    end

    def self.apple_pay_configured?(device)
      device_state = DeviceState.new(device)
      apple_pay_device?(device) && device_state.apple_pay_configuration_file_present?
    end

    def self.custom_pusher(message, params, device)
      product = params['product'] || params['genre']
      if [APP_LIVE, APP_LIVE_TESTING].include?(product)
        Utils.notify_pusher(message, params, device)
      elsif [LIVE, LIVE_TESTING].include?(product)
        Utils.notify_pusher_for_live(message, params, device)
      end
    end

    # Evaluates the requirements for Apple Pay configuration, including checking for the presence of a sandbox account
    # and DC configuration. Reports the results of the evaluation and configuration steps.
    #
    # @return [Boolean] Returns true if the configuration steps were successful, otherwise false.
    def evaluate_apple_pay  # rubocop:disable Metrics/AbcSize
      BrowserStack.logger.info "#{APPLE_PAY_MODULE_TAG} - Evaluating Apple Pay"
      start_time = Time.now.to_f
      apple_pay_already_configured = configured?
      @device_state.remove_apple_pay_configuration_file unless apple_pay_already_configured

      icloud_account_already_logged_in = icloud_account_logged_in?
      if apple_pay_already_configured && icloud_account_already_logged_in
        # recreate the file if missing - occurs when host has been changed but device is configured
        @device_state.touch_apple_pay_configuration_file unless @device_state.apple_pay_configuration_file_present?
        return true
      end

      sandbox_account_present = add_sandbox_account if !apple_pay_already_configured || !icloud_account_already_logged_in
      dc_configuration_present = apple_pay_already_configured || Secure::AssistiveTouchHelper.dc_configured?(@udid, @client) ? true : false

      case [sandbox_account_present, dc_configuration_present]
      when [true, true]
        res = apple_pay_already_configured
        res ||= session_ready? ? final_steps : false
        elapsed_time = (Time.now.to_f - start_time).round(2)
        log_and_report("Both sandbox account and DC configuration are present", apple_pay_already_configured, true, true, res, elapsed_time)
        @device_state.touch_apple_pay_configuration_file if res && !@device_state.apple_pay_configuration_file_present?
        res
      when [true, false]
        elapsed_time = (Time.now.to_f - start_time).round(2)
        log_and_report("Sandbox account is present, but DC configuration is missing", apple_pay_already_configured, false, true, false, elapsed_time)
        res = false
      when [false, true]
        # We are not attempting to sign in sandbox account here as add_sandbox_account itself adds sandbox account if not found and early exits with success when found
        elapsed_time = (Time.now.to_f - start_time).round(2)
        log_and_report("Sandbox account is missing, but DC configuration is present", apple_pay_already_configured, true, false, false, elapsed_time)
        res = false
      when [false, false]
        elapsed_time = (Time.now.to_f - start_time).round(2)
        log_and_report("Both sandbox account and DC configuration are missing", apple_pay_already_configured, false, false, false, elapsed_time)
        res = false
      else
        BrowserStack.logger.info "#{APPLE_PAY_MODULE_TAG} #{__method__} - Unhandled case"
        res = false
      end

      res
    end

    # Checks the configuration status by performing a configuration check operation using webdriveragent.
    #
    # This method initiates the configuration check process, measures the time taken for the check,
    # and reports the results using the provided configuration reporter.
    #
    # @return [Boolean] Returns `true` if the configuration check is successful, otherwise `false`.
    def configured?
      start_time = Time.now.to_f
      begin
        res = perform_configuration_check(KEYCHAIN_KEY_FOR_CONFIGURATION)
        elapsed_time = (Time.now.to_f - start_time).round(2)
        data = { status: "pass", task: "configuration", step: "initial check", elapsed_time: elapsed_time, error: 'no error' }
        @configuration_reporter.report(data)
        true
      rescue WdaClientError => e
        elapsed_time = (Time.now.to_f - start_time).round(2)
        data = { status: "fail", task: "configuration", step: "initial check", elapsed_time: elapsed_time, error: e.message }
        @configuration_reporter.report(data)
        false
      rescue => e
        elapsed_time = (Time.now.to_f - start_time).round(2)
        data = { status: "fail", task: "configuration", step: "initial check", elapsed_time: elapsed_time, error: e.message }
        @configuration_reporter.report(data)
        false
      end
    end

    # Sets the configuration as "configured" by performing a configuration set operation using webdriveragent.
    #
    # This method initiates the configuration set process, measures the time taken for the setup,
    # reports the results using the provided configuration reporter, and configuration touches apple_pay_configuration
    # state file for use in device thread.
    #
    # @return [Boolean] Returns `true` if the configuration setup is successful, otherwise `false`.
    def set_as_configured
      start_time = Time.now.to_f
      begin
        res = perform_configuration_set(KEYCHAIN_KEY_FOR_CONFIGURATION)
        elapsed_time = (Time.now.to_f - start_time).round(2)
        data = { status: "pass", task: "configuration", step: "final step", elapsed_time: elapsed_time, error: 'no error' }
        @configuration_reporter.report(data)
        @device_state.touch_apple_pay_configuration_file
        res
      rescue WdaClientError => e
        elapsed_time = (Time.now.to_f - start_time).round(2)
        data = { status: "fail", task: "configuration", step: "final step", elapsed_time: elapsed_time, error: e.message }
        @configuration_reporter.report(data)
        @device_state.remove_apple_pay_configuration_file
        false
      rescue => e
        elapsed_time = (Time.now.to_f - start_time).round(2)
        data = { status: "fail", task: "configuration", step: "final step", elapsed_time: elapsed_time, error: e.message }
        @configuration_reporter.report(data)
        @device_state.remove_apple_pay_configuration_file
        false
      end
    end

    # Checks if a session start automations for apple pay are working correctly or not.
    #
    # It first prepares the device using the provided REDIS_CLIENT and an empty option hash.
    # Reports the result to the configuration reporter.
    # If all of the automations run is successful, it reports a 'pass' status.
    # Otherwise, it reports a 'fail' status.
    # The UDID of the device is included in the report.
    # Sandbox login automation failure snapshots are stores as apple_pay_sandbox_#{@udid}.png in the screenshot_dir
    # Wallet automation failure snapshots are stores as apple_pay_wallet_#{@udid}.png in the screenshot_dir
    #
    # @return [Boolean] true if the session is ready, false otherwise.
    def session_ready?
      result = prepare_device(REDIS_CLIENT, {})
      data = {
        "status" => result ? "pass" : "fail",
        "task" => "configuration",
        "step" => "session start automations sanity run",
        "device" => @udid,
        "ip" => @device_config["ip"],
        "sandbox_login_failure_snapshot_path" => "#{@server_config['screenshot_dir']}/apple_pay_sandbox_#{@udid}.png",
        "wallet_automation_failure_snapshot_path" => "#{@server_config['screenshot_dir']}/apple_pay_wallet_#{@udid}_{1-5}.png"
      }
      @configuration_reporter.report(data)
      result
    end

    private

    def enable_passcode(redis_client, params)
      BrowserStack.logger.info "#{APPLE_PAY_MODULE_TAG} - Enabling Passcode"
      passcode_helper = Passcode.new(@udid, @session_id, @product, "apple-pay")
      passcode_helper.passcode_settings(redis_client)
      passcode_helper.set_passcode
    end

    def setup_wallet(params)
      BrowserStack.logger.info "#{APPLE_PAY_MODULE_TAG} - Setting Up Wallet"
      wallet_helper = AppleWallet.new(@udid, @session_id, @product)
      wallet_helper.add_sandbox_card(
        params['apple_pay_cards'].nil? ? nil : JSON.parse(params['apple_pay_cards']),
        params['card_network']
      )

    end

    def enable_assistive_touch(params)
      BrowserStack.logger.info "#{APPLE_PAY_MODULE_TAG} - Enabling Assistive Touch"
      AssistiveTouchHelper.new(@udid, @session_id, @product, "apple-pay").switch("enable")
    end

    # Adds a sandbox account using the Sandbox Account Helper.
    #
    # This method initializes a Sandbox Account Helper instance and adds a sandbox account.
    def add_sandbox_account
      BrowserStack.logger.info "#{APPLE_PAY_MODULE_TAG} - Adding Sandbox Account"
      Secure::SandboxAccount.new(@udid).add_sandbox_account
    end

    # Performs a configuration check using the specified keychain item.
    #
    # This method searches the keychain using webdriveragent for the given keychain item, handles the response,
    # and reports the results based on the response status.
    #
    # @param [String] keychain_item The keychain item to perform a check on.
    def perform_configuration_check(keychain_item)
      @client.search_keychain(keychain_item).tap do |response|
        handle_response(response)
      end
    end

    # Performs a configuration set operation using the specified keychain item.
    #
    # This method adds the specified keychain item, handles the response,
    # and reports the results based on the response status.
    #
    # @param [String] keychain_item The keychain item to perform a set operation on.
    def perform_configuration_set(keychain_item)
      @client.add_to_keychain(keychain_item).tap do |response|
        handle_response(response)
      end
    end

    def icloud_account_logged_in?
      @client.apple_id_signed_in['value']['status'] == 'pass'
    rescue => e
      BrowserStack.logger.error "Failed to get iCloud Login Status, error: #{e.message}"
      return true if e.message.include? "unknown command"

      false
    end

    def current_foreground_app
      @client.foreground
    rescue => e
      BrowserStack.logger.error "#{APPLE_PAY_MODULE_TAG} Error occured while fetching foreground app bundle id: #{e.message}"
      nil
    end

    def launch_foreground_app(foreground_app_bundle_id)
      @client.set_foreground(foreground_app_bundle_id, "app_name", {}, @session_id) unless foreground_app_bundle_id.nil?
    rescue => e
      BrowserStack.logger.error "#{APPLE_PAY_MODULE_TAG} Error occured while setting foreground app: #{e.message}"
      nil
    end

    # Handles the response from a configuration operation.
    #
    # This method raises a WdaClientError exception if the response status is not "pass",
    # indicating an internal error while using keychain endpoints in WDA.
    #
    # @param [Hash] response The response received from a configuration operation.
    # @return [Boolean] Returns `true` if the response status is "pass".
    # @raise [WdaClientError] If the response status is not "pass".
    def handle_response(response)

      raise WdaClientError.new("internal-error"), "Internal error while using keychain endpoints in WDA" unless response['value']['status'] == "pass"

      true
    end

    # Performs the final configuration steps including clearing passcode, disabling Assistive Touch, and disabling Safari sync.
    # @return [Boolean] Returns true if all final configuration steps were successful, otherwise false.
    def final_steps
      BrowserStack.logger.info "#{APPLE_PAY_MODULE_TAG} - Final Steps"
      # Clear passcode for the device
      clear_passcode = Passcode.new(@udid, @session_id, @product).clear_passcode(REDIS_CLIENT)

      # Disable Assistive Touch for the device
      disable_assistive_touch = AssistiveTouchHelper.new(@udid, @session_id, @product).switch("disable")

      # Disable Safari sync using the client and extract the status
      resp = @client.disable_safari_sync
      disable_safari_sync = resp['value']['status'] == 'pass'

      # Determine if all final configuration steps were successful
      result = clear_passcode && disable_assistive_touch && disable_safari_sync
      status = result ? "pass" : "fail"

      # Mark device as configured, touching machine state file for use in device thread
      set_as_configured

      # Prepare data for reporting
      data = {
        "status" => status,
        "task" => "configuration",
        "step" => "final steps",
        "clear_passcode" => clear_passcode,
        "disable_assistive_touch" => disable_assistive_touch,
        "disable_safari_sync" => disable_safari_sync,
        "device" => @udid
      }

      # Report the final configuration result
      @configuration_reporter.report(data)

      result
    end

    def log_and_report(message, apple_pay_already_configured, dc_config_present, sandbox_config_present, session_ready, elapsed_time)
      BrowserStack.logger.info "#{APPLE_PAY_MODULE_TAG} - evaluate_apple_pay - #{message}"
      data = {
        "status" => session_ready ? "pass" : "fail",
        "task" => "configuration",
        "step" => "evaluate_apple_pay",
        "device" => @udid,
        "error" => "apple pay evaluation failure",
        "apple_pay_already_configured" => apple_pay_already_configured,
        "dc_configuration_present" => dc_config_present,
        "sandbox_configuration_present" => sandbox_config_present,
        "session_ready" => session_ready,
        "elapsed_time" => elapsed_time
      }
      @configuration_reporter.report(data)
    end

    def log_info(message)
      BrowserStack.logger.info "[#{APPLE_PAY_MODULE_TAG}] #{message}"
    end
  end
end
