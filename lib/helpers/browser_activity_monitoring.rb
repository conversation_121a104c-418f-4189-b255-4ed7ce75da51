require 'browserstack_logger'
require 'json'

require_relative '../../config/constants'
require_relative '../utils/utils'
require_relative "../utils/osutils"

# currently supports device browser chrome and safari
class BrowserActivityMonitoring
  def initialize(session_id, device_id, debugger_port, genre)
    @session_id = session_id
    @device_id = device_id
    @debugger_port = debugger_port
    @genre = genre
    @history = {}

    BrowserStack.logger.info("BrowserActivityMonitoring initialize #{@session_id}, #{@device_id}, #{@debugger_port}")
  end

  def start
    # this is a safe check
    Timeout.timeout(MAX_TERMINAL_BLOCK_TIME) do
      Thread.bs_run do
        start_file = self.class.start_file(@device_id)
        FileUtils.touch(start_file)

        while File.exist?(start_file)
          begin
            send_history_to_eds
          rescue => e
            BrowserStack.logger.error("BrowserActivityMonitoring exception: #{e.message}, #{e.backtrace.join("\n")}")
            break
          end
          sleep(10)
        end

      ensure
        BrowserStack.logger.info("BrowserActivityMonitoring start ensure block")
        File.delete(start_file) if File.exist?(start_file)
      end
    end
  end

  class << self
    def start_file(device_id)
      "#{STATE_FILES_DIR}/start_monitoring_#{device_id}"
    end

    def running?(device_id)
      # check for start file, if exists then already running
      File.exist?(start_file(device_id))
    end
  end

  private

  def send_history_to_eds
    json_list = JSON.parse(OSUtils.execute("curl http://localhost:#{@debugger_port}/json/list"))
    unless json_list.empty?
      json_list.each do |list|
        url = list["url"]
        next unless !url.empty? && !@history.key?(url)

        first_request_time = Time.now.utc.strftime("%Y-%m-%dT%H:%M:%SZ")
        @history[url] = first_request_time
        File.open(self.class.start_file(@device_id), 'r') do |file|
          file.flock(File::LOCK_SH)
          value = file.gets
          @session_id = value.chomp unless value.nil?
          file.flock(File::LOCK_UN)
        end
        data = {
          session_id: @session_id,
          product: @genre,
          url_accessed: url,
          first_request_time: first_request_time,
          requests_count: 1
        }
        BrowserStack.logger.info("BrowserActivityMonitoring send_history_to_eds data: #{data}")
        Utils.send_to_eds(data, "session_network_logs", true)
      end
    end
  end
end
