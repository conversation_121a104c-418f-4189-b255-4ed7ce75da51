require_relative './automation'

# This sole purpose of this module to toggle airplane mode in ios devices.
# Toggle in the sense, if it is on, it disables it, if it is off, then it enables it
#
# toggle_airplane_mode takes a parameter is_already_set which means
# was the airplane mode enabled previously. Why do we need this check?
# Sometimes in ios devices, when we disable the airplane mode, system generates
# a no sim pop up, then it has to be handles by clickin on ok button on the popup.
# hence, the parameter is_already_set. Also, it is used only to trigger the click. If it is not clicked/handled
# it can cause issues in cleanup as observed while testing this code.
module Automation
  class AirplaneModeAutomation
    def initialize(device_id)
      @appium_driver = Automation.open_settings_app(device_id)
    end

    def toggle_airplane_mode(is_already_set = false)

      airplane_button = @appium_driver.find_elements(:name, 'Airplane Mode')
      airplane_button = @appium_driver.find_elements(:name, 'Aeroplane Mode') if airplane_button.empty?
      raise "Airplane button is nil" if airplane_button.empty?

      airplane_button[0].click
      Automation.check_and_click_sim_card_popup(@appium_driver) if is_already_set
    ensure
      @appium_driver.driver_quit

    end
  end
end
