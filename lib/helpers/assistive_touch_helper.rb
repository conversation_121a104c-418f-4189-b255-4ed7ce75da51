require_relative '../custom_exceptions'
require_relative './browserstack_app_helper'
require_relative './wda_client'
require_relative '../models/device_state'
require_relative '../models/lockdown_device_state'
require_relative '../configuration'
require_relative '../utils/zombie'
require_relative '../models/ios_device'
require_relative '../utils/idevice_utils'

module Secure
  # This class handles assistive touch i.e. enable, disable and configure.
  class AssistiveTouchHelper # rubocop:todo Metrics/ClassLength
    TAG = "[ASSISTIVE_TOUCH]"
    ENABLE = "enable"
    DISABLE = "disable"
    CONFIGURE = "configure"

    def self.dc_configured?(device, wda_client)
      keychain_response = wda_client.search_keychain("confirm-with-assistive-touch")

      if keychain_response['value']['status'].eql?("pass")
        true
      else
        check_for_confirm_with_assistive_touch(device)
        keychain_response_after_configure = wda_client.search_keychain("confirm-with-assistive-touch")
        keychain_response_after_configure['value']['status'].eql?("pass")
      end

    rescue WdaClientError, StandardError => e
      # TODO: add instrumentation
      BrowserStack.logger.error "Encountered error while checking for DC configuration - #{e.message} #{e.backtrace}"
      false
    end

    def self.check_for_confirm_with_assistive_touch(device)
      passcode_helper = Passcode.new(device, nil, nil)
      passcode_helper.passcode_settings(REDIS_CLIENT)
      passcode_set = passcode_helper.set_passcode
      assistive_touch_helper = AssistiveTouchHelper.new(device, nil, nil)
      assistive_touch_helper.switch("configure", force_configure: true)
      passcode_helper.clear_passcode(REDIS_CLIENT)
    end

    def initialize(device_id, session_id, product = "", feature = "")
      @file_path = "/tmp/assistive_touch_opened_#{device_id}"
      @start_time = @end_time = Time.now.to_i
      @device_state = DeviceState.new(device_id)
      @lockdown_device_state = LockdownDeviceState.new(device_id)
      @uuid = device_id
      @product = product
      @feature = feature
      @session_id = session_id
      @device_config = DeviceManager.device_configuration_check(@uuid)
      @ios_version = @device_config["device_version"]
      @ios_device = IosDevice.new(device_id, self.class.to_s, BrowserStack.logger)
      BrowserStack::Zombie.configure
    end

    def event_logger(toggle, data)
      event_name = "web_events"
      data_to_push = { event_name: "toggle-assistive-touch",
                       product: @product,
                       os: IdeviceUtils.os(@uuid),
                       os_version: @ios_version,
                       feature: @feature,
                       team: "device_features" }
      event_json = {
        session_id: @session_id,
        mode: toggle
      }
      event_json.merge!(data)
      data_to_push.merge!({ event_json: event_json })
      BrowserStack.logger.info("#{TAG} - Sending data to EDS regarding #{toggle} of assistive-touch on device : #{@uuid} for session ID : #{@session_id}")
      Utils.send_to_eds(data_to_push, event_name, true)
      if data[:status] == 'fail'
        BrowserStack.logger.info("#{TAG} - Sending data to ZOMBIES regarding failure to #{toggle} assistive-touch on device : #{@uuid} for session ID : #{@session_id}")
        BrowserStack::Zombie.push_logs("assistive-touch-failure",
                                       "AutomationError",
                                       { "device" => @uuid,
                                         "product" => @product,
                                         "session_id" => @session_id,
                                         "os_version" => @ios_version,
                                         "data" => {
                                           "error_message" => data[:error_message],
                                           "mode" => toggle
                                         },
                                         "error" => data[:error_reason] })
      end
    end

    def failure_data(toggle, error_object, error_message)
      @end_time = Time.now.to_i
      f_data = {
        status: "fail",
        error_reason: error_object,
        error_message: error_message,
        time_taken: @end_time - @start_time
      }
      event_logger(toggle, f_data)
      false
    end

    def switch(toggle, force_configure: false)
      BrowserStack.logger.info("#{TAG} - Started to #{toggle} assistive-touch on device : #{@uuid} for session ID : #{@session_id}")
      @start_time = Time.now.to_i

      begin
        reason = "unknown error"
        message = "No payload in response"

        case toggle

        when CONFIGURE
          check_assistive_touch_and_configure(reason, message, force_configure: force_configure)
        when ENABLE
          # 1. Configure AssistiveTouch
          check_assistive_touch_and_configure(reason, message, force_configure: force_configure)
          # 2. Enable AssistiveTouch
          enable_assistive_touch
          validate_response(toggle, 'no error', "AssistiveTouch successfully enabled")
        when DISABLE
          disable_assistive_touch
          validate_response(toggle, 'no error', "AssistiveTouch successfully disabled")
        else
          BrowserStack.logger.info "#{TAG} - Invalid toggle parameter sent"
        end

        BrowserStack.logger.info("#{TAG} - Completed to #{toggle} assistive-touch on device: #{@uuid} for session ID: #{@session_id}")
        true
      rescue WdaAutomationError => e
        failure_data(toggle, e.response_object, e.message)
        false
      rescue => e
        failure_data(toggle, "unknown code error", e.message)
        false
      end
    end

    def check_assistive_touch_and_configure(reason, message, force_configure: false) # rubocop:todo Metrics/AbcSize
      if @feature == "apple-pay"
        apple_assistive_touch_configuration = @lockdown_device_state.get_apple_pay_assistive_touch_key.to_s.chomp
        if apple_assistive_touch_configuration.empty? || force_configure
          res = configure_assistive_touch
          BrowserStack.logger.info("#{TAG} - Configuring assistive-touch on device : #{@uuid} for session ID : #{@session_id}")
          @lockdown_device_state.set_apple_pay_assistive_touch_key("true") if res['value']['error'].eql?('no error')
          @lockdown_device_state.set_assistive_touch_configuration_key("true") if res['value']['error'].eql?('no error')
          res['value'].nil? == true ? validate_response("configure", reason, message) : validate_response("configure", res['value']['error'], res['value']['message'])
        end
      else
        assistive_touch_configuration = @lockdown_device_state.get_assistive_touch_configuration_key.to_s.chomp
        if assistive_touch_configuration.empty? || force_configure
          res = configure_assistive_touch
          BrowserStack.logger.info("#{TAG} - Configuring assistive-touch on device : #{@uuid} for session ID : #{@session_id}")
          @lockdown_device_state.set_assistive_touch_configuration_key("true") if res['value'].nil? == false && res['value']['error'].eql?('no error')
          res['value'].nil? == true ? validate_response("configure", reason, message) : validate_response("configure", res['value']['error'], res['value']['message'])
        end
      end
    end

    def validate_response(operation, reason, message)
      if reason == 'no error'
        BrowserStack.logger.info("#{TAG} - Successfully #{operation}d assistive-touch menu on device : #{@uuid} for session ID : #{@session_id}")
        if operation != 'configure'
          handle_state_file(operation)
          @end_time = Time.now.to_i
          s_data = {
            status: "pass",
            error_reason: reason,
            error_message: message,
            time_taken: @end_time - @start_time
          }
          event_logger(operation, s_data)
        end
      else
        BrowserStack.logger.info("#{TAG} - Failed to #{operation} assistive-touch on device : #{@uuid} for session ID #{@session_id} due to reason: #{reason} message: #{message}")
        raise WdaAutomationError.new(reason), "Failed to #{operation} assistive-touch - #{message}"
      end
      true
    end

    def handle_state_file(toggle)
      @device_state.touch_assistive_touch_opened_file if toggle == ENABLE
      if toggle == DISABLE
        @device_state.remove_assistive_touch_opened_file
        @device_state.remove_apple_pay_assistive_touch_file
      end
    end

    private

    def configure_assistive_touch
      BrowserStack.logger.info("#{TAG} - Configuring assistive-touch on device : #{@uuid} for session ID : #{@session_id}")

      client = WdaClient.new(@device_config['webdriver_port']) # returns a hash
      res = client.play_with_assistive_touch("configure") # returns a hash

      BrowserStack.logger.info("#{TAG} - Configured assistive-touch on device : #{@uuid} for session ID : #{@session_id}")

      res
    rescue => e
      BrowserStack.logger.error("Failed to configure assistive-touch on device: #{@uuid} for session ID: #{@session_id}, error: #{e.message}, #{e.backtrace.join("\n")}")
      raise e
    end

    def enable_assistive_touch
      BrowserStack.logger.info("#{TAG} - Enabling assistive-touch on device: #{@uuid} for session ID: #{@session_id}")

      @ios_device.enable_assistive_touch

      BrowserStack.logger.info("#{TAG} - Enabled assistive-touch on device: #{@uuid} for session ID: #{@session_id}")
    rescue => e
      BrowserStack.logger.error("#{TAG} - Failed to enable assistive-touch on device: #{@uuid} for session ID: #{@session_id}")
      raise e
    end

    def disable_assistive_touch
      BrowserStack.logger.info("#{TAG} - Disabling assistive-touch on device: #{@uuid} for session ID: #{@session_id}")

      @ios_device.disable_assistive_touch

      BrowserStack.logger.info("#{TAG} - Disabled assistive-touch on device: #{@uuid} for session ID: #{@session_id}")
    rescue => e
      BrowserStack.logger.error("#{TAG} - Failed to disable assistive-touch on device: #{@uuid} for session ID: #{@session_id}")
      raise e
    end
  end
end
