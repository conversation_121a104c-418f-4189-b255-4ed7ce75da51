require_relative '../utils/utils'
require_relative '../utils/idevice_utils'
require_relative '../../server/device_manager'
require 'base64'
require 'json'

module Secure
  class SandboxAccount
    SANDBOX_ACCOUNT_TAG = "[SANDBOX_ACCOUNT]"

    def initialize(device_id)
      @udid = device_id
      @server_config = BrowserStack::Configuration.new.all
      @device_config = DeviceManager.device_configuration_check(@udid)
      @apple_pay_sandbox_mapping = begin
        JSON.parse(File.read("#{CONFIG_PATH}/custom_devices/apple_pay_sandbox_mapping.json"))
      rescue
        {}
      end
      @sandbox_account_data_reporter = DataReportHelper.new('sandbox_account_addition', ios_version: @device_config["device_version"], device: device_id)
    end

    def add_sandbox_account
      @start_time = Time.now.to_f
      @success_data = { device: @udid }
      @failure_data = { device: @udid }
      setup_and_execute
    rescue Wda<PERSON><PERSON>mation<PERSON>rror, WdaClientError => e
      handle_error(e)
      false
    rescue => e
      handle_standard_error(e)
      false
    ensure
      reset_environment
      report_data = { **@success_data, **@failure_data }
      @sandbox_account_data_reporter.report(report_data)
    end

    private

    def setup_and_execute
      setup_environment
      # We need to kill the appleaccountd, familycircled processes to ensure that cache is cleared, helps in logging
      # into the sandbox account and adding card to wallet respectively.
      # read more here: https://browserstack.atlassian.net/wiki/spaces/ENG/pages/**********/Apple+Pay+Sign+In+Issue
      # and here: https://browserstack.atlassian.net/browse/DF-3592?focusedCommentId=1329133

      if @device_config["device_version"].to_i >= 18
        IdeviceUtils.kill_process_with_name(@udid, "appleaccountd")
        IdeviceUtils.kill_process_with_name(@udid, "familycircled")
      end

      sandbox_account = find_sandbox_account
      sign_in_response = sign_in(sandbox_account)
      handle_response(sign_in_response, sandbox_account)
    end

    def setup_environment
      configure_socks5_forwarder
    end

    def reset_environment
      @socks5_forwarder.reset_privoxy
    end

    def configure_socks5_forwarder
      @socks5_forwarder ||= Privoxy::Socks5Forwarder.new(@udid)
      @socks5_forwarder.forward_traffic(["*.icloud.com"])
    end

    def find_sandbox_account
      @apple_pay_sandbox_mapping.find { |_, values| values.include?(@udid) }&.first
    end

    def sign_in(sandbox_account)
      client = WdaClient.new(@device_config['webdriver_port'])
      client.sign_in(sandbox_account)
    end

    def handle_response(response, sandbox_account)
      if response['value']['status'] == "pass"
        end_time = Time.now.to_i
        @success_data = { sandbox_account_used: sandbox_account }
        populate_success_data(response, end_time)
        true
      else
        end_time = Time.now.to_i
        handle_failure_response(response, sandbox_account, end_time)
      end
    end

    def populate_success_data(response, end_time)
      @sandbox_account_data_reporter.populate_data_hash(wda_response: response['value'], additional_data: @success_data, interval: end_time - @start_time)
    end

    def handle_failure_response(response, sandbox_account, end_time)
      @failure_data = {
        sandbox_account_used: sandbox_account,
        error: "failure response from curl request",
        screenshot_dir: "#{@server_config['screenshot_dir']}/apple_pay_sandbox_#{@udid}_*"
      }
      save_screenshot(response)
      raise WdaAutomationError.new(response['value']['message']), response['value']['debugDescription']
    end

    def save_screenshot(response)
      image_data = Base64.decode64(response['value']['screenshot'])
      output_filename = File.join(@server_config['screenshot_dir'], "apple_pay_sandbox_#{@udid}.png")
      File.open(output_filename, 'wb') { |file| file.write(image_data) }
    end

    def handle_error(error)
      end_time = Time.now.to_i
      @failure_data ||= {}
      @failure_data.merge!(wda_response: { "error" => error.message }, additional_data: { "wda-client-status" => client_running_status }, interval: end_time - @start_time)
    end

    def handle_standard_error(error)
      end_time = Time.now.to_i
      @failure_data ||= {}
      @failure_data.merge!(wda_response: { "error" => error.message }, additional_data: { "exception" => error.message }, interval: end_time - @start_time)
    end

    def client_running_status
      @client&.running? ? "running" : "non running"
    end
  end
end
