require 'appium_lib'
require_relative '../custom_exceptions'
require_relative '../utils/time_recorder'

module Automation # rubocop:todo Metrics/ModuleLength
  include BrowserStack::TimeRecorder

  time_class_methods :get_driver_for_app

  def self.get_driver_for_app(device_id, app, caps={})
    appium_server = BrowserStack::AppiumServer.new(device_id)
    appium_server.driver(caps.merge({ app: app }))
  end

  def self.open_settings_app(device_id)
    driver = Automation.get_driver_for_app(device_id, 'settings')
    driver.reset
    driver
  end

  def self.bounce_app(device_id, app)
    Automation.get_driver_for_app(device_id, app)
  end

  def self.open_app_store_and_continue(device_id, device_version, caps={}) # rubocop:todo Metrics/MethodLength,Metrics/AbcSize
    driver = Automation.get_driver_for_app(device_id, "com.apple.AppStore", caps)

    # Add implicit timeout of 10 seconds so that tests dont fail due to slow internet connection
    driver.manage.timeouts.implicit_wait = 10
    # App store pre ios11 is substantially different
    return driver if device_version.to_i < 11

    begin
      driver.find_element(:name, "Continue").click
    rescue Selenium::WebDriver::Error::NoSuchElementError
      BrowserStack.logger.info "What's New on the App Store tab not found, continuing..."
    end

    begin
      driver.find_element(:name, "Not Now").click
    rescue Selenium::WebDriver::Error::NoSuchElementError
      BrowserStack.logger.info "Turn on App Store notifications popup not displayed, continuing..."
    end

    # Dismiss the "Personalized Ads" popup, if exists
    # TODO This only really needs to be done once per device
    begin
      turn_off_button = driver.find_element(:name, 'UIA.AppStore.OnboardingAdPrivacy.turnOffButton')
      driver.execute_script('mobile: swipe', 'direction' => 'up') unless turn_off_button.enabled.to_s == 'true'
      turn_off_button.click
      sleep 3
    rescue => e
      BrowserStack.logger.info "No Personalised Ads popup found by name: #{e.message}"
    end

    begin
      turn_off_button_z = driver.find_elements(:accessibility_id, "Turn Off Personalized Ads")[-1]
      driver.execute_script('mobile: swipe', 'direction' => 'up') unless turn_off_button_z.enabled.to_s == 'true'
      turn_off_button_z.click
      sleep 3
    rescue => e
      BrowserStack.logger.info "No Personalized Ads popup displayed: #{e.message}"
    end

    # Sometimes spelt "Personalised"
    begin
      turn_off_button_s = driver.find_elements(:accessibility_id, "Turn Off Personalised Ads")[-1]
      driver.execute_script('mobile: swipe', 'direction' => 'up') unless turn_off_button_s.enabled.to_s == 'true'
      turn_off_button_s.click
      sleep 3
    rescue => e
      BrowserStack.logger.info "No Personalised Ads popup displayed: #{e.message}"
    end

    # Handling the continue page
    begin
      continue_button = driver.find_elements(:accessibility_id, "Continue")[-1]
      if continue_button
        continue_button.click
        sleep 3
      end
    rescue => e
      BrowserStack.logger.info "No continue button displayed: #{e.message}"
    end

    # Dismiss the "allow location" popup, if exists
    # TODO This only really needs to be done once per device
    begin
      driver.alert_accept
    rescue
      BrowserStack.logger.info "No location alert found"
    end

    # Open home page of app store as starting point
    begin
      today = driver.find_element(:name, 'Today')
      today.click
    rescue => e
      BrowserStack.logger.info "Could not find 'Today' page"
    end

    driver
  end

  def self.dismiss_popup(driver, app_bundle_id, dismiss_type)
    # dismiss_type - :accept, :reject

    if dismiss_type == :accept
      driver.alert_accept
      :accepted
    else
      driver.alert_reject
      :rejected
    end
  rescue Selenium::WebDriver::Error::NoSuchAlertError => e
    BrowserStack.logger.info("Popup already dismissed")
    :already_dismissed
    #ignore exception if modal already dismissed
  rescue => e
    BrowserStack.logger.error("Error while trying to dismiss #{app_bundle_id} popup #{e}")
    if e.message.match(/An attempt was made to operate on a modal dialog when one was not open/) #for older appium
      :already_dismissed
    else
      raise e
    end
  ensure
    driver.driver_quit

  end

  def self.dismiss_sliding_keyboard_popup(driver, search_bar)

      # Search bar must be clicked for popup to appear
    search_bar.click
    slide_keyboard_popup = driver.find_elements(:accessibility_id, "Continue")[-1]
    slide_keyboard_popup.click
    BrowserStack.logger.info "Dismissed sliding keyboard popup"
  rescue
    BrowserStack.logger.info "No sliding keyboard popup"

  end

  # Returns the driver instance after opening Safari Settings inside the Settings App.
  def self.open_safari_settings(device_id, device_version)
    BrowserStack.logger.info "Opening settings..."
    driver = Automation.open_settings_app(device_id)

    BrowserStack.logger.info "Scroll to Safari and click"
    element = driver.find_element(:name, "Safari")

    if Gem::Version.new(device_version) >= Gem::Version.new('13')
      element = Automation.manual_scroll_to_element(driver, "Safari")
    else
      element = driver.find_element(:name, "Safari")
      driver.execute_script('mobile: scroll', { "element": element.ref, "toVisible": true })
    end

    element.click

    driver
  end

  # Returns the driver instance after opening Safari Settings inside the Settings App.
  # For handling settings changes in iOS 18
  def self.open_safari_settings_for_ios18(device_id, device_version)
    BrowserStack.logger.info "Opening settings..."
    driver = Automation.open_settings_app(device_id)

    BrowserStack.logger.info "Scroll to Apps and click"
    element = Automation.manual_scroll_to_element_for_ios18(driver, "Apps")

    element.click

    BrowserStack.logger.info "Scroll to Safari and click"
    element = Automation.manual_scroll_to_element_for_ios18(driver, "Safari")

    element.click

    driver
  end

  def self.check_and_click_sim_card_popup(appium_driver)
    popup = nil
    begin
      popup = appium_driver.find_element(:name, 'No SIM Card Installed')
    rescue Selenium::WebDriver::Error::NoSuchElementError
      BrowserStack.logger.info("[check_and_click_sim_card_popup] Sim card pop up not found, returning.")
      return
    end

    if popup
      BrowserStack.logger.info("[check_and_click_sim_card_popup] Sim card popup found, clicking it.")
      begin
        ok_button = appium_driver.find_element(:name, 'OK')
        ok_button.click
      rescue Selenium::WebDriver::Error::NoSuchElementError
        BrowserStack.logger.error("Not able to find OK button or was not able to click it.")
      end
    end
  end

  # custom manual scroll for ios >= 18.0 due to some changes
  def self.manual_scroll_to_element_for_ios18(driver, element_name)
    BrowserStack.logger.info "Find and click #{element_name}"

    panes = %i[left_pane right_pane]
    element = nil
    panes.each do |pane|
      5.times do
        begin
          element = driver.find_element(:name, element_name)
          break if element.displayed?
        rescue => e
          BrowserStack.logger.info "Element not found: #{e.message}"
        end
        BrowserStack.logger.info "#{element_name} not on screen, scrolling..."
        if pane == :left_pane
          Appium::TouchAction.new(driver).swipe(start_x: 50, start_y: 300, end_x: 50, end_y: 100).perform
        else
          Appium::TouchAction.new(driver).swipe(start_x: driver.window_size.width - 50, start_y: 300, end_x: driver.window_size.width - 50, end_y: 100).perform
        end
      end
    end
    BrowserStack.logger.info "#{element_name} found!"
    element
  end

  # For certain devices the execute scroll script doesn't work, so manual scrolling is necessary.
  # Devices >= ios 13.2 often have this problem
  def self.manual_scroll_to_element(driver, element_name)
    BrowserStack.logger.info "Find and click #{element_name}"
    element = driver.find_element(:accessibility_id, element_name)
    5.times do
      break if element.displayed?

      BrowserStack.logger.info "#{element_name} not on screen, scrolling..."
      Appium::TouchAction.new(driver).swipe(start_y: 300, end_y: 100).perform
    end
    BrowserStack.logger.info "#{element_name} found!"
    element
  end

  def self.click_with_scroll(driver, element)
    5.times do
      break if element.attribute("value") == "0"

      element.click
      break if element.attribute("value") == "0"

      BrowserStack.logger.info "scrolling for click ..."
      Appium::TouchAction.new(driver).swipe(start_y: 300, end_y: 150).perform
    end
  end

  # IMPORTANT: This cannot be moved to XCUI tests because of conflicts between WDA and XCUI
  # More here: https://browserstack.atlassian.net/wiki/spaces/ENG/pages/2198146733/Conflicts+between+WDA+and+XCUI+tests
  def self.allow_wda_local_network(device_id, wda_client, driver=nil, skip_driver_quit=false)
    begin
      BrowserStack.logger.info "appium_automation:allow_wda_local_network starting"
      if driver.nil?
        BrowserStack.logger.info "appium_automation:allow_wda_local_network Creating new appium driver"
        driver = Automation.open_settings_app(device_id)
        skip_reset = false
      else
        BrowserStack.logger.info "appium_automation:allow_wda_local_network Appium driver already exists, reusing"
        skip_reset = true
      end
      driver.background_app(-1)
      BrowserStack.logger.info "allow_wda_local_network appium driver initiated"
      begin
        sleep(1)
        driver.find_element(:accessibility_id, "WebDriverAgentRunner-Runner").click
      rescue => e
        BrowserStack.logger.info "WDA Application unable to launch app via homescreen, checking with Ideviceutils #{e.message}}"
        IdeviceUtils.launch_app_with_bundle_id(device_id, 'com.facebook.WebDriverAgentRunner.xctrunner')
      end
      sleep(1)
      BrowserStack.logger.info "WDA Application launched, initiating call to trigger popup for local privacy"
      wda_client.trigger_local_network_access_popup
      sleep(1)
      begin
        driver.find_element(:name, "OK").click
        BrowserStack.logger.info "popup generated and handled, Enabled Local Network access for WDA using OK button"
      rescue Selenium::WebDriver::Error::NoSuchElementError
        driver.find_element(:name, "Allow").click
        BrowserStack.logger.info "popup generated and handled, Enabled Local Network access for WDA using allow button"
      end
    rescue => e
      BrowserStack.logger.info "Unable to launch WDA Application #{e.message}"
    end
  ensure
    driver.reset unless skip_reset
    driver.driver_quit unless skip_driver_quit
  end
end
