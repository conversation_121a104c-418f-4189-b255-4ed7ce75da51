require_relative '../configuration'
require_relative '../../config/constants'
require 'browserstack_logger'
require_relative '../utils/zombie'
require_relative '../utils/utils'
require_relative '../utils/http_utils'
require_relative '../custom_exceptions'
require_relative '../ios_vid_capturer'
require_relative '../utils/osutils'
require_relative '../app_automate/port_manager'
require_relative '../models/device_state'
require_relative './video_rec_process_logger'
require_relative './dedicated_video_rec_manager'

class VideoRecManager # rubocop:todo Metrics/ClassLength

  def initialize(device, params, wda_port = nil)
    @conf = BrowserStack::Configuration.new.all
    BrowserStack::Zombie.configure
    @video_record_script = "#{__dir__}/../../scripts/start_video_recording.rb"
    @device = device
    @device_state = DeviceState.new(@device)
    @params = params
    @genre = params[:genre] || params["genre"]
    @orientation = params["orientation"]
    @session_id = params['automate_session_id'] || params['session_id']
    @start_time_file = "/tmp/video_rec_start_time_#{device}.txt"
    if params['video_params_v2']
      @record_params = begin
        JSON.parse(params['video_params_v2'])
      rescue
        {}
      end
    end
    @prefix_genre = Utils.get_prefix_genre(@genre)
    @is_v2 = !@record_params.nil? && (@record_params['v2'] == 'true')
    @is_v2_performance_enabled = !@record_params.nil? && (@record_params['v2_performance'] == 'true')
    @v2_stop_experiment = !@record_params.nil? && (@record_params['stop_experiment'] == 'true')
    BrowserStack.logger.info "Initializing VideoRecManager #{device} #{params} isV2: #{@is_v2}"
    @wda_port = wda_port
  end

  def initialize_start_time(new_start_time = nil)
    if new_start_time
      @params['video_rec_start_time'] = new_start_time
      File.write(@start_time_file, new_start_time)
      BrowserStack.logger.info("Setting new start time: #{@params['video_rec_start_time']}")
    elsif File.exist?(@start_time_file)
      @params['video_rec_start_time'] = File.read(@start_time_file).to_f
      BrowserStack.logger.info("Using existing start time from file: #{@params['video_rec_start_time']}")
    else
      @params['video_rec_start_time'] = nil
    end
  rescue => e
    BrowserStack.logger.error("Error initializing start time: #{e.message} #{e.backtrace}")
    @params['video_rec_start_time'] = nil
  end

  # Launches script to start video recording
  def start_rec
    rec_start_time = Time.now.to_f
    initialize_start_time(rec_start_time)
    BrowserStack::Zombie.push_logs("#{@prefix_genre}ios-video-rec#{@is_v2 ? '-v2' : '' }", "Starting video recording", { "session_id" => @params['automate_session_id'], "rec_start_time" => rec_start_time })

    if @is_v2
      response, error = trigger_video_recording("start")
      if response == {} || (response && response["value"] && response["value"]["reason"] == "iOS video rec: springboard launch took time, try reboot after session")
        DeviceState.new(@device).touch_needs_phased_reboot_file
        start_v1_recording(rec_start_time)
        response["fallback_to_v1"] = true
      end
      push_error_to_eds(error, response, "start") if error || !(response && response["value"] && response["value"]["status"] == "Success")
    else
      start_v1_recording(rec_start_time)
    end
  end

  def start_v1_recording(rec_start_time)
    @device_state.touch_fallback_to_v1_file
    #Forking process here instead of starting video recording inline, because we want different processes. (Instead of threads running under the same process)
    cmd = "/Users/<USER>'user']}/.rvm/rubies/ruby-2.7.2/bin/ruby #{@video_record_script} #{@device} #{@session_id} #{@orientation} #{@genre} #{@wda_port}  #{rec_start_time} >> #{@conf['logging_root']}/video_recorder_#{@device}_debug.log 2>&1"
    Utils.fork_process(cmd, {
      process_name: "puma: cluster worker start_video_rec #{@device}:#{@session_id}",
      pid_file: "/tmp/pid_video_recording_#{@device}"
    })
  end

  def stop_recording_v2 # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
    video_rec_logger = VideoRecProcessLogger.new(@device, @session_id, false)
    video_rec_logger.check_and_start
    @rec_stop_time = Time.now.to_f
    response, error = trigger_video_recording("stop")
    video_file = nil
    is_wda_automation_successful = !error && response && response["value"] && response["value"]["status"] == "Success"
    begin
      raise VideoFileFetchException, "WDA not running" if Utils.is_wda_kill_flag_enabled?(@params) && !is_wda_automation_successful && response == "wda not running"

      video_file = fetch_and_upload_video # fetch video file irrespective of status
      unless video_file
        if is_wda_automation_successful
          push_error_to_eds(response, "failed to fetch video file after wda automation is successful", "stop")
          @device_state.touch_launcher_photos_permission_file
          queue_device_for_reboot # Some of the errors are due to replayd/Launcher App crash and only fix currently is to reboot the device
        end
        raise VideoFileFetchException, "Unable to fetch video file" unless is_wda_automation_successful # fallback to XCUITest stop if wda automation fails
      end
    rescue
      #falling back to xcuitest
      start_time = Time.now.to_i
      lock_unlock = false
      BrowserStack.logger.error("Video File Fetch failed after wda automation, falling back to xcuitest")
      BrowserStackAppHelper.check_and_install_browserstack_apps(@device)
      begin
        output = BrowserStackAppHelper.run_ui_test(@device, :stop_videorecord, 150, session_id: "")
        BrowserStack.logger.info("Xcuitest stopvideorecord: #{output}")
        lock_unlock = true if output.match(/Trying lock unlock/)
      rescue BrowserStackTestExecutionError => e
        BrowserStack.logger.error("xcuitest to stop video record failed #{e.test_output}")
      ensure
        stop_time = Time.now.to_i - start_time
        video_file = fetch_and_upload_video
        unless video_file
          BrowserStack.logger.error("xcuitest fallback failed to fetch video file")
          push_error_to_eds(response, "failed to fetch video file after xcuitest fallback", "stop")
          @device_state.touch_launcher_photos_permission_file
          @device_state.touch_bs_launcher_install_file
        end
        zombie_data = { "wda_response" => response, "error" => error, "stop_time" => stop_time, "lock_unlock" => lock_unlock,  "video_file" => video_file ? true : false }
        BrowserStack::Zombie.push_logs("#{@prefix_genre}ios-video-rec-xcuitest-time", "XCUItest stoprecord success", { "session_id" => @session_id, "data" => zombie_data })
      end
    end
    BrowserStack.logger.warn("[stop_recording_v2] Video duration validation failed") unless validate_video_duration(video_file)
    video_rec_logger.stop
  end

  def validate_video_duration(video_file)
    return false unless video_file && File.exist?(video_file)

    rec_start_time = File.exist?(@start_time_file) ? File.read(@start_time_file).to_f : nil
    recording_duration = @rec_stop_time - rec_start_time if rec_start_time
    BrowserStack.logger.info("[validate_video_duration] Raw recording duration calculation: #{recording_duration.round(2)}")
    # Extract video duration using ffprobe
    begin
      duration_cmd = "#{FFPROBE} -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 #{video_file}"
      video_duration = BrowserStack::OSUtils.execute(duration_cmd, timeout: 10).strip.to_f
      # Check if video duration is within ±20% of recording duration
      tolerance = recording_duration * 0.2 # 20% of recording duration
      lower_bound = recording_duration - tolerance
      upper_bound = recording_duration + tolerance
      is_valid = video_duration >= lower_bound && video_duration <= upper_bound
      # Log validation results
      BrowserStack.logger.info("[validate_video_duration] Recording duration: #{recording_duration.round(2)}s, Video duration: #{video_duration.round(2)}s")
      BrowserStack.logger.info("[validate_video_duration] Valid range: [#{lower_bound.round(2)}s, #{upper_bound.round(2)}s], Is valid: #{is_valid}")
      # Push data to monitoring
      BrowserStack::Zombie.push_logs("#{@prefix_genre}ios-video-duration-validation", is_valid ? "Video duration valid" : "Video duration invalid", { "session_id" => @session_id, "data" => { "recording_duration" => recording_duration.round(2), "video_duration" => video_duration.round(2), "is_valid" => is_valid } })
      is_valid
    rescue => e
      BrowserStack.logger.error("[validate_video_duration] Error: #{e.message} #{e.backtrace}")
      false
    ensure
      File.delete(@start_time_file) if File.exist?(@start_time_file)
    end
  end

  # Terminates the script launched by #start_rec
  # Creates upload request for uploading the video
  def stop_rec
    fallback_to_v1_file = @device_state.fallback_to_v1_file_present?
    if @is_v2 && !fallback_to_v1_file
      stop_recording_v2
    else
      capturer = IosVidCapturer.new(@device, @session_id, @conf, nil, @genre, BrowserStack.logger)
      capturer.stop
      capturer.upload(@params)
    end
  end

  def fetch_and_upload_video
    video_file = fetch_v2_video_file
    create_upload_request_for_video(video_file, @params) if video_file
    video_file
  end

  def trigger_video_recording(state)
    rec_time = Time.now.to_i
    error = false

    return ["WDA not running", false] if Utils.is_wda_kill_flag_enabled?(@params) && !BrowserStack::OSUtils.wda_xcodebuild_running?(@device)

    begin
      PortManager.stop_forwarding_port(@device, @wda_port + 1700, "video")
      PortManager.forward_port(@device,  @wda_port + 1700,  @wda_port, "video")
      wda_client = WdaClient.new(@wda_port + 1700, @device)
      response = state == 'start' ? wda_client.start_replay_kit_recording(@is_v2_performance_enabled) : wda_client.stop_replay_kit_recording(@is_v2_performance_enabled, @v2_stop_experiment)
    rescue Timeout::Error => e
      BrowserStack.logger.error("V2 Video Recording #{state} Failed : Timeout, #{e.message} #{e.backtrace}")
      response = "WDA command timed out"
      error = true
    rescue => e
      BrowserStack.logger.error("V2 Video Recording #{state} Failed: Error, #{e.message} #{e.backtrace}")
      response = e.message
      error = true
    ensure
      BrowserStack::Zombie.push_logs("#{@prefix_genre}ios-video-v2-rec-status", "#{state} video recording", { "session_id" => @session_id, "data" => { "automation_response" => response, "#{state}-time" => Time.now.to_i - rec_time } })
      PortManager.stop_forwarding_port(@device, @wda_port + 1700, "video")
    end

    [response, error]
  end

  def fetch_v2_video_file # rubocop:todo Metrics/AbcSize
    start_time = Time.now.to_i
    result = nil
    device_workspace = "#{@conf['video_recording_workspace']}#{@device}/"
    workspace = "#{device_workspace}#{@session_id}/"
    output_file = nil
    device_config = JSON.parse(File.read(CONFIG_JSON_FILE))["devices"][@device]
    device_version = device_config["device_version"]

    begin
      retry_count ||= 0
      BrowserStack.logger.info("[fetch_v2_video_file] Attempting to unmount device #{@device}, retry_count: #{retry_count}") if retry_count != 0
      BrowserStack::OSUtils.execute("sudo umount -f /Users/<USER>/#{@device}", timeout: 10) if retry_count != 0
      BrowserStack::OSUtils.execute("sudo su -l #{USER} -c 'mkdir -p /Users/<USER>/#{@device}'", timeout: 10)
      BrowserStack::OSUtils.execute("#{IFUSE} -o allow_other,umask=0000,default_permissions -u #{@device} /Users/<USER>/#{@device}", timeout: 10)
      FileUtils.rm_rf(workspace)
      FileUtils.mkdir_p(workspace)
      no_of_files = Dir.glob("/Users/<USER>/#{@device}/DCIM/*/*").select { |file| File.file?(file) }.count
      device_video_files = Dir.glob("/Users/<USER>/#{@device}/DCIM/*/*.MP4").sort_by { |f| File.mtime(f) }.reverse
      output_file, error = correct_video_file_fetched?(device_video_files, workspace)
    rescue OSUtilsError => e
      BrowserStack.logger.error("[fetch_v2_video_file] Failed : Timeout, #{e.message} #{e.backtrace} Retry #{retry_count}")
      retry_count += 1
      retry if retry_count < 2
      error = e.message
    rescue VideoFileFetchException => e
      BrowserStack.logger.error("[fetch_v2_video_file] Failed : Failed, #{e.message} #{e.backtrace} Retry #{retry_count}")
      retry_count += 1
      retry if retry_count < 2
      error = e.message
    rescue => e
      error = e.message
      BrowserStack.logger.error("[fetch_v2_video_file] Failed : Error, #{e.message} #{e.backtrace}")
    ensure
      BrowserStack::OSUtils.execute("sudo umount -f /Users/<USER>/#{@device}", timeout: 10)
      BrowserStack::Zombie.push_logs("#{@prefix_genre}ios-video-v2-rec-fetch-time", error, { "session_id" => @session_id, "data" => { "fetch-time" => Time.now.to_i - start_time, "files" => no_of_files } })
      FileUtils.rm("#{workspace}metadata") if File.exists? "#{workspace}metadata"
      FileUtils.touch("#{STATE_FILES_DIR}/#{@device}_reinstall_preloaded_media")
    end
    output_file
  end

  def correct_video_file_fetched?(device_video_files, workspace)
    output_file = nil
    copyright_count = 0
    raise VideoFileFetchException, "No video files found" if device_video_files.empty?

    dedicated_video_rec_manager = DedicatedVideoRecManager.new(@device)
    device_video_files = dedicated_video_rec_manager.get_new_video_files(device_video_files, @session_id) if @device_state.dedicated_cleanup_file_present?

    device_video_files.each do |device_video_file|
      next unless device_video_file && File.exists?(device_video_file)

      temp_output_file = "#{workspace}#{SecureRandom.uuid}.MP4"
      begin
        FileUtils.cp(device_video_file, temp_output_file)
        BrowserStack::OSUtils.execute("#{FFMPEG} -i #{temp_output_file} -f ffmetadata #{workspace}metadata", timeout: 10)
        result = BrowserStack::OSUtils.execute("cat #{workspace}metadata | grep copyright | wc -l", timeout: 10).strip
        FileUtils.rm("#{workspace}metadata")
        if result == "2"
          copyright_count += 1
          output_file = temp_output_file
        else
          FileUtils.rm(temp_output_file)
        end
      rescue => e
        FileUtils.rm(temp_output_file) if File.exists?(temp_output_file)
        BrowserStack.logger.error("[correct_video_file_fetched?] Error processing file: #{device_video_file}, Error: #{e.message}")
      end
    end

    if copyright_count == 1
      [output_file, false]
    else
      FileUtils.rm(output_file) if output_file
      BrowserStack::Zombie.push_logs("dedicated-device-events", 'Multiple session videos detected', { "device" => @device, "session_id" => @session_id, "session_videos_count": copyright_count }) if @device_state.dedicated_cleanup_file_present?
      raise VideoFileFetchException, "Incorrect Video File"
    end
  end

  def create_upload_request(uploader_request_file, json_data)
    dir_name = File.dirname(uploader_request_file)
    FileUtils.mkdir_p(dir_name) unless Dir.exist?(dir_name)
    Utils.write_to_file(uploader_request_file, json_data.to_json)
    BrowserStack.logger.info("Upload request created as #{uploader_request_file}")
  end

  def create_upload_request_for_video(file_name, params)
    uploader_request_file = @conf["videos_to_upload_dir"] + "/video_upload_#{SecureRandom.uuid}.json"
    json_data = {
      upload_type: "video",
      file_name: file_name,
      video_params: params.select { |key, _| key.include?('video') },
      genre: @genre,
      stop_req_timestamp: params['stop_req_timestamp'] || 0
    }
    create_upload_request(uploader_request_file, json_data)
  end

  def push_error_to_eds(error, response, state)
    feature_usage = {}
    data_to_push = {
      success: false,
      exception: {
        error: error,
        wda_response: response,
        state: state
      }
    }

    feature_usage["videoLogs"] = data_to_push
    data_to_push_to_eds = {
      feature_usage: feature_usage,
      hashed_id: @session_id,
      timestamp: Time.now.to_i
    }

    eds_event = ['selenium', 'js_testing', 'automate'].include?(@genre) ? EdsConstants::AUTOMATE_TEST_SESSIONS : EdsConstants::APP_AUTOMATE_TEST_SESSIONS
    Utils.send_to_eds(data_to_push_to_eds, eds_event, true)
  end

  def queue_device_for_reboot
    @device_state.touch_v2_video_needs_phased_reboot_file
    BrowserStack::Zombie.push_logs("v2-video-phased-reboot-requested", @device, { "session_id" => @session_id })
  end
end
