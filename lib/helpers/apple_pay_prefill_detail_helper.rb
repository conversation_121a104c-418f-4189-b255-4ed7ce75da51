require_relative '../../config/constants'
require_relative '../../server/device_manager'
require_relative '../utils/idevice_ffi/assistive_touch_util'
require_relative 'wda_client'
require_relative 'data_report_helper'

class ApplePayPrefillDetailHelper
  def initialize(device_id, opts = {})
    @device_id = device_id
    @session_id = opts[:session_id]
    @product = opts[:product]
  end

  def execute(apple_pay_details)
    log(:info, "Prefilling Apple Pay Details: #{apple_pay_details}")

    result = "success"
    start_time = Time.now.to_i
    error = nil

    assistive_touch_enabled = assistive_touch_util.enabled?
    assistive_touch_util.disable if assistive_touch_enabled

    foreground_app_bundle_id = wda_client.foreground

    response = wda_client.prefill_apple_pay_detail(apple_pay_details)
    raise response['value']['message'] unless response['value']['status'] == 'pass'

    log(:info, "Successfully prefilled Apple Pay Details: #{apple_pay_details}")

    [true, nil]
  rescue => e
    log(:error, "Failed prefilling Apple Pay Details: #{apple_pay_details}, #{e.message}, #{e.backtrace}")

    result = "failed"
    error = e.message

    code = nil
    error_message = e.message
    code = "APPD_0001" if error_message.include?("Failed to tap on element \"Done\" Button, because it is not enabled")
    [false, code]
  ensure
    # setting this to nil will recreate the object and helps in intermittent lockdown errors, ref MOBPL-4501
    @assistive_touch_util = nil
    assistive_touch_util.enable if assistive_touch_enabled
    # Need to add sleep to give enough time to wda for pressing home screen
    # TODO: Instead of sleep, use some other approach to open settings access
    sleep(3)
    wda_client.set_foreground(foreground_app_bundle_id, "app_name", {}, @session_id)
    time_taken = Time.now.to_i - start_time
    data_report_helper.report({
      "result" => result,
      "time_taken" => time_taken,
      "error" => error
    })
  end

  private

  def assistive_touch_util
    @assistive_touch_util ||= AssistiveTouchUtil.new({ device_id: @device_id })
  end

  def device_config
    @device_config ||= DeviceManager.device_configuration_check(@device_id)
  end

  def wda_client
    @wda_client ||= WdaClient.new(device_config['webdriver_port'])
  end

  def data_report_helper
    @data_report_helper ||= DataReportHelper.new('apple-pay-prefill-detail',
                                                 session_id: @session_id,
                                                 product: @product,
                                                 ios_version: device_config["device_version"],
                                                 device: @device_id)
  end

  def log(level, msg)
    BrowserStack.logger.send(level.to_sym, msg, { subcomponent: self.class.to_s, device_id: @device_id })
  end
end
