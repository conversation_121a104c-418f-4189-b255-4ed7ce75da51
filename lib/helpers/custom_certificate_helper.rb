require 'securerandom'
require 'plist'

require_relative '../utils/zombie'
require_relative '../custom_exceptions'
require_relative '../../config/constants'
require_relative '../models/device_state'
require_relative '../utils/http_utils'
require_relative 'data_report_helper'
require_relative '../utils/configuration_profiles_manager'

# rubocop:disable Metrics/ClassLength
class CustomCertificate
  CERTIFICATE_MODULE_TAG = "[CustomCertificate]"

  def initialize(device_id, session_id, certificate_details = nil, product = "")
    @udid = device_id
    @session_id = session_id
    @certificate_details = certificate_details
    @device_state = DeviceState.new(device_id)
    @custom_certificate_data_reporter = DataReportHelper.new('custom-certificate', session_id: session_id, product: product, device: device_id)
    @configuration_profiles_manager = ConfigurationProfilesManager.new(device_id, BrowserStack.logger)
    BrowserStack::Zombie.configure
  end

  def self.get_file_details(media)
    media = JSON.parse(media)
    [media["s3_url"], media["filename"], media["filetype"], media["password"]]
  end

  def custom_certificate_folder(udid)
    "/tmp/custom_certificates_#{udid}"
  end

  def install_pfx_certificate
    BrowserStack.logger.info("#{CERTIFICATE_MODULE_TAG} Installing custom certificate on device : #{@udid} and session ID : #{@session_id}" )
    start_time = Time.now
    FileUtils.mkdir_p(custom_certificate_folder(@udid))
    file_url, file_name, _, password = self.class.get_file_details(@certificate_details)
    file_path = File.join(custom_certificate_folder(@udid), file_name)
    BrowserStack::HttpUtils.download(file_url, file_path, { retry_count: 3, timeout: 20 }, "custom_media")

    @configuration_profiles_manager.install_profile(:pfx_certificate, { certificate_path: file_path, password: password }, install_via: :mdm)
    @device_state.touch_custom_certificate_installed_file
    data = { "action" => "install", "status" => "success", "time_taken" => Time.now - start_time }
    BrowserStack.logger.info("#{CERTIFICATE_MODULE_TAG} Successfully installed custom certificate on device : #{@udid} for session ID: #{@session_id}")
  rescue => e
    BrowserStack.logger.error("#{CERTIFICATE_MODULE_TAG} Error while installing custom certificate on device: #{@udid} for session ID: #{@session_id} due to error -#{e.message} #{e.backtrace}")
    data = { "action" => "install", "status" => "fail", "error" => e.message, "time_taken" => Time.now - start_time }
    raise e
  ensure
    File.delete(file_path) if File.exist?(file_path)
    @custom_certificate_data_reporter.report(data)
  end

  def remove_pfx_certificate
    BrowserStack.logger.info("#{CERTIFICATE_MODULE_TAG} Removing custom certificate on device : #{@udid} and session ID : #{@session_id}" )
    start_time = Time.now
    begin
      @configuration_profiles_manager.remove_profile(:pfx_certificate, nil, remove_via: :mdm)
    rescue => e
      # if the error is like "Profile.*is not installed", then we can ignore it
      raise e unless e.message.include?("profile") && e.message.include?("is not installed")
    end
    @device_state.remove_custom_certificate_installed_file
    data = { "action" => "remove", "status" => "success", "time_taken" => Time.now - start_time }
    BrowserStack.logger.info("#{CERTIFICATE_MODULE_TAG} Successfully removed custom certificate on device : #{@udid} for session ID: #{@session_id}")
  rescue => e
    BrowserStack.logger.error("#{CERTIFICATE_MODULE_TAG} Error while removing custom certificate on device: #{@udid} for session ID: #{@session_id} due to error -#{e.message} #{e.backtrace}")
    data = { "action" => "remove", "status" => "fail", "error" => e.message, "time_taken" => Time.now - start_time }
    raise e
  ensure
    @custom_certificate_data_reporter.report(data)
  end

  def install_all_custom_ca_certs
    raise "CA certs expected in array format" unless @certificate_details.is_a?(Array)

    custom_mobileconfig_file_path = "#{STATE_FILES_DIR}/app_live_custom_certificate_#{@udid}.mobileconfig"
    tmp_mobileconfig_file_path = "/tmp/app_live_custom_certificate_#{@udid}.mobileconfig"
    file_already_exists = File.exists?(custom_mobileconfig_file_path)

    custom_mobileconfig = load_mobileconfig(file_already_exists, custom_mobileconfig_file_path, tmp_mobileconfig_file_path)

    process_ca_certificates(custom_mobileconfig)

    File.write(custom_mobileconfig_file_path, custom_mobileconfig.to_plist)
    IosMdmServiceClient.install_profile(@udid, "custom_certificate", File.read(custom_mobileconfig_file_path))
  rescue => e
    FileUtils.rm(custom_mobileconfig_file_path)
    FileUtils.mv(tmp_mobileconfig_file_path, custom_mobileconfig_file_path) if file_already_exists
    raise e
  end

  def remove_all_custom_ca_certs
    payload_identifier = "com.browserstack.cert.rootca.profile"
    IosMdmServiceClient.remove_profile(@udid, "custom_certificate", payload_identifier)
    custom_mobileconfig_file_path = "#{STATE_FILES_DIR}/app_live_custom_certificate_#{@udid}.mobileconfig"
    FileUtils.rm(custom_mobileconfig_file_path)
  end

  private

  def load_mobileconfig(file_already_exists, custom_mobileconfig_file_path, tmp_mobileconfig_file_path)
    if file_already_exists
      #Storing the old version of the file just in case the cert install fails
      FileUtils.cp(custom_mobileconfig_file_path, tmp_mobileconfig_file_path)

      Plist.parse_xml(File.read(custom_mobileconfig_file_path), { marshal: false })
    else
      Plist.parse_xml(File.read("#{TEMPLATES_DIR}/mobileconfig_injection_empty_plist.plist"), { marshal: false })
    end
  end

  def process_ca_certificates(custom_mobileconfig)
    @certificate_details.each do |certificate|
      if certificate[:filetype] == "mobileconfig"
        process_mobileconfig_certificate(certificate, custom_mobileconfig)
      else
        process_cer_certificate(certificate, custom_mobileconfig)
      end
    end
  end

  def process_mobileconfig_certificate(certificate, custom_mobileconfig)
    tmp_mobileconfig_file_path = "/tmp/app_live_custom_certificate_#{@udid}.mobileconfig"
    HttpUtils.download(certificate[:media_s3_url], tmp_mobileconfig_file_path, { retry_count: 3, timeout: 20 })
    mobileconfig_xml = Plist.parse_xml(File.read(tmp_mobileconfig_file_path))
    mobileconfig_xml["PayloadContent"].each do |cert_details|
      custom_mobileconfig["PayloadContent"].append(cert_details)
    end
    FileUtils.rm(tmp_mobileconfig_file_path)
  end

  def process_cer_certificate(certificate, custom_mobileconfig)
    random_uuid = SecureRandom.uuid
    cer_file_path = "/tmp/#{random_uuid}.cer"
    HttpUtils.download(certificate[:media_s3_url], cer_file_path, { retry_count: 3, timeout: 20 })
    cer_file_content = if File.foreach(cer_file_path).any? { |line| line.include?('-----BEGIN CERTIFICATE-----') }
                         File.read(cer_file_path)
                       else
                         system("openssl x509 -inform der -in #{cer_file_path.shellescape} -out #{cer_file_path.shellescape}")
                         File.read(cer_file_path)
                       end

    cer_xml_hash = {
      "PayloadCertificateFileName" => (certificate[:filename]).to_s,
      "PayloadContent" => Plist::PData.new(cer_file_content),
      "PayloadDescription" => "Adds a root certificate",
      "PayloadDisplayName" => File.basename(certificate[:filename], ".cer"),
      "PayloadIdentifier" => "com.browserstack.cert.rootca.#{random_uuid}",
      "PayloadType" => "com.apple.security.root",
      "PayloadUUID" => random_uuid,
      "PayloadVersion" => 1
    }
    custom_mobileconfig["PayloadContent"].append(cer_xml_hash)
    FileUtils.rm(cer_file_path)
  end
end
# rubocop:enable Metrics/ClassLength
