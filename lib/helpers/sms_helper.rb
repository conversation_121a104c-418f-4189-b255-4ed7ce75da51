require 'appium_lib'
require_relative './browserstack_app_helper'
require_relative '../custom_exceptions'
require_relative '../models/device_state'
require_relative '../configuration'
require_relative '../utils/zombie'
require_relative './automation'
require_relative '../utils/idevice_utils'

# This class is responsible for ios: messages app cleanup.
class SMSHelper # rubocop:todo Metrics/ClassLength
  def initialize(device_id, session_id, product = "")
    @device_state = DeviceState.new(device_id)
    @uuid = device_id
    @product = product
    @session_id = session_id
    @device_config = device_config
    @ios_version = @device_config["device_version"]
    BrowserStack::Zombie.configure
  end

  def device_config
    config = BrowserStack::Configuration.new.all
    devices = begin
      JSON.parse(File.read(config['config_json_file']))["devices"]
    rescue
      {}
    end
    devices[@uuid]
  end

  def event_logger(error_type, data)
    event_name = "web_events"
    data_to_push = {
      event_name: "sms-cleanup",
      product: @product,
      os: IdeviceUtils.os(@uuid),
      os_version: @ios_version,
      team: "device_features"
    }
    event_json = {
      session_id: @session_id
    }
    event_json.merge!(data)
    data_to_push.merge!({ event_json: event_json })
    Utils.send_to_eds(data_to_push, event_name, true)

    if data[:status] == 'failure'
      BrowserStack::Zombie.push_logs("sms-cleanup-failure",
                                     error_type,
                                     { "device" => @uuid,
                                       "product" => @product,
                                       "session_id" => @session_id,
                                       "os_version" => @ios_version,
                                       "error_reason" => data[:error_reason],
                                       "error_message" => data[:error_message] })
    end
  end

  def prepare_data(result, start_time, error_reason, error_message)
    end_time = Time.now.to_i
    if result == "failure"
      { status: "failure",
        error_reason: error_reason,
        error_message: error_message,
        time_taken: end_time - start_time }
    else
      { status: "success",
        time_taken: end_time - start_time }
    end

  end

  def parse_test_output_for_error(error_type, err_object)
    BrowserStack.logger.error("#{error_type} - Failed to clean all sms for session- #{@session_id} due to error - #{err_object.message} - #{err_object.backtrace.join("\n")}")
    if error_type == "StandardError"
      error_message = err_object.message
    else
      error_message = err_object.test_output
      err_object.test_output.split("\n").each do |test_logline|
        next unless test_logline.include?("XCTAssertFalse failed")

        # Eg of test_output_split:  t = 4.29s Assertion Failure: MessagesUITests.swift:36: XCTAssertFalse failed - ElementNotFound, Unable to find confirmation Button for deletion - may be App UI got changed
        assertion_failure = test_logline.split(",")
        error_reason = assertion_failure[0].split("-")[1].strip
        error_message = assertion_failure[1].strip
        return "failure", error_type, error_reason, error_message
      end
    end

    error_reason = "UnknownError"
    ["failure", error_type, error_reason, error_message]
  end

  def sms_cleanup
    BrowserStack.logger.info("Running XCUI automation to clean all sms on device : #{@uuid} for session ID : #{@session_id}")
    start_time = Time.now.to_i
    error_type = ""
    error_reason = ""
    error_message = ""
    begin
      BrowserStackAppHelper.check_and_install_browserstack_test_suite(@uuid)
      output = BrowserStackAppHelper.run_ui_test(@uuid, :sms_cleanup, session_id: @session_id)

      # Remember last sms cleanup on non-sim devices
      unless DeviceSIMHelper.sim?(@uuid)
        @device_state.touch_clean_sms_file
        if output.include?("Delete button tapped")
          BrowserStack.logger.info("Deleting SMS for non sim devices")
          BrowserStack::Zombie.push_logs("sms-deleted-for-non-sim-device", "", { "device" => @uuid  })
        end
      end
      BrowserStack.logger.info("successfully cleaned all sms of device : #{@uuid} for session ID : #{@session_id}")
      status = "success"

    rescue BrowserStackTestExecutionError => e
      status, error_type, error_reason, error_message = parse_test_output_for_error("BrowserStackTestExecutionError", e)

    rescue => e
      status, error_type, error_reason, error_message = parse_test_output_for_error("StandardError", e)

    ensure
      data = prepare_data(status, start_time, error_reason, error_message)
      event_logger(error_type, data)
    end
    status == "success"
  end
end
