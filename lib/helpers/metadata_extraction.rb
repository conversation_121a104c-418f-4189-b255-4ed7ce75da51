require_relative '../../config/constants'
require_relative '../../server/device_manager'
require_relative '../utils/plist_buddy'

class MetadataExtraction # rubocop:todo Metrics/ClassLength
  attr_reader :display_name, :bundle_id, :minimum_os_version, :device_family

  def initialize(device_id, app_path, opts = {})
    @device_id = device_id
    @device = DeviceManager.device_configuration_check(@device_id)

    @app_path = app_path

    @session_id = opts["session_id"]
    @app_check_endpoint = opts["app_check_endpoint"]
    @app_auth = opts["app_bs_auth"]
    @app_filename = opts["app_filename"]
    @params = opts

    @display_name = nil
    @bundle_id = nil
    @minimum_os_version = nil
    @device_family = nil
  end

  def extract_validate_and_get_app_params
    log(:info, "Performing validation checks for async metadata flow on device: #{@device_id}")

    return false, {} unless validate_app_path

    begin
      extract_app_metadata

      return false, {} unless validate_extracted_metadata

      verify_app
    rescue => e
      log(:error, "Error during extraction and validation for async metadata flow for #{@session_id}: #{e.message}")
      false
    end
  end

  private

  def validate_app_path
    if @app_path.nil? || @app_path.empty?
      log(:error, "App path is nil or empty")
      return false
    end

    unless File.directory?(@app_path)
      log(:error, "App path is not a directory: #{@app_path}")
      return false
    end

    true
  end

  def extract_app_metadata
    @display_name = extract_display_name
    @bundle_id = extract_bundle_id
    @minimum_os_version = extract_minimum_os_version
    @device_family = extract_device_family
  end

  def validate_extracted_metadata
    if @display_name.nil? || @display_name.empty?
      log(:error, "Display name is nil or empty")
      return false
    end

    if @bundle_id.nil? || @bundle_id.empty?
      log(:error, "Bundle ID is nil or empty")
      return false
    end

    if @minimum_os_version.nil? || !@minimum_os_version.is_a?(Float)
      log(:error, "Minimum OS version is nil or empty")
      return false
    end

    if @device_family.nil? || @device_family.empty?
      log(:error, "Device family is nil or empty")
      return false
    end

    log(:info, "Validation checks passed for async metadata flow on device: #{@device_id} for session: #{@session_id}")
    true
  end

  def verify_app
    app_related_params = {
      "app_display_name" => @display_name,
      "app_testing_bundle_id" => @bundle_id
    }

    if @device_family != "all" && !@device["device_name"].downcase.include?(@device_family)
      log(:info, "session started on in-compatible device family. Device family supported: #{@device_family} and current device: #{@device['device_name']}")

      notify_mismatch_device_family
      return [false, app_related_params]
    end

    if @minimum_os_version.to_f > @device["device_version"].to_f
      log(:info, "session started on in-compatible device. Device version: #{@device['device_version']} and min app supported version: #{@minimum_os_version}")

      notify_min_os_version
      return [false, app_related_params]
    end

    app_data = {
      package: @bundle_id,
      os: "ios",
      user_id: @params["user_id"],
      bs_auth: @app_auth
    }

    begin
      response = HttpUtils.send_post(@app_check_endpoint, app_data, nil, true, { read_timeout: 61 })

      if response.code.to_i == 200
        response_body = JSON.parse(response.body)
        log(:info, "App Check API response body: #{response_body}")

        app_params = response_body['app_params']

        app_related_params.merge!(response_body['app_params']) if response_body['app_params'].is_a?(Hash)

        [true, app_related_params]
      else
        notify_close_session

        [false, app_related_params]
      end

    rescue => e
      log(:info, "unable to run app check via API: #{@session_id} with error: #{e.message}")
      [false, {}]
    end
  end

  def plist_file
    matched_files = Dir.glob("#{@app_path}/Payload/*.app/Info.plist")
    if matched_files.empty?
      log(:error, "Info.plist file not found in: #{@app_path}/Payload/*.app/")
      raise "Info.plist file not found"
    end
    matched_files.first
  end

  def en_proj_plist
    matched_files = Dir.glob("#{@app_path}/Payload/*.app/en.lproj/InfoPlist.strings")
    if matched_files.empty?
      log(:error, "InfoPlist.strings file not found in: #{@app_path}/Payload/*.app/en.lproj/")
      return nil
    end
    matched_files.first
  end

  def extract_display_name
    display_name_prospects = [
      ["CFBundleDisplayName", en_proj_plist],
      ["CFBundleDisplayName", plist_file],
      ["CFBundleName", en_proj_plist],
      ["CFBundleName", plist_file],
      ["CFBundleExecutable", plist_file]
    ]

    display_name = nil

    display_name_prospects.each do |key, file|
      next unless file

      begin
        value = PlistBuddy.get_value_of_key(file, key)
        next if value.nil? || value.strip.empty?

        display_name = value.strip
        break
      rescue => e
        log(:warn, "Error extracting '#{key}' from '#{file}': #{e.message}")
        next
      end
    end

    log(:info, "Extracted display name: #{display_name} from plist files") if display_name
    display_name
  end

  def extract_bundle_id

    value = PlistBuddy.get_value_of_key(plist_file, "CFBundleIdentifier")
    bundle_id = value&.strip

    log(:info, "Extracted bundle ID: #{bundle_id} from plist file: #{plist_file}")
    bundle_id
  rescue => e
    log(:error, "Error extracting bundle ID: #{e.message}")
  end

  def extract_minimum_os_version

    value = PlistBuddy.get_value_of_key(plist_file, "MinimumOSVersion")
    min_os_version = value&.strip.to_f

    log(:info, "Extracted minimum OS version: #{min_os_version} from plist file: #{plist_file}")
    min_os_version
  rescue => e
    log(:error, "Error extracting minimum OS version: #{e.message}")
  end

  def extract_device_family

    value = PlistBuddy.get_value_of_key(plist_file, "UIDeviceFamily")
    device_family_array = value&.strip
    device_family = "iphone"

    return if device_family_array.nil? || device_family_array.empty?

    device_family = device_family_array.include?("1") ? "all" : "ipad"

    log(:info, "Extracted device family: #{device_family} from plist file: #{plist_file}")
    device_family
  rescue => e
    log(:error, "Error extracting device_family: #{e.message}")
  end

  def notify_close_session
    Utils.notify_pusher("close_session", @params, @device)
  end

  def notify_min_os_version
    message = "in_compatible_os,#{@minimum_os_version},#{@app_filename}"
    Utils.notify_pusher(message, @params, @device)
  end

  def notify_mismatch_device_family
    message = "in_compatible_device,#{@device_family},#{@app_filename}"
    Utils.notify_pusher(message, @params, @device)
  end

  def log(level, msg)
    BrowserStack.logger.send(level.to_sym, msg, { subcomponent: self.class.to_s, device_id: @device_id })
  end

end
