require 'json'
require 'fileutils'
require_relative '../configuration'
require_relative '../../config/constants'
require_relative '../utils/utils'
require_relative '../utils/zombie'
require 'browserstack_logger'
require_relative '../utils/osutils'

class SocatHelper
  class << self

    def get_device_socat_port(device_id)
      server_config = BrowserStack::Configuration.new.all
      device_config = begin
        JSON.parse(File.read(server_config['config_json_file']))["devices"][device_id]
      rescue
        {}
      end
      device_config["selenium_port"].to_i + server_config["socat_listen_port_offset"].to_i
    end

    # Used for local testing chrome extension
    def create_secure_tcp_channel(socat_port, domain, port)
      # First, kill all socat processes running on the specified port
      kill(socat_port)
      cmd = "#{SOCAT} -d TCP-LISTEN:#{socat_port},reuseaddr,fork OPENSSL:#{domain}:#{port},verify=0 &"
      Utils.fork_process(cmd)
      BrowserStack.logger.info("Spawned socat process with command: #{cmd}")
    end

    def kill(socat_port)
      # Run the command and redirect output to /dev/null
      output, status = BrowserStack::OSUtils.execute("ps aux | grep -E 'socat.*TCP-LISTEN:' | grep -v grep | awk '{print $2}'", true, timeout: 10)
      if output.empty?
        BrowserStack.logger.info("No socat processes found on port #{socat_port}.")
        return
      end
      # Kill the socat processes
      output, status = BrowserStack::OSUtils.execute("ps aux | grep -E 'socat.*TCP-LISTEN:#{socat_port}' | grep -v grep | awk '{print $2}' | xargs -n 1 -I {} timeout 5 kill -9 {}", true)
      if status == 0
        BrowserStack.logger.info("All socat processes on port #{socat_port} killed successfully.")
      else
        BrowserStack.logger.error("Failed to kill socat processes on port #{socat_port}.")
        BrowserStack::Zombie.push_logs("ios_socat_kill_failed", "", { "socat_port" => socat_port })
      end
    end

    def cleanup(device)
      socat_port = get_device_socat_port(device)
      kill(socat_port)
    end
  end
end
