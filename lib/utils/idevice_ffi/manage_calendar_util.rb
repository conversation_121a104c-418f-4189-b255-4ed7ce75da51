require_relative 'idevice_require'
require_relative 'idevice_ffi'

class ManageCalendarUtil < IdeviceFfi
  DATA_SYNC_CLASS = "com.apple.Calendars"

  def delete_all
    # We need to attach mobile sync client here to avoid starting sync in initialize
    mobile_sync_client = Idevice::MobileSyncClient.attach(idevice: @idevice_ffi)
    mobile_sync_client.start(DATA_SYNC_CLASS, [nil, "BSANCHOR"])
    mobile_sync_client.clear_all_records_on_device
    mobile_sync_client.finish
  end
end
