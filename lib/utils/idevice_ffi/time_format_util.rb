require_relative 'idevice_require'
require_relative 'idevice_ffi'
require_relative '../../../config/constants'

class TimeFormatUtil < IdeviceFfi
  DOMAIN = nil
  KEY = "Uses24HourClock"

  def initialize(opts = {})
    super(opts)
    @lockdown_client = Idevice::LockdownClient.attach(idevice: @idevice_ffi)
  end

  def get
    twenty_four_hour_clock_enabled = @lockdown_client.get_value(DOMAIN, KEY)
    twenty_four_hour_clock_enabled ? TWENTY_FOUR_HOUR_TIME_FORMAT : TWELVE_HOUR_TIME_FORMAT
  end

  def set(time_format = TWENTY_FOUR_HOUR_TIME_FORMAT)
    enable_24hour_clock = time_format == TWENTY_FOUR_HOUR_TIME_FORMAT
    @lockdown_client.set_value(DOMAIN, KEY, enable_24hour_clock)
  end
end
