require_relative 'idevice_require'

class DeveloperModeUtil
  DOMAIN = "com.apple.security.mac.amfi"
  KEY = "DeveloperModeStatus"

  def initialize(uuid)
    @uuid = uuid
  end

  def enabled?
    idevice = _attach_idevice
    lockdown_client = Idevice::LockdownClient.attach(idevice: idevice)
    lockdown_client.get_value(DOMAIN, KEY)
  end

  def toggle
    # This will also disable developer mode when triggered on a device with developer mode active
    _send_plist(1)
  end

  def enable
    # Must be run after toggle
    _send_plist(2)
  end

  private

  def _send_plist(action)
    # Due to device reboot pointer references need to be detached after use, otherwise device
    # needs to be unplugged from machine.
    idevice = _attach_idevice
    pls_client = Idevice::PropertyListServiceClient.attach(idevice: idevice)
    pls_client.send_plist({ "action": action })
  end

  def _attach_idevice
    # Reusing same instance of Idevice can lead to connectivity issues with device.
    Idevice::Idevice.attach(udid: @uuid)
  end
end
