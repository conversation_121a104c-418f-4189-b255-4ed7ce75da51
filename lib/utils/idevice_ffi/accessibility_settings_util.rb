require_relative 'idevice_require'
require_relative 'idevice_ffi'

class AccessibilitySettingsUtil < IdeviceFfi
  DOMAIN = "com.apple.Accessibility"

  def initialize(opts = {})
    super(opts)
    @lockdown_client = Idevice::LockdownClient.attach(idevice: @idevice_ffi)
  end

  def enabled?(key)
    @lockdown_client.get_value(DOMAIN, key) == 1
  end

  def enable(key)
    @lockdown_client.set_value(DOMAIN, key, 1)
  end

  def disable(key)
    @lockdown_client.set_value(DOMAIN, key, 0)
  end

  def update(key, value)
    @lockdown_client.set_value(DOMAIN, key, value)
  end

  def value(key)
    @lockdown_client.get_value(DOMAIN, key)
  end
end