require_relative 'idevice_require'
require_relative 'idevice_ffi'

class AssistiveTouchUtil < IdeviceFfi
  DOMAIN = "com.apple.Accessibility"
  KEY = "AssistiveTouchEnabledByiTunes"
  REINCARNATE_KEY = "AssistiveTouchRepairIncarnationMode"

  def initialize(opts = {})
    super(opts)
    @lockdown_client = Idevice::LockdownClient.attach(idevice: @idevice_ffi)
  end

  def enabled?
    @lockdown_client.get_value(DOMAIN, KEY) == 1
  end

  def enable
    @lockdown_client.set_value(DOMAIN, KEY, 1)
  end

  def disable
    @lockdown_client.set_value(DOMAIN, KEY, 0)
  end

  # it basically darkens the bubble like someone has just interacted with it
  def reincarnate
    @lockdown_client.set_value(DOMAIN, REINCARNATE_KEY, 0)
  end
end
