require_relative 'idevice_require'
require_relative 'idevice_ffi'

class LanguageUtil < IdeviceFfi
  DOMAIN = "com.apple.international"
  KEY = "Language"

  def initialize(opts = {})
    super(opts)
    @lockdown_client = Idevice::LockdownClient.attach(idevice: @idevice_ffi)
  end

  def find_language
    @lockdown_client.get_value(DOMAIN, KEY)
  end

  def update_language(language = "en-GB")
    @lockdown_client.set_value(DOMAIN, KEY, language)
  end
end
