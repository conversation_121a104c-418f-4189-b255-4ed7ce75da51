# idevice_ffi

This directory (lib/utils/idevice_ffi) is meant to hold utils that we need to create by extending idevice gem.
tldr; idevice gem is ruby's ffi bindings for libimobildevice which we already make use of in idevicecontacts and idevicebookmarks.

## How to use this gem ? And Coding Practices
1. Create a util file in this directory with proper naming convention.
For example: If we want to create a util to manage assistive touch, we would create a file named `assistive_touch_util.rb` in this directory.

2. After creating a file, declare a class in that file and derive it from `IdeviceFfi` class. This class is responsible for creating the `idevice_ffi` object which calls `Idevice::Idevice.attach` from the idevice gem  . And you can access this object across in your class using `@idevice_ffi` variable. Make sure to intialize the base class too.

3. Add the method, variables etc whatever you want inside your class. Try to make your class as independent as possible, ideally it should serve only one purpose.

4. By default all clients are released by default during GA. But if you want to release it forcefully then you can call release method of that class, For example: If you create mobilesyncclient using `Idevice::MobileSyncClient.attach(idevice: @idevice_ffi)`, it starts syncing on the device, and to release that forcefully, call `Idevice::MobileSyncClient.release(mobile_sync_client)`.

5. If you think your utility can be used in daily debugging or common purposes, feel free to add relevant methods from your util in `ios_device.rb` too.

6. Happy Coding !


## Here are some of the things that we can do using libimobiledevice:
  libimobiledevice is inspired from Apple iTunes, Xcode and other such softwares that are provided by Apple which are able to control the device connected over USB. Open Source Community has reverse engineered such softwares and ported them into libimobiledevice. So basically if you think some Apple CLI or GUI based software is able to control things on the device, then those things might also be done using libimobiledevice.
  Some of the thins that we are using libimobiledevice for:
  1. **Managing contacts, bookmarks, calendars** - Using mobilesyncclient provided by libimobiledevice. We can also control Notes using mobilesyncclient.
    If you query `com.apple.mobile.data_sync` domain using `ideviceinfo --domain com.apple.mobile.data_sync`, you will get list of things that can be controlled via mobilesyncclient. Its mostly Contacts, Calendars, Bookmarks, Notes.
  2. **Setting icon layout** - libimobiledevice provides SpringBoard services client using which we can control home icon layout.
  3. **Managing Accessibility values** - There are bunch of Accessibility values that we can control using lockdown client provided by libimobiledevice. One of which is Assistive Touch. If you query domain `com.apple.Accessibility` using `ideviceinfo -q com.apple.Accessibility` you will get bunch of keys that we can control.
  4. **Taking device backup and restore** - Using idevicebackup2 provided by libimobiledevice
  5. **Modifying device locale** - We can modify values in `com.apple.international` using lockdown client provided by libimobiledevice.
  6. **Simulating network state (`NetworkLinkCondition`) and thermal conditions** - We can modify devicestate to simulate low network, or no network conditions, we can also control thermal conditions of device.
  5. There are still lot of things that we can do using libimobiledevice, the one mentioned above were just gist of some things that we can do. Try running `ideviceinfo --help`, you will get list of known domains names, try querying each of the domains, and see what values or keys you get, you can also look at the examples directory of idevice gem in this repository.

  ## Maintained By:
  @harshitshah4, @Krishna-Suravarapu

  Similar tools:
  https://github.com/danielpaulus/go-ios/
  https://github.com/alibaba/taobao-iphone-device
