require_relative "utils"
require_relative "../helpers/download_file_helper"
require_relative "../configuration"
require_relative "../helpers/data_report_helper"
require_relative "../helpers/xcui_test_helper"
require_relative "../models/device_state"
require "fileutils"

class DownloadFile < XCUITestHelper # rubocop:todo Metrics/ClassLength
  def initialize(device_id, session_id, product = "")
    super(device_id, session_id)
    @uuid = device_id
    @product = product
    @session_id = session_id
    @device_config = DeviceManager.device_configuration_check(@uuid)
    @ios_version = @device_config["device_version"]
    @device_state = DeviceState.new(device_id)
    @server_config = Configuration.new.all
    @download_file_data_reporter = DataReportHelper.new("download-file", session_id: session_id, product: product, ios_version: @ios_version, device: device_id)
    @download_files_zip_path = Utils.get_download_files_zip_path(@uuid)
    @download_files_dir_path = Utils.get_download_files_dir_path(@uuid)
    @preloaded_media_list_file_path = Utils.get_list_of_preloaded_media_path(@uuid)
    @download_file_utils = DownloadFileHelper.new(@uuid, @session_id, @product)
  end

  def setup
    log(:info, "Called setup method")
    log(:info, "Getting the contents of preloaded media in DCIM and storing it at file: #{@preloaded_media_list_file_path}")
    list_of_preloaded_media = IdeviceFileUtils.get_list_of_preloaded_media(@uuid)
    File.open(@preloaded_media_list_file_path, "w") { |file| file.puts(list_of_preloaded_media) }
    begin
      FileUtils.mkdir_p(@download_files_dir_path)
      FileUtils.touch("#{@download_files_dir_path}/sample")
      IdeviceFileUtils.add_file(@uuid, "#{@download_files_dir_path}/sample", "Documents/sample", CHROME_BUNDLE_ID)
    rescue => e
      log(:error, "Failed to create chrome bundle folder #{e.message} #{e.backtrace.join("\n")}")
      raise "Failed to create chrome bundle folder"
    end
    log(:info, "Setup done")
    true
  rescue => e
    log(:error, "Failed to setup device for download files: #{e.message} #{e.backtrace.join("\n")}")
    @download_file_data_reporter.report({ "result" => "failed", "error" => e.message.to_s })
    false
  ensure
    FileUtils.rm_rf(@download_files_dir_path)
  end

  def generate_and_upload(params, server_config)
    log(:info, "Called generate_and_upload method with params: #{params}")
    start_time = Time.now.to_i
    FileUtils.mkdir_p(@download_files_dir_path)
    downloaded_zip_status = @download_file_utils.generate(@download_files_zip_path, params)
    download_file_size = downloaded_zip_status[:file_size]
    error_message = downloaded_zip_status[:message]
    result = downloaded_zip_status[:status]
    return { message: downloaded_zip_status[:message], url: nil } if downloaded_zip_status[:status] == "failed"

    metadata = { download_zip_size: download_file_size.to_s } # uploading integer was a problem in metadata to S3
    log(:info, "Got the following data #{metadata} params: #{params} and message: #{downloaded_zip_status[:status]}")
    url = @download_file_utils.upload(params, @download_files_zip_path, metadata)
    log(:info, "Finished executing generate_and_upload method with params:#{params}. Pre-signed URL is available here: #{url}")
    result = "success"
    { message: downloaded_zip_status[:message], url: url }
  rescue => e
    log(:error, "Failed to download file #{e.message} #{e.backtrace.join("\n")}")
    download_file_size = 0
    error_message = e.message
    result = "failed"
    { message: e.message, url: nil }
  ensure
    end_time = Time.now.to_i
    @download_file_data_reporter.report({ result: result,
                                          time_taken: end_time - start_time,
                                          download_file_size: download_file_size,
                                          message: error_message })
    FileUtils.rm_rf(@download_files_dir_path)
  end

  def cleanup
    log(:info, "Called cleanup method")
    result = "failed"
    error_message = nil
    start_time = Time.now.to_i
    if @device_state.download_files_setup_done_file_clean_on_weekend?
      log(:info, "Setup file not found. Running one time setup.")
      unless @download_file_utils.one_time_setup
        log(:error, "One time setup failed to run. Continuing with the remaining cleanup steps.")
        error_message = "One time setup failed."
      end
    end
    log(:info, "Deleting the download_file folders on platform")
    FileUtils.rm_rf(@download_files_dir_path)
    FileUtils.rm_rf(@download_files_zip_path)

    log(:info, "Clearing the file with the list of all preloaded media in the device")
    FileUtils.rm_rf(@preloaded_media_list_file_path)

    log(:info, "Clearing the chrome folder")
    IdeviceFileUtils.truncate_folder(@uuid, "Documents/", CHROME_BUNDLE_ID)

    result = "success" unless error_message
    log(:info, "Cleanup done")
    true
  rescue => e
    log(:error, "Failed to clean up file #{e.message} #{e.backtrace.join("\n")}")
    false
  ensure
    end_time = Time.now.to_i
    @download_file_data_reporter.report({ result: result,
                                          time_taken: end_time - start_time,
                                          message: error_message })
    FileUtils.rm_rf(@download_files_dir_path)
  end

  def log(level, message)
    logger_params = { subcomponent: DOWNLOAD_FILE_TAG, device: @uuid, session_id: @session_id }
    BrowserStack.logger.send(level.to_sym, message, logger_params)
  end

end
