require_relative './zombie'
require 'bsenv'

##
# This is a module for intercepting and wrapping methods for a class instance or class methods.
# TODO: Add before_method and after_method options
module BrowserStack
  module MethodInterceptor
    def self.included(base_klass)
      base_klass.extend(ClassMethods)
      interceptor = const_set("#{base_klass.name.split('::').join('_')}Interceptor", Module.new)
      class_interceptor = const_set("#{base_klass.name.split('::').join('_')}ClassInterceptor", Module.new)
      base_klass.prepend interceptor
      base_klass.singleton_class.prepend class_interceptor
    end

    module ClassMethods
      ##
      # Wraps logic around an instance method for a class. Takes a list of method names and
      # a block to be executed around the method. Block has two arguments, first is the original method
      # wrapped in a proc, use this to call the original method implementation via `original_method.call`
      # (Note no need to pass arguments to original_method proc), the second argument is the original
      # method name
      def around_method(*method_names, &block)
        return if BSEnv.debug?

        klass_name = name
        interceptor = const_get("#{klass_name.split('::').join('_')}Interceptor")
        add_methods(klass_name, interceptor, method_names, &block)
      end

      ##
      # Wraps logic around a class method for a class. Takes a list of method names and
      # a block to be executed around the method. Block has two arguments, first is the original method
      # wrapped in a proc, use this to call the original method implementation via `original_method.call`
      # (Note no need to pass arguments to original_method proc), the second argument is the original
      # method name
      def around_class_method(*method_names, &block)
        return if BSEnv.debug?

        klass_name = name
        class_interceptor = const_get("#{klass_name.split('::').join('_')}ClassInterceptor")
        add_methods(klass_name, class_interceptor, method_names, &block)
      end

      private

      def add_methods(klass_name, interceptor, method_names, &around_block)
        interceptor.class_eval do
          method_names.each do |method_name|
            define_method(method_name) do |*args, &block|
              return_val = nil
              original_method = proc { return_val = super(*args, &block) }
              instance_exec(original_method, method_name, &around_block)
              return_val
            end
          end
        end
      end
    end
  end
end
