begin
  require 'browserstack_logger'
rescue LoadError
  puts "No browserstack_logger detected"
end

module Logging
  def self.bs_logger?
    defined?(BrowserStack.logger)
  end

  def self.log(level, message)
    bs_logger? ? BrowserStack.logger.send(level.to_sym, message) : puts("#{level.to_s.upcase}: #{message}")
  end

  def self.info(message)
    log(:info, message)
  end

  def self.debug(message)
    log(:debug, message)
  end

  def self.warn(message)
    log(:warn, message)
  end

  def self.error(message)
    log(:error, message)
  end
end