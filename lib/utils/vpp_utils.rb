require 'English'
require_relative '../custom_exceptions'
require_relative './osutils'
require_relative './zombie'
require_relative './http_utils'
require_relative '../configuration'
require 'net/http'
require 'json'
require 'uri'
require 'faraday'
require 'shellwords'

module BrowserStack
  class VPPUtils
    class << self

      LOG_IDENTIFIER = "[VPPUtils]"

      # Sample reponse
      # "adamId": "408709785",
      # "assignedCount": 5000,
      # "availableCount": 10000,
      def asset_information(adam_id)
        start_time = Time.now.to_i
        response = nil
        url = VPP_URLS[:assets]
        url += "?adamId=#{adam_id}"
        headers = { 'Authorization' => "Bearer #{VPP_TOKEN}" }
        response = BrowserStack::HttpUtils.make_get_request_with_headers(url, headers)
        raise VPPUtilsError, "Non 200 response code #{response.status}" if response.status != 200

        parsed_response = JSON.parse(response.body)
        if parsed_response.key?("assets")
          parsed_response["assets"].each do |asset|
            return asset if asset["adamId"] == adam_id.to_s
          end
        end
        raise VPPUtilsError, "No assets found for adamId: #{adam_id}"
      rescue => e
        handle_error("asset_information", start_time, e, response&.body)
        raise VPPUtilsError, "#{e.message}, status: #{response&.status}, body: #{response&.body}"
      end

      def token_expiry
        start_time = Time.now.to_i
        response = nil
        url = VPP_URLS[:assets]
        headers = { 'Authorization' => "Bearer #{VPP_TOKEN}" }
        response = BrowserStack::HttpUtils.make_get_request_with_headers(url, headers)
        raise VPPUtilsError, "Non 200 response code #{response.status}" if response.status != 200

        res = JSON.parse(response.body)
        res["tokenExpirationDate"]
      rescue => e
        handle_error("token_expiry", start_time, e, response&.body)
        raise VPPUtilsError, "#{e.message}, status: #{response&.status}, body: #{response&.body}"
      end

      # Will get details in format
      # {"assignments"=> [{"adamId"=>"535886823", "serialNumber"=>"GG2457D72G", "pricingParam"=>"STDQ"}]}
      def fetch_assignments(adam_id = nil, serial_number = nil)
        start_time = Time.now.to_i
        response = nil
        url = VPP_URLS[:assignments]
        # Filters for specific serial number and adam id
        url += "?adamId=#{adam_id}&serialNumber=#{serial_number}" if serial_number && adam_id

        headers = { 'Authorization' => "Bearer #{VPP_TOKEN}" }
        response = BrowserStack::HttpUtils.make_get_request_with_headers(url, headers)
        raise VPPUtilsError, "Non 200 response code #{response.status}" if response.status != 200

        JSON.parse(response.body)
      rescue => e
        handle_error("fetch_assignments", start_time, e, response&.body)
        raise VPPUtilsError, "#{e.message}, status: #{response&.status}, body: #{response&.body}"
      end

      # Sample reponse
      # {
      #     "eventStatus": "COMPLETE",
      #     "eventType": "ASSOCIATE",
      #     "numCompleted": 16,
      #     "numRequested": 16,
      #     "tokenExpirationDate": "2030-11-08T22:33:22+0000",
      #     "uId": "2049025000431439"
      # }
      def assignment_status(event_id)
        start_time = Time.now.to_i
        response = nil
        url = "#{VPP_URLS[:status]}?eventId=#{event_id}"
        headers = { 'Authorization' => "Bearer #{VPP_TOKEN}" }
        response = BrowserStack::HttpUtils.make_get_request_with_headers(url, headers)
        raise VPPUtilsError, "Non 200 response code #{response.status}" if response.status != 200

        JSON.parse(response.body)
      rescue => e
        handle_error("assignment_status", start_time, e, response&.body)
        raise VPPUtilsError, "#{e.message}, status: #{response&.status}, body: #{response&.body}"
      end

      # Sample response
      # {
      #   "eventId": "954910a8-3d9c-4fde-948d-253e5aef431a",
      #   "tokenExpirationDate": "2030-11-08T22:33:22+0000",
      #   "uId": "2049025000431439"
      # }
      def associate_license(app_name, serial_number)
        start_time = Time.now.to_i
        response = nil
        url = VPP_URLS[:associate]
        headers = {
          'Content-Type' => 'application/json',
          'Authorization' => "Bearer #{VPP_TOKEN}"
        }
        adam_id = APP_DETAILS[app_name.to_sym][:itunes_store_id]
        data = {
          "assets" => [
            {
              "adamId": adam_id,
              "pricingParam": "STDQ"
            }
          ],
          "serialNumbers": [
            serial_number
          ]
        }

        response = BrowserStack::HttpUtils.make_post_request_with_headers(url, headers, data)
        raise VPPUtilsError, "Non 200 response code #{response.status}" if response.status != 200

        JSON.parse(response.body)
      rescue => e
        handle_error("associate_license", start_time, e, response&.body)
        raise VPPUtilsError, "#{e.message}, status: #{response&.status}, body: #{response&.body}"
      end

      def handle_error(method_name, start_time, error, data = nil)
        BrowserStack.logger.info "#{LOG_IDENTIFIER} #{method_name} failed due to errror - #{error}, backtrace: #{error.backtrace} and data #{data}"
      end
    end
  end
end
