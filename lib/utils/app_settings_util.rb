require 'shellwords'
require 'base64'
require 'fileutils'
require_relative 'http_utils'
require_relative './utils'
require_relative 'osutils'
require_relative '../../config/constants'
require_relative '../../server/device_manager'
require_relative '../custom_exceptions'
require_relative '../helpers/wda_client'

module BrowserStack
  class AppSettingsUtil # rubocop:todo Metrics/ClassLength
    class << self

      def validate(app_path, params) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength, Metrics/PerceivedComplexity
        #check params
        if !params['update_app_settings'] ||
          !params['app_testing_bundle_id'] ||
          !params['app_display_name'] ||
          !params['session_id']
          raise AppSettingsError.new("Invalid params", BROWSERSTACK_ERROR_STRING, "invalid_request")
        end

        raise AppSettingsError.new("App file missing", BROWSERSTACK_ERROR_STRING, "invalid_request") unless app_path && File.exists?(app_path)

        # Check capability
        begin
          incoming_app_settings = JSON.parse(params["update_app_settings"])
          raise "Invaild DSL structure - not a hash" unless incoming_app_settings.is_a?(Hash)
        rescue => e
          BrowserStack.logger.error "update_app_settings Invalid DSL - #{e.message} #{e.backtrace}"
          raise AppSettingsError.new("Invaild DSL structure", BROWSERSTACK_ERROR_STRING, "invalid_request")
        end

        allow_defaults = true if params["is_firecmd"]
        valid_permissions_file = "#{CONFIG_PATH}/permission_settings/ios_#{params['os_version'].to_i}.json"
        parsedAppSettings = {}

        incoming_permission_settings = incoming_app_settings['Permission Settings'] || {}
        incoming_app_settings.delete('Permission Settings') # Deleting this key and using a new iterator for Permission Settings

        # Validating User's App Settings
        unless incoming_app_settings.empty?
          app_settings = if File.exists?(get_settings_bundle_json_path(app_path))
                           JSON.parse(File.read(get_settings_bundle_json_path(app_path)))
                         else
                           raise AppSettingsError.new("App Settings JSON absent", BROWSERSTACK_ERROR_STRING, "no_settings_bundle")
                         end

          parsedAppSettings = incoming_app_settings.map do |key, value|
            iterator(key, value, app_settings, allow_defaults)
          end
          parsedAppSettings = parsedAppSettings.inject(:merge)
        end

        # Validating Permission Settings
        unless incoming_permission_settings.empty? || params["is_firecmd"] || params["os_version"].to_i < 13
          permission_settings = if File.exists?(valid_permissions_file)
                                  JSON.parse(File.read(valid_permissions_file))
                                else
                                  raise AppSettingsError.new("Permission Settings JSON absent", BROWSERSTACK_ERROR_STRING, "no_settings_bundle")
                                end

          validate_permission_settings_dependancies(incoming_permission_settings)
          parsedPemissionSettings = incoming_permission_settings.map do |key, value|
            iterator(key, value, permission_settings, allow_defaults)
          end
          parsedPemissionSettings = parsedPemissionSettings.inject(:merge)

          # Merging HashMaps generated by App Settings and Permission Settings
          parsedAppSettings.merge!(parsedPemissionSettings)
        end

        parsedAppSettings
      rescue AppSettingsError => e
        BrowserStack.logger.error "update_app_settings Validation Failed - #{e.message} #{e.kind} #{e.meta_data} #{e.backtrace}"
        raise e
      rescue => e
        BrowserStack.logger.error "update_app_settings Validation Failed - #{e.message} #{e.backtrace}"
        raise AppSettingsError.new("Unknown Error - #{e.message}", BROWSERSTACK_ERROR_STRING, "internal_error")
      end

      def update_settings(device_id, app_settings_parsed_DSL, params) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
        #enable settings app
        Utils.enable_settings_app(device_id, params)

        #request WDA
        current_device_config = DeviceManager.device_configuration_check(device_id)
        wda_port = current_device_config["webdriver_port"].to_i
        request_url = "/bs/updateSettings"
        session_id =  params['session_id'] || params['automate_session_id']
        special_post_action = app_settings_parsed_DSL["special_post_action"]
        app_settings_parsed_DSL.delete("special_post_action")

        request_body = {
          "session_id" => session_id,
          "app_name" => params['app_display_name'],
          "app_bundle_id" => params['app_testing_bundle_id'],
          "DSL" => app_settings_parsed_DSL
        }
        request_body["special_post_action"] = special_post_action if special_post_action

        # For enterprise apps with non-default Appium versions, we need extra port forwarding
        # This is necessary because the default appium plist is unloaded during appium switch, breaking the iproxy tunnel to the WDA port on real devices
        # Similar to what we have for video recording, we're forwarding a new port to the wda_port to establish the tunnel connection
        # This is important otherwise update app settings request will timeout
        # JIRA: https://browserstack.atlassian.net/browse/AAP-15711
        if params["mdm_enterprise_app_install"].to_s == 'true' && params["is_non_default_appium"].to_s == 'true'
          wda_client, forwarding_port = create_enterprise_wda_client(device_id, wda_port, params)
        else
          wda_client = WdaClient.new(wda_port, device_id)
        end

        response = wda_client.send :make_request, 'POST', request_url, request_body
        #handle error
        if response['value']['status'] == 'error'
          error_obj = response['value']['error']

          if params["is_firecmd"] == true
            screenshot_data = error_obj['screenshot']
            file_name = "/tmp/update_settings_screenshot_#{session_id}.jpeg"
            File.open(file_name, 'wb') do |f|
              f.write(Base64.decode64(screenshot_data))
            end
          end

          # Delete this since if we push response to CLS and it will start giving Message Too Long error
          error_obj.delete("screenshot")

          raise AppSettingsError.new(error_obj['message'],
                                     BROWSERSTACK_ERROR_STRING,
                                     'setting_automation_error', error_obj['meta_data'])
        end
      rescue AppSettingsError => e
        BrowserStack.logger.error "update_app_settings Failed - #{e.message} #{e.kind} #{e.meta_data} #{e.backtrace}"
        raise e
      rescue => e
        BrowserStack.logger.error "update_app_settings Failed unknown error - #{e.message} #{e.backtrace}"
        raise AppSettingsError.new(e.message,
                                   BROWSERSTACK_ERROR_STRING,
                                   'internal_error')
      ensure
        push_to_cls(params, "update_settings_automation", '', { "update_settings" => response }) if response
        #disable settings app
        Utils.disable_settings_app(device_id, params)
        PortManager.stop_forwarding_port(device_id, forwarding_port, "update_settings") if defined?(forwarding_port) && !forwarding_port.nil?
      end

      # Creates a WDA client for enterprise applications that require custom port forwarding
      def create_enterprise_wda_client(device_id, wda_port, params)
        forwarding_port = wda_port + 1700 # reusing the same port as video recording
        BrowserStack.logger.info("[update_settings] Enterprise session: forwarding port #{forwarding_port} to #{wda_port} for device #{device_id}")

        # Stop any existing port forwarding before creating a new one
        PortManager.stop_forwarding_port(device_id, forwarding_port, 'update_settings')

        # Forward the new port
        PortManager.forward_port(device_id, forwarding_port, wda_port, 'update_settings')

        BrowserStack.logger.info("[update_settings] Port forwarding successful: #{forwarding_port} to #{wda_port}, device: #{device_id}")
        wda_client = WdaClient.new(forwarding_port, device_id)

        [wda_client, forwarding_port]
      rescue => e
        error_message = "[update_settings] Port forwarding failed. Error: #{e.message}. Backtrace: #{e.backtrace.join("\n")}"
        BrowserStack.logger.error(error_message)
        BrowserStack::Zombie.push_logs('update-settings-port-forwarding-error',
                                       error_message,
                                       {
                                         "device" => device_id,
                                         "session_id" => params['automate_session_id']
                                       })
        raise e
      end

      def foreground_user_app(device_id, params)
        #request WDA
        current_device_config = DeviceManager.device_configuration_check(device_id)
        wda_port = current_device_config["webdriver_port"].to_i
        request_url = "/bs/forgroundApp"
        request_body = {
          "session_id" => params['session_id'] || params['automate_session_id'],
          "app_name" => params['app_display_name'],
          "app_bundle_id" => params['app_testing_bundle_id'],
          "DSL" => {}
        }
        wda_client = WdaClient.new(wda_port)
        response = wda_client.send :make_request, 'POST', request_url, request_body
        push_to_cls(params, "update_settings_foreground", '', { "foreground_app" => response })
        #handle error
        if response['value']['status'] == 'error'
          error_obj = response['value']['error']
          raise AppSettingsError.new(error_obj['message'],
                                     BROWSERSTACK_ERROR_STRING,
                                     'setting_automation_error', error_obj['meta_data'])
        end
      rescue AppSettingsError => e
        BrowserStack.logger.error "update_app_settings foreground_user_app Failed - #{e.message} #{e.kind} #{e.meta_data} #{e.backtrace}"
        raise e
      rescue => e
        BrowserStack.logger.error "update_app_settings foreground_user_app Failed unknown error - #{e.message} #{e.backtrace}"
        raise AppSettingsError.new(e.message,
                                   BROWSERSTACK_ERROR_STRING,
                                   'internal_error')
      end

      def parse_settings_bundle(app_path, params = {})
        #input : app path = /tmp/apps/3e5b5819eff450cda933bd259d1998b1ae8963ee_production_21_20112020/f4a9727c-8f04-4b0f-af37-3a0ba2691ff5/Payload/sampleBrowserstack.app
        #output : settings JSON file
        session_id = params['session_id'] || params['automate_session_id']
        device_id = params['device'] || params['device_id']
        settings_json = {}
        Dir.glob("#{app_path}/Settings.bundle/*.plist").map do |plist_file|
          file_name = File.basename(plist_file, ".plist")
          json_string_output = BrowserStack::OSUtils.safe_execute("plutil", ["-convert", "json", plist_file, "-o", "-"])
          json_output = JSON.parse(json_string_output)
          next unless json_output.is_a?(Hash)

          key_string = file_name
          settings_json[key_string] = json_output
        end

        # incase app does not have Settings.bundle or any plist defined within it.
        return nil if settings_json.empty?

        app_settings_path = get_settings_bundle_json_path(app_path)
        File.write(app_settings_path, settings_json.to_json)
        # check for params and remove lproj from here
        remove_lproj_dir(app_path) if params['remove_ios_app_settings_localization']
        settings_json
      rescue => e
        BrowserStack.logger.error "update_app_settings Error while parsing settingsBundle - #{e.message} #{e.backtrace}"
        data = { "device" => device_id, "session_id" => session_id }
        BrowserStack::Zombie.push_logs("settings-bundle-parse-failed", "", data)
      end

      def get_settings_bundle_json_path(app_path)
        #input : app path = /tmp/apps/3e5b5819eff450cda933bd259d1998b1ae8963ee_production_21_20112020/f4a9727c-8f04-4b0f-af37-3a0ba2691ff5/Payload/sampleBrowserstack.app
        #output : json path =  /tmp/apps/3e5b5819eff450cda933bd259d1998b1ae8963ee_production_21_20112020/f4a9727c-8f04-4b0f-af37-3a0ba2691ff5/AppSettings.json
        dir_path = File.expand_path("../..", app_path)
        "#{dir_path}/AppSettings.json"
      end

      def get_response(status, error = nil, meta_data = {})
        response_obj = { status: status }
        if !error.nil? && !error.empty?
          response_obj[:error] = { "kind" => error }
          response_obj[:error][:meta_data] = meta_data unless meta_data.nil? && meta_data.empty?
        end
        response_obj
      end

      private

      def validate_settings_tuple(key, value, app_settings, allowDefaults = false)
        specifer = nil
        plists = app_settings.keys

        # Check if present in Root
        root_node = app_settings["Root"] if plists.include?("Root") && !app_settings["Root"].empty?
        specifer = find_specifier(key, value, root_node)

        # Check in other child plist nodes
        if specifer.nil?
          specifiers = (plists - ["Root"]).map do |node|
            plist_node = app_settings[node] if app_settings[node] && !app_settings[node].empty?
            find_specifier(key, value, plist_node)
          end
          specifiers.compact!
          specifer = specifiers[0]
        end

        raise AppSettingsError.new("Key not found", USER_ERROR_STRING, "invalid_key", { key: key, value: value } ) if specifer.nil?

        case specifer['Type']
        when "PSTextFieldSpecifier"
          valid_settings_tuple = validate_text_field_specifier(specifer, key, value, allowDefaults)
        when "PSToggleSwitchSpecifier"
          valid_settings_tuple = validate_toggle_switch_specifier(specifer, key, value, allowDefaults)
        when "PSMultiValueSpecifier"
          valid_settings_tuple = validate_multi_value_specifier(specifer, key, value, allowDefaults)
        when "PSChildPaneSpecifier"
          valid_settings_tuple = validate_child_pane_specifier(specifer, key, value, app_settings, allowDefaults)
        when "PSSliderSpecifier"
          valid_settings_tuple = validate_slider_specifier(specifer, key, value, allowDefaults)
        else
          raise AppSettingsError.new("Specifier type not supported", USER_ERROR_STRING, "unsupported_specifier", { key: key, value: value, type: specifer['Type'] })
        end

        # Used to perform post setting automation actions i.e handling extra popups
        valid_settings_tuple['special_post_action'] = specifer['special_post_action_value_map'][value.to_s] if specifer['special_post_action_value_map']
        valid_settings_tuple
      end

      def find_specifier(key, value, settings)
        if settings && settings["PreferenceSpecifiers"] && settings["PreferenceSpecifiers"].is_a?(Array)
          specifiers = settings["PreferenceSpecifiers"].select { |specifer| specifer['Title'] == key }
          specifiers[0] #only honouring the first Title match.
        end
      end

      def iterator(key, value, app_settings, allow_defaults)
        if value.is_a?(Hash)
          validate_settings_tuple(key, value, app_settings, allow_defaults)
          o = value.map do |inner_key, inner_value|
            iterator(inner_key, inner_value, app_settings, allow_defaults)
          end
          v = o.inject(:merge)
          v.empty? ? {} : { key => { "value" => v, "type" => "PSChildPaneSpecifier" } }
        else
          validate_settings_tuple(key, value, app_settings, allow_defaults)
        end
      end

      def validate_text_field_specifier(specifer, key, value, allowDefaults)
        allowDefaults && specifer["DefaultValue"] == value ? {} : { key => { "value" => value, "type" => specifer["Type"] } }
      end

      def validate_slider_specifier(specifer, key, value, allowDefaults)
        raise AppSettingsError.new("Specifier type not supported", USER_ERROR_STRING, "unsupported_specifier", { key: key, value: value, type: specifer['Type'] })
      end

      def validate_toggle_switch_specifier(specifer, key, value, allowDefaults)
        raise AppSettingsError.new("Invalid value for key", USER_ERROR_STRING, "invalid_value", { key: key, value: value }) unless %w[ON OFF].include?(value)

        input_value = value == "ON"
        allowDefaults && specifer["DefaultValue"] == input_value ? {} : { key => { "value" => value, "type" => specifer["Type"] } }
      end

      def validate_multi_value_specifier(specifer, key, value, allowDefaults)
        raise AppSettingsError.new("Invalid value for key", USER_ERROR_STRING, "invalid_value", { key: key, value: value }) unless specifer["Titles"].include?(value) || specifer["Titles"].include?("*")

        # raise "invalid multivalues" if specifer["Titles"] && specifer["Values"] && (specifer["Titles"].length != specifer["Values"].length)
        if specifer["Titles"].include?("*")
          { key => { "value" => value, "type" => specifer["Type"] } }
        else
          input_index = specifer["Titles"].index(value)
          allowDefaults && specifer["DefaultValue"] == specifer["Values"][input_index] ? {} : { key => { "value" => value, "type" => specifer["Type"] } }
        end
      end

      def validate_child_pane_specifier(specifer, key, value, app_settings, allowDefaults)
        plists = app_settings.keys
        raise AppSettingsError.new("Invalid value for key", USER_ERROR_STRING, "invalid_value", { key: key, value: value }) unless plists.include?(specifer["File"])
      end

      def validate_permission_settings_dependancies(incoming_permission_settings)
        # Validating Precise Location Dependancies
        raise AppSettingsError, "Precise Location cannot be set when Location Access is set to 'Never'" if !incoming_permission_settings["Location"].nil? && !incoming_permission_settings["Location"]["Precise Location"].nil? && incoming_permission_settings["Location"]["ALLOW LOCATION ACCESS"] == "Never"

        raise AppSettingsError.new("Not a valid Location Permission Setting", USER_ERROR_STRING, "invalid_dsl")  if !incoming_permission_settings["Location"].nil? && (!incoming_permission_settings["Location"].is_a?(Hash) || ( incoming_permission_settings["Location"]["ALLOW LOCATION ACCESS"].nil? && incoming_permission_settings["Location"]["Precise Location"].nil? ) )
      end

      def remove_lproj_dir(app_path)
        Dir.glob("#{app_path}/Settings.bundle/*.lproj").map do |lproj_dir|
          FileUtils.rm_rf(lproj_dir)
        end
      end

    end

  end
end
