#!/bin/bash

# Shell script to launch xcodebuild which builds our custom webdriveragent
# for Live Testing and installs them to the phone.

DEVICE=$1
XCTEST_RUN_FILE_PATH=$2
SESSION_ID=$3

lda_log() {
  echo "$SESSION_ID [$(date -u)] : $@" >> "/var/log/browserstack/live_driver_agent_$DEVICE.log"
}

lda_log "starting xcodebuild"

xcodebuild test-without-building -xctestrun "$XCTEST_RUN_FILE_PATH" -destination id="$DEVICE" 2>&1 \
  | while read line
do
    lda_log "$line"
done

lda_log "exit code is ${PIPESTATUS[0]}"
