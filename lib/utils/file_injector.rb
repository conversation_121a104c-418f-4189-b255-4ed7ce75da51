require_relative 'http_utils'
require_relative 'osutils'
require_relative '../../config/constants'
require_relative './utils'
require_relative './zombie'
require_relative './idevice_utils'
require_relative './idevice_file_utils'
require 'fileutils'
require 'shellwords'

class FileInjector
  class << self
    def inject_file(device, params) #rubocop:todo Metrics/AbcSize
      log(:info, "Started injecting file for device: #{device} and params: #{params}")
      status = "success"
      error = nil
      download_time = 0

      start_time = Time.now.to_i

      session_id = params[:session_id]
      file_url = params[:file_url]
      media_hashed_id = params[:media_hashed_id]
      format = params[:format]
      product = params[:product]
      file_name = params[:file_name]

      file_name_array = file_name.split('.')
      file_name_array.pop
      real_file_name = file_name_array.join('.')

      return unless ['app_live', 'live'].include?(product)

      injection_file_dir = get_injection_file_dir(device)
      FileUtils.mkdir_p(injection_file_dir)

      file_path = File.join(injection_file_dir, "#{media_hashed_id}#{format}")

      download_start_time = Time.now.to_i
      BrowserStack::HttpUtils.download(file_url, file_path, { retry_count: 3, timeout: 60 }, "file_injection")
      download_time = Time.now.to_i - download_start_time

      bundle_id = CHROME_BUNDLE_ID

      raise "Empty bundle id for session #{session_id}" if bundle_id.nil? || bundle_id.empty?

      insert_injection_file_in_app(device, bundle_id, file_path, format, session_id, params, real_file_name)

      log(:info, "Successfully injected file for device: #{device} and params: #{params}")
      Utils.notify_pusher(FILE_INJECTION_SUCCESS, params.transform_keys(&:to_s), device, nil, params[:product])
      true
    rescue => e
      log(:error, "Failed to inject file for device: #{device} and params: #{params}, error: #{e.message}, #{e.backtrace.join("\n")}")
      Utils.notify_pusher(FILE_INJECTION_FAILED, params.transform_keys(&:to_s), device, nil, params[:product])
      status = "failed"
      error = "Failed to push file to device"
      BrowserStack::Zombie.push_logs("inject-file-failure", e.message.to_s, { "session_id" => params[:session_id], "device" => device, "url" => params[:product] })
      raise e
    ensure
      end_time = Time.now.to_i
      data_to_push = { event_name: "FileInjection", product: params[:product], os: IdeviceUtils.os(device), team: "device_features" }
      event_json = { session_id: session_id, file_injection: status, error_reason: error, time_taken: end_time - start_time, download_time_taken: download_time }
      data_to_push.merge!({ event_json: event_json })
      Utils.send_to_eds(data_to_push, "web_events", true)
    end

    def insert_injection_file_in_app(device, bundle_id, file_path, format, session_id, params, real_file_name)
      original_format = format
      total_attempts = 3
      attempt = 0
      begin
        attempt += 1

        if original_format == ZIP
          file_unzip_directory = File.join(get_injection_file_dir(device), real_file_name)
          FileUtils.mkdir_p(Shellwords.escape(file_unzip_directory))
          unzip_result, unzip_status = BrowserStack::OSUtils.execute("unzip \"#{file_path}\" -d \"#{file_unzip_directory}\"", true, timeout: 30)

          if unzip_status != 0
            raise "Failed to push file to device #{device}, Reason: Failed to unzip file at path: " \
                  "#{file_path} to directory: #{file_unzip_directory}, result: #{unzip_result}, " \
                  "status: #{unzip_status}"
          end

          file_path = file_unzip_directory
          format = ""
        end

        destination_file_path = Shellwords.escape("Documents/Injected Files/#{real_file_name}_#{(Time.now.to_f * 1000).to_i}#{format}")
        output, status = IdeviceFileUtils.add_file(device, Shellwords.escape(file_path), destination_file_path, bundle_id, timeout: 60, return_status: true)
        log(:info, "Output of file injection device #{device} bundle filepath #{file_path} status: #{status} output #{output}")
        raise "Failed to push file to device #{device}" unless status.to_i == 0

        true
      rescue => e
        retry if attempt < total_attempts && e.message.include?("Failed to push file to device #{device}")
        raise e
      ensure
        FileUtils.rm_rf(File.join(get_injection_file_dir(device), real_file_name)) if original_format == ZIP
      end
    end

    def get_injection_file_dir(device)
      log(:info, "Device: #{device} #{INJECTION_MEDIA_DIR}")
      File.join(INJECTION_MEDIA_DIR, device)
    end

    def cleanup(device)
      # ensure rm of downloaded images folder
      FileUtils.rm_rf(get_injection_file_dir(device))
    end

    def log(level, message)
      logger_params = { component: "file_injector" }
      BrowserStack.logger.send(level.to_sym, message, logger_params)
    end
  end
end

if $PROGRAM_NAME == __FILE__
  command = ARGV[0].to_s.strip

  case command
  when "inject_file"
    device_id = ARGV[1].to_s.strip
    device_injection_media_dir = FileInjector.get_injection_file_dir(device_id)
    params = begin
      JSON.parse(File.read(File.join(device_injection_media_dir, "params.json")), symbolize_names: true)
    rescue
      {}
    end
    FileUtils.rm(File.join(device_injection_media_dir, "params.json"))
    FileInjector.inject_file(device_id, params)
  end
end