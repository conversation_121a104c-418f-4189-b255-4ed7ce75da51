require_relative 'osutils'

module DeviceCtl
  module Device
    def self.install(device, path, timeout: nil)
      BrowserStack::OSUtils.execute("xcrun devicectl device install app #{path} --device #{device}", true, timeout: timeout)
    end

    def self.uninstall(device, bundle_id)
      BrowserStack::OSUtils.execute("xcrun devicectl device uninstall app #{bundle_id} --device #{device}")
    end

    def self.reboot(device)
      BrowserStack::OSUtils.execute("xcrun devicectl device reboot --device #{device}")
    end

    def self.apps(device, timeout: nil)
      BrowserStack::OSUtils.execute("xcrun devicectl device info apps --device #{device} --include-all-apps", timeout: timeout)
    end

    def self.app(device, bundle_id)
      BrowserStack::OSUtils.execute("xcrun devicectl device info apps --device #{device} --bundle-id #{bundle_id} --include-all-apps")
    end

    def self.device_info(device)
      BrowserStack::OSUtils.execute("xcrun devicectl device info details --device #{device}")
    end

    def self.pairing_state(device)
      pairing_state = nil
      info = device_info(device)
      info.split("\n").each do |line|
        if line.include?("pairingState:")
          pairing_state = line.split(":").last.strip
          break
        end
      end
      pairing_state
    end

    def self.paired?(device)
      return true if pairing_state(device) == "paired"

      false
    end

    def self.launch_app(device, bundle_id)
      BrowserStack::OSUtils.execute("xcrun devicectl device process launch --device #{device} #{bundle_id}")
    end

    def self.launch_app_with_prefs(device, bundle_id, prefs)
      BrowserStack::OSUtils.execute("xcrun devicectl device process launch --device #{device} --payload-url \"#{prefs}\" #{bundle_id}")
    end

    def self.displays(device)
      BrowserStack::OSUtils.execute("xcrun devicectl device info displays --device #{device}")
    end
  end

  module List
    def self.devices
      BrowserStack::OSUtils.execute("xcrun devicectl list devices --columns '*'", user: USER)
    end

    def self.device_state(device_id, timeout: nil)
      output = BrowserStack::OSUtils.execute("xcrun devicectl list devices --filter \"UDID BEGINSWITH '#{device_id}'\" --columns 'State' --hide-default-columns | grep -v 'Devices'", timeout: timeout, user: USER)
      states = output.split(/\n/)[2..]
      raise "No device found." if states.nil?

      return "connected" if states.include? "connected"

      states[0]
    rescue => e
      e.message
    end
  end

  module Manage
    def self.pair(device, return_status: false, timeout: 60, kill_after: 60, return_error: false)
      BrowserStack::OSUtils.execute("xcrun devicectl manage pair --device #{device}", return_status, timeout: timeout, kill_after: kill_after, return_error: return_error)
    end

    def self.unpair(device, timeout: nil)
      BrowserStack::OSUtils.execute("xcrun devicectl manage unpair --device #{device}", timeout: timeout)
    end
  end
end
