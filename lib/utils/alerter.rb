require_relative '../configuration'
require_relative 'http_utils'

module BrowserStack
  class Alerter
    class << self
      def configure(server_config)
        @@env = server_config['environment']
        @@send_alerts = (@@env == 'prod')
        @@send_to = server_config['send_alerts_to']
        @@alerter_server_url = server_config['alerter_http_url']
        @@host = begin
          File.read(server_config['ip_file'])
        rescue
          Socket.gethostname
        end
      end

      def send(subject, message, to=nil, critical=false)
        subject = "[#{@@env}][#{@@host}]" + subject

        send_to = if to.nil?
                    @@send_to.join(',')
                  else
                    to.join(',')
                  end

        BrowserStack.logger.info("Sending Alert: sub: #{subject} msg: #{message} to:#{send_to} critical: #{critical}")

        unless @@send_alerts
          BrowserStack.logger.warn("Alerting skipped as env is #{@@env}")
          return
        end

        data = {
          'people' => send_to,
          'subject' => subject,
          'message' => message
        }

        if critical
          data.merge!({ 'mobile' => 'critical' })
        else
          data.merge!({ 'mobile' => 'false' })
        end

        begin
          HttpUtils.send_post_raw(@@alerter_server_url, data)
        rescue
          nil
        end
      end
    end
  end
end
