require_relative './http_utils'

class Telephony
  def initialize(endpoint, username, password)
    @endpoint = endpoint
    @auth = { username: username, password: password }
  end

  def push_imsi(device, imsi)
    return "device or imsi can't be empty" if device.nil? || device.empty? || imsi.nil? || imsi.empty?

    BrowserStack::HttpUtils.send_post("#{@endpoint}/device", {
      "imsi" => imsi,
      "device" => device
    }, @auth)
  rescue => e
    BrowserStack.logger.error("Error posting IMSI to telephony service! #{e.message}")
  end
end
