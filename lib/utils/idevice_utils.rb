#
# utils package for idevice related commonly used commands
#

require 'English'
Encoding.default_external = Encoding::UTF_8
Encoding.default_internal = Encoding::UTF_8

require_relative './osutils'
require_relative './utils'
require_relative 'devicectl'
require_relative '../custom_exceptions'
require_relative '../configuration'
require_relative '../../config/constants'
require_relative './pymobiledevice'

require 'securerandom'
require 'csv'
require 'plist'
require 'timeout'
require 'shellwords'
require 'nokogiri'
require 'json'
require 'vpim'

class IdeviceUtils # rubocop:todo Metrics/ClassLength
  COMMAND_TIMEOUT = 60

  IDEVICE_CONNECTION_TYPES = {
    usb: 'usb',
    network: 'network'
  }

  class << self
    attr_reader :idevicesyslog_bin_path

    def configure(settings)
      @idevice_id = IDEVICE_ID
      @ideviceinfo = IDEVICEINFO
      @idevicename = IDEVICENAME
      @idevicedate = IDEVICEDATE
      @idevicepair = IDEVICEPAIR
      @idevicediagnostics = IDEVICEDIAGNOSTICS
      @idevicescreenshot = IDEVICESCREENSHOT
      @ideviceimagemounter = IDEVICEIMAGEMOUNTER
      @ideviceinstaller = IDEVICEINSTALLER
      @idevicedebug = IDEVICEDEBUG
      @idevicelocation_bin_path = IDEVICELOCATION
      @idevicebookmark_bin_path = IDEVICEBOOKMARK
      @idevicecontacts_bin_path = IDEVICECONTACTS
      @idevicesyslog_bin_path = IDEVICESYSLOG
      @idevicecrashreport_bin_path = IDEVICECRASHREPORT

      @contacts_data_path = settings["contacts_data_path"]
      @contacts_metadata_path = settings["contacts_metadata_path"]
      @known_apps = Set.new(settings['known_apps'])
      @config_root = settings["config_root"]
      @signed_app_dir = settings["signed_app_dir"]

      @connection_type = settings['idevice_connection_type'] || IDEVICE_CONNECTION_TYPES[:usb]
      add_network_flag_to_binaries if @connection_type == IDEVICE_CONNECTION_TYPES[:network]
    end

    def ideviceinfo_bin
      @ideviceinfo
    end

    def idevices
      devices = BrowserStack::OSUtils.execute("#{@idevice_id} -l").gsub(" (Network)", "").split("\n")

      if devices.empty?
        5.times do
          devices |= BrowserStack::OSUtils.execute("#{@idevice_id} -l").gsub(" (Network)", "").split("\n") # a|b performs set union between a and b
          sleep 0.2
        end
      end

      devices
    end

    def ideviceinfo(device, query = "", domain = "")
      query = "-k #{query}" if query != ""
      domain = "-q #{domain}" if domain != ""
      BrowserStack::OSUtils.execute("#{@ideviceinfo} -u #{device} #{domain} #{query} 2>&1", timeout: 60).split("\n")
    end

    def idevicepair(device, command = "")
      BrowserStack::OSUtils.execute("#{@idevicepair} -u #{device} #{command}")
    end

    # If +name+ is nil, the current name of the device is returned
    # otherwise, name is set as the device name
    #
    # @param [String] device_id
    # @param [String] name
    # @return [String] device name if name is nil else `device name set to 'name'`
    def idevice_name(device_id, name: nil)
      name.nil? ? BrowserStack::OSUtils.execute("#{@idevicename} -u #{device_id}").strip : BrowserStack::OSUtils.execute("#{@idevicename} -u #{device_id} '#{name}'").strip
    end

    def os(device_id)
      IdeviceUtils.ideviceinfo(device_id, 'ProductType')[0].start_with?("AppleTV") ? 'tvos' : 'ios'
    end

    def os_case_sensitive(device_id)
      Utils.os_case_sensitive(os(device_id))
    end

    def apple_tv_device?(device_id)
      os(device_id) == 'tvos'
    end

    def device_version(device)
      device_info = ideviceinfo(device)
      version = device_info.find { |info| info =~ /^ProductVersion/ }.split(' ')[1]
      Gem::Version.new(version)
    end

    def use_devicectl(device)
      device_version(device) >= Gem::Version.new('17')
    end

    def device_time(device)
      BrowserStack::OSUtils.execute("#{@idevicedate} -u #{device}")
    end

    def reboot(device, return_status = false)
      BrowserStack::OSUtils.execute("#{@idevicediagnostics} -u #{device} restart", return_status)
    end

    def battery_info(device)
      YAML.safe_load(BrowserStack::OSUtils.execute("#{@ideviceinfo} -u #{device} -q com.apple.mobile.battery"))
    end

    def battery_capacity(device)
      battery_info(device).fetch("BatteryCurrentCapacity")
    end

    def disk_usage_info(device)
      YAML.safe_load(BrowserStack::OSUtils.execute("#{@ideviceinfo} -u #{device} -q com.apple.disk_usage"))
    end

    def disk_usage(device)
      disk_usage_info(device).fetch("TotalDiskCapacity")
    end

    def airplane_mode_on?(device)
      data = BrowserStack::OSUtils.execute("#{@idevicediagnostics} -u #{device} mobilegestalt \"AirplaneMode\" | grep -A1 \"<key>AirplaneMode</key>\" | tail -1 | grep true")
      !data.empty?
    end

    def device_supervised?(device)
      device_supervised = BrowserStack::OSUtils.execute("#{@ideviceinfo} -u #{device} -q com.apple.mobile.chaperone -k DeviceIsChaperoned 2>&1").strip
      device_supervised.include?("true")
    end

    def has_passcode?(device)
      ideviceinfo(device, "PasswordProtected")[0] == "true"
    rescue => e
      BrowserStack.logger.error("Could not check if device has passcode: #{e.message}")
      false
    end

    # Stores the screenshot in the file <file_name>
    def screenshot(device, file_name, log_command = true)
      data = BrowserStack::OSUtils.execute("#{@idevicescreenshot} -u #{device} #{file_name} 2>&1", log_command: log_command)

      retval = $CHILD_STATUS
      raise "Exception in screenshot : #{data}" if retval != 0
    end

    def mounted_developer_image(device)
      if apple_tv_device?(device) && device_version(device) >= Gem::Version.new('18')
        begin
          YAML.safe_load(BrowserStack::OSUtils.execute("#{@ideviceimagemounter} -u #{device} --list").to_s)
        rescue
          YAML.safe_load(BrowserStack::OSUtils.execute("#{@ideviceimagemounter} -u #{device} -l").to_s)
        end
      else
        # data["ImageSignature[1]"][0] gives the base64 encoded version of the currently mounted image's signature:
        # DeveloperDiskImage.dmg.signature (Found in /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/DeviceSupport/<IOS VERSION>/)
        YAML.safe_load(BrowserStack::OSUtils.execute("#{@ideviceimagemounter} -u #{device} -l").to_s)
      end
    end

    # returns the device timezone
    def timezone(device)
      ideviceinfo(device, "TimeZone")[0]
    rescue => e
      BrowserStack.logger.error("Could not get the device timezone : #{e.message}")
      raise e
    end

    # returns the device time
    def time(device)
      offset = ideviceinfo(device, "TimeZoneOffsetFromUTC")[0].to_s.to_f.to_i
      Time.now + offset
    rescue => e
      BrowserStack.logger.error("Could not get the device timezone : #{e.message}")
      raise e
    end

    # Returns true if image is mounted successfully
    def mount_developer_image(device, dmg_path, dmg_signature_path)
      resp = BrowserStack::OSUtils.execute("#{@ideviceimagemounter} -u #{device} -t Developer #{dmg_path.inspect} #{dmg_signature_path.inspect}") # Using inspect here to escape brackets and spaces

      $CHILD_STATUS == 0 || !resp.match(/ImageMountFailed/)
    end

    def install_app(device, path_to_app_bundle, *args, reinstall: true, avoid_devicectl: false)
      if use_devicectl(device) && !avoid_devicectl
        begin
          msg, exit_status = DeviceCtl::Device.install(device, path_to_app_bundle)
          raise msg if exit_status != 0

          return
        rescue => e
          # sometimes this fails to install due to conflicting profile, use the old method in that case
          BrowserStack.logger.error("Failed to install app using devicectl: #{e}")
        end
      end
      args.unshift('-r') if reinstall
      BrowserStack::OSUtils.execute("/usr/local/bin/gtimeout -s KILL 120 #{IOS_DEPLOY} --id #{device} --bundle #{path_to_app_bundle} #{args.join(' ')}")
    end

    def uninstall_app(device, bundle_id, force_skip_device_ctl: false)
      BrowserStack::OSUtils.execute("#{IOS_DEPLOY} --id #{device} --uninstall_only --bundle_id \"#{bundle_id}\"", timeout: 60)
    end

    def uninstall_and_reinstall_app(device, path_to_app_bundle, bundle_id)
      if use_devicectl(device)
        DeviceCtl::Device.uninstall(device, bundle_id)
        DeviceCtl::Device.install(device, path_to_app_bundle)
      end
      BrowserStack::OSUtils.execute("/usr/local/bin/gtimeout -s KILL 240 #{IOS_DEPLOY} --id #{device} -r -b #{path_to_app_bundle} --bundle_id \"#{bundle_id}\"")
    end

    def update_app(device, path_to_app_bundle, timeout)
      BrowserStack::OSUtils.safe_execute(@ideviceinstaller, ['-u', device, '-g', path_to_app_bundle], return_status = true, timeout: timeout)
    end

    def check_app_with_bundle_id_exists(device, bundle_id, avoid_devicectl: false)
      resp = BrowserStack::OSUtils.execute("/usr/local/bin/gtimeout -s KILL 100 #{IOS_DEPLOY} --id #{device} -e --bundle_id #{bundle_id} | tail -1")
      resp.to_s.strip == "true"
    end

    def check_app_with_bundle_id_exists_with_retries(device, bundle_id, max_retries = 10, wait_seconds = 5)
      retries = 0
      begin
        raise "[check_app_with_bundle_id_exists_with_retries] App with bundle id: #{bundle_id} does not exists on device: #{device}" unless IdeviceUtils.check_app_with_bundle_id_exists(device, bundle_id)

        true
      rescue => e
        retries += 1
        if retries < max_retries
          BrowserStack.logger.info "[check_app_with_bundle_id_exists_with_retries] Sleeping for #{wait_seconds} and retrying, retry_count: #{retries}, max_retries: #{max_retries}"
          sleep wait_seconds
          retry
        end
        false
      end
    end

    def uninstall_redirect_app(device)
      Thread.bs_run { BrowserStack::OSUtils.execute("#{IOS_DEPLOY} --id #{device} --uninstall_only --bundle_id com.browserstack.Redirect") }
    end

    def launch_app(device, path_to_bundle)
      BrowserStack::OSUtils.execute("#{IOS_DEPLOY} --id #{device} --justlaunch --bundle #{path_to_bundle}")
    end

    def launch_app_with_bundle_id_v2(device, bundle_id)
      if use_devicectl(device)
        output = DeviceCtl::Device.launch_app(device, bundle_id)
        raise "Failed to launch app: #{bundle_id}" unless output.include? "Launched application with #{bundle_id}"

        return
      end

      PyMobileDevice::Developer.launch(device, bundle_id)
    end

    def kill_process_with_name(device, process_name)
      PyMobileDevice::Developer.kill_process_with_name(device, process_name)
    end

    # requires separate thread as idevicedebug will not return until cancelled
    # but killing thread will leave requested app open on device
    def launch_app_with_bundle_id(device, bundle_id)
      thread = Thread.bs_run { BrowserStack::OSUtils.execute("#{@idevicedebug} -u #{device} run #{bundle_id}") }
      sleep 1
      Thread.kill(thread)
      return unless BrowserStack::OSUtils.is_process_running?('idevicedebug', device)

      BrowserStack.logger.info("Killing stale idevicedebug processes for this device: #{device}")
      OSUtils.kill_duplicate_stale_processes("idevicedebug", device)
    end

    def run_with_timeout(command, timeout = 5)
      BrowserStack::OSUtils.execute(command.to_s, true, timeout: timeout)
    rescue OSUtilsError => e
      BrowserStack.logger.error e.message.to_s
      calling_method = caller_locations[1].nil? ? "" : caller_locations[1].label
      raise "ideviceinstaller: #{calling_method} timed out"
    end

    # Do not use this unless absolutely necessary as its increased usage causes
    # more timeouts
    #
    # If using this, memoize the result instead of calling this multiple times
    def list_apps(device, kind: :user, timeout: 5, attempts: 1)
      raise ArgumentError, "Unknown kind: #{kind}" unless %i[user all system].include?(kind)

      command = "#{@ideviceinstaller} -u #{device} -o xml -l -o list_#{kind} 2>&1"

      begin
        attempts -= 1

        output, status = run_with_timeout(command, timeout)

        if status != 0
          BrowserStack.logger.error "Non zero exit code Output is #{output}"
          raise "ideviceinstaller: Non zero exit code in list_apps"
        end
      rescue
        retry unless attempts <= 0
        raise
      end

      Plist.parse_xml(output).map do |app|
        {
          bundle_id: app["CFBundleIdentifier"],
          version: app["CFBundleVersion"],
          display_name: app["CFBundleDisplayName"],
          version_string: app["CFBundleShortVersionString"],
          bundle_executable: app["CFBundleExecutable"]
        }
      end
    end

    # Do not use this unless absolutely necessary as its increased usage causes
    # more timeouts
    #
    # If using this, memoize the result instead of calling this multiple times
    def list_user_installed_apps_details(device, timeout: 5, attempts: 1)
      list_apps(device, kind: :user, timeout: timeout, attempts: attempts).reject do |app|
        @known_apps.any? { |known_app| app[:bundle_id].include?(known_app) }
      end
    end

    # Do not use this unless absolutely necessary as its increased usage causes
    # more timeouts
    #
    # If using this, memoize the result instead of calling this multiple times
    def list_user_installed_apps(device, display_name: false, timeout: 5, attempts: 1)
      key = (display_name ? :display_name : :bundle_id)

      list_user_installed_apps_details(device, timeout: timeout, attempts: attempts).map do |app|
        app[key]
      end
    end

    # Do not use this unless absolutely necessary as its increased usage causes
    # more timeouts
    #
    # If using this, memoize the result instead of calling this multiple times
    def list_installed_chromium_apps(device, display_name: false, timeout: 5, attempts: 1)
      key = (display_name ? :display_name : :bundle_id)

      list_installed_chromium_apps_details(device, timeout: timeout, attempts: attempts).map do |app|
        app[key]
      end
    end

    # Do not use this unless absolutely necessary as its increased usage causes
    # more timeouts
    #
    # If using this, memoize the result instead of calling this multiple times
    def list_installed_chromium_apps_details(device, timeout: 5, attempts: 1)
      list_apps(device, kind: :user, timeout: timeout, attempts: attempts).select do |app|
        app[:bundle_id].include?(CHROMIUM_BUNDLE_ID)
      end
    end

    # Do not use this unless absolutely necessary as its increased usage causes
    # more timeouts
    #
    # If using this, memoize the result instead of calling this multiple times
    def app_installed?(device, app_name, attempts: 1)
      apps = list_apps(device, kind: :user, attempts: attempts)
      apps.any? { |app| [app[:bundle_id], app[:display_name]].include?(app_name) }
    end

    # Do not use this unless absolutely necessary as its increased usage causes
    # more timeouts
    #
    # If using this, memoize the result instead of calling this multiple times
    def app_version(device, app_name, attempts: 1)
      apps = list_apps(device, kind: :user, attempts: attempts)
      apps.any? { |app|  }
      version = nil
      apps.each do |app|
        if [app[:bundle_id], app[:display_name]].include?(app_name)
          version = app[:version_string]
          break
        end
      end
      version
    end

    def get_usb_location(device)
      # TODO: use the --xml flag which returns "easily" parsable xml
      device_id = device.dup.gsub("-", "") # For devices with - in device_id
      BrowserStack::OSUtils.execute("/usr/local/bin/gtimeout -s KILL 10 system_profiler  SPUSBDataType | grep -A 3 '#{device_id}' | grep Location |  grep -o '0x[0-9]*'").strip.gsub("0x", "")
    end

    # this will return multiple interfaces when a devices are moved around on the hub
    def get_network_interfaces(usb_location)
      # TODO: figure out a better way to do this (else use plain ruby)
      BrowserStack::OSUtils.execute("/usr/libexec/PlistBuddy -c 'Print' /Library/Preferences/SystemConfiguration/NetworkInterfaces.plist  | grep -a  IOPathMatch | grep -E \"iPhone@#{usb_location}|iPad@#{usb_location}\" | grep -o 'en[0-9]*$'").split(/\n/).map(&:strip)
    end

    def interface_has_inet?(interface)
      !BrowserStack::OSUtils.execute("/usr/local/bin/gtimeout -s KILL 10 ifconfig #{interface} | grep inet | grep -v inet6").empty?
    end

    # won't be active if the phone is rebooting, off or disconnected
    def is_network_interface_available?(interface)
      !BrowserStack::OSUtils.execute("/usr/local/bin/gtimeout -s KILL 10 ifconfig #{interface}").empty?
    end

    # won't be in the bridge if network sharing is not enabled for this device
    def is_network_interface_in_bridge?(interface)
      !BrowserStack::OSUtils.execute("ifconfig bridge100 | grep member | grep #{interface}").empty?
    end

    def add_network_interface_to_bridge(interface)
      BrowserStack::OSUtils.execute("sudo ifconfig bridge100 addm #{interface}")
    end

    def delete_network_interface_from_bridge(interface)
      BrowserStack::OSUtils.execute("sudo ifconfig bridge100 deletem #{interface}")
    end

    def is_network_interface_in_nat_plist?(interface)
      !BrowserStack::OSUtils.execute("/usr/libexec/PlistBuddy -c 'Print NAT:SharingDevices' /Library/Preferences/SystemConfiguration/com.apple.nat.plist | grep #{interface}").empty?
    end

    def update_interface_state(interface, state)
      BrowserStack::OSUtils.execute("sudo ifconfig #{interface} #{state}", true)
    end

    # The last line is of the format: "     status: active"
    def is_interface_active?(interface)
      BrowserStack::OSUtils.execute("/usr/local/bin/gtimeout -s KILL 10 ifconfig #{interface} | tail -1").gsub("status:", "").strip == "active"
    end

    def main_interface_guid
      BrowserStack::OSUtils.execute("printf 'open\nget State:/Network/Global/IPv4\nd.show' | scutil | grep 'PrimaryService' | awk '{print $3}'")
    end

    def add_network_interface_to_nat_plist(interface)
      BrowserStack::OSUtils.execute("sudo /usr/libexec/PlistBuddy -c 'set NAT:Enabled 1'  -c 'set NAT:PrimaryService #{main_interface_guid.chop}' -c 'add NAT:SharingDevices: string #{interface}' /Library/Preferences/SystemConfiguration/com.apple.nat.plist")
    end

    def delete_bookmarks(device)
      result, return_status = BrowserStack::OSUtils.execute(
        "#{@idevicebookmark_bin_path} -u #{device} -d",
        true, # to return status
        timeout: COMMAND_TIMEOUT
      )
      raise IdeviceBookmarkError, "non-zero return: #{return_status}" unless return_status == 0
    rescue OSUtilsError => e
      BrowserStack.logger.error e.message.to_s
      raise IdeviceBookmarkError, "command timed out"
    end

    def delete_contacts(device)
      BrowserStack::OSUtils.execute("#{@idevicecontacts_bin_path} -u #{device} -d", true, timeout: 20)
    rescue OSUtilsError => e
      BrowserStack.logger.error "#{e.message} #{e.backtrace}"
      raise IdeviceError, "idevicecontacts -d timed out"
    end

    def load_contacts(device, contacts_data_path = @contacts_data_path, contacts_metadata_path = @contacts_metadata_path)
      BrowserStack::OSUtils.execute("#{@idevicecontacts_bin_path} -u #{device} -a '#{contacts_data_path},#{contacts_metadata_path}'", true, timeout: 20)
    rescue OSUtilsError => e
      BrowserStack.logger.error e.message.to_s
      raise IdeviceError, "idevicecontacts -a timed out"
    end

    def get_contacts_count(device)
      output, status = BrowserStack::OSUtils.execute("#{@idevicecontacts_bin_path} -u #{device} -l | plutil  -convert json -r -o - -- - | jq length", true, timeout: 20)
      [output.split("\n").map(&:to_i), status]
    rescue OSUtilsError => e
      BrowserStack.logger.error e.message.to_s
      raise IdeviceError, "idevicecontacts -l timed out"
    end

    def get_contacts_count_xml(device)
      output, status = BrowserStack::OSUtils.execute("#{@idevicecontacts_bin_path} -u #{device} -l ", true, timeout: 20)
      begin
        contacts_count = Nokogiri::XML(output).xpath('//plist/dict/key').count
      rescue => e
        contacts_count = 0
        BrowserStack.logger.error e.message.to_s
      end
      [contacts_count, status]
    rescue OSUtilsError => e
      BrowserStack.logger.error e.message.to_s
      raise IdeviceError, "get_contacts_count_xml failed"
    end

    def device_offline_on_usb(device)
      infoOutput = BrowserStack::OSUtils.execute("/usr/local/bin/gtimeout 30 #{@ideviceinfo} -u #{device} -k ProductType 2>&1")
      return true if $CHILD_STATUS.exitstatus != 0

      false
    end

    def device_language(device_id)
      command = "#{@ideviceinfo} -u #{device_id} --domain com.apple.international --key Language"
      BrowserStack::OSUtils.execute(command).strip
    end

    def get_crash_report_from_device(device_id, bundle_executables, session_id, create_zip = true)
      crash_reports_dir = Utils.get_crash_reports_dir_path(device_id)
      crash_report_file = "/tmp/crash_report_#{session_id}.zip"
      crash_reports = []
      error = ''
      FileUtils.mkdir_p(crash_reports_dir)
      start_time = Time.now.to_f
      begin
        bundle_executables.each do |bundle_executable|
          command = "#{@idevicecrashreport_bin_path} -u #{device_id} #{crash_reports_dir} -f #{Shellwords.escape(bundle_executable)}"
          output = BrowserStack::OSUtils.execute(command, timeout: 45)
          crash_reports.concat Dir["#{crash_reports_dir}/#{bundle_executable}*.{ips,ips.beta}"]
        end
        BrowserStack.logger.info "Crash reports for apps: #{bundle_executables} are #{crash_reports}"
        crash_reports.sort!

        create_zip_of_crash_report(crash_reports, session_id, device_id, crash_report_file) if create_zip
      rescue => e
        error = e.message
        BrowserStack.logger.error "Error while fetching crash reports for device: #{device_id}. Error: #{error}"
      end
      BrowserStack::Zombie.push_logs("idevicecrashreport-fetch-time-taken", Time.now.to_f - start_time, { "device" => device_id, "session_id" => session_id, "url" => error })
      raise "idevicecrashreport fetch failed" unless error.empty?

      crash_reports
    end

    def create_zip_of_crash_report(crash_reports, session_id, device_id, crash_zip_path)
      BrowserStack::OSUtils.execute("zip -r -j #{crash_zip_path} #{crash_reports.map! { |crash_report| Shellwords.escape(crash_report) }.join(' ')}", timeout: 10) unless crash_reports.empty?

      raise "Zip file not present for session #{session_id}" if !crash_reports.empty? && !File.exist?(crash_zip_path)
    end

    def clear_crash_reports_from_device(device_id, session_id = nil)
      crash_reports_dir = "/tmp/crash-reports_#{device_id}"
      FileUtils.mkdir_p(crash_reports_dir)
      start_time = Time.now.to_f
      error = ''
      replayd_jetsammed = false
      launcher_jetsammed = false
      begin
        # This command fetches the crash reports and clears them from device as well
        skip_files = ".log"
        command = "#{@idevicecrashreport_bin_path} -u #{device_id} #{crash_reports_dir} -s #{skip_files}"
        output = BrowserStack::OSUtils.execute(command, timeout: 45)
        BrowserStack.logger.info "Crash logs cleared for device: #{device_id}. Output: #{output}"
        replayd_jetsammed = true unless BrowserStack::OSUtils.execute("grep -C 10 '\"name\" : \"replayd\"' #{crash_reports_dir}/JetsamEvent* | grep '\"reason\"'", timeout: 5).strip.empty?
        launcher_jetsammed = true unless BrowserStack::OSUtils.execute("grep -C 10 '\"name\" : \"Launcher\"' #{crash_reports_dir}/JetsamEvent* | grep '\"reason\"'", timeout: 5).strip.empty?
      rescue => e
        error = e.message
        BrowserStack.logger.error "Error while clearing crash reports for device: #{device_id}. Error: #{error}"
      ensure
        BrowserStack::Zombie.push_logs("idevicecrashreport-clear-time-taken", Time.now.to_f - start_time, { "device" => device_id, "url" => error, "data" => { "replayd_jetsammed" => replayd_jetsammed, "launcher_jetsammed" => launcher_jetsammed }, "session_id" => session_id })
        FileUtils.rm_rf(crash_reports_dir)
      end
    end

    def start_syslog_capture(device_id, session_id = nil, log_name = nil)
      log_file = "/var/log/browserstack/#{log_name}_#{device_id}.log"
      start_banner = "\n\n#{'*' * 20} #{Time.now.utc} Starting idevicesyslog session_id: #{session_id} #{'*' * 20}\n\n"
      File.write(log_file, start_banner, mode: 'a')
      cmd = "#{@idevicesyslog_bin_path} -u #{device_id} | grep -vE 'replayd\|live_streaming\|locationd\|backboardd\|wifid' >> #{log_file} &"
      BrowserStack::OSUtils.execute(cmd)
    end

    def stop_syslog_capture(device_id, session_id = nil, log_name = nil)
      log_file = "/var/log/browserstack/#{log_name}_#{device_id}.log"
      stop_banner = "\n\n#{'*' * 20} #{Time.now.utc} Stopped idevicesyslog session_id: #{session_id} #{'*' * 20}\n\n"
      cmd = "ps -ef | grep #{device_id} | grep idevicesyslog | awk '{print $2}' | xargs -I{} kill {}"
      BrowserStack::OSUtils.execute(cmd)
      File.write(log_file, stop_banner, mode: 'a')
    end

    private

    def add_network_flag_to_binaries
      @idevice_id += " -n"
      @ideviceinfo += " -n"
      @idevicename += "  -n"
      @idevicedate += " -n"
      @idevicediagnostics += " -n"
      @idevicescreenshot += " -n"
      @ideviceimagemounter += " -n"
      @ideviceinstaller += " -n"
      @idevicedebug += " -n"
      @idevicesyslog_bin_path += " -n"
    end
  end
end

IdeviceUtils.configure(BrowserStack::Configuration)
