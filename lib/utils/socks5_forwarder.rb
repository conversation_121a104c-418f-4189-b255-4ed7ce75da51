require 'socket'
require 'net/http'
require 'uri'
require 'json'
require_relative './zombie'
require_relative '../../config/constants'
require_relative '../../server/device_manager'
require_relative '../privoxy_manager'
require_relative '../configuration'
require_relative './utils'

module Privoxy
  class Socks5Forwarder
    # This class is used to establish an adhoc local session to redirect traffic and unblock url that we block at DNS level on platform.
    # We have defined an endpoint in Proxy Controller of App-Live which would let us communicate with the repeater
    # and start a local session by connecting to the repeater and port it returned.
    # We just need to call https://{app-live-url}/proxy/adhoc_repeater_session_start to start a local session.
    # It returns repeater and port which we add in our privoxy config to redirect traffic to
    # To kill the same local session, call https://{app-live-url}/proxy/adhoc_repeater_session_stop with repeater and port
    # Refer - https://browserstack.atlassian.net/browse/DF-658
    def initialize(device)
      @device = device
      @config = BrowserStack::Configuration.new.all
      @url = Utils.generate_app_live_url
    end

    def forward_traffic(url_patterns = [], params = {})
      custom_forwarding_options = { domains: url_patterns }
      current_device_config = DeviceManager.device_configuration_check(@device)

      if !params[:hosts].nil? && !params[:hosts].empty?
        PrivoxyManager.setup_privoxy(@device, current_device_config, params, custom_forwarding_options)
      else
        @host, @port = adhoc_repeater_session_start(@device, params)
        if @host && @port
          custom_forwarding_options[:host] = @host
          custom_forwarding_options[:port] = @port
          PrivoxyManager.setup_privoxy(@device, current_device_config, {}, custom_forwarding_options)
        end
      end
    end

    def reset_privoxy(host = nil, port = nil)
      adhoc_repeater_session_stop(@device, @host || host, @port || port)
      current_device_config = DeviceManager.device_configuration_check(@device)
      PrivoxyManager.reset_proxy(@device, current_device_config)
    end

    private

    def adhoc_repeater_session_start(device_id, params = {}) # rubocop:todo Metrics/AbcSize
      ip = Socket.getaddrinfo(Socket.gethostname, "echo", Socket::AF_INET)[0][3]
      form_data = { ip: ip }
      uri = URI.parse("#{@url}/proxy/adhoc_repeater_session_start")
      request = Net::HTTP::Post.new(uri)
      request.basic_auth(@config['adhoc_repeater_session_user'], @config['adhoc_repeater_session_password'])
      request.set_form form_data

      req_options = {
        use_ssl: uri.scheme == "https",
        read_timeout: 20
      }
      BrowserStack.logger.info("[Socks5Forwarder] Sending adhoc repeater start request to #{uri.hostname}")
      response = Net::HTTP.start(uri.hostname, uri.port, req_options) do |http|
        http.request(request) do |res|
          response_code = res.code.to_i
          raise "Failed to Start Adhoc Repeater Session - Response code: #{response_code}" unless response_code == 200
        end
      end

      response_body = JSON.parse(response.body)
      if response_body["browserstack_message"].eql?("success")
        BrowserStack.logger.info("[Socks5Forwarder] Received #{response_body['host']}:#{response_body['port']}")
        BrowserStack::Zombie.push_logs("adhoc-repeater-start-successful", "", { "os" => "ios_njb", "terminal_type" => "realMobile", "device" => device_id, "data" => { "host" => uri.hostname, "form_data" => form_data } }, nil, params)
        [response_body["host"], response_body["port"]]
      else
        BrowserStack.logger.info("[Socks5Forwarder] Received no host or port")
        raise "Received No Host or Port"
      end
    rescue => e
      BrowserStack.logger.error("[Socks5Forwarder] Failed to start Adhoc Repeater Session. Exception Message: #{e.message}")
      BrowserStack::Zombie.push_logs("adhoc-repeater-start-failed", e.message, { "os" => "ios_njb", "terminal_type" => "realMobile", "device" => device_id, "data" => { "host" => uri.hostname, "form_data" => form_data } }, nil, params)
      raise e
    end

    def adhoc_repeater_session_stop(device_id, host, port)
      ip = Socket.getaddrinfo(Socket.gethostname, "echo", Socket::AF_INET)[0][3]
      form_data = { host: host, ip: ip, port: port }
      uri = URI.parse("#{@url}/proxy/adhoc_repeater_session_stop")
      request = Net::HTTP::Post.new(uri)
      request.basic_auth(@config['adhoc_repeater_session_user'], @config['adhoc_repeater_session_password'])
      request.set_form form_data

      req_options = {
        use_ssl: uri.scheme == "https",
        read_timeout: 20
      }
      BrowserStack.logger.info("[Socks5Forwarder] Sending adhoc repeater stop request to #{uri.hostname} with #{form_data}")
      response = Net::HTTP.start(uri.hostname, uri.port, req_options) do |http|
        http.request(request) do |res|
          response_code = res.code.to_i
          raise "Failed to Stop Adhoc Repeater Session - Response code: #{response_code}" unless response_code == 200
        end
      end

      response_body = JSON.parse(response.body)
      response_body["info"] if response_body["browserstack_message"].eql?("success")
      BrowserStack.logger.info("[Socks5Forwarder] Stopped Successfully")
      BrowserStack::Zombie.push_logs("adhoc-repeater-stop-successful", "", { "os" => "ios_njb", "terminal_type" => "realMobile", "device" => device_id, "data" => { "host" => uri.hostname, "form_data" => form_data } })
    rescue => e
      BrowserStack.logger.error("[Socks5Forwarder]Failed to stop Adhoc Repeater Session. Exception Message: #{e.message}")
      BrowserStack::Zombie.push_logs("adhoc-repeater-stop-failed", e.message, { "os" => "ios_njb", "terminal_type" => "realMobile", "device" => device_id, "data" => { "host" => uri.hostname, "form_data" => form_data } })
    end
  end
end

if __FILE__ == $PROGRAM_NAME

  device_id = ARGV[0]
  socks5_forwarder = Privoxy::Socks5Forwarder.new(device_id)
  case ARGV[1]
  when 'allow_domains'
    socks5_forwarder.forward_traffic(["updates-http.cdn-apple.com"])
  when 'restrict_domains'
    socks5_forwarder.reset_privoxy(ARGV[2], ARGV[3])
  else
    BrowserStack.logger.error "Method #{ARGV[1]}, is not listed."
  end
end

