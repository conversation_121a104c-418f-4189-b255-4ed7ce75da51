#!/usr/bin/env ruby
require 'yaml'
require 'bsdwh'
require_relative './zombie'

def zombie_push(platform, kind, msg, browser, data, device = nil, session = nil, user = nil)
  BrowserStack::Zombie.configure({
    os: platform,
    browser: browser
  })

  json = {
    "data" => data
  }

  json["device"] = device unless device.nil?
  json["session_id"] = session unless session.nil?
  json["user_id"] = user unless user.nil?

  BrowserStack::Zombie.push_logs(kind, msg, json)
end

if __FILE__ == $PROGRAM_NAME
  if ARGV.length < 3
    puts "Atleast platform type (android/ios), kind and message is needed."
    exit 1
  end

  # Mock logger - output to stdout
  require 'browserstack_logger'
  BrowserStack.init_logger('/var/log/browserstack/nix_zombie.log')

  platform = ARGV[0]
  kind = ARGV[1]
  msg = ARGV[2]
  browser = ARGV[3] || platform
  data = ARGV[4]
  device = ARGV[5] || nil
  session = ARGV[6] || nil

  zombie_push(platform, kind, msg, browser, data, device, session)
end
