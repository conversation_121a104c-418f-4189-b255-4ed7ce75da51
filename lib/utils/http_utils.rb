require 'English'
require_relative '../custom_exceptions'
require_relative './osutils'
require_relative './zombie'
require_relative '../configuration'
require 'net/http'
require 'json'
require 'uri'
require 'faraday'
require 'shellwords'

module BrowserStack
  class HttpUtils # rubocop:todo Metrics/ClassLength
    class << self
      def test_url_code(url, basic_auth = nil, proxy = {})
        BrowserStack.logger.info("Checking connectivity to #{url}")
        url = URI.parse(url)
        response = get_response(url, basic_auth, proxy)

        BrowserStack.logger.info "Response from #{url}: #{response.inspect}"
        response && response.code.to_i
      end

      def is_valid_url?(url)
        (url =~ URI::DEFAULT_PARSER.make_regexp) == 0
      end

      def send_post_with_redirect(url, data_hash, basic_auth=nil, json=nil, options={}, limit = 2)
        raise HTTPException, 'HTTP redirect too deep' if limit == 0

        response = send_post(url, data_hash, basic_auth, json, options)

        if response.is_a? Net::HTTPRedirection
          send_post_with_redirect(response['location'], data_hash, basic_auth, json, options, limit - 1)
        else
          response
        end
      end

      def send_post(url, data_hash, basic_auth=nil, json=nil, options={}) # rubocop:todo Metrics/AbcSize
        retry_count = options[:retry_count] || 0
        retry_interval = options[:retry_interval] || 0
        read_timeout_in_sec = options[:read_timeout] || 60
        url = URI(url)
        http = Net::HTTP.new(url.host, url.port)
        http.use_ssl = true if url.scheme == 'https'
        http.read_timeout = read_timeout_in_sec
        request = Net::HTTP::Post.new(url)
        (request.basic_auth basic_auth[:username], basic_auth[:password]) if basic_auth
        request["content-type"] = 'application/json' if json
        request["x-chitragupta-log-id"] = data_hash[:data][:app_uploader_identifier] if data_hash && data_hash[:data] && data_hash[:data][:app_uploader_identifier].to_s != ""
        request.body = data_hash.to_json if data_hash
        response = http.request request
        if response.code.to_i != 200 && retry_count > 0
          BrowserStack.logger.info("HTTP Post non 200 response: #{response.code}, retry_count: #{retry_count}. Retrying after #{retry_interval} seconds")
          options[:retry_count] -= 1
          sleep retry_interval
          send_post(url, data_hash, basic_auth, json, options)
        else
          response
        end
      rescue OpenSSL::SSL::SSLError => e
        BrowserStack.logger.error("SSL certificate verification failed: #{e}")
        BrowserStack::Zombie.push_logs("ssl_verification_failed", e, { "os" => "ios_njb", "terminal_type" => "realMobile", "method" => "http_utils:send_post" })
        raise e
      rescue => e
        raise e if retry_count <= 0

        BrowserStack.logger.info("Exception Occurred, URL: #{url}, Data: #{data_hash}, options: #{options},  #{e.inspect}, retry_count: #{retry_count}. " \
        "Retrying after #{retry_interval} seconds")
        options[:retry_count] -= 1
        sleep retry_interval
        send_post(url, data_hash, basic_auth, json, options)
      end

      def auth_string(basic_auth)
        if basic_auth
          raise "Bad basic_auth hash" if basic_auth[:username].nil? || basic_auth[:password].nil?

          "#{basic_auth[:username]}:#{basic_auth[:password]}"
        end
      end

      def faraday_request(method, url, timeout=5, data_hash=nil, auth=nil)
        conn = Faraday::Connection.new(url)
        if auth
          auth_string(auth)
          conn.basic_auth(auth[:username], auth[:password])
        end

        begin
          case method
          when 'GET'
            response = Faraday.get(url) { |req| req.options.timeout = timeout }
          when 'HEAD'
            response = Faraday.head(url) { |req| req.options.timeout = timeout }
          when 'POST'
            response = conn.post do |req|
              req.body = data_hash
              req.options.timeout = timeout
            end
          when 'PATCH'
            response = conn.patch do |req|
              req.body = data_hash
              req.options.timeout = timeout
            end
          end
        rescue Faraday::ConnectionFailed
          # Faraday doesn't have a specific 'timed_out?' variable
          response = Faraday::Response.new({ status: 0 })
        rescue Faraday::TimeoutError
          response = Faraday::Response.new({ status: 0, reason_phrase: 'Timed Out' })
        end
        response
      end

      def make_get_request(url, request_timeout)
        # More detailed info here - https://gist.github.com/raj454raj/5c80cdf0f0ebcd99436aac2f412faaa9
        # Use this over BrowserStack::HttpUtils.get_response
        faraday_request('GET', url, request_timeout)
      end

      def make_get_request_with_headers(url, headers)
        conn = Faraday::Connection.new(url)
        Faraday.get(url, nil, headers)
      end

      def make_post_request_with_headers(url, headers, data)
        conn = Faraday.new do |faraday|
          faraday.headers = headers
        end
        conn.post(url, data.to_json)
      end

      def make_head_request(url, request_timeout)
        faraday_request('HEAD', url, request_timeout)
      end

      def send_post_raw(url_string, data_hash, basic_auth=nil, timeout=5)
        faraday_request('POST', url_string, timeout, data_hash, basic_auth)
      end

      def make_patch_request(url_string, data_hash, basic_auth=nil, timeout=5)
        faraday_request('PATCH', url_string, timeout, data_hash, basic_auth)
      end

      def get_response(url, basic_auth = nil, proxy = {}, request_timeout = 3)
        # ######################################################################
        # DON'T use this for making a plain GET request with a specified timeout
        # `http.request request` internally retries on a timeout so you will end up
        # getting two requests on the server, use BrowserStack::HttpUtils.make_get_request instead
        # ######################################################################
        url = URI(url)
        Net::HTTP.start(url.host, url.port, proxy[:host], proxy[:port], use_ssl: url.scheme == 'https') do |http|
          request = Net::HTTP::Get.new url
          http.read_timeout = request_timeout # In seconds
          (request.basic_auth basic_auth[:username], basic_auth[:password]) if basic_auth
          http.request request
        end
      end

      # Net::HTTP loads the file into memory while downloadin, even in chunks
      # Curl directly writes into the file. Avoiding ruby bindings for now so
      # that deploy becomes easy with a code update
      def download(url, filename, options = {}, filetype="app") # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
        timeout = options[:timeout] || 30
        resolved_address = options[:resolved_address]
        retry_count = options[:retry_count] || 0
        kill_in_cleanup = options[:kill_in_cleanup] || false
        device_id = options[:device_id] || ''
        escaped_filename = Shellwords.escape(filename)
        cmd = "curl --max-time #{timeout} -s -S -w %{http_code} \"#{url}\" -o #{escaped_filename}"
        cmd += " --resolve #{resolved_address}" unless resolved_address.nil?

        if options[:is_app_automate]
          # Get header args and replace cmd if app-automate
          header_options = get_download_header_args_from_options(options)
          cmd += header_options
        end

        http_code = nil
        exit_code = nil
        term_code = nil
        pid_file = "/tmp/app_download_pid_#{device_id}"
        output_file = "/tmp/app_download_output_#{device_id}"
        error_file = "/tmp/app_download_error_#{device_id}"

        if kill_in_cleanup
          pid = Process.spawn(cmd, out: output_file, err: error_file)
          Utils.write_to_file(pid_file, pid.to_s)
          http_code, exit_code, term_code = execute_with_cleanup(pid, timeout, output_file, error_file)
          File.delete("/tmp/app_download_pid_#{device_id}")
        else
          http_code = OSUtils.execute(cmd)
          exit_code = $CHILD_STATUS.exitstatus
        end
        if exit_code != 0 && retry_count != 0 && term_code.nil?
          BrowserStack.logger.info("#{filetype} download failed. Retrying. exit_code: #{exit_code}, http_code: #{http_code}, retry_count: #{retry_count}")
          options[:retry_count] -= 1
          download(url, filename, options)
        else
          if !term_code.nil?
            raise HTTPException, "Download process terminated in cleanup"
          elsif http_code.to_s != "200" && http_code.to_s != "226"
            error = File.exist?(filename) ? BrowserStack::OSUtils.safe_execute("head -5", [escaped_filename]) : 'File not downloaded'
            BrowserStack.logger.info "Error (first 5 lines): #{error}. Status: #{http_code}"
            raise HTTPException, "Non 200 response: #{http_code}"
          end

          BrowserStack.logger.info("[ipa-download-failure-debug] Curl operation result, http_code: #{http_code}, exit_code: #{exit_code}")
          BrowserStack.logger.info("[ipa-download-failure-debug] filename: #{filename}, escaped_filename: #{escaped_filename}, File.exists?(escaped_filename): #{File.exists?(escaped_filename)}")
          raise HTTPException, "File not downloaded" unless File.exists?(filename)
          raise HTTPException, "Non zero exit code #{exit_code}" unless exit_code == 0
        end
      end

      def execute_with_cleanup(pid, timeout, output_file, error_file)
        http_code = nil
        exit_code = nil
        term_code = nil
        begin
          Timeout.timeout(timeout.to_i + 2) do
            pid, status = Process.wait2(pid)
            exit_code = status.exitstatus
            term_code = status.termsig
          end
        rescue Timeout::Error
          BrowserStack.logger.error("[DOWNLOAD] Process timed out for pid #{pid}")
          begin
            Process.kill("TERM", pid)
          rescue => e
            BrowserStack.logger.error("Error while killing the download process #{e.message}")
          end

          exit_code = -1
        ensure
          # Read HTTP status code from output file
          if File.exist?(output_file)
            http_code = File.read(output_file).strip
            File.delete(output_file)
          end
          File.delete(error_file) if File.exist?(error_file)
        end
        [http_code, exit_code, term_code]
      end

      def wget_download(url, filename, options = {}, filetype="app")
        timeout = options[:timeout] || 30
        retry_count = options[:retry_count] || 0
        http_code = OSUtils.execute("gtimeout --foreground #{timeout} wget --quiet \"#{url}\" -O #{filename}")
        exit_code = $CHILD_STATUS.exitstatus
        if exit_code != 0 && retry_count != 0
          BrowserStack.logger.info("#{filetype} download failed. Retrying. exit_code: #{exit_code}, retry_count: #{retry_count}")
          options[:retry_count] -= 1
          wget_download(url, filename, options)
        else
          raise HTTPException, "File not downloaded" unless File.exists?(filename)
          raise HTTPException, "Non zero exit code #{exit_code}" unless exit_code == 0
        end
      end

      def download_from_s3_with_presigned_url(presigned_url, local_file_path)

        begin
          FileUtils.mkdir_p(File.dirname(local_file_path))
        rescue
          nil
        end

        uri = URI.parse(presigned_url)

        http = Net::HTTP.new(uri.host, uri.port)
        http.use_ssl = (uri.scheme == 'https')
        http.read_timeout = 60

        request = Net::HTTP::Get.new(uri.request_uri)

        response = http.request(request)

        if response.code.to_i == 200
          File.open(local_file_path, 'wb') do |file|
            file.write(response.body)
          end

          BrowserStack.logger.info("Successfully downloaded file to #{local_file_path}")
          true
        else
          BrowserStack.logger.error("Download failed with response code 2: #{response.code}")
          BrowserStack.logger.error("Response body: #{response.body[0..500]}")
          false
        end
      rescue => e
        BrowserStack.logger.error("Error downloading file: #{e.message}")
        BrowserStack.logger.error(e.backtrace.join("\n"))
        false

      end

      # rubocop:disable Metrics/AbcSize
      def upload_to_s3_with_presigned_url(presigned_url, local_file_path)

        unless File.exist?(local_file_path)
          BrowserStack.logger.error("File does not exist: #{local_file_path}")
          return false
        end

        file_content = File.read(local_file_path)

        uri = URI.parse(presigned_url)

        http = Net::HTTP.new(uri.host, uri.port)
        http.use_ssl = (uri.scheme == 'https')
        http.read_timeout = 60

        request = Net::HTTP::Put.new(uri.request_uri)
        request.body = file_content

        content_type = case File.extname(local_file_path).downcase
                       when '.json'
                         'application/json'
                       when '.txt'
                         'text/plain'
                       when '.png'
                         'image/png'
                       when '.jpg', '.jpeg'
                         'image/jpeg'
                       else
                         'application/octet-stream'
                       end

        request['Content-Type'] = content_type
        request['Content-Length'] = file_content.bytesize.to_s

        response = http.request(request)

        if response.code.to_i == 200
          BrowserStack.logger.info("Successfully uploaded file from #{local_file_path} to S3")
          true
        else
          BrowserStack.logger.error("Upload failed with response code: #{response.code}")
          BrowserStack.logger.error("Response body: #{response.body[0..500]}")
          false
        end
      rescue => e
        BrowserStack.logger.error("Error uploading file: #{e.message}")
        BrowserStack.logger.error(e.backtrace.join("\n"))
        false
      end
      # rubocop:enable Metrics/AbcSize

      private

      def get_download_header_args_from_options(options)
        header_options = ""
        header = options[:header]
        dump_headers_file = options[:dump_headers_to_file]
        header_options += " -D #{dump_headers_file}" unless dump_headers_file.nil? || dump_headers_file.empty?
        header_options += " --header '#{header}'" unless header.nil? || header.empty?
        header_options
      end
    end
  end
end
