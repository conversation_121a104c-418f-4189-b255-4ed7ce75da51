#!/bin/bash

PBUDDY_BIN="/usr/libexec/PlistBuddy"
PLIST="/Library/Preferences/SystemConfiguration/com.apple.nat.plist"
PLIST_BACKUP="/tmp/$date-com.apple.nat.plist.bkp"
PLIST_TEMPLATE="/tmp/com.apple.nat.plist.template"
STATE_DIR="/usr/local/.browserstack/state_files"

# main interface guid, got this from http://hints.macworld.com/article.php?story=20050214200529336
SERVICE_GUID=`printf "open\nget State:/Network/Global/IPv4\nd.show" | \
scutil | grep "PrimaryService" | awk '{print $3}'`

#delete internet sharing enabled
PBUDDY_ARGS="-c 'delete NAT:Enabled' "

#enable internet sharing
PBUDDY_ARGS="$PBUDDY_ARGS -c 'add NAT:Enabled integer 1' "

#delete primary service
PBUDDY_ARGS="$PBUDDY_ARGS -c 'delete NAT:PrimaryService' "

#assign main interface guid
PBUDDY_ARGS="$PBUDDY_ARGS -c 'add NAT:PrimaryService string $SERVICE_GUID' "

#delete current shared devices
PBUDDY_ARGS="$PBUDDY_ARGS -c 'delete NAT:SharingDevices' "
PBUDDY_ARGS="$PBUDDY_ARGS -c 'add NAT:SharingDevices array' "

# all iOS versions above this do not interface without inet4 to be added to NAT
# if they are added, they no longer show in xcode, see MOBPL-2428
MIN_VERSION_FOR_NAT_EXCLUSION="16.0"

# copied from mobile
vers_gte() {
  [  "$1" = "`echo -e "$1\n$2" | sort -V | tail -n1`" ]
}

for device in `idevice_id -l`
do
    # Need to gsub out "-" from device when searching for location_id: ${device//-}
    location_id=$(system_profiler SPUSBDataType | grep -A 3 ${device//-} | grep Location | sed  's/.*0x\([0-9]*\).*/\1/')
    echo "found $device at usb location $location_id"

        #interface assigned (can be more than one in case of swapping, moving arround devices)
    interfaces=$(/usr/libexec/PlistBuddy -c 'Print' /Library/Preferences/SystemConfiguration/NetworkInterfaces.plist  | grep -a  "IOPathMatch" |  grep -E "iPhone@$location_id|iPad@$location_id" | grep -o 'en.*$')

    if [ -z "$interfaces" ]; then
	echo "can't find network interface for $device, exiting"
	exit 1
    fi

    while read -r interface; do
	if ifconfig $interface >> /dev/null; then
        # Although it echoes 'en3 active', it maybe possible that the interface is down
        # We try to bring the network interface up in device_check else reboot would bring it up.
	    echo "$interface may be active"
      os_version=$(ideviceinfo -u ${device} -k ProductVersion)
      if vers_gte "${os_version}" "${MIN_VERSION_FOR_NAT_EXCLUSION}"; then
        # check if the interface has inet4
        echo "Checking interface ${interface} for inet4 as os version is ${os_version}"
        inet_output=$(ifconfig $interface | grep inet | grep -v inet6)
        if [[ -z "$inet_output" ]]; then
          echo "Interface ${interface} does not have inet4, skipping it and removing from bridge100"
          sudo ifconfig bridge100 deletem ${interface}
          continue
        fi
      else
        echo "Not checking interface ${interface} for inet4 as os version is ${os_version}"
      fi
      echo "Adding interface ${interface} to NAT"
	    PBUDDY_ARGS="$PBUDDY_ARGS -c 'add NAT:SharingDevices: string $interface' "
      if vers_gte "${os_version}" "${MIN_VERSION_FOR_NAT_EXCLUSION}"; then
        ifconfig bridge100 addm $interface
      fi
	fi
    done <<< "$interfaces"

    touch "$STATE_DIR/nat_setup_complete_$device"
done

if [ -f $PLIST ]; then
    echo "backing up $PLIST to $PLIST_BACKUP"
    cp "$PLIST" "$PLIST_BACKUP"
elif [ -f $PLIST_TEMPLATE ]; then
    echo "copying $PLIST_TEMPLATE to $PLIST"
    cp "$PLIST_TEMPLATE" "$PLIST"
    chown root "$PLIST"
    chgrp admin "$PLIST"
    chmod 0644 "$PLIST"
else
    echo "ERROR $PLIST does not exists and we don't have a template at $PLIST_TEMPLATE exiting"
    exit 1
fi

echo "executing $PBUDDY_BIN $PBUDDY_ARGS $PLIST"
eval $PBUDDY_BIN "$PBUDDY_ARGS" "$PLIST"
