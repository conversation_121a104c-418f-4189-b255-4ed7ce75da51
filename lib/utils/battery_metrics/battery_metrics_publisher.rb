require 'browserstack_logger'
require 'json'
require 'browserstack_utils'
require_relative '../../checks/check_plist'
require_relative '../../../config/constants'
require_relative '../zombie'

class BatteryMetricsPublisher
  attr_reader :device, :session, :plist

  def initialize(device, session)
    @device = device
    @session = session
    @plist_name = "battery_metrics_#{device}"
    @user = "app"
    @service_type = ServiceType.UserService
    @plist = BrowserStack::CheckPlist.new(@plist_name,
                                          @service_type,
                                          ["/bin/bash", "-l", "-c", "bundle exec ruby lib/utils/battery_metrics/battery_metrics_collector.rb #{device}"],
                                          "#{LOGGING_DIR}/battery_metrics_#{device}.log",
                                          "#{LOGGING_DIR}/battery_metrics_err_#{device}.log",
                                          @user, nil, { WorkingDirectory: BSTACK_REALMOBILE_BASE, StartInterval: 10 }, true, nil, false, false, 'generic_plist.erb', nil, false)
    @accumulated_metrics_file = File.join(BATTERY_METRICS_DIR, "metrics_#{device}.txt")
    BrowserStack::Zombie.configure
  end

  def start
    plist.update
  rescue => e
    BrowserStack.logger.error("Error in battery metrics plist start: #{e.message} #{e.backtrace}")
    BrowserStack::Zombie.push_logs("battery_metrics_plist_launch_failed", e.message, { "device" => device, "session_id" => session })
  end

  def stop_and_push_metrics
    stop_service
    parse_metrics
    generate_inference
    push_metrics
  rescue => e
    BrowserStack.logger.error("An error occured while stopping plist/generating metrics: #{e.message} #{e.backtrace}")
    BrowserStack::Zombie.push_logs("battery_metrics_plist_stop_failed", e.message, { "device" => device, "session_id" => session })
  ensure
    clean_metrics_file
  end

  def clean_metrics_file
    File.truncate(@accumulated_metrics_file, 0)
  rescue => e
    BrowserStack.logger.error("Error cleaning metrics file: #{e.message}")
  end

  private

  def stop_service
    BrowserStack::CheckPlist.unload_service(@plist_name, @service_type, @user)
  end

  def parse_metrics
    @data = BATTERY_METRICS_TO_INSTRUMENT.keys.map { |key| [key, []] }.to_h

    File.foreach(@accumulated_metrics_file) do |line|
      parsed_line = JSON.parse(line, symbolize_names: true)
      BATTERY_METRICS_TO_INSTRUMENT.each_key do |key|
        @data[key] << parsed_line[key].to_f
      end
    end

  end

  def generate_inference
    @inference = {}
    BATTERY_METRICS_TO_INSTRUMENT.each_key do |key|
      case key
      when :battery_temperature, :voltage
        @inference[key] = BrowserStackUtils::StatisticsGenerator.new(@data[key]).generate_all_stats

      when :max_capacity
        @inference[key] = @data[key].uniq
      end
    end
    BrowserStack.logger.info("Inference: #{@inference}")
  end

  def push_metrics
    BrowserStack::Zombie.push_logs("battery_metrics", "", { "device" => device, "session_id" => session, "data" => @inference })
  end

end
