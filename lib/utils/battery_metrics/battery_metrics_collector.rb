require 'tempfile'
require 'browserstack_logger'
require 'fileutils'
require_relative '../osutils'
require_relative '../plist_buddy'
require_relative '../../../config/constants'

class BatteryMetricsCollector
  attr_reader :device

  def initialize(device)
    @device = device
    @accumulated_metrics_file = File.join(BATTERY_METRICS_DIR, "metrics_#{device}.txt")
    FileUtils.mkdir_p(BATTERY_METRICS_DIR) unless File.directory?(BATTERY_METRICS_DIR) # Ensure BATTERY_METRICS_DIR is created
  end

  def run
    temp_metrics_file = fetch_battery_metrics
    current_metrics = {}

    BATTERY_METRICS_TO_INSTRUMENT.each do |key, metric_data|
      value = BrowserStack::PlistBuddy.get_value_of_key(temp_metrics_file.path, metric_data[:plist_key]).chomp
      current_metrics[key] = value.to_f * metric_data[:multiplier]
    end
    BrowserStack.logger.info("Metrics: #{current_metrics}")
    append_current_metrics(current_metrics.to_json)
  rescue => e
    BrowserStack.logger.error("Error in BatteryMetricsCollector: #{e.message} #{e.backtrace}")
  ensure
    temp_metrics_file.unlink
  end

  private

  def append_current_metrics(metrics)
    File.open(@accumulated_metrics_file, 'a') do |file|
      file.puts metrics
    end
  end

  def fetch_battery_metrics
    temp_file = Tempfile.new("battery_metrics_#{device}")
    all_metrics = BrowserStack::OSUtils.execute("#{IDEVICEDIAGNOSTICS} -u #{device} ioregentry AppleSmartBattery", log_command: false)
    temp_file.write(all_metrics)
    temp_file
  rescue => e
    BrowserStack.logger.error("Error in fetch_battery_metrics: #{e.message} #{e.backtrace}")
    temp_file
  end

end

if __FILE__ == $PROGRAM_NAME
  device_id = ARGV[0]
  collector = BatteryMetricsCollector.new(device_id)
  collector.run
end
