require "json"
require "uri"
require "net/http"
require "socket"

module BlockMachine
  def self.machine_can_be_blocked?(mobile_dashboard_auth, machine_ip, force_machine_block_file)
    if File.exists?(force_machine_block_file)
      BrowserStack.logger.info "Found #{force_machine_block_file} file...blocking the machine immediately!"
      return true
    end
    BrowserStack.logger.info "Checking if #{machine_ip} can be blocked"
    request_url = "/admin/mobile_phones.json?terminal_ip=#{machine_ip}"
    devices_json = send_request(mobile_dashboard_auth, request_url)

    can_be_blocked = true
    devices_json.each do |_k, v|
      v.each do |device_json|
        can_be_blocked = false if ["blocked"].include?(device_json["state"]) && !device_json["user_id"].nil?
      end
    end
    can_be_blocked
  end

  def self.block_machine(mobile_dashboard_auth, machine)
    url = "/admin/block_mobile_ip?ip=#{machine}&reason=deploy"
    res = send_request(mobile_dashboard_auth, url)
    return false unless res

    res["pending"].empty?

  end

  def self.block_machine_in_loop(mobile_dashboard_auth, machine_ip)
    BrowserStack.logger.info "Trying to block #{machine_ip} in #{mobile_dashboard_auth['url']}"
    until block_machine(mobile_dashboard_auth, machine_ip)
      BrowserStack.logger.info "#{machine_ip} Blocking not successful, attempting again."
      sleep 2
    end
  end

  def self.unblock_machine(mobile_dashboard_auth, machine)
    url = "/admin/unblock_mobile_ip?ip=#{machine}&reason=deploy"
    res = send_request(mobile_dashboard_auth, url)
  end

  def self.send_request(env, url)
    request_url = "#{env['url']}#{url}"
    request = Net::HTTP::Get.new(request_url)
    request.basic_auth env["username"], env["password"]
    uri = URI.parse(request_url)
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true
    response = http.request(request)

    if response.code != "200"
      BrowserStack.logger.info response.body.to_s
      raise "Bad response from #{request_url}: Code #{response.code}"
    end
    begin
      JSON.parse(response.body)
    rescue
      nil
    end
  end
end
