require 'fileutils'
require_relative './osutils'
require_relative '../../config/constants'
require_relative '../configuration'
require_relative '../checks/check_plist'
require_relative '../../server/device_manager'
require_relative '../models/device_state'
require 'net/http'
require 'uri'
require 'json'
require 'securerandom'

module BrowserStack
  # rubocop:disable Metrics/ModuleLength
  module DeviceLogger
    @server_config = Configuration.new.all
    @static_conf = @server_config['static_conf']
    @nodepath = DEVICE_LOGGER_NODE
    @isSessionSpecificDL = @static_conf['session_specific_device_logger'] == "true"
    @logging_root = @server_config['logging_root']
    @dlUuid = SecureRandom.uuid
    BrowserStack.logger.info "DL UUID :: #{@dlUuid}"

    def self.initialize(device, session_id = "")
      Thread.bs_run do
        start_time = Time.now
        BrowserStack.logger.info "Initialized Called ..."
        BrowserStack.logger.info "DL UUID in Initialize :: #{@dlUuid}"
        current_device = DeviceManager.device_configuration_check(device)
        @port = current_device["device_logger_port"]
        @isSessionSpecificDL = false if @port.nil?
        @device_state = DeviceState.new(device)
        is_dl_running = @device_state.device_logger_file_present?
        BrowserStack.logger.info "Is DL Running : #{is_dl_running}"
        if @isSessionSpecificDL
          if is_dl_running
            BrowserStack.logger.info "Device Logger For #{device} is already initialized. Skipping initialization."
          else
            BrowserStack.logger.info "Initializing Device Logger For device #{device} on port #{@port}."
            start_plist(device, @port)
            @device_state.touch_device_logger_file
            BrowserStack.logger.info "Initialized Device Logger For device #{device}."
          end
        else
          BrowserStack.logger.info "No need to initialize for old device logger."
        end
        end_time = Time.now
        execution_time = (end_time - start_time).round
        BrowserStack::Zombie.push_logs("device-logger-init-time", "", { "device" => device, "session_id" => session_id, "data" => execution_time })
      rescue => e
        BrowserStack.logger.error "Error in DL initialize: #{e.message} #{e.backtrace}"
      end
    end

    def self.start(device)
      BrowserStack.logger.info "Starting Device Logger For session on #{device} ..."
      BrowserStack.logger.info "DL UUID in Start :: #{@dlUuid}"
      if @isSessionSpecificDL
        BrowserStack.logger.info "Invoking Session Specific DL For Start ..."
        async_retry(device, 'start')
      else
        BrowserStack.logger.info "Invoking Old DL For Start ..."
        FileUtils.touch(device_logger_file(device))
      end
    end

    def self.start_app_logs(device, params)
      BrowserStack.logger.info "DL UUID in Start App Logs :: #{@dlUuid}"

      app_session = app_session_file(device)
      app_logs_file = app_device_logs_file(device)
      bundle_ids = [params['app_testing_bundle_id'], params['test_suite_bundle_id']] + params['other_app_bundle_ids'].to_a + params['mid_session_app_bundle_ids'].to_a
      Utils.write_to_file(app_session, bundle_ids.join("\n"))
      BrowserStack.logger.info("Starting app logs of device #{device} for #{bundle_ids.join('\n')} request params : #{params}")

      FileUtils.touch(app_logs_file)
      sleep 1

      if @isSessionSpecificDL
        BrowserStack.logger.info "Invoking Session Specific DL For Start App Logs ..."
        async_retry(device, 'update')
      else
        BrowserStack.logger.info "Invoking Old DL For Start App Logs ..."
        FileUtils.touch(device_logger_file(device))
      end
    end

    def self.update(device)
      BrowserStack.logger.info "Updating Device Logger For session on #{device} ..."
      BrowserStack.logger.info "DL UUID in Update :: #{@dlUuid}"

      if @isSessionSpecificDL
        async_retry(device, 'update')
      else
        BrowserStack.logger.info "Invoking Old DL For Update ..."
        # device-logger cannot detect file_change event if file touches are immediate.
        # introducing an explicit sleep so that file_change is triggered.
        sleep 0.5
        FileUtils.touch(device_logger_file(device))
      end
    end

    def self.destroy(device)
      if @isSessionSpecificDL
        BrowserStack.logger.info "Invoking Session Specific DL For Destroy ..."
        BrowserStack.logger.info "DL UUID in Destroy :: #{@dlUuid}"
        BrowserStack.logger.info "Port is #{@port}"

        @device_state = DeviceState.new(device)
        if @device_state.device_logger_file_present?
          session_end_pid = OSUtils.execute("ps -ef | grep '[d]evice-logger' | grep #{device} | awk '{print $2}'").strip
          BrowserStack.logger.info "Session End PID is '#{session_end_pid}'"
          @device_state.write_to_device_logger_session_end_pid_file(session_end_pid)
          begin
            current_device = DeviceManager.device_configuration_check(device)
            @port = current_device["device_logger_port"]
            BrowserStack.logger.info "Updated Port is #{@port}"

            # Attempt to send the stop event
            send_event("http://localhost:#{@port}/session/stop")
            BrowserStack.logger.info "Session Specific DL stopped successfully."
            sleep 1
            unload_plist(device)
          # Error handling for specific exceptions
          rescue Errno::ENOENT => e
            BrowserStack.logger.error "Plist file not found: #{e.message}"
          rescue Errno::ECONNREFUSED => e
            BrowserStack.logger.error "Connection refused when trying to stop Device Logger: #{e.message}"
          rescue => e
            BrowserStack.logger.error "An unexpected error occurred when trying to stop the Device Logger: #{e.message}"
          end
        end
        @device_state.remove_device_logger_file
        clean_workspace(device)
      else
        BrowserStack.logger.info "Invoking Old DL For Destroy ..."
        clean_workspace(device)
      end
      OSUtils.kill_all_processes("idevicesyslog", device, true)
    end

    def self.clean_workspace(device)
      FileUtils.rm_f(device_logger_file(device))
      app_logs_file = app_device_logs_file(device)
      FileUtils.rm_f(app_logs_file)
      app_session = app_session_file(device)
      FileUtils.rm_f(app_session)
      update_log_file = update_log_level_file(device)
      FileUtils.rm_f(update_log_file)
    end

    def self.device_logger_file(device)
      "#{STATE_FILES_DIR}/session_start/#{device}"
    end

    def self.app_device_logs_file(device)
      "/var/log/browserstack/app_log_#{device}.log"
    end

    def self.pwios_proxy_logs_file(device)
      "/var/log/browserstack/pwios-proxy_#{device}.log"
    end

    def self.app_session_file(device)
      "/tmp/app_session_#{device}"
    end

    def self.update_log_level_file(device)
      "/tmp/AL_minimum_devicelog_level/#{device}"
    end

    def self.async_retry(device, event, retry_interval = 2, max_retries = 100)
      retry_count = 0
      @device_state = DeviceState.new(device)
      Thread.bs_run do
        loop do
          break if retry_count > max_retries

          if @device_state.device_logger_file_present?
            BrowserStack.logger.info "Device Logger is initialized, attempt #{retry_count + 1} to send #{event} event"
            current_device = DeviceManager.device_configuration_check(device)
            @port = current_device["device_logger_port"]
            BrowserStack.logger.info "Device Logger Port Is : #{@port}"
            begin
              send_event("http://localhost:#{@port}/session/#{event}")
              BrowserStack.logger.info "#{event} Event For Session Specific DL is Successful"
              break  # Break the loop on success
            rescue => e
              retry_count += 1
              BrowserStack.logger.error "Error in Sending #{event} Event: #{e.message}."
              BrowserStack.logger.info "Retrying #{event} Event: Attempt #{retry_count}"
              sleep retry_interval
            end
          else
            BrowserStack.logger.warn "Device logger is not initialized. Still retrying..."
            retry_count += 1
            sleep retry_interval
          end
        rescue => e
          BrowserStack.logger.error "Error in Async Retry Loop: #{e.message}. Aborting retries."
          break  # Exit the loop if any other error occurs
        end
      end
    end

    def self.send_event(url, params = {})
      BrowserStack.logger.info "URL : #{url}"
      uri = URI.parse(url)
      uri.query = URI.encode_www_form(params)
      response = Net::HTTP.get_response(uri)
      BrowserStack.logger.info "Response: #{response.body}"
      raise "Non 200 status code: #{response.code}" if response.code != '200'

    end

    def self.start_plist(device, port, username="app")
      svc_name = "com.browserstack.device_logger_#{device}"
      if CheckPlist.check_plist_exist(svc_name)
        BrowserStack.logger.info "Plist For #{svc_name} exists, loading it"
        CheckPlist.load_service(svc_name, ServiceType.SystemService, username, 0)
      else
        BrowserStack.logger.info "Plist For #{svc_name} Does Not Exist, creating & loading it"
        create_plist(device, port)
      end
    end

    def self.create_plist(device, port, username="app")
      BrowserStack.logger.info "Creating Device Logger Plist For #{device} and port #{port}"
      platform = Configuration["default_platform"] == "tvos" ? "--tvos" : "--ios-njb"
      cp = CheckPlist.new(
        "com.browserstack.device_logger_#{device}",
        ServiceType.SystemService,
        [
            @nodepath,
            "#{@server_config['device_logger_path']}/main.js",
            platform,
            device,
            port
        ],
        "#{@logging_root}/device_logger_#{device}.log",
        "#{@logging_root}/device_logger_#{device}.log",
        username,
        nil,
        { WorkingDirectory: @server_config["device_logger_path"] }
      )
      cp.update
    end

    def self.unload_plist(device, username="app")
      BrowserStack.logger.info "Unloading Device Logger Plist For #{device}"
      svc_name = "com.browserstack.device_logger_#{device}"
      CheckPlist.unload_service(svc_name, ServiceType.SystemService, username)
    end
  end
  # rubocop:enable Metrics/ModuleLength
end
