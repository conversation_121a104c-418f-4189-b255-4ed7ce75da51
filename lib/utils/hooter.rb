require 'hoothoot'

STATIC_CONF = begin
  JSON.parse(File.read('/usr/local/.browserstack/config/static_conf.json'))
rescue
  {}
end

class Hooter
  def initialize(region=nil)
    @start_time = Time.now
    @region = if region
                region
              else
                STATIC_CONF['rails_endpoint'].include?('browserstack.com') ? STATIC_CONF['region'] : 'dev'
              end
  end

  def send_time(measurement, tags)
    copied_tags = Marshal.load(Marshal.dump(tags))
    copied_tags['team'] = ['mobile']
    finish_time = Time.now
    response_time = finish_time - @start_time
    $hoot = $hoot || HootHoot::HootHoot.new(@region)
    $hoot.emit(measurement, response_time, copied_tags)
  end

  def send_mdm_response(measurement, device, response_code: nil, command_uuid: nil)
    finish_time = Time.now

    if device.nil? || device.empty?
      puts 'Insufficient data to send to hoothoot!'
      return
    else
      device_name = `/usr/local/bin/gtimeout 30 ideviceinfo -u #{device} -k ProductType`.gsub(',', '_').strip
    end

    tags = {
      "platform" => "ios_njb",
      "device" => device
    }

    tags['device_name'] = device_name if !device_name.nil? && ( device_name.downcase.include?('iphone') || !device_name.downcase.include?('ipad') )

    tags['response_code'] = "#{response_code[0]}xx" unless response_code.nil?
    tags['command_uuid'] = command_uuid unless command_uuid.nil?
    tags['team'] = 'mobile'

    response_time = finish_time - @start_time

    $hoot = $hoot || HootHoot::HootHoot.new(@region)
    $hoot.emit(measurement, response_time, tags)
  end

  def send_internet_type(measurement, tags)
    copied_tags = Marshal.load(Marshal.dump(tags))
    copied_tags['team'] = ['mobile']
    $hoot = $hoot || HootHoot::HootHoot.new(@region)
    $hoot.emit(measurement, 1, copied_tags)
  end

  def send_replay_kit_stop_failure(device_id, device_name, device_version)
    tags = {
      "os" => "ios_njb",
      "device_id" => device_id,
      "device_name" => device_name,
      "device_version" => device_version,
      "team" => "mobile"
    }
    $hoot ||= HootHoot::HootHoot.new(@region)
    $hoot.emit("replay_kit_stop_failure", 1, tags)
  end

  def send_uptime_metric(username, product, event)
    $hoot_uptime = $hoot_uptime || HootHoot::HootHoot.new(@region, 25821, { unique_user_event: true, skip_hostname: true })
    $hoot_uptime.unique_user_event(username, product, event, 'mobile', { "os" => "ios_njb" })
  end

  def send(device_name, kind, browser = nil)
    finish_time = Time.now
    tags = {
      "kind" => kind,
      "os" => "ios_njb",
      "type" => device_name
    }
    tags['team'] = 'mobile'
    tags['browser'] = browser unless browser.nil?
    execution_time = finish_time - @start_time

    $hoot = $hoot || HootHoot::HootHoot.new(@region)
    $hoot.emit("execution_time", execution_time, tags)
  end

  def push_feature_usage(user_id, product, event, tags={})
    $hoot_uptime = $hoot_uptime || HootHoot::HootHoot.new(@region)
    $hoot_uptime.unique_user_event(user_id, product, event, 'mobile', tags)
  end
end
