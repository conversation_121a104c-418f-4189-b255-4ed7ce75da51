require "json"
require "uri"
require "net/http"
require "socket"

module RailsRequest
  def self.all_devices_offline?(mobile_dashboard_auth, machine)
    res = rails_summary(mobile_dashboard_auth, machine)
    res.each { |device| return false unless ["offline", "dead"].include?(device["terminal"]["state"]) }

    true
  end

  def self.rails_summary(mobile_dashboard_auth, machine)
    url = "/admin/terminal?ip=#{machine}"
    res = send_request(mobile_dashboard_auth, url)
  end

  def self.send_request(env, url)
    request_url = "#{env['url']}#{url}"
    request = Net::HTTP::Get.new(request_url)
    request.basic_auth env["username"], env["password"]
    uri = URI.parse(request_url)
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true
    response = http.request(request)

    if response.code != "200"
      BrowserStack.logger.info response.body.to_s
      raise "Bad response from #{request_url}: Code #{response.code}"
    end
    begin
      JSON.parse(response.body)
    rescue
      nil
    end
  end
end
