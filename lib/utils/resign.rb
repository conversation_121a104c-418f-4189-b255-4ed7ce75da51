# This module resigns all iOS apps
# Required arguments is a hash with the following options
# bundle_path: Path to the ipa file or .app file
# destination: The new ipa file that should be created
#              after resigning
# keychain_path: Path to keychain
# keychain_password: Password for the above keychain
# certificate: UUID of the certificate to sign it with (optional, if not present takes from provision_profile
# user: user with which it is supposed to run
# provision_profile : provisioning profile unique id (ppuid)
# entitlements: path to entitlements plist. Usually this plist
#               resides in the provision profile itself
#

require_relative '../configuration'
require_relative '../custom_exceptions'
require_relative '../provisioning/mobileprovision_file'
require_relative '../provisioning/provisioning_profile'
require_relative './osutils'

require 'fileutils'

module BrowserStack
  module Resign
    def resign(args) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
      bundle_path = args[:bundle_path]
      destination = args[:destination]
      temp_bundle_dir = Dir.mktmpdir
      begin
        BrowserStack.logger.info "Created temporary directory #{temp_bundle_dir}"
        case File.extname(bundle_path)
        when ".ipa"
          exit_status = OSUtils.unarchive(bundle_path, temp_bundle_dir)
          raise ResigningException, "Failed to unarchive #{bundle_path}" if exit_status.to_i != 0
        when ".app"
          FileUtils.mkdir_p("#{temp_bundle_dir}/Payload")
          FileUtils.cp_r(bundle_path, "#{temp_bundle_dir}/Payload/")
        else
          raise ResigningException, "Unknown extension #{File.extname(bundle_path)}"
        end
        raise ResigningException, "App does not contain Payload directory" if Dir.entries(temp_bundle_dir).select { |contents| contents == "Payload" }.empty?

        all_app_dir = Dir.entries("#{temp_bundle_dir}/Payload").select { |contents| File.extname(contents) == ".app" }
        raise ResigningException, "Payload directory has #{all_app_dir.size} number of .app directories" if all_app_dir.size != 1

        profile_path = MobileprovisionFile.path(ppuid: args[:provision_profile])
        provisioning_profile = ProvisioningProfile.new(profile_path)

        if args[:certificate].nil?
          #fetch code sign identity from prov profile
          sha1 = provisioning_profile.developer_certificate_sha1
          args[:certificate] = sha1
        end

        raise ResigningException, "Can't find signing id." if args[:certificate].nil?

        # Getting list of all files to be resigned
        # 1. All binaries present in Payload/*.app/
        # 2. All *.dylibs
        # 3. All Plugins
        app_dir = "#{temp_bundle_dir}/Payload/#{all_app_dir[0]}"
        list_of_files_to_resign = get_all_files_to_resign(app_dir)

        BrowserStack.logger.debug("Beginning of app resigning. Need to resign #{list_of_files_to_resign.size} files")

        files_to_remove = %w[_CodeSignature embedded.mobileprovision]
        files_to_remove.each do |file|
          FileUtils.rm_f("#{app_dir}/#{file}")
        end

        FileUtils.cp(profile_path, "#{app_dir}/embedded.mobileprovision")

        all_signed = codesign_files(list_of_files_to_resign, args)
        raise ResigningException, "Non zero exit status while resigning" unless all_signed

        FileUtils.mv(app_dir, destination, force: true)
      rescue ResigningException => e
        BrowserStack.logger.error("Resigning exception: #{e.message}; #{e.backtrace}")
        raise e
      rescue => e
        BrowserStack.logger.info("Exception in resigning, printing backtrace: #{e.backtrace.join("\n")}")
        raise ResigningException, e.message
      ensure
        BrowserStack.logger.debug("Deleting directory #{temp_bundle_dir}")
        FileUtils.rm_rf(temp_bundle_dir)
      end
    end

    private

    def should_sign?(file_path)
      return true if File.directory?(file_path) && (file_path.end_with?('.appex') || file_path.end_with?('.framework'))
      return true if File.extname(file_path) == ".dylib"

      # Binaries dont have any extension
      return false unless File.extname(file_path).empty?

      !OSUtils.get_file_mime_type(file_path).match(/mach-binary/).nil?
    end

    def get_all_files_to_resign(app_path)
      all_paths = Dir.glob("#{app_path}/**/*")
      final_files = [app_path]
      all_paths.each do |curr_path|
        final_files << curr_path if should_sign?(curr_path)
      end
      final_files.reverse
    end

    def codesign_files(files, args)
      OSUtils.lock_keychain_access do
        commands = []
        lock_command = "security lock-keychain -a"
        unlock_command = "security unlock-keychain -p \"#{args[:keychain_password]}\" #{args[:keychain_path]}"
        files.each do |file|
          command_string = "/usr/bin/codesign -f -s \"#{args[:certificate]}\" \"#{file}\""
          command_string += " --entitlements \"#{args[:entitlements]}\"" if args.key?(:entitlements)
          command_string += " 2>&1"
          commands << command_string
        end
        commands.all? do |command|
          _, status = OSUtils.execute("sudo su -l #{args[:user]} -c '#{lock_command} && #{unlock_command} && #{command}'", true)
          status == 0
        end
      end
    end
  end
end
