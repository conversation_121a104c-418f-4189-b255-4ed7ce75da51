require 'json'
require 'open3'
require_relative 'osutils'
require_relative 'devicectl'
require_relative 'utils'
require_relative '../../config/constants'
require_relative '../device_conf'

module PyMobileDevice
  module Processes
    CMD = "#{PYMOBILEDEVICE3} processes"

    def self.list_processes(device)
      out = begin
        JSON.parse(BrowserStack::OSUtils.execute("#{CMD} ps --udid #{device} --no-color"))
      rescue => e
        BrowserStack.logger.info "[DEBUG]:::Going into rescue block for list_processes: #{e.class.name} #{e.message}: #{e.backtrace.join("\n")}"
        {}
      end
      BrowserStack.logger.info "[DEBUG]:::Processes output: #{out}"
      processes = []
      out.each_value do |value|
        processes.append(value["ProcessName"])
      end

      processes
    end
  end

  module Developer
    CMD = "#{PYMOBILEDEVICE3} developer dvt"

    def self.update_rsd_values(device)
      rsd_address = nil
      DeviceCtl::Device.device_info(device).split("\n").each do |ele|
        rsd_address = ele.split.last if ele.include?("tunnelIPAddress")
      end

      rsd_port = BrowserStack::OSUtils.execute("sudo lsof -i -P -n | grep #{rsd_address} | grep remoted | awk '{print $9}'").split(":").last

      rsd_config = { "address": rsd_address, "port": rsd_port }
      File.open("#{CONFIG_ROOT}/rsd_values_#{device}", 'w') { |file| file.write(rsd_config.to_json) }
    end

    def self.rsd_config(device)
      JSON.parse(File.read("#{CONFIG_ROOT}/rsd_values_#{device}"))
    rescue
      {}
    end

    def self.simulate_location(device, lat, long)
      update_rsd_values(device)
      rsd_config = rsd_config(device)
      BrowserStack.logger.info "pymobiledevice3 realpath: #{File.realpath(PYMOBILEDEVICE3)}"
      pid = Process.fork do
        exec("#{CMD} simulate-location set --rsd #{rsd_config['address'].to_s.strip} #{rsd_config['port'].to_s.strip} -- #{lat} #{long}")
      end
      Process.detach(pid)
      pid
    end

    def self.screenshot(device, path)
      rsd_config = rsd_config(device)
      op = BrowserStack::OSUtils.execute("gtimeout 10 #{CMD} screenshot #{path} --rsd #{rsd_config['address'].to_s.strip} #{rsd_config['port'].to_s.strip} 2>&1")
      return if op == ""

      update_rsd_values(device)
      rsd_config = rsd_config(device)
      BrowserStack::OSUtils.execute("gtimeout 10 #{CMD} screenshot #{path} --rsd #{rsd_config['address'].to_s.strip} #{rsd_config['port'].to_s.strip} 2>&1")
    end

    def self.launch(device, bundle_id, rsd_address = nil, rsd_port = nil)
      device_config = BrowserStack::DeviceConf[device]
      device_version = device_config['device_version'].to_f
      device_address_command = "--udid #{device}"
      if device_version >= 17
        if rsd_address.nil? || rsd_port.nil?
          update_rsd_values(device)
          rsd_config = rsd_config(device)
          rsd_address = rsd_config['address'].to_s.strip
          rsd_port = rsd_config['port'].to_s.strip
        end
        device_address_command = "--rsd #{rsd_address.to_s.strip} #{rsd_port.to_s.strip}"
      end
      result, status = BrowserStack::OSUtils.execute("gtimeout 10 #{CMD} launch #{bundle_id} #{device_address_command}", true)
      raise "Failed to launch app, result: #{result}, status: #{status}" unless status == 0
    end

    def self.run_xcui_test(device, bundle_id, rsd_address = nil, rsd_port = nil)
      if rsd_address.nil? || rsd_port.nil?
        update_rsd_values(device)
        rsd_config = rsd_config(device)
        rsd_address = rsd_config['address'].to_s.strip
        rsd_port = rsd_config['port'].to_s.strip
      end
      Open3.popen2("#{CMD} xcuitest #{bundle_id} --rsd #{rsd_address.to_s.strip} #{rsd_port.to_s.strip} 2>&1")
    end

    def self.kill_process_with_name(device, name)
      device_config = BrowserStack::DeviceConf[device]
      device_version = device_config['device_version'].to_f
      device_address_command = "--udid #{device}"
      if device_version >= 17
        update_rsd_values(device)
        rsd_config = rsd_config(device)
        device_address_command = "--rsd #{rsd_config['address'].to_s.strip} #{rsd_config['port'].to_s.strip}"
      end
      output = BrowserStack::OSUtils.execute("gtimeout 10 #{CMD} pkill '#{name}' #{device_address_command} 2>&1")
      raise "Failed to kill process with name" unless output.include?("killing #{name}")
    end

    def self.get_process_id(bundle_id, device_address_command, timeout: 30)
      command = "#{CMD} process-id-for-bundle-id #{bundle_id} #{device_address_command}"
      output, exit_status = BrowserStack::OSUtils.execute(command, true, timeout: timeout, log_command: true)
      raise "process-id-for-bundle-id command failed to execute. Exit status: #{exit_status}. Output: #{output}" if exit_status != 0

      pid = output.strip
      raise "Invalid PID received: #{pid}" unless pid.match?(/^\d+$/)

      pid.to_i
    end

    def self.disable_memory_limit(pid, device_address_command, timeout: 10)
      command = "#{CMD} memlimitoff #{pid} #{device_address_command}"
      output, exit_status = BrowserStack::OSUtils.execute(command, true, timeout: timeout, log_command: true)

      raise "memlimitoff command failed to execute. Exit status: #{exit_status}. Output: #{output}" if exit_status != 0
    end

    def self.remove_memory_limit_for_bundle_id(bundle_id, device)
      device_address_command = "--udid #{device}"

      if BrowserStack::DeviceConf[device]['device_version'].to_f >= 17
        update_rsd_values(device)
        rsd_config = rsd_config(device)
        device_address_command = "--rsd #{rsd_config['address'].to_s.strip} #{rsd_config['port'].to_s.strip}"
      end

      pid = get_process_id(bundle_id, device_address_command)
      disable_memory_limit(pid, device_address_command)

      BrowserStack.logger.info("Successfully removed memory limit for #{bundle_id} PID: #{pid}")
      true
    rescue => e
      BrowserStack.logger.error("Failed to remove memory limit for #{bundle_id}: #{e.message}: #{e.backtrace&.join("\n")}")
      raise
    end

    module Accessibility
      CMD = "#{PYMOBILEDEVICE3} developer accessibility"

      def self.run_audit(device, test_type=nil)
        resp = BrowserStack::OSUtils.execute("gtimeout 10 #{CMD} run-audit #{test_type} --udid #{device}")
        JSON.parse(resp)
      rescue => e
        BrowserStack.logger.info("Exception in run_audit #{e.class.name} #{e.message}: #{e.backtrace.join("\n")}")
        []
      end

      def self.list_items(device, focus_order_timeout)
        resp = BrowserStack::OSUtils.execute("gtimeout #{focus_order_timeout} #{CMD} list-items --udid #{device}")
        JSON.parse(resp)
      rescue => e
        BrowserStack.logger.info("Exception in list_items #{e.class.name} #{e.message}: #{e.backtrace&.join("\n")}")
        []
      end

      def self.notifications(device)
        # continuous running process, running with popen2 to get stdout in realtime
        Open3.popen2("#{CMD} notifications --udid #{device} 2>&1")
      end

      def self.show_settings(device)
        result, status = BrowserStack::OSUtils.execute("gtimeout --foreground 300 #{CMD} settings show --no-color --udid #{device}", true)
        raise "Failed to show settings, result: #{result}, status: #{status}" unless status == 0

        parse_settings(result)
      end

      def self.parse_settings(result)

        settings = {}

        # Sample output:
        # <AXAuditDeviceSetting_v1 INVERT_COLORS = False>
        # <AXAuditDeviceSetting_v1 INCREASE_CONTRAST = False>
        # <AXAuditDeviceSetting_v1 REDUCE_TRANSPARENCY = False>
        # <AXAuditDeviceSetting_v1 REDUCE_MOTION = False>
        # <AXAuditDeviceSetting_v1 ON_OFF_LABELS = False>
        # <AXAuditDeviceSetting_v1 BUTTON_SHAPES = False>
        # <AXAuditDeviceSetting_v1 GRAYSCALE = False>
        # <AXAuditDeviceSetting_v1 DIFFERENTIATE_WITHOUT_COLOR = False>
        # <AXAuditDeviceSetting_v1 DYNAMIC_TYPE = 0.2727272727272727>
        result.each_line do |line|
          next unless (match = line.match(/<AXAuditDeviceSetting_v1 (\w+) = ([\w.]+)>/))

          key = match[1]
          value = match[2]

          # Convert 'False' and 'True' to boolean, otherwise keep numbers as float/int
          value = case value
                  when "False" then false
                  when "True" then true
                  when /\A\d+\.\d+\z/ then value.to_f  # Convert float
                  when /\A\d+\z/ then value.to_i        # Convert integer
                  else value
                  end

          settings[key] = value
        end

        settings
      end

      def self.get_setting(device, setting)
        result, status = BrowserStack::OSUtils.execute(
          "gtimeout --foreground 300 #{CMD} settings show --no-color --udid #{device} | grep #{setting}", true
        )
        raise "Failed to get setting, result: #{result}, status: #{status}" unless status == 0

        settings = parse_settings(result)

        return settings["DYNAMIC_TYPE"] if settings.key?("DYNAMIC_TYPE")

        raise "Failed to get setting, result: #{result}, status: #{status}" unless status == 0
      end

      def self.set_setting(device, setting, value)
        pid = Process.fork do
          exec("#{CMD} settings set #{setting} #{value} --udid #{device}")
        end
        Process.detach(pid)
        pid
      end

      def self.reset_accessibility_settings(device)
        result, status = BrowserStack::OSUtils.execute("gtimeout 10 #{CMD} settings reset --udid #{device}", true)
        raise "Failed to reset accessibility, result: #{result}, status: #{status}" unless status == 0
      end
    end
  end

  module Profile
    CMD = "#{PYMOBILEDEVICE3} profile"

    # Returns json of profiles information
    # {
    # "OrderedIdentifiers": [
    #     "com.github.micromdm.micromdm.enroll",
    #     "proxy.00008130-001C25160210001C"
    # ],
    # "ProfileManifest": {
    #     "com.github.micromdm.micromdm.enroll": {
    #         "Description": "Enrollment Profile",
    #         "IsActive": true
    #     },
    #     "proxy.00008130-001C25160210001C": {
    #         "Description": "Proxy",
    #         "IsActive": true
    #     }
    # },
    # "ProfileMetadata": {
    #     "com.github.micromdm.micromdm.enroll": {
    #         "PayloadDescription": "The server may alter your settings",
    #         "PayloadDisplayName": "Enrollment Profile",
    #         "PayloadOrganization": "MicroMDM",
    #         "PayloadRemovalDisallowed": false,
    #         "PayloadUUID": "4FEBFF40-8807-4FD1-B3D1-01572CCCA42A",
    #         "PayloadVersion": 1
    #     },
    #     "proxy.00008130-001C25160210001C": {
    #         "PayloadDisplayName": "Proxy",
    #         "PayloadOrganization": "BrowserStack",
    #         "PayloadRemovalDisallowed": false,
    #         "PayloadUUID": "00008130-001C25160210001C",
    #         "PayloadVersion": 1
    #     }
    # }

    def self.remove_profiles(device, profile_identifier)
      result, status = BrowserStack::OSUtils.execute("gtimeout --foreground 300 #{CMD} remove --no-color --udid #{device} #{profile_identifier}", true)
      raise "Failed to remove profiles, result: #{result}, status: #{status}" unless status == 0

    end

    def self.list_profiles(device)
      result, status = BrowserStack::OSUtils.execute("gtimeout --foreground 300 #{CMD} list --no-color --udid #{device}", true)
      raise "Failed to list profiles, result: #{result}, status: #{status}" unless status == 0

      JSON.parse(result)
    end
  end

  module ProvisioningProfile
    CMD = "#{PYMOBILEDEVICE3} provision"

    def self.list_provisioning_profiles(device)
      result, status = BrowserStack::OSUtils.execute("gtimeout --foreground 300 #{CMD} list --udid #{device} --no-color | grep UUID", true)
      raise "Failed to list provisioning profiles, result: #{result}, status: #{status}" unless status == 0

      result.gsub(/\s+/, "").split(",")
    end

    def self.install_provisioning_profile(device, profile)
      result, status = BrowserStack::OSUtils.execute("gtimeout --foreground 300 #{CMD} install '#{profile}' --udid #{device}", true)
      raise "Failed to install provisioning profile, result: #{result}, status: #{status}" unless status == 0
    end

    def self.remove_provisioning_profile(device, uuid)
      result, status = BrowserStack::OSUtils.execute("gtimeout --foreground 300 #{CMD} remove #{uuid} --udid #{device}", true)
      raise "Failed to remove provisioning profile, result: #{result}, status: #{status}" unless status == 0
    end
  end

  module Lockdown
    CMD = "#{PYMOBILEDEVICE3} lockdown"

    def self.start_tunnel(device)
      Open3.popen2("sudo #{CMD} start-tunnel --no-color --script-mode --udid #{device} 2>&1")
    end

    def self.set_key(device, domain, key, value)
      result, status = BrowserStack::OSUtils.execute("gtimeout 30 #{CMD} set '#{value}' '#{domain}' '#{key}' --udid #{device} --no-color 2>&1", true)
      raise "Failed to set key, result: #{result}, status: #{status}" unless status == 0
    end

    def self.get_key(device, domain, key)
      result, status = BrowserStack::OSUtils.execute("gtimeout 30 #{CMD} get '#{domain}' '#{key}' --udid #{device} --no-color 2>&1", true)
      raise "Failed to get key, result: #{result}, status: #{status}" unless status == 0

      return nil if result.include?("No such value")

      result
    end

    def self.remove_key(device, domain, key)
      result, status = BrowserStack::OSUtils.execute("gtimeout 30 #{CMD} remove '#{domain}' '#{key}' --udid #{device} --no-color 2>&1", true)
      raise "Failed to remove key, result: #{result}, status: #{status}" unless status == 0
    end
  end

  module Mounter
    CMD = "#{PYMOBILEDEVICE3} mounter"

    def self.auto_mount(device)
      result, status = BrowserStack::OSUtils.execute("gtimeout 30 #{CMD} auto-mount --udid #{device} 2>&1", true)
      raise "Failed to auto mount developer disk image, result: #{result}, status: #{status}" unless status == 0
    end
  end
end
