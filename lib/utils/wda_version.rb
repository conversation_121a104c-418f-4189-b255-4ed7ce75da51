require 'browserstack_logger'
require 'digest'
require 'faraday'
require 'fileutils'
require 'timeout'

require_relative '../configuration'
require_relative '../custom_exceptions'
require_relative '../provisioning/mobileprovision_file'
require_relative '../models/device_state'
require_relative '../provisioning/ppuid_file'
require_relative '../provisioning/provisioning_profile'
require_relative 'http_utils'
require_relative 'osutils'
require_relative 'utils'
require_relative 'web_driver_agent'
require_relative 'xcode_build_utils'
require_relative 'xcode_utils'
require_relative 'idevice_utils'
require_relative 'apple_tv_utils'

class WDAVersion # rubocop:todo Metrics/ClassLength
  include BrowserStack

  BUNDLE_ID = 'com.facebook.WebDriverAgentRunner.xctrunner'
  # Access group used below is also hardcoded in WDA, so whenever we rotate it, we should rotate in WDA too.
  WDA_UNIQUE_KEYCHAIN_ACCESS_GROUP = 'com.browserstack.qoAX2A8vpEuU76muWHWz2tKbbBB1LVWf'

  class << self
    include BrowserStack

    def outdated?(device_id, appium_version, ios_version)
      WDAVersion.new(device_id, appium_version, ios_version).outdated_version?
    end

    def device_wda_folder_path(device_id, appium_version, ios_version = IdeviceUtils.device_version(device_id))
      WDAVersion.new(device_id, appium_version, ios_version).wda_folder_path
    end

    def wda_folder_path(ios_version, appium_version, ppuid)
      ios_version = ensure_correct_ios_version(ios_version)
      "#{BrowserStack::Configuration['config_root']}/wda_derived_data_#{ios_version}_#{appium_version}_#{ppuid}"
    end

    def tvos_ipa_path(device_id, appium_version)
      wda_folder = WDAVersion.device_wda_folder_path(device_id, appium_version)
      "#{wda_folder}/Build/Products/wda_tvos.ipa"
    end

    def xctestrun_file(device_id, appium_version, ios_version, platform_version)
      WDAVersion.new(device_id, appium_version, ios_version).xctestrun_file_template(platform_version)
    end

    # Some wda versions are shared by ios versions
    def ensure_correct_ios_version(ios_version)
      return '18' if Gem::Version.new(ios_version) >= Gem::Version.new('18')
      return '17' if Gem::Version.new(ios_version) >= Gem::Version.new('17')
      return '16' if Gem::Version.new(ios_version) >= Gem::Version.new('16')
      return '15' if Gem::Version.new(ios_version) >= Gem::Version.new('15')
      return '13.4' if Gem::Version.new(ios_version) >= Gem::Version.new('13.4')

      # If ios 12 to 13.3 devices are connected to catalina hosts return 13.4.
      # As they use wda same as 13.4 devices mob_9514
      return '13.4' if Gem::Version.new(ios_version) >= Gem::Version.new('12') &&
        OSUtils.macos_version.start_with?('10.15')

      ios_version.to_i
    end
  end

  attr_accessor :device_id, :appium_version, :ios_version, :wda_folder_path

  def initialize(device_id, appium_version, ios_version)
    @device_id = device_id
    @appium_version = appium_version
    @ios_version_full = ios_version
    @ios_version = WDAVersion.ensure_correct_ios_version(ios_version)
    @ppuid_file = PpuidFile.new(device_id)
    @wda_folder_path = WDAVersion.wda_folder_path(@ios_version, @appium_version, @ppuid_file.ppuid)
    @device_state = DeviceState.new(device_id)
  end

  # copies the built WDA (downloaded via Nix) to the derived data directory (used by Appium)
  # and resigns it
  # Does not actually install the WDA in the phone
  # WDA is installed when starting an Appium session (xcodebuild test-without-building .... )
  def install_wda_version(force_install: false)
    log_info("----- Starting wda check and install for #{device_id} - Appium Version: #{appium_version} -----")

    wda_lock_file = "#{STATE_FILES_DIR}/wda_installation_#{ios_version}_#{appium_version}_#{@ppuid_file.ppuid}.lock"

    # We take lock because we need to clear derived data directory while copying
    # WDA to the derived data directory
    Utils.with_lock(wda_lock_file, 20) do
      if force_install || outdated_version?
        if @ppuid_file.valid?
          log_info("WDA on appium #{appium_version} for #{device_id} needs installation. Copying source and Signing it")
          copy_source_files
          sign_wda
          log_info("WDA on appium #{appium_version} for #{device_id} has been signed")
          if apple_tv_device?
            # TODO: remove file deletion after updating in wda_forge
            ipa_path = "#{wda_folder_path}/Build/Products/#{ipa_name}"
            File.delete(ipa_path) if File.exist?(ipa_path)
            create_ipa_from_app
          end
        else
          log_info("Skipping #{device_id}, no ppuid file present")
          raise "No ppuid file present to sign WDA" if force_install
        end
      else
        log_info("WDA on appium #{appium_version} for #{device_id} is up to date!")
      end
    end
  rescue => e
    log_error "Failure while downloading/installing wda: #{e.message}"
    raise e
  end

  def sign_wda
    run_resign_sh
    xctestrun_fixup unless apple_tv_device?
  end

  def xctestrun_fixup
    log_info("Fixing xctestun file for possible issues")
    xctestrun_file = xctestrun_file_template(BrowserStack::XcodeUtils.get_platform_version)

    # If xctestrun file is not there after download
    unless File.readable?(xctestrun_file)
      if xctestrun_file.include?("18.2") || xctestrun_file.include?("18.3")
        old_template = xctestrun_file_template('18.0')
        FileUtils.cp(old_template, xctestrun_file) if File.readable?(old_template)
      elsif xctestrun_file.include?("17.2") || xctestrun_file.include?("17.5")
        old_template = xctestrun_file_template('17.0')
        FileUtils.cp(old_template, xctestrun_file) if File.readable?(old_template)
      elsif xctestrun_file.include?("14.4")
        xctestrun_file = xctestrun_file.gsub("14.4", "14.1")
      elsif xctestrun_file.include?("os12.0")
        xctestrun_file = xctestrun_file.gsub("12.0", "12.1")
      end
    end

    raise XCTestRunFileMissing, "Unable to find xctestrun file on disk, codesigning failed or wrong wda zip" unless File.readable?(xctestrun_file)

    # FIXME: Sometimes Appium looks for this file, no idea why.
    # It seems to be failing to get the version. Need to investigate.
    FileUtils.cp(xctestrun_file, xctestrun_file_template('null'))

    # FIXME: Same, sometimes the version appium looks for is 12.0 although it doesn't matter in build phase...
    FileUtils.cp(xctestrun_file, xctestrun_file_template('12.0')) if xctestrun_file.include? 'os12.1'
  end

  def outdated_version?
    log_info("Checking for outdated version for appium: #{appium_version} for device: #{device_id}")

    unless File.exist?(source_location)
      log_info("source_location: #{source_location} doesn't exist")
      return true
    end

    unless File.exist?(wda_folder_path)
      log_info("wda_folder_path: #{wda_folder_path} doesn't exist")
      return true
    end

    wda_xctestrun_file = xctestrun_file_template(BrowserStack::XcodeUtils.get_platform_version)
    unless apple_tv_device? || File.exist?(wda_xctestrun_file)
      log_info("wda_xctestrun_file: #{wda_xctestrun_file} doesn't exist")
      return true
    end

    unless File.exist?(wda_app_folder)
      log_info("wda_app_folder: #{wda_app_folder} doesn't exist")
      return true
    end

    unless File.exist?("#{wda_folder_path}/wda_checksum")
      log_info("#{wda_folder_path}/wda_checksum doesn't exist")
      return true
    end

    # That means that WDA installation failed and didn't place ppuid file after signing
    unless File.exist?("#{wda_folder_path}/ppuid")
      log_info("#{wda_folder_path}/ppuid doesn't exist")
      return true
    end

    expected_wda_version = File.read("#{source_location}/wda_checksum").strip
    installed_wda_version = File.read("#{wda_folder_path}/wda_checksum").strip

    log_info("Expected WDA checksum: #{expected_wda_version}")
    log_info("Installed WDA checksum: #{installed_wda_version}")

    if installed_wda_version != expected_wda_version
      log_info("Version is outdated")
      true
    else
      log_info("Version up to date")
      false
    end
  end

  # Throws exception if WDA is not signed yet
  def wda_signed_at
    # This file is created at the end of #run_resign_sh
    if File.exists?("#{wda_folder_path}/ppuid")
      File.mtime("#{wda_folder_path}/ppuid")
    else
      raise "WDA not signed yet"
    end
  end

  def xctestrun_file_template(version)
    filename = if apple_tv_device?
                 "WebDriverAgentRunner_tvOS_appletvos"
               else
                 "WebDriverAgentRunner_iphoneos"
               end

    "#{wda_folder_path}/Build/Products/#{filename}#{version}-arm64.xctestrun"
  end

  def wda_app_folder
    if apple_tv_device?
      folder = "Debug-appletvos"
      app_name = "WebDriverAgentRunner_tvOS-Runner"
    else
      folder = "Debug-iphoneos"
      app_name = "WebDriverAgentRunner-Runner"
    end

    "#{wda_folder_path}/Build/Products/#{folder}/#{app_name}.app"
  end

  private

  # Null files are created in the first run by wda, this is then re used for the next runs
  # Whenever a new wda is deployed we should delete the old file to avoid reuse of null files from old wda
  def delete_outdated_xctestrun
    wda_xctestrun_folder = BrowserStack::WebDriverAgent.xctestrun_file_path(device_id, appium_version, @ios_version_full)
    filename = "#{wda_xctestrun_folder}/#{device_id}_null.xctestrun"
    File.delete(filename) if File.exist?(filename)
  end

  def source_location
    "#{BrowserStack::Configuration['webdriver_agent_builds_path']}/#{ios_version}_#{appium_version}"
  end

  def copy_source_files
    log_info('Copying source files')

    # Copy to tmp
    BrowserStack::OSUtils.execute("cp -RL #{source_location} #{wda_folder_path}.tmp")
    # Fix permissions
    BrowserStack::OSUtils.execute("chmod -R +w #{wda_folder_path}.tmp")
    # Remove derived data directory
    BrowserStack::OSUtils.execute("sudo rm -rf #{wda_folder_path}")
    # Rename to derived data directory
    BrowserStack::OSUtils.execute("mv #{wda_folder_path}.tmp #{wda_folder_path}")
    # Clear all extended attributes, sometimes Xcode fails if this is not done
    BrowserStack::OSUtils.execute("xattr -cr #{wda_folder_path}")
    delete_outdated_xctestrun
  end

  def run_resign_sh
    log_info("Running resign.sh for WDA at: #{wda_app_folder}")

    profile_file = MobileprovisionFile.path(udid: @device_id)
    provisioning_profile = ProvisioningProfile.new(profile_file)
    cert_identity = provisioning_profile.developer_certificate_identity
    entitlements = provisioning_profile.entitlements
    # Modifying entitlements to add additional keychain access group
    keychain_access_groups = (entitlements["keychain-access-groups"] || []).map(&:clone)
    keychain_access_groups.append("#{provisioning_profile.team_id}.#{WDA_UNIQUE_KEYCHAIN_ACCESS_GROUP}")
    entitlements["keychain-access-groups"] = keychain_access_groups
    entitlements_data = provisioning_profile.entitlements_plist

    entitlements_file = Tempfile.new(['entitlements', '.plist'])
    File.write(entitlements_file, entitlements_data)

    keychain_password = BrowserStack::Configuration['appium_keychain_password']
    keychain_path = BrowserStack::Configuration['appium_keychain']

    log_info("Signing with Developer ID: #{cert_identity}, SHA1: #{provisioning_profile.developer_certificate_sha1}")

    resign_log_file = "/var/log/browserstack/resign_wda_#{@device_id}.log"
    sign_cmd = ["bash #{BrowserStack::Configuration['mobile_root']}/scripts/resign_wda.sh"]
    sign_cmd << "'#{wda_app_folder}' '#{provisioning_profile.developer_certificate_sha1}'"
    sign_cmd << "'#{profile_file}' '#{entitlements_file.path}'"
    sign_cmd << "'#{keychain_password}' '#{keychain_path}' >> #{resign_log_file} 2>&1"

    output, status = BrowserStack::OSUtils.execute("sudo su -l app -c \"#{sign_cmd.join(' ')}\"", true)
    if status != 0
      log_error("resign_wda.sh failed, check logs")
      raise "resign_wda.sh failed check logs"
    end

    BrowserStack::OSUtils.execute("cp #{@ppuid_file.path} #{wda_folder_path}/ppuid", true)
  end

  def create_ipa_from_app
    build_products_path = "#{wda_folder_path}/Build/Products"
    ipa_contents_path = "#{build_products_path}/IpaContents"
    ipa_payload_path = "#{ipa_contents_path}/Payload"
    FileUtils.mkdir_p(ipa_payload_path)
    FileUtils.rm_rf(Dir["#{ipa_payload_path}/*"])
    FileUtils.cp_r("#{build_products_path}/Debug-appletvos/#{wda_scheme}-Runner.app", ipa_payload_path)

    Utils.zip_directory_recursively(ipa_contents_path, "#{build_products_path}/#{ipa_name}")
    FileUtils.rm_rf(Dir["#{ipa_contents_path}/*"])
    log_info("Created WDA ipa from app for #{device_id}")
  end

  def log_info(message)
    if BrowserStack.logger
      BrowserStack.logger.info("#{self.class} device_id: #{device_id}, appium_version: #{appium_version} | #{message}")
    else
      puts("#{self.class} device_id: #{device_id} | #{message}")
      BrowserStack.init_logger('/dev/stdout')
    end
  end

  def log_error(message)
    if BrowserStack.logger
      BrowserStack.logger.error("#{self.class} device_id: #{device_id}, appium_version: #{appium_version} | #{message}")
    else
      puts("#{self.class} device_id: #{device_id} | #{message}")
      BrowserStack.init_logger('/dev/stdout')
    end
  end

  def wda_scheme
    scheme = "WebDriverAgentRunner"
    scheme += '_tvOS' if apple_tv_device?

    scheme
  end

  def apple_tv_device?
    @apple_tv_device ||= IdeviceUtils.apple_tv_device?(device_id)
  end

  def ipa_name
    @ipa_name ||= "wda_#{IdeviceUtils.os(device_id)}.ipa"
  end

end
