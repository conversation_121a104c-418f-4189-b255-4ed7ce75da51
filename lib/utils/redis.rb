require 'redis'
require_relative '../configuration'

module BrowserStack
  class RedisUtils
    class << self
      def redis_works?

        REDIS_CLIENT.ping == "PONG"
      rescue => e
        false

      end

      def client
        config = BrowserStack::Configuration.new.all
        Redis.new(
          host: config['redis_url'],
          port: config['mdm_redis_port'],
          timeout: config['redis_timeout'],
          reconnect_attempts: 3
        )
      end
    end
  end
end

# This global client is only used when mdm calls are used in cleanup. Device check threads start their own clients.
REDIS_CLIENT = BrowserStack::RedisUtils.client
