require 'English'
require 'etc'
require 'fileutils'
require 'ipaddr'
require 'net/http'
require 'open3'
require 'openssl'
require 'socket'
require 'stringio'
require 'uri'
require 'json'
require 'time'

require_relative '../utils/utils'
require_relative '../configuration'
require_relative '../custom_exceptions'
require_relative '../utils/zombie'

module BrowserStack
  class OSUtils # rubocop:todo Metrics/ClassLength
    TIMEOUT_CMD = "/usr/local/bin/gtimeout"
    class << self
      conf = Configuration.new
      @@server_config = conf.all

      def kill_pid(pid)
        Process.kill 9, pid
      end

      def executable_exists?(name)
        ENV['PATH'].split(':').collect do |d|
          return true if File.exists? "#{d}/#{name}"
        end
        false
      end

      def execute(command, return_status = false, timeout: nil, kill_after: 5, log_command: true, return_error: false, user: nil)
        command = "sudo -u #{user} #{command}" if user
        command = "#{TIMEOUT_CMD} -k#{kill_after} #{timeout} #{command}" unless timeout.nil?
        command = "#{command} 2>&1" if return_error
        BrowserStack.logger.info("Executing =====|#{command}|=====") if log_command
        result = `#{command}`
        status = $CHILD_STATUS
        BrowserStack.logger.info("Output of execution (first 1000 chars): =====|#{result[0..1000]}|=====. Status: #{status}") if log_command
        raise OSUtilsError, "Command `#{command}` timed out after #{timeout}s" if status.exitstatus == 124

        return_status ? [result, status.exitstatus] : result
      end

      # Helper to retry an execute() statement if it initially fails, pass :retries symbol to control amount of times it'll retry
      # Example using class method: OSUtils.execute_retry OSUtils.method(:execute), "ls -l", retries: 2
      def execute_retry(func, *args, **keyword_args)
        retries = keyword_args.fetch(:retries, 1)
        keyword_args.delete(:retries)
        func_string = "#{func.name}(#{args.empty? ? '' : args.join(', ')} #{keyword_args.empty? ? '' : keyword_args.inspect})"
        BrowserStack.logger.info "Calling with retries: #{func_string})."
        begin
          output, status = func.call(*args, **keyword_args)
          if status.to_i != 0
            BrowserStack.logger.error "Non zero exit code Output is #{output} , status code:#{status}"
            raise "#{func_string}: exit code #{status}"
          end
          [output, status]
        rescue => e
          BrowserStack.logger.info "Retrying: #{func_string}| #{retries}"
          retry if (retries -= 1) >= 0
          raise e
        end
      end

      def execute2(command)
        BrowserStack.logger.info("Executing2 =====|#{command}|=====")
        stdout, stderr, status = Utils.capture3(command)
        BrowserStack.logger.info("Output of execution2: =====#{stdout}|=====. Stderr: #{stderr}. Status: #{status}")
        [stdout, stderr, status]
      end

      # uses exec to replace the current process with command
      # *USE THIS ONLY IN A FORK*
      def exec!(command, *args)
        BrowserStack.logger.info "Exec, replacing current process with #{command} #{args}"
        exec(command, *args)
      end

      def which(name)
        full_path = execute("source ~/.profile && which #{name}").chomp
        retval = $CHILD_STATUS
        return name unless retval.success?

        full_path
      end

      def current_hostname
        execute("hostname").chomp
      end

      def set_hostname(hostname)
        BrowserStack.logger.info "Attempting to set_hostname: #{hostname}"
        case RUBY_PLATFORM
        when /linux/
          execute("sudo hostnamectl set-hostname #{hostname}")
        when /darwin/
          execute("sudo scutil --set HostName #{hostname}")
        end
      end

      def is_process_running?(process_name, process_identifier)
        !get_process_pid(process_name, process_identifier).to_s.strip.empty?
      end

      def get_pid_file(process_name, process_identifier)
        "/tmp/pid_#{process_name}_#{process_identifier}"
      end

      def verify_pid_file(pid_file, delete_file=false)
        return nil unless File.exists?(pid_file)

        pid = begin
          File.open(pid_file, &:readline)
        rescue
          "BSTACK_NAN"
        end
        process_running = !execute("#{PSTREE} #{pid}").empty? # `pstree BSTACK_NAN` is empty
        FileUtils.rm(pid_file) if !process_running || delete_file
        process_running ? pid : nil
      end

      def get_process_pid(process_name, process_identifier)
        pid_file = get_pid_file(process_name, process_identifier)
        pid_from_file = verify_pid_file(pid_file)

        return pid_from_file unless pid_from_file.nil?

        execute("ps aux | grep -i #{process_name} | grep #{process_identifier} | grep -vw grep | grep -v async_network_flow_flag | awk '{print $2}'")
      end

      def kill_process(process_name, process_identifier, signal="KILL")
        pid_file = get_pid_file(process_name, process_identifier)
        pid_from_file = verify_pid_file(pid_file, true)

        if pid_from_file.nil?
          begin
            get_process_pid(process_name, process_identifier).split("\n").each do |pid|
              BrowserStack.logger.info "kill_process - killing #{pid}.. "
              Process.kill(signal, pid.to_i) unless pid.to_i == 0
            end
          rescue
            nil
          end
          return nil
        end

        begin
          execute("#{PSTREE} #{pid_from_file} | sed -e 's/[^0-9 ]//g' | awk '{print $1}'").split("\n").each do |pid|
            BrowserStack.logger.info "killing #{pid}.. "
            Process.kill(signal, pid.strip.to_i) unless pid.strip.to_i == 0
          end
        rescue
          nil
        end

        BrowserStack.logger.info "processes after.."
        execute("#{PSTREE} #{pid_from_file}")
      end

      def kill_all_processes(process_name, device, force=false)
        command = "ps -ef | grep #{process_name} | grep #{device} | grep -v grep | awk '{print $2}' | xargs kill"
        command += " -9" if force
        execute(command)
      end

      def kill_process_on_port(port)
        BrowserStack.logger.info "**Killing for port #{port}**"
        port = begin
          Integer(port)
        rescue
          nil
        end
        return nil unless port

        execute("while sudo kill -9 $(sudo lsof -t -i:#{port} -sTCP:LISTEN) 2>/dev/null; do sleep 1; done")
      end

      def get_duplicate_process_pid(process_name, process_identifier)
        processes = execute("ps -ef | grep '#{process_name}' | grep '#{process_identifier}' | grep -v grep | awk '{print $2,$3}' ").split("\n")
        processes.map! do |process|
          process.split[1].strip.to_i == 1 ? process.split[0].strip.to_i : nil
        end
        processes.compact
      end

      def is_plist_loaded?(label, type)
        if type == ServiceType.SystemService
          !execute("sudo launchctl list | grep #{label} | grep -v grep").to_s.strip.empty?
        else
          !execute("sudo su -l app -c '/bin/launchctl list | grep #{label} | grep -v grep'").to_s.strip.empty?
        end
      end

      def check_dns(hostname)
        BrowserStack.logger.info "getting DNS answer for #{hostname}"
        dig_result = execute "dig #{hostname}"
        if !dig_result.include? "ANSWER SECTION"
          BrowserStack.logger.info "no DNS answer for #{hostname}"
          false
        else
          BrowserStack.logger.info "DNS answer found for #{hostname}"
          true
        end
      end

      def unload_bluetooth
        Utils.fork_process('sudo kextunload -b com.apple.iokit.BroadcomBluetoothHostControllerUSBTransport &')
      end

      # https://support.apple.com/en-us/HT202516
      def flush_dns
        osx_version = execute("uname -r").chomp
        if Gem::Version.new('10.5') >= Gem::Version.new(osx_version) && Gem::Version.new('10.6.8') < Gem::Version.new(osx_version)
          execute('sudo dscacheutil -flushcache')
        elsif Gem::Version.new('10.10') >= Gem::Version.new(osx_version) && Gem::Version.new('10.10.3') <= Gem::Version.new(osx_version)
          execute('sudo discoveryutil udnsflushcaches;sudo discoveryutil mdnsflushcache')
        else
          execute('sudo killall -HUP mDNSResponder')
        end
      end

      def restart_plist(plist_file)
        execute("sudo launchctl unload -w #{plist_file}; sudo launchctl load -w #{plist_file}")
      end

      def unload_plist_as(plist_file, user='app')
        execute("sudo su -l #{user} -c '/bin/launchctl unload -w #{plist_file}'")
      end

      def unload_system_plist(plist_file)
        execute("sudo launchctl unload -w #{plist_file}")
      end

      def load_plist_as(plist_file, user='app')
        execute("sudo su -l #{user} -c '/bin/launchctl load -w #{plist_file}'")
      end

      def load_system_plist(plist_file)
        execute("sudo launchctl load -w #{plist_file}")
      end

      # return true when firewall is enabled
      def is_firewall_enabled?
        execute("sudo pfctl -si 2>/dev/null | grep 'Status: Enabled' | wc -l").chomp.to_i == 1
      end

      def start_firewall
        # Load Custom PF conf if offline mode is enabled on any device else load /etc/pf.conf
        execute2("ls #{STATE_FILES_DIR}/offline_mode_* > /dev/null 2>&1 && sudo pfctl -f #{CUSTOM_PF_CONF} || sudo pfctl -e -f #{PF_ORIGINAL_CONF}")
      end

      def check_xcode
        execute("(xcodebuild -version)")
      end

      def fix_device_logger
        restart_plist("/Library/LaunchDaemons/com.browserstack.device_logger.plist")
      end

      def last_machine_boottime
        execute("sysctl -n kern.boottime | awk '{print $4}' | sed 's/,//g'").to_i
      end

      def booted_in_last_n_minutes?(minutes)
        time_difference = Time.now.to_i - last_machine_boottime
        time_difference < 60 * minutes
      end

      def bridge100_exists?
        _output, status = execute('ifconfig bridge100', true)
        status == 0
      rescue OSUtilsError
        false
      end

      def bridge100_has_local_inet?
        output, status = execute('ifconfig bridge100 | grep -E "inet (192\.168\.\d+\.\d+)"', true)
        status == 0 && !output.empty?
      rescue OSUtilsError
        false
      end

      def create_interface(interface)
        execute("sudo ifconfig #{interface} create")
        execute("sudo ifconfig #{interface} up")
        bridge100_exists?
      end

      def check_device_logger
        response = HttpUtils.make_get_request("http://localhost:45690/health", 3)
        unless response.status.to_i == 200
          BrowserStack::Zombie.push_logs("device_logger_exception", "Connect to device logger timed out")
          fix_device_logger
        end
      end

      def retrieve_certificates(user, valid_only: true)
        valid_option = valid_only ? '-v' : ''
        result, exit_status = execute("(sudo su - #{user} -c \"security find-identity #{valid_option} -p codesigning ~/Library/Keychains/Browserstack.keychain-db\") 2>&1", true)
        raise "Error during retrieve_certificates: security find-identity command failed." unless exit_status == 0
        # The "valid identities found" string should always be present, even with 0 valid identities
        raise 'Error during retrieve_certificates: find-identities returned invalid response.' unless result.include?('valid identities found')

        result
      end

      # [HH, MM, SS], [MM, SS], [SS] => Time in seconds
      def time_to_seconds(time_array)
        return 0 if time_array.empty?

        case time_array.size
        when 3
          time_array[0].to_i * 60 * 60 + time_array[1].to_i * 60 + time_array[2].to_i
        when 2
          time_array[0].to_i * 60 + time_array[1].to_i
        else
          time_array[0].to_i
        end

      end

      def process_elapsed_time(partial_command_name, process_identifier)
        cmd = "ps -ax -o etime,command -c "\
        " | grep #{partial_command_name} "\
        " | grep #{process_identifier} "\
        " | awk -F ' ' '{print $1}'"
        BrowserStack.logger.info("Command to be executed: #{cmd}")
        elapsed_time = execute(cmd).split('-')
        BrowserStack.logger.info("Elapsed time: #{elapsed_time}")
        if elapsed_time.size == 2
          time_array = elapsed_time[1].strip.split(':')
          return time_to_seconds(time_array) + elapsed_time[0].to_i * 24 * 60 * 60
        end
        time_to_seconds(elapsed_time[0].strip.split(':'))
      end

      # "security find-identity" output is like so
      #
      #   Policy: X.509 Basic
      #    Matching identities
      #    1) 566A83690796FDA76AB6854B33730D2171F65676 "iPhone Developer: Ankur Goel (L2SYV57X8M)" (CSSMERR_TP_CERT_EXPIRED)
      #    2) 74D206F62042E508487E76ED853EF547CDA025F9 "iPhone Developer: Vishal Chauhan (44K4QDFNBN)" (CSSMERR_TP_CERT_EXPIRED)
      #    3) D506754731F58F5994933045FB0039F9C5222C47 "iPhone Developer: Siddharth Lamba (9ZS959N5CN)" (CSSMERR_TP_CERT_EXPIRED)
      #
      # This command gets lines that start with num) e.g. 1), 2) then splits line arguments by space
      # We ignore the space if it is contained inside quotes (like "iPhone Developer: Ankur Goel (L2SYV57X8M)" above)
      # and take as whole. This allows us to identify the certificate hash and id
      # Return value of this is Array of tuple Arrays of format [[cert_hash, cert_id],...[]]
      # e.g. [
      #         ["566A83690796FDA76AB6854B33730D2171F65676", "iPhone Developer: Ankur Goel (L2SYV57X8M)"],
      #         ["74D206F62042E508487E76ED853EF547CDA025F9", "iPhone Developer: Vishal Chauhan (44K4QDFNBN)"],
      #         ["D506754731F58F5994933045FB0039F9C5222C47", "iPhone Developer: Siddharth Lamba (9ZS959N5CN)"],
      #      ]
      def get_cert_identities(user, valid_only: false)
        complete_cert_info = retrieve_certificates(user, valid_only: valid_only)
        cert_info_lines = complete_cert_info.split("\n").select { |line| line.strip =~ /^\d+\)/ }

        cert_info_lines.map do |cert_line|
          cert_hash = cert_line.split[1]
          cert_id = cert_line.scan(/"([^"]*)"/)[0][0]
          cert_expired = cert_line.include?("CSSMERR_TP_CERT_EXPIRED")
          [cert_hash, cert_id, cert_expired]
        end
      end

      # Check if codesigning identity is present in the keychain
      # Defaults to checking only valid codesigning identities
      def codesigning_identity_exists?(identity, valid_only: true)
        identities = get_cert_identities('app', valid_only: valid_only).map { |cert_info| cert_info[1] }
        identities.include?(identity)
      end

      # Check if a particular certificate sha1 hash is present in the keychain
      # Defaults to checking both all certificates - valid / invalid
      def certificate_sha1_in_keychain?(sha1, valid_only: false)
        cert_hashes = get_cert_identities('app', valid_only: valid_only).map(&:first)
        cert_hashes.include?(sha1)
      end

      def get_cert_expiry(cert_id)
        output, status = execute("sudo su - #{Configuration['user']} -c \"security find-certificate -c '#{cert_id}' -p\"", true)
        raise OSUtilsError, "Failed to find cert expiry for #{cert_id}" if status.to_i != 0

        OpenSSL::X509::Certificate.new(output).not_after
      end

      def certificates_sha1(cert_name, keychain_path)
        output = execute("security find-certificate -a -Z -c \"#{cert_name}\" #{keychain_path} | grep ^SHA-1")
        output.split("\n").map { |line| line.split.last }
      end

      def add_trusted_cert(cert_file, keychain_path)
        output, status = execute("sudo security add-trusted-cert -d -r trustRoot -k #{keychain_path} #{cert_file}", true)
        raise "Unable to add trusted cert" if status != 0

        output
      end

      def delete_cert(cert_hash)
        BrowserStack.logger.info("Deleting expired cert: #{cert_hash}")
        output, status = execute("sudo su - #{Configuration['user']} -c \"security delete-certificate -Z #{cert_hash}\"", true)
        raise OSUtilsError, "Failed to delete cert for #{cert_hash}" if status.to_i != 0

        output
      end

      def ios_webkit_proxy_version
        execute("(ios_webkit_debug_proxy -V) 2>&1")
      end

      ## Wifi
      def get_wifi_interface
        execute("(networksetup -listallhardwareports | grep -A1 Wi\-Fi | grep Device | awk '{print $2}') 2>&1").to_s.strip
      end

      def get_network_services
        execute("(sudo networksetup -listallnetworkservices) 2>&1").to_s.strip
      end

      def check_and_disable_bluetooth_and_wifi_network_services
        services = get_network_services
        execute("sudo networksetup -setnetworkserviceenabled \"Bluetooth PAN\" off ") unless services.include? "*Bluetooth PAN"
        execute("sudo networksetup -setnetworkserviceenabled \"Wi-Fi\" off ") unless services.include? "*Wi-Fi"
      end

      def is_wifi_active?
        interface = get_wifi_interface
        return execute("ifconfig #{interface} | grep status | grep -wc active").strip.to_i == 1 unless interface.empty?

        false
      end

      def disable_wifi_interface
        interface = get_wifi_interface
        execute("networksetup -setairportpower #{get_wifi_interface} off") unless interface.empty?
      end

      def bluetooth_enabled?
        !execute("sudo kextstat -b com.apple.iokit.BroadcomBluetoothHostControllerUSBTransport | grep BroadcomBluetoothHostControllerUSBTransport").empty?
      end

      def disable_bluetooth
        execute("sudo kextunload -b com.apple.iokit.BroadcomBluetoothHostControllerUSBTransport")
      end

      def sleep_disabled?
        execute("pmset -g | grep -w sleep | grep -v displaysleep | grep -cw 0").strip.to_i == 1
      end

      def disable_sleep
        execute('sudo pmset -a sleep 0')
      end

      def file_compare(file1_path, file2_path)
        return false unless File.exists?(file1_path) && File.exists?(file2_path)

        file1 = File.read(file1_path)
        file2 = File.read(file2_path)
        file1.gsub(/\s/, '') == file2.gsub(/\s/, '')
      end

      def who_am_i
        Etc.getpwuid(Process.uid).name
      end

      def m1_machine?
        execute('uname -m').strip == 'arm64'
      end

      def macos_version
        execute('/usr/bin/sw_vers | grep ProductVersion').split[1]
      end

      def unarchive(zip_file_path, destination = "./")
        execute("unzip -q -o #{zip_file_path} -d #{destination}")
        $CHILD_STATUS.exitstatus
      end

      def remove_files_older_than(location, time_minutes)
        remove_files_command = "find #{location} -type f -mmin +#{time_minutes} -exec rm -rf -v {} \\;"
        execute(remove_files_command)
      end

      def wda_xcodebuild_running?(device)
        !execute("ps aux | grep #{device} | grep \"[x]codebuild\" | grep \"test-without-building\" | grep \"wda_derived_data\"").empty?
      end

      def kill_wda_xcodebuild(device)
        kill_xcodebuild_command = "ps aux | grep #{device} | grep \"[x]codebuild\" | grep \"test-without-building\" | grep \"wda_derived_data\" | awk '{print $2}' | sudo xargs kill -9"
        execute(kill_xcodebuild_command)
      end

      # Returns how many minutes process has been running
      def wda_xcodebuild_running_time(device)
        ps_aux_line = `ps aux | grep #{device} | grep \"[x]codebuild\" | grep \"test-without-building\" | grep \"wda_derived_data\"`
        #output eg: app             11342   0.0  0.4  4896792  60380   ??  Ss   12:23PM   0:15.19 /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild test-without-building -xcte..."

        return if ps_aux_line.empty?

        pid = ps_aux_line.split(' ')[1]
        process_running_time_hours = `ps -o etime= -p #{pid}`
        #output eg: "15:19\n"
        # fixme : this breaks in case the uptime of process is in minutes. as per the output eg. the `minutes` = 900 which is wrong.
        minutes = process_running_time_hours.split(':')[0].to_i * 60
        minutes += process_running_time_hours.split(':')[1].to_i
        minutes
      end

      def grep_process_pid(*grep_params)
        process_details = grep_process_details(*grep_params)
        return if process_details.empty?

        process_details.split[1].to_i
      end

      def remove_folders_older_than(location, time_minutes)
        remove_empty_dirs_command = "find #{location} -type d -mmin +#{time_minutes} -exec rm -rf -v {} \\;"
        execute(remove_empty_dirs_command)
      end

      def remove_folders_older_than_and_empty(location, time_minutes)
        remove_empty_dirs_command = "find #{location} -type d -mmin +#{time_minutes} -empty | xargs rm -rf"
        execute(remove_empty_dirs_command)
      end

      def get_file_mime_type(path_to_file)
        execute("file --b --mime-type #{path_to_file} | head -1").strip
      end

      def get_session_id(device)
        return "" unless File.exist? "#{@@server_config['state_files_dir']}/#{device}_session"

        begin
          session_info = JSON.parse(File.read("#{@@server_config['state_files_dir']}/#{device}_session"))
          session_info["live_session_id"] || session_info["automate_session_id"] || session_info["app_live_session_id"] || session_info["session_id"] || ""

        rescue
          ""
        end
      end

      # Return major iOS version of particular device.
      def device_ios_version(device)
        ios_version = `ideviceinfo -u #{device}`.scan(/ProductVersion: (.*)/) # Get iOS version from ideviceinfo
        return nil if ios_version.empty?

        ios_version = ios_version[0][0]
        # iOS 13.4 and above devices are hosted on Catalina machines, returning specific version for them
        # TODO: Make that return "14" if iOS 14 devices use the same setup when they'll be released
        return "13.4" if Gem::Version.new(ios_version) >= Gem::Version.new('13.4')

        ios_version.split('.')[0] # Get major version only
      end

      def get_attr_from_plist_file(full_path, attr_key)
        OSUtils.execute("defaults read #{full_path} #{attr_key}")
      end

      def kill_duplicate_stale_processes(process_name, process_identifier)
        pids = get_duplicate_process_pid(process_name, process_identifier)
        pids.each(&method(:kill_pid))
      end

      # Query devices containing qemu in their name, so we can be sure it's a VM
      def virtual_machine?
        system("ioreg -l | grep -iq qemu")
      end

      def shutdown_machine(time: :now, message: "System going down")
        system(%(sudo shutdown -h #{time} '#{message}'))
      end

      def get_video_duration(absolute_file_path)
        execute("#{FFPROBE} -i #{absolute_file_path} -show_format -v quiet | sed -n 's/duration=//p'").to_s.chomp
      end

      def lock_keychain_access(timeout = 10, &block)
        Utils.with_lock("#{@@server_config['state_files_dir']}/keychain_access", timeout, &block)
      rescue LockfileTimeoutError
        raise KeychainAccessTimeout, "Waiting for keychain access"
      end

      def grep_process_details(*grep_params)
        raise ArgumentError, "wrong number of arguments for grep_process_details, needs at least 1" if grep_params.empty?

        grep_cmd = grep_params.map { |grep_string| "grep #{grep_string}" }.join(" | ")
        execute("ps aux | #{grep_cmd} | grep -v grep")
      end

      def is_port_listening?(port)
        output = execute("sudo lsof -nP -iTCP:#{port} | grep LISTEN")
        !output.empty?
      end

      def list_port_process(port)
        lsof_output = execute("sudo lsof -i:#{port}")
        lsof_output.split("\n")
      end

      def get_cpu_consumed
        output = execute("top -l  2 | grep -E \"^CPU\" | tail -1 | awk '{ print $3 + $5 }'")
        output.strip.to_f
      end

      def get_free_memory
        output = execute("memory_pressure | tail -1 | awk '{split($0,a,\":\"); print a[2]}' | sed 's/%//'")
        output.strip.to_f
      end

      def get_system_load
        output = execute("uptime | awk '{split($0,a,\"averages:\"); print a[2]}'")
        output.strip.to_s
      end

      def get_system_resources

        { "cpu_used" => get_cpu_consumed, "free_memory" => get_free_memory, "load" => get_system_load }
      rescue
        {}

      end

      # Creates a directory if it doesn't exist
      def ensure_path_exists(path)
        FileUtils.mkdir_p(path) unless Dir.exist? path
        path
      end

      def is_arm?
        system("arch -arm64 true")
      end

      # Added this method to execute the shell commands safely to prevent injection attacks
      def safe_execute(command, args, return_status = false, timeout: nil, kill_after: 5)

        log_result_file = get_tmp_random_file_name
        BrowserStack.logger.info("[SAFE] File name for writing system command  output: #{log_result_file}")
        FileUtils.rm(log_result_file) if File.exist?(log_result_file) # For any reason if file is not deleted, then delete now
        timeout_command_list = [TIMEOUT_CMD, "-k#{kill_after}", timeout.to_s]
        args.collect!(&:to_s) # converting all elements in an array to string
        command_list = timeout.nil? ? [command, *args] : [*timeout_command_list, command, *args]
        BrowserStack.logger.info("[SAFE] Executing =====|#{command_list.join(' ')}|=====")
        system_result = system(*command_list, out: [log_result_file, 'a'], err: [log_result_file, 'a'])
        status = $CHILD_STATUS
        result = File.read(log_result_file)
        FileUtils.rm(log_result_file)
        BrowserStack.logger.info("[SAFE] Output of execution (first 1000 chars): =====|#{result[0..1000]}|=====. Status: #{status}")
        raise OSUtilsError, "[SAFE] Command #{command_list.join(' ')} timed out after #{timeout}s" if status.exitstatus == 124

        return_status ? [result, status.exitstatus] : result
      ensure
        FileUtils.rm(log_result_file) if File.exist?(log_result_file)
      end

      def get_tmp_random_file_name
        random_key = (0...50).map { ("a".."z").to_a[rand(26)] }.join
        "/tmp/#{random_key}"
      end

      def check_machine_load(log_file, counter_file, current_date)
        #keeping the load threshold value to be dynamic
        num_cores = `nproc`.strip.to_i
        load_threshold = num_cores * 1.0

        load_averages = `sysctl -n vm.loadavg`.split
        #Fetching 1min load average
        current_load = load_averages[1].to_f
        last_15_min_load = load_averages[3].to_f

        if current_load > load_threshold
          BrowserStack.logger.info("Host machine is at high load, logging metrics of the host machine")
          log_high_load(current_load, log_file, load_averages)
        else
          BrowserStack.logger.info("Host machine is not at high load")
        end

        if last_15_min_load > load_threshold
          BrowserStack.logger.info("Last 15 minutes load average is higher than the threshold, updating the counter")
          update_breach_counter(counter_file, current_date)
        end
      end

      def update_breach_counter(counter_file, current_date)
        counter_data = if File.exist?(counter_file)
                         file_content = File.read(counter_file)
                         file_content.empty? ? {} : JSON.parse(file_content)
                       else
                         {}
                       end

        # Update the counter data
        counter_data = { current_date => (counter_data[current_date] || 0) + 1 }

        File.write(counter_file, counter_data.to_json)
      end

      def log_high_load(current_load, log_file, load_averages)
        timestamp = Time.now.strftime("%Y%m%d_%H%M%S")

        last_5_min_load = load_averages[2].to_f
        last_15_min_load = load_averages[3].to_f

        processes = fetch_top_processes("-nrk 3", 10)
        mem_processes = fetch_top_processes("-nrk 4", 10)

        swap_usage = `sysctl vm.swapusage`.gsub(/^vm\.swapusage: /, '').strip
        disk_usage = fetch_disk_usage
        cpu_info = `top -l 1 -n 0 | grep "CPU usage"`.strip

        json_entry = {
          timestamp: Time.now.iso8601,
          current_load: current_load,
          last_5_min_load: last_5_min_load,
          last_15_min_load: last_15_min_load,
          top_10_cpu_processes: processes,
          top_10_mem_processes: mem_processes,
          swap_usage: swap_usage,
          disk_usage: disk_usage,
          cpu_info: cpu_info
        }.to_json

        write_to_log(json_entry)
      end

      def fetch_top_processes(sort_by, limit)
        `ps aux | sort #{sort_by} | head -n #{limit}`.split("\n").map do |line|
          fields = line.split
          {
            pid: fields[1],
            user: fields[0],
            cpu: fields[2],
            mem: fields[3],
            vsz: fields[4],
            rss: fields[5],
            tty: fields[6],
            command: fields[10..].join(" ")
          }
        end
      end

      def fetch_disk_usage
        `df -h | awk 'NR==1 || /\\/$/'`.split("\n").map do |line|
          fields = line.split
          {
            filesystem: fields[0],
            size: fields[1],
            used: fields[2],
            avail: fields[3],
            use_percent: fields[4],
            mounted_on: fields[5]
          }
        end
      end

      def write_to_log(json_entry)
        log_file = "#{@@server_config['logging_root']}/high_load.log"
        File.open(log_file, 'a') do |file|
          file.puts(json_entry)
        end
        BrowserStack.logger.info("Log file created at: #{log_file}")
      end
    end
  end
end
