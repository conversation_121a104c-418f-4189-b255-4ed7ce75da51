require_relative '../erb_binding'
require_relative '../../config/constants'
require_relative '../configuration'
require_relative '../custom_exceptions'
require_relative './time_recorder'
require_relative '../helpers/browserstack_app_helper'
require_relative '../models/device_state'
require 'tempfile'
require 'browserstack_logger'
require 'time'

module BrowserStack
  # Deprecated: Use ConfigurationProfilesManager instead
  class CFGUtilProfileManager
    include BrowserStack::TimeRecorder
    time_class_methods :run_profile_install_automation

    def initialize(device_id)
      @udid = device_id
      @device_state = DeviceState.new(device_id)
      @cfgutil = CFGUtil.new(udid: device_id)
      @server_config = BrowserStack::Configuration.new.all
      @device_config = begin
        JSON.parse(File.read(@server_config['config_json_file']))["devices"][device_id]
      rescue => e
        BrowserStack.logger.error "Unable to load device config: #{e.message} #{e.backtrace.join("\n")}"
        {}
      end
    end

    def run_profile_install_automation(device_id)
      BrowserStackAppHelper.run_ui_test(device_id, :install_downloaded_profile)
    end

    def download_and_install_profile(device_id, profile, profile_path)
      BrowserStack.logger.info "Installing #{profile} profile on device #{device_id}"
      @cfgutil.install_profile(profile_path: profile_path)
      BrowserStack.logger.info("#{profile} profile downloaded to device via cfgutil #{device_id}")
      run_profile_install_automation(device_id)
      BrowserStack.logger.info "#{profile} profile installed on device #{device_id}"
    rescue => e
      if e.message == "Profile already installed"
        BrowserStack.logger.info "Profile already installed, nothing to do."
        return
      end

      BrowserStack.logger.error "Couldn't install #{profile} profile on device #{device_id}: #{e.message} - #{e.backtrace.join("\n")}"
      raise StandardError, "Failed to install profile : #{e.message}"
    end

    def remove_profile(device_id, profile)
      BrowserStack.logger.info "Removing #{profile} profile on device #{device_id}"
      @cfgutil.remove_profile(profile_payload_id: profile)
      BrowserStack.logger.info "#{profile} profile removed from device #{device_id}"
    rescue => e
      BrowserStack.logger.error "Couldn't remove #{profile} profile: #{e.message} - #{e.backtrace.join("\n")}"
      raise StandardError, "Couldn't remove #{profile} profile: #{e.message}"
    end

    def generate_restrictions_profile_params
      data = {
        device: @udid,
        uuid: @server_config["mdm_profiles_required"]["Restrictions"]["uuid"],
        restrictions_payload_id: "restrictions-2.0.#{@udid}",
        profile_display_name: "Restrictions 2.0",
        disable_weather_app: !@device_config['device_name'].match(/iPhone/).nil?,
        disable_messaging_app: @device_config['device_name'].match(/iPhone/).nil?,
        disable_files_app: @device_config["device_version"].to_f < 13.0,
        disable_wallet_app: @device_config["device_version"].to_f < 13.0,
        disable_esim_modification: @device_config["device_version"].to_f >= 11.0,
        profile_installation_ui_disallowed: true,
        profile_payload_id: "restrictions-2.0.#{@udid}",
        enable_pwa: ExperimentsHelper.enable_pwa?(@device_config["device_version"]), # TODO: remove when we start supporting PWAs on all iOS versions
        enable_passcode_settings: false,
        enable_bluetooth_modification: false
      }
    end

    def generate_restrictions_profile
      data = generate_restrictions_profile_params
      temp_restrictions_profile = File.new("/tmp/restrictions_2.0_#{@udid}.mobileconfig", "w+")
      erb_file = @device_state.dedicated_device_file_present? ? "dedicated_restrictions.erb" : "restrictions.erb"
      temp_restrictions_profile.write(ERB.new(File.open(File.join(@server_config['templates_dir'], erb_file)).read).result(ErbBinding.new(data).get_binding))
      temp_restrictions_profile.close

      temp_restrictions_profile.path
    end

    def install_restrictions_profile
      download_and_install_profile(@udid, "restrictions-2.0.#{@udid}", generate_restrictions_profile)
    end

    def remove_restrictions_profile
      BrowserStack.logger.info "Removing restrictions-2.0.#{@udid} profile from device #{@udid}"
      remove_profile(@udid, "restrictions-2.0.#{@udid}")
    end
  end
end

if __FILE__ == $PROGRAM_NAME

  device_id = ARGV[0]
  cfgutil_profile_manager = BrowserStack::CFGUtilProfileManager.new(device_id)
  case ARGV[1]
  when 'generate_restrictions_profile'
    puts cfgutil_profile_manager.generate_restrictions_profile
  when 'install_restrictions_profile'
    cfgutil_profile_manager.install_restrictions_profile
  when 'remove_restrictions_profile'
    cfgutil_profile_manager.remove_restrictions_profile
  else
    BrowserStack.logger.error "Method #{ARGV[1]}, is not listed."
  end
end
