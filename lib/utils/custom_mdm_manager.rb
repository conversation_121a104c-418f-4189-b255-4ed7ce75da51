require_relative '../../lib/models/device_state'
require_relative '../../lib/models/ios_device'
require_relative '../../lib/wrappers/cfg_util'
require_relative './ios_mdm_service_client'
require_relative '../../server/device_manager'
require 'browserstack_logger'

module BrowserStack
  class CustomMDMManager  # rubocop:todo Metrics/ClassLength

    def initialize(device_id, logger)
      @device_id = device_id
      @logger = logger
      @device_state = DeviceState.new(device_id)
    end

    def manage_setup
      check_and_remove_setup if @device_state.custom_mdm_remove_setup_file_present?

      unless @device_state.dedicated_device_file_present? && @device_state.dedicated_cleanup_file_present?
        log(:info, "Cannot perform custom mdm setup on non-dedicated device")
        return false
      end

      check_and_perform_setup if @device_state.custom_mdm_perform_setup_file_present?
    rescue => e
      log(:error, "Failed in manage_setup for #{@device_id} #{e}")
      false
    end

    def check_and_perform_setup
      if self.class.is_custom_mdm_device?(@device_id)
        log(:info, "Custom MDM setup already done")
        @device_state.remove_custom_mdm_perform_setup_file
      else
        log(:info, "Performing Setup")
        PyMobileDevice::Profile.remove_profiles(@device_id, MICROMDM_ENROLLMENT_PROFILE_IDENTIFIER)
        log(:info, "MDM Profile Removed")
        configuration_profiles_manager = ConfigurationProfilesManager.new(@device_id, BrowserStack.logger)
        uses_cfgutil = configuration_profiles_manager.device_uses_cfgutil_managed_profiles?(verify_cfgutil: true)
        if uses_cfgutil
          configuration_profiles_manager.install_profile(:restrictions, install_via: :cfgutil)
          log(:info, "Installed restrictions using CFGUtil")
        else
          log(:info, "Device does not support cfgutil, could not install restrictions")
        end
        @device_state.touch_custom_mdm_file
        @device_state.remove_custom_mdm_perform_setup_file
        log(:info, "Custom MDM setup completed")
      end
      true
    end

    def reboot
      device_config = DeviceManager.device_configuration_check @device_id
      idevice = BrowserStack::IPhone.new(device_config, @device_id)
      idevice.reboot
      true
    rescue => e
      log(:error, "Failed in reboot for #{@device_id} #{e}")
      false
    end

    def check_and_remove_setup
      log(:info, "Removing Setup")

      # Enroll device to our MDM
      IosMdmServiceClient.configure
      IosMdmServiceClient.mdm_re_enrollment(@device_id, true)
      @device_state.remove_custom_mdm_file

      # Install Profiles
      iphone = BrowserStack::IPhone.new(BrowserStack::DeviceConf[@device_id], @device_id)
      iphone.install_configuration_profiles

      @device_state.remove_custom_mdm_remove_setup_file
      log(:info, "Custom MDM removal completed")
    end

    def backup_unlock_token
      return unless self.class.is_custom_mdm_device?(@device_id)

      log(:info, "Fetching unlock token")
      ios_device = BrowserStack::IosDevice.new(@device_id, self.class.to_s, BrowserStack.logger)
      unless ios_device.supervision_identities_exist_and_cfgutil_installed?
        log(:info, "Cannot fetch unlock token as cfgutil is not available")
        return false
      end

      cfgutil = CFGUtil.new(udid: @device_id)
      crt_path = ios_device.supervision_crt
      der_path = ios_device.supervision_der
      unlock_token = cfgutil.get_unlock_token(crt_path, der_path)
      log(:info, "Fetching unlock token success, saving to file")
      @device_state.write_to_unlock_token_file(unlock_token)
    rescue => e
      log(:error, "Failed to fetch unlock token: #{e}")
    end

    def clear_passcode_via_unlock_token
      return unless self.class.is_custom_mdm_device?(@device_id)

      if @device_state.unlock_token_file_present?
        log(:info, "Cleaning passcode using Unlock token")
        ios_device = BrowserStack::IosDevice.new(@device_id, self.class.to_s, BrowserStack.logger)
        crt_path = ios_device.supervision_crt
        der_path = ios_device.supervision_der
        CFGUtil.new(udid: @device_id).clear_passcode(File.join(STATE_FILES_DIR, "unlock_token_#{@device_id}"), crt_path, der_path)
        @device_state.remove_unlock_token_file

        # re-install restrictions profile to avoid popups ref - https://browserstack.atlassian.net/browse/MOBPC-341
        configuration_profiles_manager = ConfigurationProfilesManager.new(@device_id, BrowserStack.logger)
        if configuration_profiles_manager.profile_installed_via_cfgutil?(:restrictions, use_cache: false)
          log(:info, "Removing restrictions profile using CFGUtil")
          configuration_profiles_manager.remove_profile(:restrictions, nil, remove_via: :cfgutil)
        end
        log(:info, "Installing restrictions profile using CFGUtil")
        configuration_profiles_manager.install_profile(:restrictions, install_via: :cfgutil)
        log(:info, "Installed restrictions using CFGUtil")

        log(:info, "Passcode cleaned using Unlock token")
      else
        log(:info, "Cannot clean passcode as unlock token not present")
      end
    rescue => e
      log(:error, "Failed to clear passcode using unlock token: #{e}")
      raise "CFGUtil Clear passcode failed"
    end

    # rubocop:disable Naming/PredicateName
    def self.is_custom_mdm_device?(device_id)
      device_state = DeviceState.new(device_id)
      device_state.custom_mdm_file_present?
    end
    # rubocop:enable Naming/PredicateName

    def log(level, message)
      @logger.send(level.to_sym, "[CustomMDM] #{message}")
    end
  end
end
