require 'dotenv'
Dotenv.load('/usr/local/.browserstack/realmobile/.env')
require_relative '../models/ios_device'
require_relative '../../config/constants'
require_relative '../configuration'
require_relative './http_utils'
require_relative '../models/device_state'
require_relative '../helpers/experiments_helper'
require_relative '../erb_binding'
require_relative './custom_mdm_manager'

class ConfigurationProfilesGenerator #rubocop:todo Metrics/ClassLength
  def initialize(device, logger)
    @device_id = device
    @logger = logger
  end

  # Makes an HTTP request to /mdm/enroll and stores the response in a file
  # returns a path to that file (aka enrollment profile)
  def prepare_mdm_enrollment_profile
    @logger.info("Creating MDM profile...")
    mdm_enrollment_profile = File.new("/tmp/mdm_enrollment_profile_#{@device_id}.mobileconfig", "w+")
    enrollment_endpoint = "#{ios_device.mdm_server_url}/mdm/enroll"

    @logger.info("Fetching MDM configuration profile from: #{enrollment_endpoint}")
    response = BrowserStack::HttpUtils.make_get_request(enrollment_endpoint, 15)
    raise "Failed to fetch enrollment profile, response status: #{response.status.to_i}" unless response.status.to_i == 200

    @logger.info("MDM Profile fetched successfully, resp code: #{response.status.to_i}")

    mdm_enrollment_profile.write(response.body)
    mdm_enrollment_profile.close

    mdm_enrollment_profile.path
  end

  def generate_proxy_configuration_profile_params(profile_identifier, payload_id, uuid, payload_uuid, version = 1)
    data = {
      profile_identifier: profile_identifier,
      payload_id: payload_id,
      uuid: uuid,
      payload_uuid: payload_uuid,
      version: version,
      url: ios_device.proxy_pac_url,
      proxy_fallback_allowed: ios_device.device_version < Gem::Version.new('15.0')
    }

    @logger.info("Proxy profile params: #{data}")
    ERB.new(File.open(File.join(server_config['templates_dir'], "generic_pac_profile_plist.erb")).read).result(ErbBinding.new(data).get_binding)
  end

  # returns a path to configuration profile
  def prepare_proxy_configuration_profile(profile_identifier, payload_id, uuid, payload_uuid, version = 1)
    @logger.info("Creating Proxy configuration profile...")
    profile = File.new("/tmp/proxy_configuration_profile_#{@device_id}.mobileconfig", "w+")
    # Inject variables into ERB
    profile.write(generate_proxy_configuration_profile_params(profile_identifier, payload_id, uuid, payload_uuid, version))
    profile.close

    profile.path
  end

  def generate_restrictions_profile_params(profile_identifier, payload_id, uuid, payload_uuid, version = 1, flags = []) # rubocop:todo Metrics/AbcSize
    data = {
      device: @device_id,
      profile_identifier: profile_identifier,
      payload_id: payload_id,
      uuid: uuid,
      payload_uuid: payload_uuid,
      version: version,
      profile_display_name: "Restrictions",
      disable_weather_app: !device_config['device_name'].match(/iPhone/).nil?,
      disable_messaging_app: device_config['device_name'].match(/iPhone/).nil?,
      disable_files_app: device_config["device_version"].to_f < 13.0,
      disable_wallet_app: device_config["device_version"].to_f < 13.0,
      disable_contacts_app: device_config["device_version"].to_i != 13,
      disable_esim_modification: device_config["device_version"].to_f >= 11.0 && !device_state.esim_modification_file_present?,
      profile_installation_ui_disallowed: false,
      enable_pwa: ExperimentsHelper.enable_pwa?(device_config["device_version"]), # TODO: remove when we start supporting PWAs on all iOS versions
      enable_passcode_settings: flags.include?('enable_passcode_settings') || (device_state.dedicated_device_file_present? && !CustomMDMManager.is_custom_mdm_device?(@device_id)),
      enable_bluetooth_modification: flags.include?('enable_bluetooth_modification'),
      force_automatic_date_time: device_state.force_automatic_date_time_file_present?,
      disable_passwords_app: device_config["device_version"].to_f >= 18.0 && !flags.include?('enable_passwords_app'),
      disable_ios_18_apps: device_config["device_version"].to_f >= 18.0,
      is_dedicated_device: device_state.dedicated_device_file_present?,
      disable_siri: device_config["device_version"].to_i == 16 || device_config["device_version"].to_i >= 18
    }
    @logger.info("Restrictions profile params: #{data}")
    erb_file = device_state.dedicated_device_file_present? ? "dedicated_restrictions.erb" : "restrictions.erb"
    ERB.new(File.open(File.join(server_config['templates_dir'], erb_file)).read).result(ErbBinding.new(data).get_binding)
  end

  def prepare_restrictions_profile(profile_identifier, payload_id, uuid, payload_uuid, version = 1, flags = [])
    @logger.info("Creating Restrictions profile...")
    restrictions_profile = File.new("/tmp/restrictions_configuration_profile_#{@device_id}.mobileconfig", "w+")
    restrictions_profile.write(generate_restrictions_profile_params(profile_identifier, payload_id, uuid, payload_uuid, version, flags))
    restrictions_profile.close

    restrictions_profile.path
  end

  def generate_setup_assistant_configuration_profile_params(profile_identifier, payload_id, uuid, payload_uuid, version = 1)
    data = {
      device: @device_id,
      profile_identifier: profile_identifier,
      payload_id: payload_id,
      uuid: uuid,
      payload_uuid: payload_uuid,
      version: version
    }

    @logger.info("Setup Assistant profile params: #{data}")
    ERB.new(File.open(File.join(server_config['templates_dir'], "setup_assistant.erb")).read).result(ErbBinding.new(data).get_binding)
  end

  # returns a path to configuration profile
  def prepare_setup_assistant_configuration_profile(profile_identifier, payload_id, uuid, payload_uuid, version = 1)
    @logger.info("Creating Setup Assistant configuration profile...")
    profile = File.new("/tmp/setup_assistant_configuration_profile_#{@device_id}.mobileconfig", "w+")
    # Inject variables into ERB
    profile.write(generate_setup_assistant_configuration_profile_params(profile_identifier, payload_id, uuid, payload_uuid, version))
    profile.close

    profile.path
  end

  def generate_mitmproxy_root_certificate_profile_params(certificate_profile_name, profile_identifier, payload_id, uuid, payload_uuid, version = 1)
    mitmproxy_root_ca_cert_path = "/Users/<USER>'USER']}/#{server_config['mdm_profiles_required'][certificate_profile_name]['relative_path']}"
    base64_cert = Base64.encode64(File.read(mitmproxy_root_ca_cert_path))

    data = {
      device: @device_id,
      profile_name: certificate_profile_name,
      profile_identifier: profile_identifier,
      payload_id: payload_id,
      uuid: uuid,
      version: version,
      cert_data: base64_cert
    }

    @logger.info("MITM Proxy profile params: #{data}")
    ERB.new(File.open(File.join(@server_config['templates_dir'], "mitmproxy.erb")).read).result(ErbBinding.new(data).get_binding)
  end

  def prepare_mitmproxy_root_certificate_profile(certificate_profile_name, profile_identifier, payload_id, uuid, payload_uuid, version = 1)
    @logger.info("Creating MITM Proxy configuration profile for certificate: #{certificate_profile_name} ...")
    profile = File.new("/tmp/#{certificate_profile_name}_#{@device_id}.mobileconfig", "w+")
    # Inject variables into ERB
    profile.write(generate_mitmproxy_root_certificate_profile_params(certificate_profile_name, profile_identifier, payload_id, uuid, payload_uuid, version))
    profile.close

    profile.path
  end

  def generate_wifi_configuration_profile_params(root_payload_identifier, nested_payload_identifier, root_payload_uuid, nested_payload_uuid, version = 1)
    data = {
      device: @device_id,
      profile_identifier: root_payload_identifier,
      payload_identifier: nested_payload_identifier,
      uuid: root_payload_uuid,
      version: version,
      payload_uuid: nested_payload_uuid,
      ssid: server_config['static_conf']['ssid'],
      password: server_config['static_conf']['wifi_password']
    }

    @logger.info("WiFi profile params: #{data}")
    ERB.new(File.open(File.join(@server_config['templates_dir'], "wifi.erb")).read).result(ErbBinding.new(data).get_binding)
  end

  def generate_mobile_config_from_pfx(file_name, password, identifier, payload_id, uuid, payload_uuid, cert_data, version = 1)
    data = {
      device: @device_id,
      profile_identifier: identifier,
      payload_id: payload_id,
      uuid: uuid,
      payload_uuid: payload_uuid,
      version: version,
      password: password,
      cert_data: cert_data,
      display_name: file_name
    }
    ERB.new(File.open(File.join(server_config['templates_dir'], "pfx_to_mobileconfig.erb")).read).result(ErbBinding.new(data).get_binding)
  end

  def prepare_mobile_config(certificate_path, password, identifier, payload_id, uuid, payload_uuid, version = 1)
    # get the file name from the path
    file_name = File.basename(certificate_path)
    custom_certificate_folder = "/tmp/custom_certificates_#{@device_id}"
    FileUtils.mkdir_p(custom_certificate_folder)
    output_file_path = File.join(custom_certificate_folder, "custom_certificate.mobileconfig")

    # Encode the certificate file
    cert_data = Base64.encode64(File.open(certificate_path, "rb").read)
    cert_data = cert_data.delete("\n")
    # the PayloadContent after being 64bit encoded had to be modified in a way
    # that a single line has 52 characters suceeded with a new line \n character at the end of each line.
    # Insert line breaks for the cert_data
    j = 0
    cert_data.chars.each_with_index do |_, i|
      if i % 52 == 0 && i != 0
        cert_data.insert(i + j, "\n")
        j += 1
      end
    end

    generate_mobile_config_from_pfx(file_name, password, identifier, payload_id, uuid, payload_uuid, cert_data, version)
  end

  def generate_notifications_configuration_profile_params(profile_identifier, payload_id, uuid, payload_uuid, version = 1, notifications_configuration = {})
    if ios_device.enable_new_notifications_profile_flow?
      notifications_configuration["com.apple.TestFlight"] = {
        notifications_enabled: false
      }
    end

    if device_state.disable_geoguard_notifications_file_present?
      notifications_configuration[GEOGUARD_BUNDLE_ID.to_s] = {
        notifications_enabled: false
      }
    end

    data = {
      profile_identifier: profile_identifier,
      payload_id: payload_id,
      uuid: uuid,
      payload_uuid: payload_uuid,
      version: version,
      bundles: notifications_configuration
    }

    @logger.info("Notifications profile params: #{data}")
    ERB.new(File.open(File.join(server_config['templates_dir'], "notifications.erb")).read).result(ErbBinding.new(data).get_binding)
  end

  # returns a path to configuration profile
  def prepare_notifications_configuration_profile(profile_identifier, payload_id, uuid, payload_uuid, version = 1, notifications_configuration = {})
    @logger.info("Creating Notification configuration profile...")
    profile = File.new("/tmp/notification_configuration_profile_#{@device_id}.mobileconfig", "w+")
    # Inject variables into ERB
    profile.write(generate_notifications_configuration_profile_params(profile_identifier, payload_id, uuid, payload_uuid, version, notifications_configuration))
    profile.close

    profile.path
  end

  private

  def ios_device
    @ios_device ||= BrowserStack::IosDevice.new(@device_id,
                                                "ConfigurationProfilesGenerator",
                                                @logger)
  end

  def device_state
    @device_state ||= DeviceState.new(@device_id)
  end

  def server_config
    @server_config ||= BrowserStack::Configuration.new.all
  end

  def device_config
    @device_config ||= begin
      JSON.parse(File.read(server_config['config_json_file']))["devices"][@device_id]
    rescue
      {}
    end
  end
end
