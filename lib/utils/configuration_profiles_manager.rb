require_relative '../models/ios_device'
require_relative './ios_mdm_service_client'
require_relative '../configuration'
require_relative './zombie'
require_relative './cfgutil_profile_manager'
require_relative './configuration_profiles_generator'
require_relative '../helpers/data_report_helper'
require_relative '../models/device_state'
require 'pry'

#
#
# Delegates configuration related queries to either MDM or cfgutil
#
#
class ConfigurationProfilesManager # rubocop:todo Metrics/ClassLength
  # These class methods are added just for easy integration, once migrating off of MDM these should not be needed.
  # You can replace IosMdmServiceClient.X calls with ConfigurationProfilesManager.X calls.
  #
  # Eventually this flow should be used (once that profile is not managed by MDM for any device):
  #    manager = ConfigurationProfilesManager.new(device, logger)
  #    manager.remove_profile :proxy
  class << self
    # Used in Dedicated-Devices
    # This is feature flag which is turned on selectively for few customers.
    def unset_proxy(device, redis_client, logger)
      manager = ConfigurationProfilesManager.new(device, logger)
      manager.remove_profile(:proxy, nil, remove_via: :automatic)
    end
  end

  attr_reader :device_id

  def initialize(device, logger)
    BrowserStack::Zombie.configure
    BrowserStack::IosMdmServiceClient.configure
    @device_id = device
    @logger = logger
    @ios_device = BrowserStack::IosDevice.new(@device_id,
                                              "ConfigurationProfilesManager",
                                              logger)
  end

  # For most devices this will be unchanged.
  #
  # returns an object of profile objects:
  # {
  #   :proxy: {
  #     "type": "com.apple.proxy.http.global
  #   },
  #   .
  #   .
  # }
  def mdm_managed_profiles
    # These profiles are present in all prod devices (before migration)
    mdm_profiles_required = server_config['mdm_profiles_required']
    cfgutil_profiles_required = cfgutil_managed_profiles
    # From all the MDM profiles, remove the profiles which will be managed using cfgutil
    new_mdm_profiles_required = {}
    mdm_profiles_required.each do |profile, data|
      next if cfgutil_profiles_required.keys.include?(profile.downcase.to_sym)

      new_mdm_profiles_required[profile.downcase.to_sym] = data
    end

    new_mdm_profiles_required.delete(:notifications) unless @ios_device.enable_new_notifications_profile_flow?

    new_mdm_profiles_required
  end

  def mdm_installed_profiles
    BrowserStack::IosMdmServiceClient.get_installed_profiles(@device_id)
  end

  def device_uses_cfgutil_managed_profiles?(verify_cfgutil: false)
    device_enabled_for_new_provisioning_flow = @ios_device.enable_new_provisioning_flow?
    return false unless device_enabled_for_new_provisioning_flow

    return true unless verify_cfgutil

    supervision_identities_exist_and_cfgutil_installed = @ios_device.supervision_identities_exist_and_cfgutil_installed?
    return false unless supervision_identities_exist_and_cfgutil_installed

    supervision_identities_correct = nil
    supervision_identities_checked_file_present = device_state.supervision_identities_checked_file_present?
    if supervision_identities_checked_file_present
      supervision_identities_correct = device_state.read_supervision_identities_checked_file.to_s.strip == "true"
    else
      supervision_identities_correct = @ios_device.supervision_identities_correct?
      device_state.write_to_supervision_identities_checked_file(supervision_identities_correct.to_s)
    end

    supervision_identities_correct
  rescue => e
    @logger.error "Failed to check if device_uses_cfgutil_managed_profiles?, error: #{e}, #{e.backtrace.join("\n")}"
    error = e.message
    false
  ensure
    if !error.nil? || ((supervision_identities_exist_and_cfgutil_installed == false || supervision_identities_correct == false) && !supervision_identities_checked_file_present)
      data_reporter.report({
        "action" => "device_uses_cfgutil_managed_profiles",
        "error" => error,
        "supervision_identities_exist_and_cfgutil_installed" => supervision_identities_exist_and_cfgutil_installed,
        "supervision_identities_correct" => supervision_identities_correct
      })
    end
  end

  # For most devices this will be unchanged.
  #
  # returns an object of profile objects:
  # {
  #   :restrictions: {
  #     :prefix: "proxy.",
  #     :latest_version: 1
  #   },
  #   .
  #   .
  # }
  def cfgutil_managed_profiles
    if device_uses_cfgutil_managed_profiles?(verify_cfgutil: true)
      temp_profiles = server_config[:cfgutil_profiles_required] || {}
      temp_profiles.delete(:notifications) unless @ios_device.enable_new_notifications_profile_flow?

      temp_profiles
    else
      {}
    end
  end

  def cfgutil_installed_profiles
    @ios_device.installed_configuration_profiles
  end

  def mitmproxy_certificate_profile_name(profile_key)
    case profile_key
    when :mitmproxy_root_certificate
      "MitmProxy_Root_Certificate"
    when :mitmproxy_5_root_certificate
      "MitmProxy_5_Root_Certificate"
    end
  end

  def profile_identifier(profile_key, medium)
    return MICROMDM_ENROLLMENT_PROFILE_IDENTIFIER if profile_key == :MDM
    return server_config[:cfgutil_profiles_required][profile_key][:prefix] + device_id if medium == :cfgutil

    case profile_key
    when :proxy
      "mdm.#{@device_id}"
    when :restrictions
      "mdm.restrictions.#{@device_id}"
    when :"setup assistant"
      "mdm.setup_assistant.#{@device_id}"
    when :mitmproxy_root_certificate, :mitmproxy_5_root_certificate
      "mdm.mitmproxy#{server_config['mdm_profiles_required'][mitmproxy_certificate_profile_name(profile_key)]['payload_identifier_suffix']}.#{@device_id}"
    when :pfx_certificate
      "mdm.pfx_certificate.#{@device_id}"
    when :notifications
      "com.browserstack.notifications.#{server_config['mdm_profiles_required']['Notifications']['uuid']}"
    end
  end

  def profile_uuid(profile_key)
    server_config[:cfgutil_profiles_required][profile_key][:prefix] + device_id + ".#{server_config[:cfgutil_profiles_required][profile_key][:latest_version]}"
  end

  # Returns:
  # {
  #        "identifier": "com.github.micromdm.micromdm.enroll",
  #        "version": 1,
  #        "displayName": "Enrollment Profile"
  # }
  def installed_profile_details(profile_key, use_cache: false)
    return false unless device_uses_cfgutil_managed_profiles?

    @ios_device.fetch_installed_profile(profile_identifier(profile_key, :cfgutil), cached: use_cache)
  end

  # Use cache to speed up execution, should use only when start session
  # In cleanup/device_check always pass use_cache as false
  def profile_installed_via_cfgutil?(profile_key, use_cache: false)
    return false unless device_uses_cfgutil_managed_profiles?

    !installed_profile_details(profile_key, use_cache: use_cache).nil?
  end

  # Removes a profile from the device ( Supports both MDM and CFGUTIL )
  # profile_key: Display Name of the profile ( downcased and symbolized ), Useful if identifier is nil, otherwise used only for logging purposes.
  # identifier:
  #  Root Level PayloadIdentifier of the profile. This can be null if you are unsure of identifier of that profile.
  #  In that case ConfigurationProfilesManager#profile_identifier will be used to calculate identifier of the profile
  #  However, its recommended to send identifier, especially when calling this method from ConfigurationProfilesEnforcer#enforce_configuration_profiles
  # remove_via: Allowed values: :mdm, :cfgutil, and :automatic ( Don't use :automatic from cleanup, or device check ).
  # If passed remove_via as :automatic, it decides to remove profile via :mdm or :cfgutil at runtime, depending on how it was installed previously.
  def remove_profile(profile_key, identifier, options = {}, remove_via:)
    start_time = Time.now.to_i

    @logger.info("Attempting to remove_profile: #{profile_key} from device: #{@device_id} with identifier: #{identifier}, remove_via: #{remove_via} and options: #{options}")
    if remove_via == :automatic
      remove_via = :mdm
      remove_via = :cfgutil if profile_installed_via_cfgutil?(profile_key)
      @logger.info "Updated remove_via to: #{remove_via}"
    end

    if identifier.nil?
      identifier = profile_identifier(profile_key, remove_via)
      @logger.info "Updated identifier to: #{identifier}"
    end

    if remove_via == :cfgutil
      @ios_device.remove_profile(identifier)
    else
      BrowserStack::IosMdmServiceClient.remove_profile(@device_id, profile_key.to_s, identifier)
    end

    @logger.info("Removed profile: #{profile_key} from device: #{@device_id} with identifier: #{identifier}, remove_via: #{remove_via}, options: #{options}!")
  rescue => e
    error = e.message
    raise e
  ensure
    time_taken = Time.now.to_i - start_time
    data_reporter.report({
      "action" => "remove_profile",
      "profile_key" => profile_key,
      "identifier" => identifier,
      "remove_via" => remove_via,
      "time_taken" => time_taken,
      "error" => error
    })
  end

  # Installs a profile on the device ( Supports both MDM and CFGUTIL )
  # profile_key: Display Name of the profile ( downcased and symbolized )
  # options: Any extra parameters that needs to be sent, for example, used for sending flags to restrictions profile.
  # install_via: Allowed values: :mdm, :cfgutil, and :automatic ( Don't use :automatic from cleanup, or device check ).
  # If passed install_via as :automatic, it decides to install profile via :mdm or :cfgutil at runtime, depending on how it was installed previously.
  def install_profile(profile_key, options = {}, install_via:) # rubocop:todo Metrics/MethodLength
    start_time = Time.now.to_i

    @logger.info("Attempting to install profile: #{profile_key} on device: #{@device_id}, with install_via: #{install_via} and options: #{options}")
    if install_via == :automatic
      install_via = :mdm
      install_via = :cfgutil if profile_installed_via_cfgutil?(profile_key)
      @logger.info "Updated install_via to: #{install_via}"
    end

    case profile_key
    when :MDM
      enroll_device_to_mdm
    when :proxy
      install_proxy_profile(install_via: install_via)
    when :restrictions
      install_restrictions_profile(options[:flags] || [], install_via: install_via)
    when :"setup assistant"
      install_setup_assistant_profile(install_via: install_via)
    when :mitmproxy_root_certificate, :mitmproxy_5_root_certificate
      install_mitmproxy_root_certificate(profile_key, install_via: install_via)
    when :pfx_certificate
      install_custom_pfx_certificate(options[:certificate_path], options[:password], install_via: install_via)
    when :notifications
      install_notifications_profile(options[:notifications_configuration] || {}, install_via: install_via)
    else
      raise "install_profile method not yet implemented for: #{profile_key}"
    end
    @logger.info("Installed profile: #{profile_key} on device: #{@device_id}, with install_via: #{install_via} and options: #{options}!")
  rescue CFGUtil::ProfileAlreadyExists => e
    error = e.message
    raise "#{profile_key} installation failed via cfgutil (it already exists), not sure about next steps"
  rescue => e
    error = e.message
    raise e
  ensure
    time_taken = Time.now.to_i - start_time
    data_reporter.report({
      "action" => "install_profile",
      "profile_key" => profile_key,
      "install_via" => install_via,
      "time_taken" => time_taken,
      "error" => error
    })
  end

  def prepare_wifi_configuration_profile
    @logger.info("Generating WiFi profile")
    root_payload_identifier = "#{server_config[:wifi_profile_data][:prefix]}#{device_id}"   # root PayloadIdentifier -> payload_identifier                      - identifier
    nested_payload_identifier = "#{root_payload_identifier}.payload"                        # nested PayloadIdentifier -> payload_identifier + .payload         - payload_id
    version = server_config[:wifi_profile_data][:latest_version]
    root_payload_uuid = "#{root_payload_identifier}.#{version}"                             # root PayloadUUID -> payload_identfier + version                   - uuid
    nested_payload_uuid = "#{root_payload_uuid}.payload"                                    # nested PayloadUUID -> payload_uuid + .payload                     - payload_uuid

    BrowserStack.logger.info("My data: identifier: #{root_payload_identifier}, payload_id: #{nested_payload_identifier}, uuid: #{root_payload_uuid}, payload_uuid: #{nested_payload_uuid}, version: #{version}")
    plist_data = configuration_profiles_generator.generate_wifi_configuration_profile_params(root_payload_identifier, nested_payload_identifier, root_payload_uuid, nested_payload_uuid, version)
    wifi_profile = File.new("/tmp/wifi_profile_#{@device_id}.mobileconfig", "w+")

    wifi_profile.write(plist_data)
    wifi_profile.close

    wifi_profile.path
  end

  private

  def data_reporter
    @data_reporter ||= DataReportHelper.new("configuration-profiles-manager", device: @device_id)
  end

  def install_restrictions_profile(flags, install_via:)
    @logger.info("Install restrictions profile with flags: #{flags} via #{install_via} on device: #{@device_id}")
    if install_via == :cfgutil
      @logger.info("Install restrictions profile via CFGUTIL")
      identifier = profile_identifier(:restrictions, :cfgutil)
      payload_id = "#{identifier}.payload"
      uuid = profile_uuid(:restrictions)
      payload_uuid = "#{uuid}.payload"
      version = 1

      restrictions_profile_path = configuration_profiles_generator.prepare_restrictions_profile(identifier, payload_id, uuid, payload_uuid, version, flags)
      @logger.info("Prepare mobileconfig to install at: #{restrictions_profile_path}")
      @ios_device.install_profile(identifier, restrictions_profile_path)
    else
      @logger.info("Install restrictions profile via MDM")
      identifier = profile_identifier(:restrictions, :mdm)
      payload_id = server_config['mdm_restrictions_profile_payload_id']
      uuid = server_config["mdm_profiles_required"]["Restrictions"]["uuid"]
      payload_uuid = uuid
      version = 1

      plist = configuration_profiles_generator.generate_restrictions_profile_params(identifier, payload_id, uuid, payload_uuid, version, flags)
      BrowserStack::IosMdmServiceClient.set_restrictions(@device_id, plist)
    end

    @logger.info("Device #{@device_id} should have restrictions profile now")
  end

  def install_custom_pfx_certificate(certificate_path, password, install_via:)
    @logger.info("Installing mobile config: #{certificate_path} via #{install_via} on device: #{@device_id}")
    if install_via == :cfgutil
      raise "Mobile Config installation via CFGUTIL is not supported"
    else
      @logger.info("Install mobile config via MDM")
      identifier = profile_identifier(:pfx_certificate, :mdm)
      payload_id = "#{identifier}.payload"
      uuid = server_config["mdm_pfx_certificate_profile_rooted_payload_uuid"]
      payload_uuid = "#{uuid}.payload"
      version = 1
      plist = configuration_profiles_generator.prepare_mobile_config(certificate_path, password, identifier, payload_id, uuid, payload_uuid, version)
      IosMdmServiceClient.install_profile(@device_id, "customCertificate", plist)
    end

    @logger.info("Device #{@device_id} should have custom certificate installed now!!")
  end

  def install_proxy_profile(install_via:)
    @logger.info("Installing Proxy profile via #{install_via} on device: #{@device_id}")
    if install_via == :cfgutil
      @logger.info("Install proxy profile via CFGUTIL")
      identifier = profile_identifier(:proxy, :cfgutil)
      payload_id = "#{identifier}.payload"
      uuid = profile_uuid(:proxy)
      payload_uuid = "#{uuid}.payload"
      version = 1

      mobileconfig_path = configuration_profiles_generator.prepare_proxy_configuration_profile(identifier, payload_id, uuid, payload_uuid, version)
      @logger.info("Prepared mobileconfig to install at: #{mobileconfig_path}")
      @ios_device.install_profile(identifier, mobileconfig_path)
    else
      @logger.info("Install proxy profile via MDM")
      identifier = profile_identifier(:proxy, :mdm)
      payload_id = server_config['pac_profile_payload_id']
      uuid = @device_id
      payload_uuid = uuid
      version = 1

      plist = configuration_profiles_generator.generate_proxy_configuration_profile_params(identifier, payload_id, uuid, payload_uuid, version)
      BrowserStack::IosMdmServiceClient.set_proxy(@device_id, plist)
    end

    @logger.info("Device #{@device_id} should have Proxy profile now")
  end

  def install_setup_assistant_profile(install_via:)
    @logger.info("Installing Setup Assistant profile via #{install_via} on device: #{@device_id}")
    if install_via == :cfgutil
      @logger.info("Install Setup Assistant profile via CFGUTIL")
      identifier = profile_identifier(:"setup assistant", :cfgutil)
      payload_id = "#{identifier}.payload"
      uuid = profile_uuid(:"setup assistant")
      payload_uuid = "#{uuid}.payload"
      version = 1

      mobileconfig_path = configuration_profiles_generator.prepare_setup_assistant_configuration_profile(identifier, payload_id, uuid, payload_uuid, version)
      @logger.info("Prepared mobileconfig to install at: #{mobileconfig_path}")
      @ios_device.install_profile(identifier, mobileconfig_path)
    else
      @logger.info("Install Setup Assistant profile via MDM")
      identifier = profile_identifier(:"setup assistant", :mdm)
      payload_id = server_config['mdm_setup_assistant_profile_payload_id']
      uuid = server_config['mdm_profiles_required']["Setup Assistant"]["uuid"]
      payload_uuid = uuid
      version = 1

      plist = configuration_profiles_generator.generate_setup_assistant_configuration_profile_params(identifier, payload_id, uuid, payload_uuid, version)
      BrowserStack::IosMdmServiceClient.install_profile(@device_id, "Setup Assistant", plist)
    end

    @logger.info("Device #{@device_id} should have Setup Assistant profile now")
  end

  def install_mitmproxy_root_certificate(profile_key, install_via:)
    @logger.info("Installing MITM Proxy Root Certificate profile with key: #{profile_key} via #{install_via} on device: #{@device_id}")
    certificate_profile_name = mitmproxy_certificate_profile_name(profile_key)
    if install_via == :cfgutil
      identifier = profile_identifier(profile_key, :cfgutil)
      payload_id = "#{identifier}.payload"
      uuid = profile_uuid(profile_key)
      payload_uuid = "#{uuid}.payload"
      version = server_config[:cfgutil_profiles_required][profile_key][:latest_version]

      mobileconfig_path = configuration_profiles_generator.prepare_mitmproxy_root_certificate_profile(certificate_profile_name, identifier, payload_id, uuid, payload_uuid, version)
      @logger.info("Prepared mobileconfig to install at: #{mobileconfig_path}")
      @ios_device.install_profile(identifier, mobileconfig_path)
    else
      identifier = profile_identifier(profile_key, :mdm)
      payload_id = identifier
      uuid = server_config["mdm_profiles_required"][certificate_profile_name]["uuid"]
      payload_uuid = uuid
      version = 1

      plist = configuration_profiles_generator.generate_mitmproxy_root_certificate_profile_params(certificate_profile_name, identifier, payload_id, uuid, payload_uuid, version)
      BrowserStack::IosMdmServiceClient.install_mitmproxy_certificate(@device_id, plist)
    end

    @logger.info("Device #{@device_id} should have MITM Proxy Root Certificate profile with key: #{profile_key} now")
  end

  def install_notifications_profile(notifications_configuration, install_via:)
    @logger.info("Installing Notifications profile via #{install_via} on device: #{@device_id}")
    if install_via == :cfgutil
      @logger.info("Install notifications profile via CFGUTIL")
      identifier = profile_identifier(:notifications, :cfgutil)
      payload_id = "#{identifier}.payload"
      uuid = profile_uuid(:notifications)
      payload_uuid = "#{uuid}.payload"
      version = 1

      mobileconfig_path = configuration_profiles_generator.prepare_notifications_configuration_profile(identifier, payload_id, uuid, payload_uuid, version, notifications_configuration)
      @logger.info("Prepared mobileconfig to install at: #{mobileconfig_path}")
      @ios_device.install_profile(identifier, mobileconfig_path)
    else
      @logger.info("Install notifications profile via MDM")
      identifier = profile_identifier(:notifications, :mdm)
      payload_id = server_config['mdm_notifications_profile_payload_id']
      uuid = server_config['mdm_profiles_required']["Notifications"]["uuid"]
      payload_uuid = uuid
      version = 1

      plist = configuration_profiles_generator.generate_notifications_configuration_profile_params(identifier, payload_id, uuid, payload_uuid, version, notifications_configuration)
      BrowserStack::IosMdmServiceClient.install_profile(@device_id, "Notifications", plist)
    end

    @logger.info("Device #{@device_id} should have Notifications profile now")
  end

  # TODO: install all MDM profiles post this.
  def enroll_device_to_mdm
    mobileconfig_path = configuration_profiles_generator.prepare_mdm_enrollment_profile
    @ios_device.install_profile(profile_identifier(:MDM, :cfgutil), mobileconfig_path)
    @logger.info("Device should be enrolled with MDM now, will need to install remaining mdm profiles !")
  end

  def device_config
    @ios_device.device_config
  end

  def server_config
    @server_config ||= Configuration.new.all
  end

  def device_state
    @ios_device.device_state
  end

  def configuration_profiles_generator
    @configuration_profiles_generator ||= ConfigurationProfilesGenerator.new(device_id, @logger)
  end
end

# For testing
if __FILE__ == $PROGRAM_NAME
  m = ConfigurationProfilesManager.new(ARGV[0], Logger.new($stdout))
  binding.pry # rubocop:todo Lint/Debugger
end
