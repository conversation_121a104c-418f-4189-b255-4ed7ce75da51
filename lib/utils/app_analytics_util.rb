module AppAnalyticsUtil
  @app_analytics_store = {}
  SPECIAL_APPS = ["app_store", "test_flight"]

  def self.set_app_source_and_hashed_id(bundle_id, session_id, send_to_eds=false, params={})
    data_hash = {
      apps_info: {
        app_hashed_id: '',
        app_source: ''
      }
    }
    if SPECIAL_APPS.include?(bundle_id)
      data_hash[:apps_info][:app_source] = bundle_id
    else
      data_hash[:apps_info][:app_hashed_id] = bundle_id
    end
    @app_analytics_store[session_id] = data_hash
    push_to_eds(session_id, params) if send_to_eds
  rescue => e
    BrowserStack.logger.info("Exception in set_app_source_and_hashed_id #{e.message}  #{e.backtrace.join('\n')}")
  end

  def self.instrument_time(instrument_key, time_in_ms, session_id, send_to_eds=false, params={})
    nested_keys = [session_id, :apps_info, :app_time_info]
    final_hash =  nested_keys.inject(@app_analytics_store) { |h, k| h[k] ||= {} }
    final_hash[instrument_key] = time_in_ms
    push_to_eds(session_id, params) if send_to_eds
  end

  def self.write_to_file(session_id, state_file_dir)
    Utils.write_to_file("#{state_file_dir}/al_app_analytics_#{session_id}.json", @app_analytics_store[session_id].to_json)
  rescue => e
    BrowserStack.logger.info("Exception in write_to_file #{e.message}  #{e.backtrace.join('\n')}")
  end

  def self.init_eds_default_value(session_id, params = {})
    Utils.send_to_eds({ session_id: session_id, app_analytics: { apps_info: [] } }, EdsConstants::APP_LIVE_TEST_SESSIONS, true, req_params: params)
  rescue => e
    BrowserStack.logger.info("Exception in init_eds_default_value #{e.message}  #{e.backtrace.join('\n')}")
  end

  def self.push_to_eds(session_id, params = {})
    Utils.send_to_eds({ session_id: session_id, app_analytics: @app_analytics_store[session_id] }, EdsConstants::APP_LIVE_TEST_SESSIONS, true, req_params: params)
  rescue => e
    BrowserStack.logger.info("Exception in push_to_eds #{e.message}  #{e.backtrace.join('\n')}")
  end
end
