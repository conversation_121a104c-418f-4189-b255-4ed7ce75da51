#!/usr/bin/ruby

#
# utils package for idevice related commonly used commands for Files
#

require_relative "./osutils"

class IdeviceFileUtils
  class << self
    def pull_folder(device_id, folder_path, bundle_id, destination)
      FileUtils.mkdir_p(destination)
      BrowserStack::OSUtils.execute("/usr/local/bin/gtimeout -s KILL 60 #{IOS_DEPLOY} --id #{device_id} --bundle_id #{bundle_id} --download='#{folder_path}' --to '#{destination}'")
    end

    def pull_all_photos(device_id, destination)
      FileUtils.mkdir_p(destination)
      BrowserStack::OSUtils.execute("/usr/local/bin/gtimeout -s KILL 60 #{IOS_DEPLOY} --id #{device_id} -f -w/DCIM/ --to '#{destination}'")
    end

    def remove_file(device_id, file_path, bundle_id)
      BrowserStack::OSUtils.execute("/usr/local/bin/gtimeout -s KILL 20 #{IOS_DEPLOY} --id #{device_id} --bundle_id #{bundle_id} --rm '#{file_path}'")
    end

    def add_file(device_id, filename, file_path, bundle_id, timeout: 20, return_status: false)
      BrowserStack::OSUtils.execute("/usr/local/bin/gtimeout -s KILL #{timeout} #{IOS_DEPLOY} --id #{device_id} --bundle_id #{bundle_id} --upload '#{filename}' --to '#{file_path}'", return_status)
    end

    def list_files(device_id, dir, bundle_id, timeout: 20)
      result = BrowserStack::OSUtils.execute("/usr/local/bin/gtimeout -s KILL #{timeout} #{IOS_DEPLOY} --id #{device_id} --bundle_id #{bundle_id} -l '#{dir}'")
      result = result.split("\n")
      result.select { |e| e.start_with?(dir) }
    end

    def truncate_folder(device_id, folder_path, bundle_id)
      BrowserStack::OSUtils.execute("/usr/local/bin/gtimeout -s KILL 20 #{IOS_DEPLOY} --id #{device_id} --bundle_id #{bundle_id} --rmtree '#{folder_path}'")
    end

    def get_list_of_preloaded_media(device_id)
      result = BrowserStack::OSUtils.execute("/usr/local/bin/gtimeout -s KILL 20 #{IOS_DEPLOY} --id #{device_id} -f -lDCIM")
      result = result.split("\n")
      result.select { |e| (e.start_with?("DCIM/") && e.include?("APPLE")) }
    end
  end
end
