require 'browserstack_logger'
require 'date'
require 'json'
require_relative 'pymobiledevice'
require_relative 'push_to_zombie'
require_relative 'idevice_utils'
require_relative '../models/device_state'
require_relative '../../config/constants'
require_relative '../helpers/device_sim_helper'

module IosWatcherActions
  # moving actions to be performed on watcher events here to reduce clutter in main module
  def self.log_event(line, device_id)
    log_file = "#{LOGGING_DIR}/ioswatcher_logs_#{device_id}.log"
    File.write(log_file, "#{line}\n", mode: 'a+')
  end

  def self.log_launched_app(watcher_event, device_id)
    detected_apps_file = "#{IOS_WATCHER_DETECTED_APPS_FILE_PREFIX}_#{device_id}"
    app = watcher_event["displayID"]
    state = watcher_event["state_description"].downcase
    File.write(detected_apps_file, "#{app}\n", mode: 'a+') if state.include?('foreground')
  end

  def self.handle_action(watcher_event, device_id)
    state_description = watcher_event['state_description']
    displayID = watcher_event['displayID']

    if state_description == IOS_WATCHER_RESTRICTED_ACTIONS[:end_call][:state_description] && displayID == IOS_WATCHER_RESTRICTED_ACTIONS[:end_call][:displayID]
      BrowserStack.logger.info("IosWatcher: Outgoing call detected, ending call...")
      sleep 1 # this sleep is added because sometimes watcher detects call too fast, and locks the device instead
      DeviceSIMHelper.new(device_id).end_call(true)
    end
  end

  def self.handle_disconnect(device_id, session_id)
    IosWatcher.stop_capture(device_id)
    BrowserStack.logger.info("IosWatcher: Checking if device is on USB")
    device_state = DeviceState.new(device_id)
    sleep 1
    if !IdeviceUtils.ideviceinfo(device_id, "ProductType").first.downcase.start_with?('error:')
      BrowserStack.logger.info("IosWatcher: Device is back on usb")
      IosWatcher.start_capture(device_id, session_id, true)
      zombie_push('ios', "ios_watcher_midsession_disconnect", '', '', '', device_id, session_id)
    else
      device_state.touch_watcher_unreliable_off_usb_file
    end
  end
end

module IosWatcher
  def self.start_capture(device_id, session_id, skip_clear = false)
    start_time = Time.now.to_f
    IosWatcher.stop_capture(device_id) # check and kill any previous running pid
    IosWatcher.clear_logs(device_id) unless skip_clear

    stdin, stdout, = PyMobileDevice::Developer::Accessibility.notifications(device_id)
    stdin.close
    pid = fork do
      stdout.each_line do |line|
        IosWatcher.handle(line, device_id, session_id)
      end
    end
    Process.detach(pid)
    IosWatcherActions.log_event("[#{session_id}][#{DateTime.now}]: Watcher started", device_id)
    device_state = DeviceState.new(device_id)
    device_state.write_to_watcher_start_time_file(Time.now.to_f - start_time)
    device_state.touch_end_call_optimised_file
  rescue => e
    err_msg = "Error in starting ios watcher: #{e}"
    BrowserStack.logger.error(err_msg)
    IosWatcherActions.log_event("[#{session_id}][#{DateTime.now}]: #{err_msg}", device_id)
    DeviceState.new(device_id).touch_watcher_unreliable_file
    zombie_push('ios', "ios_watcher_start_failed", e, '', '', device_id, session_id)
  end

  def self.stop_capture(device_id)
    `ps aux | grep pymobiledevice3 | grep 'accessibility notifications' | grep #{device_id} | awk '{print $2}' | xargs kill`
  end

  def self.clear_logs(device_id)
    detected_apps_file = "#{IOS_WATCHER_DETECTED_APPS_FILE_PREFIX}_#{device_id}"
    File.write(detected_apps_file, '')
  end

  def self.apps_opened_during_session_set(device_id)
    detected_apps_file = "#{IOS_WATCHER_DETECTED_APPS_FILE_PREFIX}_#{device_id}"
    return Set.new unless File.exist?(detected_apps_file) # empty set

    File.read(detected_apps_file).split("\n").to_set
  end

  def self.handle(line, device_id, session_id)
    # rubocop:disable Layout/LineLength
    # line format: 2024-02-07 07:36:38 mobile-185-255-127-50.browserstack.com pymobiledevice3.cli.developer[78056] INFO {'state': 8, 'state_description': 'Foreground Running', 'elevated_state_description': 'Foreground Running', 'displayID': 'com.apple.SafariViewService', 'mach_absolute_time': 1879948109365, 'appName': 'Safari', 'execName': 'SafariViewService', 'elevated_state': 8, 'timestamp': 1707291398.8371038, 'name': 'SBApplicationNotificationStateChanged', 'pid': 10282}
    # parsing everything inside {} gives us a ruby hash with following schema
    #   "state": int, maps to state of app
    #   "state_description": string, description of state
    #   "elevated_state_description": string, description of state
    #   "displayID": string, bundle id of app
    #   "mach_absolute_time"=>1879948109365, int, no idea
    #   "appName": string, name of app. Sometimes empty if app is a plugin
    #   "execName": string, no idea
    #   "elevated_state": int, same as state perhaps
    #   "timestamp": float, timestamp of notification
    #   "name"=> string, name of event
    #   "pid": int, pid of the app running on ios
    # rubocop:enable Layout/LineLength
    IosWatcherActions.log_event("[#{session_id}][#{DateTime.now}]: #{line}", device_id)
    if line.include?('Device was disconnected')
      IosWatcherActions.handle_disconnect(device_id, session_id)
      return
    end

    line = line.match("{.*}")[0]
    converted_line = line.gsub("'", '"')
    begin
      watcher_event = JSON.parse(converted_line)
    rescue JSON::ParserError
      # found issue with McDonald's app, where McDonald's gets converted to McDonald"s, leading to json parse error
      # trying a different parser in such cases
      converted_line = line.gsub("{'", '{"').gsub("'}", '"}').gsub("':", '":').gsub(" '", ' "').gsub("',", '",')
      watcher_event = JSON.parse(converted_line)
    end
    IosWatcherActions.log_launched_app(watcher_event, device_id)
    IosWatcherActions.handle_action(watcher_event, device_id)
  rescue => e
    err_msg = "Error in ios watcher handle logs: #{e}"
    BrowserStack.logger.error(err_msg)
    IosWatcherActions.log_event("[#{session_id}][#{DateTime.now}]: #{err_msg}", device_id)
    device_state = DeviceState.new(device_id)
    unless device_state.watcher_unreliable_file_present?
      # to prevent multiple zombie push
      device_state.touch_watcher_unreliable_file
      zombie_push('ios', "ios_watcher_handle_failed", e, '', '', device_id, session_id)
    end
    IosWatcher.stop_capture(device_id) # stop it right now
  end

  def self.record_metrics(device_id, device_version, session_id)
    device_state = DeviceState.new(device_id)
    if device_state.manual_cleanup_file_present?
      BrowserStack.logger.info('Not recording watcher metrics for manual cleanup')
      return
    end

    if device_state.watcher_unreliable_off_usb_file_present?
      zombie_push('ios', "ios_watcher_off_usb", '', '', '', device_id, session_id)
      return
    end

    if device_state.watcher_unreliable_file_present?
      zombie_push('ios', "ios_watcher_unreliable", '', device_version, '', device_id, session_id)
      return
    end

    unless device_state.watcher_start_time_file_present?
      # session failure or cleanup retry
      BrowserStack.logger.info('Not recording watcher metrics as start file is missing')
      return
    end

    apps_detected = IosWatcher.apps_opened_during_session_set(device_id)
    zombie_push('ios', "ios_watcher_reliable", apps_detected.length, device_version, apps_detected.to_a.to_s, device_id, session_id)
    watcher_start_time = device_state.read_watcher_start_time_file
    zombie_push('ios', "ios_watcher_start_time", '', device_version, watcher_start_time, device_id, session_id)
    device_state.remove_watcher_start_time_file # don't push again
  end
end
