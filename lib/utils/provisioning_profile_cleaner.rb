require_relative './pymobiledevice'
require_relative '../models/device_state'
require_relative '../utils/push_to_zombie'

module BrowserStack
  class ProvisioningProfileCleaner

    CLEANUP_FREQUENCY = 7       # Frequency to check (in days)
    MAX_PROFILES_THRESHOLD = 30 # Clean only if number of profiles are exceeded

    def initialize(device_id)
      @device_id = device_id
      @profiles = nil
      @device_state = DeviceState.new(device_id)
    end

    def installed_profiles(reload: false)
      return @profiles unless reload || @profiles.nil?

      @profiles = PyMobileDevice::ProvisioningProfile.list_provisioning_profiles(@device_id)
      @profiles
    end

    def should_check?
      @device_state.clean_provisioning_profile_file_present? || @device_state.provisioning_profile_cleaned_file_older_than_days?(CLEANUP_FREQUENCY)
    end

    def cleanup_required?
      installed_profiles.length > MAX_PROFILES_THRESHOLD
    end

    def check_and_clean
      unless should_check?
        BrowserStack.logger.info "Skipping provisioning profile cleanup - shouldn't check"
        return
      end

      unless cleanup_required?
        BrowserStack.logger.info "Skipping provisioning profile cleanup - not required"
        @device_state.touch_provisioning_profile_cleaned_file
        return
      end

      BrowserStack.logger.info "Starting provisioning profile cleanup"

      count_before = installed_profiles.length
      installed_profiles.each do |profile|
        profile_uuid = profile.split(":").last.gsub("\"", "")
        BrowserStack.logger.info "Removing #{profile_uuid}"
        PyMobileDevice::ProvisioningProfile.remove_provisioning_profile(@device_id, profile_uuid)
      rescue => e
        BrowserStack.logger.error "Profile removal failed #{e.message} #{e.backtrace}"
      end

      install_chromium_profiles
      install_browserstack_profile

      count_after = installed_profiles(reload: true).length
      BrowserStack::Zombie.push_logs(
        "provisioning-profile-cleanup",
        "",
        {
          "device" => @device_id,
          "data": {
            count_before: count_before,
            count_after: count_after
          }
        }
      )

      @device_state.touch_provisioning_profile_cleaned_file
      @device_state.remove_clean_provisioning_profile_file
    rescue => e
      BrowserStack::Zombie.push_logs("profile-cleanup-failed", e.message, { "device" => @device_id })
      BrowserStack.logger.error "Provisioning profile cleanup failed #{e.message} #{e.backtrace}"
    end

    def install_chromium_profiles
      BrowserStack.logger.info "Adding Chromium profiles"
      file_content = File.read("/usr/local/.browserstack/config/ppuid_chromium_#{@device_id}")
      identifier_uuid_mapping = JSON.parse(file_content)
      identifier_uuid_mapping.each do |key, uuid|
        next if key == "branch_name"

        profile_path = "/Users/<USER>/Library/MobileDevice/Provisioning Profiles/#{uuid}.mobileprovision"
        PyMobileDevice::ProvisioningProfile.install_provisioning_profile(@device_id, profile_path)
      end
    end

    def install_browserstack_profile
      BrowserStack.logger.info "Adding BrowserStack profile"
      uuid = File.readlines("/usr/local/.browserstack/config/ppuid_#{@device_id}").last&.chomp
      profile_path = "/Users/<USER>/Library/MobileDevice/Provisioning Profiles/#{uuid}.mobileprovision"
      PyMobileDevice::ProvisioningProfile.install_provisioning_profile(@device_id, profile_path)
    end
  end
end

if __FILE__ == $PROGRAM_NAME
  device_id = ARGV[0]
  cleaner = BrowserStack::ProvisioningProfileCleaner.new(device_id)
  cleaner.check_and_clean
end

