require_relative 'http_utils'
require_relative 'osutils'
require_relative '../../config/constants'
require_relative '../../server/device_manager'

# Description:
# This file contains functions related to injecting the images inside app.
# Image injection is a feature with the help of which user's can mock the camera output.

module BrowserStack
  class ImageInjector  # rubocop:todo Metrics/ClassLength
    class << self
      # Downloads the image that needs to be injected in the session
      def inject_media(device, params, app_testing_bundle_id = "", extra_params = {}, is_async = false)
        session_id = params[:session_id]
        file_url = params[:file_url]
        media_hashed_id = params[:media_hashed_id]
        format = params[:format]
        product = params[:product]
        is_video_injection = format == ".mp4"

        return unless %w[app_automate app_live].include?(product)

        # Create folder to download the media files if not created already,
        # will be get cleared in cleanup
        injection_media_dir = get_injection_media_dir(device)
        FileUtils.mkdir_p(injection_media_dir)
        extension = is_video_injection ? ".mp4" : ".png"
        # Will store file with the hashed id to make sure it does not get downloaded twice and always with extension png, other extensions will be converted to png
        media_name = "#{media_hashed_id}#{extension}"
        media_path = File.join(injection_media_dir, media_name)
        bundle_id = product == "app_automate" ? fetch_bundle_id_from_session_file(device, session_id) : app_testing_bundle_id
        raise "Empty bundle id for session #{session_id}" if bundle_id.nil? || bundle_id.empty?

        bundle_id_arr = []
        bundles_for_multi_app_image_injection = params[:bundles_for_multi_app_image_injection]
        bundle_id_arr = bundles_for_multi_app_image_injection.split(',') if bundles_for_multi_app_image_injection.to_s != ''
        if is_async
          Thread.bs_run do
            download_and_inject(device, bundle_id, bundle_id_arr, injection_media_dir, media_path, media_hashed_id, file_url, format, is_video_injection, extra_params, true)
          end
        else
          download_and_inject(device, bundle_id, bundle_id_arr,  injection_media_dir, media_path, media_hashed_id, file_url, format, is_video_injection, extra_params)
        end

      end

      def download_and_inject(device, bundle_id, bundle_id_arr, injection_media_dir, media_path, media_hashed_id, file_url, format, is_video_injection, extra_params, async_flow = false)
        unless File.exist?(media_path)
          file_path = File.join(injection_media_dir, "#{media_hashed_id}#{format}")
          file_copied = false
          begin
            if ['sample_video', 'sample_image'].include?(media_hashed_id)
              local_sample_path = File.join(SAMPLE_MEDIA_DIR, extra_params['sample_media_version'], "sample#{format}")
              BrowserStack.logger.info("Sample media to be injected, checking if it exists locally #{local_sample_path}")
              if File.exist?(local_sample_path)
                FileUtils.cp(local_sample_path, file_path)
                file_copied = true
              else
                BrowserStack.logger.info("Sample media not found locally")
              end
            end
          rescue => e
            BrowserStack.logger.error("Need to download image from internet, failed to copy sample image locally: #{e}")
          end
          BrowserStack::HttpUtils.download(file_url, file_path, { retry_count: 3, timeout: 20 }, "camera_injection_image") unless file_copied
          # Converting image to png to keep same file extension for ease of not handling multiple files with same name and different extensions
          convert_image_to_png(file_path, media_path) if format != '.png' && !is_video_injection
        end
        if !bundle_id_arr.empty?
          bundle_id_arr.each do |single_bundle_id|
            insert_media_in_app(device, single_bundle_id, media_path, is_video_injection)
          rescue => e
            BrowserStack.logger.error("Failed to inject image in bundle-id: #{single_bundle_id} : #{e}")
          end
        else
          insert_media_in_app(device, bundle_id, media_path, is_video_injection)
        end
        create_doc_bs_folder(device, bundle_id)
        Utils.notify_pusher("MEDIA_INJECTION_SUCCESSFUL", extra_params, device) if async_flow
      rescue => e
        BrowserStack.logger.info "error while download and inject #{e.message}  #{e.backtrace.join('\n')}"
        raise e unless async_flow

        Utils.notify_pusher("MEDIA_INJECTION_FAILED", extra_params, device)
      end

      def create_doc_bs_folder(device, bundle_id)
        total_attempts = 3
        attempt = 0
        begin
          attempt += 1
          output, status = BrowserStack::OSUtils.execute(get_mkdir_command(device, bundle_id), true, timeout: 10)
          BrowserStack.logger.info "Output of get_mkdir_command device #{device} bundle #{bundle_id} status: #{status} output #{output}"
          raise "Failed to create bs folder to device #{device}" unless status.to_i == 0
        rescue => e
          retry if attempt < total_attempts && e.message == "Failed to create bs folder to device #{device}"
        end
      end

      # This injects the image inside user's app via ios-deploy
      def insert_media_in_app(device, bundle_id, file_path, is_video_injection = nil)
        total_attempts = 3
        attempt = 0
        begin
          attempt += 1
          # We will be deleting both image as well as video in case of image is being injected
          # Else we will only be deleting video -> To handle failures that will be caused due to AVCapturePhotoOutput
          BrowserStack::OSUtils.execute(get_delete_media_command(device, bundle_id, file_path, false), true, timeout: 30) unless is_video_injection # for image
          BrowserStack::OSUtils.execute(get_delete_media_command(device, bundle_id, file_path, true), true, timeout: 30) # for video
          output, status = BrowserStack::OSUtils.execute(get_media_inject_command(device, bundle_id, file_path, is_video_injection),
                                                         true, timeout: 30)
          BrowserStack.logger.info "Output of image injection device #{device} bundle #{bundle_id} filepath #{file_path} status: #{status} output #{output}"
          raise "Failed to push image to device #{device}" unless status.to_i == 0
        rescue => e
          retry if attempt < total_attempts && e.message == "Failed to push image to device #{device}"
          raise e
        end
      end

      # Convert the image to png
      def convert_image_to_png(input_file_path, output_file_path)
        if File.readable?(input_file_path) && File.extname(input_file_path) != '.png'
          output, status = BrowserStack::OSUtils.execute(get_file_convert_command(input_file_path, output_file_path), true, timeout: 15)
          BrowserStack.logger.info "Output of converting image to png input_file_path #{input_file_path} output_file_path #{output_file_path} status: #{status} output #{output}"
          raise "Failed to convert image to png" if status.to_i != 0
        else
          raise "Downloaded image file was not readable or trying to convert downloaded png"
        end
      end

      def cleanup(device)
        # ensure rm of downloaded images folder
        FileUtils.rm_rf(get_injection_media_dir(device))
      end

      private

      def get_injection_media_dir(device)
        File.join(INJECTION_MEDIA_DIR, device)
      end

      def get_media_inject_command(device, bundle_id, file_path, is_video_injection = false)
        if File.executable?(IOS_DEPLOY)
          injection_file_path = is_video_injection ? "Documents/browserstack/video/injectionVideo.mp4" : "Documents/injectionImage.png"
          "#{IOS_DEPLOY} --id #{device} --bundle_id \"#{bundle_id}\" --upload \"#{file_path}\" --to \"#{injection_file_path}\" 2>&1"
        else
          raise "ios deploy v1.11 not found or is not executable"
        end
      end

      def get_mkdir_command(device, bundle_id)
        if File.executable?(IOS_DEPLOY)
          injection_file_path = "Documents/browserstack/video"
          "#{IOS_DEPLOY} --id #{device} --bundle_id \"#{bundle_id}\" --mkdir \"#{injection_file_path}\" 2>&1"
        else
          raise "ios deploy v1.11 not found or is not executable"
        end
      end

      def get_delete_media_command(device, bundle_id, file_path, is_video_injection = false)
        if File.executable?(IOS_DEPLOY)
          injection_file_path = is_video_injection ? "Documents/browserstack/video/injectionVideo.mp4" : "Documents/injectionImage.png"
          "#{IOS_DEPLOY} --id #{device} --bundle_id \"#{bundle_id}\" --rm #{injection_file_path} 2>&1"
        else
          raise "ios deploy v1.11 not found or is not executable"
        end
      end

      def get_file_convert_command(input_file_path, output_file_path)
        "mv \"#{input_file_path}\" \"#{output_file_path}\" 2>&1"
      end

      def fetch_bundle_id_from_session_file(device, session_id)
        bundle_id = ''
        if File.exist?(DeviceManager.session_file(device))
          session_file_contents = JSON.parse(File.read(DeviceManager.session_file(device)))
          BrowserStack.logger.info("session_file details #{session_file_contents}")

          bundle_id = session_file_contents['app_testing_bundle_id'] if session_id.to_s == session_file_contents['automate_session_id'].to_s
        end

        bundle_id
      end
    end
  end
end
