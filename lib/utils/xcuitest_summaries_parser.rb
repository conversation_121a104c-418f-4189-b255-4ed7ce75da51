#
# Parse XCode UI Test (XCUI Tests) TestSummaries.plist files to clean them to have relevant information.
# TestSummaries.plist -> .json -> clean .json
#
# The generated json will information for rendering the logs on the UI, i.e. text logs and path to screenshot
# for each text log entry.
#
# Generate JSON will be uploaded to S3, along with every screenshot it has.
#
# Usage
#   XCUITestSummariesParser.parse_and_upload_screenshots(...)
#

require 'json'
require 'fileutils'
require 'securerandom'
require_relative './utils'

module XCUITestSummariesParser # rubocop:todo Metrics/ModuleLength
  module_function

  @DEBUG_LOGS = ENV['DEBUG']

  # Returns the filtered JSON, caller is writing it to disk or working in memory..
  def parse_and_upload(derived_data_path, upload_debug_screenshots, s3_params_screenshot, s3_params_raw_logs, server_config, device)
    return {} unless File.exist?(derived_data_path)

    @derived_data_path = derived_data_path
    @upload_debug_screenshots = upload_debug_screenshots
    @s3_common_params = s3_params_screenshot
    @s3_raw_logs_params = s3_params_raw_logs

    @server_config = server_config
    @device = device
    @screenshot_index = 0

    testsummary_plist = find_test_summary_file
    log("Found #{testsummary_plist} for DerivedData path: #{@derived_data_path}")

    @TEST_RESULTS_ROOT = File.expand_path("..", testsummary_plist)

    testsummary_json = "/tmp/#{Random.new.rand(100)}.json"
    convert_plist_to_json(testsummary_plist, testsummary_json)

    testData = JSON.parse(File.read(testsummary_json))["TestableSummaries"]
    FileUtils.rm(testsummary_json)

    testData.each_with_index do |suite, index|
      log("Parsing test #{index}, Name: #{suite['TestName']}")

      suite["Tests"].each do |test|
        process_block(test)
      end
    end

    upload_raw_logs(testData)
    testData
  end

  def log(message, level = 0)
    return unless @DEBUG_LOGS

    puts "#{'   ' * level} \e[#{31 + level}m#{message}\e[0m"
  end

  # Process only the attachments which are screenshots.
  # Custom Screenshots (taken by XCUIScreen.main.screenshot() from code) -> png
  # Debug Screenshots (taken automatically by Xcode) -> jpg
  def get_screenshot_path(attachment)
    if attachment['Filename'].match(/\.jpg$|\.png$/)
      file_path = File.join(@TEST_RESULTS_ROOT, "Attachments", attachment['Filename'])

      if File.exist?(file_path)
        # create s3 upload request.
        file_path
      end
    end
  end

  def get_screenshot_s3_path(attachment)
    file_path = get_screenshot_path(attachment)

    if file_path.nil?
      log("Unable to find screenshot for #{attachment.inspect}")
      return
    end

    # Upload All Screenshots if Debug Screenshots is enabled, if it is disabled, then upload
    # only the Custom Screenshots.
    upload_screenshot_to_s3(file_path) if @upload_debug_screenshots || file_path.match(/\.png$/)
  end

  def upload_raw_logs(testData)
    file_to_upload = File.join('/tmp', "#{@device}_raw_logs.json")
    File.write(file_to_upload, testData.to_json)
    s3_params = @s3_raw_logs_params
    uploader_request_file = @server_config["other_files_to_upload_dir"] + "/xcuitest_raw_logs_#{SecureRandom.uuid}.json"
    json_data = {
      upload_type: "xcuitest_raw_logs",
      file_name: file_to_upload,
      s3_params: s3_params
    }

    dir_name = File.dirname(uploader_request_file)
    FileUtils.mkdir_p(dir_name) unless Dir.exist? dir_name
    Utils.write_to_file(uploader_request_file, json_data.to_json)
    log("#{file_to_upload} upload request created as #{uploader_request_file}")

    s3_params[:s3_url]
  end

  # Async, image_uploader_process will actually upload the screenshot
  # Returns the S3 URL of the screenshot
  def upload_screenshot_to_s3(file_to_upload)
    @screenshot_index += 1

    s3_params = @s3_common_params.clone
    # Can match for jpg/png to differentiate custom and debug screenshots
    s3_params[:s3_url] = "#{s3_params[:s3_url]}-#{File.basename(file_to_upload)}"
    format = file_to_upload.match(/\.jpg$/) ? "jpeg" : "png"

    uploader_request_file = @server_config["images_to_upload_dir"] + "/xcuitest_screenshot_#{@screenshot_index}_#{SecureRandom.uuid}.json"
    json_data = {
      upload_type: "xcuitest_screenshot",
      file_name: file_to_upload,
      s3_params: s3_params,
      format: format
    }

    dir_name = File.dirname(uploader_request_file)
    FileUtils.mkdir_p(dir_name) unless Dir.exist? dir_name
    Utils.write_to_file(uploader_request_file, json_data.to_json)
    log("#{file_to_upload} upload request created as #{uploader_request_file}")

    s3_params[:s3_url]
  end

  def process_block(block, level = 0, logs = {})
    if block["Title"]
      log(block["Title"], level)
    else
      log("Processing block: #{block['TestName'] || block['ActivityType'] || block['TestObjectClass']}", level)
    end

    if block["Attachments"]
      block["Attachments"].each do |attachment|
        block["Screenshots"] ||= []
        block["Screenshots"] << get_screenshot_s3_path(attachment)
      end
      log("Screenshot: #{block['Screenshots']}", level)
      block.delete("Attachments")
    end

    block["SubActivities"]&.each do |substeps|
      process_block(substeps, level + 1)
    end

    block["ActivitySummaries"]&.each do |step|
      process_block(step, level + 1)
    end

    block["Subtests"]&.each do |subtest|
      process_block(subtest, level + 1)
    end

    # Deleting extra keys from the generated JSON
    block.delete("UUID")
    block.delete("ActivityType")
    block.delete("TestObjectClass")
    block.delete("RunDestination")
    block.delete("HasScreenshotData")
  end

  def convert_plist_to_json(plist_file, json_file)
    raise "Cannot find: plutil . Please ensure it is installed." if `which plutil`.empty?

    log("Converting #{plist_file} to #{json_file}")
    `plutil -convert json -o \"#{json_file}\" \"#{plist_file}\"`
  end

  def find_test_summary_file
    test_dir = File.join(@derived_data_path, "Logs", "Test")
    xcode_9_test_summary = Dir.glob(File.join(test_dir, "*TestSummaries.plist")).first
    xcode_10_test_summary = Dir.glob(File.join(test_dir, "*xcresult", "*TestSummaries.plist")).first

    if xcode_9_test_summary
      xcode_version = 9
      plist = xcode_9_test_summary
    elsif xcode_10_test_summary
      xcode_version = 10
      plist = xcode_10_test_summary
    else
      raise "Unable to find any *TestSummaries.plist file in #{@derived_data_path}"
    end

    log("Assuming tests ran on XCode - #{xcode_version}")
    plist
  end
end

puts JSON.pretty_generate(XCUITestSummariesParser.parse_and_upload_screenshots(ARGV[0], { s3_url: "https://aws.amazon.wjabba/sds/sdss/" }, { "other_files_to_upload_dir" => "/tmp/", "images_to_upload_dir" => '/tmp/' }, "device_id")) if ENV['TESTING'] && ARGV[0]
