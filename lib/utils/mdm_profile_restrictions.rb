require_relative '../erb_binding'
require_relative '../../config/constants'
require_relative '../configuration'
require_relative '../utils/ios_mdm_service_client'
require_relative '../helpers/experiments_helper'
require_relative '../models/device_state'

module Mdm
  # Deprecated. Use ConfigurationProfilesManager
  class MdmProfileRestrictions
    def initialize(device_id)
      @udid = device_id
      @device_state = DeviceState.new(device_id)
      @server_config = BrowserStack::Configuration.new.all
      @device_config = begin
        JSON.parse(File.read(@server_config['config_json_file']))["devices"][device_id]
      rescue
        {}
      end
    end

    def compose_and_apply(redis_client, flags = [])
      BrowserStack.logger.info('[MdmProfileRestrictions] Composing MDM Profile Restrictions')
      plist = compose(flags)
      BrowserStack.logger.info('[MdmProfileRestrictions] Applying MDM Profile Restrictions')
      apply(plist, redis_client)
    end

    def device_state
      @device_state ||= DeviceState.new(@udid)
    end

    private

    def compose(flags = []) # rubocop:todo Metrics/AbcSize
      data = {
        device: @udid,
        uuid: @server_config["mdm_profiles_required"]["Restrictions"]["uuid"],
        restrictions_payload_id: @server_config['mdm_restrictions_profile_payload_id'],
        profile_display_name: "Restrictions",
        disable_weather_app: !@device_config['device_name'].match(/iPhone/).nil?,
        disable_messaging_app: @device_config['device_name'].match(/iPhone/).nil?,
        disable_files_app: @device_config["device_version"].to_f < 13.0,
        disable_wallet_app: @device_config["device_version"].to_f < 13.0,
        disable_contacts_app: @device_config["device_version"].to_i != 13,
        disable_esim_modification: @device_config["device_version"].to_f >= 11.0 && !@device_state.esim_modification_file_present?,
        profile_installation_ui_disallowed: false,
        profile_payload_id: "mdm.restrictions.#{@udid}",
        enable_pwa: ExperimentsHelper.enable_pwa?(@device_config["device_version"]), # TODO: remove when we start supporting PWAs on all iOS versions
        enable_passcode_settings: flags.include?('enable_passcode_settings'),
        enable_bluetooth_modification: flags.include?('enable_bluetooth_modification'),
        force_automatic_date_time: @device_state.force_automatic_date_time_file_present?,
        is_dedicated_device: device_state.dedicated_device_file_present?,
        disable_siri: device_config["device_version"].to_i == 16 || device_config["device_version"].to_i >= 18
      }
      erb_file = device_state.dedicated_device_file_present? ? "dedicated_restrictions.erb" : "restrictions.erb"
      ERB.new(File.open(File.join(@server_config['templates_dir'], erb_file)).read).result(ErbBinding.new(data).get_binding)
    end

    def apply(plist, redis_client)
      BrowserStack::IosMdmServiceClient.configure
      BrowserStack::IosMdmServiceClient.set_restrictions(@udid, plist, redis_client)
    end
  end
end
