require_relative './osutils'

module BrowserStack
  module PlistBuddy
    # TODO: Replace OSUtils execute with OSUtils.safe_execute
    def self.binary_path
      "/usr/libexec/PlistBuddy"
    end

    def self.add_key_in_plist(plist_path, key, type)
      OSUtils.execute("#{PlistBuddy.binary_path} -c 'add #{key} #{type}' #{plist_path}")
    end

    def self.add_key_value_in_plist(plist_path, key, type, value)
      OSUtils.execute("#{PlistBuddy.binary_path} -c 'add #{key} #{type} #{value}' #{plist_path}")
    end

    def self.set_key_in_plist(plist_path, key, value)
      OSUtils.execute("#{PlistBuddy.binary_path} -c 'set #{key} #{value}' #{plist_path}")
    end

    def self.get_value_of_key(plist_path, key, return_status = false)
      result, status = OSUtils.execute("#{PlistBuddy.binary_path} -c 'Print #{key}' '#{plist_path}'", true)
      return_status ? [result, status] : result
    end

    def self.get_value_of_key_in_xml(plist_path, key)
      OSUtils.execute("#{PlistBuddy.binary_path} -x -c 'Print #{key}' #{plist_path}")
    end

    def self.delete_key_in_plist(plist_path, key)
      OSUtils.execute("#{PlistBuddy.binary_path} -c 'Delete #{key}' #{plist_path}")
    end
  end
end
