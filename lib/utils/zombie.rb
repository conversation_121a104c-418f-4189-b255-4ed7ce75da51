require 'net/http'
require 'json'
require 'static_conf'

require_relative 'http_utils'
require 'bsdwh'
require 'network_helper'
require 'bsenv'
require_relative '../configuration'
require_relative '../../config/constants'
require_relative './helpers'

module BrowserStack
  class Zombie
    CONFIG = Configuration.new.all

    def self.configure(params = {})
      @@environment = CONFIG['environment'] == "prod" ? "production" : "staging"
      @@category = "realmobile-platform"
      @@terminal_type = params[:terminal_type] || "realMobile_njb"
      network_helper = NetworkHelper::NetworkSetup.new
      #TODO: change the default, also change to diffrenciate from current mobile platform
      @@browser = params[:browser] || Configuration['default_platform'] || "ios"
      @@os = params[:os] || Configuration['default_platform'] || "ios"
      @@ip = params[:ip] || network_helper.get_ip
      @@canary = deploy_env

      unless network_helper.is_valid_ip?(@@ip)
        BrowserStack.logger.error "Could not initialize zombie for ip=#{@@ip}, params=#{params.inspect}"
        false
      end
    end

    # We should have some kind of rate limit here for kinds which are pushed
    # very often. For example `mdm-response-time` can be actually pushed to BQ
    # every 4-5 events.
    # `overrided_env` is playing role when prod terminals are being for staging purpose
    def self.push_logs(kind, error, data={}, overrided_env=nil, params={}) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
      return if params["is_app_accessibility"].to_s.downcase == "true"

      if ["app_automation_session_stats", "automation_session_stats"].include?(kind)
        return if data["sessionid"].nil?

        data["kind"] = kind
      else
        # Add device data if device_id is present
        if data["device"]
          data["browser"] ||= device_name(data["device"])
          data["os_version"] ||= device_version(data["device"])
        end

        defaults = {
          "category" => @@category,
          "kind" => kind,
          "timestamp" => Time.now.to_i.to_s,
          "machine" => @@ip,
          "error" => !error.nil? && error.to_s.tr("'", "\'"),
          "browser" => @@browser,
          "browser_version" => nil,
          "terminal_type" => @@terminal_type,
          "os" => @@os,
          "region" => CONFIG['static_conf']["sub_region"],
          "user_browser_version" => @@canary
        }
        data = defaults.merge(data)
        if data["session_id"].nil?
          data["session_id"] = begin
            JSON.parse(data["data"])["sessionid"]
          rescue
            nil
          end
        end
        if data["url"].nil?
          data["url"] = begin
            JSON.parse(data["data"])["url"]
          rescue
            nil
          end
        end
      end

      data["team"] ||= "mobile"
      if !data["error"].nil? && data["error"].is_a?(String)
        # Truncate error so that packet is not dropped
        data["error"] = data["error"][0...200]
      end

      pager_env = overrided_env == 'staging' ? 'staging' : @@environment

      BrowserStack.logger.info "zombie log #{data.to_json} for #{pager_env} env" unless BSEnv.debug?
      begin
        DWH::Sender.send("udp", "pager", data.to_json, pager_env)
      rescue => e
        BrowserStack.logger.error "Zombie push_logs failed! #{e.message}" + e.backtrace.join("\n") unless BSEnv.debug?
      end
    end
  end
end
