require 'fileutils'
require_relative './configuration_profiles_manager'
require_relative './utils'
require_relative './idevice_utils'
require_relative '../configuration'
require_relative '../custom_exceptions'
require_relative './zombie'
require_relative '../erb_binding'
require_relative '../../config/constants'
require_relative '../helpers/data_report_helper'
require_relative './custom_mdm_manager'

class ConfigurationProfilesEnforcer # rubocop:todo Metrics/ClassLength

  def initialize(device_id, logger)
    @device_id = device_id
    @logger = logger
  end

  def enforce_configuration_profiles(force_install = false, profile_check_due = false) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
    start_time = Time.now.to_i
    final_profiles = {}
    error = nil
    return true if CustomMDMManager.is_custom_mdm_device?(@device_id)

    @logger.info "Started enforcing configuration profiles on device: #{@device_id}, with force_install: #{force_install}"

    mitm5_installed_state_file = "#{server_config['state_files_dir']}/mitm5_installed_state_file_ver#{MITM_5_CERT_VERSION}_#{@device_id}"
    force_install = true unless File.file?(mitm5_installed_state_file)

    return if !force_install && !profile_check_due

    BrowserStack::IosMdmServiceClient.configure
    # Check Device on MDM
    begin
      BrowserStack::IosMdmServiceClient.check_device_on_mdm(@device_id)
    rescue MdmApiFatalException => e
      try_re_mdm_with_cleanup
      raise e
    end

    # Parses MDM and/or CFGUTIL profiles, gives out list of profiles to remove and install
    @logger.info "Parsing profiles for device: #{@device_id}"
    parsed_profiles = parse_profiles
    @logger.info "Parsed profiles: #{parsed_profiles} for device: #{@device_id}"

    # Return early if all is good
    @logger.info "Checking if profiles are all good on device: #{@device_id}"
    if profiles_all_good?(parsed_profiles) && !force_install
      final_profiles = parsed_profiles
      @logger.info "All profiles are good, returning early ..."
      device_state.touch_configuration_profile_periodic_check_file
      device_state.remove_re_mdm_requested_file
      return
    end

    # Applies the parsed profiles, removing and/or installing MDM and/or CFGUTIL profiles
    @logger.info "Applying profiles: #{parsed_profiles}, on device: #{@device_id}"
    apply_profiles(parsed_profiles, force_install)
    @logger.info "Applied profiles: #{parsed_profiles}, on device: #{@device_id}"

    # Verifies MDM and/or CFGUTIL profiles, to ensure all the profiles are removed/installed as expected
    @logger.info "Getting final profiles applied on device: #{@device_id}..."
    final_profiles = parse_profiles
    @logger.info "Found profiles applied: #{final_profiles}, on device: #{@device_id}, verifying if profiles are all good ..."
    raise "Inconsitent profiles found" unless profiles_all_good?(final_profiles)

    FileUtils.touch(mitm5_installed_state_file)
    device_state.remove_force_install_mdm_profiles_file if force_install
    device_state.touch_configuration_profile_periodic_check_file
    device_state.remove_re_mdm_requested_file

    @logger.info "Successfully enforced configuration profiles for device: #{@device_id}"
  rescue => e
    @logger.error "Failed to enforce configuration profiles for device: #{@device_id}, error: #{e.message}, #{e.backtrace.join("\n")}"
    error = e.message

    # We should re-mdm the device if any of the MDM calls are failing, currently doing this only for FC.
    if e.instance_of?(MdmApiException) && error.include?("try re-mdm")
      @logger.info "MDMApiException, checking if we want to re-mdm the device"
      is_full_cleanup = device_state.mdm_full_cleanup_file_present? || device_state.first_cleanup_file_present?
      is_re_mdm_file_request_older = device_state.re_mdm_requested_file_older_than_minutes?(15)

      @logger.info "is_full_cleanup: #{is_full_cleanup}, is_re_mdm_file_requested_older: #{is_re_mdm_file_request_older}"
      try_re_mdm_with_cleanup if is_full_cleanup && is_re_mdm_file_request_older
    end

    raise e
  ensure
    time_taken = Time.now.to_i - start_time
    check_and_report_data({
      "profiles_installed_on_mdm" => final_profiles&.dig(:mdm, :installed_profiles),
      "profiles_installed_on_cfgutil" => final_profiles&.dig(:cfgutil, :installed_profiles),
      "check_mdm_profiles" => final_profiles&.dig(:mdm, :check_profiles),
      "force_install" => force_install,
      "profile_check_due" => profile_check_due,
      "time_taken" => time_taken,
      "error" => error,
      "release_tag" => release_tag

    })
  end

  private

  def parse_profiles
    # Get List of CFGUTIL installed profiles
    cfgutil_installed_profiles = configuration_profiles_manager.device_uses_cfgutil_managed_profiles?(verify_cfgutil: true) ? configuration_profiles_manager.cfgutil_installed_profiles : []

    # Check if we need to check MDM profiles
    # This is to ensure that migration of all the profiles is done, and there is no need to check mdm profiles any more
    check_mdm_profiles = should_check_mdm_profiles?(mdm_managed_profiles, cfgutil_managed_profiles, cfgutil_installed_profiles)

    # Get CFGUTIL profiles to remove and install
    cfgutil_profiles_to_remove, cfgutil_profiles_to_install = parse_cfgutil_profiles(cfgutil_managed_profiles, cfgutil_installed_profiles)

    if check_mdm_profiles
        # Get List of MDM installed profiles
      mdm_installed_profiles = configuration_profiles_manager.mdm_installed_profiles

      # Get MDM profiles to remove, and install
      mdm_profiles_to_remove, mdm_profiles_to_install = parse_mdm_profiles(mdm_managed_profiles, mdm_installed_profiles)
    end

    {
      mdm: {
        check_profiles: check_mdm_profiles,
        installed_profiles: !mdm_installed_profiles.nil? ? mdm_installed_profiles : {},
        profiles_to_remove: !mdm_profiles_to_remove.nil? ? mdm_profiles_to_remove : {},
        profiles_to_install: !mdm_profiles_to_install.nil? ? mdm_profiles_to_install : {}
      },
      cfgutil: {
        installed_profiles: cfgutil_installed_profiles,
        profiles_to_remove: cfgutil_profiles_to_remove,
        profiles_to_install: cfgutil_profiles_to_install
      }
    }
  end

  def apply_profiles(profiles_details, force_install)
    mdm_profiles_to_remove = profiles_details[:mdm][:profiles_to_remove]
    mdm_profiles_to_install = profiles_details[:mdm][:profiles_to_install]

    cfgutil_profiles_to_remove = profiles_details[:cfgutil][:profiles_to_remove]
    cfgutil_profiles_to_install = profiles_details[:cfgutil][:profiles_to_install]

    # Remove extra MDM profiles
    remove_profiles(mdm_profiles_to_remove, remove_via: :mdm)

    # Remove extra CFGUTIL profiles
    remove_profiles(cfgutil_profiles_to_remove, remove_via: :cfgutil)

    # Install MDM profiles
    check_and_install_profiles(mdm_profiles_to_install, force_install, install_via: :mdm)

    # Install CFGUTIL Profiles
    check_and_install_profiles(cfgutil_profiles_to_install, force_install, install_via: :cfgutil)
  end

  def profiles_all_good?(profiles_details)
    mdm_profiles_to_remove = profiles_details[:mdm][:profiles_to_remove]
    mdm_profiles_to_install = profiles_details[:mdm][:profiles_to_install]

    cfgutil_profiles_to_remove = profiles_details[:cfgutil][:profiles_to_remove]
    cfgutil_profiles_to_install = profiles_details[:cfgutil][:profiles_to_install]
    mdm_profiles_to_remove.empty? && mdm_profiles_to_install.empty? && cfgutil_profiles_to_remove.empty? && cfgutil_profiles_to_install.empty?
  end

  def release_tag
    @release_tag = JSON.parse(File.read("/usr/local/.browserstack/realmobile/.browserstack_build_version.json"))["tag"]
  rescue
    nil
  end

  def data_reporter
    @data_reporter ||= DataReportHelper.new("configuration-profiles-enforcer", device: @device_id)
  end

  def check_and_report_data(data)
    mdm_profiles = data.dig("profiles_installed_on_mdm", "ProfileList")&.map do |profile|
      next if profile["IsManaged"].to_s != "true"

      {
        "identifier" => profile["PayloadIdentifier"],
        "displayName" => profile["PayloadDisplayName"],
        "uuid" => profile["PayloadUUID"]
      }
    end&.compact

    cfgutil_profiles = data["profiles_installed_on_cfgutil"]&.map do |profile|
      {
        "identifier" => profile["identifier"],
        "displayName" => profile["displayName"],
        "uuid" => profile["uuid"]
      }
    end

    data["profiles_installed_on_mdm"] = mdm_profiles
    data["profiles_installed_on_cfgutil"] = cfgutil_profiles
    data_reporter.report(data) if data["force_install"] || data["profile_check_due"]
  end

  def configuration_profiles_manager
    @configuration_profiles_manager ||= ConfigurationProfilesManager.new(@device_id, @logger)
  end

  def device_state
    @device_state ||= DeviceState.new(@device_id)
  end

  def server_config
    @server_config ||= Configuration.new.all
  end

  def mdm_managed_profiles
    @mdm_managed_profiles ||= configuration_profiles_manager.mdm_managed_profiles
  end

  def cfgutil_managed_profiles
    @cfgutil_managed_profiles ||= configuration_profiles_manager.cfgutil_managed_profiles
  end

  def try_re_mdm_with_cleanup
    @logger.info("Sending device to Cleanup, for re-mdm'ing")
    device_state.touch_re_mdm_requested_file
    device_state.touch_re_mdm_file
    device_state.remove_force_clean_safari_file
    device_state.touch_cleanup_requested_file
  end

  # Gets the list of mdm managed profiles and list of cfgutil managed profiles
  # Returns false if mdm managed profiles is empty and all cfgutil managed profiles in installed by cfgutil
  # Return true otherwise
  def should_check_mdm_profiles?(mdm_managed_profiles, cfgutil_managed_profiles, cfgutil_installed_profiles)
    @logger.info "Checking if mdm check is required for device: #{@device_id}, with mdm_managed_profiles: #{mdm_managed_profiles}, cfgutil_managed_profiles: #{cfgutil_managed_profiles}, cfgutil_installed_profiles: #{cfgutil_installed_profiles}"
    cfgutil_installed_profiles_keys = cfgutil_installed_profiles.map { |cfgutil_installed_profile| cfgutil_installed_profile["displayName"].downcase.to_sym }
    cfgutil_managed_profiles_keys = cfgutil_managed_profiles.keys
    !mdm_managed_profiles.empty? || !(cfgutil_managed_profiles_keys - cfgutil_installed_profiles_keys).empty?
  end

  def parse_mdm_profiles(profiles_required, profiles_installed) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
    profiles_to_install = Utils.deep_copy(profiles_required)
    profiles_to_remove = {}
    profiles_installed['ProfileList'].each do |profile| # rubocop:todo Metrics/BlockLength
      # Ignore MicroMDM profile
      if profile['PayloadOrganization'] == 'MicroMDM'
        next
      elsif profile['IsManaged'].to_s == "true" && profiles_required.key?(profile['PayloadDisplayName'].downcase.to_sym)
        profile_required = profiles_required[profile['PayloadDisplayName'].downcase.to_sym]

        # Payload type is different
        if profile_required["type"]
          content = profile['PayloadContent'].map { |p| p['PayloadType'] }
          matched_content = content.select { |payload_type| (payload_type == profile_required["type"]) }
          if matched_content.empty?
            @logger.info("profile check for #{profile['PayloadDisplayName']} failed, payload type mismatch, looking for '#{profile_required['type']}' found #{content.join(',')}}")
            next
          end
        end

        # UUID mismatch
        if profile_required["uuid"] && (profile['PayloadUUID'] != profile_required["uuid"])
          @logger.info("profile check for #{profile['PayloadDisplayName']} failed, uuid mismatch, looking for '#{profile_required['uuid']}' found '#{profile['PayloadUUID']}'")
          next
        end

        # PayloadIdentifier mismatch for PAC and MDM profiles
        if ['Proxy', 'Restrictions', 'Notifications'].include?(profile['PayloadDisplayName'])
          raise "PayloadContent has more than one element, won't search for PayloadIdentifier" if profile['PayloadContent'].length > 1

          profile_installed_payload_id = profile['PayloadContent'][0]['PayloadIdentifier']
          required_profile_payload_id = case profile['PayloadDisplayName']
                                        when 'Proxy'
                                          server_config['pac_profile_payload_id']
                                        when 'Notifications'
                                          server_config['mdm_notifications_profile_payload_id']
                                        else
                                          server_config['mdm_restrictions_profile_payload_id']
                                        end

          if required_profile_payload_id != profile_installed_payload_id
            @logger.info("profile check for #{profile['PayloadDisplayName']} failed, \
                                      PayloadIdentifier mismatch, required \
                                      '#{server_config['pac_profile_payload_id']}' \
                                      and '#{profile_installed_payload_id}' installed")
            next
          end
        end

        # All good, don't need to install
        profiles_to_install.delete(profile['PayloadDisplayName'].downcase.to_sym)
      elsif profile['IsManaged'].to_s == "true"
        next if ["Configure Notifications", "Notifications"].include?(profile['PayloadDisplayName']) && !Utils.new_notifications_flow_enabled?(@device_id)

        # Add profile to profiles to remove
        profiles_to_remove[profile['PayloadDisplayName'].downcase.to_sym] = {
          'identifier' => profile['PayloadIdentifier']
        }
      end
    end
    [profiles_to_remove, profiles_to_install]
  end

  def parse_cfgutil_profiles(profiles_required, profiles_installed)
    profiles_to_install = Utils.deep_copy(profiles_required)
    profiles_to_remove = {}
    profiles_installed.each do |profile|
      next if profile["identifier"] == MICROMDM_ENROLLMENT_PROFILE_IDENTIFIER

      if profiles_required.key?(profile["displayName"].downcase.to_sym)
        profiles_to_install.delete(profile["displayName"].downcase.to_sym) if profile["uuid"] == configuration_profiles_manager.profile_uuid(profile["displayName"].downcase.to_sym)
      else
        profiles_to_remove[profile["displayName"].downcase.to_sym] = {
          "identifier" => profile["identifier"]
        }
      end
    end
    [profiles_to_remove, profiles_to_install]
  end

  def remove_profiles(profiles_to_remove, remove_via:)
    profiles_to_remove.each do |profile_to_remove, data|
      configuration_profiles_manager.remove_profile(profile_to_remove, data["identifier"], remove_via: remove_via)
    end
  end

  def check_and_install_profiles(profiles_to_install, force_install, install_via:) # rubocop:todo Metrics/CyclomaticComplexity
    required_profiles = install_via == :mdm ? mdm_managed_profiles : cfgutil_managed_profiles
    required_profiles.each do |required_profile, _data| # rubocop:todo Metrics/BlockLength
      case required_profile
      when :restrictions
        if profiles_to_install.key?(:restrictions) || force_install
          @logger.info("#{@device_id} Installing Restrictions Profile")
          configuration_profiles_manager.install_profile(:restrictions, install_via: install_via)
        end
      when :proxy
        if profiles_to_install.key?(:proxy) || force_install
          @logger.info("#{@device_id} Installing Proxy Profile")
          configuration_profiles_manager.install_profile(:proxy, install_via: install_via)
        end
      when :"setup assistant"
        install_setup_assistant_profile = IdeviceUtils.device_version(@device_id) >= Gem::Version.new('14.0') && !IdeviceUtils.apple_tv_device?(@device_id)
        if (profiles_to_install.key?(:"setup assistant") || force_install) && install_setup_assistant_profile
          @logger.info("#{@device_id} Installing Setup Assistant Profile")
          configuration_profiles_manager.install_profile(:"setup assistant", install_via: install_via)
        end
      when :mitmproxy_root_certificate
        if profiles_to_install.key?(:mitmproxy_root_certificate) || force_install
          @logger.info("#{@device_id} Installing MITM Proxy Certificate")
          configuration_profiles_manager.install_profile(:mitmproxy_root_certificate, install_via: install_via)
        end
      when :mitmproxy_5_root_certificate
        if profiles_to_install.key?(:mitmproxy_5_root_certificate) || force_install
          @logger.info("#{@device_id} Installing MITM5 Proxy Certificate")
          configuration_profiles_manager.install_profile(:mitmproxy_5_root_certificate, install_via: install_via)
        end
      when :notifications
        if profiles_to_install.key?(:notifications) || force_install
          @logger.info("#{@device_id} Installing Notifications Profile")
          configuration_profiles_manager.install_profile(:notifications, install_via: install_via)
        end
      end
    end
  end
end
