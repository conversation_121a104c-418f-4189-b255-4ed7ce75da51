#
# utils package for iproxy commands
#

require_relative '../../config/constants'
require_relative '../configuration'
require_relative 'osutils'

class Iproxy
  class << self
    def version
      output, exit_code = BrowserStack::OSUtils.execute("#{binary} --version", true)
      if exit_code != 0
        BrowserStack.logger.info "iproxy command failed: #{output}:#{exit_code}"
        raise "iproxy command failed"
      end
      output = output.split(' ')[1].strip unless output.include?("usage")
      output
    end

    #Starts iproxy for given device and ports
    def start(device_id, local_port, device_port)
      command = case version
                when '2.0.3'
                  "#{binary} #{local_port}:#{device_port} -u #{device_id}"
                when '2.0.2'
                  "#{binary} #{local_port} #{device_port} -u #{device_id}"
                else
                  "#{binary} #{local_port} #{device_port} #{device_id}"
                end

      Utils.fork_process(command, { process_name: "iproxy for device #{device_id} #{local_port}" })
    end

    def running?(device_id, port)
      !BrowserStack::OSUtils.grep_process_details('iproxy', port, device_id).empty?
    end

    def binary
      return "#{IPROXY} -n" if BrowserStack::Configuration['idevice_connection_type'] == 'network'

      IPROXY
    end
  end
end
