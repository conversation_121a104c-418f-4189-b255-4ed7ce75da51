require_relative '../configuration'
require_relative '../checks/check_plist'

class LaunchAgent
  def initialize(svc_name:, cmd:, **options)
    @svc_name = svc_name
    @username = options[:username] || BrowserStack::Configuration['user']
    stdout = options[:stdout] || "#{BrowserStack::Configuration['logging_root']}/#{svc_name}.log"
    appium_home = options.dig(:env_vars, :APPIUM_HOME) || false
    @check_plist = BrowserStack::CheckPlist.new(
      svc_name,
      ServiceType.UserService,
      cmd,
      stdout,
      options[:stderr] || stdout,
      @username,
      options[:append_path_env],
      options[:custom_config],
      options[:keep_alive].nil? ? true : options[:keep_alive],
      options[:start_calendar_interval],
      options[:session_create] || false,
      options[:set_lang_and_term] || false,
      'generic_plist.erb',
      options[:prepend_path_env],
      appium_home
    )
  end

  def update(force: false, wait_for: 0)
    @check_plist.update(force, wait_for)
  end

  def unload
    BrowserStack::CheckPlist.unload_service(@svc_name, ServiceType.UserService, @username)
  end
end
