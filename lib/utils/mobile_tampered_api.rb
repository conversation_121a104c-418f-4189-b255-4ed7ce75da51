require_relative 'http_utils'

# WARNING: Rails usually returns 200 even on error. They rely on setting fields
# on the response json to values like error. Be sure to read their source code
# and don't assume anything.

module BrowserStack
  class MobileTamperedAPI
    # This will ban the last user that used the phone and alert the necessary
    # teams about it.
    # This bans the whole group! If you ban a @browserstack user, all the
    # company will lose access to browserstack.
    def self.ban_last_user!(session_file)
      fields = JSON.parse(File.read(session_file))

      # Don't run in staging. It's not fully implemented there.
      return if fields['host'] != 'www.browserstack.com'

      url = 'https://live.browserstack.com/main/mobile_tampered'
      url << '?'
      url << 'auth=mobile_tampered_key_auth&'
      url << "user_id=#{fields['user_id']}&"
      url << "instance_id=#{fields['device']}&"
      url << "session_id=#{fields['session_id']}&"
      url << "genre=#{fields['genre']}"

      HttpUtils.get_response(url)
    end
  end
end
