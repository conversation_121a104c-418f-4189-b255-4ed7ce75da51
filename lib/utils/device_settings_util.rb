require 'shellwords'
require 'base64'
require 'fileutils'
require_relative 'http_utils'
require_relative './utils'
require_relative 'osutils'
require_relative '../../config/constants'
require_relative '../../server/device_manager'
require_relative '../custom_exceptions'
require_relative '../helpers/wda_client'

module BrowserStack
  class DeviceSettingsUtil
    class << self

      def handle_device_setting(device_id, setting_name, value, app_display_name, session_id)
        current_device_config = DeviceManager.device_configuration_check(device_id)
        session_info = DeviceManager.session_file_contents(device_id)
        app_bundle_id = session_info['app_testing_bundle_id']
        device_version = current_device_config['device_version'].to_i
        device_settings_config_path = "#{CONFIG_PATH}/device_settings/ios_#{device_version.to_i}.json"

        device_settings = if File.exists?(device_settings_config_path)
                            JSON.parse(File.read(device_settings_config_path))
                          else
                            raise DeviceSettingsError.new("Device Settings JSON absent", BROWSERSTACK_ERROR_STRING, "no_device_settings_config")
                          end

        raise DeviceSettingsError.new("Settings name absent in JSON", BROWSERSTACK_ERROR_STRING, "setting_name_not_present") unless device_settings[setting_name]

        settings_config = device_settings[setting_name]
        params = {}
        params['automate_session_id'] = session_id
        params['global_setting_name'] = settings_config['global_setting_name']
        params['special_post_action'] = settings_config['special_post_action_value_map'][value.to_s.upcase]
        params['device_settings_DSL'] = settings_config["#{value.to_s.upcase}_DSL"]
        params['app_testing_bundle_id'] = app_bundle_id
        params['app_display_name'] = app_display_name
        params['value'] = value
        update_settings(device_id, params)
      end

      def update_settings(device_id, params)
        #enable settings app

        Utils.enable_settings_app(device_id, params)

        #request WDA
        current_device_config = DeviceManager.device_configuration_check(device_id)
        wda_port = current_device_config["webdriver_port"].to_i
        wda_client = WdaClient.new(wda_port)
        response = wda_client.update_device_settings(params)

        # The below lines will be skipped if any exception is raised in wda call
        device_state = DeviceState.new(device_id)
        case params['value'].to_s.downcase
        when 'on'
          device_state.remove_device_location_off_file
        when 'off'
          device_state.touch_device_location_off_file
        end

      rescue DeviceSettingsError => e
        BrowserStack.logger.error "update_device_settings Failed - #{e.message} #{e.kind} #{e.meta_data} #{e.backtrace}"
        raise e
      rescue => e
        BrowserStack.logger.error "update_device_settings Failed unknown error - #{e.message} #{e.backtrace}"
        raise DeviceSettingsError.new(e.message,
                                      BROWSERSTACK_ERROR_STRING,
                                      'internal_error')
      ensure
        push_to_cls(params, "update_device_settings_automation", '', { "update_settings" => response }) if response
        #disable settings app
        Utils.disable_settings_app(device_id, params)
        foreground_user_app(device_id, params)
      end

      def foreground_user_app(device_id, params)
        #request WDA
        current_device_config = DeviceManager.device_configuration_check(device_id)
        wda_port = current_device_config["webdriver_port"].to_i
        request_url = "/bs/forgroundApp"
        request_body = {
          "session_id" => params['session_id'] || params['automate_session_id'],
          "app_name" => params['app_display_name'],
          "app_bundle_id" => params['app_testing_bundle_id'],
          "DSL" => {}
        }
        wda_client = WdaClient.new(wda_port)
        response = wda_client.send :make_request, 'POST', request_url, request_body
        push_to_cls(params, "update_settings_foreground", '', { "foreground_app" => response })
        #handle error
        if response['value']['status'] == 'error'
          error_obj = response['value']['error']
          raise DeviceSettingsError.new(error_obj['message'],
                                        BROWSERSTACK_ERROR_STRING,
                                        'setting_automation_error', error_obj['meta_data'])
        end
      rescue DeviceSettingsError => e
        BrowserStack.logger.error "update_device_settings foreground_user_app Failed - #{e.message} #{e.kind} #{e.meta_data} #{e.backtrace}"
        raise e
      rescue => e
        BrowserStack.logger.error "update_device_settings foreground_user_app Failed unknown error - #{e.message} #{e.backtrace}"
        raise DeviceSettingsError.new(e.message,
                                      BROWSERSTACK_ERROR_STRING,
                                      'internal_error')
      end

    end

  end
end
