require 'erb'
require_relative '../configuration'
require_relative './osutils'
require_relative 'plist_buddy'
require_relative '../provisioning/provisioning_profile'
require_relative '../../server/iphone'
require 'browserstack_logger'

module BrowserStack
  class XcodeBuildUtils
    def initialize(device, project_path = '/usr/local/.browserstack/WebDriverAgent')
      @device = device
      @server_config = Configuration.new.all
      @project_path = project_path
      @provisioning_profile_dir = @server_config['provisioning_profile_dir']
      @provisioning_profile_name = BrowserStack::IPhone.get_device_provisioning_profile(device)
      @provisioning_profile_path = "#{@provisioning_profile_dir}/#{@provisioning_profile_name}.mobileprovision"
      @profile = ProvisioningProfile.new(@provisioning_profile_path)
    end

    def stop_wda_xcodebuild
      BrowserStack.logger.info("Stoping wda")
      OSUtils.kill_process("\"test-without-building -xctestrun /usr/local/.browserstack/config/wda_derived_data_\"", @device)
    end

    def build(scheme, derived_data_path, xcconfig_file_path = nil, build_type = 'build-for-testing', entitlements_file = nil)
      keychain_path = @server_config["appium_keychain"]
      xcode_version = Gem::Version.new(OSUtils.check_xcode.split[1])
      platform = xcode_version >= Gem::Version.new('15') ? 'iOS' : 'ios'

      keychain_password = @server_config["appium_keychain_password"]
      command = "security -v list-keychains -s #{keychain_path};" \
                "security -v unlock-keychain -p #{keychain_password} #{keychain_path};" \
                "security set-keychain-settings -t 3600 -l #{keychain_path};" \
                "xcodebuild #{build_type} -project #{@project_path} -scheme #{scheme} " \
                "-destination 'platform=#{platform},id=#{@device}' -configuration Debug " \
                "-derivedDataPath #{derived_data_path}"
      command += " -xcconfig #{xcconfig_file_path}" unless xcconfig_file_path.nil?
      command += " USE_PORT=8100"
      command += " CODE_SIGN_ENTITLEMENTS=#{entitlements_file}" unless entitlements_file.nil?
      command += " CODE_SIGN_IDENTITY=#{@profile.developer_certificate_sha1}"
      command += " 2>&1"

      output, exitstatus = OSUtils.execute("sudo su -l #{@server_config['user']} -c '#{command}'", true)

      [output, exitstatus]
    end

    def test_without_building(xctest_run_file_path, session_id)
      #TODO: maybe in a better way
      command = "/bin/bash #{@server_config['mobile_root']}/lib/utils/launch_xcode_build.sh #{@device} #{xctest_run_file_path} #{session_id}"
      BrowserStack.logger.info("Running xcodebuild command #{command}")
      pid = Process.spawn(command)
      Process.detach(pid)
      pid
    end

    def self.get_team_name_from_profile(provision_profile_path)
      temp_provision_plist = Tempfile.new(['mobile_provision', '.plist'], "/tmp")
      plist_data = OSUtils.execute("/usr/bin/security cms -D -k '#{Configuration['appium_keychain']}' -i '#{provision_profile_path}'").strip
      File.write(temp_provision_plist, plist_data)
      teamName, status = PlistBuddy.get_value_of_key(temp_provision_plist.path, ":TeamName", true)
      temp_provision_plist.unlink
      teamName = "" if status != 0
      teamName.strip
    end

    def self.get_entitlements_from_profile(provision_profile_path)
      temp_provision_plist = Tempfile.new(['mobile_provision', '.plist'], "/tmp")
      plist_data = OSUtils.execute("/usr/bin/security cms -D -k '#{Configuration['appium_keychain']}' -i '#{provision_profile_path}'").strip
      File.write(temp_provision_plist, plist_data)
      entitlements = PlistBuddy.get_value_of_key_in_xml(temp_provision_plist.path, ":Entitlements").strip
      temp_provision_plist.unlink
      entitlements
    end
  end
end
