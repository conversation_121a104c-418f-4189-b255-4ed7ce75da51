require_relative 'idevice_utils'
require_relative 'time_recorder'

module BrowserStack
  class JailbreakDetector
    include BrowserStack::TimeRecorder

    time_class_methods :check_jailbroken

    JAILBREAKING_APPS = %w[
      com.saurik.Cydia
      org.coolstar.SileoStore
      org.coolstar.electra
      com.electrateam.chimera
      science.xnu.undecimus
      supplies.wall.phoenix
      ]

    def self.check_jailbroken(udid)
      phone_apps = IdeviceUtils.list_apps(udid, kind: :all, attempts: 2)
      phone_apps_ids = phone_apps.map { |app| app[:bundle_id].downcase }

      JAILBREAKING_APPS.each do |jailbreaking_app|
        phone_apps_ids.each do |app|
          return app if app.include? jailbreaking_app
        end
      end

      nil
    end
  end
end
