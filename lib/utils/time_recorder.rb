require_relative './utils'
require_relative './method_interceptor'

module BrowserStack
  module TimeRecorder
    include MethodInterceptor

    def self.included(base_klass)
      base_klass.extend(ClassMethods)
      MethodInterceptor.included(base_klass)
    end

    def record_time(report_name, device_id = nil, &block)
      report = Report.new(report_name, device_id)
      report.record(&block)
    end

    module ClassMethods
      def time_methods(*method_names, threshold: nil)
        klass_name = name
        around_method(*method_names) do |original_method, method_name|
          device_id = self.device_id if respond_to? :device_id
          report = Report.new("#{klass_name.split('::').last}##{method_name}", device_id)
          report.record(threshold: threshold) { original_method.call }
        end
      end

      def time_class_methods(*method_names)
        klass_name = name
        around_class_method(*method_names) do |original_method, method_name|
          instance_device_id = device_id if respond_to? :device_id
          report = Report.new("#{klass_name.split('::').last}##{method_name}", instance_device_id)
          report.record { original_method.call }
        end
      end
    end

    class Report
      def initialize(name, device_id)
        @name = name
        @device_id = device_id
      end

      def record(threshold: nil)
        start_time = Time.now
        error = ""
        yield
      rescue => e
        error = "#{e.class} - #{e.message}"
        raise
      ensure
        payload = { data: Time.now - start_time }
        if threshold.nil? || payload[:data] > threshold
          payload["device"] = @device_id unless @device_id.nil?
          session_json = begin
            JSON.parse(File.read("#{BrowserStack::Configuration['state_files_dir']}/#{@device_id}_session"))
          rescue
            {}
          end
          payload["user_os"] = session_json["genre"] # Using user_os to store genre because it was always null for these kinds
          payload["session_id"] = session_json["session_id"]
          BrowserStack::Zombie.push_logs("timed-#{@name}", error, payload)
        end
      end
    end
  end
end
