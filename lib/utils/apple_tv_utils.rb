require 'ios_toolkit'

require_relative './utils'
require_relative './launch_agent'
require_relative '../device_conf'

class AppleTVUtils
  include BrowserStack

  class << self
    def update_wda_launch_agent(device_id, appium_version)
      AppleTVUtils.new(device_id).update_wda_launch_agent(appium_version)
    end

    def restart_wda(device_id)
      AppleTVUtils.new(device_id).restart_wda
    end

    def verify_wda_running(device_id)
      AppleTVUtils.new(device_id).verify_wda_running
    end

    def stop_wda_launch_agent(device_id)
      AppleTVUtils.new(device_id).stop_wda_launch_agent
    end

    def apple_tv_device?(device_id)
      IdeviceUtils.os(device_id) == 'tvos'
    end
  end

  attr_reader :device_id

  def initialize(device_id)
    @device_id = device_id
  end

  def verify_wda_running
    attempt = 0
    until wda_client.running?
      sleep 0.5
      attempt += 1
      next if attempt < 3

      raise WDALaunchError, "WDA is not running for Apple TV" unless wda_client.running?
    end
  end

  def update_wda_launch_agent(appium_version)
    unless wda_installed?
      wda_path = WDAVersion.tvos_ipa_path(device_id, appium_version)
      IdeviceUtils.install_app(device_id, wda_path, reinstall: false)

      raise WDAInstallError, "Couldn't install wda ipa" unless wda_installed?

      BrowserStack.logger.info("WDA installed successfully for device: #{device_id}")
    end
    wda_launch_agent.update
  end

  def stop_wda_launch_agent
    wda_launch_agent.unload
  end

  def restart_wda
    wda_launch_agent.update(force: true)
  end

  private

  def wda_installed?
    IdeviceUtils.list_apps(device_id).any? do |app|
      app[:bundle_id] == WDAVersion::BUNDLE_ID
    end
  end

  def wda_client
    @wda_client ||= WdaClient.new(DeviceConf[device_id]['webdriver_port'])
  end

  def wda_launch_agent
    @wda_launch_agent ||= LaunchAgent.new(
      svc_name: "wda_#{device_id}",
      cmd: IosToolkit::GoIos.device(device_id).run_wda_cmd(
        WDAVersion::BUNDLE_ID,
        xctest_config: 'WebDriverAgentRunner_tvOS.xctest'
      )
    )
  end
end
