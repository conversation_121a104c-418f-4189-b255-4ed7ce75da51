require_relative '../../config/constants'
require_relative '../configuration'

require 'browserstack_logger'

$conf = BrowserStack::Configuration.new.all
$devices_data = nil
RELEASE_TAG = begin
  JSON.parse(File.read("/usr/local/.browserstack/realmobile/.browserstack_build_version.json"))
rescue
  {}
end

def push_to_cls(params, message, error, json_data, update_message=nil, overrided_env=nil) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength

  genre = params["genre"]
  user_id = params["user_id"]

  data = {}

  if genre == 'live_testing'
    data[:product] = 'Live'
    data[:live_session_id] = params["live_session_id"]
    session_id_val = data[:live_session_id]
  elsif ['selenium', 'js_testing'].include?(genre)
    data[:product] = 'Automate'
    data[:automate_session_id] = params["automate_session_id"]
    session_id_val = data[:automate_session_id]
  elsif genre == 'app_automate'
    data[:product] = 'app-automate'
    data[:app_automate_session_id] = params["automate_session_id"]
    session_id_val = data[:app_automate_session_id]
  elsif genre == 'app_live_testing'
    data[:product] = 'AppLive'
    data[:app_live_session_id] = params["app_live_session_id"]
    session_id_val = data[:app_live_session_id]
  elsif genre == 'percy'
    data[:product] = 'Percy'
    data[:percy_comparison_id] = params["percy_comparison_id"]
    session_id_val = data[:percy_comparison_id]
  else
    return
  end

  current_time = Time.now.utc
  milliseconds = (current_time.to_f * 1000).to_i.to_s[-3..]
  data.merge!({
    "user_id" => user_id,
    "release_version" => RELEASE_TAG["tag"],
    "json_data" => json_data,
    "message" => message,
    "app_timestamp" => current_time.strftime("%Y-%m-%dT%H:%M:%S.#{milliseconds}Z"),
    "error" => error,
    "app" => "realmobile_platform"
  })

  if update_message
    data["update"] = session_id_val
    data["update_message"] = message
  end

  data[session_id_val] = @session_id

  @socket = UDPSocket.new
  begin
    if overrided_env == 'staging'
      data['json_data']['platform'] = 'iphone_staging'
      # CLS staging and CLS prod ports are same
      @socket.send(data.to_json, 0, $conf['cls_staging_url'], $conf['cls_port'])
    else
      @socket.send(data.to_json, 0, $conf['cls_url'], $conf['cls_port'])
    end
  rescue => e
    BrowserStack.logger.info "Error sending data to cls, dropping: #{e.message}"
  end
end

def canary?
  File.file?(IS_CANARY_FILE)
end

def deploy_env
  canary? ? "canary" : "not-canary"
end

# New device management helper methods
def all_devices(hard_refresh = false)
  # Reload device data if `hard_refresh` is true or if data is not already cached
  if hard_refresh || $devices_data.nil?
    begin
      device_info_file = $conf['config_json_file']
      config = JSON.parse(File.read(device_info_file))
      $devices_data = config['devices'] || nil
    rescue => e
      BrowserStack.logger.error "Error loading device config: #{e.message}"
      $devices_data = nil
    end
  end

  $devices_data
end

def device_info(device)
  devices = all_devices
  raise "FATAL: Malformed JSON file : #{$conf['config_json_file']}" if devices.nil?
  raise "FATAL: Cannot find device #{device}." if devices[device].nil?

  BrowserStack.logger.info "Device: #{device} is found"
  devices[device]
end

def device_version(device)
  device_info(device)['device_version']
rescue => e
  BrowserStack.logger.error "Error getting device version for #{device}: #{e.message}"
  nil
end

def device_name(device)
  device_info(device)['device_name']
rescue => e
  BrowserStack.logger.error "Error getting device name for #{device}: #{e.message}"
  nil
end
