require 'json'
require 'plist'
require 'browserstack_logger'
require 'time'

require_relative 'redis'
require_relative 'http_utils'
require_relative '../models/device_state'
require_relative '../custom_exceptions'
require_relative 'hooter'
require_relative './zombie'
require_relative './time_recorder'
require_relative '../helpers/browserstack_app_helper'
require_relative './custom_mdm_manager'
require_relative '../configuration'

module BrowserStack
  INTERNET_DOWN_MESSAGE = "Can't fetch installed profiles/internet down"

  # The is information about the MDM API here: https://developer.apple.com/business/documentation/MDM-Protocol-Reference.pdf
  # Go to Request Types (page 30)
  class IosMdmServiceClient # rubocop:todo Metrics/ClassLength
    include BrowserStack::TimeRecorder
    time_class_methods :run_mdm_ui_automation

    class << self
      def configure
        Zombie.configure
        mdm_server_url = Configuration['mdm_server_url']
        mdm_auth_username = Configuration['mdm_auth_username']
        mdm_auth_password = Configuration['mdm_auth_password']
        mdm_queue_timeout = Configuration['mdm_queue_timeout']

        raise 'Invalid mdm server url' unless HttpUtils.is_valid_url? mdm_server_url

        @server = mdm_server_url
        @mdm_command_api = "#{@server}/v1/commands"
        @basic_auth = {
          username: mdm_auth_username,
          password: mdm_auth_password
        }
        @mdm_queue_timeout = mdm_queue_timeout
        @ios_mdm_client_reporter = DataReportHelper.new('ios-mdm-client')
      end

      def send_post(payload)
        response = HttpUtils.send_post(@mdm_command_api, payload, @basic_auth)
        begin
          json_response = JSON.parse(response.body)
        rescue JSON::ParseError
          raise MdmApiException, "Could not decode JSON object for #{payload}"
        end

        if json_response["error"]
          raise MdmApiException, "Error for request #{payload} : #{json_response['error']}"
        else
          json_response["response_code"] = response.code
          json_response
        end
      end

      def mdm_last_seen(device)
        last_seen_string = "last_seen"
        result = get_device_json(device)
        raise MdmApiFatalException, "Device not on mdm" if result.nil?
        raise MdmApiFatalException, "Device on mdm but last_seen is empty" if result[last_seen_string].nil?

        BrowserStack.logger.info "Device #{device} last seen on MDM on #{result[last_seen_string]}"

        result[last_seen_string]
      end

      def check_device_on_mdm(device)
        BrowserStack.logger.info "Checking if device #{device} is on mdm"

        result = get_device_json(device)

        raise MdmApiFatalException, "Device not on mdm" if result.nil?
        raise MdmApiFatalException, "Device on mdm but enrollment_status is false, try re-mdm" unless result["enrollment_status"]

        BrowserStack.logger.info "Device #{device} is on mdm"
      end

      def is_mdm_server_up?

        HttpUtils.test_url_code("#{@server}/")
        true
      rescue => e
        raise MdmApiFatalException, "MDM server's SSL certificate has expired. #{@server}" if e.to_s.match(/SSL.*certificate verify failed/)

        BrowserStack.logger.error "MDM Server check failed #{@server}: #{e}"
        false
      end

      def get_installed_profiles(device, client=REDIS_CLIENT)
        BrowserStack.logger.info "Getting installed profiles for device #{device}"
        payload = { "request_type" => "ProfileList", "udid" => device }
        response = make_request(payload, client)
        BrowserStack.logger.info "Response in get_installed_profiles : #{response}"
        response
      rescue => e
        # TODO: rescue only timeouts
        BrowserStack.logger.error "Couldn't get profiles from mdm: #{e.message} - #{e.backtrace.join("\n")}"
        raise MdmApiException, "#{INTERNET_DOWN_MESSAGE}: #{e.message}, try re-mdm"
      end

      def get_installed_provisioning_profiles(device, client=REDIS_CLIENT)
        BrowserStack.logger.info "Getting installed provisioning profiles for device #{device}"
        payload = { "request_type" => "ProvisioningProfileList", "udid" => device }
        response = make_request(payload, client)
        BrowserStack.logger.info "Response in get_installed_profiles : #{response}"
        response
      rescue => e
        # TODO: rescue only timeouts
        BrowserStack.logger.error "Couldn't get profiles from mdm: #{e.message} - #{e.backtrace.join("\n")}"
        raise MdmApiException, "#{INTERNET_DOWN_MESSAGE}: #{e.message}, try re-mdm"
      end

      def remove_installed_provisioning_profile(device, uuid, client=REDIS_CLIENT)
        BrowserStack.logger.info "Removing installed provisioning profiles for device #{device}"
        payload = { "request_type" => "RemoveProvisioningProfile", "udid" => device, "uuid" => uuid }
        response = make_request(payload, client)
        BrowserStack.logger.info "Response in get_installed_profiles : #{response}"
        response
      rescue => e
        # TODO: rescue only timeouts
        BrowserStack.logger.error "Couldn't remove profiles from mdm: #{e.message} - #{e.backtrace.join("\n")}"
        raise MdmApiException, "#{INTERNET_DOWN_MESSAGE}: #{e.message}, try re-mdm"
      end

      # Returns all certificates for device:
      # MicroMDM, *.browserstack.com, mitmproxy, mitmproxy
      def get_certificates(device, client=REDIS_CLIENT)
        BrowserStack.logger.info "Getting certificates for device #{device}"
        payload = { "request_type" => "CertificateList", "udid" => device }
        response = make_request(payload, client)
        parse_certificates(response)
      rescue => e
        # TODO: rescue only timeouts
        BrowserStack.logger.error "Couldn't get certificates for device #{device}: #{e.message} - #{e.backtrace.join("\n")}"
        raise MdmApiException, "#{INTERNET_DOWN_MESSAGE}: #{e.message}"
      end

      def parse_certificates(get_certificates_response)
        parsed_certs = []
        certificateList = get_certificates_response["CertificateList"]
        certificateList.each do |cert|
          data = OpenSSL::X509::Certificate.new(cert["Data"].string)
          cert_hash = {
            name: cert["CommonName"],
            expired: data.not_after < Time.now,
            issue_date: data.not_before,
            expiry_date: data.not_after
          }
          parsed_certs << cert_hash
        end
        BrowserStack.logger.info "Parsed Certificates: #{parsed_certs}"
        parsed_certs
      end

      def expired_certificates(device_id)
        certificates = get_certificates(device_id, client = REDIS_CLIENT)
        expired_certs = []
        certificates.each do |cert|
          expired_certs << cert[:name] if cert[:expired]
        end
        BrowserStack.logger.info "Device #{device_id} has expired Certificates: #{expired_certs}"
        expired_certs
      end

      def run_mdm_ui_automation(device_id)
        BrowserStackAppHelper.run_ui_test(device_id, :mdm_auto_enrollment, 120, environment_variables: { mdm_url: @server })
      end

      def mdm_cert_expired?(device_id)
        certificates = begin get_certificates(device_id, client = REDIS_CLIENT)
        rescue MdmApiException => e
          BrowserStack.logger.info "Couldn't get any certificates from the device (#{e.message}), MDM re-enrollment may be required"
          []
        end

        if certificates.empty?
          true
        else
          mdm_cert_key = "MicroMDM Identity (%ComputerName%)"
          day = 60 * 60 * 24
          buffer_period = 7 * day
          mdm_cert = certificates.detect { |cert| cert[:name] == mdm_cert_key }
          expiry_date = mdm_cert[:expiry_date].is_a?(Time) ? mdm_cert[:expiry_date] : Time.parse(mdm_cert[:expiry_date])
          !mdm_cert[:expired] && (Time.now + buffer_period > expiry_date)
        end
      end

      def mdm_re_enrollment(device_id, force_enroll = false)
        success = false

        if force_enroll || mdm_cert_expired?(device_id)
          BrowserStack.logger.info "Device #{device_id} requires MDM re-enrollment."

          # Force removing safari cleanup file to ensure it runs in both success, failure of MDM automation
          BrowserStack.logger.info("Removing force safari cleanup file to trigger safari cleanup")
          device_state = DeviceState.new(device_id)
          device_state.remove_force_clean_safari_file

          # Run re-enrollment automation
          run_mdm_ui_automation(device_id)
          success = true
          BrowserStack.logger.info "Device #{device_id} has been re-enrolled into MDM."
          Zombie.push_logs("mdm-re-enrollment",  "successful", { "device" => device_id })
        else
          BrowserStack.logger.info "Device #{device_id} does not require MDM re-enrollment."
        end

        success
      rescue BrowserStackTestExecutionError => e
        Zombie.push_logs("mdm-re-enrollment", "MDM re-enrollment automation failed", { "device" => device_id, "data" => { "automation" => "failure" } })
        raise e
      end

      def set_restrictions(device, plist_payload, client=REDIS_CLIENT)
        BrowserStack.logger.info "Setting restrictions for device #{device}"
        payload = { "request_type" => "InstallProfile", "udid" => device, "payload" => Base64.encode64(plist_payload) } #Base64.encode raises exceptions with nil arg.
        make_request(payload, client)
      rescue => e
        BrowserStack.logger.error "Couldn't set restrictions for device #{device}: #{e.message} - #{e.backtrace.join("\n")}"
        raise MdmApiFatalException, "Could not set restrictions: #{e.message}"
      end

      # device: instance_id of the device
      # manifest_url: its a url to manifest file which contains information regarding app to install and also the url for the .ipa file of app
      # This is served from rails via /mdm_app_install_manifest in app_live controller for AppLive
      def install_enterprise_application(device, manifest_url, client=nil)
        client ||= REDIS_CLIENT
        BrowserStack.logger.info "Installing enterprise application using MDM for device #{device}"
        payload = { "request_type" => "InstallApplication", "udid" => device, "manifest_url" => manifest_url }
        make_request(payload, client)
      rescue => e
        BrowserStack.logger.error "Couldn't install enterprise application using MDM for device for device #{device}: #{e.message} - #{e.backtrace.join("\n")}"
        raise MdmApiFatalException, "Could not install enterprise application using MDM for device: #{e.message}"
      end

      def remove_application(device, bundle_id)
        client ||= REDIS_CLIENT
        BrowserStack.logger.info "Uninstalling application using MDM for device #{device}"
        payload = { "request_type" => "RemoveApplication", "udid" => device, "Identifier" => bundle_id }
        make_request(payload, client)
      rescue => e
        BrowserStack.logger.error "Couldn't uninstall application using MDM for device #{device}: #{e.message} - #{e.backtrace.join("\n")}"
        raise MdmApiFatalException, "Could not uninstall application using MDM for device: #{e.message}"
      end

      def install_application(device, bundle_id, client=nil, options={})
        client ||= REDIS_CLIENT
        BrowserStack.logger.info "Installing Application #{bundle_id} on device for device #{device}"
        payload = { "request_type" => "InstallApplication", "udid" => device, "Identifier" => bundle_id, "options" => options } #Base64.encode raises exceptions with nil arg.
        make_request(payload, client)
      rescue => e
        BrowserStack.logger.error "Could not Install Application #{bundle_id} on device #{device}: #{e.message} - #{e.backtrace.join("\n")}"
        raise MdmApiFatalException, "Could not Install Application: #{e.message}"
      end

      #unused, will be needed if we need to revert.
      def remove_restrictions(device, client=REDIS_CLIENT)
        BrowserStack.logger.info "Removing restrictions for device #{device}"
        payload = { "request_type" => "RemoveProfile", "udid" => device, "Identifier" => "mdm.restrictions.#{device}" }
        make_request(payload, client)
      rescue => e
        BrowserStack.logger.error "Couldn't remove restrictions for device #{device}: #{e.message} - #{e.backtrace.join("\n")}"
        raise MdmApiFatalException, "Could not remove restrictions: #{e.message}, try re-mdm"
      end

      def install_profile(device, profile, plist_payload, client=REDIS_CLIENT)
        BrowserStack.logger.info "Installing #{profile} profile on device #{device}"
        payload = { "request_type" => "InstallProfile", "udid" => device, "payload" => Base64.encode64(plist_payload) } #Base64.encode raises exceptions with nil arg.
        make_request(payload, client)
      rescue => e
        BrowserStack.logger.error "Couldn't install #{profile} profile on device #{device}: #{e.message} - #{e.backtrace.join("\n")}"
        raise MdmApiFatalException, "Couldn't install #{profile}: #{e.message}"
      end

      def remove_profile(device, profile, identifier, client=REDIS_CLIENT)
        BrowserStack.logger.info "Removing #{profile} profile on device #{device}"
        payload = { "request_type" => "RemoveProfile", "udid" => device, "Identifier" => identifier }
        make_request(payload, client)
      rescue => e
        BrowserStack.logger.error "Couldn't remove #{profile} profile: #{e.message} - #{e.backtrace.join("\n")}"
        raise MdmApiFatalException, "Couldn't remove #{profile} profile: #{e.message}"
      end

      def install_mitmproxy_certificate(device, plist_payload, client=REDIS_CLIENT)
        BrowserStack.logger.info "Installing mitmproxy cert for device #{device}"
        payload = { "request_type" => "InstallProfile", "udid" => device, "payload" => Base64.encode64(plist_payload) }
        make_request(payload, client)
      rescue => e
        BrowserStack.logger.error "Could not install mitmproxy root certificate: #{e.message} - #{e.backtrace.join("\n")}"
        raise MdmApiFatalException, "Could not install mitmproxy root certificate: #{e.message}"
      end

      # Unused.
      def remove_mitmproxy_certificate(device, payload_identifier_suffix = '', client=REDIS_CLIENT)
        BrowserStack.logger.info "Removing mitmproxy cert for device #{device}"
        payload = { "request_type" => "RemoveProfile", "udid" => device, "Identifier" => "mdm.mitmproxy#{payload_identifier_suffix}.#{device}" }
        make_request(payload, client)
      rescue => e
        BrowserStack.logger.error "Could not remove mitmproxy root certificate: #{e.message} - #{e.backtrace.join("\n")}"
        raise MdmApiFatalException, "Could not remove mitmproxy root certificate: #{e.message}"
      end

      def set_proxy(device, plist_payload, client=REDIS_CLIENT)
        BrowserStack.logger.info "plist_payload: #{plist_payload}"
        begin
          payload = { "request_type" => "InstallProfile", "udid" => device, "payload" => Base64.encode64(plist_payload) } #Base64.encode raises exceptions with nil arg.
          make_request(payload, client)
        rescue => e
          BrowserStack.logger.error "Could not set proxy: #{e.message} - #{e.backtrace.join("\n")}"
          raise MdmApiFatalException, "Could not set proxy: #{e.message}"
        end
      end

      # Unused
      def unset_proxy(device, client=REDIS_CLIENT)
        payload = { "request_type" => "RemoveProfile", "udid" => device, "identifier" => "mdm.#{device}" }
        make_request(payload, client)
      rescue => e
        BrowserStack.logger.error "Could not unset proxy: #{e.message} - #{e.backtrace.join("\n")}"
        raise MdmApiException, "Could not unset proxy: #{e.message}"
      end

      def lock_device(device, client=REDIS_CLIENT)
        payload = { "request_type" => "DeviceLock", "udid" => device }
        make_request(payload, client)
      rescue => e
        BrowserStack.logger.info "mdm screen lock failed #{e.message}"
        raise MdmApiFatalException, "Could not lock device: #{e.message}"
      end

      def restart_device(device, client=REDIS_CLIENT)

        payload = { "request_type" => "RestartDevice", "udid" => device }
        make_request(payload, client)
      rescue => e
        BrowserStack.logger.error "MDM Restart device failed for #{device} : #{e.message}"

      end

      def factory_reset_device(device, client=REDIS_CLIENT)

        payload = { "request_type" => "EraseDevice", "udid" => device }
        make_request(payload, client)
      rescue => e
        BrowserStack.logger.info "Factory reset failed #{e.message}"

      end

      def clear_passcode(udid, client=REDIS_CLIENT)
        payload = { "request_type" => "ClearPasscode", "udid" => udid }
        make_request(payload, client)
      end

      def set_timezone(udid, timezone, client=REDIS_CLIENT)
        payload = { "request_type" => "Settings", "udid" => udid, "Settings" => [{ "Item" => "TimeZone", "time_zone" => timezone }] }
        make_request(payload, client)
        true
      rescue => e
        BrowserStack.logger.error "MDM Set timezone failed for #{udid} : #{e.message}"
        false
      end

      def get_itunes_login_status(device, client=REDIS_CLIENT)
        BrowserStack.logger.info "Getting status of itunes login on device #{device}"
        payload = { "request_type" => "DeviceInformation", "udid" => device, "queries" => ["iTunesStoreAccountIsActive"] }
        response = make_request(payload, client)
        BrowserStack.logger.info "Response in get_itunes_login_status : #{response}"
        response["QueryResponses"]["iTunesStoreAccountIsActive"]
      rescue => e
        BrowserStack.logger.error "Couldn't get get_itunes_login_status from mdm: #{e.message} - #{e.backtrace.join("\n")}"
        true # returning true to ensure appstore cleanup run since mdm command failed, UI automation also has early exits
      end

      def disable_bluetooth(device, client=REDIS_CLIENT)
        start_time = Time.now.to_i

        BrowserStack.logger.info "Disable bluetooth on device #{device}"
        payload = { "request_type" => "Settings", "udid" => device, "Settings" => [
          {
            "Item" => "Bluetooth",
            "Enabled" => false
          }
        ] }
        response = make_request(payload, client)
        BrowserStack.logger.info "Response in disable_bluetooth : #{response}"

        end_time = Time.now.to_i

        success_data = @ios_mdm_client_reporter.populate_data_hash(
          additional_data: {
            command: "disable_bluetooth",
            mdm_response: response,
            device: device
          },
          interval: end_time - start_time
        )

        @ios_mdm_client_reporter.report(success_data)

        response
      rescue => e
        end_time = Time.now.to_i
        BrowserStack.logger.error "Couldn't reset bluetooth from mdm: #{e.message} - #{e.backtrace.join("\n")}"

        failure_data = @ios_mdm_client_reporter.populate_data_hash(
          additional_data: {
            command: "disable_bluetooth",
            mdm_response: response,
            device: device,
            error_message: e.message
          },
          interval: end_time - start_time
        )

        @ios_mdm_client_reporter.report(failure_data)

        raise MdmApiFatalException, "Could not disable bluetooth: #{e.message}"
      end

      def set_text_size(udid, text_size, client=REDIS_CLIENT)
        payload = { "request_type" => "Settings", "udid" => udid, "Settings" => [{ "Item" => "AccessibilitySettings", "text_size" => text_size }] }
        make_request(payload, client)
      rescue => e
        BrowserStack.logger.error "MDM Set Text Size failed for #{udid} : #{e.message}"
        raise e
      end

      private

      # POST /v1/devices
      # TODO: this supports a post body,
      # which can be used for filter by device_id on MDM's end,
      def get_device_json(device)
        result = {}
        begin
          response = HttpUtils.send_post("#{@server}/v1/devices", {}, @basic_auth)
          json_response = JSON.parse(response.body)
          result = json_response["devices"].find do |x|
            x["udid"] == device && x["serial_number"] == device_config(device)["device_serial"]
          end

          BrowserStack.logger.info "Result: #{result.inspect}"
        rescue => e
          BrowserStack.logger.error "Couldn't check if device is on mdm: #{e.class} - #{e.message}"
          # TODO: rescue only timeout exceptions & raise other exceptions as MdmApiFatalException
          raise MdmApiException, "mdm check failed: #{e.class}"
        end

        result
      end

      def device_config(device)
        device_config ||= begin
          JSON.parse(File.read(BrowserStack::Configuration['config_json_file']))["devices"][device]
        rescue => e
          log :warn, "Unable to load device config"
          {}
        end
      end

      def is_eks?(channel)
        channel.end_with?("_EKS")
      end

      def make_request(payload, client)

        start_time = Time.now

        device = payload["udid"]
        raise "CustomMDM device does not support MDM commands" if CustomMDMManager.is_custom_mdm_device?(device)

        json_response = send_post(payload)
        channel = json_response["payload"]["CommandUUID"]

        ack = wait_response(channel, client, device)
        response_time = (Time.now - start_time).to_i

        # There is a weird dependency of Zombie on BrowserStack.logger
        # BrowserStack.logger reamins undefined if there is no request on the
        # server
        if BrowserStack.logger
          Zombie.configure
          Zombie.push_logs("mdm-response-time", payload["request_type"] || "unknown", { "device" => device, 'data' => response_time })
        end

        BrowserStack.logger.info %(
          START*********************#########*************START
          MDM-Command: #{payload['request_type']}
          MDM-Reponse: #{json_response}
          MDM-Ack : #{ack}
          END**********************#########***************END
        )
        raise "Empty Response on channel #{channel}" unless ack

        case ack["Status"]
        when "Acknowledged"
          ack
        when "Error"
          error_message = begin
            ack["ErrorChain"].last["USEnglishDescription"]
          rescue
            nil
          end
          raise(error_message || "Unknown Error")
        else
          raise "Bad Response from MDM: #{ack['Status']} on channel: #{channel}"
        end
      end

      # this method is used only for install app via vpp
      # check the blast radius of make_request method and try to read LocalizedDescription from there itself
      def make_request_for_install_app(payload, client)
        start_time = Time.now

        device = payload["udid"]
        json_response = send_post(payload)
        channel = json_response["payload"]["CommandUUID"]
        ack = wait_response(channel, client, device)
        response_time = (Time.now - start_time).to_i

        # There is a weird dependency of Zombie on BrowserStack.logger
        # BrowserStack.logger reamins undefined if there is no request on the
        # server
        if BrowserStack.logger
          Zombie.configure
          Zombie.push_logs("mdm-response-time", payload["request_type"] || "unknown", { "device" => device, 'data' => response_time })
        end

        BrowserStack.logger.info %(
          START*********************#########*************START
          MDM-Command: #{payload['request_type']}
          MDM-Reponse: #{json_response}
          MDM-Ack : #{ack}
          END**********************#########***************END
        )
        raise "Empty Response on channel #{channel}" unless ack

        case ack["Status"]
        when "Acknowledged"
          ack
        when "Error"
          error_message = begin
            ack["ErrorChain"].last["USEnglishDescription"] || ack["ErrorChain"].last["LocalizedDescription"]
          rescue
            nil
          end
          raise(error_message || "Unknown Error")
        else
          raise "Bad Response from MDM: #{ack['Status']} on channel: #{channel}"
        end
      end

      def wait_response(channel, client, device_id = "")

        if (response = get_response_from_key(channel, client, device_id))
          return response
        end

        begin
          # We need to create a new client for doing subscribe, as these clients cannot be re-used for GET
          BrowserStack.logger.info "Subscribing to client #{channel}"

          sub_client = BrowserStack::RedisUtils.client

          sub_client.subscribe_with_timeout(@mdm_queue_timeout, channel) do |on|
            on.message do |ch, message|
              response = Plist.parse_xml(message)
              sub_client.unsubscribe(ch)
            end
          end
        rescue Redis::TimeoutError => e
          BrowserStack.logger.info "Timeout error in redis in function wait_response: #{e.message}"
          response = get_response_from_key(channel, client, device_id)
          raise e unless response
        end
        response
      end

      def get_response_from_key(channel, client, device_id = "")
        BrowserStack.logger.info "client connection details: #{client.id}"
        if (response = client.get(channel))
          BrowserStack.logger.info "Got response #{response.inspect}"
          if response.is_a? Array
            if response[0] == 'message'
              Plist.parse_xml(response[2])
            else
              BrowserStack.logger.warn "Not sure how to handle this message #{response.inspect}. Falling back to subscribe"
              false
            end

          else
            Plist.parse_xml(response)
          end
        else
          BrowserStack.logger.info "Could not get key from redis as key not present. Need to subscribe and wait"
          false
        end
      rescue => e
        BrowserStack.logger.warn "Could not get key from redis. Falling back to subscribe. Error: #{e.message} #{e.backtrace.join("\n")}"
        Zombie.push_logs("mdm-redis-timeout",  "", { "device" => device_id, 'data' => e.message })
        false

      end
    end
  end
end

if __FILE__ == $PROGRAM_NAME
  BrowserStack::IosMdmServiceClient.configure

  device_id = ARGV[1]
  case ARGV[0]
  when 'last_seen'
    BrowserStack::IosMdmServiceClient.mdm_last_seen(device_id)
  when 'check_device_on_mdm'
    BrowserStack::IosMdmServiceClient.check_device_on_mdm(device_id)
  when 'get_installed_profiles'
    BrowserStack::IosMdmServiceClient.get_installed_profiles(device_id, client = REDIS_CLIENT)
  when 'get_installed_provisioning_profiles'
    BrowserStack::IosMdmServiceClient.get_installed_provisioning_profiles(device_id, client = REDIS_CLIENT)
  when 'remove_installed_provisioning_profile'
    BrowserStack::IosMdmServiceClient.remove_installed_provisioning_profile(device_id, ARGV[2], client = REDIS_CLIENT)
  when 'get_certificates'
    BrowserStack::IosMdmServiceClient.get_certificates(device_id, client = REDIS_CLIENT)
  when 'clear_passcode'
    BrowserStack::IosMdmServiceClient.clear_passcode(device_id, client = REDIS_CLIENT)
  when 'set_timezone'
    BrowserStack::IosMdmServiceClient.set_timezone(device_id, ARGV[2], client = REDIS_CLIENT)
  when 'factory_reset_device'
    # This will remove the device from MDM
    BrowserStack::IosMdmServiceClient.factory_reset_device(device_id, client = REDIS_CLIENT)
  when 'lock_device'
    BrowserStack::IosMdmServiceClient.lock_device(device_id, client = REDIS_CLIENT)
  when 'restart_device'
    BrowserStack::IosMdmServiceClient.restart_device(device_id, client = REDIS_CLIENT)
  when 'mdm_re_enrollment'
    BrowserStack::IosMdmServiceClient.run_mdm_ui_automation(device_id)
  # This is used in an ansible script to check for expired certificates
  when 'install_application'
    BrowserStack::IosMdmServiceClient.install_application(device_id, ARGV[2], client = REDIS_CLIENT)
  when 'expired_certificates'
    expired_certs = BrowserStack::IosMdmServiceClient.expired_certificates(device_id)
    # Printing directly to stderr to avoid ruby Exception backtrace in ansible output.
    warn("#{device_id} has expired certs: #{expired_certs}") unless expired_certs.empty?
  when 'get_itunes_login_status'
    BrowserStack::IosMdmServiceClient.get_itunes_login_status(device_id, client = REDIS_CLIENT)
  else
    BrowserStack.logger.error "Method #{ARGV[0]}, is not listed."
  end
end
