require 'fileutils'
require 'tempfile'
require 'json'
require 'shellwords'
require_relative '../configuration'
require_relative '../custom_exceptions'
require_relative './utils'
require_relative './xcode_utils'
require_relative './osutils'
require_relative './idevice_utils'
require_relative './plist_buddy'
require_relative './xcode_build_utils'
require_relative './helpers'
require_relative '../helpers/wda_client'
require_relative '../../lib/models/device_state'
require_relative '../../config/constants'
require_relative '../provisioning/ppuid_file'

module BrowserStack
  module WebDriverAgent # rubocop:disable Metrics/ModuleLength
    def self.launch(device, mini_ip, session_params) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
      dup_session_params = JSON.parse(File.read(BrowserStack::IPhone.session_file(device)))
      # deleting app_display_name because they may contain special characters
      # like ', or diacritics like ó, which fails adding this to xctest_run_file,
      # so removing it, not being used anywhere
      dup_session_params.delete("app_display_name")
      dup_session_params["mini_ip"] = mini_ip
      config_file = Configuration.new.all
      dup_session_params[:cls_servers] = []
      dup_session_params[:allow_long_press] = session_params[:allow_long_press] || "false"
      dup_session_params[:enable_settings_app_access] = session_params[:enable_settings_app_access] || "false"
      dup_session_params[:cls_servers] << { "type" => "CLS", "host" => config_file['cls_url'], "port" => config_file['cls_port'] }
      iproxy_port = dup_session_params["iproxyPort"]
      use_v2_endpoint = session_params[:ios_should_use_set_rtcdata_v2_endpoint].to_s == "true"
      dup_session_params = dup_session_params.slice(*AUTOMATE_APP_AUTOMATE_RTC_PARAMS) if [GENRE_AUTOMATE, GENRE_APP_AUTOMATE].include?(dup_session_params["genre"])
      APP_LIVE_RTC_PARAMS_BLACKLIST.each { |key| dup_session_params.delete(key) } if dup_session_params["genre"] == APP_LIVE_TESTING
      (1..5).each do |retry_number|
        BrowserStack.logger.info "[/bs/set_rtcdata_and_start]: Start. Count: #{retry_number}"
        begin
          dup_session_params['set_rtcdata_request_count'] = retry_number
          if session_params[:useReplayKit].to_s == "true"
            WdaClient.new(iproxy_port).set_rtc_data_and_start_replay_kit(dup_session_params, (session_params["use_blank_url"] ? '' : session_params["url"]), session_params["device_browser"], session_params[:genre], use_v2_endpoint)
          else
            WdaClient.new(iproxy_port).set_rtc_data_and_start(dup_session_params)
          end

          # update port if wda is successful
          session_params["iproxyPort"] = iproxy_port
          Utils.write_to_file_with_lock(BrowserStack::IPhone.session_file(device), session_params.to_json)

          BrowserStack.logger.info "[/bs/set_rtcdata_and_start]: Successful. Count: #{retry_number}"
          push_to_cls(dup_session_params, "live_session_report", "", { "set_rtcdata_request_count": retry_number }, true)
          send_launch_log_to_eds(dup_session_params, "WDALaunch")
          break
        rescue => e
          BrowserStack.logger.error "[/bs/set_rtcdata_and_start]: Failed. Count: #{retry_number}. Error: #{e.message}"
          sleep(1)
        end
      end

      # We need to launch it separately because if the launcher app is active and then
      # the browser is launched the fps drops to 0 very frequently
      # The additional delay is to let webrtc offer exchange to complete.
      if session_params["genre"] == "live_testing"
        # Tracking to be used in cleanup to skip Safari cleanup if not required
        # Do not touch the file if it's a chrome session. If Safari is launched inside the session,
        # it is detected by the device-logger.
        DeviceState.new(device).touch_safari_live_session_file if session_params["device_browser"].to_s.casecmp("chrome") != 0

        return if session_params["live_apple_pay_session"].to_s == "true"
        # browser launch gets overriden during sim setup, so we are launching browser at end of sim setup, so returning early here.
        return if session_params["enable_sim_live"].to_s == "true"

        if session_params[:useReplayKit].to_s != "true"
          sleep(1.5)
          Utils.fork_code_block_for_device(device) do
            launch_browser_with_url(
              (session_params["use_blank_url"] ? '' : session_params["url"]),
              session_params["device_browser"],
              iproxy_port,
              session_params["device_version"].to_f
            )
          end
        end

      end
    end

    def self.launch_browser_with_url(url, device_browser, wda_port, ios_version)
      BrowserStack.logger.info "[bs/live/launch_browser_with_url]: Start."
      WdaClient.new(wda_port).launch_browser_with_url(url, device_browser, ios_version)
      BrowserStack.logger.info "[bs/live/launch_browser_with_url]: Successful."
    end

    def self.stop(device)
      BrowserStack.logger.info("Stoping wda")
      xcodebuild = XcodeBuildUtils.new(device)
      xcodebuild.stop_wda_xcodebuild
    rescue => e
      BrowserStack.logger.error "Failed to kill wda xcodebuild #{e}"
    end

    def self.get_xcconfig_data(scheme, device)
      config_file = Configuration.new.all
      ppuid_file = "#{config_file['config_root']}/ppuid_#{device}"
      ppuid_config = File.read(ppuid_file)
      development_team = ppuid_config.lines[1].strip
      prov_id = ppuid_config.lines[2].strip
      device_config = DeviceConf[device]
      data = {}

      data = if scheme == "BrowserStack"
               {
                 "DEVELOPMENT_TEAM" => development_team,
                 "PROVISIONING_PROFILE_SPECIFIER_#{scheme}" => prov_id,
                 "PROVISIONING_PROFILE_SPECIFIER_BrowserStackUITests" => prov_id,
                 "PROVISIONING_PROFILE_SPECIFIER" => "$(PROVISIONING_PROFILE_SPECIFIER_$(PRODUCT_NAME))"
               }
             else
               {
                 "DEVELOPMENT_TEAM_WebDriverAgentRunner" => development_team,
                 "DEVELOPMENT_TEAM_WebDriverAgentLib" => development_team,
                 "DEVELOPMENT_TEAM" => "$(DEVELOPMENT_TEAM_$(PRODUCT_NAME))",
                 "PROVISIONING_PROFILE_SPECIFIER_WebDriverAgentRunner" => prov_id,
                 "PROVISIONING_PROFILE_SPECIFIER" => "$(PROVISIONING_PROFILE_SPECIFIER_$(PRODUCT_NAME))"
               }
             end
      data["IPHONEOS_DEPLOYMENT_TARGET"] = "15.0" if device_config["device_version"].to_i >= 18
      data
    end

    def self.build_project_with_xcconfig(device, project_path, derived_data_path, target, scheme, build_type)
      config_file = Configuration.new.all
      xcconfig_data = WebDriverAgent.get_xcconfig_data(target, device)
      temp_xcconfig_file = Tempfile.new([device, 'xcconfig'], "/tmp")
      FileUtils.chown_R(config_file["user"], nil, temp_xcconfig_file.path) if Etc.getpwuid(File.stat(temp_xcconfig_file.path).uid).name != config_file["user"]
      XcodeUtils.create_xcconfig_file(xcconfig_data, temp_xcconfig_file.path)
      temp_xcconfig_file.close

      provision_profile = BrowserStack::IPhone.get_device_provisioning_profile(device)
      provisioning_profile_dir = config_file["provisioning_profile_dir"]

      temp_entitlements_file = Tempfile.new(['entitlements', '.plist'])
      entitlements_data = XcodeBuildUtils.get_entitlements_from_profile("#{provisioning_profile_dir}/#{provision_profile}.mobileprovision")
      File.write(temp_entitlements_file, entitlements_data)

      xcodebuild = XcodeBuildUtils.new(device, project_path)
      output, exit_code = xcodebuild.build(scheme, derived_data_path, temp_xcconfig_file.path, build_type, temp_entitlements_file.path)
      temp_xcconfig_file.unlink
      temp_entitlements_file.unlink

      [output, exit_code]
    end

    def self.use_new_path_without_date?(device)
      device_state = DeviceState.new(device)
      device_state.touch_tmp_use_path_without_date_file unless device_state.tmp_use_path_without_date_file_present?
      rebooted_minutes_ago = (Time.now.to_i - BrowserStack::OSUtils.last_machine_boottime) / 60
      device_state.tmp_use_path_without_date_file_older_than_minutes?(rebooted_minutes_ago)
    end

    def self.provision_profile_identifier(device)
      ppuid_file = PpuidFile.new(device)
      if IdeviceUtils.use_devicectl(device) && use_new_path_without_date?(device)
        ppuid_file.branch_name_without_date
      else
        ppuid_file.ppuid
      end
    rescue => e
      BrowserStack.logger.error "Could not calculate the profile identifier: #{e}"
      ppuid_file.ppuid
    end

    def self.build_browserstack_app(device) # rubocop:disable Metrics/AbcSize
      if IdeviceUtils.device_offline_on_usb(device)
        BrowserStack.logger.info "device not reachable"
        return
      end

      config_file = Configuration.new.all
      ppuid_file = "#{config_file['config_root']}/ppuid_#{device}"
      prov_id = provision_profile_identifier(device)
      derived_data_path = "#{config_file['browserstack_app_dir']}/#{prov_id}"
      project_path = (config_file['browserstack_app_xcodeproj']).to_s
      BrowserStack.logger.info "Starting xcodebuild for BrowserStack app, purging derived data: #{derived_data_path}"

      FileUtils.rm_rf(derived_data_path)

      output, exit_code = WebDriverAgent.build_project_with_xcconfig(device, project_path, derived_data_path, "BrowserStack", "BrowserStack", "build-for-testing")

      if exit_code == 0
        BrowserStack.logger.info("BrowserStack App Build Successfull")
        # Creates a file to show the app has been built successfully, storing the version also.
        version_file = "/usr/local/.browserstack/ios-njb-app/BrowserStackUITests/Info.plist"
        build_version = PlistBuddy.get_value_of_key(version_file, "CFBundleVersion").strip

        # Used by Nomad for health checks: ios-njb-app/deploy/health_checks/app_build_success.sh
        build_success_file = "#{config_file['state_files_dir']}/browserstack_app_built_#{device}"
        File.open(build_success_file, 'w') { |file| file.write(build_version) }

        # To avoid parallel builds for same prov_id
        build_success_file_prov_id = "#{config_file['state_files_dir']}/browserstack_app_built_for_prov_id_#{prov_id}"
        File.open(build_success_file_prov_id, 'w') { |file| file.write(build_version) }
      elsif output.match(/Developer Mode/i)
        BrowserStack.logger.error("Could not build BrowserStack app, enable developer mode on the device")
        raise XcodeBuildFailedException, "manual fix required: enable Developer Mode"
      else
        BrowserStack.logger.error("Could not build BrowserStack app, exit code: #{exit_code}, output: #{output}")

        if exit_code.to_i == 70
          BrowserStack.logger.info "Issue is with the device, please check output"
        else
          BrowserStack.logger.info "FIXME purging derived data: #{derived_data_path} as build failed"
          # Purging derived data is not the best thing to do as
          # This will cause offlines if other devices are on the same prov_id
          # (hence the same derived data)
          FileUtils.rm_rf(derived_data_path)
        end

        raise XcodeBuildFailedException, "BrowserStack App xcodebuild failed with: #{exit_code}"
      end
    end

    def self.xctestrun_file_path(device_id, appium_version, ios_version)
      config = BrowserStack::Configuration.new.all
      "#{WDAVersion.device_wda_folder_path(device_id, appium_version, ios_version)}/Build/Products"
    end

    def self.send_launch_log_to_eds(session_params, event_name)
      if session_params["genre"] == "app_live_testing"
        web_event = EdsConstants::APP_LIVE_WEB_EVENTS
        session_id = session_params["app_live_session_id"]
      else
        web_event = EdsConstants::LIVE_WEB_EVENTS
        session_id = session_params["live_session_id"]
      end

      data = {
        session_id: session_id,
        event_name: event_name,
        set_rtcdata_request_count: session_params['set_rtcdata_request_count']
      }
      Utils.send_to_eds(data, web_event, true)
    end
  end
end
