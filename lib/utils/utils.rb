require 'socket'
require 'uri'
require 'net/http'
require 'json'
require 'open3'
require 'fileutils'
require 'time'
require 'bsdwh'
require 'aws-sdk-s3'
require 'timeout'

require "/usr/local/.browserstack/mobile-common/utils/screenshot_util"
require_relative 'http_utils'
require_relative 'time_recorder'
require_relative 'zombie'
require_relative '../configuration'
require_relative '../device_logger_metric'
require_relative '../custom_exceptions'
require_relative '../utils/idevice_utils'
require_relative '../models/device_state'
require_relative '../../config/constants'
require "/usr/local/.browserstack/mobile-common/utils/constants"

THRESHOLD_FOR_STANDARD_STORAGE_CLASS = 131072

class Utils # rubocop:todo Metrics/ClassLength
  include BrowserStack::TimeRecorder

  time_class_methods :upload_app_live_logs

  class << self

    def empty?(str)
      if str.nil? || str.to_s.empty?
        true
      else
        false
      end
    end

    def array_and_non_empty?(arr)
      !arr.nil? && arr.is_a?(Array) && !arr.empty?
    end

    def add_app_to_cls_tracking_file(bundle_id, device_id)
      apps_tracking_dir = "/tmp/#{device_id}_installed_apps"
      FileUtils.mkdir_p apps_tracking_dir
      File.open("#{apps_tracking_dir}/UPLOADED_APPS", 'a') { |f| f.write("#{bundle_id}\n") }
    end

    def write_as_root(filename, data)
      `V='#{data}'; sudo sh -c "printf '$V' > #{filename}"`
    end

    def with_lock(file, lock_timeout = 5, &block)
      File.open("#{file}.lock", "w+") do |f|
        Timeout.timeout(lock_timeout) { f.flock(File::LOCK_EX) }
        yield
      rescue Timeout::Error => e
        raise LockfileTimeoutError, "Timeout of #{lock_timeout} reached trying to acquire lock for file #{f}"
      ensure
        f.flock(File::LOCK_UN)
      end
    end

    def write_to_file(filename, data, params = {})

      File.open(filename, 'w+') { |file| file.write(data) }
      raise "file write failed" unless File.exist?(filename)
    rescue => e
      BrowserStack::Zombie.push_logs("file-write-failed", "file write failed", { "data" => data }, nil, params)
      BrowserStack.logger.info "Failed to write the file : #{filename} , #{e.message} #{e.backtrace}"

    end

    def touch_file_and_create_parents(filepath)
      dir = File.dirname(filepath)
      FileUtils.mkdir_p(dir) unless File.exist?(dir)
      FileUtils.touch(filepath)

      raise "Failed to touch file at: #{filepath}" unless File.exist?(filepath)
    end

    def write_to_file_with_lock(file_name, data, lock_timeout: 5)
      with_lock(file_name) do
        write_to_file(file_name, data, data)
      end
    end

    def read_first_line(filename)
      File.open(filename, &:gets).strip
    end

    def read_third_line(filename)
      file = File.open(filename)
      2.times { file.gets }
      file.gets.strip
    end

    def read_json_file(filename)
      JSON.parse(File.read(filename))
    end

    def read_file_in_array(filename)
      File.read(filename).strip.split("\n")
    end

    def write_array_to_file!(filename, array)
      # Overwrites the file
      File.open(filename, 'w') { |f| f.write(array.join("\n")) }
    end

    def substring_match?(test, filter_test, module_name)
      # test format : module_name/class_name/test_name
      # filter can be equal or substring of test
      test_cases = test.split('/')
      filter_test_cases = filter_test.split('/')
      substring_matching = false

      if test_cases.length == filter_test_cases.length
        substring_matching = true if test_cases != filter_test_cases
      elsif filter_test_cases.first == module_name
        substring_matching = true if filter_test_cases.length > 1 && test_cases[1] != filter_test_cases[1]
      else
        substring_matching = true
      end

      substring_matching
    end

    # Returns true if the process with pid <pid> is currently running, false otherwise.
    def process_running?(pid)
      pid = pid.to_i

      begin
        Process.getpgid(pid)
        true
      rescue Errno::ESRCH
        false
      end
    end

    def fork_process(cmd, options = {})
      pid = fork do
        Process.setproctitle(options[:process_name]) unless options[:process_name].nil?
        stdout, stderr, status = capture3(cmd)
        callback = options[:callback]
        callback[:block].call(stdout, stderr, status, callback[:args]) unless callback.nil?
      end
      Process.detach(pid)
      return pid if options[:pid_file].nil?

      File.open(options[:pid_file], 'w') { |f| f.write(pid) }
    end

    def write_config_with_lock(file, data)
      File.open(file, File::RDWR | File::CREAT, 0o644) do |f|
        f.flock(File::LOCK_EX)
        f.rewind
        f.write(data)
        f.flush
        f.truncate(f.pos)
      end
    end

    def enter_keys_and_wait(driver, send_key, wait_for)
      BrowserStack.logger.info "Sending string #{send_key} and waiting for #{wait_for}"
      send_key.each_char do |char|
        BrowserStack.logger.info "Sending char: #{char}"
        f = driver.find_ele_by_attr_include("XCUIElementTypeKey", "name", char.to_s)
        f.click
        find_element, visible = check_if_element_present(driver, wait_for)
        find_element.click && break if visible
      end
    end

    def wait_for_element(driver, wait_for, element_type, multiple = false, select_element_index = 0, timeout = 60)
      begin
        Timeout.timeout(timeout) do
          start_time = Time.now
          BrowserStack.logger.info "Looking for #{wait_for}"
          find_element, visible = check_if_element_present(driver, wait_for, element_type, multiple = multiple, select_element_index = select_element_index)
          execution_time = (Time.now - start_time).round
          BrowserStack.logger.info "wait_for_element for #{wait_for} took #{execution_time} seconds"
          return false unless visible
          find_element.click && break if visible
        end
      rescue Timeout::Error
        return false
      end
      true
    end

    def check_if_element_present(driver, wait_for, element_type = :accessibility_id, multiple = false, select_element_index = 0)
      if multiple
        fields = driver.find_elements(element_type, wait_for)
        field = fields[select_element_index]
      else
        field = driver.find_element(element_type, wait_for)
      end
      is_field_displayed = field.nil? ? false : field.displayed?
      [field, is_field_displayed]
    end

    def select_element_by_attr(driver, search_bar_text)
      field = driver.find_ele_by_attr_include("XCUIElementTypeSearchField", "name", search_bar_text)
      field.click
      BrowserStack.logger.info "#{search_bar_text} box clicked"
      field
    end

    def valid_live_url?(url)
      return false if url.nil? || url.empty?
      return true if url == "about:blank"

      has_http = (url =~ %r{^https?://})
      url = "http://#{url}" if has_http != 0
      url.gsub! " ", ""

      # Copied from https://gist.github.com/dperini/729294
      regex = %r{^(?:(?:(?:https?|ftp):)?//)(?:\S+(?::\S*)?@)?(?:(?:(?:[a-z0-9\u00a1-\uffff][a-z0-9\u00a1-\uffff_-]{0,62})?[a-z0-9\u00a1-\uffff]\.?)+(?:[a-z\u00a1-\uffff]{2,}\.?)?)(?::\d{2,5})?(?:[/?#]\S*)?$}

      begin
        res = Timeout.timeout(0.2) do
          regex =~ url
        end
      rescue Timeout::Error
        res = nil
        BrowserStack.logger.info "Live URL regex timedout for #{url}"
      end

      res == 0
    end

    def fork_code_block(pid_tracking_file, &block)
      pid = fork_code(&block)
      write_to_file(pid_tracking_file, pid) unless pid_tracking_file.nil?
      Process.detach pid
    end

    def fork_code_block_for_device(device, &block)
      fork_code_block(forked_pid_file(device), &block)
    end

    def all_forked_files_filter(device)
      "#{forked_pid_file_prefix(device)}*"
    end

    def forked_pid_file(device)
      forked_pid_file_prefix(device) + SecureRandom.uuid.to_s
    end

    # Creates an archive and truncates the given log file
    def truncate_log_file(log_file_path)
      log_to_append = File.read(log_file_path)
      File.open("#{log_file_path}.archive", 'a') do |archive_log_file|
        archive_log_file.puts log_to_append
      end
      File.truncate(log_file_path, 0)
    end

    def create_upload_request_with_metadata(file_name, params, upload_type, type, metadata, server_config)
      uploader_request_file = server_config["other_files_to_upload_dir"] + "/#{upload_type}_upload_#{SecureRandom.uuid}.json"
      json_data = {
        upload_type: upload_type,
        file_name: file_name,
        s3_params: {
          session_id: params['automate_session_id'],
          aws_key: params["#{type}logs_aws_key"],
          aws_secret: params["#{type}logs_aws_secret"],
          aws_bucket: params["#{type}logs_aws_bucket"],
          aws_region: params["#{type}logs_aws_region"],
          metadata: metadata
        },
        genre: params[:genre] || params["genre"]
      }
      dir_name = File.dirname(uploader_request_file)
      FileUtils.mkdir_p(dir_name) unless Dir.exist? dir_name

      Utils.write_to_file(uploader_request_file, json_data.to_json)
      BrowserStack.logger.info("Upload request created as #{uploader_request_file}")
    end

    def create_upload_request(upload_request_file, type, device, s3_params, server_config, framework = nil, genre = nil)
      temp_file = upload_request_temp_file(upload_request_file)
      BrowserStack::OSUtils.execute("cp #{upload_request_file} #{temp_file}")
      BrowserStack::OSUtils.execute("chown app #{temp_file}")
      upload_request = {
        file_name: temp_file,
        s3_params: s3_params,
        type: type,
        framework: framework,
        genre: genre,
        upload_type: "logs_upload"
      }
      request_file = server_config["other_files_to_upload_dir"] + "/#{type}_logs_upload_#{SecureRandom.uuid}.json"
      dir_name = File.dirname(request_file)
      FileUtils.mkdir_p(dir_name) unless Dir.exist? dir_name
      Utils.write_to_file(request_file, upload_request.to_json)
      FileUtils.rm_rf(upload_request_file) if ["appium", "percy"].include?(type)
      FileUtils.rm_rf(upload_request_file) if ["crash-report"].include?(type) && genre && genre == 'app_automate'

      # Below is for deleting pw_on_ios_proxy_logs
      # FIXME: Move pw proxy logs from device logs to correct bucket in phase2
      FileUtils.rm_rf(upload_request_file) if ["device"].include?(type) && genre && genre == 'playwright'
    end

    def upload_request_temp_file(upload_request_file)
      upload_request_temp_file_dir = File.dirname(upload_request_file)
      upload_request_temp_file_name = File.basename(upload_request_file, ".log")
      upload_request_temp_file_name += "_#{Time.now.to_i}.log"
      File.join(upload_request_temp_file_dir, upload_request_temp_file_name)
    end

    def bridgecloud_ios_voiceover_session?(params)
      params ? params["lseIOSBluetoothVoiceover"].to_s == "true" : false
    rescue => e
      false
    end

    def notify_pusher_for_live(message, params, device, status = "", other_params_to_pusher = nil)
      # Use for Live sessions only
      pusher_params_hash = {
        type: "live_dashboard",
        live_session_id: params["live_session_id"] || params["session_id"],
        channel: params["pusher_channel"],
        token: params["pusher_auth"],
        event: "logs",
        message: message,
        status: status
      }
      pusher_params_hash.merge!(other_params_to_pusher) unless other_params_to_pusher.nil?
      pusher_params = URI.encode_www_form(pusher_params_hash)
      pusher_url = "#{params['pusher_url']}/sendMessage"
      fork_code_block_for_device(device) do
        BrowserStack.logger.info "Sending #{message} to #{pusher_url} with params #{pusher_params}}"
        BrowserStack::OSUtils.execute(%(curl -XPOST "#{pusher_url}" -d "#{pusher_params}"), timeout: 60)
      rescue => e
        BrowserStack.logger.error "Sending message to pusher failed #{e.message}"
      end
    end

    def notify_pusher(message, params, device, other_params_to_pusher = nil, product = APP_LIVE)
      return notify_pusher_for_live(message, params, device, "", other_params_to_pusher) if product != APP_LIVE

      pusher_params_hash = {
        type: "app_live_dashboard",
        app_live_session_id: params["app_live_session_id"] || params["session_id"],
        channel: params["pusher_channel"],
        token: params["pusher_auth"],
        event: "logs",
        message: message
      }
      pusher_params_hash.merge!(other_params_to_pusher) unless other_params_to_pusher.nil?
      pusher_params = URI.encode_www_form(pusher_params_hash)
      pusher_url = "#{params['pusher_url']}/sendMessage"
      fork_code_block_for_device(device) do
        BrowserStack.logger.info "Sending #{message} to #{pusher_url} with params #{pusher_params}}"
        # It was failing intermittently when net/http was used, hence used curl to send pusher updates
        BrowserStack::OSUtils.execute("curl -XPOST \"#{pusher_url}\" -d \"#{pusher_params}\"")
      rescue Exception => e
        BrowserStack.logger.error "Sending message to pusher failed #{e.message}"
      end
    end

    def upload_app_live_logs(params, device)
      config = BrowserStack::Configuration.new.all
      pm_tools_used_file = "/tmp/pm_tools_used_#{device}"
      pm_tools_used = File.exists?(pm_tools_used_file)
      FileUtils.rm_rf(pm_tools_used_file)

      return unless pm_tools_used

      s3_params = get_app_live_s3_params(params, device)

      BrowserStack.logger.info("create upload request")
      session_device_logs = BrowserStack::DeviceLogger.app_device_logs_file(device)

      temp_file = upload_request_temp_file(session_device_logs)
      BrowserStack::OSUtils.execute("sed -E 's/^[0-9]+ //' #{session_device_logs} > #{temp_file}")
      BrowserStack::OSUtils.execute("chown app #{temp_file}")
      upload_request = {
        upload_type: 'logs_upload',
        file_name: temp_file,
        s3_params: s3_params,
        type: "device",
        genre: "app_live_testing"
      }
      request_file = "#{config['other_files_to_upload_dir']}/device_logs_upload_#{SecureRandom.uuid}.json"
      dir_name = File.dirname(request_file)
      FileUtils.mkdir_p(dir_name) unless Dir.exist? dir_name
      Utils.write_to_file(request_file, upload_request.to_json)

      BrowserStack.logger.info("Device Logs upload request created")
    end

    def generate_app_live_url
      # Since we have defined adhoc repeater session endpoint in Proxy Controller of AL,
      # We need AL url to send request to start an adhoc repeater session
      # There are cases where we can trigger cleanup manually and we don't have any session related data i.e., URLS
      # this helps to get app-live url from the static conf. It checks rails endpoint and gives app-live url for the endpoint
      # This would work if the device is pointed to any regression or staging or prod environment.
      # TODO: Handle when user points device to local environment (establish session with any staging environment in such cases)
      config = BrowserStack::Configuration.new.all
      if config['static_conf']['env'] == 'prod'
        "https://app-live.browserstack.com"
      else
        url = config['static_conf']['rails_endpoint']
        url.gsub!('callbacks-', '')
        url.gsub!('https://', 'https://app-live-')
        url.gsub!('http://', 'https://app-live-')
        url.gsub!('com/', 'com')
        url
      end
    end

    def get_app_live_s3_params(params, device)
      bucket = params['app_testing_aws_bucket']

      s3url = "https://s3.amazonaws.com/#{bucket}/#{params['app_live_session_id']}/app_log.log"

      {
        session_id: params['app_live_session_id'],
        aws_key: params['app_testing_aws_key'],
        aws_secret: params['app_testing_aws_secret'],
        aws_bucket: bucket,
        aws_region: params['app_testing_aws_region'],
        s3_url: s3url,
        no_acl: false,
        is_json: false
      }

    end

    def get_device_versions_on_machine(config)
      config_json = JSON.parse(config)
      config_json['devices'].values
                            .map { |device| device['device_version'] }
                            .uniq
                            .reject { |version| version.nil? || version.empty? }
    end

    def calculate_symbols_diff(required_symbols, present_symbols)
      symbols_to_add = []
      symbols_to_remove = []
      BrowserStack.logger.info("Present Symbols: #{present_symbols}")

      present_symbols.map! { |symbol| symbol.chomp("arm64e") }
      if required_symbols.sort != present_symbols.sort
        required_symbols.each do |version|
          symbols_to_add.push(version) if !present_symbols.include?(version) && version.to_i < 17
          BrowserStack.logger.info("Checking Symbol: #{version}")
          present_symbols.delete(version)
        end
        symbols_to_remove = present_symbols unless present_symbols.empty?
      end
      BrowserStack.logger.info("Symbols to remove: #{symbols_to_remove}")
      [symbols_to_add, symbols_to_remove]
    end

    def download_symbol(os_type, download_endpoint, symbols_list)
      BrowserStack.logger.info("Downloading symbol: #{os_type}")
      to_download = []
      to_download.push(os_type) if symbols_list.include?(os_type)
      to_download.push("#{os_type}arm64e") if symbols_list.include?("#{os_type}arm64e")

      raise "Developer symbols not found in config" if to_download.empty?

      symbol_dir_path = "/Users/<USER>/Library/Developer/Xcode/iOS DeviceSupport/"
      symbol_dir_path.sub!(' ', '\\\\ ')

      failed_to_download = []
      to_download.each do |symbol|
        symbol_name = "#{symbol.gsub('.', '_')}_symbol.tar.gz"
        symbol_path = "/tmp/#{symbol_name}"
        symbol_download_url = "#{download_endpoint}/#{symbol_name}"
        BrowserStack::HttpUtils.download(symbol_download_url, symbol_path, { timeout: 150 }, "symbol")
        file_type = OSUtils.execute("file #{symbol_path}")
        BrowserStack.logger.info("Symbol #{symbol_name} file type: #{file_type}")
        result, status = OSUtils.execute("tar -xf #{symbol_path} -C #{symbol_dir_path}", true)
        FileUtils.rm(symbol_path)
        if status != 0
          BrowserStack.logger.error("Extract symbol failed: #{result}, status: #{status}")
          raise 'Unable to extract downloaded symbols'
        end
      rescue => e
        BrowserStack.logger.error("Unable to download symbol: #{e.message}")
        failed_to_download.push(symbol)
      end
      failed_to_download
    end

    def get_web_driver_agent_xctestrun_file(config, appium_version)
      if config["web_driver_agent_xctestrun_file"].is_a?(Hash)
        config["web_driver_agent_xctestrun_file"][appium_version]
      else
        config["web_driver_agent_xctestrun_file"]
      end
    end

    # DEPERCATED moveed to wda_version.rb
    # Getting from the file system, not the config
    def wda_xctestrun_file(device, ios_version, appium_version)
      platform_version = BrowserStack::XcodeUtils.get_platform_version
      folder = WDAVersion.device_wda_folder_path(device, appium_version, ios_version)
      "#{folder}/Build/Products/WebDriverAgentRunner_iphoneos#{platform_version}-arm64.xctestrun"
    end

    # DEPERCATED moveed to wda_version.rb
    def wda_app_folder(device, ios_version, appium_version)
      folder = WDAVersion.device_wda_folder_path(device, appium_version, ios_version)
      "#{folder}/Build/Products/Debug-iphoneos/WebDriverAgentRunner-Runner.app"
    end

    # Should only be used for running wda download async as a script
    def run_wda_install_script(device)
      result = `ps aux | grep 'wda_download.rb --device #{device}' | grep -v grep`
      unless result.empty?
        pid = result.split[1]
        start = Time.parse(result.split[8])
        if (Time.now - start) > 1200
          BrowserStack.logger.warn("wda install script running for over 20min. Killing process.")
          `kill #{pid}`
        else
          BrowserStack.logger.info("wda install script already running.")
          return
        end
      end
      pid = spawn "bundle exec ruby /usr/local/.browserstack/realmobile/scripts/wda_download.rb --device #{device}"
      Process.detach(pid)
    end

    def folder_md5(folder)
      files_hash = ''
      Dir.glob("#{folder}/**/*", File::FNM_DOTMATCH).each do |filename|
        next if File.directory?(filename)

        File.open(File.expand_path(filename)) do |file|
          data = file.read
          files_hash += Digest::MD5.hexdigest(data).to_s
        end
      end
      Digest::MD5.hexdigest(files_hash)
    end

    def get_working_interfaces(device, check_inet=false)
      interfaces = []
      location_id = IdeviceUtils.get_usb_location(device)
      # It returns all the interfaces present on device when location_id is empty.
      # To prevent toggling interface for another devices, we return empty interfaces and raise error.
      interfaces = IdeviceUtils.get_network_interfaces(location_id) unless location_id.empty?
      BrowserStack.logger.info("Device internet_sharing: found device at location #{location_id} with interfaces #{interfaces.join(',')}")
      # check if interface in ifconfig
      interfaces.select do |interface|
        interface_available = IdeviceUtils.is_network_interface_available?(interface)
        # check_inet is true if get_working_interfaces is called from device-check
        # for network simulation it should remain false
        if check_inet && IdeviceUtils.device_version(device) >= Gem::Version.new('16.0')
          # only add interfaces with inet available, others are used for device discovery in iOS 16
          interface_available &&= IdeviceUtils.interface_has_inet?(interface)
        end
        interface_available
      end
    end

    def type_check(event_name, event_hash)
      event_name_check = [String, Symbol].include? event_name.class
      event_hash_check = event_hash.is_a?(Hash)
      BrowserStack.logger.error "event_name must be a String or a Symbol but given #{event_name.class}" unless event_name_check
      BrowserStack.logger.error "event_hash must be a Hash but given #{event_hash.class}" unless event_hash_check
      event_name_check && event_hash_check
    end

    def mark_event_start(event_name, event_hash)
      return unless type_check(event_name, event_hash)

      event_hash[event_name] = (Time.now.to_f * 1000).to_i
    end

    def mark_event_end(event_name, event_hash)
      return unless type_check(event_name, event_hash)

      event_hash[event_name] = begin
        (Time.now.to_f * 1000).to_i - (event_hash[event_name] || event_hash[:absolute_start_time])
      rescue
        -1
      end
    end

    def track_feature_usage(feature, params, state="attempt")
      return unless feature && params && params[:user_id]

      tags = {
        "feature" => feature,
        "state" => state,
        "os" => "ios_njb"
      }
      hooter = Hooter.new('use')
      hooter.push_feature_usage(params[:user_id].to_s, params["genre"], AA_FEATURE_USAGE, tags)
      BrowserStack.logger.info("Feature usage event pushed successfully with tags: #{tags}")
    rescue => e
      BrowserStack.logger.error("Failed to track feature usage: #{e.message}, Backtrace: #{e.backtrace}")
      BrowserStack::Zombie.push_logs('track-feature-usage-failed', e.message, { "session_id" => params["automate_session_id"], "tags" => tags })
    end

    def set_event_value(event_name, event_hash, value)
      return unless type_check(event_name, event_hash)

      event_hash[event_name] = value
    end

    def os_case_sensitive(os)
      return 'iOS' if os =~ /ios/i
      return 'tvOS' if os =~ /tvos/i

      os
    end

    def fetch_eds_config(params)
      config = BrowserStack::Configuration.new.all
      eds_host = params[:edsHost] || config["eds"]["hostname"]
      eds_port = params[:edsPort] || config["eds"]["port_udp"]
      eds_key = params[:edsKey] || config["eds_api_key"]
      [eds_host, eds_port, eds_key]
    end

    def send_to_eds(params, event_type, merge_params = false, key = nil, value = nil, send_data_in_level_one = false, req_params: {}) # rubocop:todo Metrics/AbcSize

      eds_host, eds_port, eds_key = fetch_eds_config(params)
      params[:os] = os_case_sensitive(params[:os])

      params[:event_hash]&.delete(:absolute_start_time)

      data = { 'timestamp' => Time.now.to_i, 'source' => 'platform', 'type' => "start" }
      if req_params[:is_app_accessibility].to_s.downcase == "true"
        case event_type
        when EdsConstants::APP_LIVE_WEB_EVENTS
          event_type = "web_events"
        end

        data[:product] = "app_accessibility"
        data[:team] = "app_a11y_dev"
        data[:frontend_exception] = "app_accessibility_dashboard"
      end
      unless params[:session_id].nil?
        data["session_id"] = params[:session_id]
        # removing because, we might merge this in data later
        params.delete(:session_id)
      end

      if !merge_params
        # Setting event_hash if present
        data['data'] = convert_dot_to_nested_hash(params[:event_hash]).to_json unless params[:event_hash].nil?
        # Adding video_session_id only when passed
        data["hashed_id"] = params[:video_session_id] unless params[:video_session_id].nil?
        data["feature_usage"] = params[:feature_usage] if params[:feature_usage]
      else
        data.merge!(params)
      end

      event = {
        'event_type' => event_type,
        'data' => data
      }

      event['data'][key] = value if send_data_in_level_one

      Bsdwh.send(event.to_json, eds_host, eds_port, eds_key)
      BrowserStack.logger.info "Data sent to EDS: #{event_type}, #{event}"
    rescue => e
      BrowserStack.logger.error "Got error when pushing data to EDS: #{e.message} #{e.backtrace}"

    end

    def send_privoxy_data_to_eds(data, event_type)

      config = BrowserStack::Configuration.new.all
      eds_host = data[:edsHost] || config["eds"]["hostname"]
      eds_port = data[:edsPort] || config["eds"]["port_udp"]
      eds_key = data[:edsKey] || config["eds_api_key"]
      if [EdsConstants::APP_AUTOMATE_TEST_SESSIONS, EdsConstants::AUTOMATE_TEST_SESSIONS].include?(event_type)
        data['hashed_id'] = data['session_id']
        data.delete(:session_id)
      end
      event = {
        event_type: event_type,
        data: data
      }
      Bsdwh.send(event.to_json, eds_host, eds_port, eds_key)
      BrowserStack.logger.info "Data sent to EDS: #{event_type}, #{event}"
    rescue => e
      BrowserStack.logger.error "Got error when pushing data to EDS: #{e.message} #{e.backtrace}"

    end

    def prepare_skip_urls_for_session_network_logs
      BrowserStack.logger.info "REGEX_TO_SKIP_FOR_NETWORK_LOGS - #{REGEX_TO_SKIP_FOR_NETWORK_LOGS}"

      starts_with_skip_urls = REGEX_TO_SKIP_FOR_NETWORK_LOGS["StartsWith"].map { |url| "^" << url.gsub(".", "[.]") }
      exact_match_skip_urls = REGEX_TO_SKIP_FOR_NETWORK_LOGS["ExactMatch"].map { |url| url.gsub(".", "[.]") }

      BrowserStack.logger.info "starts_with_skip_urls - #{starts_with_skip_urls}"
      BrowserStack.logger.info "exact_match_skip_urls - #{exact_match_skip_urls}"

      urls_to_skip = starts_with_skip_urls + exact_match_skip_urls

      skip_urls = /#{urls_to_skip.join('|')}/

      BrowserStack.logger.info "skip_urls - #{skip_urls}"

      skip_urls
    end

    def send_network_logs_to_eds(session_id, genre, all_host_requests, event_type, key_config = {})
      config = BrowserStack::Configuration.new.all

      return if File.exist?("#{config['state_files_dir']}/stop_network_logs_push") ||
        all_host_requests.empty? ||
        !config["network_logs_send_to_eds"]

      eds_host = key_config[:edsHost] || config["eds"]["hostname"]
      eds_port = key_config[:edsPort] || config["eds"]["port_udp"]
      eds_key = key_config[:edsKey] || config["eds_api_key"]

      skip_urls = prepare_skip_urls_for_session_network_logs

      BrowserStack.logger.info "skip_urls - #{skip_urls}"

      begin
        all_host_requests.each do |each_host|
          domain = each_host[0]

          # Skip if domain is part of skip urls or domain is an IP address
          next if skip_urls === domain || /^((25[0-5]|(2[0-4]|1\d|[1-9]|)\d)\.?\b){4}/m === domain # rubocop:todo Style/CaseEquality

          data = {
            session_id: session_id,
            product: genre,
            url_accessed: domain,
            first_request_time: each_host[1]["first_request_time"],
            requests_count: each_host[1]["count"]
          }

          event = {
            event_type: event_type,
            data: data
          }

          Bsdwh.send(event.to_json, eds_host, eds_port, eds_key)
        end
      rescue => e
        BrowserStack.logger.error "Got error when pushing data to EDS: #{e.message} #{e.backtrace}"
      end
    end

    def send_product_stability_reason_to_eds(params, session_id, reason)
      config = BrowserStack::Configuration.new.all
      eds_host = params[:edsHost] || config["eds"]["hostname"]
      eds_port = params[:edsPort] || config["eds"]["port_udp"]
      eds_key = params[:edsKey] || config["eds_api_key"]
      data = {
        timestamp: Time.now.to_i,
        source: 'platform',
        type: "start",
        hashed_id: session_id,
        product: { stability: { reason: reason } }
      }
      event = {
        event_type: EdsConstants::APP_AUTOMATE_TEST_SESSIONS,
        data: data
      }
      Bsdwh.send(event.to_json, eds_host, eds_port, eds_key)
    end

    def network_exceptions
      [
        Net::OpenTimeout,
        Net::ReadTimeout,
        Errno::ECONNRESET,
        Errno::ETIMEDOUT,
        Errno::EHOSTUNREACH,
        Errno::ECONNREFUSED,
        Errno::ENETUNREACH
      ]
    end

    def get_prefix_genre(genre)
      genre.to_s == "app_automate" ? "app-" : ""
    end

    def send_appium_version_to_zombie(params, device, appium_version)

      kind = "#{get_prefix_genre(params[:genre])}appium-version-firecmd-time"
      zombie_data = {
        "session_id" => params['automate_session_id'],
        "device" => device
      }
      if params[:genre].to_s.downcase.eql?('app_automate')
        zombie_data.merge!({
          "data" => params[:event_hash]['firecmd_time']
        })
      else
        zombie_data.merge!({
          "data" => params[:event_hash]['total']
        })
      end

      BrowserStack::Zombie.push_logs(kind, appium_version, zombie_data) if appium_version
    rescue Exception => e
      BrowserStack.logger.error "Got #{e.message} #{e.backtrace} when pushing appium version and firecmd start time to zombie"

    end

    # rubocop:disable Metrics/MethodLength
    def push_syslog_parse_results(syslog_parse_result_file, session_id, device = nil)
      return unless File.exists?(syslog_parse_result_file)

      begin
        text = File.read(syslog_parse_result_file)
        text.gsub!(/\r?\n/, "\n")

        if device
          instrumentation_data = Hash.new(0)
          text.each_line do |line|
            name = line.split[0]
            instrumentation_data[name] += 1
          end

          BrowserStack.logger.info("[push_syslog_parse_results] instrumentation_data is #{instrumentation_data}")
          BrowserStack::Zombie.push_logs("ios-syslog-parse-results", GENRE_APP_AUTOMATE, { "device" => device, "session_id" => session_id, "data" => instrumentation_data })

        else
          kill_patterns = {
            /Process testmanagerd/ => { name: "testmanagerd", reason: "highwater" },
            /Process WebDriverAgentRunner-Runner/ => { name: "WebDriverAgentRunner-Runner", reason: "per-process-limit" },
            /app_ide_disconnected/ => { name: "app_ide", reason: "disconnected" }
          }

          text.each_line do |line|
            kill_patterns.each do |process_pattern, details|
              match = line.match(process_pattern)
              next unless match

              process_name = details[:name]
              reason = details[:reason]

              eds_data = {
                secondary_diagnostic_reason: "Process killed by jetsam: #{reason}",
                hashed_id: session_id,
                timestamp: Time.now.to_i
              }

              BrowserStack.logger.info("[Utils][push_syslog_parse_results] Detected #{process_name} kill with reason: #{reason}")
              send_to_eds(eds_data, EdsConstants::APP_AUTOMATE_TEST_SESSIONS, true)
              break
            end
            break if match
          end
        end
      rescue => e
        BrowserStack.logger.info("[push_syslog_parse_results] failed with error : #{e.message} #{e.backtrace}")
      ensure
        FileUtils.rm_f(syslog_parse_result_file)
      end
    end
    # rubocop:enable Metrics/MethodLength

    def redact_params(params, request=nil)
      keys_to_redact = ['app_store_username', 'app_store_password',
                        'video_aws_keys', 'video_aws_secret', 's3_app_url',
                        'appiumlogs_aws_secret', 'stats_aws_secret', 'password']
      log_params = params.clone
      if !request.nil? && request.request_method.upcase == "POST" && request.url.include?("app_live/inject_pfx_file")
        request_body = begin
          JSON.parse(request.body.read)
        rescue
          {}
        end
        request_body_key = log_params.keys[1]
        log_params.delete(request_body_key)
        log_params.merge!(request_body)
      end
      keys_to_redact.each do |key|
        log_params[key] = '[REDACTED]' if log_params.key?(key)
      end
      log_params
    end

    def s3_get_storage_class_based_on_obj_size(object_size, storage_class, default="STANDARD")
      return default if object_size && (object_size < THRESHOLD_FOR_STANDARD_STORAGE_CLASS)

      storage_class
    end

    def zip_logs(path_to_file, file_size, zip_ld_flag)
      path_to_zipped_file = "#{path_to_file}.gz"
      updated_path_to_file = path_to_file
      zipped_file_size_bytes = compression_time_ms = -1

      if zip_ld_flag
        compression_start_time = DateTime.now.strftime('%Q').to_i
        begin
          res = system('gzip', '-1', '-k', path_to_file)
          if res.to_s == 'true' && File.exist?(path_to_zipped_file)
            updated_path_to_file = path_to_zipped_file
            zipped_file_size_bytes = -1
            if File.file?(updated_path_to_file)
              zipped_file_size_bytes = begin
                File.stat(updated_path_to_file).size.to_i
              rescue
                -1
              end
            end
            BrowserStack.logger.info "Compressed #{path_to_file} of size #{file_size}"
            BrowserStack.logger.info "Compressed File Path: #{path_to_zipped_file}, Size (in bytes): #{zipped_file_size_bytes}"
          end
        rescue => e
          BrowserStack.logger.info "Error occurred while zipping the logs: #{e.message}"
        end
        compression_time_ms = DateTime.now.strftime('%Q').to_i - compression_start_time
        BrowserStack.logger.info "Compression time took for file : #{updated_path_to_file} is #{compression_time_ms}"
      end
      [updated_path_to_file, zipped_file_size_bytes, compression_time_ms]
    end

    def delete_file(file_path)
      if File.exist?(file_path)
        File.delete(file_path)
        BrowserStack.logger.info("Deleted file with path #{file_path}")
      end
    rescue => e
      BrowserStack.logger.error("Exception while deleting file #{file_path} reason #{e.message}")
    end

    def upload_zip_to_s3_via_presigned_url(path_to_file, upload_url)
      upload_url = URI.parse(upload_url)
      zip_file = File.open(path_to_file, "rb")
      response = Net::HTTP.start(upload_url.host) do |http|
        http.send_request("PUT", upload_url.request_uri, zip_file.read, "content_type" => "application/zip")
      end
      zip_file.close
    end

    def upload_file_to_s3(aws_key, aws_secret, content_type, path_to_file, acl, s3_url, session_id, genre, region = nil, timeout = nil, file_meta_data = {}, s3_metadata= {}, aws_storage_class = "STANDARD", zip_logs = nil, compression_time_ms = -1, updated_file_size_bytes = -1, original_file_size = -1) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength, Metrics/ParameterLists
      upload_to_s3_start_time = DateTime.now.strftime('%Q').to_i
      status = false
      error = nil
      zip_logs_flag = !zip_logs.nil? && zip_logs.to_s.downcase == 'true'

      # default region
      region ||= "us-east-1"
      # default timeout for upload is 10 mins
      timeout ||= 600

      begin
        client = Aws::S3::Client.new(
          access_key_id: aws_key,
          secret_access_key: aws_secret,
          region: region,
          http_read_timeout: timeout # Num seconds to wait for response data
        )

        ##need s3_url
        regex_result = s3_url.match(%r{.com/([a-zA-Z0-9-]+)/(.*)$}i).captures
        key_path = regex_result[1]
        bucket = regex_result[0]

        raise "key_path:#{key_path} or bucket: #{bucket} is nil" if key_path.nil? || bucket.nil?

        resp = nil

        begin
          File.open(path_to_file, 'rb') do |file|
            object = {
              acl: acl,
              body: file,
              bucket: bucket,
              key: key_path,
              storage_class: aws_storage_class,
              content_type: content_type
            }

            object[:content_encoding] = 'gzip' if zip_logs_flag.to_s.downcase == 'true' && path_to_file.match(/.gz/)
            object[:metadata] = s3_metadata unless s3_metadata.empty?

            resp = client.put_object(object)
            status = !resp.nil? && !resp[:etag].empty?
          end
        rescue Errno::ENOENT => e # File not found
          BrowserStack.logger.error("Upload to S3 failed, File not found: #{path_to_file}. Session #{session_id}.")
          raise e
        rescue => e
          BrowserStack.logger.error "Upload to S3 failed for File #{path_to_file} and Session #{session_id}. Error: #{e.class}"
          raise e
        end
      rescue => e
        status = false
        error = e.message.empty? ? e.class : e.message
      end
      upload_to_s3_time_ms = DateTime.now.strftime('%Q').to_i - upload_to_s3_start_time
      stop_req_time = file_meta_data['stop_req_timestamp'].to_i || 0
      file_meta_data[:video_upload_delay] = Time.now.to_i - stop_req_time
      begin
        send_feature_usage_to_eds(path_to_file, s3_url, status, error, session_id, genre, file_meta_data, aws_storage_class, zip_logs, compression_time_ms, updated_file_size_bytes, upload_to_s3_time_ms, original_file_size)
      rescue
        nil
      end
      [status, error.to_s]
    end

    def get_presigned_url(path, s3_config, expires_in)
      s3 = Aws::S3::Resource.new(access_key_id: s3_config["s3_access_keyId"],
                                 secret_access_key: s3_config["s3_secret_access_key"],
                                 region: s3_config["s3_region"] || "us-east-1")

      bucket = s3.bucket(s3_config["s3_bucket"])
      object = bucket.object(path)
      if object.exists?
        object.presigned_url(:get, expires_in: expires_in).to_s
      else
        raise "Aws Bucket Object path does not exist"
      end
    end

    def get_epoch_time_diff(timestamp)
      DateTime.now.strftime("%Q").to_i - timestamp
    end

    def upload_qa_test_video_to_s3(screenshot_dir, device_id, session_id)
      BrowserStack.logger.info("Debug: #{screenshot_dir} #{device_id} #{session_id}")
      client = Aws::S3::Client.new(profile: 'bs')
      BrowserStack.logger.info("Debug: AWS client #{client}")
      s3_url = "#{S3_BASE_URL}/mobile_regression/#{device_id}/#{session_id}.mp4"
      BrowserStack.logger.info("Debug: s3 url #{s3_url}")
      regex_result = s3_url.match(%r{.com/([a-zA-Z0-9-]+)/(.*)$}i).captures
      key_path = regex_result[1]
      bucket = regex_result[0]
      response = {}
      BrowserStack.logger.info("Debug: s3 details #{bucket} #{key_path}")
      File.open("#{screenshot_dir}/#{session_id}.mp4", 'rb') do |file|
        response['s3_upload'] = client.put_object({
          acl: 'public-read',
          body: file,
          bucket: bucket,
          key: key_path,
          content_type: 'video/mp4'
        })
      end
      BrowserStack.logger.info("Debug: s3 video uploaded")
      s3_url
    end

    # This function sends data for feature usage column in eds
    # session_id: automate and app automate session's hashed_id
    # feature: name of the feature
    # success: boolean, to represent the feature was successfully handled or not
    # message: some message that needs to be store, generally in case of failures
    # genre: field representing whether the session belongs to automate or app automate
    def send_general_feature_usage_data_to_eds(session_id, feature, success, genre, message = "")

      if feature.to_s.empty? || session_id.to_s.empty? || success.to_s.empty? || genre.to_s.empty?
        BrowserStack.logger.error("Failed to send data to eds as one of the required params was empty #{feature} #{session_id} #{success} #{genre}.")
        return
      end

      feature_usage = {}
      feature_usage[feature] = {
        success: success
      }

      feature_usage[feature]['exception'] = message.to_s[0, 100] if success.to_s.downcase != "true"

      data_to_push = {
        feature_usage: feature_usage,
        hashed_id: session_id,
        timestamp: Time.now.to_i
      }
      eds_event = ['selenium', 'js_testing', 'automate'].include?(genre) ? EdsConstants::AUTOMATE_TEST_SESSIONS : EdsConstants::APP_AUTOMATE_TEST_SESSIONS
      send_to_eds(data_to_push, eds_event, true)
    rescue => e
      BrowserStack.logger.error("Exception while sending data to eds : Error: #{e.message} #{e.backtrace}")
      kind = genre == "app_automate" ? "app-" : ""
      zombie_data = {
        "session_id" => session_id,
        "data" => {
          "feature" => feature,
          "status" => success
        }
      }
      BrowserStack::Zombie.push_logs("#{kind}feature-usage-failed", e.message, zombie_data)

    end

    def parse_plist_and_convert_to_json(file_path)
      #- at the end gives response to stdout
      file_content = BrowserStack::OSUtils.safe_execute("plutil", ["-convert", "json", file_path, "-o", "-"])
      JSON.parse(file_content.strip)
    end

    def get_md5_checksum_for_file(file_path)
      Digest::MD5.hexdigest(File.read(file_path)).strip
    end

    def is_md5_checksum_equal?(file_path_1, file_path_2)

      (get_md5_checksum_for_file(file_path_1) == get_md5_checksum_for_file(file_path_2))
    rescue => e
      BrowserStack.logger.error("Error while comparing md5sum of files #{file_path_1} and #{file_path_2} #{e.message} #{e.backtrace}")
      false

    end

    # Method to execute a command and capture stdout, stderr as strings as well as the process status.
    # NB: Use this instead of Open3.capture3 to prevent deadlock
    # Method uses Open3.popen3 to execute and get streams for stdout and stderr, then IO.select to determine
    # whether a stream is ready for reading. IO.select is called with a timeout that progressively increases
    # so it will never block indefinitely. We also at the same time check the state of the process as
    # process termination state is not determined by the whether the streams have reached EOF. If process is dead
    # we return the result
    def capture3(cmd)
      out_buff = ''
      err_buff = ''
      chunk_size = 4096
      # Progressive timeout array
      sleeps = [[0.05] * 20, [0.1] * 5, [0.5] * 3, 1, 2].flatten
      input, out, err, wait_thread = Open3.popen3(cmd)
      input.close
      open_pipes = [out, err]
      until open_pipes.empty?
        timeout = sleeps.shift || timeout
        ready_pipes, = IO.select(open_pipes, nil, nil, timeout)
        if ready_pipes.nil? # there is nothing new to read, check process status
          # Resume to next loop iteration if process is still running
          next if child_process_running?(wait_thread[:pid])

          # Process is dead. Return result
          return [out_buff, err_buff, wait_thread.value]
        end
        read_from_ready = lambda do |pipe, buff|
          if ready_pipes.include?(pipe)
            begin
              buff << pipe.readpartial(chunk_size)
            rescue EOFError
              pipe.close
              open_pipes.delete(pipe)
            end
          end
        end
        read_from_ready.call(out, out_buff)
        read_from_ready.call(err, err_buff)
      end
      [out_buff, err_buff, wait_thread.value]
    end

    def push_stability_metrics(session_id, test_name_or_id, logs_stability_file_data, metric_type, total_tests, total_tests_so_far)
      logs_stability_file = get_logs_stability_file_path(session_id)

      if total_tests == total_tests_so_far
        logs_stability_file_key = logs_stability_file_schema[metric_type]
        stability_file_data = {
          logs_stability_file_key => logs_stability_file_data[logs_stability_file_key]
        }
        data_to_push = { hashed_id: session_id, feature_usage: stability_file_data }
        BrowserStack.logger.info("[#{test_name_or_id}] Sending logs stability data to EDS #{data_to_push.to_json}")
        send_to_eds(data_to_push, EdsConstants::APP_AUTOMATE_TEST_SESSIONS, true)
        clean_logs_stabaility_file(session_id, metric_type)
      elsif total_tests_so_far > total_tests
        error_message = "Mismatch in total tests so far and total tests or session"
        zombie_data = { "session_id" => session_id, "log_tag" => metric_type, "test" => test_name_or_id, "total_tests_or_session_so_far" => total_tests_so_far, "total_tests_or_session" => total_tests }
        BrowserStack.logger.info("[#{test_name_or_id}] Found total tests so far: #{total_tests_so_far} out of total: #{total_tests}.")
        BrowserStack::Zombie.push_logs("xcuitest-push-to-eds-failure", error_message, zombie_data)
        clean_logs_stabaility_file(session_id, metric_type)
      end
    end

    def send_cleanup_request(device, from, reason)
      uri = URI('http://localhost:45680/cleanup')
      uri_params = { device: device,
                     retry_cleanup: true,
                     read_without_lock: true,
                     from: from,
                     reason: reason,
                     full_cleanup: false }
      uri.query = URI.encode_www_form(uri_params)
      Net::HTTP.get_response(uri)
    end

    def clean_logs_stabaility_file(session_id, metric_type)
      logs_stability_file = get_logs_stability_file_path(session_id)
      return unless File.exists?(logs_stability_file)

      logs_stability_file_data = {}
      File.open(logs_stability_file, File::RDWR, 0o644) do |f|
        f.flock(File::LOCK_EX)
        logs_stability_file_data = JSON.parse(f.read)
        logs_stability_file_data.delete(metric_type)
        f.rewind
        f.write(logs_stability_file_data.to_json)
        f.flush
        f.truncate(f.pos)
      end
      FileUtils.rm_f(logs_stability_file) if logs_stability_file_data.empty?
    end

    def get_logs_stability_file_path(session_id)
      "/tmp/#{session_id}_xctest_logs_stability"
    end

    def logs_stability_file_schema
      {
        "instru" => "instrumentationLogs",
        "device" => "deviceLogs",
        "network" => "networkLogs"
      }
    end

    def get_crash_reports_dir_path(device_id)
      "/tmp/crash-reports_#{device_id}"
    end

    def get_download_files_dir_path(device_id)
      "/tmp/download-files_#{device_id}"
    end

    def get_download_files_zip_path(device_id)
      "/tmp/download-files_#{device_id}.zip"
    end

    def get_list_of_preloaded_media_path(device_id)
      "/tmp/preloaded-media-list_#{device_id}"
    end

    # #
    # If a path exists like these:
    # ~/folder/sub_folder1/bs1.txt ~/folder/sub_folder1/bs2.txt ~/folder/sub_folder2/bs1.txt
    # base_location_of_folders_to_zip zip sub_folder2 and sub_folder1 recursively
    # and zip at /tmp/bs.zip and want to exclude
    # pass params as this : ~/folder/sub_folder1/bs2.txt
    # create_zip("/tmp/bs.zip" , ~/folder, ["sub_folder1","sub_folder2"] ,["sub_folder1/bs2.txt"]
    ##
    def create_zip(zip_file_path, base_location_of_folders_to_zip, folders_to_zip = [], exclude_files = [])
      raise "No folders given to zip" if folders_to_zip.empty?

      escaped_folders = folders_to_zip.map! { |folder| Shellwords.escape(folder) }.join(" ")
      if exclude_files.empty?
        BrowserStack::OSUtils.execute("cd #{base_location_of_folders_to_zip} && zip -r #{zip_file_path} #{escaped_folders}")
      else
        escaped_excluded_files = exclude_files.map! { |exclude_file| Shellwords.escape(exclude_file) }.join(" ")
        BrowserStack::OSUtils.execute(
          "cd #{base_location_of_folders_to_zip} && zip -r #{zip_file_path} #{escaped_folders} -x #{escaped_excluded_files}"
        )
      end
      raise "Zip file not present for session" unless File.exist?(zip_file_path)
    end

    def get_session_id_from_test_id(test_id)
      test_id[0...40]
    end

    def update_screenshot_instrumentation_with_lock(file, from, to, time_components, wait_for = 2, is_black_screenshot = "skipped") # rubocop:todo Metrics/AbcSize
      BrowserStack.logger.info "updating update_screenshot_instrumentation_with_lock called #{file}, from: #{from} to: #{to}"
      begin
        Utils.with_lock(file, wait_for) do
          # if File does not exist means it is first screenshot hence we need default data.
          # File should exist in other cases.
          instrumentation_data = begin
            JSON.parse(File.read(file))
          rescue
            default_screenshot_instrumentation_data
          end

          if from.nil? # If screenshot is just received we increment total screenshots and capture_stage counter
            instrumentation_data["total_screenshots"] = instrumentation_data["total_screenshots"] + 1
            instrumentation_data["capture_stage"] = instrumentation_data["capture_stage"] + 1
          else
            instrumentation_data["#{from}_stage"] = instrumentation_data["#{from}_stage"] - 1
            update_to_state = to == "failed" ? "#{from}_failed" : "#{to}_stage"
            instrumentation_data[update_to_state] = instrumentation_data[update_to_state] + 1

            if time_components.key?(:stage_time_taken)
              instrumentation_data["total_#{from}_time"] = instrumentation_data["total_#{from}_time"] + time_components[:stage_time_taken]
              instrumentation_data["max_#{from}_time"] = [instrumentation_data["max_#{from}_time"], time_components[:stage_time_taken]].max
            end
          end

          if ["success", "timeout"].include?(to)
            instrumentation_data["total_time"] = instrumentation_data["total_time"] + time_components[:total_time]
            instrumentation_data["max_time"] = [instrumentation_data["max_time"], time_components[:total_time]].max
            ScreenshotUtil.process_black_screenshot_instrumentation(instrumentation_data, is_black_screenshot)
          end

          if time_components.key?(:queue_time)
            instrumentation_data["total_#{from}_queue_time"] = instrumentation_data["total_#{from}_queue_time"] + time_components[:queue_time]
            instrumentation_data["max_#{from}_queue_time"] = [instrumentation_data["max_#{from}_queue_time"], time_components[:queue_time]].max
          end
          Utils.write_to_file(file, instrumentation_data.to_json)
          BrowserStack.logger.info "update complete for instrumentation file #{file}, from: #{from} to: #{to} data: #{instrumentation_data} "
        end
      rescue LockfileTimeoutError => e
        BrowserStack.logger.error "Failed to acquire lock for file #{file}: Error:#{e.message} #{e.backtrace}"
        BrowserStack::Zombie.push_logs("ss-instrumentation-failed", e.message, { "session_id" => file })
      rescue => e
        BrowserStack.logger.error "Exception update_screenshot_instrumentation_with_lock for #{file} #{e.message} #{e.backtrace}"
        BrowserStack::Zombie.push_logs("ss-instrumentation-failed", e.message, { "session_id" => file })
      end
    end

    def default_screenshot_instrumentation_data
      {
        "total_screenshots" => 0,
        "capture_stage" => 0,
        "convert_stage" => 0,
        "upload_stage" => 0,
        "convert_failed" => 0,
        "upload_failed" => 0,
        "capture_failed" => 0,
        "capture_failed_buckets" => {
          "execution_timeout" => 0,
          "wda_empty_response" => 0,
          "wda_unknown_command_response" => 0,
          "unknown" => 0
        },
        "success_stage" => 0,
        "timeout_stage" => 0,
        "total_time" => 0,
        "total_capture_time" => 0,
        "total_convert_time" => 0,
        "total_upload_time" => 0,
        "total_convert_queue_time" => 0,
        "total_upload_queue_time" => 0,
        "max_time" => 0,
        "max_capture_time" => 0,
        "max_convert_time" => 0,
        "max_upload_time" => 0,
        "max_convert_queue_time" => 0,
        "max_upload_queue_time" => 0,
        "total_black_screenshots" => 0,
        "total_non_black_screenshots" => 0,
        "error_check_black_screenshots" => 0
      }
    end

    def get_screenshot_instrumentation_file(conf, session_id)

      "#{conf['screenshot_dir']}/#{conf['screenshot_instrumentation_prefix']}#{session_id}"
    rescue
      ""

    end

    def ip(server_config)
      File.read(server_config['ip_file']).strip
    rescue
      nil
    end

    def enable_settings_app(device_id, params)
      device_state = DeviceState.new(device_id)
      if device_state.allow_settings_app_file_present?
        BrowserStack.logger.info "Enable Settings File already present for #{device_id}"
        data = { "device" => device_id, "session_id" => params['automate_session_id'] }
        BrowserStack::Zombie.push_logs("enable-settings-file-present", "", data)
      end
      BrowserStack.logger.info "Enabling Settings for #{device_id}"
      device_state.touch_allow_settings_app_file
      # increment settings app launched
      settings_app_launch_count = device_state.allow_settings_app_count
      device_state.write_to_allow_settings_app_count_file(settings_app_launch_count + 1)
    rescue => e
      BrowserStack.logger.info "Error creating settings enable file: #{device_id} : #{e.message}"
      raise AppSettingsError.new("Failed to Enable settings app", "enable_settings_app_failed", BROWSERSTACK_ERROR_STRING)
    end

    def disable_settings_app(device_id, params)
      device_state = DeviceState.new(device_id)
      BrowserStack.logger.info "Disabling Settings for #{device_id}"
      device_state.remove_allow_settings_app_file
      if params[:is_firecmd]
        settings_app_launch_count = device_state.allow_settings_app_count
        device_state.write_to_allow_settings_app_count_file(settings_app_launch_count - 1)
      end
    rescue => e
      data = { "device" => device_id, "session_id" => params['automate_session_id'] }
      BrowserStack::Zombie.push_logs("settings-file-delete-failed", "", data)
      BrowserStack.logger.info "Error deleting settings enable file: #{device_id} : #{e.message}"
    end

    def is_app_product_genre?(genre)
      APP_PRODUCTS_GENRE.include?(genre)
    end

    def get_error_bucket(error_message)
      case error_message
      when /undefined method .*WdaClient/
        "wda_unknown_command_response"
      when /undefined method .* for nil:NilClass/
        "wda_empty_response"
      else
        "unknown"
      end
    end

    def monitor_device_logger_metric(device_id, session_id, params={})
      BrowserStack.logger.info("Starting device logger metric monitoring")
      pid = Process.fork do
        dl_metric = DeviceLoggerMetric.new(device_id, session_id)
        dl_metric.start_monitoring(params)
      end
      Process.detach(pid)
    rescue => e
      BrowserStack.logger.error("Unable to start device logger metric monitoring: #{e.message} #{e.backtrace.join("\n")}")
    end

    def update_screenshot_states_with_lock(file, from, to, bucket, wait_for = 2)
      BrowserStack.logger.info "update_screenshot_states_with_lock called #{file}, from: #{from} to: #{to}"
      begin
        Utils.with_lock(file, wait_for) do
          # if File does not exist means it is first screenshot hence we need default data.
          # File should exist in other cases.
          instrumentation_data = begin
            JSON.parse(File.read(file))
          rescue
            default_screenshot_instrumentation_data
          end
          instrumentation_data["#{from}_#{to}_buckets"][bucket] = instrumentation_data["#{from}_#{to}_buckets"][bucket] + 1
          Utils.write_to_file(file, instrumentation_data.to_json)
          BrowserStack.logger.info "update complete for instrumentation file #{file}, from: #{from} to: #{to} data: #{instrumentation_data}"
        end

      rescue LockfileTimeoutError => e
        BrowserStack.logger.error "Failed to acquire lock for file #{file}: Error:#{e.message} #{e.backtrace}"
        BrowserStack::Zombie.push_logs("ss-states-instrumentation-failed", e.message, { "session_id" => file })

      rescue => e
        BrowserStack.logger.error "Exception update_screenshot_states_with_lock for #{file} #{e.message} #{e.backtrace}"
        BrowserStack::Zombie.push_logs("ss-states-instrumentation-failed", e.message, { "session_id" => file })
      end
    end

    def new_notifications_flow_enabled?(device_id)
      ios_device = BrowserStack::IosDevice.new(device_id, self.class.to_s, BrowserStack.logger)
      ios_device.enable_new_notifications_profile_flow?
    end

    def enable_notifications(device_id)
      response_mdm = if IdeviceUtils.check_app_with_bundle_id_exists(device_id, GEOGUARD_BUNDLE_ID)
                       Utils.change_notifications_profile_mdm([GEOGUARD_BUNDLE_ID], device_id, true)
                     else
                       Utils.change_notifications_profile_mdm([], device_id, false)
                     end

      # Returning if profile is already uninstalled or not present.
      return if response_mdm["error"] && response_mdm["error"]["message"].match(/.*The profile.* is not installed.*/)

      raise response_mdm["error"]["message"] if response_mdm["result"] == "failed"
    end

    def install_enterprise_signed_app(device, manifest_url)
      IosMdmServiceClient.configure
      IosMdmServiceClient.install_enterprise_application(device, manifest_url)
    rescue => e
      BrowserStack.logger.info "Enterprise App Install via MDM Failed #{e.message}"
    end

    def toggle_notifications_mdm(bundle_ids, device) # rubocop:todo Metrics/MethodLength
      config = BrowserStack::Configuration.new.all
      device_state = DeviceState.new(device)
      BrowserStack.logger.info "Request for toggling #{bundle_ids} app notifications through MDM"
      start_time = Time.now.to_i

      configuration_profiles_manager = ConfigurationProfilesManager.new(device, BrowserStack.logger)
      begin
        notifications_configuration = {}

        bundle_ids.each do |bundle_id|
          notifications_configuration[bundle_id] = {
            notifications_enabled: false
          }
        end
        configuration_profiles_manager.install_profile(:notifications, { notifications_configuration: notifications_configuration }, install_via: :automatic)

        if Utils.new_notifications_flow_enabled?(device)
          configuration_profiles_manager.install_profile(:notifications, install_via: :automatic)
        else
          notifications_configuration = {}
          if IdeviceUtils.check_app_with_bundle_id_exists(device, GEOGUARD_BUNDLE_ID) && !device_state.geoguard_cleanup_file_present?
            notifications_configuration = {
              "#{GEOGUARD_BUNDLE_ID}": {
                notifications_enabled: false
              }
            }
          end
          if notifications_configuration.empty?
            configuration_profiles_manager.remove_profile(:notifications, nil, remove_via: :automatic)
          else
            configuration_profiles_manager.install_profile(:notifications, {
              notifications_configuration: notifications_configuration
            }, install_via: :automatic)
          end
        end
      rescue => e
        reason = "Toggle Notifications Failed"

        return {
          "result" => "failed",
          "time_taken" => Time.now.to_i - start_time,
          "error" => { "reason" => reason, "message" => e.message }
        }
      end

      { "result" => "success", "time_taken" => Time.now.to_i - start_time }
    end

    def change_notifications_profile_mdm(bundle_ids, device, disable_notifications = false)
      config = BrowserStack::Configuration.new.all
      BrowserStack.logger.info "Request for toggling #{bundle_ids} app notifications through MDM"
      start_time = Time.now.to_i
      configuration_profiles_manager = ConfigurationProfilesManager.new(device, BrowserStack.logger)

      begin
        if disable_notifications
          notifications_configuration = {}
          bundle_ids.each do |bundle_id|
            notifications_configuration[bundle_id] = {
              notifications_enabled: false
            }
          end
          configuration_profiles_manager.install_profile(:notifications, { notifications_configuration: notifications_configuration }, install_via: :automatic)
          BrowserStack.logger.info "Disabled notifications via profile successful"
        else
          if Utils.new_notifications_flow_enabled?(device)
            configuration_profiles_manager.install_profile(:notifications, install_via: :automatic)
          else
            configuration_profiles_manager.remove_profile(:notifications, nil, remove_via: :automatic)
          end

          BrowserStack.logger.info "Enabling notifications via profile successful"
        end
      rescue => e
        reason = disable_notifications ? "Installing profile failed" : "Removing profile failed"

        return {
          "result" => "failed",
          "time_taken" => Time.now.to_i - start_time,
          "error" => { "reason" => reason, "message" => e.message }
        }
      end

      { "result" => "success", "time_taken" => Time.now.to_i - start_time }
    end

    def deep_copy(object)
      Marshal.load(Marshal.dump(object))
    end

    # NOTE: (App-Live + Appium-Inspector sessions only)
    # In case of,
    # - Network log enabled
    # - Ip/location changed.
    # - Network config change
    # In all the above three scenarios App-live session relaunch and session-id changed but the device will be the same.
    # So we don't need to replace the cached in devtools with the new session-id. And erase the old session's cached.
    def process_devtools_cache(params, server_config)
      return if params[:genre] != "app_live_testing"

      session_id = params["app_live_session_id"]
      old_session_id = params["oldLive_session_id"]
      Utils.write_to_file("#{server_config['state_files_dir']}/al_session_#{session_id}", params.to_json)
      relaunch_flow = ['local', 'ip_change', 'network_config_change'].include?(params["startElement"]) && !(
        params["startElement"] == "ip_change" && params["ipLocationCode"] == "-1")

      if relaunch_flow && old_session_id
        FileUtils.rm_rf("#{server_config['state_files_dir']}/al_session_#{old_session_id}")
        cmd = "curl --connect-timeout 20 -k -i 'https://localhost:443/wd/hub/refreshBridge?rails_session_id=#{old_session_id}&new_rails_session_id=#{session_id}'"
        response = `#{cmd}`
        BrowserStack.logger.info("[Appium App-Live] refreshBridge response: #{response}")
      end
    end

    def developer_disk_images_dir
      platform = BrowserStack::Configuration['developer_disk_image_platform'] || 'iPhoneOS.platform'
      "#{BrowserStack::Configuration['developer_disk_images_path']}/#{platform}/DeviceSupport/"
    end

    def download_developer_disk_image(version)
      platform = BrowserStack::Configuration['developer_disk_image_platform'] || 'iPhoneOS.platform'
      remote_tar_file = case platform
                        when 'iPhoneOS.platform'
                          "#{version.gsub('.', '_')}_developer_disk_image.tar.gz"
                        when 'AppleTVOS.platform'
                          "#{version.gsub('.', '_')}_tvos_developer_disk_image.tar.gz"
                        else
                          raise "Unsupported platform #{platform}"
                        end

      image_version_file_path = "/tmp/#{remote_tar_file}"
      image_version_file_download_url = "#{BrowserStack::Configuration['download_endpoint']}/#{remote_tar_file}"
      BrowserStack.logger.info "Image for #{version} not present. Trying to download #{remote_tar_file}"
      BrowserStack::HttpUtils.download(image_version_file_download_url, image_version_file_path, { timeout: 100 }, "developer_disk_image")
      file_type = OSUtils.execute("file #{image_version_file_path}")
      return if file_type.include?("XML")

      OSUtils.execute("tar -xf /tmp/#{remote_tar_file} -C #{developer_disk_images_dir}")
      FileUtils.rm("/tmp/#{remote_tar_file}")
    end

    # Zips the specified directory recursively
    def zip_directory_recursively(dir_path, zip_file_name)
      Zip::File.open(zip_file_name, Zip::File::CREATE) do |zip_file|
        Dir.chdir(dir_path) do
          Dir['**/*'].each do |destination_file_path|
            src_file_path = File.join(dir_path, destination_file_path)
            zip_file.add(destination_file_path, src_file_path)
          end
        end
      end
    end

    def kill_ws_proxy(ws_proxy_port, terminal_ip, retries = 3)
      while retries >= 0
        ws_proxy_pids = `lsof -i -P -n | grep node | grep #{ws_proxy_port} | grep #{terminal_ip}:#{ws_proxy_port} | awk '{print $2}'`
        if !ws_proxy_pids.nil? && ws_proxy_pids != ""
          pids = ws_proxy_pids.split("\n")
          BrowserStack.logger.info "Killing ws_proxy founds Pids to kill #{pids} running on port #{ws_proxy_port}"
          pids.each do |pid|
            system("kill -9 #{pid}")
          end
        else
          break
        end
        retries -= 1
      end
    end

    # rubocop:disable Metrics/AbcSize
    def set_ws_proxy_config(ws_proxy_config_path, terminal_ip, selenium_port, ws_proxy_upstream,
                            ws_proxy_port, request_params)
      begin
        file_data = File.read(ws_proxy_config_path)
        json_data = JSON.parse(file_data)
      rescue => e
        file_data = File.read("#{ws_proxy_config_path}.sample")
        json_data = JSON.parse(file_data)
      end
      json_data["prod"]["hostname"] = terminal_ip
      json_data["prod"]["upstream"] = ws_proxy_upstream if ws_proxy_upstream
      json_data["prod"]["upstreamPort"] = selenium_port.to_i if selenium_port
      json_data["prod"]["port"] = ws_proxy_port if ws_proxy_port

      json_data["prod"]["ADDITIONAL_JSON_CAP_PW_ON_IOS"] = JSON.parse(request_params['ADDITIONAL_JSON_CAP_PW_ON_IOS']) rescue {} # rubocop:todo Style/RescueModifier

      json_data["prod"]["keyObject"] = JSON.parse(request_params['ai_enabled_session'])['keyObject'] if request_params['ai_enabled_session']['keyObject']
      json_data["prod"]["tcgService"] = JSON.parse(request_params['ai_enabled_session'])['tcgService'] if request_params['ai_enabled_session']['tcgService']
      json_data["prod"]["tcgUrl"] = JSON.parse(request_params['ai_enabled_session'])['tcgUrl'] if request_params['ai_enabled_session']['tcgUrl']

      File.open(ws_proxy_config_path, "w") do |f|
        f.write(json_data.to_json)
      end
      json_data
    end
    # rubocop:enable Metrics/AbcSize

    def is_wda_kill_flag_enabled?(params)
      return false unless params

      custom_params = params["app_automate_custom_params"]
      custom_params && custom_params["xcuitest_wda_kill"].to_s.downcase == "true"
    end

    private

    def child_process_running?(pid)
      Process.waitpid(pid, Process::WNOHANG).nil?
    rescue Errno::ECHILD # No child process, means it's dead
      false
    end

    # forks code and sets the process group id, used to kill this process group (which includes all its children) later
    def fork_code(&block)
      pid = Process.fork(&block)
      Process.setpgid(pid, 0)
      Process.getpgid(pid)
    end

    def forked_pid_file_prefix(device)
      "/tmp/#{device}_pid_"
    end

    def convert_dot_to_nested_hash(dot_notation_hash)
      dot_notation_hash_map = dot_notation_hash.map do |main_key, main_value|
        main_key.to_s.split(".").reverse.inject(main_value) do |value, key|
          { key => value }
        end
      end
      dot_notation_hash_map.inject({}) { |final_hash, elem| final_hash.deep_merge!(elem) }
    end

    # send feature usage report to EDS
    # Params:
    # - file: local file
    # - s3_file: s3 complete path
    # - status: file uploaded or not (true / false)
    # - error: any exception during upload process
    # - session_id: session id
    # - genre: identify if prodouct / framework of the session
    def send_feature_usage_to_eds(file, s3_file, status, error, session_id, genre, file_metadata, aws_storage_class = 'STANDARD', zip_logs = nil, compression_time_ms = -1, updated_file_size_bytes = -1, upload_to_s3_time_ms = -1, original_file_size = -1) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength, Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity
      feature_usage = {}
      feature_usage_meta_data = {}
      feature = nil
      zip_logs_flag = !zip_logs.nil? && zip_logs.to_s.downcase == 'true'
      case s3_file
      when /har-logs/
        feature = 'networkLogs'
        feature_usage_meta_data[:upload_logs_stats] = if zip_logs_flag
                                                        {
                                                          "s3_zip_nw_logs_ios_#{genre}" => zip_logs_flag.to_s,
                                                          "original_file_size" => original_file_size.to_s,
                                                          "compression_time" => compression_time_ms.to_s,
                                                          "compressed_log_size" => updated_file_size_bytes.to_s,
                                                          "s3_upload_time" => upload_to_s3_time_ms.to_s
                                                        }
                                                      else
                                                        { "s3_zip_nw_logs_ios_#{genre}" => zip_logs_flag.to_s }
                                                      end
      when /appium-logs/
        feature = 'appiumLogs'
        feature_usage_meta_data[:upload_logs_stats] = if zip_logs_flag
                                                        {
                                                          "s3_zip_appium_logs_ios_#{genre}" => zip_logs_flag.to_s,
                                                          "compression_time" => compression_time_ms.to_s,
                                                          "original_file_size" => original_file_size.to_s,
                                                          "compressed_log_size" => updated_file_size_bytes.to_s,
                                                          "s3_upload_time" => upload_to_s3_time_ms.to_s
                                                        }
                                                      else
                                                        { "s3_zip_appium_logs_ios_#{genre}" => zip_logs_flag.to_s }
                                                      end
      when /device-logs/
        feature = 'deviceLogs'
      when /instrumentation-logs/
        feature = 'instrumentationLogs'
      when /crash_report/
        feature = 'crashLogs'
        feature_usage_meta_data[:num_crash_reports] = file_metadata[:num_crash_reports] || file_metadata["num_crash_reports"]
      when /video-.*mp4/
        feature = 'videoLogs'
        feature_usage_meta_data[:duration] = file_metadata["video_duration"]
        feature_usage_meta_data[:upload_delay] = file_metadata[:video_upload_delay]
      when /result-bundle/
        feature = 'resultBundle'
      when /download-file/
        feature = 'downloadFile'
      end

      # we don't want to upload devicelogs and networklogs for all tests as of now in feature usage for xcuitest
      return if feature.nil? || session_id.nil? || genre.nil? || session_id.length > 40

      uploaded_file_size = begin
        File.size(file).to_i
      rescue
        0
      end

      feature_usage[feature] = {
        size: uploaded_file_size,
        storage_class: aws_storage_class,
        success: status
      }

      feature_usage[feature].merge!(feature_usage_meta_data)

      unless error.nil?
        if error.class <= Exception
          feature_usage[feature]['exception'] = error.message || error.class.to_s
        elsif error.instance_of?(Class) && error <= Exception
          feature_usage[feature]['exception'] = error.to_s
        elsif error.instance_of?(String)
          feature_usage[feature]['exception'] = error[0, 100]
        end
      end

      data_to_push = {
        feature_usage: feature_usage,
        hashed_id: session_id,
        timestamp: Time.now.to_i
      }

      eds_event = ['selenium', 'js_testing', 'automate', 'playwright'].include?(genre) ? EdsConstants::AUTOMATE_TEST_SESSIONS : EdsConstants::APP_AUTOMATE_TEST_SESSIONS
      send_to_eds(data_to_push, eds_event, true)
    rescue => e
      puts "#{session_id} send_feature_usage_to_eds, Error: #{e.message} #{e.backtrace}"
      kind = genre == "app_automate" ? "app-" : ""
      zombie_data = {
        "session_id" => session_id,
        "data" => {
          "status" => status,
          "s3_file" => s3_file
        }
      }
      BrowserStack::Zombie.push_logs("#{kind}feature-usage-failed", e.message, zombie_data)
    end
  end
end

# Hash utilities
class ::Hash
  def deep_merge!(second)
    merger = proc { |_key, v1, v2| v1.is_a?(Hash) && v2.is_a?(Hash) ? v1.merge(v2, &merger) : v2 }
    merge!(second, &merger)
  end
end
