require 'json'
require_relative '../../server/device_manager'

class ScreenshotsUtil
  def self.capture_screenshot_via_wda(file_path, device, wda_port)
    wda_client = WdaClient.new(wda_port)
    response = wda_client.get_scaled_screenshot
    base_64_encoded_data = response["value"]
    File.open(file_path, 'wb') do |f|
      f.write(Base64.decode64(base_64_encoded_data))
    end
  end

  def self.capture_screenshot_via_appium(device_id, appium_session_id)
    appium_port = DeviceManager.device_configuration_check(device_id)["port"]
    uri = URI("http://localhost:#{appium_port}/wd/hub/session/#{appium_session_id}/screenshot")
    res = Net::HTTP.get_response(uri)

    raise 'Failed to take screenshot via appium' if res.code != '200'

    res = JSON.parse(res.body)

    res["value"]
  end
end
