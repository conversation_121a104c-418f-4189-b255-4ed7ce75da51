class AutomateFunnel
  # The usage of this class is explained here - https://browserstack.atlassian.net/wiki/spaces/ENG/pages/2160176973/Automate+Session+Funnel+Analysis+Implementation
  # Using this class, we want to achieve funnel analysis of the data flow circuit in the Automate infrastructure.
  def initialize
    @funnel_events = [] # To maintain order and nesting of the blocks
    @funnel_blocks = {} # To store the blocks
  end

  def mark_block_start(block_name)
    @funnel_events.push(block_name)
    @funnel_blocks[block_name] = { blockName: block_name, startTime: (Time.now.to_f * 1000).round }
  end

  def mark_block_end(block_name, result = nil, failure_reason = nil)
    unless @funnel_blocks[block_name].nil? || @funnel_blocks[block_name].empty?
      @funnel_blocks[block_name][:totalTime] = (Time.now.to_f * 1000).round - @funnel_blocks[block_name][:startTime]
      @funnel_blocks[block_name][:result] = result unless result.nil?
      @funnel_blocks[block_name][:failureReason] = failure_reason unless failure_reason.nil?
    end
  end

  def mark_breakup_start
    @funnel_events.push('breakup_start')
  end

  def mark_breakup_end
    @funnel_events.push('breakup_end')
  end

  def generate_data_json
    #  Process the funnel events and build the data_json object
    #  Events:
    #  1. <blockName>: push the block from funnel_blocks to the current breakup array
    #  2. breakup_start: to create a breakup array in the last block of current breakup array and nest into it
    #  3. breakup_end: to nest-out of the breakup
    root = []
    parent = root
    predecessors = []

    @funnel_events.each do |event|
      case event
      when 'breakup_start'
        return nil if parent.empty?

        last_block = parent.last
        predecessors.push(parent)
        last_block[:breakup] = [] if last_block[:breakup].nil?
        parent = last_block[:breakup]
      when 'breakup_end'
        return nil if predecessors.empty?

        parent = predecessors.pop
      else
        parent.push(@funnel_blocks[event])
      end
    end

    root[0][:component] = 'ios' unless root[0].nil?
    root[0]
  end
end
