require 'appium_lib'
require 'digest'
require 'yaml'
require 'fileutils'
require 'json'
require 'network_helper'

require_relative 'cleanup_check'
require_relative 'custom_exceptions'
require_relative 'utils/utils'
require_relative 'utils/http_utils'
require_relative 'utils/idevice_utils'
require_relative 'utils/time_recorder'
require_relative './helpers/automation'
require_relative './helpers/browserstack_app_helper'
require_relative './helpers/photos_sqlite_helper'
require_relative '../server/device_manager'
require_relative '../lib/apps/app'
require_relative '../lib/utils/configuration_profiles_manager'
require_relative '../lib/utils/configuration_profiles_enforcer'
require_relative '../lib/apps/chrome'
require_relative '../lib/apps/redirect'
require_relative '../lib/apps/launcher'
require_relative '../lib/configuration'
require_relative '../lib/device_params'
require_relative '../lib/handle_cross_site_tracking'
require_relative '../lib/ifuse'
require_relative '../lib/provisioning/ppuid_file'
require_relative '../lib/version'
require_relative '../config/constants'
require_relative 'models/device_state'
require_relative 'models/lockdown_device_state'
require_relative '../lib/helpers/timezone_helper'
require_relative '../lib/helpers/favorite_contact_helper'
require_relative '../lib/helpers/assistive_touch_helper'
require_relative '../lib/helpers/passcode_helper'
require_relative '../lib/device_setup/backup_manager/managers/cfgutil_backup_manager'
require_relative './device_setup/appstore_installer/appstore_installer'
require_relative '../lib/helpers/apple_pay'
require_relative '../lib/helpers/voiceover_helper'
require_relative '../lib/helpers/appium/location_services'
require_relative '../lib/helpers/contacts_app_helper'
require_relative '../lib/helpers/appearance_helper'
require_relative '../lib/utils/cfgutil_profile_manager'
require_relative '../lib/utils/socks5_forwarder'
require_relative '../lib/helpers/accessibility_settings_helper'
require_relative '../lib/utils/download_file'
require_relative '../lib/utils/custom_mdm_manager'
require_relative './apps/chromium'
require_relative '../lib/device_conf'
require_relative '../lib/overridden/thread'
require_relative './helpers/location_simulator'
require_relative './helpers/geoguard_helper'
require_relative '../lib/device_setup/full_cleanup/full_cleanup_state'

# This class just acts as an interface between iphone.rb and other classes
# that do the heavy-lifting. This class should just delegate all the work to other classes
# and save the steps that have been completed. Do not put any other business logic here.
# TODO: remove all the business from this class
class CleanupIphone # rubocop:todo Metrics/ClassLength
  include BrowserStack::TimeRecorder

  methods_to_wrap = %i[
    clear_call_logs
    favorite_contact_cleanup
    apple_id_signout
    apple_wallet_cleanup
    enable_apple_wallet_enable_double_click_side_button
    check_global_proxy_installed
    chrome_cleanup
    clean_bookmarks_favorites
    clean_imessages_app
    clean_orientation
    clean_pwa_and_waiting_state_apps
    clean_safari_fav_and_bookmarks
    clean_safari_tab_groups
    clean_safari_url_bar_position
    cleanup_custom_media
    clear_contact_accounts
    clear_reading_list
    clean_safari_app
    clear_testflight_account
    clear_third_party_accounts
    crash_logs_cleanup
    delete_downloads
    clean_voiceover
    disable_airplane_mode_from_settings
    configure_assistive_touch
    disable_assistive_touch
    evaluate_apple_pay
    disable_auto_lock
    disable_bluetooth
    disable_bluetooth_optimized
    disable_dark_mode
    disable_government_notifications
    disable_low_power_mode
    disable_paint_timing
    disable_safari_websocket
    disable_standby_mode
    disable_testflight_notifications
    disconnect_wifi
    dismiss_appstore_popup
    dismiss_appstore_popup_via_xcui
    enable_location_services
    enable_pwa
    enable_safari_web_inspector
    enable_wifi
    grant_access_photos_permission
    idevice_name
    install_preloaded_files
    install_preloaded_files_legacy
    kill_apps
    preload_media_ios_njb_app
    remove_extra_keyboards
    reset_keyboard_settings
    reset_view_to_standard
    reset_time_zone_to_utc
    safari_cleanup
    safari_remote_automation
    set_default_font_size
    set_time_to_utc
    sign_out_sandbox_accounts
    siri_contacts_cleanup
    testflight_login
    trust_dummy_app_via_xcui
    sim_cleanup
    check_sim_signal_strength
    log_ios_opened_apps
    download_file_cleanup
    geoguard_cleanup
    message_notification_cleanup
    reset_contacts_app
    install_launcher_and_bs_app
    force_re_enroll_to_mdm
    install_proxy_profile
    erase_and_restore
    install_rigid_restrictions_profile
    enable_redirect_extension
    install_required_apps
    check_and_install_testflight_via_vpp
    install_first_party_system_apps
    reset_accessibility_settings
    chromium_cleanup
    fix_enlarged_font_size
    handle_app_store_popups
    disable_testflight_background_refresh
    clean_stored_password
    clean_device_theme
    disable_stage_manager
    clear_clipboard
    disable_apple_intelligence
  ]

  CONTACTS_APP_BUNDLE_ID = "com.apple.MobileAddressBook".freeze

  # around_method takes a list of method names, and a block to be executed which wraps around the original method, see lib/utils/method_interceptor.rb
  around_method(*methods_to_wrap) do |original_method, method_name|
    zombie_timer_kind = "#{self.class.name.split('::').last}##{method_name}"
    unless step_completed?(method_name)
      @current_cleanup_method = method_name
      record_time(zombie_timer_kind, device_id) { original_method.call }
      @current_cleanup_method = nil
      mark_step_completed(method_name)
    end
  end

  attr_reader :appium_port, :device_version, :device_name, :wda_port, :orientation_lock_value, :device_id, :session_id, :device_state, :lockdown_device_state, :configuration_profiles_manager, :configuration_profiles_enforcer, :current_cleanup_method

  def initialize(device_id, appium_port, device_version, device_name, wda_port, orientation_lock_value = 0, session_id = "")
    @device_id = device_id
    @appium_port = appium_port
    @device_version = device_version
    @device_name = device_name
    @wda_port = wda_port
    @orientation_lock_value = orientation_lock_value
    @completed_steps = get_completed_steps_state
    @session_id = session_id
    @config = BrowserStack::Configuration.new.all
    @device_state = DeviceState.new(device_id)
    @lockdown_device_state = BrowserStack::LockdownDeviceState.new(device_id)
    @device_config = DeviceConf[device_id]
    @configuration_profiles_manager = ConfigurationProfilesManager.new(device_id, BrowserStack.logger)
    @configuration_profiles_enforcer = ConfigurationProfilesEnforcer.new(device_id, BrowserStack.logger)
    @current_cleanup_method = nil
    DeviceParams.configure
  end

  def full_cleanup_state
    @full_cleanup_state ||= FullCleanup::FullCleanupState.new(device_id)
  end

  def save_completed_steps_state
    File.write(completed_steps_file, @completed_steps.to_json)
  end

  def cleanup_check
    @cleanup_check ||= CleanupCheck.new(@device_config, device_id)
  end

  def fix_enlarged_font_size
    accessibility_settings_helper = Secure::AccessibilitySettingsHelper.new(device_id, session_id, "cleanup")
    accessibility_settings_helper.reset_text_size_and_larger_accessibility_sizes
  end

  def clear_completed_steps_state
    FileUtils.rm_rf(completed_steps_file)
    @completed_steps = {}
  end

  def clear_clipboard
    BrowserStack.logger.info "Clearing Clipboard"
    if device_version.to_f >= 18.0
      IdeviceUtils.launch_app_with_bundle_id_v2(device_id, "com.facebook.WebDriverAgentRunner.xctrunner")
    else
      wda_client.set_foreground("com.facebook.WebDriverAgentRunner.xctrunner", "WDA", {}, @session_id)
    end

    wda_client.clear_clipboard
  rescue => e
    BrowserStack.logger.info "WDA error while clearing clipboard"
    BrowserStack.logger.error "#{e.class.name} #{e.message}: #{e.backtrace.join("\n")}"
  end

  def dismiss_appstore_popup
    BrowserStack.logger.info "Opening AppStore for accepting popup"
    driver = Automation.open_app_store_and_continue(device_id, device_version)
    Automation.location_permissions_popup(driver, permission: :always)
    result = Automation.dismiss_popup(driver, "com.apple.AppStore", :accept)
    BrowserStack.logger.info "Result of dismiss popup: #{result.inspect}"
    mark_already_dismissed if result == :already_dismissed
    BrowserStack.logger.info "Dismiss popup completed for AppStore"
  rescue => e
    BrowserStack.logger.error "#{e.class.name} #{e.message}: #{e.backtrace.join("\n")}"
    raise CleanupAutomationError, e.message
  end

  def dismiss_appstore_popup_via_xcui
    AppStoreInstaller.handle_app_store_popups(device_id)
    device_state.touch_app_store_popup_handled_file
  rescue => e
    if device_version.to_f > 18.2
      clear_completed_steps_state
      full_cleanup_state.clear_completed_steps_state
    end
    raise e
  end

  def verify_optimised_photos_permission
    photos_permission_log = "Documents/photos_permission.log"
    temp_path = "/tmp/photos_permission_#{device_id}"
    begin
      retries ||= 3
      BrowserStack.logger.info("Photo permission retry left: #{retries}")

      # Clear old photos permission file
      FileUtils.rm_f("#{temp_path}/#{photos_permission_log}")

      # Getting photos permission file from device
      result, status = BrowserStack::OSUtils.execute("/usr/local/bin/gtimeout -s KILL 20 #{IOS_DEPLOY} -I --id #{device_id} -1 #{BROWSERSTACK_APP_BUNDLE_ID} -w #{photos_permission_log} --to #{temp_path}", true)
      if status != 0
        BrowserStack.logger.info("gtimeout Command exited with status #{status}")
        raise "gtimeout Command exited with status #{status} #{result}"
      end

      # Checking if photos permission is granted
      permission, status = BrowserStack::OSUtils.execute("grep 'status=3' #{temp_path}/#{photos_permission_log}", true)
      if status != 0
        BrowserStack.logger.info("grep Command exited with status #{status}")
        raise "grep Command exited with status #{status}"
      end
    rescue => e
      if retries > 0
        retries -= 1
        retry
      else
        raise e
      end
    end

    if permission.empty?
      BrowserStack.logger.info("Photo permission not granted: #{permission}")
      raise "Photo permission not granted: #{permission}"
    end
    BrowserStack.logger.info("Photo permission granted: #{permission}")
  end

  # Uses PHPhotoLibrary to grant photos permission. It writes the status in Documents/photos_permission.log.
  # Uses verify_optimised_photos_permission to verify the status.
  def optimised_photos_permission
    retries ||= 3
    BrowserStack.logger.info("optimised_photos_permission retry left: #{retries}")
    # It takes more time on older devices so the timeout is 120 seconds
    output = BrowserStackAppHelper.run_ui_test(device_id, :allow_photos_popup, 120, session_id: session_id, environment_variables: { flow: "optimised_photos_permission" })
    verify_optimised_photos_permission
    device_state.remove_photos_permission_file
  rescue => e
    # Reinstalling the Browserstack app and try again
    device_state.touch_reinstall_browserstack_app_file
    BrowserStackAppHelper.reinstall_browserstack_app(device_id, remove_existing_app: true)

    if retries > 0
      retries -= 1
      retry
    end
    raise e
  end

  def grant_access_photos_permission
    return unless cleanup_check.need_photos_permission?

    if device_version.to_f >= OPTIMISED_PHOTOS_AUTHORIZATION_SUPPORT_VERSION
      optimised_photos_permission
    else
      begin
        # It takes more time on older devices so the timeout is 120 seconds
        output = BrowserStackAppHelper.run_ui_test(device_id, :allow_photos_popup, 120, session_id: session_id)
        device_state.remove_photos_permission_file
        BrowserStack.logger.info "grant_access_photos_permission #{device_id}"
        BrowserStack.logger.info "grant_access_photos_permission cmd output #{output}"
      rescue => e
        BrowserStackAppHelper.check_and_install_browserstack_apps(device_id)
        raise e
      end
    end
  end

  def grant_access_photos_permission_launcher
    # It takes more time on older devices so the timeout is 120 seconds
    output = nil
    BrowserStackAppHelper.check_and_install_browserstack_apps(device_id)
    output = BrowserStackAppHelper.run_ui_test(device_id, :launcher_allow_photos_popup, 120, session_id: session_id)
    device_state.remove_launcher_photos_permission_file
    output
  rescue => e
    BrowserStackAppHelper.build_and_install_launcher_app(device_id)
    raise e
  end

  def optimised_preload_media_verification
    media_sync_log = "/tmp/media_sync_#{device_id}/Documents/media_sync.log"
    begin
      retries ||= 3
      BrowserStack.logger.info "PreLoad Media Sync Retry Left: #{retries}"
      result, status = BrowserStack::OSUtils.execute("/usr/local/bin/gtimeout -s KILL 20 #{IOS_DEPLOY} -I --id #{device_id} -1 #{BROWSERSTACK_APP_BUNDLE_ID} -w Documents/media_sync.log --to /tmp/media_sync_#{device_id}", true)
      if status != 0
        BrowserStack.logger.info "[Browserstack Media] Command Execution Failed with status #{status}"
        raise "[Browserstack PreloadMedia] Command Execution Failed #{status}"
      end
      sync_done, ver_status = BrowserStack::OSUtils.execute("grep 'count=9' #{media_sync_log}")
      if status != 0
        BrowserStack.logger.info "[Browserstack Media] Verification Command Execution Failed with status #{ver_status}"
        raise "[Browserstack PreloadMedia] Command Execution Failed #{ver_status}"
      end
      BrowserStack.logger.info "[Browserstack Media] sync done successfully #{sync_done}"
      raise "PreLoad media not synced" if sync_done.to_s.empty?
    rescue => e
      if retries > 0
        retries -= 1
        sleep(2)
        retry
      end
      raise "PreLoad media not synced #{e}"
    ensure
      BrowserStack.logger.info "[Browserstack Media] Reached ensure block #{status}"
    end
  end

  def preload_media_ios_njb_app(timeout=10, capture_syslog=false, optimised_flow=false)
    if optimised_flow
      BrowserStackAppHelper.run_ui_test(device_id, :preload_media_ios_njb_app, timeout, session_id: session_id, capture_syslog: capture_syslog, environment_variables: { flow: "optimised_flow" })
      optimised_preload_media_verification
    else
      BrowserStackAppHelper.run_ui_test(device_id, :preload_media_ios_njb_app, timeout, session_id: session_id, capture_syslog: capture_syslog)
    end
    device_state.remove_needs_preload_media_file
  end

  def safari_cleanup #rubocop:todo Metrics/AbcSize
    xcui_output = BrowserStackAppHelper.run_ui_test(device_id, :safari_cleanup, session_id: session_id)
    if !xcui_output.nil? && !xcui_output.empty?
      if !xcui_output[/SAFARI_POPUP_SWITCH/].nil?
        BrowserStack.logger.info "Safari cleanup succeded with switches"
        BrowserStack::Zombie.push_logs("safari_cleanup_block_popup_switch", "", { "device" => device_id  })
      elsif !xcui_output[/SAFARI_POPUP_STATIC_TEXT/].nil?
        BrowserStack.logger.info "Safari cleanup succeded with static texts"
        BrowserStack::Zombie.push_logs("safari_cleanup_block_popup_static_text", "", { "device" => device_id })
      elsif !xcui_output[/BLOCK_POPUP_NOT_FOUND/].nil?
        BrowserStack.logger.info "Safari cleanup failed retrying test"
        BrowserStack::Zombie.push_logs("safari_cleanup_block_popup_retry", "", { "device" => device_id })
        xcui_output = BrowserStackAppHelper.run_ui_test(device_id, :safari_cleanup, session_id: session_id)
        if !xcui_output.nil? && !xcui_output.empty? && !xcui_output[/BLOCK_POPUP_NOT_FOUND/].nil?
          BrowserStack.logger.error "Safari cleanup automation retry failed as well"
          test_meta_data = @config['xcui_class_function_map'][:safari_cleanup]
          raise BrowserStackTestExecutionError.new(test_meta_data[:class], test_meta_data[:function], xcui_output)
        end
      end
    end

    device_state.remove_safari_live_session_file
    device_state.remove_device_logger_detected_safari_launched_file
    # If Safari was used during the session, then we don't want to force clean it
    # until CLEANUP_STEPS_FREQ[:force_clean_safari] days from now
    device_state.touch_force_clean_safari_file
  end

  def chromium_cleanup
    chromium_manager = Chromium.new(device_id)
    chromium_manager.clean

    device_state.remove_device_logger_detected_chromium_launched_file
    device_state.touch_force_clean_chromium_file
  end

  def enable_safari_web_inspector
    BrowserStackAppHelper.run_ui_test(device_id, :enable_safari_web_inspector, session_id: session_id)
    device_state.touch_enable_safari_web_inspector_file
  end

  def request_reinstall_mdm_restrictions(skip_removing_restrictions: false)
    return if CustomMDMManager.is_custom_mdm_device?(@device_id)

    @configuration_profiles_manager.remove_profile(:restrictions, nil, remove_via: :automatic) unless skip_removing_restrictions
    device_state.touch_force_install_mdm_profiles_file
  rescue => e
    if e.message.match(/Couldn't remove restrictions profile: The profile .* is not installed./)
      BrowserStack.logger.info "MDM profile is already removed: #{e.message}"
    else
      BrowserStack.logger.info "Error Removing MDM profile: #{e.message} #{e.backtrace}"
      raise e
    end
  end

  def remove_bluetooth_restrictions
    @configuration_profiles_manager.install_profile(:restrictions, { flags: ['enable_bluetooth_modification'] }, install_via: :automatic)
    device_state.touch_force_install_mdm_profiles_file
  end

  def check_and_enforce_configuration_profiles
    BrowserStack.logger.info "Started check_and_enforce_configuration_profiles"
    return unless @configuration_profiles_manager.device_uses_cfgutil_managed_profiles?

    force_install = device_state.force_install_mdm_profiles_file_present?
    profile_check_due = device_state.configuration_profile_periodic_check_file_older_than_days?(CLEANUP_STEPS_FREQ[:configuration_profile_periodic_check])
    BrowserStack.logger.info "force_install: #{force_install}, profile_check_due: #{profile_check_due}"
    @configuration_profiles_enforcer.enforce_configuration_profiles(force_install, profile_check_due)
  end

  def enroll_device_to_mdm_via_cfgutil(force_enroll = false)
    mdm_enrolled = @configuration_profiles_manager.profile_installed_via_cfgutil?(:MDM)
    force_enroll = true unless mdm_enrolled

    return true unless force_enroll

    @configuration_profiles_manager.remove_profile(:MDM, nil, remove_via: :cfgutil) if mdm_enrolled
    @configuration_profiles_manager.install_profile(:MDM, install_via: :cfgutil)
    true
  end

  def re_enroll_device_to_mdm
    return true if CustomMDMManager.is_custom_mdm_device?(@device_id)

    # Check for MDM expiry and/or re-enrollment
    re_mdm_success = false
    re_mdm_file_present = device_state.re_mdm_file_present?
    is_mdm_check_due = device_state.re_mdm_periodic_check_file_older_than_days?(CLEANUP_STEPS_FREQ[:re_mdm_periodic_check])

    if re_mdm_file_present || is_mdm_check_due
      BrowserStack.logger.info "Checking if MDM enrollment is required. Re-MDM-File-present? #{re_mdm_file_present}, MDM check due?: #{is_mdm_check_due}"
      # If we don't remove this profile in full cleanup supported devices, MDM enrollment fails as we are restriciting Profile install through UI using this profile
      re_mdm_success = nil
      if @configuration_profiles_manager.device_uses_cfgutil_managed_profiles?(verify_cfgutil: true)
        re_mdm_success = enroll_device_to_mdm_via_cfgutil(re_mdm_file_present)
      else
        remove_rigid_restrictions_profile
        re_mdm_success = IosMdmServiceClient.mdm_re_enrollment(device_id, re_mdm_file_present)
      end

      if re_mdm_success
        device_state.remove_re_mdm_file if re_mdm_file_present
        device_state.touch_force_install_mdm_profiles_file
      end
      device_state.touch_re_mdm_periodic_check_file
    end

    re_mdm_success
  rescue BrowserStackTestExecutionError => e
    BrowserStack.logger.error "MDM enrollment failed #{e.message}"
  end

  def install_launcher_and_bs_app
    file_path = "#{INSTALL_LAUNCHER_AND_BS_APP_PATH}_#{device_id}"
    BrowserStack.logger.info "install_launcher_and_bs_app: file_path #{file_path}"
    if File.exist?(file_path)
      # bs-app
      BrowserStack.logger.info "install_launcher_and_bs_app: Installing BS app and Launcher app for #{device_id}"
      BrowserStackAppHelper.build_and_install_browserstack_app(device_id)
      BrowserStack.logger.info "BrowserStack TestSuite install cmd executed"
      #launcher-app
      BrowserStackAppHelper.build_and_install_launcher_app(device_id)
      BrowserStack.logger.info "launcher-app install cmd executed"
      FileUtils.rm_f(file_path)
    else
      BrowserStack.logger.info "install_launcher_and_bs_app: File not found to install BS app and Launcher app for #{device_id}"
    end
  end

  def install_required_apps
    apps = [Launcher.new, Redirect.new, Chrome.new]
    user_installed_apps = IdeviceUtils.list_apps(device_id, attempts: 2)
    apps.each do |app|
      app.update_app_version_using_ios_version(@device_config['device_version'].to_i)
      installed_app = InstalledApp.new(device_id, app.bundle_id)
      next unless installed_app.reinstall?(latest_version: app.version) || !app.present_on_device?(user_installed_apps) || chrome_cleanup_required?(app)

      app.setup(device_id, ios_device.device_provisioning_profile, true)
    end

    chromium = Chromium.new(device_id)
    chromium.ensure_install
  end

  def chrome_cleanup_required?(app)
    return false if app.bundle_id != CHROME_BUNDLE_ID

    device_state.chrome_cleanup_required_file_present?
  end

  def check_and_install_testflight_via_vpp
    apple_business_manager_helper = BrowserStack::AppleBusinessManagerHelper.new(device_id, "testflight")
    raise "Failed to install TestFlight" if apple_business_manager_helper.device_eligible?(@device_config["region"]) && !apple_business_manager_helper.app_installed_with_retries?(1) && !apple_business_manager_helper.ensure_license_and_install_app
  end

  def install_first_party_system_apps(apps, install_via:) # rubocop:todo Metrics/AbcSize
    # We block `updates-http.cdn-apple.com`, `updates.cdn-apple.com` which inturn is responsible to download and update system apps
    # Upon Restoration, the system apps like FilesApp, TranslateApp is missing as they are downloaded from this domain using restore
    # Here we are using a local tunnel to unblock and let MDM/AppStore ( based on install_via: parameter ), download the app.
    # Refer - https://browserstack.atlassian.net/browse/DF-1150
    socks5_forwarder = Privoxy::Socks5Forwarder.new(device_id)
    socks5_forwarder.forward_traffic(["updates-http.cdn-apple.com", "updates.cdn-apple.com"])
    user_apps = BrowserStack::OSUtils.execute("/usr/local/bin/gtimeout -s KILL 20 #{IOS_DEPLOY} --id #{device_id} -B").split(/\n+/)
    apps.each do |app_name|
      bundle_id = APP_DETAILS[app_name.to_sym][:bundle_id]
      if user_apps.include?(bundle_id)
        BrowserStack.logger.info("[install_first_party_system_apps] Skipping installation as #{bundle_id} is already present")
      else
        BrowserStack.logger.info("[install_first_party_system_apps] Starting Installation of #{bundle_id}, via: #{install_via}")
        if install_via == :APP_STORE
          begin
            AppStoreInstaller.navigate_to_app(device_id, app_bundle: bundle_id, app_name: APP_DETAILS[app_name.to_sym][:app_name], app_url: APP_DETAILS[app_name.to_sym][:app_url], pass: @config["apple_password"], exit_after_tapping_download: true)
          rescue => e
            if e.respond_to?(:test_output) && e.test_output&.include?("Can't find Download button")
              BrowserStack.logger.info("Executing dismiss_appstore_popup_via_xcui")
              unmark_method(:dismiss_appstore_popup_via_xcui)
              dismiss_appstore_popup_via_xcui
            end
            raise e
          end
        else
          IosMdmServiceClient.install_application(device_id, bundle_id, nil, { "purchase_method" => 1 })
        end
        raise "Failed to Install App: #{app_name}" unless IdeviceUtils.check_app_with_bundle_id_exists_with_retries(device_id, bundle_id)
      end
    end
  ensure
    BrowserStack.logger.info("[install_first_party_system_apps] Resetting Privoxy")
    socks5_forwarder.reset_privoxy
  end

  def install_to_restore_app_store(apps, install_via:)
    # We block `updates-http.cdn-apple.com`, `updates.cdn-apple.com` which inturn is responsible to download and update system apps
    # Upon Restoration, the system apps like FilesApp, TranslateApp is missing as they are downloaded from this domain using restore
    # Here we are using a local tunnel to unblock and let MDM/AppStore ( based on install_via: parameter ), download the app.
    # Refer - https://browserstack.atlassian.net/browse/DF-1150
    socks5_forwarder = Privoxy::Socks5Forwarder.new(device_id)
    socks5_forwarder.forward_traffic(["updates-http.cdn-apple.com", "updates.cdn-apple.com"])
    user_apps = BrowserStack::OSUtils.execute("/usr/local/bin/gtimeout -s KILL 20 #{IOS_DEPLOY} --id #{device_id} -B").split(/\n+/)
    apps.each do |app_name|
      bundle_id = APP_DETAILS[app_name.to_sym][:bundle_id]
      # if user_apps.include?(bundle_id)
      #   BrowserStack.logger.info("[install_first_party_system_apps] Skipping installation as #{bundle_id} is already present")
      # else
      #   BrowserStack.logger.info("[install_first_party_system_apps] Starting Installation of #{bundle_id}, via: #{install_via}")
      next unless install_via == :APP_STORE

      begin
        AppStoreInstaller.restore_app_store(device_id, app_bundle: bundle_id, app_name: APP_DETAILS[app_name.to_sym][:app_name], app_url: APP_DETAILS[app_name.to_sym][:app_url], pass: @config["apple_password"], exit_after_tapping_download: true)
      rescue => e
        if e.respond_to?(:test_output) && e.test_output&.include?("Can't find Download button")
          BrowserStack.logger.info("Executing dismiss_appstore_popup_via_xcui")
          unmark_method(:dismiss_appstore_popup_via_xcui)
          dismiss_appstore_popup_via_xcui
        end
        raise e
      end
      # else
      #   IosMdmServiceClient.install_application(device_id, bundle_id, nil, { "purchase_method" => 1 })
        # raise "Failed to Install App: #{app_name}" unless IdeviceUtils.check_app_with_bundle_id_exists_with_retries(device_id, bundle_id)
      # end
    end
  ensure
    BrowserStack.logger.info("[install_to_restore_app_store] Resetting Privoxy")
    socks5_forwarder.reset_privoxy
  end

  def enable_pwa(skip_removing_restrictions: false)
    request_reinstall_mdm_restrictions(skip_removing_restrictions: skip_removing_restrictions)
    device_state.touch_pwa_enabled_file
  end

  def disable_bluetooth
    if @device_version.to_f >= 17.0
      BrowserStack::IosMdmServiceClient.disable_bluetooth(device_id)
    else
      request_reinstall_mdm_restrictions
      BrowserStackAppHelper.run_ui_test(device_id, :disable_bluetooth, session_id: session_id)
    end
    device_state.touch_disabled_bluetooth_file
  end

  def disable_bluetooth_optimized
    if @device_version.to_f >= 17.0
      BrowserStack::IosMdmServiceClient.disable_bluetooth(device_id)
    else
      remove_bluetooth_restrictions
      BrowserStackAppHelper.run_ui_test(device_id, :disable_bluetooth, session_id: session_id)
    end
    device_state.touch_disabled_bluetooth_file
  end

  def reset_view_to_standard
    xcui_output = BrowserStackAppHelper.run_ui_test(device_id, :reset_view_to_standard, session_id: session_id)
    device_state.touch_reset_view_to_standard_file
    xcui_output
  end

  def disable_paint_timing
    BrowserStackAppHelper.run_ui_test(device_id, :safari_experimental_feature_paint_timing_disable, session_id: session_id)
    device_state.remove_paint_timing_enabled_file
  end

  def disable_safari_websocket
    BrowserStackAppHelper.run_ui_test(device_id, :safari_experimental_feature_websocket_disable, session_id: session_id)
    device_state.touch_safari_websocket_file
  end

  def disable_standby_mode
    env_vars = {}
    prefs = SETTINGS_PREFS[:disable_standby_prefs]
    prefs_output = self.class.get_prefs_for_version(device_version, device_name, prefs)

    if prefs_output
      env_vars.merge!(flow: "optimised_flow")
      launch_app_output = DeviceCtl::Device.launch_app_with_prefs(device_id, "com.apple.Preferences", prefs_output)
      raise "Failed to launch Settings app" unless launch_app_output.include? "Launched application"

      BrowserStackAppHelper.run_ui_test(device_id, :disable_standby_mode, session_id: session_id, environment_variables: env_vars)
    else
      BrowserStackAppHelper.run_ui_test(device_id, :disable_standby_mode, session_id: session_id)
    end
    device_state.touch_standby_mode_file
  rescue => e
     # we will not make the cleanup fail due to this, it does not affect any functionality
    BrowserStack.logger.info "Standby mode disable test failed: #{e}"
    raise e
  end

  def clean_bookmarks_favorites
    # TODO: Investigate - sometimes idevicebookmark seems to hang for several minutes.
    IdeviceUtils.delete_bookmarks(device_id)
  end

  def clean_safari_fav_and_bookmarks
    xcui_output = BrowserStackAppHelper.run_ui_test(device_id, :clean_safari_fav_and_bookmarks, session_id: session_id)
    if !xcui_output.nil? && !xcui_output.empty? && xcui_output[/SAFARI_FAVOURITES_TEXT_EXISTS/].nil?
      BrowserStack.logger.info "Failed to tap on safari favourites tab"
      BrowserStack::Zombie.push_logs("failed_to_tap_safari_favourites", "", { "device" => device_id  })
    end
    device_state.remove_device_logger_detected_safari_bookmarks_launched_file
    device_state.remove_device_logger_detected_safari_favorites_launched_file
    device_state.touch_force_clean_safari_bookmarks_file
    device_state.touch_force_clean_safari_favorites_file
  end

  def set_safari_default_browser_in_eu
    BrowserStack.logger.info "running set_safari_default_browser_in_eu"
    xcui_output = BrowserStackAppHelper.run_ui_test(device_id, :set_safari_default_browser_in_eu, 60, session_id: session_id)

    BrowserStack.logger.info "xcui output: #{xcui_output}"

    if !xcui_output.nil? && !xcui_output.empty?
      if xcui_output.include?("safari page not opened in app store")
        BrowserStack.logger.info("Executing dismiss_appstore_popup_via_xcui")
        unmark_method(:dismiss_appstore_popup_via_xcui)
        dismiss_appstore_popup_via_xcui
        return
      end
      BrowserStack.logger.info "default browser screen in eu did not appear" if xcui_output[/SET_SAFARI_DEFAULT_BROWSER_IN_EU_SCREEN_NOT_PRESENT/]
      if xcui_output[/SET_SAFARI_DEFAULT_BROWSER_IN_EU_SUCCEEDED/]
        device_state.touch_set_safari_default_browser_in_eu_file
        BrowserStack.logger.info "safari set as default browser in eu"
        BrowserStack::Zombie.push_logs("set_safari_default_browser_succeeded", "", { "device" => device_id, "session_id" => session_id })
      end
    end
  rescue => e
    BrowserStack.logger.info "Failed in set_safari_default_browser_in_eu for device: #{@device_id}, error: #{e.message}, #{e.backtrace.join("\n")}"
    BrowserStack::Zombie.push_logs("set_safari_default_browser_error", e.message, { "device" => device_id, "session_id" => session_id })
  ensure
    device_state.remove_force_set_safari_default_browser_in_eu_file
  end

  def clean_safari_app(safari_app_automations) # rubocop:todo Metrics/AbcSize
    xcui_output = BrowserStackAppHelper.run_ui_test(device_id, :clean_safari_app, 180, session_id: session_id,
                                                                                       environment_variables: safari_app_automations)

    if safari_app_automations["clean_safari_fav_and_bookmarks"]
      if !xcui_output.nil? && !xcui_output.empty? && xcui_output[/SAFARI_FAVOURITES_TEXT_EXISTS/].nil?
        BrowserStack.logger.info "Failed to tap on safari favourites tab"
        BrowserStack::Zombie.push_logs("failed_to_tap_safari_favourites", "", { "device" => device_id  })
      end
      device_state.remove_device_logger_detected_safari_bookmarks_launched_file
      device_state.remove_device_logger_detected_safari_favorites_launched_file
      device_state.touch_force_clean_safari_bookmarks_file
      device_state.touch_force_clean_safari_favorites_file
    end

    if safari_app_automations["clear_reading_list"]
      BrowserStack.logger.info "Safari reading list items removed"
      FileUtils.rm_f("/tmp/safari_reading_list_opened_#{device_id}")
    end
  rescue BrowserStackTestExecutionError => e
    test_output = e.test_output
    if !test_output.nil? && !test_output.empty? && test_output[/About Your Default Browser/]
      BrowserStack.logger.info "Force setting safari default browser in EU state file"
      device_state.touch_force_set_safari_default_browser_in_eu_file if device_version.to_i >= 18
    end
    # For 18.4 we were seeing the default tab being google tab. Hence we need to unmark safari_cleanup method
    if device_version.to_f >= 18.4
      BrowserStack.logger.info "Unmarking safari_cleanup method"
      unmark_method(:safari_cleanup)
    end
    raise e
  end

  def clean_safari_tab_groups
    # TODO: Add detection to tab groups
    BrowserStackAppHelper.run_ui_test(device_id, :clean_safari_tab_groups, session_id: session_id)
  end

  def clean_safari_url_bar_position
    BrowserStackAppHelper.run_ui_test(device_id, :clean_safari_url_bar_position, session_id: session_id)
    device_state.remove_device_logger_detected_safari_url_bar_changed_file
  end

  def disable_government_notifications
    return unless cleanup_check.need_disable_government_notifications?

    env_vars = {}
    prefs = SETTINGS_PREFS[:disable_government_notifications_prefs]
    prefs_output = self.class.get_prefs_for_version(device_version, device_name, prefs)

    if prefs_output
      env_vars.merge!(flow: "optimised_flow")
      launch_app_output = DeviceCtl::Device.launch_app_with_prefs(device_id, "com.apple.Preferences", prefs_output)
      raise "Failed to launch Settings app" unless launch_app_output.include? "Launched application"

      BrowserStackAppHelper.run_ui_test(device_id, :disable_government_notifications, session_id: session_id, environment_variables: env_vars)
    else
      BrowserStackAppHelper.run_ui_test(device_id, :disable_government_notifications, session_id: session_id)
    end
    device_state.touch_government_notifications_file
  end

  def disable_dark_mode
    BrowserStack::SwitchMode.new(device_id, session_id).change_appearance_to(BrowserStack::SwitchMode::LIGHT_MODE)
    device_state.touch_disable_dark_mode_file
  end

  def safari_remote_automation
    env_vars = {}
    prefs = SETTINGS_PREFS[:safari_remote_automation_prefs]
    prefs_output = self.class.get_prefs_for_version(device_version, device_name, prefs)

    if prefs_output
      env_vars.merge!(flow: "optimised_flow")
      launch_app_output = DeviceCtl::Device.launch_app_with_prefs(device_id, "com.apple.Preferences", prefs_output)
      raise "Failed to launch Settings app" unless launch_app_output.include? "Launched application"

      BrowserStackAppHelper.run_ui_test(device_id, :safari_remote_automation, session_id: session_id, environment_variables: env_vars)
    else
      BrowserStackAppHelper.run_ui_test(device_id, :safari_remote_automation, session_id: session_id)
    end
    device_state.touch_safari_remote_automation_file
  end

  # This UI test removes all the extra keyboards which have been added
  # and eventually leaves the Emoji and English keyboard on the device (so
  # that English becomes the default keyboard)
  def remove_extra_keyboards
    output = BrowserStackAppHelper.run_ui_test(device_id, :reset_keyboard_language, session_id: session_id)
    if output.include?("Foreign keyboards detected")
      BrowserStack.logger.info("Foreign keyboards detected")
      BrowserStack::Zombie.push_logs("foreign_keyboards_detected", "", { "device" => device_id  })
    end

    if output.include?("Keyboard languages are not detected")
      BrowserStack.logger.info("Keyboard languages are not detected by xcui tests")
      BrowserStack::Zombie.push_logs("foreign_keyboards_not_detected", "", { "device" => device_id  })
    end

    device_state.touch_remove_extra_keyboards_file
  end

  def set_default_font_size
    if device_version.to_f < 17.0
      output = BrowserStackAppHelper.run_ui_test(
        device_id,
        :set_default_font_size,
        120,
        session_id: session_id
      )
      device_state.touch_set_default_font_size_file
      return output
    end

    begin
      accessibility_settings_helper = Secure::AccessibilitySettingsHelper.new(@device_id, @session_id)

      accessibility_settings_helper.change_font_size_programatically(
        false, Secure::AccessibilitySettingsHelper.default_font_size
      )

      device_state.touch_set_default_font_size_file
      [true, "Font size changed to default"]
    rescue => e
      BrowserStack.logger.error("Failed changing font size to default via pymobiledevice3, error: #{e.message}, #{e.backtrace.join("\n")}")
      [false, "Failed to change font size to default"]
    end
  end

  def apple_id_signout(use_wda: false)
    return if Secure::ApplePay.apple_pay_device?(device_id) || Secure::ApplePay.dedicated_cloud_apple_pay_device?(device_id)

    output = nil

    if use_wda
      output = wda_client.apple_id_signout
      raise output['value']['message'] unless output['value']['status'] == 'pass'
    else
      output = BrowserStackAppHelper.run_ui_test(device_id, :apple_id_signout, session_id: session_id)
    end

    # If Apple ID was used during the session, then we don't want to force clean it
    # until CLEANUP_STEPS_FREQ[:force_clean_apple_id] days from now
    device_state.touch_force_clean_apple_id_file
    device_state.remove_device_logger_detected_apple_id_signed_in_file
    output
  end

  def apple_id_popup_present?(use_wda: false)
    popup_present = false
    if use_wda
      retries = 2
      while retries > 0
        output = wda_client.check_apple_id_popup
        break if output['value']['status'] == 'success'

        retries -= 1
      end
      popup_present = output['value']['message'] == 'present'
    end
    popup_present
  rescue => e
    BrowserStack.logger.info("Check apple id popup failed! - #{e.message} - #{e.backtrace}")
    false
  end

  def sign_out_sandbox_accounts
    env_vars = {}

    prefs = SETTINGS_PREFS[:sandbox_signout_prefs]
    prefs_output = self.class.get_prefs_for_version(device_version, device_name, prefs)

    if prefs_output
      # kill setting app
      processes = PyMobileDevice::Processes.list_processes(device_id)
      BrowserStack.logger.info("[DEBUG]:::Running process list: #{processes}")
      IdeviceUtils.kill_process_with_name(device_id, "Preferences") if processes.include?("Preferences")
      env_vars.merge!(flow: "optimised_flow")
      launch_app_output = DeviceCtl::Device.launch_app_with_prefs(device_id, "com.apple.Preferences", prefs_output)
      raise "Failed to launch Settings app" unless launch_app_output.include? "Launched application"

      output = BrowserStackAppHelper.run_ui_test(device_id, :signout_sandbox_accounts, session_id: session_id, environment_variables: env_vars)
    else
      output = BrowserStackAppHelper.run_ui_test(device_id, :signout_sandbox_accounts, session_id: session_id)
    end
    # If sandbox account was used during the session, then we don't want to force clean it
    # until CLEANUP_STEPS_FREQ[:force_clean_sandbox_account] days from now
    device_state.touch_force_clean_sandbox_account_file
    device_state.remove_device_logger_detected_sandbox_signed_in_file
    device_state.remove_device_logger_detected_sandbox_signin_logline_file
    device_state.remove_device_logger_detected_sandbox_signin_popup_file
    output
  end

  def siri_contacts_cleanup
    begin
      if device_version.to_i >= 16 && !IdeviceUtils.check_app_with_bundle_id_exists(device_id, CONTACTS_APP_BUNDLE_ID)
        # Installing contacts app as a fix for MOBPE-679
        BrowserStack.logger.info("Trying to install contacts app via MDM")
        BrowserStack::IosMdmServiceClient.install_application(device_id, CONTACTS_APP_BUNDLE_ID, nil, { "purchase_method" => 1 })
      end
    rescue MdmApiFatalException
      BrowserStack.logger.error("Contacts App Install Failed")
    end
    BrowserStackAppHelper.run_ui_test(device_id, :siri_contacts_cleanup, session_id: session_id)
    device_state.touch_siri_contacts_cleanup_file
  rescue BrowserStackTestExecutionError => e
    # if Siri & Search is not found, don't mark the device as offline,
    # as we don't know the state of Siri & Search for Contacts app yet
    # Don't touch the file either as that will cause this step to be not called in future
    return if e.test_output.include? "XCTAssertTrue failed - Siri & Search not found"

    raise e
  end

  def clear_call_logs
    CallLogsHelper.new(device_id, session_id).clear_call_logs
  end

  def favorite_contact_cleanup
    FavoriteContactHelper.new(device_id, session_id).cleanup
  end

  def clean_voiceover
    return unless cleanup_check.need_voiceover_cleanup?

    begin
      BrowserStackAppHelper.run_ui_test(device_id, :disable_voiceover)
    rescue => e
      BrowserStack.logger.error("Disable voiceover UI test failed with #{e.message}:\n #{e.backtrace}")
    end
    voiceover_helper = BrowserStack::VoiceoverHelper.new
    voiceover_helper.trigger_cleanup_on_bluetooth_server(device_id)
    device_state.remove_voiceover_used_file # Clearing used file for bluetooth client mini
  rescue => e
    BrowserStack.logger.error("clean_voiceover failed with #{e.message}:\n #{e.backtrace}")
  end

  def sim_cleanup
    result = DeviceSIMHelper.new(device_id, session_id).cleanup
    raise "SIM cleanup failed" unless result
  end

  def validate_sim_device # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
    BrowserStack.logger.info("SIM validation for device: #{device_id}")
    sim_helper = DeviceSIMHelper.new(device_id, "validate_#{session_id}")
    sim_enabled, error_message = sim_helper.change_sim_state("enable", true, use_wda: false, max_retry_attempts: 1)

    if !sim_enabled && error_message != "\"Used as “Primary”\" StaticText not visible"
      sim_enabled, error_message = sim_helper.change_sim_state(
        "enable", true, use_wda: false, max_retry_attempts: 2
      )
    end

    esim_is_enabled = true

    unless sim_enabled
      esim_is_enabled = false
      BrowserStack.logger.info("Enable ESIM failure #{!sim_enabled} #{error_message}")

      if error_message == "\"Used as “Primary”\" StaticText not visible"
        BrowserStack.logger.info("Switching to physical SIM flow")
      else
        raise "Failed to enable eSIM through XCUI automation"
      end
    end

    BrowserStack.logger.info("SIM signal strength for device: #{device_id}")
    check_sim_signal_strength

    begin
      if DeviceSIMHelper.sim_inserted?(device_id)
        if DeviceSIMHelper.esim_enabled?(device_id, validate_output: true)
          device_state.touch_esim_file
          device_state.remove_physical_sim_file
        else
          device_state.touch_physical_sim_file
          device_state.remove_esim_file
        end

        device_sim_config = DeviceSIMHelper.sim_config(device_id)

        device_state.write_with_lock_to_sim_config_file(
          JSON.generate(
            DeviceSIMHelper.retrieve_sim_details_from_device(
              device_id, device_sim_config["phone_number"]
            )
          )
        )
      end
    rescue IdeviceinfoOutputFieldMissing => e
      # Mark devices with flaky behaviour as non SIM devices for now.
      device_state.remove_esim_file
      device_state.remove_physical_sim_file

      # Instrumention for visibility of the faulty devices.
      BrowserStack::Zombie.push_logs(
        "esim-ideviceinfo-output-missing",
        "",
        {
          "device" => device_id,
          "session_id" => session_id,
          "error" => e.message
        }
      )
    end

    if esim_is_enabled
      sim_disabled, error_message = sim_helper.change_sim_state("disable", true, use_wda: false, max_retry_attempts: 3)
      unless sim_disabled
        BrowserStack.logger.info("Disable SIM failure #{!sim_disabled}")
        raise "Failed to disable eSIM through XCUI automation"
      end
    end

    BrowserStack.logger.info("SIM validation done device: #{device_id}")
    device_state.touch_sim_validation_check_file
  rescue => e
    device_state.remove_esim_file
    device_state.remove_physical_sim_file
    device_state.remove_sim_config_file
    BrowserStack.logger.error("validate_sim_device failed with #{e.message}:\n #{e.backtrace}")

    BrowserStack::Zombie.push_logs(
      "validate-sim-device-failure",
      "",
      {
        "device" => device_id,
        "session_id" => session_id,
        "error" => e.message
      }
    )

    raise "validate_sim_device failed with error: #{e.message}"
  end

  def sim_signal_strength_xcui_run_test
    BrowserStack.logger.info("Running XCUI test to fetch Sim Signal Strength for device: #{device_id}")
    BrowserStackAppHelper.check_and_install_browserstack_test_suite(device_id)
    xcui_output = BrowserStackAppHelper.run_ui_test(device_id, :fetch_sim_signal_strength)

    if xcui_output.nil? || xcui_output.empty?
      BrowserStack.logger.info("Failed to get signal strength from XCUI. Skipping update.")
      return { "status" => false, "xcui_output" => nil }
    end
    { "status" => true, "xcui_output" => xcui_output }
  rescue => e
    BrowserStack.logger.info("Error in running sim_signal_strength_xcui_run_test: #{e.message}, #{e.backtrace.join("\n")}")
    { "status" => false, "xcui_output" => nil }
  end

  def get_bars_from_xcui_output(xcui_output, sim_details)
    # Extract matches from the xcui_output
    # Example xcui_output: '[SIM_STRENGTH] SIM Signal Strengths: ["3 of 4 bars, signal strength"]'
    matches = xcui_output&.scan(/(-?\d+) of 4 bars, signal strength/) || []
    BrowserStack.logger.info("Signal strength matches found: #{matches.inspect}")

    # Return Unknown if no matches found
    if matches.empty?
      BrowserStack.logger.info("No signal strength matches found. Matches: #{matches.inspect}")
      return [{ "carrier" => sim_details[0]["carrier"], "signal_strength" => "Unknown", "sim_slot" => sim_details[0]["sim_slot"] }]
    end

    # Update SIM details, ignoring extra signal strength values
    sim_details.each_with_index.map do |sim, index|
      next sim unless matches[index]  # Ignore extra matches beyond available sim slots

      signal_strength = matches[index][0]  # Ensure correct assignment
      signal_strength = signal_strength.to_i < 0 ? "Unknown" : signal_strength # If signal strength is negative, then it is unknown
      sim_slot = sim["sim_slot"]

      BrowserStack.logger.info("Processing match ##{index + 1}: Signal Strength - #{signal_strength}, sim_slot - #{sim_slot}")

      {
        "carrier" => sim["carrier"],
        "signal_strength" => signal_strength,
        "sim_slot" => sim_slot
      }
    end

  rescue => e
    BrowserStack.logger.info("Error in get_bars_from_xcui_output: #{e.message}, #{e.backtrace.join("\n")}")
    [{ "carrier" => sim_details[0]["carrier"], "signal_strength" => "Unknown", "sim_slot" => sim_details[0]["sim_slot"] }]
  end

  def check_sim_signal_strength
    sim_details = @device_config["sim_details"]
    if sim_details.nil? || sim_details.empty?
      BrowserStack.logger.info("No SIM details found for device: #{device_id} when checking sim signal strength")
      return
    end

    test_response = sim_signal_strength_xcui_run_test
    if test_response['status'] == false
      # Push logs to zombie with error 'XCUI-Automation-Failed'
      BrowserStack.logger.info("Failed to get signal strength from XCUI. Pushing logs to zombie.")
      zombie_data = { "carrier" => sim_details[0]["carrier"], "signal_strength" => "Unknown bars", "sim_slot" => sim_details[0]["sim_slot"], "dedicated_device" => @device_state.dedicated_device_file_present?, "esim_device" => @device_state.esim_file_present? }
      BrowserStack::Zombie.push_logs("sim-signal-strength", 'XCUI-Automation-Failed', { "device" => device_id, "data" => zombie_data })
      return
    end

    sim_details_output = get_bars_from_xcui_output(test_response['xcui_output'], sim_details)

    sim_details_output.each do |sim_output|
      # Prepare the data to be pushed to zombie
      zombie_data = {
        "carrier" => sim_output["carrier"],
        "signal_strength" => "#{sim_output['signal_strength']} bars",
        "sim_slot" => sim_output["sim_slot"],
        "dedicated_device" => @device_state.dedicated_device_file_present?,
        "esim_device" => @device_state.esim_file_present?
      }
      BrowserStack::Zombie.push_logs("sim-signal-strength", nil, { "device" => device_id, "data" => zombie_data })
    end
  rescue => e
    BrowserStack.logger.info("Error in fetching sim_details: #{e.message}, #{e.backtrace.join("\n")}")
  end

  def log_ios_opened_apps
    filename = "/tmp/launched_apps/#{session_id}"
    if File.exist?(filename)
      apps_opened = File.read(filename).to_s
      BrowserStack::Zombie.push_logs("all_apps_opened", "", { "device" => device_id, "session_id" => session_id, "data" => apps_opened })
    end
  rescue => e
    BrowserStack.logger.error("Error in log_ios_opened_apps: #{e.message}")
    BrowserStack::Zombie.push_logs("apps_opened_failed", e.message, { "device" => device_id, "session_id" => session_id })
  ensure
    FileUtils.rm_f(filename)
  end

  def download_file_helper
    @download_file_helper ||= DownloadFile.new(device_id, session_id)
  end

  def download_file_cleanup
    # Clean all the files and setup done for download files
    result = download_file_helper.cleanup
    raise "Download file cleanup failed" unless result
  end

  def apple_wallet_cleanup
    # High timeout, because if the user has added multiple passes then this
    # automation will take a lot of time.
    # In case timeout is reached, then cleanup will retry this step and
    # hopefully after all the retries we will get rid of all the passes
    BrowserStackAppHelper.run_ui_test(device_id, :apple_wallet_cleanup, 100, session_id: session_id)
  end

  def enable_apple_wallet_enable_double_click_side_button
    BrowserStackAppHelper.run_ui_test(device_id, :wallet_double_click_side_button, session_id: session_id)
    device_state.touch_apple_wallet_double_click_side_button_enabled_file
  end

  def geoguard_cleanup
    geoguard_helper.cleanup
  end

  def message_notification_cleanup
    return true unless device_state.enable_message_notification_failure_file_present?

    BrowserStack.logger.info("Starting message_notification_cleanup cleanup")
    Utils.enable_notifications(device_id)
    device_state.remove_enable_message_notification_failure_file
    BrowserStack.logger.info("message_notification_cleanup cleanup successful")
    true
  rescue => e
    BrowserStack.logger.error("Failed to cleanup message_notification_cleanup, error: #{e.message}, #{e.backtrace.join("\n")}")
    raise e
  end

  def configure_assistive_touch
    Secure::AssistiveTouchHelper.new(device_id, session_id).switch("configure")
  end

  def evaluate_apple_pay
    apple_pay_class_obj = Secure::ApplePay.new(device_id, "cleanup_onboarding_#{session_id}", nil)
    res = apple_pay_class_obj.evaluate_apple_pay
    BrowserStack.logger.info("Apple Pay configuration response - #{res}")
  end

  def disable_assistive_touch
    Secure::AssistiveTouchHelper.new(device_id, session_id).switch("disable")
    # Not throwing any errors for now since this is first feature going out with libimobiledevice, need to observe
    # it on how it holds on machines, as if there are failures we will get alerts.
  end

  def date_time_cleanup
    DateTimeHelper.new(device_id, { "session_id" => session_id }).cleanup
  end

  def date_cleanup
    DateTimeHelper.new(device_id, { "session_id" => session_id }).cleanup_date
  end

  def disable_low_power_mode
    begin
      env_vars = {}
      prefs = SETTINGS_PREFS[:disable_low_power_mode_prefs]
      prefs_output = self.class.get_prefs_for_version(device_version, device_name, prefs)
      if prefs_output
        env_vars.merge!(flow: "optimised_flow")
        launch_app_output = DeviceCtl::Device.launch_app_with_prefs(device_id, "com.apple.Preferences", prefs_output)
        raise "Failed to launch Settings app" unless launch_app_output.include? "Launched application"

        BrowserStackAppHelper.run_ui_test(device_id, :disable_low_power_mode, session_id: session_id, environment_variables: env_vars)
      else
        BrowserStackAppHelper.run_ui_test(device_id, :disable_low_power_mode, session_id: session_id)
      end
    rescue => e
      BrowserStack.logger.error("Disabling low power mode failed with #{e.message}:\n #{e.backtrace}")
      BrowserStack.logger.info("Retrying disable lower power mode")
      # There is a bug in XCode 9 that causes this XCUI test to fail on some devices with:
      #
      # Assertion Failure: <unknown>:0: failed: caught "NSInternalInconsistencyException",
      # "Activity cannot be used after its scope has completed."
      #
      # A retry causes this particular XCUI test to pass. Retrying for all iOS versions to be extra careful
      BrowserStackAppHelper.run_ui_test(device_id, :disable_low_power_mode, session_id: session_id)
    end
    FileUtils.rm_f("/tmp/low_power_mode_opened_#{device_id}")
  end

  def enable_wifi
    BrowserStackAppHelper.run_ui_test(device_id, :enable_wifi, session_id: session_id)
    device_state.touch_enabled_wifi_file
  end

  def clear_reading_list
    BrowserStackAppHelper.run_ui_test(device_id, :clear_safari_reading_list, session_id: session_id)
    BrowserStack.logger.info "Reading list items removed"
    FileUtils.rm_f("/tmp/safari_reading_list_opened_#{device_id}")
  end

  def disable_auto_lock
    env = {}
    prefs = SETTINGS_PREFS[:disable_autolock_prefs]
    prefs_output = self.class.get_prefs_for_version(device_version, device_name, prefs)

    if prefs_output
      env.merge!(flow: "optimised_flow")
      launch_app_output = DeviceCtl::Device.launch_app_with_prefs(device_id, "com.apple.Preferences", prefs_output)
      raise "Failed to launch Settings app" unless launch_app_output.include? "Launched application"

    end

    BrowserStackAppHelper.run_ui_test(device_id, :disable_auto_lock, session_id: session_id, environment_variables: env)
    device_state.touch_disable_auto_lock_file
  rescue => e
    raise e unless CustomMDMManager.is_custom_mdm_device?(device_id)

    device_state.touch_disable_auto_lock_file
  end

  def clean_imessages_app
    SMSHelper.new(device_id, session_id).sms_cleanup
  end

  def clear_passcode
    Secure::Passcode.new(device_id, session_id).clear_passcode(REDIS_CLIENT)
  end

  def delete_downloads
    output = BrowserStackAppHelper.run_ui_test(device_id, :delete_downloads, session_id: session_id)
    device_state.touch_force_clean_files_app_file
    device_state.remove_device_logger_detected_file_download_triggered_file
    output
  end

  def reset_keyboard_settings
    BrowserStackAppHelper.run_ui_test(device_id, :reset_keyboard_settings, session_id: session_id)
    device_state.touch_reset_keyboard_settings_file
  end

  def disable_airplane_mode_from_settings
    return unless cleanup_check.need_disable_airplane_mode_from_settings?

    BrowserStackAppHelper.run_ui_test(device_id, :disable_airplane_mode, session_id: session_id)
  end

  def disconnect_wifi
    BrowserStackAppHelper.run_ui_test(device_id, :disconnect_wifi, session_id: session_id, environment_variables: { wifi_name: @config['static_conf']['ssid'] })
  end

  def clean_pwa_and_waiting_state_apps(timeout = 120)
    BrowserStackAppHelper.run_ui_test(device_id, :clean_pwa_and_waiting_state_apps, timeout, session_id: session_id, environment_variables: { pwa_min_ios_support_version: PWA_MIN_IOS_SUPPORT_VERSION })
  end

  def clear_contact_accounts
    # TODO: Use it when Apple Login is detected

    # Siri manages to save Account Contact information when user log in to AppStore
    # This UI test will clear it
    # Timeout is high because it depends on number of accounts added to the device
    BrowserStackAppHelper.run_ui_test(device_id, :clear_contact_accounts, 100, session_id: session_id)
  end

  def set_env_vars_for_navigation(device_id, device_version, device_name, pref_for_automation)
    env_vars = {}
    prefs = SETTINGS_PREFS[pref_for_automation]
    prefs_output = self.class.get_prefs_for_version(device_version, device_name, prefs)

    if prefs_output
      env_vars.merge!(flow: "optimised_flow")
      launch_app_output = DeviceCtl::Device.launch_app_with_prefs(device_id, "com.apple.Preferences", prefs_output)
      raise "Failed to launch Settings app" unless launch_app_output.include? "Launched application"
    end

    env_vars
  end

  def enable_location_services # rubocop:todo Metrics/AbcSize
    env_vars = {}
    if device_version == "16.0"
      build_version = IdeviceUtils.ideviceinfo(device_id, "BuildVersion").first
      env_vars = {
        'IOS_BUILD_VERSION' => build_version
      }
      BrowserStack.logger.info("Passing BuildVersion(#{build_version}) to xctest case for iOS16 Beta bug")
    end
    chrome = Chrome.new
    is_full_cleanup = device_state.mdm_full_cleanup_file_present? || device_state.first_cleanup_file_present?
    if !IdeviceUtils.check_app_with_bundle_id_exists(device_id, chrome.bundle_id) || (is_full_cleanup && device_state.chrome_cleanup_required_file_present?)
      # chrome should be installed during this test
      ppuid = PpuidFile.new(device_id).ppuid
      chrome.update_app_version_using_ios_version(device_version)
      chrome.setup(device_id, ppuid, true)
      chrome_cleanup
    end

    prefs = SETTINGS_PREFS[:location_service_prefs]
    prefs_output = self.class.get_prefs_for_version(device_version, device_name, prefs)

    if prefs_output
      env_vars.merge!(flow: "optimised_flow")
      launch_app_output = DeviceCtl::Device.launch_app_with_prefs(device_id, "com.apple.Preferences", prefs_output)
      raise "Failed to launch Settings app" unless launch_app_output.include? "Launched application"

      BrowserStackAppHelper.run_ui_test(device_id, :enable_location_services, 120, session_id: session_id, environment_variables: env_vars)
    else
    # It takes more time on older devices so the timeout is 120 seconds
      BrowserStackAppHelper.run_ui_test(device_id, :enable_location_services, 120, session_id: session_id, environment_variables: env_vars)
    end
    device_state.touch_location_services_enabled_file
    @device_state.remove_device_location_off_file
  end

  def disable_testflight_notifications
    result = "failure"
    notifications_cell_found = nil
    notifications_disabled_file = "/usr/local/.browserstack/config/installed_TestFlight/disabled_TestFlight_notifs_#{device_id}"
    notifications_opened_file = "/tmp/testflight_notification_opened_#{device_id}"

    begin
      response = BrowserStackAppHelper.run_ui_test(device_id, :disable_testflight_notifications)
      notifications_cell_found = !response.include?("Not able to locate Notifications cell")
      result = "sucess"
      FileUtils.touch(notifications_disabled_file)
      FileUtils.rm_f(notifications_opened_file)
    ensure
      BrowserStack::Zombie.push_logs("disable_testflight_notifications", "", { "device" => device_id, "session_id" => session_id, "data" => { "test_flight_notification" => "disabled", "result" => result, "notifications_cell_found" => notifications_cell_found } })
    end
  end

  def enable_redirect_extension
    return unless device_version.to_f >= 15

    bundle_id = "com.browserstack.Redirect"
    retries_left = 3
    begin
      env_vars = {}
      prefs = SETTINGS_PREFS[:enable_redirect_extension_prefs]
      prefs_output = self.class.get_prefs_for_version(device_version, device_name, prefs)

      if prefs_output
        env_vars.merge!(flow: "optimised_flow")
        launch_app_output = DeviceCtl::Device.launch_app_with_prefs(device_id, "com.apple.Preferences", prefs_output)
        raise "Failed to launch Settings app" unless launch_app_output.include? "Launched application"

        BrowserStackAppHelper.run_ui_test(device_id, :enable_redirect_extension, session_id: session_id, environment_variables: env_vars)
      else
        BrowserStackAppHelper.run_ui_test(device_id, :enable_redirect_extension)
      end
      device_state.touch_enable_redirect_extension_file
    rescue => e
      BrowserStack.logger.info "Uninstalling #{bundle_id} since Redirect extension is not showing in settings"
      device_state.remove_enable_redirect_extension_file
      ppuid = PpuidFile.new(device_id).ppuid
      redirect = Redirect.new
      redirect.update_app_version_using_ios_version(device_version)
      redirect.setup(device_id, ppuid, true)
      retries_left -= 1
      retry if retries_left > 0
      raise e
    end
  end

  def testflight_login
    BrowserStackAppHelper.run_ui_test(device_id, :testflight_login, environment_variables: { apple_id: @config["apple_id"], apple_password: @config["apple_password"] })
  end

  def disable_testflight_background_refresh
    env_vars = {}
    prefs = SETTINGS_PREFS[:disable_testflight_background_refresh_prefs]
    prefs_output = self.class.get_prefs_for_version(device_version, device_name, prefs)

    if prefs_output
      env_vars.merge!(flow: "optimised_flow")
      launch_app_output = DeviceCtl::Device.launch_app_with_prefs(device_id, "com.apple.Preferences", prefs_output)
      raise "Failed to launch Settings app" unless launch_app_output.include? "Launched application"

      BrowserStackAppHelper.run_ui_test(device_id, :disable_testflight_background_refresh, session_id: session_id, environment_variables: env_vars)
    else
      BrowserStackAppHelper.run_ui_test(device_id, :disable_testflight_background_refresh, session_id: nil)
    end
    device_state.touch_disable_testflight_background_refresh_file
  end

  def clean_stored_password
    xcui_output = BrowserStackAppHelper.run_ui_test(device_id, :clean_stored_password, session_id: nil)
    device_state.touch_clean_stored_password_file
    device_state.remove_device_logger_detected_password_added_file if device_state.device_logger_detected_password_added_file_present?
    BrowserStack::Zombie.push_logs("clean_saved_password", "", { "device" => device_id  }) if !xcui_output.nil? && !xcui_output.empty? && xcui_output.include?("Deleting passwords stored in settings")
  end

  def disable_stage_manager
    env_vars = set_env_vars_for_navigation(device_id, device_version, device_name, :disable_stage_manager_pref)
    BrowserStackAppHelper.run_ui_test(device_id, :disable_stage_manager, session_id: nil, environment_variables: env_vars)
    device_state.touch_disable_stage_manager_file
    device_state.remove_device_logger_detected_stage_manager_enabled_file if device_state.device_logger_detected_stage_manager_enabled_file_present?
  rescue => e
    BrowserStack.logger.info("Disable Stage Manager XCUI Tests failed: #{e}")
    BrowserStack::Zombie.push_logs("disable_stage_manager_xcui_failed", "", { "device" => device_id  })
  end

  # TODO: Dead code, can be removed later and it's related methods.
  def clean_passwords_app
    @configuration_profiles_manager.install_profile(:restrictions, { flags: ['enable_passwords_app'] }, install_via: :automatic)
    xcui_output = BrowserStackAppHelper.run_ui_test(device_id, :clean_password_app, session_id: nil)
    @configuration_profiles_manager.install_profile(:restrictions, install_via: :automatic)
    device_state.touch_clean_stored_password_file
    device_state.remove_device_logger_detected_password_added_file if device_state.device_logger_detected_password_added_file_present?
    BrowserStack::Zombie.push_logs("clean_passwords_app", "", { "device" => device_id  }) if !xcui_output.nil? && !xcui_output.empty? && xcui_output.include?("Deleting passwords stored in settings")
  rescue => e
    BrowserStack.logger.info("Clean passwords app failed: #{e}")
    BrowserStack::Zombie.push_logs("clean_passwords_app-failed", "", { "device" => device_id  })
    raise e
  end

  def clean_device_theme
    BrowserStack.logger.info("Cleaning Device theme on #{device_id}")
    xcui_output = BrowserStackAppHelper.run_ui_test(device_id, :clean_device_theme, session_id: nil)
    device_state.remove_theme_change_attempted_file if device_state.theme_change_attempted_file_present?
    device_state.touch_theme_change_cleaned_file
    BrowserStack::Zombie.push_logs("clean_device_theme", "", { "device" => device_id  })
  rescue => e
    BrowserStack.logger.info("Failed to reset Device theme: #{e}")
    BrowserStack::Zombie.push_logs("clean_device_theme_failed", "", { "device" => device_id  })
    raise e
  end

  def disable_apple_intelligence
    BrowserStack.logger.info("Disabling Apple Intelligence if models are downloaded on #{device_id}")
    env_vars = set_env_vars_for_navigation(device_id, device_version, device_name, :disable_apple_intelligence_prefs)
    xcui_output = BrowserStackAppHelper.run_ui_test(device_id, :disable_apple_intelligence, session_id: nil, environment_variables: env_vars)
    device_state.remove_disable_apple_intelligence_file if device_state.disable_apple_intelligence_file_present?
    device_state.touch_disable_apple_intelligence_file
    BrowserStack::Zombie.push_logs("disable_apple_intelligence", "", { "device" => device_id  })
  rescue => e
    BrowserStack.logger.info("Failed to Disable Apple Intelligence: #{e}")
    BrowserStack::Zombie.push_logs("disable_apple_intelligence_failed", "", { "device" => device_id  })
    raise e
  end

  def set_time_to_utc
    # It takes more time on older devices so the timeout is 120 seconds
    env_vars = {}

    prefs = SETTINGS_PREFS[:set_time_to_UTC_prefs]
    prefs_output = self.class.get_prefs_for_version(device_version, device_name, prefs)

    if prefs_output
      env_vars.merge!(flow: "optimised_flow")
      launch_app_output = DeviceCtl::Device.launch_app_with_prefs(device_id, "com.apple.Preferences", prefs_output)
      raise "Failed to launch Settings app" unless launch_app_output.include? "Launched application"

      BrowserStackAppHelper.run_ui_test(device_id, :set_time_to_utc, 120, session_id: session_id, environment_variables: env_vars)
    else
      BrowserStackAppHelper.run_ui_test(device_id, :set_time_to_utc, 120, session_id: session_id)
    end
    device_state.touch_set_time_to_utc_file
  end

  def clear_third_party_accounts
    # This UI test will clear third party accounts if signed into
    # Timeout is high because it depends on number of accounts added to the device
    xcui_output = BrowserStackAppHelper.run_ui_test(device_id, :clear_third_party_accounts, 100, session_id: session_id)
    if !xcui_output.nil? && !xcui_output.empty? && xcui_output[/MAIL_STATIC_TEXT_EXISTS/].nil?
      BrowserStack.logger.info "Failed to tap on Mail App"
      BrowserStack::Zombie.push_logs("failed_to_tap_mail_app", "", { "device" => device_id  })
    end
    device_state.remove_third_party_account_sign_in_file
  end

  # To check whether reset to UTC timezone is needed for cleanup
  def timezone_reset_to_utc_needed?
    timezone = IdeviceUtils.ideviceinfo(device_id, "TimeZone")[0]
    timezone != 'GMT'
  end

  # This method is for cleanup of timzone to UTC. Different from set_time_to_utc as that is not for cleanup but to sync device clock with server
  def reset_time_zone_to_utc
    if device_version.to_i < 12
      BrowserStack.logger.info("The device version is not supported for timezone : #{device_version.to_i}")
    else
      begin
        timezone_helper = BrowserStack::TimezoneHelper.new(@device_id, @session_id, "cleanup", @device_config)
        response_status = timezone_helper.change_time_zone("utc", "GMT")
        raise "Timezone change failure. Falling back to XCUI" if !response_status && @device_version.to_i < 18

        BrowserStack::Zombie.push_logs("timezone-change-success", "", { "device" => @device_id, "session_id" => @session_id, "data" => { "cleanup" => "success" } }) if response_status
      rescue MDMTimezoneFailureException => e
        raise e
      rescue => e
        begin
          BrowserStackAppHelper.run_ui_test(device_id, :change_time_zone, environment_variables: { TIMEZONE: "utc" })
          BrowserStack::Zombie.push_logs("timezone-change-success", "XCUI Fallback", { "device" => device_id, "session_id" => session_id, "data" => { "cleanup" => "success", "fallback" => "true" } })
        rescue BrowserStackTestExecutionError => error
          response_message = error.test_output
          BrowserStack::Zombie.push_logs("timezone-change-failure", response_message, { "device" => device_id, "session_id" => session_id, "data" => { "cleanup" => "failure" } })
        end
      end
    end
  end

  # This was added as part of this task: MOB-5645, to ensure that proxy profile is installed with correct machine ip when device is moved from one machine to other.
  # But this shouldn't be required now, since now in cleanup cleanup_iphone#re_enroll_device_to_mdm which triggers profiles installation when re_mdm_periodic_check state file is not present for that device on the machine.
  # This state file won't be present, since device is moved to another host.
  # Have removed this step for cfgutil managed devices( #ConfigurationProfilesManager.device_uses_cfgutil_managed_profiles? ), but can be removed for other devices too in future.
  def check_global_proxy_installed
    begin
      xcui_test_output = BrowserStackAppHelper.run_ui_test(device_id, :get_global_proxy_url, 120, session_id: session_id)
    rescue BrowserStackTestExecutionError
      raise MdmProfileException # This will trigger install_mdm_profiles
    end

    my_ip = NetworkHelper::NetworkSetup.new.get_ip

    if my_ip.nil?
      BrowserStack.logger.info("#{device_id} | IP is nil skipping global proxy check")
      return
    end

    BrowserStack.logger.info("#{device_id} | IP=#{my_ip}; XCUI output=|#{xcui_test_output}|")

    if xcui_test_output.include?(my_ip)
      BrowserStack.logger.info("#{device_id} | Touching check_global_proxy_file as correct global proxy is installed")
      device_state.touch_check_global_proxy_file
      return
    end

    raise MdmProfileException, 'Wrong global proxy installed in device'
  end

  def clean_orientation
    check_orientation_lock
    set_orientation("portrait")

    # If orientation lock was used during the session, then we don't want to force clean it
    # until CLEANUP_STEPS_FREQ[:check_orientation_lock] days from now
    device_state.touch_force_check_orientation_lock_file
    device_state.remove_device_logger_detected_orientation_lock_changed_file
    FileUtils.rm_f "/tmp/orientation_lock_opened_#{device_id}"
  end

  # Non-UI automation stuff
  def chrome_cleanup
    app = Chrome.new
    app.update_app_version_using_ios_version(device_version.to_i)
    app.cleanup(device_id, device_version)
  end

  def reset_gps_location
    location_simulator.reset
  end

  def crash_logs_cleanup
    if device_version.to_f >= 17.0
      Thread.bs_run do
        IdeviceUtils.clear_crash_reports_from_device(device_id, @session_id)
      end
    else
      IdeviceUtils.clear_crash_reports_from_device(device_id, @session_id)

      nil
    end
  end

  def copy_media
    # 3 files under the Downloads directory are not cleared
    # downloads.28.sqlitedb, downloads.28.sqlitedb-shm and downloads.28.sqlitedb-wal
    # These are used for signing in to App Store
    mount_point = ifuse.mount_point
    command = "sudo su -l #{USER} -c '\
find #{mount_point}/ -not \\( -name 'downloads*sqlite*' -or -name 'Downloads' \\) -delete; \
mkdir -p #{mount_point}/DCIM/100APPLE; \
cp #{@config['bs_media']}/* #{mount_point}/DCIM/100APPLE/; \
sleep 2;\
'"
    ifuse.run(command)
  end

  def backup_preloaded_files
    mount_point = ifuse.mount_point
    command = "sudo su -l #{USER} -c '\
mkdir -p #{@config['photo_data']}/#{device_id}; \
cp -R #{mount_point}/PhotoData/* #{@config['photo_data']}/#{device_id}/; \
'"
    ifuse.run(command)
  end

  def delete_listed_files(files_to_delete)
    FileUtils.rm(files_to_delete)
  rescue
    BrowserStack.logger.info("delete_preloaded_media_files: forcefully deleting files")
    #needed to fix offline-apply2files (MOBPE-324)
    FileUtils.rm(files_to_delete, force: true ) #scary
  end

  def delete_files(path, subdirs_to_delete, patterns_to_not_delete)
    subdirs_to_delete ||= []
    subdirs_to_delete.each do |subdir|
      subdir_path = "#{path}/#{subdir}"
      files_to_delete = if File.file?(subdir_path)
                          Dir.glob(subdir_path)
                        else
                          Dir.glob("#{subdir_path}/**/*")
                        end
      patterns_to_not_delete.each do |pattern|
        files_to_delete.reject! { |file| file.match(pattern) }
      end
      files_to_delete.select! { |file| File.file?(file) }
      BrowserStack.logger.info("Deleting files in #{subdir} dir: #{files_to_delete.inspect}")
      delete_listed_files(files_to_delete)
    end
  end

  def optimised_preload_media_deletion(subdirs_to_delete)
    is_version_valid = ios_device.device_eligible_for_optimised_flow?

    if is_version_valid
      BrowserStack.logger.info('Adding deletion of PhotoData DCIM and killing assetsd process')
      subdirs_to_delete.push("PhotoData")
      subdirs_to_delete.push("DCIM")
    end
    subdirs_to_delete
  rescue => e
    BrowserStack.logger.info "optimised_preload_media_deletion failed #{e}"
    subdirs_to_delete
  end

  def preloaded_files_consistent?(mount_point)
    # Our current logic checks only count sometimes there could be video files from v2 video which can make count == 9 and hence the file won't get cleaned
    mp4_media_files = Dir.glob("#{mount_point}/DCIM/*/*.MP4")
    return false unless mp4_media_files.empty?

    all_media_files = Dir.glob("#{mount_point}/DCIM/**/*").reject { |f| File.directory?(f) }
    BrowserStack.logger.info "Preloaded files: #{all_media_files}"
    return false if all_media_files.size != PRELOADED_MEDIA_HASHES.size

    # Image files in ios >= 14.1 have different md5 on different versions with different compressions
    # So skipping md5 check for them
    return true if device_version.to_f >= ALTERNATE_MEDIA_CLEANUP_MIN_IOS_SUPPORT_VERSION

    current_media_hashes = []
    all_media_files.each do |file|
      current_media_hashes.push(Digest::MD5.file(file).hexdigest)
    end

    are_files_consistent = current_media_hashes.sort == PRELOADED_MEDIA_HASHES.sort
    BrowserStack.logger.info "Are preloaded files consistent?: #{are_files_consistent}"
    are_files_consistent
  end

  def install_preloaded_files # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
    BrowserStack.logger.info("Cleaning downloaded media and adding default media")

    preload_media_with_reboot_due = device_version.to_f >= ALTERNATE_MEDIA_CLEANUP_MIN_IOS_SUPPORT_VERSION && device_state.clean_preloaded_media_with_reboot_file_older_than_days?(CLEANUP_STEPS_FREQ[:preload_media_with_reboot])
    if preload_media_with_reboot_due
      BrowserStack.logger.info("Rebooting device before preloaded media cleanup step")
      DeviceManager.reboot_and_wait(device_id)
      @device_state.touch_clean_preloaded_media_with_reboot_file
    end

    device_version = BrowserStack::Version.new(@device_version).canonical_without_patch
    media_directory = @config['bs_media_multiversion']
    raise "Can't find media at #{media_directory}" unless Dir.exist?(media_directory)

    # 3 files under the Downloads directory are not cleared
    # downloads.28.sqlitedb, downloads.28.sqlitedb-shm and downloads.28.sqlitedb-wal
    # These are used for signing in to App Store

    mount_point = ifuse.mount_point

    media_files_preloaded = ifuse.run do # rubocop:disable Metrics/BlockLength
      subdirs_to_delete = Dir.children(mount_point)
      subdirs_to_delete.delete("Downloads")
      patterns_to_not_delete = [/.*downloads.*sqlite.*/]

      if @device_version.to_f >= ALTERNATE_MEDIA_CLEANUP_MIN_IOS_SUPPORT_VERSION
        BrowserStack.logger.info('Skipping deletion of PhotoData, will be handled by BrowserStack App')
        subdirs_to_delete.delete("PhotoData")
      end

      bs_media_files_path = Dir.glob("#{media_directory}/IMG*")

      files_in_download = Dir.glob("#{mount_point}/Downloads/**/*")
      BrowserStack.logger.info("Downloads: #{files_in_download.inspect}")
      check_photos_file_size = Gem::Version.new(device_version) >= Gem::Version.new("16.0")
      if !preload_media_with_reboot_due && photos_sqlite_state_consistent?(true, check_photos_file_size) && preloaded_files_consistent?(mount_point) && !force_install_preloaded_media?
        BrowserStack.logger.info("Skipping preloading media as media is already present and no extra files found.")
        BrowserStack.logger.info("Not deleting: #{subdirs_to_delete}")
        next false
      end
      subdirs_to_delete = optimised_preload_media_deletion(subdirs_to_delete)
      BrowserStack.logger.info("install_preloaded_files: will delete #{subdirs_to_delete}")
      delete_files(mount_point, subdirs_to_delete, patterns_to_not_delete)
      IdeviceUtils.kill_process_with_name(device_id, "assetsd") if ios_device.device_eligible_for_optimised_flow?
      next true if @device_version.to_f >= ALTERNATE_MEDIA_CLEANUP_MIN_IOS_SUPPORT_VERSION

      BrowserStack.logger.info("install_preloaded_files: adding preloaded media for device version #{device_version}")

      FileUtils.mkdir_p %W[#{mount_point}/DCIM/100APPLE #{mount_point}/PhotoData]
      # At this point is safe to abort the function. The user data has been erased.
      db_files = Dir.glob("#{media_directory}/Photos_#{device_version}.sqlite*")
      raise "No Photos.sqlite databases for v#{device_version} are available!" if db_files.empty?

      db_files.each do |f|
        target_name = File.basename f.sub("_#{device_version}", '')
        FileUtils.cp f, "#{mount_point}/PhotoData/#{target_name}"
      end
      # For iOS 14.1+, we use the ios-njb-app to preload media otherwise videos aren't playable
      # See https://browserstack.atlassian.net/browse/MOB-6465
      FileUtils.cp(bs_media_files_path, "#{mount_point}/DCIM/100APPLE/") if device_version.to_f < 14.1
      raise "Preloaded files inconsistent even after reloading" unless preloaded_files_consistent?(mount_point)

      true
    end

    BrowserStack.logger.info("Media added?: #{media_files_preloaded}")
    FileUtils.rm(force_install_preloaded_media_file) if force_install_preloaded_media?
    device_state.touch_needs_preload_media_file if media_files_preloaded
    media_files_preloaded
  end

  def install_preloaded_files_legacy
    BrowserStack.logger.info("[LEGACY APPROACH] Cleaning Downloads and adding default media")
    mount_point = ifuse.mount_point

    # 3 files under the Downloads directory are not cleared
    # downloads.28.sqlitedb, downloads.28.sqlitedb-shm and downloads.28.sqlitedb-wal
    # These are used for signing in to App Store
    command = "sudo su -l #{USER} -c '\
update_happened=$(find #{mount_point}/PhotoData/Photos.sqlite* -newer #{session_start_file(device_id)} 2> /dev/null); \
no_data=$(sqlite3 #{mount_point}/PhotoData/Photos.sqlite \"select count(*) from ZGENERICASSET\" 2> /dev/null)
if [[ ! -z \"$update_happened\" ]] || [[ -z \"$no_data\" ]] || [[ \"$no_data\" = 0 ]]; then \
  find #{mount_point}/ -not \\( -name 'downloads*sqlite*' -or -name 'Downloads' \\) -delete; \
  mkdir -p #{mount_point}/DCIM/100APPLE #{mount_point}/PhotoData; \
  cp #{@config['bs_media']}/* #{mount_point}/DCIM/100APPLE/; \
  cp -R #{@config['photo_data']}/#{device_id}/* #{mount_point}/PhotoData/; \
fi;\
'"

    ifuse.run(command)
    BrowserStack.logger.info("Media added.")
  end

  def cleanup_custom_media
    return if @device_version.to_f >= 17.0 && !@device_state.custom_media_cleanup_file_present?

    FileUtils.mkdir_p(custom_media_mount_point)
    custom_media_app = BrowserStack::App.new(nil, nil)

    custom_media_app.mount_root_directory_of_app(device_id, custom_media_mount_point, BROWSERSTACK_APP_BUNDLE_ID)
    _, status = BrowserStack::OSUtils.execute("rm -rf #{custom_media_mount_point}/Documents/Photos #{custom_media_mount_point}/Documents/Videos", return_status = true)
    custom_media_app.unmount_root_directory_of_app(custom_media_mount_point)

    # On some devices, rm command is failing with error "Operation not permitted".
    if status != 0
      BrowserStack.logger.info "Custom Media cleanup failed with ifuse, falling back to ios-deploy"
      BrowserStack::OSUtils.execute("/usr/local/bin/gtimeout -s KILL 20 #{IOS_DEPLOY} --id #{device_id} -1 #{BROWSERSTACK_APP_BUNDLE_ID} --rmtree Documents/Photos")
      BrowserStack::OSUtils.execute("/usr/local/bin/gtimeout -s KILL 20 #{IOS_DEPLOY} --id #{device_id} -1 #{BROWSERSTACK_APP_BUNDLE_ID} --rmtree Documents/Videos")
    end

    @device_state.remove_custom_media_cleanup_file if @device_version.to_f >= 17.0
  rescue => e
    BrowserStack.logger.error "Custom Media cleanup failed, error: #{e}"
    BrowserStack.logger.info "Reinstalling BrowserStack to as fallback of cleanup_custom_media"
    BrowserStackAppHelper.build_and_install_browserstack_app(device_id)
  end

  def trust_dummy_app_via_xcui
    BrowserStackAppHelper.trust_dummy_app_certificate(@device_id)
  end

  def idevice_name(name)
    BrowserStack.logger.info("Setting #{@device_id} name to #{name}")
    IdeviceUtils.idevice_name(@device_id, name: name)
  end

  def kill_apps_via_wda(apps)
    wda_client.kill_apps(apps)
  end

  def kill_apps
    BrowserStackAppHelper.run_ui_test(device_id, :kill_apps, 30, session_id: session_id)
    device_state.touch_force_kill_apps_file
  end

  def reset_contacts_app
    BrowserStack.logger.info "Started reset_contacts_app for device: #{@device_id}"
    # We need to add contacts app icon to home screen because user can remove it
    # mid session
    contacts_app_helper.add_icon_to_home_screen
    return unless device_state.device_logger_detected_contacts_app_opened_file_present?

    Utils.send_to_eds({
      session_id: session_id,
      product: {
        ios_contacts_app_accessed: true
      }
    }, EdsConstants::APP_LIVE_TEST_SESSIONS, true)
    wda_client.kill_apps([CONTACTS_APP_BUNDLE_ID])
    device_state.remove_device_logger_detected_contacts_app_opened_file
    BrowserStack.logger.info "Completed reset_contacts_app for device: #{@device_id}"
  rescue => e
    BrowserStack.logger.info "Failed in reset_contacts_app for device: #{@device_id}, error: #{e.messsage}, #{e.backtrace.join("\n")}"
    raise e
  end

  def force_re_enroll_to_mdm(cleanup_type)
    BrowserStack.logger.info("#{cleanup_type}: Starting MDM Re-enrollment for #{@device_id}")
    IosMdmServiceClient.run_mdm_ui_automation(@device_id)
    BrowserStack::Zombie.push_logs("mdm-re-enrollment", "#{cleanup_type}: MDM re-enrollment successful", { "device" => @device_id })
    sleep 3
  rescue BrowserStackTestExecutionError => e
    BrowserStack.logger.info("#{cleanup_type}: MDM re-enrollment automation failed")
    BrowserStack::Zombie.push_logs("mdm-re-enrollment", "#{cleanup_type}: MDM re-enrollment automation failed", { "device" => @device_id, "data" => { "automation" => "failure" } })
    raise "MDM re-enrollment failed"
  end

  def install_proxy_profile(cleanup_type)
    @configuration_profiles_manager.install_profile(:proxy, install_via: :automatic)
    BrowserStack.logger.info("#{cleanup_type}: Proxy Profile Installation Successful")
    device_state.touch_check_global_proxy_file
  rescue => e
    BrowserStack.logger.info("#{cleanup_type}: Proxy Profile Installation Failed")
    raise "Proxy Profile Installation Failed"
  end

  def erase_and_restore
    Timeout.timeout(600) do
      backup_manager = CFGUtilBackupManager.new(@device_id)
      raise CFGUtilBackupManager::BackupNotFoundError, "No backup found for #{@device_id}" unless backup_manager.backup_ready?

      device_state.remove_app_store_login_file
      backup_manager.erase_device
      sleep 15
      BrowserStack.logger.info("Full Cleanup: Waiting for device to come online after erasing")
      sleep 5 until OSUtils.execute("idevice_id -l | grep #{@device_id}") != ""
      BrowserStack.logger.info("Full Cleanup: Device came online after erase, Waiting for the erase process to be completed")
      sleep 30
      BrowserStack.logger.info("Full Cleanup: Proceeding with Restoring")
      backup_manager.restore_backup
      BrowserStack.logger.info("Full Cleanup: Waiting for device to come online from restoration")
      sleep 5 until OSUtils.execute("idevice_id -l | grep #{@device_id}") != ""
      BrowserStack.logger.info("Full Cleanup: Device came online after restoring, Waiting for the restoration process to be completed")
      sleep 10
      device_state.touch_install_cfgutil_restrictions_profile_file # Since the profile would be removed after erase, we need to install it back
    end
  rescue Timeout::Error => e
    BrowserStack.logger.error("Full Cleanup: Erase and Restore Timed out")
    raise "Full Cleanup: Erase and Restore Timed out"
  end

  def remove_rigid_restrictions_profile
    # If this profile is not removed, MDM enrollment would fail
    # See - https://browserstack.atlassian.net/browse/DF-913
    backup_manager = CFGUtilBackupManager.new(@device_id)
    return unless backup_manager.supported?

    cfgutil_profile_manager = BrowserStack::CFGUtilProfileManager.new(@device_id)
    cfgutil_profile_manager.remove_restrictions_profile

    device_state.touch_install_cfgutil_restrictions_profile_file # Touch this file to install the profile back
  end

  def install_rigid_restrictions_profile
    # Preventing Users from installing Profiles on our devices and Setting passcode by removing MDM profiles through this rigid profile
    # See - https://browserstack.atlassian.net/browse/DF-913
    backup_manager = CFGUtilBackupManager.new(@device_id)
    return unless backup_manager.supported? &&
      device_state.install_cfgutil_restrictions_profile_file_present?

    cfgutil_profile_manager = BrowserStack::CFGUtilProfileManager.new(@device_id)
    cfgutil_profile_manager.install_restrictions_profile

    device_state.remove_install_cfgutil_restrictions_profile_file # This file can be removed as the profile is installed now
  end

  def unmark_method(step)
    BrowserStack.logger.info("Deleting #{step}")
    @completed_steps.delete(step)
    save_completed_steps_state
  end

  def mark_step_pending(step)
    BrowserStack.logger.info("Marking #{step} as pending.")
    @completed_steps[step] = false
  end

  def photos_sqlite_state_consistent?(push_to_zombie = false, check_file_size = false)
    photos_sqlite_helper.ios_media_state_consistent?(push_to_zombie: push_to_zombie, check_file_size: check_file_size)
  end

  def reset_accessibility_settings
    Secure::AccessibilitySettingsHelper.new(device_id, session_id, "cleanup").reset_accessibility_settings if @device_state.accessibility_settings_file_present?
  end

  def set_orientation(orientation)
    BrowserStack.logger.info "Setting orientation to #{orientation}"
    wda = WdaClient.new(wda_port)
    wda.set_orientation(orientation)
  rescue => e
    BrowserStack.logger.error("Error setting orientation: #{e.class.name} #{e.message}: #{e.backtrace}")
    if @device_version.to_f >= 18.0 && e.message.include?("waitForQuiescenceIncludingAni")
      BrowserStack.logger.error("Skipping the error safely")
    else
      raise CleanupAutomationError, e.message
    end
  end

  private

  def ifuse
    @ifuse ||= Ifuse.new(device_id)
  end

  def wda_client
    @wda_client ||= WdaClient.new(wda_port)
  end

  def ios_device
    @ios_device ||= IosDevice.new(device_id, self.class.to_s, BrowserStack.logger)
  end

  def photos_sqlite_helper
    @photos_sqlite_helper ||= PhotosSqliteHelper.new(@device_id, @device_version)
  end

  def contacts_app_helper
    @contacts_app_helper ||= ContactsAppHelper.new(@device_id, session_id)
  end

  def location_simulator
    @location_simulator ||= LocationSimulator.new(@device_id)
  end

  def geoguard_helper
    @geoguard_helper ||= GeoguardHelper.new(@device_id, session_id, BrowserStack.logger)
  end

  # TODO: Move this to device state class
  def session_start_file(device_id)
    "#{STATE_FILES_DIR}/#{device_id}_session_start_indicator"
  end

  def force_install_preloaded_media?
    File.exist?(force_install_preloaded_media_file)
  end

  def force_install_preloaded_media_file
    "#{STATE_FILES_DIR}/#{device_id}_reinstall_preloaded_media"
  end

  # TODO: Move this to device state class
  def custom_media_mount_point
    "/tmp/custom_media_mount_#{device_id}"
  end

  def step_completed?(step)
    BrowserStack.logger.info("Skipping #{step}, already cleaned") if @completed_steps[step]
    @completed_steps[step]
  end

  def mark_step_completed(step)
    BrowserStack.logger.info("Marking #{step} as done. Will not call again for this cleanup")
    @completed_steps[step] = true
  end

  def check_orientation_lock
    BrowserStack.logger.info "Checking if Orientation lock is disabled on phone"
    # Send to home screen
    begin
      wda = WdaClient.new(wda_port)
      wda.homescreen
      sleep 1

      open_control_centre(wda)

      # Disable Orientation lock if enabled
      wda.check_orientation(orientation_lock_value)
      sleep 1

      # Dismissing the control centre
      dismiss_control_centre(wda)
    rescue => e
      BrowserStack.logger.error "#{e.class.name} #{e.message}: #{e.backtrace.join("\n")}"
      raise CleanupAutomationError, e.message
    end
  end

  def open_control_centre(wda)
    # Bringing up the control centre

    # For iPhones, with Home button simple drag is unable to open to Control Center
    # Using drag_with_tap_duration to tap for 0.1s and then start dragging
    # This opens the control centre
    # Currently using this for all devices on v15+

    if Gem::Version.new(device_version) >= Gem::Version.new("18.0")
      wda.open_control_centre(duration = 0.1) # Tap for 0.1 sec
    else
      control_center_coordinates = DeviceParams.control_centre_show_coordinates(device_name, device_version)
      if Gem::Version.new(device_version) >= Gem::Version.new("15.0")
        wda.drag_with_tap_duration(
          control_center_coordinates['start']['x'],
          control_center_coordinates['start']['y'],
          control_center_coordinates['end']['x'],
          control_center_coordinates['end']['x'], # please check MOBFR-884 before fixing this
          0.1
        ) # Tap for 0.1 sec
      else
        wda.drag(
          control_center_coordinates['start']['x'],
          control_center_coordinates['start']['y'],
          control_center_coordinates['end']['x'],
          control_center_coordinates['end']['x']
        ) # someone brave please fix the typo here one day (but only after checking MOBFR-884)
      end
    end

    sleep 2
  end

  def dismiss_control_centre(wda)
    if Gem::Version.new(device_version) >= Gem::Version.new("18.0")
      wda.dismiss_control_centre
    else
      dismiss_coordinates = DeviceParams.control_centre_dismiss_coordinates(device_name, device_version)
      wda.tap(dismiss_coordinates['x'], dismiss_coordinates['y'])
    end
  end

  def scroll_till_element_visible(driver, element, direction = "down")
    return if element.displayed?

    if device_version.to_f >= 13.4
      5.times do
        driver.execute_script("mobile: scroll", direction: direction)
        return if element.displayed?
      end
      raise CleanupAutomationError, "Could not find element #{element.name}"
    else
      driver.execute_script("mobile: scroll", element: element.ref, toVisible: true)
    end
  end

  def mark_already_dismissed
    BrowserStack.logger.info "Touching already_dismissed_#{device_id}"
    dir_name = "/usr/local/.browserstack/config/already_dismissed"
    FileUtils.mkdir_p(dir_name) unless Dir.exist? dir_name
    # If this file exists => AppStore location services popup was already dismissed by some user
    FileUtils.touch("#{dir_name}/already_dismissed_#{device_id}")
  end

  def get_completed_steps_state
    return {} unless File.exist?(completed_steps_file)

    completed_steps_content = File.read(completed_steps_file)
    return {} if completed_steps_content.empty?

    JSON.parse(completed_steps_content, symbolize_names: true)
  end

  def completed_steps_file
    "#{BrowserStack::Configuration['state_files_dir']}/cleanup_completed_steps_#{device_id}"
  end

  # These are are class level methods because they are being used outside of the
  # cleanup flow, i.e. in install_app.rb
  class << self
    def get_prefs_for_version(device_version, device_name, prefs)
      return nil if device_version.to_f < prefs[:min_os_version]

      device_version = device_version.to_s
      rules = prefs[:rules] || []

      # Iterate from bottom to top (reverse order)
      rules.reverse_each do |rule|
        # Check if device and OS version match
        if rule[:device_name].match?(device_name) && rule[:os_version].match?(device_version)
          return rule[:rule] == "include" ? rule[:pref] : nil
        end
      end

      nil
    end

    def appstore_cleanup(device_id)
      BrowserStackAppHelper.run_ui_test(device_id, :apple_id_signout)
    rescue => e
      BrowserStack.logger.error "#{e.class.name} #{e.message}: #{e.backtrace.join("\n")}"
      raise CleanupAutomationError, e.message
    end
  end
end
