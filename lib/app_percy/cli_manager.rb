# frozen_string_literal: true

require 'timeout'

require_relative '../utils/osutils'
require_relative '../../server/device_manager'

module AppPercy
  class CLIManager

    LOGGING_DIR = '/var/log/browserstack'

    attr_reader :device

    def initialize(device_id)
      @device = DeviceManager.device_configuration_check(device_id)
    end

    def start_percy_cli(params)
      cli_start_command =
        "#{cli_env(params['app_percy']['env'])}" \
        "percy app exec:start --port #{CLIManager.cli_port(@device['port'])} " \
        " > #{cli_log_file_path(params['automate_session_id'])} 2>&1"

      Thread.bs_run { system(cli_start_command) }
    end

    def stop_percy_cli
      if cli_running?
        system("percy app exec:stop --port #{CLIManager.cli_port(@device['port'])}")
        begin
          attempts ||= 1
          raise 'Percy CL<PERSON> still running' if cli_running?
        rescue => e
          if (attempts += 1) < 5
            sleep 1
            retry
          end
          CLIManager.force_stop(device['port'])
        end
      end
      !cli_running?
    end

    def cli_running?
      BrowserStack::OSUtils.is_process_running?("percy\\\ app\\\ exec:start", "'port #{CLIManager.cli_port(@device['port'])}'")
    end

    def cli_check
      Timeout.timeout(5) do
        loop do
          if cli_running?
            BrowserStack.logger.info "Percy CLI started successfully"
            break
          end
          BrowserStack.logger.info "Percy CLI still starting ..."
          sleep(1)
        end
      end
    rescue Timeout::Error
      BrowserStack.logger.info "App Percy Timed out waiting CLI to start"
    end

    def self.cli_port(device_port)
      "5#{device_port}"
    end

    def cli_log_file_path(session_id)
      "#{LOGGING_DIR}/percy_cli.#{session_id}_#{CLIManager.cli_port(@device['port'])}.log"
    end

    def self.force_stop(device_port)
      BrowserStack::OSUtils.kill_process("percy\\\ app\\\ exec:start", "'port #{CLIManager.cli_port(device_port)}'")
    end

    private

    def cli_env(params)
      env = ""
      params.each do |key, value|
        env += "#{key}='#{value}' "
      end
      env
    end
  end
end
