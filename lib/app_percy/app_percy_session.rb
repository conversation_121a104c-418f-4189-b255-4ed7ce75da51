require_relative '../privoxy_manager'
require_relative './cli_manager'
require 'app_percy_utils'

module AppPercy
  class Session
    attr_reader :device_id, :cli_manager

    FRAMEWORK = 'XCUI'
    PLAN_LIMIT_ERROR_MSG =
      'This organization has exceeded the limits of the Percy BrowserStack plan'

    def initialize(device_id)
      @device_id = device_id
      @cli_manager = AppPercy::CLIManager.new(@device_id)
    end

    def start(params)
      start_percy_cli(params)
    end

    def stop(session_id)
      stop_percy_cli(session_id)
    end

    def start_percy_cli(params)
      AppPercy::Util.logit('start_percy_cli', FRAMEWORK, suppress: true) do |event_data|
        session_id = params['automate_session_id']
        event_data[:session_id] = session_id
        event_data[:device_id] = device_id
        cli_manager.start_percy_cli(params)
        cli_manager.cli_check
        output = cli_manager.cli_running?
        unless output
          url = org_limit_reached?(session_id)
          if url
            event_data[:message] = 'Organization plan limit reached'
            event_data[:organization_slug] = url.split('/')[-2]
            BrowserStack.logger.info(event_data[:message])
            BrowserStack.logger.info(event_data[:organization_slug])
          else
            event_data[:message] = 'Percy CLI start failed'
          end
          event_data[:success] = false
          return false
        end
        BrowserStack.logger.info('Percy CLI started')
        event_data[:success] = true
      end
    end

    def stop_percy_cli(session_id)
      AppPercy::Util.logit('stop_percy_cli', FRAMEWORK, suppress: true) do |event_data|
        event_data[:session_id] = session_id
        event_data[:device_id] = device_id
        output = cli_manager.stop_percy_cli
        unless output
          event_data[:success] = false
          event_data[:message] = 'CLI stop failed'
          return false
        end
        BrowserStack.logger.info('Percy CLI stopped')
        event_data[:success] = true
      end
    end

    def org_limit_reached?(session_id)
      file_content = File.read(cli_manager.cli_log_file_path(session_id))
      return false unless file_content.include?(PLAN_LIMIT_ERROR_MSG)

      url_regex = %r{https?://\S+}
      file_content[url_regex]
    rescue => e
      BrowserStack.logger.info("session=#{session_id}, Failed to read file: #{e.message}")
      false
    end
  end
end
