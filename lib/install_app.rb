require 'fileutils'
require 'appium_lib'
require 'selenium-webdriver'
require_relative './utils/idevice_utils'
require_relative './helpers/automation'
require_relative './cleanup_iphone'
require_relative './custom_exceptions'

class InstallApp # rubocop:todo Metrics/ClassLength
  class << self
    def install(device_name, device_version, app_name, bundle_id, appium_port, udid, apple_id, password)
      BrowserStack.logger.info "Starting #{app_name} app install for #{device_name} #{device_version}"

      if device_version.to_i > 10
        driver = Automation.open_app_store_and_continue(udid, device_version)
        sign_in_via_app_store(driver, device_version, apple_id, password)
      elsif device_version.to_i == 10
        sign_in_via_settings(udid, apple_id, password)
        driver = Automation.open_app_store_and_continue(udid, device_version)
      else
        raise InstallAppException, "This iOS version is not supported"
      end

      touch_appstore_usage_file(udid)

      # Testflight is not accessible via App Store search anymore on iOS 11.
      # See https://browserstack.atlassian.net/browse/MOB-5751
      # FIXME: This is defining a specific edge case in a generic class
      if device_version.to_i == 11 && app_name == "TestFlight"
        # Changing app name to match item name in menu.
        # TODO: Add an argument for developper (Apple, Google LLC, ...)
        download_from_purchased(driver, "TestFlight, Apple", password)
      else
        search_for_app(driver, device_version, app_name)
        click_search_result(driver, app_name)
        download_and_install(driver, device_version, password)
      end

      handle_popups(driver)
      wait_for_install_complete(udid, bundle_id)
      handle_popups(driver)
    rescue AppStoreSigninError => e
      BrowserStack.logger.error "Unable to sign into app store #{e.message} - #{e.backtrace}"
      raise e
    rescue => e
      BrowserStack.logger.error "#{e.message} - #{e.backtrace}"
      raise AppInstallationException, "install app failed - #{e.message}"
    ensure
      BrowserStack.logger.info "Signing out of appstore"
      sign_out(driver, device_version, udid) if driver
      remove_appstore_usage_file(udid)
      BrowserStack.logger.info "#{app_name} installation done"
    end

    def sign_in_via_app_store(driver, device_version, apple_id, password)
      BrowserStack.logger.info 'Signing into the appstore in app store.'
      find_element_with_name_or_label(driver, "My Account").click
      sleep 1
      if device_version == '14.1' || device_version >= '15.0'
        driver.find_element(:xpath, "//XCUIElementTypeTextField[contains(@name, 'Apple ID')]").send_keys apple_id
        driver.find_element(:xpath, "//XCUIElementTypeSecureTextField[contains(@name, 'Password')]").send_keys password
      else
        find_element_with_name_or_label(driver, "Apple ID").send_keys apple_id
        find_element_with_name_or_label(driver, "Password").send_keys password
      end
      find_element_with_name_or_label(driver, "Sign In").click
      sleep 5
      handle_2fa_auth_pop_up(driver, device_version)
      handle_sign_in_popups(driver)
      find_element_with_name_or_label(driver, "Done").click
      sleep 1
      BrowserStack.logger.info 'Signed in'
    rescue => e
      raise AppStoreSigninError, e.message
    end

    private

    def touch_appstore_usage_file(udid)
      # Touching this file would help in cases when the installation fails along with the signout causing a reboot.
      # This file should then trigger appstore cleanup so that user sign out is successful at least.
      FileUtils.touch("/tmp/appstore_opened_#{udid}")
    end

    def remove_appstore_usage_file(udid)
      file_name = "/tmp/appstore_opened_#{udid}"
      File.delete file_name if File.exists?(file_name)
    end

    def search_for_app(driver, device_version, app_name)
      driver.reset
      BrowserStack.logger.info "Starting #{app_name} app search"

      begin
        find_element_with_name_or_label(driver, "Continue").click
      rescue Selenium::WebDriver::Error::NoSuchElementError
        BrowserStack.logger.info "whats new tab not found, continuing..."
      end

      BrowserStack.logger.info "Clicking Search tab"
      find_element_with_name_or_label(driver, "Search").click

      BrowserStack.logger.info "Getting search bar"
      search_bar = driver.find_element(class_name: "XCUIElementTypeSearchField")

      send_search_input(driver, device_version, search_bar, app_name)
    rescue => e
      raise InstallAppException, e.message
    end

    def send_search_input(driver, device_version, search_bar, keys)
      BrowserStack.logger.info "Sending search input"
      search_bar.click
      sleep 2 # wait for keyboard to open up
      search_bar.clear
      search_bar.send_keys keys
      sleep 1
      # No search button from iOS 12 onwards
      if device_version.to_i >= 12
        search_bar.send_keys "\n"
      else
        BrowserStack.logger.info "Getting search button"
        search_button = driver.find_elements(:accessibility_id, "Search")[-1]
        search_button.click
      end
      sleep 1
    rescue => e
      raise InstallAppException, e.message
    end

    def click_search_result(driver, app_name)
      BrowserStack.logger.info "Click #{app_name}"
      app = driver.find_element(:xpath, "//*[contains(@name,'#{app_name}')]")
      app.click
      sleep 2
    rescue => e
      raise InstallAppException, e.message
    end

    def sign_in_via_settings(device_id, apple_id, password)
      BrowserStack.logger.info 'Signing into the appstore via the Settings app.'
      driver = Automation.open_settings_app(device_id)
      driver.set_wait(2)
      driver.execute_script 'mobile: scroll', name: 'iTunes & App Store'
      BrowserStack.logger.info 'Scrolled to App Store settings'
      driver.find('iTunes & App Store').click
      BrowserStack.logger.info 'Clicked iTunes & App Store button'
      driver.find('Sign In').click
      BrowserStack.logger.info 'Clicked Sign In button'
      driver.finds('Apple ID')[1].send_keys apple_id
      BrowserStack.logger.info 'Apple ID entered'
      driver.find('Password').send_keys password
      BrowserStack.logger.info 'password entered'
      sign_in_button = driver.finds('Sign In')[1]
      sign_in_button ||= driver.finds('Sign In').last
      sign_in_button.click
      BrowserStack.logger.info 'Clicked Sign In button'
      sleep 5
      handle_sign_in_popups(driver)
      # driver.find 'Password Settings' # this is not used, commenting for the moment
      BrowserStack.logger.info 'Sign In Successful'
    rescue Selenium::WebDriver::Error::NoSuchElementError => e
      raise AppStoreSigninError, "Sign in failed: #{e.message}"
    end

    def handle_2fa_auth_pop_up(driver, device_version)
      # Click on other options
      begin
        if device_version.to_i >= 14
          begin
            find_element_with_name_or_label(driver, "Other Options").click
          rescue
            find_element_with_name_or_label(driver, "Other options").click
          end
        else
          find_element_with_name_or_label(driver, "Apple ID Security")
          find_element_with_name_or_label(driver, "Other options").click
        end
      rescue Selenium::WebDriver::Error::NoSuchElementError
        BrowserStack.logger.info "2FA screen didn't show"
      end

      # Click on do not upgrade in alert
      begin
        alert = driver.find_element(:class, 'XCUIElementTypeAlert')
        alert_text = alert.text.downcase!
      rescue Selenium::WebDriver::Error::NoSuchElementError
        alert_text = nil
      end

      case alert_text
      when /protect your account/
        BrowserStack.logger.info 'Handle popup: "Two-factor authentication is the best way to keep..."'
        if device_version.to_i >= 14
          begin
            find_element_with_name_or_label(driver, "Don’t Upgrade").click
          rescue
            find_element_with_name_or_label(driver, "Do not upgrade").click
          end
        else
          find_element_with_name_or_label(driver, "Do not upgrade").click
        end
        BrowserStack.logger.info 'Redirected to app store'
        sleep 2
        nil
      when nil
        BrowserStack.logger.info "No popups found, continuing..."
      else
        raise "Unhandled sign-in popup: #{alert_text}"
      end
    end

    def handle_sign_in_popups(driver)
      begin
        alert = driver.find_element(:class, 'XCUIElementTypeAlert')
        alert_text = alert.text
      rescue Selenium::WebDriver::Error::NoSuchElementError
        alert_text = nil
      end

      case alert_text
      when /This Apple ID is only valid for purchases/
        BrowserStack.logger.info 'Handle popup: "This Apple ID is only valid for purchases in x country..."'
        find_element_with_name_or_label(driver, "OK").click
        BrowserStack.logger.info 'Redirected to app store'
        sleep 2
        nil
      when /Apple ID Verification/
        raise AppleIDVerificationException
      when nil
        BrowserStack.logger.info "No popups found, continuing..."
      else
        raise "Unhandled sign-in popup: #{alert_text}"
      end
    rescue => e
      raise InstallAppException, e.message
    end

    def download_and_install(driver, device_version, password)
      BrowserStack.logger.info "Redownloading app"
      if device_version.to_i > 10
        # label variants include: "redownload", "re-download", "Re-download"
        BrowserStack.logger.info "Click 'download' button"
        button = driver.find_element(:xpath, "//XCUIElementTypeButton[contains(@label,'download')]")
        button.click
      elsif device_version.to_i == 10
        BrowserStack.logger.info "Click 'PurchaseButton' button"
        button = driver.find_element(:name, "PurchaseButton")
        button.click

        # If this element exists, it is the first time the app is being installed.
        begin
          driver.find_element(:name, "INSTALL")
          first_download(driver, device_version, password)
        rescue Selenium::WebDriver::Error::NoSuchElementError
          BrowserStack.logger.info "Redownloading app proceeding..."
        end
      end
    rescue Selenium::WebDriver::Error::NoSuchElementError
      BrowserStack.logger.info "Redownloading app failed. Try download for first time."
      first_download(driver, device_version, password)
    rescue => e
      raise InstallAppException, e.message
    end

    def first_download_gt10(driver, password)
      # Sometimes the name is 'GET' and sometimes 'get'
      BrowserStack.logger.info "Attempting to click 'get'"
      begin
        get = find_element_with_name_or_label(driver, "get")
      rescue Selenium::WebDriver::Error::NoSuchElementError
        get = find_element_with_name_or_label(driver, "GET")
      end
      get.click

      sleep 2
      BrowserStack.logger.info "Attempting to click 'Install'"
      install = find_element_with_name_or_label(driver, "Install")
      install.click

      sleep 2
      BrowserStack.logger.info "Putting in the password"
      begin
        password_field = find_element_with_name_or_label(driver, "Password")
      rescue Selenium::WebDriver::Error::NoSuchElementError
        password_field = driver.find_element(:xpath, "//XCUIElementTypeSecureTextField[contains(@value, 'Password')]")
      end
      password_field.send_keys password

      sleep 2
      BrowserStack.logger.info "Clicking 'Sign In' button"
      sign_in = find_element_with_name_or_label(driver, "Sign In")
      sign_in.click

      #check for alert require password for additional purchases alert. two options (Require After 15 Minutes, Always Require)
      begin
        sleep 2
        BrowserStack.logger.info "Click on 'Always Require'"
        find_element_with_name_or_label(driver, "Always Require").click
      rescue Selenium::WebDriver::Error::NoSuchElementError
        BrowserStack.logger.info "Password require alert not found."
      end
    end

    def first_download(driver, device_version, password)
      BrowserStack.logger.info "Attempting first download"
      if device_version.to_i > 10
        first_download_gt10(driver, password)

      elsif device_version.to_i == 10
        install = driver.find_element(:name, "INSTALL")
        install.click
        sleep 2
        password_field = driver.find_element(:accessibility_id, "Password")
        password_field.send_keys password
        sleep 2
        get = driver.find_element(:name, "Get")
        get.click
      end
    rescue => e
      raise InstallAppException, e.message
    end

    def download_from_purchased(driver, app_name, password)
      driver.reset
      BrowserStack.logger.info "Trying to install #{app_name} from Purchased menu"
      find_element_with_name_or_label(driver, "My Account").click
      sleep 1
      find_element_with_name_or_label(driver, "Purchased").click
      sleep 2
      BrowserStack.logger.info "Scrolling to #{app_name}"
      driver.execute_script('mobile: scroll', name: app_name)
      testflight_button = find_element_with_name_or_label(driver, app_name)
      testflight_download = testflight_button.find_element(:xpath, "//XCUIElementTypeButton[contains(@name,'#{device_version.to_f > 15 ? 'OfferButton' : 'download'}')]")
      BrowserStack.logger.info "Clicking on re-download button for #{app_name}"
      testflight_download.click
      sleep 2

    rescue => e
      raise InstallAppException, e.message
    end

    def wait_for_install_complete(udid, bundle_id)
      retries = 0
      while retries < 5
        BrowserStack.logger.info "Trying install check #{retries}"
        retries += 1
        sleep 5
        return true if IdeviceUtils.app_installed?(udid, bundle_id)
      end
      raise InstallAppException, "Could not validate #{bundle_id} installation"
    rescue => e
      raise InstallAppException, e.message
    end

    def sign_out(driver, device_version, device_id)
      BrowserStack.logger.info "Trying to sign the user out"
      driver.driver_quit

      CleanupIphone.appstore_cleanup(device_id)
    rescue => e
      raise InstallAppException, e.message
    end

    def handle_popups(driver)
      install_retry = false
      BrowserStack.logger.info "Handling popups"
      popups = [
        { name: "older_version_required", button_text: "Download" },
        { name: "region_change", button_text: "OK" },
        { name: "save_password", button_text: "No" },
        { name: "save_password_free_items", button_text: "Save" },
        { name: "require_password", button_text: "Always Require" },
        { name: "explicit_content", button_text: "Allow" },
        { name: "region_change", button_text: "OK" }
      ]
      popups.each do |p|
        sleep 2
        region_changed = close_popup(driver, p[:name], p[:button_text])
        install_retry = true if region_changed
      end
      install_retry
    rescue => e
      raise InstallAppException, e.message
    end

    def close_popup(driver, popup_name, button_text)
      BrowserStack.logger.info "handling #{popup_name}"
      driver.set_wait 4
      close_button = driver.find_elements(:accessibility_id, button_text)[-1]
      close_button.click
      popup_name == "region_change"
    rescue => e
      BrowserStack.logger.info "#{popup_name} popup did not display: #{e.message}"
      false
    end

    def find_element_with_name_or_label(driver, text)
    # name was changed for many elements in iOS 15.4.1 but label was same
      driver.find_element(:accessibility_id, text)
    rescue Selenium::WebDriver::Error::NoSuchElementError
      begin
        driver.find_element(:name, text)
      rescue Selenium::WebDriver::Error::NoSuchElementError
        driver.find_element(:xpath, "//*[@label='#{text}']")
      end
    end
  end
end
