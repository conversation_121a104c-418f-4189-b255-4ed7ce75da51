require 'base64'
require 'json'
require 'fileutils'
require 'digest'
require 'securerandom'

class ConvertRequest
  def initialize(folder)
    @request_folder = folder
    raise 'Uninitialized image converter folder' if @request_folder.nil? || @request_folder.empty?
  end

  def create_request(dest_filename, json_data)
    dir_name = File.dirname(dest_filename)
    FileUtils.mkdir_p(dir_name) unless Dir.exist? dir_name

    if File.exist?(dest_filename)
      BrowserStack.logger.info("Not generating request as already created !!.")
    else
      Utils.write_to_file(dest_filename, json_data.to_json)
      BrowserStack.logger.info("Convert request created #{dest_filename} : #{json_data}")
    end
  end

  def start(device, image_disk_filename, s3_file_name, screenshot_lock_file, session_id, start_time, orientation = 'portrait', key_id = nil, secret_key = nil, use_wda = false, check_black_screenshot = false)

    json_data = {
      file_name: image_disk_filename,
      dest: s3_file_name,
      orientation: orientation,
      screenshot_lock_file: screenshot_lock_file,
      key_id: key_id,
      secret_key: secret_key,
      session_id: session_id,
      start_time: start_time,
      use_wda: use_wda,
      check_black_screenshot: check_black_screenshot
    }

    dest_filename = "#{@request_folder}/#{SecureRandom.uuid}.json"

    create_request(dest_filename, json_data)
    puts "creating #{dest_filename} with data: #{json_data}"
  rescue => e
    puts "Something went wrong in sending this request: file: #{image_disk_filename} : #{e.message}" + e.backtrace.join("\n")

  end
end
