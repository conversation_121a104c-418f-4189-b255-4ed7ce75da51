require 'match'

require_relative '../configuration'
require_relative '../custom_exceptions'
require_relative '../utils/idevice_utils'

class FastlaneMatch

  def initialize(device_id)
    @device_id = device_id
    @config = BrowserStack::Configuration.conf
    @user = @config['user']
    @params = {
      app_identifier: @config['fastlane_match_app_identifier'],
      force: false,
      force_for_new_devices: false,
      git_branch: nil,
      git_url: @config['fastlane_match_git_url'],
      keychain_name: @config['appium_keychain'],
      keychain_password: @config['appium_keychain_password'],
      platform: IdeviceUtils.os(@device_id),
      readonly: true,
      shallow_clone: false,
      skip_confirmation: false,
      skip_docs: false,
      storage_mode: 'git',
      team_id: nil,
      team_name: nil,
      type: "development",
      username: nil,
      verbose: false,
      workspace: nil,
      clone_branch_directly: true
    }
  end

  def run(branch)
    match(branch)

    data = fetch_fastlane_match_environment_variables

    raise FastlaneError, 'fastlane match failed' unless data.key?('uuid')

    ppuid = data['uuid']
    team_id = data['team-id']

    [ppuid, team_id]
  end

  def run_chromium(branch)
    match_chromium(branch)
    fetch_chromium_match_environment_variables(branch)
  end

  private

  # Fastlane match:
  # - Fetches the latest version of the given branch from the realmobile certificate repo
  #
  # - Decrypts the provisioning profile, certificate and private key files.
  #
  # - Adds the certificate & private key to the keychain (cert + key = codesigning identity)
  #
  # - Saves the mobileprovision file on the machine:
  # /Users/<USER>/Library/MobileDevice/Provisioning Profiles/<ppuid>.mobileprovision
  #
  def match(branch)
    set_match_params
    match_params = @params.clone
    match_params[:git_branch] = branch
    match_params[:app_identifier] = @config['fastlane_match_app_identifier']
    Match::Runner.new.run(match_params)
  end

  def match_chromium(branch)
    set_match_params
    match_params = @params.clone
    match_params[:git_branch] = "#{branch}_chromium"
    match_params[:app_identifier] = chromium_bundles(branch)
    Match::Runner.new.run(match_params)
  end

  def chromium_bundles(branch)
    bundle_prefix = chromium_bundle_prefix(branch)
    [
      "#{bundle_prefix}.chrome.ios.dev",
      "#{bundle_prefix}.chrome.ios.dev.ContentTodayExtension",
      "#{bundle_prefix}.chrome.ios.dev.CredentialProviderExtension",
      "#{bundle_prefix}.chrome.ios.dev.IntentsExtension",
      "#{bundle_prefix}.chrome.ios.dev.OpenExtension",
      "#{bundle_prefix}.chrome.ios.dev.SearchTodayExtension",
      "#{bundle_prefix}.chrome.ios.dev.ShareExtension",
      "#{bundle_prefix}.chrome.ios.dev.TodayExtension",
      "#{bundle_prefix}.chrome.ios.dev.WidgetKitExtension"
    ]
  end

  def chromium_bundle_prefix(branch)
    account_name_split = branch.split('_')
    account_name_split[0] = account_name_split[0][0] # only take the first letter
    # production_57_******** => p.57.********
    "browserstack.chromium.#{account_name_split.join('.')}"
  end

  # Fastlane match initlises env variables during its run. These vars include:
  # - provisioning profile unique id (ppuid)
  # - team id
  # This method returns these variables as a hash
  def fetch_fastlane_match_environment_variables
    vars = {}

    ENV.each do |key, value|
      next unless key.start_with?(fastlane_env_string)

      key = key.gsub(fastlane_env_string, '')
      key = 'uuid' if key.empty?
      key = key.split('_').last
      vars[key] = value
    end

    vars
  end

  def fetch_chromium_match_environment_variables(branch)
    vars = {}
    chromium_bundles(branch).each do |bundle|
      vars[bundle] = ENV["sigh_#{bundle}_development"]
    end
    vars
  end

  # Normally, this would look like: sigh_com.example.app_developement
  # However, we use a wildcard "*" instead of the app bundle id
  def fastlane_env_string
    IdeviceUtils.os(@device_id) == 'tvos' ? 'sigh_*_development_tvos' : 'sigh_*_development'
  end

  def set_match_params
    # reads this directly from env for some reason
    ENV['MATCH_PASSWORD'] = @config['fastlane_match_password']

    # script runs as root ... won't find the app users ssh keys
    ENV['GIT_SSH_COMMAND'] = "ssh -i /Users/<USER>/.ssh/id_rsa_mobilegithub -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no"
  end
end
