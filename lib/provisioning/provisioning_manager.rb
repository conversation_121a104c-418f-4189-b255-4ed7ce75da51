require 'browserstack_logger'
require 'json'
require 'time'
require 'yaml'

require_relative '../../config/constants'
require_relative '../apps/chrome'
require_relative '../apps/launcher'
require_relative '../apps/redirect'
require_relative 'fastlane_match'
require_relative 'mobileprovision_file'
require_relative 'ppuid_file'
require_relative 'provisioning_profile'
require_relative 'provisioning_server_client'

class ProvisioningManager # rubocop:todo Metrics/ClassLength
  class << self
    def run_from_bash
      udid = ARGV[0]
      command = ARGV[1]

      pm = ProvisioningManager.new(udid)
      pm.problem_detected?
    end

    # The provisioning profile config is used to record the ppuid for each branch name.
    #
    # Config structure:
    # { 'production_X_DDMMYYYY' => { 'ppuid' => '1234', ...}, 'production_Y_DDMMYYYY' => {}, ...}
    def read_profile_config
      return {} unless File.exist?(PROVISIONING_PROFILE_CONFIG)

      begin
        JSON.parse(File.read(PROVISIONING_PROFILE_CONFIG))
      rescue JSON::ParserError
        {}
      end
    end

    def update_profile_config(branch_name, profile)
      config = read_profile_config

      config[branch_name] = {
        ppuid: profile.ppuid,
        team_id: profile.team_id,
        certificate_expiry: profile.developer_certificate.not_after,
        config_update_time: Time.now.utc
      }

      File.write(PROVISIONING_PROFILE_CONFIG, config.to_json)
    end
  end

  def initialize(udid)
    @udid = udid
    @ppuid_file = PpuidFile.new(@udid)
    @device_state = DeviceState.new(@udid)
  end

  def update(device_name)
    # Throws error if the device is not yet provisioned: "Awaiting provisioning"
    branch_name = fetch_branch_name_or_request_provisioning(device_name)

    # Check that returned branch name is listed in provisioning_profile_branches.yml
    if current_branch_names.include?(branch_name)
      log("Received known branch from provisioning server: #{branch_name}")
    else
      error("Received unknown branch from provisioning server: #{branch_name}")
    end

    # TEMPORARY CODE BLOCK
    # Temp block used to avoid additional offlines during high cap migration process. See comment:
    # https://browserstack.atlassian.net/browse/MOBPL-1203?focusedCommentId=523120
    if @device_state.update_provisioning_profile_file_present? && branch_name != 'production_61_15022023'
      log(:warn, 'Not updating provisioning profile - assigned branch is not new high cap account')
      @device_state.remove_update_provisioning_profile_file
      return
    end
    # TEMPORARY CODE BLOCK

    # Check profile config for branch name
    profile_config = ProvisioningManager.read_profile_config
    branch_info = profile_config[branch_name] if profile_config.key?(branch_name)

    # If branch is in config, the provisioning profile may have been already
    # downloaded on the machine by another device.
    # Skip & force full download if rotate certificate file present (for dev use)
    if branch_info && !@device_state.rotate_certificate_file_present?
      # e.g. maybe it is an older revision that does not have the most recent devices.
      log("Branch #{branch_name} present in provisioning_profile_config.json"\
          " - checking for any problems with provisioning profile")

      new_profile = ProvisioningProfile.new(MobileprovisionFile.path(ppuid: branch_info['ppuid']))
      if valid_provisioning_profile?(profile: new_profile)
        log('No problems found - updating ppuid file')
        @ppuid_file.write(branch_name, new_profile.team_id, new_profile.ppuid)
        @device_state.remove_update_provisioning_profile_file
        return
      end
    end

    download_provisioning_profile(branch_name)
    @device_state.touch_rebuild_browserstack_app_file
    @device_state.remove_rotate_certificate_file
    @device_state.remove_update_provisioning_profile_file
  end

  def problem_detected?
    if @device_state.rotate_certificate_file_present?
      log(:warn, 'Rotate certificate file present')
      return true
    end

    if @device_state.update_provisioning_profile_file_present?
      log(:warn, 'Update provisioning profile file present')
      return true
    end

    unless @ppuid_file.valid?
      log(:warn, 'Invalid ppuid file')
      return true
    end

    unless current_branch_names.include?(@ppuid_file.branch_name)
      log(:warn, "Invalid branch name: #{@ppuid_file.branch_name}")
      return true
    end

    unless valid_provisioning_profile?
      log(:warn, 'Problem with provisioning profile')
      return true
    end

    log('No problems detected')
    false
  end

  # List of all currently active provisioning profile branch names
  def current_branch_names
    YAML.load_file("#{CONFIG_PATH}/provisioning_profile_branches.yml")
  end

  def valid_provisioning_profile?(profile: nil)
    profile = ppuid_file_profile if profile.nil?
    validate_provisioning_profile(profile)
    true
  rescue ProvisioningError, ProvisioningProfileError
    false
  end

  # Check for problems with provisioning profile
  def validate_provisioning_profile(profile)
    error('developer certificate expired') if profile.developer_certificate_expired?
    error('device not provisioned with this profile') unless profile.device_included?(@udid)

    log("Developer Cert ID: #{profile.developer_certificate_identity}")
    log("Developer Cert SHA1: #{profile.developer_certificate_sha1}")
    unless BrowserStack::OSUtils.codesigning_identity_exists?(profile.developer_certificate_identity)
      # Developer's identity should have been added to the keychain by fastlane match
      error('codesigning identity not in keychain')
    end
  end

  # Fetches the device's associated account's provisioning profile branch name.
  # If the device is not yet provisioned (registered to an account), it requests
  # the provisioning server add the device to the database, and raises an error:
  # "Awaiting provisioning"
  def fetch_branch_name_or_request_provisioning(device_name)
    client = ProvisioningServerClient.new

    begin
      response_body = client.request_device_info(@udid)
    rescue ProvisioningServerError
      client.request_add_device(@udid, device_name)
    end

    device_info = JSON.parse(response_body)

    # If device is provisioned (registered to an account), return branch_name
    # branch_name = account_name in the server db
    return device_info['account_name'] if device_info['already_provisioned']

    # Otherwise request device be provisioned. The device will be
    # provisioned the next time `run_fastlane` is called (manually by devs).
    client.request_add_device(@udid, device_name)
  end

  def download_provisioning_profile(branch_name)
    log("Downloading new provisioning profile onto machine")
    log('Running fastlane match to download new provisioning profile from '\
        'realmobile certificates repo, and add it to the keychain.')
    begin
      new_ppuid, _new_team_id = FastlaneMatch.new(@udid).run(branch_name)
    rescue => e
      log("Fastlane Match failed to download provisioning profile #{e.message} #{e.backtrace.join("\n")}")
      error("Fastlane Match failed #{e.message}")
    end

    log('Checking that new provisioning profile is ok')
    new_profile = ProvisioningProfile.new(MobileprovisionFile.path(ppuid: new_ppuid))
    validate_provisioning_profile(new_profile)

    log('Adding new profile to the provisioning profile config')
    ProvisioningManager.update_profile_config(branch_name, new_profile)

    log('Updating ppuid file with new profile')
    @ppuid_file.write(branch_name, new_profile.team_id, new_profile.ppuid)
  end

  def download_chromium_provisioning_profile(branch_name)
    log("Downloading new chromium provisioning profile onto machine")

    begin
      chromium_data = FastlaneMatch.new(@udid).run_chromium(branch_name)
      chromium_data[:branch_name] = branch_name
    rescue => e
      log("Fastlane Match failed to download provisioning profile #{e.message} #{e.backtrace.join("\n")}")
      error("Fastlane Match failed #{e.message}")
    end

    log('Updating ppuid file with new profile')
    @ppuid_file.write_chromium(chromium_data)
  end

  def ppuid_file_profile
    @ppuid_file_profile ||= ProvisioningProfile.new(
      MobileprovisionFile.path(ppuid: @ppuid_file.ppuid)
    )
  end

  # If single arg given, uses log level info.
  # log('hello') => log level info
  # log(:error, 'hello') => log level error
  def log(*args)
    level, msg = args.length == 1 ? [:info, args[0]] : [args[0], args[1]]
    BrowserStack.logger.send(level.to_sym, msg, { subcomponent: self.class.to_s })
  end

  def error(msg)
    log(:error, msg)
    raise ProvisioningError, msg
  end
end

ProvisioningManager.run_from_bash if __FILE__ == $PROGRAM_NAME
