require_relative 'ppuid_file'

require_relative '../../config/constants'

# A small convenience module for a method used in several classes
module MobileprovisionFile
  def self.path(ppuid: nil, udid: nil)
    raise "Must provide either ppuid or device udid" if ppuid.nil? && udid.nil?

    ppuid = PpuidFile.new(udid).ppuid if ppuid.nil?

    File.join(PROVISIONING_PROFILE_DIR, "#{ppuid}.mobileprovision")
  end
end
