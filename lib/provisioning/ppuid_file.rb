require_relative '../../config/constants'
require_relative '../models/device_state'
require 'json'

class PpuidFile
  attr_reader :path, :chromium_path

  def initialize(udid)
    @path = File.join(CONFIG_ROOT, "ppuid_#{udid}")
    @chromium_path = File.join(CONFIG_ROOT, "ppuid_chromium_#{udid}")
  end

  def valid?
    contents.size == 3
  rescue => e
    false
  end

  def contents
    return @contents unless @contents.nil?

    raise "File not found: #{@path}" unless File.exist?(@path)

    @contents = File.readlines(@path, chomp: true)
    raise 'Invalid ppuid file' unless @contents.size == 3

    @contents
  end

  def branch_name
    contents[0]
  end

  def team_id
    contents[1]
  end

  def ppuid
    contents[2]
  end

  def branch_name_without_date
    contents[0].rpartition("_")[0]
  end

  def chromium_ppuid
    JSON(File.read(@chromium_path))
  rescue
    {}
  end

  # Updates values for this object as well as writing file.
  def write(branch_name, team_name, ppuid)
    @contents = [branch_name, team_name, ppuid]
    File.open(@path, 'w') { |f| f.write(contents.join("\n")) }
  end

  # Updates values for chromium object as well as writing file.
  def write_chromium(chromium_data)
    File.open(@chromium_path, 'w') { |f| f.write(chromium_data.to_json) }
  end
end
