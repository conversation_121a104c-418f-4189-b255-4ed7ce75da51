require 'browserstack_logger'
require 'openssl'
require 'plist'

require_relative '../custom_exceptions'
require_relative '../models/device_state'
require_relative '../utils/osutils'

# -------------
# A provisioning profile contains the following:
#
# - Developer certificate
#   + <PERSON><PERSON><PERSON>'s identity e.g. "iPhone Developer: <PERSON> (ABCDEFG)"
#   + certificate expiry date (1 year from creation)
#
# - App bundle id
#   + We use a wildcard "*" to match multiple apps
#
# - App entitlements & capabilities
#   + wda requires the "get-task-allow" entitlement
#
# - Devices registered to account
#   + Standard developer accounts have a max of 100 iphones & 100 ipads
#   + Browserstack's high capacity account has a max of 5000 iphones & 5000 ipads
#
# Provisioning profiles are contained in .mobileprovision files:
# /Users/<USER>/Library/MobileDevice/Provisioning Profiles/<ppuid>.mobileprovision
#
# ppuid = provisioning profile unique id
#
# Provisioning profiles are also included inside user apps as "embedded.mobileprovision" files.
# -------------
#
# This class contains methods for reading the contents of a .mobileprovision file
class ProvisioningProfile

  def initialize(mobileprovision_file)
    @mobileprovision_file = mobileprovision_file
  end

  def data
    @data ||= parse_mobileprovision
  end

  # Parses the .mobileprovision file and returns the contents as a hash
  def parse_mobileprovision
    error("File not found: #{@mobileprovision_file}") unless File.exist?(@mobileprovision_file)

    cmd = "sudo security cms -D -i '#{@mobileprovision_file}'"
    profile_xml, status = BrowserStack::OSUtils.execute(cmd, true)
    error("Failed to read #{@mobileprovision_file}") unless status == 0

    Plist.parse_xml(profile_xml)
  end

  def ppuid
    data['UUID']
  end

  def entitlements
    data['Entitlements']
  end

  def entitlements_plist
    entitlements.to_plist
  end

  def team_name
    data['TeamName']
  end

  def team_id
    data['TeamIdentifier'].first
  end

  # Decodes the developer certificate contained inside the provisioning profile
  def developer_certificate
    certificate = data['DeveloperCertificates'].first.string
    error('No developer certificate found') if certificate.nil? || certificate.empty?

    OpenSSL::X509::Certificate.new(certificate)
  end

  # Name of person who owns the developer account which was used
  # to create the developer certificate in this provisioning profile.
  # This name/identity is added to the machine's keychain and can be found
  # with the command: security find-identity
  def developer_certificate_identity
    subject = developer_certificate.subject.to_utf8
    subject_hash = subject.split(",").map { |s| s.split('=') }.to_h
    subject_hash['CN']
  end

  def developer_certificate_expired?
    expiry_date = developer_certificate.not_after
    now = Time.now

    cert_expired = Time.now > expiry_date

    log(:info, "Developer certificate expiry date: #{expiry_date}; "\
               "today's date: #{now}; identity expired: #{cert_expired}")

    cert_expired
  end

  # Note, SHA1 hash is converted to uppercase since that is how it appears
  # in the output of security commands e.g. security find-identity
  def developer_certificate_sha1
    OpenSSL::Digest::SHA1.new(developer_certificate.to_der).to_s.upcase
  end

  def device_included?(udid)
    data['ProvisionedDevices'].include?(udid)
  end

  def log(level, msg)
    BrowserStack.logger.send(level.to_sym, msg, { subcomponent: self.class.to_s })
  end

  def error(msg)
    log(:error, msg)
    raise ProvisioningProfileError, msg
  end
end
