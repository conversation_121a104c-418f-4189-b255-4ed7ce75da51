require 'browserstack_logger'
require 'json'
require 'uri'

require_relative '../configuration'
require_relative '../custom_exceptions'
require_relative '../utils/http_utils'

class ProvisioningServerClient

  def initialize
    @config = BrowserStack::Configuration.conf
    @server_address = @config['provisioning_profile_service']
    @provisioning_profile_config_path = '/usr/local/.browserstack/realmobile/keys/provisioning_server/auth_config.json'
    @auth_config = begin
      JSON.parse(File.read(@provisioning_profile_config_path))
    rescue
      error("Missing Provisioning server auth config file")
    end
  end

  def request_device_info(udid)
    log(:info, "Requesting provisioning server info for #{udid}")

    uri = URI.parse("#{@server_address}/devices/#{udid}")
    basic_auth = { username: @auth_config['user'], password: @auth_config['password'] }
    response = BrowserStack::HttpUtils.get_response(uri, basic_auth)

    log(:info, "Provisioning service response #{udid}: #{response.body}")

    case response.code
    when '200'
      response.body
    when '404'
      error('Device not found')
    else
      error("Provisioning server: #{response.code}")
    end
  end

  # This method makes a request to the server to add the device to the db.
  # This method always raises an error as the device cannot be immediately registered to an account.
  # Instead it must wait to be registered next time `run_fastlane` is called (manually by devs)
  def request_add_device(udid, device_name)
    log(:warn, "Requesting provisioning server add device: #{udid}")

    device_type = case device_name
                  when /ipad/i
                    'ipad'
                  when /appletv/i
                    'appletv'
                  else
                    'iphone'
                  end

    env = @config['environment']
    env = env.match?(/stag/i) ? 'staging' : 'production'

    data = {
      'device_id' => udid,
      'env' => env,
      'device_type' => device_type
    }

    url = "#{@server_address}/add_device"
    basic_auth = { username: @auth_config['user'], password: @auth_config['password'] }
    response = BrowserStack::HttpUtils.send_post(URI.parse(url), data, basic_auth, true)

    case response.code
    when '202'
      error('Awaiting provisioning')
    when '400', '500'
      error("Can't provision: #{response.body}")
    else
      error("Provisioning server: #{response.code}")
    end
  end

  def log(level, msg)
    BrowserStack.logger.send(level.to_sym, msg, { subcomponent: self.class.to_s })
  end

  def error(msg)
    log(:error, msg)
    raise ProvisioningServerError, msg
  end
end
