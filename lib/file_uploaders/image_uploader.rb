require 'fileutils'
require 'time'
require 'browserstack_logger'

require "/usr/local/.browserstack/mobile-common/utils/screenshot_util"
require_relative '../file_processing_queue'
require_relative '../configuration'
require_relative '../utils/osutils'
require_relative '../utils/zombie'
require_relative '../utils/utils'
require_relative 'file_uploader'
require_relative '../../config/constants'
require_relative '../../lib/custom_exceptions'

class ImageUploader < FileUploader # rubocop:todo Metrics/ClassLength
  def initialize
    super(IMAGE_UPLOADER)
  end

  def image_upload(data) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
    input_file = data['file_name']
    s3_url = data['dest']
    time_in_queue = begin
      (Time.now - data['upload_request_created_at']).round(2)
    rescue
      -1
    end
    session_id = data['session_id']
    start_time = begin
      Time.parse(data['start_time'])
    rescue
      Time.now
    end
    upload_start_time = Time.now
    instrumentation_file = Utils.get_screenshot_instrumentation_file(@conf, session_id)
    key_id = data['key_id']
    secret_key = data['secret_key']
    screenshot_lock_file = data['screenshot_lock_file']
    screenshot_grace_period = 0.5
    check_black_screenshot = data['check_black_screenshot']

    if screenshot_lock_file && File.exist?(screenshot_lock_file)
      BrowserStack.logger.error("Screenshot currently in progress.")

      sleep screenshot_grace_period
      # Raising exception here so that retry is triggerred.
      raise "Screenshot currently in progress!"
    end

    unless File.readable?(input_file)
      BrowserStack.logger.error("Image at #{input_file} is not readable/non-existent time_in_queue : #{time_in_queue}.")
      # Raising NonRetryableRequestException here so that retry is NOT triggerred.
      raise NonRetryableRequestException, "Image at #{input_file} is not readable/non-existent !"
    end

    # checking if it's a black screenshot.
    is_black_screenshot = check_black_screenshot ? ScreenshotUtil.is_black_screenshot?(input_file).to_s : "skipped"

    BrowserStack.logger.info("Uploading: #{input_file} to #{s3_url}")
    ret, error = Utils.upload_file_to_s3(key_id, secret_key, "image/jpeg", input_file, "public-read", s3_url, session_id, "", nil, 10)

    if !ret && !error.empty? # || ret.match?(/something/)
      BrowserStack.logger.error("Error while uploading image to s3 url #{s3_url}  with ret #{ret} and error #{error} time_in_queue : #{time_in_queue}")
      raise "Error while uploading to S3: #{error}"
    end

    if File.exist? instrumentation_file
      total_time = (Time.now - start_time).round(2)
      time_components = {
        stage_time_taken: (Time.now - upload_start_time).round(2),
        queue_time: time_in_queue,
        total_time: total_time
      }
      stage = if total_time > SCREENSHOT_TIMEOUT_THRESHOLD
                "timeout"
              else
                "success"
              end
      Utils.update_screenshot_instrumentation_with_lock(instrumentation_file, "upload", stage, time_components, 2, is_black_screenshot)
    end

    BrowserStack.logger.info("Uploading: Response : #{ret}")
  end

  def xcuitest_screenshot_upload(data)
    BrowserStack.logger.info("Got XCUITest Screenshot upload request !!! : #{data}")

    screenshot_file = data['file_name']
    s3_params = data['s3_params']
    session_id = s3_params['session_id']
    key = s3_params['aws_key']
    secret = s3_params['aws_secret']
    bucket = s3_params['aws_bucket']
    format = data['format']

    genre = data[:genre] || data['genre']
    genre = (s3_params[:genre] || s3_params['genre']) if genre.to_s.empty?

    s3_url = s3_params['s3_url'].to_s
    time_in_queue = begin
      (Time.now - data['upload_request_created_at'])
    rescue
      -1
    end

    unless File.exist?(screenshot_file)
      BrowserStack::Zombie.push_logs("xcuitest-ss-upload-request-file-not-found", "File missing", { "session_id" => session_id, "url" => time_in_queue })
      # Raising NonRetryableRequestException here so that retry is NOT triggerred.
      raise NonRetryableRequestException, "Cannot find #{screenshot_file} on disk, Cannot upload."
    end

    region = bucket == "bs-stag" || s3_params['aws_region'] == "us-east-1" ? nil : (s3_params['aws_region']).to_s
    ret, error = Utils.upload_file_to_s3(key, secret, "image/#{format}", screenshot_file, "public-read", s3_url, session_id, genre, region, 30)

    if !ret && !error.empty?
      BrowserStack.logger.error("Error while uploading image to s3 url #{s3_url}  with ret #{ret}  error : #{error} time_in_queue : #{time_in_queue}")
      raise "Error while uploading to S3: #{error}"
    end
  end

  def process(request_file)
    data = JSON.parse(File.read(request_file))

    # adding time for upload request creation
    data['upload_request_created_at'] = begin
      File.mtime(request_file)
    rescue
      nil
    end

    # All image uploads should have upload types
    case data['upload_type']
    when "xcuitest_screenshot"
      xcuitest_screenshot_upload(data)
    when "screenshot"
      image_upload(data)
    else
      # If not valid image upload request then send data to zombie, this should not happen ever, keeping just in case someone starts adding non image files upload requests in this folder.
      BrowserStack.logger.error("File found in image folder but was not an image : data : #{data}")
      BrowserStack::Zombie.push_logs("invalid-file-upload-request", data['file_name'], { "upload_type": data['upload_type'], "url": "image" })
    end

    return if data['keep_screenshot'] # not deleting screenshots in the new flow since other request might need the image

    #cleanup, delete the file from disk (WARNING: Only do this if upload was successfull)
    begin
      File.delete(data['file_name'])
      BrowserStack.logger.info("Deleted file #{data['file_name']} ")
    rescue => e
      BrowserStack.logger.error("Exception while deleting file #{data['file_name']} reason #{e.message}")
    end
  end
end
