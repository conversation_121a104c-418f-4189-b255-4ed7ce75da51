require 'fileutils'
require 'browserstack_logger'
require_relative '../utils/zombie'
require_relative '../utils/utils'
require_relative '../configuration'

class FileUploader
  def initialize(name)
    @conf = BrowserStack::Configuration.new.all
    BrowserStack::Zombie.configure
    @name = name
    logger_params = { component: "FileUploader-#{name}" }
    log_file = @conf['logging_root'] + "/FileUploader-#{name}.log"
    BrowserStack.init_logger(log_file, logger_params)
  end

  # The below methods are used to update test level debug logs metrics for xcuitest.
  # However, this is a base class for all file uploads. Even if its called for non-xcui
  # file uploads, this method will not process it and return.
  def update_logs_stability_metrics(test_id, status, file_size, time_components, metric_type, framework) # rubocop:todo Metrics/AbcSize
    logs_stability_file_schema = Utils.logs_stability_file_schema
    return unless logs_stability_file_schema.key?(metric_type) && framework == "xcuitest"  #Avoid pushing stability metrics for appium sessions

    session_id = Utils.get_session_id_from_test_id(test_id)
    # This is same as function get_logs_stability_file_path in xctest_session.rb
    logs_stability_file = Utils.get_logs_stability_file_path(session_id)
    logs_stability_metric = logs_stability_file_schema[metric_type]

    if ["instru", "device"].include? metric_type
      test_logs_status = status == "success" && file_size > 0 ? "uploaded" : "failed"
    elsif ["network"].include? metric_type
      test_logs_status = status == "success" && file_size > 140 ? "uploaded" : "failed"
    end

    logs_stability_file_data = ""

    unless File.exists?(logs_stability_file)
      eds_data = {
        hashed_id: session_id,
        event_hash: {
          logs_stability_metric => time_components
        }
      }

      Utils.send_to_eds(eds_data, EdsConstants::APP_AUTOMATE_TEST_SESSIONS)
      return
    end

    # The following block handles metric Logs stability
    File.open(logs_stability_file, File::RDWR, 0o644) do |f|
      f.flock(File::LOCK_EX)
      logs_stability_file_data = JSON.parse(File.read(logs_stability_file))
      logs_stability_file_data[logs_stability_metric][test_logs_status] = logs_stability_file_data[logs_stability_metric][test_logs_status] + 1
      f.rewind
      f.write(logs_stability_file_data.to_json)
      f.flush
      f.truncate(f.pos)
    end
    total_tests = logs_stability_file_data["total_tests"]
    total_tests_so_far = logs_stability_file_data[logs_stability_metric]["uploaded"] + logs_stability_file_data[logs_stability_metric]["failed"]
    BrowserStack.logger.info("[#{test_id}] Updating #{logs_stability_metric} stability metrics with #{test_logs_status} of size #{file_size}, total_tests_or_session_so_far: #{total_tests_so_far}")
    Utils.push_stability_metrics(session_id, test_id, logs_stability_file_data, metric_type, total_tests, total_tests_so_far)
  rescue => e
    BrowserStack.logger.info("error in update_logs_stability_metrics message #{e.message} backtrace: #{e.backtrace.join('\n')}")
  end
end
