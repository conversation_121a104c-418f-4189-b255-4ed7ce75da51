require 'fileutils'
require 'browserstack_logger'
require_relative '../file_processing_queue'
require_relative '../configuration'
require_relative '../utils/osutils'
require_relative '../utils/zombie'
require_relative '../utils/utils'
require_relative 'file_uploader'
require_relative '../../config/constants'
require_relative '../ios_vid_capturer'
require_relative '../../server/device_manager'

class VideoUploader < FileUploader # rubocop:todo Metrics/ClassLength
  def initialize
    super(VIDEO_UPLOADER)
  end

  def video_upload(data) # rubocop:todo Metrics/AbcSize
    input_file = data['file_name']
    video_params = data['video_params']
    genre = data[:genre] || data['genre']
    genre = (video_params[:genre] || video_params['genre']) if genre.to_s.empty?
    prefix_genre = Utils.get_prefix_genre(genre)

    session_id = video_params['video_session_id']
    file_name = video_params['video_file']
    key = video_params['video_aws_keys']
    secret = video_params['video_aws_secret']
    bucket = video_params['video_aws_bucket']
    aws_region = bucket == "bs-stag" || video_params['video_aws_region'] == "us-east-1" ? "" : "-#{video_params['video_aws_region']}"
    storage_class = video_params['video_aws_storage_class'] && video_params['video_aws_storage_class'] == "STANDARD_IA" ? "STANDARD_IA" : "STANDARD"
    time_in_queue = begin
      (Time.now - data['upload_request_created_at'])
    rescue
      -1
    end

    s3_url = "https://s3#{aws_region}.amazonaws.com/#{bucket}/#{session_id}/#{file_name}.mp4"
    unless File.exist?(input_file)
      BrowserStack::Zombie.push_logs("video-upload-request-file-not-found", "File missing", { "session_id" => session_id, "url" => time_in_queue })
      raise("Cannot find #{input_file} on disc, Cannot upload.")
    end

    region = bucket == "bs-stag" || video_params['video_aws_region'] == "us-east-1" ? nil : (video_params['video_aws_region']).to_s
    updated_storage_class = Utils.s3_get_storage_class_based_on_obj_size(data['file_size'], storage_class)
    ret, error = Utils.upload_file_to_s3(key, secret, "video/mp4", input_file, "public-read", s3_url, session_id, genre, region, 300, data, {}, updated_storage_class)

    if !ret && !error.empty? # || ret.match?(/something/)
      BrowserStack::Zombie.push_logs("#{prefix_genre}ios-video-upload-fail", "Failed to upload: #{error}", { "session_id" => session_id, "url" => time_in_queue, "data" => { "size" => data['file_size'] } })
      raise "Error while uploading to S3: #{error}"
    end

    # push total time taken from the video being queued to getting uploaded, this will help us in checking whether the upcoming changes for improving this had any impact or not
    begin
      BrowserStack::Zombie.push_logs("#{prefix_genre}ios-video-upload-success", data['file_size'], { "session_id" => session_id, "url" => time_in_queue, "data" => { "size" => data['file_size'] } })
    rescue
      nil
    end
    BrowserStack.logger.info("Got video upload request !!! : #{data}")
  end

  def cleanup_video_process_files(data, process_video_in_async)
    if process_video_in_async
      File.delete(data['async_meta_data_file']) if File.exist?(data['async_meta_data_file'])
      File.delete(data['async_process_file'])
      BrowserStack.logger.info("Deleted files #{data['async_meta_data_file']}, #{data['async_process_file']} ")
    end
  end

  def process(request_file) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength, Metrics/PerceivedComplexity
    data = JSON.parse(File.read(request_file))
    video_params = data['video_params']
    genre = data[:genre] || data['genre']
    genre = (video_params[:genre] || video_params['genre']) if genre.to_s.empty?
    prefix_genre = Utils.get_prefix_genre(genre)
    session_id = video_params['video_session_id']
    device = data['device']
    actual_rec_start_time = data['actual_rec_start_time']
    initial_delay = data['initial_delay']
    process_video_in_async = begin
      File.exist?(data['async_process_file'])
    rescue
      false
    end

    if process_video_in_async && !File.exist?(data['file_name'])
      begin
        Timeout.timeout(10) do
          start_time = Time.now.to_i
          loop do
            break if File.exist?(data['async_meta_data_file'])

            BrowserStack.logger.info("#{session_id} : Waiting for #{data['async_meta_data_file']} - Elapsed wait time : #{Time.now.to_i - start_time}")
            sleep 0.5
          end
        end
      rescue => e
        BrowserStack.logger.error("Video Metadata File missing, #{e.message} #{e.backtrace}")
        raise "Video Metadata File missing"
      end

      meta_data = JSON.parse(File.read(data['async_meta_data_file']))
    end

    # adding time for upload request creation
    data['upload_request_created_at'] = begin
      File.mtime(request_file)
    rescue
      nil
    end
    if File.file?(data['file_name'])
      data['file_size'] = begin
        File.size(data['file_name'])
      rescue
        -1
      end
      data['video_duration'] = begin
        BrowserStack::OSUtils.get_video_duration(data['file_name'])
      rescue
        -1
      end
    elsif process_video_in_async && !File.exist?(meta_data['video_out_file'])
      begin
        last_video_part_start = Time.now.to_f
        capturer = IosVidCapturer.new(device, session_id, @conf, nil, genre, BrowserStack.logger, skip_device_version_check = true)
        capturer.create_video_part(meta_data['video_part_name']) if meta_data['screenshot_counter'] != 0
        last_video_part_end = Time.now.to_f
      rescue => e
        BrowserStack.logger.error("#{session_id} : Video recording failure #{e.message} #{e.backtrace}")
        BrowserStack::Zombie.push_logs("#{prefix_genre}ios-video-rec-failure", e.message, { "session_id" => session_id })
        cleanup_video_process_files(data, process_video_in_async)
        return
      else
        begin
          video_parts_stich_start = Time.now.to_f
          capturer.create_video(meta_data['video_parts_counter'], meta_data['video_out_file'])
          video_parts_stich_done = Time.now.to_f
          if !File.exist?(meta_data['video_out_file'])
            BrowserStack::Zombie.push_logs("#{prefix_genre}ios-video-rec-failure", "Video not rendered: File does not exist.", { "session_id" => session_id, "data" => { "last_video_part" => last_video_part_end - last_video_part_start, "video_concat_time" => video_parts_stich_done - video_parts_stich_start, "last_video_part_screenshots_count" => meta_data['screenshot_counter'], "video_parts_count" => meta_data['video_parts_counter'] + 1 } })
            cleanup_video_process_files(data, process_video_in_async)
            return
          else
            zombie_data = { "last_video_part" => last_video_part_end - last_video_part_start,
                            "video_concat_time" => video_parts_stich_done - video_parts_stich_start,
                            "last_video_part_screenshots_count" => meta_data['screenshot_counter'],
                            "video_parts_count" => meta_data['video_parts_counter'] + 1,
                            "actual_rec_start_time" => actual_rec_start_time, "initial_delay" => initial_delay }
            BrowserStack::Zombie.push_logs("#{prefix_genre}ios-video-rec-success", "Video render success", { "session_id" => session_id, "data" => zombie_data })
          end
        rescue => e
          BrowserStack.logger.error("#{session_id} : Error during recording. #{e.message} #{e.backtrace}")
          BrowserStack::Zombie.push_logs("#{prefix_genre}ios-video-rec-failure", "Video not rendered #{e.message}", { "session_id" => session_id })
          cleanup_video_process_files(data, process_video_in_async)
          return
        end
      end
    end

    if data['upload_type'] == "video"
      video_upload(data)
    else
      # If not valid image upload request then send data to zombie, this should not happen ever, keeping just in case someone starts adding non image files upload requests in this folder.
      BrowserStack.logger.error("File found in video folder but was not a video : data : #{data}")
      BrowserStack::Zombie.push_logs("invalid-file-upload-request", data['file_name'], { "upload_type": data['upload_type'], "url": "video" })
    end

    begin
      File.delete(data['file_name'])
      BrowserStack.logger.info("Deleted file #{data['file_name']} ")
    rescue => e
      BrowserStack.logger.error("Exception while deleting file #{data['file_name']} reason #{e.message}")
    ensure
      cleanup_video_process_files(data, process_video_in_async)
    end
  end
end
