require 'fileutils'
require 'browserstack_logger'
require_relative '../file_processing_queue'
require_relative '../configuration'
require_relative '../utils/osutils'
require_relative '../utils/zombie'
require_relative '../utils/utils'
require_relative 'file_uploader'
require_relative '../../config/constants'

class OtherFilesUploader < FileUploader # rubocop:todo Metrics/ClassLength
  def initialize
    super(OTHER_FILES_UPLOADER)
  end

  def automation_screenshot_upload(data)
    BrowserStack.logger.info("Got automation screenshot upload upload request !!! : #{data}")
    input_file = data['file_name']
    s3_params = data['s3_params']

    session_id = s3_params['session_id']
    key = s3_params['aws_key']
    secret = s3_params['aws_secret']
    bucket = s3_params['aws_bucket']
    aws_region = bucket == "bs-stag" || s3_params['aws_region'] == "us-east-1" ? "" : "-#{s3_params['aws_region']}"
    genre = data[:genre] || data['genre']
    genre = (s3_params[:genre] || s3_params['genre']) if genre.to_s.empty?

    s3_url = "https://s3#{aws_region}.amazonaws.com/#{bucket}/#{session_id}/#{session_id}-automation-screenshot.jpeg"

    unless File.exist?(input_file)
      BrowserStack::Zombie.push_logs("automation-screenshot-request-file-not-found", "File missing", { "session_id" => session_id })
      raise("Cannot find #{input_file} on disc, Cannot upload.")
    end

    region = bucket == "bs-stag" || s3_params['aws_region'] == "us-east-1" ? nil : (s3_params['aws_region']).to_s

    ret, error = Utils.upload_file_to_s3(key, secret, "image/jpeg", input_file, "public-read", s3_url, session_id, genre, region, 300)

    if !ret && !error.empty?
      BrowserStack::Zombie.push_logs("automation-screenshot-upload-fail", "Failed to upload automation screenshot: #{error}", { "session_id" => session_id, "data" => { "size" => data['file_size'] } })
      raise "Error while uploading automation screenshot to S3: #{error}"
    end
  end

  def crash_reports_upload(data)
    BrowserStack.logger.info("Got crash reports upload request !!! : #{data}")
    input_file = data['file_name']
    s3_params = data['s3_params']

    session_id = s3_params['session_id']
    key = s3_params['aws_key']
    secret = s3_params['aws_secret']
    bucket = s3_params['aws_bucket']
    aws_region = bucket == "bs-stag" || s3_params['aws_region'] == "us-east-1" ? "" : "-#{s3_params['aws_region']}"
    genre = data[:genre] || data['genre']
    genre = (s3_params[:genre] || s3_params['genre']) if genre.to_s.empty?
    aws_storage_class = s3_params['aws_storage_class'] || "STANDARD"
    metadata = s3_params['metadata']

    s3_url = "https://s3#{aws_region}.amazonaws.com/#{bucket}/#{session_id}/#{session_id}-crash_reports.zip"

    unless File.exist?(input_file)
      BrowserStack::Zombie.push_logs("crash-report-request-file-not-found", "File missing", { "session_id" => session_id })
      raise("Cannot find #{input_file} on disc, Cannot upload.")
    end

    region = bucket == "bs-stag" || s3_params['aws_region'] == "us-east-1" ? nil : (s3_params['aws_region']).to_s
    updated_storage_class = Utils.s3_get_storage_class_based_on_obj_size(data['file_size'], aws_storage_class)
    ret, error = Utils.upload_file_to_s3(key, secret, "application/zip", input_file, "public-read", s3_url, session_id, genre, region, 300, metadata, metadata, updated_storage_class)

    if !ret && !error.empty?
      BrowserStack::Zombie.push_logs("ios-crash-report-upload-fail", "Failed to upload crash reports: #{error}", { "session_id" => session_id, "data" => { "size" => data['file_size'] } })
      raise "Error while uploading crash reports to S3: #{error}"
    end
  end

  def video_map_upload(data)
    input_file = data['file_name']
    s3_params = data['video_params']

    session_id = s3_params['video_session_id']
    key = s3_params['video_aws_keys']
    secret = s3_params['video_aws_secret']
    bucket = s3_params['video_aws_bucket']
    aws_region = bucket == "bs-stag" || s3_params['video_aws_region'] == "us-east-1" ? "" : "-#{s3_params['video_aws_region']}"
    #Ignoring as map file is in bytes. Can enable if needed.
    #aws_storage_class = s3_params['aws_storage_class'] || "STANDARD"
    time_in_queue = begin
      (Time.now - data['upload_request_created_at'])
    rescue
      -1
    end

    genre = data[:genre] || data['genre']
    genre = (s3_params[:genre] || s3_params['genre']) if genre.to_s.empty?

    s3_url = "https://s3#{aws_region}.amazonaws.com/#{bucket}/#{session_id}/#{session_id}-video-mapping.json"
    unless File.exist?(input_file)
      BrowserStack::Zombie.push_logs("video-map-upload-request-file-not-found", "File missing", { "session_id" => session_id, "url" => time_in_queue })
      raise("Cannot find #{input_file} on disc, Cannot upload.")
    end

    region = bucket == "bs-stag" || s3_params['video_aws_region'] == "us-east-1" ? nil : (s3_params['video_aws_region']).to_s
    ret, error = Utils.upload_file_to_s3(key, secret, "text/json", input_file, "public-read", s3_url, session_id, genre, region, 300)

    if !ret && !error.empty?
      BrowserStack::Zombie.push_logs("ios-video-map-upload-fail", "Failed to upload video map: #{error}", { "session_id" => session_id, "url" => time_in_queue, "data" => { "size" => data['file_size'] } })
      raise "Error while uploading video time mapping to S3: #{error}"
    end

    BrowserStack.logger.info("Got video time map upload request !!! : #{data}")
  end

  def logfile_upload(data) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength, Metrics/PerceivedComplexity, Metrics/CyclomaticComplexity
    upload_started_at = Time.now
    type = data['type']
    framework_tag = data['framework'] ? "#{data['framework']}_" : ""
    BrowserStack.logger.info("Got #{type} logs upload request !!! : #{data}")
    logs_file = data['file_name']
    s3_params = data['s3_params']
    session_id = s3_params['session_id']
    key = s3_params['aws_key']
    secret = s3_params['aws_secret']
    bucket = s3_params['aws_bucket']
    aws_region = bucket == "bs-stag" || s3_params['aws_region'] == "us-east-1" ? "" : "-#{s3_params['aws_region']}"
    genre = s3_params[:genre] || s3_params['genre']
    genre = (data[:genre] || data['genre']) if genre.to_s.empty?
    aws_storage_class = s3_params['aws_storage_class'] || "STANDARD"
    prefix_genre = Utils.get_prefix_genre(genre)
    time_in_queue = begin
      (Time.now - data['upload_request_created_at'])
    rescue
      -1
    end

    zip_appium_logs_ld_flag_value = genre.to_s.downcase == 'app_automate' ? s3_params['zip_appium_logs_ios_app_aut'] : s3_params['zip_appium_logs_ios_aut']
    s3_zip_appium_logs_ios = !zip_appium_logs_ld_flag_value.nil? &&
                                  zip_appium_logs_ld_flag_value.to_s.downcase == 'true'

    url = s3_params['s3_url'].to_s
    acl = s3_params['no_acl'] ? "" : "public-read"
    content_type = s3_params['is_json'] ? "text/json" : "text/plain"
    content_type = s3_params['is_zip'] ? "application/zip" : content_type

    s3_url = url == "" ? "https://s3#{aws_region}.amazonaws.com/#{bucket}/#{session_id}/#{session_id}-#{type}-logs.txt" : url

    BrowserStack.logger.info("Upload request for logfile - prefix_genre #{prefix_genre} framework_tag #{framework_tag} type #{type} session_id #{session_id} : #{data}")
    unless File.exist?(logs_file)
      BrowserStack::Zombie.push_logs("#{type}-upload-request-file-not-found", "File missing", { "session_id" => session_id, "url" => time_in_queue })
      BrowserStack::Zombie.push_logs("#{prefix_genre}#{framework_tag}#{type}-upload-file-not-found", "File missing", { "session_id" => session_id, "url" => time_in_queue })
      BrowserStack.logger.info("#{prefix_genre}#{framework_tag}#{type}-upload-file-not-found for #{session_id} : #{data}")
      update_logs_stability_metrics(session_id, "failed", 0, { queue: time_in_queue, upload: 0 }, type, data['framework']) # Applicable only for xcuitest
      raise("Cannot find #{logs_file} on disc, Cannot upload.")
    end

    region = bucket == "bs-stag" || s3_params['aws_region'] == "us-east-1" ? nil : (s3_params['aws_region']).to_s

    updated_path_to_file, zipped_file_size_bytes, compression_time_ms = Utils.zip_logs(logs_file, data['file_size'], s3_zip_appium_logs_ios)
    updated_file_size_bytes = zipped_file_size_bytes.to_i != -1 ? zipped_file_size_bytes : data['file_size']
    updated_storage_class = Utils.s3_get_storage_class_based_on_obj_size(updated_file_size_bytes, aws_storage_class)

    automate_type = ["appium", "device"].include?(type)

    if automate_type && s3_zip_appium_logs_ios
      ret, error = Utils.upload_file_to_s3(key, secret, content_type, updated_path_to_file, acl, s3_url, session_id, genre, region, 300, {}, {}, updated_storage_class, s3_zip_appium_logs_ios, compression_time_ms, zipped_file_size_bytes, data['file_size'])
      Utils.delete_file(updated_path_to_file) if updated_path_to_file.match(/.gz/)
    else
      ret, error = Utils.upload_file_to_s3(key, secret, content_type, logs_file, acl, s3_url, session_id, genre, region, 300, {}, {}, updated_storage_class)
    end

    upload_time = begin
      (Time.now - upload_started_at)
    rescue
      -1
    end

    time_components = {
      queue: time_in_queue,
      upload: upload_time
    }

    if !ret && !error.empty?
      BrowserStack::Zombie.push_logs("#{prefix_genre}#{framework_tag}#{type}logs-upload-timeout", "Failed to upload: #{error}", { "session_id" => session_id, "url" => time_in_queue, "data" => { "size" => updated_file_size_bytes } })
      update_logs_stability_metrics(session_id, "failed", updated_file_size_bytes, time_components, type, data['framework']) # Applicable only for xcuitest
      raise "Error while uploading to S3: #{error}"
    else
      BrowserStack::Zombie.push_logs("#{prefix_genre}#{framework_tag}#{type}logs-upload-done", "success", { "session_id" => session_id, "url" => time_in_queue, "data" => { "size" => updated_file_size_bytes } })
      update_logs_stability_metrics(session_id, "success", updated_file_size_bytes, time_components, type, data['framework']) # Applicable only for xcuitest
    end
  end

# Upload Raw Logs
#  Formatted Text Logs + Screenshots S3 URLs
  def xcuitest_raw_logs_upload(data) # rubocop:todo Metrics/AbcSize
    BrowserStack.logger.info("Got XCUITest Raw Logs upload request !!! : #{data}")

    raw_logs_file = data['file_name']
    s3_params = data['s3_params']
    session_id = s3_params['session_id']
    key = s3_params['aws_key']
    secret = s3_params['aws_secret']
    bucket = s3_params['aws_bucket']
    aws_region = bucket == "bs-stag" || s3_params['aws_region'] == "us-east-1" ? "" : "-#{s3_params['aws_region']}"

    genre = data[:genre] || data['genre']
    genre = (s3_params[:genre] || s3_params['genre']) if genre.to_s.empty?

    aws_storage_class = s3_params['aws_storage_class'] || "STANDARD"

    s3_url = s3_params['s3_url'].to_s
    acl = " -H \"x-amz-acl: public-read\""
    content_type = "--contentType \"text/json\""
    time_in_queue = begin
      (Time.now - data['upload_request_created_at'])
    rescue
      -1
    end

    unless File.exist?(raw_logs_file)
      BrowserStack::Zombie.push_logs("xcuitest-raw-upload-request-file-not-found", "File missing", { "session_id" => session_id, "url" => time_in_queue })
      raise("Cannot find #{raw_logs_file} on disk, Cannot upload.")
    end

    region = bucket == "bs-stag" || s3_params['aws_region'] == "us-east-1" ? nil : (s3_params['aws_region']).to_s
    updated_storage_class = Utils.s3_get_storage_class_based_on_obj_size(data['file_size'], aws_storage_class)
    ret, error = Utils.upload_file_to_s3(key, secret, "text/json", raw_logs_file, "public-read", s3_url, session_id, genre, region, 60, {}, {}, updated_storage_class)

    raise "Error while uploading to S3: #{error}" if !ret && !error.empty?
  end

  def har_upload(data) # rubocop:todo Metrics/AbcSize
    BrowserStack.logger.info("Got HAR upload request : #{data}")
    framework_tag = data['framework'] ? "#{data['framework']}_" : ""
    input_file = data['file_name']
    session_id = data['session_id']
    s3_params = data['s3_params']
    genre = s3_params[:genre] || s3_params['genre']
    genre = (data[:genre] || data['genre']) if genre.to_s.empty?
    prefix_genre = Utils.get_prefix_genre(genre)

    key = s3_params['networklogs_aws_key']
    secret = s3_params['networklogs_aws_secret']
    bucket = s3_params['networklogs_aws_bucket']
    aws_storage_class = s3_params['networklogs_aws_storage_class'] || "STANDARD"
    time_in_queue = begin
      (Time.now - data['upload_request_created_at'])
    rescue
      -1
    end

    zip_nw_logs_ld_flag_value = genre.to_s.downcase == 'app_automate' ? data['zip_nw_logs_ios_app_aut'] : data['zip_nw_logs_ios_aut']
    s3_zip_nw_logs_ios = !zip_nw_logs_ld_flag_value.nil? &&
                          zip_nw_logs_ld_flag_value.to_s.downcase == 'true'

    aws_region = bucket == "bs-stag" || s3_params['networklogs_aws_region'] == "us-east-1" ? "" : "-#{s3_params['networklogs_aws_region']}"

    s3_url = "https://s3#{aws_region}.amazonaws.com/#{bucket}/#{session_id}/#{session_id}-har-logs.txt"

    unless File.exist?(input_file)
      BrowserStack::Zombie.push_logs("har-upload-request-file-not-found", "File missing", { "session_id" => session_id, "url" => time_in_queue })
      raise("Cannot find #{input_file} on disk, Cannot upload.")
    end

    updated_path_to_file, zipped_file_size_bytes, compression_time_ms = Utils.zip_logs(input_file, data['file_size'], s3_zip_nw_logs_ios)
    updated_file_size_bytes = zipped_file_size_bytes.to_i != -1 ? zipped_file_size_bytes : data['file_size']

    region = bucket == "bs-stag" || s3_params['networklogs_aws_region'] == "us-east-1" ? nil : (s3_params['networklogs_aws_region']).to_s
    updated_storage_class = Utils.s3_get_storage_class_based_on_obj_size(updated_file_size_bytes, aws_storage_class)
    ret, error = Utils.upload_file_to_s3(key, secret, "text/json", updated_path_to_file, "public-read", s3_url, session_id, genre, region, 300, {}, {}, updated_storage_class, s3_zip_nw_logs_ios, compression_time_ms, zipped_file_size_bytes, data['file_size'])

    Utils.delete_file(updated_path_to_file) if s3_zip_nw_logs_ios && updated_path_to_file.match(/.gz/)

    if !ret && !error.empty?
      BrowserStack::Zombie.push_logs("#{prefix_genre}#{framework_tag}networklogs-upload-timeout", "Failed to upload: #{error}", { "session_id" => session_id, "url" => time_in_queue, "data" => { "size" => updated_file_size_bytes } })
      raise "Error while uploading to S3: #{error}"
    else
      BrowserStack::Zombie.push_logs("#{prefix_genre}#{framework_tag}networklogs-upload-done", "success", { "session_id" => session_id, "url" => time_in_queue, "data" => { "size" => updated_file_size_bytes } })
    end
    puts "This har_upload is getting executed"
    BrowserStack.logger.info("Successfully uploaded #{updated_path_to_file} to #{s3_url}")
  end

  def process(request_file)
    data = JSON.parse(File.read(request_file))

    # adding time for upload request creation
    data['upload_request_created_at'] = begin
      File.mtime(request_file)
    rescue
      nil
    end
    if File.file?(data['file_name'])
      data['file_size'] = begin
        File.size(data['file_name'])
      rescue
        -1
      end
    end

    # All new uploads must have an upload_type
    case data['upload_type']
    when "xcuitest_raw_logs"
      xcuitest_raw_logs_upload(data)
    when 'har-file'
      har_upload(data)
    when 'video_map_upload'
      video_map_upload(data)
    when 'logs_upload'
      logfile_upload(data)
    when 'crash-report'
      crash_reports_upload(data)
    when 'automation_screenshot_upload'
      automation_screenshot_upload(data)
    else
      # If not valid recognized upload_type then send data to zombie, this should not happen ever, keeping just in case someone starts adding non recognized files upload requests in this folder.
      BrowserStack.logger.error("File found in other folder but was not recognized : data : #{data}")
      BrowserStack::Zombie.push_logs("invalid-file-upload-request", data['file_name'], { "upload_type": data['upload_type'], "url": "others" })
    end
    #cleanup, delete the file from disk (WARNING: Only do this if upload was successfull)
    begin
      File.delete(data['file_name'])
      BrowserStack.logger.info("Deleted file #{data['file_name']} ")
    rescue => e
      BrowserStack.logger.error("Exception while deleting file #{data['file_name']} reason #{e.message}")
    end
  end
end
