require 'fileutils'
require 'browserstack_logger'
require_relative '../file_processing_queue'
require_relative '../configuration'
require_relative '../utils/osutils'
require_relative '../utils/zombie'
require_relative '../utils/utils'
require_relative 'file_uploader'
require_relative '../../config/constants'
require_relative '../mitm_proxy'

class NetworkFilesUploader < FileUploader
  def initialize
    super(NETWORK_FILES_UPLOADER)
    IdeviceUtils.configure(@conf)
  end

  def har_upload(data) # rubocop:todo Metrics/AbcSize
    BrowserStack.logger.info("Got HAR upload request : #{data}")
    framework_tag = data['framework'] ? "#{data['framework']}_" : ""
    input_file = data['file_name']
    session_id = data['session_id']
    s3_params = data['s3_params']
    genre = s3_params[:genre] || s3_params['genre']
    genre = (data[:genre] || data['genre']) if genre.to_s.empty?
    prefix_genre = Utils.get_prefix_genre(genre)

    key = s3_params['networklogs_aws_key']
    secret = s3_params['networklogs_aws_secret']
    bucket = s3_params['networklogs_aws_bucket']
    storage_class = s3_params[:networklogs_aws_storage_class] || "STANDARD"
    time_in_queue = begin
      (Time.now - data['upload_request_created_at'])
    rescue
      -1
    end
    zip_nw_logs_ld_flag_value = genre.to_s.downcase == 'app_automate' ? data['zip_nw_logs_ios_app_aut'] : data['zip_nw_logs_ios_aut']
    s3_zip_nw_logs_ios = !zip_nw_logs_ld_flag_value.nil? &&
                                zip_nw_logs_ld_flag_value.to_s.downcase == 'true'

    aws_region = bucket == "bs-stag" || s3_params['networklogs_aws_region'] == "us-east-1" ? "" : "-#{s3_params['networklogs_aws_region']}"

    s3_url = "https://s3#{aws_region}.amazonaws.com/#{bucket}/#{session_id}/#{session_id}-har-logs.txt"

    unless File.exist?(input_file)
      BrowserStack::Zombie.push_logs("har-upload-request-file-not-found", "File missing", { "session_id" => session_id, "url" => time_in_queue })
      raise("Cannot find #{input_file} on disk, Cannot upload.")
    end

    compression_time_ms = zipped_file_size_bytes = -1
    updated_path_to_file, zipped_file_size_bytes, compression_time_ms = Utils.zip_logs(input_file, data['file_size'], s3_zip_nw_logs_ios)
    updated_file_size_bytes = zipped_file_size_bytes.to_i != -1 ? zipped_file_size_bytes : data['file_size']

    updated_storage_class = Utils.s3_get_storage_class_based_on_obj_size(updated_file_size_bytes, storage_class)
    region = bucket == "bs-stag" || s3_params['networklogs_aws_region'] == "us-east-1" ? nil : (s3_params['networklogs_aws_region']).to_s
    ret, error = Utils.upload_file_to_s3(key, secret, "text/json", updated_path_to_file, "public-read", s3_url, session_id, genre, region, 300, {}, {}, updated_storage_class, s3_zip_nw_logs_ios, compression_time_ms, zipped_file_size_bytes, data['file_size'])

    Utils.delete_file(updated_path_to_file) if s3_zip_nw_logs_ios && updated_path_to_file.match(/.gz/)

    if !ret && !error.empty?
      BrowserStack::Zombie.push_logs("#{prefix_genre}#{framework_tag}networklogs-upload-timeout", "Failed to upload: #{error}", { "session_id" => session_id, "url" => time_in_queue, "data" => { "size" => updated_file_size_bytes } })
      raise "Error while uploading to S3: #{error}"
    else
      BrowserStack::Zombie.push_logs("#{prefix_genre}#{framework_tag}networklogs-upload-done", "success", { "session_id" => session_id, "url" => time_in_queue, "data" => { "size" => updated_file_size_bytes } })
    end
    BrowserStack.logger.info("Successfully uploaded #{updated_path_to_file} to #{s3_url}")
  end

  def process(request_file)
    data = JSON.parse(File.read(request_file))

    # adding time for upload request creation
    data['upload_request_created_at'] = begin
      File.mtime(request_file)
    rescue
      nil
    end
    if File.file?(data['file_name'])
      har_file_size = begin
        File.size(data['file_name'])
      rescue
        -1
      end
      data['file_size'] = har_file_size
    else
      har_file_size = -1
    end

    # All new uploads must have an upload_type
    case data['upload_type']
    when 'har-file'
      if har_file_size > 0
        har_upload(data)
      else
        mitm = MitmProxy.new(data['device'], data, @conf)
        mitm.convert_flow_to_har
        File.delete(data['async_process_file'])
        if mitm.har_captured?
          har_upload(data)
        else
          return
        end
      end
    else
      # If not valid recognized upload_type then send data to zombie, this should not happen ever, keeping just in case someone starts adding non recognized files upload requests in this folder.
      BrowserStack.logger.error("File found in other folder but was not recognized : data : #{data}")
      BrowserStack::Zombie.push_logs("invalid-file-upload-request", data['file_name'], { "upload_type": data['upload_type'], "url": "others" })
    end
    #cleanup, delete the file from disk (WARNING: Only do this if upload was successfull)
    begin
      File.delete(data['file_name'])
      BrowserStack.logger.info("Deleted file #{data['file_name']} ")
    rescue => e
      BrowserStack.logger.error("Exception while deleting file #{data['file_name']} reason #{e.message}")
    end
  end

end
