require_relative '../lib/utils/zombie'
require 'open3'
require_relative '../config/constants'
require_relative '../lib/configuration'

SESSION_START_DIR = "#{STATE_FILES_DIR}/session_start".freeze
DL_EVENT_CAPTURE_FILE_PREFIX = "#{STATE_FILES_DIR}/dl_instrumentations/chowkidar_instrumentation_".freeze
class DeviceLoggerMetric # rubocop:todo Metrics/ClassLength

  def initialize(device_id, session_id)
    @device_id = device_id
    @session_id = session_id
    @device_logger_state_filepath = "#{STATE_FILES_DIR}/device_logger_metric_#{@device_id}"
    @last_instrumention_stats = {
      "cpu" => 0,
      "ram" => 0,
      "no_of_session" => 0,
      "total_dl_process" => 0
    }
    @chowkidar_instrumented = false
    @config = Configuration.new.all
    @isSessionSpecificDL = @config['static_conf']['session_specific_device_logger'] == "true"
    BrowserStack::Zombie.configure
  end

  def mtime_file(file)
    (File.mtime(file).to_f * 1000000).floor
  end

  def session_instrumentation_locked?(session_id, device_id)
    File.exist?("#{STATE_FILES_DIR}/dl_instrumentations/#{session_id}_#{device_id}.lock")
  end

  def lock_session_instrumentation(session_id, device_id)
    cmd = "ls #{STATE_FILES_DIR}/dl_instrumentations/ | grep -ie _#{device_id}.lock"
    previous_locks_present = system(cmd)
    if previous_locks_present
      log :info, "lock_session_instrumentation: previous locks present #{session_id} #{device_id}, removing"
      system("cd #{STATE_FILES_DIR}/dl_instrumentations/ && rm -rf *_#{device_id}.lock")
    end
    log :info, "lock_session_instrumentation: creating session_device lock for instrumentation"
    FileUtils.touch("#{STATE_FILES_DIR}/dl_instrumentations/#{session_id}_#{device_id}.lock")
  end

  def acquire_session_lock(session_id, device_id)
    if session_instrumentation_locked?(session_id, device_id)
      log :error, "acquire_session_lock: received duplicate instrumentation request for #{device_id}:#{session_id}"
      return false
    end
    lock_session_instrumentation(session_id, device_id)
    session_instrumentation_locked?(session_id, device_id)
  end

  # this will capture delay in starting device_logger session in microseconds and any missed events
  def instrument_chowkidar(retry_count)
    data = {
      delay: 0,
      missed: 0,
      session_count: 1
    }
    kind = error = ''
    max_delay = retry_count * 1000000
    log :info, "instrument_chowkidar: starting chowkidar intrumentation"

    Dir.mkdir("#{STATE_FILES_DIR}/dl_instrumentations") unless Dir.exist?("#{STATE_FILES_DIR}/dl_instrumentations")

    return unless acquire_session_lock(@session_id, @device_id)

    while retry_count > 0
      kind = "device-logger-metric-chowkidar"
      log :info, "instrument_chowkidar: Retries left #{retry_count}..."
      unless File.exist?("#{SESSION_START_DIR}/#{@device_id}")
        log :error, "instrument_chowkidar: Session start file not present exiting..."
        error = 'device-logger-metric-chowkidar-session-start-absent'
        break
      end

      if File.exist?("#{DL_EVENT_CAPTURE_FILE_PREFIX}#{@device_id}")
        session_start_mtime = mtime_file("#{SESSION_START_DIR}/#{@device_id}")
        event_capture_mtime = mtime_file("#{DL_EVENT_CAPTURE_FILE_PREFIX}#{@device_id}")
        data[:delay] = event_capture_mtime - session_start_mtime
        # data[:delay] is -ve when DL captures add event after threshold/n retires and doesn't capture any add event
        # from there on
        break if data[:delay] > 0

        kind = "device-logger-metric-chowkidar-anomaly"
        data[:delay] = max_delay
      end

      retry_count -= 1
      sleep 1
    end

    data[:missed] = 1 if retry_count == 0
    data[:session_count] = current_session_count
    log :info, "instrument_chowkidar: zombie values #{data} #{error} #{@session_id} #{@devices_id} #{kind}"

    BrowserStack::Zombie.push_logs(
      kind,
      error,
      { "session_id" => @session_id, "device" => @device_id , "data" => data }
    )

    @chowkidar_instrumented = true
  end

  def should_instrument_chowkidar
    !@session_id.nil? && !@session_id.to_s.empty? && !@chowkidar_instrumented && File.exist?("#{SESSION_START_DIR}/#{@device_id}")
  end

  def start_monitoring(params={})
    if File.exist?(@device_logger_state_filepath)
      BrowserStack.logger.info "Device logger metric monitoring already running for #{@device_id}"
      return
    end
    FileUtils.touch(@device_logger_state_filepath)
    every_n_seconds(60) do
      instrument_chowkidar(6) if should_instrument_chowkidar
      current_stats = dl_macro_stats
      if current_stats != @last_instrumention_stats
        BrowserStack.logger.info "Device logger metric #{current_stats}"
        BrowserStack::Zombie.push_logs("device-logger-metric", "cpu", { "session_id" => @session_id, "device" => @device_id , "data" => current_stats }, nil, params)
        @last_instrumention_stats = current_stats
      end
    end
  rescue => e
    BrowserStack.logger.error "Failed while starting the device logger metric monitoring "\
    "message : #{e.message} backtrace : #{e.backtrace}"
  end

  def stop_monitoring
    FileUtils.rm(@device_logger_state_filepath) if File.exist?(@device_logger_state_filepath)
  end

  private

  def dl_macro_stats
    if @isSessionSpecificDL
      dl_stats, status = Open3.capture2e(
        "ps -ef | grep [d]evice-logger | grep #{@device_id} | awk '{print $2}' | xargs ps -o %cpu,%mem -p $1 | tail -n +2"
      )
      unless status.exitstatus == 0
        BrowserStack.logger.info "Unable to get CPU and RAM stats for device-logger processes, "\
        "pid #{status.pid} exitstatus #{status.exitstatus} Error #{dl_stats}"
        return { "cpu" => 0, "ram" => 0, "no_of_session" => 0, "total_dl_process" => 0 }
      end
      cpu_total = dl_stats.split(" ")[0].to_f
      ram_total = dl_stats.split(" ")[1].to_f
      BrowserStack.logger.info "Device Logger Metrics For #{@device_id} :: CPU : #{cpu_total}, RAM : #{ram_total}"
      {
        "cpu" => cpu_total.round,
        "ram" => ram_total.round
      }
    else
      dl_stats, status = Open3.capture2e(
        "ps -ef | grep [d]evice-logger | awk '{print $2}' | xargs ps -o %cpu,%mem -p $1 | tail -n +2"
      )
      unless status.exitstatus == 0
        BrowserStack.logger.info "Unable to get CPU and RAM stats for device-logger processes, "\
        "pid #{status.pid} exitstatus #{status.exitstatus} Error #{dl_stats}"
        return { "cpu" => 0, "ram" => 0, "no_of_session" => 0, "total_dl_process" => 0 }
      end
      dl_stats = dl_stats.split("\n")
      cpu_total = 0
      ram_total = 0
      dl_stats.each do |row|
        cpu_total += row.split(" ")[0].to_f
        ram_total += row.split(" ")[1].to_f
      end
      {
        "cpu" => cpu_total.round,
        "ram" => ram_total.round,
        "no_of_session" => current_session_count,
        "total_dl_process" => dl_stats.size
      }
    end
  end

  def current_session_count
    Dir.entries(SESSION_START_DIR).count - 2
  end

  def should_stop_monitor?
    !File.exist?(@device_logger_state_filepath)
  end

  def every_n_seconds(time_interval)
    loop do
      break if should_stop_monitor?

      before = Time.now
      yield
      interval = time_interval - (Time.now - before)
      sleep(interval) if interval > 0
    end
  end

  def log(level, msg)
    BrowserStack.logger.send(level.to_sym, "DeviceLoggerMetric: #{msg}", {})
  end

end
