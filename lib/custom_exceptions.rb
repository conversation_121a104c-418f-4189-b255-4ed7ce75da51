require_relative './utils/utils'
require_relative '../config/constants'

# Send an alert with a message to the mobile team.
# The alert gets sent when the exception is initialized.
#
#   message      : The message of the alert.
#   mobile_alert : Use pushover for the alert.
#   environment  : prod or staging.
#
class MachineCheckException < StandardError
  attr_reader :reason

  def initialize(message, environment = "development", mobile_alert = false)
    @reason = message
    super(message)
  end
end

class MobileCheckException < StandardError
  attr_reader :reason

  def initialize(message)
    super(message)
    @reason = message
  end
end

class XCTestRunFileMissing < StandardError
end

class LockfileTimeoutError < StandardError
end

class KeychainAccessTimeout < StandardError
end

class MdmApiException < StandardError
end

class MdmApiFatalException < MdmApiException
end

class MdmProfileException < StandardError
end

class XcTestRunMismatch < StandardError
end

class XcodeBuildFailedException < StandardError
end

class HTTPException < StandardError
end

class ResigningException < StandardError
end

class AppInstallationException < StandardError
end

class MountError < StandardError
end

class EmptyMountDirError < StandardError
end

class CleanupError < StandardError
  def initialize(msg)
    failed_method = caller[3].match(/(?<=`)(.*?)(?=')/)
    message = "#{failed_method} - " + msg
    super(message)
  end
end

class IdeviceError < StandardError
end

class IdeviceBookmarkError < IdeviceError
  def initialize(msg)
    message = "idevicebookmark failed with #{msg}"
    super(message)
  end
end

class CleanupAutomationError < StandardError
  def initialize(msg)
    failed_method = caller[3].match(/(?<=`)(.*?)(?=')/)
    message = "#{failed_method} - " + msg
    super(message)
  end
end

class ContactsCleanUpError < StandardError
  def initialize(msg)
    message = "ContactsCleanUpError - #{msg}"
    super(message)
  end
end

class IdeviceError < StandardError
end

class AppleIDVerificationException < StandardError
  def initialize(msg="Apple ID verification required")
    super
  end
end

class AppAbsent < MobileCheckException
end

class MessageClearException < StandardError
end

class InstallAppException < StandardError
  def initialize(msg = "")
    failed_method = caller[3].match(/(?<=`)(.*?)(?=')/)
    message = "#{failed_method} - " + msg
    super(message)
  end
end

class AppStoreSigninError < InstallAppException
end

class FireCMDException < StandardError
  attr_reader :message, :type, :kind, :meta_data

  def initialize(message, type = BROWSERSTACK_ERROR_STRING, kind = nil, meta_data = {})
    super(message)
    @message = message
    @type = type.to_s.empty? ? BROWSERSTACK_ERROR_STRING : type
    @kind = kind
    @meta_data = meta_data

    APP_AUTOMATE_USER_ERRORS.each do |error_message|
      next unless message =~ /#{error_message}/

      @message = error_message
      @type = USER_ERROR_STRING
      break
    end
  end
end

class CustomContactsError < StandardError
  attr_reader :message, :type, :kind, :meta_data

  def initialize( message = "", type = BROWSERSTACK_ERROR_STRING, kind = "internal_error", meta_data = {})
    super(message)
    @message = message
    @type = type.to_s.empty? ? BROWSERSTACK_ERROR_STRING : type
    @kind = kind
    @meta_data = meta_data
  end

end

class AppSettingsError < StandardError
  attr_reader :message, :type, :kind, :meta_data

  def initialize( message = "", type = BROWSERSTACK_ERROR_STRING, kind = "internal_error", meta_data = {})
    super(message)
    @message = message
    @type = type.to_s.empty? ? BROWSERSTACK_ERROR_STRING : type
    @kind = "app_settings_#{kind}"
    @meta_data = meta_data

    @type = USER_ERROR_STRING if APP_AUTOMATE_APPSETTINGS_USER_ERROR_KINDS.include?(kind)
  end

end

class DeviceSettingsError < StandardError
  attr_reader :message, :type, :kind, :meta_data

  def initialize( message = "", type = BROWSERSTACK_ERROR_STRING, kind = "internal_error", meta_data = {})
    super(message)
    @message = message
    @type = type.to_s.empty? ? BROWSERSTACK_ERROR_STRING : type
    @kind = "device_settings_#{kind}"
    @meta_data = meta_data
  end

end

class AppiumServerError < StandardError
end

class WDALaunchError < AppiumServerError
end

class WDAInstallError < AppiumServerError
end

class VideoRecordError < StandardError
end

class OSUtilsError < StandardError
end

class RendererKeyError < StandardError
end

class AutomationError < StandardError
end

class WifiError < StandardError
end

class AppUpdateError < StandardError
end

# Raised when BrowserStack XCUITEST automation script fails
class BrowserStackTestExecutionError < StandardError
  attr_reader :test_output

  def initialize(test_class, test_function, test_output = '')
    super(build_message(test_class, test_function, test_output))
    @test_output = test_output
  end

  def build_message(test_class, test_func, output)
    underlying_error = output.scan(/Underlying Error: [^.)]*/).last
    error_message = "#{test_func} @ #{test_class} "
    error_message += output.include?('BUILD INTERRUPTED') ? 'timed out' : 'failed' # For some reason, xcodebuild doesn't exit with 124 even on timeout
    error_message += " possibly due to popup" if output.match(/Failed to background test runner/)
    error_message += " due to passcode" if output.match(/The device is passcode protected/)
    error_message += ":#{underlying_error.split(':')[1]}" unless underlying_error.nil?

    error_message
  end
end

# Raised when WDA automation fails. Since raise already sets the message hence we do not pass  it to the constructor
class WdaAutomationError < StandardError
  attr_reader :response_object

  def initialize(object)
    super(object)
    @response_object = object
  end
end

class WdaClientError < StandardError
  attr_reader :response_object

  def initialize(object)
    super(object)
    @response_object = object
  end
end

class ScriptExecutionError < StandardError
  attr_reader :response_object

  def initialize(object)
    super(object)
    @response_object = object
  end
end

class UnsupportedElementError < StandardError
  attr_reader :response_object

  def initialize(object)
    super(object)
    @response_object = object
  end
end

# Raised when BrowserStack launch app command fails
class LaunchBrowserStackError < StandardError
end

class BrowserStackInstallError < StandardError
end

class BrowserStackAppBuildError < StandardError
end

class PhotosGrantPermissionError < StandardError
end

class AppStringsCommandError < StandardError
end

class AppInstallCommandFailedException < StandardError
end

class NonRetryableRequestException < StandardError
end

class MultipleTimezonesException < StandardError
end

class NoTimezonesException < StandardError
end

class DisableAssistiveTouchException < StandardError
end

class MDMServerNotRunningException < StandardError
end

class SSLCertificateNotInstalledException < StandardError
end

class TimezoneNotSupportedException < StandardError
end

class TimezoneFailureException < StandardError
end

class MDMTimezoneFailureException < StandardError
end

class VideoFileFetchException < StandardError
end

class ProvisioningError < StandardError
end

class ProvisioningProfileError < StandardError
end

class ProvisioningServerError < StandardError
end

class FastlaneError < StandardError
end

class OfflineModeFailureException < StandardError
end

class DateTimeException < StandardError
  attr_reader :code, :status

  def initialize(message, code = nil, status = nil)
    super(message)

    @code = code
    @status = status
  end
end

class DeviceConfigException < StandardError
end

class GenericException < StandardError
  attr_reader :meta_data

  def initialize(message, meta_data = {})
    super(message)

    @meta_data = meta_data
  end
end

class AppleBusinessManagerError < StandardError
end

class VPPUtilsError < StandardError
end

class IdeviceinfoOutputFieldMissing < StandardError
end

class SimValidationError < StandardError
end