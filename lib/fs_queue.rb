require 'fileutils'

require_relative './utils/utils'
require_relative './configuration'
require 'browserstack_logger'

# Queues are polling for files in <working_directory>/<device_id>/
# TODO:
# => FSQueues are single threaded processes, can try processing each queue entry in a new thread.
module BrowserStack
  class FSQueue
    def initialize(name, working_directory, max_retries=5)
      conf = Configuration.new
      @server_config = conf.all
      logger_params = { component: "FSqueue-#{name}" }
      log_file = @server_config['logging_root'] + "/FSQueue-#{name}.log"
      BrowserStack.init_logger(log_file, logger_params)
      @name = name
      @channel = working_directory
      @max_retries = max_retries
      @retry_counter = Hash.new(0)
      puts "FSQueue initialized log file can be found at #{log_file}"
    end

    def working_directory
      @server_config[@channel]
    end

    def process_files
      Dir.foreach(working_directory) do |device_id|
        next if ['.', '..'].include?(device_id)

        dir = "#{working_directory}/#{device_id}"
        request_file = Dir.entries(dir).map do |e|
          File.join(dir, e)
        end
        request_file = request_file.select do |f|
          File.file? f
        end
        request_file = request_file.min_by do |f|
          File.mtime f
        end

        next if request_file.nil? || request_file.empty? || !File.readable?(request_file)

        begin
          yield request_file

          #cleanup
          @retry_counter.delete(request_file)
          File.delete request_file
        rescue => e
          BrowserStack.logger.error("Exception while processing:#{request_file} : #{"#{e.message}\n#{e.backtrace[0...2].join("\n")}"}")
          @retry_counter[request_file] += 1
          if @retry_counter[request_file] > @max_retries
            File.delete request_file
            BrowserStack.logger.error("Retries expired for :#{request_file}, Deleting it.")
          end
        end
      end
    end

    def run(poll_interval=0.1)
      BrowserStack.logger.info("Started FSQueue for #{@name}.")
      loop do
        process_files(&block)
        sleep poll_interval
      end
    end
  end
end
