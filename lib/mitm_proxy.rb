require 'English'
require 'dotenv'
require 'fileutils'
require_relative 'utils/utils'
require_relative 'utils/osutils'
require_relative 'utils/idevice_utils'
require_relative 'utils/time_recorder'
require_relative '../config/constants'
require_relative '../lib/helpers/camera_injection_helper'
require_relative '../lib/helpers/local_testing_chrome_extension_helper'
require 'securerandom'
require 'mitmproxybuilder'
require_relative './utils/zombie'

require 'browserstack_logger'

Dotenv.load("/usr/local/.browserstack/realmobile/.env")

MOBILE_COMMON_ROOT = "/usr/local/.browserstack/mobile-common"
DIR_HOME = "/usr/local/.browserstack/realmobile".freeze

class MitmProxy # rubocop:todo Metrics/ClassLength
  include BrowserStack::TimeRecorder

  attr_reader :dumpfile, :harfile, :proxy_log_file

  time_methods :capture_har

  time_class_methods :stop_proxy
  MITM_5_MIN_IOS_VERSION = Gem::Version.new(13.0)

  CONVERT_TIMEOUT = 200 # Time limit in seconds for flow_to_har conversion
  # Increasing the timeout to check if there is improvement in failure mobpe-1016
  # Improve this to modular method where we can add domains in the list and a
  # method will it into required regex
  IGNORE_HOSTS = "--ignore-hosts \".*\.apple\.com.*|.*updates\-http\.cdn\-apple\.com.*|.*\.icloud\.com.*|.*mobile\-.*\.browserstack\.com.*|.*mobile\-.*\.bsstag\.com.*\|.*sensormockerdata*\.browserstack\.com.*|.*\.mzstatic\.com.*\""

  def initialize(device, params, settings) # rubocop:todo Metrics/AbcSize
    @device = device
    @device_version = IdeviceUtils.device_version(@device)
    @server_config = settings
    @params = params

    proxy = mitmproxy_version
    mitmdump_5_default = BrowserStack::OSUtils.macos_version.to_i >= 11 ? '/usr/local/bin/mitmdump' : '/Users/<USER>/.local/bin/mitmdump'

    @binary_path = proxy[:bin] || mitmdump_5_default
    @config_path = proxy[:conf]
    @session_id = params['automate_session_id'] || params['session_id']
    @proxy_log_file = File.join(@server_config['logging_root'], "mitmproxy_#{@device}.log")
    @convert_log_file = File.join(@server_config['logging_root'], "mitmproxy_flow_to_har_#{@device}.log")
    @capture_network_logs = params['networkLogs'].to_s == 'true'
    @async_process_file = "/tmp/#{@device}/#{@session_id}/async_har_#{@session_id}"

    # Create workspace
    Dir.mkdir(@server_config['mitmdump_dir']) unless Dir.exist?(@server_config['mitmdump_dir'])

    @dumpfile = File.join(@server_config['mitmdump_dir'], "dumpfile_#{@session_id}")

    @logger_prefix = "NetworkLogs_#{@device} " # FIXME: change to a param for the logger
    @pidfile = "/tmp/pid_mitmproxy_#{@device}"
    @harfile = "/tmp/har_file_#{@session_id}.har"
    @customharfile = "/tmp/custom_har_file_#{@session_id}.har"
    @har_dump_script = File.join(@server_config['mobile_root'], "scripts/har_dump.py")
    @har_dump_script_v10 = File.join(@server_config['mobile_root'], "scripts/har_dump_v10.1.0.py")
    @split_flow_to_har_script = File.join(@server_config['mobile_root'], "scripts/split_flow_to_har.py")
    @split_flow_to_har_v10_script = File.join(@server_config['mobile_root'], "scripts/split_flow_to_har_v10.py")
    @app_live_har_dump_script = File.join(MOBILE_COMMON_ROOT, "app_live_har_dump/app_live_har_dump.py")
    @app_live_har_dump_script_v10 = File.join(MOBILE_COMMON_ROOT, "app_live_har_dump/v10.1.0/app_live_har_dump.py")
    @tls_passthrough_script = File.join(MOBILE_COMMON_ROOT, "aa_tls_passthrough/aa_tls_passthrough.py")
    @tls_passthrough_script_v10 = File.join(MOBILE_COMMON_ROOT, "aa_tls_passthrough/v10.1.0/aa_tls_passthrough.py")
    @injector_script = File.join(DIR_HOME, "scripts/live/injector_script.py")
    @tls_failed_error_suppress = File.join(DIR_HOME, "scripts/error_messages/tls_failed_error_suppress.py")
    @camera_injection_script = File.join(DIR_HOME, "scripts/camera_injection/camera_injection_script.py")
    @local_testing_extension_script = File.join(DIR_HOME, "scripts/local_testing_extension_script.py")
    @camera_injection_extension_script_path = File.join(DIR_HOME, "scripts/camera_injection/extension/")
    @interaction_script = File.join(DIR_HOME, "scripts/live/interaction_script.js")
    @nw_filter_regex = params["nw_filter_regex"] || ".*"
    @genre = params['genre']
    @network_logs_port = params['network_logs_port']
    @exclude_host_regex = params["exclude_host_regex"] || ""
    @include_host_regex = params["include_host_regex"] || ""
  end

  def mitmproxy_version
    return MITM_PROXIES[:MITM10] if should_use_mitmproxy_v10

    mitm5_installed_state_file = "#{@server_config['state_files_dir']}/mitm5_installed_state_file_ver#{MITM_5_CERT_VERSION}_#{@device}"
    mitmproxy_5_ver = File.file?(mitm5_installed_state_file) ? MITM_PROXIES[:mitm_5_new] : MITM_PROXIES[:MITM5]
    @device_version >= MITM_5_MIN_IOS_VERSION ? mitmproxy_5_ver : MITM_PROXIES[:MITM4]
  end

  def set_details_for_instrumentation
    BrowserStack.logger.info "IN set_details_for_instrumentation with params: #{@device} #{@genre}"

    begin
      FileUtils.rm_f("/tmp/mitm_log_file_info_#{@device}.txt")
      mitm_log_file_info = File.new("/tmp/mitm_log_file_info_#{@device}.txt", "w")
      mitm_log_file_info.puts(@genre)
      log_file_size = `stat -f "%z" #{@proxy_log_file}`
      mitm_log_file_info.puts(log_file_size)
      mitm_log_file_info.close
    rescue => e
      BrowserStack.logger.info "Something went wrong while set_details_for_instrumentation: #{e}"
    end
  end

  def boot(listen_port, upstream_port)
    cmd = get_launch_cmd(listen_port, upstream_port)
    log("Starting with #{cmd}")

    start_banner = "\n\n#{'*' * 20} #{Time.now.utc} Starting new execution for session_id: #{@session_id} #{'*' * 20}\n\n"
    File.write(@proxy_log_file, start_banner, mode: 'a')

    pid = fork do
      # TODO: figure out how to capture exit code. perhaps we can write wrapper bash script to launch mitmproxy
      exec(cmd)
      # Do not write any code after `exec` it won't run
    end
    File.open(@pidfile, 'w') { |f| f.write(pid) } # Store pid to kill the process later

    # Wait for MitmProxy to start.
    mitm_validation_expected_response = should_use_mitmproxy_v10 ? "Invalid header" : "Bad Request"
    tries = 0
    while tries < 30
      break if `curl localhost:#{listen_port}`.match(mitm_validation_expected_response)

      sleep 1
      tries += 1
    end
    set_details_for_instrumentation

    unless `curl localhost:#{listen_port}`.match(mitm_validation_expected_response)
      error = MitmProxy.running?(@device) ? "MITM not listening" : "MITM not started"
      BrowserStack::Zombie.push_logs("mitm-start-error", "#{@params[:genre]} - #{error}" , { "session_id" => @session_id, "device" => @device })
      raise "Failed to launch mitmproxy on port #{listen_port} #{error}"
    end
  end

  def self.running?(device_id)
    BrowserStack::OSUtils.is_process_running?("mitmproxy", device_id)
  end

  # Waits for sometime to ensure
  # process is killed.
  def self.kill_proxy(device_id, signal="TERM")
    BrowserStack::OSUtils.kill_process("mitmproxy", device_id, signal)
    sleep 0.5
  end

  def self.stop_proxy(device_id, session_id=nil, genre=nil)
    return unless running?(device_id)

    kill_proxy(device_id)

    try = 0
    while running?(device_id) && try < 5
      BrowserStack::Zombie.push_logs("mitm-process-found-running", genre.to_s, { "device" => device_id, "session_id" => session_id }) if try == 0
      kill_proxy(device_id)
      try += 1
    end

    kill_proxy(device_id, "KILL") if running?(device_id)

    if running?(device_id)
      BrowserStack::Zombie.push_logs("mitm-process-still-running", genre.to_s, { "device" => device_id, "session_id" => session_id })
      BrowserStack.logger.info("FATAL: Failed to kill mitmproxy!")
    else
      BrowserStack.logger.info("Successfully killed mitmproxy")
    end
    send_script_injection_status(device_id, session_id)
  end

  def stop_proxy(current_device_config)
    if @capture_network_logs
      listen_port = PrivoxyManager.listen_port(current_device_config)
      #checks if mitm was not started or killed process in middle of session, push to platform_stats and return
      if !self.class.running?(@device)
        BrowserStack::Zombie.push_logs("mitm-already-killed-or-not-started", @genre.to_s, { "session_id" => @session_id, "device" => @device , "os_version" => @device_version })
        return
        # checks if mitm process is hanged, push to platform_stats
      elsif mitm_process_hanged?(listen_port)
        BrowserStack::Zombie.push_logs("mitm-process-hanged", @genre.to_s, { "session_id" => @session_id, "device" => @device , "os_version" => @device_version })
      end
    end

    self.class.stop_proxy(@device, @session_id)
  end

  def mitm_process_hanged?(listen_port)
    log("mitm listen port: #{listen_port}")

    if listen_port.nil?
      log("mitm listen port is nil")
      return true
    end

    `gtimeout 1 curl localhost:#{listen_port}`.match(/Bad Request/) ? false : true
  end

  def push_mitm_error(stderr)
    log("Checking to push mitm error")
    if stderr.match(/Segmentation fault/)
      log("Segmentation fault occurred")
      BrowserStack::Zombie.push_logs("mitm-force-exit", "Segmentation fault - #{stderr}" , { "session_id" => @session_id, "device" => @device })
    end
  end

  def capture_har
    unless flow_captured?
      log("Cannot find #{@dumpfile} file for session #{@session_id}")
      return
    end

    convert_flow_to_har unless process_network_logs_in_async?
    create_upload_request
  end

  def capture_har_for_framework
    convert_flow_to_har
    reset_flow_file
    @harfile
  end

  # Params Definition
  # input_file: The flow_file which contains network logs.
  # split_details_file: JSON file containing test details summary. Only for XCTest NwLogs Splitting
  def convert_flow_to_har(input_file = @dumpfile, split_details_file = nil)
    unless File.exist?(@har_dump_script) || File.exist?(@har_dump_script_v10)
      log("FATAL: Cannot find har_dump.py to convert flow to HAR file")
      return
    end

    if process_network_logs_in_async?
      File.rename(input_file, "#{input_file}_async_network_flow_flag")
      log("Renamed #{input_file} to #{"#{input_file}_async_network_flow_flag"}")
      input_file += "_async_network_flow_flag"
    end

    flow_to_har_start = Time.now.to_f

    cmd = if split_details_file
            get_cmd_for_split_har(input_file, split_details_file)
          else
            get_convert_cmd(input_file)
          end

    flow_to_har_end = Time.now.to_f
    log("Executing: #{cmd}... Please wait...")
    BrowserStack::OSUtils.execute(cmd)
    exit_state = $CHILD_STATUS

    if exit_state.exitstatus != 0
      log("FATAL: Failed to convert flow to har.. #{exit_state}")
      BrowserStack::Zombie.push_logs("convert-flow-to-har-error", exit_state.to_s, { "session_id" => @session_id, "device" => @device, "data" => { "convert_time" => flow_to_har_end - flow_to_har_start } })
    else
      log("Conversion success!")
      BrowserStack::Zombie.push_logs("convert-flow-to-har-success", "flow_to_har_success", { "session_id" => @session_id, "device" => @device, "data" => { "convert_time" => flow_to_har_end - flow_to_har_start } })
    end
    log("Deleting: #{input_file}")
    FileUtils.rm_f(input_file) unless split_details_file # Flow file deletion is managed by xctest_network_log_split
  end

  def har_captured?
    File.exist?(@harfile) && File.size(@harfile) > 0
  end

  def self.send_script_injection_status(device, session_id)
    BrowserStack.logger.info "IN send_script_injection_status with params: #{session_id} #{device}"

    begin
      if File.exist?("/tmp/mitm_log_file_info_#{device}.txt")
        mitmdump_log_file_details = File.read("/tmp/mitm_log_file_info_#{device}.txt")
        BrowserStack.logger.info "/tmp/mitm_log_file_info_#{device}.txt file found with #{mitmdump_log_file_details}"
        genre = mitmdump_log_file_details.split(' ')[0]
        file_size = mitmdump_log_file_details.split(' ')[1].to_i

        if genre == "live_testing"
          fail_cnt = `grep "\\[injector_script\\] failed.*#{session_id}" /var/log/browserstack/mitmproxy_#{device}.log | wc -l`.to_i
          success_cnt = `grep "\\[injector_script\\] successful.*#{session_id}" /var/log/browserstack/mitmproxy_#{device}.log | wc -l`.to_i
          event_name = "web_events"
          data_to_push = {
            event_name: "mitm_injection_status",
            genre: LIVE_TESTING,
            product: LIVE,
            team: LIVE,
            device_id: device,
            success_count: success_cnt,
            failure_count: fail_cnt
          }
          event_json = {
            session_id: session_id
          }
          data_to_push.merge!({ event_json: event_json })
          Utils.send_to_eds(data_to_push, event_name, true)
        end
        FileUtils.rm_f("/tmp/mitm_log_file_info_#{device}.txt")
      else
        BrowserStack.logger.info "/tmp/mitm_log_file_info_#{device}.txt file not found at MITM stop"
      end
    rescue => e
      BrowserStack.logger.info "Something went wrong while sending mitm_injection_status to zombies: #{e}"
    end
  end

  private

  def should_use_mitmproxy_v10
    @params["applive_should_use_upgraded_mitmproxy_v10"].to_s == "true" ||
      @params["local_testing_chrome_extension_enabled"].to_s == "true" ||
      @params['use_mitmproxy_v10'].to_s == "true" ||
      @device_version >= Gem::Version.new(18.2)
  end

  def reset_flow_file
    FileUtils.rm_rf @dumpfile
  end

  # rubocop:disable Metrics/AbcSize
  # rubocop:disable Metrics/MethodLength
  # rubocop:disable Metrics/PerceivedComplexity
  # rubocop:disable Metrics/CyclomaticComplexity
  def get_launch_cmd(listen_port, upstream_port)
    mitm = MITMProxyBuilder::MITMDump.new(@binary_path)
    mitm.arg(IGNORE_HOSTS)
    mitm.set 'block_global', false
    mitm.ssl_insecure
    mitm.listen_port listen_port
    mitm.set 'confdir', @config_path

    local_testing_chrome_extension_enabled = true if @params["local_testing_chrome_extension_enabled"].to_s == "true"
    mitm.mode 'upstream', should_use_mitmproxy_v10 ? "http://localhost:#{upstream_port}" : "https://localhost:#{upstream_port}"
    custom_headers = @params["custom_headers"]
    custom_headers = JSON.parse(custom_headers) if custom_headers && !custom_headers.empty?
    camera_injection = true if @params["cameraInjection"].to_s == "true"
    camera_url = @params["cameraInjectionUrl"]

    if @genre == "app_live_testing"
      mitm.set 'device', @device
      mitm.set 'port', @network_logs_port
      mitm.set 'hardump', @harfile
      mitm.set 'bs_allow_hosts', "'#{@nw_filter_regex}'"

      #This wont work with custom headers
      if should_use_mitmproxy_v10
        mitm.scripts @app_live_har_dump_script_v10 unless custom_headers && !custom_headers.empty?
      else
        mitm.scripts @app_live_har_dump_script unless custom_headers && !custom_headers.empty?
      end
    elsif  ((
      @params["exclude_host_regex"] || @params["include_host_regex"]
    ) && @capture_network_logs) || @params["proxy_exclude_hosts"]
      mitm.set 'bs_allow_hosts', "'#{@include_host_regex}'"
      @exclude_host_regex = @params["proxy_exclude_hosts"] || @params["exclude_host_regex"] || ""
      mitm.ignore_hosts("'#{@exclude_host_regex}'") if @params["proxy_exclude_hosts"] || @params["exclude_host_regex"]
      #This wont work with custom headers
      if should_use_mitmproxy_v10
        mitm.scripts @tls_passthrough_script_v10 unless custom_headers && !custom_headers.empty?
      else
        mitm.scripts @tls_passthrough_script unless custom_headers && !custom_headers.empty?
      end
    end

    mitm.scripts @tls_failed_error_suppress if should_use_mitmproxy_v10

    if camera_injection
      camera_injection_helper = CameraInjectionHelper.new(@device, @session_id, @product)
      camera_injection_helper.update_camera_url(camera_url)
      mitm.scripts @camera_injection_script
    end

    if local_testing_chrome_extension_enabled
      mitm.set 'privoxy_port', upstream_port
      mitm.set 'connection_strategy', "lazy"
      mitm.set 'upstream_cert', false
      mitm.set 'request_stats_output_file', LocalTestingChromeExtension.get_state_file_path(@device)
      mitm.scripts @local_testing_extension_script
    end

    #Custom Header Injection
    arg = %w[/Users/<USER>/.local/bin/mitmdump /usr/local/bin/mitmdump].include?(@binary_path) ? '--setheader' : '--modify-header'
    custom_headers&.each { |k, v| mitm.arg(arg, "/~q/#{k}/\"#{v}\"") }
    mitm.save_stream_file @dumpfile if @capture_network_logs
    if @params["interaction_sync"]
      download_interaction_script
      mitm.set 'interaction_sync_host', "'#{@params['interaction_sync_host']}'"
      mitm.set 'user_id', "'#{@params['interaction_user_id']}'"
      mitm.set 'tab_id', "'#{@params['interaction_tab_id']}'"
      mitm.set 'session_id', "'#{@session_id}'"
      mitm.set 'device_id', "'#{@device}'"
      mitm.set 'override_csp', "'#{@params['override_csp']}'"
      mitm.set 'interaction_script', "'#{@interaction_script}'"
      mitm.scripts @injector_script
    end
    mitm.cmd + " >> #{@proxy_log_file} 2>&1"
  end

  # rubocop:enable Metrics/AbcSize
  # rubocop:enable Metrics/MethodLength
  # rubocop:enable Metrics/PerceivedComplexity
  # rubocop:enable Metrics/CyclomaticComplexity

  def download_interaction_script
    log("Downloading interaction script from #{@params['interaction_script_url']}")
    curl = HttpUtils.download(@params['interaction_script_url'], @interaction_script, { retry_count: 3, timeout: 30 })
    log("Download successfull for interaction script")
  end

  def get_convert_cmd(input_file)

    network_logs_capture_content = @params[:networkLogsCaptureContent]
    network_logs_capture_content = @params['networkLogsCaptureContent'] if network_logs_capture_content.nil?
    # Capture content flag is set as false by default and would be set as true
    # only if network_logs_capture_content explicitly sent as true
    capture_content_flag = !network_logs_capture_content.nil? && network_logs_capture_content.to_s.downcase == 'true'

    har_dump_script = should_use_mitmproxy_v10 ? @har_dump_script_v10 : @har_dump_script

    if should_use_mitmproxy_v10
      "gtimeout #{CONVERT_TIMEOUT} "\
      "#{@binary_path} -n -r #{input_file} -s \"#{har_dump_script}\" "\
      "--set bs_hardump=\"#{@harfile}\" "\
      "--set captureContent=#{capture_content_flag} "\
      " >> #{@convert_log_file} 2>&1"
    else
      "gtimeout #{CONVERT_TIMEOUT} "\
      "#{@binary_path} -n -r #{input_file} -s \"#{har_dump_script}\" "\
      "--set hardump=\"#{@harfile}\" "\
      "--set captureContent=#{capture_content_flag} "\
      " >> #{@convert_log_file} 2>&1"
    end
  end

  def get_cmd_for_split_har(input_file, test_details_file)

    network_logs_capture_content = @params[:networkLogsCaptureContent]
    network_logs_capture_content = @params['networkLogsCaptureContent'] if network_logs_capture_content.nil?
    # Capture content flag is set as false by default and would be set as true
    # only if network_logs_capture_content explicitly sent as true
    capture_content_flag = !network_logs_capture_content.nil? && network_logs_capture_content.to_s.downcase == 'true'
    har_dump_script = should_use_mitmproxy_v10 ? @split_flow_to_har_v10_script : @split_flow_to_har_script

    command = "gtimeout #{CONVERT_TIMEOUT} "\
    "#{@binary_path} -n -r #{input_file} -s \"#{har_dump_script}\" "\
    "--set captureContent=#{capture_content_flag} "\
    "--set testDetails=\"#{test_details_file}\" "

    # MITM v10 do not let control the har file dumping
    # By default they write the har file in prettified json format which breaks the xctest logsplitting logic for nw logs
    # We have introduced a custom har file which will be written by our custom python script in encoded format.
    if should_use_mitmproxy_v10
      command += "--set customHardump=\"#{@customharfile}\" "
      @harfile = @customharfile
    else
      command += "--set hardump=\"#{@harfile}\" "
    end

    command += " >> #{@convert_log_file} 2>&1"

    command
  end

  def flow_captured?
    return false unless File.exist?(@dumpfile)

    if File.size(@dumpfile) > 0
      log("#{@dumpfile} file for session #{@session_id} has logs")
    else
      log("#{@dumpfile} file for session #{@session_id} is empty")
    end

    true
  end

  def process_network_logs_in_async?
    File.exists?(@async_process_file)
  end

  def create_upload_request
    if !process_network_logs_in_async? && !har_captured?
      log("FATAL: Cannot find #{@harfile}")
      return
    end

    if !process_network_logs_in_async?
      uploader_request_file = @server_config["other_files_to_upload_dir"] + "/har_file_#{SecureRandom.uuid}.json"
      json_data = {
        upload_type: "har-file",
        file_name: @harfile,
        s3_params: @params.select { |key, _value| key.include?('aws') },
        session_id: @session_id,
        zip_nw_logs_ios_aut: @params["zip_nw_logs_ios_aut"],
        zip_appium_logs_ios_aut: @params["zip_appium_logs_ios_aut"],
        zip_nw_logs_ios_app_aut: @params["zip_nw_logs_ios_app_aut"],
        zip_appium_logs_ios_app_aut: @params["zip_appium_logs_ios_app_aut"],
        genre: @params[:genre] || @params['genre']
      }
    else
      uploader_request_file = @server_config["network_files_to_upload_dir"] + "/har_file_#{SecureRandom.uuid}.json"
      json_data = {
        upload_type: "har-file",
        file_name: @harfile,
        s3_params: @params.select { |key, _value| key.include?('aws') },
        session_id: @session_id,
        genre: @params[:genre] || @params['genre'],
        device: @device,
        networkLogs: "true",
        network_logs_port: @network_logs_port,
        async_process_file: @async_process_file,
        zip_nw_logs_ios_aut: @params["zip_nw_logs_ios_aut"],
        zip_appium_logs_ios_aut: @params["zip_appium_logs_ios_aut"],
        zip_nw_logs_ios_app_aut: @params["zip_nw_logs_ios_app_aut"],
        zip_appium_logs_ios_app_aut: @params["zip_appium_logs_ios_app_aut"]
      }
    end

    dir_name = File.dirname(uploader_request_file)
    FileUtils.mkdir_p(dir_name) unless Dir.exist? dir_name

    Utils.write_to_file(uploader_request_file, json_data.to_json)
    log("HAR upload request created as #{uploader_request_file}, data : #{json_data}")
  end

  def log(data)
    BrowserStack.logger.info(@logger_prefix + data)
  end
end
