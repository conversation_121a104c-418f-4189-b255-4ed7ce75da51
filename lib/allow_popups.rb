require_relative 'utils/utils'
require_relative './helpers/automation'

class AllowPopups
  class << self
    def do(device_id, device_version, product = nil)

      driver = Gem::Version.new(device_version) >= Gem::Version.new('18') ? Automation.open_safari_settings_for_ios18(device_id, device_version) : Automation.open_safari_settings(device_id, device_version)

      button_name = 'Block Pop-ups'

      if Gem::Version.new(device_version) >= Gem::Version.new('13')
        block_popup_switch = Automation.manual_scroll_to_element(driver, button_name)
      else
        driver.execute_script 'mobile: scroll', name: button_name
        block_popup_switch = driver.find_elements(:accessibility_id, "Block Pop-ups")[-1]
      end

      Automation.click_with_scroll(driver, block_popup_switch)
      success = block_popup_switch.attribute("value") == "0"
      driver.driver_quit unless product.to_s.eql?('app_automate') # This also closes the iproxy connection. Moving forward in firecmd flow, while updating app settings this iproxy connection is required

      success
    rescue => e
      BrowserStack.logger.info "Failed To Enable Popups. #{e.message}"
      false

    end
  end
end
