require 'fileutils'
require 'browserstack_logger'
require_relative '../wrappers/cfg_util'
require_relative '../utils/http_utils'
require_relative '../models/model_state'
require_relative '../utils/osutils'
require_relative '../utils/ios_mdm_service_client'
require_relative '../helpers/browserstack_app_helper'

class UpdateManager
  class UpdateRetrievalError < StandardError; end

  class UpdateDeviceError < StandardError; end

  class IncompatibleMachine < StandardError; end

  BASE_IPSW_URL = "http://ax.phobos.apple.com.edgesuite.net/WebObjects/MZStore.woa/wa/com.apple.jingle.appserver.client.MZITunesClientCheck/version".freeze
  DC_FILE_SERVER = "http://dc-file-server.service.prod.mobile.browserstack.com:3131".freeze

  def initialize(device_uuid, device_model)
    @device = device_uuid
    @model = device_model
    @cfgutil = CFGUtil.new(udid: device_uuid)
    @model_state = ModelState.new(device_model, device_uuid)

    log_file = @model_state.send(:update_manager_log_file)
    logger_params = { device: @device, component: 'device-update' }
    BrowserStack.init_logger(log_file, logger_params)
  end

  def start_update
    @model_state.touch_undergoing_update_file
    begin
      pre_update_checks
      if @model_state.downloading_update_file_present?
        BrowserStack.logger.info("Another download in progress. Waiting to update #{@device}")
        10.times do |_|
          sleep 60
          break unless @model_state.downloading_update_file_present?
        end
      elsif !File.exist?(local_path)
        BrowserStack.logger.info("Fetching update file for #{@model}")
        retrieve_update_file
      end
      update_device
      # Device gets stuck with reason device thread crashed after update. Adding this line to avoid it
      BrowserStack::OSUtils.execute("bash /usr/local/.browserstack/bshelper.sh removedevice #{@device}")
    rescue UpdateManager::IncompatibleMachine => e
      @model_state.touch_incompatible_machine_for_update_file
      BrowserStack.logger.error("Incompatible machine. Not updating #{@device} Reason - #{e.message}")
    rescue
      @model_state.touch_update_failed_file
      BrowserStack.logger.error("Error in /update #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
      e.message
    ensure
      @model_state.remove_undergoing_update_file
    end
  end

  def pre_update_checks
    raise UpdateManager::IncompatibleMachine unless @cfgutil.installed? && (BrowserStack::OSUtils.execute("idevice_id -l | grep #{@device}").strip == @device.to_s)

    BrowserStack::IosMdmServiceClient.configure
    BrowserStack::IosMdmServiceClient.get_installed_profiles(@device)
  rescue MdmApiException => e
    BrowserStack.logger.error "Fetch profiles timeout. Attempting to re-mdm ... #{e.message}"
    BrowserStackAppHelper.check_and_build_and_install_browserstack_app(@device)
    BrowserStackAppHelper.check_and_install_browserstack_test_suite(@device)
    BrowserStack::IosMdmServiceClient.run_mdm_ui_automation(@device)
  rescue => e
    BrowserStack.logger.error "Unable to re-mdm. Not performing update ... #{e.message}"
    raise UpdateManager::IncompatibleMachine, e.message
  end

  def update_device
    BrowserStack.logger.info("Starting update for #{@model}")
    @cfgutil.update_device(local_path)
  rescue => e
    raise UpdateDeviceError, e.message
  end

  def resolve_ip(hostname)
    response = BrowserStack::HttpUtils.make_get_request_with_headers("https://cloudflare-dns.com/dns-query?name=#{hostname}", { "accept": "application/dns-json" })
    JSON.parse(response.env.response.env.response_body)["Answer"].each do |item|
      return item["data"] if item["data"] =~ /^((?:(?:^|\.)(?:\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])){4})$/
    end
  end

  def find_ipsw_url
    response = BrowserStack::HttpUtils.get_response(BASE_IPSW_URL)
    ipsw = response.body.scan(/.*#{@model}.*ipsw.*/)
    ipsw.map! do |i|
      i.gsub('<string>', '').gsub('</string>', '').strip
    end
    ipsw[0] unless ipsw.empty?
  end

  def retrieve_update_file
    # Download the latest signed IPSW
    @model_state.touch_downloading_update_file
    url = "#{DC_FILE_SERVER}/#{@model}.ipsw"
    BrowserStack::HttpUtils.download(url, local_path, { timeout: 600 })
  rescue HTTPException => e
    BrowserStack.logger.info("File not present in NAS, Downloading from Apple URL: #{DC_FILE_SERVER}/#{@model}.ipsw")
    url = find_ipsw_url
    hostname = URI(url).host
    BrowserStack::HttpUtils.download(url, local_path, { timeout: 600, resolved_address: "#{hostname}:443:#{resolve_ip(hostname)}" })
  rescue => e
    cleanup
    raise UpdateRetrievalError, e.message
  ensure
    @model_state.remove_downloading_update_file
  end

  def cleanup
    FileUtils.rm_f(local_path)
  end

  def local_path
    "/tmp/#{@model}_update.ipsw"
  end
end
