require_relative '../config/constants'
require_relative 'utils/osutils'

require 'browserstack_logger'

# Wrapper class for ifuse
class Ifuse

  def initialize(udid)
    @udid = udid
  end

  def run(command = nil)
    mount_filesystem
    output = nil
    output = BrowserStack::OSUtils.execute(command) if command
    output = yield if block_given?
    output
  ensure
    unmount_filesystem
  end

  def mount_point
    "/Users/<USER>/#{@udid}"
  end

  def mount_app_point(bundle_id)
    "/Users/<USER>/#{bundle_id}_#{@udid}"
  end

  def mount_root_directory_of_app(bundle_id)
    app_mount_point = mount_app_point(bundle_id)

    FileUtils.mkdir_p(app_mount_point) unless Dir.exist?(app_mount_point)
    cmd = "#{IFUSE} -o allow_other,umask=0000,default_permissions -u #{@udid} --container #{bundle_id} #{app_mount_point}"
    msg, error_msg, status = OSUtils.execute2(cmd)
    if !error_msg.empty? && error_msg.include?('is itself on a macFUSE volume')
      BrowserStack.logger.error "ifuse failed, error: #{error_msg}"
      BrowserStack.logger.info "umounting #{app_mount_point}"
      OSUtils.execute("sudo umount -f #{app_mount_point}")
      msg, error_msg, status = OSUtils.execute2(cmd)
    end
    BrowserStack.logger.error "ifuse failed, error: #{error_msg}, msg: #{msg}" unless error_msg.empty?
    raise "ifuse failed" unless error_msg.empty?
  end

  def unmount_root_directory_of_app(bundle_id)
    app_mount_point = mount_app_point(bundle_id)
    retry_num = 0
    begin
      OSUtils.execute "sudo umount -f #{app_mount_point}"
      FileUtils.remove_dir app_mount_point if File.exists?(app_mount_point)
    rescue Errno::EBUSY
      BrowserStack.logger.error "Got resource busy on removing app_mount_point."
      if retry_num < 2
        BrowserStack.logger.info "Retrying for #{retry_num + 1} times to unmount"
        retry_num += 1
        sleep 0.1
        retry
      else
        raise Errno::EBUSY, app_mount_point
      end
    end
  end

  private

  def mount_filesystem
    BrowserStack::OSUtils.execute("sudo su -l #{USER} -c 'mkdir -p #{mount_point}'")
    BrowserStack::OSUtils.execute("#{IFUSE} -o allow_other,umask=0000,default_permissions -u #{@udid} #{mount_point}")
    sanity_check_filesystem # we could consider raising errors here. It just logs now.
  end

  def unmount_filesystem
    status = 0
    5.times do
      _, status = BrowserStack::OSUtils.execute("sudo umount -f #{mount_point}", true)
      break if status == 0

      sleep 1
    end

    if status != 0
      # raise error: fail cleanup
      raise "umount command failed, non-zero return status"
    end

    FileUtils.remove_dir mount_point if File.exist?(mount_point)
  end

  def sanity_check_filesystem
    BrowserStack.logger.info "Running filesystem sanity checks!"

    fs_contents = Dir.glob("#{mount_point}/*") # for debug.
    BrowserStack.logger.info "Filesystem mounted. Contents: #{fs_contents.inspect}"

    # We could consider raising an exception even
    BrowserStack.logger.warn "Filesystem has no contents!" if fs_contents.empty?

    FileUtils.touch "#{mount_point}/test_file"
    touchable = File.exist? "#{mount_point}/test_file"
    BrowserStack.logger.info "Can we create files? #{touchable}"

    File.write "#{mount_point}/test_file", "hello there"
    contents = File.read "#{mount_point}/test_file"
    can_rw = (contents == 'hello there')
    BrowserStack.logger.info "Can we r/w files? #{can_rw}"

    FileUtils.rm "#{mount_point}/test_file"
    existing = File.exist? "#{mount_point}/test_file"
    BrowserStack.logger.info "Can we delete files? #{!existing}"
  end
end
