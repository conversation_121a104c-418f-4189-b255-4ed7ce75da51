# Uses idevicescreenshot to capture device screenshots and joins them to create a video.
# Screenshots are uncompressed tiff files, therefore a video is created every X number of frames to save disk space.
# When the recording stops, these video snippets are joined to get a single video file
# TODO: recording should not happen in case of start errors.
require 'securerandom'
require_relative '../config/constants'
require_relative 'overridden/thread'
require_relative '../server/device_manager'
require_relative 'utils/idevice_utils'
require_relative 'utils/osutils'
require_relative 'utils/screenshot_util'

class IosVidCapturer # rubocop:todo Metrics/ClassLength
  attr_reader :workspace

  def initialize(device_id, session_id, server_config, orientation = nil, genre = "", logger = nil, skip_device_version_check = false)
    if !skip_device_version_check && DeviceManager.all_devices[device_id]['device_version'].start_with?('12.0')
      @skip_recording = true
      return
    end

    @logger = logger
    @session_id = session_id
    @server_config = server_config
    @device_id = device_id
    @orientation = orientation # portrait, landscape
    @genre = genre
    prefix_genre = Utils.get_prefix_genre(@genre)

    unless server_config['video_recording_workspace'] && server_config['video_recording_part_size'] && server_config['video_recording_fps']
      BrowserStack::Zombie.push_logs("#{prefix_genre}ios-video-rec-failure", 'server config invalid', { "session_id" => @session_id })
      raise('server_config not set for video recording')
    end
    @device_workspace = "#{server_config['video_recording_workspace']}#{device_id}/"
    @workspace = "#{@device_workspace}#{session_id}/"
    @async_meta_data_file = "#{@workspace}video_process_params.json"
    @async_process_file = @workspace + "async_#{session_id}"
    @part_size = server_config['video_recording_part_size'] # Create a video snippet from every <part_size> screenshots.
    @fps = server_config['video_recording_fps']
    @ffmpeg_processes_semaphore = server_config['video_max_ffmpeg_processes'] || 1 # Semaphore to limit the number of ffmpeg processes running concurrently.

    @video_parts_prefix = "__video__"
    @video_recording_lock_file = "#{@workspace}started_video_recording" # This lock file is used to indicate that video is recording, so that debug screenshots do not need to take separate screenshots
    @ffmpeg_process_threads = []
    @videopart_processing_lock_file = "#{@workspace}video_part_processing_#{session_id}"
    @video_time_mapping_file = @workspace + "video_time_mapping_#{@session_id}.txt"
    @video_merge_completed_file = "#{@workspace}video_merge_completed.txt" # This file is created when all the video parts are merged and video is ready to be uploaded

    FileUtils.mkdir_p(@workspace) if !Utils.empty?(@session_id) && !Dir.exist?(@workspace)

    if File.exists?(stop_file)
      log "Deleting stop file"
      File.delete(stop_file)
    end
  end

  def skip_recording
    @skip_recording ||= false
  end

  def device_version
    @device_version ||= DeviceManager.all_devices[@device_id]['device_version'].to_f
  end

  def device_wda_port
    @device_wda_port ||= DeviceManager.all_devices[@device_id]['webdriver_port'].to_i
  end

  # removes folder for session id, if present
  def clean_workspace
    return if @skip_recording

    unless Utils.empty?(@session_id)
      log("#{Time.now} Deleting video folder #{@workspace}")
      FileUtils.rm_rf(@workspace)
    end
  end

  # Cleans all the folders in @device_workspace whose last modified time was more than 1 day
  def clean_stale_session_folders_from_workspace
    return if @skip_recording

    if Dir.exist?(@device_workspace)
      stale_directories = Dir.entries(@device_workspace).select do |entry|
        File.directory?(File.join(@device_workspace, entry)) && !['.', '..'].include?(entry)
      end
      stale_directories = stale_directories.map do |entry|
        File.join(@device_workspace, entry)
      end
      stale_directories = stale_directories.select do |entry|
        File.mtime(entry) < (Time.now - 86400)
      end
      log("#{Time.now} Deleting #{stale_directories.length} stale session folders from workspace #{@device_workspace}")
      stale_directories.each do |stale_directory|
        log("#{Time.now} Deleting folder : #{stale_directory}")
        FileUtils.rm_rf(stale_directory)
      end
    end
  end

  def stop
    return if @skip_recording

    log("#{Time.now} #{@device_id} Stopping video recording.")
    FileUtils.touch(stop_file)
    log("#{Time.now} #{@device_id} Stop file created.")
  end

  # Stop video recording if stop_file exists.
  def should_stop_recording?
    File.exists?(stop_file)
  end

  def process_video_in_async?
    File.exists?(@async_process_file)
  end

  # Uploads the video file using the image_uploader_process process, i.e. async
  def upload(params)
    return if @skip_recording

    video_file = File.read(@video_recording_lock_file) # Lock file has path of the video file which will be rendered.
    wait_for_render(video_file) unless process_video_in_async?
    create_upload_request_for_video(video_file, params)

    create_upload_request_for_video_time_mapping(@video_time_mapping_file, params)
  rescue Timeout::Error => e
    log "Video Render Failure: Timeout, #{e.message} #{e.backtrace}"
    clean_workspace
    raise e # Will be caught down the stack.
  end

  def record(wda_port=nil, rec_start_time=0, device_version = nil) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
    return if @skip_recording

    video_parts_counter = 0
    screenshot_counter = 0
    last_video_part_start = 0
    last_video_part_end = 0

    # These are needed to generate mapping for video-text logs syncing
    next_second_to_map = 1
    total_time_elapsed = 0
    video_time_mapping = {
      0 => 0
    }

    video_out_file = @workspace + "video_rec_#{Time.now.to_i}.mp4"
    process_video_in_async_json_data = {}

    log "Recording started.... will generate: #{video_out_file}"
    actual_rec_start_time = Time.now
    initial_delay = Time.now.to_f - rec_start_time.to_f
    Utils.write_to_file(@video_recording_lock_file, video_out_file)
    begin
      prefix_genre = Utils.get_prefix_genre(@genre)
      loop do # rubocop:todo Metrics/BlockLength
        video_part_name = @video_parts_prefix + video_parts_counter.to_s
        frame_name = "#{video_part_name}-#{screenshot_counter}.png" # __video__0-0.png, __video__0-1.png, ....
        frame_path = @workspace + frame_name

        if should_stop_recording?
          # Create video snippet from pending frames.
          log "#{Time.now.getutc} Stop file detected"

          if process_video_in_async?
            if screenshot_counter == 0
              video_parts_counter -= 1
              video_part_name = @video_parts_prefix + video_parts_counter.to_s
            end
            process_video_in_async_json_data = {
              session_id: @session_id,
              video_part_name: video_part_name,
              screenshot_counter: screenshot_counter,
              video_parts_counter: video_parts_counter,
              video_out_file: video_out_file,
              actual_rec_start_time: actual_rec_start_time,
              initial_delay: initial_delay
            }
            dir_name = File.dirname(@async_meta_data_file)
            FileUtils.mkdir_p(dir_name) unless Dir.exist? dir_name
          else
            log "#{Time.now.getutc} Stop file detected, creating the last video : #{video_part_name}"
            last_video_part_start = Time.now.to_f
            create_video_part(video_part_name)
            last_video_part_end = Time.now.to_f
          end
          break
        end

        init_time = Time.now.to_f
        capture_idevicescreenshot(frame_path)
        time_taken_to_capture_screenshot = Time.now.to_f - init_time
        screenshot_counter += 1

        if screenshot_counter == @part_size
          # Join image sequence into a video part and delete the frames. Doing async because we don't want to miss frames.
          # bs_run creates a new thread & copies logging params to it
          @ffmpeg_process_threads << Thread.bs_run do
            create_video_part(video_part_name)
          end

          screenshot_counter = 0
          video_parts_counter += 1
        end
        end_time = Time.now.to_f
        total_time_elapsed += end_time - init_time
        while total_time_elapsed >= next_second_to_map
          frame_number_to_record = video_parts_counter * @part_size + screenshot_counter
          time_in_video = frame_number_to_record.to_f / @fps
          video_time_mapping[next_second_to_map] = time_in_video
          next_second_to_map += 1
        end
      end
    rescue => e
      log "Video recording failure #{e.message} #{e.backtrace}"
      BrowserStack::Zombie.push_logs("#{prefix_genre}ios-video-rec-failure", e.message, { "session_id" => @session_id })
    else
      begin
        File.open(@video_time_mapping_file, "w") { |f| f.write(video_time_mapping.to_json) }

        @ffmpeg_process_threads.each(&:join)

        if process_video_in_async?
          Utils.write_to_file(@async_meta_data_file, process_video_in_async_json_data.to_json)
          log "#{@async_meta_data_file} file created. Data : #{process_video_in_async_json_data}"
        else
          ffmpeg_join_done = Time.now.to_f
          #Video recording stopped when stop_file was generated
          create_video(video_parts_counter, video_out_file)
          video_parts_stich_done = Time.now.to_f
          if !File.exist?(video_out_file) # Video recording failure
            BrowserStack::Zombie.push_logs("#{prefix_genre}ios-video-rec-failure", "Video not rendered: File does not exist.",
                                           { "session_id" => @session_id, "data" => { "stop_to_join" => ffmpeg_join_done - last_video_part_start, "last_video_part" => last_video_part_end - last_video_part_start, "ffmpeg_join_after_last_vid_part" => ffmpeg_join_done - last_video_part_end, "video_concat_time" => video_parts_stich_done - ffmpeg_join_done, "last_video_part_screenshots_count" => screenshot_counter, "video_parts_count" => video_parts_counter + 1 } })
          else
            FileUtils.touch(@video_merge_completed_file)
            zombie_data = { "stop_to_join" => ffmpeg_join_done - last_video_part_start, "last_video_part" => last_video_part_end - last_video_part_start, "ffmpeg_join_after_last_vid_part" => ffmpeg_join_done - last_video_part_end, "video_concat_time" => video_parts_stich_done - ffmpeg_join_done, "last_video_part_screenshots_count" => screenshot_counter, "video_parts_count" => video_parts_counter + 1, "actual_rec_start_time" => actual_rec_start_time,
                            "initial_delay" => initial_delay }
            BrowserStack::Zombie.push_logs("#{prefix_genre}ios-video-rec-success", "Video render success", { "session_id" => @session_id, "data" => zombie_data })
          end
        end
      rescue => e
        log "Error during recording. #{e.message} #{e.backtrace}"
        BrowserStack::Zombie.push_logs("#{prefix_genre}ios-video-rec-failure", "Video not rendered #{e.message}", { "session_id" => @session_id })
      end
    end
  end

  def get_video_duration
    video_parts_count = Dir.glob("#{@workspace}#{@video_parts_prefix}*.mp4").count
    screenshots_count = Dir.glob("#{@workspace}#{@video_parts_prefix}*.{png,jpeg}").count
    race_condition_factor = File.exist?(@videopart_processing_lock_file) ? (@part_size.to_f / @fps) : 0
    current_video_duration = (video_parts_count * (@part_size.to_f / @fps)) + (screenshots_count.to_f / @fps) - race_condition_factor
    current_video_duration.to_i
  end

  def create_video_part(video_part_prefix)
    log "Creating video part : #{video_part_prefix}"
    # Busy wait while not able to aquire lock
    until acquire_ffmpeg_lock
      log "Waiting on other ffmpeg processes to complete"
      sleep 1
    end

    File.new(@videopart_processing_lock_file, "w")
    input_files_pattern = "#{@workspace}#{video_part_prefix}-%d.{png,jpeg}"
    output_file = "#{@workspace}#{video_part_prefix}.mp4"

    # Resize video to nearest multiple of 2
    ffmpeg_options = "scale=trunc(iw/2)*2:trunc(ih/2)*2"

    # Rotate video 90 degrees counterclockwise
    ffmpeg_options += ",transpose=2"  if @orientation == 'landscape'

    log_time("FFMPEG joining #{@part_size} frames into video_part " + video_part_prefix) do
      # ffmpeg version 3.3.2
      # TODO: set fps dynamically here. ( fps = total_number_of_screenshots / time_elapsed )
      system("#{FFMPEG} -r #{@fps} -f image2 -i #{input_files_pattern} -vcodec libx264 -crf 25 -vf \"#{ffmpeg_options}\" -pix_fmt yuv420p #{output_file} -y")
    end

    if File.exist?(output_file)
      log "Created #{output_file}, Size: #{File.size(output_file)} bytes"
      # Deleting frames which are present in the video part
      delete_all_matching("#{@workspace}#{video_part_prefix}-*.{png,jpeg}")
      log "Deleted image files of #{output_file}"
    else
      log "Failed to create video part #{video_part_prefix}"
    end

    release_ffmpeg_lock

    # TODO
    # Handle this in a better way, remove dependency of #get_video_duration from the lock file
    FileUtils.rm(@videopart_processing_lock_file) if File.exist?(@videopart_processing_lock_file)
  end

  def create_video(num_video_parts, video_file_path)
    # Creating list of video files to concat
    num_video_parts += 1
    video_concat_file = create_concat_file(num_video_parts)

    log_time("FFMPEG concatenating #{num_video_parts} snippets ") do
      system("#{FFMPEG} -f concat -safe 0 -i #{video_concat_file} -c copy #{video_file_path}")
    end

    # TODO: Make retry logic DRY
    if File.exist?(video_file_path)
      log "#{Time.now.getutc} Created #{video_file_path}, Size: #{File.size(video_file_path)} bytes"
    else
      log "#{Time.now.getutc} Video output file not generated, retrying once more."
      log_time("#{@session_id} FFMPEG concatenating #{num_video_parts} snippets ") do
        system("#{FFMPEG} -f concat -safe 0 -i #{video_concat_file} -c copy #{video_file_path}")
      end
      if File.exist?(video_file_path)
        log "#{@session_id} #{Time.now.getutc} Created #{video_file_path}, Size: #{File.size(video_file_path)} bytes"
      else
        log "#{@session_id} #{Time.now.getutc} Concatenation Failed. No video available"
      end

    end
    delete_all_matching("#{@workspace}#{@video_parts_prefix}*.mp4")
  end

  def capture_idevicescreenshot(frame_path)
      # NOTE: using IdeviceUtils.screenshot as changes for idevice utilities are handled for smart_tv
        # and for idevicescreenshot command we are aware it will log to file lots of times, hence setting flag as false
        # to avoid logging for this call
    method = ''
    if device_version >= 17.0
      method = 'wda_screenshot'
      ScreenshotsUtil.capture_screenshot_via_wda(frame_path, @device_id, device_wda_port)
      BrowserStack::OSUtils.execute("#{IMAGEMAGICK_CONVERT} #{frame_path} #{frame_path}") # convert jpg to png
      FileUtils.rm_f(frame_path) if File.size(frame_path) == 0 # corrupt files makes ffmpeg video concat fail
      sleep 0.33 # wda is faster, video stitching takes a lot of time. Achieve 3 fps video
    else
      method = 'idevicescreenshot'
      IdeviceUtils.screenshot(@device_id, frame_path, false)
    end
  rescue => e
    log "#{method} capture failed #{e.message}"
  end

  private

  # Can implement locking trivially because of GIL in ruby, which implies that there will be no parallelism.
  def acquire_ffmpeg_lock
    if @ffmpeg_processes_semaphore == 0
      false
    else
      @ffmpeg_processes_semaphore -= 1
      true
    end
  end

  def release_ffmpeg_lock
    @ffmpeg_processes_semaphore += 1
  end

  def delete_all_matching(glob_pattern)
    log "#{@session_id} Deleting all files matching : " + glob_pattern

    Dir.glob(glob_pattern).each do |file_name|
      File.delete(file_name)
    end
  end

  def create_concat_file(num_files)
    video_concat_file = "#{@workspace}video_part_names.txt"
    File.delete(video_concat_file) if File.exist?(video_concat_file)

    File.open(video_concat_file, 'a') do |f|
      num_files.times do |counter|
        file_path = "#{@workspace}#{@video_parts_prefix}#{counter}.mp4"

        # Join only those video snippets which are present on disk.
        f.puts "file '#{file_path}'" if File.exist?(file_path)
      end
    end

    video_concat_file
  end

  def log_time(log_data)
    started_at = Time.now
    yield
    log "Time taken for #{log_data} : #{Time.now - started_at} seconds."
  end

  # TODO: Move this to separate uploader ?
  def create_upload_request(uploader_request_file, json_data)
    dir_name = File.dirname(uploader_request_file)
    FileUtils.mkdir_p(dir_name) unless Dir.exist? dir_name
    Utils.write_to_file(uploader_request_file, json_data.to_json)
    log("#{@session_id} #{Time.now.getutc} Video Upload request created as #{uploader_request_file}")
  end

  def create_upload_request_for_video(file_name, params)
    log("#{@session_id} #{Time.now.getutc} Creating Video Upload request for #{file_name}")
    uploader_request_file = @server_config["videos_to_upload_dir"] + "/video_upload_#{SecureRandom.uuid}.json"
    json_data = {
      upload_type: "video",
      file_name: file_name,
      video_params: params.select { |key, _value| key.include?('video') },
      genre: @genre,
      device: @device_id,
      async_process_file: @async_process_file,
      stop_req_timestamp: params['stop_req_timestamp'] || 0
    }
    json_data[:async_meta_data_file] = @async_meta_data_file if process_video_in_async?
    create_upload_request(uploader_request_file, json_data)
  end

  def create_upload_request_for_video_time_mapping(file_name, params)
    uploader_request_file = @server_config["other_files_to_upload_dir"] + "/video_time_mapping_upload_#{SecureRandom.uuid}.json"
    json_data = {
      upload_type: "video_map_upload",
      file_name: file_name,
      video_params: params.select { |key, _value| key.include?('video') }
    }
    if process_video_in_async?
      begin
        Timeout.timeout(20) do
          start_time = Time.now.to_i
          loop do
            break if File.exist?(file_name)

            log "Waiting for rendering - Elapsed render time : #{Time.now.to_i - start_time}"
            sleep 0.5
          end
        end
      rescue => e
        BrowserStack.logger.error("video_map_upload File missing, #{e.message} #{e.backtrace}")
        return
      end
    end
    create_upload_request(uploader_request_file, json_data)
  end

  def stop_file
    "#{@workspace}stop_video_recording"
  end

  def wait_for_render(video_file)
    Timeout.timeout(60) do
      start_time = Time.now.to_i
      loop do
        break if File.exist?(video_file) && File.exist?(@video_merge_completed_file)

        log "Waiting for rendering - Elapsed render time : #{Time.now.to_i - start_time}"
        sleep 0.5
      end
    end
  end

  def log(log_string)
    if @logger
      @logger.info "IosVidCapturer: #{@session_id} #{log_string}"
    else
      puts "#{Time.now.getutc} IosVidCapturer: #{@session_id} #{log_string}"
    end
  end
end
