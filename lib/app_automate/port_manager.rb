require 'timeout'

require_relative '../utils/osutils'
require_relative '../utils/utils'
require_relative '../utils/iproxy'
require_relative '../custom_exceptions'
require_relative '../../config/constants'

module BrowserStack
  #includes methods to support youiengine sessions
  class PortManager
    class << self

      def forward_port(device_id, driver_port, app_port, tag = 'PortManager')
        retries = 0

        while retries < 3
          Iproxy.start(device_id, driver_port, app_port)
          sleep 1 # waiting for iproxy to boot
          retries += 1

          if Iproxy.running?(device_id, driver_port)
            BrowserStack.logger.info "[#{tag}] Port #{driver_port} forwarded successfully to #{app_port} app after #{retries} retries"
            return
          end
        end
        raise FireCMDException, "[#{tag}] Couldn't start iproxy to forward the port #{driver_port} to port #{app_port} for device #{device_id} even after #{retries} retries"
      end

      def stop_forwarding_port(device_id, driver_port, tag = 'PortManager')
        iproxy_pid = OSUtils.grep_process_pid("iproxy", device_id, driver_port)
        return if iproxy_pid.nil?

        OSUtils.kill_pid(iproxy_pid)
        BrowserStack.logger.info "[#{tag}] Port forwarding stopped for the port #{driver_port}"
      end

    end
  end
end
