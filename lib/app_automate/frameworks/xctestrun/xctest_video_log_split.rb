module BrowserStack
  class XCTestVideoLogSplit < XCTestLogProcess
    def initialize(log_split_manager)
      super(log_split_manager)
      @time_components = log_split_manager.time_components
      # time_components[:video][:device_start_time] -> Time of device during video recording start
      # time_components[:video][:start_duration] -> Duration of recorded video when video recording start was noted down.
      #     v2 video -> It will always be 0
      #     v1 video -> It will be equal to video chunk length that was present when video start time was noted down.
      @video_start_time = sanitized_time(@time_components[:video][:device_start_time]) - @time_components[:video][:start_duration]
    end

    def process_log
      @test_details.each_with_index do |(test, detail), index|
        @test_details[test]["video_log_boundary"] = { "start" => nil, "end" => nil }

        update_video_start_duration(test, detail, index)
        update_video_end_duration(test, index)
      end
    end

    def update_video_start_duration(test, detail, index)
      return unless detail["start_time"]

      test_start_time = sanitized_time(detail["start_time"])

      # For the very first test video will start from 0th second of recorded video.
      # For rest of the test(except last one) it will be calculated using the test start time and video recording start time
      video_start_duration = index == 0 ? @time_components[:video][:start_duration] : test_start_time - @video_start_time
      @test_details[test]["video_log_boundary"]["start"] = video_start_duration.round(0)
    end

    def update_video_end_duration(test, index)
      # Do not update the video end tag if its the last test
      # Last test will show the video till the end of video recording
      next_test_detail = next_test_details(index)
      video_end_duration = sanitized_time(next_test_detail["start_time"]) - @video_start_time if next_test_detail && next_test_detail["start_time"]

      # For the very last test, video duration will be till the end of recorded video.
      video_end_duration = @time_components[:video][:end_duration] if last_test?(index)
      @test_details[test]["video_log_boundary"]["end"] = video_end_duration.round(0) if video_end_duration
    end

    def update_corrupt_video_log_boundary(test_name)
      @log_split_manager.log("Updating video log boundary of #{test_name} from #{@test_details[test_name]['video_log_boundary']}")
      @test_details[test_name]["video_log_boundary"]["start"] = nil
      @test_details[test_name]["video_log_boundary"]["end"] = nil
    end

    def video_log_boundary_valid?(test_detail)
      boundary = test_detail["video_log_boundary"]
      return false if boundary.nil?
      return false unless boundary['start'].is_a?(Numeric) && boundary['start'] >= 0 && boundary['end'].is_a?(Numeric) && boundary['end'] >= 0
      return false if boundary['start'] > boundary['end']

      true
    end

    def validate_test_details
      @log_split_manager.log("Validating video log split details")
      @test_details.each do |_test, test_detail|
        next if video_log_boundary_valid?(test_detail)

        update_corrupt_video_log_boundary(test_detail['name'])
        @log_split_manager.update_log_split_failures_count(XCTEST_VIDEO_LOG_SPLIT_FAILURES)
        @log_split_manager.add_log_split_error(test_detail["name"], XCTEST_VIDEO_LOG_BOUNDARY_NOT_VALID)
      end
    end
  end
end
