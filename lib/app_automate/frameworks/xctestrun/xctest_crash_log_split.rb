require_relative '../../../utils/utils'
require 'zip'

module BrowserStack
  class XCTestCrashLogSplit < XCTestLogProcess
    def initialize(log_split_manager)
      super(log_split_manager)
      @crash_log_path = log_split_manager.crash_log_path
      @crash_logs = Dir["#{@crash_log_path}/*.{ips,ips.beta}"]
    end

    def process_log
      if @crash_logs.empty?
        @log_split_manager.log("[crash logs splitting]: No crash logs found in #{@crash_log_path}")
        return
      end
      map_crashes_to_tests
    end

    def validate_log_file
      return if @crash_log_path.nil? || !Dir.exist?(@crash_log_path) || Dir.empty?(@crash_log_path)
    end

    private

    def parse_crash_time_from_crash_file(file_path)
      File.foreach(file_path) do |line|
        if line.start_with?("Date/Time:")
          time_str = line.split("Date/Time:").last.strip
          begin
            return DateTime.parse(time_str).to_time
          rescue ArgumentError
            return nil
          end
        elsif line.include?('"captureTime"')
          capture_time = line.match(/"captureTime"\s*:\s*"([^"]+)"/)
          begin
            return DateTime.parse(capture_time[1]).to_time if capture_time
          rescue ArgumentError
            return nil
          end
        end
      end
      @log_split_manager.log("[crash log splitting]: Unable to parse crash time from file: #{file_path}")
      @log_split_manager.update_log_split_failures_count(XCTEST_CRASH_LOG_SPLIT_FAILURES)
      nil
    end

    def crash_logs_with_timestamps
      crash_logs_with_time = @crash_logs.map do |file|
        time = parse_crash_time_from_crash_file(file)
        time ? { path: file, crash_time: time } : nil
      end.compact

      crash_logs_with_time.sort_by { |c| c[:crash_time] }
    end

    def map_crashes_to_tests # rubocop:disable Metrics/MethodLength, Metrics/AbcSize
      @log_split_manager.log("[Crash log splitting]: Mapping crash logs to tests")
      # Though Tests are stored in sorted order in @test_details, we can sort it again for any misses.
      test_objects = @test_details.map do |test_name, test_detail|
        next if test_detail["start_time"].nil? || test_detail["duration"].nil?

        start_time = DateTime.parse(test_detail["start_time"].to_s).to_time
        {
          name: test_name,
          start_time: start_time,
          end_time: start_time + test_detail["duration"],
          crash_files: []
        }
      end.compact
      tests = test_objects.sort_by { |t| t[:start_time] }

      # Early return if no tests
      if tests.empty?
        @log_split_manager.log("[Crash log splitting]: No tests found with valid timing information")
        return
      end

      sorted_crash_logs = crash_logs_with_timestamps
      first_test = tests.first

      i = 0
      prev_test = nil

      sorted_crash_logs.each do |crash_log|
        crash_time = crash_log[:crash_time]
        crash_log_file = crash_log[:path]

        # Handle crashes before first test
        if crash_time < first_test[:start_time]
          @log_split_manager.log("[Crash log splitting]: Crash occurred before first test, assigning to first test: #{first_test[:name]}")
          first_test[:crash_files] << crash_log_file
          next
        end

        while i < tests.size && crash_time > tests[i][:end_time]
          prev_test = tests[i]
          i += 1
        end

        if i < tests.size
          if crash_time >= tests[i][:start_time]
            tests[i][:crash_files] << crash_log_file
          elsif prev_test # Crash logs in between two test boundary gets added to the previous test
            prev_test[:crash_files] << crash_log_file
          end
        elsif prev_test # Crash logs which happened after the last test will get added to the last test details
          prev_test[:crash_files] << crash_log_file
        end
      end

      # Push crash_files back into @test_details
      tests.each do |test|
        @log_split_manager.log("[Crash log splitting]: Test: #{test[:name]}, Crash files: #{test[:crash_files]}")
        @test_details[test[:name]]["crash_files"] = test[:crash_files] if test[:crash_files]&.any?
      end
    end
  end
end
