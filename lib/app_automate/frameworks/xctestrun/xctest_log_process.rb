module BrowserStack
  # XCTestLogProcess
  #
  # This is the parent class for log splitting functionality. It provides common methods and behavior
  # that can be shared among different log splitting implementations.
  #
  # Subclasses of XCTestLogProcess should implement log splitting logic inside process method
  # and validates splitted data using validate_test_details
  class XCTestLogProcess
    def initialize(log_split_manager, log_file = "")
      @log_split_manager = log_split_manager
      @test_details = @log_split_manager.test_details.clone
      @log_file = log_file
    end

    def process
      validate_log_file
      process_log
      validate_test_details
      post_validation_steps
      update_log_split_details
    end

    def process_log
      # Child class should implement this
    end

    def validate_test_details
      # Child class should implement this
    end

    def post_validation_steps
      # Child class should implement this
    end

    # This method is overriden for networkLogsSplit Class
    def validate_log_file
      return if @log_file.empty?

      raise FileNotFound, "Log file: #{@log_file} not found" unless File.exist?(@log_file)
      raise FileEmpty, "Log file: #{@log_file} is empty" if File.zero?(@log_file)
    end

    def last_test_details
      @test_details[@test_details.keys.last]
    end

    def next_test_details(index)
      return @test_details[@test_details.keys[index + 1]] unless last_test?(index)

      nil
    end

    def last_test?(index)
      index >= @test_details.length - 1
    end

    def update_log_split_details
      @log_split_manager.test_details = @test_details
    end

    # Drop millisecond part of time object if it is present and return a datetime object of format -> YY:MM:DD HH:MM:SS
    def sanitized_time(time)
      DateTime.parse(DateTime.parse(time.to_s).to_time.to_s).to_time
    end
  end
end
