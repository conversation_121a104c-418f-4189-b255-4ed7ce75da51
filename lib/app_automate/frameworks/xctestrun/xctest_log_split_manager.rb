require 'json'
require 'fileutils'
require 'date'

require_relative '../../../utils/utils'
require_relative '../../../../config/constants'
require_relative './errors'

module BrowserStack
  # XCTestRun flow executes all the tests in a single execution process that results in consolidated logs
  # To show test specific logs on Xcuitest dashboard, logs need to be splitted

  class XCTestLogSplitManager # rubocop:todo Metrics/ClassLength
    attr_accessor :test_details, :split_time, :final_test_status, :log_split_errors, :log_split_failures, :test_details_valid, :mitmproxy_instance
    attr_reader :total_session_time, :instrumentation_file, :device, :session_id, :time_components, :retry_config, :crash_log_path

    # Initialize a new instance of XCTestLogSplitManager.
    #
    # Parameters:
    # - instrumentation_file (String): Instrumentation file path.
    # - session_id (String): session id.
    #
    # Instance variables:
    # - @test_details: Consists of all the raw test data parsed from instru log, device log, network log files.
    # - @log_split_errors: All the edge cases detected during log splitting
    # - @log_split_failures: All the test where log any of log split failed and have nil values
    # - @split_time: Total time taken for splitting each log
    # - @final_test_status: Overall executon status parsed from instru file
    # - @test_details_valid: Parsed test details are valid or not
    def initialize(instrumentation_file, device, mitmproxy_instance, network_enabled, video_enabled, device_enabled, session_id, time_components, total_session_time, retry_config)
      @instrumentation_file = instrumentation_file
      @device = device
      @mitmproxy_instance = mitmproxy_instance
      @video_enabled = video_enabled
      @network_enabled = network_enabled
      @device_enabled = device_enabled
      @session_id = session_id
      @time_components = time_components
      @total_session_time = total_session_time
      @test_details = {}
      @log_split_errors = {}
      @log_split_failures = {}
      @final_test_status = nil
      @test_details_valid = false
      @retry_config = retry_config
      @split_time = { event_hash: {} }
    end

    def process_logs
      processing_start_time = Time.now.utc
      process_xctest_instru_log
      process_xctest_device_log if @device_enabled
      process_xctest_video_log if @video_enabled
      process_xctest_network_log if @network_enabled
      process_xctest_crash_log
    rescue => e
      raise e
    end

    def add_log_split_error(test, error)
      if @log_split_errors.key?(error)
        @log_split_errors[error] << test
      else
        @log_split_errors[error] = [test]
      end
    end

    def update_log_split_failures_count(failure_type, test_property = nil)
      if test_property
        if @log_split_failures[failure_type]
          existing_value = @log_split_failures[failure_type][test_property]
          @log_split_failures[failure_type][test_property] = existing_value.is_a?(Integer) ? existing_value + 1 : 1
        else
          @log_split_failures[failure_type] = {}
          @log_split_failures[failure_type][test_property] = 1
        end
      else
        existing_value = @log_split_failures[failure_type]
        @log_split_failures[failure_type] = existing_value.is_a?(Integer) ? existing_value + 1 : 1
      end
    end

    def log(logline)
      BrowserStack.logger.info "[#{@session_id}] [Log splitting] #{logline}"
    end

    private

    def process_xctest_instru_log
      Utils.mark_event_start('instrumentationLogs.split_time', @split_time[:event_hash])
      XCTestInstruLogParse.new(self).process
    rescue => e
      raise e if ["FileEmpty", "FileNotFound"].include?(e.class.to_s)

      log("Error in parsing instru log: #{e.message}, #{e.backtrace.join("\n")}")
      raise InstruParseError, "Error in parsing instrumentation log, error: #{e.message}"
    else
      raise InstruParseEmpty, "No test parsed" if @test_details.empty?
      raise TestsValidationFailed, "Validation failed" unless @test_details_valid
    ensure
      Utils.mark_event_end('instrumentationLogs.split_time', @split_time[:event_hash])
    end

    def process_xctest_device_log
      Utils.mark_event_start('deviceLogs.split_time', @split_time[:event_hash])
      XCTestDeviceLogSplit.new(self).process
    rescue => e
      log("Error in splitting device log: #{e.message}, #{e.backtrace.join("\n")}")
      BrowserStack::Zombie.push_logs("xctest-device-split-error", "Error: #{e.message[0, 100]}", { "session_id" => @session_id })
      update_log_split_failures_count(XCTEST_DEVICE_LOG_SPLIT_ERROR)
    ensure
      Utils.mark_event_end('deviceLogs.split_time', @split_time[:event_hash])
    end

    def process_xctest_video_log
      Utils.mark_event_start('videoLogs.split_time', @split_time[:event_hash])
      XCTestVideoLogSplit.new(self).process
    rescue => e
      log("Error in splitting video log: #{e.message}, #{e.backtrace.join("\n")}")
      BrowserStack::Zombie.push_logs("xctest-video-split-error", "Error: #{e.message[0, 100]}", { "session_id" => @session_id })
      update_log_split_failures_count(XCTEST_VIDEO_LOG_SPLIT_ERROR)
    ensure
      Utils.mark_event_end('videoLogs.split_time', @split_time[:event_hash])
    end

    def process_xctest_network_log
      Utils.mark_event_start('networkLogs.split_time', @split_time[:event_hash])
      XCTestNetworkLogSplit.new(self).process
    rescue => e
      log("Error in splitting network log: #{e.message}, #{e.backtrace.join("\n")}")
      BrowserStack::Zombie.push_logs("xctest-network-split-error", "Error: #{e.message[0, 100]}", { "session_id" => @session_id })
      update_log_split_failures_count(XCTEST_NETWORK_LOG_SPLIT_ERROR)
    ensure
      log("Cleaning up flow file and temp test_details file")
      FileUtils.rm_f(@mitmproxy_instance.dumpfile)
      temp_test_details_file = XCTestNetworkLogSplit.temp_test_details_file(@session_id)
      FileUtils.rm_f(temp_test_details_file) if File.exist?(temp_test_details_file)
      Utils.mark_event_end('networkLogs.split_time', @split_time[:event_hash])
    end

    def process_xctest_crash_log
      Utils.mark_event_start('crashLogs.split_time', @split_time[:event_hash])
      @crash_log_path = Utils.get_crash_reports_dir_path(@device)
      XCTestCrashLogSplit.new(self).process
    rescue => e
      log("Error in splitting crash log: #{e.message}, #{e.backtrace.join("\n")}")
      BrowserStack::Zombie.push_logs("xctest-crash-split-error", "Error: #{e.message[0, 100]}", { "session_id" => @session_id })
      update_log_split_failures_count(XCTEST_CRASH_LOG_SPLIT_ERROR)
    ensure
      Utils.mark_event_end('crashLogs.split_time', @split_time[:event_hash])
    end
  end
end
