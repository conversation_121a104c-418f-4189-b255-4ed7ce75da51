module BrowserStack
  # Map device logs to each tests parsed from instrumentation logs
  # Device log timestamp precision level is seconds while instrumentation logs timestamp precision level is milliseconds.
  # Multiple test can start and end in single second

  class XCTestDeviceLogSplit < XCTestLogProcess
    def initialize(log_split_manager)
      device_logs_file = BrowserStack::DeviceLogger.app_device_logs_file(log_split_manager.device)
      super(log_split_manager, device_logs_file)
    end

    def process_log # rubocop:todo Metrics/AbcSize
      current_tests = []
      current_second = nil
      test_details = @test_details.clone

      device_log = File.new(@log_file)
      device_log.each do |log_line|
        next unless (time_meta = log_line.match(XCTEST_DEVICE_LOG_TIME_MATCH_REGEX))

        time_str = time_meta[1]
        log_line_time = sanitized_time(time_str)

        # Check if a new second is detected or the very first logline
        if current_second.nil? || log_line_time != current_second
          # Process the current tests for the previous second
          current_tests.each do |test|
            if sanitized_time(test["end_time"]) <= current_second
              device_log_boundary = { "start" => test["start_byte"], "end" => device_log.pos - log_line.bytesize }
              update_device_log_boundary(test['name'], device_log_boundary)
            end
          end

          # Clear completed tests from current_tests array
          current_tests.reject! { |test| sanitized_time(test["end_time"]) <= current_second }

          # Process the test details for the new second
          # Also Process tests that doesn't have start_time, shift operation will make room for next test
          while test_details.first && ( !test_details.first[1]["start_time"] || log_line_time >= sanitized_time(test_details.first[1]["start_time"]))
            test = test_details.shift

            next unless test[1]["start_time"] && test[1]["duration"]

            end_time = test[1]["start_time"] + test[1]["duration"]
            start_byte = device_log.pos - log_line.bytesize
            current_tests << { "name" => test[0], "end_time" => end_time, "start_byte" => start_byte }
          end
        end
        # Update the current_second
        current_second = log_line_time
      end

      # Handle tests where loglines ended before test end time
      total_size = device_log.size
      current_tests.each do |test|
        if sanitized_time(test["end_time"]) >= current_second
          device_log_boundary = { "start" => test["start_byte"], "end" => total_size }
          update_device_log_boundary(test['name'], device_log_boundary)
        end
      end

      update_first_and_last_test_device_log_boundary(total_size)
    end

    def update_device_log_boundary(test_name, boundary)
      @test_details[test_name]['device_log_boundary'] = boundary
    end

    def fallback_device_logs(test_name)
      @log_split_manager.log("Updating device log boundary of #{test_name} to nil from #{@test_details[test_name]['device_log_boundary']}")
      @test_details[test_name]["device_log_boundary"] = { "start" => nil, "end" => nil }
    end

    def device_log_boundary_valid?(test_detail)
      boundary = test_detail["device_log_boundary"]
      return false if boundary.nil?
      return false unless boundary['start'].is_a?(Numeric) && boundary['start'] >= 0 && boundary['end'].is_a?(Numeric) && boundary['end'] >= 0

      true
    end

    def validate_test_details
      @log_split_manager.log("Validating device log split details")
      @test_details.each do |_test, test_detail|
        next if device_log_boundary_valid?(test_detail)

        fallback_device_logs(test_detail['name'])
        @log_split_manager.update_log_split_failures_count(XCTEST_DEVICE_LOG_SPLIT_FAILURES)
        @log_split_manager.add_log_split_error(test_detail["name"], XCTEST_DEVICE_LOG_BOUNDARY_NOT_VALID)
      end
    end

    private

    # Device log for first test should start from 0 and last test should cover log till end
    def update_first_and_last_test_device_log_boundary(total_size)
      return if @test_details.empty?

      first_test = @test_details.keys.first
      first_test_device_logs = @test_details[first_test]["device_log_boundary"]
      first_test_device_logs["start"] = 0 if first_test_device_logs && first_test_device_logs["start"]

      last_test = @test_details.keys.last
      last_test_device_logs = @test_details[last_test]["device_log_boundary"]
      last_test_device_logs["end"] = total_size if last_test_device_logs && last_test_device_logs["end"]
    end
  end
end
