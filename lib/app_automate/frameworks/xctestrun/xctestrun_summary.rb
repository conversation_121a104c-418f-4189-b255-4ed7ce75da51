require 'json'
require 'fileutils'
require 'securerandom'

require_relative '../../../configuration'
require_relative '../../../utils/osutils'
require_relative '../../../utils/utils'
require_relative '../../../../config/constants'
require_relative '../../../session/xctest_session'
require_relative '../../../../../mobile-common/frameworks_timeout_manager/app_automate_frameworks/xctest_timeout_manager'

module BrowserStack
  class XCTestrunSummary < BrowserStack::XCTestSession # rubocop:todo Metrics/ClassLength
    # Using this default test names to support the intermidiate summary v2
    # file. The final test name could only be populated after test completion.
    # The intermidiate summary file employs test name as the key of the json.
    # The transition from this default name to "<x> Failures" happens in
    # update_test_summary_v2 & flatten_summary_v2 method
    DEFAULT_TEST_NAME = "RunnerTests"
    DEFAULT_CLASS_NAME = "All Devices"
    DEFAULT_TEST_STATUS = "running"

    # We are inheriting XCTestSession because currently it holds a huge monolith
    # of methods concerning with summary handling, session execution e.t.c.
    # We only want to inherit certain methods like notify_pusher, update_tests_status
    # which require preset instance variables like: @params & auth keys.
    # TODO: Decouple summary methods from XCTestSession and inherit that class here.
    def initialize(params, device_config)
      super
      @summary_file_v2 = get_summary_path_v2
    end

    def setup_summary_v2(build_id, session_id, test_id)
      log_start "setup_summary_v2"
      basic_summary_v2 = {
        build_id: build_id,
        session_id: session_id,
        test_summary: test_states,
        classes: {
          DEFAULT_CLASS_NAME.to_s => {
            name: DEFAULT_CLASS_NAME,
            tests_summary: test_states,
            tests: {
              DEFAULT_TEST_NAME.to_s => {
                name: DEFAULT_TEST_NAME,
                start_time: '',
                status: DEFAULT_TEST_STATUS,
                test_id: test_id,
                duration: '',
                video: '',
                class: DEFAULT_CLASS_NAME
              }
            }
          }
        }
      }
      ['total', DEFAULT_TEST_STATUS].each do |state|
        basic_summary_v2[:classes][DEFAULT_CLASS_NAME][:tests_summary][state] += 1
        basic_summary_v2[:test_summary][state] += 1
      end
      Utils.write_to_file(@summary_file_v2, basic_summary_v2.to_json)
      log_finish "setup_summary_v2"
      @summary_file_v2
    end

    def update_test_summary_v2(time_components, test_status, nos_failures, num_crash_reports)
      log_start "update_test_summary_v2"
      summary_data = get_summary_data(@summary_file_v2)
      test_object = begin
        summary_data['classes'][DEFAULT_CLASS_NAME]['tests'][DEFAULT_TEST_NAME]
      rescue
        nil
      end
      test_start, test_end = time_components[:xcodebuild].values
      video_start = time_components[:video][:start_duration]
      video_end = time_components[:video][:end_duration]

      if test_object
        test_name = nos_failures == "NA" ? DEFAULT_TEST_NAME : "#{nos_failures} Failures"
        test_object.merge!({
          status: test_status,
          start_time: Time.at(test_start.to_i),
          name: test_name,
          duration: test_duration(test_end, test_start).to_f.round(3),
          crash_logs_count: num_crash_reports
        })
        test_object[:video] = "#{video_start},#{video_end}" if video_enabled?
        BrowserStack.logger.info "Updated test object in summary file with #{test_status} with #{nos_failures} failures"
      end

      summary_data['duration'] = test_duration(test_end, test_start)
      summary_data = update_tests_status(summary_data, DEFAULT_CLASS_NAME, test_status, 'running')
      Utils.write_to_file(@summary_file_v2, summary_data.to_json)
      log_finish "update_test_summary_v2"
      summary_data["test_summary"]
    end

    def get_log_split_test_status(test_name, status, session_id, last_test)
      return status if status

      if last_test && (session_timedout? || test_expired?(@session_start_time, @total_session_time))
        BrowserStack::Zombie.push_logs("xctest-test-marked-timedout", '', { "session_id" => session_id, "data" => { "testname" => test_name, "original_status" => status, "updated_status" => XCTEST_LOG_SPLIT_TEST_DEFAULT_STATUS } })
        "timedout"
      else
        BrowserStack::Zombie.push_logs("xctest-test-marked-failed", '', { "session_id" => session_id, "data" => { "testname" => test_name, "original_status" => status, "updated_status" => XCTEST_LOG_SPLIT_TEST_DEFAULT_STATUS } })
        XCTEST_LOG_SPLIT_TEST_DEFAULT_STATUS
      end
    end

    def get_log_split_test_hash(test_details, session_id) # rubocop:disable Metrics/MethodLength, Metrics/AbcSize
      test_states = {
        "total" => 0,
        "passed" => 0,
        "failed" => 0,
        "skipped" => 0,
        "timedout" => 0,
        "error" => 0,
        "running" => 0,
        "queued" => 0
      }
      log_split_test_hash = {}

      test_details.each_with_index do |(test, test_detail), index| # rubocop:disable Metrics/BlockLength
        if test_detail["original_test_name"]
          BrowserStack.logger.info "[#{session_id}] Back-Updating Summary File. Iterative Test: #{test}, Targetted Test: #{test_detail['original_test_name']}"
          test = test_detail["original_test_name"]
        end

        id = get_md5(test)
        class_name = test[/(?:\b\w+\.)?(\w+)\s+\w+/, 1]
        test_name = test[/\s(.+)/, 1]

        unless class_name && test_name
          BrowserStack::Zombie.push_logs("xctest-summary-nil-testname", '', { "session_id" => session_id, "data" => { "testname" => test_name, "classname" => class_name } })
          next
        end

        last_test = test_details.length - 1 == index
        test_status = get_log_split_test_status(test_name, test_detail["status"], session_id, last_test)
        video_duration = nil

        # If video logs not enabled, video_log_boundary will be nil
        if test_detail["video_log_boundary"]
          video_start, video_end = test_detail["video_log_boundary"].values
          video_duration = "#{video_start},#{video_end}"
        end

        log_split_test_hash[class_name] ||= {
          "name" => class_name,
          "tests_summary" => {
            "total" => 0,
            "passed" => 0,
            "failed" => 0,
            "skipped" => 0,
            "timedout" => 0,
            "error" => 0,
            "running" => 0,
            "queued" => 0
          },
          "tests" => {}
        }

        log_split_test_hash[class_name]['tests'][test_name] = {
          name: test_name,
          start_time: test_detail["start_time"],
          status: test_status,
          test_id: session_id + id,
          duration: test_detail["duration"],
          video: video_duration,
          instrumentation_log: test_detail["instru_log_boundary"],
          device_log: test_detail["device_log_boundary"],
          network_log: test_detail["network_log_boundary"],
          class: class_name
        }
        if test_detail["crash_files"]
          log_split_test_hash[class_name]['tests'][test_name][:crash_files] = test_detail["crash_files"]
          log_split_test_hash[class_name]['tests'][test_name][:crash_logs_count] = test_detail["crash_files"].length
        end

        next if test_detail["original_test_name"]

        log_split_test_hash[class_name]["tests_summary"][test_status] += 1
        log_split_test_hash[class_name]["tests_summary"]["total"] += 1
        test_states[test_status] += 1
        test_states["total"] += 1
      end

      [log_split_test_hash, test_states]
    end

    def generate_summary_log_split(test_details, build_id, session_id, time_components, session_start_time, total_session_time)
      @session_start_time = session_start_time
      @total_session_time = total_session_time
      test_start, test_end = time_components[:xcodebuild].values

      log_split_test_hash, test_states = get_log_split_test_hash(test_details, session_id)

      log_split_summary_data = {
        build_id: build_id,
        session_id: session_id,
        test_summary: test_states,
        classes: log_split_test_hash,
        duration: test_duration(test_end, test_start).to_f.round(3)
      }

      [test_states, log_split_summary_data]
    end

    def update_callback_file(callback_file, test_summary)
      return if test_summary.nil?

      log_start "update_callback_file"
      callback_data = Utils.read_json_file(callback_file)
      callback_data['test_status'] = test_summary.reject { |status| status == "total" }
      Utils.write_to_file(callback_file, callback_data.to_json)
      log_finish "update_callback_file"
    end

    def update_session_stats(session_id, test_summary)
      return if test_summary.nil?

      log_start "update_session_stats"
      stats_payload = {
        sessionid: session_id,
        test_failed: test_summary["failed"],
        test_success: test_summary["passed"],
        test_ignored: test_summary["skipped"],
        test_timedout: test_summary["timedout"]
      }
      BrowserStack::Zombie.push_logs("app_automation_session_stats", '', stats_payload)
      log_finish "update_session_stats"
    end

    def send_setup_build_pusher_event(build_id, session_id, device_name)
      log_start "send_setup_build_pusher_event"
      message = {
        build_id: build_id,
        device: device_name,
        session_id: session_id,
        testlist: []
      }
      notify_pusher("setup_build", message.to_json)
      log_finish "send_setup_build_pusher_event"
    end

    private

    def test_duration(end_time, start_time)
      end_time - start_time
    rescue => e
      BrowserStack.logger.info "Time Components ill-formatted. Exception: #{e.message}"
      0
    end
  end
end
