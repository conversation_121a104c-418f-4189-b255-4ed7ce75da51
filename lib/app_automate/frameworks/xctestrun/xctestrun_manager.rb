require_relative '../../../utils/plist_buddy'

module BrowserStack
  module XCTestrunManager  # rubocop:todo Metrics/ModuleLength
    def self.generate_xctestrun_for_testing(device, app_bundle_id, device_os_version, app_path, test_runner_path)
      file_path = XCTestrunManager.file_path(device)
      @@retry_strategy = ""

      @format_version = PlistBuddy.get_value_of_key(file_path, XCTESTRUN_FORMAT_VERSION_KEY).to_s.strip

      if @format_version == V1_XCTESTRUN
        handle_v1_xctestrun(file_path, app_path, test_runner_path)
      else
        BrowserStack.logger.info("[XCTestrunManager] Processing v2 xctestrun file")
        if device_os_version >= 17
          # From iOS17 useDestinationArtifacts doesn't work
          # https://browserstack.atlassian.net/browse/AASI-2993
          remove_usedestinationartifacts_keys(file_path)
          update_xctestrun_test_and_app_path(file_path, app_path, test_runner_path)
        else
          update_xctestrun_with_usedestinationartifacts(file_path, app_bundle_id)
        end
      end
      remove_code_coverage_buildable_infos_source_files(file_path)
      update_xctestrun_test_and_app_path_code_coverage(file_path, app_path, test_runner_path)

      # XCTRepetitionMode -> @@retry_strategy
      # 1 -> Until Failure
      # 2 -> Retry On Failure
      # 3 -> Up Until Maximum Repititions
      [file_path, @@retry_strategy]
    end

    def self.handle_v1_xctestrun(file_path, app_path, test_runner_path)
      # Use plutil and jq to get test target keys
      json_str = `plutil -convert json -o - "#{file_path}" | jq -r 'del(.FormatVersion, .__xctestrun_metadata__) | keys[]'`
      test_targets = json_str.split("\n").reject(&:empty?)
      BrowserStack.logger.info("[XCTestrunManager] Test targets found in xctestrun file: #{test_targets}")

      test_targets.each do |test_target_key|
        # Process only test target which have key IsAppHostedTestBundle set to true
        is_app_hosted_test_bundle = PlistBuddy.get_value_of_key(file_path, "#{test_target_key}:IsAppHostedTestBundle").strip

        if is_app_hosted_test_bundle.empty? || is_app_hosted_test_bundle.downcase != "true"
          BrowserStack.logger.info("[XCTestrunManager] Key IsAppHostedTestBundle is missing or not true for the target #{test_target_key}, Skipping processing")
          next
        end
        test_bundle_path = PlistBuddy.get_value_of_key(file_path, "#{test_target_key}:TestBundlePath").strip
        test_bundle_path = File.join(File.dirname(test_runner_path), File.basename(test_runner_path), "PlugIns", File.basename(test_bundle_path))
        PlistBuddy.set_key_in_plist(file_path, "#{test_target_key}:TestBundlePath", test_bundle_path)
        PlistBuddy.delete_key_in_plist(file_path, "#{test_target_key}:DependentProductPaths")
        PlistBuddy.delete_key_in_plist(file_path, "#{test_target_key}:TestHostPath")
        add_test_host_path(file_path, test_target_key, test_runner_path, app_path)
        add_preffered_screen_capture_format(file_path, test_target_key, "screenshots")
        break
      end
    end

    # Replaces TestBundlePath with TestBundleDestinationRelativePath, with the same value as TestBundlePath
    def self.replace_test_bundle_path(file_path, test_target_key)
      test_bundle_path = PlistBuddy.get_value_of_key(file_path, "#{test_target_key}:TestBundlePath").strip
      PlistBuddy.add_key_value_in_plist(file_path, "#{test_target_key}:TestBundleDestinationRelativePath", "string", test_bundle_path)
      PlistBuddy.delete_key_in_plist(file_path, "#{test_target_key}:TestBundlePath")
    end

    # Adds UITargetAppBundleIdentifier with the same value as TestHostBundleIdentifier
    def self.add_ui_target_bundle_id(file_path, test_target_key, app_bundle_id)
      PlistBuddy.add_key_value_in_plist(file_path, "#{test_target_key}:UITargetAppBundleIdentifier", "string", app_bundle_id)
    end

    def self.unit_tests_target?(file_path, test_target_key)
      PlistBuddy.get_value_of_key(file_path, "#{test_target_key}:IsAppHostedTestBundle").strip.to_s.downcase == "true"
    end

    def self.add_ui_target_app_path(file_path, test_target_key, app_path)
      # don't add in case of unit tests
      if XCTestrunManager.unit_tests_target?(file_path, test_target_key)
        BrowserStack.logger.info("[XCTestrunManager] Not adding UITargetAppPath as target #{test_target_key} is unit test")
        return
      end
      PlistBuddy.add_key_value_in_plist(file_path, "#{test_target_key}:UITargetAppPath", "string", app_path)
    end

    def self.add_test_host_path(file_path, test_target_key, test_runner_path, app_path)
      runner_path = if @format_version == V1_XCTESTRUN
                      test_runner_path
                    else
                      XCTestrunManager.unit_tests_target?(file_path, test_target_key) && !app_path.empty? ? app_path : test_runner_path
                    end
      BrowserStack.logger.info("[XCTestrunManager] Adding TestHostPath for #{test_target_key}: #{runner_path}")
      PlistBuddy.add_key_value_in_plist(file_path, "#{test_target_key}:TestHostPath", "string", runner_path)
    end

    def self.add_preffered_screen_capture_format(file_path, test_target_key, format)
      PlistBuddy.add_key_value_in_plist(file_path, "#{test_target_key}:PreferredScreenCaptureFormat", "string", format)
    end

    def self.file_path(device)
      File.join("/tmp", "#{device}_xctestrun.xctestrun")
    end

    def self.remove_code_coverage_buildable_infos_source_files(file_path)
      MAX_CODE_COVERAGE_BUNDLES.times do |index|
        base_key = @format_version == V1_XCTESTRUN ? "__xctestrun_metadata__" : ""
        coverage_bundle_key = "#{base_key}:CodeCoverageBuildableInfos:#{index}"
        break if PlistBuddy.get_value_of_key(file_path, coverage_bundle_key).strip.empty?

        unless PlistBuddy.get_value_of_key(file_path, "#{coverage_bundle_key}:SourceFiles").strip.empty?
          BrowserStack.logger.info("[XCTestrunManager] Removing SourceFiles key from xctestrun file from #{coverage_bundle_key}")
          PlistBuddy.delete_key_in_plist(file_path, "#{coverage_bundle_key}:SourceFiles")
        end

        unless PlistBuddy.get_value_of_key(file_path, "#{coverage_bundle_key}:SourceFilesCommonPathPrefix").strip.empty?
          BrowserStack.logger.info("[XCTestrunManager] Removing SourceFilesCommonPathPrefix key from xctestrun file from #{coverage_bundle_key}")
          PlistBuddy.delete_key_in_plist(file_path, "#{coverage_bundle_key}:SourceFilesCommonPathPrefix")
        end
      end
    end

    def self.update_xctestrun_test_and_app_path_code_coverage(file_path, app_path, test_runner_path)
      app_binary = File.basename(app_path)
      test_runner_binary = File.basename(test_runner_path)

      if @format_version == V1_XCTESTRUN
        update_v1_code_coverage(file_path, test_runner_path, test_runner_binary)
      else
        update_v2_code_coverage(file_path, app_path, app_binary, test_runner_path, test_runner_binary)
      end
    end

    def self.update_v1_code_coverage(file_path, test_runner_path, test_runner_binary)
      MAX_CODE_COVERAGE_BUNDLES.times do |index|
        break if PlistBuddy.get_value_of_key(file_path, "__xctestrun_metadata__:CodeCoverageBuildableInfos:#{index}").strip.empty?

        product_path = "__xctestrun_metadata__:CodeCoverageBuildableInfos:#{index}:ProductPath"
        product_path_value = PlistBuddy.get_value_of_key(file_path, product_path).strip
        break if product_path_value.empty?

        BrowserStack.logger.info("[XCTestrunManager] Updating ProductPath key in xctestrun file from #{product_path} for #{test_runner_binary}")
        updated_product_path_value = update_product_path_value(product_path_value, test_runner_binary, test_runner_path)
        PlistBuddy.set_key_in_plist(file_path, product_path, updated_product_path_value)
      end
    end

    def self.update_v2_code_coverage(file_path, app_path, app_binary, test_runner_path, test_runner_binary)
      MAX_CODE_COVERAGE_BUNDLES.times do |index|
        break if PlistBuddy.get_value_of_key(file_path, ":CodeCoverageBuildableInfos:#{index}").strip.empty?

        MAX_CODE_COVERAGE_BUNDLES.times do |product_path_index|
          product_path = ":CodeCoverageBuildableInfos:#{index}:ProductPaths:#{product_path_index}"
          product_path_value = PlistBuddy.get_value_of_key(file_path, product_path).strip
          break if product_path_value.empty?

          if product_path_value.include?(app_binary)
            BrowserStack.logger.info("[XCTestrunManager] Updating ProductPaths key in xctestrun file from #{product_path} for #{app_binary}")
            updated_product_path_value = update_product_path_value(product_path_value, app_binary, app_path)
            PlistBuddy.set_key_in_plist(file_path, product_path, updated_product_path_value)
          end

          next unless product_path_value.include?(test_runner_binary)

          BrowserStack.logger.info("[XCTestrunManager] Updating ProductPaths key in xctestrun file from #{product_path} for #{test_runner_binary}")
          updated_product_path_value = update_product_path_value(product_path_value, test_runner_binary, test_runner_path)
          PlistBuddy.set_key_in_plist(file_path, product_path, updated_product_path_value)
        end
      end
    end

    def self.update_product_path_value(product_path_value, binary_name, replacement_path)
      pattern = /.*#{Regexp.escape(binary_name)}/
      product_path_value.sub(pattern, replacement_path)
    end

    def self.remove_usedestinationartifacts_keys(file_path)
      MAX_CONFIGS.times do |config_index|
        break if PlistBuddy.get_value_of_key(file_path, ":TestConfigurations:#{config_index}").strip.empty?

        MAX_TARGETS.times do |target_index|
          test_target_key = ":TestConfigurations:#{config_index}:TestTargets:#{target_index}"
          break if PlistBuddy.get_value_of_key(file_path, test_target_key).strip.empty?
          break if PlistBuddy.get_value_of_key(file_path, "#{test_target_key}:UseDestinationArtifacts").strip.empty?

          BrowserStack.logger.info("[XCTestrunManager] Removing useDestinationArtifacts keys from xctestrun file at configuration:#{config_index} target:#{target_index}")
          test_bundle_path = PlistBuddy.get_value_of_key(file_path, "#{test_target_key}:TestBundleDestinationRelativePath").strip
          PlistBuddy.add_key_value_in_plist(file_path, "#{test_target_key}:TestBundlePath", "string", test_bundle_path) unless test_bundle_path.empty?
          PlistBuddy.delete_key_in_plist(file_path, "#{test_target_key}:UseDestinationArtifacts")
          PlistBuddy.delete_key_in_plist(file_path, "#{test_target_key}:TestBundleDestinationRelativePath")
          PlistBuddy.delete_key_in_plist(file_path, "#{test_target_key}:UITargetAppBundleIdentifier")
        end
      end
    end

    def self.update_xctestrun_with_enviromentvariable(device)
      file_path = XCTestrunManager.file_path(device)
      @format_version = PlistBuddy.get_value_of_key(file_path, XCTESTRUN_FORMAT_VERSION_KEY).to_s.strip
      case @format_version
      when V2_XCTESTRUN
        MAX_CONFIGS.times do |config_index|
          break if PlistBuddy.get_value_of_key(file_path, ":TestConfigurations:#{config_index}").strip.empty?

          MAX_TARGETS.times do |target_index|
            test_target_key = ":TestConfigurations:#{config_index}:TestTargets:#{target_index}"
            break if PlistBuddy.get_value_of_key(file_path, test_target_key).strip.empty?

            BrowserStack.logger.info("[XCTestrunManager][V2] Adding Envirobment Varibales to  target:#{test_target_key}")
            PlistBuddy.add_key_value_in_plist(file_path, "#{test_target_key}:EnvironmentVariables:SENSOR_MOCKER_HOST", "string", "http://#{SENSOR_MOCKER_HOST}")
            PlistBuddy.add_key_value_in_plist(file_path, "#{test_target_key}:EnvironmentVariables:IMAGE_INJECTION_PATH", "string", "xcuitest/inject-media")
          end
        end
      when V1_XCTESTRUN
        # Use plutil and jq to get test target keys
        json_str = `plutil -convert json -o - "#{file_path}" | jq -r 'del(.FormatVersion, .__xctestrun_metadata__) | keys[]'`
        test_targets = json_str.split("\n").reject(&:empty?)
        BrowserStack.logger.info("[XCTestrunManager] Test targets found in xctestrun file: #{test_targets}")

        test_targets.each do |test_target_key|
          BrowserStack.logger.info("[XCTestrunManager][V1] Adding Envirobment Varibales to  target:#{test_target_key}")
          PlistBuddy.add_key_value_in_plist(file_path, "#{test_target_key}:EnvironmentVariables:SENSOR_MOCKER_HOST", "string", "http://#{SENSOR_MOCKER_HOST}")
          PlistBuddy.add_key_value_in_plist(file_path, "#{test_target_key}:EnvironmentVariables:IMAGE_INJECTION_PATH", "string", "xcuitest/inject-media")
        end
      end
    end

    def self.update_xctestrun_with_usedestinationartifacts(file_path, app_bundle_id)
      MAX_CONFIGS.times do |config_index|
        break if PlistBuddy.get_value_of_key(file_path, ":TestConfigurations:#{config_index}").strip.empty?

        MAX_TARGETS.times do |target_index|
          test_target_key = ":TestConfigurations:#{config_index}:TestTargets:#{target_index}"
          break if PlistBuddy.get_value_of_key(file_path, test_target_key).strip.empty?

          @@retry_strategy = PlistBuddy.get_value_of_key(file_path, "#{test_target_key}:TestRepetitionPolicy:XCTRepetitionMode")
          BrowserStack.logger.info("[XCTestrunManager] Retry Strategy found at test target:#{test_target_key}")

          BrowserStack.logger.info("[XCTestrunManager] Updating xctestrun file at configuration:#{config_index} target:#{target_index}")

          PlistBuddy.delete_key_in_plist(file_path, "#{test_target_key}:DependentProductPaths")
          PlistBuddy.add_key_value_in_plist(file_path, "#{test_target_key}:UseDestinationArtifacts", "bool", "true")

          XCTestrunManager.replace_test_bundle_path(file_path, test_target_key)

          PlistBuddy.delete_key_in_plist(file_path, "#{test_target_key}:TestHostPath")

          XCTestrunManager.add_ui_target_bundle_id(file_path, test_target_key, app_bundle_id)

          PlistBuddy.delete_key_in_plist(file_path, "#{test_target_key}:UITargetAppPath")
          PlistBuddy.delete_key_in_plist(file_path, "#{test_target_key}:TestBundlePath")
        end
      end
    end

    def self.update_xctestrun_test_and_app_path(file_path, app_path, test_runner_path)
      MAX_CONFIGS.times do |config_index|
        break if PlistBuddy.get_value_of_key(file_path, ":TestConfigurations:#{config_index}").strip.empty?

        MAX_TARGETS.times do |target_index|
          test_target_key = ":TestConfigurations:#{config_index}:TestTargets:#{target_index}"
          break if PlistBuddy.get_value_of_key(file_path, test_target_key).strip.empty?

          @@retry_strategy = PlistBuddy.get_value_of_key(file_path, "#{test_target_key}:TestRepetitionPolicy:XCTRepetitionMode")
          BrowserStack.logger.info("[XCTestrunManager] Retry Strategy found at test target:#{test_target_key}")

          BrowserStack.logger.info("[XCTestrunManager] Updating app and test runner path in xctestrun file at configuration:#{config_index} target:#{target_index}")
          PlistBuddy.delete_key_in_plist(file_path, "#{test_target_key}:DependentProductPaths")
          PlistBuddy.delete_key_in_plist(file_path, "#{test_target_key}:TestHostPath")
          PlistBuddy.delete_key_in_plist(file_path, "#{test_target_key}:UITargetAppPath")
          PlistBuddy.delete_key_in_plist(file_path, "#{test_target_key}:PreferredScreenCaptureFormat")
          XCTestrunManager.add_ui_target_app_path(file_path, test_target_key, app_path)
          XCTestrunManager.add_test_host_path(file_path, test_target_key, test_runner_path, app_path)
          XCTestrunManager.add_preffered_screen_capture_format(file_path, test_target_key, "screenshots")
        end
      end
    end
  end
end
