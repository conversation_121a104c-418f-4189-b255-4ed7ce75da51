require 'set'
require 'json'
require 'fileutils'
require 'securerandom'

require_relative './xctestrun_summary'
require_relative './xctestrun_manager'
require_relative '../../../configuration'
require_relative '../../../utils/osutils'
require_relative '../../../utils/utils'
require_relative '../../../helpers/video_rec_manager'
require_relative '../../../../config/constants'
require_relative '../../../session/xctest_session'
require_relative '../../../../../mobile-common/frameworks_timeout_manager/app_automate_frameworks/xctest_timeout_manager'

module BrowserStack
  class XCTestrunSession < BrowserStack::XCTestSession # rubocop:todo Metrics/ClassLength
    TEST_SUCCESS = "** TEST EXECUTE SUCCEEDED **"
    TEST_FAILURE = "** TEST EXECUTE FAILED **"

    def start
      log_start "start"
      ##
      # XCTestrunSession need to inherit #start method from GrandParent class `Session`
      # Not using super here as it will inherit #start method from Parent `XCTestSession` class
      BrowserStack::Session.instance_method(:start).bind(self).call
      @summary_manager = BrowserStack::XCTestrunSummary.new(@params, @device_config)
      save_session_params_v2
      setup_params
      @summary_file_v2 = @summary_manager.setup_summary_v2(@params["build_id"], @params["automate_session_id"], test_id)
      setup_xctestrun
      process_exit_code = run_xctestsuite(@xctestrun_path)
      post_test_execution_actions(process_exit_code)
      log_finish "start"
    rescue Exception => e # rubocop:disable Lint/RescueException
      BrowserStack.logger.error("Error in XCTestrun Start: #{e.message}\n#{e.backtrace.join("\n")}")
      handle_test_execution_error(e)
    ensure
      stop
    end

    private

    def run_xctestsuite(xctestrun_path)
      log_start "run_xctestsuite"

      setup_time_components
      initialize_logs_stability_metrics unless should_split_log?
      setup_test_list
      @instru_log_file_size = begin
        File.size(instrumentation_file)
      rescue
        0
      end
      @summary_manager.send_setup_build_pusher_event(@build_id, @session_id, @params['user_device_name'])

      VideoRecManager.new(@device, @params, @params['wda_port']).start_rec if video_enabled?
      ensure_wda_not_running if Utils.is_wda_kill_flag_enabled?(@params) && video_enabled? && @is_v2_video_recording
      update_time_components("start")
      process_exit_code = invoke_xcodebuild_command(xctestrun_path)
      update_time_components("end")
      log_finish "run_xctestsuite"
      process_exit_code
    end

    def invoke_xcodebuild_command(xctestrun_path, retry_count = 0) # rubocop:disable Metrics/AbcSize
      log_start "invoke_xcode_command_#{retry_count}"

      result_bundle_dir = result_bundle_path if result_bundle_enabled?
      timeout = get_test_expire_time
      xcodebuid_cmd = "gtimeout -s KILL #{timeout} xcodebuild test-without-building"
      xcodebuid_cmd += " -destination \"id=#{@device}\""
      xcodebuid_cmd += " -xctestrun #{xctestrun_path}"
      xcodebuid_cmd += configuration_filters
      xcodebuid_cmd += xcode_retry_flags if xcuitest_sri_flow?
      xcodebuid_cmd += eval_test_identifiers_string
      xcodebuid_cmd += " -derivedDataPath \"#{derived_data_path}\""
      xcodebuid_cmd += " -resultBundlePath \"#{result_bundle_dir}\" -resultBundleVersion 3" if result_bundle_enabled?
      xcodebuid_cmd += " >> #{instrumentation_file} 2>&1"

      start_timeout_manager
      xcodebuild_process_start_time = Time.now.utc

      _, status = BrowserStack::OSUtils.execute(xcodebuid_cmd, true)

      xcodebuild_process_stop_time = Time.now.utc
      @xcodebuild_process_time = (xcodebuild_process_stop_time - xcodebuild_process_start_time).to_f.round(3)
      stop_timeout_manager
      log_finish "invoke_xcode_command_#{retry_count}"

      retry_count += 1
      if should_retry_test?("", retry_count) || should_retry_sri_execution?(retry_count)
        @test_meta_info["retries"] = retry_count
        @test_meta_info["retry_time"] = (@test_meta_info["retry_time"].to_i + @xcodebuild_process_time).round(2) if retry_count > 1

        if result_bundle_dir
          FileUtils.rm_rf(result_bundle_dir)
          FileUtils.rm_rf("#{result_bundle_dir}.xcresult")
        end

        # adding sleep so that the device gets time to recover from
        # bad state to reduce possibility of failure
        # in the worst case when 3 retries (and 1 try) happens we sleep
        # for total 1 + 2 + 3 seconds
        sleep retry_count
        status = invoke_xcodebuild_command(xctestrun_path, retry_count)
      end

      status
    end

    # error_type: Exception class name
    # Errors are divided into two seperate groups
    # 1. Error as a result of code/platform issues
    #    - These errors are considerd as Bstack error
    #    - error reasons are appended by substring `bstack`
    # 2. Error as a result of Xcode behaviour/User error
    #    - These errors are considered as User error
    #    - error reasons are appended by substring `xcode`
    def get_log_splitting_error_reason(error_type)
      case error_type
      when "InstruParseError"
        "instru-parse-exception-bstack"
      when "InstruParseEmpty"
        "instru-no-test-detected-#{user_issue? ? 'user' : 'xcode'}"
      when "SummaryFileTestsEmpty"
        "summaryfile-tests-empty-bstack"
      when "TestsValidationFailed"
        "instru-validation-fail-bstack"
      when "FileEmpty"
        "instru-file-empty-bstack"
      when "FileNotFound"
        "instru-file-not-found-bstack"
      else
        "log-unknown-exception-bstack"
      end
    end

    def user_issue?
      return true unless @test_meta_info.empty?

      # No Test Cases Executed in an non-empty instruLog file. AAP-12218
      return true unless @any_test_case_found

      false
    end

    def get_log_splitting_failure_reason(log_split_failures)
      return if log_split_failures.empty?

      log_split_failure_reasons = []
      log_split_failure_reasons.push("instru-partial-split-failure") if log_split_failures[XCTEST_INSTRU_LOG_SPLIT_FAILURES]
      log_split_failure_reasons.push("device-partial-split-failure") if log_split_failures[XCTEST_DEVICE_LOG_SPLIT_FAILURES]
      log_split_failure_reasons.push("video-partial-split-failure") if log_split_failures[XCTEST_VIDEO_LOG_SPLIT_FAILURES]
      log_split_failure_reason.push("crash-partial-split-failure") if log_split_failures[XCTEST_CRASH_LOG_SPLIT_FAILURES]

      # Log splitting doesn't stops if device/video log splitting throws an error.
      # Hence error thrown by these log processor won't be handled by #handle_log_processing_error
      # We want to track these errors as error_reasons too.
      log_split_failure_reasons.push("device-split-failure") if log_split_failures[XCTEST_DEVICE_LOG_SPLIT_ERROR]
      log_split_failure_reasons.push("video-split-failure") if log_split_failures[XCTEST_VIDEO_LOG_SPLIT_ERROR]
      log_split_failure_reasons.push("network-split-failure") if log_split_failures[XCTEST_NETWORK_LOG_SPLIT_ERROR]
      log_split_failure_reasons.push("network-split-fallback") if log_split_failures[XCTEST_NETWORK_LOG_SPLIT_FALLBACK]
      log_split_failure_reasons.push("crash-split-fallback") if log_split_failures[XCTEST_CRASH_LOG_SPLIT_ERROR]

      log_split_failure_reasons.to_s
    end

    def handle_log_processing_error(error)
      error_type = error.class.to_s
      error_reason = get_log_splitting_error_reason(error_type)
      BrowserStack.logger.info("#{error_type} occurred during log splitting: #{error.message}")
      BrowserStack::Zombie.push_logs("xctest-#{error_reason}", "Error: #{error.message[0, 100]}", { "session_id" => @session_id })
      send_error_reason_in_file(@callback_file, error_reason)
    end

    def send_log_split_feature_usage_data(failures_list, failures, feature, success)
      Utils.send_general_feature_usage_data_to_eds(@session_id, feature, failures_list[failures].nil?, GENRE_APP_AUTOMATE, failures_list[failures].to_json) if success
    end

    # Fatal error for instru log parsing/splitting is handled in rescue logic of #split_log
    # Fatal error for device log, video log splitting is tracked with log_split_failures hash
    def instrument_log_split_feature(log_split_failures)
      send_log_split_feature_usage_data(log_split_failures, XCTEST_INSTRU_LOG_PARSE_FAILURES, XCTEST_FEATURE_INSTRU_LOG_PARSE, true)
      send_log_split_feature_usage_data(log_split_failures, XCTEST_INSTRU_LOG_SPLIT_FAILURES, XCTEST_FEATURE_INSTRU_LOG_SPLIT, true)
      send_log_split_feature_usage_data(log_split_failures, XCTEST_CRASH_LOG_SPLIT_FAILURES, XCTEST_FEATURE_CRASH_LOG_SPLIT, log_split_failures[XCTEST_CRASH_LOG_SPLIT_ERROR].nil?)
      send_log_split_feature_usage_data(log_split_failures, XCTEST_DEVICE_LOG_SPLIT_FAILURES, XCTEST_FEATURE_DEVICE_LOG_SPLIT, log_split_failures[XCTEST_DEVICE_LOG_SPLIT_ERROR].nil?) if devicelogs_enabled?
      send_log_split_feature_usage_data(log_split_failures, XCTEST_VIDEO_LOG_SPLIT_FAILURES, XCTEST_FEATURE_VIDEO_LOG_SPLIT, log_split_failures[XCTEST_VIDEO_LOG_SPLIT_ERROR].nil?) if video_enabled?
      send_log_split_feature_usage_data(log_split_failures, XCTEST_NETWORK_LOG_SPLIT_FAILURES, XCTEST_FEATURE_NETWORK_LOG_SPLIT, log_split_failures[XCTEST_NETWORK_LOG_SPLIT_ERROR].nil?) if networklogs_enabled?
      send_log_split_feature_usage_data(log_split_failures, XCTEST_NETWORK_LOG_SPLIT_FALLBACK, XCTEST_NETWORK_LOG_SPLIT_FALLBACK, log_split_failures[XCTEST_NETWORK_LOG_SPLIT_FALLBACK].nil?) if networklogs_enabled?

      send_error_reason_in_file(@callback_file, get_log_splitting_failure_reason(log_split_failures))
    end

    def xctest_summary_without_log_split(process_exit_code, num_crash_reports = 0)
      test_status, nos_failures = process_instru_logs(process_exit_code)
      @summary_manager.update_test_summary_v2(@time_components, test_status, nos_failures, num_crash_reports)
    end

    def test_execution_retry_config
      if xcuitest_sri_flow?
        @params["retry_params"] || {}
      elsif @retry_strategy.to_s != ""
        { "retry_on_failure" => (@retry_strategy.to_s == "2") }
      else
        {}
      end
    end

    def split_log
      log_start "Logs_Split"
      mitmproxy_instance = networklogs_enabled? ? @mitmproxy : nil
      retry_config = test_execution_retry_config
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(instrumentation_file, @device, mitmproxy_instance, networklogs_enabled?, video_enabled?, devicelogs_enabled?, @session_id, @time_components, @total_session_time, retry_config)
      xctest_log_split_manager.process_logs
      BrowserStack.logger.info("Executed Test Data: #{JSON.parse(xctest_log_split_manager.test_details.to_json)} \n
                                  LogSplitting Issues: #{xctest_log_split_manager.log_split_errors.to_json}")
      log_split_zombie_data = { "split_issues" => xctest_log_split_manager.log_split_errors }
      BrowserStack::Zombie.push_logs("xctest-logsplit-info", '', { "session_id" => @session_id, "data" => log_split_zombie_data.to_json })

      eds_data = xctest_log_split_manager.split_time.merge({ hashed_id: @session_id })
      Utils.send_to_eds(eds_data, EdsConstants::APP_AUTOMATE_TEST_SESSIONS)

      instrument_log_split_feature(xctest_log_split_manager.log_split_failures)

      test_summary, summary_file_data = @summary_manager.generate_summary_log_split(xctest_log_split_manager.test_details, @build_id, @session_id, @time_components, @session_start_time, @total_session_time)
      raise SummaryFileTestsEmpty, "No tests in summary file" if summary_file_data && summary_file_data[:classes].empty?

      Utils.write_to_file(get_summary_path_v2, summary_file_data.to_json)

      log_finish "Logs_Split"

      [test_summary, summary_file_data]
    rescue => e
      handle_log_processing_error(e)
      raise "Error in splitting logs: #{e.message}"
    end

    def should_split_log?
      @params["singleRunnerInvocation"] == true || @params["log_split"] == true
    end

    def setup_xctestrun
      if xcuitest_sri_flow?
        filter_testlist(@params["test_params"])
        # Get the product_name
        xctest = @params["xctests"]&.first
        generate_xctestrun_xml(@params["test_framework"], xctest)
        @xctestrun_path = @xctest_xmlfile
      else
        @xctestrun_path, @retry_strategy = BrowserStack::XCTestrunManager.generate_xctestrun_for_testing(@device, @params["app_details_bundle_id"], @device_os_version, @downloaded_app_path, @downloaded_test_app_path)
        BrowserStack::XCTestrunManager.update_xctestrun_with_enviromentvariable(@device) if @params["enableCameraImageInjection"].to_s.downcase == "true"
      end
    end

    def post_test_execution_actions(process_exit_code)
      log_start "post_test_execution"

      parse_test_execution_logs

      if should_split_log?
        fetch_crash_logs
        summary_file_data = nil

        begin
          test_summary, summary_file_data = split_log
        rescue => e
          BrowserStack.logger.error("Error in splitting logs, Falling back to consolidated logs summary creation, #{e.message}")
          num_crash_reports = fetch_and_upload_crash_logs("")
          test_summary = xctest_summary_without_log_split(process_exit_code, num_crash_reports)
        end

        upload_logfiles("") #empty test name will upload logs at session level
        zip_and_upload_crash_logs(summary_file_data) if summary_file_data
      else
        num_crash_reports = fetch_and_upload_crash_logs("")
        test_summary = xctest_summary_without_log_split(process_exit_code, num_crash_reports)
        upload_logfiles(@session_id)
      end

      @summary_manager.update_callback_file(@callback_file, test_summary)
      @summary_manager.update_session_stats(@session_id, test_summary)

      log_finish "post_test_execution"
    rescue => e
      BrowserStack.logger.error("post xctest execution error: #{e.message}\n#{e.backtrace.join("\n")}")
      BrowserStack::Zombie.push_logs("xctest-post-execution-error", "Error: #{e.message[0, 100]}", { "session_id" => @session_id })
      send_error_reason_in_file(@callback_file, "error-post-execution")
    end

    def zip_and_upload_crash_logs(summary_file_data) # rubocop:disable Metrics/MethodLength, Metrics/AbcSize
      BrowserStack.logger.info("[crash logs] Zipping and Uploading crash logs summary_file_data: #{summary_file_data}")
      return unless summary_file_data && summary_file_data[:classes]

      total_crash_count = 0

      summary_file_data[:classes].each do |_class_name, class_data|
        next unless class_data && class_data["tests"]

        class_data["tests"].each do |test_name, test_details|
          crash_files = test_details[:crash_files] || []
          next if crash_files.empty?

          total_crash_count += test_details[:crash_logs_count]
          test_id = test_details[:test_id]
          crash_report_path = "/tmp/crash_report_#{test_id}.zip"

          BrowserStack.logger.info("[crash logs] Zipping crash logs for test: #{test_name}, crash_files: #{crash_files}")

          begin
            Zip::File.open(crash_report_path, Zip::File::CREATE) do |zipfile|
              crash_files.each do |file|
                zipfile.add(File.basename(file), file)
              rescue => e
                BrowserStack.logger.error("[crash logs]Failed to add crash file to zip: #{e.message}")
              end
            end
            BrowserStack.logger.info("[crash logs] Crash logs zipped successfully: #{crash_report_path}")
            s3_params = get_s3_params("crash_reports.zip", "", nil, test_id)
            BrowserStack.logger.info("[crash logs] Uploading crash logs for test: #{test_name}, s3_params: #{s3_params}")
            Utils.create_upload_request(crash_report_path, "crash-report", @device, s3_params, @server_config, @test_framework, @genre)
          rescue => e
            BrowserStack.logger.error("[crash logs]Failed to process crash reports: #{e.message}")
          ensure
            crash_files.each do |file|
              File.delete(file) if File.exist?(file)
            rescue => e
              BrowserStack.logger.error("[crash logs]Failed to delete crash file #{file}: #{e.message}")
            end
          end
        end
      end

      if total_crash_count > 0 && File.exist?(@callback_file)
        begin
          callback_hash = JSON.parse(File.read(@callback_file))
          callback_hash["crash_logs"] ||= 0
          callback_hash["crash_logs"] += total_crash_count
          Utils.write_to_file(@callback_file, callback_hash.to_json)
          BrowserStack.logger.info("[crash logs] Updated callback file with crash count: #{total_crash_count}")
        rescue => e
          BrowserStack.logger.error("[crash logs] Failed to update callback file with crash count: #{e.message}")
        end
      end
    end

    def result_bundle_path
      # xcodebuild cmd takes xcresult bundle path as `/path/bundlefilename`
      # xcresult file gets created in folder `/path/` with filename `bundlefilename.xcresult`
      # for xcuitest-flow path being used is `/module/class/test`, so result bundle created in folder `/module/class` with filename `test.xcresult`
      # method `merge_result_bundles` finds xcresult files in folder `/module/class/`
      # Adding dummy module, class and test will enable us in reusing the merge_result_bundles method
      "#{@result_bundle_directory}/dummyModule/dummyClass/dummyTest_#{Time.now.utc.to_i}"
    end

    def configuration_filters
      config_filter_command = ""
      return config_filter_command if @params["test_params"].nil?

      test_params = JSON.parse(@params["test_params"])

      test_params["only-test-configuration"].to_a.each do |param|
        config_filter_command += " -only-test-configuration \"#{param}\""
      end
      test_params["skip-test-configuration"].to_a.each do |param|
        config_filter_command += " -skip-test-configuration \"#{param}\""
      end

      config_filter_command
    end

    def setup_test_list
      log_start "setup_test_list"
      @only_testing_list = Set.new
      @skip_testing_list = Set.new
      @session_with_test_filters = false
      return if !xcuitest_sri_flow? || @params["test_params"].nil?

      test_params = JSON.parse(@params["test_params"])
      if test_params["is_dynamic_xcuitest"].to_s == "true" || test_params["only-testing"].to_a.any? || test_params["skip-testing"].to_a.any?
        @session_with_test_filters = true
        @testsuite_files.each do |filename|
          testlist = get_testlist(filename)
          testlist.to_a.each do |test_name|
            @only_testing_list << test_name
          end
        end
      end
      BrowserStack.logger.info("[#{@session_id}] only-testing: #{@only_testing_list.inspect}, skip-testing: #{@skip_testing_list.inspect}")
      log_finish "setup_test_list"
    rescue => e
      BrowserStack.logger.error("[#{@session_id}] Setting empty test identifier list. Error in setup_test_list: #{e.message}\n#{e.backtrace.join("\n")}")
      BrowserStack::Zombie.push_logs("xctest-sri-setup-test-list", "Error: #{e.message[0, 100]}", { "session_id" => @session_id })
    end

    def any_system_level_error_occured?(line)
      error_pattern = nil
      SYSTEM_LEVEL_PATTERNS.each do |key, pattern|
        error_pattern = key if line.match(pattern)
      end

      error_pattern = "runner_restarted" if line.match(NON_SYSTEM_LEVEL_PATTERNS["runner_restarted"])

      unless error_pattern.nil?
        @test_meta_info["bstack_retry"] = {} if @test_meta_info["bstack_retry"].nil?
        @test_meta_info["bstack_retry"][error_pattern] = @test_meta_info["bstack_retry"][error_pattern].to_i.succ
        return true
      end

      false
    end

    def analyse_tests_execution(retry_count) # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
      log_start "analyse_tests_execution_#{retry_count}"
      curr_test = nil
      executed_tests = Set.new
      errored_tests = Set.new
      only_testing_size = @only_testing_list.size
      skip_testing_size = @skip_testing_list.size

      instru_file = File.open(instrumentation_file, 'r')
      instru_file.seek(@instru_log_file_size, IO::SEEK_SET)
      instru_file.readlines.each do |line|
        if (test_match_meta = line.match(XCTEST_TEST_MATCH_REGEX))
          test_name, test_status, = test_match_meta.captures

          case test_status
          when "started"
            curr_test = format_test_name(test_name)
          when "passed", "failed", "skipped"
            executed_tests << curr_test
            curr_test = nil
          end

        elsif curr_test && any_system_level_error_occured?(line)
          executed_tests.delete(curr_test)
          errored_tests << curr_test
          curr_test = nil
        end
      end

      BrowserStack.logger.info("[#{@session_id}] executed_tests: #{executed_tests.inspect}, errored_tests: #{errored_tests.inspect}")

      if @session_with_test_filters
        # Remove executed_tests from @only_testing_list
        @only_testing_list -= executed_tests

        # Add errored_tests to @only_testing_list
        errored_tests.to_a.each do |errored_test|
          @only_testing_list << errored_test
        end

        @pending_tests = true unless @only_testing_list.empty?

      else
        # Remove errored_tests from @skip_testing_list
        @skip_testing_list -= errored_tests

        # Add executed_tests to @skip_testing_list
        executed_tests.to_a.each do |executed_test|
          @skip_testing_list << executed_test
        end

        @pending_tests = true unless errored_tests.empty?
      end

      if @pending_tests
        zombie_data = {
          "retry_count" => retry_count,
          "executed_tests" => executed_tests.size,
          "errored_tests" => errored_tests.size,
          "only_test_size" => only_testing_size,
          "skip_testing_size" => skip_testing_size
        }
        BrowserStack::Zombie.push_logs("xcodebuild-retry-bstack-sri", "", { "session_id" => @session_id, "data" => zombie_data })
      end

      BrowserStack.logger.info("[#{@session_id}] Filtered Test List for next Iteration. only_testing: #{@only_testing_list.inspect}, skip-testing: #{@skip_testing_list.inspect}")
      log_finish "analyse_tests_execution_#{retry_count}"
    rescue => e
      BrowserStack.logger.error("[#{@session_id}] Error Occurred while analysing test execution: #{e.message}\n#{e.backtrace.join("\n")}")
      BrowserStack::Zombie.push_logs("xctest-sri-analyse-test-exec", "Error: #{e.message[0, 100]}", { "session_id" => @session_id })
    ensure
      @instru_log_file_size = begin
        File.size(instrumentation_file)
      rescue
        0
      end
      instru_file&.close
    end

    def should_retry_sri_execution?(retry_count)
      return false if retry_count > MAX_TEST_RETRY_COUNT || session_timedout?

      @pending_tests = false
      if xcuitest_sri_flow? && bstack_retry_on_sri_enabled?

        analyse_tests_execution(retry_count)
        if @pending_tests
          add_bstack_error_retry_marker(retry_count)
          BrowserStack.logger.info("[#{@session_id}] Retrying Tests on SRI because pending_tests > 0. Added Browserstack Test Retry Marker")
          return true
        end
      end

      false
    end

    def eval_test_identifiers_string
      only_test_command = ""
      skip_test_command = ""

      @only_testing_list.to_a.each do |test_name|
        only_test_command += " -only-testing:\"#{test_name}\" "
      end

      @skip_testing_list.to_a.each do |test_name|
        skip_test_command += " -skip-testing:\"#{test_name}\" "
      end

      only_test_command + skip_test_command
    end

    # Convert "UITests.DashboardManageTests testCancelCard" to "UITests/DashboardManageTests/testCancelCard"
    def format_test_name(log_test_name)
      module_name = log_test_name.split(".").first
      class_name = log_test_name.split(".").last.split(" ").first
      test_name = log_test_name.split(".").last.split(" ").last

      "#{module_name}/#{class_name}/#{test_name}"
    end

    def add_bstack_error_retry_marker(retry_count)
      File.open(instrumentation_file, "a") do |file|
        file.puts "\n#{BSTACK_TEST_ERROR_RETRY_MARKER}-#{retry_count}\n"
      end
    end

    # Detection of failure test count logic doesn't work below Xcode 13
    #
    # Sample instrumentation logs for detecting failure test count:
    # Failing tests:
    # 	BudgetKeeperTests:
    # 		AccountsViewModelUnitTest.testAddNewAccount()
    # 	BudgetKeeperUITests:
    # 		AccountsViewUITest.testAddAccount()
    #
    # ** TEST EXECUTE FAILED **
    def process_instru_logs(exit_code)
      nos_failures = 0
      contains_failing_tests = false

      # default status
      status = "failed"
      failures = "NA"

      # Error code for empty xctestrun file and bad formatted xctestrun file is 70
      # it is handled by #is_test_errored? along with other xcode error
      return ["error", nos_failures] if is_test_errored?(exit_code)
      return ["timedout", nos_failures] if is_test_timedout?(exit_code) || test_expired?

      File.foreach(instrumentation_file) do |instru_line|
        instru_line.strip!
        if instru_line == TEST_SUCCESS
          status = "passed"
          failures = nos_failures
          break
        end
        if instru_line == TEST_FAILURE
          status = "failed"
          failures = nos_failures if nos_failures > 0
          break
        end

        contains_failing_tests = true if instru_line == "Failing tests:"

        nos_failures += 1 if contains_failing_tests && !instru_line.empty? && !instru_line.end_with?(":")
      end

      [status, failures]
    end

    def parse_test_execution_logs
      log_start "parse_execution_logs_for_errors"

      parse_instru_logs(@session_id)
      # TODO: Create methods here to parse device logs in future.

      log_finish "parse_execution_logs_for_errors"
    end

    def fetch_crash_logs
      num_crash_reports, = DeviceManager.crash_reports(@device, @session_id, nil, false) # Avoid crash reports zip
    end

    def update_time_components(event)
      case event
      when "start"
        @time_components[:xcodebuild][:start] = Time.now.utc
        @time_components[:video][:start_duration] = get_video_duration
        @time_components[:video][:device_start_time] = begin
          # Incase of intermittent device not found issues, set to Time.now
          DateTime.parse(IdeviceUtils.device_time(@device).to_s).to_time
        rescue
          Time.now.utc
        end
      when "end"
        @time_components[:xcodebuild][:end] = Time.now.utc
        @time_components[:video][:end_duration] = get_video_duration(@time_components[:xcodebuild][:start])
      end
    end

    # Ruby Hash, enumerates their values in order of insertion
    # Maintain the order of keys in the hash.
    def setup_time_components
      @time_components = {
        xcodebuild: { start: nil, end: nil },
        video: { start_duration: nil, end_duration: nil, device_start_time: nil }
      }
    end

    # xctestrun flow involves single command invocation, hence no concept of testName/testId
    # applicable here. Use test_identifer as a replacement, which is derived from session_id
    def test_id
      @test_id ||= "#{@session_id}#{get_md5(@session_id.to_s)}"
    end

    def derived_data_path
      @derived_data_path ||= File.join("/tmp", "#{@device}_xcuitest_derived_data")
    end

    def instrumentation_file
      @instrumentation_file ||= File.join("/tmp", "#{@device}_#{@test_framework}_instrumentation.log")
    end

    # list of all temp session generated files which should get deleted after session
    def generated_session_files
      list = super
      list.push(@xctestrun_path, derived_data_path, instrumentation_file)
      list
    end
  end
end
