module BrowserStack
  # Extract test details i.e testname, test start time, test duration , test status from instrumentation logs
  # When not able to pass some details of a test i.e (duration, start time) Use nearby test details.
  #   eg: duration = start time of next test - start time of current test.
  #   eg: start_time = start time of next test - duration of current test.
  #   eg: start_time = start time of previous test + duration of previous test.

  class XCTestInstruLogParse < XCTestLogProcess # rubocop:todo Metrics/ClassLength
    def initialize(log_split_manager)
      super(log_split_manager, log_split_manager.instrumentation_file)

      @final_test_status = nil
      @current_test = nil
      @current_offset = 0
      @current_instru_line_byte_size = 0
      @bstack_test_retry = 0

      # @test_state tracks the state of test execution lifecycle.
      # Test states and meanings:
      #   nil -> unknown
      #   started -> Test has been started (Test start log line detected i.e Test Case '-[BudgetKeeperTests.AccountsViewModelUnitTest testAccountsListEmpty]' started.)
      #   running -> Test is running (Test start time log line detected)
      #   passed/failed/skipped -> Test execution resulted test status
      #   end -> Test execution completed
      @test_state = nil
    end

    private

    # Test start sample line: Test Case '-[BudgetKeeperTests.AccountsViewModelUnitTest testAccountsListEmpty]' started.
    # Test end sample line: Test Case '-[BudgetKeeperTests.AccountsViewModelUnitTest testAccountsListEmpty]' passed (0.006 seconds).
    # Test start time sample line: t =     0.00s Start Test at 2022-12-15 19:58:33.826
    def process_log
      instru_file = File.new(@log_file)
      instru_file.each do |instru_line| # rubocop:disable Metrics/BlockLength
        # instru_file.pos returns the bytesize of logs from start of file till rightmost character of the current logline
        @current_offset = instru_file.pos
        @current_instru_line_byte_size = instru_line.bytesize

        if (test_match_meta = instru_line.match(XCTEST_TEST_MATCH_REGEX))
          test_name, test_status, test_duration = test_match_meta.captures

          case test_status
          when "started"
            @test_state = test_status
            handle_previous_test_with_no_end_logline
            initialize_curr_test(test_name)
          when "passed", "failed", "skipped"
            update_previous_incomplete_test_details
            update_curr_test_status(instru_line, test_name, test_status, test_duration)
            @test_state = test_status
          else
            updated_test_name = @bstack_test_retry > 0 ? "#{test_name}-#{@bstack_test_retry}" : test_name
            @log_split_manager.add_log_split_error(updated_test_name, XCTEST_UNKNOWN_TEST_STATUS)
            @log_split_manager.log("Unknown test status detected, test: #{test_name}, status: #{test_status}")
            @test_state = nil
          end

        elsif (test_start_time_meta = instru_line.match(XCTEST_TEST_START_TIME_REGEX))
          test_start_time = test_start_time_meta.captures.first
          update_curr_test_start_time(test_start_time)
          update_previous_incomplete_test_details
          @test_state = "running"
        elsif (final_test_status_match_meta = instru_line.match(XCTEST_FINAL_STATUS_MATCH_REGEX))
          @final_test_status = final_test_status_match_meta.captures.first.to_s
          @test_state = "end"
        elsif instru_line.include?(BSTACK_TEST_ERROR_RETRY_MARKER)
          retry_count = get_retry_count_from_retry_marker(instru_line)
          @bstack_test_retry = retry_count
        else
          @test_state = nil
        end
      end

      # Capture the very last test present in instrumentation that had missing test end logline
      handle_previous_test_with_no_end_logline
      update_first_and_last_test_instru_log_boundary

      # Ensure that there are no gaps between 2 tests' bounds
      fill_gaps_instru_logs_bounds

      # Test that retries will have seperate test status for each iteration.
      # Calculate final status using all iteration status and final execution status
      process_retry_test_status
    end

    def initialize_curr_test(test_name)
      @current_test = {
        "name" => test_name,
        "duration" => nil,
        "start_time" => nil,
        "status" => nil,
        "instru_log_boundary" => {
          "start" => @current_offset - @current_instru_line_byte_size,
          "end" => nil
        }
      }

      if @bstack_test_retry > 0
        @current_test["name"] = "#{test_name}-#{@bstack_test_retry}"
        @current_test["original_test_name"] = test_name
      end

      @current_test
    end

    def update_curr_test_status(log_line, test_name, test_status, test_duration)
      unless @current_test
        handle_missing_test_start(test_name, test_status, test_duration)
        return
      end

      if test_name != @current_test["name"] && @current_test["name"] != "#{test_name}-#{@bstack_test_retry}"
        handle_start_and_end_mismatch(test_name, test_status, test_duration)
        return
      end

      # Case where test start logline is present but test start time not present
      # While marking test end, calculate missing start time of current parsed test using previous parsed test data
      handle_missing_curr_test_start_time

      if @bstack_test_retry > 0
        bstack_retried_test = "#{test_name}-#{@bstack_test_retry}"
        if @test_details[bstack_retried_test]
          handle_test_retries(bstack_retried_test, test_status, test_duration)
        else
          handle_new_test(bstack_retried_test, test_status, test_duration)
        end
      elsif @test_details[test_name]
        handle_test_retries(test_name, test_status, test_duration)
      else
        handle_new_test(test_name, test_status, test_duration)
      end

      @current_test = nil
    end

    def handle_new_test(test_name, test_status, test_duration)
      @log_split_manager.log("New test detected #{test_name}")
      @current_test["status"] = test_status
      @current_test["duration"] = test_duration.to_f.round(3) if test_duration
      @current_test["instru_log_boundary"]["end"] = @current_offset

      # If between a test start logline and test end logline there is no other log then that test is considered as Unit Tests
      # example log pattern:
      #   Test Case '-[UnitTests.ApiCookieTests test_APICookie_clearSessionCookies]' started.
      #   Test Case '-[UnitTests.ApiCookieTests test_APICookie_clearSessionCookies]' passed (0.002 seconds).
      #   Test Case '-[UnitTests.ApiCookieTests test_APICookie_ReadCookieValue]' started.
      #   Test Case '-[UnitTests.ApiCookieTests test_APICookie_ReadCookieValue]' passed (0.001 seconds).

      # @test_state is "started" if previous logline was test start logline
      # current method gets executed when test end log line appears
      # If test state at this point is "started", means no other logline were preset in between.
      # In case there is logline in between then test_state will be altered from "started" to "running" or nil
      @current_test["is_unit_test"] = true if @test_state == "started"

      @test_details[test_name] = @current_test
    end

    def update_curr_test_start_time(test_start_time)
      return unless @current_test

      test_start_time_timestamp = begin
        DateTime.parse(test_start_time.to_s).to_time
      rescue
        nil
      end

      if test_start_time_timestamp.nil?
        @log_split_manager.log("Timestamp conversion error, test_start_time parsed: #{test_start_time}, test_details: #{@current_test}")
        return
      end

      # In case of retries, consider start time of first iteration
      @current_test["start_time"] = test_start_time_timestamp if @test_details[@current_test["name"]].nil?
    end

    # Store the status as an array element which gets processed in the end
    def handle_test_retries(test_name, test_status, test_duration)
      @log_split_manager.log("Test #{test_name} is already been executed, treating it as test retries")
      if @test_details[test_name]["status"].is_a?(Array)
        @test_details[test_name]["status"] << test_status
      else
        @test_details[test_name]["status"] = [@test_details[test_name]["status"]] << test_status
      end

      # Add up individual execution duration
      @test_details[test_name]["duration"] = (@test_details[test_name]["duration"].to_f + test_duration.to_f).round(3) if test_duration && @test_details[test_name]["duration"]
      @test_details[test_name]["instru_log_boundary"]["end"] = @current_offset
    end

    def update_previous_incomplete_test_details
      update_previous_test_missing_duration
      update_previous_test_missing_start_time
    end

    # Test captured as started not matching with test ended
    # T1 has test end missing and T2 has test start missing
    # TODO: T1 start_time not getting populated
    # TODO: T1 and T2 both both will have same instru log boundary
    def handle_start_and_end_mismatch(test_name, test_status, test_duration)
      @log_split_manager.log("Test #{test_name} not matching with started test #{@current_test['name']}")
      updated_test_name = @bstack_test_retry > 0 ? "#{test_name}-#{@bstack_test_retry}" : test_name
      @log_split_manager.add_log_split_error(updated_test_name, XCTEST_TEST_START_END_MISMATCH)

      handle_previous_test_with_no_end_logline
      handle_missing_test_start(test_name, test_status, test_duration, true)
    end

    # At the start of test a template of test details gets intiated
    # At the end of test template test details gets added to final @test_details hash
    # In case where test end logline is not present,
    # this method make sure that template test gets added to final @test_details hash
    def handle_previous_test_with_no_end_logline
      return unless @current_test

      @log_split_manager.log("No status detected for test: #{@current_test['name']}")
      @log_split_manager.add_log_split_error(@current_test["name"], XCTEST_PREV_TEST_STATUS_MISSING)

      @current_test["instru_log_boundary"]["end"] = @current_offset - @current_instru_line_byte_size
      @test_details[@current_test["name"]] = @current_test
      @current_test = nil
    end

    # Use instru log end bounds of previous test
    # start bound should be 0 in case no previous test available
    def update_instru_log_start_bound(test_mismatch)
      previous_test_details = last_test_details
      return unless @current_test

      current_test_instru_log_start_bound = previous_test_details&.dig("instru_log_boundary", "end") || 0

      # In case of test mismatch, previous test end_instru_bound is already updated with current test start_instru_bound
      # Updating current test instru_start_bound with previous test end_instru_bound will lead to same value
      # Update start bound with the start bound of previous test, T1 and T2 will have same instru log boundary.
      current_test_instru_log_start_bound = previous_test_details&.dig("instru_log_boundary", "start") || 0 if test_mismatch

      @log_split_manager.log("Updating start instru log boundary of #{@current_test['name']} with #{current_test_instru_log_start_bound} from previous test: #{previous_test_details&.dig('name')}")
      @current_test["instru_log_boundary"]["start"] = current_test_instru_log_start_bound
      @log_split_manager.add_log_split_error(@current_test["name"], XCTEST_TEST_START_INSTRU_BOUND_MISSING)
    end

    # Operations done when start time of test is missing.
    #
    # Parameters:
    # - test_name (String): Fully qualified test name parsed from instru log.
    # - test_status (String): Status of test execution
    # - test_duration (Float): Test execution duration
    # - test_mismtach (Boolean): Whether this method called when start and end of test were not matching
    def handle_missing_test_start(test_name, test_status, test_duration, test_mismatch = false)
      @log_split_manager.log("Test #{test_name} have missing test start logline, creating a test entry using test status logline")
      initialize_curr_test(test_name)

      @log_split_manager.add_log_split_error(@current_test["name"], XCTEST_TEST_START_MISSING)

      # test start time only gets captured when test start gets captured
      handle_missing_curr_test_start_time
      update_instru_log_start_bound(test_mismatch)
      update_curr_test_status("", test_name, test_status, test_duration)
    end

    # Current test start time and previous test duration can be used to calculate previous test start time
    def update_previous_test_missing_start_time
      previous_test_details = last_test_details
      return unless previous_test_details && !previous_test_details["start_time"] && previous_test_details["duration"]

      previous_test_start_time = begin
        ((@current_test["start_time"] - previous_test_details["duration"]))
      rescue
        @log_split_manager.log("Error in calculating previous test start time using current start time, test: #{@current_test['name']}")
        @log_split_manager.add_log_split_error(@current_test["name"], XCTEST_PREV_TEST_START_TIME_ERROR)
        nil
      end

      @test_details[@test_details.keys.last]["start_time"] = previous_test_start_time

      @log_split_manager.add_log_split_error(@current_test["name"], XCTEST_PREV_TEST_START_TIME_MISSING)
    end

    # previous parsed test duration can be calculated using current test details
    def update_previous_test_missing_duration
      previous_test_details = last_test_details
      return unless previous_test_details && !previous_test_details["duration"] && previous_test_details["start_time"] && @current_test&.dig("start_time")

      previous_test_duration = begin
        ((@current_test["start_time"] - previous_test_details["start_time"])).abs.to_f.round(3)
      rescue => e
        @log_split_manager.log("Error in calculating previous test duration using current start time, test: #{@current_test['name']}, error: #{e.message}")
        nil
      end

      @test_details[@test_details.keys.last]["duration"] = previous_test_duration
      @log_split_manager.add_log_split_error(@current_test["name"], XCTEST_PREV_TEST_DURATION_MISSING)
    end

    # when marking test end, if start_time is not present for the test
    # Sum of start_time and duration of previous test is current test start_time
    def handle_missing_curr_test_start_time
      return unless @current_test && !@current_test["start_time"]

      previous_test_details = last_test_details
      @log_split_manager.log("start_time logline not present, using last test details: #{previous_test_details}")
      return unless previous_test_details && previous_test_details["duration"] && previous_test_details["start_time"]

      current_test_start_time = begin
        ((previous_test_details["start_time"] + previous_test_details["duration"]))
      rescue => e
        @log_split_manager.log("Error while calculating test start time using previous test details #{e.message}")
        nil
      end

      @log_split_manager.log("Updating start_time of #{@current_test['name']} with #{current_test_start_time}")
      @current_test["start_time"] = current_test_start_time

      @log_split_manager.add_log_split_error(@current_test["name"], XCTEST_TEST_START_TIME_MISSING)
    end

    # Instru log for first test should start from 0 and last test should cover log till end
    # Duration of last test, if null, use the xcodebuild end time as upper bound
    def update_first_and_last_test_instru_log_boundary
      return if @test_details.empty?

      first_test = @test_details.keys.first
      @test_details[first_test]["instru_log_boundary"]["start"] = 0

      last_test = @test_details.keys.last
      @test_details[last_test]["instru_log_boundary"]["end"] = @current_offset

      if @test_details[last_test]["start_time"] && !@test_details[last_test]["duration"]
        test_execution_end_time = @log_split_manager.time_components[:xcodebuild][:end]
        last_test_start_time = sanitized_time(@test_details[last_test]["start_time"])

        @test_details[last_test]["duration"] = (test_execution_end_time - last_test_start_time).to_f.round(3)
      end
    end

    # Algorithm
    # For retry_strategy = retry_test_on_failure:
    #   The final status is the status of the last iteration.
    # For retry_strategy = retry_test_until_failure or no_strategy:
    #   Atleast one failure iteration causes entire test status to be failure.
    def process_retry_test_status
      @log_split_manager.log("Processing retry test statuses")

      retry_params = @log_split_manager.retry_config
      @test_details.each do |_test, test_detail|
        next unless test_detail["status"].is_a?(Array)

        test_status = test_detail["status"].first

        test_status = if retry_params["retry_on_failure"].to_s == "true"
                        test_detail["status"].last
                      else
                        test_detail["status"].any?("failed") ? "failed" : "passed"
                      end

        test_detail["status"] = test_status
      end
    end

    # Check if Gap = (next_test.start - curr_test.end > 1)
    # Update curr_test.end is just before next_test.start
    def fill_gaps_instru_logs_bounds
      @log_split_manager.log("Filling in instru_logs bounds gaps across tests.")
      @test_details.each_with_index do |(_test, test_detail), index|
        curr_test_detail = test_detail
        next_test_detail = next_test_details(index)

        curr_test_detail["instru_log_boundary"]["end"] = next_test_detail["instru_log_boundary"]["start"] - 1 if next_test_detail && next_test_detail["instru_log_boundary"]["start"] - curr_test_detail["instru_log_boundary"]["end"] > 1
      end
    end

    #***************************** test details validation *****************************************
    # Mark garbage value as nil to avoid logsplitting
    def update_corrupt_duration(test_name)
      @log_split_manager.log("Updating duration of #{test_name} to nil from #{@test_details[test_name]['duration']}")
      @test_details[test_name]["duration"] = nil
    end

    def test_duration_valid?(test_detail)
      duration = test_detail["duration"]
      unless duration.is_a?(Numeric) && duration >= 0 && duration <= @log_split_manager.total_session_time
        update_corrupt_duration(test_detail["name"])
        return false
      end

      true
    end

    def test_details_consistent?(test_detail, next_test_detail)
      return true unless next_test_detail && next_test_detail["start_time"]

      start_time = test_detail["start_time"]
      duration = test_detail["duration"]
      return false if start_time.nil? || duration.nil?

      # performs epoch comparisons at second level precision
      unless DateTime.parse(start_time.to_s).to_time.to_i + duration.to_i <= DateTime.parse(next_test_detail["start_time"].to_s).to_time.to_i
        update_corrupt_duration(test_detail["name"])
        return false
      end

      true
    end

    def instrument_test_details_with_nil_values(test_detail)
      test_detail_property_with_nil_value = test_detail.select { |_, value| value.nil? }.keys
      test_detail_property_with_nil_value.each do |property|
        @log_split_manager.update_log_split_failures_count(XCTEST_INSTRU_LOG_PARSE_FAILURES, property)
      end
    end

    def track_test_details_with_instru_log_split_failure
      @log_split_manager.update_log_split_failures_count(XCTEST_INSTRU_LOG_SPLIT_FAILURES)
    end

    def test_details_having_no_nil_values?(test_detail)
      test_detail.values.none?(&:nil?)
    end

    def test_start_time_valid?(test_detail)
      begin
        DateTime.parse(test_detail["start_time"].to_s)
      rescue
        return false
      end

      true
    end

    def instru_log_bounds_valid?(test_detail)
      valid = false
      instru_log_boundary = test_detail["instru_log_boundary"]
      valid = !instru_log_boundary.nil?
      valid = !(instru_log_boundary["start"].nil? || instru_log_boundary["end"].nil?) if valid
      valid = !(!instru_log_boundary["start"].is_a?(Integer) || !instru_log_boundary["end"].is_a?(Integer)) if valid
      valid = instru_log_boundary["start"] <= instru_log_boundary["end"] if valid
      track_test_details_with_instru_log_split_failure unless valid

      valid
    end

    # Test detail validation fails when even one check fails for UI Tests
    # For Unit tests, test start time validation is ignored.
    def test_detail_valid?(test_detail, next_test_detail)
      valid = false

      valid = test_start_time_valid?(test_detail)
      valid &= test_details_consistent?(test_detail, next_test_detail)
      valid &= test_details_having_no_nil_values?(test_detail)

      # If the test is "XCTest unit test" then mark the validation success even
      # if start time is not present.
      valid = true if test_detail["is_unit_test"]
      valid &= test_duration_valid?(test_detail)
      valid &= instru_log_bounds_valid?(test_detail)

      valid
    end

    def validate_test_details
      @log_split_manager.log("Validating test details")
      @test_details.each_with_index do |(_test, test_detail), index|
        unless test_detail_valid?(test_detail, next_test_details(index))
          @log_split_manager.add_log_split_error(test_detail["name"], XCTEST_TEST_VALIDATION_FAILED)
          instrument_test_details_with_nil_values(test_detail)
          next
        end

        # mark test details valid if atleast a single test is valid
        # if none of test details is valid then fall back to xctestplan summary generation
        @log_split_manager.test_details_valid = true
      end
    end
    #***************************** test details validation *****************************************

    def update_log_split_details
      super
      @log_split_manager.final_test_status = @final_test_status
    end

    def get_retry_count_from_retry_marker(line)
      line.split("-").last.to_i
    rescue
      0
    end
  end
end
