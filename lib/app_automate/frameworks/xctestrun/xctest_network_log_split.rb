require_relative '../../../utils/utils'

module BrowserStack
  class XCTestNetworkLogSplit < XCTestLogProcess
    def initialize(log_split_manager)
      @mitmproxy = log_split_manager.mitmproxy_instance
      super(log_split_manager, @mitmproxy.dumpfile)
    end

    def process_log
      contains_start_time = false
      test_details_file = nil
      @fallback_happened = false
      @test_details.each_with_index do |(test, _detail), _index|
        @test_details[test]["network_log_boundary"] = { "start" => nil, "end" => nil }
        contains_start_time ||= @test_details[test]["start_time"]
      end

      MitmProxy.stop_proxy(@log_split_manager.device, @log_split_manager.session_id, "xcuitest")

      if File.zero?(@log_file)
        fallback_nw_logs("DumpFile is empty")
      elsif !contains_start_time
        fallback_nw_logs("No Tests contain start_time")
      else
        test_details_file = XCTestNetworkLogSplit.temp_test_details_file(@log_split_manager.session_id)
        @log_split_manager.log("Creating temporary test_details file at: #{test_details_file}")
        Utils.write_to_file(test_details_file, @test_details.to_json)

        @log_split_manager.log("Triggering split_flow_to_har script to split network logs")
        @mitmproxy.convert_flow_to_har(@mitmproxy.dumpfile, test_details_file)

        test_details_after_split = read_test_details(test_details_file)
        @test_details = test_details_after_split unless test_details_after_split.empty?
      end
    end

    def validate_test_details
      @log_split_manager.log("Validating network log split details")

      @valid_tests = 0
      @test_details.each do |_test, test_detail|
        # When fallback is triggered, treat it as valid.
        # Because we neither want to trigger another fallback nor do we want to track it as a failure.
        if network_log_boundary_valid?(test_detail) || @fallback_happened
          @valid_tests += 1
          next
        end

        @log_split_manager.update_log_split_failures_count(XCTEST_NETWORK_LOG_SPLIT_FAILURES)
        @log_split_manager.add_log_split_error(test_detail["name"], XCTEST_NETWORK_LOG_BOUNDARY_NOT_VALID)
      end
    end

    def post_validation_steps
      # Fallback if atleast 1 test's nwLogs bounds are invalid
      # Assuming any invalid bound's effect will trickle down to subsequent tests
      return if @valid_tests >= @test_details.keys.count

      fallback_nw_logs("Atleast one test contain invalid bounds")
    end

    def validate_log_file
      return if @log_file.empty?

      raise FileNotFound, "Log file: #{@log_file} not found" unless File.exist?(@log_file)
    end

    def self.temp_test_details_file(session_id)
      "/tmp/test_details_#{session_id}.json"
    end

    private

    def fallback_nw_logs(error_reason)
      return if @fallback_happened

      @fallback_happened = true
      @log_split_manager.log("Fallback to consolidated nw logs")
      BrowserStack::Zombie.push_logs("xctest-network-split-fallback", error_reason, { "session_id" => @log_split_manager.session_id })
      @log_split_manager.update_log_split_failures_count(XCTEST_NETWORK_LOG_SPLIT_FALLBACK)

      # Truncates any partial har file generation
      File.truncate(@mitmproxy.harfile, 0) if File.exist?(@mitmproxy.harfile)
      @mitmproxy.convert_flow_to_har
      update_consolidated_bounds
    rescue => e
      @log_split_manager.log("Error Occured in nwLogs fallback: #{e.message}, #{e.backtrace}")
      BrowserStack::Zombie.push_logs("xctest-network-split-fallback-error", e.message[0, 100], { "session_id" => @log_split_manager.session_id })
    end

    def update_consolidated_bounds
      @log_split_manager.log("Updating nwLogs bounds for every test with nil values")

      @test_details.each_with_index do |(test, _detail), _index|
        @test_details[test]["network_log_boundary"] = { "start" => nil, "end" => nil }
      end
    end

    def read_test_details(file_name)
      JSON.parse(File.read(file_name))
    rescue => e
      @log_split_manager.log("Could not read test_details file with nwLogs: #{e.message}")
      {}
    end

    def network_log_boundary_valid?(test_detail)
      boundary = test_detail["network_log_boundary"]
      return false if boundary.nil?
      return false unless boundary['start'].is_a?(Numeric) && boundary['start'] >= 0 && boundary['end'].is_a?(Numeric) && boundary['end'] >= 0
      return false if (boundary['end'].to_i - boundary['start'].to_i) < XCTEST_NETWORK_LOG_BASE_SIZE

      true
    end
  end
end
