require 'English'
require 'appium_lib'
require 'dotenv/load'
require 'fileutils'
require 'time'
require 'timeout'

require_relative '../../config/constants'
require_relative '../../server/iphone'
require_relative '../appium_server'
require_relative '../configuration'
require_relative '../erb_binding'
require_relative '../helpers/experiments_helper'
require_relative '../helpers/wda_client'
require_relative '../helpers/browserstack_app_helper'
require_relative '../ios_influxdb_client'
require_relative '../network_simulator'
require_relative '../privoxy_manager'
require_relative '../utils/configuration_profiles_manager'
require_relative '../utils/devicectl'
require_relative '../utils/pymobiledevice'
require_relative '../utils/http_utils'
require_relative '../utils/idevice_utils'
require_relative '../utils/ios_mdm_service_client'
require_relative '../utils/osutils'
require_relative '../utils/utils'
require_relative '../utils/wda_version'
require_relative '../utils/web_driver_agent'
require_relative '../utils/xcode_utils'
require_relative '../utils/zombie'
require_relative 'check_plist'
require_relative '../utils/mdm_profile_restrictions'
require_relative '../utils/apple_tv_utils'
require_relative '../device_setup/full_cleanup/mdm_cleanup_manager'
require_relative '../wrappers/cfg_util'
require_relative '../utils/custom_mdm_manager'

PRIVOXY_TEST_URL = 'http://config.privoxy.org'

module BrowserStack
  class CheckDevice # rubocop:todo Metrics/ClassLength
    attr_reader :device_state

    def initialize(device, redis_client)
      @device = device
      @redis_client = redis_client
      @server_config = Configuration.new.all
      DeviceManager.configure @server_config
      @influxdb_client = BrowserStack::IosInfluxdbClient.new(BrowserStack.logger)
      @device_state = DeviceState.new(@device)
    end

    def lockdown_file
      "/tmp/#{@device}_lockdownd"
    end

    def ensure_ios_webkit_debug_proxy_running_plist(username, uid, port, logging_root) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
      cp = CheckPlist.new(
        "ios_webkit_debug_proxy_#{port}",
        ServiceType.SystemService,
        ['/bin/bash',
         '-c',
         "gstdbuf -oL #{OSUtils.which('ios_webkit_debug_proxy')} -u #{uid}:#{port} | xargs -L1 echo [$(date -u)]"],
        "#{logging_root}/ios_webkit_debug_proxy_#{uid}.log",
        "#{logging_root}/ios_webkit_debug_proxy_#{uid}.log",
        username
      )
      begin
        cp.update
      rescue => e
        BrowserStack.logger.info e.message
        raise MobileCheckException, "webkit_proxy_plist_setup_failure"
      end

      begin
        raise "IOS Webkit Debug Proxy error port: #{port}" if HttpUtils.test_url_code("http://localhost:#{port}/") != 200
      rescue Errno::ECONNREFUSED => e
        BrowserStack.logger.error e.message
        if OSUtils.is_process_running?('ios_webkit_debug_proxy', port.to_s)
          restart_ios_webkit_debug_proxy(username, uid, port, "is already running")
          BrowserStack.logger.info("Manual Step required: Ensure 'Setting > Safari > Advanced > Web Inspector' is switched to ON for #{uid}")
          if device_state.enable_safari_web_inspector_file_older_than_days?(CLEANUP_STEPS_FREQ[:enable_safari_web_inspector])
            # Cleanup has the automation to enable safari web inspector
            device_state.touch_cleanup_requested_file
            # Remove the state file to run safari web inspector automation
            device_state.remove_enable_safari_web_inspector_file

            BrowserStack.logger.info('Requested cleanup to enable safari web inspector')
            Zombie.push_logs("device-check-requested-web-inspector-enable", '', { device: @device })
            return
          end

          # TEMP code to kill stale processes on devices
          BrowserStack.logger.info('Killing stale ios_webkit_debug_proxy processes for this device')
          OSUtils.kill_duplicate_stale_processes("ios_webkit_debug_proxy", uid)

          raise MobileCheckException, "manual step required: Enable Safari Web Inspector"
        else
          needs_safari_web_inspector = false
          cmd_output = `gtimeout 2 ios_webkit_debug_proxy -u #{uid}:#{port}`
          needs_safari_web_inspector = cmd_output.include? 'must have the inspector enabled'

          if needs_safari_web_inspector
            if device_state.enable_safari_web_inspector_file_older_than_days?(CLEANUP_STEPS_FREQ[:enable_safari_web_inspector])
              # Cleanup has the automation to enable safari web inspector
              device_state.touch_cleanup_requested_file
              # Remove the state file to run safari web inspector automation
              device_state.remove_enable_safari_web_inspector_file

              BrowserStack.logger.info('Cleanup requested to enable safari web inspector')
              Zombie.push_logs("device-check-requested-web-inspector-enable", '', { device: @device })
              return
            end
            raise MobileCheckException, "manual step required: Enable Safari Web Inspector"
          else
            BrowserStack.logger.error("Loading plist as ios_webkit the process is not running port: #{port} and uid : #{uid}")
            CheckPlist.load_service("ios_webkit_debug_proxy_#{port}", ServiceType.SystemService, username)
            raise MobileCheckException, "ios_webkit not loaded"
          end
        end
      rescue Net::ReadTimeout => e
        restart_ios_webkit_debug_proxy(username, uid, port, "could be hanged running")
        raise MobileCheckException, "ios_webkit not loaded"
      rescue => e
        BrowserStack.logger.error("could not connect to ios_webkit_debug_proxy for uid: #{uid} and port : #{port}")
        BrowserStack.logger.error e.backtrace.join("\n")
        BrowserStack.logger.info('Killing stale ios_webkit_debug_proxy processes for this device')
        OSUtils.kill_duplicate_stale_processes("ios_webkit_debug_proxy", uid)
        restart_ios_webkit_debug_proxy(username, uid, port, "gave an invalid response")
        raise MobileCheckException, "ios_webkit error"
      end
    end

    def devtools_proxy_server_running?(port)
      proto = port == 443 ? 'https' : 'http'
      url = "#{proto}://localhost:#{port}"

      # https://github.com/browserstack/mobile-devtools/blob/9da4a1db42f6c6fe17f089566fdd045ab68db062/devtools-proxy/app.js#L7-L9
      devtools_proxy_server_default_response = "There's no sun in the shadow of the wizard"

      begin
        conn = Faraday.new url, ssl: { verify: false }
        response = conn.get("/")
        unless response.status == 200 && response.body.to_s.match(/#{devtools_proxy_server_default_response}/i)
          BrowserStack.logger.debug("Devtools Proxy server response = #{response}")
          raise "Devtools Proxy Server not running"
        end
      rescue => e
        BrowserStack.logger.error("Unable to connect to Devtools Proxy")
      end

      BrowserStack.logger.info("Devtools Proxy Server is running")
    end

    def restart_ios_webkit_debug_proxy(username, uid, port, reason)
      BrowserStack.logger.error("BEGIN Reloading plist as ios_webkit_debug_proxy process #{reason} for uid: #{uid} and port : #{port}")
      CheckPlist.restart_service("ios_webkit_debug_proxy_#{port}", ServiceType.SystemService, username)
      BrowserStack.logger.error("END   Reloading plist as ios_webkit_debug_proxy process #{reason} for uid: #{uid} and port : #{port}")
    end

    def ensure_webdriveragentrunner_version(uid, config, default_appium_version)
      if config && !config['current_appium_version'].to_s.eql?(default_appium_version)
        BrowserStack.logger.info("Uninstalling WebDriverAgentRunner uninstalling for #{uid}")
        begin
          OSUtils.execute("#{IOS_DEPLOY} --id #{uid} --uninstall_only -1 com.apple.test.WebDriverAgentRunner-Runner")
          raise 'Failed uninstall' unless OSUtils.execute("#{IOS_DEPLOY} --id #{uid} -B | grep com.apple.test.WebDriverAgentRunner-Runner").to_s.empty?
        rescue => e
          BrowserStack.logger.error("could not uninstall WebDriverAgentRunner for uid: #{uid}")
          BrowserStack.logger.error e.backtrace.join("\n")
          raise MobileCheckException, "uninstall_webdriver_agent error"
        end
      end
    end

    def check_privoxy_running(proxy_options, default_privoxy_template)
      # Test that privoxy server is running using special test url
      response = HttpUtils.get_response(URI.parse(PRIVOXY_TEST_URL), nil, proxy_options)
      response_code = response.code.to_i
      raise 'Did not receive default template from privoxy config url' unless response_code == 200 && response.body.to_s.match(/#{default_privoxy_template}/i)
    end

    def check_internet_connection(proxy_options)
      # Check that internet can be reached through proxy
      response_code = HttpUtils.test_url_code('http://www.google.com', nil, proxy_options)
      raise 'Could not connect to internet' if response_code != 200
    end

    def ensure_privoxy_running_plist(username, server_config, config, logging_root) # rubocop:todo Metrics/MethodLength
      privoxy_conf_dir = server_config["privoxy_conf_dir"]
      privoxy_port = config["selenium_port"].to_i + server_config["privoxy_listen_port_offset"].to_i
      privoxy_default_template_content = server_config["privoxy_templates_content"].to_s

      cp = CheckPlist.new(
        "privoxy_#{privoxy_port}",
        ServiceType.UserService,
        [
          ENV['PRIVOXY'],
          '--no-daemon',
          File.join(privoxy_conf_dir, "privoxy_conf_#{privoxy_port}")
        ],
        "#{logging_root}/privoxy_#{@device}.log",
        "#{logging_root}/privoxy_#{@device}.log",
        username
      )
      begin
        cp.update
      rescue => e
        BrowserStack.logger.info e.message
        raise MobileCheckException, "privoxy_plist_setup_failure"
      end

      PrivoxyManager.configure(server_config)
      PrivoxyManager.ensure_pac_file @device, config

      max_attempts = 20
      max_attempts.times do |attempt|
        sleep 0.5
        begin
          if attempt > (max_attempts - 2)
            BrowserStack.logger.info "Privoxy does not seem to be running"
            raise MobileCheckException, "Privoxy not working after #{attempt} attempts"
          end

          proxy_options = { host: 'localhost', port: privoxy_port }
          check_privoxy_running(proxy_options, privoxy_default_template_content)
          check_internet_connection(proxy_options)
          break
        rescue => e
          BrowserStack.logger.error(
            "Privoxy is not working - #{e.message}. " \
            "Attempting to load plist, pacfile and privoxy conf..."
          )
          kill_mitmproxy_on(privoxy_port) if attempt > 5
          CheckPlist.unload_service("privoxy_#{privoxy_port}", ServiceType.UserService, username)
          CheckPlist.load_service("privoxy_#{privoxy_port}", ServiceType.UserService, username)
          PrivoxyManager.reset_proxy(@device, config)
        end
      end
    end

    def kill_mitmproxy_on(privoxy_port)
      BrowserStack::OSUtils.execute("ps aux | grep mitmproxy | grep #{privoxy_port} | awk '{print $2}' | xargs kill -9")
    end

    def check_device_date
      ret = IdeviceUtils.device_time(@device)
      raise MobileCheckException, "idevicedate error : #{ret}" if $CHILD_STATUS.exitstatus != 0
    end

    def check_battery(device_name)
      battery_level = IdeviceUtils.battery_capacity(@device)
      BrowserStack.logger.info("BatteryStatus: #{@device} #{device_name} #{Time.now} #{battery_level}")
      raise MobileCheckException, "low battery" if battery_level < 30

      battery_level
    end

    def airplane_mode
      airplane_mode_on = IdeviceUtils.airplane_mode_on?(@device)
      return unless airplane_mode_on

      # Cleanup has the automation to disable airplane mode
      device_state.touch_cleanup_requested_file
      Zombie.push_logs("device-check-requested-airplane-disable", '', { device: @device })
      BrowserStack.logger.info("Requested cleanup to disable airplane mode")
    end

    def verify_iproxy_version_for_device(device_version)
      # this check is to avoid mac os versions < 10.15.5 to not have a updated version of iproxy i.e (2.0.3, 2.0.2)
      # valid output of `iproxy -version` on < 13.4 is `usage: iproxy LOCAL_TCP_PORT DEVICE_TCP_PORT [UDID]`
      mac_version = OSUtils.macos_version
      invalid_iproxy_version = Gem::Version.new(mac_version) < Gem::Version.new("10.15.5") && !Iproxy.version.include?("usage")
      raise MobileCheckException, OFFLINE_REASON_INVALID_IPROXY_VERSION if invalid_iproxy_version
    end

    def check_device_supervised
      BrowserStack.logger.info("Checking if device supervised for: #{@device}")
      device_supervised = IdeviceUtils.device_supervised?(@device)

      BrowserStack.logger.info("Device Supervised check for device: #{@device} is #{device_supervised}")
      raise OFFLINE_REASON_DEVICE_NOT_SUPERVISED unless device_supervised
    end

    def check_rsd_values
      BrowserStack.logger.info("Checking RSD values for #{@device}")

      PyMobileDevice::Developer.update_rsd_values(@device)

      BrowserStack.logger.info("Updated RSD values for #{@device}")
    end

    def check_developer_image_mounted(device_version, developer_disk_images_path = nil)
      return unless IdeviceUtils.mounted_developer_image(@device)&.[]('ImageSignature[1]').nil?

      BrowserStack.logger.info("Developer Disk Image is not mounted, trying to mount images available in #{Utils.developer_disk_images_dir}")

      # Try mounting with all the developer images present matching the device_version
      Dir["#{Utils.developer_disk_images_dir}*"].each do |devloper_image_dir|
        disk_image_name = devloper_image_dir.split('/').last
        device_major_version = device_version.split(".")[0..1].join(".")

        next unless disk_image_name.start_with?(device_major_version)

        dmg_path, dmg_signature_path = Dir["#{devloper_image_dir}/*"].sort

        next if dmg_path.nil? || dmg_path.empty?
        next if dmg_signature_path.nil? || dmg_signature_path.empty?

        BrowserStack.logger.info("Trying to mount: #{disk_image_name}")

        if IdeviceUtils.mount_developer_image(@device, dmg_path, dmg_signature_path)
          BrowserStack.logger.info("Mounted Successfully: #{disk_image_name}")
          break
        end
      end

      raise MobileCheckException, "Developer Disk Image not mounted" unless IdeviceUtils.mounted_developer_image(@device)
    end

    def check_developer_symbols(device_version, developer_symbols_path)
      raise MobileCheckException, "Device version is empty!" if device_version.nil? || device_version.empty?

      return nil if device_version.to_i >= 17 # xcode will download it automatically hopefully

      developer_symbols_list = @server_config['developer_symbols']
      if !developer_symbols_list.include?(device_version) &&
        !developer_symbols_list.include?("#{device_version}arm64e")
        raise MobileCheckException, "Developer symbols not found in config"
      end

      Dir["#{developer_symbols_path}*"].each do |symbol_dir|
        symbol_name = symbol_dir.split('/').last
        return nil if symbol_name.start_with?(device_version)
      end

      BrowserStack.logger.info("Couldn't find symbols for device version #{device_version} in #{developer_symbols_path}")
      raise MobileCheckException, "Developer symbols not found in path"
    end

    def check_mdm_server
      unless IosMdmServiceClient.is_mdm_server_up?
        BrowserStack.logger.error("mdm server down")
        Zombie.push_logs("mdm-server-down", @device, { device: @device })
        @influxdb_client.event(@device, 'mdm-server-down', subcomponent: 'ppuid-independent-checks', is_error: true)
      end
    end

    def check_platform_version_consistency(device_version)
      if device_version.nil? || device_version.to_i.zero?
        BrowserStack.logger.warn("Device version: #{device_version} is invalid, returning")
        return
      end

      # Need to check for 13.4+ separate to other v13s
      # then normalize versions:
      device_hosting_version = if Gem::Version.new(device_version) >= Gem::Version.new('18.0')
                                 ['18']
                               elsif Gem::Version.new(device_version) >= Gem::Version.new('17.0')
                                 ['17']
                               elsif Gem::Version.new(device_version) >= Gem::Version.new('16.0')
                                 ['16']
                               elsif Gem::Version.new(device_version) >= Gem::Version.new('15')
                                 ['15']
                               elsif Gem::Version.new(device_version) >= Gem::Version.new('13.4')
                                 ['13_4']
                               elsif Gem::Version.new(device_version) >= Gem::Version.new('12.0')
                                 ['12', '13_4']
                               elsif Gem::Version.new(device_version) >= Gem::Version.new('10.0')
                                 ['11']
                               else
                                 raise MobileCheckException, "Invalid iOS version"
                               end
      machine_version = if @server_config["platform_category"].start_with?('smart_tv_mac_mini_')
                          @server_config["platform_category"].sub('smart_tv_mac_mini_', '')
                        else
                          @server_config["platform_category"].sub('ios_njb_', '')
                        end

      BrowserStack.logger.info("Device hosting: #{device_hosting_version} vs. machine: #{machine_version}")
      return if device_hosting_version.include?(machine_version)

      raise MobileCheckException, OFFLINE_REASON_PLUGGED_IN_WRONG_MACHINE
    end

    def check_media_backup(config, device)
      BrowserStack.logger.info("#{device} backup isn't created") unless File.exists?("#{config['photo_data']}/#{device}/Photos.sqlite")
    end

    def parse_installed_profiles(profiles_required, profiles_installed) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
      mdm_ca_certs_present = false
      profiles_to_install = profiles_required.keys
      profiles_installed['ProfileList'].each do |profile| # rubocop:todo Metrics/BlockLength
        # Verify the check again when we get APN certs later.
        if profile['PayloadOrganization'] == 'MicroMDM'
          mdm_ca_certs_present = true
        elsif profiles_required.key?(profile['PayloadDisplayName'])
          profile_required = profiles_required[profile['PayloadDisplayName']]

          # Payload type is different
          if profile_required["type"]
            content = profile['PayloadContent'].map { |p| p['PayloadType'] }
            matched_content = content.select { |payload_type| (payload_type == profile_required["type"]) }
            if matched_content.empty?
              BrowserStack.logger.info("profile check for #{profile['PayloadDisplayName']} failed, payload type mismatch, looking for '#{profile_required['type']}' found #{content.join(',')}}")
              next
            end
          end

          # UUID mismatch
          if profile_required["uuid"] && (profile['PayloadUUID'] != profile_required["uuid"])
            BrowserStack.logger.info("profile check for #{profile['PayloadDisplayName']} failed, uuid mismatch, looking for '#{profile_required['uuid']}' found '#{profile['PayloadUUID']}'")
            next
          end

          # PayloadIdentifier mismatch for PAC and MDM profiles
          if ['Proxy', 'Restrictions', 'Notifications'].include?(profile['PayloadDisplayName'])
            raise "PayloadContent has more than one element, won't search for PayloadIdentifier" if profile['PayloadContent'].length > 1

            profile_installed_payload_id = profile['PayloadContent'][0]['PayloadIdentifier']
            required_profile_payload_id = case profile['PayloadDisplayName']
                                          when 'Proxy'
                                            @server_config['pac_profile_payload_id']
                                          when 'Notifications'
                                            @server_config['mdm_notifications_profile_payload_id']
                                          else
                                            @server_config['mdm_restrictions_profile_payload_id']
                                          end

            if required_profile_payload_id != profile_installed_payload_id
              BrowserStack.logger.info("profile check for #{profile['PayloadDisplayName']} failed, \
                                        PayloadIdentifier mismatch, required \
                                        '#{@server_config['pac_profile_payload_id']}' \
                                        and '#{profile_installed_payload_id}' installed")
              next
            end
          end

          # All good, don't need to install
          profiles_to_install.delete(profile['PayloadDisplayName'])
        end
      end
      [mdm_ca_certs_present, profiles_to_install]
    end

    def profile_check_needed?(profile_check_limit, profile_check_counter, offline_reason)
      return true if offline_reason.include?(OFFLINE_REASON_DEVICE_NOT_SUPERVISED)

      profile_check_counter = begin
        (profile_check_counter || 0).to_i
      rescue
        0
      end

      should_run_mod = profile_check_counter % profile_check_limit
      BrowserStack.logger.info("Last profile checked for #{@device} Mod - #{should_run_mod}")
      should_run_mod == 0
    end

    def check_device_internet(port, device_version)
      wda_client = WdaClient.new(port)
      device_unlocked = false
      total_attempts = 2

      total_attempts.times do |count|
        response = wda_client.internet_check(6)
        BrowserStack.logger.info("check_device_internet, attempt ##{count} - device: #{@device}, response: #{response.inspect}")

        # always lock screen after the response, does not matter if it is not success
        wda_client.lock_device(device_version) if device_unlocked

        return nil if ((response.key?('status') && response['status'] == 0) || (!response.key?('status') && response['value'].nil?)) && !response.empty?

        if count < (total_attempts - 1)
          # Unlock screen in case of first attempt fails (it will lock again after next request)
          sleep 1
          wda_client.unlock_device
          device_unlocked = true
        end
        sleep 1
      end

      raise "wda can't reach internet"
    rescue => e
      raise MobileCheckException, "#{INTERNET_DOWN_MESSAGE} - #{e.to_s[0...34]}"
    end

    def check_device_internet_during_session(method, event, session_id)
      BrowserStack.logger.info("Checking Device Internet at #{event} via #{method}")
      begin
        current_device_config = DeviceManager.device_configuration_check(@device)
        wda_port = current_device_config["webdriver_port"].to_i

        attempts = 2
        attempts.times do |count|
          response = WdaClient.new(wda_port).internet_check(6)
          if ((response.key?('status') && response['status'] == 0) || (!response.key?('status') && response['value'].nil?)) && !response.empty?
            BrowserStack.logger.info("Internet Working via #{method} at #{event}")
            BrowserStack::Zombie.push_logs("in-session-internet-check", "", { "device" => @device, "session_id" => session_id, "data" => { "internet_working" => "true", "method" => method, "event" => event } })
            return true
          end
          sleep 1 if count < attempts - 1
        end
        BrowserStack.logger.error("Internet NOT Working via #{method} at #{event}")
        BrowserStack::Zombie.push_logs("in-session-internet-check", "", { "device" => @device, "session_id" => session_id, "data" => { "internet_working" => "false", "method" => method, "event" => event } })
        false
      rescue => e
        BrowserStack.logger.error("Exception occurred during Internet check via #{method} at #{event}: #{e.message}")
        BrowserStack::Zombie.push_logs("in-session-internet-check-error", e.message, { "device" => @device, "session_id" => session_id, "data" => { "method" => method, "event" => event } })
        false
      end
    end

    def invalid_custom_mdm_device?
      !(device_state.dedicated_cleanup_file_present? && device_state.dedicated_device_file_present?) && (CustomMDMManager.is_custom_mdm_device?(@device) || device_state.custom_mdm_remove_setup_file_present?)
    end

    def check_insecure_websocket_proxy_map(port, device_version, selenium_port)
      return nil if device_version.to_i < 17

      wda_client = WdaClient.new(port)
      wda_response = wda_client.device_ip
      device_ip = wda_response["value"] || ""
      return if device_ip.empty? || selenium_port.to_s.empty?

      proxy_port = "4#{selenium_port}"

      Utils.touch_file_and_create_parents(WS_PROXY_DEVICES_CONF) unless File.exist?(WS_PROXY_DEVICES_CONF)
      old_config = begin
        JSON.parse(File.read(WS_PROXY_DEVICES_CONF))
      rescue
        {}
      end

      return if old_config[device_ip] == proxy_port

      old_config[device_ip] = proxy_port
      Utils.write_to_file_with_lock(WS_PROXY_DEVICES_CONF, old_config.to_json)

      BrowserStack.logger.info("check_insecure_websocket_proxy_map done, device_ip : #{device_ip}, proxy_port : #{proxy_port}")
    end

    def try_re_mdm_with_cleanup
      BrowserStack.logger.info("Sending device to Cleanup in next device-check")
      device_state.touch_re_mdm_file
      device_state.remove_force_clean_safari_file
      device_state.touch_cleanup_requested_file
    end

    # Method used in device-check and cleanup
    # Changes done here should also be made in ConfigurationProfilesEnforces#enforce_configuration_profiles
    # This should be deprecated in future
    def check_mdm_settings(proxy_pac_url, force_install, profile_check_limit, mdm_profiles_required, device_name, device_version, offline_reason, profile_check_counter = 0) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength, Metrics/PerceivedComplexity, Metrics/CyclomaticComplexity
      mdm_profiles_required.delete("Notifications") unless Utils.new_notifications_flow_enabled?(@device)

      mitm5_installed_state_file = "#{@server_config['state_files_dir']}/mitm5_installed_state_file_ver#{MITM_5_CERT_VERSION}_#{@device}"
      force_install = true unless File.file?(mitm5_installed_state_file)
      return if (!force_install && !profile_check_needed?(profile_check_limit, profile_check_counter, offline_reason)) || CustomMDMManager.is_custom_mdm_device?(@device)

      BrowserStack.logger.info("Starting check_mdm_settings, force_install: #{force_install}")

      BrowserStack::Zombie.push_logs("mdm_profile_check", force_install, { "os" => "ios_njb",
                                                                           "device" => @device,
                                                                           "browser" => "",
                                                                           "terminal_type" => "realMobile" })

      begin
        IosMdmServiceClient.check_device_on_mdm(@device)
      rescue MdmApiFatalException => e
        try_re_mdm_with_cleanup
        raise e
      end

      configuration_profiles_manager = ConfigurationProfilesManager.new(@device, BrowserStack.logger)

      uses_cfgutil = configuration_profiles_manager.device_uses_cfgutil_managed_profiles?
      # Its unexpected that for devices where cfgutil migration is enabled, force_install comes as true, as we handle all profiles management in cleanup itself.
      # Instrumenting this to understand any misses.
      if uses_cfgutil && force_install
        BrowserStack::Zombie.push_logs("unexpected-finstall", nil, { "os" => "ios_njb",
                                                                     "device" => @device,
                                                                     "browser" => "",
                                                                     "terminal_type" => "realMobile" })
      end

      # For devices where CFGUTIL migration is enabled, we check the profiles in CleanupIphone#check_and_enforce_configuration_profiles
      return if uses_cfgutil

      profiles = []

      begin
        profiles = IosMdmServiceClient.get_installed_profiles(@device, @redis_client)
      rescue => e
        if force_install && e.message.match(/Connection timed out/)
          BrowserStack.logger.info("Need to re-mdm device as get_installed_profiles timedout")
          try_re_mdm_with_cleanup
          raise e
        else
          raise e
        end
      end

      mdm_ca_certs_present, profiles_to_install = parse_installed_profiles(mdm_profiles_required, profiles)
      raise MobileCheckException, "mdm certs not installed" unless mdm_ca_certs_present

      # Condition to install setup assistant profile based on version
      install_setup_assistant_profile = device_version.to_f >= 14.0 && !IdeviceUtils.apple_tv_device?(@device)
      BrowserStack.logger.info("Skipping Setup Assistant Profile Installation") unless install_setup_assistant_profile
      force_mitm_cert_installation = mitm_cert_installation_needed?

      # if everything is awesome return
      if !force_install && profiles_to_install.empty? && !force_mitm_cert_installation
        BrowserStack.logger.info("No mdm profiles to install or cert installations needed, Returning ...")
        return
      end

      if !profiles_to_install.empty? || force_install
        if force_install
          BrowserStack.logger.info("#{@device} seems to be new. Installing profiles")
        else
          BrowserStack.logger.info("#{@device} does not have #{profiles_to_install.join(',')} profile(s) installed. trying to install.")
        end
      end

      #TODO: make this bit more generic (maybe move the template file name to config or something)
      if profiles_to_install.include?("Restrictions") || force_install
        BrowserStack.logger.info("#{@device} Installing Restrictions Profile")
        configuration_profiles_manager.install_profile(:restrictions, install_via: :mdm)
      end

      if (profiles_to_install.include?("Setup Assistant") || force_install) && install_setup_assistant_profile
        BrowserStack.logger.info("#{@device} Installing Setup Assistant Profile")
        configuration_profiles_manager.install_profile(:"setup assistant", install_via: :mdm)
      end

      if profiles_to_install.include?("Proxy") || force_install
        BrowserStack.logger.info("#{@device} does not have proxy profile installed. trying to install.")
        configuration_profiles_manager.install_profile(:proxy, install_via: :mdm)
      end

      if profiles_to_install.include?("Notifications") || (force_install && !mdm_profiles_required["Notifications"].nil?)
        BrowserStack.logger.info("#{@device} does not have notifications profile installed. trying to install.")
        configuration_profiles_manager.install_profile(:notifications, install_via: :mdm)
      end

      # Install mitmproxy root certificate for network logs.
      ROOT_CA_LIST.each do |certificate_profile_name|
        next unless profiles_to_install.include?(certificate_profile_name) || force_install || force_mitm_cert_installation

        BrowserStack.logger.info("#{@device} does not have #{certificate_profile_name} installed. trying to install.")

        configuration_profiles_manager.install_profile(certificate_profile_name.downcase.to_sym, install_via: :mdm)
      end

      profiles = IosMdmServiceClient.get_installed_profiles(@device, @redis_client)
      mdm_ca_certs_present, profiles_to_install = parse_installed_profiles(mdm_profiles_required, profiles)

      raise MobileCheckException, "proxy profile invalid" if profiles_to_install.include?("Proxy")
      raise MobileCheckException, "restrictions profile invalid" if profiles_to_install.include?("Restrictions")
      raise MobileCheckException, "Setup Assistant profile invalid" if profiles_to_install.include?("Setup Assistant") && install_setup_assistant_profile
      raise MobileCheckException, "MitmProxy root certificate not installed" if (ROOT_CA_LIST & profiles_to_install).any?
      raise MobileCheckException, "Notifications profile invalid" if profiles_to_install.include?("Notifications")

      FileUtils.touch(mitm5_installed_state_file)
      device_state.remove_force_install_mdm_profiles_file if force_install
    end

    def check_for_mitm_cert
      !File.exists?(mitm_cert_installation_file) || ((Time.now - File.mtime(mitm_cert_installation_file)) > (60 * 60 * 24 * 1))
    end

    def mitm_cert_installation_needed?
      # check mitm cert install once per day
      return false unless check_for_mitm_cert

      FileUtils.touch(mitm_cert_installation_file)

      parsed_certs = IosMdmServiceClient.get_certificates(@device, @redis_client)
      mitm_certs = parsed_certs.select { |cert| cert[:name] == "mitmproxy" }

      # 2 root proxy certs expected in all iOS versions (MOBPE-308)
      return true if mitm_certs.empty? || mitm_certs.length != 2

      expired_certs = mitm_certs.select { |cert| cert[:expired] == true }
      BrowserStack.logger.info("#{@device} cert installation, expired_certs #{expired_certs}")

      data = {}
      data[:error] = expired_certs.map { |element| element[:name] }.join(",") unless expired_certs.empty?
      data[:device] = @device
      BrowserStack::Zombie.push_logs("mitmproxy_certificate_missing", "", data)

      !expired_certs.empty?
    rescue => e
      BrowserStack.logger.error "intrument_missing_mitm_cert failed with error: #{e.class} - #{e.backtrace.join("\n")}"
    end

    def working_interfaces_with_internet
      working_interfaces = []
      try = 0
      while working_interfaces.empty? && try < 6
        sleep 3 if try > 0
        working_interfaces = Utils.get_working_interfaces(@device, check_inet = true)
        BrowserStack.logger.info "Trying to get working interfaces in the #{try + 1} attempt - #{working_interfaces}"
        try += 1
      end
      working_interfaces
    end

    def check_internet_sharing(wda_port) #rubocop:todo Metrics/AbcSize
      unless OSUtils.bridge100_exists?
        BrowserStack.logger.info "Bridge100 interface doesn't exist, creating"
        BrowserStack.logger.error "Failed to create Bridge100 interface" unless OSUtils.create_interface('bridge100')
      end

      working_interfaces = working_interfaces_with_internet
      if working_interfaces.empty?
        BrowserStack.logger.error("internet_sharing: can't find network interfaces in ifconfig for device: #{@device}")
        # interface changes in iOS 17 sometimes make working interfaces empty but still internet works, rely on WDA for internet check
        # changing the working interface to 16.0 version
        return if IdeviceUtils.device_version(@device) >= Gem::Version.new('16.0')

        raise MobileCheckException, "device's network interface not found"
      end

      # check if interface in bridge (add it if its not)
      bridged_interfaces = []
      working_interfaces.each do |i|
        IdeviceUtils.update_interface_state(i, "up") unless IdeviceUtils.is_interface_active?(i)
        unless IdeviceUtils.is_network_interface_in_nat_plist?(i)
          BrowserStack.logger.info "internet_sharing: network interface #{i} not in NAT plist .. adding"
          IdeviceUtils.add_network_interface_to_nat_plist(i)
        end
        unless IdeviceUtils.is_network_interface_in_bridge?(i)
          IdeviceUtils.add_network_interface_to_bridge(i)
          BrowserStack.logger.error("internet_sharing: network interface #{i} not in bridge100 .. adding")
        end
        bridged_interfaces.push(i) if IdeviceUtils.is_network_interface_in_bridge?(i) && IdeviceUtils.is_network_interface_in_nat_plist?(i)
      end
      if bridged_interfaces.empty?
        BrowserStack.logger.error("internet_sharing: network interfaces not in bridge100 #{working_interfaces.join(',')}")
        raise MobileCheckException, "device's network interface not in bridge, is internet sharing enabled for this device?"
      end

      # Check if interfaces are active/up
      active_interfaces = working_interfaces.select do |interface|
        IdeviceUtils.is_interface_active?(interface)
      end
      if active_interfaces.empty?
        BrowserStack.logger.error("internet_sharing: network interfaces are not up #{working_interfaces.join(',')}")
        raise MobileCheckException, "device's network interface is not active"
      end

      # check if the device is using internet sharing
      wda_response, device_ip = get_device_ip(wda_port)
      # if the string is an ip address but doesn't start with 192.xx then it means it's not using internet sharing
      if (/^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/ =~ device_ip) && !device_ip.start_with?("192.168.2")
        BrowserStack.logger.error("device is not using internet sharing, device_ip: #{device_ip}, wda_response: #{wda_response}")
        device_state.touch_cleanup_requested_file
        raise MobileCheckException, "device is not using internet sharing"
      end
    end

    def extract_package_name_from_wda_path(wda_path)
      # Returns package name from paths like /usr/local/.browserstack/package/*, or returns empty string

      package = wda_path.split("/")[4]
      package.nil? ? "" : package
    rescue NoMethodError => e
      BrowserStack.logger.error("Couldn't call split on wda path #{wda_path}")
      ""

    end

    def check_filename_with_hash_exists(hash, package_name = "")
      # valid filenames with hashes are like /usr/local/.browserstack/package/...hash1_hash2_hash3...
      BrowserStack.logger.info("Checking filename with calculated hash for package #{package_name}")
      unless File.exists?(hash)
        BrowserStack.logger.error("#{package_name} filename with calculated hash doesn't exist: #{hash}")
        raise MobileCheckException, "#{package_name} filename with calculated hash doesn't exist."
      end

      true
    end

    def ensure_no_sim_alert(idevice)
      # Unlock and lock device screen to get rid of any no sim card alert

      BrowserStack.logger.info "Unlocking and locking screen - ensure_no_sim_alert"
      idevice.unlock_device
      idevice.lock_device
    rescue Exception => e
      BrowserStack.logger.error "Unlock/lock to clear sim might have failed! #{e.message}"

    end

    def testflight_installed?
      IdeviceUtils.check_app_with_bundle_id_exists(@device, "com.apple.TestFlight")
    rescue => e
      BrowserStack.logger.error "Error while checking if testflight is installed #{e.message}"
      false
    end

    def wda_client_running?(device, port_webdriver)
      BrowserStack.logger.info "Checking wda status for #{device} on port #{port_webdriver}"
      wda_client = WdaClient.new(port_webdriver)
      wda_status = wda_client.running?
      BrowserStack.logger.info "[wda_client_running] device: #{device} port #{port_webdriver} status: #{wda_status}"
      wda_status
    end

    def ensure_xcodebuild_running(offline_reason, idevice, device, port_webdriver)
      if offline_reason.start_with?("wda not running")
        if xcodebuild_or_wda_client_not_running?(device, port_webdriver) || wda_outdated?(device, idevice)
          BrowserStack.logger.warn "Either xcodebuild or wda server not running or wda is outdated..."
          fix_wda_not_running(device, idevice, port_webdriver)
        else
          device_state.remove_xcodebuild_retry_count_file
        end
      else
        # Reload wda in device in case we deployed a new version
        # Send the device offline before trying to bring up the new WDA.
        # Check the below comment. See: MOB-6331
        raise MobileCheckException, "wda not running - outdated wda" if wda_outdated?(device, idevice)

        if xcodebuild_or_wda_client_not_running?(device, port_webdriver)
          # Not fixing the offline device immediately as the WDA might be down because of
          # appium switch during start of the session. Fixing it immediately will cause
          # the driver to open the Settings app in the middle of the user's session.
          # See: https://browserstack.atlassian.net/browse/MOB-6331
          raise MobileCheckException, "wda not running - xcodebuild or wda server not running"
        end

        BrowserStack.logger.info "Both Xcodebuild and wda client are running"
        device_state.remove_xcodebuild_retry_count_file
      end
    end

    def fix_xcodebuild(device, idevice, port_webdriver)
      BrowserStack.logger.info "Unlocking, Locking, Unlocking screen - fix_xcodebuild"
      unlock1_result = idevice.unlock_device
      lock_result = idevice.lock_device
      unlock2_result = idevice.unlock_device

      uninstall_wda_result = BrowserStack::IPhone.uninstall_wda(device)
      appium_server = BrowserStack::AppiumServer.new(device)
      Automation.allow_wda_local_network(device, WdaClient.new(port_webdriver)) if Gem::Version.new(idevice.device_version) >= Gem::Version.new(14.2)

      driver = appium_server.driver # Not quiting the driver on purpose. We need WDA to be running and reachable
      WdaClient.new(port_webdriver).homescreen # Press home so Settings isn't showing on session start
    rescue => e
      BrowserStack.logger.error("Error fixing xcodebuild: #{e.message}; #{e.backtrace}")

      # Raise first non-nil result as offline reason
      reason = unlock1_result || lock_result || unlock2_result || uninstall_wda_result || (e.message.length > 50 ? "#{e.message[0..50]}..." : e.message ).to_s
      raise MobileCheckException, "wda not running - #{reason}"
    end

    def kill_wda_xcodebuild_if_old(device)
      # if xcodebuild process is older than 10minutes it is stuck, kill it
      if OSUtils.wda_xcodebuild_running_time(device) > 10
        BrowserStack.logger.info "Killing xcodebuild on #{@device}"
        OSUtils.kill_wda_xcodebuild(device) # kill to restart xcodebuild process
        return "xcodebuild killed"
      end
      "xcodebuild running"
    end

    def xcodebuild_or_wda_client_not_running?(device, port_webdriver)
      !OSUtils.wda_xcodebuild_running?(device) || !wda_client_running?(device, port_webdriver)
    end

    def get_device_ip(port)
      response = nil
      ip_address = nil
      begin
        wda = WdaClient.new(port)
        response = wda.device_ip
        ip_address = response["value"] || ""
      rescue => e
        BrowserStack.logger.error "Error while fetching the device ip. Exception - #{e.message} #{e.backtrace.join('\n')}"
      end
      [response, ip_address]
    end

    def verify_device_firewall_rules(server_config, config)
      BrowserStack.logger.info "Verifying firewall setup for #{@device}"
      privoxy_port = config["selenium_port"].to_i + server_config["privoxy_listen_port_offset"].to_i
      simulator = NetworkSimulator.new(@device, privoxy_port)
      unless simulator.verify_firewall_rules
        sleep(privoxy_port % 10) # Adding sleep to avoid race condition between all devices for first time
        simulator.setup_dummynet_rules
        raise MobileCheckException, "firewall setup failed" unless simulator.verify_firewall_rules
      end
      BrowserStack.logger.info "Firewall setup verification for #{@device} completed"
    rescue => e
      BrowserStack.logger.error "Firewall setup verification failed for #{@device}. Exception - #{e.message} #{e.backtrace.join('\n')}"
    end

    def check_all_contents_and_settings_erased
      return unless device_state.apple_id_sign_in_requested_file_present?

      return if CustomMDMManager.is_custom_mdm_device?(@device)

      file_created_at = device_state.apple_id_sign_in_requested_file_created_at
      BrowserStack.logger.info("Apple ID sign in requested file created at: #{file_created_at}")

      device_re_mdmed_at = Time.parse(IosMdmServiceClient.mdm_last_seen(@device))
      BrowserStack.logger.info("Device remdmed at: #{device_re_mdmed_at}")

      if device_re_mdmed_at > file_created_at
        device_state.remove_apple_id_sign_in_requested_file
        device_state.remove_check_global_proxy_file # Remove this file so that MDM profiles can be checked (and installed) in cleanup
      else
        raise MobileCheckException, OFFLINE_REASON_DEVICE_STUCK_WITH_APPLE_ID
      end
    end

    def ensure_appium_server_running
      appium_server = AppiumServer.new(@device, DeviceConf[@device])
      appium_server.start_server_for_version(Configuration['default_appium_version'])

    rescue WDALaunchError => e
      AppleTVUtils.stop_wda_launch_agent(@device) unless IdeviceUtils.apple_tv_device?(@device) && IdeviceUtils.device_version(@device) >= Gem::Version.new('18.0')
      BrowserStack.logger.info("Rebooting device:#{@device} via MDM due to WDALaunchError")
      reboot_result = IosMdmServiceClient.restart_device(@device)
      raise "Rebooting device #{@device} failed!" if reboot_result.nil?

      # sending device to cleanup which will kill xcodebuild and respawn wda
      device_state.touch_cleanup_requested_file if IdeviceUtils.apple_tv_device?(@device) && IdeviceUtils.device_version(@device) >= Gem::Version.new('18.0')
      raise e
    end

    private

    def mitm_cert_installation_file
      "#{@server_config['state_files_dir']}/mitm_cert_install_#{@device}"
    end

    def fix_wda_not_running(device, idevice, port_webdriver)
      if OSUtils.wda_xcodebuild_running?(device)
        kill_xcodebuild_result = kill_wda_xcodebuild_if_old(device)
        raise MobileCheckException, "wda not running - #{kill_xcodebuild_result}"
      else
        retry_count = device_state.xcodebuild_retry_count
        raise MobileCheckException, "wda not running - max retries reached" if retry_count > 5

        device_state.write_to_xcodebuild_retry_count_file(retry_count + 1)

        fix_xcodebuild(device, idevice, port_webdriver)
      end
    end

    def wda_outdated?(device, idevice)
      wda_version = @server_config['default_wda_version'] || @server_config['default_appium_version']
      WDAVersion.outdated?(device, wda_version, idevice.device_version)
    end

  end
end
