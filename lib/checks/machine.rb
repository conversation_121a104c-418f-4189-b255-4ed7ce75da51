require 'fileutils'
require 'etc'
require 'socket'
require 'timeout'
require 'faraday'
require 'aws_s3_wrapper'
require 'public_suffix'
require 'download_bundle_id_config'
require 'reboot'

require_relative '../../lib/custom_exceptions'
require_relative '../utils/utils'
require_relative '../utils/osutils'
require_relative '../utils/idevice_utils'
require_relative '../utils/devicectl'
require_relative '../utils/redis'
require_relative '../utils/block_machine'
require_relative '../utils/rails_request'
require_relative '../utils/http_utils'
require_relative '../utils/vpp_utils'
require_relative '../launch_xcode'
require_relative '../../lib/uhubctl'
require_relative '../../lib/ios_influxdb_client'
require_relative '../../config/constants'
require_relative '../helpers/host_stats'
require_relative '../helpers/crypto_mining_detection_helper'
require_relative '../utils/idevice_ffi/idevice_require'

require 'yaml'
require 'json'
require 'time'

require '/usr/local/.browserstack/mobile-common/mac_user_and_group_management/manage_mac_groups'
require '/usr/local/.browserstack/mobile-common/mac_user_and_group_management/manage_mac_users'

CONFIG = "/usr/local/.browserstack/config"
CONFIG_FILE = "#{CONFIG}/config.json"
STATIC_CONFIG_FILE = "#{CONFIG}/static_conf.json"
BSTACK_REALMOBILE_BASE = "/usr/local/.browserstack/realmobile"
SERVER_CONFIG_SAMPLE = "#{BSTACK_REALMOBILE_BASE}/config/config.yml.sample"
FORCE_DUMMY_UPDATE_FILE = "#{CONFIG}/replace_dummy_app"

KEY_AMOUNT_CMD = 'sudo su -l app -c "security dump-keychain | grep 0x00000007 | awk -F= \"{print $2}\" | wc -l"'
KEY_CLEANER_CMD = "chmod +x /usr/local/.browserstack/deps/cleanup_private_keys && sudo su -l app -c '/usr/local/.browserstack/deps/cleanup_private_keys'"

DISK_USAGE_THRESHOLD_IN_PERC = 80
SPACE_USAGE_THRESHOLD_IN_GB = 3

PUBLIC_DNS_IPS = ['*******', '*******', '*******', '*******']

module BrowserStack
  class MachineCheck # rubocop:todo Metrics/ClassLength
    include DownloadBundleIdConfig

    def initialize(mobile_root, logging_root, appium_root, user, server_config)
      @mobile_root = mobile_root
      @logging_root = logging_root
      @appium_root = appium_root
      @user = user
      @server_config = server_config
      uri = URI.parse(server_config["static_conf"]["rails_endpoint"])
      uri.scheme = "https"
      @mobile_dashboard_auth = {
        "url" => uri.to_s,
        "username" => server_config["static_conf"]["admin_terminals_user"],
        "password" => server_config["static_conf"]["admin_terminals_pass"]
      }
      @machine_on_prod = server_config["environment"] == 'prod'
      @influxdb_client = BrowserStack::IosInfluxdbClient.new(BrowserStack.logger)
    end

    def reboot_check
      if File.exist?("#{STATE_FILES_DIR}/kill_periodic_reboot")
        BrowserStack.logger.info "kill switch for periodic reboot exists"
        return
      end

      return if STATIC_CONF['env'] != "prod"

      reboot_worker = Reboot::Worker.new
      reboot_worker.start
    end

    def find_pid(process_name)
      pgrep_command = "pgrep \"#{process_name}\""
      process_pid = OSUtils.execute(pgrep_command).to_i
    end

    def kill_pid_if_more_than_threshold(pid, time_threshold_in_min, process_name)
      process_start_time = DateTime.parse(OSUtils.execute("ps -p #{pid} -o lstart| tail -n1"))
      current_time = DateTime.now

      elapsed_minutes = ((current_time - process_start_time) * 24 * 60).to_i
      BrowserStack.logger.info "Elapsed Minutes : #{elapsed_minutes}"

      if elapsed_minutes >= time_threshold_in_min
        pid_kill_resp = OSUtils.kill_pid(pid)
        BrowserStack.logger.info "Kill Pid Res : #{pid_kill_resp}"
        BrowserStack::Zombie.push_logs("machine_check_kill_stale_process", "" , { "data" => { "time" => elapsed_minutes, "pid" => pid, "process_name" => process_name } })
      end
    end

    def kill_stale_process
      host_stats = HostStats.new
      swap_stats = host_stats.swap_memory_stats

      if swap_stats["swap_used_percentage"] >= 70
        apple_conf_pid = find_pid("Apple Configurator")
        kill_pid_if_more_than_threshold(apple_conf_pid, 60, "apple configurator") if apple_conf_pid != 0

        fseventsd_pid = find_pid("fseventsd")
        kill_pid_if_more_than_threshold(fseventsd_pid, 24 * 60, "fseventsd") if fseventsd_pid != 0
      end
      newstoday2_pid = find_pid("NewsToday2")
      unless newstoday2_pid.nil?
        BrowserStack.logger.info "Killing Newstoday process with pid : #{newstoday2_pid}"
        kill_pid_if_more_than_threshold(newstoday2_pid, 60, "NewsToday2")
      end
    end

    def ensure_core_device_working
      devices = get_devices
      begin
        devices.each do |device|
          return "Not using devicectl" unless IdeviceUtils.use_devicectl(device)

          device_state = DeviceCtl::List.device_state(device)
          if ["connected", "available", "available (paired)"].include? device_state
            BrowserStack.logger.info "Device: #{device} is available/connected, CoreDevice working!"
            return "Device is available/connected"
          end
        end
        BrowserStack.logger.info "No device in devicectl connected state"
      rescue => e
        BrowserStack.logger.error("Some issue while using devicectl #{e.class}: #{e.message} #{e.backtrace}")
      end
      machine_ip = get_machine_ip
      all_devices_offline = RailsRequest.all_devices_offline?(@mobile_dashboard_auth, machine_ip)
      if all_devices_offline && !machine_rebooted_recently?
        BrowserStack.logger.info "Rebooting machine..."
        BrowserStack::Zombie.push_logs("core-device-not-working", nil)
        sleep 2 # sleep to ensure zombie push before reboot
        OSUtils.execute("sudo reboot")
      end
    end

    def ensure_private_key_amount
      key_amount = OSUtils.execute(KEY_AMOUNT_CMD).strip
      BrowserStack.logger.info "Keychain private key amount: #{key_amount}"
      if key_amount.to_i > 200
        Zombie.push_logs("keychain-cleaner-keys", 'Too many keys', { "data" => key_amount } )
        OSUtils.execute(KEY_CLEANER_CMD)
        key_amount = OSUtils.execute(KEY_AMOUNT_CMD).strip
        BrowserStack.logger.info "Reduced key amount to #{key_amount}"
      end
    end

    def cleanup_heavy_log_files
      host_stats = HostStats.new
      disk_stats = host_stats.disk_stats
      # Checks if disk_usage is > DISK_USAGE_THRESHOLD_IN_PERC
      if disk_stats["disk_space_percentage"] > DISK_USAGE_THRESHOLD_IN_PERC
        BrowserStack.logger.info "Machine Check Found Disk Usage > #{DISK_USAGE_THRESHOLD_IN_PERC}"

        # Finds files top 5 files with highest space
        #ls -l => has a total size as first line which is not required and thus "head -6 | tail -n+2"
        files_with_size = OSUtils.execute("ls -laS #{@logging_root}| head -6 | tail -n+2 |awk '{print $5 \" \" $9}'").split("\n")

        # Iterating through files and check if size if greater than SPACE_USAGE_THRESHOLD_IN_GB
        # and deleting it and pushing it to zombie
        files_with_size.each do |file|
          file_data = file.split
          file_size = file_data[0].to_i
          file_name = file_data[1]

          next if file_size < convert_gb_b(SPACE_USAGE_THRESHOLD_IN_GB)

          OSUtils.execute("rm #{@logging_root}/#{file_name}")
          BrowserStack.logger.info "Machine Check Deleted : #{file_name}"
          BrowserStack::Zombie.push_logs("machine_check_deleted_files", "" , { "data" => { "file_name" => file_name , "file_size" => file_size } })
        end
      end
    end

    def push_machine_stats
      host_stats = HostStats.new
      host_stats.perform
    end

    def push_machine_reboot
      logs = `log show --predicate 'eventMessage contains "Previous shutdown cause"' --last 1h`.strip
      return if logs.empty?

      logs.split("\n").each do |l|
        # 2023-08-31 03:15:04.965044+0000 0xb4  Default 0x0 0 0 kernel: (AppleSMC) Previous shutdown cause: -20
        # For all shutdown causes, refer https://logi.wiki/index.php/Shutdown_causes
        machine_reboot = l.match(/(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}).*Previous shutdown cause: (-?\d*)$/)

        if machine_reboot
          puts "Machine rebooted at #{machine_reboot[1]} with reason code #{machine_reboot[2]}"
          BrowserStack::Zombie.push_logs("ios_machine_reboot", machine_reboot[2], { "data" => { "date" => machine_reboot[1] } })
        end
      end
    end

    def ensure_timezone_UTC # rubocop:todo Naming/MethodName
      current_timezone = `date +'%Z'`.strip
      if current_timezone != "UTC"
        BrowserStack.logger.info "Current timezone (#{current_timezone}) is not UTC. Changing to UTC"
        OSUtils.execute("sudo unlink /etc/localtime; sudo ln -s /usr/share/zoneinfo/UTC /etc/localtime")
      end
    end

    def ensure_machine_logged_in
      # console will only be present once logged in
      return if OSUtils.execute("who").include?("console")

      BrowserStack.logger.info "Machine is logged out. Checking if we have already rebooted today and if all devices are offline"
      BrowserStack::Zombie.push_logs("machine-logged-out", nil)
      logged_out_reboot_file = "#{@server_config['state_files_dir']}/machine_logged_out_reboot"
      rebooted_today = File.file?(logged_out_reboot_file) &&
        File.mtime(logged_out_reboot_file).to_date == DateTime.now.to_date
      return if rebooted_today

      machine_ip = get_machine_ip
      return unless RailsRequest.all_devices_offline?(@mobile_dashboard_auth, machine_ip)

      FileUtils.touch(logged_out_reboot_file)
      BrowserStack.logger.info "Machine not rebooted today, rebooting..."
      BrowserStack::Zombie.push_logs("machine-logged-out-reboot", nil)
      sleep 2 # sleep to ensure zombie push before reboot
      OSUtils.execute("sudo reboot")
    end

    def ensure_server_running_plist(username='app')
      cp = CheckPlist.new(
        'com.browserstack.real_mobile',
        ServiceType.SystemService,
        ["/usr/local/.browserstack/realmobile/scripts/real_mobile_launch.sh"],
        "#{@logging_root}/realmobile_plist.log",
        "#{@logging_root}/realmobile_plist.log",
        username,
        "/Users/<USER>/.rvm/bin",
        { WorkingDirectory: "#{@mobile_root}/server/" }
      )
      cp.update
    end

    def ensure_csp_server_running_plist(username='app')
      mac_version = OSUtils.macos_version
      if Gem::Version.new(mac_version) >= Gem::Version.new("10.15")
        CheckPlist.update_nix_plist(
          name: 'com.browserstack.mobilecspt',
          type: ServiceType.SystemService,
          restart: false
        )

        CheckPlist.update_nix_plist(
          name: 'com.browserstack.pyios',
          type: ServiceType.SystemService,
          restart: false
        )
      end
    end

    def ensure_device_server_running_plist(username='app')
      cp = CheckPlist.new(
        'com.browserstack.device_server',
          # We are using device server to execute cfgutil commands in the '/cfgutil' endpoint.
          # Cfgutil doesn't work when using SystemService. See https://browserstack.atlassian.net/browse/MOB-8226
        ServiceType.UserService,
        ["/usr/local/.browserstack/realmobile/scripts/device_server_launch.sh"],
        "#{@logging_root}/device_server.log",
        "#{@logging_root}/device_server.log",
        username,
        "/Users/<USER>/.rvm/bin",
        { WorkingDirectory: "#{@mobile_root}/server/" }
      )
      cp.update
    end

    def ensure_device_check_running_plist(username="app")
      cp = CheckPlist.new(
        'com.browserstack.device_check',
        ServiceType.UserService,
        ["/usr/local/.browserstack/realmobile/scripts/device_check_launch.sh"],
        "#{@logging_root}/device_check_plist.log",
        "#{@logging_root}/device_check_plist.log",
        username,
        "/Users/<USER>/.rvm/bin",
        { WorkingDirectory: @mobile_root, StartInterval: 60 },
        false, # keep_alive
        nil, # calendar_interval
        true, # session_create
        true # set_lang_and_term
      )
      unless cp.update
        raise MachineCheckException.new('com.browserstack.device_check not loaded',
                                        @server_config["environment"])
      end
    end

    def ensure_inventory_push_running_plist(username="app")
      cp = CheckPlist.new(
        'com.browserstack.inventory_push',
        ServiceType.SystemService,
        ["/bin/bash", "-l", "-c", "rvm use 2.7.2; cd /usr/local/.browserstack/realmobile && bundle exec ruby /usr/local/.browserstack/realmobile/scripts/inventory_push.rb"],
        "#{@logging_root}/inventory_push_plist.log",
        "#{@logging_root}/inventory_push_plist.log",
        username,
        "/Users/<USER>/.rvm/bin",
        { WorkingDirectory: @mobile_root, StartInterval: 86400 },
        false
      )
      cp.update
    end

    def ensure_device_logger_running_plist(username="app")
      platform = Configuration["default_platform"] == "tvos" ? "--tvos" : "--ios-njb"
      nodepath = DEVICE_LOGGER_NODE
      cp = CheckPlist.new(
        'com.browserstack.device_logger',
        ServiceType.SystemService,
        [
            nodepath,
            "#{@server_config['device_logger_path']}/index.js",
            platform,
            "--state_files_dir=#{@server_config['state_files_dir']}",
            "--idevicesyslog_path=#{IDEVICESYSLOG}"
        ],
        "#{@logging_root}/device_logger.log",
        "#{@logging_root}/device_logger.log",
        username,
        nil,
        { WorkingDirectory: @server_config["device_logger_path"] }
      )
      cp.update
    end

    def ensure_plist_updated_for_session_specific_device_logger(username="app")
      BrowserStack.logger.info "Checking if Session Specific DL Plists are Updated"
      platform = Configuration["default_platform"] == "tvos" ? "--tvos" : "--ios-njb"
      nodepath = DEVICE_LOGGER_NODE
      devices = get_devices
      devices.each do |device|
        BrowserStack.logger.info "Checking Plist For Device #{device}"
        current_device = DeviceManager.device_configuration_check(device)
        port = current_device["device_logger_port"]
        cp = CheckPlist.new(
          "com.browserstack.device_logger_#{device}",
          ServiceType.SystemService,
          [
            nodepath,
            "#{@server_config['device_logger_path']}/main.js",
            platform,
            device,
            port
          ],
          "#{@logging_root}/device_logger_#{device}.log",
          "#{@logging_root}/device_logger_#{device}.log",
          username,
          nil,
          { WorkingDirectory: @server_config["device_logger_path"] }
        )
        cp.create
      end
    end

    def ensure_ws_proxy_running_plist(username="app")
      nodepath = OSUtils.which('node')
      nodepath = NODE_14 if nodepath == "node"
      cp = CheckPlist.new(
        'com.browserstack.ws_proxy',
        ServiceType.SystemService,
        [
            nodepath,
            "#{@mobile_root}/scripts/insecure_websocket_proxy/main.js"
        ],
        "#{@logging_root}/insecure_websocket_proxy.log",
        "#{@logging_root}/insecure_websocket_proxy.log",
        username,
        nil,
        { WorkingDirectory: @mobile_root }
      )
      cp.update

      # Check if ws_proxy health is good
      validate_ws_proxy(username)
    end

    def ensure_devtools_proxy_running_plist
      CheckPlist.update_nix_plist(
        name: 'com.browserstack.devtools_proxy',
        type: ServiceType.SystemService
      )
    end

    def ensure_block_domains_running_plist(username="app")
      cp = CheckPlist.new(
        'com.browserstack.block_domains',
        ServiceType.SystemService,
        ["/bin/bash", "/usr/local/.browserstack/block_domains.sh"],
        "#{@logging_root}/block_domains.log",
        "#{@logging_root}/block_domains.log",
        username,
        nil,
        nil,
        false,
        { Hour: 2, Minute: 5 }
      )
      cp.update
    end

    def ensure_logrotate_running_plist(username="root")
      cp = CheckPlist.new(
        'com.browserstack.logrotate',
        ServiceType.SystemService,
        ["bash", "-c", "bash -c \"/usr/local/bin/logrotate -f /etc/logrotate.d/browserstack -s /var/lib/logrotate.status\""],
        "dev/null",
        "dev/null",
        username,
        nil,
        nil,
        false,
        { Hour: 3, Minute: 5 }
      )
      cp.update
    end

    def ensure_image_uploader_running_plist(username='app')
      OSUtils.kill_duplicate_stale_processes("image_uploader_process", "[^bundle exec] ruby")
      cp = CheckPlist.new(
        'ruby_image_uploader',
        ServiceType.SystemService,
        ["/bin/bash", "-l", "-c", "rvm use 2.7.2; cd /usr/local/.browserstack/realmobile && bundle exec ruby /usr/local/.browserstack/realmobile/scripts/file_uploaders/image_uploader_process.rb"],
        "#{@logging_root}/image_uploader.log",
        "#{@logging_root}/image_uploader.log",
        username,
        "/Users/<USER>/.rvm/bin",
        { WorkingDirectory: @mobile_root }
      )
      cp.update
    end

    def ensure_image_uploader_cleaner_running_plist(username='app')
      OSUtils.kill_duplicate_stale_processes("image_uploader_cleaner", "[^bundle exec] ruby")
      cp = CheckPlist.new(
        'ruby_image_uploader_cleaner',
        ServiceType.SystemService,
        ["/bin/bash", "-l", "-c", "rvm use 2.7.2; cd /usr/local/.browserstack/realmobile && bundle exec ruby /usr/local/.browserstack/realmobile/scripts/image_uploader_cleaner.rb"],
        "#{@logging_root}/image_uploader.log",
        "#{@logging_root}/image_uploader.log",
        username,
        "/Users/<USER>/.rvm/bin",
        { WorkingDirectory: @mobile_root, StartInterval: 600 },
        false
      )
      cp.update
    end

    def ensure_video_uploader_running_plist(username='app')
      OSUtils.kill_duplicate_stale_processes("video_uploader_process", "[^bundle exec] ruby")
      cp = CheckPlist.new(
        'ruby_video_uploader',
        ServiceType.SystemService,
        ["/bin/bash", "-l", "-c", "rvm use 2.7.2; cd /usr/local/.browserstack/realmobile && bundle exec ruby /usr/local/.browserstack/realmobile/scripts/file_uploaders/video_uploader_process.rb"],
        "#{@logging_root}/video_uploader.log",
        "#{@logging_root}/video_uploader.log",
        username,
        "/Users/<USER>/.rvm/bin",
        { WorkingDirectory: @mobile_root }
      )
      cp.update
    end

    def ensure_other_files_uploader_running_plist(username='app')
      OSUtils.kill_duplicate_stale_processes("other_files_uploader_process", "[^bundle exec] ruby")
      cp = CheckPlist.new(
        'ruby_other_files_uploader',
        ServiceType.SystemService,
        ["/bin/bash", "-l", "-c", "rvm use 2.7.2; cd /usr/local/.browserstack/realmobile && bundle exec ruby /usr/local/.browserstack/realmobile/scripts/file_uploaders/other_files_uploader_process.rb"],
        "#{@logging_root}/other_files_uploader.log",
        "#{@logging_root}/other_files_uploader.log",
        username,
        "/Users/<USER>/.rvm/bin",
        { WorkingDirectory: @mobile_root }
      )
      cp.update
    end

    def ensure_network_files_uploader_running_plist(username='app')
      OSUtils.kill_duplicate_stale_processes("network_files_uploader_process", "[^bundle exec] ruby")
      cp = CheckPlist.new(
        'ruby_network_files_uploader',
        ServiceType.SystemService,
        ["/bin/bash", "-l", "-c", "rvm use 2.7.2; cd /usr/local/.browserstack/realmobile && bundle exec ruby /usr/local/.browserstack/realmobile/scripts/file_uploaders/network_files_uploader_process.rb"],
        "#{@logging_root}/network_files_uploader.log",
        "#{@logging_root}/network_files_uploader.log",
        username,
        "/Users/<USER>/.rvm/bin",
        { WorkingDirectory: @mobile_root }
      )
      cp.update
    end

    def ensure_file_converter_running_plist(username='app')
      OSUtils.kill_duplicate_stale_processes("file_converter_process", "[^bundle exec] ruby")
      cp = CheckPlist.new(
        'ruby_file_converter',
        ServiceType.SystemService,
        ["/bin/bash", "-l", "-c", "rvm use 2.7.2; cd /usr/local/.browserstack/realmobile && bundle exec ruby /usr/local/.browserstack/realmobile/scripts/file_converter_process.rb"],
        "#{@logging_root}/file_converter.log",
        "#{@logging_root}/file_converter.log",
        username,
        "/Users/<USER>/.rvm/bin",
        { WorkingDirectory: @mobile_root }
      )
      cp.update
    end

    def ensure_file_converter_cleaner_running_plist(username='app')
      OSUtils.kill_duplicate_stale_processes("file_converter_cleaner", "[^bundle exec] ruby")
      cp = CheckPlist.new(
        'ruby_file_converter_cleaner',
        ServiceType.SystemService,
        ["/bin/bash", "-l", "-c", "rvm use 2.7.2; cd /usr/local/.browserstack/realmobile && bundle exec ruby /usr/local/.browserstack/realmobile/scripts/file_converter_cleaner.rb"],
        "#{@logging_root}/file_converter.log",
        "#{@logging_root}/file_converter.log",
        username,
        "/Users/<USER>/.rvm/bin",
        { WorkingDirectory: @mobile_root, StartInterval: 600 },
        false
      )
      cp.update
    end

    def ensure_delete_app_downloads_running_plist(username='app')
      OSUtils.kill_duplicate_stale_processes("delete_app_downloads", "[^bundle exec] ruby")
      cp = CheckPlist.new(
        'com.browserstack.delete_app_downloads',
        ServiceType.SystemService,
        ["/bin/bash", "-l", "-c", "rvm use 2.7.2; cd #{BSTACK_REALMOBILE_BASE} && bundle exec rvmsudo ruby #{BSTACK_REALMOBILE_BASE}/scripts/delete_app_downloads.rb"],
        "#{@logging_root}/app_cleanup.log",
        "#{@logging_root}/app_cleanup.log",
        username,
        "/Users/<USER>/.rvm/bin",
        { WorkingDirectory: @mobile_root, StartInterval: 1800 },
        false
      )
      cp.update
    end

    def ensure_log_process_running_plist(username='app')
      cp = CheckPlist.new(
        'log_process',
        ServiceType.SystemService,
        ['bash', "#{@mobile_root}/scripts/log_process.sh"],
        "#{@logging_root}/processes.log",
        "#{@logging_root}/processes.log",
        username,
        nil,
        { WorkingDirectory: @mobile_root, StartInterval: 60 },
        false
      )
      cp.update
    end

    def ensure_screenshot_instrumentation_running_plist(username='app')
      cp = CheckPlist.new(
        'ruby_screenshot_instrumentation',
        ServiceType.SystemService,
        ["/bin/bash", "-l", "-c", "rvm use 2.7.2; cd /usr/local/.browserstack/realmobile && bundle exec ruby /usr/local/.browserstack/realmobile/scripts/screenshot_instrumentation_process.rb"],
        "#{@logging_root}/screenshot_instrumentation.log",
        "#{@logging_root}/screenshot_instrumentation.log",
        username,
        "/Users/<USER>/.rvm/bin",
        { WorkingDirectory: @mobile_root }
      )
      cp.update
    end

    def ensure_dns_logger_running_plist
      cp = CheckPlist.new(
        'com.browserstack.dns_logger',
        ServiceType.SystemService,
        ['tcpdump', '-n', '-l', 'port', '53'],
        "#{@logging_root}/dns_requests.log",
        "#{@logging_root}/dns_requests.log",
        'root'
      )
      cp.update
    end

    def ensure_devtools_not_hanged

      Timeout.timeout(1) do
          s = TCPSocket.new("localhost", port) # rubocop:todo Layout/IndentationWidth
          s.close
      rescue Errno::ECONNREFUSED, Errno::EHOSTUNREACH
          raise MachineCheckException.new("devtools_down", @server_config["environment"]) # rubocop:todo Layout/IndentationWidth
      end
    rescue Timeout::Error
      raise MachineCheckException.new("devtools_down", @server_config["environment"])
    rescue
      BrowserStack::OSUtils.kill_process("devtools_proxy", "server_port=443")

    end

    def ensure_firewall_enabled
      OSUtils.start_firewall
    end

    def ensure_xcode
      result = OSUtils.check_xcode
      if result
        if result.match(/command not found/)
          raise MachineCheckException.new("Xcode not found", @server_config["environment"])
        else
          version_number = result.split[1]
          raise MachineCheckException.new("Xcode version is wrong. Found: #{result}", @server_config["environment"]) unless Configuration['xcode_versions'].include?(version_number)
        end
      else
        raise MachineCheckException.new("No result in check_xcode", @server_config["environment"])
      end
    end

    def ensure_correct_dirs_are_present(username="app")
      dirs_to_check = [@server_config['state_files_dir'], "#{@server_config['state_files_dir']}/session_start"]

      dirs_to_check.each do |dir|
        FileUtils.mkdir_p(dir) unless File.exists?(dir)
        FileUtils.chown_R(username, nil, dir) if Etc.getpwuid(File.stat(dir).uid).name != username
        raise MachineCheckException.new("Cannot create #{dir} directory", @server_config["environment"]) unless File.exists?(dir)
        raise MachineCheckException.new("#{dir} has different owner", @server_config["environment"]) if Etc.getpwuid(File.stat(dir).uid).name != username
      end
    end

    def ifuse_binary_is_present?
      File.exists?(IFUSE)
    end

    def ensure_correct_ip_and_hostname
      network_helper = NetworkHelper::NetworkSetup.new
      network_helper.set_ip
      hostname = network_helper.get_hostname
      unless OSUtils.check_dns(hostname)
        BrowserStack.logger.error "Hostname DNS check failure"
        raise MachineCheckException.new("Hostname DNS check failure", @server_config["environment"])
      end
    end

    def remove_expired_certs
      OSUtils.get_cert_identities(@user).each do |cert_hash, cert_id, cert_expired|
        if cert_expired
          OSUtils.delete_cert(cert_hash)
          BrowserStack::Zombie.push_logs("ios-provisioning-cert-deleted", nil, { "cert_hash" => cert_hash, "cert_identity" => cert_id })
        end
      rescue => e
        BrowserStack.logger.warn("remove_expired_certs failed for #{cert_id}, #{cert_hash}... continuing")
        BrowserStack.logger.warn("Warning #{e.class}: #{e.message} #{e.backtrace}")
      end
    rescue => e
      BrowserStack.logger.warn("Warning remove_expired_certs failed #{e.class}: #{e.message} #{e.backtrace}")
    end

    def ensure_certs
      result = OSUtils.retrieve_certificates(@user)
      raise MachineCheckException.new('Failed to find build certificate or development keychain', @server_config["environment"]) if result.match(/^0 valid identities found/i)

      true
    end

    def ensure_static_config_has_params

      raise MachineCheckException.new('static_conf.json is not present on machine', @server_config["environment"]) unless File.exists?(STATIC_CONFIG_FILE.to_s)

      static_conf_check = HttpUtils.make_get_request('http://localhost:45671/check_static_conf', 5)
      raise MachineCheckException.new("Invalid Static Conf: #{static_conf_check.body}", @server_config["environment"]) if static_conf_check.status != 200
    rescue => e
      raise MachineCheckException.new("Error in STATIC_CONFIG_FILE check: #{e.message}", @server_config["environment"])

    end

    def check_ios_webkit_debug_proxy
      webkit_output = OSUtils.ios_webkit_proxy_version
      raise MachineCheckException.new("ios webkit debug proxy not working", @server_config["environment"]) if !webkit_output || webkit_output.empty? || webkit_output.match(/command not found/)
    end

    def ensure_sleep_disabled
      unless OSUtils.sleep_disabled?
        OSUtils.disable_sleep
        raise MachineCheckException.new('Sleep should not be enabled', @server_config["environment"]) unless OSUtils.sleep_disabled?
      end
    end

    def remove_old_derived_data
      OSUtils.remove_folders_older_than("/Users/<USER>/Library/Developer/Xcode/DerivedData/", 360)
    end

    def check_redis_connection
      BrowserStack.logger.info "Checking whether Redis Client connection is successful "
      if RedisUtils.redis_works?
        BrowserStack.logger.info "Machine can connect to the redis client successfully"
      else
        raise MachineCheckException.new("Redis Client error connecting on port #{@server_config['redis_port']}", @server_config["environment"])
      end
    end

    def ensure_realmobile_files_present
      realmobile_dir = @server_config["mobile_root"]
      config_yml = "#{@server_config['mobile_root']}/config/config.yml"
      keys_yml = "#{@server_config['mobile_root']}/keys/keys.yml"

      [realmobile_dir, config_yml, keys_yml].each do |file_location|
        raise MachineCheckException.new("File/dir missing: #{file_location}", @server_config["environment"]) unless File.exists?(file_location)
      end
    end

    def ensure_var_lockdown_permission_is_set
      lockdown_folder = "/var/db/lockdown"
      BrowserStack.logger.info "Checking permission of the #{lockdown_folder}"
      OSUtils.execute "sudo chmod 777 /var/db/lockdown"
      lockdown_file_stat = File.stat(lockdown_folder)
      lockdown_permission_bits = format("%o", lockdown_file_stat.mode)[-3..]
      raise MachineCheckException.new('Machine has bad permissions for /var/db/lockdown folder', @server_config["environment"]) if lockdown_permission_bits != "777"
    end

    def ensure_machine_bluetooth_and_wifi_disabled
      BrowserStack.logger.info "Checking and disabling Wifi and Bluetooth network Services"
      OSUtils.check_and_disable_bluetooth_and_wifi_network_services

      BrowserStack.logger.info "Checking and disabling machine Wifi and Bluetooth"
      OSUtils.disable_wifi_interface if OSUtils.is_wifi_active?
      OSUtils.disable_bluetooth if OSUtils.bluetooth_enabled? && !@server_config["bluetooth_check_disabled"]

      raise MachineCheckException.new("Failed to disable machine wifi and bluetooth", @server_config["environment"]) if OSUtils.is_wifi_active? || OSUtils.bluetooth_enabled?
    end

    def ensure_spotlight_indexing_is_disabled
      # can't disable spotlight because of System Integrated Protection in OSX.
      # sudo launchctl unload -w /System/Library/LaunchDaemons/com.apple.metadata.mds.plist
      BrowserStack.logger.info "Disabling spotlight indexing"
      OSUtils.execute("sudo mdutil -a -i off")
    end

    def clean_machine
      BrowserStack.logger.info "Cleaning up machine temporary files"
      command = "/bin/bash #{@server_config['mobile_root']}/phase-scripts/clean-machine.sh"
      OSUtils.execute(command, timeout: 180)
    end

    def ensure_usbmuxd_up
      BrowserStack.logger.info "Checking idevice_id -l for usbmuxd errors"
      idevice_id_output = OSUtils.execute "idevice_id -l 2>&1"
      if idevice_id_output.include? "Unable to retrieve device list"
        BrowserStack.logger.error "usbmuxd error"
        if @server_config["platform_category"] == 'ios_njb_12'
          hub_list = Uhubctl.get_hubs("USB2")
          hub_list.each do |hub|
            Uhubctl.poweroff_hub(hub)
            sleep 1
          end
          hub_list.each do |hub|
            Uhubctl.poweron_hub(hub)
            sleep 1
          end
          sleep 5
          idevice_id_output = OSUtils.execute "idevice_id -l 2>&1"
          if idevice_id_output.include? "Unable to retrieve device list"
            BrowserStack::Zombie.push_logs("uhubctl_failed", nil)
            @influxdb_client.event('NA', 'uhubctl-failed', subcomponent: 'usbmuxd', is_error: true)
            OSUtils.execute "sudo reboot -q"
          end
        else
          OSUtils.execute "ps -ef | grep usbmuxd | awk '{print $2}' | xargs kill"
        end
        BrowserStack::Zombie.push_logs("usbmuxd_error", @server_config["platform_category"])
        @influxdb_client.event('NA', 'usbmuxd-error', subcomponent: 'usbmuxd', is_error: true)
        raise MachineCheckException.new("Unable to setup usbmuxd", @server_config["environment"])
      end
    end

    def ensure_supervision_identities_downloaded
      identities_path = "#{CONFIG_DIR_PATH}/supervision_identities"
      return if Dir.exists?(identities_path) && Dir.glob(File.join(identities_path, '**', '*')).count == 12

      s3_profile = "bs"
      s3_bucket = @server_config["environment"] == "prod" ? "bs-mobile" : "bs-mobile-stag"
      s3_file_location = 'deploy/realios/supervision_identities.tar.gz'
      download_path = "/tmp/supervision_identities.tar.gz"
      AwsS3Wrapper.download!(s3_bucket, s3_file_location, '/tmp/supervision_identities.tar.gz', s3_profile)

      OSUtils.execute("tar xvfC #{download_path} #{CONFIG_DIR_PATH}")
      username = @server_config["user"]
      FileUtils.chown_R(username, nil, identities_path)
    end

    def ensure_enterprise_dummy_app_downloaded(username = "app") # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
      dummy_app_path = "/usr/local/.browserstack/enterpriseDummy.app"
      replace_dummy_app = enterprise_dummy_out_of_date?
      if !File.exists?(dummy_app_path) || replace_dummy_app
        BrowserStack.logger.error "Dummy app not present. Downloading it"
        if @server_config["environment"] == "prod"
          environment = "Production"
          s3_profile = "automate"
        else
          environment = "Staging"
          s3_profile = "bs"
        end
        dummy_path_ipa = "/usr/local/.browserstack/enterpriseDummy.ipa"
        if replace_dummy_app
          BrowserStack.logger.info "About to replace Dummy App"
          # cleanup all destinations
          FileUtils.rm(dummy_path_ipa) if File.exists?(dummy_path_ipa)
          FileUtils.rm_rf("/tmp/enterpriseDummyApp/") if Dir.exist?("/tmp/enterpriseDummyApp/")
          FileUtils.rm_rf(dummy_app_path) if Dir.exist?(dummy_app_path)
        end

        s3_bucket = get_s3_bucket_name(environment)
        download_url = "mobile/realios/enterpriseDummy6#{environment}.ipa"
        AwsS3Wrapper.download!(s3_bucket, download_url, dummy_path_ipa, s3_profile)
        raise MachineCheckException.new("Downloading enterpriseDummy app failed", @server_config["environment"]) unless File.exists?(dummy_path_ipa)

        FileUtils.chown_R(username, nil, dummy_path_ipa) if Etc.getpwuid(File.stat(dummy_path_ipa).uid).name != username
        OSUtils.execute("unzip #{dummy_path_ipa} -d /tmp/enterpriseDummyApp/")
        OSUtils.execute("mv /tmp/enterpriseDummyApp/Payload/enterpriseDummy.app #{dummy_app_path}")
        if replace_dummy_app
          BrowserStack.logger.info "Touching files for requesting cleanup and app install"
          config_json = begin
            JSON.parse(File.read(@server_config["config_json_file"]))
          rescue
            nil
          end
          if !config_json.nil? && !config_json.empty?
            DeviceManager.configure @server_config
            config_json["devices"].each do |device, _config|
              bad_enterprise_path = "/tmp/unclean_bad_enterprise_app_#{device}"
              cleanup_requested_path = DeviceManager.cleanup_requested_file(device)
              OSUtils.execute("sudo su -l #{username} -c \"touch #{bad_enterprise_path}\"")
              OSUtils.execute("sudo su -l #{username} -c \"touch #{cleanup_requested_path}\"")
            end
          end
        end
        FileUtils.rm_rf FORCE_DUMMY_UPDATE_FILE
      end
      FileUtils.chown_R(username, nil, dummy_app_path) if Etc.getpwuid(File.stat(dummy_app_path).uid).name != username
      raise MachineCheckException.new("Enterprise Dummy app has root permissions", @server_config["environment"]) if Etc.getpwuid(File.stat(dummy_app_path).uid).name != username
    end

    def enterprise_dummy_out_of_date?
      minimum_version = @server_config["enterprise_dummy_min_version"]
      current_version = OSUtils.get_attr_from_plist_file(
        "/usr/local/.browserstack/enterpriseDummy.app/Info.plist",
        "CFBundleShortVersionString"
      )
      force_app_update = File.exists? FORCE_DUMMY_UPDATE_FILE
      force_app_update || Gem::Version.new(current_version) < Gem::Version.new(minimum_version)
    end

    def get_s3_bucket_name(environment)
      if environment == "Staging"
        "bs-stag"
      else
        "bs-automate-prod"
      end
    end

    def ensure_xcode_is_open

      LaunchXcode.launch
    rescue Exception => e
      raise MachineCheckException.new("Unable to open Xcode.app: #{e.message}", @server_config["environment"])

    end

    def flush_dns
      OSUtils.flush_dns
    end

    def download_bs_media(ios_platform_version, user = "app")
      if ios_platform_version != 'ios_njb_11'
        download_bs_media_ios13
        return
      end

      return true if File.exists?(@server_config['bs_media'])

      tmp_bs_media = "/tmp/bs_media.zip"
      s3_bucket = "bs-mobile"
      download_url = "ios/bs_media.zip"
      s3_profile = "bs"
      AwsS3Wrapper.download!(s3_bucket, download_url, tmp_bs_media, s3_profile)
      command = "unzip #{tmp_bs_media} -d #{@server_config['bs_media']}; \
rm #{tmp_bs_media}; \
chmod a+r #{@server_config['bs_media']}/*; \
rm -rf #{@server_config['photo_data']}/*;\
"
      OSUtils.execute(command)
    end

    # Checks and downloads media files required for ios13, for the legacy approach
    def download_bs_media_ios13(user = "app")
      return if Dir.exist?(@server_config['bs_media_ios13']) && !File.exist?(REDOWNLOAD_IOS13_MEDIA)

      FileUtils.rm_rf(@server_config['bs_media_ios13'])
      FileUtils.rm_rf(REDOWNLOAD_IOS13_MEDIA)

      tmp_bs_media = "/tmp/bs_media_ios13.zip"
      download_url = "ios/bs_media_ios13.zip"
      s3_profile = "bs"
      s3_bucket = @server_config["environment"] == "prod" ? "bs-mobile" : "bs-mobile-stag"
      AwsS3Wrapper.download!(s3_bucket, download_url, tmp_bs_media, s3_profile)
      command = "unzip #{tmp_bs_media} -d #{@server_config['bs_media_ios13']}; \
rm #{tmp_bs_media}; \
chown -R #{user} #{@server_config['bs_media_ios13']}"
      OSUtils.execute(command)
    end

    def ensure_idevice_libraries_present
      missing_libraries = []
      missing_libraries << 'idevicevideoproxy' unless idevicevideoproxy_is_present?
      missing_libraries << 'ifuse' unless ifuse_binary_is_present?
      raise MachineCheckException.new("idevice libraries missing: #{missing_libraries}", @server_config["environment"]) unless missing_libraries.empty?
    end

    def ensure_correct_developer_symbols_are_present # rubocop:todo Metrics/AbcSize
      developer_symbols_list = @server_config['developer_symbols']
      device_config = File.read(@server_config["config_json_file"])
      os_versions = Utils.get_device_versions_on_machine(device_config).compact # skips nil values (devices off usb)
      BrowserStack.logger.info("os versions for symbols: #{os_versions}")
      return if os_versions.empty?

      symbol_dir_path = "/Users/<USER>/Library/Developer/Xcode/iOS DeviceSupport/"
      Dir.mkdir(symbol_dir_path) unless Dir.exists?(symbol_dir_path)
      list_symbols_dirs = Dir.entries(symbol_dir_path)
      symbol_dir_path.sub!(' ', '\\\\ ')
      list_symbols = []
      list_symbols_dirs.each do |dir_name|
        list_symbols << [dir_name.split[0], dir_name.split[2]].join unless dir_name =~ /^\.\.?$/
      end

      symbols_to_add, symbols_to_remove = Utils.calculate_symbols_diff(os_versions, list_symbols)
      BrowserStack.logger.info("Symbols to download: #{symbols_to_add}")
      return if symbols_to_add.empty? && symbols_to_remove.empty?

      # Download required symbols
      failures = []
      symbols_to_add.each do |symbol|
        download = Utils.download_symbol(symbol, @server_config['download_endpoint'], developer_symbols_list)
        failures.push(symbol) unless download.empty?
      rescue => e
        BrowserStack.logger.error("Failed to download symbol #{symbol}: #{e.message} #{e.backtrace}")
        failures.push(symbol)
      end
      raise MachineCheckException.new("Unable to download symbols: #{failures}", @server_config["environment"]) unless failures.empty?

      # Remove unnecessary symbols
      Dir.glob("#{symbol_dir_path}/*").each do |file|
        needed = false
        os_versions.each do |version|
          if file.to_s.include?("#{version} (")
            needed = true
            break
          end
        end
        FileUtils.rm_rf(file) unless needed
      end
    end

    def ensure_disk_images_are_present
      developer_disk_images = @server_config['developer_disk_images']
      download_endpoint = @server_config['download_endpoint']
      disk_images_path = Utils.developer_disk_images_dir

      if developer_disk_images.nil? || download_endpoint.nil?
        BrowserStack.logger.warn('Skipping ensure_disk_images_are_present due to missing config')
        return
      end

      list_image_dirs = Dir.entries(disk_images_path)
      disk_images_path.sub!(' ', '\\\\ ')
      list_images = []
      list_image_dirs.each do |dir_name|
        list_images << dir_name.split[0] unless dir_name =~ /^\.\.?$/
      end

      BrowserStack.logger.info "Found developer disk images: #{list_images}"
      to_download = developer_disk_images - (developer_disk_images & list_images)
      to_download.each do |image_version|
        Utils.download_developer_disk_image(image_version)
      end
    end

    def ensure_pointed_to_prod
      pointed_endpoint = @server_config['static_conf']['rails_endpoint']
      prod_rails_endpoint = 'browserstack.com'
      raise MachineCheckException.new("Prod machine not pointed to #{prod_rails_endpoint}", @server_config["environment"]) if !pointed_endpoint.include?(prod_rails_endpoint) && @server_config["environment"] == 'prod'
    end

    def ensure_correct_consul_address

      machine_ip = get_machine_ip
      machine_hostname = "mobile-#{machine_ip.gsub('.', '-')}"

      begin
        consul_machine_info = `consul catalog nodes -filter='Node contains "#{machine_hostname}"'`
        raise MachineCheckException.new("Machine not present in consul", @server_config["environment"]) if consul_machine_info.include?('No nodes match')

        consul_machine_info.split("\n").each do |machine_info|
          consul_hostname = machine_info.split[0]
          consul_address = machine_info.split[2]
          if consul_hostname.include?(machine_hostname) && consul_address != machine_ip
            OSUtils.restart_plist("#{@server_config['plist_dir_system']}com.hashicorp.consul.plist")
            raise MachineCheckException.new("Incorrect Consul address. Restarting Consul", @server_config["environment"])
          end
        end
      rescue => e
        raise MachineCheckException.new("Error checking consul: #{e.message}", @server_config["environment"])
      end
    end

    def brew_package_version(package, user = "app")

      brew_path = OSUtils.execute("which brew").strip
      package_info = JSON.parse(OSUtils.execute("sudo su -l #{user} -c '#{brew_path} info --json=v1 #{package}'"))
      package_info[0]["linked_keg"]

    rescue => e
      ""

    end

    def sync_time
      t1 = Time.now.to_i
      `sudo touch /var/db/ntp-kod` # Supress warning message for sntp
      `sudo sntp -sS pool.ntp.org`
      t2 = Time.now.to_i
      BrowserStack.logger.info("Time adjusted (drift: #{t2 - t1} seconds)")
    end

    def machine_rebooted_recently?
      last_boot_time = OSUtils.last_machine_boottime
      current_time = Time.now.to_i
      time_difference = current_time - last_boot_time
      time_difference < 60 * 60 * 6       # 6 hours
    end

    def ensure_internet_sharing_enabled_on_all_devices
      BrowserStack.logger.info("Running ensure_internet_sharing_enabled_on_all_devices..")

      return if internet_sharing_setup_on_all_devices? ||
        File.exists?(@server_config["machine_blocked_in_rails_file"]) ||
        Configuration["default_platform"] == "tvos"

      BrowserStack.logger.info("Setup internet sharing...")
      machine_ip = get_machine_ip
      unless @machine_on_prod
        run_internet_sharing_deploy
        return
      end
      (1..10).each do |n|
        if BlockMachine.machine_can_be_blocked?(@mobile_dashboard_auth, machine_ip, @server_config["force_machine_block_file"])
          BlockMachine.block_machine_in_loop(@mobile_dashboard_auth, machine_ip)
          BrowserStack.logger.info "#{machine_ip} Blocked successfully!"
          run_internet_sharing_deploy
          break
        else
          BrowserStack.logger.info "Try ##{n}: Machine can't be blocked. Retrying..."
        end
        sleep 120
      end
    end

    def run_internet_sharing_deploy
      BrowserStack.logger.info "Create file #{@server_config['machine_blocked_in_rails_file']}"
      FileUtils.touch(@server_config["machine_blocked_in_rails_file"])
      OSUtils.execute("cp #{@server_config['mobile_root']}/lib/utils/com.apple.nat.plist /tmp/com.apple.nat.plist.template")
      OSUtils.execute("sudo /bin/bash #{@server_config['mobile_root']}/lib/utils/generate_nat_plist.sh")
      if machine_rebooted_recently?
        BrowserStack.logger.info "Skipping machine reboot, was rebooted recently."
      else
        # If the network Interface is in bridge, but not active, reboot would bring the interface up.
        BrowserStack.logger.info "Rebooting machine in the hope for good!"
        OSUtils.execute("sudo reboot")
      end
    end

    def get_machine_ip
      machine_ip = begin
        File.read(@server_config["ip_file"])
      rescue
        nil
      end
      raise "whatsmyip file not found at #{@server_config['ip_file']} or is empty" if machine_ip.nil? || machine_ip.empty?

      machine_ip.chomp
    end

    def get_devices_offline_with_reason(reasons)
      devices = []
      config_json = begin
        JSON.parse(File.read(@server_config["config_json_file"]))
      rescue
        nil
      end
      if !config_json.nil? && !config_json.empty?
        config_json['devices'].each do |device, config|
          devices << device if reasons.include? config['offline_reason']
        end
      end
      devices
    end

    def get_devices
      devices = []
      config_json = begin
        JSON.parse(File.read(@server_config["config_json_file"]))
      rescue
        nil
      end
      if !config_json.nil? && !config_json.empty?
        config_json['devices'].each do |device, _config|
          devices << device
        end
      end
      devices
    end

    def bridge100_setup_on_machine?
      unless OSUtils.bridge100_exists?
        BrowserStack.logger.warn('bridge100 not found on machine')
        BrowserStack::Zombie.push_logs("bridge100-issue", "bridge100 not found")
        return false
      end

      unless OSUtils.bridge100_has_local_inet?
        BrowserStack.logger.warn('bridge100 does not have a local inet')
        BrowserStack::Zombie.push_logs("bridge100-issue", "No local inet")
        return false
      end
      true
    end

    def internet_sharing_setup_on_all_devices?
      return true if get_devices.empty?
      return false unless bridge100_setup_on_machine?

      required = get_devices_offline_with_reason(["device's network interface not in bridge, is internet sharing enabled for this device?", "device's network interface not found", "device is not using internet sharing", "device's network interface is not active"])
      BrowserStack.logger.info "Required devices according to offline reasons: #{required}"

      completed = Dir.glob(File.join(@server_config['state_files_dir'], "nat_setup_complete_**")).map { |e| e.gsub(/^.*nat_setup_complete_/, "") }
      BrowserStack.logger.info "Completed devices according to state file: #{completed}"

      config_json = begin
        JSON.parse(File.read(@server_config['config_json_file']))
      rescue
        {}
      end

      # Need to create a temp copy to not delete from the same array
      completed_temp = completed.dup

      completed_temp.each do |device|
        next if config_json['devices'][device]['last_online'].nil?

        last_online = Time.parse(config_json['devices'][device]['last_online'])
        completed.delete(device) if OSUtils.last_machine_boottime < last_online.to_i
      end

      BrowserStack.logger.info "Completed devices after checking last online and machine reboot time: #{completed}"

      BrowserStack.logger.info "Need to setup internet sharing on the following devices: #{required - completed}"

      (required - completed).empty?
    end

    def ensure_machine_is_unblocked_in_rails
      machine_ip = get_machine_ip
      return unless File.exists?(@server_config["machine_blocked_in_rails_file"])

      unless @machine_on_prod
        FileUtils.rm(@server_config["machine_blocked_in_rails_file"])
        BrowserStack.logger.info "#{machine_ip} Finished setting up internet sharing."
        return
      end
      BrowserStack.logger.info "#{machine_ip} is blocked. Trying to unblock."
      begin
        BlockMachine.unblock_machine(@mobile_dashboard_auth, machine_ip)
        FileUtils.rm(@server_config["machine_blocked_in_rails_file"])
        BrowserStack.logger.info "#{machine_ip} unblocked successfully!"
        BrowserStack.logger.info "#{machine_ip} Finished setting up internet sharing."
      rescue
        BrowserStack.logger.info "#{machine_ip} Machine unblock failed in #{@mobile_dashboard_auth['url']}"
        raise MachineCheckException.new("#{machine_ip} Machine unblock failed in #{@mobile_dashboard_auth['url']}", @server_config["environment"])
      end
    end

    def correct_file_permissions
      files_to_check = ["/var/log/browserstack/mobile_poll.log",
                        "/var/log/browserstack/device_poll.log",
                        "/var/log/browserstack/device_check_plist.log",
                        "/usr/local/.browserstack/config/config.json",
                        "/usr/local/.browserstack/config/apps/cache",
                        "/Users/<USER>/Library/Developer/Xcode/iOS\\ DeviceSupport",
                        "/usr/local/.browserstack/cleanup_status.sqlite"]
      files_to_check.each do |file|
        user = OSUtils.execute "stat #{file} | awk '{print $5}'"
        OSUtils.execute "sudo chown app #{file}" if user != "app"
      end
    end

    def ensure_logrotate_config_updated
      config = "#{@server_config['mobile_root']}/deploy/logrotate/browserstack"
      destination = "/etc/logrotate.d/browserstack"
      begin
        OSUtils.execute "sudo cp #{config} #{destination}" unless FileUtils.compare_file(config, destination)
      rescue => e
        BrowserStack.logger.info "logrotate config update failed: #{e.message}" + e.backtrace.join("\n")
        raise MachineCheckException.new("logrotate config update failed", @server_config["environment"])
      end
    end

    def remove_old_files
      # no files in session start directory to make sure we run this only when not running any session
      if Dir.children("#{@server_config['state_files_dir']}/session_start").count == 0
        core_device_cache = "/Users/<USER>/Library/Containers/com.apple.CoreDevice.CoreDeviceService/Data/Library/Caches/AppInstallationBinaryDeltas"
        OSUtils.remove_folders_older_than(core_device_cache, 1440)
      end

      OSUtils.remove_folders_older_than("/tmp", 1440)
      OSUtils.remove_folders_older_than("/Users/<USER>/macOS_*.app", 1440)
      OSUtils.remove_folders_older_than("/Users/<USER>/ventura_*.app", 1440)
      OSUtils.remove_files_older_than("/var/log/browserstack", 15 * 24 * 60)
      OSUtils.remove_files_older_than("/Users/<USER>/macOS_*.zip", 1440)
      OSUtils.remove_files_older_than("/Users/<USER>/iPhone*.ipsw", 1440)
      OSUtils.remove_files_older_than("/Users/<USER>/iPad*.ipsw", 1440)
      OSUtils.remove_files_older_than("/Users/<USER>/Xcode*.xip", 1440)
      OSUtils.remove_files_older_than("/Users/<USER>/*symbol.tar.gz", 1440)
    rescue => e
      BrowserStack.logger.info "Remove Old File Failed: #{e.message}" + e.backtrace.join("\n")
    end

    def ensure_ssh_password_login_disabled
      machine_ip = get_machine_ip
      password_auth_present = OSUtils.execute("sudo cat /etc/ssh/sshd_config | grep -E 'PasswordAuthentication (yes|no)'")
      if password_auth_present.include?('yes') || password_auth_present.include?('#')
        OSUtils.execute("sudo cat /etc/ssh/sshd_config | sed -E 's/#*PasswordAuthentication (yes|no)/PasswordAuthentication no/g' |  sed -E 's/#*ChallengeResponseAuthentication (yes|no)/ChallengeResponseAuthentication no/g'> /tmp/ssh_password_auth_change")
        OSUtils.execute('chmod 755 /tmp/ssh_password_auth_change')
        OSUtils.execute('sudo mv /tmp/ssh_password_auth_change /etc/ssh/sshd_config')
        OSUtils.execute('sudo chown root:wheel /etc/ssh/sshd_config')
        OSUtils.execute('sudo chmod 644 /etc/ssh/sshd_config')
        OSUtils.execute('sudo launchctl stop com.openssh.sshd && sudo launchctl start com.openssh.sshd')
        BrowserStack.logger.info "#{machine_ip} ssh_config updated in #{@mobile_dashboard_auth['url']}"
      end
    rescue
      BrowserStack.logger.info "#{machine_ip} SSH password auth disable check failed in #{@mobile_dashboard_auth['url']}"
      raise MachineCheckException.new("#{machine_ip} SSH password auth disable check failed in #{@mobile_dashboard_auth['url']}", @server_config["environment"])
    end

    def add_machine_to_ansible_inventory # rubocop:todo Metrics/AbcSize
      machine_ip = get_machine_ip
      params = {
        machine_ip: machine_ip,
        status: 'online',
        status_description: 'available',
        env: @server_config['machine_env'],
        region: @server_config['static_conf']['region'],
        sub_region: @server_config['static_conf']['sub_region'],
        platform: @server_config['platform_category']
      }
      auth = {
        username: @server_config['inventory_api_user'],
        password: @server_config['inventory_api_pass']
      }
      machine_inventory_url = "#{@server_config['machine_inventory_ip']}:#{@server_config['machine_inventory_port']}/api/v1/machine"

      response = HttpUtils.get_response("#{machine_inventory_url}/#{machine_ip}", auth)
      case response.code.to_i
      when 404
        BrowserStack.logger.info "#{machine_ip}: adding to machine inventory at #{machine_inventory_url} with #{params}"
        post_response = HttpUtils.send_post_raw(machine_inventory_url, params, auth)
        BrowserStack.logger.info "Post response code #{post_response.status} #{post_response.body}"
      when 200
        response_info = JSON.parse(response.body)
        update_machine_inventory(auth) if response_info['platform'] != @server_config['platform_category'] || response_info['region'] != @server_config['static_conf']['region'] || response_info['sub_region'] != @server_config['static_conf']['sub_region']
        BrowserStack.logger.info "#{machine_ip}: already added in machine inventory"
      else
        BrowserStack.logger.error "#{machine_ip}: Could not check inventory service"
      end
    rescue => e
      BrowserStack.logger.error "#{machine_ip}: adding machine to ansible inventory failed in #{machine_inventory_url} - #{e.message}"
      raise MachineCheckException.new("#{machine_ip}: adding machine to ansible inventory failed in #{machine_inventory_url}", @server_config["environment"])
    end

    def ensure_nomad_running

      nomad_binary = "/usr/local/bin/nomad"
      nomad_binary_present = File.file?(nomad_binary)
      nomad_process = `ps aux | grep -v grep | grep '/opt/nomad/nomad.d'`
      raise MachineCheckException.new("Nomad not installed", @server_config["environment"]) unless nomad_binary_present

      raise MachineCheckException.new("Nomad not running", @server_config["environment"]) if nomad_process.empty?

      true
    end

    def update_machine_inventory(auth = nil)
      machine_ip = get_machine_ip
      machine_inventory_url = "#{@server_config['machine_inventory_ip']}:#{@server_config['machine_inventory_port']}/api/v1/machine/#{machine_ip}"

      machine_data = {
        platform: @server_config['platform_category'],
        region: @server_config['static_conf']['region'],
        sub_region: @server_config['static_conf']['sub_region']
      }

      response = HttpUtils.make_patch_request(machine_inventory_url, machine_data, auth)

      if response.status.to_i != 200
        BrowserStack.logger.info "#{machine_ip}: update machine inventory api unsuccessful"
        BrowserStack::Zombie.push_logs("platform-changed", "machine inventory patch", { "success" => "false" })
        @influxdb_client.event('NA', 'update-inventory', subcomponent: 'inventory', is_error: true)
      else
        BrowserStack::Zombie.push_logs("platform-changed", "machine inventory patch", { "success" => "true" })
        @influxdb_client.event('NA', 'update-inventory', subcomponent: 'inventory')
      end
    rescue => e
      BrowserStack.logger.info "#{machine_ip}: updating machine to ansible inventory failed in #{@mobile_dashboard_auth['url']} - #{e.message}"
      raise MachineCheckException.new("#{machine_ip}: updating machine to ansible inventory failed in #{@mobile_dashboard_auth['url']}", @server_config["environment"])
    end

    def ensure_device_logger_not_stale
      restart_file_location = "/usr/local/.browserstack/device-logger/tmp/restart.txt"
      time_to_consider_stale = 300
      expected_device_logger_pids = 2

      # returns hierarchically sorted list of PIDs: cluster main, cluster child
      device_logger_pids = `#{PSTREE} | grep "[d]evice-logger/index.js" | sed 's/[^0-9 ]*//g' | awk '{print $1}'`.chomp.split

      unless device_logger_pids.size == expected_device_logger_pids
        BrowserStack::Zombie.push_logs("device-logger-stale-check", "incorrect number of processes", { "success" => "false" })

        raise MachineCheckException, "Expected #{expected_device_logger_pids} device logger processes, got #{device_logger_pids}"
      end

      if File.exist?(restart_file_location)
        restart_file_modified = File.mtime(restart_file_location).to_i
        process_start_time = Time.parse(`ps -olstart= #{device_logger_pids.last}`.chomp).to_i

        if restart_file_modified - process_start_time > time_to_consider_stale
          BrowserStack::Zombie.push_logs("device-logger-stale-check", "device logger process's stale", { "success" => "false" })
          raise MachineCheckException, "Device logger hasn't been restart for #{time_to_consider_stale} seconds"
        end
      end
    end

    def ensure_launch_daemon_dir_is_present_for_app_user
      machine_ip = get_machine_ip
      username = 'app'
      launch_daemon_dir = "/Users/<USER>/Library/LaunchDaemons/"
      FileUtils.mkdir_p(launch_daemon_dir) unless File.exists?(launch_daemon_dir)
      FileUtils.chown_R(username, nil, launch_daemon_dir) if Etc.getpwuid(File.stat(launch_daemon_dir).uid).name != username
      raise MachineCheckException.new("#{machine_ip}: Cannot create #{launch_daemon_dir} directory", @server_config["environment"]) unless File.exists?(launch_daemon_dir)
      raise MachineCheckException.new("#{machine_ip}: launch_daemon_dir has different owner", @server_config["environment"]) if Etc.getpwuid(File.stat(launch_daemon_dir).uid).name != username
    end

    def idevicevideoproxy_is_present?
      idevicevideoproxy_path = OSUtils.which('idevicevideoproxy').chomp
      return true if File.exists?(idevicevideoproxy_path)

      iproxy_path = OSUtils.which('iproxy').chomp
      idevicevideoproxy_dir_path = "#{File.dirname(iproxy_path)}/idevicevideoproxy"
      FileUtils.copy_file(iproxy_path, idevicevideoproxy_dir_path)
      help_output, status = OSUtils.execute("#{idevicevideoproxy_path} -h", true)
      status.to_i == 0
    end

    # Attempts to bring the 45671 server up if it is down by killing stale
    # processes still listening on the :45671 port.
    def ensure_main_device_server_is_up
      if server_running?
        BrowserStack.logger.info "45671 server is up all good"
        return
      else
        BrowserStack.logger.info "45671 server is down, attempting to fix"
      end

      # Check if issue is because of port conflict
      port_conflict = `tail -100 /var/log/browserstack/realmobile_plist.log`.strip.scan(/EADDRINUSE/).count > 0

      unless port_conflict
        return if server_running? # cross check before panicking

        BrowserStack.logger.info "45671 server is down, not sure how to bring it back"
        Zombie.push_logs('ios-njb-45671-down', "45671 is not responding" )
        return
      end

      BrowserStack.logger.info "45671 port is down probably due to port conflict, attempting to fix"
      lsof_output = `lsof -i:45671`
      BrowserStack.logger.info "lsof output: #{lsof_output}"

      kill_processes_listening_on_port
      if server_running?(defer: 10)
        BrowserStack.logger.info "Server is up now"
        Zombie.push_logs('ios-njb-45671-up', "45671 was down due to port conflict")
        return
      end

      kill_processes_listening_on_port(signal: "-9")
      if server_running?(defer: 20)
        BrowserStack.logger.info "Server is up now after force killing forks"
        Zombie.push_logs('ios-njb-45671-up', "45671 was down due to port conflict")
      else
        BrowserStack.logger.info "45671 server is down due to port conflict, unable to bring it back"
        Zombie.push_logs('ios-njb-45671-down', "failed to bring 45671 up after port conflict")
      end
    end

    # Downloads & installs the new apple intermediate cert which will expire in Feb, 2030,
    # the current intermediate cert expires in Feb, 2023 which can't be used with provisioning
    # certs issued after Jan, 2021.
    #
    # For more info see https://developer.apple.com/support/wwdr-intermediate-certificate/
    def ensure_apple_intermediate_cert_present
      apple_new_intermediate_cert_sha1 = Configuration['apple_new_intermediate_cert_sha1']
      apple_intermediate_cert_name = Configuration['apple_intermediate_cert_name']
      system_keychain_path = Configuration['system_keychain_path']
      browserstack_keychain_path = Configuration['browserstack_keychain_path']
      apple_intermediate_required_md5sum = Configuration['apple_intermediate_cert_file_md5sum']

      system_intermediate_cert_shas = OSUtils.certificates_sha1(apple_intermediate_cert_name, system_keychain_path)
      bs_intermediate_cert_shas = OSUtils.certificates_sha1(apple_intermediate_cert_name, browserstack_keychain_path)

      if system_intermediate_cert_shas.include?(apple_new_intermediate_cert_sha1) &&
        bs_intermediate_cert_shas.include?(apple_new_intermediate_cert_sha1)
        BrowserStack.logger.info("Apple's new intermediate cert present in system & BrowserStack keychains")
        return
      end

      apple_intermediate_file = "AppleWWDRCAG3.cer"
      apple_intermediate_file_path = "/tmp/#{apple_intermediate_file}"
      apple_intermediate_file_url = "#{Configuration['download_endpoint']}/#{apple_intermediate_file}"

      begin
        BrowserStack::HttpUtils.download(apple_intermediate_file_url, apple_intermediate_file_path)
      rescue => e
        BrowserStack.logger.info("Unable to download the apple's intermediate cert: #{e.message}; #{e.backtrace}")
        Zombie.push_logs('intermediate-cert-download-failed', e.message)
        raise e
      end

      apple_intermediate_cert_md5sum = Utils.get_md5_checksum_for_file(apple_intermediate_file_path)
      if apple_intermediate_cert_md5sum != apple_intermediate_required_md5sum
        BrowserStack.logger.error("md5sum of apple intermediate cert downloaded doesn't match: required: #{apple_intermediate_required_md5sum} vs. found: #{apple_intermediate_cert_md5sum}")
        raise "md5sum of apple intermediate doesn't match"
      end

      [system_keychain_path, browserstack_keychain_path].each do |keychain_path|
        OSUtils.add_trusted_cert(apple_intermediate_file_path, keychain_path)
      end

      Zombie.push_logs('apple-intermediate-cert-downloaded', "")
      FileUtils.rm_f(apple_intermediate_file_path)
    end

    def remove_debug_screenshots
      days = 10
      debug_screenshots_dir = '/Users/<USER>/debug_screenshots'

      # Ensure directory exists
      FileUtils.mkdir_p(debug_screenshots_dir) unless File.exist?(debug_screenshots_dir)

      # Ensure app user, not root
      system("sudo chown -R app:staff #{debug_screenshots_dir}")

      # Delete old screenshots
      (Dir.entries(debug_screenshots_dir) - ['.', '..']).each do |entry|
        entry = "#{debug_screenshots_dir}/#{entry}"
        next unless File.exist?(entry)
        next if Time.now - File.mtime(entry) < (60 * 60 * 24 * days)

        FileUtils.rm_rf(entry)
      end
    end

    def remove_mcspt_metrics
      days = 10
      mcspt_metrics_dir = '/var/log/browserstack/csp/sessions'
      BrowserStack.logger.info("[remove_mcspt_metrics] Method called!!")

      # Ensure app user has access, not root
      system("sudo chmod -R 777 /var/log/browserstack/csp")

      # Ensure directory exists
      return nil unless File.exist?(mcspt_metrics_dir)

      BrowserStack.logger.info("[remove_mcspt_metrics] dir exists!!")

      # Delete old screenshots
      (Dir.entries(mcspt_metrics_dir) - ['.', '..']).each do |entry|
        BrowserStack.logger.info("[remove_mcspt_metrics] entry #{entry}!!")
        entry = "#{mcspt_metrics_dir}/#{entry}"
        next unless File.exist?(entry)

        BrowserStack.logger.info("[remove_mcspt_metrics] entry mtime: #{File.mtime(entry)}!!")
        next if Time.now - File.mtime(entry) < (60 * 60 * 24 * days)

        FileUtils.rm_rf(entry)
      end
    end

    def instrument_crypto_mining
      CryptoMiningDetectionHelper.new.instrument_crypto_mining
    end

    def update_domain_blocking_list
      fileloc = '/etc/hosts'
      destination_file = '/usr/local/.browserstack/privoxy/privoxy_domain_blocking.conf'
      file_cleaned_state_loc = "#{@server_config['state_files_dir']}/domain_block_cleaned"
      BrowserStack.logger.info("[update_domain_blocking_list] Method called!!")

      FileUtils.touch(destination_file) unless File.exist?(destination_file)

      feature_enabled = !File.exist?("#{@server_config['state_files_dir']}/disable_domain_block")
      is_file_cleaned = File.exist?(file_cleaned_state_loc)

      if File.exist?(fileloc)
        modified_time_etc = File.mtime(fileloc)
        modified_time_privoxy = File.mtime(destination_file)
        if feature_enabled && (modified_time_privoxy <= modified_time_etc || is_file_cleaned)
          parse_and_update_conf(fileloc, destination_file)
          File.delete(file_cleaned_state_loc) if is_file_cleaned
        elsif !feature_enabled && !is_file_cleaned
          BrowserStack.logger.info("[update_domain_blocking_list] File cleaned - #{destination_file}")
          File.open(destination_file, 'w') { |file| file.truncate(0) }
          FileUtils.touch(file_cleaned_state_loc)
        end
      end
    rescue Exception => e
      raise MachineCheckException.new("Privoxy domain blocking file update error: #{e.message}", @server_config["environment"])
    end

    def privoxy_block_whitelist_domain # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
      env = @server_config["environment"].strip
      feature_enabled = File.exist?(GROUP_DOMAIN_BLOCKING_FLAG)
      BrowserStack.logger.info("[privoxy_block_whitelist_domain] feature_enabled: #{feature_enabled}")
      if feature_enabled
        bucket_name = S3_PLATFORM_BUCKET
        resp = `curl -s https://s3.amazonaws.com/#{bucket_name}/domain_blocking/domain_status.json -o #{DOMAIN_STATUS_JSON_FILE} -z #{DOMAIN_STATUS_JSON_FILE} -R -w '%{http_code}'`
        resp_code = resp.to_s.strip
        create_file_if_not_exist(BLOCK_DOMAINS_FILE)
        create_file_if_not_exist(WHITELIST_DOMAINS_FILE)
        create_file_if_not_exist(DOMAINS_CONFIG_JSON_FILE)
        is_file_cleaned = File.exist?(CLEANED_STATE_FILE)
        BrowserStack.logger.info("[privoxy_block_whitelist_domain] curl_resp: #{resp}, is_file_cleaned: #{is_file_cleaned}")
        if resp_code != "200" && !(is_file_cleaned && resp_code == "304")
          if resp.to_s != "304"
            BrowserStack::Zombie.push_logs('privoxy_store_domains_config', 'parsed_file_invalid', { "resp" => resp, "content" => File.read(DOMAIN_STATUS_JSON_FILE) } )
            File.delete(DOMAIN_STATUS_JSON_FILE)
          end
          return
        end

        begin
          config_json = JSON.parse(File.read(DOMAIN_STATUS_JSON_FILE))
        rescue Exception => e
          BrowserStack.logger.info("[privoxy_block_whitelist_domain] msg: #{e.message}, backtrace: #{e.backtrace}")
          raise "Error in file parsing. #{e.message}"
        end
        BrowserStack.logger.info("[privoxy_block_whitelist_domain] config_json_count= #{config_json.count}")
        blocked_domains = []
        whitelist_domains = []
        etchost_domains = []
        processed_domains = []
        config_json.each do |domain, conf|
          host_ip = conf["host_ip"]
          if privoxy_blanket_domain?(conf)
            if conf["status"] == "Blocked"
              blocked_domains << domain
            else
              whitelist_domains << domain
            end
            processed_domains << domain
          end
          # rubocop:disable Style/Next
          if etchost_domain?(conf, host_ip)
            dmn = begin
              PublicSuffix.parse(domain)
            rescue
              nil
            end
            etchost_domains << "#{host_ip} #{domain}"
            etchost_domains << "#{host_ip} www.#{domain}" if host_ip == BLOCKED_DOMAIN_PAGE_IP && !dmn.nil? && dmn.subdomain.nil?
            processed_domains << domain
          end
          # rubocop:enable Style/Next
        end
        add_domains_to_file(BLOCK_DOMAINS_FILE, blocked_domains, "block")
        add_domains_to_file(WHITELIST_DOMAINS_FILE, whitelist_domains, "whitelist")
        add_domains_to_file(ETC_HOST_DOMAINS, etchost_domains, "etchost")
        store_group_level_config(config_json, DOMAINS_CONFIG_JSON_FILE, processed_domains)
        File.delete(CLEANED_STATE_FILE) if is_file_cleaned
      else
        clean_domain_status_privoxy(BLOCK_DOMAINS_FILE)
        clean_domain_status_privoxy(WHITELIST_DOMAINS_FILE)
        clean_domain_status_privoxy(DOMAINS_CONFIG_JSON_FILE)
        create_file_if_not_exist(CLEANED_STATE_FILE)
      end
    rescue Exception => e
      BrowserStack.logger.info("[privoxy_block_whitelist_domain] error: #{e.message}, backtrace: #{e.backtrace}")
      raise MachineCheckException.new("privoxy_block_whitelist_domain error: #{e.message}", @server_config["environment"])
    end

    def download_app_bundleid_block_config
      download_bundleid_config_file
      BrowserStack.logger.info("[download_app_bundleid_block_config] File downloaded!!")
    rescue Exception => e
      BrowserStack.logger.info("[download_app_bundleid_block_config] error: #{e.message}, backtrace: #{e.backtrace}")
      BrowserStack::Zombie.push_logs("app_bundleid_block_config", "file_download_failed", { "message" => e.message, "backtrace" => e.backtrace })
      raise MachineCheckException.new("download_app_bundleid_block_config error: #{e.message}", @server_config["environment"])
    end

    def clear_ios_backup_files_weekly
      files_exists = File.exists?(IOS_BACKUP_CLEANUP_FLAG)
      perform_cleanup = !files_exists || (files_exists && (Time.now - File.mtime(IOS_BACKUP_CLEANUP_FLAG)) > 7 * 24 * 60 * 60)
      if Dir.exist?(IOS_BACKUP_DIR) && perform_cleanup
        ios_backup_files = Dir.children(IOS_BACKUP_DIR)
        devices_on_terminal = Idevice.device_list
        ios_backup_files.each do |file|
          full_path = File.join(IOS_BACKUP_DIR, file)
          unless devices_on_terminal.include?(file)
            FileUtils.rm_rf(full_path)
            BrowserStack.logger.info("clear_ios_backup_files_weekly: #{file} deleted from ios_backup.")
          end
        end
        f = File.new(IOS_BACKUP_CLEANUP_FLAG, "w")
        f.close
      end
    rescue Exception => e
      BrowserStack.logger.info("[clear_ios_backup_files_weekly] error: #{e.message}, backtrace: #{e.backtrace}")
      BrowserStack::Zombie.push_logs("clear_ios_backup_files_weekly", "backup_cleanup_failed", { "message" => e.message, "backtrace" => e.backtrace })
      raise MachineCheckException.new("clear_ios_backup_files_weekly error: #{e.message}", @server_config["environment"])
    end

    def instrument_usb_removals
      num_usb_removals_command = "log show --predicate 'eventMessage contains \"USB\"' --info --debug --last 1h | grep -iE 'com.apple.imagecapture.*removed.*usb' | wc -l"
      num_usb_removals = OSUtils.execute(num_usb_removals_command).to_i
      BrowserStack::Zombie.push_logs("high_usb_removals", "" , { "data" => num_usb_removals }) if num_usb_removals >= 10
    end

    def update_vpp_token_expiry_file
      machine_not_eligible = VPP_TOKEN == "" || VPP_TOKEN == "<no value>" || !VPP_CHECKS[:regions].include?(@server_config['static_conf']['region'])
      BrowserStack.logger.info "Updating vpp token expiry file? #{!machine_not_eligible}"
      return if machine_not_eligible # fail safe

      if File.exist?(VPP_TOKEN_EXPIRY_FILE) && (Time.now - File.mtime(VPP_TOKEN_EXPIRY_FILE)) <= 24 * 60 * 60
        BrowserStack.logger.info "VPP token expiry file exists and was modified in the last 24 hours."
      else
        BrowserStack.logger.info "File does not exist or was not modified in the last 24 hours. Updating the file..."
        expiry_date = VPPUtils.token_expiry
        FileUtils.mkdir_p(File.dirname(VPP_TOKEN_EXPIRY_FILE))
        File.write(VPP_TOKEN_EXPIRY_FILE, expiry_date)
        BrowserStack.logger.info "Content written to file: #{VPP_TOKEN_EXPIRY_FILE}"

        expiry_date = DateTime.parse(expiry_date)
        current_date = DateTime.now
        remaining_days = (expiry_date - current_date).to_i

        if remaining_days <= 15
          message = "VPP Token is valid for only #{remaining_days} days, please renew"
          BrowserStack.logger.info(message)
          BrowserStack::Zombie.push_logs("vpp-token-expiring", "update_vpp_token_expiry_file" , { "data" => message })
        end
      end
    rescue => e
      BrowserStack::Zombie.push_logs("vpp-installation-failure", "update_vpp_token_expiry_file" , { "data" => e.message })
    end

    def kill_usb_process_with_high_removals
      region_list = ["us-east-1", "us-west-1", "eu-west-1"]
      BrowserStack.logger.info "checking if high usb removal process is there"
      #only checks in these regions
      if region_list.include?(@server_config['static_conf']['region']) && usb_hub_file_touched_due? && no_session_running? && no_cleanup_running?
        # no files in session start directory to make sure we run this only when not running any session
        usb_hub_removal_num = usb_hub_removal_count
        BrowserStack.logger.info "Total usb hub removal logs count in last 1 hr is #{usb_hub_removal_num}"
        if usb_hub_removal_num > 400
          BrowserStack.logger.info "Running stop command to kill all usb hub process"
          OSUtils.execute "sudo killall -STOP -c usbd"
          FileUtils.touch("#{@server_config['state_files_dir']}/high_usb_removal")
          BrowserStack.logger.info "High USB removal machine usb process killed"
          BrowserStack::Zombie.push_logs("high_usb_removal_process_killed", "", { "data" => usb_hub_removal_num })
        else
          BrowserStack.logger.info "USB removal count is less than the threshold value"
        end
      end
    rescue => e
      BrowserStack.logger.info "Killing usb process of high usb removal machine fail: #{e.message}" + e.backtrace.join("\n")
    end

    def ensure_no_public_dns_configured
      BrowserStack.logger.info "Checking for public DNS servers in configuration..."

      dns_servers = OSUtils.execute("scutil --dns | grep 'nameserver\\[[0-9]*\\]' | awk '{print $3}'").split("\n").uniq

      public_dns_found = dns_servers.any? { |server| PUBLIC_DNS_IPS.include?(server) }

      if public_dns_found
        public_servers = dns_servers.select { |server| PUBLIC_DNS_IPS.include?(server) }
        BrowserStack.logger.error "Found public DNS servers in configuration: #{public_servers.join(', ')}"
        BrowserStack::Zombie.push_logs("public-dns-found", nil, { "dns_servers" => public_servers })

        raise MachineCheckException.new("Public DNS servers found in configuration", @server_config["environment"])
      end

      BrowserStack.logger.info "No public DNS servers found in configuration"
    end

    private

    def convert_gb_b(giga_bytes)
      giga_bytes * 1024 * 1024 * 1024
    end

    def ws_proxy_running?
      url = "http://localhost:#{@server_config['insecure_websocket_proxy_port']}/websocket_proxy_health"
      begin
        response_code = `curl -s -o /dev/null -I -w "%{http_code}" #{url}`
        BrowserStack.logger.debug("Insecure Websocket Proxy response_code = #{response_code}")
        response_code == "200"
      rescue => e
        BrowserStack.logger.error("Unable to connect to Insecure Websocket Proxy : #{e.message}")
        false
      end
    end

    def reload_ws_proxy(username)
      BrowserStack.logger.info("Reloading ws_proxy plist")
      CheckPlist.restart_service("com.browserstack.ws_proxy", ServiceType.SystemService, username)
      BrowserStack.logger.debug("Reloaded ws_proxy plist")
    end

    def validate_ws_proxy(username)
      return if ws_proxy_running?

      reload_ws_proxy(username)
      sleep 1 # Since the service is restarted, adding a small delay before next health check
      data = { "success" => "false" }
      data['success'] = "true" if ws_proxy_running?
      BrowserStack.logger.info("Insecure Websocket Proxy is now running = #{data['success']}")
      BrowserStack::Zombie.push_logs("InsecureWSProxy-plist-reload", "plist_reloaded", data)
      raise "Insecure Websocket Proxy is not running" if data['success'] == "false"

    rescue => e
      BrowserStack.logger.error("Exception while checking the Insecure Websocket Proxy : #{e.message}")
      # raise MachineCheckException.new("Insecure Websocket Proxy is not running", @server_config["environment"])
    end

    def server_running?(port: 45671, defer: 0)
      sleep defer
      r = HttpUtils.make_get_request("http://localhost:#{port}", 2)
      r.success? && r.body == ":-)"
    end

    def kill_processes_listening_on_port(port: 45671, signal: "")
      `lsof -i:45671 | grep -v PID | awk '{print $2}' | xargs kill #{signal}`
    end

    def create_file_if_not_exist(input_file_name)
      unless File.exist?(input_file_name)
        f = File.new(input_file_name, "w")
        f.close
      end
    end

    def add_domains_to_file(conf_file, domains, status="whitelist")
      privoxy_domain_list = []
      domains.each do |domain|
        domain = ".#{domain.gsub(%r{https://|http://|www\.}, '')}" if status != "etchost"
        privoxy_domain_list << domain
      end
      File.write(conf_file, privoxy_domain_list.join("\n"))
      BrowserStack::Zombie.push_logs("privoxy_domains_parse", 'add_domains_to_file_ios', { "status" => status, "domain_count" => privoxy_domain_list.count } )
    end

    def privoxy_blanket_domain?(conf)
      conf["blocking_level"].include?("privoxy") && conf["group_ids"].count == 1 &&
              conf["group_ids"].include?("all") && conf["group_category"].include?("all")
    end

    def etchost_domain?(conf, host_ip)
      conf["blocking_level"].include?("hosts") && conf["status"] == "Blocked" && conf["group_ids"].count == 1 &&
            conf["group_ids"].include?("all") && conf["group_category"].include?("all") && !host_ip.nil? &&
            [LOCALHOST_IP, BLOCKED_DOMAIN_PAGE_IP, BROADCASTHOST_IP].include?(host_ip)
    end

    def store_group_level_config(config_json, config_json_file, processed_domains)  # rubocop:todo Metrics/AbcSize
      temp_config = {}
      config_json.each do |domain, conf|
        conf_status = conf["status"]
        next if !conf["blocking_level"].include?("privoxy") || processed_domains.include?(domain)

        domain = ".#{domain.gsub(%r{https://|http://|www\.}, '')}"
        conf["group_ids"].each do |group_id|
          next if group_id == "all"

          group_id = group_id.to_i
          status = if group_id > 0
                     conf_status
                   else
                     conf_status == "Blocked" ? "Whitelisted" : "Blocked"
                   end
          grp_id = group_id.abs.to_s
          temp_config[grp_id] = {} if temp_config[grp_id].nil?
          temp_config[grp_id][status] = [] if temp_config[grp_id][status].nil?
          temp_config[grp_id][status] << domain
        end
        conf["group_category"].each do |plan|
          temp_config[plan] = {} if temp_config[plan].nil?
          temp_config[plan][conf_status] = [] if temp_config[plan][conf_status].nil?
          temp_config[plan][conf_status] << domain
        end
      end
      BrowserStack.logger.info("[privoxy_block_whitelist_domain] store_group_level_config: temp_config_count= #{temp_config.count}")
      File.write(config_json_file, JSON.pretty_generate(temp_config))
      BrowserStack::Zombie.push_logs('privoxy_store_domains_config', 'store_group_level_config_ios', { "domain_count" => temp_config.count } )
    end

    def clean_domain_status_privoxy(filename)
      File.open(filename, 'w') { |file| file.truncate(0) }
    end

    def parse_and_update_conf(fileloc, destination_file)
      content = File.read(fileloc)
      content = content.gsub(%r{https://|http://|www\.}, "")

      blocked_urls_content = content.scan(/54.197.230.202\s+(.*)/).flatten
      blocked_urls_content = blocked_urls_content.uniq
      blocked_urls_content = blocked_urls_content.sort_by { |e| e.count(".") }

      privoxy_block_list = []
      header = "{ +block{easylist} }\n"
      privoxy_block_list << header
      blocked_urls_content.each do |url|
        final_url = ".#{url}"
        privoxy_block_list << final_url
      end
      File.write(destination_file, privoxy_block_list.join("\n"))
      BrowserStack.logger.info("[update_domain_blocking_list] Privoxy conf list updated!")
    end

    def usb_hub_file_touched_due?
      file_path = "#{@server_config['state_files_dir']}/high_usb_removal"
      !File.exist?(file_path) || (File.mtime(file_path) < (Time.now - (60 * 60 * 24)))
    end

    def usb_hub_removal_count
      num_usb_removals_command = "log show --predicate 'eventMessage contains \"USB\"' --info --debug --last 1h | grep -iE 'com.apple.imagecapture.*removed.*usb' | wc -l"
      OSUtils.execute(num_usb_removals_command).to_i
    end

    def no_session_running?
      BrowserStack.logger.info "checking if session is running"
      Dir.children("#{@server_config['state_files_dir']}/session_start").count == 0
    end

    def no_cleanup_running?
      BrowserStack.logger.info "checking if cleanup is running"
      Dir.glob("#{@server_config['state_files_dir']}/cleanupdone_*").empty?
    end
  end
end
