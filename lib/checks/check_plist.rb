require 'tempfile'
require 'erb'
require 'fileutils'

require_relative '../../config/constants'
require_relative '../../lib/service_type'
require_relative '../configuration'
require_relative '../utils/osutils'
require_relative '../utils/utils'

module BrowserStack
  class CheckPlist # rubocop:todo Metrics/ClassLength
    class << self
      @@template_dir = "#{Configuration['mobile_root']}/templates/"
      @@plists_dir = [
        Configuration['plist_dir_system'],
        Configuration['plist_dir_user']
      ]

      def configure(template_dir, plists_dir_system, plists_dir_user)
        BrowserStack.logger.info('CheckPlist::configure')
        @@template_dir = template_dir
        @@plists_dir = [plists_dir_system, plists_dir_user]
      end

      def restart_service(svc_name, svc_type = ServiceType.SystemService, username = nil)
        BrowserStack.logger.info "Restarting service #{svc_name} as #{username}"
        unload_service(svc_name, svc_type, username)
        sleep 5
        load_service(svc_name, svc_type, username)
      end

      def unload_service(svc_name, svc_type = ServiceType.SystemService, username = nil)
        BrowserStack.logger.info "Unloading service #{svc_name}"
        if svc_type == ServiceType.SystemService
          OSUtils.unload_system_plist("#{@@plists_dir[svc_type]}/#{svc_name}.plist")
        else
          OSUtils.unload_plist_as("#{@@plists_dir[svc_type]}/#{svc_name}.plist", username)
        end
      end

      def load_service(svc_name, svc_type = ServiceType.SystemService, username = nil, wait_in_seconds = 2)
        BrowserStack.logger.info "Attempting to load service: #{svc_name}"
        if svc_type == ServiceType.SystemService
          OSUtils.load_system_plist("#{@@plists_dir[svc_type]}/#{svc_name}.plist")
        else
          OSUtils.load_plist_as("#{@@plists_dir[svc_type]}/#{svc_name}.plist", username)
        end
        sleep wait_in_seconds
      end

      def remove_unwanted_plists(port_list)
        unwanted_plist_type = ["appium", "ios_webkit_debug_proxy", "privoxy"].join("\\|")
        exclude_ports = port_list.join("\\|")
        [ServiceType.SystemService, ServiceType.UserService].each do |svc_type|
          location = @@plists_dir[svc_type]
          unwanted_plist = if port_list.empty?
                             `ls -1 #{location} | grep "#{unwanted_plist_type}"`.split("\n")
                           else
                             `ls -1 #{location} | grep "#{unwanted_plist_type}" | grep -v "#{exclude_ports}"`.split("\n")
                           end
          unwanted_plist.each do |unload_plist|
            BrowserStack.logger.info "Removing plist #{location + unload_plist}"
            if svc_type == ServiceType.SystemService
              OSUtils.unload_system_plist(location + unload_plist)
            else
              OSUtils.unload_plist_as(location + unload_plist)
            end
            OSUtils.execute "sudo rm #{location + unload_plist}"
          end
        end
      end

      # Nix plists are written to this location:
      # /usr/local/.browserstack/nix/services/Library/Launch(Agents/Daemons)/x.plist
      #
      # This method:
      # 1. Copies them here if required: /Library/Launch(Agents/Daemons)/x.plist
      # 2. Loads the service if not running.
      def update_nix_plist(name:, type:, restart: true)
        BrowserStack.logger.info "Updating nix plist: #{name}"

        destination = File.join(@@plists_dir[type], "#{name}.plist")
        source = File.join(NIX_SERVICE_FILES, destination)

        raise "File not found: #{source}" unless File.exist?(source)

        # Copy plist if source & destination files don't match (ignoring whitespace)
        unless OSUtils.file_compare(source, destination)
          BrowserStack.logger.info "Writing plist for service: #{name}"
          CheckPlist.unload_service(name, type) if File.exist?(destination) && restart
          OSUtils.execute("sudo cp #{source} #{destination}")
        end

        unless OSUtils.is_plist_loaded?(name, type)
          BrowserStack.logger.info "Loading plist for service: #{name}"
          CheckPlist.load_service(name, type)
        end
      end

      # Checks if plist file for the svc_name and svc_type exists already
      def check_plist_exist(svc_name, svc_type = ServiceType.SystemService)
        plist_path = "#{@@plists_dir[svc_type]}/#{svc_name}.plist"
        plist_path.is_a?(String) && File.exist?(plist_path) ? true : false
      rescue => e
        BrowserStack.logger.error "Error in check_plist_exist For #{svc_name}: #{e.message}"
        false
      end

      # Delete the plist and stop the associated service if it is running
      def delete_plist(svc_name, svc_type = ServiceType.SystemService, username = nil)
        BrowserStack.logger.info "Deleting plist for service: #{svc_name}"
        unload_service(svc_name, svc_type, username)
        plist_file = "#{@@plists_dir[svc_type]}/#{svc_name}.plist"
        if File.exist?(plist_file)
          OSUtils.execute "sudo rm #{plist_file}"
        else
          BrowserStack.logger.warn "Plist file does not exist: #{plist_file}"
        end
      end
    end

    def initialize(svc_name, svc_type, args, log_file, error_file, username, custom_path=nil, custom_hash=nil, keep_alive=true, start_calendar_interval=nil, session_create=false, set_lang_and_term=false, template='generic_plist.erb', prepend_custom_path = nil, set_appium_home=false) #rubocop:todo Metrics/ParameterLists
      raise "Templates not present: #{template_file}" unless File.exist?(template_file)

      @name = svc_name
      @type = svc_type
      @args = args
      @log_file = log_file
      @error_file = error_file
      @username = username
      @template_file = template
      @custom_hash = custom_hash
      @custom_path = custom_path
      @prepend_custom_path = prepend_custom_path
      @keep_alive = keep_alive
      @start_calendar_interval = start_calendar_interval
      @session_create = session_create
      @set_lang_and_term = set_lang_and_term
      @plist_dir = @@plists_dir[svc_type]
      @set_appium_home = set_appium_home
    end

    def plist_file
      "#{@plist_dir}/#{@name}.plist"
    end

    def template_file
      "#{@@template_dir}/#{@template_file}"
    end

    def create
      @config = {
        username: @username,
        name: @name.to_s,
        log_file: @log_file,
        error_log: @error_file,
        arguments: @args,
        custom_hash: @custom_hash,
        custom_path: @custom_path,
        prepend_custom_path: @prepend_custom_path,
        keep_alive: @keep_alive,
        session_create: @session_create,
        set_lang_and_term: @set_lang_and_term,
        start_calendar_interval: @start_calendar_interval,
        set_appium_home: @set_appium_home
      }
      plist_data = ERB.new(File.open(template_file).read).result(binding)

      temp_file = Tempfile.new('plist')
      temp_file.write(plist_data)
      temp_file.close

      if !File.exist?(plist_file) || !OSUtils.file_compare(temp_file.path, plist_file)
        BrowserStack.logger.info "Plist for service: #{@name} with user = #{@username} doesn't exist or is modified"
        Utils.write_as_root(plist_file, plist_data)
        BrowserStack.logger.info "Plist File For service: #{@name} Created with user = #{@username}"
      else
        BrowserStack.logger.info "Plist for service: #{@name} with user = #{@username} is same as before"
      end
    end

    def update(force_update = false, wait_in_seconds = 2) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
      @config = {
        username: @username,
        name: @name.to_s,
        log_file: @log_file,
        error_log: @error_file,
        arguments: @args,
        custom_hash: @custom_hash,
        custom_path: @custom_path,
        prepend_custom_path: @prepend_custom_path,
        keep_alive: @keep_alive,
        session_create: @session_create,
        set_lang_and_term: @set_lang_and_term,
        start_calendar_interval: @start_calendar_interval,
        set_appium_home: @set_appium_home
      }
      plist_data = ERB.new(File.open(template_file).read).result(binding)

      temp_file = Tempfile.new('plist')
      temp_file.write(plist_data)
      temp_file.close
      username = nil
      username = @username if @type == ServiceType.UserService

      # if plist file doesn't exist or the existing plist file is different from the above generated one
      # Note that #file_compare returns false if the file doesn't exist.
      if force_update || !File.exist?(plist_file) || !OSUtils.file_compare(temp_file.path, plist_file)
        # modify the plist file... and restart the service...
        BrowserStack.logger.info "Trying to update plist for service: #{@name} with user = #{@username}"
        CheckPlist.unload_service(@name, @type, username) if File.exist? plist_file
        Utils.write_as_root(plist_file, plist_data)
        CheckPlist.load_service(@name, @type, username, wait_in_seconds) if File.exist? plist_file
      elsif !OSUtils.is_plist_loaded?(@name, @type)
        BrowserStack.logger.info "Plist for service: #{@name} not loaded, loading it with user = #{@username}"
        CheckPlist.load_service(@name, @type, username, wait_in_seconds)
      end

      other_plist_type = if @type == ServiceType.UserService
                           ServiceType.SystemService
                         else
                           ServiceType.UserService
                         end
      other_plist_location = "#{@@plists_dir[other_plist_type]}#{@name}.plist"
      if File.exist? other_plist_location
        BrowserStack.logger.warn("Found other plist for #{@name} at #{other_plist_location}! Unloading!")
        CheckPlist.unload_service(@name, other_plist_type)
        OSUtils.execute "sudo rm #{other_plist_location}"
      end

      temp_file.unlink

      OSUtils.is_plist_loaded?(@name, @type)
    end
  end
end
