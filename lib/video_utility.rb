require 'securerandom'
require_relative './utils/osutils'
require_relative '../server/device_manager'

class VideoUtility # rubocop:todo Metrics/ClassLength
  attr_reader :workspace

  def log(message)
    puts "#{Time.now} #{@device_id} #{@session_id} : #{message}"
  end

  def log_time(log_data)
    started_at = Time.now
    yield
    log "Time taken for #{log_data} : #{Time.now - started_at} seconds."
  end

  def clean_workspace
    return if @skip_recording

    unless Utils.empty?(@session_id)
      log "#{Time.now} Deleting video folder #{@workspace}"
      FileUtils.rm_rf(@workspace)
    end
  end

  # Cleans all the folders in @device_workspace whose last modified time was more than 1 day
  def clean_stale_session_folders_from_workspace
    return if @skip_recording

    if Dir.exist?(@device_workspace)
      stale_directories = Dir.entries(@device_workspace).select do |entry|
        File.directory?(File.join(@device_workspace, entry)) && !['.', '..'].include?(entry)
      end
      stale_directories = stale_directories.map do |entry|
        File.join(@device_workspace, entry)
      end
      stale_directories = stale_directories.select do |entry|
        File.mtime(entry) < (Time.now - 86400)
      end
      log "#{Time.now} Deleting #{stale_directories.length} stale session folders from workspace #{@device_workspace}"
      stale_directories.each do |stale_directory|
        log "#{Time.now} Deleting folder : #{stale_directory}"
        FileUtils.rm_rf(stale_directory)
      end
    end
  end

  def delete_all_matching(glob_pattern)
    puts "Deleting all files matching : #{glob_pattern}"

    Dir.glob(glob_pattern).each do |file_name|
      File.delete(file_name)
    end
  end

  def create_concat_file
    video_concat_file = "#{@workspace}video_part_names.txt"
    File.delete(video_concat_file) if File.exist?(video_concat_file)
    files_list = Dir["#{@workspace}#{@video_parts_prefix}*.mp4"].select { |f| File.file? f }.map { |f| "file #{File.absolute_path(f)}" }.sort
    if files_list.length >= 1
      log "Number of files to concat : #{files_list.length}"
      File.open(video_concat_file, 'w') { |f| f.write files_list.join("\n") }
    end
    video_concat_file
  rescue => e
    log "Failed to create concat file. Error: #{e.message}"
    raise VideoRecordError, "failed to create concat file. Error: #{e.message}"
  end

  def create_upload_request(uploader_request_file, json_data)
    return if @skip_recording

    dir_name = File.dirname(uploader_request_file)
    FileUtils.mkdir_p(dir_name) unless Dir.exist? dir_name
    Utils.write_to_file(uploader_request_file, json_data.to_json)
    BrowserStack.logger.info("Upload request created as #{uploader_request_file}")
  end

  def create_upload_request_for_video(file_name, params)
    uploader_request_file = @server_config["videos_to_upload_dir"] + "/video_upload_#{SecureRandom.uuid}.json"
    json_data = {
      upload_type: "video",
      file_name: file_name,
      video_params: params.select { |key, _value| key.include?('video') },
      genre: @genre
    }
    create_upload_request(uploader_request_file, json_data)
  end

  def create_upload_request_for_video_time_mapping(file_name, params)
    uploader_request_file = @server_config["other_files_to_upload_dir"] + "/video_time_mapping_upload_#{SecureRandom.uuid}.json"
    json_data = {
      upload_type: "video_map_upload",
      file_name: file_name,
      video_params: params.select { |key, _value| key.include?('video') }
    }
    create_upload_request(uploader_request_file, json_data)
  end

  def stop_file
    "#{@workspace}stop_video_recording"
  end

  def recording_lock_file
    @video_recording_lock_file
  end

  def wait_for_render(video_file)
    Timeout.timeout(60) do
      loop do
        break if File.exist?(video_file) && File.exist?(@video_merge_completed_file)

        sleep 0.5
      end
    end
  end

  def idevicevideoproxy_stop
    OSUtils.kill_all_processes("idevicevideoproxy", @device_id)
  end

  def get_video_duration
    # get duration of all the completed video chunks
    duration_command_ffprobe = "ls -r #{@workspace}#{@video_parts_prefix}*.mp4 | xargs -I{} #{FFPROBE} -i {} -show_format -v quiet | sed -n 's/duration=//p' | awk '{s+=$1} END {print s}'"
    duration1 = OSUtils.execute(duration_command_ffprobe).to_s.chomp.to_f

    # get duration of the ongoing video file
      #sample line1 : `frame= 2360 fps=9.6 q=-1.0 Lsize=    1298kB time=00:03:55.70 bitrate=  45.1kbits/s speed=0.964x`
      #sample line2 : `frame=  187 fps=9.7 q=42.0 size=       0kB time=00:00:12.80 bitrate=   0.0kbits/s speed=0.666x`
      # output :
    command = "sed -n '/time/h;${x;p;}' #{@video_process_log_file}"    # get last occurrence
    + " | awk '{print $(NF-3) " " $(NF-4)}'"                                        # get time related coulumns, varies in case of errors. hence considered two columns.
    + " | awk 'match($0, /time=(.*:.*:.*\.[0-9][0-9])/){ print substr( $0, RSTART, RLENGTH )}'" # extract time column
    + " | awk -F '=' '{print $NF}'"                                                 # get timestamp
    + " | awk -F':' '{print $1 * 60 * 60 + $2 * 60 + $3}'"                          # convert timestamp to seconds
    duration2 = OSUtils.execute(command).to_s.chomp.to_f
    log "Duration is : #{duration1} #{duration2}"
    (duration1 + duration2).to_i
  end
end
