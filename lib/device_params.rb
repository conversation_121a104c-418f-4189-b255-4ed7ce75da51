require_relative './configuration'
require_relative './custom_exceptions'

class DeviceParams
  class << self
    def configure
      @@device_config ||= BrowserStack::Configuration.new.device_config
    end

    def get_device_width_and_height(device_type)
      device_specific_config = @@device_config[device_type]
      [
        device_specific_config["streaming_params"]["width"],
        device_specific_config["streaming_params"]["height"]
      ]
    end

    def control_center_coordinates(model, version, key)
      device_specific_config = @@device_config[model]
      raise DeviceConfigException, "device_specific_config entry for #{model} is missing" if device_specific_config.nil?

      device_version = Gem::Version.new(version)
      control_centre = device_specific_config['control_centre'][key]
      control_centre.each do |config|
        os_version = config['os_version']
        return config if os_version == '*' || device_version >= Gem::Version.new(os_version)
      end
      raise DeviceConfigException, "#{key} coordinates for #{device_name} - #{version} is missing"
    end

    def control_centre_show_coordinates(model, version)
      control_center_coordinates(model, version, 'show')
    end

    def control_centre_dismiss_coordinates(model, version)
      control_center_coordinates(model, version, 'dismiss')
    end
  end
end
