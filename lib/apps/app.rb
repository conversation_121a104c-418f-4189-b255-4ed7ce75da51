require 'English'
require_relative '../utils/idevice_utils'
require_relative '../utils/plist_buddy'
require_relative '../custom_exceptions'
require_relative '../configuration'
require_relative '../utils/resign'
require_relative '../utils/xcode_build_utils'
require_relative '../utils/osutils'
require_relative '../helpers/wda_client'
require_relative '../ios_influxdb_client'
require_relative '../../config/constants'

module BrowserStack
  class App # rubocop:todo Metrics/ClassLength
    include Resign

    attr_accessor :bundle_id, :version, :config, :name, :app_versions

    def initialize(bundle_id, name)
      @config = Configuration.new.all
      @bundle_id = bundle_id
      @app_versions = @config["version_specific_apps_installed"][@bundle_id] unless @config["version_specific_apps_installed"].nil?
      @name = name
      @influxdb_client = BrowserStack::IosInfluxdbClient.new(BrowserStack.logger)
    end

    def download_file
      "#{@bundle_id}-#{@version}.app"
    end

    def download_url
      @config["s3_base_url"].end_with?("/") ? "#{@config['s3_base_url']}#{download_file}.tar.bz" : "#{@config['s3_base_url']}/#{download_file}.tar.bz"
    end

    def setup(device, provision_profile, force_install_apps = false)
      BrowserStack.logger.info "installing app #{self} for #{device}"

      app_cache_dir = @config["app_cache_dir"]
      signed_app_dir = @config["signed_app_dir"]

      raise AppAbsent, "Cant find #{app_folder_name} in #{app_cache_dir}" unless generic_app_present?(app_cache_dir)

      unless app_present_for_provision_profile?(provision_profile)
        sign_app_for_profile(provision_profile, app_cache_dir, signed_app_dir)
        raise AppAbsent, "Cant find #{app_folder_name} in #{signed_app_path(signed_app_dir, provision_profile)}" unless app_present_for_provision_profile?(provision_profile)
      end

      if force_install_apps || !present_on_device?(IdeviceUtils.list_apps(device))
        install_on_device(device, signed_app_dir, provision_profile)
        raise AppAbsent, "Cant find #{app_folder_name} on #{device}" unless present_on_device?(IdeviceUtils.list_apps(device))
      end
    end

    def generic_app_present?(cache_dir)
      app_path = generic_app_path(cache_dir)
      File.exists?(app_path) && App.get_app_version(app_path) == version
    end

    def self.get_app_version(app_path)
      PlistBuddy.get_value_of_key("#{app_path}/Info.plist", "CFBundleVersion").strip
    end

    def app_present_for_provision_profile?(provision_profile)
      app_path = signed_app_path(@config["signed_app_dir"], provision_profile)
      File.exists?(app_path) && App.get_app_version(app_path) == version
    end

    # Accepting a list of user_installed_apps to avoid calling IdeviceUtils.list_apps multiple times
    # to reduce list_apps timeouts
    # @param [{bundle_id: "<bundle-id>" , version: "200"}] user_installed_apps
    def present_on_device?(user_installed_apps)
      user_installed_apps.any? { |app| app[:bundle_id] == bundle_id && app[:version] == version }
    end

    def download_app!(cache_path)
      FileUtils.mkdir_p(cache_path) unless Dir.exist? cache_path

      @app_versions.values.uniq.each do |app_version|
        @version = app_version
        download_path = "/tmp/#{download_file}.tar.bz"
        if generic_app_present?(@config["app_cache_dir"])
          BrowserStack.logger.info "#{@bundle_id} App already downloaded in cache. Not Downloading"
          next
        end
        unless File.exists?(download_path)
          BrowserStack.logger.info "#{@bundle_id} App not present. Downloading it"
          username = @config["user"]
          command = "sudo su -l #{username} -c \"/usr/local/bin/gtimeout #{@config['s3_timeout']} /usr/local/bin/s3curl --id \'bs\' #{download_url} -- -o #{download_path} \""
          BrowserStack.logger.info(command)
          OSUtils.execute(command)
          raise MachineCheckException.new("Downloading #{download_file} failed/timed-out", @config["environment"]) if $CHILD_STATUS.exitstatus != 0 || !File.exists?(download_path)
        end
        FileUtils.chown_R(username, nil, download_path) if Etc.getpwuid(File.stat(download_path).uid).name != username
        OSUtils.execute("tar xvfC #{download_path} #{cache_path}")
      end
    end

    def app_folder_name
      "#{bundle_id}-#{version}.app"
    end

    def generic_app_path(cache_dir)
      "#{cache_dir}/#{app_folder_name}"
    end

    def signed_app_path(signed_app_dir, provision_profile)
      "#{signed_app_dir}/#{provision_profile}/#{app_folder_name}"
    end

    def install_on_device(device, signed_app_dir, provision_profile)
      IdeviceUtils.install_app(device, signed_app_path(signed_app_dir, provision_profile))
      exit_status = $CHILD_STATUS.exitstatus
      if exit_status != 0
        BrowserStack.logger.info("Retrying #{bundle_id} installation with ideviceinstaller")
        OSUtils.execute("#{IDEVICEINSTALLER} -u #{device} -U #{bundle_id}")
        OSUtils.execute("#{IDEVICEINSTALLER} -u #{device} -i #{signed_app_path(signed_app_dir, provision_profile)}")
        exit_status = $CHILD_STATUS.exitstatus
        raise AppInstallationException, "Got non zero exit code #{exit_status}" if exit_status != 0
      end
    end

    def get_nix_store_source(source)
      OSUtils.execute("ls -al #{source} | awk '{print $NF}'").chomp("\n") # nix adds meta files of files present in package and prefixes them with ._
    end

    def remove_nix_meta_files(source)
      nix_store_source = get_nix_store_source(source)
      OSUtils.execute("find #{nix_store_source} -name '._*' -delete"); # the ._ prefixed meta files are searched and deleted
      BrowserStack.logger.info("Ran find and delete on dir: #{nix_store_source}")
    end

    def sign_app_for_profile(provision_profile, app_cache_dir, signed_app_dir)
      source = generic_app_path(app_cache_dir)
      OSUtils.execute("sudo chmod -R 755 #{source}/") # Ensure all files in app cache dir are writable so `codesign` command will work
      BrowserStack.logger.info("Ran chmod on dir: #{source}/")
      remove_nix_meta_files(source) if @name == "chrome"  # nix adds meta files of files present in package and prefixes them with ._ which caused chrome install failure on iOS 12
      raise 'Invalid app config' if signed_app_dir.nil? || provision_profile.nil? || app_folder_name.nil?

      destination = signed_app_path(signed_app_dir, provision_profile)
      #delete old folder
      FileUtils.rm_r(destination, force: true) #scary
      FileUtils.mkdir_p("#{signed_app_dir}/#{provision_profile}")
      args = {
        provision_profile: provision_profile,
        keychain_path: @config["appium_keychain"],
        keychain_password: @config["appium_keychain_password"],
        provisioning_profile_dir: @config["provisioning_profile_dir"],
        user: @config["user"],
        bundle_path: source,
        destination: destination
      }

      entitlements_file = Tempfile.new(['entitlements', '.plist'])
      entitlements_data = XcodeBuildUtils.get_entitlements_from_profile("#{@config['provisioning_profile_dir']}/#{provision_profile}.mobileprovision")
      File.write(entitlements_file, entitlements_data)
      args[:entitlements] = entitlements_file.path
      begin
        resign(args)
      rescue ResigningException => e
        BrowserStack.logger.info "Failed to create #{@bundle_id} for profile #{provision_profile}"
        raise e
      ensure
        entitlements_file.unlink
      end
    end

    def uninstall_from_device(device)
      IdeviceUtils.uninstall_app(device, @bundle_id)
    end

    # Try to kill the app using WDA max 2 times. If failed, try with older approach using Instruments.
    def kill_app(device, device_version)
      BrowserStack.logger.info "Killing of Chrome app via WDA - started - #{device}, #{device_version}, #{@bundle_id}"
      success = false
      max_retry_limit = 2
      current_retry_count = 0
      app_list = [@bundle_id]
      current_device_config = DeviceManager.device_configuration_check(device)
      wda_port = current_device_config["webdriver_port"].to_i
      while !success && current_retry_count < max_retry_limit
        begin
          current_retry_count += 1
          WdaClient.new(wda_port).kill_apps(app_list)
          success = true
          BrowserStack::Zombie.push_logs('njb_chrome_kill_app', "", { "method" => "wda", "retry_count" => current_retry_count })
          @influxdb_client.event(device, 'wda-kill-app', component: 'cleanup', subcomponent: 'app')
        rescue => e
          BrowserStack.logger.error("Exception while killing Chrome app via WDA. Retry count: #{current_retry_count}, #{device}, #{device_version}: #{e.message} \n#{e.backtrace.join("\n")}")
        end
      end
      unless success
        BrowserStack.logger.info "Killing Chrome app via WDA - failed - Trying with instruments. #{device}, #{device_version}, #{@bundle_id}"
        kill_app_with_instruments(device, device_version) if instruments_supported?
      end
    end

    # instruments utility was removed from Xcode 13 onwards
    def instruments_supported?
      xcode_version = OSUtils.check_xcode
      Gem::Version.new(xcode_version.split[1]) < Gem::Version.new(13)
    end

    def kill_app_with_instruments(device, device_version)

      if device_version.split(".")[0].to_i >= 12
        success = false
        max_retry_limit = 2
        current_retry_count = 0
        while !success && current_retry_count < max_retry_limit
          stdout, stderr, status = OSUtils.execute2("/usr/local/bin/gtimeout -s KILL 20 /usr/bin/instruments -t \"Activity Monitor\" -l 100 -D kill_iphone_#{device} -w #{device} #{@bundle_id}; rm -rf kill_iphone_#{device}.trace")
          current_retry_count += 1
          if stderr.empty?
            success = true
          else
            sleep(1)
            BrowserStack.logger.info "Failed to kill the app. Retry count - #{current_retry_count}"
          end
        end
        raise "Failed to kill the app during cleanup" unless success
      else
        OSUtils.execute("/usr/local/bin/gtimeout -s KILL 20 /usr/bin/instruments -t \"Activity Monitor\" -l 100 -D kill_iphone_#{device} -w #{device} #{@bundle_id}; rm -rf kill_iphone_#{device}.trace")
      end
      BrowserStack::Zombie.push_logs('njb_chrome_kill_app', "", { "method" => "instruments", "retry_count" => current_retry_count })
      @influxdb_client.event(device, 'instruments-kill-app', component: 'cleanup', subcomponent: 'app')
    end

    # subclasses should override this
    def skip_setup(driver)
      BrowserStack.logger.warn("Trying to skip the setup for #{@name} but #skip_setup"\
                               "method wasn't overriden. Defaulting to not doing anything.")
    end

    # TODO: Move this into ifuse class
    def mount_root_directory_of_app(device, mount_point, bundle_id=@bundle_id)
      cmd = "#{IFUSE} -o allow_other,umask=0000,default_permissions -u #{device} --container #{bundle_id} #{mount_point}"
      msg, error_msg, status = OSUtils.execute2(cmd)
      if !error_msg.empty? && error_msg.include?('is itself on a macFUSE volume')
        BrowserStack.logger.error "ifuse failed, error: #{error_msg}"
        BrowserStack.logger.info "umounting #{mount_point}"
        OSUtils.execute("sudo umount -f #{mount_point}")
        msg, error_msg, status = OSUtils.execute2(cmd)
      end
      raise "ifuse failed" unless error_msg.empty?
    end

    # TODO: Move this into ifuse class
    def unmount_root_directory_of_app(mount_point)
      retry_num = 0
      begin
        OSUtils.execute "sudo umount -f #{mount_point}"
        FileUtils.remove_dir mount_point if File.exists?(mount_point)
      rescue Errno::EBUSY
        BrowserStack.logger.error "Got resource busy on removing mount_point."
        if retry_num < 2
          BrowserStack.logger.info "Retrying for #{retry_num + 1} times to unmount"
          retry_num += 1
          sleep 0.1
          retry
        else
          raise Errno::EBUSY, mount_point
        end
      end
    end

    def update_app_version_using_ios_version(ios_version)
      BrowserStack.logger.info "app versions: #{@app_versions}"
      @version = @app_versions[ios_version] || @app_versions['latest']
    end

    private

    def to_s
      @bundle_id
    end
  end
end
