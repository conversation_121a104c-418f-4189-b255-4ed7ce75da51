require_relative 'app'
require_relative 'installed_app'
require_relative '../helpers/automation'
require_relative '../utils/time_recorder'

module BrowserStack
  class Chrome < App # rubocop:todo Metrics/ClassLength
    include BrowserStack::TimeRecorder

    time_methods :dismiss_chrome_popup

    BUNDLE_ID = "com.google.chrome.ios".freeze

    def initialize
      super(BUNDLE_ID, "chrome")
    end

    def setup(device, provision_profile, force = false)
      super(device, provision_profile, force)
      # don't kill app, this is called only in device check,
      # killing the app causes it to launch temporarily the user
      # might see it if a start comes in at this time.
      cleanup(device, "", true)
    end

    def install_on_device(device, signed_app_dir, provision_profile)
      super(device, signed_app_dir, provision_profile)
      InstalledApp.new(device, BUNDLE_ID).update_config(version, provision_profile)
      FileUtils.touch(Chrome.dismiss_chrome_popup_file(@config['config_root'], @device))
      # Removing the automation state file to ensure the automation to run after reset
      # We will install chrome after reset. So removing state here would handle the same
      # MOBPE-591
      remove_automation_state(device)
    end

    def remove_automation_state(device)
      @device_state = DeviceState.new(device)
      is_full_cleanup = @device_state.mdm_full_cleanup_file_present?
      if is_full_cleanup
        BrowserStack.logger.info("Skipped removing automation state files, since its FC")
        return
      end
      BrowserStack.logger.info("Removing automation state files")
      @device_state.remove_siri_contacts_cleanup_file
      @device_state.remove_download_files_setup_done_file
    end

    def cleanup(device, device_version, no_kill = false) # rubocop:todo Metrics/MethodLength, Metrics/AbcSize
      # Don't close the brace, it's closed at the end of the logs
      BrowserStack.logger.info("CLEANUP { Chrome.cleanup on #{device}")

      @device_state = DeviceState.new(device)
      is_full_cleanup = @device_state.mdm_full_cleanup_file_present? || @device_state.first_cleanup_file_present?

      mount_point = "/Users/<USER>/chrome_#{device}"
      first_run_path = "Library/Application Support/Google/Chrome/First Run"
      retries = 3
      begin
        kill_app(device, device_version) unless no_kill
        FileUtils.mkdir_p(mount_point)
        mount_root_directory_of_app(device, mount_point)
        sleep 2**(4 - retries) if device_version.to_i >= 15
        clean_neccessary_files(mount_point)
        add_neccessary_files(mount_point)
        create_first_run_file(mount_point)
      rescue EmptyMountDirError => e
        BrowserStack.logger.error("Chrome mount dir is empty!!!")
        retries -= 1
        retry if retries > 0
        BrowserStack::Zombie.push_logs(
          "chrome-cleanup-error",
          e.message,
          {
            "device" => device
          }
        )
        @device_state.touch_chrome_cleanup_required_file if is_full_cleanup
        raise "#{OFFLINE_REASON_CHROME_CLEANUP_FAILURE}: #{e.message}"
      rescue MountError => e
        @device_state.touch_chrome_cleanup_required_file if is_full_cleanup
        BrowserStack.logger.error("Mounting failed. Make sure UIFileSharing is enabled or get the latest libimobiledevice")
        raise e
      rescue Exception => e
        @device_state.touch_chrome_cleanup_required_file if is_full_cleanup
        BrowserStack.logger.error("Chrome cleanup error #{e.message} #{e.backtrace.join("\n")}")
        raise e
      ensure
        unmount_root_directory_of_app(mount_point)
      end

      if is_full_cleanup
        success = IdeviceFileUtils.list_files(device, first_run_path, BUNDLE_ID).include?(first_run_path)
        @device_state.touch_chrome_cleanup_required_file unless success
        raise 'First run file does not exist' unless success
      end

      @device_state.remove_chrome_cleanup_required_file
      BrowserStack.logger.info("CLEANUP } Chrome.cleanup on #{device}")
    end

    # The first time safari launcher is used to launch chrome on a device it shows a popup
    # The first time chrome opens a site that requires location it shows another popup
    def self.dismiss_chrome_popup(device_id)
      #Need to do this for both url schemes
      BrowserStack.logger.info "Dismissing chrome popup"
      Chrome.dismiss_chrome_popup_for(device_id, "https://google.com", true)
      Chrome.dismiss_chrome_popup_for(device_id, "http://google.com", false)
    end

    def self.dismiss_chrome_popup_file(parent, uuid)
      "#{parent}/dismiss_chrome_popup_#{uuid}"
    end

    def self.dismiss_chrome_popup_for(device_id, url, dismiss_location_popup)
      caps = { processArguments: { args: ["chrome", url] } }
      driver = Automation.get_driver_for_app(device_id, Launcher::BUNDLE_ID, caps)
      begin
        if dismiss_location_popup
          begin
            driver.alert_accept
          rescue
            nil
          end
          sleep(10) #wait for location popup
        end
        driver.alert_accept #dismission chrome or location popup depending on flag
        driver.driver_quit
      rescue Selenium::WebDriver::Error::NoSuchAlertError => e
        BrowserStack.logger.info("Chrome popup already dismissed")
        #ignore exception if modal already dismissed
      rescue Exception => e
        BrowserStack.logger.error("Error whille trying to dismiss chrome popup #{e}")
        raise e unless e.message.match(/An attempt was made to operate on a modal dialog when one was not open/) #for older appium
      end
    end

    private

    def clean_neccessary_files(mount_point)
      directories_to_clean = %w[SystemData tmp Documents Library]
      directories_to_clean.each do |item|
        files_before = Dir.glob("#{mount_point}/#{item}/*")
        BrowserStack.logger.info("File contents on #{item} before FileUtils.rm_rf: #{files_before}")
        raise EmptyMountDirError, "No files present in mount dir" if files_before.empty? && item == "Library"

        FileUtils.rm_rf(files_before)

        files_after = Dir.glob("#{mount_point}/#{item}/*")
        BrowserStack.logger.info("File contents on #{item} after FileUtils.rm_rf: #{files_after}")
      end
    end

    def add_neccessary_files(mount_point)
      directories_to_add = %w[Library/Caches/Snapshots/com.google.chrome.ios Library/Preferences]
      directories_to_add.each do |item|
        FileUtils.mkdir_p("#{mount_point}/#{item}")
      end
    end

    def create_first_run_file(mount_point)
      files_to_add = ["Library/Application Support/Google/Chrome/First Run"]
      files_to_add.each do |item|
        complete_path = "#{mount_point}/#{item}"
        directory_name = File.dirname(complete_path)
        FileUtils.mkdir_p(directory_name) unless File.exists?(directory_name)
        FileUtils.touch(complete_path)
      end
    end

    def skip_setup(driver)
      # We do using ifuse, this was done for a hotfix. Dead code at the moment.
      BrowserStack.logger.info("Skipping chrome setup via appium")

      BrowserStack.logger.info("Looking for Accept & Continue")
      accept_and_continue = driver.find_elements(accessibility_id: "Accept & Continue")
      if accept_and_continue.empty?
        BrowserStack.logger.warn("No accept and continue! Assuming the setup was already skipped.")
        return
      end
      raise "ambiguous accept and continue button" unless accept_and_continue.length == 1

      accept_and_continue.first.click

      BrowserStack.logger.info("Looking for No Thanks button")
      # Mind locale differences here, multiple labels are possible
      no_thanks = driver.find_elements(accessibility_id: "No, Thanks")
      no_thanks += driver.find_elements(name: "SkipButtonAccessibilityIdentifier")
      raise "cant find no thanks button" if no_thanks.empty?
      raise "ambiguous no thanks button" unless no_thanks.length == 1

      no_thanks.first.click
    end
  end
end
