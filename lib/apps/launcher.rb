require_relative 'app'
require_relative 'installed_app'
require_relative '../models/device_state'
require_relative '../helpers/browserstack_app_helper'

module BrowserStack
  class Launcher < App
    BUNDLE_ID = "com.browserstack.Launcher".freeze

    def initialize
      super(BUNDLE_ID, "launcher")
    end

    def device_version(uuid)
      DeviceManager.device_configuration_check(uuid)['device_version'].to_f
    end

    def device_type(uuid)
      DeviceManager.device_configuration_check(uuid)["device_name"].match(/iPad/i).nil? ? "iPhone" : "iPad"
    end

    def setup(device, provision_profile, force = false)
      super(device, provision_profile, force)
      InstalledApp.new(device, BUNDLE_ID).update_config(version, provision_profile)
      grant_photos_permission(version, device)
    end

    def grant_photos_permission(app_version, device)
      if app_version && app_version.to_i > 7
        device_state = DeviceState.new(device)
        device_state.touch_launcher_photos_permission_file
        BrowserStackAppHelper.check_and_install_browserstack_apps(device)
        BrowserStackAppHelper.run_ui_test(device, :launcher_allow_photos_popup, 120, session_id: "")
        device_state.remove_launcher_photos_permission_file
      end
    end
  end
end
