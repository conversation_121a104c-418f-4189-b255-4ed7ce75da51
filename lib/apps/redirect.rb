require_relative './app'
require_relative 'installed_app'
require_relative '../models/device_state'
require_relative '../helpers/browserstack_app_helper'

module BrowserStack
  class Redirect < App
    BUNDLE_ID = "com.browserstack.Redirect".freeze

    def initialize
      super(BUNDLE_ID, "redirect")
    end

    def setup(device, provision_profile, force = false)
      super(device, provision_profile, force)
      InstalledApp.new(device, BUNDLE_ID).update_config(version, provision_profile)

      device_state = DeviceState.new(device)
      if device_state.enable_redirect_extension_file_older_than_days?(CLEANUP_STEPS_FREQ[:enable_redirect_extension])
        BrowserStackAppHelper.check_and_install_browserstack_apps(device)
        BrowserStackAppHelper.run_ui_test(device, :enable_redirect_extension, 120, session_id: "")
        device_state.touch_enable_redirect_extension_file
      end
    end
  end
end
