require 'json'
require 'time'

require_relative '../../config/constants'
require_relative '../provisioning/ppuid_file'

# Class for recording and retrieving data about key browserstack apps installed on devices.
# e.g. chrome, launcher, browserstack_app
#
# Immediately after installation of an app, the following data points are written to a config:
# 1. provisioning profile the app was signed with
# 2. version of the app
# 3. date of installation
#
# This data can be used at a later date for checking whether a new install is required.
# Config path: /usr/local/.browserstack/config/installed_apps_<udid>.json

class InstalledApp

  def initialize(udid, bundle_id)
    @udid = udid
    @bundle_id = bundle_id
    @config_path = File.join(CONFIG_ROOT, "installed_apps_#{@udid}.json")
  end

  def reinstall?(latest_version:)
    config = read_config

    if config.empty? || !config.key?(@bundle_id)
      log(:info, "Reinstall required: installed apps config does not contain #{@bundle_id}")
      return true
    end

    ppuid_file = PpuidFile.new(@udid)
    if config[@bundle_id]["ppuid"] != ppuid_file.ppuid
      log(:info, 'Reinstall required: device ppuid has changed since last install')
      return true
    end

    if Gem::Version.new(latest_version) != Gem::Version.new(config[@bundle_id]["version"])
      log(:info, 'Reinstalled required: latest app version has changed since last install')
      return true
    end

    false
  end

  def current_version
    config = read_config
    return nil if config[@bundle_id].nil?

    config[@bundle_id]["version"]
  end

  def read_config
    return {} unless File.exist?(@config_path)

    begin
      JSON.parse(File.read(@config_path))
    rescue JSON::ParserError
      {}
    end
  end

  def update_config(version, ppuid)
    config = read_config

    ppuid_file = PpuidFile.new(@udid)
    provisioning_branch = ppuid_file.branch_name

    # ensure valid version string
    version = Gem::Version.new(version).to_s

    config[@bundle_id] = {
      version: version,
      ppuid: ppuid,
      branch_name: provisioning_branch,
      install_date: Time.now.utc
    }

    log(:info, 'Updating installed apps config')
    File.write(@config_path, config.to_json)
  end

  def log(level, message)
    params = { subcomponent: "InstalledApp[#{@bundle_id}]", device: @udid }
    BrowserStack.logger.send(level.to_sym, message, params)
  end
end
