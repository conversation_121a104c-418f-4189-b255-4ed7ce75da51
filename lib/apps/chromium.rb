require_relative '../../config/constants'
require_relative '../utils/idevice_utils'
require_relative '../utils/plist_buddy'
require_relative '../utils/resign'
require_relative '../utils/idevice_file_utils'
require_relative '../provisioning/ppuid_file'
require_relative '../provisioning/provisioning_manager'
require_relative '../apps/installed_app'
require_relative '../configuration'
require_relative '../ifuse'
require_relative '../helpers/data_report_helper'
require 'logger'
require '/usr/local/.browserstack/realmobile/lib/utils/push_to_zombie'

# rubocop:disable Lint/Loop
class Chromium #rubocop:todo Metrics/ClassLength

  BUNDLE_ID = "browserstack.chromium.branch.chrome.ios.dev"
  MAX_RETRIES = 3

  def initialize(device_id)
    @device = device_id
    @ppuid_file = PpuidFile.new(device_id)
    @config = BrowserStack::Configuration.new.all
    @device_config = BrowserStack::DeviceConf[device_id]
    @installed_app = InstalledApp.new(@device, bundle_id)
    @cert_rotated = check_rotation
    @chromium_data_reporter = DataReportHelper.new("chromium-installation")
  end

  def check_rotation
    branch_rotated = File.exists?(@ppuid_file.chromium_path) && provision_branch != chromium_branch
    return true if branch_rotated

    current_version = @installed_app.current_version
    return true if current_version != desired_version && current_version == "111.0.5563.99"

    false
  end

  def desired_version
    return '125.0.6422.80' if @device_config['device_version'].to_f >= 15

    '111.0.5563.99'
  end

  def install_required?(device_version)
    return false if device_version.to_f < 14

    @installed_app.reinstall?(latest_version: desired_version)
  end

  def bundle_id
    prefix_branch = provision_branch.split('_')
    prefix_branch[0] = prefix_branch[0][0]
    branch = prefix_branch.join('.')
    BUNDLE_ID.gsub('branch', branch)
  end

  def ensure_chromium_profile_present
    unless File.exists?(@ppuid_file.chromium_path) && !@cert_rotated
      provisioning_manager = ProvisioningManager.new(@device)
      provisioning_manager.download_chromium_provisioning_profile(provision_branch)
    end
  end

  def download_url
    @config["s3_base_url"].end_with?("/") ? "#{@config['s3_base_url']}#{chromium_folder}.tar.gz" : "#{@config['s3_base_url']}/#{chromium_folder}.tar.gz"
  end

  def ensure_chromium_ipa_present
    app_cache_dir = @config["app_cache_dir"]
    download_path = "/tmp/#{chromium_folder}.tar.gz"
    unless Dir.exist?(unsigned_app_path) && !@cert_rotated
      FileUtils.rm_rf(unsigned_app_path)
      BrowserStack.logger.info "#{bundle_id} App not present. Downloading it"
      username = @config["user"]
      command = "sudo su -l #{username} -c \"/usr/local/bin/gtimeout #{@config['chromium_timeout']} /usr/local/bin/s3curl --id \'bs\' #{download_url} -- -o #{download_path} \""
      BrowserStack.logger.info(command)
      OSUtils.execute(command)
      raise "Downloading #{chromium_folder}.tar.gz failed/timed-out", @config["environment"] if $CHILD_STATUS.exitstatus != 0 || !File.exists?(download_path)

      FileUtils.chown_R(username, nil, download_path) if Etc.getpwuid(File.stat(download_path).uid).name != username
      OSUtils.execute("tar xvfC #{download_path} #{app_cache_dir}")
      FileUtils.rm_f download_path
    end
  end

  def resign_folder(path, wildcard_sign: false)
    if wildcard_sign
      ppuid = @ppuid_file.ppuid
      provision_file = MobileprovisionFile.path(ppuid: ppuid)
    else
      chromium_ppuid = @ppuid_file.chromium_ppuid
      bundle_id = PlistBuddy.get_value_of_key("#{path}/Info.plist", "CFBundleIdentifier").strip
      bundle_ppuid = chromium_ppuid[bundle_id]
      raise "Missing provision for #{bundle_id}" if bundle_ppuid.nil?

      provision_file = MobileprovisionFile.path(ppuid: bundle_ppuid)
    end
    puts "Resigning #{path} using #{provision_file}"
    provisioning_profile = ProvisioningProfile.new(provision_file)
    entitlements_file = Tempfile.new(['entitlements', '.plist'])
    entitlements_data = XcodeBuildUtils.get_entitlements_from_profile(provision_file)
    File.write(entitlements_file, entitlements_data)
    ['_CodeSignature', 'embedded.mobileprovision'].each do |file|
      FileUtils.rm_f("#{path}/#{file}")
    end
    FileUtils.cp(provision_file, "#{path}/embedded.mobileprovision")
    cert = provisioning_profile.developer_certificate_sha1
    sign_args = {
      keychain_password: @config['appium_keychain_password'],
      keychain_path: @config['appium_keychain'],
      certificate: cert,
      entitlements: entitlements_file.path,
      user: @config['user']
    }
    Class.new.extend(BrowserStack::Resign).send(:codesign_files, [path], sign_args)
    FileUtils.rm_f(entitlements_file)
  end

  def ensure_chromium_ipa_resigned
    return if Dir.exist?(signed_app_path) && !@cert_rotated

    # copy unsigned app to signed_app_path
    FileUtils.rm_rf(signed_app_path)
    FileUtils.cp_r(unsigned_app_path, signed_app_path)
    # resign plugins
    plugins = Dir.glob("#{signed_app_path}/PlugIns/*")
    plugins.each do |plugin|
      resign_folder(plugin)
    end

    # resign main app
    resign_folder(signed_app_path)

    # resign frameworks
    frameworks = Dir.glob("#{signed_app_path}/Frameworks/*")
    frameworks.each do |framework|
      resign_folder(framework, wildcard_sign: true)
    end
  end

  def signed_app_path
    signed_app_dir = @config["signed_app_dir"]
    "#{signed_app_dir}/#{@ppuid_file.ppuid}/#{chromium_folder}"
  end

  def unsigned_app_path
    app_cache_dir = @config["app_cache_dir"]
    "#{app_cache_dir}/#{chromium_folder}"
  end

  def chromium_folder
    "#{bundle_id}-#{desired_version}.app"
  end

  def install_on_device
    IdeviceUtils.install_app(@device, signed_app_path)
    exit_status = $CHILD_STATUS.exitstatus

    if exit_status != 0
      BrowserStack.logger.info("Retrying chromium installation after removing framework embedded profiles")
      framework_embedded_provision = Dir.glob("#{signed_app_path}/Frameworks/*/embedded.mobileprovision")
      framework_embedded_provision.each do |path|
        FileUtils.rm_f path
      end
      OSUtils.execute("#{IDEVICEINSTALLER} -u #{@device} -U #{bundle_id}")
      sleep 1
      OSUtils.execute("#{IDEVICEINSTALLER} -u #{@device} -i #{signed_app_path}")
      exit_status = $CHILD_STATUS.exitstatus
      raise AppInstallationException, "Got non zero exit code #{exit_status}" if exit_status != 0
    end
  end

  def ensure_install
    retries = 0
    install_success = false
    install_failure = nil
    begin
      @cert_rotated = true if retries > 0
      ensure_chromium_profile_present
      ensure_chromium_ipa_present
      ensure_chromium_ipa_resigned
      install_on_device
      touch_first_run_file # removes chromium welcome screen on first launch
      # We need to enable Webview Inspector on latest Chromium since iOS 16.4, to access Webview Context ref: https://browserstack.atlassian.net/browse/MOBPL-5019
      enable_webview_inspector if @device_config['device_version'].to_f >= 16.4
      @chromium_data_reporter.report({ branch: provision_branch, device: @device, result: 'success' }, eds_only: true)
      install_success = true
    rescue => e
      BrowserStack.logger.error "chromium installation failed, error: #{e.message} #{e.backtrace.join("\n")}"
      install_failure = e
    ensure
      retries += 1
    end while retries < MAX_RETRIES && !install_success

    unless install_success
      zombie_push('ios', 'chromium-not-installed', install_failure.message, '', provision_branch, @device, '', '')
      @chromium_data_reporter.report({ branch: provision_branch, device: @device, result: 'failure', error: install_failure.message }, eds_only: true)
      raise "Chromium install failed"
    end

    @installed_app.update_config(desired_version, @ppuid_file.ppuid)
  end

  def clean
    IdeviceUtils.uninstall_app(@device, bundle_id)
    ensure_install
  end

  def provision_branch
    @ppuid_file.branch_name
  end

  def chromium_branch
    @ppuid_file.chromium_ppuid[:branch_name] || @ppuid_file.chromium_ppuid['branch_name']
  end

  def touch_first_run_file_via_ios_deploy
    first_run_file = "/Library/Application Support/Chromium/First Run"
    tmp_file = Tempfile.new('FirstRun')
    IdeviceFileUtils.add_file(@device, tmp_file.path, first_run_file, bundle_id)
    tmp_file.unlink
  end

  def touch_first_run_file_via_ifuse
    ifuse = Ifuse.new(@device)
    mount_point = ifuse.mount_app_point(bundle_id)
    first_run_path = '/Library/Application Support/Chromium/First Run'
    BrowserStack.logger.info "Creating first run file for chromium"
    begin
      ifuse.mount_root_directory_of_app(bundle_id)
      first_run_file = File.join(mount_point, first_run_path)
      directory_name = File.dirname(first_run_file)
      FileUtils.mkdir_p(directory_name) unless File.exists?(directory_name)
      FileUtils.touch(first_run_file)
    rescue MountError => e
      BrowserStack.logger.error("Mounting failed. Make sure UIFileSharing is enabled or get the latest libimobiledevice")
      raise e
    rescue => e
      BrowserStack.logger.error("Chromium cleanup error #{e.message} #{e.backtrace.join("\n")}")
      raise e
    ensure
      ifuse.unmount_root_directory_of_app(bundle_id)
    end
    BrowserStack.logger.info("CLEANUP } Chromium.cleanup on #{@device}")
    success = IdeviceFileUtils.list_files(@device, first_run_path, bundle_id).include?(first_run_path)
    raise 'First run file does not exist' unless success
  end

  def touch_first_run_file
    touch_first_run_file_via_ifuse
  rescue => e
    BrowserStack.logger.error("Ifuse method failed, touching file via ios-deploy now")
    zombie_push('ios', 'chromium-ifuse-failed', e.message, '', provision_branch, @device, '', '')
    touch_first_run_file_via_ios_deploy
  end

  def enable_webview_inspector
    BrowserStack.logger.info "Enabling Webview Inspector"

    IdeviceUtils.launch_app_with_bundle_id_v2(@device, bundle_id)
    IdeviceUtils.kill_process_with_name(@device, "Chromium")

    temp_library_folder = "/tmp/#{@device}/#{bundle_id}/"
    preferences_file_path = "/Library/Application Support/Chromium/Default/Preferences"

    IdeviceFileUtils.pull_folder(@device, preferences_file_path, bundle_id, temp_library_folder)
    temp_preferences_file = File.join(temp_library_folder, preferences_file_path)
    raise "Preferences file not found" unless File.exists?(temp_preferences_file)

    preferences_json = JSON.parse(File.read(temp_preferences_file))
    preferences_json["ios"] = {} if preferences_json["ios"].nil?
    preferences_json["ios"]["web_inspector_enabled"] = true
    Utils.write_to_file(temp_preferences_file, preferences_json.to_json)
    _, status = IdeviceFileUtils.add_file(@device, temp_preferences_file, preferences_file_path, bundle_id, return_status: true)
    raise "Failed to add Preferences file" unless status == 0

    BrowserStack.logger.info "Enabled Webview Inspector"
  ensure
    FileUtils.rm_rf(temp_library_folder) if temp_library_folder
  end

end

# rubocop:enable Lint/Loop
