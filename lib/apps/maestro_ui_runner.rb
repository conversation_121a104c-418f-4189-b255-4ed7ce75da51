require_relative 'app'
require_relative 'installed_app'
require_relative '../models/device_state'
require_relative '../helpers/browserstack_app_helper'

module BrowserStack
  class MaestroUIRunner < App
    BUNDLE_ID = "dev.mobile.maestro-driver-iosUITests.xctrunner".freeze

    def initialize
      super(BUNDLE_ID, "MaestroUIRunner")
    end

    def device_version(uuid)
      DeviceManager.device_configuration_check(uuid)['device_version'].to_f
    end

    def device_type(uuid)
      DeviceManager.device_configuration_check(uuid)["device_name"].match(/iPad/i).nil? ? "iPhone" : "iPad"
    end

    def setup(device, provision_profile, force = false)
      super(device, provision_profile, force)
    end
  end
end
