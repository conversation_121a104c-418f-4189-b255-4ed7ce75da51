require 'appium_lib'
require 'yaml'
require 'fileutils'
require 'json'
require 'network_helper'

require_relative 'utils/utils'
require_relative 'utils/idevice_utils'
require_relative 'custom_exceptions'
require_relative 'models/device_state'
require_relative '../config/constants'

# This class is responsible for checking the device state and cleaning up conditions for
# the methods present in `cleanup_iphone.rb` and the only task of this class is to check the
# conditions that were earlier directly present in `iphone.rb`

class CleanupCheck
  attr_reader :uuid, :device_state, :lockdown_device_state
  alias device_id uuid

  def initialize(device_config, uuid)
    @uuid = uuid
    @device_config = device_config
    @device_state = DeviceState.new(uuid)
    @device_type = device_config["device_type"]
    @device_version = device_config["device_version"]
  end

  def need_photos_permission?
    device_state.photos_permission_file_present?
  end

  def need_voiceover_cleanup?
    device_state.voiceover_used_file_present?
  end

  def need_disable_government_notifications?
    device_state.government_notifications_file_clean_on_weekend?(CLEANUP_STEPS_FREQ[:disable_government_notifications])
  end

  def need_disable_airplane_mode_from_settings?
    IdeviceUtils.airplane_mode_on?(uuid)
  end
end
