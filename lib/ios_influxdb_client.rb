require_relative '../config/constants'
require_relative './configuration'
require_relative './utils/helpers'
require 'mobile_influxdb_client' # Imported from mobile-common
require 'browserstack_logger'
require 'bsenv'

module BrowserStack
  class IosInfluxdbClient < MobileInfluxdbClient

    EVENTS = %w[
      app-reinstall-retry-failed
      cleanup-error
      device-check
      device-check-cleanup
      device-check-error
      device-check-timeout
      device-thread-crash
      home-press-error
      instruments-kill-app
      ios-njb-orientation-failed
      ipa-download-failed
      mdm-exception
      mdm-lock-command
      mdm-server-down
      mobile-offline
      multiple-cleanups
      njb-chrome-kill-app
      open-photos-failed
      uhubctl-failed
      unknown-device-popup
      unknown-safari-tabs
      unlock-device-failed
      update-inventory
      usbmuxd-error
      using-cleanup-to-fix-xcodebuild
      wda-kill-app
      wda-request-timeout
      xcode-build-failure
    ].freeze

    @@component = 'unknown'

    def initialize(logger = BrowserStack.logger, logger_params = {})
      super(logger, logger_params)
    end

    def self.instance
      @instance ||= new
    end

    # Set this once per process to define a component default (e.g. 'server')
    def self.component=(component)
      @@component = component
    end

    def event(device_id, event_name, component: nil, subcomponent: nil, is_error: false)
      component = @@component if component.nil?

      if subcomponent.nil?
        # Subcomponent = method that called the method in which the event occurred
        call_stack = caller_locations(1, 2)
        subcomponent = call_stack[1].nil? ? 'unknown' : call_stack[1].label.tr('_', '-')
      end

      @logger.info("Influxdb event: #{event_name}, #{device_id}, #{component}, #{subcomponent}") unless BSEnv.debug?
      track_event(event_name, component, subcomponent, device_id, is_error.to_s)
    end

    def device_name(device_id)
      return 'not-applicable' if device_id == 'NA'
      return 'unknown' if device_id.nil?

      config = device_config(device_id)
      return 'unknown' if config['device_name'].empty?

      config['device_name']
    end

    def device_config(device_id)
      config_json = File.read(CONFIG_JSON_FILE)
      config = JSON.parse(config_json)["devices"]
      raise 'Device id is not in config' unless config.include?(device_id)

      config[device_id]
    end

    def region
      self.class.config["region"] || "dev"
    end

    def sub_region
      self.class.config["sub_region"] || "dev"
    end

    def canary
      canary?.to_s
    end

    def os
      Configuration['default_platform'] || "ios"
    end
  end
end

# This code path is invoked when using this script via bash
if $PROGRAM_NAME == __FILE__
  BrowserStack.init_logger('/dev/stdout')

  BrowserStack::IosInfluxdbClient.new.track_event(*ARGV)
end
