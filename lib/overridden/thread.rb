# Use Thread.bs_run instead of Thread.new because it will copy over
# the logging params to the new thread and session id & other logger
# params will be logged.
class Thread
  class << self
    def bs_run(*args, &block)
      logger_params = Thread.current[:logger_params].dup

      instance = new(*args, &block)
      instance[:logger_params] = logger_params unless logger_params.nil?

      instance
    end
  end
end
