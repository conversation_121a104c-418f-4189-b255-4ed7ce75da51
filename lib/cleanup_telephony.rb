require 'appium_lib'
require_relative '../lib/utils/utils'
require_relative '../lib/utils/http_utils'
require_relative '../lib/utils/time_recorder'
require_relative '../server/iphone'
require_relative './helpers/automation'
require 'json'

class CleanupTelephony
  include BrowserStack::TimeRecorder

  time_methods :cleanup, :disconnect_call, :call_records_cleanup, :messages_records_cleanup

  def initialize(uuid, device_version, device_name, appium_port, webdriver_port, clean_call_records, clean_message_records )
    @uuid = uuid
    @device_version = device_version
    @device_name = device_name
    @appium_port = appium_port
    @webdriver_port = webdriver_port
    @clean_call_records = clean_call_records
    @clean_message_records = clean_message_records
  end

  def cleanup
    call_records_cleanup if @clean_call_records
    messages_records_cleanup if @clean_message_records
  end

  def call_records_cleanup
    BrowserStack.logger.info "Starting the cleanup for the call data: #{@uuid}"
    driver = Automation.bounce_app(@uuid, "com.apple.mobilephone")
    driver.reset
    BrowserStack.logger.info "Looking for Recents"
    driver.find_element(:accessibility_id, "Recents").click
    BrowserStack.logger.info "Looking for Edit"
    edit = driver.find_elements(:accessibility_id, "Edit")
    BrowserStack.logger.info "Edit found #{!edit.empty?}"
    return if edit.empty?

    edit[0].click
    sleep 2
    BrowserStack.logger.info "Looking to Clear"
    driver.find_element(:accessibility_id, "Clear").click
    sleep 1
    BrowserStack.logger.info "Clear All Recents"
    driver.find_element(:accessibility_id, "Clear All Recents").click
    sleep 2
    driver.driver_quit
  end

  def messages_records_cleanup
    BrowserStack.logger.info "Starting cleanup for messages data: #{@uuid}"
    driver = Automation.bounce_app(@uuid, "com.apple.MobileSMS")
    driver.reset
    sleep 2
    clear_messages_timeout = 60
    size_messages_chunk = @device_name =~ /iPhone/i ? 5 : 10
    loop do
      Timeout.timeout(clear_messages_timeout) do
        BrowserStack.logger.info "Looking for Edit"
        edit = driver.find_elements(:accessibility_id, "Edit")
        BrowserStack.logger.info "Edit found #{!edit.empty?}"
        return if edit.empty?  # if no message is present

        edit[0].click
        selected = false
        messages = driver.find_elements(:class_name, "XCUIElementTypeCell")
        size_messages_chunk = messages.count if messages.count < size_messages_chunk
        messages[0..size_messages_chunk - 1].each do |message|
          message.click
          selected = true
        end
        if selected
          driver.find_element(:accessibility_id, "Delete").click
          sleep 2
        else
          driver.find_element(:accessibility_id, "Cancel").click
          break  # When there are no messages to select
        end
      end
    rescue Exception => e
      driver.driver_quit
      raise MessageClearException, e.message
    end
    driver.driver_quit
  end

  def disconnect_call
    if @clean_call_records
      BrowserStack::IPhone.drop_call(@webdriver_port)
      BrowserStack::IPhone.unlock_device(@uuid)
      BrowserStack.logger.info "Ensured ongoing cellular call dropped!"
    end
  end
end
