require_relative '../../helpers/browserstack_app_helper'
require_relative '../../custom_exceptions'

module AppStoreInstaller
  module_function

  def install(device_uuid, app_bundle: nil, app_name: nil, apple_id: nil, pass: nil, bounce_required: false)
    handle_app_store_popups(device_uuid)
    sign_in_to_appstore(device_uuid, apple_id, pass)
    navigate_to_app(device_uuid, app_bundle: app_bundle, app_name: app_name, pass: pass, bounce_required: bounce_required)
    restore_app_store(device_uuid, app_bundle: app_bundle, app_name: app_name, pass: pass, bounce_required: bounce_required)
  ensure
    sign_out_of_appstore(device_uuid)
  end

  def handle_app_store_popups(uuid)
    BrowserStackAppHelper.run_ui_test(uuid, :dissmiss_appstore_popups, session_id: nil)
  end

  def sign_in_to_appstore(uuid, apple_id, pass)
    BrowserStackAppHelper.run_ui_test(uuid, :sign_in_to_appstore, environment_variables: { apple_id: apple_id, pass: pass })
  rescue => e
    raise AppStoreSigninError, e.message
  end

  def navigate_to_app(uuid, app_bundle: nil, app_name: nil, app_url: nil, pass: nil, bounce_required: false, exit_after_tapping_download: false)
    BrowserStackAppHelper.run_ui_test(uuid, :navigate_to_app, environment_variables: { app_bundle: app_bundle, app_name: app_name, app_url: app_url, pass: pass, bounce_app: bounce_required, exit_after_tapping_download: exit_after_tapping_download })
  end

  def restore_app_store(uuid, app_bundle: nil, app_name: nil, app_url: nil, pass: nil, bounce_required: false, exit_after_tapping_download: false)
    BrowserStackAppHelper.run_ui_test(uuid, :restore_app_store, environment_variables: { app_bundle: app_bundle, app_name: app_name, app_url: app_url, pass: pass, bounce_app: bounce_required, exit_after_tapping_download: exit_after_tapping_download })
  end

  def sign_out_of_appstore(uuid)
    BrowserStackAppHelper.run_ui_test(uuid, :sign_out_of_appstore)
  end
end
