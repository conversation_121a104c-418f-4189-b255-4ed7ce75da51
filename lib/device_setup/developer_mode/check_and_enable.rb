require_relative '../../utils/idevice_ffi/developer_mode_util'
require_relative '../../utils/logging'
require_relative 'exceptions'

module DeveloperMode
  module_function

  def check_and_enable(device_uuid)
    devmode_util = DeveloperModeUtil.new(device_uuid)

    if devmode_util.enabled?
      Logging.info "Developer Mode enabled"
      return
    end

    Logging.info("Activating Developer mode")

    devmode_util.toggle

    # idevice_id will report online for a few seconds
    sleep 5

    7.times do |_|
      sleep 5
      next unless device_connected?(device_uuid)

      begin
        sleep 7 # Developer mode popup can be generated after enable, delaying stops popup
        devmode_util.enable
        break
      rescue Idevice::IdeviceLibError
        # Ignored
      end
    end
    sleep 3 # Prevents false negatives from command being run too fast
    raise DeveloperModeError, 'Could not enable developer mode' unless devmode_util.enabled?

    Logging.info "Developer Mode enabled"
  end

  def device_connected?(device_uuid)
    `idevice_id`.include?(device_uuid)
  end
end

if __FILE__ == $PROGRAM_NAME
  device_id = ARGV[0]
  DeveloperMode.check_and_enable(device_id)
end