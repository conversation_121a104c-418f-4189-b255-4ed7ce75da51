require 'browserstack_logger'
require_relative 'install_check'
require_relative '../../config/constants'
require_relative '../../lib/apps/chrome'
require_relative '../../lib/apps/launcher'
require_relative '../../lib/apps/redirect'
require_relative '../../lib/helpers/browserstack_app_helper'
require_relative '../../lib/utils/wda_version'
require_relative '../../lib/utils/utils'
require_relative '../../lib/models/device_state'
require_relative '../../scripts/wda_download'
require_relative '../../lib/utils/zombie'
require_relative '../../lib/cleanup_iphone'
require_relative '../../lib/apps/chromium'
require_relative '../../server/device_manager'

module BrowserStack
  # This is will call InstallCheck to get the installations required and install the missing
  # components. This will run in async in device check and in sync during cleanup.
  class Installer # rubocop:todo Metrics/ClassLength
    attr_reader :device_state

    def initialize(device_uuid, device_version, provision_profile, device_config)
      @device = device_uuid
      @device_state = DeviceState.new(device_uuid)

      log_file = device_state.send(:install_phase_log_file)
      logger_params = { device: @device, component: 'install-phase' }
      BrowserStack.init_logger(log_file, logger_params)

      @device_version = device_version
      @device_config = device_config
      @provision_profile = provision_profile
      @server_config = BrowserStack::Configuration.new.all
      @web_driver_agents = @server_config['webdriver_agent_project_paths']
      @install_check = InstallCheck.new(
        device_uuid,
        device_version,
        provision_profile,
        device_config
      )
      @appium_versions = if @server_config['default_wda_version']
                           @server_config['webdriver_agent_project_paths'].keys
                         else
                           @server_config['appium_roots'].keys
                         end
      @cleanup_iphone = CleanupIphone.new(
        device_uuid,
        device_config["selenium_port"],
        device_version,
        device_config["device_name"],
        device_config["webdriver_port"]
      )
    end

    def trigger_install # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
      BrowserStack.logger.info(" ")
      BrowserStack.logger.info(" ")
      BrowserStack.logger.info("Install-Phase Starting now, checking full requirements again")
      BrowserStack.logger.info(" ")
      BrowserStack.logger.info(" ")

      @installations_required = @install_check.check_requirements # This does a full check again

      BrowserStack.logger.info("install-phase trigger_install required: #{@installations_required}")

      if @installations_required.empty?
        log_info("No installations required")
        return
      end

      log_info("Installing the required components: #{@installations_required.to_a} in install-phase")

      if @installations_required.include?(BrowserStackAppHelper::INSTALL_PHASE_IDENTIFIER)
        BrowserStackAppHelper.check_and_build_and_install_browserstack_app(@device)
        @cleanup_iphone.disable_standby_mode if @device_version.to_i >= 18 && device_type != "iPad" && !@device_state.standby_mode_file_present?
        @cleanup_iphone.grant_access_photos_permission if device_state.photos_permission_file_present?
        @installations_required.delete(BrowserStackAppHelper::INSTALL_PHASE_IDENTIFIER)
      end

      install_app(Chrome.new) if @installations_required.include?(Chrome::BUNDLE_ID)
      install_app(Launcher.new) if @installations_required.include?(Launcher::BUNDLE_ID)
      install_app(Redirect.new) if @installations_required.include?(Redirect::BUNDLE_ID)
      chromium_manager = Chromium.new(@device)
      if @installations_required.include?(chromium_manager.bundle_id)
        chromium_manager.ensure_install
        @installations_required.delete(chromium_manager.bundle_id)
      end
      check_wdas
      assign_license_to_device if @installations_required.include?("assign_license_to_device")

      if @installations_required.empty?
        device_state.remove_installations_required_file
        device_state.remove_install_phase_failed_file
        log_info("All installations successful!")
      else
        log_warn("Installations were not successful for #{@installations_required.to_a}")
        device_state.write_array_to_installations_required_file(@installations_required.to_a)
      end
    rescue => e
      BrowserStack.logger.error("install-phase failed with exception: #{e.message}; #{e.backtrace}")
      if device_state.rebooted_recently_file_older_than_minutes?(60)
        BrowserStack.logger.info("Rebooting device in the hope for good")
        device_state.touch_rebooted_recently_file
        DeviceManager.reboot_and_wait(@device)
      end
      device_state.write_to_install_phase_failed_file(e.message)
      # Writing the updated @installations_required to the file again,
      # so we dont try re-install something that already installed successfully.
      # We're writing to this file from 2 different processes i.e. device-check & install-phase
      data = @installations_required.reject(&:empty?).join("\n")
      device_state.write_to_installations_required_file(data)
      BrowserStack::Zombie.configure
      BrowserStack::Zombie.push_logs('install-phase-failed', e.message, { 'device' => @device })
    end

    private

    def device_type
      @device_config["device_name"].match(/iPad/i).nil? ? "iPhone" : "iPad"
    end

    def check_wdas
      wdas_missing = []
      wdas_not_signed = []

      @appium_versions.each do |appium_version|
        wdas_missing << appium_version if @installations_required.include?("wda_install_#{appium_version}")

        wdas_not_signed << appium_version if @installations_required.include?("wda_signing_#{appium_version}")
      end

      download_wda_for_appiums(wdas_missing) unless wdas_missing.empty?

      sign_wda_for_appiums(wdas_not_signed) unless wdas_not_signed.empty?

      # cleanup
      # If there are any exceptions these won't clear and hence a retry will
      # trigger
      @appium_versions.each do |appium_version|
        @installations_required.delete("wda_install_#{appium_version}")
        @installations_required.delete("wda_signing_#{appium_version}")
      end
    end

    def assign_license_to_device
      log_info("Assigning license to device")
      @installations_required.delete("assign_license_to_device")
      abm_helper = AppleBusinessManagerHelper.new(@device, "testflight")
      return unless abm_helper.device_eligible?(@device_config["region"]) # fail safe

      is_license_assigned_to_device = abm_helper.ensure_license_and_install_app
      if is_license_assigned_to_device
        log_info("TestFlight license assigned to device and TestFlight installed")
      else
        log_info("TestFlight license not assigned to device, still deleting from installations required as we don't want to mark device offline")
      end
    end

    def install_app(app)
      app_bundle_id = app.bundle_id
      log_info("installer is installing #{app_bundle_id}")
      app.update_app_version_using_ios_version(@device_version.to_i)
      app.uninstall_from_device(@device)
      app.setup(@device, @provision_profile, true)

      # TODO: find a better place to put this
      #Trigger safari launcher -> chrome popup dismissal code, if any app was installed
      FileUtils.touch(Chrome.dismiss_chrome_popup_file(CONFIG_ROOT, @device))

      @installations_required.delete(app_bundle_id)
      log_info("Installation successful for #{app_bundle_id}")
    end

    def download_wda_for_appiums(appium_versions)
      log_info("WDA download(s) required for appiums: #{appium_versions}")

      wda_download = WdaDownload.new(devices: [@device], appiums: appium_versions)
      wda_download.download_and_install

      log_info("WDA download(s) completed")
    end

    def sign_wda_for_appiums(appium_versions)
      log_info("WDA code signing required for appium: #{appium_versions}")
      web_driver_agents = @server_config['webdriver_agent_project_paths']

      appium_versions.each do |appium_version|
        log_info("Resigning #{appium_version}")
        sign_wda(appium_version)
        log_info("WDA signing completed for appium #{appium_version}")
      end

      @device_state.remove_resign_wda_file
    end

    def sign_wda(appium_version)
      wda_version = WDAVersion.new(@device, appium_version, @device_version)
      wda_version.sign_wda
    end

    # TODO: Make a class containing files like this.
    def ppuid_file
      "#{CONFIG_ROOT}/ppuid_#{@device}"
    end

    def log_info(message)
      BrowserStack.logger.info(message, { subcomponent: self.class.to_s })
    end

    def log_warn(message)
      BrowserStack.logger.warn(message, { subcomponent: self.class.to_s })
    end
  end
end

if $PROGRAM_NAME == __FILE__
  # Mock logger - output to stdout
  # FIXME: Use a gem instead of a path
  BrowserStack.init_logger('/dev/stdout')

  device_id = ARGV[0]
  raise "Device id required" unless device_id

  BrowserStack.logger.info "Running install phase for #{device_id}"

  server_config = BrowserStack::Configuration.new.all

  IdeviceUtils.configure(server_config)

  device_config = JSON.parse(File.read(CONFIG_JSON_FILE))["devices"][device_id]
  ppuid_config = File.read("#{CONFIG_ROOT}/ppuid_#{device_id}")
  provision_profile = ppuid_config.lines[2].strip

  installer = BrowserStack::Installer.new(
    device_id,
    device_config["device_version"],
    provision_profile,
    device_config
  )
  installer.trigger_install
end
