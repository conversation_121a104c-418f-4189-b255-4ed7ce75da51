require 'base64'
require 'fileutils'

require_relative 'cleanup_manager'
require_relative 'full_cleanup_state'
require_relative '../../recover_device'
require_relative '../../utils/configuration_profiles_generator'
require_relative '../../utils/devicectl'
require_relative '../../utils/idevice_utils'
require_relative '../../utils/ios_watcher'
require_relative '../../utils/custom_mdm_manager'
require_relative '../../../server/device_manager'
require_relative '../../helpers/browserstack_app_helper'
require_relative '../../helpers/data_report_helper'
require_relative '../../helpers/experiments_helper'
require_relative './full_cleanup_state'
require_relative '../../helpers/apple_pay'
require_relative '../../helpers/device_sim_helper'
require_relative '../../helpers/pairing_helper'

require '/usr/local/.browserstack/mobile-common/utils/auth_key_util'

module FullCleanup
  class MDMCleanupManager < CleanupManager # rubocop:todo Metrics/ClassLength

    methods_to_wrap = %i[
      pre_erase_checks
      pre_erase_actions
      erase_device
      recover_device
      ensure_device_paired
      post_recover_steps
      setup_device
    ]

    def initialize(device_id, logger)
      super(device_id, logger)
      @full_cleanup_state = FullCleanup::FullCleanupState.new(device_id)
    end

    include BrowserStack::TimeRecorder

    around_method(*methods_to_wrap) do |original_method, method_name|
      zombie_timer_kind = "#{self.class.name.split('::').last}##{method_name}"

      if method_name.to_s == "erase_device" && !@full_cleanup_state.step_completed?(method_name) && @full_cleanup_state.step_count?(method_name).to_i >= 2 && !@full_cleanup_state.steps_count_file_older_than?(hours: 2)
        raise "Erase device already called twice in the last 2 hours"
      elsif @full_cleanup_state.steps_count_file_older_than?(hours: 2)
        @full_cleanup_state.clear_steps_count_file
      end

      unless @full_cleanup_state.step_completed?(method_name)
        @full_cleanup_state.increment_step_count(method_name) # this is incremented before method call, because if EraseDevice fails twice, we should not call it again
        record_time(zombie_timer_kind, @device_id) { original_method.call }
        @full_cleanup_state.mark_step_completed(method_name)
      end
    end

    def perform
      log(:info, "Starting MDM Full Cleanup")

      pre_erase_checks
      pre_erase_actions
      erase_device
      recover_device
      ensure_device_paired
      post_recover_steps
      setup_device

      log(:info, "Successfully Completed MDM Full Cleanup")
    rescue => e
      log(:error, "Error occured in perform method: #{e}, #{e.backtrace.join("\n")}")
      raise e
    ensure
      cleanup_iphone.save_completed_steps_state
    end

    def pre_erase_checks
      log(:info, "Performing pre erase checks")

      log(:info, "Checking if device is on idevice_id")
      full_cleanup_utils.check_and_recover_device_on_idevice_id

      log(:info, "Device is on idevice_id, checking if device is on devicectl")
      full_cleanup_utils.wait_until_device(:on_devicectl)

      log(:info, "Device is on devicectl, lockdown pairing the device")
      full_cleanup_utils.wait_until_device(:lockdown_paired)

      log(:info, "Checking if device if on ideviceinfo")
      full_cleanup_utils.wait_until_device(:on_ideviceinfo)

      log(:info, "Checking if device is on cfgutil")
      full_cleanup_utils.wait_until_device(:on_cfgutil)

      # Ensure device is supervised
      erase_and_supervise_device unless device_supervised?

      full_cleanup_utils.wait_until_device(:connected, accept_no_ddi: true, accept_available_paired: true, accept_available: true)

      iphone.check_internet_sharing

      # check enrollment to MDM, if not install it
      if device_state.install_mdm_profiles_file_present? || !full_cleanup_utils.device_enrolled_on_mdm_with_retry(ios_device)
        configuration_profiles_manager.install_profile(:MDM, install_via: :cfgutil)
        device_state.remove_install_mdm_profiles_file
      end
      data_report_helper.report({ "status" => "success", "message" => "Pre erase checks passed", "device" => @device_id, "method" => "pre_erase_checks" }, eds_only: true)
    rescue => e
      log(:info, "Error occured in pre erase checks: #{e}")
      data_report_helper.report({ "status" => "failed", "message" => e, "device" => @device_id, "method" => "pre_erase_checks" })
      raise e
    end

    def erase_and_supervise_device
      log(:info, "Started Supervise device")

      log(:info, "Erasing device")
      ios_device.erase

      log(:info, "Checking if device went offline")
      full_cleanup_utils.wait_until_device(:offline)

      log(:info, "Checking if device is on idevice_id")
      full_cleanup_utils.check_and_recover_device_on_idevice_id

      log(:info, "Device is on idevice_id, checking if device is on devicectl")
      full_cleanup_utils.wait_until_device(:on_devicectl)

      log(:info, "Checking if device is on cfgutil")
      full_cleanup_utils.wait_until_device(:on_cfgutil)

      log(:info, "Device is on devicectl, lockdown pairing the device")
      full_cleanup_utils.wait_until_device(:lockdown_paired)

      log(:info, "Checking if device if on ideviceinfo")
      full_cleanup_utils.wait_until_device(:on_ideviceinfo)

      supervise_device

      full_cleanup_utils.wait_until_device(:supervised)

      log(:info, "Device is successfully supervised")
    rescue => e
      log(:error, "Failed to supervise device: #{e.message}, #{e.backtrace.join("\n")}")
      error = e.message
      raise e
    ensure
      status = "success"
      status = "failed" unless error.nil?

      data_report_helper.report({ "status" => status, "error" => error, "device" => @device_id, "method" => "supervise_device" })
    end

    def supervise_device
      retry_count ||= 0
      log(:info, "Supervising device")
      ios_device.supervise_device
    rescue => e
      log(:error, "Failed Supervising Device, retry_count: #{retry_count} error: #{e.message}, #{e.backtrace.join("\n")}")
      retry_count += 1
      if retry_count < 3
        log(:info, "Retrying Supervise Device, retry_count: #{retry_count}")
        sleep 5
        retry
      end
      raise e
    end

    def pre_erase_actions
      log(:info, "Started pre erase actions")
      iphone.check_lockdown
      check_device.check_platform_version_consistency(device_version)
      iphone.check_replay_kit_still_running
      check_device.check_device_supervised
      iphone.check_device_logger_reliability
      iphone.check_for_stale_mitm_process
      device_name == "iPhone14,5" && AuthKeyUtil.delete_auth_key_state_file(session_id, @device_id)
      iphone.check_allow_settings_app_disabled
      iphone.reset_socks5_forwarder
      FileUtils.rm_f("/var/log/browserstack/app_log_#{@device_id}.log")
      DeviceManager.archive_and_truncate_appium_logs(device_config["selenium_port"])
      data_report_helper.report({ "status" => "success", "message" => "Pre erase actions passed", "device" => @device_id, "method" => "pre_erase_actions" }, eds_only: true)
    rescue => e
      log(:info, "Error occured in pre erase actions: #{e}")
      data_report_helper.report({ "status" => "failed", "message" => e, "device" => @device_id, "method" => "pre_erase_actions" })
      raise e
    end

    def erase_device
      start_time = Time.now.to_i
      log(:info, "Erase device triggred...")

      full_cleanup_utils.check_and_install_browserstack_app

      log(:info, "Browserstack app is installed")

      log(:info, "Generating MDM and WiFi profile, sending MDM EraseDevice request")
      mdm_response = full_cleanup_utils.send_mdm_erase_device_request(device_state)

      log(:info, "Checking if device is offline")
      full_cleanup_utils.wait_until_device(:offline) # adding this as device is expected to be online for a while after erase device is triggered.
      log(:info, "Device is offline")

      log(:info, "Checking if device is on idevice_id")
      full_cleanup_utils.check_and_recover_device_on_idevice_id
      log(:info, "Device is on idevice_id")

      # if device came online after going offline, that depicts our erase device command worked fine.
      data_report_helper.report({ "status" => "success", "message" => "Device is erased", "device" => @device_id, "method" => "erase_device" }, eds_only: true)
      log(:info, "Erase device completed")
      true
    rescue => e
      log(:info, "Error occured in erase device: #{e}")
      @full_cleanup_state.clear_completed_steps_state
      data_report_helper.report({ "status" => "failed", "message" => e, "device" => @device_id, "method" => "erase_device" })
      raise e
    end

    def recover_device # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
      retry_count ||= 1
      start_time = Time.now.to_i
      log(:info, "Recover device triggered... Attempt: #{retry_count}")

      full_cleanup_utils.check_and_recover_device_on_idevice_id

      log(:info, "Device is on idevice_id, checking if device is on devicectl")
      full_cleanup_utils.wait_until_device(:on_devicectl)

      log(:info, "Checking if device is on cfgutil")
      full_cleanup_utils.wait_until_device(:on_cfgutil)

      log(:info, "Device is on devicectl, lockdown pairing the device")
      begin
        full_cleanup_utils.wait_until_device(:lockdown_paired)
      rescue => e
        log(:warn, "Lockdown pairing failed, moving forward")
      end

      log(:info, "Checking if device if on ideviceinfo")
      full_cleanup_utils.wait_until_device(:on_ideviceinfo)

      log(:info, "Device is on ideviceinfo, pairing device")
      begin
        full_cleanup_utils.pair_device
      rescue => e
        log(:info, "Failed to pair device, moving forward")
      end

      log(:info, "Turning internet sharing on")
      iphone.check_internet_sharing

      full_cleanup_utils.wait_until_device(:active)
      log(:info, "Device is activated, checking if device is enrolled to MDM")

      full_cleanup_utils.wait_until_device(:enrolled_to_mdm, ios_device)
      log(:info, "Device is on MDM, enabling developer mode")

      sleep(5) # After device is enrolled on MDM, it takes some time to arrive at home screen

      full_cleanup_utils.check_and_enable_developer_mode
      log(:info, "Enabled developer mode on device, checking device on idevice and devicectl")

      full_cleanup_utils.check_and_recover_device_on_idevice_id
      full_cleanup_utils.wait_until_device(:on_idevice)
      full_cleanup_utils.wait_until_device(:on_devicectl)
      full_cleanup_utils.wait_until_device(:on_cfgutil)

      # Checking if BrowserStack app is present or not to ensure EraseDevice
      raise "Erase device failed. BrowserStack app not uninstalled." if full_cleanup_utils.browserstack_app_installed?

      data_report_helper.report({ "status" => "success", "message" => "Device is recovered", "device" => @device_id, "method" => "recover_device" }, eds_only: true)
      true
    rescue => e
      if retry_count < 5
        retry_count += 1
        log(:info, "Error occured in recover device: #{e}, restarting device. Retry attempt: #{retry_count}")
        iphone.reboot
        retry
      end
      log(:info, "Error occured in recover device: #{e}, raising error.")
      data_report_helper.report({ "status" => "failed", "message" => e, "device" => @device_id, "method" => "recover_device" })
      @full_cleanup_state.clear_completed_steps_state
      raise e
    end

    def ensure_device_paired
      log(:info, "Started ensuring device is paired")
      retry_count ||= 0

      log(:info, "Checking if device is on devicectl")
      full_cleanup_utils.wait_until_device(:on_idevice_id)

      log(:info, "Device is on idevice_id, checking if device is on devicectl")
      full_cleanup_utils.wait_until_device(:on_devicectl)

      log(:info, "Checking if device is on cfgutil")
      full_cleanup_utils.wait_until_device(:on_cfgutil)

      log(:info, "Ensuring internet sharing")
      iphone.check_internet_sharing

      log(:info, "Ensure pairing the device")
      pairing_helper = PairingHelper.new(@device_id, BrowserStack.logger)
      pairing_helper.ensure_pair

      log(:info, "Ensured device is paired")
    rescue => e
      log(:error, "Failed ensure pairing the device, error: #{e.message}, #{e.backtrace.join("\n")}, retries: #{retry_count}")
      error = e.message

      if retry_count < 3
        retry_count += 1
        iphone.reboot
        sleep(30)
        retry
      end
      raise e
    ensure
      status = "success"
      status = "failed" unless error.nil?
      data_report_helper.report({ "status" => status, "error" => error, "device" => @device_id, "method" => "ensure_device_paired" })
    end

    def post_recover_steps
      upgraded_device_state.remove_state_files_after_erase unless device_state.first_cleanup_file_present?
      upgraded_device_state.touch_periodic_cleanup_related_frequency_files

      iphone.check_lockdown
      check_device.check_platform_version_consistency(device_version)
      iphone.check_replay_kit_still_running
      check_device.check_device_supervised

      iphone.check_for_stale_mitm_process
      iphone.check_internet_sharing

      iphone.cleanup_live_testing_setup
      iphone.kill_xcode_build_and_iproxy

      device_state.touch_chrome_cleanup_required_file if device_state.first_cleanup_file_present?
      data_report_helper.report({ "status" => "success", "message" => "Post recover steps done", "device" => @device_id, "method" => "post_recover_steps" }, eds_only: true)
    rescue => e
      log(:info, "Error occured in post recover checks: #{e}")
      data_report_helper.report({ "status" => "failed", "message" => e, "device" => @device_id, "method" => "post_recover_steps" })
      raise e
    end

    def setup_device # rubocop:todo Metrics/AbcSize, Metrics/MethodLength, Metrics/PerceivedComplexity, Metrics/CyclomaticComplexity
      log(:info, "Started Device Setup Steps")
      start_time = Time.now.to_i
      retries ||= 0
      last_cleanup_method ||= nil
      last_method_retries ||= 0

      full_cleanup_utils.wait_until_device(:on_idevice)
      full_cleanup_utils.wait_until_device(:connected, accept_no_ddi: false, accept_available_paired: true)
      launch_app_output = DeviceCtl::Device.launch_app(@device_id, "com.apple.Preferences")
      raise "Failed to launch Settings app" unless launch_app_output.include? "Launched application"

      iphone.check_device_language
      iphone.check_internet_sharing if server_config['internet_sharing_enabled'] # TODO: Remove dependency from iphone.rb

      iphone.app_cleanup if device_state.first_cleanup_file_present?

      cleanup_iphone.re_enroll_device_to_mdm

      cleanup_iphone.enable_pwa(skip_removing_restrictions: true) if ExperimentsHelper.enable_pwa?(device_version) && !device_state.pwa_enabled_file_present?

      iphone.install_configuration_profiles

      BrowserStackAppHelper.check_and_build_and_install_browserstack_app(@device_id)

      # Disable Standby Mode
      cleanup_iphone.disable_standby_mode if ios_device.device_type != "iPad"

      # Disable Auto Lock
      cleanup_iphone.disable_auto_lock

      test_to_restore_app = ["files"]

      # Restore AppStore
      cleanup_iphone.install_to_restore_app_store(test_to_restore_app, install_via: :APP_STORE)

      # Install Apps
      # Install Launcher, Chrome, Redirect, Chromium programatically
      cleanup_iphone.install_required_apps

      cleanup_iphone.dismiss_appstore_popup_via_xcui # Dismiss App Store popups after enabling Location Services, or else AppStore will not show Location popup

      # Install Files, Apple Wallet, Translate via UI Automation from AppStore
      first_party_apps = ["files", "find_my", "translate", "safari", "photos", "camera", "messages"]

      first_party_apps += if ios_device.device_type == "iPad"
                            ["weather", "magnifier", "measure", "photo_booth"]
                          else
                            ["apple_wallet", "fitness"]
                          end

      cleanup_iphone.install_first_party_system_apps(first_party_apps, install_via: :APP_STORE)
      # Install Testflight via VPP
      cleanup_iphone.check_and_install_testflight_via_vpp

      cleanup_iphone.enable_location_services

      # Set Device Name
      cleanup_iphone.idevice_name(ios_device.device_type)

      # Device Sanity related
      cleanup_iphone.disable_bluetooth_optimized
      cleanup_iphone.disable_government_notifications
      cleanup_iphone.set_time_to_utc
      iphone.clean_pwa_and_waiting_state_apps

      # Testflight Related
      if IdeviceUtils.app_installed?(@device_id, "TestFlight", attempts: 2)
        cleanup_iphone.disable_testflight_notifications if !ios_device.enable_new_notifications_profile_flow? && !(device_state.first_cleanup_file_present? || device_version.to_i >= 18)

        cleanup_iphone.disable_testflight_background_refresh
      end

      # Disables contacts app suggestion from Apple Intelligence
      cleanup_iphone.siri_contacts_cleanup

      # Safari Related
      cleanup_iphone.safari_cleanup # Need to run Safari Cleanup, since, Enable Location Services opens url in Safari browser
      cleanup_iphone.safari_remote_automation
      cleanup_iphone.enable_safari_web_inspector

      # Feature Specific
      cleanup_iphone.reset_gps_location # TODO: Make this run only once
      cleanup_iphone.install_preloaded_files
      cleanup_iphone.grant_access_photos_permission if device_state.photos_permission_file_present?
      cleanup_iphone.download_file_cleanup

      # Kill xcodebuild and start WDA
      iphone.kill_xcode_build_and_iproxy
      iphone.appium_server.start_server_for_version(server_config['default_appium_version'], true)
      BrowserStack::IPhone.uninstall_wda(@device_id)

      # Install, run WDA and grant location permission
      driver = iphone.launch_appium_and_stop_xcui
      iphone.appium_automation(driver)

      cleanup_iphone.configure_assistive_touch
      cleanup_iphone.set_orientation("potrait")

      cleanup_iphone.kill_apps_via_wda(["com.apple.Preferences", "com.apple.AppStore", "com.apple.mobilesafari"])

      # Re install configuration profiles if any are missing
      # Some steps in cleanup (for example: disable_bluetooth), might remove some profiles, hence we need to ensure those are installed at the end of cleanup
      iphone.install_configuration_profiles

      iphone.restart_webkit_proxy

      iphone.press_home
      iphone.lock_device_from_wda_with_fallback

      if device_version.to_i >= IOS_WATCHER_MIN_IOS_VERSION
        # clean at end of cleanup
        IosWatcher.clear_logs(@device_id)
        device_state.remove_watcher_unreliable_file
        device_state.remove_watcher_unreliable_off_usb_file
      end
      iphone.cleanup_app_testing_files
      FileUtils.rm_f(DeviceManager.session_start_file(@device_id).to_s)

      log(:info, "Completed Device Setup Steps")
    rescue => e
      if device_state.first_cleanup_file_present?
        current_cleanup_method = cleanup_iphone.current_cleanup_method
        last_method_retries = 0 if last_cleanup_method != current_cleanup_method
        last_cleanup_method = current_cleanup_method
        if !current_cleanup_method.nil? && retries < 5 && last_method_retries < 2
          retries += 1
          last_method_retries += 1
          log(:info, "Retrying setup device for failure in method: #{current_cleanup_method}, last_method_retries: #{last_method_retries}, overall_retry_count: #{retries}")
          retry
        end
      end
      error = e.message
      log(:error, "Error in Device Setup steps, error: #{e}")
      raise e
    ensure
      status = "success"
      status = "failed" unless error.nil?
      data_report_helper.report({ "status" => status, "error" => error, "device" => @device_id, "method" => "setup_device", "time_taken" => Time.now.to_i - start_time })
    end

    def first_cleanup
      log(:info, "Started First Cleanup Steps")
      start_time = Time.now.to_i

      post_recover_steps
      setup_device
    rescue => e
      error = e.message
      log(:error, "Error in First Cleanup, error: #{e}")
      raise e
    ensure
      cleanup_iphone.save_completed_steps_state
      status = "success"
      status = "failed" unless error.nil?
      data_report_helper.report({ "status" => status, "error" => error, "device" => @device_id, "method" => "first_cleanup", "time_taken" => Time.now.to_i - start_time })
    end

    def run_full_cleanup?
      # Uncomment this once we want to run full cleanup every 5th cleanup
      return false unless frequent_device_id?

      cleanup_count = device_state.cleanup_count_file_present? ? device_state.read_cleanup_count_file.to_i : 0
      if cleanup_count < 5
        return false
      else
        device_state.remove_cleanup_count_file
      end

      true
    end

    def frequent_device_id?
      FULL_CLEANUP_CHECKS[:frequent_device_ids].include?(@device_id)
    end

    def device_eligible?
      return false if CustomMDMManager.is_custom_mdm_device?(@device_id)

      is_disabled = device_state.disable_full_cleanup_file_present?
      return false if is_disabled

      # check for version
      is_version_valid = device_version.to_f >= FULL_CLEANUP_CHECKS[:min_os_version] && device_version.to_f <= FULL_CLEANUP_CHECKS[:max_os_version]
      is_device_valid = FULL_CLEANUP_CHECKS[:devices].include?(device_name) || FULL_CLEANUP_CHECKS[:device_ids].include?(@device_id)
      is_region_valid = FULL_CLEANUP_CHECKS[:regions].include?(region)
      return false if !is_version_valid || !is_device_valid || !is_region_valid

      is_cgfutil_installed_and_device_provisioned = configuration_profiles_manager.device_uses_cfgutil_managed_profiles?(verify_cfgutil: true)
      return false unless is_cgfutil_installed_and_device_provisioned

      is_dedicated_cloud_device = device_state.dedicated_device_file_present?
      return false if is_dedicated_cloud_device

      is_apple_pay_device = Secure::ApplePay.apple_pay_device?(@device_id)
      return false if is_apple_pay_device

      is_public_sim_device = DeviceSIMHelper.sim?(@device_id)
      return false if is_public_sim_device

      log(:error, "Device is eligible for Full cleanup")
      true
    rescue => e
      log(:error, "Error in device eligible, error: #{e}")
      false
    end

    def device_supervised?
      is_device_supervised = false
      begin
        full_cleanup_utils.wait_until_device(:supervised)
        is_device_supervised = true
      rescue => e
        log(:error, "Device is not supervised, error: #{e.message}, #{e.backtrace.join("\n")}")
      end

      is_device_supervised
    end

    private

    def data_report_helper
      event_name = "mdm-full-cleanup"
      event_name = "mdm-first-cleanup" if device_state.first_cleanup_file_present?
      @data_report_helper ||= DataReportHelper.new(event_name,
                                                   session_id: session_id,
                                                   ios_version: device_version,
                                                   device: @device_id)
    end
  end
end
