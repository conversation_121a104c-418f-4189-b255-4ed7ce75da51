# frozen_string_literal: true

require_relative '../../configuration'
require_relative '../../models/device_state'

require 'json'

module FullCleanup
  class FullCleanupState
    def initialize(device_id)
      @device_id = device_id
      @device_state = DeviceState.new(device_id)
      @completed_steps = get_completed_steps_state(device_id)
      @steps_count = get_steps_count_state(device_id)
    end

    def get_completed_steps_state(device_id)
      return {} unless File.exist?(completed_steps_file(device_id))

      completed_steps_content = File.read(completed_steps_file(device_id))
      return {} if completed_steps_content.empty?

      JSON.parse(completed_steps_content, symbolize_names: true)
    end

    def save_completed_steps_state
      File.write(completed_steps_file(@device_id), @completed_steps.to_json)
    end

    def clear_completed_steps_state
      FileUtils.rm_rf(completed_steps_file(@device_id))
      @completed_steps = {}
    end

    def mark_step_completed(step)
      @completed_steps[step] = true
      save_completed_steps_state
    end

    def step_completed?(step)
      BrowserStack.logger.info("Skipping #{step}, already cleaned") if @completed_steps[step]
      @completed_steps[step]
    end

    def unmark_method(step)
      @completed_steps.delete(step)
    end

    def mark_step_pending(step)
      @completed_steps[step] = false
      save_completed_steps_state
    end

    def completed_steps_file(device_id)
      "#{BrowserStack::Configuration['state_files_dir']}/full_cleanup_completed_steps_#{device_id}"
    end

    def get_steps_count_state(device_id)
      return {} unless File.exist?(full_cleanup_steps_count_file(device_id))

      steps_count = File.read(full_cleanup_steps_count_file(device_id))
      return {} if steps_count.empty?

      JSON.parse(steps_count, symbolize_names: true)
    end

    def save_steps_count
      File.write(full_cleanup_steps_count_file(@device_id), @steps_count.to_json)
    end

    def clear_steps_count_file
      FileUtils.rm_rf(full_cleanup_steps_count_file(@device_id))
      @steps_count = {}
    end

    def increment_step_count(step)
      @steps_count[step] = @steps_count[step] ? @steps_count[step] + 1 : 1
      save_steps_count
    end

    def step_count?(step)
      @steps_count[step]
    end

    def steps_count_file_older_than?(hours: 2)
      file_path = full_cleanup_steps_count_file(@device_id)

      # Check if the file exists
      return true unless File.exist?(file_path)

      # Get the last modification time of the file
      file_mtime = File.mtime(file_path)

      # Calculate the time difference
      time_difference_in_hours = (Time.now - file_mtime) / 3600.0

      # Check if the time difference is greater than the specified hours
      time_difference_in_hours > hours
    end

    def decrement_step_count(step)
      @steps_count[step] = @steps_count[step] - 1
      save_steps_count
    end

    def full_cleanup_steps_count_file(device_id)
      "#{BrowserStack::Configuration['state_files_dir']}/full_cleanup_steps_count_#{device_id}"
    end

    def increment_cleanup_count
      cleanup_count = @device_state.cleanup_count_file_present? ? @device_state.read_cleanup_count_file.to_i : 0
      @device_state.write_to_cleanup_count_file(cleanup_count + 1)
    end
  end
end
