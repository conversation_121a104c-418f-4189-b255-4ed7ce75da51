require_relative '../../configuration'
require_relative '../../device_conf'
require_relative '../../models/device_state'
require_relative '../../cleanup_iphone'
require_relative '../../utils/utils'
require_relative '../../models/ios_device'
require_relative '../../checks/check_device'
require_relative '../../utils/configuration_profiles_manager'
require_relative './full_cleanup_utils'

module FullCleanup
  class CleanupManager
    def initialize(device_id, logger)
      @device_id = device_id
      @logger = logger
    end

    def erase_device
      raise NotImplementedError, "erase_device method must be implemented by subclass"
    end

    def recover_device
      raise NotImplementedError, "recover_device method must be implemented by subclass"
    end

    def perform
      raise NotImplementedError, "perform method must be implemented by subclass"
    end

    def post_cleanup
      raise NotImplementedError, "post_cleanup method must be implemented by subclass"
    end

    def session_file
      @session_file ||= "#{server_config['state_files_dir']}/#{@device_id}_session"
    end

    def full_cleanup_utils
      @full_cleanup_utils ||= FullCleanupUtils.new(@device_id, @logger)
    end

    def session_params
      begin
        @session_params ||= Utils.read_json_file(session_file)
      rescue Errno::ENOENT => e
        log(:info, "No session file found, Product specific tasks won't run! #{e.message}")
        @session_params ||= {}
      end
      @session_params
    end

    def session_id
      @session_id ||= (session_params["live_session_id"] || session_params["automate_session_id"] || session_params["app_live_session_id"] || session_params["session_id"]).to_s
    end

    def device_config
      @device_config ||= BrowserStack::DeviceConf[@device_id]
    end

    def configuration_profiles_manager
      @configuration_profiles_manager ||= ConfigurationProfilesManager.new(@device_id, BrowserStack.logger)
    end

    def device_version
      @device_version ||= device_config["device_version"]
    end

    def region
      @region ||= device_config["region"]
    end

    def device_name
      @device_name ||= device_config["device_name"]
    end

    def server_config
      @server_config ||= BrowserStack::Configuration.new.all
    end

    def device_state
      @device_state ||= DeviceState.new(@device_id)
    end

    def upgraded_device_state
      @upgraded_device_state ||= UpgradedDeviceCheckState.new(@device_id)
    end

    def ios_device
      @ios_device ||= BrowserStack::IosDevice.new(@device_id, self.class.to_s, BrowserStack.logger)
    end

    def check_device
      @check_device ||= BrowserStack::CheckDevice.new(@device_id, REDIS_CLIENT)
    end

    def wda_port
      @wda_port ||= device_config["webdriver_port"]
    end

    # TODO: Remove dependency from iphone.rb
    def iphone
      @iphone ||= BrowserStack::IPhone.new(device_config, @device_id)
    end

    def cleanup_iphone
      @cleanup_iphone ||= CleanupIphone.new(
        @device_id,
        device_config["selenium_port"],
        device_config["device_version"],
        device_config["device_name"],
        device_config["webdriver_port"],
        server_config["orientation-lock-value"] || 0,
        session_id
      )
    end

    def log(level, message)
      @logger.send(level.to_sym, "[FullCleanup] #{message}")
    end
  end
end
