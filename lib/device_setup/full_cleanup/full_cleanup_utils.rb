require 'base64'

require_relative 'cleanup_manager'
require_relative '../../../lib/utils/configuration_profiles_generator'
require_relative '../../../lib/utils/devicectl'
require_relative '../../../lib/recover_device'
require_relative '../../../lib/utils/idevice_utils'
require_relative '../../../server/device_manager'
require_relative '../../../lib/helpers/pairing_helper'
require_relative '../../wrappers/cfg_util'
require_relative './full_cleanup_state'

module FullCleanup
  class FullCleanupUtils # rubocop:todo Metrics/ClassLength
    def initialize(device_id, logger)
      @device_id = device_id
      @logger = logger
    end

    def send_mdm_erase_device_request(device_state)
      # generate_wifi_profile_data_file # Not sending wifi profile data, and will be using USB internet so that for each host public IPs differ
      generate_mdm_profile_data_file
      IosMdmServiceClient.send(:make_request, {
        "request_type" => "EraseDevice",
        "udid" => @device_id,
        "disallow_proximity_setup" => true,
        "return_to_service" => {
          "enabled" => true,
          "mdm_profile_data" => encode_file_to_base64(mdm_profile_data_file_path)
          # "wifi_profile_data" => encode_file_to_base64(wifi_profile_data_file_path)
        }
      }, REDIS_CLIENT)
    rescue => e
      device_state.touch_install_mdm_profiles_file
      log(:info, "MDM erase device failed due to error: #{e.message}")
      raise e
    end

    def configuration_profiles_generator
      @configuration_profiles_generator ||= ConfigurationProfilesGenerator.new(@device_id, BrowserStack.logger)
    end

    def configuration_profiles_manager
      @configuration_profiles_manager ||= ConfigurationProfilesManager.new(@device_id, BrowserStack.logger)
    end

    def cfgutil
      @cfgutil ||= CFGUtil.new(udid: @device_id)
    end

    def generate_wifi_profile_data_file
      configuration_profiles_manager.prepare_wifi_configuration_profile
    end

    def generate_mdm_profile_data_file
      configuration_profiles_generator.prepare_mdm_enrollment_profile
    end

    def encode_file_to_base64(file_path)
      file_content = File.binread(file_path)
      Base64.strict_encode64(file_content)
    rescue => e
      log(:info, "Encoding file to base64 failed: #{e.message}")
      raise e
    end

    def wifi_profile_data_file_path
      "/tmp/wifi_profile_#{@device_id}.mobileconfig"
    end

    def mdm_profile_data_file_path
      "/tmp/mdm_enrollment_profile_#{@device_id}.mobileconfig"
    end

    def activation_state
      IdeviceUtils.ideviceinfo(@device_id, "ActivationState").first
    end

    def check_and_install_browserstack_app
      return true if browserstack_app_installed?

      BrowserStackAppHelper.install_browserstack_app(@device_id, avoid_devicectl: true)
    end

    def browserstack_app_installed?
      BrowserStackAppHelper.browserstack_app_present?(@device_id)
    end

    def device_on_usb?
      IdeviceUtils.idevices.include?(@device_id)
    rescue => e
      false
    end

    def ensure_lockdown_pairing
      begin
        return true if IdeviceUtils.idevicepair(@device_id, "validate").start_with?("SUCCESS") # If validate returns SUCCESS, no need to run pair command - MOBCR-4053
        return true if IdeviceUtils.idevicepair(@device_id, "pair").start_with?("SUCCESS")
      rescue => e
        log(:info, "Error occured in idevicepair. Error: #{e}. Continuing...")
      end

      pairing_helper = PairingHelper.new(@device_id, BrowserStack.logger)
      pairing_helper.ensure_lockdown_pairing

      true
    rescue => e
      log(:info, "Error occured in ensure_lockdown_pairing. Error: #{e}. Continuing...")
      false
    end

    def unique_id
      unique_device_id_output = IdeviceUtils.ideviceinfo(@device_id, "UniqueDeviceID").first
      IdeviceUtils.ideviceinfo(@device_id, "UniqueDeviceID").first

      raise "Device not found"  if unique_device_id_output.include?("No device found") || unique_device_id_output.include?("Not found")

      unique_device_id_output
    end

    def online?
      device_on_usb? && unique_id == @device_id
    end

    def device_enrolled_on_mdm?(ios_device)
      !ios_device.fetch_installed_profile(MICROMDM_ENROLLMENT_PROFILE_IDENTIFIER, cached: false).nil?
    end

    def device_enrolled_on_mdm_with_retry(ios_device, retry_limit: 15)
      retry_count ||= 1

      return true if device_enrolled_on_mdm?(ios_device)
      return false if retry_count == 15

      raise "Device still not enrolled"
    rescue => e
      if retry_count < retry_limit
        retry_count += 1
        log(:info, "Error occured in check_device_enrolled_on_mdm_with_retry: #{e}")
        sleep 5
        retry
      end
      raise e
    end

    def wait_until_device(action, ios_device = nil, accept_no_ddi: false, accept_available_paired: false, accept_available: false) # rubocop:todo Metrics/CyclomaticComplexity
      online_check_count = 15 # can be increased as per testing
      online_check_count.times do |i|
        case action
        when :on_idevice
          return nil if online?
        when :on_idevice_id
          return nil if device_on_usb?
        when :on_ideviceinfo
          return nil if unique_id == @device_id
        when :on_cfgutil
          return nil if on_cfgutil?
        when :lockdown_paired
          return nil if ensure_lockdown_pairing
        when :active
          return nil if activation_state == "Activated"
        when :on_devicectl
          return nil if on_devicectl?
        when :enrolled_to_mdm
          return nil if device_enrolled_on_mdm?(ios_device)
        when :connected
          return nil if connected?(connected_no_ddi: accept_no_ddi, available_paired: accept_available_paired, available: accept_available)
        when :offline
          return nil unless online?
        when :supervised
          return nil if IdeviceUtils.device_supervised?(@device_id)
        end

        log(:info, "Device still not #{action} #{@device_id}. Check number: #{i}")
        sleep 5
      end
      raise "Manual fix required: device still not #{action}"
    end

    def reset_usb
      DeviceManager.reset_usb(@device_id)
    end

    def check_and_recover_device_on_idevice_id
      idevice_id_retries ||= 1
      wait_until_device(:on_idevice_id)
    rescue => e
      log(:error, "Device not found on idevice_id, retries: #{idevice_id_retries} error: #{e.message}, #{e.backtrace.join("\n")}")
      if idevice_id_retries < 2
        idevice_id_retries += 1
        log(:info, "Retrying after resetting usb...")
        reset_usb
        retry
      end
      raise e
    end

    def connect_device_when_paired
      device_state = devicectl_state
      if device_state.include?("paired")
        # this will establish connection to the device again, device is already paired
        DeviceCtl::Device.apps(@device_id)
        nil
      end
    end

    def check_and_enable_developer_mode
      output, exit_code = BrowserStack::OSUtils.execute("xcrun devmodectl single #{@device_id}", true)
      log(:info, "Output of enabling developer mode: #{output}")
      raise "Some error occured in enabling developer mode" if exit_code != 0

      true
    rescue => e
      raise OFFLINE_REASON_NO_DDI unless connected?
    end

    def devicectl_state
      state = DeviceCtl::List.device_state(@device_id)
      log(:info, "Device state: #{state}")
      state
    end

    def on_devicectl?
      device_state = devicectl_state
      return false if device_state.nil? || device_state.empty? || device_state.include?("No device") || device_state.include?("unavailable")

      true
    end

    def on_cfgutil?
      ecid = cfgutil.list_devices.find { |_, device_info| device_info['UDID'] == @device_id }&.first
      log(:info, "Found ecid: #{ecid}")
      !ecid.nil?
    rescue => e
      false
    end

    def pair_device
      retry_count ||= 1
      output, exit_code = DeviceCtl::Manage.pair(@device_id, return_status: true, timeout: 30, kill_after: 30, return_error: true)
      log(:info, "Pair device output: #{output}, exit_code: #{exit_code}")
      if output.include?("This device has already been manually paired")
        return true
      elsif output.include?("ERROR")
        raise output
      end

      true
    rescue => e
      if retry_count < 3
        retry_count += 1
        log(:info, "Error occured in pair device: #{e}, sleeping for 1 second. Retry attemp: #{retry_count}")
        sleep 1
        retry
      end
      raise e
    end

    def connected?(connected_no_ddi: false, available_paired: false, available: false)
      device_state = devicectl_state
      connected_no_ddi_state = connected_no_ddi ? device_state.eql?("connected (no DDI)") : false
      available_paired_state = available_paired ? device_state.eql?("available (paired)") : false
      available_state = available ? device_state.start_with?("available") : false
      connected_state = device_state.eql?("connected")
      connected_state || connected_no_ddi_state || available_paired_state || available_state
    end

    def log(level, message)
      @logger.send(level.to_sym, "[FullCleanupUtils] #{message}")
    end
  end
end
