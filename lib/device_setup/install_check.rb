require 'set'

require_relative '../../config/constants'
require_relative '../../lib/apps/chrome'
require_relative '../../lib/apps/launcher'
require_relative '../../lib/apps/redirect'
require_relative '../../lib/utils/utils'
require_relative '../../lib/custom_exceptions'
require_relative '../../lib/models/device_state'
require_relative '../../lib/provisioning/ppuid_file'
require_relative '../../lib/utils/apple_tv_utils'
require_relative '../../lib/apps/chromium'

module BrowserStack
  # InstallCheck is responsible for checking if all the required components not present on the
  # device. This also updates the version of the apps installed on the device in the config file
  class InstallCheck # rubocop:todo Metrics/ClassLength
    attr_reader :device_state

    def initialize(udid, device_version, ppuid, config, full_check: true)
      @device = udid
      @device_state = DeviceState.new(udid)
      @device_version = device_version
      @ppuid = ppuid
      @full_check = full_check
      @device_config = config
      @server_config = BrowserStack::Configuration.new.all
      @installations_required = if device_state.installations_required_file_present?
                                  device_state.installations_required_file_to_array.to_set
                                else
                                  Set.new
                                end
      # This is to avoid IdeviceUtils.list_apps being called for all devices
      # as running ideviceinstaller on multiple devices causes timeouts
      @user_installed_apps = if @full_check
                               begin
                                 IdeviceUtils.list_apps(udid)
                               rescue => e
                                 log_warn("Error checking installed apps on device: #{e.message}; #{e.backtrace}")
                                 nil
                               end
                             end
      @web_driver_agents = @server_config['webdriver_agent_project_paths']
    end

    def check_requirements
      log_info("Full check enabled for device? #{@full_check}")
      # @full_check is true for 1 device only in each device check
      # Checking requirements 1 device at a time to avoid building and signing the same app multiple times with the same provisioning profile
      return @installations_required unless @full_check

      log_info("Full check is true, checking all required components to be installed for device #{@device}")

      is_apple_tv_device = IdeviceUtils.apple_tv_device?(@device)
      @installations_required = Set.new if is_apple_tv_device

      check_missing_browserstack_app unless is_apple_tv_device

      check_missing_wdas
      check_unsigned_wdas
      check_device_assignment

      unless IdeviceUtils.apple_tv_device?(@device)
        check_app(Chrome.new)
        check_app(Launcher.new)
        chromium_manager = Chromium.new(@device)
        @installations_required << chromium_manager.bundle_id if chromium_manager.install_required?(@device_version)
        if !@device_state.enable_redirect_extension_file_present? && @device_version.to_f >= 15
          # Only install the redirect application in device check if cleanup hasn't been run on this device before
          check_app(Redirect.new)
        end
      end

      update_installations_required

      @installations_required
    end

    private

    def check_app(app)
      if app_installation_required?(app)
        log_info("Need to reinstall: #{app.name}")
        @device_config['app_info']&.delete(app.bundle_id)
        @installations_required << app.bundle_id
      else
        update_config_if_nil(app)
      end
    end

    def app_installation_required?(app)
      return true if @installations_required.include?(app.bundle_id)

      app.update_app_version_using_ios_version(@device_version.to_i)
      return true unless app.app_present_for_provision_profile?(@ppuid)

      installed_app = InstalledApp.new(@device, app.bundle_id)
      return true if installed_app.reinstall?(latest_version: app.version)

      # Assuming apps are installed if IdeviceUtils.list_apps raised an exception
      return true if !@user_installed_apps.nil? && !app.present_on_device?(@user_installed_apps)

      false
    end

    # BrowserStack app = Main App (com.browserstack.app) + Test Suite App (com.browserstack.BrowserStackUITests.xctrunner)
    # The browserstack test suite app is not required in session - only needed for cleanup automation,
    # It is uninstalled post-cleanup automation on iOS 14+ ( and < iOS 17 ) devices.
    #
    # During installation we do these checks again and only do the
    # required part: build or install. Refer to BrowserStackAppHelper.check_and_build_and_install_browserstack_app
    def check_missing_browserstack_app
      app_built = BrowserStackAppHelper.browserstack_app_built?(@device)
      app_installed = !BrowserStackAppHelper.browserstack_app_install_required?(@device)

      if app_built && app_installed
        log_info('BrowserStack app is already built and installed !')
      else
        unless app_built
          log_info("Browserstack App not built")
          @installations_required << BrowserStackAppHelper::INSTALL_PHASE_IDENTIFIER
          return
        end

        # TODO: Use different install phase identifier for this
        log_info("Browserstack App not installed")
        @installations_required << BrowserStackAppHelper::INSTALL_PHASE_IDENTIFIER
      end
    end

    def check_missing_wdas
      missing_wdas = wdas_not_downloaded
      if !missing_wdas.empty?
        log_info("WDA downloads/install required for appiums: #{missing_wdas}")
        missing_wdas.each do |appium_version|
          @installations_required << "wda_install_#{appium_version}"
        end
      else
        log_info('No WDAs to be downloaded/installed')
      end
    end

    def check_device_assignment
      log_info("Checking license is assigned to device or not")
      abm_helper = AppleBusinessManagerHelper.new(@device, "testflight")
      return unless abm_helper.device_eligible?(@device_config["region"])

      is_license_assigned_to_device = abm_helper.license_assigned_to_device?
      if !is_license_assigned_to_device
        log_info("License needs to be assigned to device: #{!is_license_assigned_to_device}")
        @installations_required << "assign_license_to_device"
      else
        log_info('License already assigned to device')
      end
    end

    def wdas_not_downloaded
      missing_wdas = []
      @web_driver_agents.keys.map(&:to_s).each do |appium_version|
        missing_wdas << appium_version if WDAVersion.outdated?(@device, appium_version, @device_version)
      end
      missing_wdas
    end

    def check_unsigned_wdas
      unsigned_wdas = wdas_not_signed
      if !unsigned_wdas.empty?
        log_info("WDA signing required for appiums: #{unsigned_wdas}")

        unsigned_wdas.each do |appium_version|
          @installations_required << "wda_signing_#{appium_version}"
        end
      else
        log_info('No Wda signing required')
      end
    end

    def wdas_not_signed
      unsigned_wdas = []

      @web_driver_agents.keys.map(&:to_s).each do |appium_version|
        if unsigned_wda?(appium_version)
          unsigned_wdas << appium_version
        else
          # if it is codesigned, then the wda should not be in the
          # installation requirements
          @installations_required.delete("wda_signing_#{appium_version}")
        end
      end

      unsigned_wdas
    end

    def unsigned_wda?(appium_version)
      # For dev use, when wda resigning is required
      if @device_state.resign_wda_file_present?
        log_info("Wda #{appium_version} needs signing - resign wda file present")
        return true
      end

      ppuid_file = PpuidFile.new(@device)

      raise 'Invalid ppuid file' unless ppuid_file.valid?

      wda_folder = WDAVersion.wda_folder_path(@device_version, appium_version, ppuid_file.ppuid)

      unless File.exists?("#{wda_folder}/ppuid")
        log_info("Wda #{appium_version} needs signing - "\
                 "ppuid in device ppuid file: #{ppuid_file.ppuid}")
        return true
      end

      ppuid_file_content = File.readlines("#{wda_folder}/ppuid")
      unless ppuid_file.ppuid == ppuid_file_content[2]
        log_info("Wda #{appium_version}_#{wda_folder} needs signing - "\
                 "ppuid file in WDA outdated: #{ppuid_file.ppuid}")
        return true
      end

      false
    end

    def update_config_if_nil(app)
      @device_config['app_info'] ||= {} # in case empty

      return unless @device_config['app_info'][app.bundle_id].nil?

      @device_config['app_info'][app.bundle_id] = {
        "version" => app.version,
        "installed_at" => Time.now
      }
    end

    def update_installations_required
      installations_required_array = @installations_required.reject(&:empty?).join("\n")

      log_info("State file for all components to be installed: #{installations_required_array}")
      # We're writing to this file from 2 different processes i.e. device-check & install-phase
      device_state.write_to_installations_required_file(installations_required_array)
    end

    def log_info(message)
      BrowserStack.logger.info(message, { subcomponent: self.class.to_s })
    end

    def log_warn(message)
      BrowserStack.logger.warn(message, { subcomponent: self.class.to_s })
    end

  end
end
