# IOSBackup Manager

Proxy object used to back up iOS devices, a class is passed reference to the constructor of this object. The object will then handle any logic to backup a device based on the utility passed. 

Each Utility needs to have a corresponding [Manager class](managers/cfgutil_mananger.rb) which has methods inherited from [manager_abs.rb](managers/maanger_abs.rb). 

> Avoid directly using specific Manager classes

## Usage

```ruby
cd realmobile && bundle exec irb

require_relative 'lib/device_setup/backup_manager/backup_manager'
```

### Use IOSBackupManager
```ruby
# Create object without device
bm = IOSBackupManager.new(CFGUtil)

# Create object with device
bm = IOSBackupManager.new(CFGUtil, '00008110-000260691E52801E')

# Set ID after
bm.device_uuid = '00008110-000260691E52801E'

# Backup Device
bm.backup_device

# Check Backup Exists
bm.backup_present?

# Check Backup Exists, while specifying backup name
bm.backup_present? backup_name: '00008110-000260691E52801E'

# Restore Backup
bm.restore_backup backup_name: 'ios15_ipad12,1'

# Change the device, using keyword arg
bm.restore_backup device_uuid: '00008110-000260691E52801E', backup_name: 'ios15_ipad12,1'
```

### Create new interface for BackupManager

Add logic for backing up and restore into 

```ruby
class SomeNewBackupSolutionManager < BackupManager  
  def backup_device(*args, **kwargs)
    doSomething()
  end

  def backup_present?(*args, **kwargs)
    doSomething()
  end

  def restore_backup(*args, **kwargs)
    doSomething()
  end

  def _setup_internal(*args, **kwargs)
    return if args.empty? && kwargs.empty?
  end
end
```

## TODO's
- Some macs, have the `/Users/<USER>/Library/Application Support/MobileSync` directory blocked from terminal access. Which could cause problems with specifying backups
- I haven't been able to try `restore_backup`, as it will break the device. 
- setup_internal could be altered to just automatically run after each method call. 