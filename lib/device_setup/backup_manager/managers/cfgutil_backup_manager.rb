require_relative 'manager_abs'
require_relative '../../../wrappers/cfg_util'
require_relative '../../../utils/plist_buddy'
require_relative '../../../utils/http_utils'
require_relative '../../../../lib/configuration'

class CFGUtilBackupManager < BackupManager #rubocop:todo Metrics/ClassLength

  def self.run_from_bash
    device_id = ARGV[0]
    command = ARGV[1]

    manager = CFGUtilBackupManager.new(device_id)

    case command
    when "supported?"
      puts manager.supported?
    when "retrieve_backup"
      manager.retrieve_backup
    when "backup_ready?"
      puts manager.backup_ready?
    else
      raise "Unsupported Command: #{command}"
    end
  end

  MOBILESYNC = '/Users/<USER>/Library/Application\ Support/MobileSync/Backup'.freeze
  BASE_DOWNLOAD_URL = 'http://caching-proxy-server.service.prod.mobile.browserstack.com:3128/ios-njb-backups'.freeze

  def initialize(device_uuid)
    @device = device_uuid
    @version = device_config["device_version"]
    @serial = device_config["device_serial"]
    @model = device_config["device_name"]
    @region = device_config["sub_region"]
    @ip_addr = device_config["ip"]
    @cfgutil = CFGUtil.new(udid: device_uuid)
  end

  def device_config
    @device_config ||= begin
      JSON.parse(File.read(BrowserStack::Configuration['config_json_file']))["devices"][@device]
    rescue => e
      log :warn, "Unable to load device config"
      {}
    end
  end

  def machine_provisioned?
    Dir["#{MOBILESYNC}/*"]  # Will raise an operation not permitted error if symlink is not configured
    true
  rescue => e
    false
  end

  def supported?
    false
  end

  def retrieve_backup(force_clean: true)
    clean if force_clean

    download
    extract
    patch_backup
  end

  def clean
    log :info, "Cleaning directories to download again from scratch"
    BrowserStack::OSUtils.execute("rm -r #{tmp_backup_archive_path}")
    BrowserStack::OSUtils.execute("rm -r #{backup_folder}")
  end

  def download
    # Download the correct version/model backup and change all the names etc.
    download_url = BASE_DOWNLOAD_URL + download_path(@model, @version, @region)
    log :info, "Downloading backup from #{download_url} to #{tmp_backup_archive_path}"
    BrowserStack::HttpUtils.download(download_url, tmp_backup_archive_path)
  rescue => e
    log :error, "Failed to download backup: #{e.message} #{e.backtrace}"
    raise BackupRetrievalError, e.message
  end

  def extract
    # Extract backup files to the correct location and rename folder to device ID
    log :info, "Extracting backup from #{tmp_backup_archive_path} to #{backup_folder}"
    result, status = BrowserStack::OSUtils.execute("unzip -uo #{tmp_backup_archive_path} -d #{backup_folder}", true)
    raise "unzip command failed: #{result} " if status != 0
  rescue => e
    log :error, "Failed to extract backup: #{e.message} #{e.backtrace}"
    raise BackupRetrievalError, e.message
  end

  def patch_backup
    log :info, "Make device specific changes in backup files"
    BrowserStack::PlistBuddy.set_key_in_plist("#{backup_folder}/Info.plist", "\"Target Identifier\"", @device)
    BrowserStack::PlistBuddy.set_key_in_plist("#{backup_folder}/Manifest.plist", "Lockdown:UniqueDeviceID", @device)
    BrowserStack::PlistBuddy.set_key_in_plist("#{backup_folder}/Manifest.plist", "Lockdown:SerialNumber", @serial)
  rescue => e
    log :error, "Failed to patch backup: #{e.message} #{e.backtrace}"
    raise BackupSpecificationError, e.message
  end

  def validate_backup
    log :info, "Validating patched backup"

    raise BackupSpecificationError, "Unable to set device id in Info.plist"  if BrowserStack::PlistBuddy.get_value_of_key("#{backup_folder}/Info.plist", "\"Target Identifier\"").strip != @device

    raise BackupSpecificationError, "Unable to set device id in Manifest.plist" if BrowserStack::PlistBuddy.get_value_of_key("#{backup_folder}/Manifest.plist", "Lockdown:UniqueDeviceID").strip != @device

    raise BackupSpecificationError, "Unable to set serial in Manifest.plist" if BrowserStack::PlistBuddy.get_value_of_key("#{backup_folder}/Manifest.plist", "Lockdown:SerialNumber").strip != @serial

    log :info, "validations passed!"
  end

  def backup_device
    @cfgutil.backup
  end

  def erase_device
    @cfgutil.erase
  end

  def backup_ready?
    unless @cfgutil.backup_exists?(@device)
      log :info, "Backup not downloaded yet"
      return false
    end

    begin
      validate_backup
    rescue BackupSpecificationError => e
      log :info, "Backup validation failed"
      return false
    end

    true
  end

  def restore_backup
    raise BackupNotFoundError, "No backup found for #{@device}" unless backup_ready?

    @cfgutil.restore_backup(source: @device)
  rescue => e
    raise BackupRestoreError, e.message
  end

  def delete_backup
    BrowserStack::OSUtils.execute("rm -r #{backup_folder}")
  end

  private

  def tmp_backup_archive_path
    "/tmp/#{@device}_backup.zip"
  end

  def backup_folder
    "#{MOBILESYNC}/#{@device}"
  end

  def manifest_version
    BrowserStack::PlistBuddy.get_value_of_key("#{backup_folder}/Manifest.plist", "Lockdown:ProductVersion").strip
  end

  def download_path(device_model, device_version, device_region)
    "/#{device_model}_#{device_version}_#{device_region}_backup_v2.zip"
  end
end

if __FILE__ == $PROGRAM_NAME
  # To run directly from bash
  CFGUtilBackupManager.run_from_bash
end
