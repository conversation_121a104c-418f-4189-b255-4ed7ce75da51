class BackupManager
  class MethodNotImplemented < StandardError; end

  class IOSBackupManagerError < StandardError; end

  class BackupNotImplemented < IOSBackupManagerError; end

  class BackupNotFoundError < IOSBackupManagerError; end

  class BackupRetrievalError < IOSBackupManagerError; end

  class BackupSpecificationError < IOSBackupManagerError; end

  class BackupRestoreError < IOSBackupManagerError; end

  attr_accessor :device_uuid

  def initialize(*args, **kwargs)
    @device_uuid = nil
  end

  def supported?
    false
  end

  def backup_device(*args, **kwargs)
    raise MethodNotImplemented, "#{self.class} doesn't implement #{__method__}"
  end

  def backup_present?(*args, **kwargs)
    raise MethodNotImplemented, "#{self.class} doesn't implement #{__method__}"
  end

  def restore_backup(*args, **kwargs)
    raise MethodNotImplemented, "#{self.class} doesn't implement #{__method__}"
  end

  def delete_backup(*args, **kwargs)
    raise MethodNotImplemented, "#{self.class} doesn't implement #{__method__}"
  end

  def clean(*args, **kwargs)
    raise MethodNotImplemented, "#{self.class} doesn't implement #{__method__}"
  end

  def log(level, message)
    message = "#{self.class} :: #{@device} - #{message}"

    if defined?(BrowserStack.logger.info)
      BrowserStack.logger.send(level.to_sym, message)
    else
      puts message
    end
  end
end
