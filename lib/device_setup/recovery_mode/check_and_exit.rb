require_relative '../../../config/constants'

module RecoveryMode
  module_function

  def check_and_exit(ecid)
    exit_recovery_mode(ecid) if in_recovery_mode?(ecid)
  end

  def in_recovery_mode?(ecid)
    BrowserStack::OSUtils.execute("#{IRECOVERY} -i #{ecid} -m").include?('Recovery Mode')
  end

  def exit_recovery_mode(ecid)
    BrowserStack::OSUtils.execute("#{IRECOVERY} -i #{ecid} -n")
  end
end

if __FILE__ == $PROGRAM_NAME
  ecid = ARGV[0]
  RecoveryMode.check_and_exit(ecid)
end
