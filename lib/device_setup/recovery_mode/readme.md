# Recovery Mode 
Takes device out of recovery mode if its offline due to that
Using `irecovery` command line tool for this

## Usage
To check mode of device (recovery, normal)

```irecovery -i <edid> -m```

To boot device to normal mode

```irecovery -i <edid> -n```

If ecid is not passed it will perform operation on the first device that is in recovery

For more details - https://github.com/libimobiledevice/libirecovery
