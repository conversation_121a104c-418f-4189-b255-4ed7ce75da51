require 'json'
require 'appium_lib'
require_relative './appium_server'
require_relative './utils/utils'
require_relative './utils/http_utils'
require_relative './utils/time_recorder'

class EnterpriseAppTrust # rubocop:todo Metrics/ClassLength
  include BrowserStack::TimeRecorder

  time_methods :verify_app_trust
  attr_reader :driver

  DEVICE_MANAGEMENT_EXISTING_ENTRIES = ["Enrollment Profile", "BinaryLife Inc", "Disable USB Restricted Mode", "Wifi-Only", "Wifi", "iOS 14 & iPadOS 14 Beta Software Profile Beta Software Profile", "iOS 14 Beta Software Profile", "Untitled", "mitmproxy"]
  DUMMY_APP_DIST_NAME = "BinaryLife Inc"

  def initialize(uuid, device_config, device_details, dist_name = '', bundle_id = '')
    @device_id = uuid
    @device_config = device_config
    @device_details = device_details
    @dist_name = dist_name
    @bundle_id = bundle_id
    @appium_port = @device_details[:appium_port]
  end

  def perform_action(action)
    case action
    when "trust"
      trust_enterprise
    when "untrust"
      untrust_and_delete_client_enterprise_app
    else
      BrowserStack.logger.info "EnterpriseAppTrust invalid perform_action #{action}"
    end
  end

  # this methods verifies whether the enterprise dummy app is trusted or not
  def verify_app_trust
    verify_trust
  end

  private

  def verify_trust
    setup_appium_driver
    found_enterprise_dist = go_to_device_management_enterprise_dist
    unless found_enterprise_dist
      @driver.driver_quit
      return false
    end
    result = trusted?
    @driver.driver_quit
    BrowserStack.logger.info "EnterpriseAppTrust verification  for #{@appium_port} done, result: #{result}!"
    result
  end

  def trust_enterprise
    setup_appium_driver
    found_enterprise_dist = go_to_device_management_enterprise_dist
    unless found_enterprise_dist # to avoid exception if client enterprise dist not found
      @driver.driver_quit
      return true
    end

    if trusted?
      BrowserStack.logger.info "EnterpriseAppTrust for #{@appium_port} done, was already trusted!"
      result = true
    else
      2.times do |click_retry_count|
        BrowserStack.logger.info "clicking on trust for: #{@appium_port}. Retry Count: #{click_retry_count}"
        click_trust
        sleep 3
        result = trusted?
        break if result
      end
    end

    @driver.driver_quit
    BrowserStack.logger.info "EnterpriseAppTrust for #{@appium_port} done, result: #{result}!"
    result
  end

  # deletes client enterprise app via ui automation which untrusts client distribution
  def untrust_and_delete_client_enterprise_app
    setup_appium_driver
    found_enterprise_dist = go_to_device_management_enterprise_dist
    return true unless found_enterprise_dist  # to avoid exception if client enterprise dist not found

    sleep 1
    delete_app_enterprise_dist
    sleep 3
    result = untrusted?

    @driver.driver_quit
    BrowserStack.logger.info "EnterpriseAppTrust delete client_enterprise_app #{@dist_name} for #{@appium_port} done, result: #{result}!"
    result
  end

  def is_client_enterprise_app?
    File.exists?(client_enterprise_app_file)
  end

  def client_enterprise_app_file
    "/tmp/client_enterprise_app_#{@device_id}"
  end

  def setup_appium_driver
    appium_server = BrowserStack::AppiumServer.new(@device_id)
    @driver = appium_server.driver
    @driver.reset
    BrowserStack.logger.info "driver started for: #{@appium_port}."
    @driver.manage.timeouts.implicit_wait = 5 # seconds
  end

  def navigate_to_general_settings
    if @find_el_by_accessibility_id
      generalField = @driver.find_elements("accessibility_id", "General")
      generalField = generalField[0]
    end
    generalField = @driver.find_ele_by_attr_include("XCUIElementTypeCell", "name", "General") if generalField.nil?
    if @device_details[:os_version].to_i == 12
      @driver.execute_script 'mobile: scroll', name: 'General'
    else
      BrowserStack.logger.info "Checking if the General field is accessible: #{generalField.accessible}."
      # Adding check for os version as 15 to minimize the impact
      if @device_details[:os_version].to_i == 15 && generalField && generalField.accessible.to_s == "false"
        BrowserStack.logger.info "Retrying to find the field using accessibility for general field."
        generalField = @driver.find_ele_by_attr_include("XCUIElementTypeStaticText", "name", "General")
        BrowserStack.logger.info "is the field accessible for General : #{generalField.accessible?}."
      end
      @driver.execute_script('mobile: scroll', { "element": generalField.ref, "toVisible": true })
    end
    generalField.click
    BrowserStack.logger.info "Inside general for: #{@appium_port}."
  end

  def scroll_to_element(driver, element)

    driver.execute_script 'mobile: scroll', name: element
    BrowserStack.logger.info "scroll to #{element} success for: #{@device_details.values.join(' ')}."
  rescue Exception => e
    BrowserStack.logger.info "scroll to #{element} failed for: #{@device_details.values.join(' ')}. Error - #{e.message}"

  end

  def navigate_to_device_management
    if @find_el_by_accessibility_id
      deviceMgmtField = @driver.find_elements("accessibility_id", "Device Management")
      deviceMgmtField = @driver.find_elements("accessibility_id", "Profiles & Device Management") if deviceMgmtField.empty?
      deviceMgmtField = deviceMgmtField[0]
    end
    deviceMgmtField = @driver.find_ele_by_attr_include("XCUIElementTypeCell", "name", "Device Management") if deviceMgmtField.nil?
    if @device_details[:os_version].to_i == 12
      # On some machines, Profiles & device management is present and only Device management on others
      scroll_to_element(@driver, 'Profiles & Device Management')
      scroll_to_element(@driver, 'Device Management')
    else
      BrowserStack.logger.info "Checking if the Device management field is accessible: #{deviceMgmtField.accessible}."
      # Adding check for os version as 15 to minimize the impact
      if @device_details[:os_version].to_i == 15 && deviceMgmtField && deviceMgmtField.accessible.to_s == "false"
        BrowserStack.logger.info "Retrying to find the field using ManagedConfigurationList."
        deviceMgmtField = @driver.find_ele_by_attr_include("XCUIElementTypeStaticText", "name", "ManagedConfigurationList")
        BrowserStack.logger.info "is the field accessible using ManagedConfigurationList : #{deviceMgmtField.accessible?}."
      end
      @driver.execute_script('mobile: scroll', { "element": deviceMgmtField.ref, "toVisible": true })
    end
    deviceMgmtField.click
    BrowserStack.logger.info "inside device management for: #{@appium_port}."
  end

  def navigate_to_enterprise_dist # rubocop:todo Metrics/AbcSize, Metrics/MethodLength

    enterpriseField = nil
    if @find_el_by_accessibility_id
      enterpriseField = @driver.find_elements("accessibility_id", @dist_name)
      enterpriseField = enterpriseField[0]
    end
    if enterpriseField.nil?
      enterpriseField = begin
        @driver.find_ele_by_predicate(class_name: "XCUIElementTypeCell", value: @dist_name)
      rescue
        nil
      end
      @dist_name = "" if enterpriseField.nil?
    end
    if is_client_enterprise_app? && @dist_name.empty?
      if @device_details[:device_type] == "iPad"
        device_management_table = @driver.find_elements("class", "XCUIElementTypeTable").reject { |table| table.location.x == 0 }[0]
        candidate_enterprise_fields = device_management_table.find_elements(class_name: "XCUIElementTypeCell")
      else
        candidate_enterprise_fields = @driver.find_elements(class_name: "XCUIElementTypeCell")
      end
      BrowserStack.logger.info "candidate_enterprise_fields #{candidate_enterprise_fields}"
      candidate_enterprise_fields.each do |candidate_enterprise_field|
        candidate_dist_name = candidate_enterprise_field.name
        BrowserStack.logger.info "processing #{candidate_dist_name}"
        next if DEVICE_MANAGEMENT_EXISTING_ENTRIES.include?(candidate_dist_name)

        BrowserStack.logger.info "setting dist_name to #{candidate_dist_name}"
        @dist_name = candidate_dist_name
        enterpriseField = candidate_enterprise_field
        break
      end
    else
      BrowserStack.logger.info "EnterpriseAppTrust client #{@dist_name} found."
    end

    if enterpriseField.nil?
      BrowserStack.logger.info "EnterpriseAppTrust client dist_name not found"
      false
    else
      enterpriseField.click
      BrowserStack.logger.info "inside #{@dist_name} for: #{@appium_port}."
      @dist_name
    end
  rescue => e
    BrowserStack.logger.info "Could not trust enterprise_app. Got Exception. Printing page source: #{@driver.get_source} Exception message: #{e.message} Backtrace: #{e.backtrace}"
    raise e

  end

  def go_to_device_management_enterprise_dist
    navigate_to_general_settings
    navigate_to_device_management
    sleep 1
    result = navigate_to_enterprise_dist
    BrowserStack.logger.info "navigate_to_enterprise_dist returned false. Printing page source: #{@driver.get_source}" unless result
    result
  end

  def click_trust
    trust_fields = @driver.find_eles_by_predicate_include(class_name: "*", value: "Trust")
    BrowserStack.logger.info "fields: #{trust_fields.inspect} for: #{@appium_port}."
    required_field = trust_fields.select { |trust_field| trust_field.name == "Trust “#{@dist_name}”" }
    if required_field.count == 1
      required_field[0].click
      @driver.execute_script('mobile: alert', { 'action': 'accept' })
    else
      # This code block would be hit when the trust popup(with two options "Trust" or "Cancel") appears, we would click on trust explicitly.
      trust_button = trust_fields.select { |trust_field| trust_field.name == "Trust" }
      if trust_button.count == 1
        BrowserStack.logger.info "Clicking on Trust Button manually for the trust popup on: #{@appium_port}"
        trust_button[0].click
      else
        BrowserStack.logger.info "Could not find required field to trust: #{@appium_port}."
      end
    end
  end

  def delete_app_enterprise_dist(retry_count = 1)

    num_cells = @driver.find_elements(:class_name, "XCUIElementTypeCell")
    delete_app_selector = num_cells.count == 2 ? "Delete App" : "Delete Apps"
    delete_app = @driver.find_element("accessibility_id", delete_app_selector)
    delete_app.click
    delete_app_confirm = @driver.find_elements(:xpath, "//XCUIElementTypeButton[@name='#{delete_app_selector}']")
    if delete_app_confirm.count == 1
      delete_app_confirm[0].click
      BrowserStack.logger.info "Delete App confirm dialog clicked for dist_name #{@dist_name} and appium_port: #{@appium_port}."
    elsif retry_count > 0
      retry_count -= 1
      BrowserStack.logger.info "Delete App click didn't work. Retrying: #{@appium_port}."
      delete_app_enterprise_dist(retry_count)
    else
      BrowserStack.logger.info "Could not find required delete dialog for enterprise dist_name: #{@dist_name}: #{@appium_port}."
    end
  rescue Exception => e
    BrowserStack.logger.info "EnterpriseApp trust got exception while deleting Enterprise dist. Uninstalling it."
    IdeviceUtils.uninstall_app(@device_id, @bundle_id)

  end

  def verified_over_internet?
    device_type = @device_details[:device_type]
    not_verified_sentence = "An app from developer “iPhone Distribution: #{@dist_name}” is not verified on this #{device_type} and will not run until it is verified using your network connection."
    not_verified_check_fields = driver.find_eles_by_predicate_include(class_name: "*", value: "not verified")

    BrowserStack.logger.info "[verified_over_internet?] non verified trust fields: #{not_verified_check_fields.inspect}, count: #{not_verified_check_fields.count} for: #{@appium_port}."

    not_verified_check_fields.each do |not_verified_check_field|
      BrowserStack.logger.info "[verified_over_internet?] Not Verified value: #{not_verified_check_field.value} for: #{@appium_port}."
      if not_verified_check_field.value == not_verified_sentence
        BrowserStack.logger.info "[verified_over_internet?] The app is not verified for: #{@appium_port}. Page Source: #{driver.get_source}"
        return false
      end
    end
    true
  end

  def trusted?
    @appium_port = @device_details[:appium_port]
    device_type = @device_details[:device_type]
    return false unless verified_over_internet?

    trusted_sentence = "Apps from developer “iPhone Distribution: #{@dist_name}” are trusted on this #{device_type} and will be trusted until all apps from the developer are deleted."
    trust_check_fields = @driver.find_eles_by_predicate_include(class_name: "*", value: "are trusted")
    result = false

    BrowserStack.logger.info "trust check fields: #{trust_check_fields.inspect} for: #{@appium_port}."

    BrowserStack.logger.info "#{trust_check_fields.count} elements found for trust for: #{@appium_port}."
    trust_check_fields.each do |trust_check_field|
      BrowserStack.logger.info "trust value: #{trust_check_field.value} for: #{@appium_port}."
      next unless trust_check_field.value == trusted_sentence

      result = true
      BrowserStack.logger.info "Found trusted sentence."
      break
    end

    BrowserStack.logger.info "The app is still not trusted for: #{@appium_port}. Page Source: #{@driver.get_source}" unless result
    result
  end

  def untrusted?
    # if delete dialog didn't get automated properly (dist_name screen)
    delete_check_sentence = @driver.find_eles_by_predicate_include(class_name: "*", value: "are deleted")
    # if even after delete app automation, entry for dist_name exists in device management (device management screen)
    enterprise_field = @driver.find_eles_by_predicate_include(class_name: "XCUIElementTypeCell", value: @dist_name)

    delete_check_sentence.count != 0 || enterprise_field.count != 0 ? false : true
  end
end
