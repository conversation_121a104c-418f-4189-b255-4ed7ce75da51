require_relative 'utils/utils'
require_relative 'utils/osutils'
require_relative 'custom_exceptions'
require_relative 'utils/zombie'
require_relative 'utils/idevice_utils'
require_relative 'utils/time_recorder'

class NetworkSimulator # rubocop:todo Metrics/ClassLength
  include BrowserStack::TimeRecorder

  time_methods :reset_throttling_rules

  def initialize(device, port)
    @device = device
    @port = port
  end

  def upload_pipe
    (2 * (@port % 10) + 1)
  end

  def download_pipe
    (2 * (@port % 10) + 2)
  end

  def device_pipes
    [upload_pipe, download_pipe]
  end

  def get_dnctl_pipes
    BrowserStack::OSUtils.execute("sudo dnctl pipe list | awk '$1 ~ /^[0-9]{5}:$/' | awk '{print $1}' | cut -d\":\" -f1").split.map(&:to_i)
  end

  def get_current_rules
    BrowserStack::OSUtils.execute("sudo pfctl -q -s dummynet -a customNetworkRule").split("\n")
  end

  def verify_dnctl_pipes
    current_pipes = get_dnctl_pipes
    BrowserStack.logger.info "Dnctl pipes on machine - #{current_pipes.join(',')}"
    (device_pipes - current_pipes).empty?
  end

  def verify_dummynet_rules
    rule_count = BrowserStack::OSUtils.execute("sudo pfctl -q -s dummynet -a customNetworkRule | grep #{@port} | wc -l").chomp.to_i
    (rule_count == 2)
  end

  def verify_firewall_rules
    (verify_dummynet_rules && verify_dnctl_pipes)
  end

  def setup_dummynet_rules
    BrowserStack.logger.info "Adding pipes #{device_pipes.join(',')} for #{@device} with port #{@port}"
    rules = get_current_rules
    rules << "dummynet in quick proto tcp from any to any port = #{@port} pipe #{upload_pipe}"
    rules << "dummynet out quick proto tcp from any port = #{@port} to any pipe #{download_pipe}"
    dummynet_cmd = "cat <<EOF | sudo pfctl -q -a customNetworkRule -f -
#{rules.join("\n")}
EOF"
    BrowserStack::OSUtils.execute(dummynet_cmd) unless verify_dummynet_rules
    sleep 2
    reset_throttling_rules
    BrowserStack.logger.info "Setup dummynet rules for #{@device} with port #{@port} completed"
  end

  def setup_download_rules(params)
    dwld_cmd = "sudo dnctl pipe #{download_pipe} config "
    dwld_cmd += "bw #{params[:network_bw_dwld]}Kbit/s " unless params[:network_bw_dwld].nil?
    dwld_cmd += "delay #{params[:network_latency]}ms " unless params[:network_latency].nil?
    dwld_cmd += "plr #{params[:network_pk_loss]} " unless params[:network_pk_loss].nil?
    dwld_cmd += "mask proto tcp"
    BrowserStack::OSUtils.execute(dwld_cmd)
    BrowserStack.logger.info "Setup download rules for #{@device} with port #{@port} completed"
  end

  def setup_upload_rules(params)
    upld_cmd = "sudo dnctl pipe #{upload_pipe} config "
    upld_cmd += "bw #{params[:network_bw_upld]}Kbit/s " unless params[:network_bw_upld].nil?
    upld_cmd += "mask proto tcp"
    BrowserStack::OSUtils.execute(upld_cmd)
    BrowserStack.logger.info "Setup upload rules for #{@device} with port #{@port} completed"
  end

  def is_upload_setup?
    BrowserStack::OSUtils.execute("sudo dnctl pipe #{upload_pipe} show | grep \"#{upload_pipe}: unlimited\" | wc -l").chomp.to_i == 0
  end

  def is_download_setup?
    BrowserStack::OSUtils.execute("sudo dnctl pipe #{download_pipe} show | grep \"#{download_pipe}: unlimited\" | wc -l").chomp.to_i == 0
  end

  def throttling_setup?(params)
    done = (is_download_setup? && is_upload_setup?)
    BrowserStack::Zombie.push_logs("network-simulation-setup-failed", "app-automate", { "session_id" => params['automate_session_id'], "device" => @device }) unless done
    done
  end

  def setup_throttling_rules(params)
    setup_download_rules(params)
    setup_upload_rules(params)
    throttling_setup?(params)
  end

  def reset_throttling_rules
    device_pipes.each { |p| BrowserStack::OSUtils.execute("sudo dnctl pipe #{p} config mask proto tcp") }
  end

  def working_interfaces_list
    @working_interfaces_list ||= Utils.get_working_interfaces(@device)
  end

  def fetch_working_interfaces_for_device
    working_interfaces = working_interfaces_list

    if working_interfaces.empty?
      BrowserStack.logger.error("Device internet_sharing: can't find network interfaces in ifconfig for device #{@device}")
      raise "no working network interface found for the device #{@device}"
    end
    working_interfaces
  end

  # return nil if bridge interfaces are empty else return list of interfaces
  # which are member of bridge interface for the given device
  def list_of_bridge_interfaces_for_device
    working_interfaces = fetch_working_interfaces_for_device
    bridged_interfaces = working_interfaces.select do |interface|
      IdeviceUtils.is_network_interface_in_bridge?(interface)
    end
    BrowserStack.logger.warn("Device internet_sharing: network interfaces not in bridge100 #{working_interfaces.join(',')}") if bridged_interfaces.empty?
    bridged_interfaces
  end

  # TODO: Deprecated code Block, we no more, delete interface from bridge to acheive no-network mode
  def enable_no_network_mode
    bridge_interfaces = list_of_bridge_interfaces_for_device
    unless bridge_interfaces.empty?
      bridge_interfaces.each do |interface|
        IdeviceUtils.delete_network_interface_from_bridge(interface)
      end
    end
  end

  # TODO: Deprecated code Block, we no more, delete interface from bridge to acheive no-network mode
  def disable_no_network_mode
    working_interfaces = fetch_working_interfaces_for_device
    working_interfaces.each do |interface|
      if !IdeviceUtils.is_network_interface_in_bridge?(interface) && IdeviceUtils.is_network_interface_in_nat_plist?(interface)
        IdeviceUtils.add_network_interface_to_bridge(interface)
        BrowserStack.logger.info("Device internet_sharing: network interface #{interface} not in bridge100 .. adding")
      end
    end
  end

  # Depending on exit_code of the command to verify if interface was taken down/up successfully:
  # We found that frequently, when we immediately check IdeviceUtils.is_interface_active?, the
  # updated status is not reflected in `ifconfig en2` command.
  # Sometimes, we get multiple working interfaces, and on trying to update state for, say en2 we get interface not
  # found, we want to ignore this(Note: it does not come in output) and return true if any of the interface gets up/down
  def apply_online_mode
    BrowserStack.logger.info("Applying Online Mode in Network Simulator")
    working_interfaces = fetch_working_interfaces_for_device
    BrowserStack.logger.info("Turning Up Network Interfaces #{working_interfaces}")
    success = false
    working_interfaces.each do |interface|
      result, status = IdeviceUtils.update_interface_state(interface, "up")
      success ||= (status == 0)
    end
    raise "Interface could not be turned up" unless success
  end

  def apply_offline_mode
    BrowserStack.logger.info("Applying Offline Mode in Network Simulator")
    working_interfaces = fetch_working_interfaces_for_device
    BrowserStack.logger.info("Turning Down Network Interfaces #{working_interfaces}")
    success = false
    working_interfaces.each do |interface|
      result, status = IdeviceUtils.update_interface_state(interface, "down")
      success ||= (status == 0)
    end
    raise "Interface could not be turned down" unless success
  end

  def log_device_interface_status
    working_interfaces = fetch_working_interfaces_for_device
    status = {}
    working_interfaces.each do |interface|
      status[interface] = IdeviceUtils.is_interface_active?(interface)
    end
    BrowserStack.logger.info("Interfaces status: #{status}")
    status
  end

  def apply_network_mode(network_mode)
    apply_online_mode if network_mode == "online"
    # Adding two retries to check if it impacts error rate for no_network simulation and logged results
    if network_mode == "offline"
      3.times do
        apply_offline_mode
        status = log_device_interface_status
        break unless status.any? { |_key, value| value }

        sleep 0.5
      end
    end
  end

  def reset_network_simulation
    # To Ensure we update interface with some lag, keeping reset_throttling_rules after apply_network_mode
    apply_network_mode("online")
    reset_throttling_rules
  end
end
