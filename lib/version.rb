module BrowserStack
  # This is like Gem::Version but is used to get a canonical string representation
  # It's pretty much a copy paste of the same class in the android repo
  class Version
    attr_accessor :gem_version

    def initialize(semver)
      @gem_version = Gem::Version.new(semver)
      raise(ArgumentError, "Invalid version #{semver}") unless semver.match(/^[\d.]+$/)
    end

    def canonical_without_patch
      major = gem_version.segments[0]
      minor = gem_version.segments[1] || 0
      "#{major}.#{minor}"
    end
  end
end
