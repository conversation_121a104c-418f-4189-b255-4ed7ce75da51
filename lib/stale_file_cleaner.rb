# Description: This is a process which loops a directory and removes the stale file from it based on glob pattern passed.
require_relative './utils/utils'
require_relative './configuration'
require 'browserstack_logger'

module BrowserStack
  class StaleFileCleaner
    def initialize(name, directory, pattern, threshold)
      conf = Configuration.new
      @name = name
      @directory = directory
      @pattern = pattern
      @threshold = threshold
    end

    def process_files
      BrowserStack.logger.info("StaleFileCleaner: Starting stale file cleaner #{@name} for #{@directory}/#{@pattern} with threshold #{@threshold}")
      Dir["#{@directory}/#{@pattern}"].each do |file|
        next unless File.exists?(file) # skip files in case of race condition
        next unless Time.now - File.mtime(file) > @threshold

        BrowserStack.logger.info("StaleFileCleaner: Starting to delete stale file: #{file} ( #{Time.now - File.mtime(file)} seconds old )")
        begin
          yield file # add custom handling for file about to be deleted
        rescue => e
          BrowserStack.logger.info("Exception occured while processing #{file} ( #{Time.now - File.mtime(file)} seconds old )")
        end
        File.delete file
        BrowserStack.logger.info("StaleFileCleaner: Deleted: #{file}")
      end
    end
  end
end
