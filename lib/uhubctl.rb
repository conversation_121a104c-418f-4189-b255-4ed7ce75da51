require 'fileutils'
require_relative './utils/utils'
require_relative './utils/osutils'
require_relative '../config/constants'

require 'browserstack_logger'

class Uhubctl
  def self.get_hubs(filter)
    cmd = "sudo su -l #{USER} -c \"#{UHUBCTL} | grep Current | grep #{filter}\""
    BrowserStack.logger.info "Getting all hubs with filter #{filter}"
    result = BrowserStack::OSUtils.execute(cmd).split("\n")
    hubs = result.map { |h| h.split(' ')[4] }
    BrowserStack.logger.info "Hu<PERSON> found #{hubs}"
    hubs
  end

  def self.poweroff_hub(hub_id)
    cmd = "sudo su -l #{USER} -c \"#{UHUBCTL} -e -a 0 -l #{hub_id}\""
    BrowserStack.logger.info "Power off hub #{hub_id}"
    out = BrowserStack::OSUtils.execute cmd
  end

  def self.poweron_hub(hub_id)
    cmd = "sudo su -l #{USER} -c \"#{UHUBCTL} -a 1 -l #{hub_id}\""
    BrowserStack.logger.info "Power on hub #{hub_id}"
    out = BrowserStack::OSUtils.execute cmd
  end
end
