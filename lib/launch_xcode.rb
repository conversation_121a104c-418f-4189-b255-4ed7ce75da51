# frozen_string_literal: true

# Utility to ensure Xcode.app is running in the machine.
#
# Usage: just run `XcodeLauncher.launch`
#
# Features:
#
#     - Ensures xcode is running after launching it.
#     - noop if xcode already running
#     - noop if the ensuring failed before, via a lockfile in /tmp
#
# It will raise an exception if the lockfile is present or if the ensuring failed.

module LaunchXcode
  module_function

  LOCK_FILE = '/tmp/previous_launch_error'

  def launch
    return if xcode_running?
    raise 'Xcode failed to be launched previously. Refusing to retry.' if previous_launch_error?

    launch_new_xcode
    unless xcode_running?
      create_launch_error_lock_file
      raise 'Failed to launch Xcode'
    end
  end

  # private functions

  def create_launch_error_lock_file
    File.write(LOCK_FILE, Time.now.to_i)
  end
  private_class_method :create_launch_error_lock_file

  def previous_launch_error?
    File.file? LOCK_FILE
  end
  private_class_method :previous_launch_error?

  def xcode_running?
    ps_aux.include? '/Applications/Xcode.app/Contents/MacOS/Xcode'
  end
  private_class_method :xcode_running?

  def launch_new_xcode
    system('open -a Xcode')
    sleep 5 # Sometimes it needs a bit of time to open.
  end
  private_class_method :launch_new_xcode

  def ps_aux
    `ps aux`
  end
  private_class_method :ps_aux
end
