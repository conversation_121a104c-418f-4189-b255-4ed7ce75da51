require_relative 'utils/utils'
require_relative 'utils/osutils'
require_relative 'utils/time_recorder'
require_relative 'app_percy/cli_manager'
require_relative '../lib/helpers/local_testing_chrome_extension_helper'
require_relative '../lib/helpers/socat_helper'
require 'base64'
require 'yaml'
require 'date'
require 'json'

class PrivoxyManager # rubocop:todo Metrics/ClassLength
  include BrowserStack::TimeRecorder

  TEMPLATE_FILE_NAMES = %w[blocked connect-failed forwarding-failed no-server-data no-such-domain]

  time_class_methods :reset_proxy,
                     :truncate_privoxy_logs

  class << self
    def configure(settings)
      @@settings = settings
      @@templates_dir = settings['templates_dir']

      @@pacfile_dir = settings['pacfile_dir']
      # Added ws.betboom.ru as part of ticket: https://browserstack.atlassian.net/browse/AAP-8407
      #  This is a temporary measure and addition of user-specific domains to this block is discouraged.

      # Added ssl.oobee-gc.com for adding it to whitelisted_hosts would ensure geoguard SSL URL is passed thru device proxy
      # JIRA Story: MOBPE-1298
      @@whitelisted_hosts = ["smtp.gmail.com", "ws.betboom.ru", "ru-ws.sporthub.bet", "ssl.oobee-gc.com", "ppq.apple.com"]
      @@whitelisted_hosts.concat(PRIVOXY_WHITELISTED_APP_HOSTS)

      @@whitelisted_urls = ["imap:*", "smtp:*", "imaps:*"]

      @@socket_timeout = settings['privoxy_socket_timeout']
      @@max_client_connections = settings['privoxy_max_client_connections']
      @@keep_alive_timeout = settings['privoxy_keep_alive_timeout']
      @@server_timeout = settings['privoxy_server_timeout']
      @@listen_port_offset = settings['privoxy_listen_port_offset']
      @@conf_dir = settings['privoxy_conf_dir']
      @@log_dir = settings['privoxy_logs_dir']
      @@privoxy_templates_dir = settings['privoxy_templates_dir']
      @@set_custom_max_client_connections = false
      @@custom_max_client_connections = settings['privoxy_max_client_connections']
      @@set_custom_privoxy_keep_alive_timeout = false
      @@custom_privoxy_keep_alive_timeout = settings['privoxy_keep_alive_timeout']
      @@insecure_websocket_proxy_port = settings['insecure_websocket_proxy_port']

      [@@conf_dir, @@log_dir].each do |present_dir|
        FileUtils.mkdir_p(present_dir) unless Dir.exist?(present_dir)
      end
    end

    def setup_privoxy(device, current_device_config, params, custom_forwarding_options = {})
      ensure_pac_file(device, current_device_config)
      write_templates(device, params)

      privoxy_options = get_privoxy_options(params, device)
      write_privoxy_conf(device, current_device_config, privoxy_options, custom_forwarding_options)

      { status: true }
    end

    # Tech Spec: https://browserstack.atlassian.net/wiki/x/Hgg17Q
    def privoxy_domain_block_whitelist(device, group_id, group_plan_type, flag_enabled) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
      BrowserStack.logger.info "[privoxy_domain_block_whitelist] device: #{device}, group_id: #{group_id}, group_plan_type: #{group_plan_type}, flag_enabled: #{flag_enabled}"
      blocked_domains_file = "/usr/local/.browserstack/privoxy/blocked_domains_paction-#{device}.conf"
      whitelisted_domains_file = "/usr/local/.browserstack/privoxy/whitelisted_domains_paction-#{device}.conf"

      if flag_enabled.to_s == "true"
        create_blank_file(GROUP_DOMAIN_BLOCKING_FLAG)
      else
        create_blank_file(blocked_domains_file)
        create_blank_file(whitelisted_domains_file)
        File.delete(GROUP_DOMAIN_BLOCKING_FLAG) if File.exist?(GROUP_DOMAIN_BLOCKING_FLAG)
        return
      end
      blocked_domains = ["{ +block }\n"]
      whitelisted_domains = ["{ -block }\n"]
      parsed_config_json = begin
        File.exist?(DOMAINS_CONFIG_JSON_FILE) ? JSON.parse(File.read(DOMAINS_CONFIG_JSON_FILE)) : {}
      rescue
        {}
      end
      blocked_domains += File.readlines(BLOCK_DOMAINS_FILE).map(&:chomp) if File.exist?(BLOCK_DOMAINS_FILE)
      whitelisted_domains += File.readlines(WHITELIST_DOMAINS_FILE).map(&:chomp) if File.exist?(WHITELIST_DOMAINS_FILE)
      [group_id, group_plan_type, "all"].each do |val|
        next unless parsed_config_json.key?(val.to_s)

        inner_json = parsed_config_json[val.to_s]
        blocked_domains += inner_json["Blocked"] if inner_json.key?("Blocked")
        whitelisted_domains += inner_json["Whitelisted"] if inner_json.key?("Whitelisted")
      end
      File.write(blocked_domains_file, blocked_domains.join("\n"))
      File.write(whitelisted_domains_file, whitelisted_domains.join("\n"))
      BrowserStack.logger.info "[privoxy_domain_block_whitelist] written domains: blocked: #{blocked_domains.count}, whitelisted: #{whitelisted_domains.count}"
    rescue => e
      BrowserStack.logger.info "[privoxy_domain_block_whitelist] Parse domain. Error occured -> #{e.message} || #{e.backtrace}"
      create_blank_file(blocked_domains_file)
      create_blank_file(whitelisted_domains_file)
      data = {
        device: device,
        group_id: group_id,
        group_plan_type: group_plan_type,
        message: e.message,
        backtrace: e.backtrace.first(3)
      }
      BrowserStack::Zombie.push_logs('privoxy_store_domains_config', 'privoxy_domains_populate_error', data.to_json )
    end

    def create_blank_file(filename)
      File.open(filename, 'w') { |file| file.truncate(0) }
    end

    def reset_proxy(device, current_device_config, params={})
      paction_file_path = paction_conf_path(current_device_config)
      ensure_pac_file(device, current_device_config)
      write_templates(device, params)

      FileUtils.rm(paction_file_path) if File.exists?(paction_file_path)
      privoxy_options = get_privoxy_options(params, device)
      write_privoxy_conf(device, current_device_config, privoxy_options)
      restart_proxy(current_device_config)
    end

    def start_proxy_checker(device, current_device_config, session_id, params)
      genre = params["genre"]
      user_id = params["user_id"]
      is_mitm_proxy_used = [params["networkLogs"], params["acceptInsecureCerts"], params["live_self_signed_certs"], params["localTestingChromeExtensionEnabled"]].any? { |item| item.to_s == 'true' }
      privoxy_port = is_mitm_proxy_used ? modified_listen_port(current_device_config) : listen_port(current_device_config)
      cmd = "/Users/<USER>/.rvm/rubies/ruby-2.7.2/bin/ruby /usr/local/.browserstack/realmobile/scripts/proxy_check.rb \"start\" \"#{device}\" \"#{genre}\" \"#{session_id}\" \"#{privoxy_port}\" \"#{privoxy_conf_path(current_device_config)}\" \"#{user_id}\" \"#{params[:tunnelHostServer]}\""
      Utils.fork_process(cmd, { process_name: "Proc: proxy_check #{device} #{session_id}" })
    end

    def truncate_privoxy_logs(device)
      privoxy_log_file = "/var/log/browserstack/privoxy_#{device}.log"
      return unless File.file?(privoxy_log_file)

      archive_log_file = File.open("#{privoxy_log_file}.archive", 'a')
      IO.foreach(privoxy_log_file) do |privoxy_log|
        archive_log_file << privoxy_log
      end
      archive_log_file.close
      File.truncate(privoxy_log_file, 0)
    end

    def restart_proxy(current_device_config)
      current_port = listen_port(current_device_config)

      BrowserStack::OSUtils.unload_plist_as(File.join(@@settings['plist_dir_user'], "privoxy_#{current_port}.plist"))
      BrowserStack::OSUtils.load_plist_as(File.join(@@settings['plist_dir_user'], "privoxy_#{current_port}.plist"))
    end

    def compare_ports(current_port, new_port)
      current_port_process = BrowserStack::OSUtils.list_port_process(current_port)
      new_port_process = BrowserStack::OSUtils.list_port_process(new_port)

      BrowserStack.logger.info("current port process: #{current_port_process}")
      BrowserStack.logger.info("new port process: #{new_port_process}")
    end

    # Start privoxy on a different port, so that pacfile can forward to
    # mitmproxy in case of networkLogs. And mitmproxy will forward to privoxy running on the new port.
    #
    # Returns the current port, and the new port.
    def change_port(device, current_device_config, restart=true)
      priv_conf_path = privoxy_conf_path(current_device_config)
      current_port = listen_port(current_device_config)
      new_port = modified_listen_port(current_device_config)
      return [current_port, new_port] unless restart

      # Logline to print process running on a port before changing ports
      compare_ports(current_port, new_port)
      # Performing conf changes.
      BrowserStack::OSUtils.execute("sed -i.bak 's/listen-address :#{current_port}/listen-address :#{new_port}/' #{priv_conf_path}")

      # Restart Proxy
      restart_proxy(current_device_config)

      # Wait for Privoxy to start on the new port.
      tries = 0
      while !privoxy_running?(new_port) && tries < 10
        compare_ports(current_port, new_port)
        sleep 0.5
        tries += 1
      end

      raise "Failed to launch privoxy on port #{new_port}" unless privoxy_running?(new_port)

      [current_port, new_port]
    end

    def ensure_pac_file(device, current_device_config)
      BrowserStack.logger.info "Ensuring PacFile for device: #{device} routes to privoxy"

      # iOS17 needs insecure websocket proxy, so we use different PAC file for these devices
      template_pacfile = current_device_config['device_version'].to_i >= 17 ? File.join(@@templates_dir, 'generic_pacfile_ios17.erb') : File.join(@@templates_dir, 'generic_pacfile.erb')

      use_proxy_instead_of_socks = true
      host_only = false
      hosts = ""
      proxy_host = current_device_config["ip"]
      proxy_port = listen_port(current_device_config)
      insecure_ws_proxy_port = @@insecure_websocket_proxy_port
      pacfile_data = ERB.new(File.open(template_pacfile).read).result(binding)

      if File.exists? pacfile_path(device)
        old_content = File.read pacfile_path(device)
        if old_content == pacfile_data
          BrowserStack.logger.info "PacFile for device #{device} already up to date"
          return
        end
      end

      File.open(pacfile_path(device), "w") { |f| f.write(pacfile_data) }
    end

    def listen_port(current_device_config)
      current_device_config['selenium_port'].to_i + @@listen_port_offset.to_i
    end

    def percy_cli_port(current_device_config)
      AppPercy::CLIManager.cli_port(current_device_config['selenium_port']).to_i
    end

    def write_privoxy_log_head(device, current_device_config, session_id)
      privoxy_log_file = "/var/log/browserstack/privoxy_#{device}.log"
      File.open(privoxy_log_file, "a") { |f| f.write("\n----\n#{DateTime.now} Starting privoxy for #{device} on #{listen_port(current_device_config)} for session_id: #{session_id}\n----\n") }
    end

    # this is health check for Percy session
    # this function will check if forwarding rule to browserup is set in privoxy config
    def percy_config_set?(current_device_config, percy_forwarding)
      status = File.readlines(privoxy_conf_path(current_device_config)).grep(%r{\s*forward\s+/\s+:#{percy_forwarding}\s*}).any?
      BrowserStack.logger.info('Forwarding rule to browserup is not set in privoxy config file') unless status
      status
    end

    private

    def privoxy_running?(privoxy_port)
      resp = `curl -x localhost:#{privoxy_port} "config.privoxy.org"`

      if !resp.nil? && resp.chomp == (@@settings["privoxy_templates_content"])
        BrowserStack.logger.info "Privoxy is listening properly on port #{privoxy_port}."
        true
      else
        BrowserStack.logger.info "Privoxy is NOT listening properly on port #{privoxy_port}."
        false
      end
    end

    def write_paction_conf(paction_file_path, paction_options)
      geo_auth = begin
        Base64.encode64(paction_options[:geo_auth]).gsub(/\n/, '').chomp
      rescue
        nil
      end
      proxy_host_ip = paction_options[:proxy_host_ip]
      sensor_mocking = paction_options[:sensor_mocking]
      use_replay_kit = paction_options[:use_replay_kit] || false
      device = paction_options[:device]
      cors_header_override = paction_options[:cors_header_override]

      template_paction_conf = File.join(@@templates_dir, 'generic_paction.erb')
      paction_data = ERB.new(File.open(template_paction_conf).read, trim_mode: '-').result(binding)
      File.open(paction_file_path, "w") { |f| f.write(paction_data) }
    end

    def write_privoxy_conf(device, current_device_config, privoxy_options, custom_forwarding_options = {}) # rubocop:todo Metrics/AbcSize, Metrics/PerceivedComplexity
      log_file = "privoxy_#{device}.log"
      blocked_domains_file = "/usr/local/.browserstack/privoxy/blocked_domains_paction-#{device}.conf"
      whitelisted_domains_file = "/usr/local/.browserstack/privoxy/whitelisted_domains_paction-#{device}.conf"
      device_templates_dir = "/usr/local/.browserstack/privoxy/templates-#{device}"
      create_blank_file(blocked_domains_file) unless File.exist?(blocked_domains_file)
      create_blank_file(whitelisted_domains_file) unless File.exist?(whitelisted_domains_file)
      geo_host_port = privoxy_options[:geo_host_port]
      use_paction = false
      repeater_host_port = ""
      proxy_exceptions = @@settings['proxy_whitelisted_hosts']
      sensor_mocking = privoxy_options[:sensor_mocking] || false
      use_replay_kit = privoxy_options[:use_replay_kit] || false
      is_settings_enabled = privoxy_options[:is_settings_enabled] || false
      cors_header_override = privoxy_options[:cors_header_override] || false
      auth_enabled = privoxy_options[:auth_enabled] || false
      auth_username = privoxy_options[:auth_username]
      auth_password = privoxy_options[:auth_password]
      voiceover_bluetooth_server_ip = BrowserStack::VoiceoverHelper.voiceover_device?(device) ? BrowserStack::VoiceoverHelper.new.bluetooth_server_ip : "localhost"

      proxy_host_ip = current_device_config['ip']
      use_paction = (!proxy_host_ip.nil? && !proxy_host_ip.empty?) || geo_host_port || sensor_mocking || is_settings_enabled || use_replay_kit

      percy_forwarding = privoxy_options[:percy_forwarding] || false
      proxy_exceptions += privoxy_options[:custom_privoxy_exceptions] if !privoxy_options[:custom_privoxy_exceptions].nil? && privoxy_options[:custom_privoxy_exceptions].is_a?(Array)
      unless custom_forwarding_options.empty?
        proxy_exceptions -= custom_forwarding_options[:domains]
        BrowserStack.logger.info "[Socks5Forwarder] Forwarding traffic for device: #{device}; Modifying privoxy configuration file"
        repeater_host = custom_forwarding_options[:host]
        repeater_port = custom_forwarding_options[:port]
      end

      if geo_host_port
        BrowserStack.logger.info "Using geoLocation for device: #{device}; Creating paction file"
      else
        repeater_host_port = privoxy_options[:repeater_host_port] || ""
        hosts = privoxy_options[:hosts]
      end

      # paction file path is needed in privoxy conf template as well
      paction_file_path = paction_conf_path(current_device_config)
      paction_options = { geo_auth: privoxy_options[:geo_auth], proxy_host_ip: proxy_host_ip, sensor_mocking: sensor_mocking, device: device, is_settings_enabled: is_settings_enabled, use_replay_kit: use_replay_kit, cors_header_override: cors_header_override }
      write_paction_conf(paction_file_path, paction_options) if use_paction

      template_privoxy_conf = File.join(@@templates_dir, 'generic_privoxy_conf.erb')
      privoxy_config_data = ERB.new(File.open(template_privoxy_conf).read).result(binding)
      File.open(privoxy_conf_path(current_device_config), "w") { |f| f.write(privoxy_config_data) }
    end

    # Place the correct templates inside the device's template dir
    # Different templates should be shown depending on the session type, e.g. show templates for local errors if local session
    def write_templates(device, params)
      device_template_dir = "/usr/local/.browserstack/privoxy/templates-#{device}"
      system("rm -rf #{device_template_dir} && mkdir #{device_template_dir}")

      # Default template is the same for all sessions
      # Contents of this template are constant: used by device check to check if privoxy is working
      system("cp /usr/local/.browserstack/privoxy/templates/default #{device_template_dir}/default")

      local_session = !params[:hosts].nil? && !params[:hosts].empty?

      TEMPLATE_FILE_NAMES.each do |template|
        template_path_destination = "#{device_template_dir}/#{template}"
        if !local_session
          html = File.read "/usr/local/.browserstack/privoxy/templates/forwarding-failed_non_local"
          html = html.gsub("{}", params.to_json)
          Utils.write_to_file(template_path_destination, html, params)
        else
          system("cp /usr/local/.browserstack/privoxy/templates/#{template} #{template_path_destination}")
        end
      end
    end

    def privoxy_conf_path(current_device_config)
      File.join(@@conf_dir, "privoxy_conf_#{listen_port(current_device_config)}")
    end

    def paction_conf_path(current_device_config)
      File.join(@@conf_dir, "paction_conf_#{listen_port(current_device_config)}.conf")
    end

    def pacfile_path(device)
      File.join(@@pacfile_dir, "pacfile_#{device}")
    end

    # Privoxy will run on the port <listen_port> + 100
    # This is done because mitmproxy will listen on <listen_port> and forward traffic to Privoxy.
    # Done only in the case when networkLogs are enabled.
    def modified_listen_port(current_device_config)
      listen_port(current_device_config) + 100
    end

    def get_auth_options(params)
      auth_options = {}

      auth_options[:auth_enabled] = params[:auth] || false

      if auth_options[:auth_enabled]
        session_id = params[:live_session_id] ||
                      params[:app_live_session_id] ||
                      params[:automate_session_id] ||
                      params[:app_automate_session_id]

        auth_options[:auth_username] = session_id[0..19]
        auth_options[:auth_password] = session_id[-20..]
      end

      auth_options
    end

    def get_privoxy_options(params, device) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength, Metrics/PerceivedComplexity, Metrics/CyclomaticComplexity
      privoxy_options = {}
      privoxy_options[:use_replay_kit] = true if params[:useReplayKit].to_s == "true" && ["live_testing", "app_live_testing", "automate", "js_testing", "app_automate"].include?(params[:genre])
      privoxy_options[:sensor_mocking] = true if params[:sensor_mocking].to_s == "true" && Utils.is_app_product_genre?(params[:genre])
      privoxy_options[:is_settings_enabled] = true if params["enable_settings_app_access"].to_s == "true" && params[:genre] == 'app_live_testing'
      privoxy_options[:local_testing_chrome_extension_enabled] = true if params["localTestingChromeExtensionEnabled"].to_s == "true" && ["live_testing"].include?(params[:genre])
      privoxy_options[:percy_forwarding] = params[:percy_forwarding]
      privoxy_options[:cors_header_override] = true if params[:cors_header_override].to_s == "true" && ["live_testing", "app_live_testing"].include?(params[:genre])
      privoxy_options[:set_custom_max_client_connections] = true if params["set_custom_max_client_connections"].to_s == "true" && (params[:genre] == 'app_live_testing' || params[:genre] == 'automate')
      if privoxy_options[:set_custom_max_client_connections]
        privoxy_options[:custom_max_client_connections] = params["custom_max_client_connections"]
        @@set_custom_max_client_connections = true
        @@custom_max_client_connections = privoxy_options[:custom_max_client_connections]
      end

      unless params[:custom_privoxy_exceptions].nil?
        # params[:custom_privoxy_exceptions] comes as a string and needs to be JSON parsed
        privoxy_options[:custom_privoxy_exceptions] = begin
          JSON.parse(params[:custom_privoxy_exceptions])
        rescue
          []
        end
      end

      unless params["privoxyKeepAliveTimeout"].to_s.empty?
        @@set_custom_privoxy_keep_alive_timeout = true
        @@custom_privoxy_keep_alive_timeout = params["privoxyKeepAliveTimeout"].to_i
      end

      privoxy_options = privoxy_options.merge(get_auth_options(params))

      return privoxy_options if params.empty? || params[:tunnelPresent] == "false"

      if params[:geoLocation]
        privoxy_options[:geo_host_port] = "#{params[:tunnelHostServer]}:#{params[:tunnelPorts]}"
        privoxy_options[:geo_auth] = params[:geoLocation]
      elsif params[:tunnelHostServer].to_s != "" && params[:tunnelPorts].to_s != ""
        if privoxy_options[:local_testing_chrome_extension_enabled] == true
          socat_host = "localhost"
          socat_port = SocatHelper.get_device_socat_port(device)
          privoxy_options[:repeater_host_port] = "#{socat_host}:#{socat_port}"
          SocatHelper.create_secure_tcp_channel(socat_port, params[:tunnelHostServer], params[:tunnelPorts])
        else
          privoxy_options[:repeater_host_port] = "#{params[:tunnelHostServer]}:#{params[:tunnelPorts]}"
        end
        hosts = []
        if params[:host_only] == "true"
          split_hosts = params[:hosts].split(",")
          split_hosts.each do |y|
            parts = y.split(':')
            hosts << ("#{parts[1]}:#{parts[0]}")
          end
        else
          hosts << "/"
        end
        privoxy_options[:hosts] = hosts
      end

      privoxy_options
    end
  end
end
