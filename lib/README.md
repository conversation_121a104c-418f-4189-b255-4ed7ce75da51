# lib/

## Resign

### Sign .app or .ipa bundle files on host using Resign module.
```ruby
device = '00008101-00046C3111F0001E'
bundle_path = '/tmp/Launcher.ipa'
require_relative 'lib/utils/resign'
require_relative 'lib/configuration'
require_relative 'lib/utils/xcode_build_utils'
config = BrowserStack::Configuration.new.all
destination = "#{bundle_path}.signed.#{device}"
state_files = DeviceState.new(device)
k_pth = config["appium_keychain"]
k_pss = config["appium_keychain_password"]
user = config["user"]
ppdir = config['provisioning_profile_dir']
provision_profile = state_files.ppuid_file_to_array[2].strip
entitlements_file = Tempfile.new(['entitlements', '.plist'])
entitlements_data = BrowserStack::XcodeBuildUtils.get_entitlements_from_profile(File.join(ppdir,"#{provision_profile}.mobileprovision"))
File.write(entitlements_file, entitlements_data)
build_args = {
	:bundle_path => bundle_path,
	:destination => destination,
	:provision_profile => provision_profile,
#	:certificate => '',
	:keychain_password => k_pss,
	:keychain_path => k_pth,
	:user => user,
	:entitlements => entitlements_file.path
}
Class.new.extend(BrowserStack::Resign).resign(build_args)
puts "Signed package: '#{destination}'"
```
