# frozen_string_literal: true

require_relative '../../task_runner'

module DeviceTasks
  class TvosTaskRunner < AppleTaskRunner
    def delete_user_apps
      if genre.nil? || genre.empty?
        BrowserStack.logger.info("Delete user apps called without genre")
        return
      end

      BrowserStack.logger.info("Going to delete user apps")
      session_bundle_ids.each do |bundle_id|
        next if bundle_id.nil? || bundle_id.empty? || bundle_id == 'undefined' || known_apps.include?(bundle_id)

        BrowserStack.logger.info("Attempting to uninstall #{bundle_id}")
        IdeviceUtils.uninstall_app(udid, bundle_id)
      end

      uploaded_apps = read_installed_apps_file("UPLOADED_APPS")
      all_installed_apps = read_installed_apps_file("ALL_INSTALLED_APPS")
      testflight_apps = read_installed_apps_file("TESTFLIGHT_APPS")
      updated_apps = system_apps & all_installed_apps
      all_installed_apps -= (Configuration['app_updates'] + Configuration['known_apps'] + updated_apps)

      (all_installed_apps + other_apps_installed).uniq.each do |app|
        # This step is required for all the products as there are distribution
        # tools which can attempt to install app from URLs which results in waiting
        # state apps. One of such distribution tool that leads to waiting state apps
        # is Applivery.
        BrowserStack.logger.info("User installed #{app}. Uninstalling it")
        IdeviceUtils.uninstall_app(udid, app)
      end

      send_user_apps_to_cls(uploaded_apps, all_installed_apps, testflight_apps, updated_apps) if %w[app_automate app_live_testing].include?(genre)
    end

    private

    def known_apps
      [
        "com.apple.AppStore",
        "com.apple.TestFlight",
        "com.apple.DocumentsApp", # Files App
        "com.apple.mobilesafari"
      ]
    end

    def session_bundle_ids
      [
        session_params['app_testing_bundle_id'],
        session_params['app_details_bundle_id'],
        session_params['test_suite_bundle_id']
      ] + session_params["other_app_bundle_ids"].to_a +
        session_params['mid_session_app_bundle_ids'].to_a
    end

    def read_installed_apps_file(app_type)
      File.read("/tmp/#{udid}_installed_apps/#{app_type}").split(/\n+/).uniq
    rescue
      []
    end

    def system_apps
      IdeviceUtils.list_apps(udid, kind: :system, attempts: 2).map { |app| app[:bundle_id] }
    rescue
      []
    end

    def other_apps_installed
      IdeviceUtils.list_user_installed_apps(udid, attempts: 2)
    rescue
      []
    end

    def send_user_apps_to_cls(uploaded_apps, all_installed_apps, testflight_apps, updated_apps)
      store_apps = all_installed_apps - uploaded_apps - testflight_apps

      push_to_cls(session_params, "store_apps", '', { "store_apps" => store_apps }) unless store_apps.empty?
      push_to_cls(session_params, "testflight_apps", '', { "testflight_apps" => testflight_apps }) unless testflight_apps.empty?
      push_to_cls(session_params, "uploaded_apps", '', { "uploaded_apps" => uploaded_apps }) unless uploaded_apps.empty?
      push_to_cls(session_params, "updated_apps", '', { "updated_apps" => updated_apps }) unless updated_apps.empty?
    end

  end
end
