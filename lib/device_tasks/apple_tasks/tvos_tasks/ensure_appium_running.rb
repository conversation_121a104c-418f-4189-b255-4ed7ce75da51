# frozen_string_literal: true

require_relative '../../task_runner'
require_relative '../../../appium_server'
require_relative '../../../utils/apple_tv_utils'
require_relative '../../../checks/check_device'
require_relative '../../../utils/idevice_utils'

module DeviceTasks
  class AppleTaskRunner < TaskRunner
    def ensure_appium_running(force_restart: false)
      kill_xcode_build_and_iproxy if IdeviceUtils.device_version(udid) >= Gem::Version.new('18') && apple_tv_device?
      appium_server = AppiumServer.new(udid, device_config, check_running: false)
      appium_server.start_server_for_version(Configuration['default_appium_version'], force_restart)
      appium_server.driver if IdeviceUtils.device_version(udid) >= Gem::Version.new('18') && apple_tv_device?
      device_state.remove_wda_uninstalled_file
    rescue WDALaunchError => e
      handle_wda_launch_error(e)
    end

    def kill_xcode_build_and_iproxy
      BrowserStack.logger.info "Restarting xcodebuild"
      OSUtils.kill_process("xcodebuild", udid.to_s)
      BrowserStack.logger.info "Restarting iproxy"
      OSUtils.kill_process("iproxy", udid.to_s)
    end

    def apple_tv_device?
      IdeviceUtils.os(udid) == 'tvos'
    end

    private

    def handle_wda_launch_error(err)
      if device_state.wda_uninstalled_file_present?
        device_state.remove_wda_uninstalled_file
        BrowserStack.logger.info "trying to mount developer image for device: #{udid}"
        CheckDevice.new(udid, nil).check_developer_image_mounted(device_config['device_version'])
        BrowserStack.logger.info("#{err.message}, restarting WDA")
        AppleTVUtils.restart_wda(udid)
        AppleTVUtils.verify_wda_running(udid)
      else
        BrowserStack.logger.info "Uninstalling WebDriverAgentRunner"
        OSUtils.safe_execute(IDEVICEINSTALLER, ['-n', '-u', udid, '-U', WDA_BUNDLE_ID], return_status = true, timeout: 60)
        BrowserStack.logger.info "Uninstalled WebDriverAgentRunner"
        device_state.touch_wda_uninstalled_file
      end
      raise err
    end
  end
end
