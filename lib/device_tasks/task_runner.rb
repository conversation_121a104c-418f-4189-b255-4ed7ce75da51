# frozen_string_literal: true

require 'json'

require_relative '../utils/helpers'
require_relative '../utils/utils'
require_relative '../device_conf'

module DeviceTasks
  class TaskRunner

    attr_reader :udid,
                :device_state

    def initialize(udid:)
      @udid = udid
      @device_state = DeviceState.new(udid)
    end

    def device_config
      DeviceConf[udid]
    end

    def genre
      @genre ||= session_params["genre"].to_s
    end

    def session_params
      begin
        @session_params ||= JSON.parse(device_state.read_session_file)
      rescue Errno::ENOENT => e
        BrowserStack.logger.info("No session file found, Product specific tasks won't run! #{e.message}")
        @session_params ||= {}
      end
      @session_params
    end

  end

  require_relative 'task_runners/apple_task_runner'
  require_relative 'task_runners/ios_task_runner'
  require_relative 'task_runners/tvos_task_runner'
end
