
# CFGUtil

https://configautomation.com/cfgutil-man-page.html

## Running
```shell
cd realmobile
bundle exec irb
```

```ruby
require_relative 'lib/wrappers/cfg_util'
```

### List Devices
```ruby
CFGUtil.new.list_devices || CFGUtil.new.list # Aliased
{"0x169EC1E82801E"=>
  {
    "locationID"=>338964480, "UDID"=>"00008110-000169EC1E82801E", "ECID"=>"0x169EC1E82801E", "name"=>"iPhone","deviceType"=>"iPhone14,4"
  }
}

```
### List Backups
```ruby
CFGUtil.new.list_backups
=> [{"Source"=>"00008110-001875502644801E", "Encrypted"=>false, "Name"=>"iPhone", "Date"=>"2022-03-09T16:30:33Z"}]
```

### Running on specific instance
```ruby
did='00008110-00164DAA3C84801E'
cf = CFGUtil.new(udid: did)
=> <CFGUtil:0x00007faf0d7bf0e8 @udid="00008110-00164DAA3C84801E">
cf.backup_exists?
=> false
cf.backup
=>
{"Command"=>"backup", "Type"=>"CommandOutput", "Devices"=>["0x1875502644801E"]}
```
