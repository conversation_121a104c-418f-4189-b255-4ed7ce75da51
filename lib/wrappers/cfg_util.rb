require 'json'
require 'browserstack_logger'

require_relative '../../config/constants'
require_relative '../utils/http_utils'
require_relative '../configuration'

# It's a wrapper over the command line utility called cfgutil provided with the Apple Configurator 2
# See https://configautomation.com/cfgutil-man-page.html
#
# Usage:
# - CFGUtil.new.list_devices
# - CFGUtil.new(udid: <udid>).get_icon_layout
class CFGUtil #rubocop:todo Metrics/ClassLength
  class ExecutionError < StandardError; end

  class CommandNotFoundError < StandardError; end

  class ProfileAlreadyExists < StandardError; end

  class DeviceNotFoundError < StandardError; end

  class BackupNotFoundError < StandardError; end

  OUTPUT_PRINT_LENGTH = 1000
  JSON_FORMAT = 'JSON'
  TEXT_FORMAT = 'text'
  DEFAULT_TIMEOUT = 300

  attr_accessor :udid

  def initialize(udid: nil, ecid: nil)
    @udid = udid
    @ecid = ecid
  end

  def list
    execute('list')['Output']
  end

  def list_backups
    execute('list-backups')['Output']
  end

  alias list_devices list

  # @return [String] - A unique identifier of a device which is available even in recovery mode. Used to select devices.
  # UDID in only available when booted into iOS.
  def ecid
    @ecid ||= list_devices
              .find { |_, device_info| device_info['UDID'] == udid }
              &.first
  end

  def device_type
    return nil if list_devices[ecid].nil?

    list_devices[ecid]['deviceType']
  end

  # @return [Bool] - Check if cfgutil is installed or not
  def installed?
    retries_left = 3
    begin
      execute('version')
      true
    rescue CommandNotFoundError => e
      false
    rescue => e
      retries_left -= 1
      retry if retries_left > 0
      raise e
    end
  end

  # This will perform a silent installation if the correct supervision certs are passed
  # Similar to #install_profile
  def install_profile_supervised(profile_path, crt_path, der_path)
    cmd = ['install-profile', profile_path]
    output = supervised_device_execute(*cmd, crt_path, der_path, format: JSON_FORMAT, timeout: DEFAULT_TIMEOUT)
  end

  # @return [Hash] - Causes a device to perform Erase All Content and Settings.
  def get_unlock_token(crt_path, der_path)
    cmd = ['get-unlock-token']
    output = supervised_device_execute(*cmd, crt_path, der_path, format: JSON_FORMAT, timeout: DEFAULT_TIMEOUT)
    output["Output"][ecid]
  end

  # @return [Hash] - Causes a device to perform Erase All Content and Settings.
  def clear_passcode(unlock_token_path, crt_path, der_path)
    cmd = ['clear-passcode', unlock_token_path]
    output = supervised_device_execute(*cmd, crt_path, der_path, format: JSON_FORMAT, timeout: DEFAULT_TIMEOUT)
  end

  def get_property(property_name, crt_path, der_path)
    cmd = ['get', property_name]
    output = supervised_device_execute(*cmd, crt_path, der_path, format: JSON_FORMAT, timeout: DEFAULT_TIMEOUT)
    output["Output"][ecid][property_name]
  end

  def get_property_without_crt_files(property_name)
    cmd = ['get', property_name]
    output = device_execute(*cmd, format: JSON_FORMAT, timeout: DEFAULT_TIMEOUT)
    output["Output"][ecid][property_name]
  end

  def add_tag(uuid, name)
    # uuid is not device id, it is unique identifier for tag. Can relate as uuid = key, name = value
    cmd = ['add-tags', '-u', uuid, '-n', name]
    device_execute(*cmd, format: JSON_FORMAT, timeout: 10)
  end

  def get_tag(uuid)
    all_tags = get_property_without_crt_files('tags')
    filtered_tags = all_tags.select { |item| item[:UUID] == uuid || item["UUID"] == uuid }
    return nil if filtered_tags.empty?

    filtered_tags.first[:name]
  end

  # @return [Bool] - Check if backup exists for a particular name, udid by default
  def backup_exists?(backup_name = nil)
    backup_name ||= @udid
    list_backups.any? { |b| b["Source"] == backup_name }
  end

  # @return [Hash] - Take a backup of a prepared iOS device and stores it under the device's UDID in the MobileSync directory.
  def backup(format: TEXT_FORMAT, timeout: DEFAULT_TIMEOUT)
    device_execute('backup', format: format, timeout: timeout)
  end

  # @return [Hash] - Causes a device to perform Erase All Content and Settings.
  def erase(format: TEXT_FORMAT, timeout: DEFAULT_TIMEOUT)
    device_execute('erase', format: format, timeout: timeout)
  end

  # @return [Hash] - Fetches the home screen layout of the device
  def get_icon_layout(format: JSON_FORMAT, timeout: DEFAULT_TIMEOUT)
    device_execute('get-icon-layout', format: format, timeout: timeout)
  end

  # Saves app icon as <app_bundle_id>.png file
  def get_app_icon(app_bundle_id, format: TEXT_FORMAT, timeout: DEFAULT_TIMEOUT)
    cmd = ['get-app-icon', app_bundle_id]
    device_execute(*cmd, format: format, timeout: timeout)
  end

  # @return [Hash] - Restore a backup of data and settings taken from a device.
  # @param [source] [String]
  def restore_backup(format: TEXT_FORMAT, timeout: DEFAULT_TIMEOUT, source: nil)
    cmd = ['restore-backup']
    cmd += ['--source', source] unless source.nil?
    device_execute(*cmd, format: format, timeout: timeout)
  end

  def install_profile(format: TEXT_FORMAT, timeout: DEFAULT_TIMEOUT, profile_path: nil)
    raise StandardError, "No profile supplied" unless profile_path

    cmd = ['install-profile', profile_path]
    device_execute(*cmd, format: format, timeout: timeout)
  rescue ExecutionError => e
    # This is expected as we run UI Automation later to install the profile from
    # the `Downloaded Profiles` section in the Settings app.
    case e.message
    when /User interaction on the device is required to install this profile/
      true
    when /The profile must be installed using a device management tool/
      raise StandardError, "Profile already installed"
    else
      raise StandardError, e.message
    end
  end

  def remove_profile(format: TEXT_FORMAT, timeout: DEFAULT_TIMEOUT, profile_payload_id: nil)
    raise StandardError, "No profile supplied" unless profile_payload_id

    cmd = ['remove-profile', profile_payload_id]
    device_execute(*cmd, format: format, timeout: timeout)
  end

  def update_device(file_path, format: JSON_FORMAT, timeout: 2000)
    cmd = ['update', '-I', file_path]
    device_execute(*cmd, format: format, timeout: timeout)
  end

  def pair(crt_path, der_path, format: JSON_FORMAT, timeout: 30)
    supervised_device_execute('pair', crt_path, der_path, format: format, timeout: timeout)
  end

  def supervise(crt_path, org_name, format: JSON_FORMAT, timeout: DEFAULT_TIMEOUT)
    cmd = ['prepare', '--host-cert', crt_path, '--name', "'#{org_name}'", '--supervised']
    device_execute(*cmd, format: format, timeout: timeout)
  end

  def device_execute(*command, format: JSON_FORMAT, timeout: DEFAULT_TIMEOUT)
    device_ecid = ecid
    raise DeviceNotFoundError unless device_ecid

    execute('--ecid', device_ecid, *command, format: format, timeout: timeout)
  end

  def supervised_device_execute(*command, crt_path, der_path, format: JSON_FORMAT, timeout: DEFAULT_TIMEOUT)
    device_ecid = ecid
    raise DeviceNotFoundError unless device_ecid

    execute('--ecid', device_ecid, '-C', crt_path, '-K', der_path, *command, format: format, timeout: timeout)
  end

  # @param args - Command with args to execute
  # @param [String] format - output format (text, JSON, plist)
  # @return [Hash | String] - String if JSON.parse fails
  def execute(*args, format: JSON_FORMAT, timeout: DEFAULT_TIMEOUT)
    BrowserStack.logger.info("Executing cfgutil command: '#{TIMEOUT_COMMAND} #{timeout} #{CFGUTIL_COMMAND} --verbose --format #{format} #{args.join(' ')}'")

    data_hash = { timeout: timeout, format: format, command_args: args }
    static_conf = BrowserStack::Configuration['static_conf']
    basic_auth = { username: static_conf['sinatra_auth_user'], password: static_conf['sinatra_auth_pass'] }
    response = BrowserStack::HttpUtils.send_post(CFGUTIL_ENDPOINT, data_hash, basic_auth, true, { read_timeout: timeout })
    raise 'cfgutil request non-200 response' if response.code != '200'

    response_body = JSON.parse(response.body)
    output = response_body['output']
    status = response_body['exitstatus']
    error = response_body['error']

    BrowserStack.logger.info("cfgutil output: '#{output.to_s[0..OUTPUT_PRINT_LENGTH]}'; status: #{status}")

    case status
    when 0
      output.strip!

      if format == JSON_FORMAT
        begin
          JSON.parse(output)
        rescue
          output
        end

      else
        output
      end
    when 124
      raise TimeoutError, "cfgutil Command timed out"
    when 127
      raise CommandNotFoundError, error.to_s
    else
      case output
      when /Remove the profile.*before installing this profile/
        raise ProfileAlreadyExists, error.to_s
      else
        raise ExecutionError, error.to_s
      end
    end
  end
end
