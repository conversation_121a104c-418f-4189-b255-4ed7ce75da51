require 'appium_lib'
require 'json'
require_relative '../lib/utils/utils'
require_relative '../lib/utils/http_utils'

class PermitPhotosAccess
  def self.click_ok(device_id)
    BrowserStack.logger.info "Starting Appium for PermitPhotosAccess for device: #{device_id}."
    @appium_driver = Automation.get_driver_for_app(device_id, "com.browserstack.app")
    begin
      @appium_driver.find_element(:id, "OK").click
      BrowserStack.logger.info("Permission granted to access photos app")
      true
    rescue
      BrowserStack.logger.info("Permission not granted to access photos app")
      false
    end
  end
end
