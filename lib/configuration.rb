require 'yaml'
require 'json'
require 'static_conf'

module BrowserStack
  class Configuration
    attr_reader :device_config

    #TODO: these should be static
    def initialize
      config = YAML.load_file("#{Configuration.base_dir}/config/config.yml")
      keys = YAML.load_file("#{Configuration.base_dir}/keys/keys.yml")
      constants = YAML.load_file("#{Configuration.base_dir}/config/constants.yml")
      # Do not merge with the constants since the key is the device_name and it
      # might get overwritten if the same key exists in some other config.
      @device_config = YAML.load_file("#{Configuration.base_dir}/config/device_specific_config.yml")
      @server_config = config.merge(keys).merge(constants)

      @static_conf_file = @server_config['static_conf_file']
      # extra settings
      @server_config['static_conf'] = StaticConf::StaticConfHelper.setup

      if File.exists?("#{Configuration.base_dir}/config/machine_custom_config.yml")
        custom_config = YAML.load_file("#{Configuration.base_dir}/config/machine_custom_config.yml")
        @server_config.merge!(custom_config)
      end
    end

    def all
      @server_config
    end

    class << self
      def [](key)
        conf[key]
      end

      def conf
        @conf ||= Configuration.new.all
      end

      def base_dir
        @base_dir ||= "#{__dir__}/.."
      end
    end
  end
end
