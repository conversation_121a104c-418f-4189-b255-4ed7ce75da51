# Description: This is a queue which is used by file uploader and converter processes to monitor addition of new files for their consumption.
require 'fileutils'

require_relative './utils/utils'
require_relative './configuration'
require_relative './custom_exceptions'
require_relative './utils/zombie'
require 'browserstack_logger'

# Queues are polling for files in <working_directory>/
module BrowserStack
  class FileProcessingQueue

    Zombie.configure # TODO: Needs to be moved into initialize function

    def initialize(name, working_directory, max_retries=5)
      conf = Configuration.new
      @server_config = conf.all
      @name = name
      @channel = @server_config[working_directory]
      @max_retries = max_retries
      @retry_counter = Hash.new(0)
      @current_file_batch = []
      FileUtils.mkdir_p(@channel) unless File.directory? @channel
    end

    def cleanup_request_data(request_file)
      @retry_counter.delete(request_file)
      @retry_counter.delete("#{request_file}_queue_time")
      @retry_counter.delete("#{request_file}_start_time")
      File.delete request_file
    end

    def handle_failed_request(request_file)
      data = begin
        JSON.parse(File.read(request_file))
      rescue
        {}
      end
      unless data.empty?
        session_id = data['session_id']
        instrumentation_file = Utils.get_screenshot_instrumentation_file(@server_config, session_id)
        time_components = {
          stage_time_taken: (Time.now - @retry_counter["#{request_file}_start_time"]).round(2),
          queue_time: @retry_counter["#{request_file}_queue_time"]
        }
        if File.exist? instrumentation_file
          case @name
          when IMAGE_UPLOADER
            Utils.update_screenshot_instrumentation_with_lock(instrumentation_file, "upload", "failed", time_components)
          when FILE_CONVERTER
            Utils.update_screenshot_instrumentation_with_lock(instrumentation_file, "convert", "failed", time_components)
          when SCREENSHOT_INSTRUMENTATION
            BrowserStack.logger.info("Deleting instrumentation_file #{instrumentation_file}")
            File.delete instrumentation_file
            File.delete("#{instrumentation_file}.lock") if File.exist?("#{instrumentation_file}.lock")
          end
        end
        if @name == VIDEO_UPLOADER && File.exist?(data['async_process_file'].to_s)
          BrowserStack.logger.info("Deleting following files for async flow, after retries exhausted : #{data['async_meta_data_file']}, #{data['async_process_file']}")
          File.delete(data['async_meta_data_file']) if File.exist?(data['async_meta_data_file'].to_s)
          File.delete(data['async_process_file'])
        end
      end

      BrowserStack.logger.error("Retries expired for :#{request_file}, Deleting it.")
    ensure
      cleanup_request_data(request_file)
    end

    def process_files # rubocop:todo Metrics/AbcSize
      if @current_file_batch.empty?
        @current_file_batch = Dir["#{@channel}/*.json*"].sort_by do |f|
          File.mtime f
        rescue
                                                   Time.now # rubocop:todo Layout/IndentationWidth
        end
      end
      request_file = @current_file_batch.shift

      return if request_file.nil? || request_file.empty? || !File.readable?(request_file) || (@name == SCREENSHOT_INSTRUMENTATION && (Time.now - File.mtime(request_file)) / 60 < SCREENSHOT_INSTRUMENTATION_FILE_PICK_THRESHOLD)

      begin
        unless request_file.end_with?("_lock")
          File.rename(request_file, "#{request_file}_lock") # add `_lock` suffix to the file to prevent getting deleted by stale cleaner
          request_file += "_lock"
        end
      rescue => e # prevent race condition
        BrowserStack.logger.error("Exception while locking files:#{request_file} : #{"#{e.message}\n#{e.backtrace[0...2].join("\n")}"}")
        return
      end

      begin
        @retry_counter["#{request_file}_queue_time"] = (Time.now - File.mtime(request_file)).round(2) unless @retry_counter.key?("#{request_file}_queue_time")
        @retry_counter["#{request_file}_start_time"] = Time.now unless @retry_counter.key?("#{request_file}_start_time")
        yield request_file
        cleanup_request_data(request_file)
      rescue NonRetryableRequestException => e
        handle_failed_request(request_file)
      rescue => e
        BrowserStack.logger.error("Exception while processing:#{request_file} : #{e.message}\n#{e.backtrace[0...2].join("\n")}")
        Zombie.push_logs('ios-file-processing-failed', e.message, { 'data' => @name })
        @retry_counter[request_file] += 1
        if @retry_counter[request_file] > @max_retries
          handle_failed_request(request_file)
        else
          @current_file_batch.unshift(request_file) # push back to batch for retry
        end
      end
    end

    def run(poll_interval=0.1, &block)
      BrowserStack.logger.info("Started FileProcessingQueue for #{@name}.")
      loop do
        process_files(&block)
        sleep poll_interval
      end
    end
  end
end
