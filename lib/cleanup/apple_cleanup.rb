# frozen_string_literal: true

require_relative '../models/device_state'
require_relative '../device_tasks/task_runner'

module Cleanup
  class AppleCleanup

    CleanupError = Class.new(StandardError)

    attr_reader :udid,
                :device_state

    def initialize(udid:)
      @udid = udid
      @device_state = DeviceState.new(udid)
    end

    def perform
      run

      on_success
    end

    private

    def on_success
      device_state.remove_full_cleanup_file
      device_state.remove_xctest_session_timedout_file
      device_state.remove_manual_cleanup_file
      device_state.remove_device_logger_pid_file
      device_state.remove_device_logger_session_end_pid_file
      device_state.remove_wifi_enabled_file
      device_state.remove_xcuitest_result_bundle_zip_file
      device_state.remove_cleanupdone_file
      device_state.remove_session_start_indicator_file
    end

    def run
      raise CleanupError, "#{self.class} needs to implement #{__method__} method"
    end

    def task_runner
      raise CleanupError, "#{self.class} needs to implement #{__method__} method"
    end

  end

  # Require all sub classes of the apple cleanup
  Dir[File.join(__dir__, 'apple_cleanup', '*.rb')].sort.each do |file|
    require file
  end
end
