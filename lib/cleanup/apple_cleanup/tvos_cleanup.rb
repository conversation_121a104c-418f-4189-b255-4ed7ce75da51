# frozen_string_literal: true

require_relative '../apple_cleanup'

module Cleanup
  class TvosCleanup < AppleCleanup

    private

    def run
      task_runner.delete_user_apps
      # Forcing the restart of the appium server to cleanup appium's internal sessions
      # thereby improving the performance of appium server for running a lot of sessions
      task_runner.ensure_appium_running(force_restart: true)
    end

    def task_runner
      @task_runner ||= DeviceTasks::TvosTaskRunner.new(udid: udid)
    end
  end
end
