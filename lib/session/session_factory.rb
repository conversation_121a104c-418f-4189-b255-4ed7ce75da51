require_relative './live_session'
require_relative './app_live_session'
require_relative './js_testing_session'

module BrowserStack
  class SessionFactory
    TYPES = {
      live_testing: LiveSession,
      app_live_testing: AppLiveSession,
      js_testing: JsTestingSession
    }.freeze

    def self.for(session_params, device_config)
      genre = session_params["genre"].to_sym
      raise "Unknown session genre #{genre}" if TYPES[genre].nil?

      TYPES[genre].new(session_params, device_config)
    end
  end
end
