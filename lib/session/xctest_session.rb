# All params[:symbols] have been converted to params["string"]
# This was introduced when xctest had to be spawned instead of forked
# While spawning, the params need to be passed as json string which
# modifies all fields with strings.
# Transform_keys and Marshaling did not work perfectly for a highly nested
# object like params within the code and given the libraries/versions that we use.

require 'json'
require 'fileutils'
require 'securerandom'
require_relative '../configuration'
require_relative '../utils/osutils'
require_relative '../utils/utils'
require_relative '../utils/http_utils'
require_relative '../utils/device_logger'
require_relative '../utils/xcuitest_summaries_parser'
require_relative '../helpers/video_rec_manager'
require_relative '../helpers/wda_client'
require_relative '../utils/idevice_utils'
require_relative '../ios_vid_capturer'
require_relative '../mitm_proxy'
require_relative '../app_percy/app_percy_session'
require_relative '../privoxy_manager'
require_relative '../../../mobile-common/frameworks_timeout_manager/app_automate_frameworks/xctest_timeout_manager'
require_relative '../models/device_state'
require_relative '../../config/constants'

module BrowserStack
  # XCTestSession is the parent class for running different flavours of xctests
  # using xcodebuild command on AA. Browserstack::Session is common for all products.
  # Currently, this class serves as the base class for both fluttertest & Default Flow.
  # xctestrun flow has been moved to lib/app_automate/frameworks/xctestrun/xctestrun_session.rb
  # TODO: Move the different flows, namely: Default, fluttertest, xctestrun flows to separate class.
  class XCTestSession < BrowserStack::Session # rubocop:todo Metrics/ClassLength

    DEFAULT_TEST_STATUS = 'QUEUED'
    TIMEDOUT_EXIT_CODE = 137
    UNEXPECTED_XCODEBUILD_EXIT_CODES = [70]
    MAX_TEST_RETRY_COUNT = 3
    MAX_MITM_RESTART_RETRY_COUNT = 5
    DEVICE_NOT_FOUND_REGEX = /Unable to find a destination matching the provided destination specifier|Unable to find device with identifier|Timed out waiting for all destinations matching the provided destination specifier/
    MACHINE_ISSUES_REGEX = /Too many open files|No space left/
    # These patterns are marked as platform-issues.
    SYSTEM_LEVEL_PATTERNS = {
      "runner_failure" => /Failed to install or launch the test runner/,
      "runner_comm_failure" => /Failed to establish communication with the test runner/,
      "remote_comm_failure" => /An error occurred while communicating with a remote process|Connection with the remote side was unexpectedly closed/,
      "device_not_found" => DEVICE_NOT_FOUND_REGEX,
      "device_busy" => /Device is busy \(Waiting to reconnect/,
      "device_conn_failure" => /A connection to this device could not be established|Timeout while connecting to remote device/,
      "app_install_failure" => /Failed to install the app on the device/,
      "destination_not_ready" => /Run Destination Preflight: The destination is not ready\./,
      "test_runner_exited" => /Test runner exited before starting test execution\./
    }
    NON_SYSTEM_LEVEL_PATTERNS = {
      "runner_launch_failure" => /Encountered a problem with the test runner after launch/,
      "runner_restarted" => /Restarting after unexpected exit, crash, or test timeout/,
      "unexpected_exit" => /Early unexpected exit, operation never finished bootstrapping - no restart will be attempted/,
      "runner_unexpected_exit" => /Test runner never began executing tests after launching/,
      "bundle_id_not_found" => /Failed to get the identifier for the app bundle/,
      "app_not_installed" => /No installed application found for launch session/,
      "app_terminated" => /Terminating app due to uncaught exception '([^']*)', reason: '([^']*)'/,
      "test_conn_lost" => /Lost connection to testmanagerd/,
      "xctrunner_fail" => /The request to open "com\.apple\.test\.XCTRunner" failed/,
      "request_timedout" => /The request timed out\./,
      "operation_couldnot_complete" => /The operation couldn’t be completed\./,
      "paysafe_wiremock_error" => /WireMockUtils.swift/,
      "testsuite_missing_test_dynamic_test" => /Failed to build workspace temporary with scheme Transient Testing.: Cannot test target|Failed to create a bundle instance representing/
    }

    attr_reader :app_percy_session

    def setup_config(server_config)
      @server_config = server_config
      @app_percy_session = AppPercy::Session.new(@device) if @params['app_percy']
    end

    def start
      log_start "start"
      super
      start_app_percy
      save_session_params
      setup_params
      filter_testlist(@params["test_params"])
      setup_summary
      run_xctestsuite
      log_finish "start"
    rescue Exception => e
      handle_test_execution_error(e)
    ensure
      stop

    end

    def track_stability
      matching_keys = SYSTEM_LEVEL_PATTERNS.keys & (@test_meta_info || {}).keys
      return if matching_keys.empty?

      session_data = JSON.parse(File.read(@callback_file))
      test_status = session_data["test_status"]
      primary_reason = test_status["error"] > 0 ? "error-platform-issues" : nil
      track_stability_reason_in_eds(primary_reason, "xcodebuild-device-issue") unless matching_keys.empty?
    end

    def stop

      log_start "stop"
      stop_app_percy
      BrowserStack::DeviceLogger.destroy(@device)
      MCSPT.stop_session_running_on_device_async(@device.to_s, cancelled: false)
      track_stability
      push_feature_usage_to_eds
      syslog_parse_result_file = "#{APP_AUTOMATE_PARSER_DIR}/results_#{@params['automate_session_id']}.log"
      Utils.push_syslog_parse_results(syslog_parse_result_file, @params['automate_session_id'], @device)
      upload_result_bundle if result_bundle_enabled?
      upload_summary
      Utils.write_to_file("/tmp/video_params_#{@device}", @params.to_json) if video_enabled?
      if boot_mitmproxy?
        MitmProxy.stop_proxy(@device, @params['automate_session_id'], "xcuitest")
        if @mitmproxy&.proxy_log_file
          output, = BrowserStack::OSUtils.execute("tail -100 #{@mitmproxy.proxy_log_file} | grep -E \"TypeError: 'NoneType' object is not subscriptable|Exception in thread ServerThread\"", true, timeout: 3)
          BrowserStack.logger.info("[#{@params['automate_session_id']}] mitm exception captured: #{output}")
          BrowserStack::Zombie.push_logs("xcuitest-mitm-exception", "Exception in mitm proxy logfile", { "session_id" => @params['automate_session_id'], "data" => output[0, 1000] }) unless output.empty?
        end
      end

      super
      log_finish "stop"
    rescue => e
      BrowserStack.logger.error("Error in XCTest Stop: #{e.message}\n#{e.backtrace.join("\n")}")
    ensure
      cleanup_files
      inform_rails

    end

    def ensure_session_stop
      spawn_xctest_pid_file = spawn_xctest_pid_file_path
      spawn_xctest_pid = AppAutomateFrameworks::ProcessUtils.get_process_id(spawn_xctest_pid_file)
      running_status = AppAutomateFrameworks::ProcessUtils.process_running?(spawn_xctest_pid)

      BrowserStack.logger.info("#{spawn_xctest_pid} Running: #{running_status} for session: #{@params['automate_session_id']}")
      unless running_status
        FileUtils.rm_rf spawn_xctest_pid_file
        return
      end

      BrowserStack.logger.info("#{@params['automate_session_id']} : Killing spawn_xctest process, cleanup requested mid session")
      AppAutomateFrameworks::ProcessUtils.kill_pid(spawn_xctest_pid)

      xctest_manager = AppAutomateFrameworks::XCTestTimeoutManager.new(
        @device,
        nil,
        logger: BrowserStack.logger,
        logger_params: BrowserStack.logger.params
      )
      xctest_manager.kill_idle_process
      xctest_manager.stop # Stop timeout manager

      AppPercy::Session.new(@device).stop(@params['automate_session_id'])
      BrowserStack::DeviceLogger.destroy(@device)
      MitmProxy.stop_proxy(@device, @params['automate_session_id'], "xcuitest")

      ensure_session_files_cleanup
      BrowserStack::Zombie.push_logs("cleanup-forcefully-stopping-session", "Invalid cleanup request received while session is running", { "session_id" => @params['automate_session_id'] })
    rescue => e
      BrowserStack.logger.error("Error in XCTest ensure_session_stop #{e.message} \n#{e.backtrace.join("\n")}")
      raise e
    end

    def ensure_session_files_cleanup
      BrowserStack.logger.info("#{@params['automate_session_id']} : Cleaning session generated files")
      Dir.glob("/tmp/#{@device}_xctest_*").each { |file| FileUtils.rm_rf(file) } # Delete testsuite files
      Dir.glob("/tmp/#{@device}_xcuitest_instrumentation*").each { |file| FileUtils.rm_rf(file) }
      FileUtils.rm_rf "/tmp/video_params_#{@device}"
      FileUtils.rm_rf Utils.get_logs_stability_file_path(@params['automate_session_id'])
      cleanup_files
    end

    # To forcefully stop the session
    def force_stop
      log_start "force_stop"
      stop_app_percy
      # Timeout the whole session
      device_state.touch_xctest_session_timedout_file
      BrowserStack.logger.info("force_stop : created timeout file for device: #{@device}")

      # Kill the current running test and mark it as timedout
      xctest_manager = AppAutomateFrameworks::XCTestTimeoutManager.new(
        @device,
        nil,
        logger: BrowserStack.logger,
        logger_params: BrowserStack.logger.params
      )

      xctest_manager.kill_idle_process
      BrowserStack.logger.info("Killed running session for device: #{@device}")
    rescue => e
      BrowserStack.logger.error("Error in XCTest force stop#{e.message} \n#{e.backtrace.join("\n")}")
      raise e
    ensure
      log_finish "force_stop"
    end

    def timeout
      log_start "timeout"
      session_data = JSON.parse(File.read("/tmp/#{@device}_xctest_callback"))
      if (@params['build_id'] == session_data["build_id"]) && (@params['automate_session_id'] == session_data["session_id"])
        device_state.touch_xctest_session_timedout_file
        BrowserStack.logger.info("Created Session Timeout File")
      else
        BrowserStack.logger.error("Error in XCTest Timeout. Session Details didn't match")
      end
      log_finish "timeout"
    end

    def generated_session_files
      [
        xctest_xmlfile_path,
        product_module_name_file_path,
        spawn_xctest_pid_file_path,
        xctest_timeout_pid_file_path,
        xctest_summary_file_path,
        get_summary_path_v2,
        xcresult_bundle_directory,
        xcresult_bundle_zip_file,
        custom_har_dump_file
      ]
    end

    def start_app_percy
      @app_percy_session&.start(@params)
    end

    def stop_app_percy
      @app_percy_session&.stop(@params['automate_session_id'])
    end

    # Tries deleting xctest runner cache file. If it fails after 2 retries, sends an alert
    def delete_xctest_runner_app_cache
      log_start("delete_xctest_runner_app_cache")
      successful = false
      exit_code = nil
      2.times do
        _, exit_code = BrowserStack::OSUtils.execute("rm -rf #{xctest_runner_app_cache}", true, timeout: 3)
        if exit_code == 0
          successful = true
          break
        end
      end
      BrowserStack::Zombie.push_logs('xctest-cleanup-files-failure', 'xctest-cleanup-files-failure', { "session_id" => @session_id, "exit_code" => exit_code }) unless successful
      log_finish("delete_xctest_runner_app_cache")
    rescue => e
      BrowserStack.logger.error("Error in delete_xctest_runner_app_cache : #{e.message}\n#{e.backtrace.join("\n")}")
      BrowserStack::Zombie.push_logs('xctest-cleanup-files-error', e.message[0, 150], { "session_id" => @session_id })
      log_finish("delete_xctest_runner_app_cache")
    end

    def cleanup_files
      log_start "cleanup_files"
      @testsuite_files&.each { |f| FileUtils.rm_rf f }
      generated_session_files.each { |f| FileUtils.rm_rf f unless f.nil? }
      delete_xctest_runner_app_cache if Gem::Version.new(@device_os_version) >= Gem::Version.new(17.0)
      log_finish "cleanup_files"
    end

    def save_session_params
      BrowserStack.logger.info("Saving xctest session params for #{@device}")
      save_session_params_v1
      save_session_params_v2
    end

    def save_session_params_v1
      log_start "save_session_params_v1"
      details = ""
      test_framework = @params['test_framework']
      summary = {
        'build_id' => @params["build_id"],
        'session_id' => @params["automate_session_id"],
        'device' => @params["user_device_name"],
        'start_time' => Time.now.utc.to_s,
        'duration' => '',
        'idle_timeout' => @params["idle_timeout"],
        'error_reason' => ''
      }

      case test_framework
      when "xcuitest"
        details = 'test_suite_details'
        summary['app_details'] =  {
          'url' => @params["app_details_url"],
          'bundle_id' => @params["app_details_bundle_id"],
          'custom_id' => @params["app_details_custom_id"],
          'version' => @params["app_details_version"],
          'name' => @params["app_details_name"]
        }
      when "earlgrey"
        details = 'app_dir_details'
      when "fluttertest"
        details = 'test_suite_details'
      end

      summary[details] = {
        'url' => @params["test_suite_url"],
        'bundle_id' => @params["test_suite_bundle_id"],
        'custom_id' => @params["test_suite_custom_id"],
        'name' => @params["test_suite_name"]
      }

      @summary_file = xctest_summary_file_path
      Utils.write_to_file(@summary_file, summary.to_json)
      log_finish "save_session_params_v1"
    end

    def save_session_params_v2
      log_start "save_session_params_v2"
      @callback_file = "/tmp/#{@device}_xctest_callback"
      callback_hash = {
        'build_id' => @params["build_id"],
        'session_id' => @params["automate_session_id"],
        'timeout_sidekiq_worker_hashed_id' => @params["timeout_sidekiq_worker_hashed_id"],
        'auth_key' => @params["auth_key"],
        'rails_callback' => "http://#{@params['rails_host']}/app-automate/session_done",
        'device' => @device,
        'error_reason' => ""
      }

      Utils.write_to_file(@callback_file, callback_hash.to_json)
      log_finish "save_session_params_v2"
    end

    def get_product_module_name_from_filename(filename)
      # testsuites filenames are in format
      # /tmp/#{device_id}_xcuitest_#{product_module_name}
      product_module_name = filename.split("/").last # ==> #{device_id}_xcuitest_product_module_name}
      product_module_name = product_module_name.split("_") # ==> [ #{device_id}, xcuitest, product, module, name]
      product_module_name = product_module_name.drop(2) # ==> [product, module, name]
      product_module_name = product_module_name.join("_") #==> product_module_name
    end

    # Pushing this stability reason as SDR directly to EDS as machine issues like "Too many files|No Space left" errors
    # prevent updating the callback file. To prevent overriding of product.stability.reason in rails, sending it to rails also.
    def handle_test_execution_error(exception)
      if exception.message.match(MACHINE_ISSUES_REGEX)
        track_stability_reason_in_eds("error-platform-issues", "machine-storage-issue")
        @override_error_reason = "machine-storage-issue"
      end
      BrowserStack.logger.info("[#{@params['automate_session_id']}]Test Execution Error while running XCTest: #{exception.message}\n#{exception.backtrace.join("\n")}")
      BrowserStack::Zombie.push_logs("#{@params['test_framework']}-execution-error", exception.message[0, 150], { "session_id" => @params['automate_session_id'] })
    rescue => e
      BrowserStack.logger.error("[#{@params['automate_session_id']}]Error while rescuing Test Execution error: #{e.message}\n#{e.backtrace.join("\n")}")
    end

    def handle_testsuite_parse_empty(filename)
      BrowserStack.logger.info("No XCTests found after filter for device #{@device} for #{filename}")
      BrowserStack::Zombie.push_logs("#{@params['test_framework']}-testsuite-parse-empty", "Testsuite file empty", { "session_id" => @session_id })
      send_error_reason_in_file(@callback_file, "user-testsuite-parse-empty")
      send_error_reason_in_file(@summary_file, "user-testsuite-parse-empty") if @summary_file && File.exists?(@summary_file)
      s3_params = get_s3_params("testsuite-parsed-tests.txt")
      # For xcuitest @testsuite_files array consists of only one element i.e. dry run output
      create_upload_request(s3_params, @testsuite_files.first, "parsedtest") if @testsuite_files
    end

    def handle_testsuite_parse_failed(e)
      BrowserStack.logger.error "XCTest filter_testlist: #{e.message}"
      BrowserStack::Zombie.push_logs("#{@params['test_framework']}-testsuite-filter-failed", e.message.to_s, { "session_id" => @session_id })
      BrowserStack::Zombie.push_logs("app_automation_session_stats", '', { "sessionid" => @session_id, "secondary_diagnostic_reason" => "start-error-testsuite-parse-failed" })
      send_error_reason_in_file(@callback_file, "testsuite-parse-failed")
      send_error_reason_in_file(@summary_file, "testsuite-parse-failed") if @summary_file && File.exists?(@summary_file)
    end

    def filter_testlist(testparams) # rubocop:todo Metrics/AbcSize, Metrics/PerceivedComplexity
      log_start "filter_testlist"
      substring_match_strategy = []

      return if testparams.nil?

      begin
        @testsuite_files.each { |filename| raise "XCTest Suite file missing" unless File.exists?(filename) }

        testparams = JSON.parse(testparams)

        @testsuite_files.each do |filename|
          testlist = get_testlist(filename)
          product_module_name = get_product_module_name_from_filename(filename)
          testparams["skip-testing"].to_a.each do |skip|
            testlist = testlist.reject do |t|
              substring_match_strategy |= ["skip-testing"] if t.match(/#{skip}/) && Utils.substring_match?(t, skip, product_module_name)
              t.match(/#{skip}/)
            end
          end

          include_list = testparams["only-testing"].to_a.empty? ? testlist : []
          # Remove trailing newline characters from test names
          testparams["only-testing"] = remove_newlines(testparams["only-testing"].to_a) if testparams["only-testing"]
          if testparams["is_dynamic_xcuitest"].to_s == "true"
            # include only those test of the product module name in respective file
            include_list = testparams["only-testing"].to_a.select { |test_name| test_name.split("/").first == product_module_name }
          else
            testparams["only-testing"].to_a.each do |incl|
              include_list += testlist.select do |t|
                substring_match_strategy |= ["only-testing"] if t.match(/#{incl}/) && Utils.substring_match?(t, incl, product_module_name)
                t.match(/#{incl}/)
              end
            end
          end
          testlist = include_list.uniq
          BrowserStack.logger.info("XCTests filtered for device #{@device} for #{filename} and testlist #{testlist}")

          handle_testsuite_parse_empty(filename) if testlist.empty?
          Utils.write_to_file(filename, testlist.join("\n"))
        end
      rescue => e
        handle_testsuite_parse_failed(e)
      end
      BrowserStack::Zombie.push_logs("#{@params['test_framework']}-testname-substring-match", "", { "session_id" => @session_id,  "data" => substring_match_strategy }) unless substring_match_strategy.empty?
      log_finish "filter_testlist"
    end

    def remove_newlines(arr)
      return unless arr.is_a?(Array)

      arr.map(&:strip)
    end

    def default_test(input)
      id = get_md5(input)
      {
        start_time: '',
        status: DEFAULT_TEST_STATUS,
        test_id: @session_id + id,
        duration: '',
        instrumentation_log: '',
        device_log: '',
        video: ''
      }
    end

    def send_error_reason_in_file(filename, error_reason)
      log_start "send_error_reason_in_#{filename}_file"
      file_hash = Utils.read_json_file(filename)
      file_hash["error_reason"] = error_reason
      Utils.write_to_file(filename, file_hash.to_json)
      log_finish "send_error_reason_in_#{filename}_file"
    end

    def get_testhash
      testhash = {}
      @testsuite_files.each do |testfile|
        testlist = get_testlist(testfile)
        testlist.each do |t|
          test_obj = default_test(t)
          t_split = t.split("/")
          t_name = t_split.pop
          c_name = t_split.join("/")
          testhash[c_name] ||= {}
          testhash[c_name][t_name] = test_obj
        end
      end
      testhash
    end

    def test_states(count = 0)
      {
        "total" => count,
        'passed' => 0,
        'failed' => 0,
        'skipped' => 0,
        'timedout' => 0,
        'error' => 0,
        'running' => 0,
        'queued' => count
      }
    end

    def get_testhash_v2
      testhash_v2 = {}
      @testsuite_files.each do |testfile|
        testlist = get_testlist(testfile)
        testlist.each do |test_obj|
          id = get_md5(test_obj)
          full_test_name = test_obj.split("/")
          test_name = full_test_name.pop
          class_name = @is_duplicate ? full_test_name.join("/") : full_test_name.pop # We name module + class name as unqiue string if duplicate class + test are present
          testhash_v2[class_name] ||= {
            "name" => class_name,
            "tests_summary" => test_states,
            "tests" => {}
          }
          testhash_v2[class_name]['tests'][test_name] = {
            name: test_name,
            start_time: '',
            status: DEFAULT_TEST_STATUS.downcase,
            test_id: @session_id + id,
            duration: '',
            video: '',
            class: class_name
          }
          ['total', 'queued'].each { |state| testhash_v2[class_name]['tests_summary'][state] += 1 }
        end
      end
      testhash_v2
    end

    def get_total_number_of_tests
      @testsuite_files.each do |testfile|
        testlist = get_testlist(testfile)
        @total_test_count += testlist.count
      end
    end

    def setup_summary
      setup_summary_v1
      setup_summary_v2
    end

    def setup_summary_v1
      log_start "setup_summary_v1"
      testhash = get_testhash
      get_total_number_of_tests
      summary_data = get_summary_data(@summary_file)
      summary_data.merge!(
        {
          'test_count' => @total_test_count,
          'test_details' => testhash,
          'test_status' => {
            'SUCCESS' => 0,
            'FAILED' => 0,
            'IGNORED' => 0,
            'TIMEDOUT' => 0,
            'ERROR' => 0,
            'RUNNING' => 0,
            'QUEUED' => @total_test_count
          }
        }
      )
      Utils.write_to_file(@summary_file, summary_data.to_json)
      log_finish "setup_summary_v1"
    end

    def setup_summary_v2
      log_start "setup_summary_v2"
      # Check for duplicate classname + testname
      # This checks for duplicate class name + testname as in v2 we map class name and test name and there's a possibility
      #  that class name and test name are same but product module name differs
      check_duplicate_test
      # Session Summary File v2
      @summary_file_v2 = get_summary_path_v2
      testhash_v2 = get_testhash_v2
      summary_data_v2 = {
        build_id: @params["build_id"],
        session_id: @params["automate_session_id"],
        test_summary: test_states(@total_test_count),
        classes: testhash_v2
      }

      Utils.write_to_file(@summary_file_v2, summary_data_v2.to_json)
      log_finish "setup_summary_v2"
    end

    def get_summary_path_v2
      "/tmp/#{@device}_xctest_summary_v2.json"
    end

    def get_testlist(testfile)
      File.readlines(testfile).each { |e| e.gsub!("\n", "") }
    end

    def get_summary_data(summary_file)
      JSON.parse(File.read(summary_file))
    end

    def read_product_module_name_file
      return unless File.exist? @product_module_name_file

      names = File.readlines(@product_module_name_file).each { |e| e.gsub!("\n", "") }
      names.each do |name|
        name_split = name.split(":")
        @product_module_names[name_split[0]] = name_split[1]
      end
    end

    def generate_xctestrun_xml(test_framework, xctest)
      log_start "generate_xctestrun_xml"
      testdir = "/tmp/#{@device}_test"

      product_module_name = @product_module_names[xctest]
      product_module_name = product_module_name.to_s.empty? ? xctest : product_module_name
      product_module_name = @params['product_module_name'] if @params['product_module_name'] && test_framework == "xcuitest"
      xcui_app_identifier = test_framework == "xcuitest" ? @params["app_details_bundle_id"].to_s : ""
      environment_variables = begin
        JSON.parse(@params["environment_variables"])
      rescue
        {}
      end
      if @params["enableCameraImageInjection"] == true && test_framework == "xcuitest"
        environment_variables["SENSOR_MOCKER_HOST"] = "http://#{SENSOR_MOCKER_HOST}"
        environment_variables["IMAGE_INJECTION_PATH"] = "xcuitest/inject-media"
      end
      language = @params["language"]
      locale = @params["locale"]

      data = {
        xctestname: xctest,
        xctest_identifier: @params["test_suite_bundle_id"].to_s,
        product_module_name: product_module_name,
        xcui_app_identifier: xcui_app_identifier,
        environment_variables: environment_variables,
        language: language,
        locale: locale
      }
      xctestrun_erb_filename = "#{__dir__}/../../templates/#{test_framework}_xctestrun.erb"
      if @device_os_version >= 17.0
        BrowserStack.logger.info("XCTEST: Using secondary xctestrun erb file for device_os_version: #{@device_os_version}, app_path: #{@downloaded_app_path}, test_app_path: #{@downloaded_test_app_path}")
        data[:app_path] = @downloaded_app_path
        data[:test_bundle_path] = @downloaded_test_app_path
        xctestrun_erb_filename = "#{__dir__}/../../templates/#{test_framework}_xctestrun_with_test_host_path.erb"
      end

      xctestrun_contents = ERB.new(File.open(xctestrun_erb_filename).read).result(ErbBinding.new(data).get_binding)
      Utils.write_to_file(@xctest_xmlfile, xctestrun_contents)
      FileUtils.rm_rf(testdir)
      log_finish "generate_xctestrun_xml"
    end

    def reset_logfiles
      log_start "reset_logfiles"
      Utils.write_to_file(@devicelog_file, "")
      log_finish "reset_logfiles"
    end

    def create_upload_request(s3_params, file_name, type = "")
      log_start "create_upload_request"
      curr_time = (Time.now.to_f * 1000).to_i
      temp_file = s3_params['is_json'] ? "/tmp/#{@device}_xctest_log_summary_#{curr_time}.log" : "/tmp/#{@device}_xctest_log_#{curr_time}.log"
      BrowserStack::OSUtils.execute("cp #{file_name} #{temp_file}")
      BrowserStack::OSUtils.execute("chown app #{temp_file}")
      uploader_request_file = @server_config["other_files_to_upload_dir"] + "/xctest_logs_#{SecureRandom.uuid}.json"
      json_data = {
        upload_type: "logs_upload",
        file_name: temp_file,
        s3_params: s3_params,
        genre: @genre,
        type: type,
        framework: @test_framework
      }
      dir_name = File.dirname(uploader_request_file)
      FileUtils.mkdir_p(dir_name) unless Dir.exist? dir_name
      Utils.write_to_file(uploader_request_file, json_data.to_json)
      BrowserStack.logger.info("#{file_name} upload request created as #{uploader_request_file}")
      log_finish "create_upload_request"
    end

    def sync_upload(s3_params, file_name, type = "") # rubocop:todo Metrics/AbcSize
      log_start "sync_upload"
      curr_time = (Time.now.to_f * 1000).to_i
      temp_file = s3_params['is_json'] || s3_params[:is_json] ? "/tmp/#{@device}_xctest_log_summary_#{curr_time}.log" : "/tmp/#{@device}_xctest_log_#{curr_time}.log"
      BrowserStack::OSUtils.execute("cp #{file_name} #{temp_file}")
      BrowserStack::OSUtils.execute("chown app #{temp_file}")

      framework_tag = "#{@test_framework}_"
      session_id = s3_params[:session_id]
      key = s3_params[:aws_key]
      secret = s3_params[:aws_secret]
      bucket = s3_params[:aws_bucket]
      storage_class = s3_params[:aws_storage_class]
      genre = @genre
      prefix_genre = Utils.get_prefix_genre(genre)
      s3_url = s3_params[:s3_url].to_s
      acl = s3_params['no_acl'] ? "" : "public-read"
      content_type = s3_params['is_json'] || s3_params[:is_json] ? "text/json" : "text/plain"
      region = bucket == "bs-stag" || s3_params[:aws_region] == "us-east-1" ? nil : (s3_params[:aws_region]).to_s

      unless File.exist?(temp_file)
        BrowserStack::Zombie.push_logs("#{type}-upload-request-file-not-found", "File missing", { "session_id" => session_id })
        raise("Cannot find #{temp_file} on disc, Cannot upload.")
      end

      BrowserStack.logger.info("Summary file uploaded to: #{s3_url}")
      file_size = -1
      if File.file?(temp_file)
        file_size = begin
          File.stat(temp_file).size.to_i
        rescue
          -1
        end
      end
      updated_storage_class = Utils.s3_get_storage_class_based_on_obj_size(file_size, storage_class)
      ret, error = Utils.upload_file_to_s3(key, secret, content_type, temp_file, acl, s3_url, session_id, genre, region, 300, {}, {}, updated_storage_class)
      if !ret && !error.empty?
        BrowserStack::Zombie.push_logs("#{prefix_genre}#{framework_tag}#{type}logs-upload-timeout", "Failed to upload: #{ret}", { "session_id" => session_id })
        raise "Error while uploading to S3: #{error}"
      else
        BrowserStack::Zombie.push_logs("#{prefix_genre}#{framework_tag}#{type}logs-upload-done", "success", { "session_id" => session_id })
      end
      log_finish "sync_upload"
    end

    def get_s3_params(filetag, testname = "", aws_params = nil, test_id = nil)
      log_start "get_s3_params"
      if aws_params.nil?
        bucket = @params['devicelogs_aws_bucket']
        aws_region = @params['devicelogs_aws_region']
        region = bucket == "bs-stag" || @params['devicelogs_aws_region'] == "us-east-1" ? "s3" : "s3-#{@params['devicelogs_aws_region']}"
        s3url = ""
        key = @params['devicelogs_aws_key']
        secret = @params['devicelogs_aws_secret']
        aws_storage_class = @params['devicelogs_aws_storage_class']
      else
        bucket = aws_params['bucket']
        aws_region = aws_params['region']
        region = bucket == "bs-stag" || aws_params['region'] == "us-east-1" ? "s3" : "s3-#{aws_params['region']}"
        s3url = ""
        key = aws_params['key']
        secret = aws_params['secret']
        aws_storage_class = aws_params['storage_class']
      end
      test_id ||= @session_id

      unless testname.empty? || @test_framework == "fluttertest"
        id = get_md5(testname)
        test_id = "#{@session_id}#{id}"
      end
      s3url = "https://#{region}.amazonaws.com/#{bucket}/#{@build_id}/#{test_id}/#{test_id}-#{filetag}"

      params = {
        session_id: test_id,
        aws_key: key,
        aws_secret: secret,
        aws_bucket: bucket,
        aws_region: aws_region,
        aws_storage_class: aws_storage_class,
        s3_url: s3url.to_s,
        no_acl: true,
        is_json: false
      }
      log_finish "get_s3_params"
      params
    end

    def capture_har_file(testname)
      s3_params = get_s3_params("har-logs.txt", testname)
      harfile = @mitmproxy.capture_har_for_framework
      BrowserStack.logger.info("Session Id: #{@session_id}, Harfile Generated")
      create_upload_request(s3_params, harfile, "network")
      PrivoxyManager.change_port(@device, @device_config, false)
    end

    def mitm_restart(testname, retry_count = 0, current_privoxy_port = 0, new_privoxy_port = 0)
      if retry_count < MAX_MITM_RESTART_RETRY_COUNT
        BrowserStack.logger.info("Session Id: #{@session_id}, MITM Restart Params: Retry Count #{retry_count}, current_privoxy_port: #{current_privoxy_port}, new_privoxy_port: #{new_privoxy_port}")
        MitmProxy.stop_proxy(@device, @session_id, "xcuitest")
        # Do not capture har file if already captured. If the ports are 0 in subsequent retries means, stop_proxy raised exceptions
        current_privoxy_port, new_privoxy_port = capture_har_file(testname) unless current_privoxy_port > 0 && new_privoxy_port > 0
        BrowserStack.logger.info("Session Id: #{@session_id}, current_privoxy_port: #{current_privoxy_port}, new_privoxy_port: #{new_privoxy_port}")
        @mitmproxy.boot(current_privoxy_port, new_privoxy_port)
      end
      retry_count
    rescue => e
      lsof_current_privoxy_port_output = `lsof -i:#{current_privoxy_port} | tail`
      lsof_new_privoxy_port_output = `lsof -i:#{new_privoxy_port} | tail`
      BrowserStack.logger.info("Session Id: #{@session_id}, lsof current privoxy port output: #{lsof_current_privoxy_port_output}, lsof new privoxy port output: #{lsof_new_privoxy_port_output}")
      sleep_time = retry_count * 2
      BrowserStack.logger.error("Session Id: #{@session_id}, Retry_count: #{retry_count}, Retrying in: #{sleep_time} sec, Exception in Mitm Boot: #{e.message} backtrace: #{e.backtrace.join("\n")}")
      sleep sleep_time
      retry_count += 1
      if retry_count == MAX_MITM_RESTART_RETRY_COUNT
        BrowserStack::Zombie.push_logs("xcui-mitm-restart-error", "error", { "session_id" => @session_id, "error" => "Mitm Restart Failure retry_count: #{retry_count}" })
        raise e
      end

      mitm_restart(testname, retry_count, current_privoxy_port, new_privoxy_port)
    end

    def upload_percy_logs
      s3_params = get_s3_params("percy-logs.txt")
      s3_params['no_acl'] = false
      session_percy_logs = @app_percy_session.cli_manager.cli_log_file_path(@params['automate_session_id'])
      Utils.create_upload_request(session_percy_logs, "percy", @device, s3_params, @server_config, @test_framework, @genre)
      BrowserStack.logger.info("Percy Logs upload request created")
    rescue Exception => e
      BrowserStack.logger.error("Exception in uploading percy logs #{e.message}.")
    end

    def upload_logfiles(testname)
      log_start "upload_logfiles"
      if devicelogs_enabled?
        s3_params = get_s3_params("device-logs.txt", testname)
        session_device_logs = BrowserStack::DeviceLogger.app_device_logs_file(@device)
        Utils.create_upload_request(session_device_logs, "device", @device, s3_params, @server_config, @test_framework, @genre)
        BrowserStack.logger.info("Device Logs upload request created")
      end

      # Restart mitmproxy and capture network logs
      # Only if networkLogs is enabled.
      if networklogs_enabled?
        if testname.empty?
          s3_params = get_s3_params("har-logs.txt", testname)
          harfile = @mitmproxy.harfile
          BrowserStack.logger.info("Session Id: #{@session_id}, Uploading harfile from: #{harfile}")
          create_upload_request(s3_params, harfile, "network")
        else
          mitm_restart_retries = mitm_restart(testname)
          BrowserStack::Zombie.push_logs("xcui-mitm-restart-error", "error", { "session_id" => @session_id, "error" => "Mitm Restart Failure retry_count: #{mitm_restart_retries}" }) if mitm_restart_retries > 0
        end
      end

      if @params['execution_flow'] != "xctestrun"
        begin
          log_start "capturing_raw_logs_and_screenshots"
          s3_params_screenshot = get_s3_params("screenshot", testname)
          s3_params_raw_logs = get_s3_params("raw-logs.json", testname)
          XCUITestSummariesParser.parse_and_upload(@derived_data_path, upload_debug_screenshots?, s3_params_screenshot, s3_params_raw_logs, @server_config, @device)
          log_finish "capturing_raw_logs_and_screenshots"
        rescue Exception => e
          BrowserStack.logger.error("Exception in logs parsing #{e.message} backtrace: #{e.backtrace.join("\n")}")
        end
      end

      s3_params = get_s3_params("instrumentation-logs.txt", testname)
      create_upload_request(s3_params, @instrumentation_file, "instru")
      BrowserStack.logger.info("instrumentation_file upload request created")
      FileUtils.rm_rf @instrumentation_file

      log_finish "upload_logfiles"
    end

    def fetch_and_upload_crash_logs(testname)
      log_start "fetch_and_upload_crash_logs"
      total_crash_count = 0

      begin
        test_id = testname.empty? ? @session_id : "#{@session_id}#{get_md5(testname)}"
        crash_logs_feature_usage, num_crash_reports = process_crash_logs(testname, test_id)
        upload_crash_logs(test_id, testname) if num_crash_reports > 0

        total_crash_count += num_crash_reports

        unless crash_logs_feature_usage["crash_logs"].nil?
          @test_meta_info[:crash_logs] ||= {}
          @test_meta_info[:crash_logs][testname] = crash_logs_feature_usage["crash_logs"]
        end

        if File.exist?(@callback_file)
          callback_hash = JSON.parse(File.read(@callback_file))
          callback_hash["crash_logs"] ||= 0
          callback_hash["crash_logs"] += num_crash_reports
          Utils.write_to_file(@callback_file, callback_hash.to_json)
        end
      rescue => e
        BrowserStack.logger.error("Error uploading crash logs: #{e.message}\n#{e.backtrace.join("\n")}")
        BrowserStack::Zombie.push_logs("xcuitest-crash-logs-upload-error", e.message[0, 150], { "session_id" => @session_id, "test_id" => test_id })
      end
      log_finish "fetch_and_upload_crash_logs"

      num_crash_reports
    end

    def process_crash_logs(testname, test_id)
      BrowserStack.logger.info("Crash Report Processing")
      crash_reports_block_start_time = Time.now.to_f
      num_crash_reports = 0
      feature_usage = {}
      crash_reports_dir = Utils.get_crash_reports_dir_path(@device)
      begin
        begin
          num_crash_reports, app_binary_name = DeviceManager.crash_reports(@device, @session_id, test_id)
          feature_usage["crashLogs"] = { success: "true", exception: "", num_crash_reports: num_crash_reports }
        rescue => e
          feature_usage["crashLogs"] = { success: "false", exception: e.message }
          BrowserStack.logger.error("Error while fetching crash logs Error: #{e.message}")
          kind = e.message.include?("App not found") ? "idevicecrashreport-app-not-found" : "idevicecrashreport-fetch-failed"
          BrowserStack::Zombie.push_logs(kind, e.message, { "device" => @device, "session_id" => @session_id })
        end
        BrowserStack.logger.info "Number of crash reports for test #{testname}: #{num_crash_reports}"

        [feature_usage, num_crash_reports]
      rescue => e
        BrowserStack.logger.error("Error while processing crash logs Error: #{e.message}")
        feature_usage["crashLogs"] = { success: "false", exception: e.message }
        [feature_usage, num_crash_reports]
      ensure
        FileUtils.rm_rf(crash_reports_dir)

        crash_reports_block_total_time = (Time.now.to_f - crash_reports_block_start_time).to_i
        BrowserStack.logger.info("crash_reports_block_total_time :#{crash_reports_block_total_time}")
      end
    end

    def upload_crash_logs(test_id, testname)
      crash_report_path = "/tmp/crash_report_#{test_id}.zip"
      s3_params = get_s3_params("crash_reports.zip", testname)

      Utils.create_upload_request(crash_report_path, "crash-report", @device, s3_params, @server_config, @test_framework, @genre)
    end

    def upload_result_bundle
      aws_params = {
        'bucket' => @params["xcresultbundle_aws_bucket"],
        'region' => @params["xcresultbundle_aws_region"],
        'key' => @params["xcresultbundle_aws_key"],
        'secret' => @params["xcresultbundle_aws_secret"]
      }
      return unless merge_result_bundles

      s3_params = get_s3_params("result-bundle.xcresult.zip", "", aws_params)

      Utils.create_upload_request(@result_bundle_zip.to_s, "result-bundle", @device, s3_params, @server_config, @test_framework, @genre)
    end

    def merge_result_bundles
      output, status = BrowserStack::OSUtils.execute("ls #{@result_bundle_directory}/*/* | egrep '.xcresult$'", true)
      BrowserStack.logger.info("List of all xcresult file #{output} and exit_code: #{status}")
      @xcresult_count = output.split("\n").length
      if @xcresult_count == 0
        BrowserStack::Zombie.push_logs("xcresult-file-missing", "error", { "session_id" => @session_id, "message" => "No xcresult file found in #{@result_bundle_directory}" })
        return false
      end
      if @xcresult_count > 1
        merge_cmd = "gtimeout -s KILL 120 xcrun xcresulttool merge #{@result_bundle_directory}/*/*/*.xcresult --output-path #{@result_bundle_directory}/result-bundle.xcresult 2>&1"
        output, status = BrowserStack::OSUtils.execute(merge_cmd, true)
        BrowserStack.logger.info("[merge_result_bundles] xcresulttool merge output: #{output} and exit_code: #{status}")
        # Successful output: "[v3] Merged to: /tmp/00008030-001044C80EF9802E_xcresult_bundle/result-bundle.xcresult"
        unless output == "[v3] Merged to: #{@result_bundle_directory}/result-bundle.xcresult\n" && status == 0
          BrowserStack::Zombie.push_logs("xcuitest-resultbundle-error", "exit_code: #{status} & output: #{output}", { "session_id" => @session_id, "url" => "merge-failed" })
          return false
        end
      else
        output, status = BrowserStack::OSUtils.execute("cp -R #{@result_bundle_directory}/*/*/*.xcresult #{@result_bundle_directory}/result-bundle.xcresult", true)
        BrowserStack.logger.info("[merge_result_bundles] cp -R output: #{output} and exit_code: #{status}")
      end

      zip_cmd = "cd #{@result_bundle_directory}; gtimeout -s KILL 120 zip -rq #{@result_bundle_zip} result-bundle.xcresult 2>&1"
      output, status = BrowserStack::OSUtils.execute(zip_cmd, true)
      BrowserStack.logger.info("[merge_result_bundles] zip_cmd output: #{output} and exit_code: #{status}")
      BrowserStack::Zombie.push_logs("xcuitest-resultbundle-error", "exit_code: #{status} & output: #{output}", { "session_id" => @session_id, "url" => "zip-failed" }) if status != 0
      status == 0
    end

    def upload_summary
      # Session Summary File v2
      s3_params = get_s3_params("summary-v2.json")
      s3_params['is_json'] = true
      flatten_summary_v2
      sync_upload(s3_params, @summary_file_v2, "summary")

      s3_params = get_s3_params("summary.json")
      s3_params['is_json'] = true
      # create_upload_request(s3_params, @summary_file, "summary")
      # uploading summary inline
      sync_upload(s3_params, @summary_file, "summary") if @summary_file && File.exists?(@summary_file)

      BrowserStack.logger.info("summary.json upload request created")
    end

    def flatten_summary_v2
      summary_data_v2 = get_summary_data(@summary_file_v2)

      if summary_data_v2['classes']
        summary_data_v2['classes'] = summary_data_v2['classes'].values

        summary_data_v2['classes'].each do |class_object|
          class_object['tests'] = class_object['tests'].values
        end
      end

      Utils.write_to_file(@summary_file_v2, summary_data_v2.to_json)
    end

    def set_v2_video_params
      begin
        @params['video_params_v2'] = @params['video_params_v2'].to_json unless @params['video_params_v2'].nil?
      rescue
        {}
      end
      @is_v2_video_recording = v2_video_enabled?
      device_state.write_with_lock_to_xctest_v2_video_file(@params['video_params_v2']) if @is_v2_video_recording
    end

    def initialize_params
      @test_framework = @params['test_framework']
      @build_id = @params['build_id']
      @session_id = @params['automate_session_id']
      @device_os_version = @device_config['device_version'].to_f
      @downloaded_app_path = @params['downloaded_app_path'] || ""

      @downloaded_test_app_path = @params['downloaded_test_app_path']
    end

    def setup_params
      log_start "setup_params"
      IdeviceUtils.configure(Configuration.conf)
      initialize_params
      @devicelog_file = "/var/log/browserstack/app_log_#{@device}.log"
      @logs_s3url = "https://#{@params['rails_api_host']}/app-automate/#{@test_framework}/builds/#{@params['build_id']}/sessions/tests/"
      @video_s3url = get_video_s3url
      @xctest_xmlfile = xctest_xmlfile_path
      if result_bundle_enabled?
        @result_bundle_directory = xcresult_bundle_directory
        @result_bundle_zip = xcresult_bundle_zip_file
      end
      @product_module_name_file = product_module_name_file_path
      @product_module_names = {}
      @total_test_count = 0
      read_product_module_name_file
      @testsuite_files = []
      xctests = @params['xctests']
      xctests.each do |xctest|
        @testsuite_files.push("/tmp/#{@device}_xctest_#{xctest}")
      end
      set_v2_video_params
      @vid_capturer = IosVidCapturer.new(@device, @session_id, @server_config, nil, @genre)
      setup_proxies if boot_mitmproxy?
      @session_start_time = Time.now
      @total_session_time = @params['session_time'].to_i

      # Stores the errors obtained for each test in a cumulative fashion.
      @test_meta_info = if @params['execution_flow'] == "xctestrun" && !xcuitest_sri_flow?
                          {}
                        else
                          {
                            "retries" => 0,
                            "retry_time" => 0
                          }
                        end

      log_finish "setup_params"
    end

    def setup_proxies(retries = 2)
      return if retries < 0 # Redundant condition to rescue block, net 3 retries

      current_privoxy_port, new_privoxy_port = PrivoxyManager.change_port(@device, @device_config)
      @mitmproxy = MitmProxy.new(@device, @params, @server_config)
      @mitmproxy.boot(current_privoxy_port, new_privoxy_port)
    rescue => e
      BrowserStack.logger.info("[#{@session_id}] Setting up proxy failed with error: #{e.message} - #{e.backtrace}")
      BrowserStack::Zombie.push_logs("xcui-proxy-restart-error", "error", { "session_id" => @session_id, "error" => "Proxy Setup Failure retry_count: #{retries}. Exception: #{e.message}" })

      if retries > 0
        sleep retries
        setup_proxies(retries - 1)
      else
        raise e
      end
    end

    def get_video_s3url
      bucket = @params['video_aws_bucket']
      region = bucket == "bs-stag" || @params['video_aws_region'] == "us-east-1" ? "s3" : "s3-#{@params['video_aws_region']}"
      "https://#{@params['rails_host']}/s3-upload/#{bucket}/#{region}/#{@params['automate_session_id']}/#{@params['video_file']}.mp4"
    end

    def get_video_duration(suite_v2_video_start_time = Time.now.utc)
      BrowserStack.logger.info("[get_video_duration] is_v2_video_recording: #{@is_v2_video_recording} and suite_v2_video_start_time: #{suite_v2_video_start_time} and #{(Time.now.utc - suite_v2_video_start_time).to_i} seconds")
      if @is_v2_video_recording
        (Time.now.utc - suite_v2_video_start_time).to_i
      else
        @vid_capturer.get_video_duration
      end
    end

    def get_test_expire_time
      current_time = Time.now
      timeout_time = @total_session_time - (current_time - @session_start_time).to_i
      timeout if timeout_time.to_i <= 0
      timeout_time
    end

    def get_build_pusher_message
      messages = []
      combined_test_list = []
      message = {
        'build_id' => @build_id,
        'device' => @params['user_device_name'],
        'session_id' => @session_id,
        'testlist' => []
      }

      # We do not want to push "temporary" test list to dashboard/elastic-search
      if @params["test_framework"] == "fluttertest"
        messages << message.to_json
        return messages
      end

      @testsuite_files.each do |testfile|
        testlist = get_testlist(testfile)
        testlist.each do |full_testname|
          test_split = full_testname.split("/")
          test_name = test_split.pop
          classname = @is_duplicate ? test_split.join("/") : test_split.pop # We name module + class name as unqiue string if duplicate class + test are present
          test_id = "#{@session_id}#{get_md5(full_testname)}"
          combined_test_list << {
            'test_id' => test_id,
            'name' => test_name,
            'classname' => classname,
            'status' => DEFAULT_TEST_STATUS.downcase
          }
        end
      end

      chunked_test_list = combined_test_list.each_slice(30).to_a
      chunked_test_list.each do |test_list|
        message['testlist'] = test_list
        messages << message.to_json
      end
      messages
    end

    def start_timeout_manager
      log_start "start_timeout_manager"

      FileUtils.touch(@instrumentation_file)
      xctest_manager = AppAutomateFrameworks::XCTestTimeoutManager.new(
        @device,
        @instrumentation_file,
        logger: BrowserStack.logger,
        logger_params: BrowserStack.logger.params
      )

      xctest_manager.start(@params["idle_timeout"])
      log_finish "start_timeout_manager"
    end

    def stop_timeout_manager
      log_start "stop_timeout_manager"

      xctest_manager = AppAutomateFrameworks::XCTestTimeoutManager.new(
        @device,
        @instrumentation_file,
        logger: BrowserStack.logger,
        logger_params: BrowserStack.logger.params
      )

      xctest_manager.stop
      log_finish "stop_timeout_manager"
    end

    def initialize_logs_stability_metrics
      @logs_stability_file = Utils.get_logs_stability_file_path(@session_id)
      FileUtils.touch(@logs_stability_file)
      Utils.write_to_file(@logs_stability_file, {
        "total_tests" => @total_test_count,
        "instrumentationLogs" => {
          "uploaded" => 0,
          "failed" => 0
        },
        "networkLogs" => {
          "uploaded" => 0,
          "failed" => 0
        },
        "deviceLogs" => {
          "uploaded" => 0,
          "failed" => 0
        }
      }.to_json)
    end

    def xcode_retry_flags
      retry_params = @params["retry_params"]
      retry_flags = ""

      return retry_flags if retry_params.nil?

      case retry_params["relaunch_enabled"].to_s
      when "true"
        retry_flags += " -test-repetition-relaunch-enabled YES "
      when "false"
        retry_flags += " -test-repetition-relaunch-enabled NO "
      end
      retry_flags += " -retry-tests-on-failure " if retry_params["retry_on_failure"].to_s == "true"
      retry_flags += " -test-iterations #{retry_params['test_iterations']} " if retry_params["test_iterations"]
      retry_flags
    end

    def handle_test_for_logs_stability_metrics(testname, test_count)
      return unless File.exists?(@logs_stability_file)

      # Specifically For instruLogs stability metrics, we need to decrement total count in @logs_stability_file
      logs_stability_file_data = ""
      logs_stability_file_schema = Utils.logs_stability_file_schema
      File.open(@logs_stability_file, File::RDWR, 0o644) do |f|
        f.flock(File::LOCK_EX)
        logs_stability_file_data = JSON.parse(File.read(@logs_stability_file))
        logs_stability_file_schema.each_value do |metric_log|
          logs_stability_file_data[metric_log]["uploaded"] = logs_stability_file_data[metric_log]["uploaded"] + test_count
        end
        f.rewind
        f.write(logs_stability_file_data.to_json)
        f.flush
        f.truncate(f.pos)
      end

      BrowserStack.logger.info "[#{testname}] Incrementing uploaded count by #{test_count}. Adjusting logs stability data to #{logs_stability_file_data}."

      # Try pushing to eds if this is the last test
      logs_stability_file_schema.each do |metric_type, metric_log|
        total_tests_so_far = logs_stability_file_data[metric_log]["uploaded"] + logs_stability_file_data[metric_log]["failed"]
        Utils.push_stability_metrics(@session_id, testname, logs_stability_file_data, metric_type, logs_stability_file_data["total_tests"], total_tests_so_far)
      end
    end

    def run_xctest(test, test_num = 0, retry_count = 0)
      log_start "run_xctest_#{retry_count}"

      result_bundle_dir = "#{@result_bundle_directory}/#{test}_#{Time.now.utc.to_i}" if result_bundle_enabled?
      generate_xctestrun_xml(@params["test_framework"], test[0, test.index("/")])
      @derived_data_path = File.join("/tmp", "#{@device}_xcuitest_derived_data_#{test_num}")
      timeout = get_test_expire_time

      start_timeout_manager
      xctestrun_cmd = "gtimeout -s KILL #{timeout} xcodebuild -xctestrun #{@xctest_xmlfile} -destination \"id=#{@device}\" test-without-building -derivedDataPath \"#{@derived_data_path}\""
      if @params["test_framework"] != "fluttertest"
        xctestrun_cmd += " -only-testing:\"#{test}\" "
        xctestrun_cmd += xcode_retry_flags
      end
      xctestrun_cmd += " -resultBundlePath \"#{result_bundle_dir}\" -resultBundleVersion 3" if result_bundle_enabled?
      xctestrun_cmd += " > #{@instrumentation_file} 2>&1"

      xcodebuild_process_start_time = Time.now.utc
      output, exit_status = BrowserStack::OSUtils.execute(xctestrun_cmd, true)
      xcodebuild_process_stop_time = Time.now.utc
      @xcodebuild_process_time = (xcodebuild_process_stop_time - xcodebuild_process_start_time).to_f.round(3)
      stop_timeout_manager

      log_finish "run_xctest_#{retry_count}"

      retry_count += 1
      if should_retry_test?(test, retry_count, exit_status)
        @test_meta_info["retries"] += 1
        @test_meta_info["retry_time"] = (@test_meta_info["retry_time"] + @xcodebuild_process_time).round(2)

        if result_bundle_dir
          FileUtils.rm_rf(result_bundle_dir)
          FileUtils.rm_rf("#{result_bundle_dir}.xcresult")
        end

        # adding sleep so that the device gets time to recover from
        # bad state to reduce possibility of failure
        # in the worst case when 3 retries (and 1 try) happens we sleep
        # for total 1 + 2 + 3 seconds
        sleep retry_count
        exit_status = run_xctest(test, test_num, retry_count)
      end

      exit_status
    end

    def should_retry_test?(testname, retry_count, xctestrun_cmd_exit_status=nil)
      return false if retry_count > MAX_TEST_RETRY_COUNT || session_timedout?

      if Gem::Version.new(@device_os_version) >= Gem::Version.new('16')
        cursor = @instru_log_file_size || 0
        instru_file = File.open(@instrumentation_file, 'r')
        instru_file.seek(cursor, IO::SEEK_SET)
        instru_logs = instru_file.readlines
        test_id = testname.empty? ? @session_id : "#{@session_id}#{get_md5(testname)}"

        # Hard Retry when the error logline appears but no test case has started
        if instru_logs.grep(XCTEST_TEST_MATCH_REGEX).count == 0
          if instru_logs.grep(/No installed application found for launch session/i).count > 0
            BrowserStack::Zombie.push_logs("xcodebuild-retry-app-not-installed", "Retry Count: #{retry_count}, Time Spent: #{@xcodebuild_process_time}", { "session_id" => test_id })
            return true
          end

          SYSTEM_LEVEL_PATTERNS.each_value do |regex|
            match = instru_logs.grep(regex).count > 0
            next unless match

            BrowserStack.logger.info "System Level Pattern matched: #{regex}"
            BrowserStack::Zombie.push_logs("xcodebuild-retry-platform-issue", "Retry Count: #{retry_count}, Time Spent: #{@xcodebuild_process_time}", { "session_id" => test_id, "data" => regex.to_s })
            return true
          end
          # retry if exit status is timedout and no test case ran
          if xctestrun_cmd_exit_status == TIMEDOUT_EXIT_CODE
            BrowserStack::Zombie.push_logs(
              "xcodebuild-retry-platform-issue",
              "Retry Count: #{retry_count}, Time Spent: #{@xcodebuild_process_time}",
              {
                "session_id" => test_id,
                "data" => "xcode command timed out without any test case started."
              }
            )
            return true
          end
        end
      end

      false
    end

    def notify_test_name_info_to_pusher(testname)
      if @params["test_framework"] != "fluttertest"
        pusher_message = get_test_pusher_message(testname)
        notify_pusher("update_build", pusher_message.to_json)
      end
    end

    def run_xctestsuite # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
      log_start "run_xctestsuite"
      suite_start_time = Time.now.utc

      VideoRecManager.new(@device, @params, @params['wda_port']).start_rec if video_enabled?
      suite_v2_video_start_time = Time.now.utc

      ensure_wda_not_running if Utils.is_wda_kill_flag_enabled?(@params) && video_enabled? && @is_v2_video_recording

      initialize_logs_stability_metrics

      messages = get_build_pusher_message

      log_start "setup_build_pusher_event"
      messages.each do |message|
        notify_pusher("setup_build", message)
        # Added sleep here to send chunked messages to pusher with gaps:
        # https://github.com/browserstack/realmobile/pull/1141
        sleep 0.5
      end
      log_finish "setup_build_pusher_event"

      test_num = 0

      @testsuite_files.each do |testfile|
        testlist = get_testlist(testfile)
        testlist.each do |testname|
          break if session_timedout?

          reset_logfiles
          update_test_status_to_running(testname)
          notify_test_name_info_to_pusher(testname)
          test_start_time = Time.now.utc
          @instrumentation_file = "/tmp/#{@device}_#{@test_framework}_instrumentation_#{test_start_time.to_i}.log" #check for instrumentation elsewhere
          video_start_time = get_video_duration(suite_v2_video_start_time)
          test_num += 1
          exit_status = run_xctest(testname, test_num)
          video_stop_time = get_video_duration(suite_v2_video_start_time)
          video_url = video_enabled? ? "#{@video_s3url}#t=#{video_start_time},#{video_stop_time}" : ""

          test_stop_time = Time.now.utc
          test_duration = test_stop_time - test_start_time

          parse_instru_logs(testname)
          num_crash_reports = fetch_and_upload_crash_logs(testname)
          test_bstack_reason = get_bstack_reason(exit_status, testname)
          update_test_summary(testname, test_start_time, test_duration, video_url, test_bstack_reason, num_crash_reports)

          upload_logfiles(testname)

          notify_test_name_info_to_pusher(testname)
        end
        break if session_timedout?
      end
      upload_percy_logs if @params['app_percy']
      update_timeout_tests if session_timedout?
      suite_stop_time = Time.now.utc
      update_session_duration(suite_start_time, suite_stop_time)

      summary_data_v2 = get_summary_data(@summary_file_v2)
      test_status = summary_data_v2['test_summary']

      callback_hash = JSON.parse(File.read(@callback_file))
      callback_hash['test_status'] = test_status
      callback_hash['test_status'].delete('total')
      Utils.write_to_file(@callback_file, callback_hash.to_json)

      # FAILED    -> failed
      # SUCCESS   -> passed
      # IGNORED   -> skipped
      # TIMEDOUT  -> timedout
      failed_count = test_status['failed']
      passed_count = test_status['passed']
      skipped_count = test_status['skipped']
      timedout_count = test_status['timedout']
      BrowserStack::Zombie.push_logs("app_automation_session_stats", '', { "sessionid" => @session_id, "test_failed" => failed_count, "test_success" => passed_count, "test_ignored" => skipped_count, "test_timedout" => timedout_count })

      # This handles those tests for which upload_logfiles was not triggered
      pending_tests = (@total_test_count - test_num)
      handle_test_for_logs_stability_metrics(@session_id, pending_tests) if pending_tests > 0

      log_finish "run_xctestsuite"
    end

    def get_md5(test_input)
      md5_id = Digest::MD5.hexdigest test_input
      md5_id[0..7]
    end

    def update_session_duration(s, e)
      log_start "update_session_duration"
      summary_data = get_summary_data(@summary_file)
      summary_data['duration'] = (e - s)
      Utils.write_to_file(@summary_file, summary_data.to_json)

      # Session Summary File v2
      summary_data_v2 = get_summary_data(@summary_file_v2)
      summary_data_v2['duration'] = (e - s)
      Utils.write_to_file(@summary_file_v2, summary_data_v2.to_json)
      log_finish "update_session_duration"
    end

    def devicelogs_enabled?
      @params['deviceLogs'].to_s == 'true'
    end

    def boot_mitmproxy?
      networklogs_enabled? || acceptInsecureCerts_enabled? || live_self_signed_certs_enabled?
    end

    def networklogs_enabled?
      @params['networkLogs'].to_s == 'true'
    end

    def upload_debug_screenshots?
      @params['upload_debug_screenshots'].to_s == 'true'
    end

    def live_self_signed_certs_enabled?
      @params['live_self_signed_certs'] == 'true'
    end

    def acceptInsecureCerts_enabled? # rubocop:todo Naming/MethodName
      @params['acceptInsecureCerts'].to_s == 'true'
    end

    def video_enabled?
      @params['video'].to_s == "true"
    end

    def result_bundle_enabled?
      @params['enableResultBundle'].to_s.downcase == "true"
    end

    def bstack_retry_on_sri_enabled?
      @params['retryTestsOnBrowserstack'].to_s.downcase == "true"
    end

    def session_timedout?
      device_state.xctest_session_timedout_file_present?
    end

    # Used in xctestrun flow
    def test_expired?(session_start_time = @session_start_time, total_session_time = @total_session_time)
      current_time = Time.now
      (current_time - session_start_time).to_i > total_session_time
    end

    def is_test_timedout?(exit_status)
      result = (exit_status.to_i == TIMEDOUT_EXIT_CODE)
      result ||= File.exists?("#{@instrumentation_file}_timedout")

      begin
        FileUtils.rm("#{@instrumentation_file}_timedout")
      rescue
        nil
      end
      result
    end

    def is_test_errored?(exit_status)
      @test_errored || UNEXPECTED_XCODEBUILD_EXIT_CODES.include?(exit_status.to_i)
    end

    def get_bstack_reason(exit_status, testname)
      return "TIMEDOUT" if is_test_timedout?(exit_status)
      return "ERROR" if is_test_errored?(exit_status)

      test_id = "#{@session_id}#{get_md5(testname)}"
      instru_logs = File.readlines(@instrumentation_file)

      ""
    end

    def parse_flutter_xctest_output
      test_data = []
      test_object = {}
      log_start "parse_flutter_xctest_output"
      instru_file = File.new(@instrumentation_file)
      instru_file.each do |instru_line|
        next unless (test_match_meta = instru_line.match(XCTEST_TEST_MATCH_REGEX))

        #Test Case '-[RunnerTests testGetTextLocalTest]' started.
        #Test Case '-[RunnerTests testGetTextLocalTest]' failed (0.031 seconds).
        #Test Case '-[RunnerTests testGetOtherTests]' started.
        #Test Case '-[RunnerTests testGetOtherTests]' failed (0.002 seconds).

        test_name, test_status, test_duration = test_match_meta.captures
        # For eg: test_name = RunnerTests testGetTextLocalTest, test_status = failed,
        #         test_duration = 0.031
        product_module_name, original_test_name = test_name.split(" ")

        case test_status
        when "started"
          test_object[original_test_name] = ""
        when "passed"
          test_object[original_test_name] = test_status
          test_data << [original_test_name, 'SUCCESS']
        when "failed"
          test_object[original_test_name] = test_status
          test_data << [original_test_name, 'FAILED']
        when "skipped"
          test_object[original_test_name] = test_status
          test_data << [original_test_name, 'IGNORED']
        else
          test_data << [original_test_name, 'FAILED']
          BrowserStack.logger.error "unknown status detected for session_id: #{@session_id} test_status: #{test_status}"
        end
      end
      BrowserStack.logger.info "test_object: #{test_object}"
      log_finish "parse_flutter_xctest_output"
      test_data
    end

    def instrument_test_execution_performance(test_id, xcodebuild_process_time, device_test_execution_time = nil)
      blocked_devices = DeviceManager.blocked_devices_count
      system_resources = BrowserStack::OSUtils.get_system_resources
      perf_data = {
        "testid" => test_id,
        "device_test_execution_time" => device_test_execution_time,
        "xcodebuild_process_time" => xcodebuild_process_time,
        "num_blocked_devices" => blocked_devices,
        "system_resources" => system_resources
      }
      # TODO: Remove this temporary metric once we have setup instrumentation at test_sessions_part level
      BrowserStack::Zombie.push_logs("xcodebuild-execution-stats", '', { "session_id" => @session_id, "data" => perf_data })
    rescue => e
      BrowserStack.logger.error("Error in pushing logs for execution time duration: #{e.message}")
    end

    def parse_xctest_output(testname) # rubocop:todo Metrics/MethodLength
      log_start "parse_xctest_output"

      test_completion_mark = begin
        BrowserStack::OSUtils.execute("awk '/Test Case.*#{testname}.*seconds/{ print $0 }' #{@instrumentation_file}")
      rescue
        ""
      end

      passed = begin
        test_completion_mark.match(/passed/)[0]
      rescue
        ""
      end

      succeeded = begin
        BrowserStack::OSUtils.execute("grep 'TEST EXECUTE SUCCEEDED' #{@instrumentation_file}").match(/SUCCEEDED/)[0]
      rescue
        ""
      end
      result = (passed == "passed") || (succeeded == "SUCCEEDED") ? "SUCCESS" : "FAILED"

      skipped = begin
        BrowserStack::OSUtils.execute("grep 'Executed 0 tests, with 0 failures' #{@instrumentation_file}").match(/0 tests, with 0 failures/)[0]
      rescue
        ""
      end
      skipped_v2 = begin
        test_completion_mark.match(/skipped/)[0]
      rescue
        ""
      end

      result = skipped == "0 tests, with 0 failures" || skipped_v2 == "skipped" ? "IGNORED" : result
      failed = begin
        BrowserStack::OSUtils.execute("grep 'Executed [0-9]\\+ \\(test\\|tests\\), with [1-9]\\+ failure' #{@instrumentation_file}").match(/[0-9]+ (test|tests), with [1-9]+ (failure|failures)/)[0]
      rescue
        ""
      end
      result = failed.match?("[0-9]+ (test|tests), with [1-9]+ failure") ? "FAILED" : result

      # update the result for xctestrun
      if !@params["retry_params"].nil? && result == 'FAILED'
        updated_result = update_status_for_xcretry(testname, passed, succeeded)
        BrowserStack::Zombie.push_logs("xcretry-status-update", '', { "session_id" => @session_id, "data" => { "testname" => testname, "original_result" => result, "updated_result" => updated_result } })
        result = updated_result
      end

      duration = begin
        output.match(/\(.*\)/)[0].match('(\d+\.\d+)')[0]
      rescue
        ""
      end

      log_finish "parse_xctest_output"
      [result, duration]
    end

    def parse_instru_logs(testname) # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
      log_start "parse_instru_logs"

      session_or_test_id = "#{@session_id}#{get_md5(testname)}" || @session_id
      @any_test_case_found = false
      @test_errored = false
      device_test_execution_time_array = nil

      cursor = @instru_log_file_size || 0
      instru_file = File.open(@instrumentation_file, 'r')
      instru_file.seek(cursor, IO::SEEK_SET)
      instru_file.readlines.each do |instru_line|
        # TODO: Calculate device_test_execution_line for multiple configuations
        device_test_execution_line = instru_line.match(/Executed [0-9]+ (test|tests), with .+ failure.+/)
        device_test_execution_time_array = device_test_execution_line.to_s.scan(/[0-9]+.[0-9]+/) if device_test_execution_line

        @any_test_case_found ||= !instru_line.match(/Test Case/).nil?

        runner_exit = instru_line.match(/The test runner exited with code (\d+)/)
        @test_meta_info["runner_exit_#{runner_exit[1]}"] = @test_meta_info["runner_exit_#{runner_exit[1]}"].to_i.succ if runner_exit && runner_exit[1]

        test_crash = instru_line.match(/\(Test crashed with signal ([A-Za-z0-9_-]+)./)
        @test_meta_info["test_crash_#{test_crash[1]}"] = @test_meta_info["test_crash_#{test_crash[1]}"].to_i.succ if test_crash && test_crash[1]

        SYSTEM_LEVEL_PATTERNS.each do |identifier, regex|
          result = instru_line.match(regex)
          next unless result

          @test_errored = true
          @test_meta_info[identifier] = @test_meta_info[identifier].to_i.succ

          begin
            ideviceinfo = IdeviceUtils.ideviceinfo(@device, "ProductType").first
            wda_client = WdaClient.new(@params["wda_port"])
            wda_running = wda_client.running?
            BrowserStack::Zombie.push_logs("xcui-system-error-device-connection", "", {
              "session_id" => @session_id,
              "data" => { "ideviceinfo" => ideviceinfo, "wda_running" => wda_running }
            })
          rescue => e
            BrowserStack.logger.info "Error while checking ideviceinfo & wda status - #{e.message} - #{e.backtrace}"
          end
        end

        NON_SYSTEM_LEVEL_PATTERNS.each do |identifier, regex|
          result = instru_line.match(regex)
          @test_meta_info[identifier] = @test_meta_info[identifier].to_i.succ if result
        end
      end

      device_test_execution_time = device_test_execution_time_array.nil? || device_test_execution_time_array.empty? ? '' : device_test_execution_time_array[-1]
      instrument_test_execution_performance(session_or_test_id, @xcodebuild_process_time, device_test_execution_time)

      log_finish "parse_instru_logs"
    rescue => e
      BrowserStack.logger.info "Error while parsing instru logs. #{e.message} - #{e.backtrace}"
    end

    def update_status_for_xcretry(testname, passed, succeeded)
      log_start "update_status_for_xcretry"
      success_status = 'SUCCESS'
      failed_status = 'FAILED'

      failed = begin
        BrowserStack::OSUtils.execute("grep 'TEST EXECUTE FAILED' #{@instrumentation_file}").match(/FAILED/)[0]
      rescue
        ''
      end

      # Both "FAILED" and "SUCCEEDED" is present in instrumentation logs
      return failed_status if !failed.empty? && !succeeded.empty?

      # Both "FAILED" and "SUCCEEDED" is absent in instrumentation logs
      if succeeded.empty? && failed.empty?
        status = passed.empty? ? failed_status : success_status
        return status
      end

      log_finish "update_status_for_xcretry"
      # Either "FAILED" or "SUCCEEDED" is present in instrumentation logs
      failed.empty? ? success_status : failed_status
    end

    # Updates the test_status at session and class level
    def update_tests_status(summary_data_v2, classname, new_state, old_state)
      unless summary_data_v2.nil?
        class_result = begin
          summary_data_v2['classes'][classname]['tests_summary']
        rescue
          nil
        end
        unless class_result.nil?
          class_result[new_state] += 1
          class_result[old_state] -= 1
        end

        unless summary_data_v2["test_summary"].nil?
          summary_data_v2["test_summary"][new_state] += 1
          summary_data_v2["test_summary"][old_state] -= 1
        end
      end
      summary_data_v2
    end

    def update_flutter_test_summary_v2(classname, start_time, duration, video_url, test_data)
      # Session Summary File v2
      summary_data_v2 = get_summary_data(@summary_file_v2)

      # We name module + class name as unqiue string if duplicate class + test are present
      classname = @is_duplicate ? classname : classname.split('/').pop
      test_status = {
        "total" => 0,
        'passed' => 0,
        'failed' => 0,
        'skipped' => 0,
        'timedout' => 0,
        'error' => 0,
        'running' => 0,
        'queued' => 0
      }

      video_url = (video_url.split('#')[1].split('=')[1]).to_s if video_enabled?
      summary_data_v2['classes'][classname]['tests'] = {}
      total_test_cases = 0

      test_data.each do |test_case_name, test_case_status|
        test_case_status = test_case_status.downcase
        test_case_status = "passed" if test_case_status == "success"

        test_status[test_case_status] += 1
        total_test_cases += 1

        summary_data_v2['classes'][classname]['tests'][test_case_name] = {
          name: test_case_name,
          start_time: Time.at(start_time.to_i),
          status: test_case_status,
          test_id: @session_id + get_md5(test_case_name),
          duration: duration.to_f.round(3),
          video: video_url,
          class: classname
        }
      end

      test_status['total'] = total_test_cases
      summary_data_v2['classes'][classname]['tests_summary'] = test_status
      summary_data_v2['test_summary'] = test_status

      Utils.write_to_file(@summary_file_v2, summary_data_v2.to_json)
    end

    def update_flutter_test_summary(classname, start_time, duration, video_url, test_data)
      test_status = {
        'SUCCESS' => 0,
        'FAILED' => 0,
        'IGNORED' => 0,
        'TIMEDOUT' => 0,
        'ERROR' => 0,
        'RUNNING' => 0,
        'QUEUED' => 0
      }

      unless test_data.empty?
        summary_data = get_summary_data(@summary_file)
        summary_data['test_details'][classname] = {}
        tests_count = 0

        test_data.each do |test_case_name, test_case_status|
          test_obj = default_test(test_case_name)
          tests_count += 1
          test_status[test_case_status] += 1

          summary_data['test_details'][classname][test_case_name] = test_obj

          if test_case_status == "SUCCESS"
            summary_data['test_details'][classname][test_case_name].merge!({
              'duration' => duration.to_f.round(3),
              'device_log' => !devicelogs_enabled? ? "" : "#{@logs_s3url}#{@session_id}/devicelogs",
              'network_log' => !networklogs_enabled? ? "" : "#{@logs_s3url}#{@session_id}/networklogs",
              'instrumentation_log' => "#{@logs_s3url}#{@session_id}/instrumentationlogs",
              'rawlogs' => "#{@logs_s3url}#{@session_id}/rawlogs",
              'video' => video_url,
              'start_time' => Time.at(start_time.to_i)
            })
          end
          summary_data['test_details'][classname][test_case_name].merge!({
            'status' => test_case_status
          })
        end

        summary_data['test_count'] = tests_count
        summary_data['test_status'] = test_status
        Utils.write_to_file(@summary_file, summary_data.to_json)
        update_flutter_test_summary_v2(classname, start_time, duration, video_url, test_data)
      end
    end

    def evaluate_xctest_status(test_bstack_reason, testname)
      result, duration = test_bstack_reason.empty? ? parse_xctest_output(testname) : [test_bstack_reason, ""]

      if test_bstack_reason == "TIMEDOUT" && !session_timedout?
        BrowserStack.logger.info "#{@session_id} Parsing instru logs for test status"
        result, duration = parse_xctest_output(testname)
        test_bstack_reason = ""
      end

      if test_bstack_reason == "ERROR"
        test_completion_mark = begin
          BrowserStack::OSUtils.execute("awk '/Test Case.*#{testname}.*seconds/{ print $0 }' #{@instrumentation_file}")
        rescue
          ""
        end

        BrowserStack.logger.info "#{@session_id} Test Completion mark for errored test: #{test_completion_mark}"

        xctest_status_match = begin
          test_completion_mark.match(/ (passed|failed|skipped) \(.* seconds\)/)
        rescue
          ""
        end

        unless xctest_status_match.to_s == ""
          BrowserStack.logger.info "#{@session_id} Parsing xctest output for error sessions"
          result, duration = parse_xctest_output(testname)
          test_bstack_reason = ""
        end

        if @params["test_params"] && @test_meta_info["testsuite_missing_test_dynamic_test"]
          testparams = JSON.parse(@params["test_params"])
          result = "IGNORED" if testparams["is_dynamic_xcuitest"].to_s == "true"
        end
      end

      [result, duration, test_bstack_reason]
    end

    def update_test_summary(testname, start_time, test_duration, video_url, test_bstack_reason, num_crash_reports = 0) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
      @fully_qualified_testname = testname
      log_start "update_test_summary"
      t_split = testname.split("/")
      testname = t_split.pop
      classname = t_split.join("/")
      result, duration, test_bstack_reason = evaluate_xctest_status(test_bstack_reason, testname)
      duration = duration.to_s == "" ? test_duration : duration
      summary_data = get_summary_data(@summary_file)
      test_object = begin
        summary_data['test_details'][classname][testname]
      rescue
        nil
      end

      test_data = parse_flutter_xctest_output if @params['test_framework'] == "fluttertest"

      if @params['test_framework'] == "fluttertest" && !test_data.empty?
        update_flutter_test_summary(classname, start_time, duration, video_url, test_data)
      else
        unless test_object.nil?
          test_id = test_object['test_id']
          if test_bstack_reason.empty?
            test_object.merge!({
              'status' => result,
              'duration' => duration.to_f.round(3),
              'device_log' => !devicelogs_enabled? ? "" : "#{@logs_s3url}#{test_id}/devicelogs",
              'network_log' => !networklogs_enabled? ? "" : "#{@logs_s3url}#{test_id}/networklogs",
              'instrumentation_log' => "#{@logs_s3url}#{test_id}/instrumentationlogs",
              'rawlogs' => "#{@logs_s3url}#{test_id}/rawlogs",
              'video' => video_url,
              'start_time' => Time.at(start_time.to_i),
              'crash_logs_count' => num_crash_reports,
              'crash_log' => num_crash_reports > 0 ? "#{@logs_s3url}#{test_id}/crashlogs" : ""
            })
          else
            test_object.merge!({
              'status' => result
            })
          end
        end
        summary_data["test_status"][result] += 1
        summary_data["test_status"]['RUNNING'] -= 1
        Utils.write_to_file(@summary_file, summary_data.to_json)

        # Session Summary File v2
        summary_data_v2 = get_summary_data(@summary_file_v2)
        classname = @is_duplicate ? classname : classname.split('/').pop # We name module + class name as unqiue string if duplicate class + test are present
        test_object_v2 = begin
          summary_data_v2['classes'][classname]['tests'][testname]
        rescue
          nil
        end
        video_url = (video_url.split('#')[1].split('=')[1]).to_s if video_enabled?
        result_status = result.downcase
        case result.downcase
        when "success"
          result_status = "passed"
        when "ignored"
          result_status = "skipped"
        end
        unless test_object_v2.nil?
          test_object_v2.merge!({
            'status' => result_status,
            'video' => video_url,
            'start_time' => Time.at(start_time.to_i),
            'crash_logs_count' => num_crash_reports
          })
          test_object_v2.merge!({ 'duration' => duration.to_f.round(3) }) if test_bstack_reason.empty?
        end

        summary_data_v2 = update_tests_status(summary_data_v2, classname, result_status, 'running')
        Utils.write_to_file(@summary_file_v2, summary_data_v2.to_json)
      end

      # Refresh summary data
      summary_data = get_summary_data(@summary_file)
      push_to_cls(@params, 'test_idle_timeout', '', { "testname" => testname, "start_time" => start_time, "test_duration" => test_duration, "timedout" => true, "summary_data" => summary_data }) if result == "TIMEDOUT"

      log_finish "update_test_summary"
    end

    def update_timeout_tests # rubocop:todo Metrics/AbcSize
      log_start "update_timeout_tests"
      summary_data = get_summary_data(@summary_file)
      # Session Summary File v2
      summary_data_v2 = get_summary_data(@summary_file_v2)

      @testsuite_files.each do |testfile|
        testlist = get_testlist(testfile)
        testlist.each do |testname|
          t_split = testname.split("/")
          testname = t_split.pop
          classname = t_split.join("/")
          test_object = begin
            summary_data['test_details'][classname][testname]
          rescue
            nil
          end
          if !test_object.nil? && (test_object["status"] == "QUEUED")
            test_object["status"] = "TIMEDOUT"
            summary_data["test_status"]["TIMEDOUT"] += 1
            summary_data["test_status"]["QUEUED"] -= 1
          end

          classname = @is_duplicate ? classname : classname.split('/').pop # We name module + class name as unqiue string if duplicate class + test are present
          test_object_v2 = begin
            summary_data_v2['classes'][classname]['tests'][testname]
          rescue
            nil
          end
          if !test_object_v2.nil? && (test_object_v2["status"].downcase == "queued")
            test_object_v2["status"] = "skipped"
            summary_data_v2 = update_tests_status(summary_data_v2, classname, 'skipped', 'queued')
          end
        end
      end
      Utils.write_to_file(@summary_file, summary_data.to_json)

      # Session Summary File v2
      Utils.write_to_file(@summary_file_v2, summary_data_v2.to_json)
      log_finish "update_timeout_tests"
    end

    def update_test_status_to_running(testname_with_classname_combined)
      log_start "update_test_status_to_running for #{testname_with_classname_combined}"
      summary_data = get_summary_data(@summary_file)
      # Session Summary File v2
      summary_data_v2 = get_summary_data(@summary_file_v2)
      t_split = testname_with_classname_combined.split("/")
      testname = t_split.pop
      classname = t_split.join("/")
      test_object = begin
        summary_data['test_details'][classname][testname]
      rescue
        nil
      end
      if !test_object.nil? && (test_object["status"] == "QUEUED")
        test_object["status"] = "RUNNING"
        summary_data["test_status"]["RUNNING"] += 1
        summary_data["test_status"]["QUEUED"] -= 1
      end

      classname = @is_duplicate ? classname : classname.split('/').pop # We name module + class name as unqiue string if duplicate class + test are present
      test_object_v2 = begin
        summary_data_v2['classes'][classname]['tests'][testname]
      rescue
        nil
      end
      if !test_object_v2.nil? && (test_object_v2["status"].downcase == "queued")
        test_object_v2["status"] = "running"
        summary_data_v2 = update_tests_status(summary_data_v2, classname, 'running', 'queued')
      end
      Utils.write_to_file(@summary_file, summary_data.to_json)

      # Session Summary File v2
      Utils.write_to_file(@summary_file_v2, summary_data_v2.to_json)
      log_finish "update_test_status_to_running for #{testname_with_classname_combined}"
    end

    def get_test_pusher_message(full_testname)
      test_split = full_testname.split("/")
      testname = test_split.pop
      classname = @is_duplicate ? test_split.join("/") : test_split.pop # We name module + class name as unqiue string if duplicate class + test are present
      summary_data = get_summary_data(@summary_file_v2)
      test_object = begin
        summary_data['classes'][classname]['tests'][testname]
      rescue
        nil
      end
      unless test_object.nil?
        message = {
          'build_id' => summary_data['build_id'],
          'session_id' => summary_data['session_id'],
          'test_id' => test_object['test_id'],
          'device' => @params['user_device_name'],
          'classname' => classname,
          'testname' => testname,
          'name' => "#{classname}/#{testname}",
          'status' => test_object['status'],
          'duration' => test_object['duration'],
          'video' => test_object['video']
        }
      end
      message
    end

    def notify_pusher(event, message)
      pusher_params = {
        type: "app_frameworks",
        channel: @params["pusher_channel"],
        token: @params["pusher_auth"],
        event: event,
        message: message
      }
      pusher_url = "#{@params['pusher_url']}/sendMessage"
      Utils.fork_code_block_for_device(@device) do
        BrowserStack.logger.info "Sending #{message} to #{pusher_url} with params #{pusher_params}}"
        HttpUtils.send_post_raw(pusher_url, pusher_params)
      rescue => e
        BrowserStack.logger.error "Sending message to pusher failed #{e}"
      end
    rescue => e
      BrowserStack.logger.error "Sending message to pusher failed #{e}"
    end

    def inform_rails

      log_start "inform_rails"
      session_data = JSON.parse(File.read(@callback_file))

      rails_endpoint = session_data["rails_callback"]
      session_data["error_reason"] = @override_error_reason if @override_error_reason

      BrowserStack.logger.info("Informing Rails about xctest session completed")
      BrowserStack.logger.info("Posting to #{rails_endpoint}")
      BrowserStack.logger.info("Data is #{session_data}")
      push_to_cls(@params, 'xctest_session_inform_rails_done', '', { "session_data" => session_data })
      response = HttpUtils.send_post(rails_endpoint, session_data, nil, true, { retry_count: 3, retry_interval: 5 })

      BrowserStack.logger.info("Response code: #{response.code}, Body: #{response.body}")

      if response.code.to_i != 200
        BrowserStack.logger.error("Inform Rails unsuccessful for #{@device}")
        BrowserStack::Zombie.push_logs("xctest-rails-post-error", "Inform Rails Post Exception", { "session_id" => @session_id })
      end
      begin
        FileUtils.rm(@callback_file)
      rescue
        nil
      end
      log_finish "inform_rails"
    rescue => e
      BrowserStack::Zombie.push_logs("xctest-rails-inform-error", "Inform Rails Exception", { "session_id" => @session_id })
      push_to_cls(@params, 'xctest_session_inform_rails_failed', e.message.to_s, { "device" => @device, "session_data" => session_data, "error_data" => e.inspect.to_s })

    end

    def ensure_wda_not_running
      return if Gem::Version.new(@device_os_version) < Gem::Version.new('16')
      return unless BrowserStack::OSUtils.wda_xcodebuild_running?(@device)

      BrowserStack::OSUtils.kill_wda_xcodebuild(@device)
    rescue => e
      BrowserStack::Zombie.push_logs("xctest-wda-kill-failure", e.message[0, 150], { "session_id" => @session_id })
    end

    def log_start(method)
      class_name = self.class.to_s
      session_id = @session_id || @params['automate_session_id']
      BrowserStack.logger.info("#{class_name} #{session_id} Session - #{method} called", { subcomponent: class_name })
    end

    def log_finish(method)
      class_name = self.class.to_s
      session_id = @session_id || @params['automate_session_id']
      BrowserStack.logger.info("#{class_name} #{session_id} Session - #{method} finished", { subcomponent: class_name })
    end

    def check_duplicate_test
      classname_testname_set = []
      @is_duplicate = false
      @testsuite_files.each do |testfile|
        testlist = get_testlist(testfile)
        testlist.each do |test_obj|
          full_test_name = test_obj.split("/")
          test_name = full_test_name.pop
          class_name = full_test_name.pop
          classname_testname = "#{class_name}/#{test_name}"
          if classname_testname_set.include? classname_testname
            @is_duplicate = true
            break
          end
          classname_testname_set << classname_testname
        end

        break if @is_duplicate
      end

      @is_duplicate
    end

    def track_stability_reason_in_eds(reason, sdr)
      log_start "track_stability_reason_in_eds"
      data_to_push = {
        secondary_diagnostic_reason: sdr,
        product: { stability: { reason: reason } },
        hashed_id: @params['automate_session_id'],
        timestamp: Time.now.to_i
      }

      Utils.send_to_eds(data_to_push, EdsConstants::APP_AUTOMATE_TEST_SESSIONS, true)

      send_error_reason_in_file(@callback_file, sdr)
      log_finish "track_stability_reason_in_eds"
    rescue Exception => e
      BrowserStack.logger.info("Error while tracking stability reason to eds. Error: #{e.message}\nBacktrace: #{e.backtrace.join("\n")}")
    end

    def push_feature_usage_to_eds
      return if @test_meta_info.nil? || @test_meta_info.empty?

      log_start "push_feature_usage_to_eds"

      data_to_push = {
        hashed_id: @params['automate_session_id'],
        timestamp: Time.now.to_i,
        feature_usage: { "tests_stability" => @test_meta_info }
      }

      Utils.send_to_eds(data_to_push, EdsConstants::APP_AUTOMATE_TEST_SESSIONS, true)

      log_finish "push_feature_usage_to_eds"
    rescue Exception => e
      BrowserStack.logger.info("Error while pushing feature usage to eds. Error: #{e.message}\nBacktrace: #{e.backtrace.join("\n")}")
    end

    def device_state
      @device_state ||= DeviceState.new(@device)
    end

    def xcuitest_sri_flow?
      @params['singleRunnerInvocation'].to_s == 'true'
    end

    def xctest_xmlfile_path
      "/tmp/#{@device}_xctestrun.xctestrun"
    end

    def product_module_name_file_path
      "/tmp/#{@device}_product_module_name"
    end

    def spawn_xctest_pid_file_path
      "/tmp/#{@device}-xctest.pid"
    end

    def xctest_timeout_pid_file_path
      "/tmp/#{@device}-xctesttimeout.pid"
    end

    def xctest_summary_file_path
      "/tmp/#{@device}_xctest_summary.json"
    end

    def xcresult_bundle_directory
      "/tmp/#{@device}_xcresult_bundle"
    end

    def custom_har_dump_file
      "/tmp/custom_har_file_#{@session_id}.har"
    end

    def xcresult_bundle_zip_file
      device_state.send(:xcuitest_result_bundle_zip_file)
    end

    def xctest_runner_app_cache
      # From ios17 onwards, xcode introduced a new component named coreDeviceService.
      # one of the functionality of this component is to cache the apps every time a fresh app is installed.
      # This caching was getting accumulated over 10 days of TTL and consuming a lot of disk space.
      "#{XCTEST_TEST_APP_COREDEVICE_SERVICE_CACHE_PATH}#{@params['test_suite_bundle_id']}" if @device_config['device_version'].to_i >= 17 && @params["test_suite_bundle_id"]
    end
  end
end
