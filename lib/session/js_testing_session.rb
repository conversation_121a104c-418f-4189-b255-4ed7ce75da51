require_relative './session'
require_relative '../appium_server'
require_relative '../utils/utils'
require_relative '../../server/iphone'
require_relative '../overridden/appium_lib_overridden' # overrides check_server_version_xcuitest method

module BrowserStack
  class JsTestingSession < BrowserStack::Session
    def start
      super
      Utils.fork_code_block_for_device(@device) do
        @appium_server = BrowserStack::AppiumServer.new(@device)
        setup_local
        unlock_device
        check_and_change_appium_version_for_js_testing if @device_config['device_version'].to_i >= 13
        start_video_rec
        start_browser
      rescue => e
        raise "Error while starting session #{@params['session_id']} - #{e.message} #{e.backtrace[0]}"
      end
    end

    def stop
      super

      @driver&.driver_quit
      return if video_disabled?

      @video_rec_manager.stop_rec
    end

    private

    def video_disabled?
      @params["video"].to_s != "true"
    end

    def start_video_rec
      return if video_disabled?

      @video_rec_manager.start_rec
    end

    def unlock_device
      BrowserStack::IPhone.unlock_device(@device)
    end

    def setup_local
      BrowserStack::Session.setup_local(@params, @device_config)
    end

    def start_browser
      desired_caps = {
        browserName: 'Safari',
        safariInitialUrl: @params["url"],
        "browserstack.isTargetBased" => @device_config['device_version'].to_f >= 12.2
      }
      @driver = @appium_server.driver(desired_caps)
      current_url = @driver.current_url rescue nil # rubocop:todo Style/RescueModifier
      if current_url && current_url.start_with?("safari-web-extension") # rubocop:disable Style/SafeNavigation
        begin
          window_handles = @driver.window_handles
          if window_handles && window_handles.size >= 2
            desired_window = window_handles[1]
            @driver.switch_to.window(desired_window)
            BrowserStack.logger.info "[JS_TESTING] - Switched window handle from safari-web-extension to #{@driver.window_handle} from available handles #{window_handles}"
          end
        rescue => e
          BrowserStack::Zombie.push_logs('js-test-window-handle-failure', e.message, { "data" => { "window_handles" => window_handles, "current_url" => current_url }, "session_id" => @params['session_id'] })
        end
      end
      @driver.navigate.to(@params["url"]) if @driver.current_url == "about:blank"
      # Instrumentation for future purposes
      BrowserStack::Zombie.push_logs('js-test-open-url-mismatch', 'url-not-matched', { "data" => { "current_url" => @driver.current_url, "request_url" => @params["url"] }, "session_id" => @params['session_id'] }) if @driver.current_url != @params["url"]
    end

    def check_and_change_appium_version_for_js_testing
      appium_version = @device_config['device_version'].to_f >= 13.4 ? "1.17.0" : @@server_config['appium_version_for_js_testing_in_ios_13']
      appium_version = "1.21.0" if @device_config['device_version'].to_f >= 16.0
      appium_version = "*******.21.6" if @device_config['device_version'].to_f >= 17.0
      @appium_server.start_server_for_version(appium_version)
      BrowserStack::WebDriverAgent.stop(@device)
      BrowserStack::IPhone.uninstall_wda(@device)
    end
  end
end
