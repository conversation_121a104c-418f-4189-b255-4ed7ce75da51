require 'json'
require_relative '../utils/utils'
require_relative '../utils/device_logger'
require_relative '../configuration'
require_relative '../privoxy_manager'
require_relative '../helpers/video_rec_manager'
require_relative '../privoxy_push_repeater'
require_relative '../models/device_state'
require_relative '../network_simulator'

module BrowserStack
  class SessionException < StandardError
  end

  class Session # rubocop:todo Metrics/ClassLength

    attr_reader :genre, :device, :session_id, :old_session_params

    def initialize(params, device_config)
      conf = Configuration.new
      @@server_config = conf.all

      @genre = params[:genre] || params["genre"]
      @device = params[:device] || params["device"]
      @mini_ip = params[:terminal_ip] || params["terminal_ip"]
      @device_config = device_config
      @params = params
      @is_update_session = false
      @old_session_params = nil
      @video_rec_manager = VideoRecManager.new(@device, @params) # Used in JS Testing.
      @streaming_params_data_reporter = DataReportHelper.new("streaming-params")
    end

    def idevice
      @idevice ||= BrowserStack::IPhone.new(@device_config, @device)
    end

    def get_full_URL(param_url) # rubocop:todo Naming/MethodName
      param_url = "http://#{param_url}" unless /(https?:)/.match(param_url)
      param_url
    end

    def proxy_localhost(param_url)
      param_full_url = get_full_URL(param_url)
      begin
        param_url_parsed = URI.parse(param_full_url)
      rescue URI::Error => e
        BrowserStack.logger.info "#{@params['device_browser']} Proxy URL : Got malformed URL #{param_url} -> #{param_full_url}"
        return '' if param_url.to_s == 'about:blank'

        return param_url
      end
      if ["localhost", "127.0.0.1", "0.0.0.0"].include?(param_url_parsed.host)
        param_url_parsed.host = "bs-local.com"
        BrowserStack.logger.info "#{@params['device_browser']} Proxy URL : #{param_url} was proxied to #{param_url_parsed}."
      end
      param_url_parsed.to_s
    end

    # rubocop:disable Metrics/MethodLength
    def start # rubocop:todo Metrics/AbcSize
      BrowserStack.logger.info "Starting Session For Device #{@device}"
      raise BrowserStack::SessionException, "Cannot start session without device and genre params set" if @genre.nil? || @device.nil?

      @params["url"] = proxy_localhost(@params["url"]) if ["live_testing", "js_testing"].include?(@genre)

      current_device = DeviceManager.device_configuration_check(@device)
      session_id = @params["live_session_id"] || @params["app_live_session_id"]
      PrivoxyManager.write_privoxy_log_head(@device, current_device, session_id)
      DeviceLogger.initialize(@device, session_id)
      if File.exist? session_file
        @old_session_params = JSON.parse(File.read(session_file))
        if @old_session_params["user_id"] != @params["user_id"]
          BrowserStack.logger.error "Terminal sharing detected. Old params - #{@old_session_params}"
          BrowserStack::Zombie.push_logs('terminal_sharing_detected', '', {
            "device" => @device,
            "user_id" => @params["user_id"],
            "old_user_id" => @old_session_params["user_id"],
            "session_id" => @params["live_session_id"],
            "old_session_id" => @old_session_params["live_session_id"]
          }, nil, @params)
          raise BrowserStack::SessionException, "Device already allocated - #{@device}"
        elsif @old_session_params["device"] == @params["device"]
          @is_update_session = true
        end
      end

      unless ["js_testing", "app_automate"].include? @genre
        @params.merge!({
          "debugger_ip" => current_device["zotac_host"], "debugger_port" => current_device["debugger_port"],
          "device_version" => current_device["device_version"], "device_static_name" => current_device["device_name"],
          "iproxyPort" => current_device["webdriver_port"].to_i
        })

        if Gem::Version.new(current_device["device_version"]) < Gem::Version.new("18.0") ||
          !@params.key?("non_replay_kit_params")
          update_streaming_params_from_platform(current_device["device_name"])
        else
          begin
            streaming_params = JSON.parse(@params["non_replay_kit_params"])
            @params.merge!({
              "width" => streaming_params["captureWidth"].to_i,
              "height" => streaming_params["captureHeight"].to_i
            })
            @streaming_params_data_reporter.report(
              {
                device: @device,
                result: 'success',
                streaming_params: streaming_params
              },
              eds_only: true
            )
          rescue => e
            BrowserStack.logger.info(
              "non_replay_kit_params failed for device #{@device} #{e.message}"
            )
            BrowserStack::Zombie.push_logs(
              "non_replay_kit_params", "non_replay_kit_params from rails failed for device",
              {
                "device" => @device,
                "data" => {
                  "non_replay_kit_params" => @params["non_replay_kit_params"],
                  "error_message" => e.message
                }
              }
            )
            update_streaming_params_from_platform(current_device["device_name"])
          end
        end
      end

      if @genre == "app_live_testing"
        @params.merge!({ "network_logs_port" => current_device["app_live_network_logs_port"] })
        Utils.process_devtools_cache(@params, @@server_config)
      end
      # enable_contacts_app_access - This is for Phone App
      # enable_mobile_addressbook_app_access - This is for Contacts App
      @params["enable_contacts_app_access"] = @params["enable_contacts_app_access"].to_s == "true"
      @params["is_app_accessibility"] = @params["is_app_accessibility"].to_s == "true"
      @params["enable_mobile_addressbook_app_access"] = @params["enable_mobile_addressbook_app_access"].to_s == "true"
      @params["enable_test_flight_app"] = @params["enable_test_flight_app"].to_s == "true"
      Utils.monitor_device_logger_metric(@device, session_id, @params)
      Utils.write_to_file_with_lock(session_file, @params.to_json)
    end
    # rubocop:enable Metrics/MethodLength

    def v2_video_enabled?
      if @params['video'].to_s == "true" && @params['video_params_v2']
        record_params = begin
          JSON.parse(@params['video_params_v2'])
        rescue
          {}
        end
        !record_params.nil? && (record_params['v2'] == 'true')
      else
        false
      end
    end

    def device_state
      @device_state ||= DeviceState.new(@device)
    end

    def network_simulator
      @network_simulator ||= NetworkSimulator.new(@device, @@server_config['privoxy_listen_port_offset'].to_i + @device_config['selenium_port'].to_i)
    end

    def session_file
      "#{@@server_config['state_files_dir']}/#{@device}_session"
    end

    def reboot_file
      "/tmp/reboot_#{@device}"
    end

    def session_start_file
      "#{@@server_config['state_files_dir']}/#{@device}_session_start_indicator"
    end

    def update_session?
      @is_update_session
    end

    def self.setup_local(params, device_config) # rubocop:todo Metrics/CyclomaticComplexity, Metrics/AbcSize, Metrics/PerceivedComplexity
      # hosts format - "host1:port1:useless_port1,host2:port2:useless_port2"
      local_session = !params[:hosts].nil? && !params[:hosts].empty?
      tunnel_killed = (!params[:oldLive_session_id].nil? && !params[:oldLive_session_id].empty?) &&
        (params[:genre] == 'live_testing' || params[:genre] == 'app_live_testing') &&
        params[:tunnelPresent] != 'true' &&
        params[:loader_trigger] == 'local'
      device = params[:device] || params["device"]
      session_id = params[:live_session_id] || params[:automate_session_id] || params[:app_live_session_id] || ""

      device_check_lock_file = "/tmp/device_check_local_lock_#{device}"

      retries = 0
      while File.exists?(device_check_lock_file) && retries < 5
        BrowserStack.logger.info "[setup_local] Device check lock file present. Waiting for device check to finish"
        sleep 3
        retries += 1
      end

      BrowserStack.logger.info "[setup_local] Device check lock file present still going ahead for local setup. Test might break" if File.exists?(device_check_lock_file)

      PrivoxyManager.privoxy_domain_block_whitelist(device, params[:group_id], params[:group_plan_type], params[:privoxy_domain_control_flag]) unless device.nil?
      # setup_privoxy in case of set_custom_max_client_connections is set as true
      if local_session || (params["set_custom_max_client_connections"].to_s == "true" && params[:genre] == 'automate')
        BrowserStack.logger.info "Setting up privoxy for local: #{device}, #{session_id}"
        PrivoxyManager.truncate_privoxy_logs(device)
        PrivoxyManager.setup_privoxy(device, device_config, params)
        PrivoxyManager.start_proxy_checker(device, device_config, session_id, params)
        BrowserStack.logger.info "Using Privoxy for local session: #{device}, #{session_id}"
      elsif tunnel_killed
        config_hash = {}
        BrowserStack.logger.info('Resetting privoxy due to tunnel kill on frontend')
        config_hash.merge!(get_privoxy_config_hash(params)) if Utils.is_app_product_genre?(params[:genre]) || params[:genre] == "live_testing"
        PrivoxyManager.reset_proxy(device, device_config, config_hash)
      else
        BrowserStack.logger.info "Skipping local setup: #{device}, #{session_id}, params: #{params}"
        param_check = params[:sensor_mocking].to_s == "true" || params["enable_settings_app_access"].to_s == "true" ||
          params[:useReplayKit].to_s == "true" || params["set_custom_max_client_connections"].to_s == "true" ||
          !params["privoxyKeepAliveTimeout"].to_s.empty? || params[:geoLocation]
        if params[:genre] == "live_testing" || (Utils.is_app_product_genre?(params[:genre]) && param_check)
          BrowserStack.logger.info('Setting privoxy with params without local setup')
          PrivoxyManager.reset_proxy(device, device_config, get_privoxy_config_hash(params))
        end
      end

      return IdeviceUtils.uninstall_redirect_app(device) if params[:genre] == "automate"
    end

    def self.extension_uninstalled?(params, thread)
      return true if !["automate", "percy"].include?(params[:genre]) || !thread.instance_of?(Thread)

      output = "[ OK ] Uninstalled package with bundle id com.browserstack.Redirect"
      return true if thread.value.include? output

      raise "Uninstall failed for com.browserstack.Redirect"
    rescue => e
      BrowserStack.logger.info e.message
    end

    def stop
      PrivoxyPushRepeater.push_privoxy_logs(@params)
      DeviceLogger.destroy(@device)
    end

    def self.get_privoxy_config_hash(params) # rubocop:todo Metrics/AbcSize
      config_hash = { genre: params[:genre] }
      if params[:sensor_mocking].to_s == "true"
        BrowserStack.logger.info('sensor_mocking while resetting privoxy')
        config_hash.merge!({ sensor_mocking: true })
      end
      if params["enable_settings_app_access"].to_s == "true"
        BrowserStack.logger.info('Adding privoxy with settings_enabled for reset_proxy flow')
        config_hash.merge!({ "enable_settings_app_access" => true })
      end
      if params[:useReplayKit].to_s == "true"
        BrowserStack.logger.info('using sensormocker host for replaykit')
        config_hash.merge!({ useReplayKit: true })
      end
      if params["set_custom_max_client_connections"].to_s == "true"
        BrowserStack.logger.info('setting custom max client connection')
        config_hash.merge!({ "set_custom_max_client_connections" => true })
        config_hash.merge!({ "custom_max_client_connections" => params["custom_max_client_connections"] })
      end
      unless params["privoxyKeepAliveTimeout"].to_s.empty?
        BrowserStack.logger.info('setting custom privoxy_keep_alive_timeout')
        config_hash.merge!({ "privoxyKeepAliveTimeout" => params["privoxyKeepAliveTimeout"].to_i })
      end
      pusher_params = ["pusher_url", "pusher_channel", "pusher_auth"]
      pusher_params.each do |pusher_param|
        unless params[pusher_param].to_s.empty?
          BrowserStack.logger.info("setting #{pusher_param} = #{params[pusher_param]}")
          config_hash.merge!({ pusher_param => params[pusher_param] })
        end
      end
      unless params["live_session_id"].to_s.empty?
        BrowserStack.logger.info('setting custom live_session_id')
        config_hash.merge!({ session_id: params["live_session_id"].to_s })
      end
      config_hash
    end

    private

    def update_streaming_params_from_platform(device_name)
      width, height = get_device_width_and_height(device_name)

      @params.merge!({
        "width" => width,
        "height" => height
      })
    end

    def get_device_width_and_height(device_type)
      device_specific_config = YAML.load_file("#{__dir__}/../../config/device_specific_config.yml")[device_type]
      [device_specific_config["streaming_params"]["width"], device_specific_config["streaming_params"]["height"]]
    end

  end
end
