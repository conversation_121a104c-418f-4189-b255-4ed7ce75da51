require 'json'
require 'fileutils'
require 'securerandom'
require 'pathname'
require 'yaml'
require 'optparse'
require 'nokogiri'
require_relative '../configuration'
require_relative '../utils/osutils'
require_relative '../utils/utils'
require_relative '../utils/http_utils'
require_relative '../utils/device_logger'
require_relative '../utils/xcuitest_summaries_parser'
require_relative '../helpers/video_rec_manager'
require_relative '../helpers/wda_client'
require_relative '../utils/idevice_utils'
require_relative '../ios_vid_capturer'
require_relative '../mitm_proxy'
require_relative '../privoxy_manager'
maestro_timeout_manager = '../../../mobile-common/frameworks_timeout_manager/app_automate_frameworks/maestro_time_out_manager.rb'
maestro_command_formatter = '../../../mobile-common/maestro/maestro_command_formater.rb'

require_relative maestro_timeout_manager if File.file?(File.expand_path(maestro_timeout_manager, __dir__))
require_relative maestro_command_formatter if File.file?(File.expand_path(maestro_command_formatter, __dir__))
require_relative '../models/device_state'
require_relative '../../config/constants'
require_relative '../apps/maestro_ui_runner'

module BrowserStack
  class MaestroSession < BrowserStack::Session # rubocop:todo Metrics/ClassLength
    DEFAULT_TEST_STATUS = 'QUEUED'
    TIMEDOUT_EXIT_CODE = 137
    UNEXPECTED_XCODEBUILD_EXIT_CODES = [70]
    MAX_TEST_RETRY_COUNT = 3
    MAX_MITM_RESTART_RETRY_COUNT = 5
    ERROR_REGEX_PATTERNS = [
      /java\.net\.ConnectException: Failed to connect to/i,
      /java\.net\.SocketTimeoutException/i,
      /Connection refused/i,
      /java.net.SocketException: Connection reset/i
    ].freeze

    def setup_config(server_config)
      @server_config = server_config
    end

    def start
      log_start "start"
      super
      save_session_params
      setup_params
      collect_maestro_flow_files
      setup_summary
      run_maestro_session
      log_finish "start"
    rescue => e
      handle_test_execution_error(e)
    ensure
      stop
    end

    def setup_proxies(retries = 2)
      return if retries < 0 # Redundant condition to rescue block, net 3 retries

      current_privoxy_port, new_privoxy_port = PrivoxyManager.change_port(@device, @device_config)
      @mitmproxy = MitmProxy.new(@device, @params, @server_config)
      @mitmproxy.boot(current_privoxy_port, new_privoxy_port)
    rescue => e
      BrowserStack.logger.info("[#{@session_id}] Setting up proxy failed with error: #{e.message} - #{e.backtrace}")
      BrowserStack::Zombie.push_logs("xcui-proxy-restart-error", "error", { "session_id" => @session_id, "error" => "Proxy Setup Failure retry_count: #{retries}. Exception: #{e.message}" })

      if retries > 0
        sleep retries
        setup_proxies(retries - 1)
      else
        raise e
      end
    end

    def set_v2_video_params
      begin
        @params['video_params_v2'] = @params['video_params_v2'].to_json unless @params['video_params_v2'].nil?
      rescue
        {}
      end
      @is_v2_video_recording = v2_video_enabled?
      device_state.write_with_lock_to_xctest_v2_video_file(@params['video_params_v2']) if @is_v2_video_recording
    end

    def initialize_params
      @test_framework = @params['test_framework']
      @build_id = @params['build_id']
      @session_id = @params['automate_session_id']
      @device_os_version = @device_config['device_version'].to_f
      @downloaded_app_path = @params['downloaded_app_path'] || ""

      @downloaded_test_app_path = @params['downloaded_test_app_path']
    end

    def boot_mitmproxy?
      networklogs_enabled? || accept_insecure_certs_enabled? || live_self_signed_certs_enabled?
    end

    def video_s3url
      bucket = @params['video_aws_bucket']
      region = bucket == "bs-stag" || @params['video_aws_region'] == "us-east-1" ? "s3" : "s3-#{@params['video_aws_region']}"
      "https://#{@params['rails_host']}/s3-upload/#{bucket}/#{region}/#{@params['automate_session_id']}/#{@params['video_file']}.mp4"
    end

    def xctest_xmlfile_path
      "/tmp/#{@device}_xctestrun.xctestrun"
    end

    def setup_params
      log_start "setup_params"
      IdeviceUtils.configure(Configuration.conf)
      initialize_params
      @devicelog_file = "/var/log/browserstack/app_log_#{@device}.log"
      @logs_s3url = "https://#{@params['rails_api_host']}/app-automate/#{@test_framework}/builds/#{@params['build_id']}/sessions/tests/"
      @video_s3url = video_s3url
      @xctest_xmlfile = xctest_xmlfile_path
      @total_test_count = 0
      @testsuite_files = []
      set_v2_video_params
      @vid_capturer = IosVidCapturer.new(@device, @session_id, @server_config, nil, @genre)
      setup_proxies if boot_mitmproxy?
      @session_start_time = Time.now
      @total_session_time = @params['session_time'].to_i
      @java_home = File.dirname(File.dirname(ENV['JAVA_ZULU_16']))
      @maestro_cli_path = "#{BS_DIR_PATH}/deps/maestro-cli/#{MAESTRO_CLI_VERSION}/bin/maestro"

      # Stores the errors obtained for each test in a cumulative fashion.
      @test_meta_info = if @params['execution_flow'] == "xctestrun"
                          {}
                        else
                          {
                            "retries" => 0,
                            "retry_time" => 0
                          }
                        end

      log_finish "setup_params"
    end

    def stop
      log_start "stop"
      BrowserStack::DeviceLogger.destroy(@device)
      stop_maestro_ui_runner
      stop_iproxy_forwarding
      uninstall_maestro_app
      push_feature_usage_to_eds
      upload_summary
      Utils.write_to_file("/tmp/video_params_#{@device}", @params.to_json) if video_enabled?
      if boot_mitmproxy?
        MitmProxy.stop_proxy(@device, @params['automate_session_id'], "maestro")
        if @mitmproxy&.proxy_log_file
          output, = BrowserStack::OSUtils.execute("tail -100 #{@mitmproxy.proxy_log_file} | grep -E \"TypeError: 'NoneType' object is not subscriptable|Exception in thread ServerThread\"", true, timeout: 3)
          BrowserStack.logger.info("[#{@params['automate_session_id']}] mitm exception captured: #{output}")
          BrowserStack::Zombie.push_logs("xcuitest-mitm-exception", "Exception in mitm proxy logfile", { "session_id" => @params['automate_session_id'], "data" => output[0, 1000] }) unless output.empty?
        end
      end
      super
      log_finish "stop"
    rescue => e
      BrowserStack.logger.error("Error in Maestro Stop: #{e.message}\n#{e.backtrace.join("\n")}")
    ensure
      cleanup_files
      inform_rails
    end

    def ensure_session_stop

      spawn_xctest_pid_file = spawn_xctest_pid_file_path
      spawn_xctest_pid = AppAutomateFrameworks::ProcessUtils.get_process_id(spawn_xctest_pid_file)
      running_status = AppAutomateFrameworks::ProcessUtils.process_running?(spawn_xctest_pid)

      BrowserStack.logger.info("#{spawn_xctest_pid} Running: #{running_status} for session: #{@params['automate_session_id']}")

      unless running_status
        FileUtils.rm_rf spawn_xctest_pid_file
        return
      end

      BrowserStack.logger.info("#{@params['automate_session_id']} : Killing spawn_xctest process, cleanup requested mid session")
      AppAutomateFrameworks::ProcessUtils.kill_pid(spawn_xctest_pid)

      if defined?(AppAutomateFrameworks::MaestroTimeoutManager)
        maestro_manager = AppAutomateFrameworks::MaestroTimeoutManager.new(
          @device,
          nil,
          logger: BrowserStack.logger,
          framework: "maestro",
          test_id: nil,
          logger_params: BrowserStack.logger.params,
          script_identifier: ["spawn_xctest.rb"]
        )

        maestro_manager.kill_idle_process
        maestro_manager.stop # Stop timeout manager
      else
        BrowserStack.logger.info "MaestroTimeoutManager is not available, skipping execution."
      end

      # Stop the Maestro UI Runner process if running
      stop_maestro_ui_runner
      stop_iproxy_forwarding
      uninstall_maestro_app
      # Cleanup other session-related resources
      BrowserStack::DeviceLogger.destroy(@device)
      MitmProxy.stop_proxy(@device, @params['automate_session_id'], "maestro")
      ensure_session_files_cleanup
      BrowserStack::Zombie.push_logs("cleanup-forcefully-stopping-session", "Invalid cleanup request received while session is running", { "session_id" => @params['automate_session_id'] })
    rescue => e
      BrowserStack.logger.error("Error in Maestro ensure_session_stop #{e.message} \n#{e.backtrace.join("\n")}")
      raise e
    end

    def ensure_session_files_cleanup
      BrowserStack.logger.info("#{@params['automate_session_id']} : Cleaning session generated files")
      Dir.glob("/tmp/#{@device}_maestro_*").each { |file| FileUtils.rm_rf(file) }
      Dir.glob("/tmp/#{@device}_xctest_*").each { |file| FileUtils.rm_rf(file) }
      FileUtils.rm_rf "/tmp/video_params_#{@device}"
      cleanup_files
    end

    def force_stop
      log_start "force_stop"
      device_state.touch_maestro_session_timedout_file
      BrowserStack.logger.info("force_stop : created timeout file for device: #{@device}")

      # Kill the current running test and mark it as timedout
      if defined?(AppAutomateFrameworks::MaestroTimeoutManager)
        maestro_manager = AppAutomateFrameworks::MaestroTimeoutManager.new(
          @device,
          nil,
          logger: BrowserStack.logger,
          framework: "maestro",
          test_id: nil,
          logger_params: BrowserStack.logger.params,
          script_identifier: ["spawn_xctest.rb"]
        )
        maestro_manager.kill_idle_process
      else
        BrowserStack.logger.info "MaestroTimeoutManager is not available, skipping execution."
      end
      BrowserStack.logger.info("Killed running session for device: #{@device}")
    rescue => e
      BrowserStack.logger.error("Error in Maestro force stop#{e.message} \n#{e.backtrace.join("\n")}")
      raise e
    ensure
      log_finish "force_stop"
    end

    def timeout
      log_start "timeout"
      session_data = JSON.parse(File.read("/tmp/#{@device}_maestro_callback"))
      if (@params['build_id'] == session_data["build_id"]) && (@params['automate_session_id'] == session_data["session_id"])
        device_state.touch_maestro_session_timedout_file
        BrowserStack.logger.info("Created Session Timeout File")
      else
        BrowserStack.logger.error("Error in Maestro Timeout. Session Details didn't match")
      end
      log_finish "timeout"
    end

    def generated_session_files
      [
        maestro_xmlfile_path,
        xctest_xmlfile_path,
        maestro_ui_pid_file_path,
        maestro_summary_file_path,
        summary_path_v2,
        spawn_xctest_pid_file_path,
        maestro_timeout_file,
        "/tmp/#{@session_id}"
      ]
    end

    def cleanup_files
      log_start "cleanup_files"
      generated_session_files.each { |f| FileUtils.rm_rf f unless f.nil? }
      log_finish "cleanup_files"
    rescue => e
      BrowserStack.logger.info("Error occurred while cleanining files #{e.message} #{e.backtrace}")
    end

    def save_session_params
      BrowserStack.logger.info("Saving Maestro session params for #{@device}")
      save_session_params_v1
      save_session_params_v2
    end

    def save_session_params_v1
      log_start "save_session_params_v1"
      summary = {
        'build_id' => @params["build_id"],
        'session_id' => @params["automate_session_id"],
        'device' => @params["user_device_name"], # what is this?
        'start_time' => Time.now.utc.to_s,
        'duration' => '',
        'idle_timeout' => @params["idle_timeout"],
        'error_reason' => ''
      }
      @summary_file = maestro_summary_file_path
      Utils.write_to_file(@summary_file, summary.to_json)
      log_finish "save_session_params_v1"
    end

    def save_session_params_v2
      log_start "save_session_params_v2"
      @callback_file = "/tmp/#{@device}_maestro_callback"
      callback_hash = {
        'build_id' => @params["build_id"],
        'session_id' => @params["automate_session_id"],
        'timeout_sidekiq_worker_hashed_id' => @params["timeout_sidekiq_worker_hashed_id"],
        'auth_key' => @params["auth_key"],
        'rails_callback' => "http://#{@params['rails_host']}/app-automate/session_done",
        'device' => @device,
        'error_reason' => ""
      }
      Utils.write_to_file(@callback_file, callback_hash.to_json)
      log_finish "save_session_params_v2"
    end

    def handle_test_execution_error(exception)
      BrowserStack.logger.info("[#{@params['automate_session_id']}]Test Execution Error while running Maestro: #{exception.message}\n#{exception.backtrace.join("\n")}")
      BrowserStack::Zombie.push_logs("maestro-execution-error", exception.message[0, 150], { "session_id" => @params['automate_session_id'] })
    rescue => e
      BrowserStack.logger.error("[#{@params['automate_session_id']}]Error while rescuing Test Execution error: #{e.message}\n#{e.backtrace.join("\n")}")
    end

    def collect_maestro_flow_files
      log_start "collect_maestro_flow_files"
      # Custom logic to collect Maestro flow files
      # Example: Collect all .yaml files from a specified directory
      begin
        flow_params = @params['execute']
        input_paths = combine_paths(@downloaded_test_app_path, flow_params)
        BrowserStack.logger.info("input_paths: #{input_paths}")
        input_paths = input_paths.join(" ")
        dry_run_path = "/tmp/#{@session_id}/"
        @flow_files = []

        maestro_dry_run_cmd = "MAESTRO_CLI_NO_ANALYTICS=1 JAVA_HOME=#{@java_home} #{@maestro_cli_path} " \
                    "--verbose test #{input_paths} " \
                    "--dry-run --flatten-debug-output --debug-output #{dry_run_path} " \
                    "2>&1"

        maestro_dry_run_cmd = "gtimeout -s KILL 120 sh -c '#{maestro_dry_run_cmd}'"
        BrowserStack.logger.info "Running command #{maestro_dry_run_cmd}"
        output, exit_status = BrowserStack::OSUtils.execute(maestro_dry_run_cmd, true)

        status = case exit_status
                 when 0 then "SUCCESS"
                 when TIMEDOUT_EXIT_CODE then "TIMEDOUT"
                 else "FAILED"
                 end

        dry_run_content = "#{dry_run_path}execution_plan.json"
        BrowserStack.logger.info("Status of dry run command #{status} #{output} #{File.exist?(dry_run_content)}")
        if File.exist?(dry_run_content)
          json_content = File.read(dry_run_content)
          @flow_files = JSON.parse(json_content)
          unless @flow_files.is_a?(Array)
            BrowserStack.logger.error("flow files is not an array #{@flow_files}")
            @flow_files = []
          end
        end
        if @flow_files.empty?
          BrowserStack.logger.error("No Maestro flow files found in #{flow_params}")
          raise "No Maestro flow files found"
        end
      rescue => e
        handle_flow_file_parse_failed(e)
      end
      BrowserStack.logger.info("Collected Maestro flow files: #{@flow_files}")
      log_finish "collect_maestro_flow_files"
    end

    def handle_flow_file_parse_failed(error)
      BrowserStack.logger.error "Maestro flow file parse failed: #{error.message}"
      BrowserStack::Zombie.push_logs("#{@params['test_framework']}-testsuite-filter-failed", error.message.to_s, { "session_id" => @session_id })
      BrowserStack::Zombie.push_logs("app_automation_session_stats", '', { "sessionid" => @session_id, "secondary_diagnostic_reason" => "start-error-testsuite-parse-failed" })
      send_error_reason_in_file(@callback_file, "testsuite-parse-failed")
      send_error_reason_in_file(@summary_file, "testsuite-parse-failed") if @summary_file && File.exists?(@summary_file)
    end

    def combine_paths(flow_file_path, input_paths)
      # Ensure flow_file_path ends with a separator
      BrowserStack.logger.info("combine_paths: #{flow_file_path} #{input_paths}")
      return [flow_file_path] if !input_paths || input_paths.empty? || (input_paths.is_a?(String) && input_paths.strip.empty?)

      base_path = flow_file_path.end_with?('/') ? flow_file_path : "#{flow_file_path}/"

      # Map through input_paths and combine with base_path
      input_paths.map do |relative_path|
        # Remove leading slash from relative_path if it exists
        clean_relative = relative_path.start_with?('/') ? relative_path[1..] : relative_path
        File.join(base_path, clean_relative)
      end
    end

    def filter_testlist(testparams)
      # No filtering needed for Maestro flows as each flow file is treated as a test
    end

    def default_test(input)
      id = get_sha256(input)
      {
        start_time: '',
        status: DEFAULT_TEST_STATUS,
        test_id: @session_id + id,
        duration: '',
        instrumentation_log: '',
        device_log: '',
        video: '',
        maestro_log: '',
        command_log: '',
        screenshot_log: ''
      }
    end

    def send_error_reason_in_file(filename, error_reason)
      log_start "send_error_reason_in_#{filename}_file"
      file_hash = Utils.read_json_file(filename)
      file_hash["error_reason"] = error_reason
      Utils.write_to_file(filename, file_hash.to_json)
      log_finish "send_error_reason_in_#{filename}_file"
    end

    def test_hash_v1
      testhash = {}
      @flow_files.each do |flow_file|
        classname = extract_classname(flow_file)
        testname = classname
        test_obj = default_test("#{classname}/#{testname}")
        testhash[classname] ||= {}
        testhash[classname][testname] = test_obj
      end
      testhash
    end

    def test_states(count = 0)
      {
        "total" => count,
        'passed' => 0,
        'failed' => 0,
        'skipped' => 0,
        'timedout' => 0,
        'error' => 0,
        'running' => 0,
        'queued' => count
      }
    end

    def test_hash_v2
      testhash_v2 = {}
      @flow_files.each do |flow_file|
        classname = extract_classname(flow_file)
        testname = classname
        id = get_sha256("#{classname}/#{testname}")
        testhash_v2[classname] ||= {
          "name" => classname,
          "tests_summary" => test_states,
          "tests" => {}
        }
        testhash_v2[classname]['tests'][testname] = {
          name: testname,
          start_time: '',
          status: DEFAULT_TEST_STATUS.downcase,
          test_id: @session_id + id,
          duration: '',
          video: '',
          class: classname
        }
        ['total', 'queued'].each { |state| testhash_v2[classname]['tests_summary'][state] += 1 }
      end
      testhash_v2
    end

    def total_number_of_tests
      @total_test_count = @flow_files.count
    end

    def setup_summary
      setup_summary_v1
      setup_summary_v2
    end

    def setup_summary_v1
      log_start "setup_summary_v1"
      testhash = test_hash_v1
      total_number_of_tests
      summary_data = get_summary_data(@summary_file)
      summary_data.merge!(
        {
          'test_count' => @total_test_count,
          'test_details' => testhash,
          'test_status' => {
            'SUCCESS' => 0,
            'FAILED' => 0,
            'IGNORED' => 0,
            'TIMEDOUT' => 0,
            'ERROR' => 0,
            'RUNNING' => 0,
            'QUEUED' => @total_test_count
          }
        }
      )
      Utils.write_to_file(@summary_file, summary_data.to_json)
      log_finish "setup_summary_v1"
    end

    def setup_summary_v2
      log_start "setup_summary_v2"
      @summary_file_v2 = summary_path_v2
      testhash_v2 = test_hash_v2
      summary_data_v2 = {
        build_id: @params["build_id"],
        session_id: @params["automate_session_id"],
        test_summary: test_states(@total_test_count),
        classes: testhash_v2
      }
      Utils.write_to_file(@summary_file_v2, summary_data_v2.to_json)
      log_finish "setup_summary_v2"
    end

    def summary_path_v2
      "/tmp/#{@device}_maestro_summary_v2.json"
    end

    def get_summary_data(summary_file)
      JSON.parse(File.read(summary_file))
    end

    def generate_xctestrun_xml(xctestrun_erb_filename = "#{__dir__}/../../templates/maestro_xctestrun.erb")
      log_start "generate_xctestrun_xml"
      testdir = "/tmp/#{@device}_test"

      ppuid_file = PpuidFile.new(@device).ppuid

      signed_app_dir = @server_config["signed_app_dir"]
      maestro_runner = MaestroUIRunner.new
      maestro_runner.update_app_version_using_ios_version(@device_os_version)
      data = {
        app_path: maestro_runner.signed_app_path(signed_app_dir, ppuid_file)
      }

      xctestrun_contents = ERB.new(File.open(xctestrun_erb_filename).read).result(ErbBinding.new(data).get_binding)
      Utils.write_to_file(@xctest_xmlfile, xctestrun_contents)
      FileUtils.rm_rf(testdir)
      log_finish "generate_xctestrun_xml"
    end

    def start_maestro_ui_runner
      log_start "start_maestro_ui_runner"
      generate_xctestrun_xml
      maestro_ui_pid_file = maestro_ui_pid_file_path
      timeout = @params["session_time"].to_i
      xctestrun_cmd = "gtimeout -s KILL #{timeout} xcodebuild -xctestrun #{@xctest_xmlfile} -destination \"id=#{@device}\" test-without-building > /var/log/browserstack/#{@device}_maestro_ui.log 2>&1 & echo $! > #{maestro_ui_pid_file}"
      BrowserStack::OSUtils.execute(xctestrun_cmd, true)
      maestro_ui_pid = File.read(maestro_ui_pid_file).to_i
      BrowserStack.logger.info("Started Maestro UI Runner with PID: #{maestro_ui_pid}")
      log_finish "start_maestro_ui_runner"
    end

    def stop_maestro_ui_runner
      maestro_ui_pid_file = maestro_ui_pid_file_path
      maestro_ui_pid = AppAutomateFrameworks::ProcessUtils.get_process_id(maestro_ui_pid_file).to_s.chomp
      if maestro_ui_pid && maestro_ui_pid != "BSTACK_NAN" && AppAutomateFrameworks::ProcessUtils.process_running?(maestro_ui_pid)
        BrowserStack.logger.info("#{@params['automate_session_id']} : Killing Maestro UI Runner process, cleanup requested mid session pid #{maestro_ui_pid}")
        BrowserStack::OSUtils.kill_pid(maestro_ui_pid.to_i)
      end
      FileUtils.rm_rf maestro_ui_pid_file if File.exist?(maestro_ui_pid_file)
    rescue => e
      BrowserStack.logger.error("Unable to stop maestro ui runner #{e.message} #{e.backtrace}")
    end

    def stop_iproxy_forwarding
      PortManager.stop_forwarding_port(@device, @params['wda_port'] + 2700, "maestro")
    end

    def uninstall_maestro_app
      log_start "start_uninstall_maestro_app"
      maestro_ui_runner = MaestroUIRunner.new
      maestro_ui_runner.uninstall_from_device(@device)
    rescue => e
      BrowserStack.logger.error("uninstall app failed #{e.message} #{e.backtrace}")
    ensure
      log_finish "start_uninstall_maestro_app"
    end

    def start_iproxy_forwarding
      log_start "start_iproxy_forwarding"
      begin
        PortManager.stop_forwarding_port(@device, @params['wda_port'] + 2700, "maestro")
        PortManager.forward_port(@device,  @params['wda_port'] + 2700, 22087, "maestro")
      rescue => e
        BrowserStack.logger.error("Port forwarding failed")
        PortManager.stop_forwarding_port(@device, @params['wda_port'] + 2700, "maestro")
      end
      log_finish "start_iproxy_forwarding"
    end

    def test_expire_time
      current_time = Time.now
      timeout_time = @total_session_time - (current_time - @session_start_time).to_i
      timeout if timeout_time.to_i <= 0
      timeout_time
    end

    def run_maestro_flow(flow_file, test_num = 0, retry_count = 0)
      log_start "run_maestro_flow_#{retry_count}"
      classname = extract_classname(flow_file)
      testname = classname
      timeout = test_expire_time

      @debug_output = "/tmp/#{@session_id}/#{@device}_maestro_debug_#{classname}_#{testname}_#{test_num}"
      driver_host_port = @params['wda_port'] + 2700
      instru_logs_path = "#{@debug_output}/instrulogs.xml"

      maestro_cmd = "MAESTRO_CLI_NO_ANALYTICS=1 SCREENSHOTS_DIR=#{@debug_output} JAVA_HOME=#{@java_home} #{@maestro_cli_path} " \
                    "--device=#{@device} " \
                    "--driver-host-port #{driver_host_port} " \
                    "--verbose test #{flow_file} " \
                    "--flatten-debug-output --debug-output #{@debug_output} " \
                    "--format junit --output #{instru_logs_path} " \
                    "2>&1"

      maestro_cmd = "gtimeout -s KILL #{timeout} sh -c '#{maestro_cmd}'"
      BrowserStack.logger.info "Running command #{maestro_cmd}"
      start_time = Time.now.utc
      start_timeout_manager
      output, exit_status = BrowserStack::OSUtils.execute(maestro_cmd, true)
      duration = (Time.now.utc - start_time).to_f.round(3)

      status = case exit_status
               when 0 then "SUCCESS"
               when TIMEDOUT_EXIT_CODE then "TIMEDOUT"
               else "FAILED"
               end

      stop_timeout_manager

      if File.exist?(instru_logs_path)
        status = parse_instru_logs(instru_logs_path, classname, testname) || status
        BrowserStack.logger.info("Maestro flow #{flow_file} executed with refined status #{status} from instrulogs")
      else
        BrowserStack.logger.info("Instrulogs file #{instru_logs_path} not found; using exit code status: #{status}")
      end
      log_finish "run_maestro_flow_#{retry_count}"

      [status, duration, output]
    end

    # Parse JUnit XML instrulogs with regex-based error detection
    def parse_instru_logs(instru_logs_path, expected_classname, expected_testname)
      doc = Nokogiri::XML(File.read(instru_logs_path))
      doc.remove_namespaces!

      testsuite = doc.at('testsuite')
      return nil unless testsuite

      testcase = testsuite.xpath('.//testcase')[0]

      if (failure_node = testcase.at('failure'))
        failure_message = failure_node.text
        BrowserStack.logger.info("Test '#{expected_classname}' failed with message: #{failure_message}")

          # Check if failure matches any error regex
        if ERROR_REGEX_PATTERNS.any? { |regex| failure_message =~ regex }
          BrowserStack.logger.info("Failure matches error pattern: #{failure_message}")
          "ERROR"
        else
          BrowserStack.logger.info("Failure does not match error patterns, marking as failed: #{failure_message}")
          "FAILED"
        end
      elsif testcase['time'] && !testcase.at('error')
        "SUCCESS"
      else
        BrowserStack.logger.info("Test '#{expected_classname}' has no clear status in instrulogs")
        nil
      end
    rescue => e
      BrowserStack.logger.error("Error parsing instrulogs #{instru_logs_path}: #{e.message}\n#{e.backtrace.join("\n")}")
      nil
    end

    def should_retry_test?(flow_file, retry_count, exit_status)
      return false if retry_count > MAX_TEST_RETRY_COUNT || session_timedout?

      exit_status == TIMEDOUT_EXIT_CODE || exit_status != 0
    end

    def confirm_ui_runner_running
      url = "http://127.0.0.1:#{@params['wda_port'] + 2700}/status"
      BrowserStack.logger.info "Checking if Maestro UI Runner is running on port #{@params['wda_port'] + 2700} url: #{url}}"
      max_attempts = 10
      running = false
      attempts = 0
      until running
        begin
          status_response = BrowserStack::HttpUtils.make_get_request(url, 2)
          BrowserStack.logger.info("Status response: #{status_response}")
          status_body = {}
          status_body = JSON.parse(status_response.body, max_nesting: 400) if status_response.success?
          running = !status_body.empty? && status_body["status"] == "ok"
        rescue => e
          BrowserStack.logger.error("Status request failed #{e.message}")
        end

        next if running

        if attempts > max_attempts
          BrowserStack.logger.error "Maestro UI Runner is not running on port #{@params['wda_port'] + 2700}"
          break
        end

        attempts += 1
        sleep 2
      end
      pid_running = confirm_maestro_ui_runner_pid_running
      BrowserStack.logger.info("status: #{running} pid_status: #{pid_running}")
      running
    end

    def confirm_maestro_ui_runner_pid_running
      ui_runner_running = false
      maestro_runner_pid_file = maestro_ui_pid_file_path
      maestro_ui_pid = AppAutomateFrameworks::ProcessUtils.get_process_id(maestro_runner_pid_file).to_s.chomp
      ui_runner_running = true if maestro_ui_pid && maestro_ui_pid != "BSTACK_NAN" && AppAutomateFrameworks::ProcessUtils.process_running?(maestro_ui_pid)
      ui_runner_running
    rescue => e
      BrowserStack.logger.error("confirm_maestro_ui_runner_pid_running failed #{e.message} #{e.backtrace}")
    end

    def setup_maestro_session
      start_maestro_ui_runner
      start_iproxy_forwarding
      ui_runner_running = confirm_ui_runner_running
      unless ui_runner_running
        BrowserStack.logger.info "Re-installing UI Runner app since it is not running"
        stop_maestro_ui_runner
        maestro_runner = MaestroUIRunner.new
        ppuid_file = PpuidFile.new(@device).ppuid
        maestro_runner.update_app_version_using_ios_version(@device_os_version)
        maestro_runner.setup(@device, ppuid_file, true)
        start_maestro_ui_runner
        confirm_ui_runner_running
      end
      VideoRecManager.new(@device, @params, @params['wda_port']).start_rec if video_enabled?
    end

    def run_maestro_session
      log_start "run_maestro_session"
      suite_start_time = Time.now.utc
      setup_maestro_session
      suite_v2_video_start_time = Time.now.utc
      platform_video_start_time = Time.now.to_f

      test_num = 0
      @flow_files.each do |flow_file|
        break if session_timedout?

        classname = extract_classname(flow_file)
        testname = classname
        extension = File.extname(flow_file)
        test_id = get_sha256("#{classname}/#{testname}")
        update_test_status_to_running("#{classname}/#{testname}")
        notify_test_name_info_to_pusher("#{classname}/#{testname}")
        video_start_time = get_video_duration(suite_v2_video_start_time)
        test_num += 1
        status, duration, output = run_maestro_flow(flow_file, test_num)
        video_stop_time = get_video_duration(suite_v2_video_start_time)
        video_url = video_enabled? ? "#{@video_s3url}#t=#{video_start_time},#{video_stop_time}" : ""
        update_test_summary("#{classname}/#{testname}", suite_start_time, duration, video_url, status)
        upload_logfiles(testname, test_id)
        upload_maestro_debug_files(classname, testname, test_num, extension, test_id)
        notify_test_name_info_to_pusher("#{classname}/#{testname}")
      end
      post_run_maestro_session(suite_start_time, platform_video_start_time)
      log_finish "run_maestro_session"
    end

    def post_run_maestro_session(suite_start_time, platform_video_start_time)
      update_timeout_tests if session_timedout?
      suite_stop_time = Time.now.utc
      update_session_duration(suite_start_time, suite_stop_time)

      summary_data_v2 = get_summary_data(@summary_file_v2)
      test_status = summary_data_v2['test_summary']
      callback_hash = JSON.parse(File.read(@callback_file))
      callback_hash['test_status'] = test_status
      callback_hash['test_status'].delete('total')
      callback_hash['video_start_time'] = platform_video_start_time
      Utils.write_to_file(@callback_file, callback_hash.to_json)

      failed_count = test_status['failed']
      passed_count = test_status['passed']
      skipped_count = test_status['skipped']
      timedout_count = test_status['timedout']
      BrowserStack::Zombie.push_logs("app_automation_session_stats", '', { "sessionid" => @session_id, "test_failed" => failed_count, "test_success" => passed_count, "test_ignored" => skipped_count, "test_timedout" => timedout_count })
    end

    def get_video_duration(suite_v2_video_start_time = Time.now.utc)
      BrowserStack.logger.info("[get_video_duration] is_v2_video_recording: #{@is_v2_video_recording} and suite_v2_video_start_time: #{suite_v2_video_start_time} and #{(Time.now.utc - suite_v2_video_start_time).to_i} seconds")
      if @is_v2_video_recording
        (Time.now.utc - suite_v2_video_start_time).to_i
      else
        @vid_capturer.get_video_duration
      end
    end

    def notify_test_name_info_to_pusher(testname)
      if @params["test_framework"] != "fluttertest"
        pusher_message = get_test_pusher_message(testname)
        notify_pusher("update_build", pusher_message.to_json)
      end
    end

    def get_sha256(test_input)
      sha256_id = Digest::SHA256.hexdigest(test_input)
      sha256_id[0..7]
    end

    def update_session_duration(start_time, end_time)
      log_start "update_session_duration"
      summary_data = get_summary_data(@summary_file)
      summary_data['duration'] = (end_time - start_time)
      Utils.write_to_file(@summary_file, summary_data.to_json)
      summary_data_v2 = get_summary_data(@summary_file_v2)
      summary_data_v2['duration'] = (end_time - start_time)
      Utils.write_to_file(@summary_file_v2, summary_data_v2.to_json)
      log_finish "update_session_duration"
    end

    def session_timedout?
      device_state.maestro_session_timedout_file_present?
    end

    def update_v1_test_summary(summary_data, classname, testname, status, test_duration, video_url, start_time)
      test_object = summary_data['test_details'][classname][testname]
      test_id = test_object['test_id']
      test_object.merge!(
        {
          'status' => status,
          'duration' => test_duration,
          'device_log' => !devicelogs_enabled? ? "" : "#{@logs_s3url}#{test_id}/devicelogs",
          'network_log' => !networklogs_enabled? ? "" : "#{@logs_s3url}#{test_id}/networklogs",
          'command_log' => "#{@logs_s3url}#{test_id}/commandlogs",
          'maestro_log' => "#{@logs_s3url}#{test_id}/maestrologs",
          'screenshots' => "#{@logs_s3url}#{test_id}/screenshots",
          'instrumentation_log' => "#{@logs_s3url}#{test_id}/instrumentationlogs",
          'rawlogs' => "#{@logs_s3url}#{test_id}/rawlogs",
          'video' => video_url,
          'start_time' => Time.at(start_time.to_i)
        }
      )
      summary_data["test_status"][status] += 1
      summary_data["test_status"]['RUNNING'] -= 1
      Utils.write_to_file(@summary_file, summary_data.to_json)
    end

    def update_v2_test_summary(summary_data_v2, classname, testname, status, start_time, test_duration, video_url)
      test_object_v2 = summary_data_v2['classes'][classname]['tests'][testname]
      video_url = (video_url.split('#')[1].split('=')[1]).to_s if video_enabled?
      result_status = status.downcase
      case result_status
      when "success"
        result_status = "passed"
      when "ignored"
        result_status = "skipped"
      end
      test_object_v2.merge!(
        {
          'status' => result_status,
          'video' => video_url,
          'start_time' => Time.at(start_time.to_i),
          'duration' => test_duration
        }
      )
      summary_data_v2 = update_tests_status(summary_data_v2, classname, result_status, 'running')
      Utils.write_to_file(@summary_file_v2, summary_data_v2.to_json)
    end

    def update_test_summary(testname_with_classname, start_time, test_duration, video_url, status)
      log_start "update_test_summary"
      t_split = testname_with_classname.split("/")
      testname = t_split.pop
      classname = t_split.join("/")
      summary_data = get_summary_data(@summary_file)
      update_v1_test_summary(summary_data, classname, testname, status, test_duration, video_url, start_time)

      summary_data_v2 = get_summary_data(@summary_file_v2)
      update_v2_test_summary(summary_data_v2, classname, testname, status, start_time, test_duration, video_url)

      summary_data = get_summary_data(@summary_file)
      push_to_cls(@params, 'test_idle_timeout', '', { "testname" => testname, "start_time" => start_time, "test_duration" => test_duration, "timedout" => true, "summary_data" => summary_data }) if status == "TIMEDOUT"
      log_finish "update_test_summary"
    end

    def extract_classname(flow_file)
      relative_name = flow_file.start_with?(@downloaded_test_app_path) ? flow_file["#{@downloaded_test_app_path}/".length..] : flow_file
      if relative_name.end_with?('.yaml')
        relative_name[0..-6] # Remove .yaml (5 chars + dot)
      elsif relative_name.end_with?('.yml')
        relative_name[0..-5] # Remove .yml (4 chars + dot)
      else
        BrowserStack.logger.info("Flow file #{flow_file} has an unexpected extension; treating as classname: #{relative_name}")
        relative_name
      end
    end

    def update_timeout_tests
      log_start "update_timeout_tests"
      summary_data = get_summary_data(@summary_file)
      summary_data_v2 = get_summary_data(@summary_file_v2)
      @flow_files.each do |flow_file|
        classname = extract_classname(flow_file)
        testname = classname
        test_object = summary_data['test_details'][classname][testname]
        if test_object && test_object["status"] == "QUEUED"
          test_object["status"] = "TIMEDOUT"
          summary_data["test_status"]["TIMEDOUT"] += 1
          summary_data["test_status"]["QUEUED"] -= 1
        end
        test_object_v2 = summary_data_v2['classes'][classname]['tests'][testname]
        if test_object_v2 && test_object_v2["status"].downcase == "queued"
          test_object_v2["status"] = "skipped"
          summary_data_v2 = update_tests_status(summary_data_v2, classname, 'skipped', 'queued')
        end
      end
      Utils.write_to_file(@summary_file, summary_data.to_json)
      Utils.write_to_file(@summary_file_v2, summary_data_v2.to_json)
      log_finish "update_timeout_tests"
    end

    def update_test_status_to_running(testname_with_classname)
      log_start "update_test_status_to_running for #{testname_with_classname}"
      summary_data = get_summary_data(@summary_file)
      summary_data_v2 = get_summary_data(@summary_file_v2)
      t_split = testname_with_classname.split("/")
      testname = t_split.pop
      classname = t_split.join("/")
      test_object = summary_data['test_details'][classname][testname]
      if test_object && test_object["status"] == "QUEUED"
        test_object["status"] = "RUNNING"
        summary_data["test_status"]["RUNNING"] += 1
        summary_data["test_status"]["QUEUED"] -= 1
      end
      test_object_v2 = summary_data_v2['classes'][classname]['tests'][testname]
      if test_object_v2 && test_object_v2["status"].downcase == "queued"
        test_object_v2["status"] = "running"
        summary_data_v2 = update_tests_status(summary_data_v2, classname, 'running', 'queued')
      end
      Utils.write_to_file(@summary_file, summary_data.to_json)
      Utils.write_to_file(@summary_file_v2, summary_data_v2.to_json)
      log_finish "update_test_status_to_running for #{testname_with_classname}"
    end

    def get_test_pusher_message(full_testname)
      test_split = full_testname.split("/")
      testname = test_split.pop
      classname = test_split.join("/")
      summary_data = get_summary_data(@summary_file_v2)
      test_object = summary_data['classes'][classname]['tests'][testname]
      message = {
        'build_id' => summary_data['build_id'],
        'session_id' => summary_data['session_id'],
        'test_id' => test_object['test_id'],
        'device' => @params['user_device_name'],
        'classname' => classname,
        'testname' => testname,
        'name' => "#{classname}/#{testname}",
        'status' => test_object['status'],
        'duration' => test_object['duration'],
        'video' => test_object['video']
      }
    end

    def notify_pusher(event, message)
      pusher_params = {
        type: "app_frameworks",
        channel: @params["pusher_channel"],
        token: @params["pusher_auth"],
        event: event,
        message: message
      }
      pusher_url = "#{@params['pusher_url']}/sendMessage"
      Utils.fork_code_block_for_device(@device) do
        BrowserStack.logger.info "Sending #{message} to #{pusher_url} with params #{pusher_params}}"
        HttpUtils.send_post_raw(pusher_url, pusher_params)
      rescue => e
        BrowserStack.logger.error "Sending message to pusher failed #{e}"
      end
    rescue => e
      BrowserStack.logger.error "Sending message to pusher failed #{e}"
    end

    def inform_rails
      log_start "inform_rails"
      session_data = JSON.parse(File.read(@callback_file))
      rails_endpoint = session_data["rails_callback"]
      session_data["error_reason"] = @override_error_reason if @override_error_reason
      BrowserStack.logger.info("Informing Rails about Maestro session completed")
      BrowserStack.logger.info("Posting to #{rails_endpoint}")
      BrowserStack.logger.info("Data is #{session_data}")
      push_to_cls(@params, 'maestro_session_inform_rails_done', '', { "session_data" => session_data })
      response = HttpUtils.send_post(rails_endpoint, session_data, nil, true, { retry_count: 3, retry_interval: 5 })
      BrowserStack.logger.info("Response code: #{response.code}, Body: #{response.body}")
      if response.code.to_i != 200
        BrowserStack.logger.error("Inform Rails unsuccessful for #{@device}")
        BrowserStack::Zombie.push_logs("maestro-rails-post-error", "Inform Rails Post Exception", { "session_id" => @session_id })
      end
      FileUtils.rm(@callback_file) if File.exist?(@callback_file)
      log_finish "inform_rails"
    rescue => e
      BrowserStack.logger.error("Inform Rails unsuccessful for #{@device} #{e.message} #{e.backtrace}")
      BrowserStack::Zombie.push_logs("maestro-rails-inform-error", "Inform Rails Exception", { "session_id" => @session_id })
      push_to_cls(@params, 'maestro_session_inform_rails_failed', e.message.to_s, { "device" => @device, "session_data" => session_data, "error_data" => e.inspect.to_s })
    end

    def log_start(method)
      class_name = self.class.to_s
      session_id = @session_id || @params['automate_session_id']
      BrowserStack.logger.info("#{class_name} #{session_id} Session - #{method} called", { subcomponent: class_name })
    end

    def log_finish(method)
      class_name = self.class.to_s
      session_id = @session_id || @params['automate_session_id']
      BrowserStack.logger.info("#{class_name} #{session_id} Session - #{method} finished", { subcomponent: class_name })
    end

    def track_stability_reason_in_eds(reason, sdr)
      log_start "track_stability_reason_in_eds"
      data_to_push = {
        secondary_diagnostic_reason: sdr,
        product: { stability: { reason: reason } },
        hashed_id: @params['automate_session_id'],
        timestamp: Time.now.to_i
      }
      Utils.send_to_eds(data_to_push, EdsConstants::APP_AUTOMATE_TEST_SESSIONS, true)
      send_error_reason_in_file(@callback_file, sdr)
      log_finish "track_stability_reason_in_eds"
    rescue => e
      BrowserStack.logger.info("Error while tracking stability reason to eds. Error: #{e.message}\nBacktrace: #{e.backtrace.join("\n")}")
    end

    def push_feature_usage_to_eds
      return if @test_meta_info.nil? || @test_meta_info.empty?

      log_start "push_feature_usage_to_eds"
      data_to_push = {
        hashed_id: @params['automate_session_id'],
        timestamp: Time.now.to_i,
        feature_usage: { "tests_stability" => @test_meta_info }
      }
      Utils.send_to_eds(data_to_push, EdsConstants::APP_AUTOMATE_TEST_SESSIONS, true)
      log_finish "push_feature_usage_to_eds"
    rescue => e
      BrowserStack.logger.info("Error while pushing feature usage to eds. Error: #{e.message}\nBacktrace: #{e.backtrace.join("\n")}")
    end

    def device_state
      @device_state ||= DeviceState.new(@device)
    end

    def maestro_xmlfile_path
      "/tmp/#{@device}_maestro.xctestrun"
    end

    def spawn_xctest_pid_file_path
      "/tmp/#{@device}-xctest.pid"
    end

    def maestro_timeout_file
      "/tmp/#{@device}_maestro_timeout"
    end

    def maestro_ui_pid_file_path
      "/tmp/#{@device}-maestro-ui.pid"
    end

    def maestro_summary_file_path
      "/tmp/#{@device}_maestro_summary.json"
    end

    def devicelogs_enabled?
      @params['deviceLogs'].to_s == 'true'
    end

    def networklogs_enabled?
      @params['networkLogs'].to_s == 'true'
    end

    def upload_debug_screenshots?
      @params['upload_debug_screenshots'].to_s == 'true'
    end

    def live_self_signed_certs_enabled?
      @params['live_self_signed_certs'] == 'true'
    end

    def accept_insecure_certs_enabled?
      @params['acceptInsecureCerts'].to_s == 'true'
    end

    def video_enabled?
      @params['video'].to_s == "true"
    end

    # Used in xctestrun flow
    def test_expired?(session_start_time = @session_start_time, total_session_time = @total_session_time)
      current_time = Time.now
      (current_time - session_start_time).to_i > total_session_time
    end

    def update_tests_status(summary_data_v2, classname, new_state, old_state)
      unless summary_data_v2.nil?
        class_result = begin
          summary_data_v2['classes'][classname]['tests_summary']
        rescue
          nil
        end

        unless class_result.nil?
          class_result[new_state] += 1
          class_result[old_state] -= 1
        end

        unless summary_data_v2["test_summary"].nil?
          summary_data_v2["test_summary"][new_state] += 1
          summary_data_v2["test_summary"][old_state] -= 1
        end
      end
      summary_data_v2
    end

    def get_s3_params(filetag, test_id = nil, aws_params = nil)
      log_start "get_s3_params"
      if aws_params.nil?
        bucket = @params['devicelogs_aws_bucket']
        aws_region = @params['devicelogs_aws_region']
        region = bucket == "bs-stag" || @params['devicelogs_aws_region'] == "us-east-1" ? "s3" : "s3-#{@params['devicelogs_aws_region']}"
        s3url = ""
        key = @params['devicelogs_aws_key']
        secret = @params['devicelogs_aws_secret']
        aws_storage_class = @params['devicelogs_aws_storage_class']
      else
        bucket = aws_params['bucket']
        aws_region = aws_params['region']
        region = bucket == "bs-stag" || aws_params['region'] == "us-east-1" ? "s3" : "s3-#{aws_params['region']}"
        s3url = ""
        key = aws_params['key']
        secret = aws_params['secret']
        aws_storage_class = aws_params['storage_class']
      end

      test_id = test_id.nil? ? @session_id : @session_id + test_id
      s3url = "https://#{region}.amazonaws.com/#{bucket}/#{@build_id}/#{test_id}/#{test_id}-#{filetag}"

      is_json = filetag&.end_with?(".json") ? true : false
      is_zip = filetag&.end_with?(".zip") ? true : false
      params = {
        session_id: test_id,
        aws_key: key,
        aws_secret: secret,
        aws_bucket: bucket,
        aws_region: aws_region,
        aws_storage_class: aws_storage_class,
        s3_url: s3url.to_s,
        no_acl: true,
        is_json: is_json,
        is_zip: is_zip
      }
      log_finish "get_s3_params"
      params
    end

    def flatten_summary_v2
      summary_data_v2 = get_summary_data(@summary_file_v2)

      if summary_data_v2['classes']
        summary_data_v2['classes'] = summary_data_v2['classes'].values

        summary_data_v2['classes'].each do |class_object|
          class_object['tests'] = class_object['tests'].values
        end
      end

      Utils.write_to_file(@summary_file_v2, summary_data_v2.to_json)
    end

    def upload_logfiles(testname, test_id)
      log_start "upload_logfiles"
      if devicelogs_enabled?
        s3_params = get_s3_params("device-logs.txt", test_id)
        session_device_logs = BrowserStack::DeviceLogger.app_device_logs_file(@device)
        Utils.create_upload_request(session_device_logs, "device", @device, s3_params, @server_config, @test_framework, @genre)
        BrowserStack.logger.info("Device Logs upload request created")
      end

      # Restart mitmproxy and capture network logs
      # Only if networkLogs is enabled.
      if networklogs_enabled?
        if testname.empty?
          s3_params = get_s3_params("har-logs.txt", test_id)
          harfile = @mitmproxy.harfile
          BrowserStack.logger.info("Session Id: #{@session_id}, Uploading harfile from: #{harfile}")
          Utils.create_upload_request(harfile, "network", @device, s3_params, @server_config, @test_framework, @genre)
        else
          mitm_restart_retries = mitm_restart(test_id)
          BrowserStack::Zombie.push_logs("maestro-mitm-restart-error", "error", { "session_id" => @session_id, "error" => "Mitm Restart Failure retry_count: #{mitm_restart_retries}" }) if mitm_restart_retries > 0
        end
      end

      # s3_params = get_s3_params("instrumentation-logs.txt", testname)
      # create_upload_request(s3_params, @instrumentation_file, "instru")
      # BrowserStack.logger.info("instrumentation_file upload request created")
      # FileUtils.rm_rf @instrumentation_file
      log_finish "upload_logfiles"
    end

    def mitm_restart(test_id, retry_count = 0, current_privoxy_port = 0, new_privoxy_port = 0)
      if retry_count < MAX_MITM_RESTART_RETRY_COUNT
        BrowserStack.logger.info("Session Id: #{@session_id}, MITM Restart Params: Retry Count #{retry_count}, current_privoxy_port: #{current_privoxy_port}, new_privoxy_port: #{new_privoxy_port}")
        MitmProxy.stop_proxy(@device, @session_id, "xcuitest")
        # Do not capture har file if already captured. If the ports are 0 in subsequent retries means, stop_proxy raised exceptions
        current_privoxy_port, new_privoxy_port = capture_har_file(test_id) unless current_privoxy_port > 0 && new_privoxy_port > 0
        BrowserStack.logger.info("Session Id: #{@session_id}, current_privoxy_port: #{current_privoxy_port}, new_privoxy_port: #{new_privoxy_port}")
        @mitmproxy.boot(current_privoxy_port, new_privoxy_port)
      end
      retry_count
    rescue => e
      lsof_current_privoxy_port_output = `lsof -i:#{current_privoxy_port} | tail`
      lsof_new_privoxy_port_output = `lsof -i:#{new_privoxy_port} | tail`
      BrowserStack.logger.info("Session Id: #{@session_id}, lsof current privoxy port output: #{lsof_current_privoxy_port_output}, lsof new privoxy port output: #{lsof_new_privoxy_port_output}")
      sleep_time = retry_count * 2
      BrowserStack.logger.error("Session Id: #{@session_id}, Retry_count: #{retry_count}, Retrying in: #{sleep_time} sec, Exception in Mitm Boot: #{e.message} backtrace: #{e.backtrace.join("\n")}")
      sleep sleep_time
      retry_count += 1
      if retry_count == MAX_MITM_RESTART_RETRY_COUNT
        BrowserStack::Zombie.push_logs("maestro-mitm-restart-error", "error", { "session_id" => @session_id, "error" => "Mitm Restart Failure retry_count: #{retry_count}" })
        raise e
      end

      mitm_restart(test_id, retry_count, current_privoxy_port, new_privoxy_port)
    end

    def capture_har_file(test_id)
      s3_params = get_s3_params("har-logs.txt", test_id)
      harfile = @mitmproxy.capture_har_for_framework
      BrowserStack.logger.info("Session Id: #{@session_id}, Harfile Generated")
      Utils.create_upload_request(harfile, "network", @device, s3_params, @server_config, @test_framework, @genre)
      PrivoxyManager.change_port(@device, @device_config, false)
    end

    def upload_screenshots_files(flow_file_name, test_num, test_id)
      screenshot_patterns = [
        File.join(@debug_output, "*.png"),
        File.join(@debug_output, "*.jpeg"),
        File.join(@debug_output, "*.jpg")
      ]

      screenshot_files = screenshot_patterns.flat_map { |pattern| Dir.glob(pattern) }

      ignore_pattern = /screenshot-[^a-zA-Z0-9]-?\d+-\([^)]+\)\.(png|jpeg|jpg)$|^[^a-zA-Z0-9-_]+\.(png|jpeg|jpg)$/i

      screenshot_files = screenshot_files.reject do |file|
        basename = File.basename(file)
        ignored = basename =~ ignore_pattern
        BrowserStack.logger.info("Ignoring screenshot file: #{basename}") if ignored
        ignored
      end

      if screenshot_files.empty?
        BrowserStack.logger.info("No screenshots found in #{@debug_output}")
      else
        BrowserStack.logger.info("Grouping and uploading screenshots from #{@debug_output}")

        # Create a temporary ZIP file
        zip_file_path = "/tmp/#{@device}_#{@session_id}_#{test_num}_screenshots_#{flow_file_name}.zip"
        FileUtils.rm_f(zip_file_path) if File.exist?(zip_file_path)

        Zip::File.open(zip_file_path, Zip::File::CREATE) do |zipfile|
          screenshot_files.each do |file|
            next unless File.exist?(file)

            screenshot_filename = File.basename(file)
            zipfile.add(screenshot_filename, file)
            BrowserStack.logger.info("Added #{screenshot_filename} to ZIP")
          end
        end

        # Upload the ZIP file to S3
        if File.exist?(zip_file_path) && File.size(zip_file_path) > 0
          s3_params = get_s3_params("screenshots.zip", test_id)
          create_upload_request(zip_file_path, "maestro_screenshots", @device, s3_params, @server_config, @test_framework, @genre, "logs_upload")
          BrowserStack.logger.info("Uploaded ZIP file #{zip_file_path} to S3")
        else
          BrowserStack.logger.info("ZIP file #{zip_file_path} is empty or not created")
        end
      end
    end

    def upload_command_logs(commands_file, test_id, test_num, flow_file_name)
      if commands_file && File.exist?(commands_file)
        s3_params = get_s3_params("commands.json", test_id)
        commands_file_tmp_path = "/tmp/#{@device}_#{@session_id}_#{test_num}_commands-#{flow_file_name}.json"
        BrowserStack::OSUtils.execute("cp '#{commands_file}' '#{commands_file_tmp_path}'")
        BrowserStack::OSUtils.execute("chown app '#{commands_file_tmp_path}'")
        if defined?(MaestroCommandGenerator)
          command_generator = MaestroCommandGenerator.new(
            commands_file_tmp_path,
            logger_params: BrowserStack.logger.params,
            logger: BrowserStack.logger
          )
          nested_commands_path = command_generator.process
          if nested_commands_path
            create_upload_request(nested_commands_path, "command_logs", @device, s3_params, @server_config, @test_framework, @genre, "logs_upload")
            BrowserStack.logger.info("Uploaded #{nested_commands_path} to S3")
            FileUtils.rm_f(commands_file)
            FileUtils.rm_f(commands_file_tmp_path)
          end
        else
          BrowserStack.logger.info "MaestroCommandGenerator is not available, skipping execution."
        end
      else
        BrowserStack.logger.info("#{commands_file} not found")
      end
    end

    def upload_maestro_debug_files(flow_file_name, test_name, test_num, extension, test_id)
      log_start "upload_maestro_debug_files"

      debug_dirs = Dir.glob(@debug_output)

      if debug_dirs.empty?
        BrowserStack.logger.info("No Maestro debug directories found under #{@debug_output}")
        return
      end

      debug_dir = debug_dirs.max

      # Define file patterns to upload
      commands_file = File.join(debug_dir, "commands-(#{flow_file_name}).json")
      maestro_log_path = File.join(debug_dir, "maestro.log")

      # Upload commands-<flow_file_name>.json if it exists
      BrowserStack.logger.info("Uploading debug files from #{debug_dir} #{maestro_log_path} #{commands_file} #{flow_file_name}")
      upload_command_logs(commands_file, test_id, test_num, flow_file_name)

      # Upload maestro.log if it exists
      if maestro_log_path && File.exist?(maestro_log_path)
        s3_params = get_s3_params("maestro.log", test_id)
        maestro_file_tmp_path = "/tmp/#{@device}_#{@session_id}_#{test_num}_maestro.log"
        BrowserStack::OSUtils.execute("cp '#{maestro_log_path}' '#{maestro_file_tmp_path}'")
        BrowserStack::OSUtils.execute("chown app '#{maestro_file_tmp_path}'")
        create_upload_request(maestro_file_tmp_path, "maestro_logs", @device, s3_params, @server_config, @test_framework, @genre, "logs_upload" )
        BrowserStack.logger.info("Uploaded #{maestro_log_path} to S3")
        FileUtils.rm_f(maestro_log_path)
      else
        BrowserStack.logger.info("File #{maestro_log_path} not found")
      end

      upload_screenshots_files(flow_file_name, test_num, test_id)
      log_finish "upload_maestro_debug_files"
    rescue => e
      BrowserStack.logger.info("Error uploading Maestro debug files: #{e.message}\n#{e.backtrace.join("\n")}")
    end

    def sync_upload(s3_params, file_name, type = "")
      log_start "sync_upload"
      curr_time = (Time.now.to_f * 1000).to_i
      temp_file = s3_params['is_json'] || s3_params[:is_json] ? "/tmp/#{@device}_xctest_log_summary_#{curr_time}.log" : "/tmp/#{@device}_xctest_log_#{curr_time}.log"
      BrowserStack::OSUtils.execute("cp #{file_name} #{temp_file}")
      BrowserStack::OSUtils.execute("chown app #{temp_file}")

      session_id = s3_params[:session_id]
      s3_url = s3_params[:s3_url].to_s
      acl = s3_params['no_acl'] ? "" : "public-read"
      content_type = s3_params['is_json'] || s3_params[:is_json] ? "text/json" : "text/plain"
      region = s3_params[:aws_bucket] == "bs-stag" || s3_params[:aws_region] == "us-east-1" ? nil : (s3_params[:aws_region]).to_s

      unless File.exist?(temp_file)
        BrowserStack::Zombie.push_logs("#{type}-upload-request-file-not-found", "File missing", { "session_id" => session_id })
        raise("Cannot find #{temp_file} on disc, Cannot upload.")
      end

      BrowserStack.logger.info("Summary file uploaded to: #{s3_url}")
      file_size = -1
      if File.file?(temp_file)
        file_size = begin
          File.stat(temp_file).size.to_i
        rescue
          -1
        end
      end
      updated_storage_class = Utils.s3_get_storage_class_based_on_obj_size(file_size, s3_params[:aws_storage_class])
      ret, error = Utils.upload_file_to_s3(s3_params[:aws_key], s3_params[:aws_secret], content_type, temp_file, acl, s3_url, session_id, @genre, region, 300, {}, {}, updated_storage_class)
      handle_upload_result(ret, error, s3_params, type)
      log_finish "sync_upload"
    end

    def handle_upload_result(ret, error, s3_params, type)
      prefix_genre = Utils.get_prefix_genre(@genre)
      session_id = s3_params[:session_id]
      if !ret && !error.empty?
        BrowserStack::Zombie.push_logs("#{prefix_genre}#{@test_framework}_#{type}logs-upload-timeout", "Failed to upload: #{ret}", { "session_id" => session_id })
        raise "Error while uploading to S3: #{error}"
      else
        BrowserStack::Zombie.push_logs("#{prefix_genre}#{@test_framework}_#{type}logs-upload-done", "success", { "session_id" => session_id })
      end
    end

    def create_upload_request(upload_request_file, type, device, s3_params, server_config, framework = nil, genre = nil, upload_type = "logs_upload")
      upload_request = {
        file_name: upload_request_file,
        s3_params: s3_params,
        type: type,
        framework: framework,
        genre: genre,
        upload_type: upload_type
      }
      request_file = server_config["other_files_to_upload_dir"] + "/#{type}_logs_upload_#{SecureRandom.uuid}.json"
      dir_name = File.dirname(request_file)
      FileUtils.mkdir_p(dir_name) unless Dir.exist? dir_name
      Utils.write_to_file(request_file, upload_request.to_json)
    end

    def upload_summary
      # Session Summary File v2
      s3_params = get_s3_params("summary-v2.json")
      s3_params['is_json'] = true
      flatten_summary_v2
      sync_upload(s3_params, @summary_file_v2, "summary")

      s3_params = get_s3_params("summary.json")
      s3_params['is_json'] = true
      # create_upload_request(s3_params, @summary_file, "summary")
      # uploading summary inline
      sync_upload(s3_params, @summary_file, "summary") if @summary_file && File.exists?(@summary_file)

      BrowserStack.logger.info("summary.json upload request created")
    end

    # Other methods (e.g., upload_summary, flatten_summary_v2, etc.) can remain unchanged or be adapted as needed
  end

  def start_timeout_manager
    log_start "start_timeout_manager"

    if defined?(AppAutomateFrameworks::MaestroTimeoutManager)
      maestro_manager = AppAutomateFrameworks::MaestroTimeoutManager.new(
        @device,
        "#{@debug_output}/maestro.log",
        logger: BrowserStack.logger,
        framework: "maestro",
        test_id: nil,
        logger_params: BrowserStack.logger.params,
        script_identifier: ["spawn_xctest.rb"]
      )

      maestro_manager.start(@params["idle_timeout"])
    else
      BrowserStack.logger.info "MaestroTimeoutManager is not available, skipping execution."
    end
    log_finish "start_timeout_manager"
  end

  def stop_timeout_manager
    log_start "stop_timeout_manager"

    if defined?(AppAutomateFrameworks::MaestroTimeoutManager)
      maestro_manager = AppAutomateFrameworks::MaestroTimeoutManager.new(
        @device,
        "#{@debug_output}/maestro.log",
        logger: BrowserStack.logger,
        framework: "maestro",
        test_id: nil,
        logger_params: BrowserStack.logger.params,
        script_identifier: ["spawn_xctest.rb"]
      )

      maestro_manager.stop
    else
      BrowserStack.logger.info "MaestroTimeoutManager is not available, skipping execution."
    end
    log_finish "stop_timeout_manager"
  end
end