require 'benchmark'
require 'fileutils'

require_relative './session'
require_relative '../utils/idevice_utils'
require_relative '../utils/web_driver_agent'
require_relative '../utils/device_logger'
require_relative '../checks/check_device'
require_relative '../../server/iphone'
require_relative '../utils/helpers'
require_relative '../utils/utils'
require_relative '../mitm_proxy'
require_relative '../privoxy_manager'
require_relative '../../server/device_manager'
require_relative '../apps/chrome'
require_relative '../helpers/apple_pay'
require_relative '../helpers/device_sim_helper'
require_relative '../helpers/local_testing_chrome_extension_helper'
require_relative '../helpers/wda_client'
require_relative '../utils/download_file'
require_relative '../device_setup/appstore_installer/appstore_installer'
require_relative '../configuration'
require_relative '../helpers/browserstack_app_helper'
require_relative '../../config/constants'
require_relative '../models/device_state'
require_relative '../helpers/data_report_helper'
require_relative '../helpers/location_simulator'
require_relative '../helpers/geoguard_helper'
require_relative '../helpers/browser_activity_monitoring'
require_relative '../helpers/session_save_polling'
require_relative '../utils/redis'

module BrowserStack
  class LiveSession < BrowserStack::Session # rubocop:todo Metrics/ClassLength
    def start # rubocop:todo Metrics/MethodLength, Metrics/AbcSize
      super
      t0 = Time.now.to_f
      # ios_parallel_execution_of_methods flag from railsApp is boolean
      execute_methods_in_parallel = @params[:ios_parallel_execution_of_methods].to_s == "true"
      @params['session_start_events'].merge!({ ios_parallel_execution_of_methods: execute_methods_in_parallel })

      # ios_start_request_wait_time is fetched from LD ( > 25s and <= 60s ) or else railsApp ( default wait time of 25s ), it will never be nil
      timeout_value = @params[:ios_start_request_wait_time].to_i || 25
      @params['session_start_events'].merge!({ ios_start_request_wait_time: timeout_value })

      if execute_methods_in_parallel
        Timeout.timeout(timeout_value) do
          run_methods_in_parallel([
            'kill_browser_during_switch',
            'setup_local_and_start_mitm',
            'start_webdriver_agent',
            'start_device_logger',
            'set_geolocation'
          ])
        end
      else
        @session_stats = Benchmark.bm(25) do |bm|
          bm.report("kill_browser_during_switch") do
            Thread.bs_run { kill_browser_during_switch }
          end

          bm.report("setup_local") do
            Session.setup_local(@params, @device_config)
          end

          bm.report("start_mitm_proxy") do
            start_mitm_proxy
          end

          bm.report("start_webdriver_agent") do
            start_webdriver_agent
          end

          bm.report("start_device_logger") do
            start_device_logger
          end

          bm.report("set_geolocation") do
            set_geolocation
          end
        end
        @params['session_start_events'].merge!(@session_stats.map { |stat| [stat.label, (stat.real * 1000).to_i] }.to_h)
      end

      if @params['enable_sim_live'].to_s == "true"
        Utils.fork_code_block_for_device(@device) do
          sim_setup
        end
      end

      if @params['live_apple_pay_session'].to_s == "true" || Secure::ApplePay.dedicated_cloud_apple_pay_device?(@device)
        Utils.fork_code_block_for_device(@device) do
          apple_pay_setup
        end
      end

      if @params["localTestingChromeExtensionEnabled"].to_s == "true"
        localTestingChromeExtension = LocalTestingChromeExtension.new(@device)
        localTestingChromeExtension.touch_state_file
      end

      if @params['mobile_install_geoguard_app'].to_s == "true"
        Utils.fork_code_block_for_device(@device) do
          start_geoguard_flow
        end
      end

      Utils.fork_code_block_for_device(@device) do
        download_file_setup
      end

      t1 = Time.now.to_f
      @params['session_start_events'].merge!({ overall_live_session_methods: ((t1 - t0) * 1000.0).to_i })
    end

    def restart
      unless @params['actions'].nil?
        t0 = Time.now.to_f
        actions_to_execute = []
        @session_restart_stats = []
        begin
          actions_to_execute = @params['actions'].split(',')
        rescue => e
          BrowserStack.logger.error("[restart] received invalid actions, error: #{e.message}")
          raise e
        end
        actions_to_execute.each do |action|
          @session_restart_stats << Benchmark.bm(25) do |bm|
            bm.report(action) { execute_restart_action(action) }
          end
        end
        @session_restart_stats = @session_restart_stats.map(&:first)
        if @params['session_restart_events']
          @params['session_restart_events'].merge!(@session_restart_stats.map { |stat| [stat.label, (stat.real * 1000).to_i] }.to_h)
          t1 = Time.now.to_f
          @params['session_restart_events'].merge!({ overall_live_session_methods: ((t1 - t0) * 1000.0).to_i })
        end
      end
    end

    def stop
      super
      begin
        if BrowserActivityMonitoring.running?(@device)
          File.delete(BrowserActivityMonitoring.start_file(@device))
          BrowserStack.logger.info("/stop BrowserActivityMonitoring removing start file for #{@params[:live_session_id]}")
        end
      rescue => e
        BrowserStack.logger.error("/stop BrowserActivityMonitoring removing start file exception: #{e.message}")
      end
      begin
        local_file_path = "/usr/local/.browserstack/state_files/#{@device}_cookie_data_from_s3.json"
        if @params["additional_action"] == "enable_cookie_restore"
          if BrowserStack::HttpUtils.upload_to_s3_with_presigned_url(@params[:pre_signed_url], local_file_path)
            BrowserStack.logger.info("[SessionSavePolling] Uploaded the cookies file to S3")
          else
            BrowserStack.logger.error("[SessionSavePolling] Failed to upload cookies file to S3")
          end
        end

        if File.exist?(local_file_path)
          File.delete(local_file_path)
          BrowserStack.logger.info("[SessionSavePolling] Deleted the cookies file from local path")
        end
        if SessionSavePolling.running?(@device)
          File.delete(SessionSavePolling.start_file(@device))
          BrowserStack.logger.info("[SessionSavePolling] /stop SessionSavePolling removing start file for #{@params[:live_session_id]}")
        end
      rescue => e
        BrowserStack.logger.error("[SessionSavePolling] /stop SessionSavePolling removing start file exception: #{e.message}")
      end
    end

    private

    def sim_setup
      t0 = Time.now.to_f

      BrowserStack.logger.info "[SIM SETUP FOR LIVE]"
      sim_helper = DeviceSIMHelper.new(@device, @params['session_id'], @params['genre'], user_id: @params['user_id'])
      result = sim_helper.session_setup
      unless result
        Utils.notify_pusher_for_live("PUBLIC_SIM_SETUP_UNSUCCESSFUL", @params, @device)
        raise BrowserStack::SessionException, "Can't initiate session on device #{@device} with SIM"
      end
      Utils.notify_pusher_for_live("PUBLIC_SIM_SETUP_SUCCESSFUL", @params, @device)
      # calling launch browser from here as session needs to start with the browser open which isn't happening as sim setup calls automation and ends with home screen
      BrowserStack::WebDriverAgent.launch_browser_with_url(
        (@params["use_blank_url"] ? '' : @params["url"]),
        @params["device_browser"],
        @params["iproxyPort"],
        @params["device_version"].to_f
      )

      # ensuring device logger is started
      start_device_logger(true)

      t1 = Time.now.to_f
      @params['session_restart_events']&.merge!({ sim_setup: ((t1 - t0) * 1000.0).to_i })
    end

    def apple_pay_setup
      t0 = Time.now.to_f
      Secure::ApplePay.setup(@device, @params)
      t1 = Time.now.to_f
      @params['session_restart_events'].merge!({ apple_pay_setup: ((t1 - t0) * 1000.0).to_i })
    end

    def start_geoguard_flow
      geoguard_helper = GeoguardHelper.new(@device, @params["session_id"], BrowserStack.logger)
      geoguard_helper.setup if geoguard_helper.geoguard_enabled?
      Utils.notify_pusher_for_live("GeoGuard Location Validator successfully installed and configured", @params, @device)
    rescue => e
      Utils.notify_pusher_for_live("Failed to install GeoGuard Location Validator", @params, @device)
    end

    def run_methods_in_parallel(methods_array)
      threads = []
      methods_array.each { |method_name| threads << Thread.bs_run { send(method_name) } }
      threads.map(&:join)
    end

    def setup_local_and_start_mitm
      t0 = Time.now.to_f
      setup_local
      start_mitm_proxy
      t1 = Time.now.to_f
      # @params['session_start_events'].merge!({ setup_local_and_start_mitm: ((t1 - t0) * 1000.0).to_i })
    end

    # In case of switch browser, kill the unused browser and restart iwdp for devtools to work.
    # Safari to Chrome switch -> Kill safari ()
    # Chrome to Safari switch -> Kill chrome
    # Jira ticket for more info: https://browserstack.atlassian.net/browse/LIVE-8333
    def kill_browser_during_switch
      t0 = Time.now.to_f
      if @params['loader_trigger'] == "switcher" && @is_update_session
        case @params['device_browser'].to_s
        when "safari"
          app_to_kill = Chrome.new
          app_to_kill.cleanup(@device, @params[:device_version].to_i)
          restart_iwdp_on_switch_browser
        when "chrome"
          # Kill Safari
          begin
            BrowserStack.logger.info("[kill_browser_during_switch] Killing Safari browser...")
            current_device_config = DeviceManager.device_configuration_check(@device)
            wda_port = current_device_config["webdriver_port"].to_i
            WdaClient.new(wda_port).kill_apps([BrowserStack::IPhone::SAFARI_APP])
            BrowserStack.logger.info("[kill_browser_during_switch] Killed Safari browser...")
            restart_iwdp_on_switch_browser
          rescue => e
            BrowserStack.logger.error("[kill_browser_during_switch] Got error: #{e.message}, backtrace: #{e.backtrace.join('\n')}")
          end
        else
          BrowserStack.logger.info "Unsupported browser passed during switch: #{@params[:device_browser]} #{@device}"
        end
      end
      t1 = Time.now.to_f
      # @params['session_start_events'].merge!({ kill_browser_during_switch: ((t1 - t0) * 1000.0).to_i })
    end

    def restart_iwdp_on_switch_browser
      idevice = BrowserStack::IPhone.new(@params, @device)
      idevice.restart_webkit_proxy
    end

    def setup_local
      t0 = Time.now.to_f
      if @params['loader_trigger'].to_s != "local" && @params[:use_ios_connect_time_improvement_logic_phase4].to_s == "true"
        device = @params[:device] || @params["device"]
        PrivoxyManager.privoxy_domain_block_whitelist(device, @params[:group_id], @params[:group_plan_type], @params[:privoxy_domain_control_flag]) unless device.nil?
        BrowserStack.logger.info("[iosConnectTime] skip local setup: true")
      else
        Session.setup_local(@params, @device_config)
        BrowserStack.logger.info("[iosConnectTime] skip local setup: false")
      end

      t1 = Time.now.to_f
      @params['session_start_events'].merge!({ setup_local: ((t1 - t0) * 1000.0).to_i })
    end

    def start_webdriver_agent
      BrowserStack.logger.info("Starting Web Driver Agent")
      t0 = Time.now.to_f
      dc = CheckDevice.new(@device, REDIS_CLIENT)
      creative_offline_mode_enabled = device_state.dedicated_device_file_present? && @params["creative_offline_mode_enabled"].to_s == "true" && !@device_config["sim_details"].empty?

      if creative_offline_mode_enabled
        device_sim_status
        dc.check_device_internet_during_session("USB", "Session Start", @params["live_session_id"])
        network_simulator.apply_offline_mode
      end

      launch_safari_via_wda
      BrowserStack::WebDriverAgent.launch(@device, @mini_ip, @params)

      if creative_offline_mode_enabled
        # Adding sleep to give enough time to WDA to start streaming via mobile data,
        # this is just an additional step, as even if streaming is not done via mobile data,
        # it will be done via USB Internet Sharing, and in-session even if USB Internet Sharing
        # is disabled, webrtc re-connection will connect it back using mobile data.
        sleep 5
        # Internet Check After sleep
        dc.check_device_internet_during_session("Mobile Data", "Session Start", @params["live_session_id"])
        network_simulator.apply_online_mode
      end

      start_browser_activity_monitoring

      @params['set_cookies'] = true
      start_session_save_polling

      t1 = Time.now.to_f
      @params['session_start_events'].merge!({ start_webdriver_agent: ((t1 - t0) * 1000.0).to_i })
    end

    def device_sim_status
      sim_present = DeviceSIMHelper.sim_inserted?(@device)
      BrowserStack.logger.info("Sim Present In Device : #{sim_present}")
      BrowserStack::Zombie.push_logs("device-sim-status", "", { "device" => @device, "session_id" => @params["live_session_id"], "data" => { "sim_present" => sim_present } })
    rescue => e
      BrowserStack.logger.error("Error checking SIM status: #{e.message}")
      BrowserStack::Zombie.push_logs("device-sim-status-error", e.message , { "device" => @device, "session_id" => @params["live_session_id"] })
    end

    def launch_safari_via_wda
      # Launch safari via bundle ID to fix the devtools issue on 17.4+
      # Jira: https://browserstack.atlassian.net/browse/LIVE-13686
      if @params['device_browser'] == 'safari' && @params["ios_should_launch_safari_using_bundle_id"].to_s == "true"
        current_device_config = DeviceManager.device_configuration_check(@device)
        wda_port = current_device_config["webdriver_port"].to_i
        launch_safari_start = Time.now.to_f
        WdaClient.new(wda_port).launch_app_with_bundle_id(BrowserStack::IPhone::SAFARI_APP)
        time_taken = Time.now.to_f - launch_safari_start
        BrowserStack::Zombie.push_logs("launch-safari-using-bundle-id", "", { "device" => @device, "session_id" => @params["live_session_id"], "data" => { "time_taken" => time_taken } })
      end
    end

    def start_browser_activity_monitoring
      return if @params[:enable_url_detection].to_s != "true"

      is_monitoring_running = BrowserActivityMonitoring.running?(@device)

      # if monitoring not running
      # create instance of BrowserActivityMonitoring
      # call start - this will start a while loop inside a thread
      # otherwise, update session id of already running monitoring
      if !is_monitoring_running
        debugger_port = DeviceManager.device_configuration_check(@device)['debugger_port']
        browser_activity_monitoring = BrowserActivityMonitoring.new(@params[:live_session_id], @device,
                                                                    debugger_port, @params[:genre])
        BrowserStack.logger.info("start_browser_activity_monitoring calling start for #{@params[:live_session_id]}")
        browser_activity_monitoring.start
      else
        BrowserStack.logger.info("start_browser_activity_monitoring updating session_id to #{@params[:live_session_id]}")
        File.open(BrowserActivityMonitoring.start_file(@device), 'w') do |file|
          file.flock(File::LOCK_EX)
          file.puts(@params[:live_session_id])
          file.flock(File::LOCK_UN)
        end
      end
    rescue => e
      BrowserStack.logger.error("start_browser_activity_monitoring exception: #{e.message}")
      File.delete(BrowserActivityMonitoring.start_file(@device)) if BrowserActivityMonitoring.running?(@device)
    end

    # rubocop:disable Metrics/AbcSize
    def start_session_save_polling
      return if @params["additional_action"] != "enable_cookie_restore"

      debugger_port = DeviceManager.device_configuration_check(@device)['debugger_port']

      BrowserStack.logger.info("[SessionSavePolling] Starting session save polling from start_session_save_polling method, params[:live_session_id]: #{@params[:live_session_id]}, params[:debugger_port]: #{debugger_port}, params[:genre]: #{@params[:genre]}, params[:set_cookies]: #{@params[:set_cookies]}")

      local_file_path = "/usr/local/.browserstack/state_files/#{@device}_cookie_data_from_s3.json"
      if BrowserStack::HttpUtils.download_from_s3_with_presigned_url(@params[:pre_signed_url], local_file_path)
        BrowserStack.logger.info("[SessionSavePolling] Downloaded the cookies file from S3")
      else
        BrowserStack.logger.error("[SessionSavePolling] Failed to download cookies file from S3")
      end

      is_polling_running = SessionSavePolling.running?(@device)

      if !is_polling_running
        session_save_polling = SessionSavePolling.new(@params[:live_session_id], @device,
                                                      debugger_port, @params[:genre], @params[:set_cookies])
        BrowserStack.logger.info("[SessionSavePolling] start_session_save_polling calling start for #{@params[:live_session_id]}")
        session_save_polling.start
      else
        BrowserStack.logger.info("[SessionSavePolling] start_session_save_polling updating session_id to #{@params[:live_session_id]}")
        File.open(SessionSavePolling.start_file(@device), 'w') do |file|
          file.flock(File::LOCK_EX)
          file.puts(@params[:live_session_id])
          file.flock(File::LOCK_UN)
        end
      end
    rescue => e
      BrowserStack.logger.error("[SessionSavePolling] start_session_save_polling exception: #{e.message}")
      File.delete(SessionSavePolling.start_file(@device)) if SessionSavePolling.running?(@device)
    end
    # rubocop:enable Metrics/AbcSize

    def start_device_logger(force_setup_when_sim = false)
      return if Utils.bridgecloud_ios_voiceover_session?(@params)

      t0 = Time.now.to_f
      if update_session?
        BrowserStack::DeviceLogger.clean_workspace(@device)
        sleep 0.2
      end

      # condition added so device logger doesn't start when sim setup is to be called, and to start device logger when sim set up is done
      ignore_setup_when_sim = (force_setup_when_sim || !(@params['enable_sim_live'] || @params['enableSim']))

      if (@params['is_whitelisted_for_self_cert_flow'].nil? || @params['is_whitelisted_for_self_cert_flow'] != 'true') && ignore_setup_when_sim
        # The above field will only be passed for applive users who are whitelisted for self_certificate flow and also for sim sessions
        BrowserStack::DeviceLogger.start(@device)
      end
      t1 = Time.now.to_f
      begin
        @params['session_start_events'].merge!({ start_device_logger: ((t1 - t0) * 1000.0).to_i })
      rescue
        nil
      end
    end

    def set_geolocation
      t0 = Time.now.to_f
      LocationSimulator.new(device).simulate(@params["latitude"], @params["longitude"]) if !@params["latitude"].nil? && !@params["latitude"].empty? && !@params["longitude"].nil? && !@params["longitude"].empty?
      t1 = Time.now.to_f
      begin
        @params['session_start_events'].merge!({ set_geolocation: ((t1 - t0) * 1000.0).to_i })
      rescue
        nil
      end
    end

    # TODO: Benchmark this on prod and improve, as this can impact connect time.
    def start_mitm_proxy #rubocop:todo Metrics/AbcSize, Metrics/MethodLength
      t0 = Time.now.to_f
      current_device_config = DeviceManager.device_configuration_check(@device)
      if @params["live_self_signed_certs"] == "true" || @params["localTestingChromeExtensionEnabled"] == "true"
        mitm_proxy_options = {
          "genre" => @params["genre"],
          "session_id" => @params["live_session_id"],
          "nw_filter_regex" => @params["nw_filter_regex"],
          "network_logs_port" => current_device_config["app_live_network_logs_port"],
          "custom_headers" => @params["custom_headers"],
          "networkLogs" => @params["networkLogs"],
          "exclude_host_regex" => @params["exclude_host_regex"],
          "include_host_regex" => @params["include_host_regex"],
          "interaction_sync" => @params["interaction_sync"],
          "interaction_user_id" => @params["user_id"],
          "interaction_tab_id" => @params["interaction_tab_id"],
          "interaction_sync_host" => @params["interaction_sync_host"],
          "interaction_script_url" => @params["interaction_script_url"],
          "override_csp" => @params["override_csp"],
          "local_testing_chrome_extension_enabled" => @params["localTestingChromeExtensionEnabled"],
          "applive_should_use_upgraded_mitmproxy_v10" => @params["applive_should_use_upgraded_mitmproxy_v10"]
        }
        if !MitmProxy.running?(@device)
          BrowserStack.logger.info "[MITM] [#{@params['session_id']}] proxy not running - Starting"
        else
          BrowserStack.logger.info "[MITM] [#{@params['session_id']}] proxy running - Restarting"
          MitmProxy.stop_proxy(@device)
          PrivoxyManager.reset_proxy(@device, current_device_config, @params)
        end
        DeviceManager.setup_mitm_proxy(@device, current_device_config, mitm_proxy_options)
      elsif MitmProxy.running?(@device) && (@params["live_self_signed_certs"] == "false" || @params["live_self_signed_certs"].nil?)
        BrowserStack.logger.info "[MITM] [#{@params['session_id']}] proxy is already running - Disabling"
        MitmProxy.stop_proxy(@device)
        PrivoxyManager.reset_proxy(@device, current_device_config, @params)
      elsif !@params[:ipLocationCode].nil? && @params[:ipLocationCode] != "-1"
        PrivoxyManager.reset_proxy(@device, current_device_config, @params)
      end
      t1 = Time.now.to_f
      @params['session_start_events'].merge!({ start_mitm_proxy: ((t1 - t0) * 1000.0).to_i })
      true
    rescue => e
      BrowserStack.logger.error "[MITM] [#{@params['session_id']}] Error in starting mitm_proxy: #{e.message} #{e.backtrace.join('\n')} - Resetting privoxy"
      MitmProxy.stop_proxy(@device)
      PrivoxyManager.reset_proxy(@device, current_device_config)
      false
    end

    def download_file_setup
      t0 = Time.now.to_f
      download_file_helper = DownloadFile.new(@device, @params["session_id"], "live")
      download_file_helper.setup
      # do not raise an exception if download file setup fails. continue as usual.
      t1 = Time.now.to_f
      @params['session_start_events'].merge!({ download_file_setup: ((t1 - t0) * 1000.0).to_i })
    end

    def fetch_valid_url(debugger_port, device_version)
      url = DeviceManager.get_url(@device, debugger_port, device_version)
      url = Utils.valid_live_url?(url) ? url : ''
      if @params["interaction_sync"].to_s == "true"
        url = @params['url'] unless @params['url'].nil?
        eds_data = {
          event_name: 'live_interaction_sync_url_opened',
          session_id: @params['live_session_id'],
          url_opened: url,
          device_id: @params['device'],
          remote_browser: @params['device_browser'],
          platform: 'ios',
          product: 'live',
          team: 'live'
        }
        Utils.send_to_eds(eds_data, "web_events", true)
      end
      url
    end

    def reconnect_streaming
      dup_session_params = JSON.parse(File.read(BrowserStack::IPhone.session_file(@device)))
      dup_session_params.delete("app_display_name")
      dup_session_params["mini_ip"] = @mini_ip
      dup_session_params["allow_long_press"] = "false" if dup_session_params['allow_long_press'].nil?
      endpoint = dup_session_params["ios_should_use_set_rtcdata_v2_endpoint"].to_s == "true" ? '/bs/set_rtcdata_and_start_v2' : '/bs/set_rtcdata_and_start'

      current_device_config = DeviceManager.device_configuration_check(@device)
      wda_port = current_device_config["webdriver_port"].to_i
      url = "http://127.0.0.1:#{wda_port}/bs/live/stop?is_voiceover_enabled=false"
      response = BrowserStack::HttpUtils.make_get_request(url, 100)

      url = "http://127.0.0.1:#{wda_port}#{endpoint}"
      data = { rtc_data: dup_session_params, url: dup_session_params["url"], browser: dup_session_params["device_browser"], genre: @params["genre"] }
      BrowserStack::HttpUtils.send_post_raw(url, data.to_json, basic_auth = nil, 100)
    end

    def cookie_restore_settings
      if @params["additional_action"] == "enable_cookie_restore"
        @params[:set_cookies] = false
        BrowserStack.logger.info("[SessionSavePolling] Starting session save polling from cookie_restore_settings request #{@device}")
        start_session_save_polling
      else
        BrowserStack.logger.error("[SessionSavePolling] received invalid additional_action")
      end
    end

    # rubocop:disable Metrics/AbcSize
    def execute_restart_action(action_name)
      case action_name
      when 'proxy-setting'
        setup_local if !@params[:ipLocationCode].nil? && (@params[:tunnelPresent] == "true" || !@params[:localAppIdentifier].empty?)
        start_mitm_proxy
        if @params['device_browser']
          current_device_config = DeviceManager.device_configuration_check(@device)
          wda_port = current_device_config["webdriver_port"].to_i
          device_version = current_device_config["device_version"]
          url = fetch_valid_url(current_device_config['debugger_port'], device_version)
          WdaClient.new(wda_port).kill_apps([BrowserStack::IPhone::SAFARI_APP]) if @params['device_browser'] == 'safari'
          WdaClient.new(wda_port).kill_apps([CHROME_BUNDLE_ID]) if @params['device_browser'] == 'chrome'
          # After benchmarking, we determined that 3 seconds is a safe amount of time for MITM to start,
          # so a 3-second sleep has been added.
          sleep 3
          BrowserStack::WebDriverAgent.launch_browser_with_url(
            url,
            @params['device_browser'],
            wda_port,
            device_version.to_f
          )
        end
      when 'reconnect-streaming'
        reconnect_streaming
      when 'cookie_restore_settings'
        cookie_restore_settings
      else
        BrowserStack.logger.error("[execute_restart_action] received invalid action_name")
      end
    rescue => e
      BrowserStack::Zombie.push_logs("execute-restart-action-failed", e.message, { "device" => @device, "session_id" => @params["live_session_id"].to_s })
      BrowserStack.logger.error("[execute_restart_action] exception received: #{e.message}")
      raise e
    end
    # rubocop:enable Metrics/AbcSize
  end
end
