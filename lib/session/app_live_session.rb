require 'English'
require 'fileutils'
require 'shellwords'
require 'benchmark'
require_relative './live_session'
require_relative '../utils/utils'
require_relative '../utils/osutils'
require_relative '../utils/http_utils'
require_relative '../../server/iphone'
require_relative '../utils/idevice_utils'
require_relative '../utils/helpers'
require_relative '../utils/app_analytics_util'
require_relative '../../server/device_manager'
require_relative '../helpers/wda_client'
require_relative '../helpers/browserstack_app_helper'
require_relative '../helpers/device_sim_helper'
require_relative '../helpers/apple_pay'
require_relative '../utils/download_file'
require_relative '../helpers/app_accessibility'
require_relative '../helpers/location_simulator'
require_relative '../helpers/network_logs_util'
require_relative '../helpers/metadata_extraction'
require_relative '../helpers/browser_activity_monitoring'

module BrowserStack
  class AppLiveSession < BrowserStack::LiveSession # rubocop:todo Metrics/ClassLength
    class AppInstallFailureException < StandardError
    end

    SPECIAL_APPS = ["app_store", "test_flight"]
    SPECIAL_APP_BUNDLE_IDS = {
      "app_store" => "com.apple.AppStore",
      "test_flight" => "com.apple.TestFlight"
    }

    def stop
      begin
        FileUtils.rm_rf("#{@@server_config['state_files_dir']}/al_session_#{@params['app_live_session_id']}")
        cmd = "curl --connect-timeout 20 -k -i https://localhost:443/wd/hub/breakAppiumBridge?rails_session_id=#{@params['app_live_session_id']}"
        response = `#{cmd}`
        BrowserStack.logger.info("[Appium App-Live] breakAppiumBridge response: #{response}")
        NetworkLogsUtil.perform_clean_up(@params['app_live_session_id'])
      rescue
        BrowserStack.logger.info "Failed to remove the file : #{@@server_config['state_files_dir']}/al_session_#{@params['app_live_session_id']}"
      end
      begin
        if BrowserActivityMonitoring.running?(@device)
          File.delete(BrowserActivityMonitoring.start_file(@device))
          BrowserStack.logger.info("/stop BrowserActivityMonitoring removing start file for #{@params[:live_session_id]}")
        end
      rescue => e
        BrowserStack.logger.error("/stop BrowserActivityMonitoring removing start file exception: #{e.message}")
      end
      super
    end

    def start # rubocop:todo Metrics/AbcSize, Metrics/MethodLength, Metrics/PerceivedComplexity, Metrics/CyclomaticComplexity
      upload_device_logs if update_session?

      async_app_launch_setup_enabled = async_app_launch_setup_enabled?

      if async_app_launch_setup_enabled
        reader, writer = IO.pipe
        notify_download_and_install_success = "download_and_install_success"
        notify_download_and_install_fail = "download_and_install_fail"

        handle_presetup_params

        pre_setup_thread = Thread.bs_run { super }
      else
        super
      end

      Utils.fork_code_block_for_device(@device) do # rubocop:todo Metrics/BlockLength
        eds_hash = {
          event_name: "app_live_launch_setup",
          product: "app_live",
          session_id: @params["app_live_session_id"],
          hashed_id: @params["app_hashed_id"],
          team: @params["genre"],
          user_id: @params["user_id"],
          device: @device,
          status: "success",
          async_app_launch_setup_enabled: async_app_launch_setup_enabled
        }
        Utils.mark_event_start('launch_time', eds_hash)
        # Touch the file where all the logs from device-logger will be streamed
        FileUtils.touch("/var/log/browserstack/app_log_#{@device}.log")
        AppAnalyticsUtil.init_eds_default_value(@params['app_live_session_id'], @params)
        write_auth_key_data_to_state_file_helper(@params['app_live_session_id'], @device, @params['device_static_name']) unless async_app_launch_setup_enabled
        download_file_setup
        start_browser_activity_monitoring
        sim_setup
        Secure::ApplePay.setup(@device, @params) if @params['apple_pay_session'].to_s == "true"
        ensure_device_logger if @params['is_whitelisted_for_self_cert_flow'] != "true"
        set_default_timezone
        set_default_location unless async_app_launch_setup_enabled
        set_default_locale
        enable_network_logs
        check_full_cleanup_session

        idevice.press_home if needs_home?

        AppAnalyticsUtil.set_app_source_and_hashed_id(@params['app_hashed_id'], @params['app_live_session_id'], true, @params)

        # app_hashed_id is 'app_store' in case of AppStore app &
        # 'test_flight' in case of TestFlight app (no download url required)
        if SPECIAL_APPS.include?(@params["app_hashed_id"])
          # Notify device logger to start putting logs into app_log file in the start of the session
          DeviceLogger.update(@device)
          launch_special_app(@params["app_hashed_id"])
          if @params["app_hashed_id"] == "test_flight" && !IdeviceUtils.check_app_with_bundle_id_exists(@device, SPECIAL_APP_BUNDLE_IDS["test_flight"])
            # Push to zombie if testflight is not installed on the device
            BrowserStack::Zombie.push_logs("app-live-testflight-not-found", "", { "session_id" => @params["app_live_session_id"], "device" => @device }, nil, @params)
          end
          eds_hash[:special_app] = true
        elsif @params["app_hashed_id"] == "no_app"
          # Skip app download and install for no_app
          BrowserStack.logger.info "Skipping app download/install for no_app"
          return
        elsif trigger_relaunch_flow?
          # Relaunch flow in case of ip change, local and network config except ipchange with location None
          relaunch_flow
          eds_hash[:relaunch_flow] = true
        elsif device_state.dedicated_cleanup_file_present? && IdeviceUtils.app_installed?(@device, @params["app_testing_bundle_id"], attempts: 2)
          update_app_or_install
          eds_hash[:update_app_or_install] = true
        else
          return if @params[:is_app_accessibility].to_s.downcase == "true" && @params["app_hashed_id"].to_s.empty?

          create_temp_app_folder
          download_app
          add_bundle_id
          if @params['is_whitelisted_for_self_cert_flow'] == "true"
            BrowserStack.logger.info "In self trust cert flow"
            begin
              install_automate_and_launch_app(switch_app = false)
            ensure
              ensure_device_logger unless Utils.bridgecloud_ios_voiceover_session?(@params)
            end
          else
            BrowserStack.logger.info "Install And Launch Flow"
            install_and_launch_app(switch_app = false)
          end
          eds_hash[:download_app] = true
        end

        inject_media

        if @params[:is_app_accessibility].to_s.downcase == "true"
          device_state.touch_app_a11y_app_details_file
          AppAccessibility.fetch_app_ipa_details(temp_app_folder, @device)
        end
      rescue AppInstallFailureException => e
        BrowserStack::Zombie.push_logs("app-live-ipa-install-failed", "App install failed #{e.message}", { "session_id" => @params["app_live_session_id"], "device" => @device }, nil, @params)
        BrowserStack.logger.error "App install failed because #{e.message} #{e.backtrace}"
        mark_install_failure_uptime_metric unless e.message.include?("IOS_DEPLOY_FAILED")
        eds_hash[:status] = "failed"
      ensure
        delete_temp_app_folder

        Utils.mark_event_end('launch_time', eds_hash)
        Utils.send_to_eds(eds_hash, EdsConstants::APP_LIVE_WEB_EVENTS, true)

        # notify download & install app flow has been finished with success / failure message
        if async_app_launch_setup_enabled
          reader.close
          writer.puts(eds_hash[:status] == "success" ? notify_download_and_install_success : notify_download_and_install_fail) if eds_hash[:download_app].to_s == "true"
          writer.close
        end
      end

      if async_app_launch_setup_enabled
        pre_setup_thread.join

        # wait for download/install app setup to complete and then launch the app
        Utils.fork_code_block_for_device(@device) do
          writer.close
          message = reader.gets.strip
          # prevent app launch on iOS 18 or later enteprise app. Here, users manually launch the app
          launch_app_helper if message == notify_download_and_install_success && !enterprise_app_on_ios_18_or_later?
          reader.close
        end
      end

    end

    def restart
      BrowserStack.logger.info "Inside app-live restart flow... #{@params.inspect}"
      begin
        t0 = Time.now.to_f
        actions_to_execute = []
        @session_restart_stats = []
        actions_to_execute = @params['actions'].split(',')
        actions_to_execute.each do |action|
          @session_restart_stats << Benchmark.bm(25) do |bm|
            bm.report(action) { execute_restart_action(action) }
          end
        end
        @session_restart_stats = @session_restart_stats.map(&:first)
        @params['session_restart_events'].merge!(@session_restart_stats.map { |stat| [stat.label, (stat.real * 1000).to_i] }.to_h)
        t1 = Time.now.to_f
        @params['session_restart_events'].merge!({ overall_live_session_methods: ((t1 - t0) * 1000.0).to_i })
      rescue => e
        BrowserStack.logger.error("[App-Live restart] received invalid actions, error: #{e.message}")
      end
    end

    def inject_media
      if @params["is_camera_toggled"] == "true" || @params["is_video_toggled"] == "true"
        Utils.notify_pusher("Image_Injection_Started,#{@params['app_live_session_id']}", @params, @device) if @params["is_camera_toggled"] == "true"
        Utils.notify_pusher("Video_Injection_Started,#{@params['app_live_session_id']}", @params, @device) if @params["is_video_toggled"] == "true"

        if @params[:image_injection_media_data]
          media_data = begin
            JSON.parse(@params["image_injection_media_data"], symbolize_names: true)
          rescue
            nil
          end
          begin
            media_data[:is_video_toggled] = @params["is_video_toggled"]
            media_data[:is_camera_toggled] = @params["is_camera_toggled"]
            BrowserStack.logger.info("Injecting media at session start for device #{@device}, session #{@params['app_live_session_id']}")
            ImageInjector.inject_media(@device, media_data, @params["app_testing_bundle_id"], @params)
            BrowserStack.logger.info("media injected successfully at session start for device #{@device} and session #{@params['app_live_session_id']}")
            BrowserStack::Zombie.push_logs("session-start-inject-image-success", "", { "session_id" => @params["app_live_session_id"], "device" => @device, "url" => "app_live" }, nil, @params)
          rescue => e
            BrowserStack::Zombie.push_logs("session-start-inject-image-failure", e.message.to_s, { "session_id" => @params["app_live_session_id"], "device" => @device, "url" => "app_live" }, nil, @params)
            BrowserStack.logger.error("Exception in inject_media #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
          end
        end
      end
    end

    def download_file_setup
      download_file_helper = DownloadFile.new(@device, @params['app_live_session_id'], APP_LIVE)
      download_file_helper.setup
      # do not raise an exception if download file setup fails. continue as usual.
    end

    def update_app_or_install(switch_app=false)
      create_temp_app_folder
      download_app
      add_bundle_id

      if Gem::Version.new(@params[:app_testing_app_version]) == Gem::Version.new(IdeviceUtils.app_version(@device, @params["app_testing_bundle_id"], attempts: 2))
        BrowserStack.logger.info "Skip app installation as it is already installed on device and this is private cleanup policy"
        just_launch_app(false, false)
        Utils.notify_pusher("app_install_end", @params, @device)
        return
      end

      begin
        update_and_launch_app(switch_app)
      rescue AppUpdateError => e
        BrowserStack.logger.error("Exception in updating app, will fallback to install #{e}")
        IdeviceUtils.uninstall_app(@device, @params["app_testing_bundle_id"])
        install_and_launch_app(switch_app)
      end
    end

    def sim_setup
      BrowserStack.logger.info "[SIM SETUP FOR APP_LIVE]"

      return unless @params['enableSim']

      sim_helper = DeviceSIMHelper.new(@device, @params['app_live_session_id'], APP_LIVE, user_id: @params["user_id"])
      result = sim_helper.session_setup

      unless result
        Utils.notify_pusher("PUBLIC_SIM_SETUP_UNSUCCESSFUL", @params, @device)
        raise BrowserStack::SessionException, "Can't initiate session on device #{@device} with SIM"
      end
      Utils.notify_pusher("PUBLIC_SIM_SETUP_SUCCESSFUL", @params, @device)
    end

    def start_browser_activity_monitoring
      return if @params[:enable_url_detection].to_s != "true"

      is_monitoring_running = BrowserActivityMonitoring.running?(@device)

      # if monitoring not running
      # create instance of BrowserActivityMonitoring
      # call start - this will start a while loop inside a thread
      # otherwise, update session id of already running monitoring
      if !is_monitoring_running
        debugger_port = DeviceManager.device_configuration_check(@device)['debugger_port']
        browser_activity_monitoring = BrowserActivityMonitoring.new(@params[:app_live_session_id], @device,
                                                                    debugger_port, @params[:genre])
        BrowserStack.logger.info("start_browser_activity_monitoring calling start for #{@params[:app_live_session_id]}")
        browser_activity_monitoring.start
      else
        BrowserStack.logger.info("start_browser_activity_monitoring updating session_id to #{@params[:app_live_session_id]}")
        File.open(BrowserActivityMonitoring.start_file(@device), 'w') do |file|
          file.flock(File::LOCK_EX)
          file.puts(@params[:app_live_session_id])
          file.flock(File::LOCK_UN)
        end
      end
    rescue => e
      BrowserStack.logger.error("start_browser_activity_monitoring exception: #{e.message}")
      File.delete(BrowserActivityMonitoring.start_file(@device)) if BrowserActivityMonitoring.running?(@device)
    end

    def ensure_device_logger
      # ensure device logger is started irrespective of success failure
      BrowserStack.logger.info "Starting Device Logger"
      # initialize devicelogger
      BrowserStack::DeviceLogger.start(@device)
      # to avoid sending two restarts immediately
      sleep 2
      FileUtils.touch("/var/log/browserstack/app_log_#{@device}.log")
      DeviceLogger.update(@device)
    end

    def relaunch_flow
      idevice.kill_all_apps
      if @params['is_whitelisted_for_self_cert_flow'] == "true" && device_state.user_entitlement_automation_file_present?
        BrowserStack.logger.info "Not relaunching as this is Enterprise Entitlement flow and Automation is still running"
        return
      end
      just_launch_app
    end

    def update_app(update_params)
      Utils.fork_code_block_for_device(@device) do
        update_app_params(update_params)
        idevice.press_home
        if SPECIAL_APPS.include?(@params["app_hashed_id"])
          launch_special_app(@params["app_hashed_id"])
        elsif (device_state.dedicated_cleanup_file_present? || @params["app_live_upgrade_app"]) && IdeviceUtils.app_installed?(@device, @params["app_testing_bundle_id"], attempts: 2)
          update_app_or_install(true)
        else
          uninstall_previous_app
          create_temp_app_folder
          download_app
          add_bundle_id
          install_and_launch_app(switch_app = true)
        end
      rescue AppInstallFailureException => e
        BrowserStack::Zombie.push_logs("app-live-ipa-install-failed", "Update app failed: #{e.message}", { "session_id" => @params["app_live_session_id"], "device" => @device }, nil, @params)
        mark_install_failure_uptime_metric unless e.message.include?("IOS_DEPLOY_FAILED")
        BrowserStack.logger.error "App install failed because #{e.message}"
      ensure
        delete_temp_app_folder
      end
    end

    private

    def update_app_params(update_params)
      AppAnalyticsUtil.set_app_source_and_hashed_id(@params['app_hashed_id'], @params['app_live_session_id'], false, @params)

      update_app_download_settings(update_params)
      update_feature_toggles(update_params)
      update_app_metadata(update_params)
      update_locale_settings(update_params)

      Utils.write_to_file_with_lock(session_file, @params.to_json)
    end

    def update_app_download_settings(update_params)
      app_download_url = update_params["new_app_download_url"]
      if app_download_url.nil? && update_params["backfill"]
        @params["backfill"] = update_params["backfill"]
        app_download_url = backfill_and_get_url
      end
      @params["new_app_download_url"] = app_download_url
      @params["app_download_timeout"] = update_params["app_download_timeout"] || 60
    end

    def update_feature_toggles(update_params)
      @params["sensor_instrument"] = update_params["sensor_instrument"] || false
      @params["is_biometric_toggled"] = update_params["is_biometric_toggled"] || false
      @params["is_camera_toggled"] = update_params["is_camera_toggled"] || false
      @params["is_passcode_toggled"] = update_params["is_passcode_toggled"] || false
      @params["biometric_hash"] = update_params["biometric_hash"]
      @params["is_continuous_scanning_enabled"] = update_params["is_continuous_scanning_enabled"] || false
      @params["app_live_upgrade_app"] = update_params["app_live_upgrade_app"] || false
    end

    def update_app_metadata(update_params)
      @params["app_testing_bundle_id"] = update_params["app_testing_bundle_id"]
      @params["app_hashed_id"] = update_params["app_hashed_id"]
      @params["app_display_name"] = update_params["app_display_name"]
    end

    def update_locale_settings(update_params)
      @params["locale"] = update_params["locale"] if !update_params["locale"].nil? && !update_params["locale"].empty?
      @params["region"] = update_params["region"] if !update_params["region"].nil? && !update_params["region"].empty?
    end

    def trigger_relaunch_flow?
      ['local', 'ip_change', 'network_config_change'].include?(@params["startElement"]) && !( @params["startElement"] == "ip_change" && @params["ipLocationCode"] == "-1")
    end

    def launch_app_helper(relaunch=false)
      if @params.key?("app_testing_bundle_id") && !@params["app_testing_bundle_id"].empty?
        locale = region = "en-US"
        locale = (@params['locale']).to_s if @params.key?("locale") && !@params["locale"].nil?
        region = (@params['region']).to_s if @params.key?("region") && !@params["region"].nil?
        # always launch with bundle id in case of relaunch.
        # launch by name is not working for new ipados devices
        display_name = if relaunch && @device['device_name'] == 'iPad8,7'
                         nil
                       else
                         @params["app_display_name"]
                       end
        set_browserstack_env = @params.key?("set_browserstack_env") && @params["set_browserstack_env"] == "true"
        idevice.launch_app(display_name, @params["app_testing_bundle_id"], locale, region, 0, set_browserstack_env)
        Utils.write_to_file("/tmp/#{@device}_current_locale", "#{locale},#{region}")
      end
    end

    def async_app_launch_setup_enabled?
      @params["enable_async_app_launch_flow"].to_s == "true" &&
      !SPECIAL_APPS.include?(@params["app_hashed_id"]) &&
      !trigger_relaunch_flow? &&
      !needs_home?
    end

    # Handle pre-setup params when the pre-setup and download/install setup runs in parallel
    def handle_presetup_params
      current_device = DeviceManager.device_configuration_check(@device)
      @params.merge!({
        "debugger_ip" => current_device["zotac_host"], "debugger_port" => current_device["debugger_port"],
        "device_version" => current_device["device_version"], "device_static_name" => current_device["device_name"],
        "iproxyPort" => current_device["webdriver_port"].to_i,
        "network_logs_port" => current_device["app_live_network_logs_port"]
      })
      write_auth_key_data_to_state_file_helper(@params['app_live_session_id'], @device, @params['device_static_name'])
    rescue => e
      BrowserStack.logger.error("[handle_presetup_params] exception received: #{e.message}")
    end

    def reconnect_streaming
      dup_session_params = JSON.parse(File.read(BrowserStack::IPhone.session_file(@device)))
      endpoint = dup_session_params["ios_should_use_set_rtcdata_v2_endpoint"].to_s == "true" ? '/bs/set_rtcdata_and_start_v2' : '/bs/set_rtcdata_and_start'

      current_device_config = DeviceManager.device_configuration_check(@device)
      wda_port = current_device_config["webdriver_port"].to_i
      url = "http://127.0.0.1:#{wda_port}/bs/live/stop?is_voiceover_enabled=false"
      response = BrowserStack::HttpUtils.make_get_request(url, 100)

      url = "http://127.0.0.1:#{wda_port}#{endpoint}"
      data = { rtc_data: dup_session_params, url: dup_session_params["url"], browser: dup_session_params["device_browser"], genre: @params["genre"] }
      BrowserStack::HttpUtils.send_post_raw(url, data.to_json, basic_auth = nil, 100)
    end

    def execute_restart_action(action_name)
      case action_name
      when 'proxy-setting'
        setup_local
        relaunch_flow
      when 'network_config_change'
        @params[:network_config_change_via_restart_api] = true
        network_logs_util = NetworkLogsUtil.new(@device, @params) if @params[:network_logs_2_0_enabled]&.to_s == 'true'
        network_logs_util&.set_local_params
        enable_network_logs
        network_logs_util&.run
        relaunch_flow
      when 'reconnect-streaming'
        reconnect_streaming
      else
        BrowserStack.logger.error("[execute_restart_action] received invalid action_name")
      end
    rescue => e
      BrowserStack.logger.error("[execute_restart_action] exception received: #{e.message}")
      raise e
    end

    def mark_install_failure_uptime_metric
      hooter = Hooter.new
      hooter.send_uptime_metric(@params["username"], APP_LIVE.gsub('_', '-'), 'ipa_install_failed') if @params["is_app_accessibility"].to_s.downcase != "true"
    end

    def write_auth_key_data_to_state_file_helper(session_id, device_id, device_name)
      device_name == "iPhone14,5" && write_auth_key_data_to_state_file(session_id, device_id)
    end

    def upload_device_logs
      Utils.upload_app_live_logs(old_session_params, device) if old_session_params
    end

    def temp_app_folder
      "/tmp/#{@device}_user_app"
    end

    def launch_special_app(hashed_id)
      Utils.notify_pusher("app_launch_start", @params, @device)
      add_bundle_id
      Utils.notify_pusher("launching_#{hashed_id}", @params, @device)
      push_to_cls(@params, "launching_#{hashed_id}", '', { "device_id" => @device })
      idevice.launch_app(nil, SPECIAL_APP_BUNDLE_IDS[hashed_id], 'en-US', 'en-US')
      Utils.notify_pusher("app_launch_end", @params, @device)
    end

    def create_temp_app_folder
      FileUtils.mkdir_p(temp_app_folder)
    rescue Exception => e
      raise AppInstallFailureException, "Temp folder creation failed: #{e.message}"
    end

    def delete_temp_app_folder
      FileUtils.rm_rf(temp_app_folder)
    end

    def backfill_and_get_url
      backfill_request_json = @params["backfill"] ? JSON.parse(@params["backfill"]) : nil
      options = {}
      options[:is_framework] = "false"
      options.merge!({
        backfill: true,
        unsigned_app_url: backfill_request_json["unsigned_app_url"],
        certificate: backfill_request_json["certificate"],
        app_hashed_id: backfill_request_json["app_hashed_id"],
        product: 'app_live',
        codesigner_host: backfill_request_json["codesigner_host"],
        rails_host: backfill_request_json["rails_host"],
        device_id: @device,
        s3_config: backfill_request_json["s3_config"],
        app_live_session_id: @params["app_live_session_id"],
        browserstackInjectorVersion: backfill_request_json["browserstackInjectorVersion"],
        patched_app_download_url: backfill_request_json["patched_app_download_url"],
        backfill_signed_app_url: backfill_request_json["backfill_signed_app_url"]
      })
      options[:user_id] = @params["user_id"] if @params["user_id"].to_s != ""

      DeviceManager.backfill_app(options)
    rescue Exception => e
      raise AppInstallFailureException, "Backfill and get url failed: #{e.message}"
    end

    def build_patch_options(biometric_hash, is_continuous_scanning_enabled)
      current_device = DeviceManager.device_configuration_check(@device)
      port = current_device["device_logger_port"]
      network_usage_description = is_continuous_scanning_enabled ? "Continuous Scanning requires scanning local network" : ""

      # Build base options from biometric_hash
      options = {
        app_url: @params["new_app_download_url"],
        rails_host: biometric_hash["rails_host"],
        app_hashed_id: @params["app_hashed_id"],
        product: "app_live",
        codesigner_host: biometric_hash["codesigner_host"],
        certificate: biometric_hash["certificate"],
        skipSigningFrameworks: biometric_hash["skipSigningFrameworks"],
        device_id: @device,
        s3_config: biometric_hash["s3_config"],
        appFramework: biometric_hash["appFramework"],
        browserstackInjectorVersion: biometric_hash["browserstackInjectorVersion"],
        patched_app_download_url: biometric_hash["patched_app_download_url"],
        enable_bonjour: is_continuous_scanning_enabled.to_s,
        bonjour_service_type: "_appa11y-#{port}._tcp",
        network_usage_description: network_usage_description
      }

      # Add optional user_id if present
      options[:user_id] = @params["user_id"] if @params["user_id"].to_s != ""

      BrowserStack.logger.info "patch_app: device is #{@device}"
      options
    end

    def patch_biometric_libs_and_get_url(biometric_hash, is_continuous_scanning_enabled = false)
      BrowserStack.logger.info "patch_app: patch_biometric_libs_and_get_url #{biometric_hash}, is_continuous_scanning_enabled - #{is_continuous_scanning_enabled} "

      options = build_patch_options(biometric_hash, is_continuous_scanning_enabled)
      DeviceManager.patch_app(options)
    end

    def get_sensor_instrument_for_app_live(params)
      return "ON" if params["sensor_instrument"] == "true" && params["biometric_hash"] && params["biometric_hash"] != "{}"
      return "ON" if params["is_biometric_toggled"] == "true" || params["is_camera_toggled"] == "true" || params["is_passcode_toggled"] == "true" || params["password_visibility_patch"] == "true" || params["is_video_toggled"] == "true"

      "OFF"
    end

    def download_app # rubocop:todo Metrics/AbcSize, Metrics/MethodLength, Metrics/PerceivedComplexity, Metrics/CyclomaticComplexity
      download_experiment_enabled = @params[:download_experiment_enabled] == "true"
      Utils.notify_pusher("app_download_start", @params, @device)
      push_to_cls(@params, 'app_download_start', '', { "device_id" => @device })

      if @params['is_whitelisted_for_self_cert_flow'] == "true"
        current_device = DeviceManager.device_configuration_check(@device)
        device_version = current_device['device_version'].to_i
        if device_version >= 18
          BrowserStack.logger.info "[iOSEnterprise] Skipping app download for iOS 18 enterprise app"
          Utils.notify_pusher("app_download_end", @params, @device)
          return
        end
      end

      app_download_url = @params["new_app_download_url"]
      app_download_timeout = @params["app_download_timeout"] || 60
      is_continuous_scanning_enabled = @params["is_continuous_scanning_enabled"].to_s.downcase == "true"
      sensor_instrument = get_sensor_instrument_for_app_live(@params)
      dylib_version = nil
      if sensor_instrument == "ON"
        biometric_hash = JSON.parse(@params["biometric_hash"])
        dylib_version = biometric_hash["dylib_version"]
        # Pusher event for instrumentation start
        Utils.notify_pusher("Biometric_Auth_Started,#{@params['app_live_session_id']}", @params, @device) if @params["is_biometric_toggled"] == "true"
        Utils.notify_pusher("Passcode_Started,#{@params['app_live_session_id']}", @params, @device) if @params["is_passcode_toggled"] == "true"
        Utils.notify_pusher("Continuous_Scanning_Configuration_Started,#{@params['app_live_session_id']}", @params, @device) if is_continuous_scanning_enabled

        if biometric_hash["patch_app_required"]
          BrowserStack.logger.info "PatchApp NEEDED for app_hashed_id: #{@params['app_hashed_id']}"
          app_download_url = patch_biometric_libs_and_get_url(biometric_hash, is_continuous_scanning_enabled)
        else
          BrowserStack.logger.info "PatchApp NOT NEEDED for app_hashed_id: #{@params['app_hashed_id']}"
        end
        # Pusher event for instrumentation
        if app_download_url.to_s == ""
          Utils.notify_pusher("Biometric_Auth_Failed,#{@params['app_live_session_id']}", @params, @device) if @params["is_biometric_toggled"] == "true"
          Utils.notify_pusher("Image_Injection_Failed,#{@params['app_live_session_id']}", @params, @device) if @params["is_camera_toggled"] == "true"
          Utils.notify_pusher("Video_Injection_Failed,#{@params['app_live_session_id']}", @params, @device) if @params["is_video_toggled"] == "true"
          Utils.notify_pusher("Passcode_Failed,#{@params['app_live_session_id']}", @params, @device) if @params["is_passcode_toggled"] == "true"
        else
          Utils.notify_pusher("Biometric_Auth_Success,#{@params['app_live_session_id']}", @params, @device) if @params["is_biometric_toggled"] == "true"
          Utils.notify_pusher("Image_Injection_Success,#{@params['app_live_session_id']}", @params, @device) if @params["is_camera_toggled"] == "true"
          Utils.notify_pusher("Video_Injection_Success,#{@params['app_live_session_id']}", @params, @device) if @params["is_video_toggled"] == "true"
          Utils.notify_pusher("Passcode_Success,#{@params['app_live_session_id']}", @params, @device) if @params["is_passcode_toggled"] == "true"
          Utils.notify_pusher("Continuous_Scanning_Configuration_Success,#{@params['app_live_session_id']}", @params, @device) if is_continuous_scanning_enabled
        end
      elsif app_download_url.to_s == "" && @params["backfill"]
        BrowserStack.logger.info "Sending app for on the fly(backfill request)"
        app_download_url = backfill_and_get_url
      end
      raise BrowserStack::SessionException, "Cannot get app download url" if app_download_url.to_s == ""

      eds_hash = {}
      eds_hash[:sensor_instrument] = sensor_instrument
      eds_hash[:dylib_version] = dylib_version
      begin
        Utils.mark_event_start('download_time', eds_hash)
        if download_experiment_enabled
          begin
            wget = HttpUtils.wget_download(app_download_url, "#{temp_app_folder}/app.ipa", { retry_count: 3, timeout: app_download_timeout })
          rescue
            curl = HttpUtils.download(app_download_url, "#{temp_app_folder}/app.ipa", { retry_count: 3, timeout: app_download_timeout })
          end
        else
          curl = HttpUtils.download(app_download_url, "#{temp_app_folder}/app.ipa", { retry_count: 3, timeout: app_download_timeout })
        end
        Utils.mark_event_end('download_time', eds_hash)
        Utils.notify_pusher("app_download_end", @params, @device)
        push_to_cls(@params, 'app_download_end', '', { "device_id" => @device })
        eds_hash[:status] = "success"
        eds_hash[:session_id] = @params["app_live_session_id"]
        eds_hash[:event_name] = "NJBDownloadTime"
        Utils.send_to_eds(eds_hash, EdsConstants::APP_LIVE_WEB_EVENTS, true)
        AppAnalyticsUtil.instrument_time(:app_download_duration_ms, eds_hash["download_time"], @params['app_live_session_id'], false, @params)
      rescue Exception => e
        BrowserStack.logger.error "App download failed from url #{app_download_url}: #{e}"
        Utils.notify_pusher("app_download_failed", @params, @device)
        push_to_cls(@params, 'app_download_failed', '', { "device_id" => @device })
        Utils.mark_event_end('download_time', eds_hash)
        eds_hash[:status] = "failed"
        eds_hash[:session_id] = @params["app_live_session_id"]
        eds_hash[:event_name] = "NJBDownloadTime"
        AppAnalyticsUtil.instrument_time(:app_download_duration_ms, eds_hash["download_time"], @params['app_live_session_id'], true, @params)
        if @@server_config["static_conf"].device_pointed_to_prod?(@device.to_s)
          Utils.send_to_eds(eds_hash, EdsConstants::APP_LIVE_WEB_EVENTS, true)
          instrumentation_type = sensor_instrument == "ON" ? "biometric" : "none"
          BrowserStack::Zombie.push_logs("app-live-ipa-download-failed", "#{e.message}, instrumentation_type:#{instrumentation_type}", { "session_id" => @params["app_live_session_id"], "device" => @device }, nil, @params)
        end

        raise AppInstallFailureException, "Download failed because of #{e.message}"
      end
    rescue Exception => e
      raise AppInstallFailureException, "Download/Intrumentation of the app failed because of #{e.message}"
    end

    def unarchive_app
      t0 = Time.now.to_f
      exit_code = OSUtils.unarchive("#{temp_app_folder}/app.ipa", temp_app_folder)
      t1 = Time.now.to_f

      @params["unarchive_app_time"] = ((t1 - t0) * 1000.0).to_i

      exit_code
    end

    def just_launch_app(switch_app=false, restarting=true)

      Utils.add_app_to_cls_tracking_file(@params["app_testing_bundle_id"], @device)
      # Notify device logger to start putting logs into app_log file in the start of the session
      DeviceLogger.update(@device) unless switch_app
      launch_app_helper(relaunch = true)
      @params["apps_during_session"] = @params["apps_during_session"] || []
      @params["apps_during_session"].push(@params["app_testing_bundle_id"])
      Utils.write_to_file_with_lock(session_file, @params.to_json) unless @params[:network_config_change_via_restart_api]
      Utils.notify_pusher("show_restart_device_logs", @params, @device) if restarting
      if @params['is_whitelisted_for_self_cert_flow'] == "true"
        update_automation_status(@params["user_id"], @params["app_live_session_id"], "User_Automation_Success")
        DeviceLogger.update(@device)
      end
    rescue Exception => e
      BrowserStack.logger.error "App failed to launch #{e}"
      raise AppInstallFailureException, e.message

    end

    def install_and_launch_app(switch_app=false) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
      eds_hash = {}
      sensor_instrument = get_sensor_instrument_for_app_live(@params)
      dylib_version = JSON.parse(@params["biometric_hash"])["dylib_version"] if @params["biometric_hash"]
      eds_hash[:sensor_instrument] = sensor_instrument
      eds_hash[:dylib_version] = dylib_version

      unless @params["optimised_app_install"].to_s == "true"
        status_code = unarchive_app
        raise "Invalid IPA File" if status_code.to_i != 0
      end

      if @params["async_metadata_extraction_enabled"].to_s == "true"
        if @params["optimised_app_install"].to_s == "true"
          status_code = unarchive_app
          raise "Invalid IPA File" if status_code.to_i != 0
        end

        success, app_related_params = MetadataExtraction.new(@device, temp_app_folder, @params).extract_validate_and_get_app_params
        @params.merge!(app_related_params)

        return unless success
      end

      Utils.notify_pusher("app_install_start", @params, @device)
      push_to_cls(@params, 'app_install_start', '', { "device_id" => @device })

      Utils.add_app_to_cls_tracking_file(@params["app_testing_bundle_id"], @device)

      if @params['is_app_accessibility'].to_s.downcase == "true"
        device_state.touch_app_a11y_app_details_file
        AppAccessibility.fetch_app_ipa_details(temp_app_folder, @device)
      end

      # Notify device logger to start putting logs into app_log file in the start of the session
      DeviceLogger.update(@device) unless switch_app
      Utils.mark_event_start('install_time', eds_hash)
      launch_app(switch_app)
      Utils.mark_event_end('install_time', eds_hash)
      eds_hash[:event_name] = "NJBInstallTime"
      eds_hash[:status] = "success"
      eds_hash[:session_id] = @params["app_live_session_id"]
      eds_hash[:unarchive_app_time] = @params["unarchive_app_time"] || 0
      eds_hash[:optimised_app_install] = @params["optimised_app_install"] || false
      eds_hash[:optimised_app_install_type] = @params["optimised_app_install_type"] || "ios_deploy_app"
      Utils.send_to_eds(eds_hash, EdsConstants::APP_LIVE_WEB_EVENTS, true, req_params: @params)
      Utils.notify_pusher("app_install_end", @params, @device)
      push_to_cls(@params, 'app_install_end', '', { "device_id" => @device })
      @params["apps_during_session"] = @params["apps_during_session"] || []
      @params["apps_during_session"].push(@params["app_testing_bundle_id"])
      Utils.write_to_file_with_lock(session_file, @params.to_json)
      AppAnalyticsUtil.instrument_time(:app_install_duration_ms, eds_hash["install_time"], @params['app_live_session_id'], false, @params)
      AppAnalyticsUtil.write_to_file(@params['app_live_session_id'], @@server_config['state_files_dir'])
    rescue Exception => e
      Utils.notify_pusher("app_install_error: #{e.message}___unknown___#{e.message}", @params, @device)
      push_to_cls(@params, "app_install_error: #{e.message}", e.message , { "device_id" => @device })
      BrowserStack.logger.error "App failed to launch #{e}"
      Utils.mark_event_end('install_time', eds_hash)
      eds_hash[:event_name] = "NJBInstallTime"
      eds_hash[:status] = "failed"
      eds_hash[:session_id] = @params["app_live_session_id"]
      AppAnalyticsUtil.instrument_time(:app_install_duration_ms, eds_hash["install_time"], @params['app_live_session_id'], true, @params)
      Utils.send_to_eds(eds_hash, EdsConstants::APP_LIVE_WEB_EVENTS, true, req_params: @params)
      raise AppInstallFailureException, e.message
    end

    def update_and_launch_app(switch_app=false) # rubocop:todo Metrics/AbcSize
      eds_hash = {}
      sensor_instrument = get_sensor_instrument_for_app_live(@params)
      dylib_version = JSON.parse(@params["biometric_hash"])["dylib_version"] if @params["biometric_hash"]
      eds_hash[:sensor_instrument] = sensor_instrument
      eds_hash[:dylib_version] = dylib_version
      Utils.notify_pusher("app_install_start", @params, @device)
      push_to_cls(@params, 'app_update_start', '', { "device_id" => @device })
      Utils.add_app_to_cls_tracking_file(@params["app_testing_bundle_id"], @device)
      status_code = unarchive_app
      raise "Invalid IPA File" if status_code.to_i != 0

      # Notify device logger to start putting logs into app_log file in the start of the session
      DeviceLogger.update(@device) unless switch_app
      Utils.mark_event_start('update_time', eds_hash)
      update_app_v2(switch_app)
      Utils.mark_event_end('update_time', eds_hash)
      eds_hash[:event_name] = "NJBUpdateTime"
      eds_hash[:status] = "success"
      eds_hash[:session_id] = @params["app_live_session_id"]
      Utils.send_to_eds(eds_hash, EdsConstants::APP_LIVE_WEB_EVENTS, true, req_params: @params)
      Utils.notify_pusher("app_install_end", @params, @device)
      push_to_cls(@params, 'app_update_end', '', { "device_id" => @device })
      @params["apps_during_session"] = @params["apps_during_session"] || []
      @params["apps_during_session"].push(@params["app_testing_bundle_id"])
      Utils.write_to_file_with_lock(session_file, @params.to_json)
    rescue Exception => e
      push_to_cls(@params, "app_update_error: #{e.message}", e.message , { "device_id" => @device })
      BrowserStack.logger.error "App failed to launch #{e}"
      Utils.mark_event_end('update_time', eds_hash)
      eds_hash[:event_name] = "NJBUpdateTime"
      eds_hash[:status] = "failed"
      eds_hash[:session_id] = @params["app_live_session_id"]
      Utils.send_to_eds(eds_hash, EdsConstants::APP_LIVE_WEB_EVENTS, true, req_params: @params)
      raise AppUpdateError, e.message
    end

    def handle_ios_18_enterprise_app_install
      Utils.notify_pusher("app_install_start", @params, @device)
      if @params['app_manifest_id'].nil? && @params['rails_host'].nil?
        Utils.notify_pusher("app_install_error: Enterprise App Trust failed", @params, @device)
        BrowserStack.logger.info "[iOSEnterpriseFlow] [iOS 18] app_manifest_id or rails_host is nil"
        return
      end

      BrowserStack.logger.info "[iOSEnterpriseFlow] Starting iOS 18 flow"

      Utils.notify_pusher("User_Automation_Started,#{@params['app_live_session_id']}", @params, @device)
      device_state.touch_user_entitlement_automation_file
      manifest_url = "https://#{@params['rails_host']}/mdm_app_install_manifest/al_ios18_entitlements_#{@params['app_manifest_id']}"
      BrowserStack.logger.info "[iOSEnterpriseFlow] Calling MDM"
      Utils.install_enterprise_signed_app(@device, manifest_url)

      device_state.remove_user_entitlement_automation_file
      Utils.notify_pusher("app_install_end", @params, @device)
      update_automation_status(@params["user_id"], @params["app_live_session_id"], "User_Automation_Success")
      BrowserStack.logger.info "[iOSEnterpriseFlow] Flow completed"
    end

    def install_automate_and_launch_app(switch_app=false) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
      BrowserStack.logger.info "Starting Trust Automation"
      current_device = DeviceManager.device_configuration_check(@device)
      device_version = current_device['device_version'].to_i

      FileUtils.touch "/tmp/client_enterprise_app_#{@device}"

      return handle_ios_18_enterprise_app_install if device_version >= 18

      begin
        retries ||= 0
        install_app(retries)

        if retries == 0
          Utils.notify_pusher("User_Automation_Started,#{@params['app_live_session_id']}", @params, @device)
          device_state.touch_user_entitlement_automation_file
        end
        BrowserStack.logger.info "starting client enterprise app trust"
        trust_start_time = Time.now

        if device_version < 11
          raise "app trust returned false after possible retries" unless idevice.trust_client_enterprise_app
        else
          BrowserStackAppHelper.check_and_install_browserstack_test_suite(@device)
          raise "app trust returned false after possible retries" unless idevice.trust_client_enterprise_app_via_browserstack_app(1)
        end

        update_automation_status(@params["user_id"], @params["app_live_session_id"], "User_Automation_Success")
        BrowserStack.logger.info "client enterprise app trust successful, time taken: #{Time.now - trust_start_time}"
      rescue AppInstallFailureException => e
        BrowserStack.logger.info "App install and automate failed: #{e.message} #{e.backtrace}"
        update_automation_status(@params["user_id"], @params["app_live_session_id"], "User_Automation_Failed") if retries > 0
        raise e
      rescue => e
        BrowserStack.logger.info "client enterprise app trust failed: #{e.message} #{e.backtrace}"
        if (retries += 1) < 2
          Utils.notify_pusher("User_Automation_Retrying,#{@params['app_live_session_id']}", @params, @device)
          uninstall_previous_app
          BrowserStack.logger.info "Retrying AppInstall and Automation: Retry Attempt #{retries}"
          retry
        else
          update_automation_status(@params["user_id"], @params["app_live_session_id"], "User_Automation_Failed")
          BrowserStack::Zombie.push_logs("app-live-enterprise-trust-failed", e.message.to_s, { "session_id" => @params["app_live_session_id"], "device" => @device }, nil, @params)
          raise AppInstallFailureException, e.message
        end
      ensure
        device_state.remove_user_entitlement_automation_file
      end

      if device_version < 11
        BrowserStack.logger.info "Pressing home to move out of settings"
        # presshome
        idevice.press_home
        sleep 2
      end
      # launch_step
      BrowserStack.logger.info "Launching User App"
      launch_app_helper(relaunch = false) unless async_app_launch_setup_enabled?
    end

    def update_automation_status(user_id, session_id, status)
      Utils.notify_pusher("#{status},#{@params['app_live_session_id']}", @params, @device)
      begin
        rails_endpoint = "#{@@server_config['static_conf'].device_rails_endpoint(@device)}/app-live/update_automation_status?auth=selenium"
        data_hash = {
          "automation_status" => status,
          "session_id" => session_id,
          "user_id" => user_id
        }
        HttpUtils.send_post_with_redirect(rails_endpoint, data_hash)
      rescue => e
        BrowserStack.logger.info "Failed to update automation status in rails #{e.message} #{e.backtrace}"
      end
    end

    def install_app(retry_count) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
      eds_hash = {}
      # Just Install app
      begin
        BrowserStack.logger.info "Starting app install"
        if retry_count == 0
          Utils.notify_pusher("app_install_start", @params, @device)
          push_to_cls(@params, 'app_install_start', '', { "device_id" => @device })
          Utils.add_app_to_cls_tracking_file(@params["app_testing_bundle_id"], @device)
          status_code = unarchive_app
          raise AppInstallFailureException, "Invalid IPA File" if status_code.to_i != 0
        end

        Utils.mark_event_start('install_time', eds_hash)
        install_command = generate_install_command
        install_output, exit_code = OSUtils.execute(install_command, true)
        if exit_code != 0
          error_message = begin
            install_output[/Error.*AMDeviceSecureInstallApplication/].gsub(/Error(.*):/, '').gsub(/AMDeviceSecureInstallApplication/, '').strip
          rescue
            ""
          end
          error_message = "Failed to install IPA" if error_message.nil? || error_message.empty?
          raise AppInstallFailureException, error_message
        end

        Utils.mark_event_end('install_time', eds_hash)
        eds_hash[:event_name] = "NJBInstallTime"
        eds_hash[:status] = "success"
        eds_hash[:session_id] = @params["app_live_session_id"]
        eds_hash[:unarchive_app_time] = @params["unarchive_app_time"] || 0
        eds_hash[:optimised_app_install] = @params["optimised_app_install"] || false
        eds_hash[:optimised_app_install_type] = @params["optimised_app_install_type"] || "ios_deploy_app"

        Utils.send_to_eds(eds_hash, EdsConstants::APP_LIVE_WEB_EVENTS, true, req_params: @params)

        Utils.notify_pusher("app_install_end", @params, @device)
        push_to_cls(@params, 'app_install_end', '', { "device_id" => @device })
        @params["apps_during_session"] = @params["apps_during_session"] || []
        @params["apps_during_session"].push(@params["app_testing_bundle_id"])
        Utils.write_to_file_with_lock(session_file, @params.to_json)
      rescue => e
        Utils.notify_pusher("app_install_error: #{e.message}___unknown___#{e.message}", @params, @device)
        push_to_cls(@params, "app_install_error: #{e.message}", e.message , { "device_id" => @device })
        BrowserStack.logger.error "App failed to launch #{e}"
        Utils.mark_event_end('install_time', eds_hash)
        eds_hash[:event_name] = "NJBInstallTime"
        eds_hash[:status] = "failed"
        eds_hash[:session_id] = @params["app_live_session_id"]
        eds_hash[:unarchive_app_time] = @params["unarchive_app_time"] || 0
        eds_hash[:optimised_app_install] = @params["optimised_app_install"] || false
        eds_hash[:optimised_app_install_type] = @params["optimised_app_install_type"] || "ios_deploy_app"

        Utils.send_to_eds(eds_hash, EdsConstants::APP_LIVE_WEB_EVENTS, true, req_params: @params)
        raise AppInstallFailureException, e.message
      end
    end

    def modify_ios_install_error_message(install_output)
      error_message = begin
        install_output[/Error.*AMDeviceSecureInstallApplication/]
          .gsub(/Error(.*):/, '').gsub(/AMDeviceSecureInstallApplication/, '')
          .strip
      rescue
        ""
      end

      error_message = if error_message.nil? || error_message.empty?
                        "Failed to install IPA"
                      else
                        "IOS_DEPLOY_FAILED #{error_message}"
                      end
    end

    # ios-deploy --just-launch option does not work to launch distribution cert signed builds (which also includes enterprise builds)
    def launch_app(switch_app)
      install_command = generate_install_command
      install_output = OSUtils.execute(install_command)

      exit_code = $CHILD_STATUS.exitstatus
      if exit_code != 0
        error_message = modify_ios_install_error_message(install_output)
        raise AppInstallFailureException, error_message
      end

      launch_app_helper(relaunch = false) unless async_app_launch_setup_enabled?
    end

    def update_app_v2(switch_app)
      bundle_id = Shellwords.escape(Dir.glob("#{temp_app_folder}/Payload/*.app")[0])
      update_output = IdeviceUtils.update_app(@device, bundle_id, 420)
      exit_code = $CHILD_STATUS.exitstatus
      if exit_code != 0
        error_message = begin
          update_output[/Error.*AMDeviceSecureInstallApplication/].gsub(/Error(.*):/, '').gsub(/AMDeviceSecureInstallApplication/, '').strip
        rescue
          ""
        end
        error_message = "Failed to update IPA" if error_message.nil? || error_message.empty?
        raise AppUpdateError, error_message
      end

      launch_app_helper(relaunch = false)
    end

    def add_bundle_id
      Utils.write_to_file("/tmp/app_session_#{@device}", @params["app_testing_bundle_id"])
    rescue Exception => e
      raise AppInstallFailureException, "Add bundle id failed: #{e.message}"
    end

    def uninstall_previous_app
      if @params["app_testing_bundle_id"].nil? || @params["app_testing_bundle_id"].empty? || @params["app_testing_bundle_id"] == "undefined"
        BrowserStack.logger.error "Unknown app_testing_bundle_id from session file. Let cleanup handle this uninstall"
      elsif @params["apps_during_session"].to_a.include?(@params["app_testing_bundle_id"]) && IdeviceUtils.check_app_with_bundle_id_exists(@device, @params["app_testing_bundle_id"])
        Utils.notify_pusher("Uninstalling the app since it is already present on the device.", @params, @device)
        IdeviceUtils.uninstall_app(@device, @params["app_testing_bundle_id"])
        push_to_cls(@params, "Uninstalling #{@params['app_testing_bundle_id']}", '', { "device_id" => @device })
      end
    rescue Exception => e
      raise AppInstallFailureException, "Uninstall failed for previous app: #{e.message}"
    end

    def set_default_timezone
      timezone_on_start = "UTC"
      timezone_on_start = @params["timezone"] if !@params["timezone"].nil? && !@params["timezone"].empty?
      timezone_on_start = "UTC" if timezone_on_start == 'GMT'
      timezone_on_start = timezone_on_start.gsub('_', ' ')
      timezone_mdm = @params["timezone_mdm"] == "GMT" ? "UTC" : @params["timezone_mdm"]
      timezone_mdm = "UTC" if timezone_mdm == 'GMT'
      if @params["genre"] == "app_live_testing" && timezone_on_start != "UTC"
        Utils.send_to_eds({
          session_id: @params["app_live_session_id"],
          product: {
            ios_timezone_changed: true
          }
        }, EdsConstants::APP_LIVE_TEST_SESSIONS, true, req_params: @params)
        begin
          BrowserStack::IPhone.change_time_zone(@device, timezone_on_start, timezone_mdm, @params['app_live_session_id'], @params['genre'])
        rescue => e
          BrowserStack.logger.error("Exception while timezone automation: #{@device}: #{e.message}")
          return
        end
        BrowserStack.logger.info("Timezone set on session start (Remember for all ios sessions)")
      end
    end

    def set_default_location
      location_on_start = "not set"
      location_on_start = "set" if !@params["latitude"].nil? && !@params["latitude"].empty? && !@params["longitude"].nil? && !@params["longitude"].empty?
      Utils.send_to_eds({
        session_id: @params["app_live_session_id"],
        product: {
          location_status_on_start: location_on_start
        }
      }, EdsConstants::APP_LIVE_TEST_SESSIONS, true, req_params: @params)
      LocationSimulator.new(device).simulate(@params["latitude"], @params["longitude"]) if location_on_start == "set"
    end

    def set_default_locale
      locale_on_start = "en-US"
      locale_on_start = @params["locale"] if !@params["locale"].nil? && !@params["locale"].empty?
      region_on_start = "en-US"
      region_on_start = @params["region"] if !@params["region"].nil? && !@params["region"].empty?
      if @params["genre"] == "app_live_testing"
        Utils.send_to_eds({
          session_id: @params["app_live_session_id"],
          product: {
            language_status_on_start: locale_on_start,
            region_on_start: region_on_start
          }
        }, EdsConstants::APP_LIVE_TEST_SESSIONS, true, req_params: @params)
      end
    end

    def enable_network_logs
      if @params && @params['genre'] == "app_live_testing"
        BrowserStack.logger.info "Setting up mitm_proxy"
        begin
          current_device = DeviceManager.device_configuration_check(@device)
          if @params["networkLogs"] == "true"
            mitm_proxy_options = {
              "session_id" => @params["session_id"],
              "genre" => @params["genre"],
              "network_logs_port" => current_device["app_live_network_logs_port"],
              "nw_filter_regex" => @params["nw_filter_regex"],
              "applive_should_use_upgraded_mitmproxy_v10" => @params["applive_should_use_upgraded_mitmproxy_v10"],
              "custom_headers" => @params["custom_headers"]
            }
            if !MitmProxy.running?(@device)
              BrowserStack.logger.info "mitm_proxy not running. starting."
            else
              BrowserStack.logger.info "mitm_proxy running. re-starting."
              MitmProxy.stop_proxy(@device)
              PrivoxyManager.reset_proxy(@device, current_device, @params)
            end
            DeviceManager.setup_mitm_proxy(@device, current_device, mitm_proxy_options)
          elsif MitmProxy.running?(@device) && @params["networkLogs"] == "false"
            BrowserStack.logger.info "mitm_proxy is already running. Disabling network logs."
            MitmProxy.stop_proxy(@device)
            PrivoxyManager.reset_proxy(@device, current_device, @params)
          end
          true
        rescue => e
          BrowserStack.logger.error "Error in starting mitm_proxy: #{e.message} #{e.backtrace.join('\n')}. Re-setting privoxy"
          MitmProxy.stop_proxy(@device)
          PrivoxyManager.reset_proxy(@device, current_device)
          false
        end
      end
    end

    # Ensure the homescreen is active MOB-9429
    def needs_home?
      current_device = DeviceManager.device_configuration_check(@device)
      current_device['device_name'] == 'iPad6,11' && current_device['device_version'].to_f >= 15.1
    end

    def check_full_cleanup_session
      device_state = DeviceState.new(@device)
      if @params['ios_unrestricted'].to_s == "true"
        device_state.touch_full_cleanup_file
        BrowserStack.logger.info "Starting an iOS Unrestricted Session and Touching State file for full cleanup"
      end
    end

    def enterprise_app_on_ios_18_or_later?
      current_device = DeviceManager.device_configuration_check(@device)
      @params[:is_whitelisted_for_self_cert_flow].to_s == "true" && current_device['device_version'].to_i >= 18
    end

    def generate_install_command
      if @params["optimised_app_install"].to_s == "true"
        BrowserStack.logger.info "Using optimised app install type: #{@params['optimised_app_install_type']} to install app"

        case @params["optimised_app_install_type"]
        when "ios_deploy_ipa"
          "#{IOS_DEPLOY} --id #{@device} --bundle #{temp_app_folder}/app.ipa 2>&1"
        when "ideviceinstaller"
          "#{IDEVICEINSTALLER} --udid #{@device} --install #{temp_app_folder}/app.ipa 2>&1"
        else
          unarchive_app

          app_filename = Shellwords.escape(Dir.glob("#{temp_app_folder}/Payload/*.app")[0])
          "#{IOS_DEPLOY} --id #{@device} --bundle #{app_filename} 2>&1"
        end
      else
        app_filename = Shellwords.escape(Dir.glob("#{temp_app_folder}/Payload/*.app")[0])
        "#{IOS_DEPLOY} --id #{@device} --bundle #{app_filename} 2>&1"
      end
    end
  end
end
