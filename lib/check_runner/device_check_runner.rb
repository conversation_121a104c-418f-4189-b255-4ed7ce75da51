require 'json'
require_relative 'check_runner'
require_relative '../service_type'
require_relative '../configuration'
require_relative '../utils/zombie'
require_relative '../ios_influxdb_client'
require_relative '../overridden/thread'
require_relative '../utils/idevice_utils'
require_relative '../../config/constants'
require_relative '../helpers/first_device_check_helper'
require 'fileutils'

module BrowserStack
  class DeviceCheckRunner < CheckRunner # rubocop:todo Metrics/ClassLength
    attr_reader :selenium_port, :webkit_proxy_port, :webdriver_agent_port, :lockfile

    def initialize(settings)
      @lockfile = '/tmp/device_check.pid'
      @log_file_name = settings['device_log_file_name']
      @settings = settings
      super settings
      # BrowserStack.logger is defined after super call
      @influxdb_client = BrowserStack::IosInfluxdbClient.new
    end

    def perform # rubocop:todo Metrics/CyclomaticComplexity, Metrics/AbcSize, Metrics/MethodLength, Metrics/PerceivedComplexity
      old_config = begin
        JSON.parse(File.read(@config_json_file.to_s))
      rescue
        {}
      end

      Utils.write_to_file(lockfile, Process.pid)
      unless File.exists? lockfile
        BrowserStack.logger.error("Device check lock file doesn't exist.")
        BrowserStack::Zombie.push_logs("ios_njb", "device-check: lockfile-doesnt-exist")
      end

      BrowserStack.logger.info("Initial Config: #{old_config}")

      # Deep copy/recursively copy config hash
      config = Marshal.load(Marshal.dump(old_config)) || {}
      devices_json = config['devices'] || {}

      connected_devices = IdeviceUtils.idevices

      # Sometimes during os upgrade idevice library returns garbage udid
      # We should not add these garbage ids to the config (don't run device check for those ids)
      # These device ids start with `ffffff`
      # More info: https://browserstack.atlassian.net/browse/MOBPE-239
      connected_devices.reject! { |id| id.start_with?('ffffff') || id.start_with?(/fff[a-z0-9]{2}:/) }

      BrowserStack.logger.info("Currently connected devices: #{connected_devices.inspect}")

      devices = (devices_json.keys + connected_devices).uniq
      new_devices = (connected_devices - devices_json.keys)

      BrowserStack.logger.info("current devices: #{devices.inspect}")
      need_plists_for_ports = []
      if old_config.empty? || devices_json.empty?
        BrowserStack.logger.info "Deleting all old appium and webkit plists"
        CheckPlist.remove_unwanted_plists(need_plists_for_ports)
      end

      # checking if device logger is up and fixing it.
      OSUtils.check_device_logger

      # running script to check if load is high and then to note down the procrss running
      check_machine_load

      devices_json.each do |dev, conf|
        if connected_devices.include?(dev) || conf["online"] == true || conf["offline_reason"] == "Device under cleaning" || File.exists?("#{@settings['state_files_dir']}/cleanupdone_#{dev}")
          need_plists_for_ports << conf["selenium_port"]
          need_plists_for_ports << conf["debugger_port"]
          need_plists_for_ports << conf["selenium_port"] + @server_config['privoxy_listen_port_offset'].to_i
        else
          BrowserStack.logger.info("No ports were added to need_plists_for_ports for device #{dev}.")
          # Logging info to find out why the ports were not added
          BrowserStack.logger.info(
            "Connected devices: #{connected_devices}. " \
            "#{dev} online? #{conf['online'] == true}. " \
            "#{dev} offline reason: #{conf['offline_reason']}."
          )
        end
        BrowserStack.logger.info("Keeping these ports: #{need_plists_for_ports}")
      end
      CheckPlist.remove_unwanted_plists(need_plists_for_ports)

      @default_selenium_port = @server_config['selenium_default_port_start']
      @default_webkit_proxy_port = @server_config['webkit_default_port_start']
      @webdriver_agent_default_port_start = @server_config['webdriver_agent_default_port_start']

      @selenium_port = @default_selenium_port.to_i
      @webkit_proxy_port = @default_webkit_proxy_port.to_i
      @webdriver_agent_port = @webdriver_agent_default_port_start.to_i
      port_mapping = {}
      devices_json.each do |device, conf|
        port_allocated = begin
          conf['port']
        rescue
          ''
        end
        port_mapping[port_allocated] = device
      end

      # Now start the device based checks...
      device_threads = []
      DeviceThread.configure(
        @user,
        @ip,
        @static_conf,
        @config_root,
        @logging_root,
        @default_appium_version,
        @appium_root,
        @appium_keychain,
        @appium_keychain_password,
        @devtools_port,
        @hostname,
        @region,
        @sub_region,
        @check_timeout,
        @infra_api,
        @developer_disk_images_path,
        @developer_symbols_path,
        @web_driver_agents,
        @profile_check_limit,
        @internet_sharing_enabled,
        @strict_app_check,
        @mdm_profiles_required
      )

      # read device_check_count from the first device and use it for all
      # device_check_count is capped to 1000a.
      device_check_count = begin
        ((devices_json[devices.min]['device_check_count'] + 1) % 1000)
      rescue
        0
      end
      # For all devices, check and configure...
      devices.sort.each_with_index do |device, index| # rubocop:todo Metrics/BlockLength
        BrowserStack.logger.info("Processing: #{device}")
        if devices_json.key?(device)
          selenium_port = devices_json[device]['port']
          webkit_proxy_port = devices_json[device]['debugger_port']
          webdriver_agent_port = devices_json[device]['webdriver_port']
        else
          allocated = false
          (@default_selenium_port.to_i + 1..@default_selenium_port.to_i + 10).each do |port|
            next if port_mapping.key?(port)

            diff = port - @default_selenium_port.to_i
            selenium_port = @default_selenium_port.to_i + diff
            webkit_proxy_port = @default_webkit_proxy_port.to_i + diff
            webdriver_agent_port = @webdriver_agent_default_port_start.to_i + diff
            allocated = true
            port_mapping[port] = device
            break
          end
          unless allocated
            BrowserStack.logger.error("ERROR allocating port, all ports till #{@default_selenium_port + 10} are allocated")
            BrowserStack::Zombie.push_logs("port_allocation_overflow", (@default_selenium_port + 10).to_s, { "device" => device })
            next
          end
        end

        if FirstDeviceCheckHelper.first_device_check?(device)
          BrowserStack.logger.info("DeviceCheckRunner: Performing first run setup for new or previously unprocessed device: #{device}")
          devices_json[device] ||= {}
          FirstDeviceCheckHelper.perform_first_device_check_setup(device, devices_json[device], @static_conf)

          begin
            DeviceState.new(device).touch_first_device_thread_done_file
            BrowserStack.logger.info("DeviceCheckRunner: Marked first run setup as done for device: #{device}")
          rescue => e
            BrowserStack.logger.error("DeviceCheckRunner: Error touching first_device_thread_done file for #{device}: #{e.message}")
          end
        end

        # bs_run creates a new thread & copies logging params to it
        device_threads << Thread.bs_run(device) do |_j| #rubocop:todo Metrics/BlockLength
          Thread.current[:device_id] = device
          BrowserStack.logger.params[:device] = device

          redis_client = Redis.new(host: @settings['redis_url'], port: @settings['mdm_redis_port'], timeout: @settings['redis_timeout'])
          #device check lock, used to run code that can be run only on one device at at time.
          device_check_lock = ((device_check_count % devices.size) == index)

          # adding default timeout of 500 secs for device thread
          begin
            Timeout.timeout(500) do
              params = [devices_json[device], device, device_check_lock, selenium_port, webkit_proxy_port, webdriver_agent_port, redis_client]
              device_thread = device_thread_implementation(device).new(*params)
              devices_json[device] = device_thread.run
              devices_json[device]["new_device_on_host"] = new_devices.include?(device)
            end
          rescue Timeout::Error => e
            BrowserStack.logger.error("#{device} | Device check thread timed out: #{e.message} #{e.backtrace}")
            BrowserStack::Zombie.push_logs("device-thread-timed-out", e.message , { "device" => device })
            next if devices_json[device].nil? # Means it crashed for a new device. Can't add it to config as it might be incomplete.

            devices_json[device]['online'] = false
            devices_json[device]['offline_reason'] = OFFLINE_REASON_DEVICE_CHECK_THREAD_TIMED_OUT
          rescue => e
            # Device thread should never crash, if it's crashing that means there's a bug in
            # device check.
            # This has been added so that such device appear on the rails admin/mobile_phones
            # dashboard with the below offline reason. Also, a single device will not prevent
            # all the devices from updating their state to rails because if a single thread
            # raises an exception it will propagate to the main, causing the main thread to die.
            BrowserStack.logger.error("#{device} | Device check thread crashed: #{e.message} #{e.backtrace}")
            BrowserStack::Zombie.push_logs("device-thread-crash", e.message , { "device" => device })
            next if devices_json[device].nil? # Means it crashed for a new device. Can't add it to config as it might be incomplete.

            # Pushing influxdb event requires device id to be in config.json already
            @influxdb_client.event(device, 'device-thread-crash', component: 'device-thread', is_error: true)

            devices_json[device]['online'] = false
            devices_json[device]['offline_reason'] = OFFLINE_REASON_DEVICE_CHECK_THREAD_CRASHED
          end
          devices_json[device]['device_check_count'] = device_check_count

          BrowserStack.logger.info("Fetching node_path and platform_version: #{device}")
          nodepath = OSUtils.which('node')
          nodepath = NODE_14 if nodepath == "node"
          BrowserStack.logger.info("node_path for #{device} : #{nodepath}")
          devices_json[device]['node_path'] = nodepath
          devices_json[device]['platform_version'] = XcodeUtils.get_platform_version
          BrowserStack.logger.info("Processed: #{device}")
        end
      end
      begin
        device_threads.each(&:join)
      rescue => e
        BrowserStack.logger.error("Device check runner crashed: #{e.message}; #{e.backtrace}")
        BrowserStack::Zombie.push_logs("device-check-runner-crash", e.message)
      end

      File.delete(lockfile)
      [new_devices, devices_json]
    end

    private

    def device_thread_implementation(device)
      return AppleTVDeviceThread if IdeviceUtils.apple_tv_device?(device)

      device_state = DeviceState.new(device)
      return DedicatedDeviceThread if device_state.dedicated_cleanup_file_present?

      DeviceThread
    end

    def check_machine_load
      log_file = "#{@server_config['logging_root']}/high_load.log"
      counter_file = "#{@server_config['state_files_dir']}/15_min_load_average_count.json"

      current_date = Time.now.strftime("%Y-%m-%d").to_s

      if log_file_written_recently?(log_file, 5 * 60)
        BrowserStack.logger.info("Log file was written in the last 5 minutes, Skipping check.")
      elsif breach_limit_exceeded?(current_date, counter_file, 30)
        BrowserStack.logger.info("Host machine 15 min load average breach limit exceeded for today, Skipping check.")
        #will send alert for the host later
      else
        OSUtils.check_machine_load(log_file, counter_file, current_date)
      end
    end

    def log_file_written_recently?(log_file, threshold_seconds)
      return false unless File.exists?(log_file)

      last_modified_time = File.mtime(log_file)
      current_time = Time.now
      (current_time - last_modified_time) < threshold_seconds
    end

    def breach_limit_exceeded?(current_date, counter_file, limit)
      return false unless File.exists?(counter_file)

      counter_data = JSON.parse(File.read(counter_file))
      counter_data[current_date] && counter_data[current_date] >= limit
    end
  end
end
