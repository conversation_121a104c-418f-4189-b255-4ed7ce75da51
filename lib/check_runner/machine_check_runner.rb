require 'json'
require_relative 'check_runner'
require_relative '../service_type'
require_relative '../../lib/custom_exceptions'
require_relative '../configuration'

BLUE = "\033[94m"
RED = "\033[91m"
GREEN = "\033[92m"
YELLOW = "\033[93m"
BOLD = "\033[1m"
ENDC = "\033[0m"

module BrowserStack
  class MachineCheckRunner < CheckRunner # rubocop:todo Metrics/ClassLength
    attr_reader :lockfile

    def initialize(settings)
      @lockfile = '/tmp/machine_check.pid'
      @log_file_name = settings['machine_log_file_name']
      @errors = 0
      conf = Configuration.new
      @server_config = conf.all
      @static_conf = @server_config['static_conf']
      @isSessionSpecificDL = @static_conf['session_specific_device_logger'] == "true"
      BrowserStack.logger.info "@isSessionSpecificDL Machine Check : #{@isSessionSpecificDL}"
      super settings
    end

    def perform # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
      # Start the machine CheckPlist
      start_time = Time.now.to_f

      mc = MachineCheck.new @mobile_root, @logging_root, @appium_root, @user, @server_config
      # Machine Setup
      perform_test { mc.reboot_check }
      perform_test { mc.ensure_firewall_enabled }
      perform_test { mc.add_machine_to_ansible_inventory }
      perform_test { mc.ensure_timezone_UTC }
      perform_test { mc.ensure_machine_is_unblocked_in_rails }  # Remove?
      perform_test { mc.ensure_correct_ip_and_hostname }
      perform_test { mc.ensure_sleep_disabled }
      perform_test { mc.ensure_machine_bluetooth_and_wifi_disabled }
      perform_test { mc.ensure_spotlight_indexing_is_disabled }
      perform_test { mc.ensure_usbmuxd_up }
      perform_test { mc.flush_dns }
      perform_test { mc.ensure_ssh_password_login_disabled }
      perform_test { mc.sync_time }
      perform_test { mc.ensure_static_config_has_params }
      perform_test { mc.ensure_pointed_to_prod }
      perform_test { mc.ensure_nomad_running }
      perform_test { mc.ensure_correct_consul_address }
      perform_test { mc.ensure_devtools_not_hanged }
      perform_test { mc.check_redis_connection }
      perform_test { mc.update_domain_blocking_list }
      perform_test { mc.privoxy_block_whitelist_domain }
      perform_test { mc.download_app_bundleid_block_config }
      perform_test { mc.clear_ios_backup_files_weekly }
      perform_test { mc.ensure_no_public_dns_configured }

      # Checking files/dirs
      perform_test { mc.ensure_correct_dirs_are_present @user }
      perform_test { mc.ensure_idevice_libraries_present }
      perform_test { mc.ensure_launch_daemon_dir_is_present_for_app_user }
      perform_test { mc.ensure_logrotate_config_updated }
      perform_test { mc.remove_old_files }

      # Other
      perform_test { mc.ensure_internet_sharing_enabled_on_all_devices }
      perform_test { mc.ensure_machine_logged_in }
      perform_test { mc.ensure_realmobile_files_present }
      perform_test { mc.ensure_xcode }
      perform_test { mc.check_ios_webkit_debug_proxy }
      perform_test { mc.ensure_apple_intermediate_cert_present }
      perform_test { mc.remove_expired_certs }
      perform_test { mc.ensure_certs }
      perform_test { mc.ensure_var_lockdown_permission_is_set } if ['ios_njb_11', 'ios_njb_12'].include? @server_config["platform_category"]
      perform_test { mc.ensure_enterprise_dummy_app_downloaded @user }
      perform_test { mc.ensure_supervision_identities_downloaded } if ['ios_njb_17', 'ios_njb_18'].include? @server_config["platform_category"]
      perform_test { mc.ensure_correct_developer_symbols_are_present }
      perform_test { mc.ensure_disk_images_are_present }
      perform_test { mc.ensure_xcode_is_open }
      perform_test { mc.ensure_device_logger_not_stale } unless @isSessionSpecificDL
      perform_test { mc.ensure_private_key_amount }
      perform_test { mc.push_machine_stats }
      perform_test { mc.instrument_crypto_mining }
      perform_test { mc.cleanup_heavy_log_files }
      perform_test { mc.push_machine_reboot }
      perform_test { mc.kill_stale_process }
      perform_test { mc.ensure_core_device_working }
      perform_test { mc.instrument_usb_removals }
      perform_test { mc.update_vpp_token_expiry_file }
      perform_test { mc.kill_usb_process_with_high_removals }

      # No alert
      perform_test { mc.remove_old_derived_data }
      perform_test { mc.clean_machine }
      perform_test { mc.remove_debug_screenshots }
      perform_test { mc.remove_mcspt_metrics }
      perform_test { mc.download_bs_media(@server_config["platform_category"].to_s) }
      perform_test { mc.correct_file_permissions }

      # Plists
      perform_test { mc.ensure_server_running_plist @user }
      perform_test { mc.ensure_csp_server_running_plist "root" }
      perform_test { mc.ensure_device_server_running_plist @user }
      perform_test { mc.ensure_device_check_running_plist @user }
      perform_test { mc.ensure_device_logger_running_plist @user } # unless @isSessionSpecificDL
      perform_test { mc.ensure_plist_updated_for_session_specific_device_logger @user } # if @isSessionSpecificDL
      perform_test { mc.ensure_ws_proxy_running_plist @user } if @server_config["platform_category"].sub('ios_njb_', '').to_i >= 17
      perform_test { mc.ensure_block_domains_running_plist @user }
      perform_test { mc.ensure_logrotate_running_plist "root" }
      perform_test { mc.ensure_devtools_proxy_running_plist }
      perform_test { mc.ensure_log_process_running_plist @user }
      perform_test { mc.ensure_file_converter_running_plist @user }
      perform_test { mc.ensure_file_converter_cleaner_running_plist @user }
      perform_test { mc.ensure_image_uploader_running_plist @user }
      perform_test { mc.ensure_image_uploader_cleaner_running_plist @user }
      perform_test { mc.ensure_video_uploader_running_plist @user }
      perform_test { mc.ensure_other_files_uploader_running_plist @user }
      perform_test { mc.ensure_network_files_uploader_running_plist @user }
      perform_test { mc.ensure_screenshot_instrumentation_running_plist @user }
      perform_test { mc.ensure_dns_logger_running_plist }
      perform_test { mc.ensure_delete_app_downloads_running_plist @user }
      perform_test { mc.ensure_inventory_push_running_plist @user }

      #check server is up after plist operations, see mobpe-905
      perform_test { mc.ensure_main_device_server_is_up }
      elapsed_time = (Time.now.to_f - start_time).round(2)

      Zombie.push_logs('machine-check-time-elapsed', "", { data: elapsed_time })

      if @errors.zero?
        puts "#{GREEN}#{BOLD}No exceptions while running the tests.#{ENDC}"
      else
        puts RED + BOLD + "#{@errors} tests failed! Check the output of machine check in #{@log_file_name}" + ENDC
      end
    end

    private

    def perform_test
      yield
    rescue => e
      puts "#{RED}#{BOLD}Error when running test: #{ENDC}#{e.message}"
      puts YELLOW + "Backtrace: #{e.backtrace}" + ENDC
      @errors += 1

      Zombie.push_logs('machine-check-exception', e.message)
    end

  end
end
