require_relative '../utils/zombie'
require_relative '../utils/ios_mdm_service_client'
require_relative '../utils/alerter'
require 'network_helper'

module BrowserStack
  class CheckRunner

    def initialize(settings)
      read_config settings
      bootstrap
    end

    def read_config(settings) # rubocop:todo Metrics/AbcSize
      @server_config = settings
      @logging_root = @server_config['logging_root']
      @config_json_file = @server_config['config_json_file']
      @static_conf_file = @server_config['static_conf_file']
      @environment = @server_config['environment']
      @config_root = @server_config['config_root']
      @selenium_default_port_start = @server_config['selenium_default_port_start']
      @webkit_default_port_start = @server_config['webkit_default_port_start']
      @webdriver_agent_default_port_start = @server_config['webdriver_agent_default_port_start']
      @ip_file = @server_config['ip_file']
      @devtools_port = @server_config['devtools_port']
      @check_timeout = @server_config['checks_timeout']
      @infra_api = @server_config['infra_api']
      @plist_dir_system = @server_config['plist_dir_system']
      @plist_dir_user = @server_config['plist_dir_user']
      @mobile_root = @server_config['mobile_root']
      @default_appium_version = @server_config['default_appium_version']
      @appium_root = @server_config['appium_roots'][@default_appium_version]
      @appium_keychain = @server_config['appium_keychain']
      @appium_keychain_password = @server_config['appium_keychain_password']
      @user = @server_config['user']
      @ios_mdm_server = @server_config['mdm_server_url']
      @mdm_auth_username = @server_config['mdm_auth_username']
      @mdm_auth_password = @server_config['mdm_auth_password']
      @mdm_queue_timeout = @server_config['mdm_queue_timeout']
      @templates_dir = @server_config['templates_dir']
      @developer_disk_images_path = @server_config['developer_disk_images_path']
      @developer_symbols_path = @server_config['developer_symbols_path']
      @profile_check_limit = @server_config['profile_check_limit']
      @web_driver_agents = @server_config['webdriver_agent_project_paths']
      @internet_sharing_enabled = @server_config['internet_sharing_enabled']
      @strict_app_check = @server_config['strict_app_check']
      @mdm_profiles_required = @server_config['mdm_profiles_required']

      # parse static conf. data
      @static_conf = @server_config['static_conf']
      @region = @static_conf['region'] || 'unknown'
      @sub_region = @static_conf['sub_region'] || 'unknown'
    end

    def bootstrap
      log_file = "#{@logging_root}/#{@log_file_name}"
      # Set logging params to get device id from current threads local variables
      Thread.current[:device_id] = 'N/A'
      BrowserStack.init_logger(log_file, { component: 'check_runner.rb' })
      @hostname = Socket.gethostname

      @ip = NetworkHelper::NetworkSetup.new.get_ip
      raise 'Still cannot get IP with static conf ..exiting.' if @ip.nil? || @ip.empty?

      BrowserStack.logger.info "My IP: #{@ip}"

      Zombie.configure

      IdeviceUtils.configure(@server_config)
      CheckPlist.configure("#{@mobile_root}/templates/", @plist_dir_system, @plist_dir_user)
      IosMdmServiceClient.configure
      Alerter.configure(@server_config)
    end

    # Returns true if device check is running, return false otherwise.
    def running?
      if File.exist?(lockfile)
        previous_run_pid = File.read(lockfile).chomp
        BrowserStack.logger.warn("WARNING: Previous #{self.class} with pid - #{previous_run_pid} :")
        if Utils.process_running?(previous_run_pid)
          BrowserStack.logger.info(" Has been running for #{Time.now - File.mtime(lockfile)} seconds.")
          #TODO: Kill the previous run and continue ? or wait for it to finish ? Delete the lock file and continue?
          true
        else
          BrowserStack.logger.info(" Crashed.")
          # Previous device_check crashed. We delete the lockfile and proceed with the device_check launch.
          File.delete(lockfile)
          false
        end
      else
        false
      end
    end
  end
end
