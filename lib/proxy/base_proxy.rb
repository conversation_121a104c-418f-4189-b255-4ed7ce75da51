require 'socket'
require 'timeout'
require 'browserstack_logger'
require 'json'

require_relative "../../config/constants"
require_relative '../../lib/utils/utils'
require_relative '../../lib/utils/zombie'

class BaseProxy
  attr_reader :proxy_port, :upstream_port, :upstream, :terminal_ip, :session_id

  def initialize(params, proxy_port, upstream_port, device)
    @request_params = params
    @device = device
    @session_id = params["automate_session_id"]
    @proxy_port = proxy_port
    @upstream_port = upstream_port
    @terminal_ip = params[:terminal_ip]
    @upstream = "http://localhost:#{@upstream_port}"
  end

  def start_proxy
    raise NotImplementedError, "#{self.class} must implement #start_proxy"
  end

  protected

  def proxy_running?
    cmd = "lsof -i -P -n | grep #{process_name} | grep #{@proxy_port} | "\
    "grep #{@terminal_ip}:#{@proxy_port} | awk '{print $2}'"
    proxy_pids = `#{cmd}`
    return true if !proxy_pids.nil? && proxy_pids != ""

    false
  end

  def push_to_zombie(kind: '', error: '', data: '')
    zombie_key_value(
      platform: platform_name,
      kind: kind,
      error: error,
      device: @device,
      data: data
    )
  end

  private

  def process_name
    raise NotImplementedError, "#{self.class} must implement #process_name"
  end

  def platform_name
    raise NotImplementedError, "#{self.class} must implement #platform_name"
  end
end
