GIT
  remote: **************:browserstack/mobile-common.git
  revision: 7f1b85d215b0ebc904216c2122160a22cfbc7b98
  branch: master
  specs:
    app_percy_common (0.1.2)
      google-cloud-storage
    aws_s3_wrapper (0.1.2)
      aws-sdk-s3
    browserstack_logger (0.2.6)
      binding_of_caller
      bsenv
    browserstack_utils (0.0.2)
    bsenv (0.0.2)
      dotenv
    cleanup_status_db (0.1.3)
    device_fork_executor (1.0.3)
    download_bundle_id_config (0.1.0)
    env_middleware (1.0.0)
    interaction_sync_stability_tester (0.1.0)
    ios_toolkit (0.1.0)
    mitmproxybuilder (0.0.1)
    mobile_influxdb_client (0.1.4)
    network_helper (0.0.2)
    percy_on_automate_common (0.0.7)
      google-cloud-storage
    reboot (0.1.1)
      diplomat (~> 2.0, >= 2.0.2)
    server_info (1.0.4)
    sirenlogs (0.5.0)
    so_timeout_util (0.2.1)
    static_conf (0.0.4)

PATH
  remote: dependencies/idevice
  specs:
    idevice (1.2.5)
      ffi
      plist

GIT
  remote: ssh://**************/browserstack/dwh_ruby.git
  revision: 6cf6004bc7669eef13c0dbb75f4dd76e85dc9451
  branch: master
  specs:
    bsdwh (0.1.48)
      aws-sdk-s3 (~> 1)

GIT
  remote: ssh://**************/browserstack/ruby-hoothoot.git
  revision: e534ae3117ba3418ef7f63072e1b284212a2f72b
  branch: master
  specs:
    hoothoot (0.3.15)

GEM
  remote: https://rubygems.org/
  specs:
    CFPropertyList (3.0.7)
      base64
      nkf
      rexml
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    appium_lib (11.0.0)
      appium_lib_core (~> 4.0)
      nokogiri (~> 1.8, >= 1.8.1)
      tomlrb (~> 1.1)
    appium_lib_core (4.7.1)
      faye-websocket (~> 0.11.0)
      selenium-webdriver (~> 3.14, >= 3.14.1)
    artifactory (3.0.17)
    ast (2.4.2)
    atomos (0.1.3)
    aws-eventstream (1.3.0)
    aws-partitions (1.997.0)
    aws-sdk-core (3.211.0)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-kms (1.95.0)
      aws-sdk-core (~> 3, >= 3.210.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.169.0)
      aws-sdk-core (~> 3, >= 3.210.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.10.1)
      aws-eventstream (~> 1, >= 1.0.2)
    babosa (1.0.4)
    base64 (0.2.0)
    binding_of_caller (1.0.1)
      debug_inspector (>= 1.2.0)
    childprocess (3.0.0)
    claide (1.1.0)
    coderay (1.1.2)
    colored (1.2)
    colored2 (3.1.2)
    commander (4.6.0)
      highline (~> 2.0.0)
    concurrent-ruby (1.1.6)
    crack (0.4.5)
      rexml
    debug_inspector (1.2.0)
    declarative (0.0.20)
    deep_merge (1.2.2)
    diff-lcs (1.3)
    digest-crc (0.6.5)
      rake (>= 12.0.0, < 14.0.0)
    diplomat (2.6.4)
      deep_merge (~> 1.2)
      faraday (>= 0.9, < 3.0, != 2.0.0)
    docile (1.3.2)
    domain_name (0.6.20240107)
    dotenv (2.8.1)
    emoji_regex (3.2.3)
    event_emitter (0.2.6)
    eventmachine (1.2.7)
    excon (0.112.0)
    faraday (1.10.4)
      faraday-em_http (~> 1.0)
      faraday-em_synchrony (~> 1.0)
      faraday-excon (~> 1.1)
      faraday-httpclient (~> 1.0)
      faraday-multipart (~> 1.0)
      faraday-net_http (~> 1.0)
      faraday-net_http_persistent (~> 1.0)
      faraday-patron (~> 1.0)
      faraday-rack (~> 1.0)
      faraday-retry (~> 1.0)
      ruby2_keywords (>= 0.0.4)
    faraday-cookie_jar (0.0.7)
      faraday (>= 0.8.0)
      http-cookie (~> 1.0.0)
    faraday-em_http (1.0.0)
    faraday-em_synchrony (1.0.0)
    faraday-excon (1.1.0)
    faraday-httpclient (1.0.1)
    faraday-multipart (1.0.4)
      multipart-post (~> 2)
    faraday-net_http (1.0.2)
    faraday-net_http_persistent (1.2.0)
    faraday-patron (1.0.0)
    faraday-rack (1.0.0)
    faraday-retry (1.0.3)
    faraday_middleware (1.2.1)
      faraday (~> 1.0)
    fastimage (2.3.1)
    fastlane (2.225.0)
      CFPropertyList (>= 2.3, < 4.0.0)
      addressable (>= 2.8, < 3.0.0)
      artifactory (~> 3.0)
      aws-sdk-s3 (~> 1.0)
      babosa (>= 1.0.3, < 2.0.0)
      bundler (>= 1.12.0, < 3.0.0)
      colored (~> 1.2)
      commander (~> 4.6)
      dotenv (>= 2.1.1, < 3.0.0)
      emoji_regex (>= 0.1, < 4.0)
      excon (>= 0.71.0, < 1.0.0)
      faraday (~> 1.0)
      faraday-cookie_jar (~> 0.0.6)
      faraday_middleware (~> 1.0)
      fastimage (>= 2.1.0, < 3.0.0)
      fastlane-sirp (>= 1.0.0)
      gh_inspector (>= 1.1.2, < 2.0.0)
      google-apis-androidpublisher_v3 (~> 0.3)
      google-apis-playcustomapp_v1 (~> 0.1)
      google-cloud-env (>= 1.6.0, < 2.0.0)
      google-cloud-storage (~> 1.31)
      highline (~> 2.0)
      http-cookie (~> 1.0.5)
      json (< 3.0.0)
      jwt (>= 2.1.0, < 3)
      mini_magick (>= 4.9.4, < 5.0.0)
      multipart-post (>= 2.0.0, < 3.0.0)
      naturally (~> 2.2)
      optparse (>= 0.1.1, < 1.0.0)
      plist (>= 3.1.0, < 4.0.0)
      rubyzip (>= 2.0.0, < 3.0.0)
      security (= 0.1.5)
      simctl (~> 1.6.3)
      terminal-notifier (>= 2.0.0, < 3.0.0)
      terminal-table (~> 3)
      tty-screen (>= 0.6.3, < 1.0.0)
      tty-spinner (>= 0.8.0, < 1.0.0)
      word_wrap (~> 1.0.0)
      xcodeproj (>= 1.13.0, < 2.0.0)
      xcpretty (~> 0.3.0)
      xcpretty-travis-formatter (>= 0.0.3, < 2.0.0)
    fastlane-sirp (1.0.0)
      sysrandom (~> 1.0)
    faye-websocket (0.11.1)
      eventmachine (>= 0.12.0)
      websocket-driver (>= 0.5.1)
    ffi (1.15.5)
    gh_inspector (1.1.3)
    google-apis-androidpublisher_v3 (0.54.0)
      google-apis-core (>= 0.11.0, < 2.a)
    google-apis-core (0.11.3)
      addressable (~> 2.5, >= 2.5.1)
      googleauth (>= 0.16.2, < 2.a)
      httpclient (>= 2.8.1, < 3.a)
      mini_mime (~> 1.0)
      representable (~> 3.0)
      retriable (>= 2.0, < 4.a)
      rexml
    google-apis-iamcredentials_v1 (0.17.0)
      google-apis-core (>= 0.11.0, < 2.a)
    google-apis-playcustomapp_v1 (0.13.0)
      google-apis-core (>= 0.11.0, < 2.a)
    google-apis-storage_v1 (0.31.0)
      google-apis-core (>= 0.11.0, < 2.a)
    google-cloud-core (1.7.1)
      google-cloud-env (>= 1.0, < 3.a)
      google-cloud-errors (~> 1.0)
    google-cloud-env (1.6.0)
      faraday (>= 0.17.3, < 3.0)
    google-cloud-errors (1.4.0)
    google-cloud-storage (1.47.0)
      addressable (~> 2.8)
      digest-crc (~> 0.4)
      google-apis-iamcredentials_v1 (~> 0.1)
      google-apis-storage_v1 (~> 0.31.0)
      google-cloud-core (~> 1.6)
      googleauth (>= 0.16.2, < 2.a)
      mini_mime (~> 1.0)
    googleauth (1.8.1)
      faraday (>= 0.17.3, < 3.a)
      jwt (>= 1.4, < 3.0)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    hashdiff (1.0.1)
    highline (2.0.3)
    http-cookie (1.0.7)
      domain_name (~> 0.5)
    httpclient (2.8.3)
    io-console (0.5.9)
    irb (1.3.5)
      reline (>= 0.1.5)
    jmespath (1.6.2)
    json (2.5.1)
    jwt (2.9.3)
      base64
    method_source (0.9.2)
    mini_magick (4.13.2)
    mini_mime (1.1.5)
    mini_portile2 (2.8.8)
    multi_json (1.15.0)
    multipart-post (2.4.1)
    mustermann (2.0.2)
      ruby2_keywords (~> 0.0.1)
    nanaimo (0.4.0)
    naturally (2.2.1)
    net-scp (2.0.0)
      net-ssh (>= 2.6.5, < 6.0.0)
    net-ssh (4.2.0)
    nkf (0.2.0)
    nokogiri (1.15.7)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    observer (0.1.2)
    optparse (0.5.0)
    os (1.1.4)
    parallel (1.21.0)
    parser (*******)
      ast (~> 2.4.1)
    pkg-config (1.5.6)
    plist (3.7.1)
    pry (0.12.2)
      coderay (~> 1.1.0)
      method_source (~> 0.9.0)
    pry-nav (0.3.0)
      pry (>= 0.9.10, < 0.13.0)
    pry-remote (0.1.8)
      pry (~> 0.9)
      slop (~> 3.0)
    public_suffix (5.1.1)
    puma (3.8.2)
    racc (1.8.1)
    rack (2.2.13)
    rack-protection (2.2.4)
      rack
    rack-test (1.1.0)
      rack (>= 1.0, < 3)
    rainbow (3.0.0)
    rake (13.2.1)
    redis (3.3.3)
    regexp_parser (2.1.1)
    reline (0.2.5)
      io-console (~> 0.5)
    representable (3.2.0)
      declarative (< 0.1.0)
      trailblazer-option (>= 0.1.1, < 0.2.0)
      uber (< 0.2.0)
    retriable (3.1.2)
    rexml (3.3.9)
    rmagick (5.5.0)
      observer (~> 0.1)
      pkg-config (~> 1.4)
    rouge (2.0.7)
    rspec (3.8.0)
      rspec-core (~> 3.8.0)
      rspec-expectations (~> 3.8.0)
      rspec-mocks (~> 3.8.0)
    rspec-core (3.8.2)
      rspec-support (~> 3.8.0)
    rspec-expectations (3.8.4)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.8.0)
    rspec-mocks (3.8.1)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.8.0)
    rspec-support (3.8.2)
    rubocop (1.22.2)
      parallel (~> 1.10)
      parser (>= *******)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 1.8, < 3.0)
      rexml
      rubocop-ast (>= 1.12.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 1.4.0, < 3.0)
    rubocop-ast (1.12.0)
      parser (>= *******)
    ruby-progressbar (1.11.0)
    ruby2_keywords (0.0.5)
    rubyzip (2.3.2)
    security (0.1.5)
    selenium-webdriver (3.142.7)
      childprocess (>= 0.5, < 4.0)
      rubyzip (>= 1.2.2)
    sequel (5.28.0)
    signet (0.19.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simctl (1.6.10)
      CFPropertyList
      naturally
    simplecov (0.17.0)
      docile (~> 1.1)
      json (>= 1.8, < 3)
      simplecov-html (~> 0.10.0)
    simplecov-html (0.10.2)
    sinatra (2.2.4)
      mustermann (~> 2.0)
      rack (~> 2.2)
      rack-protection (= 2.2.4)
      tilt (~> 2.0)
    sinatra-config-file (1.0)
      sinatra-contrib
    sinatra-contrib (2.2.4)
      multi_json
      mustermann (~> 2.0)
      rack-protection (= 2.2.4)
      sinatra (= 2.2.4)
      tilt (~> 2.0)
    slop (3.6.0)
    sqlite3 (1.5.4)
      mini_portile2 (~> 2.8.0)
    sys-filesystem (1.4.3)
      ffi (~> 1.1)
    sysrandom (1.0.5)
    terminal-notifier (2.0.0)
    terminal-table (3.0.2)
      unicode-display_width (>= 1.1.1, < 3)
    tilt (2.1.0)
    timecop (*******)
    tomlrb (1.3.0)
    trailblazer-option (0.1.2)
    tty-cursor (0.7.1)
    tty-screen (0.8.2)
    tty-spinner (0.9.3)
      tty-cursor (~> 0.7)
    uber (0.1.0)
    unicode-display_width (2.6.0)
    vpim (13.11.11)
    webmock (3.11.2)
      addressable (>= 2.3.6)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    websocket (1.2.11)
    websocket-client-simple (0.3.0)
      event_emitter
      websocket
    websocket-driver (0.7.5)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    word_wrap (1.0.0)
    xcodeproj (1.26.0)
      CFPropertyList (>= 2.3.3, < 4.0)
      atomos (~> 0.1.3)
      claide (>= 1.0.2, < 2.0)
      colored2 (~> 3.1)
      nanaimo (~> 0.4.0)
      rexml (>= 3.3.6, < 4.0)
    xcpretty (0.3.0)
      rouge (~> 2.0.7)
    xcpretty-travis-formatter (1.0.1)
      xcpretty (~> 0.2, >= 0.0.7)

PLATFORMS
  ruby

DEPENDENCIES
  app_percy_common!
  appium_lib (= 11.0.0)
  aws_s3_wrapper!
  browserstack_logger!
  browserstack_utils!
  bsdwh (= 0.1.48)!
  bsenv!
  cleanup_status_db!
  concurrent-ruby
  device_fork_executor!
  dotenv
  download_bundle_id_config!
  env_middleware!
  faraday
  fastlane
  google-cloud-storage
  hoothoot!
  idevice!
  interaction_sync_stability_tester!
  ios_toolkit!
  irb (~> 1.3)
  jmespath (~> 1.6.1)
  json (~> 2.5.1)
  mitmproxybuilder!
  mobile_influxdb_client!
  net-scp
  net-ssh (= 4.2.0)
  network_helper!
  nokogiri (~> 1.15.0)
  percy_on_automate_common!
  plist
  pry
  pry-nav
  pry-remote
  puma
  rack-test
  reboot!
  redis
  rmagick (~> 5.5.0)
  rspec (~> 3.7)
  rubocop
  sequel
  server_info!
  simplecov
  sinatra (= 2.2.4)
  sinatra-config-file
  sirenlogs!
  so_timeout_util!
  sqlite3 (~> 1.5.0)
  static_conf!
  sys-filesystem
  timecop
  vpim (~> 13.11, >= 13.11.11)
  webmock (~> 3.4)
  websocket-client-simple (= 0.3.0)
  xcodeproj

BUNDLED WITH
   1.17.3
