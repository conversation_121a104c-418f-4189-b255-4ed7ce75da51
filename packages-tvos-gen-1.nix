let
  nixpkgs_repo = import (builtins.fetchGit {
    url = "**************:browserstack/bs-nixpkgs.git";
    rev = "0ab39caba79f86e9f8d092ec4a9a4b78e23d8402";
  });
  pkgs = nixpkgs_repo { };
  rosetta_pkgs = nixpkgs_repo { system = "x86_64-darwin"; };

  packages =
    pkgs.callPackage ./packages/dotenv/tvos-gen-1.nix { inherit rosetta_pkgs; }
    ++ pkgs.callPackage ./packages/deps/tvos-gen-1.nix { }
    ++ pkgs.callPackage ./packages/percy/percy-setup.nix { }
    ++ pkgs.callPackage ./packages/ios-upgrades { }
    ++ rosetta_pkgs.callPackage ./packages/usb-reset { }
    ++ rosetta_pkgs.callPackage ./packages/appium/tvos-gen-1.nix { native_pkgs = pkgs; }
    ++ pkgs.callPackage ./packages/packages-all-generations.nix { };

in pkgs.bs-tools.broInstallerManifest (pkgs.lib.flatten packages)
