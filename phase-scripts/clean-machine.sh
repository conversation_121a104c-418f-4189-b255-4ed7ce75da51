BIN_FOLDER=`dirname $0`

AGE_MINUTES=120
COMMAND="rm -rf"

### To verify the behaviour, uncomment the COMMAND that uses ls
#COMMAND="ls -ltd"

BASE=/var/folders
USER=app

THIS=`basename $0`

realpath()
{
	echo "$(cd "$1"; pwd)"
}

_clean_folder()
{
	target=
	if [ "$TMPDIR" != "" ]
	then
		let tmp_depth=$TARGET_DEPTH-2
		target=`find $TMPDIR/.. -depth $tmp_depth -type d -user $USER -name $FOLDER -print -quit 2> /dev/null`
	fi
	if [ "$target" == "" ]
	then
		target=`find $BASE -depth $TARGET_DEPTH -type d -user $USER -name $FOLDER -print -quit 2> /dev/null`
		if [ "$target" == "" ]
		then
			echo "$THIS - ERROR: Cannot find $FOLDER for user $USER in $BASE"
			return 1
		fi
	fi

	if [ "$SUBFOLDER" != "" ]
	then
		target=$target/$SUBFOLDER
	fi

	if [ -d $target ]
	then
		echo "$THIS - INFO: Cleaning folder $target"
		echo "$THIS - INFO: find $target -depth $SUB_DEPTH -type d -mmin +$AGE_MINUTES | xargs $COMMAND"
		find $target -depth $SUB_DEPTH -type d -mmin +$AGE_MINUTES | xargs $COMMAND
	else
		echo "$THIS - ERROR: $target does not exist"
		return 1
	fi
	return 0
}

clean_instruments()
{
	FOLDER=com.apple.dt.InstrumentsCLI
	SUBFOLDER=
	TARGET_DEPTH=4
	SUB_DEPTH=2

	_clean_folder
}


clean_lda()
{
	FOLDER=com.apple.DeveloperTools
	SUBFOLDER=All/Xcode/EmbeddedAppDeltas
	TARGET_DEPTH=4
	SUB_DEPTH=1

	_clean_folder
}

clean_xctest()
{
	FOLDER=com.apple.dt.XCTest
	SUBFOLDER=
	TARGET_DEPTH=4
	SUB_DEPTH=1

	_clean_folder
}

clean_apps()
{
	if [ "$TMPDIR" == "" ]
	then
		echo "$0 - ERROR: cannot find app install folder (TMPDIR=$TMPDIR)"
	else
		echo "$THIS - INFO: find $TMPDIR -depth 1 -type d -mmin +$AGE_MINUTES -name \"20[0-9][0-9][0-9][0-9][0-9][0-9]-*\" | xargs $COMMAND"
		find $TMPDIR -depth 1 -type d -mmin +$AGE_MINUTES -name "20[0-9][0-9][0-9][0-9][0-9][0-9]-*" | xargs $COMMAND
	fi
}

clean_deriveddata()
{
	target=/Users/<USER>/Library/Developer/Xcode/DerivedData
        SUB_DEPTH=1

	if [ -d $target ]
	then
		echo "$THIS - INFO: Cleaning folder $target"
		echo "$THIS - INFO: find -name temporary-\* $target -depth $SUB_DEPTH -type d -mmin +$AGE_MINUTES | xargs $COMMAND"
		find $target -name temporary-\* -depth $SUB_DEPTH -type d -mmin +$AGE_MINUTES | xargs $COMMAND
	else
		echo "$THIS - ERROR: $target does not exist"
		return 1
	fi
	return 0
}

clean_old_wda_builds()
{
	builds=`ls -d /usr/local/.browserstack/config/*derived_data*`
	for build in $builds; do
		build_used=`grep -c $build /usr/local/.browserstack/config/config.json`
		if [ "$build_used" == "0" ]; then
			echo "$build is not used, deleting"
			rm -rf $build
		else
			echo "$build is used"
		fi
	done
}

clean_cleanup_failure_screenshots()
{
	# delete all debug screenshots after 6 hours
	find /tmp/ -name 'cleanup_failure_snapshot_*' -mmin 360 -delete
}

clean_debug_flags()
{
	# we reset all the debug flags every 6 hours, in case someone forgets it
	find /tmp/ -name 'debug_flag*' -mmin 360 -delete
}

# may be running as root (under sudo or rvmsudo), so deduce TMPDIR once to save time for cleaning tasks
if [ "$TMPDIR" == "" -o "`id -un`" != "$USER" ]
then
	TMPDIR=`find $BASE -depth 4 -type d -user $USER -name com.apple.dt.XCTest -print -quit 2> /dev/null`
	if [ "$TMPDIR" != "" ]
	then
		TMPDIR=`realpath "$TMPDIR/.."`
		if [ `basename "$TMPDIR"` != "T" ]
		then
			TMPDIR=
		else
			echo "$0 - INFO: setting TMPDIR to $TMPDIR"
		fi
	fi
fi


clean_instruments
clean_lda
clean_xctest
clean_apps
clean_deriveddata
# clean_old_wda_builds
clean_cleanup_failure_screenshots
clean_debug_flags
