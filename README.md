# realmobile: Mobile Platform for iOS NJB

https://browserstack.atlassian.net/wiki/spaces/ENG/pages/23692090/Mobile+Platform+-+iOS+NJB+Non-jailbroken

## Dependencies
  * ruby 2.7.2
  * imagemagick 6

## Local unittesting using docker
To cope with the amount of dependencies the repo has, a Dockerfile is provided
to make running the test suite easier.

In order to run the unittests inside a docker container:

#### Minikube installation
Make sure you have installed [minikube](https://browserstack.atlassian.net/wiki/spaces/ENG/pages/3391848463/Minikube+Hyperkit+as+Docker+Desktop+alternative+POC) and docker cli as docker desktop is phasing out. 

1. Install docker and minikube as per the doc above
2. `minikube mount $(pwd):/realmobile`
3. `eval $(minikube docker-env)` 
4. Build a docker image for this project. From this directory run:
  `docker build -t realmobile --build-arg ssh_prv_key="$(cat ~/.ssh/id_rsa)" .`
This requires that you have a ssh key valid for all the necessary github repositories at `~/.ssh/id_rsa`
After building the image, you can verify that it is available using `docker images`. 
Use ` --no-cache` flag if the external repos are out of date. 
5. `cp config/config.yml.sample config/config.yml`
6. `cp keys/keys.yml.sample keys/keys.yml`
7. Run the unittests (or anything else you like) inside a docker container from this image with:
  `docker run -it -v /realmobile:/usr/local/.browserstack/realmobile realmobile bundle exec rspec`
8. Run specific spec test.
```shell
# Run spec file
docker run -it -v /realmobile:/usr/local/.browserstack/realmobile realmobile bundle exec rspec './spec/server/iphone_spec.rb'
# Run module in spec
docker run -it -v /realmobile:/usr/local/.browserstack/realmobile realmobile bundle exec rspec ./spec/server/iphone.rb -e contacts_app_cleanup
docker run -it -v /realmobile:/usr/local/.browserstack/realmobile realmobile bundle exec rspec ./spec/lib/file_processing_queue_spec.rb -e BrowserStack::FileProcessingQueue
```

NOTE: This command mounts the current directory inside the container, meaning
your local changes will be present in the container. To avoid this, omit the -v
argument in the command.

If you would prefer to open a shell inside the container, simply replace `rspec` above with `bash`.
It is advisable to rebuild the docker image if there are any changes to dependencies.

## Constants/secrets
Constants and secrets used in the platform are stored in several different places.

* **constants.yml:** Located in `config/constants.yml` it holds constants that are common across all ios versions and all Datacentres.
* **Config Templates:** Located in `config` dir, these templates hold values that vary across [ios platform versions](https://browserstack.atlassian.net/wiki/spaces/ENG/pages/2104528451/iOS+platform+versions) and Datacentres. Dynamic values are retrieved here from consul. Consul retrieves it's values from bsconfigurations, explained below.
* **static_conf.json:** Stores secrets on the machine and is generated during platform deploy by using the template in `config/static_conf.json.template` to pull values from Vault.
* **bsconfigurations:** Stores constant values that can vary across datacentres. Simply add to the correct location [here](https://github.com/browserstack/bsconfigurations/tree/master/mobile).

### Adding a new constant/secret
Follow the flow chart below to determine the correct location for the new constant.

![Constant Addition Flow](constant_addition_flow.png?raw=true "Constant Addition Flow")

## Packages
Our platform utilizes packaging of components to take care of versioning and consistency of it's dependencies on our machines. Nix is our chosen package manager. For more information why see [here](https://browserstack.atlassian.net/wiki/spaces/ENG/pages/2275573862/Appium+deploys+revamp).
Information on Nix itself can be found [here](https://browserstack.atlassian.net/wiki/spaces/ENG/pages/2506195927/Onboarding+Nix) also.

Packages are defined in `default.nix` files which are maintained for each component. For example [appium 1.10.0](https://github.com/browserstack/realmobile/blob/master/packages/appium/ios-gen-1/appium_1.10.1_bstack/default.nix). They can be installed manually during testing using `nix-build` locally or `bro packages install` on a mobile machine.
During the deploy of the platform code we check for newer versions of the packages, if one exists it's automatically built on a single machine and cached in our caching servers. This package is then distributed to all machines during deploy. The install process can be found [here](https://github.com/browserstack/realmobile/blob/master/deploy/main.hcl#L96)

Currently appium is the only component setup fully with packages.
[Developing with Appium Nix](https://browserstack.atlassian.net/wiki/spaces/ENG/pages/2849900608/Deploying+new+iOS+Appium+version+to+Mobile+Platform)

## Rubocop

Before merging a new PR, run rubocop to check for errors:

```bash
bundle exec rubocop

# Or run just for a particular file
bundle exec rubocop server/server.rb
```
   
