# Device Logs Endpoint

## Overview

The `/device_logs` endpoint provides efficient access to device log lines during a session without loading the entire log file into memory. It supports reading from specific byte positions and limiting the number of lines returned.

## Endpoint Details

- **Route**: `GET /device_logs`
- **Purpose**: Return device log lines from a specific position with timestamp parsing

## Parameters

### Required Parameters
- `session_id` (string): Session identifier
- `device` (string): Device identifier
- `genre` (string): Genre/type identifier (e.g., "app_automate", "live_testing")

### Optional Parameters
- `start_pos` (number): Byte index position to start reading from (default: 0)
- `num_lines` (number): Maximum number of lines to return (default: 10000, max: 50000)

## Response Format

### Success Response (HTTP 200)
```json
{
  "meta": {
    "start_pos": 1234654,
    "end_pos": 1239009
  },
  "value": [
    {
      "timestamp": 1748599327715,
      "level": "ALL",
      "message": "Apr 19 09:01:40 healthappd(Summaries)[431] <Notice>: Starting health app"
    },
    {
      "timestamp": 1748599327716,
      "level": "ALL",
      "message": "Apr 19 09:01:41 SpringBoard[123] <Warning>: App launched"
    }
  ]
}
```

### Error Response (HTTP 400/500)
```json
{
  "error": "Error message describing the issue"
}
```

## Usage Examples

### Basic Usage
```bash
GET /device_logs?session_id=abc123&device=test_device&genre=app_automate
```

### With Pagination
```bash
# First request
GET /device_logs?session_id=abc123&device=test_device&genre=app_automate&start_pos=0&num_lines=1000

# Next request using end_pos from previous response
GET /device_logs?session_id=abc123&device=test_device&genre=app_automate&start_pos=1239009&num_lines=1000
```

## Implementation Details

### Log File Location
Device logs are read from: `/var/log/browserstack/app_log_{device}.log`

### Timezone-Aware Timestamp Parsing
- Extracts timestamps using regex: `/^([A-Za-z]{3}\s+\d{1,2}\s\d{2}:\d{2}:\d{2})/`
- **Timezone Detection Strategy**:
  1. First tries to get timezone from session parameters (`timezone` field)
  2. Falls back to device's actual timezone using `IdeviceUtils.timezone(device)`
  3. Final fallback to UTC if both methods fail
- **Timezone-Aware Conversion**:
  - Parses timestamps in the context of the device's timezone
  - Converts to UTC epoch milliseconds for consistency
  - Handles timezone name mapping (e.g., "America/Los_Angeles" → "PST")
- **Edge Case Handling**:
  - Gracefully handles invalid timezones
  - Falls back to current UTC timestamp if parsing fails
  - Ensures all timestamps are returned in UTC epoch milliseconds

### Timezone Behavior Examples
- If device is in PST and logs show "Apr 19 09:01:40", converts to epoch as if it's 09:01:40 PST
- If device is in EST and logs show "Apr 19 09:01:40", converts to epoch as if it's 09:01:40 EST
- If timezone was changed via session parameters, uses that timezone for parsing
- Always returns epoch milliseconds in UTC for consistency across different device timezones

**Timezone Sources (in priority order)**:
1. Session parameters (`timezone` field from session file)
2. Device's actual timezone (via `IdeviceUtils.timezone()`)
3. UTC fallback

### Memory Efficiency
- Uses `File.seek()` to start reading from specific byte positions
- Reads line by line without loading entire file into memory
- Tracks actual byte positions during reading

### Error Handling
- Returns empty logs (200) if file doesn't exist
- Returns empty logs (200) if start_pos exceeds file size
- Returns 400 for missing required parameters
- Returns 500 for file access errors

## Security & Validation

- All parameters are validated for presence and type
- `start_pos` is corrected to 0 if negative
- `num_lines` is capped at 50,000 for safety
- Device filtering middleware ensures proper access control

## Testing

The endpoint includes comprehensive unit tests covering:
- Parameter validation
- File existence checks
- Timestamp parsing
- Response format validation
- Error handling scenarios

Run tests with:
```bash
bundle exec rspec spec/server/server_spec.rb -e "GET /device_logs"
```
