require_relative '../lib/proxy/base_proxy'

NODE_PATH = NODE_20
PWIOS_PROXY_START_TIMEOUT = 15
PWIOS_PROXY_START_MAX_TIMEOUT = 15

PWIOS_PROXY_CONFIG = {
  "pwios_proxy_path" => "#{BS_DIR_PATH}/deps/automate-pwios-proxy/v1",
  "log_path_wo_device" => "#{LOGGING_DIR}/pwios-proxy"
}.freeze

class PWIOSProxy < BaseProxy
  attr_reader :playwright_url

  def initialize(params, proxy_port, upstream_port, device)
    super(params, proxy_port, upstream_port, device)
    @playwright_url = "ws://localhost:#{@proxy_port}"
    @pwios_proxy_log_path = "#{PWIOS_PROXY_CONFIG['log_path_wo_device']}_#{@device}.log"

    BrowserStack.logger.info(
      "Setting PWIOS Proxy iOS properties: "\
      "@request_params #{@request_params}, "\
      "@device #{@device}, "\
      "@pwios_proxy_port #{@proxy_port}, "\
      "@terminal_ip #{@terminal_ip}, "\
      "@upstream_port #{@upstream_port}, "\
      "@pwios_proxy_log_path #{@pwios_proxy_log_path} "\
    )
  end

  def start_proxy
    if proxy_running?
      BrowserStack.logger.info "PWIOS Proxy is already running at #{@proxy_port}, killing it"
      Utils.kill_ws_proxy(@proxy_port, @terminal_ip)
    else
      BrowserStack.logger.info "PWIOS Proxy is not running at #{@proxy_port}"
    end

    system("sudo chmod 666 #{PWIOS_PROXY_CONFIG['pwios_proxy_path']}/lib/config/config.json")
    pwios_proxy_config_path = "#{PWIOS_PROXY_CONFIG['pwios_proxy_path']}/lib/config/config.json"
    pwios_proxy_config_json_data = Utils.set_ws_proxy_config(
      pwios_proxy_config_path,
      @terminal_ip,
      @upstream_port,
      @playwright_url,
      @proxy_port,
      @request_params
    )
    BrowserStack.logger.info "Setting pwios_proxy_config_json_data as #{pwios_proxy_config_json_data}"

    pid = Process.fork do
      command = "NODE_ENV=prod "\
      "PORT=#{@proxy_port} "\
      "UPSTREAM=#{@playwright_url} "\
      "HOST=#{@terminal_ip} "\
      "UDID=#{@device} "\
      "DEVICE_NAME=#{@request_params['deviceName']} "\
      "ADDITIONAL_JSON_CAP_PW_ON_IOS=#{@request_params['ADDITIONAL_JSON_CAP_PW_ON_IOS']} "\
      "#{NODE_PATH} #{PWIOS_PROXY_CONFIG['pwios_proxy_path']}/cluster.js > #{@pwios_proxy_log_path} 2>&1"
      BrowserStack.logger.info "Starting PWIOS Proxy with the command #{command}"
      system(command)
    end
    Process.detach(pid)

    Timeout.timeout(PWIOS_PROXY_START_TIMEOUT) do
      PWIOS_PROXY_START_MAX_TIMEOUT.times do |attempt|
        sleep(1)
        if proxy_running?
          BrowserStack.logger.info(
            "PWIOS proxy running on port #{@proxy_port}"
          )
          return true
        else
          BrowserStack.logger.info(
            "Attempt to start PWIOS proxy: #{attempt}"
          )
        end
      end
    end
  rescue Timeout::Error => e
    BrowserStack.logger.info "Timeout unable to start PWIOS proxy for session_id :#{@session_id} - #{e.message}"
    push_to_zombie(
      'start_pwios_proxy',
      'Timeout unable to start PWIOS proxy',
      { "session_id" => @session_id }
    )
    false
  rescue => e
    BrowserStack.logger.info "Some Error occurred while in start_pwios_proxy: "\
    "#{e.inspect} #{e.message} #{e.backtrace.join("\n")}"
    push_to_zombie(
      'start_pwios_proxy',
      'Some Error occurred while in start_pwios_proxy',
      { "session_id" => @session_id, "message" => e.message, "backtrace" => e.backtrace.join("\n") }
    )
    false
  end

  private

  def process_name
    'node'
  end

  def platform_name
    'ios'
  end
end
