Give a small description related to the changes you have made.

JIRA Story: <!-- *link to JIRA Story* -->
JIRA Story for Deploy: <!-- *link to JIRA Story* --> [*create one in the mobile Ops board*]

#### Related PRs
Link to all the related PRs (<PERSON>ceLogger, iOS NJB, ... )

- [ ] By placing an `x` in the preceding checkbox, you confirm that you have run the tests.
- [ ] Code has been tested thoroughly and runs correctly on staging machines.
- [ ] PR has been approved by a member of your team.
- [x] This PR is will go through canary.

<details><summary>CLICK ME if deploying via QA</summary>
<p>

- [ ] Approved via QA.
- [ ] Tested on different device types (if applicable)
- [ ] Request a review from browserstack/mobile-platform on Github.

</p>
</details>

<details><summary>CLICK ME if deploying without QA</summary>
<p>

- [ ] Request a review from browserstack/mobile-platform on Github.

</p>
</details>

Details can be found [here](https://browserstack.atlassian.net/wiki/spaces/ENG/pages/1395491776/Mobile+Deployment)

⚠️ Important ⚠️

Please use `Squash and Merge` incase if you are merging changes in `regression` or canary `release` branches
