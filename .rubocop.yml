inherit_from: .rubocop_todo.yml

###############################################
#                                             #
#   https://docs.rubocop.org/en/latest/cops/  #
#                                             #
###############################################

AllCops:
  Exclude:
    - 'Gemfile'
    - 'Gemfile.lock'
    - 'Rakefile'
    - 'spec/fixtures/**/*'
    - 'db/**/*'
    - 'dependencies/idevice/**/*'
  UseCache: false
  DefaultFormatter: progress
  DisplayStyleGuide: true
  DisplayCopNames: true
  TargetRubyVersion: 2.7.2


# Metrics

Metrics/AbcSize:
   Max: 40
   Exclude:
    - 'ai-proxy/ai_proxy.rb'
    - 'pwios-proxy/pwios_proxy.rb'

Metrics/CyclomaticComplexity:
   Max: 22

Metrics/MethodLength:
   Max: 40
   Exclude:
    - 'ai-proxy/ai_proxy.rb'
    - 'pwios-proxy/pwios_proxy.rb'

Metrics/ClassLength:
   Exclude:
    - 'ai-proxy/ai_proxy.rb'
    - 'pwios-proxy/pwios_proxy.rb'

Metrics/PerceivedComplexity:
   Max: 20

Metrics/BlockLength:
   Max: 30
   Exclude:
    - 'spec/**/*'
    - '**/*/*.rake'

# Style

Style/FormatStringToken:
   Enabled: false

Style/For:
  Enabled: true
  EnforcedStyle: each

Style/StringLiterals:
  Enabled: false

# Enabling this would be a major project.
# Everywhere we use in place modificiation would have to change.
# Eg: gsub!(string)
Style/FrozenStringLiteralComment:
  Enabled: false

Style/ParenthesesAroundCondition:
  Enabled: true

Style/NumericPredicate:
  Enabled: false

Style/WordArray:
  Enabled: false

Style/RescueStandardError:
  EnforcedStyle: implicit

# Layout

# Disabling this because it overrides the setting in .rubocop_todo.yml
# and causes lots of errors. See: https://github.com/rubocop/rubocop/issues/5422
#
# Layout/LineLength:
#   Description: 'Limit lines to 100 characters.'
#   StyleGuide: '#80-character-limits'
#   Enabled: true
#   Max: 100

Layout/SpaceInsideHashLiteralBraces:
  Enabled: true

Layout/FirstHashElementIndentation:
  EnforcedStyle: consistent

Layout/LeadingCommentSpace:
  Enabled: false

Layout/SpaceAroundEqualsInParameterDefault:
  Enabled: false

Layout/IndentationConsistency:
  Enabled: true

Layout/ExtraSpacing:
  Enabled: false

Layout/SpaceBeforeComma:
  Enabled: false

Layout/TrailingEmptyLines:
  Enabled: false

Layout/SpaceInsideParens:
  Enabled: false

Layout/MultilineOperationIndentation:
  Enabled: false

Layout/EmptyLinesAroundClassBody:
  Enabled: false

Layout/FirstArrayElementIndentation:
  Enabled: false

Layout/EmptyLinesAroundMethodBody:
  Enabled: false

Layout/HeredocIndentation:
  Enabled: false

Layout/SpaceInsideStringInterpolation:
  Enabled: false

Layout/EmptyLinesAroundExceptionHandlingKeywords:
  Enabled: false

Layout/CommentIndentation:
  Enabled: false

Layout/ParameterAlignment:
  Enabled: true
  EnforcedStyle: with_first_parameter

Lint/MissingSuper:
  Enabled: false

Lint/DeprecatedClassMethods:
  Enabled: false

Lint/UnusedMethodArgument:
  Enabled: false

# I really don't like them either but we have too many in our code.
Style/ClassVars:
  Enabled: false

Style/IdenticalConditionalBranches:
  Enabled: false

Style/MutableConstant:
  Enabled: false

Style/NumericLiterals:
  Enabled: false
