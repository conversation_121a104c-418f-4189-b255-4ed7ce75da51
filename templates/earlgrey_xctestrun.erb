<?xml version="1.0" encoding="UTF-8"?>
<plist version="1.0">
    <dict>
        <key><%= xctestname %></key>
        <dict>
            <key>CommandLineArguments</key>
            <array>
              <% if language.to_s != "" %>
                  <string>-AppleLanguages</string>
                  <string>(<%= language %>)</string>
                  <string>-AppleTextDirection</string>
                  <string>NO</string>
              <% end %>
              <% if locale.to_s != "" %>
                  <string>-AppleLocale</string>
                  <string><%= locale %></string>
              <% end %>
            </array>
            <key>UseDestinationArtifacts</key>
            <true />
            <key>EnvironmentVariables</key>
            <dict>
                <key>DYLD_INSERT_LIBRARIES</key>
                <string>@executable_path/EarlGrey.framework/EarlGrey</string>
                <key>OS_ACTIVITY_DT_MODE</key>
                <string>YES</string>
                <% environment_variables.each do |key, value| %>
                  <key><%= key.to_s %></key>
                  <string><%= value.to_s %></string>
                <% end %>
            </dict>
            <key>IsAppHostedTestBundle</key>
            <true />
            <key>ProductModuleName</key>
            <string><%= product_module_name %></string>
            <key>SystemAttachmentLifetime</key>
            <string>deleteOnSuccess</string>
            <key>TestHostBundleIdentifier</key>
            <string><%= xctest_identifier %></string>
            <key>TestingEnvironmentVariables</key>
            <dict>
                <key>DYLD_FRAMEWORK_PATH</key>
                <string>/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks</string>
                <key>DYLD_INSERT_LIBRARIES</key>
                <string>__PLATFORMS__/iPhoneOS.platform/Developer/Library/PrivateFrameworks/IDEBundleInjection.framework/IDEBundleInjection</string>
                <key>DYLD_LIBRARY_PATH</key>
                <string>/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks</string>
                <key>XCInjectBundleInto</key>
                <string>__TESTHOST__/MyLocation</string>
                <key>XCODE_DBG_XPC_EXCLUSIONS</key>
                <string>com.apple.dt.xctestSymbolicator</string>
            </dict>
            <key>ToolchainsSettingValue</key>
            <array />
            <key>UITargetAppCommandLineArguments</key>
            <array />
            <key>UITargetAppMainThreadCheckerEnabled</key>
            <true />
            <key>UserAttachmentLifetime</key>
            <string>deleteOnSuccess</string>
            <key>TestBundleDestinationRelativePath</key>
            <string>__TESTHOST__/PlugIns/<%= xctestname %>.xctest</string>
        </dict>
    </dict>
</plist>
