<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
        <key>PayloadContent</key>
        <array>
                <dict>
                        <key>PayloadDescription</key>
                        <string>Global HTTP Proxy</string>
                        <key>PayloadDisplayName</key>
                        <string>Global HTTP Proxy</string>
                        <key>PayloadIdentifier</key>
                        <string><%= payload_id %></string>
                        <key>PayloadType</key>
                        <string>com.apple.proxy.http.global</string>
                        <key>PayloadUUID</key>
                        <string><%= payload_uuid %></string>
                        <key>PayloadVersion</key>
                        <integer>1</integer>
                        <key>ProxyCaptiveLoginAllowed</key>
                        <false/>
                        <% if (defined?(proxy_fallback_allowed) && proxy_fallback_allowed) %>
                        <key>ProxyPACFallbackAllowed</key>
                        <true/>
                        <% end %>
                        <key>ProxyPACURL</key>
                        <string><%= url %></string>
                        <key>ProxyType</key>
                        <string>Auto</string>
                </dict>
        </array>
        <key>PayloadDisplayName</key>
        <string>Proxy</string>
        <key>PayloadIdentifier</key>
        <string><%= profile_identifier %></string>
        <key>PayloadOrganization</key>
        <string>BrowserStack</string>
        <key>PayloadRemovalDisallowed</key>
        <false/>
        <key>PayloadType</key>
        <string>Configuration</string>
        <key>PayloadUUID</key>
        <string><%= uuid %></string>
        <key>PayloadVersion</key>
        <integer><%= version || 1 %></integer>
</dict>
</plist>
