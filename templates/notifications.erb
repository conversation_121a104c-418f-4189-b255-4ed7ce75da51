<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version=”1.0”>
  <dict>
    <key>PayloadContent</key>
    <array>
      <dict>
        <key>NotificationSettings</key>
        <array>

        <% bundles.each do |bundle, config| %>
          <dict>
            <key>BundleIdentifier</key>
            <string><%= bundle %></string>
            <key>NotificationsEnabled</key>
            <<%= config[:notifications_enabled] %>/>
          </dict>
        <%end%>
        </array>
        <key>PayloadIdentifier</key>
        <string><%= payload_id %></string>
        <key>PayloadType</key>
        <string>com.apple.notificationsettings</string>
        <key>PayloadUUID</key>
        <string><%= payload_uuid %></string>
        <key>PayloadVersion</key>
        <real>1</real>
      </dict>
    </array>
    <key>PayloadDisplayName</key>
    <string>Notifications</string>
    <key>PayloadIdentifier</key>
    <string><%= profile_identifier %></string>
    <key>PayloadType</key>
    <string>Configuration</string>
    <key>PayloadUUID</key>
    <string><%= uuid %></string>
    <key>PayloadVersion</key>
    <integer><%= version || 1 %></integer>
  </dict>
</plist>
