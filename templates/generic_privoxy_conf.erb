listen-address :<%= listen_port(current_device_config) %>
socket-timeout <%= @@socket_timeout %>
<% if @@set_custom_max_client_connections %>
  max-client-connections <%= @@custom_max_client_connections %>
<% else %>
  max-client-connections <%= @@max_client_connections %>
<% end %>
logdir <%= @@log_dir %>

<% if percy_forwarding %>
  forward / :<%= percy_forwarding %>
<% end %>

<% if geo_host_port %>
  forward / <%= geo_host_port %>
<% elsif repeater_host_port != "" %>
  <% hosts.each do |host| %>
    <% if auth_enabled %>
      forward-socks5 <%= host %> <%= auth_username %>:<%= auth_password %>@<%= repeater_host_port %> .
    <% else %>
      forward-socks5 <%= host %> <%= repeater_host_port %> .
    <% end %>
  <% end %>
<% end %>

<% if defined?(custom_forwarding_options) && !custom_forwarding_options.empty? && !repeater_host.nil? %>
  forward-socks5 / <%= repeater_host %>:<%= repeater_port %> .
<% end %>

forward percy.cli:5338 :<%= percy_cli_port(current_device_config) %>

<% proxy_exceptions.each do |exception| %>
  forward <%= exception %> .
<% end %>

<% if SENSOR_MOCKER_HOST %>
  forward <%= SENSOR_MOCKER_HOST %> localhost:45671
<% end %>

<% if HID_MOCKER_HOST %>
  <% if voiceover_bluetooth_server_ip %>
    forward <%= HID_MOCKER_HOST %> <%= voiceover_bluetooth_server_ip %>:9001
  <% else %>
    forward <%= HID_MOCKER_HOST %> localhost:9001
  <% end %>
<% end %>

<% if is_settings_enabled %>
  forward <%= proxy_host_ip %>:45671 localhost:45671
<% end %>

forward .gvt1.com .  # Google update server - changed to support subdomain
forward .dl.google.com . # Google download server
forward .gstatic.com . # Google CDN
forward .media.tenor.com .
forward .cdn.mozilla.net .
forward assets.msn.com .
forward safebrowsing.googleapis.com .
forward optimizationguide-pa.googleapis.com .
forward .services.mozilla.com .

filterfile /usr/local/.browserstack/realmobile/templates/pfilter.conf
actionsfile /usr/local/.browserstack/privoxy/privoxy_domain_blocking.conf
actionsfile <%= blocked_domains_file %>
actionsfile <%= whitelisted_domains_file %>

logfile <%= log_file %>
debug 1 # Log the destination for each request. See also debug 1024
debug 2 # show each connection status
debug 8 # show header parsing (logs user agent)
debug 512 # Common Log Format
debug 1024 # Log the destination for requests Privoxy didn't let through, and the reason why
debug 4096 # Startup banner and warnings
debug 8192 # Non-fatal errors
tolerate-pipelining 1
<% if @@set_custom_privoxy_keep_alive_timeout %>
  keep-alive-timeout <%= @@custom_privoxy_keep_alive_timeout %>
<% else %>
  keep-alive-timeout <%= @@keep_alive_timeout %>
<% end %>
default-server-timeout <%= @@server_timeout %>
<% if use_paction %>
  actionsfile <%= paction_file_path %>
<% end %>
templdir <%= device_templates_dir %>


