<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
      <key>EnvironmentVariables</key>
      <dict>
        <key>PATH</key>
        <string><% if @config[:prepend_custom_path] %><%= "#{@config[:prepend_custom_path]}:" %><% end %>/bin:/usr/bin:/usr/local/bin:/opt/homebrew/bin<% if @config[:custom_path] %><%= ":#{@config[:custom_path]}" %><% end %></string>
        <key>rvmsudo_secure_path</key>
        <string>0</string>
        <% if @config[:set_lang_and_term] %>
        <key>LANG</key> <string>en_IE.UTF-8</string>
        <key>TERM</key> <string>xterm-256color</string>
        <% end %>
        <% if @config[:set_appium_home] %>
          <key>APPIUM_HOME</key>
          <string><%= "#{@config[:set_appium_home]}" %></string>
        <% end %>
      </dict>
      <key>UserName</key>
        <string><%= @config[:username] %></string>
        <key>Label</key>
        <string><%= @config[:name] %></string>
        <% if @config[:arguments] %>
        <key>ProgramArguments</key>
        <array>
          <% @config[:arguments].each do |args| %>
              <string><%= args %></string>
          <% end %>
        </array>
        <% end %>
        <% if @config[:session_create] %>
        <key>SessionCreate</key> <true/>
        <% end %>
        <key>RunAtLoad</key>
        <true/>
        <key>KeepAlive</key>
        <<%= @config[:keep_alive]%>/>
        <key>StandardOutPath</key>
        <string><%= @config[:log_file] %></string>
        <key>StandardErrorPath</key>
        <string><%= @config[:error_log] %></string>
        <% if @config[:custom_hash] && @config[:custom_hash].is_a?(Hash)  %>
          <% @config[:custom_hash].each do |key, val| %>
          <% key_type = "integer" %>
          <% if val.instance_of? (String) then key_type="string" end %>
              <key><%= key %></key>
              <<%= key_type %>><%= val %></<%= key_type %>>
          <% end %>
        <% end %>
        <% if @config[:start_calendar_interval] %>
          <key>StartCalendarInterval</key>
          <dict>
          <% @config[:start_calendar_interval].each do |key, val| %>
            <key><%= key %></key>
            <integer><%= val %></integer>
          <% end %>
          </dict>
        <% end %> 
    </dict>
</plist>
