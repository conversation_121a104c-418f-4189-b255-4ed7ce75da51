<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>PayloadContent</key>
	<array>
		<dict>
			<key>PayloadCertificateFileName</key>
			<string>customcertificate.pfx</string>
			<key>PayloadDescription</key>
			<string>Adds a PKCS#12-formatted certificate</string>
			<key>PayloadIdentifier</key>
			<string><%= payload_id %></string>
			<key>PayloadType</key>
			<string>com.apple.security.pkcs12</string>
			<key>PayloadUUID</key>
			<string><%= payload_uuid %></string>
			<key>PayloadVersion</key>
			<integer>1</integer>
			<key>Password</key>
			<string><%= password %></string>
			<key>PayloadContent</key>
			<data><%= cert_data %></data>
			<key>PayloadDisplayName</key>
			<string><%= display_name %></string>
		</dict>
	</array>
	<key>PayloadDisplayName</key>
	<string>experiment</string>
	<key>PayloadIdentifier</key>
	<string><%= profile_identifier %></string>
	<key>PayloadRemovalDisallowed</key>
	<false/>
	<key>PayloadType</key>
	<string>Configuration</string>
	<key>PayloadUUID</key>
	<string><%= uuid %></string>
	<key>PayloadVersion</key>
	<integer>1</integer>
</dict>
</plist>