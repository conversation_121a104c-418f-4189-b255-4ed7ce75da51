<%- if !proxy_host_ip.nil? && !proxy_host_ip.empty? -%>
{+block{Nobody is supposed to request this.}}
<%-# Blocking Sinatra port -%>
<%= proxy_host_ip %>:45671
<%-# Blocking Cleanup port -%>
<%= proxy_host_ip %>:45680
<%-# Blocking App Caching Proxy Ports -%>
<%= proxy_host_ip %>:45900
<%= proxy_host_ip %>:45901
<%-# Blocking WDA (iproxy) ports -%>
<%- (8400..8410).each do |port| -%>
<%= "#{proxy_host_ip}:#{port}" %>
provisioning-server.browserstack.com
mdp.browserstack.com
<%- end -%>
{-block}
<%- end -%>
<%- unless geo_auth.nil? -%>
{+add-header{Proxy-Authorization: Basic <%= geo_auth %>}}
/
{-add-header{Proxy-Authorization: Basic <%= geo_auth %>}}
<%- end -%>
<%-# Adding device id in headers for detecting the device from which request originated -%>
<%- if device -%>
{+add-header{x-bstacknonce: <%= device %>}}
sensormockerdata.browserstack.com
hidmocker.browserstack.com
<%- end -%>
<%- if cors_header_override -%>
{+client-header-filter{override-client-headers}}
bs-local.com
www.localhost.com
{+server-header-filter{override-server-headers}}
bs-local.com
www.localhost.com
<%- end -%>
