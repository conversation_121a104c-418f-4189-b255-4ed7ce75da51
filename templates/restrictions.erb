<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>HasRemovalPasscode</key>
    <false/>
    <key>PayloadContent</key>
    <array>
      <dict>
        <key>PayloadDescription</key>
        <string>Configures restrictions</string>
        <key>PayloadDisplayName</key>
        <string>Restrictions</string>
        <key>PayloadIdentifier</key>
        <string><%= payload_id %></string>
        <key>PayloadType</key>
        <string>com.apple.applicationaccess</string>
        <key>PayloadUUID</key>
        <string><%= payload_uuid %></string>
        <key>PayloadVersion</key>
        <integer>1</integer>
        <key>allowWallpaperModification</key>
        <false/>
        <key>allowChat</key>
        <false/>
        <key>allowAutomaticAppDownloads</key>
        <false/>
        <key>allowBluetoothModification</key>
        <<%= enable_bluetooth_modification %>/>
        <key>allowPasscodeModification</key>
        <<%= enable_passcode_settings %>/>
        <key>allowPredictiveKeyboard</key>
        <false/>
        <key>allowSystemAppRemoval</key>
        <false/>
        <key>forceDelayedSoftwareUpdates</key>
        <true/>
        <key>safariAllowAutoFill</key>
        <false/>
        <key>allowVideoConferencing</key>
        <false/>
        <key>allowDictation</key>
        <false/>
        <key>allowNFC</key>
        <false/>
        <key>allowFindMyDevice</key>
        <false/>
        <key>allowCloudBackup</key>
        <false/>
        <key>allowCloudDocumentSync</key>
        <false/>
        <key>allowCallRecording</key>
        <false/>
        <key>allowMailSummary</key>
        <false/>
        <key>allowImageWand</key>
        <false/>
        <key>allowGenmoji</key>
        <false/>
        <key>allowImagePlayground</key>
        <false/>
        <key>allowPersonalizedHandwritingResults</key>
        <false/>
        <key>allowWritingTools</key>
        <false/>
        <key>allowAppsToBeHidden</key>
        <false/>
        <key>allowAppsToBeLocked</key>
        <false/>
        <key>allowDefaultBrowserModification</key>
        <false/>
        <% if defined?(disable_siri) && disable_siri && 
          !(defined?(is_dedicated_device) && is_dedicated_device) %>
          <key>allowAssistant</key>
          <false/>
        <% end %>
        <% unless defined?(is_dedicated_device) && is_dedicated_device %>
          <key>forceWiFiPowerOn</key>
          <true/>
          <key>forceWiFiToAllowedNetworksOnly</key>
          <true/>
        <% end %>
        <key>allowExternalIntelligenceIntegrations</key>
        <false/>
        <key>allowExternalIntelligenceIntegrationsSignIn</key>
        <false/>
        <key>allowCloudKeychainSync</key>
        <false/>
        <key>allowCloudPhotoLibrary</key>
        <false/>
        <key>allowPassbookWhileLocked</key>
        <false/>
        <key>allowPasswordAutoFill</key>
        <false/>
        <key>allowPasswordProximityRequests</key>
        <false/>
        <key>allowPasswordSharing</key>
        <false/>
        <key>allowUSBRestrictedMode</key>
        <false/>
        <key>allowAirDrop</key>
        <false/>
        <key>allowGameCenter</key>
        <false/>
        <% if disable_esim_modification %>
        <key>allowESIMModification</key>
        <false/>
        <% end %>
        <% if profile_installation_ui_disallowed %>
        <key>allowUIConfigurationProfileInstallation</key>
        <false/>
        <% end %>
        <key>forceAutomaticDateAndTime</key>
        <<%= !!force_automatic_date_time %>/>
    <%#
            The blacklisted apps listed here should not be added to the known apps list in the ios-njb-app
            because the user can install a PWA with the same name as a blacklisted app, and the automation
            will ignore it as it's a known app.
            If you're removing a blacklisted bundle id from this file then do add it to the ios-njb-app, otherwise,
            the automation to remove PWAs & waiting apps will remove the app.
            See https://github.com/browserstack/ios-njb-app/blob/master/BrowserStackUITests/Constants.swift
        %>
        <key>blacklistedAppBundleIDs</key>
        <array>
          <string>com.apple.iBooks</string>
          <string>com.apple.Music</string>
          <string>com.apple.mobilemail</string>
          <string>com.apple.Maps</string>
          <string>com.apple.MobileStore</string>
          <string>com.apple.facetime</string>
          <string>com.apple.mobilecal</string>
          <string>com.apple.stocks</string>
          <string>com.apple.Home</string>
          <string>com.apple.podcasts</string>
          <string>com.apple.mobileme.fmip1</string>
          <string>com.apple.mobileme.fmf1</string>
          <string>com.apple.VoiceMemos</string>
          <string>com.apple.videos</string>
          <string>com.apple.reminders</string>
          <% if defined?(disable_ios_18_apps) && disable_ios_18_apps %>
            <string>com.apple.gamecenter</string>
            <string>com.apple.classroom</string>
            <string>com.apple.journal</string>
          <% end %>
          <string>com.apple.Bridge</string>
          <% if defined?(disable_passwords_app) && disable_passwords_app %>
            <string>com.apple.Passwords</string>
          <% end %>
          <string>com.apple.Health</string>
          <string>com.apple.mobiletimer</string>
          <string>com.apple.mobilenotes</string>
          <string>com.apple.news</string>
          <string>com.apple.calculator</string>
          <string>com.apple.compass</string>
          <string>com.apple.tips</string>
          <string>com.apple.tv</string>
          <string>com.apple.shortcuts</string>
          <% unless enable_pwa %>
            <string>com.apple.webapp</string>
            <string>com.apple.webapp1</string>
          <% end %>
          <% if disable_weather_app %>
            <string>com.apple.weather</string>
          <% end %>
          <% if disable_messaging_app %>
            <string>com.apple.MobileSMS</string>
          <% end %>
          <% if disable_files_app %>
            <string>com.apple.DocumentsApp</string>
          <% end %>
          <% if disable_contacts_app %>
            <string>com.apple.MobileAddressBook</string>
          <% end %>
          <% if disable_wallet_app %>
            <string>com.apple.Passbook</string>
          <% end %>
          <string>com.apple.journal</string>
        </array>
      </dict>
    </array>
    <key>PayloadDisplayName</key>
    <string><%= profile_display_name %></string>
    <key>PayloadIdentifier</key>
    <string><%= profile_identifier %></string>
    <key>PayloadOrganization</key>
    <string>BrowserStack</string>
    <key>PayloadRemovalDisallowed</key>
    <true/>
    <key>PayloadType</key>
    <string>Configuration</string>
    <key>PayloadUUID</key>
    <string><%= uuid %></string>
    <key>PayloadVersion</key>
    <integer><%= version || 1 %></integer>
  </dict>
</plist>
