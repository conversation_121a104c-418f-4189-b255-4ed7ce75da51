<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-/Apple/DTD PLIST 1.0/EN" "http:/www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key><%= xctestname %></key>
    <dict>
      <key>CommandLineArguments</key>
      <array>
        <% if language.to_s != "" %>
            <string>-AppleLanguages</string>
            <string>(<%= language %>)</string>
	    <string>-AppleTextDirection</string>
	    <string>NO</string>
        <% end %>
        <% if locale.to_s != "" %>
            <string>-AppleLocale</string>
            <string><%= locale %></string>
        <% end %>
      </array>
      <key>IsUITestBundle</key>
      <true/>
      <key>UseDestinationArtifacts</key>
      <true/>
      <key>IsXCTRunnerHostedTestBundle</key>
      <true/>
      <key>EnvironmentVariables</key>
      <dict>
        <% environment_variables.each do |key, value| %>
          <key><%= key.to_s %></key>
          <string><%= value.to_s %></string>
        <% end %>
      </dict>
      <key>ProductModuleName</key>
      <string><%= product_module_name %></string>
      <key>SystemAttachmentLifetime</key>
      <string>keepAlways</string>
      <key>TestHostBundleIdentifier</key>
      <string><%= xctest_identifier %></string>
      <key>TestingEnvironmentVariables</key>
      <dict>
        <key>DYLD_FRAMEWORK_PATH</key>
        <string>/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks</string>
        <key>DYLD_LIBRARY_PATH</key>
        <string>/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks</string>
        <key>XCODE_DBG_XPC_EXCLUSIONS</key>
        <string>com.apple.dt.xctestSymbolicator</string>
      </dict>
      <key>ToolchainsSettingValue</key>
      <array/>
      <key>UITargetAppCommandLineArguments</key>
      <array/>
      <key>UITargetAppMainThreadCheckerEnabled</key>
      <false/>
      <key>UserAttachmentLifetime</key>
      <string>keepAlways</string>
      <key>UITargetAppBundleIdentifier</key>
      <string><%= xcui_app_identifier %></string>
      <key>TestBundleDestinationRelativePath</key>
      <string>__TESTHOST__/PlugIns/<%= xctestname %>.xctest</string>
    </dict>
  </dict>
</plist>
