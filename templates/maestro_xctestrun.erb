<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>maestro-driver-iosUITests</key>
    <dict>
      <key>BlueprintName</key>
      <string>maestro-driver-iosUITests</string>
      <key>BundleIdentifiersForCrashReportEmphasis</key>
      <array>
        <string>dev.mobile.maestro-driver-ios</string>
        <string>dev.mobile.maestro-driver-iosUITests</string>
      </array>
      <key>CommandLineArguments</key>
      <array/>
      <key>DefaultTestExecutionTimeAllowance</key>
      <integer>600</integer>
      <key>DependentProductPaths</key>
      <array>
        <string><%=app_path%></string>
        <string><%=app_path%>/PlugIns/maestro-driver-iosUITests.xctest</string>
      </array>
      <key>EnvironmentVariables</key>
      <dict>
        <key>MJPEG_SERVER_PORT</key>
        <string></string>
        <key>OS_ACTIVITY_DT_MODE</key>
        <string>YES</string>
        <key>SQLITE_ENABLE_THREAD_ASSERTIONS</key>
        <string>1</string>
        <key>UPGRADE_TIMESTAMP</key>
        <string></string>
        <key>WDA_PRODUCT_BUNDLE_IDENTIFIER</key>
        <string></string>
      </dict>
      <key>IsUITestBundle</key>
      <true/>
      <key>IsXCTRunnerHostedTestBundle</key>
      <true/>
      <key>ProductModuleName</key>
      <string>maestro_driver_iosUITests</string>
      <key>RunOrder</key>
      <integer>0</integer>
      <key>SystemAttachmentLifetime</key>
      <string>keepNever</string>
      <key>TestBundlePath</key>
      <string><%=app_path%>/PlugIns/maestro-driver-iosUITests.xctest</string>
      <key>TestHostBundleIdentifier</key>
      <string>dev.mobile.maestro-driver-iosUITests.xctrunner</string>
      <key>TestHostPath</key>
      <string><%=app_path%></string>
      <key>TestLanguage</key>
      <string></string>
      <key>TestRegion</key>
      <string></string>
      <key>TestTimeoutsEnabled</key>
      <false/>
      <key>TestingEnvironmentVariables</key>
      <dict>
        <key>DYLD_INSERT_LIBRARIES</key>
        <string>/Developer/usr/lib/libMainThreadChecker.dylib</string>
        <key>MTC_CRASH_ON_REPORT</key>
        <string>1</string>
      </dict>
      <key>ToolchainsSettingValue</key>
      <array/>
      <key>UITargetAppCommandLineArguments</key>
      <array/>
      <key>UITargetAppEnvironmentVariables</key>
      <dict/>
      <key>UITargetAppMainThreadCheckerEnabled</key>
      <true/>
      <key>UseUITargetAppProvidedByTests</key>
      <true/>
      <key>UserAttachmentLifetime</key>
      <string>deleteOnSuccess</string>
    </dict>
    <key>__xctestrun_metadata__</key>
    <dict>
      <key>FormatVersion</key>
      <integer>1</integer>
    </dict>
  </dict>
</plist>
