<% 
  proxy_hosts_match = []
  default_condition = "return \"DIRECT\""
  ws_condition = "return \"PROXY #{proxy_host}:#{insecure_ws_proxy_port}\""

  
  if host_only
    hosts.split(",").each { |host_port|
      split_arr = host_port.split(":")
      if split_arr[0] == "*" && split_arr[1] == "*"
        default_condition = "return \"SOCKS #{proxy_host}:#{proxy_port}\""
        next
      end
      proxy_hosts_match.push("shExpMatch(url, '*#{split_arr[0]}:#{split_arr[1]}*')")
    }
  elsif use_proxy_instead_of_socks
    default_condition = "return \"PROXY #{proxy_host}:#{proxy_port}\""
  else
    default_condition = "return \"SOCKS #{proxy_host}:#{proxy_port}\""
  end
%>
function FindProxyForURL(url, host) {
<% @@whitelisted_hosts.each do |host| %>
    if(shExpMatch(host,'<%=host%>') && dnsResolve(host)) {
    return "DIRECT";
  }
<% end %>
<% @@whitelisted_urls.each do |url| %>
    if(shExpMatch(url,'<%=url%>') && dnsResolve(host)) {
    return "DIRECT";
  }
<% end %>

  if(shExpMatch(url,'ws:*')) {
    <%= ws_condition %>;
  }

<% if proxy_hosts_match.size > 0 %>  if (<%= proxy_hosts_match.join(" || ") %>) { return "SOCKS <%= proxy_host %>:<%= proxy_port %>" };<% end %>
  <%= default_condition %>;
}
