<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>HasRemovalPasscode</key>
    <false/>
    <key>PayloadContent</key>
    <array>
      <dict>
        <key>PayloadDescription</key>
        <string>Configures restrictions</string>
        <key>PayloadDisplayName</key>
        <string>Restrictions</string>
        <key>PayloadIdentifier</key>
        <string><%= payload_id %></string>
        <key>PayloadType</key>
        <string>com.apple.applicationaccess</string>
        <key>PayloadUUID</key>
        <string><%= payload_uuid %></string>
        <key>PayloadVersion</key>
        <integer>1</integer>
        <key>allowPasscodeModification</key>
        <<%= enable_passcode_settings %>/>
        <key>forceDelayedSoftwareUpdates</key>
        <true/>
        <key>allowFindMyDevice</key>
        <false/>
        <key>allowUSBRestrictedMode</key>
        <false/>
        <key>allowNFC</key>
        <false/>
        <key>allowBluetoothModification</key>
        <<%= enable_bluetooth_modification %>/>
        <key>allowPasswordProximityRequests</key>
        <false/>
        <% if profile_installation_ui_disallowed %>
        <key>allowUIConfigurationProfileInstallation</key>
        <false/>
        <% end %>
        <key>forceAutomaticDateAndTime</key>
        <<%= !!force_automatic_date_time %>/>
        <% if defined?(disable_passwords_app) && disable_passwords_app %>
          <key>blacklistedAppBundleIDs</key>
          <array>
              <string>com.apple.Passwords</string>
          </array>
        <% end %>
      </dict>
    </array>
    <key>PayloadDisplayName</key>
    <string><%= profile_display_name %></string>
    <key>PayloadIdentifier</key>
    <string><%= profile_identifier %></string>
    <key>PayloadOrganization</key>
    <string>BrowserStack</string>
    <key>PayloadRemovalDisallowed</key>
    <true/>
    <key>PayloadType</key>
    <string>Configuration</string>
    <key>PayloadUUID</key>
    <string><%= uuid %></string>
    <key>PayloadVersion</key>
    <integer><%= version || 1 %></integer>
  </dict>
</plist>
