<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key><%= xctestname %></key>
    <dict>
      <key>CommandLineArguments</key>
      <array>
        <% if language.to_s != "" %>
          <string>-AppleLanguages</string>
          <string>(<%= language %>)</string>
          <string>-AppleTextDirection</string>
          <string>NO</string>
        <% end %>
        <% if locale.to_s != "" %>
          <string>-AppleLocale</string>
          <string><%= locale %></string>
        <% end %>
      </array>
      <key>EnvironmentVariables</key>
      <dict>
        <key>OS_ACTIVITY_DT_MODE</key>
        <string>YES</string>
        <key>SQLITE_ENABLE_THREAD_ASSERTIONS</key>
        <string>1</string>
        <% environment_variables.each do |key, value| %>
          <key><%= key.to_s %></key>
          <string><%= value.to_s %></string>
        <% end %>
      </dict>
      <key>UseDestinationArtifacts</key>
      <true/>
      <key>ProductModuleName</key>
      <string><%= product_module_name %></string>
      <key>TestBundleDestinationRelativePath</key>
      <string>__TESTHOST__/PlugIns/<%= xctestname %>.xctest</string>
      <key>TestHostBundleIdentifier</key>
      <string><%= xctest_identifier %></string>
      <key>TestingEnvironmentVariables</key>
      <dict>
        <key>DYLD_INSERT_LIBRARIES</key>
        <string>__TESTHOST__/Frameworks/libXCTestBundleInject.dylib</string>
        <key>XCInjectBundleInto</key>
        <string>unused</string>
      </dict>
      <key>ToolchainsSettingValue</key>
      <array/>
      <key>UserAttachmentLifetime</key>
      <string>deleteOnSuccess</string>
    </dict>
    <key>__xctestrun_metadata__</key>
    <dict>
      <key>FormatVersion</key>
      <integer>1</integer>
    </dict>
  </dict>
</plist>
