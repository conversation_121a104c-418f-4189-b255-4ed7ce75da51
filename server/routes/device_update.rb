module BrowserStack
  class PlatformServer < Sinatra::Base
    post '/update_device/start' do
      BrowserStack.logger.info("Got Update request for: #{@device}")
      halt 404, "Device not found in config" if DeviceManager.all_devices[@device].nil?
      halt 400, "Device already under update" if @device_state.undergoing_update_file_present?

      model = @@devices_conf["devices"][@device.to_s]["device_name"]
      fork do
        manager = UpdateManager.new(@device, model)
        manager.start_update
      end

      status 200
    end

    post '/update_device/reset' do
      @device_state.remove_undergoing_update_file
      @device_state.remove_update_failed_file

      status 200
    end

    get '/update_device/status' do
      message = "Successful"
      status = 200
      if @device_state.undergoing_update_file_present?
        message = "Undergoing"
        status = 202
      elsif @device_state.update_failed_file_present?
        message = "Failed"
      elsif @device_state.incompatible_machine_for_update_file_present?
        @device_state.remove_incompatible_machine_for_update_file
        message = "Incompatible Machine"
      end
      return status, { "status": message }.to_json
    end
  end
end
