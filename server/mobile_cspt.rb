require 'json'
require 'net/http'
require 'fileutils'
require 'browserstack_logger'
require '/usr/local/.browserstack/realmobile/lib/utils/push_to_zombie'
require_relative '../lib/overridden/thread'
require_relative '../lib/utils/http_utils'
require_relative '../config/constants'

BS_DIR = '/usr/local/.browserstack'.freeze
MCSPT_DEVICE_FILES_DIR = '/usr/local/.browserstack/csp_devices'.freeze
CSPT_FEATURE = "mcspt_session".freeze

class MCSPT # rubocop:todo Metrics/ClassLength
  @port = 45700
  @host = "localhost"
  @perf_tool_hostname = "127.0.0.1"
  @perf_tool_port = 45701
  @aa_recorded_metrics = %w[cpu memory disk]
  @default_app_automate_bundles = ["com.facebook.WebDriverAgentRunner.xctrunner",
                                   "com.apple.test.WebDriverAgentRunner-Runner",
                                   "com.browserstack.Launcher",
                                   "com.browserstack.Launcher.live-streaming"]

  def self.get_device_file_path(device_id)
    "#{MCSPT_DEVICE_FILES_DIR}/#{device_id}"
  end

  def self.get_session_file_path(session_id)
    "#{MCSPT_DEVICE_FILES_DIR}/#{session_id}"
  end

  def self.get_session_info_file_path(device_id)
    "#{MCSPT_DEVICE_FILES_DIR}/#{device_id}_session_info.json"
  end

  def self.set_running_session(session_id, cspt_session_id)
    session_file = get_session_file_path(session_id)
    FileUtils.mkdir_p(MCSPT_DEVICE_FILES_DIR) unless Dir.exist? MCSPT_DEVICE_FILES_DIR
    File.open(session_file, 'w') { |file| file.write(cspt_session_id.to_s) }
  end

  def self.get_session_running_on_device(device_id)
    device_file = get_device_file_path(device_id)
    File.read(device_file).strip.split(",") if device_id.to_s != "" && File.exist?(device_file)
  end

  def self.get_running_session(session_id)
    # Returns the current running session based on session_id
    session_file = get_session_file_path(session_id)
    File.read(session_file).strip if session_id.to_s != "" && File.exist?(session_file)
  end

  def self.delete_running_session(session_id)
    # Returns the current running session based on session_id
    session_file = get_session_file_path(session_id)
    FileUtils.rm_f(session_file) if File.exist? session_file
  end

  def self.copy_session_info(device_id)
    FileUtils.copy_file(
      "/tmp/_mobile/session/#{device_id}/session_info.json", get_session_info_file_path(device_id)
    )
  end

  def self.get_session_report_file_path(session_id)
    "#{MCSPT_DEVICE_FILES_DIR}/report_#{session_id}.json"
  end

  def self.send_request(url, payload, method = "post")
    Timeout.timeout(30) do
      Net::HTTP.new(@host, @port).start do |http|
        if method.to_s.downcase == "post"
          request = Net::HTTP::Post.new(url, 'Content-Type' => 'application/json')
          request.body = payload.to_json
        else
          request = Net::HTTP::Get.new(url, 'Content-Type' => 'application/json')
        end
        response = http.request(request)
        begin
          return [response.code, JSON.parse(response.body)]
        rescue
          return [400, {}]
        end
      end
    end
  rescue Timeout::Error
    BrowserStack.logger.info("Timeout while sending request to #{url}")
    [400, {}]
  end

  def self.send_request_py_server(url, payload, method = "post")
    Timeout.timeout(30) do
      Net::HTTP.new(@perf_tool_hostname, @perf_tool_port).start do |http|
        if method.to_s.downcase == "post"
          request = Net::HTTP::Post.new(url, 'Content-Type' => 'application/json')
          request.body = payload.to_json
        else
          request = Net::HTTP::Get.new(url, 'Content-Type' => 'application/json')
        end
        response = http.request(request)
        begin
          return [response.code, JSON.parse(response.body)]
        rescue => e
          BrowserStack.logger.info("py server exception #{e.message}")
          return [400, {}]
        end
      end
    end
  rescue Timeout::Error
    BrowserStack.logger.info("Timeout while sending request to #{url}")
    {}
  end

  def self.get_app_details(device_id, bundle_id, origin)
    url = "/api/v1/ios/devices/#{device_id}/apps/#{bundle_id}?origin=#{origin}"
    code, response = send_request(url, {}, "get")
    if code.to_i == 200
      modified_resp = response&.map do |k, v|
        if k.to_s == "packageName"
          ["app_package", v]
        else
          ["app_#{k}", v]
        end
      end
      return Hash[modified_resp] if modified_resp

      response
    else
      BrowserStack.logger.info("MCSPT App Detail API Failed. Device: #{device_id}, Bundle Id: #{bundle_id}")
      {}
    end
  end

  def self.get_analytics(session_id, device_id, bundle_id)
    cspt_session_id = get_running_session(session_id)
    app_details = get_app_details(device_id, bundle_id, "analytics")
    { app: app_details, cspt_session_id: cspt_session_id.to_s }
  end

  def self.get_session_info(device_id)
    if File.exist?(get_session_info_file_path(device_id))
      data = JSON.parse(File.read(get_session_info_file_path(device_id)).strip)
      data["s3"]["stats"]["url"].gsub! "stats.csv", "cspt-session-report.json"
      {
        "s3_id" => data["s3"]["stats"]["id"].gsub("\n", ""),
        "s3_key" => data["s3"]["stats"]["key"].gsub("\n", ""),
        "s3_url" => data["s3"]["stats"]["url"].gsub("\n", "")
      }
    else
      {}
    end
  rescue => e
    BrowserStack.logger.error "CSPT parsing the session info file failed with error #{e.message}"
    {}
  end

  def self.get_pusher_params(device_id)
    file_path = "#{BS_DIR}/state_files/#{device_id}_session"
    if File.exist?(file_path)
      data = JSON.parse(File.read(file_path).strip)
      {
        "pusher_url" => data["pusher_url"],
        "pusher_channel" => data["pusher_channel"],
        "pusher_auth" => data["pusher_auth"]
      }
    else
      {}
    end
  end

  def self.notify_pusher(device_id, session_id, message)
    BrowserStack.logger.info(
      "Sending CSPT pusher event for session: #{session_id} and message: #{message}"
    )
    params = get_pusher_params(device_id)
    pusher_params_hash = {
      event: "update_app_profiling_session",
      type: "app_frameworks",
      app_live_session_id: session_id,
      channel: params["pusher_channel"],
      token: params["pusher_auth"],
      message: { "session_id": session_id.to_s, "mcspt_report_upload_status": message.to_s }.to_json
    }
    pusher_params = URI.encode_www_form(pusher_params_hash)
    pusher_url = "#{params['pusher_url']}/sendMessage"
    BrowserStack.logger.info "CSPT Sending pusher event #{pusher_url}"
    cmd = "curl -XPOST \"#{pusher_url}\" -d \"#{pusher_params}\""
    OSUtils.execute(cmd)
  rescue => e
    BrowserStack.logger.error "Sending message to pusher failed #{e.message}"

  end

  def self.upload_session_logs_to_s3(device_id, session_id, data)
    BrowserStack.logger.info(
      "Uploading CSPT session report to S3 for session: #{session_id}"
    )
    session_report_file = "/tmp/mcspt_session_report_#{session_id}.json"
    File.open(session_report_file, 'w') { |file| file.write(data.to_json) }
    file_size = File.size(session_report_file) / 1024.0
    eds_kind = EdsConstants::APP_AUTOMATE_TEST_SESSIONS
    session_info = get_session_info(device_id)
    upload_url = session_info["s3_url"]
    s3_id = session_info["s3_id"]
    s3_key = session_info["s3_key"]
    s3_region = if upload_url
                  upload_url.gsub(/.amazonaws.*/, '').gsub("https://s3-", '')
                else
                  ""
                end
    s3_storage_class = if file_size < 128
                         "STANDARD"
                       else
                         "STANDARD_IA"
                       end
    status, error = Utils.upload_file_to_s3(
      s3_id, s3_key, "text/plain", session_report_file, "public-read", upload_url, session_id, "app_automate", s3_region, 900, {}, {}, s3_storage_class
    )
    File.delete(session_report_file)
    [status, error]
  rescue => e
    BrowserStack.logger.error("Unable to upload CSPT session report to S3 - #{e.message} \n\n#{e.backtrace.join("\n")}")
    [false, e.message]
  end

  def self.start_session(session_id, user_id, device_id, bundle_id, app_relaunch, region: "default", app_automate: false, app_startup_time_flag: true) #rubocop:todo Metrics/AbcSize
    # Starts the session for the given device and app
    url = "/api/v1/session"
    payload = { deviceID: device_id, appID: bundle_id, appRelaunch: app_relaunch, userId: user_id.to_i, region: region, appStartupTimeEnabled: app_startup_time_flag }
    if app_automate
      payload[:appAutomateSessionId] = session_id
      source = "app-automate"
    else
      payload[:appLiveSessionId] = session_id
      source = "app-live"
    end
    url += "?source=#{source}"
    device_file = get_device_file_path(device_id)
    BrowserStack.logger.info("Starting MCSPT Session for the #{source} session: #{session_id}")
    code, response = MCSPT.send_request(url, payload)
    data = { request: payload, response_code: code, response: response }
    if response["status"] == "Success" && response["sessionID"]
      MCSPT.set_running_session(session_id, response["sessionID"])
      FileUtils.mkdir_p(MCSPT_DEVICE_FILES_DIR) unless Dir.exist? MCSPT_DEVICE_FILES_DIR
      File.open(device_file, 'w') { |file| file.write("#{session_id},#{user_id},#{source}") }
      copy_session_info(device_id) if source == "app-automate"
      zombie_push('ios', "#{source}-cspt-session-start-success", '', '', data.to_json, device_id, session_id, user_id)
    else
      zombie_push('ios', "#{source}-cspt-session-start-failure", '', '', data.to_json, device_id, session_id, user_id)
      Utils.send_general_feature_usage_data_to_eds(session_id, CSPT_FEATURE, false, GENRE_APP_AUTOMATE, "Session start failure: #{response['status']}") if app_automate
    end
  rescue => e
    BrowserStack.logger.info("Unable to start session - #{e.message} \n\n#{e.backtrace.join("\n")}")
    data = { request: { deviceID: device_id, appID: bundle_id, appRelaunch: app_relaunch, userId: user_id } }
    if app_automate
      data[:request][:appAutomateSessionId] = session_id
      Utils.send_general_feature_usage_data_to_eds(session_id, CSPT_FEATURE, false, GENRE_APP_AUTOMATE, "Session start failure: #{e.message}")
    else
      data[:request][:appLiveSessionId] = session_id
    end
    zombie_push('ios', "#{source}-cspt-session-start-failure", e.message, '', data.to_json, device_id, session_id, user_id)
  end

  def self.fetch_stop_session_response(device_id, session_id, cspt_session_id, payload, source)
    BrowserStack.logger.info(
      "Fetching CSPT session #{cspt_session_id} stop response for #{source} session #{session_id}"
    )
    url = "/api/v1/session/#{cspt_session_id}/stop?source=#{source}"
    MCSPT.delete_running_session(session_id)
    session_report_file = get_session_report_file_path(session_id)
    if source == "app-automate" && File.exist?(session_report_file)
      # If session report file already exists then the session has timed out.
      response = JSON.parse(File.read(session_report_file).strip)
      code = 200
    else
      code, response = MCSPT.send_request(url, payload)
    end
    [code, response]
  ensure
    device_file = get_device_file_path(device_id)
    FileUtils.rm_f(device_file) if File.exist?(device_file)
    FileUtils.rm_f(session_report_file) if source == "app-automate" && File.exist?(session_report_file)
  end

  def self.process_stop_session_response(device_id, session_id, user_id, response, source)
    BrowserStack.logger.info(
      "Processing CSPT stop session response for #{source} session #{session_id}"
    )
    if source == "app-automate"
      if response["status"] == "Session Stopped"
        status, error = upload_session_logs_to_s3(device_id, session_id, response)
        BrowserStack.logger.info("CSPT Report logs upload status: #{status}")
        if status
          notify_pusher(device_id, session_id, "SUCCESS")
          Utils.send_general_feature_usage_data_to_eds(session_id, CSPT_FEATURE, true, GENRE_APP_AUTOMATE, "")
        else
          BrowserStack.logger.info("CSPT Report logs upload failure error: #{error}")
          notify_pusher(device_id, session_id, "FAILURE")
          zombie_push('ios', "#{source}-cspt-session-report-upload-failure", error.to_s, '', {}.to_json,
                      device_id, session_id, user_id)
          Utils.send_general_feature_usage_data_to_eds(session_id, CSPT_FEATURE, false, GENRE_APP_AUTOMATE, "Report upload failure: #{error}")
        end
      else
        BrowserStack.logger.info(
          "CSPT stop session failed with status: #{response['status']}"
        )
        Utils.send_general_feature_usage_data_to_eds(session_id, CSPT_FEATURE, false, GENRE_APP_AUTOMATE, "Session stop failure: #{response['status']}")
      end
    end
  ensure
    session_info_file = get_session_info_file_path(device_id)
    FileUtils.rm_f(session_info_file) if source == "app-automate" && File.exist?(session_info_file)
  end

  def self.stop_session(session_id, user_id, device_id, cancelled: false)
    # Stops the CSPT session for the given app live session

    cspt_session_id = MCSPT.get_running_session(session_id)
    BrowserStack.logger.info("Stopping MCSPT Session: #{cspt_session_id} for the Session: #{session_id} with cancelled: #{cancelled}")
    payload = { cancelled: cancelled, userId: user_id.to_i }
    if cspt_session_id
      source = get_session_running_on_device(device_id)[2]
      code, response = fetch_stop_session_response(device_id, session_id, cspt_session_id, payload, source)
      process_stop_session_response(device_id, session_id, user_id, response, source) if cancelled == false
      data = { request: payload, response_code: code }
      if response["status"] == "Session Stopped"
        zombie_push('ios', "#{source}-cspt-session-stop-success", '', '', data.to_json, device_id, session_id, user_id)
      else
        data[:response] = response
        zombie_push('ios', "#{source}-cspt-session-stop-failure", response["status"], '', data.to_json, device_id, session_id, user_id)
      end
    end
  rescue => e
    BrowserStack.logger.info("Unable to stop session - #{e.message} \n\n#{e.backtrace.join("\n")}")
    zombie_push('ios', "#{source}-cspt-session-stop-failure", e.message, '', {}.to_json, device_id, session_id, user_id)
    Utils.send_general_feature_usage_data_to_eds(session_id, CSPT_FEATURE, false, GENRE_APP_AUTOMATE, "Session stop failure: #{e.message}") if source == "app-automate"
  end

  def self.start_session_async(session_id, user_id, device_id, bundle_id, app_relaunch, region: "default", app_automate: false, app_startup_time_flag: true)
    BrowserStack.logger.info("Starting CSPT session in async for session: #{session_id}")
    data = { user_id: user_id, device_id: device_id, bundle_id: bundle_id, region: region }
    if app_automate
      data[:app_automate_session_id] = session_id
    else
      data[:app_live_session_id] = session_id
    end
    if [session_id.to_s, user_id.to_s, device_id.to_s, bundle_id.to_s].include?("")
      zombie_push('ios', 'cspt-start-session-failed-invalid-inputs', "", '', data.to_json, device_id, session_id, user_id)
      BrowserStack.logger.info("Invalid input in start session: #{data}")
      return [400, {}]
    end
    cspt_session_id = MCSPT.get_running_session(session_id)
    if cspt_session_id
      device_file = get_device_file_path(device_id)
      if File.exist?(device_file)
        if Time.now - File.ctime(device_file) < 1800
          BrowserStack.logger.info("MCSPT Session: #{cspt_session_id} already running for Session: #{session_id}")
          return [400, {}]
        end
        FileUtils.rm_f(device_file)
      end
    end
    Thread.bs_run do
      start_session(session_id, user_id, device_id, bundle_id, app_relaunch, region: region, app_automate: app_automate, app_startup_time_flag: app_startup_time_flag)
    end
    [200, {}]
  end

  def self.stop_session_async(session_id, user_id, device_id, cancelled: false)
    BrowserStack.logger.info("Stopping CSPT session in async for session: #{session_id}")
    data = { session_id: session_id, user_id: user_id, device_id: device_id }
    if [session_id.to_s, user_id.to_s, device_id.to_s].include?("")
      kind = 'cspt-stop-session-failed-invalid-inputs'
      msg = "Invalid input in stop session: #{data}"
      if cancelled
        kind = 'cspt-cancel-session-failed-invalid-input'
        msg = "Invalid input in cancel session: #{data}"
      end
      BrowserStack.logger.info(msg)
      zombie_push('ios', kind, '', '', data.to_json, device_id, session_id, user_id)
      return [400, {}]
    end
    cspt_session_id = MCSPT.get_running_session(session_id)
    unless cspt_session_id
      BrowserStack.logger.info("No MCSPT Session running for the session: #{session_id}")
      return [404, {}]
    end
    Thread.bs_run do
      stop_session(session_id, user_id, device_id, cancelled: cancelled)
    end
    [200, {}]
  end

  def self.generate_perf_payload(action, device_id, session_id, user_id, poll_interval, bundles_to_be_monitored)
    bundles_to_be_monitored.to_a.map do |app_bundle_id|
      perf_URI = "/devices/#{device_id}/apps/#{app_bundle_id}/sessions/#{session_id}/metrics/all/#{action}/"
      query = URI.encode_www_form({ metrics: @aa_recorded_metrics.join(","), interval: poll_interval })
      "#{perf_URI}?#{query}"
    end
  end

  def self.platform_perf_measurement(action, product, session_id, user_id, app_bundle_ids, device_id, options = {})
    BrowserStack.logger.info("Starting platform app perf measurement: #{session_id}")
    raise "Invalid action" unless %w[start stop].include?(action)

    # construct payload
    poll_interval = options[:interval] || 30000 #default per 30seconds
    bundles_to_be_monitored = app_bundle_ids.to_a | @default_app_automate_bundles
    perf_URLS = generate_perf_payload(action, device_id, session_id, user_id, poll_interval, bundles_to_be_monitored)

    # request in async
    perf_URLS.map do |url|
      Thread.bs_run do
        BrowserStack.logger.info("Starting perf measurement for: #{session_id} : url: #{url}")
        t = Time.now
        code, response = MCSPT.send_request_py_server(url, {}, "post")
        data = { request: url, response_code: code, response: response }
        zombie_push('ios', "#{product}-cspt-perf-#{action}-req", (Time.now - t).round(2) , '', data.to_json, device_id, session_id, user_id)
      end
    end

  rescue => e
    # instrument the success/failures. There is no way to kill process if stop request fails, the below kind can be monitored.
    BrowserStack.logger.info("Unable to #{action} perf session - #{e.message} \n\n#{e.backtrace.join("\n")}")
    zombie_push('ios', "#{product}-cspt-perf-#{action}-req-failed", e.message, '', data.to_json, device_id, session_id, user_id)
  end

  def self.clean_platform_perf_measurement_stats
    #TODO: incase we are pushing the metrics/analysis clean the generated logs
    # /var/log/browserstack/csp/sessions
  end

  def self.stop_session_running_on_device_async(device_id, cancelled: false)
    BrowserStack.logger.info(
      "Stopping CSPT session running in async for device: #{device_id} with cancelled: #{cancelled}"
    )
    session_id, user_id = get_session_running_on_device(device_id)
    if session_id.to_s != ""
      stop_session_async(session_id, user_id, device_id, cancelled: cancelled)
    else
      BrowserStack.logger.info("No CSPT session running on device: #{device_id}")
    end
  end

  def self.start_session_for_framework(params, device)
    is_app_testing = !params[:genre].nil? && params[:genre].to_s.eql?('app_automate')
    begin
      if is_app_testing && params[:app_profiling].to_s == "true"
        code, response = MCSPT.start_session_async(
          params['automate_session_id'].to_s, params[:user_id].to_s, device,
          params['app_testing_bundle_id'].to_s, false, app_automate: true
        )
      end
    rescue => e
      BrowserStack.logger.error("Error occurred while starting CSPT session: #{e.message}")
      BrowserStack.logger.error(e.backtrace.join("\n"))
    end
  end
end
