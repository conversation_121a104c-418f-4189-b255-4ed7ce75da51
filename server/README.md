## cleanup_fixes.rb
[Module](./cleanup_fixes.rb) created to handle cleanup exceptions and program a way to attept to resolve the error should it occur.
Created to avoid having to program complex logic into cleanup scripts/classes exceptions. 

Can be included into other classes to provide functionality. 

> In the current state it's relatively simple and can't handle all types of params. 

### How it works
#### Add a function to handle the exception
Create a handler function 
```ruby
module CleanupFixes
  def my_handler(device_id, some_param)
    `sudo rm -r /usr/local/.browserstack/`
  end
end
```
> It's best to try leave default params whenever possible. 

#### Add function to EXCEPTION_MESSAGE_MAP
Add an entry to the hash with the format

```ruby
{
  "exception_message": { "func": "function_symbol", "retries": 10 }
}
```

*Example*
```ruby
EXCEPTION_MESSAGE_MAP = {
  ...
  { "undefined local variable or method `l' for main:Object": {
      "func": :my_handler,
      "retries": 1
    }
  }
}.freeze
```

#### Managing State
Typically fixes aren't garenteed to work. So in order to not have the fixes get stuck in a loop a count is managed by the statefiles. The files just store the number of times the function has been run. 

The statefiles are stored in `{STATE_FILES_PATH}/cleanup_fixes/{func_name}_{...args}`

Checkout the [tests](../spec/server/cleanup_fixes_spec.rb) to see what way filenames are generated. 

### Testing in irb
You'll probably need to test these inside the main applications context, here's some scripts to do that. 

```shell
cd /usr/local/.browserstack/realmobile && bundle exec irb
```

> Running these should rebuild ios-njb and run a test on the device.

```ruby
require_relative 'server/cleanup_fixes'

# Test in general context
include  CleanupFixes
def test_exception(exception_message)
	raise exception_message
rescue => e
	handle_cleanup_execption(e, '00008110-00161D1422A2801E')
end
test_exception('An element could not be located on the page using the given search parameters.')

# Test in class context
class Temp
  include CleanupFixes
	
	def test_exception(exception_message)
		raise exception_message
	rescue => e
		handle_cleanup_execption(e, '00008110-00161D1422A2801E')
	end
end
t = Temp.new
t.test_exception('An element could not be located on the page using the given search parameters.')
```