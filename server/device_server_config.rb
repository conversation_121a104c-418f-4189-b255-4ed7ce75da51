rackup 'device_server.ru'

# https://www.rubydoc.info/gems/puma/Puma/DSL#prune_bundler-instance_method
# This allows us to install new gems with just a phased-restart. Otherwise you
# need to take the master process down each time.
prune_bundler

port 45691

workers 2

worker_shutdown_timeout 300

persistent_timeout 75

tag 'device_server'

# Until we update ruby version to 2.5 we will have forking issues in new mac mini
# For reference https://github.com/puma/puma/issues/1421
if /darwin/ =~ RUBY_PLATFORM
  before_fork do
    require 'fiddle'
    # Dynamically load Foundation.framework, ~implicitly~ initialising
    # the Objective-C runtime before any forking happens in Puma
    Fiddle.dlopen '/System/Library/Frameworks/Foundation.framework/Foundation'
  end
end
