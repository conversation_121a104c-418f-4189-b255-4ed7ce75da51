require 'English'
require 'sinatra/base'

require 'fileutils'
require 'sinatra/config_file'
require 'json'
require 'logger'
require 'timeout'
require 'net/https'
require 'bsdwh'
require 'benchmark'
require 'static_conf'
require 'server_info'
require 'network_helper'
require 'sirenlogs'
require 'screenshot_manager'
require 'interaction_sync_stability_tester'
require 'securerandom'
require 'env_middleware'

require_relative 'device_manager'
require_relative 'iphone'
require_relative 'routes/device_update'

require_relative '../lib/configuration'
require_relative '../lib/helpers/wda_client'
require_relative '../lib/helpers/xcui_test_helper'
require_relative '../lib/ios_influxdb_client'

require_relative '../lib/helpers/cleanup_helpers'
require_relative '../lib/helpers/crash_log'
require_relative '../lib/helpers/voiceover_helper'
require_relative '../lib/helpers/blueutil_helper'
require_relative '../lib/utils/download_file'
require_relative '../lib/utils/utils'
require_relative '../lib/utils/osutils'
require_relative '../lib/utils/ios_mdm_service_client'
require_relative '../lib/utils/alerter'
require_relative '../lib/utils/zombie'
require_relative '../lib/utils/push_to_zombie'
require_relative '../lib/utils/helpers'
require_relative '../lib/utils/hooter'
require_relative '../lib/utils/image_injector'
require_relative '../lib/utils/file_injector'
require_relative '../lib/overridden/thread'
require_relative '../lib/models/device_state'
require_relative '../lib/utils/device_settings_util'
require_relative '../lib/utils/app_settings_util'
require_relative '../lib/percy/percy_session'
require_relative '../lib/percy/tiles_manager'
require_relative '../lib/device_setup/backup_manager/managers/cfgutil_backup_manager'
require_relative '../lib/device_update/update_manager'
require_relative '../lib/helpers/automate_gestures/shake'
require_relative '../lib/helpers/automate_gestures/apple_pay_executor'
require_relative '../lib/utils/app_analytics_util'
require_relative '../lib/utils/idevice_utils'
require_relative '../lib/utils/ios_watcher'
require_relative '../lib/utils/configuration_profiles_manager'
require_relative '../lib/helpers/apple_pay_session_data'
require_relative '../lib/helpers/apple_pay_prefill_detail_helper'
require_relative 'mobile_cspt'
require_relative '../lib/helpers/app_accessibility'
require_relative '../lib/helpers/authentication_helper'
require_relative '../lib/helpers/browser_activity_monitoring'
require_relative '../lib/helpers/session_save_polling'
require_relative '../lib/device_setup/full_cleanup/mdm_cleanup_manager'
require_relative '../lib/feature_flags'
require_relative '../lib/custom_exceptions'
require_relative '../lib/helpers/device_sim_helper'

BS_DIR = '/usr/local/.browserstack'.freeze
MOBILE_COMMON_ROOT = "#{BS_DIR}/mobile-common".freeze
DIR_HOME = "#{BS_DIR}/realmobile".freeze

require 'browserstack_logger'
require "#{MOBILE_COMMON_ROOT}/utils/server_utils"
require "#{MOBILE_COMMON_ROOT}/utils/app_patching_util"
require "#{MOBILE_COMMON_ROOT}/utils/auth_key_util"

include ServerUtils
include StaticConf
include AppPatchingUtil
include AuthKeyUtil

CLEANUP_SCRIPT = "#{__dir__}/cleanup.rb".freeze

DEVICE_FILTER_PATH = %w[
  /start
  /restart
  /app_start
  /update_app
  /stop
  /cleanup
  /cleanup_done
  /set_cleanup_policy
  /pacfile
  /selenium_command
  /snapshot
  /snapshot_hub
  /logs
  /device_logs
  /hit_wda_endpoint
  /cleanup_failure_reason
  /remove_device
  /accept_alert
  /start_xcuitest_session
  /start_fluttertest_session
  /start_maestro_session
  /start_earlgrey_session
  /timeout_session
  /set_geolocation
  /kill_all_apps
  /shake
  /user_installed_apps
  /user_installed_app_details
  /update_network
  /update_device_locale_and_region
  /update_device_log_level
  /relaunch_app_with_locale
  /log_bridge_commands
  /reset_app
  /geturl
  /get_screenshot
  /get_snapshot_details
  /restart_device
  /uninstall_user_apps
  /reset_auth_keys
  /app_strings
  /install_app
  /inject_image
  /inject_media
  /update_app_settings
  /app_percy/screenshot
  /percy/start_server
  /percy/stop_server
  /percy/start_jackproxy
  /percy/stop_jackproxy
  /percy/dom_metadata_finalize
  /percy/dom_metadata_upload
  /percy/clean_mobile
  /percy/capture_tile
  /percy/capture_finalize
  /percy/health_check
  /percy/is_running
  /percy/keep_alive
  /percy/minified_cleanup
  /percy/minified_cleanup_done
  /percy/screenshot
  /percy/setup_automate_session
  /launch_user_app_settings
  /set_timezone
  /set_ios_settings
  /biometric_user_option
  /get_ios_settings_state
  /update_device/start
  /update_device/status
  /update_device/reset
  /media/push_to_device
  /upload_crash_logs
  /download_files
  /execute_apple_pay
  /prefill_apple_pay_detail
  /device_info
  /get_device_injected_file
  /trigger_interaction_sync_script
  /accessibility_info
  /toggle_continuous_scanning
  /toggle_private_device_feature
].freeze

# List of endpoints with which DEVICE_ID_HEADER header will be allowed as a part of securing
# the exposing localhost server via privoxy in case of app live with sensor mocking enabled
DEVICE_HEADER_ALLOWED_PATHS = [
  '/get_patch_type',
  '/get_rtc_data',
  '/biometric_value',
  '/xcuitest/inject-media',
  '/device_logger_port'
].freeze

feature_flags = FeatureFlags.new
SECRET_SCANNING_FLAG = feature_flags&.get_flag("secret_scanning")
RAILS_ENV_RESTRICIONS_FLAG = feature_flags&.get_flag("rails_env_restrictions")

REDACTION_STRING = "[REDACTED]".freeze

def detect_and_push_reason_for_start
  puts "[#{Process.pid}] Checking reason for Puma worker start.."

  # If Worker has started within 300 seconds of deployment
  if (Time.now - File.mtime("#{DIR_HOME}/.browserstack_build_version.json")) <= 300
    puts "[#{Process.pid}] Worker Starting possibly due to Deployment"
    return
  end

  # If Worker has started due to `restart_servers` or `pumactl -p <pid> phased-restart`
  if `gtimeout 2 tail -50 #{LOGGING_DIR}/realmobile_plist.log | grep -c "phased upgrade"`.strip.to_i != 0
    puts "[#{Process.pid}] Worker Starting Due to Manual Phased Restart"
    return
  end

  puts "[#{Process.pid}] Worker Starting Due to Unknown Reason (Possible Worker Crash)"
  zombie_push("ios", "puma-worker-starting", "possible-crash", "ios", Process.pid.to_s)
end

if ENV["RACK_ENV"] != 'test'
  begin
    detect_and_push_reason_for_start
  rescue => e
    puts "[#{Process.pid}] Unable to detect puma worker start reason: #{e.message} #{e.backtrace.join("\n")}"
  end
end

module BrowserStack
  class PlatformServer < Sinatra::Base # rubocop:todo Metrics/ClassLength

    configure do # rubocop:todo Metrics/BlockLength
      set :clean_trace, true

      # TODO: The following  logic doesn't really belong in this block, which will be run every
      # time this file is loaded, making test isolation extremely difficult.
      # Configuration from common file location:
      conf = Configuration.new
      @@server_config = conf.all

      @@environment = @@server_config['environment']
      @@static_conf_file = @@server_config['static_conf_file']
      @@config_json_file = @@server_config['config_json_file']
      begin
        @@devices_conf = JSON.parse(File.read(@@config_json_file))
      rescue
        @@devices_conf = {}
      end
      @@static_conf = @@server_config['static_conf']

      set :static_conf, @@static_conf

      # Make sure the dir to serve static files is present/created
      @@templates_dir = @@server_config['templates_dir']
      @@pacfile_dir = @@server_config['pacfile_dir']
      @@log_file = "#{@@server_config['logging_root']}/#{@@environment}.log"
      FileUtils.mkdir_p @@pacfile_dir

      IdeviceUtils.configure(@@server_config)
      IosMdmServiceClient.configure
      Alerter.configure(@@server_config)

      @@automate_per_device_config = YAML.load_file(
        "#{File.expand_path(__dir__)}/../config/automate_per_device_config.yml"
      )

      if @@environment == 'prod'
        # Spit stdout and stderr to a file during production
        # in case something goes wrong
        $stdout.reopen(@@log_file, 'a')
        $stdout.sync = true
        $stderr.reopen($stdout)
      end

      register Sirenlogs

      DeviceManager.configure @@server_config

      BrowserStack::IosInfluxdbClient.component = 'server'

      logger_params = { device: 'N/A', component: 'server.rb', subcomponent: 'N/A', session: 'N/A' }
      BrowserStack.init_logger(@@log_file, logger_params)
    end

    def mark_replay_kit_still_running(reason)
      device_config = @@devices_conf["devices"][@device.to_s]
      platform_version = device_config['platform_version'].to_f
      return if platform_version >= 13.4

      BrowserStack.logger.info("Marking ReplayKit still running")
      @hooter.send_replay_kit_stop_failure(@device.to_s, device_config['device_name'], platform_version)
      @device_state.touch_replay_kit_running_file
    end

    def send_app_live_url_to_eds(params)
      session_id = params["app_live_session_id"]
      device = @device.to_s
      debugger_port = @@devices_conf["devices"][device]["debugger_port"] unless @@devices_conf["devices"][device].nil?
      url = DeviceManager.get_url(device, debugger_port)

      BrowserStack.logger.info("app_live_geturl got #{url} to respond for device #{device}")

      if !url.nil? && url != ""
        eds_data = { event_name: "app_live_url_opened", session_id: session_id, url_opened: url, platform: "realios" }
        Utils.send_to_eds(eds_data, EdsConstants::APP_LIVE_WEB_EVENTS, true, req_params: params)
      end
      FileUtils.rm_f("#{@@server_config['state_files_dir']}/al_app_analytics_#{@params['app_live_session_id']}.json")
    rescue => e
      BrowserStack.logger.info("Exception in send_app_live_url_to_eds #{e.message}  #{e.backtrace.join('\n')}")
    end

    def hit_wda
      wda_endpoint = params['endPoint']
      raise StandardError, "Mobile device: #{@device} not found" if @@devices_conf["devices"][@device.to_s].nil?

      return 400, { error: 'Device pointing to prod. Please point it to some staging env' }.to_json if @@static_conf.device_pointed_to_prod?(@device.to_s)

      webdriver_port = @@devices_conf["devices"][@device.to_s]["webdriver_port"]

      response = yield(webdriver_port, wda_endpoint)
      [200, response.to_json]
    rescue => e
      BrowserStack.logger.error("Exception in /hit_wda_endpoint device: #{@device} exception: #{e.message}\n#{e.backtrace.join("\n")}")
      status 422
      { error: "Exception in /hit_wda_endpoint device: #{@device}; exception: #{e.message}\n#{e.backtrace.join("\n")}" }.to_json
    end

    before do # rubocop:todo Metrics/BlockLength
      BrowserStack.logger.params[:subcomponent] = request.path_info
      params.delete(:captures) if params.key?(:captures) && params[:captures].empty?

      unless safe_params?(params)
        BrowserStack.logger.error("Illegal character(s) detected in params: #{params}")
        halt(400, 'Illegal character(s) found in request params')
      end

      session = params[:app_live_session_id] || params[:live_session_id] || params[:automate_session_id]
      BrowserStack.logger.params[:session] = session
      BrowserStack.logger.params[:device] = params[:device]

      # If HTTP_X_BSTACKNONCE header, which is the device id of the device from which request is originating is sent for a request and the path is not in the list of allowed paths for that header, simply return 404 and do nothing.
      # This is being done for adding a security layer. The HTTP_X_BSTACKNONCE header will be added by privoxy in case of sensor mocking enabled in app live sessions
      # Since we are exposing our server localhost:45671 in privoxy via forwarding, this will make sure only one endpoint is accessible to user with the header only.
      # Also checking whether it is being routed from the forwarded endpoint only
      if !request.env[DEVICE_ID_HEADER].to_s.empty? && (request.env['HTTP_HOST'].to_s == SENSOR_MOCKER_HOST)
        unless DEVICE_HEADER_ALLOWED_PATHS.include?(request.path_info)
          BrowserStack.logger.error("#{DEVICE_ID_HEADER} sent but path #{request.path_info} is not whitelisted")
          halt(404)
        end

        # Validate device id passed
        @device = request.env[DEVICE_ID_HEADER]
        if @@devices_conf["devices"][@device].nil?
          BrowserStack.logger.error("Device #{@device} is not present on machine for path #{request.path_info}")
          halt(200)
        end
      end

      device_info = @@devices_conf['devices'][@device.to_s]
      if DEVICE_HEADER_ALLOWED_PATHS.include?(request.path_info) && !AuthenticationHelper.auth_key_valid?(device_info, @device)
        BrowserStack.logger.error("#{DEVICE_ID_HEADER} sent but path #{request.path_info} is not whitelisted")
        halt(404)
      end

      params['session_start_events'] = {}
      if params[:live_session_id] && ['/start'].include?(request.path_info)
        @current_route = request.path_info.split('/')[1]
        params['session_start_events']["#{@current_route}_received_at_platform"] = Utils.get_epoch_time_diff(params[:start_time].to_i) unless params[:start_time].nil?
      end

      if (params[:live_session_id] || params[:app_live_session_id]) && ['/restart'].include?(request.path_info)
        params['session_restart_events'] = {}
        @current_route = request.path_info.split('/')[1]
        params['session_restart_events']["#{@current_route}_received_at_platform"] = Utils.get_epoch_time_diff(params[:start_time].to_i) unless params[:start_time].nil?
      end

      begin
        ip = File.read(@@server_config['ip_file']).strip
      rescue
        ip = nil
      end
      Zombie.configure
      @hooter = Hooter.new
      BrowserStack.logger.info(
        "New #{request.request_method} request for url: #{request.url} with params: #{Utils.redact_params(params, request)} | #{request.scheme} #{request.ip} #{request.port} #{request.path_info}"
      )
    end

    after do
      if params[:live_session_id] || params[:app_live_session_id]
        if ['/start'].include?(request.path_info) && params['session_start_events']
          params['session_start_events']["#{@current_route}_completed_at_platform"] = Utils.get_epoch_time_diff(params[:start_time].to_i) unless params[:start_time].nil?
          params['session_start_events'].merge!({ overall_platform_start: (params['session_start_events']['start_completed_at_platform'] - params['session_start_events']['start_received_at_platform']) })
          BrowserStack.logger.info "Live connect time buckets session_start_events: #{params['session_start_events']}"
          push_to_cls(params, 'live_connect_time_buckets', '', params['session_start_events'])
          params.delete('session_start_events')
        end

        if ['/app_start'].include?(request.path_info) && params['session_start_events']
          BrowserStack.logger.info "App Live connect time buckets session_start_events: #{params['session_start_events']}"
          push_to_cls(params, 'app_live_connect_time_buckets', '', params['session_start_events'])
          params.delete('session_start_events')
        end

        if ['/restart'].include?(request.path_info) && params['session_restart_events']
          params['session_restart_events']["#{@current_route}_completed_at_platform"] = Utils.get_epoch_time_diff(params[:start_time].to_i) unless params[:start_time].nil?
          params['session_restart_events'].merge!({ overall_platform_restart: (params['session_restart_events']['restart_completed_at_platform'] - params['session_restart_events']['restart_received_at_platform']) })
          BrowserStack.logger.info "Live connect time buckets session_restart_events: #{params['session_restart_events']}"
          push_to_cls(params, params[:live_session_id] ? 'live_connect_time_buckets' : 'app_live_connect_time_buckets', '', params['session_restart_events'])
          params.delete('session_restart_events')
        end
      end

      Thread.bs_run do
        if SECRET_SCANNING_FLAG['sinatra']
          regex_to_match = SECRET_SCANNING_FLAG['regex_to_match']
          regex_to_redact = SECRET_SCANNING_FLAG['regex_to_redact']
          EnvMiddleware.scan_request_for_secrets(request, regex_to_match, regex_to_redact, SECRET_SCANNING_WHITELISTED_REQUESTS)
        end
      end
    end

    DEVICE_FILTER_PATH.each do |path|
      before path do
        @device = params['device']
        @device_state = DeviceState.new(@device)
        Thread.current[:device_id] = @device
        halt 400 if @device.nil? || @device.empty?

        headers['Access-Control-Allow-Methods'] = 'GET, POST'
        headers['Access-Control-Allow-Origin'] = '*'
      end

      after path do
        if ["/geturl"].include?(path)
          device_info = @@devices_conf['devices'][@device.to_s]
          device_name = device_info.nil? ? '[device name not found]' : device_info['device_name']
          @hooter.send(device_name, path.sub(%r{/}, ''))
        end
      end
    end

    # rubocop:disable Style/IfUnlessModifier
    ENV_START_REQUEST_MIDDLEWARE_PATHS.each do |path|
      before path do
        status = EnvMiddleware.process_start_request(params['device'], request, path)
        if RAILS_ENV_RESTRICIONS_FLAG['enabled'] && status == :reject
          halt 409, "Request rejected due to Rails env restrictions"
        end
      end
    end

    ENV_NON_START_REQUEST_MIDDLEWARE_PATHS.each do |path|
      before path do
        status = EnvMiddleware.process_non_start_request(params['device'], request, path)
        if RAILS_ENV_RESTRICIONS_FLAG['enabled'] && status == :reject
          halt 409, "Request rejected due to Rails env restrictions"
        end
      end
    end
    # rubocop:enable Style/IfUnlessModifier

    START_REQUESTS_PATHS.each do |path|
      after path do
        device = params['device']
        device_state = DeviceState.new(@device)

        if response.status.to_s.start_with?('5')
          BrowserStack.logger.info("encountered a bm, response: #{response.inspect} ")
          device_state.touch_session_bm_file
        end
      end
    end

    register StaticConf

    get '/' do
      ":-)"
    end

    get '/devices' do
      DeviceManager.all_devices.to_json
    end

    get '/devices_connected' do
      DeviceManager.connected_devices.to_json
    end

    get '/git_info' do
      [200, ServerInfo.git_info.to_json]
    rescue => e
      BrowserStack.logger.error("Error fetching git details: #{e.message}")
      [500, { error: e.message }.to_json]
    end

    get '/stop' do
      params["stop_req_timestamp"] = Time.now.to_i

      component_breakdown = ""
      FileUtils.touch("/tmp/session_stop_#{@device}")
      component_start = Time.now
      MCSPT.stop_session_running_on_device_async(@device, cancelled: params[:genre] != "app_automate")
      component_time = (Time.now - component_start).round(2)
      BrowserStack.logger.info "mcspt_stop_session_async done in: #{component_time}"
      component_breakdown += "mcspt_stop_session_async:#{component_time},"

      send_app_live_url_to_eds(params) if params[:genre] == "app_live_testing"
      DeviceManager.stop(@device, params, component_breakdown)
      FileUtils.rm_f("/tmp/session_stop_#{@device}")
      status 200
    rescue Exception => e
      status 500
      BrowserStack.logger.error("Exception in /stop #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
      FileUtils.rm_f("/tmp/session_stop_#{@device}")
      e.message
    end

    get '/start' do
      DeviceManager.start(@device, params)
      status 200
    rescue Exception => e
      status 500
      BrowserStack.logger.error("Exception in /start #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
      push_to_cls(params, 'Exception in /start', e.message.to_s, { "device_id" => @device, "backtrace" => e.backtrace.join("\n")[0..2000], "error_message" => e.message.to_s })
      e.message
    end

    get '/restart' do
      return 400, {} if params["actions"].nil?

      DeviceManager.restart(@device, params)
      status 200
    rescue Exception => e
      status 500
      BrowserStack.logger.error("Exception in /restart #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
      push_to_cls(params, 'Exception in /restart', e.message.to_s, { "device_id" => @device, "backtrace" => e.backtrace.join("\n")[0..2000], "error_message" => e.message.to_s })
      e.message
    end

    post '/app_start' do
      # Incase no data is present in the POST request body
      return 400 if request.body.nil?

      request_body_str = request.body.read
      return 400 if request_body_str == ""

      request_body_json = JSON.parse(request_body_str)
      request_body_json_str = {}
      # This is done because in JSON.parse the integers/boolean/others are all parsed.
      # But in GET it used to be in string form. This is done to keep all in sync and tamper minimal.
      request_body_json.each do |key, value|
        request_body_json_str[key] = value.to_s
      end
      params.merge!(request_body_json_str)
      DeviceManager.start(@device, params)
      # Cancelling any running MCSPT Session on the device
      MCSPT.stop_session_running_on_device_async(@device, cancelled: true)
      status 200
    rescue Exception => e
      status 500
      BrowserStack.logger.error("Exception in /start #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
      push_to_cls(params, 'Exception in /app_start', e.message.to_s, { "device_id" => @device, "backtrace" => e.backtrace.join("\n")[0..2000], "error_message" => e.message.to_s })
      e.message
    end

    get '/app_start' do
      DeviceManager.start(@device, params)
      # Cancelling any running MCSPT Session on the device
      MCSPT.stop_session_running_on_device_async(@device, cancelled: true)
      status 200
    rescue Exception => e
      status 500
      BrowserStack.logger.error("Exception in /app_start #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
      push_to_cls(params, 'Exception in /app_start', e.message.to_s, { "device_id" => @device, "backtrace" => e.backtrace.join("\n")[0..2000], "error_message" => e.message.to_s })
      e.message
    end

    get '/update_app' do
      DeviceManager.update_app_on_device(@device, params)
      status 200
    rescue Exception => e
      status 500
      BrowserStack.logger.error("Exception in /update_app #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
      e.message
    end

    get '/geturl' do
      device = @device.to_s
      debugger_port = @@devices_conf["devices"][device]["debugger_port"] unless @@devices_conf["devices"][device].nil?
      url = DeviceManager.get_url(device, debugger_port)
      BrowserStack.logger.info("geturl got #{url} to respond for device #{device}")

      if !Utils.valid_live_url?(url) && !debugger_port.nil?
        @hooter.send(
          @@devices_conf["devices"][device]["device_name"], 'geturl_error', 'safari'
        )

        BrowserStack::Zombie.push_logs("ios_get_url_error", 'safari', {
          "device" => device,
          "genre" => params["genre"],
          "session_id" => params["live_session_id"] || "unknown"
        })
      end
      url
    end

    get '/cleanup' do # rubocop:todo Metrics/BlockLength
      BrowserStack.logger.info("Got Cleanup request for: #{@device}")
      is_minified_flow = false
      device_state = DeviceState.new(@device)
      backup_manager = CFGUtilBackupManager.new(@device)
      session_id = OSUtils.get_session_id(@device)
      FileUtils.touch(DeviceManager.cleanup_done_file(@device))
      # Cancelling any running MCSPT Session on the device
      MCSPT.stop_session_running_on_device_async(@device, cancelled: true)

      begin
        if File.exist?("/tmp/session_stop_#{@device}")
          BrowserStack.logger.info("Cleanup came for session #{session_id} while Session stop running for #{@device}")
          FileUtils.rm_f("/tmp/session_stop_#{@device}")
          BrowserStack::Zombie.push_logs("cleanup-session-stop-running", "cleanup triggered when session stop running", { "session_id" => session_id, "device" => @device })
        end
      rescue => e
        BrowserStack.logger.error("Error while checking stop file for device #{@device}: #{e.message}")
      end

      if DeviceManager.all_devices[@device].nil?
        DeviceManager.write_cleanup_failure_reason(@device, "", "Device not present in config")
        halt 200, "Device not found in config"
      end

      unless DeviceManager.device_setup_completed?(@device)
        DeviceManager.request_setup(@device)
        DeviceManager.request_cleanup(@device)
        device_check_offline_reason = begin
          CleanupHelpers.offline_reason(@device)
        rescue
          ''
        end
        error = "Device check has not finished setting up device! #{device_check_offline_reason}".strip
        DeviceManager.write_cleanup_failure_reason(@device, "", error)
        halt 200, "Halting cleanup as device check hasn't finished setting up device"
      end

      if OSUtils.is_process_running?('/usr/local/.browserstack/realmobile/server/cleanup.rb', @device)
        BrowserStack.logger.info("cleanup process is running, checking process elapsed time...")
        if OSUtils.process_elapsed_time('clean', @device) > MAIN_CLEANUP_TERMINATION_THRESHOLD
          BrowserStack.logger.info("process elapsed time is greater than #{MAIN_CLEANUP_TERMINATION_THRESHOLD}, kill cleanup process")
          OSUtils.kill_process('/usr/local/.browserstack/realmobile/server/cleanup.rb', @device)
          BrowserStack.logger.info("cleanup process killed successfully")
        end
      end

      if params[:check_and_clean].to_s == "true"
        process_running = OSUtils.is_process_running?('/usr/local/.browserstack/realmobile/server/cleanup.rb', @device)
        halt "already running" if process_running
      end

      if DeviceManager.setup_requested? @device
        device_check_offline_reason = begin
          CleanupHelpers.offline_reason(@device)
        rescue
          ''
        end
        error = "Device check needs to run before cleanup! #{device_check_offline_reason}".strip
        DeviceManager.write_cleanup_failure_reason(@device, "", error)
        halt 200, "Device needs setup"
      end

      begin
        webdriver_port = @@devices_conf["devices"][@device.to_s]["webdriver_port"]
        is_voiceover_enabled = File.exist?("#{@@server_config['state_files_dir']}/voiceover_used_#{@device}")
        device_state.touch_ios_voiceover_automation_running_file if is_voiceover_enabled
        stop_live_request_time = Benchmark.measure do
          if [LIVE_TESTING, APP_LIVE_TESTING].include?(params[:genre].to_s)
            resp = WdaClient.new(webdriver_port).stop_live_session({ "is_voiceover_enabled": is_voiceover_enabled })
            BrowserStack.logger.info("resp: #{resp} if: #{!resp['value'].nil? && !resp['value']['status'].nil? && resp['value']['status'] == 'failure'}")
            mark_replay_kit_still_running("wda returned failure") if !resp['value'].nil? && !resp['value']['status'].nil? && resp['value']['status'] == 'failure'

            # this is a safe check
            # in case it failed in /stop, trying in /cleanup
            if BrowserActivityMonitoring.running?(@device)
              File.delete(BrowserActivityMonitoring.start_file(@device))
              BrowserStack.logger.info("/cleanup BrowserActivityMonitoring removing start file for #{session_id}")
            end

            if SessionSavePolling.running?(@device)
              File.delete(SessionSavePolling.start_file(@device))
              BrowserStack.logger.info("[SessionSavePolling] /cleanup removing start file for #{session_id}")
            end
          end
        end
        BrowserStack.logger.info("Stop Live Request Took: #{stop_live_request_time.real * 1000} ms")
      rescue => e
        BrowserStack.logger.info("Tried stop_live_session endpoint, but failed:#{e.message} \n#{e.backtrace.join("\n")}")
      ensure
        device_state.remove_ios_voiceover_automation_running_file if is_voiceover_enabled
      end
      DeviceManager.check_and_stop_ios_watcher(@device, session_id, true)
      FileUtils.rm_f("#{STATE_FILES_DIR}/#{@device}_env")
      if params[:retry_cleanup].to_s == "true"
        # When rails sends cleanup after 30 minutes
        cleanup_count = begin
          File.read(DeviceManager.cleanup_done_file(@device)).to_i
        rescue
          0
        end
        if cleanup_count > 5
          BrowserStack.logger.info("Resetting cleanup count for #{@device} to 0")
          Utils.write_to_file(DeviceManager.cleanup_done_file(@device), "0")
          device_state.remove_minimized_cleanup_reserved_file
          device_state.remove_preserve_app_state_reserved_file
        end
      end

      if params[:skipEnterpriseDummyAutomation].to_s == "true"
        BrowserStack.logger.info("Will skip enterprise dummy automation in this step")
        device_state.touch_skip_enterprise_dummy_automation_file
      end

      if !params[:telephony_call_used].nil? && (params[:telephony_call_used].to_s == "true")
        BrowserStack.logger.info("telephony call was used")
        FileUtils.touch(BrowserStack::IPhone.clean_call_records_file(@device))
      end

      if !params[:telephony_sms_used].nil? && (params[:telephony_sms_used].to_s == "true")
        BrowserStack.logger.info("telephony sms was used")
        FileUtils.touch(BrowserStack::IPhone.clean_message_records_file(@device))
      end

      BrowserStack.logger.info("[#{session_id}] reserveDevice : #{params[:reserveDevice]} was sent")
      if params[:reserveDevice].to_s == "true" || params["reserveDevice"].to_s == "true"
        is_minified_flow = true
        device_state.touch_minimized_cleanup_reserved_file
      else
        device_state.remove_minimized_cleanup_reserved_file
        device_state.remove_preserve_app_state_reserved_file
      end
      device_state.remove_app_a11y_app_details_file if device_state.app_a11y_app_details_file_present?
      device_state.remove_app_a11y_deduplication_hash_file if device_state.app_a11y_deduplication_hash_file_present?
      mdm_cleanup_manager = FullCleanup::MDMCleanupManager.new(@device, BrowserStack.logger)
      should_run_full_cleanup = mdm_cleanup_manager.run_full_cleanup? && mdm_cleanup_manager.device_eligible?
      device_state.touch_mdm_full_cleanup_file if should_run_full_cleanup
      device_state.touch_first_cleanup_file if !should_run_full_cleanup && !device_state.mdm_full_cleanup_file_present? && DeviceManager.first_cleanup?(@device)
      if device_state.mdm_full_cleanup_file_present?
        cleanup_type = "full_cleanup"
        BrowserStack.logger.info("Full Cleanup is requested")
      elsif device_state.first_cleanup_file_present?
        cleanup_type = "first_cleanup"
        BrowserStack.logger.info("First Cleanup is requested")
      else
        cleanup_type = "quick_cleanup"
        BrowserStack.logger.info("Quick Cleanup is requested")
      end

      self_cleanup = params[:self_cleanup].to_s == "true"
      cmd = "/Users/<USER>'user']}/.rvm/rubies/ruby-2.7.2/bin/ruby #{CLEANUP_SCRIPT} #{@device} #{cleanup_type}"
      cmd += " #{Time.now.to_i}" if is_minified_flow

      on_complete_complete = proc do |_stdout, _stderr, status, args|
        unless status.success?
          BrowserStack::Zombie.push_logs("cleanup-error", args[:device])
          BrowserStack::IosInfluxdbClient.instance.event(@device, 'cleanup-error', component: 'server', subcomponent: 'cleanup', is_error: true)
        end
      end

      manual_cleanup = params.key?("manual_cleanup") ? params[:manual_cleanup] : false
      if manual_cleanup
        @device_state.touch_manual_cleanup_file
        BrowserStack.logger.info("Manual cleanup triggered for device on mini")
      end
      if !self_cleanup && !OSUtils.execute("ps -ef | grep cleanup.rb | grep #{@device} | grep -v grep").strip.split("\n").empty?
        BrowserStack.logger.info("One cleanup process for #{@device} is already running. Skipping this request.")
      elsif is_minified_flow
        if device_state.minimized_cleanup_unreserved_file_present?
          BrowserStack.logger.error "Either wda-not-signed-start-error or sim-card-popup-njb. Check zombie. Aborting reserve device flow"
          device_state.remove_minimized_cleanup_unreserved_file
          device_state.remove_minimized_cleanup_reserved_file
          device_state.remove_preserve_app_state_reserved_file
          halt 500
        end
        begin
          timeout = params[:genre].to_s == GENRE_APP_AUTOMATE ? AA_MINIFIED_CLEANUP_MAX_THRESHOLD : AUT_MINIFIED_CLEANUP_MAX_THRESHOLD
          Timeout.timeout(timeout) do
            minified_cleanup_start_time = Time.now
            output, status = OSUtils.execute(cmd, true)
            is_minimized_flow = device_state.minimized_cleanup_reserved_file_present?
            BrowserStack.logger.info "[#{session_id}] Minified cleanup completed with status : #{status},is_minimized_flow: #{is_minimized_flow} MINIFIED CLEAN UP TIME : #{Time.now - minified_cleanup_start_time}"
            if !is_minimized_flow || (status && status.to_s != '0')
              device_state.remove_minimized_cleanup_reserved_file
              device_state.remove_preserve_app_state_reserved_file
              BrowserStack.logger.error "[#{session_id}] Deleting minimized_cleanup_reserved_file as Minified cleanup completed with status : #{status}, minified_cleanup_failure : Non zero exit status"
              BrowserStack::Zombie.push_logs("minified_cleanup_failure", "Non zero exit status", { "session_id" => session_id, "device" => @device })
              halt 500
            end
          end
        rescue => e
          BrowserStack::Zombie.push_logs("minified_cleanup_failure", e.message.to_s, { "session_id" => session_id, "device" => @device })
          BrowserStack.logger.error "[#{session_id}] Minified cleanup error; message : #{e.message} backtrace : #{e.backtrace}"
          device_state.remove_minimized_cleanup_reserved_file
          device_state.remove_preserve_app_state_reserved_file
          halt 500
        end
      else
        Utils.fork_process(cmd, {
          process_name: "puma: cluster worker cleanup #{@device}",
          callback: {
            block: on_complete_complete,
            args: {
              device: @device
            }
          }
        })
      end
      status 200
    end

    get '/cleanup_done' do
      status 200
      if DeviceManager.cleanup_done?(@device)
        "true"
      else
        "false"
      end
    end

    get '/start_cspt_session' do
      code, response = MCSPT.start_session_async(
        params[:session_id].to_s,
        params[:user_id].to_s,
        params[:device_id].to_s,
        params[:app_package].to_s,
        params[:app_relaunch].to_s.downcase == "true",
        region: params[:region].to_s,
        app_startup_time_flag: params[:app_startup_time_flag].to_s.downcase == "true"
      )
      BrowserStack.logger.info("Started CSPT session")
      return [code, response.to_json]
    end

    get '/stop_cspt_session' do
      code, response = MCSPT.stop_session_async(
        params[:session_id].to_s,
        params[:user_id].to_s,
        params[:device_id].to_s,
        cancelled: params[:cancelled].to_s.downcase == "true"
      )
      BrowserStack.logger.info("Stopped CSPT session")
      return [code, response.to_json]
    end

    get '/get_cspt_analytics' do
      data = MCSPT.get_analytics(
        params[:session_id].to_s,
        params[:device_id].to_s,
        params[:app_package].to_s
      )
      return [200, data.to_json]
    end

    post '/set_cleanup_policy' do # rubocop:todo Metrics/BlockLength
      begin
        data = JSON.parse(request.body.read)
      rescue
        return 400, { error: "Please provide a valid json" }.to_json
      end

      cleanup_type = data["cleanup_type"]

      return 400, { error: "Invalid cleanup_type: #{cleanup_type}" }.to_json unless ["public", "dedicated"].include?(cleanup_type)
      return 400, { error: "Invalid device: #{@device}" }.to_json if @device.nil? || @device.empty?
      return 400, { error: "Please provide dedicated_cleanup_config in input json" }.to_json if cleanup_type == "dedicated" && !data.key?("dedicated_cleanup_config")

      device_state = DeviceState.new(@device)
      if data.key?("moved_to_public") && data["moved_to_public"] == "true" && device_state.dedicated_device_file_present?
        BrowserStack.logger.info "Deleting dedicated device file for - #{@device}"
        device_state.remove_dedicated_device_file

        BrowserStack.logger.info "Removing restrictions profile for - #{@device}"
        configuration_profiles_manager = ConfigurationProfilesManager.new(@device, BrowserStack.logger)
        configuration_profiles_manager.remove_profile(:restrictions, nil, remove_via: :automatic)

        BrowserStack.logger.info "Deleting Extra access state files for - #{@device}"
        EXTRA_ACCESS_RESTRICTION_KEYS.each do |feature|
          device_state.send("remove_#{feature}_enabled_file")
        end
      end

      BrowserStack.logger.info "Setting cleanup policy to #{cleanup_type} for #{@device}"
      cleanup_configuration = cleanup_type == "dedicated" ? data["dedicated_cleanup_config"] : ""
      case cleanup_type
      when "public"
        FileUtils.rm_f("#{STATE_FILES_DIR}/#{@device}_installed_apps_data")
        device_state.remove_dedicated_cleanup_file
        device_state.remove_dedicated_video_state_file
      when "dedicated"
        device_state.write_to_dedicated_cleanup_file(cleanup_configuration)
      end

      device_state.touch_force_install_mdm_profiles_file

      return 200, {}.to_json
    end

    def execute_fire_cmd(params) # rubocop:todo Metrics/MethodLength, Metrics/AbcSize
      params[:event_hash] = {
        absolute_start_time: (Time.now.to_f * 1000).to_i
      }
      is_app_testing = params[:genre].to_s.eql?('app_automate')
      begin
        Utils.mark_event_start('firecmd_time', params[:event_hash])
        start_output = DeviceManager.fire_cmd_start(@device, params).to_json
        BrowserStack.logger.info("Start output for: #{@device} with params, #{params}: #{start_output}")
        start_output
      rescue FireCMDException => e
        BrowserStack.logger.error("FireCMDException Exception in /selenium_command #{@device} #{params['automate_session_id']}: #{e.message} \n#{e.backtrace.join("\n")}")
        { error: e.message.to_s, type: e.type.to_s, kind: e.kind, meta_data: e.meta_data }.to_json
      rescue => e
        BrowserStack.logger.error("Exception in /selenium_command #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
        { error: "Got Error in selenium_command #{e.message}" }.to_json
      ensure
        if is_app_testing
          # bs_run creates a new thread & copies logging params to it
          Thread.bs_run do
            output = OSUtils.get_system_resources
            BrowserStack.logger.info("system_resources : #{output}")
            push_to_cls( params, 'system_resources', '', output )
          end
        end
        Utils.mark_event_start('archive_and_truncate_appium_logs', params[:event_hash])
        current_device = @@devices_conf['devices'][@device]
        begin
          DeviceManager.archive_and_truncate_appium_logs(current_device['selenium_port'])
        rescue
          nil
        end
        Utils.mark_event_end('archive_and_truncate_appium_logs', params[:event_hash])

        Utils.mark_event_start('fire_cmd.start_mcspt_session', params[:event_hash])
        if is_app_testing && params["appProfiling"].to_s == "true"
          code, response = MCSPT.start_session_async(
            params['automate_session_id'].to_s, params[:user_id].to_s, @device,
            params['app_testing_bundle_id'].to_s, false, app_automate: true
          )
          BrowserStack.logger.info("Started CSPT session")
        end
        Utils.mark_event_end('fire_cmd.start_mcspt_session', params[:event_hash])

        appium_version = nil
        if is_app_testing
          Utils.mark_event_end('firecmd_time', params[:event_hash])
          appium_version = DeviceManager.check_and_set_appium_version(params)
          value = appium_version == @@server_config['default_appium_version'].to_s ? "appium-default-#{appium_version}" : "appium-non-default-#{appium_version}"
          Utils.send_to_eds(params, EdsConstants::APP_AUTOMATE_TEST_SESSIONS, false, "framework_version", value, true)
          Utils.send_to_eds(params, EdsConstants::APP_AUTOMATE_TEST_SESSIONS, false, "tertiary_params", { "is_canary" => canary? }, true)
        else
          Utils.mark_event_end('total', params[:event_hash])
          Utils.send_to_eds(params, EdsConstants::AUTOMATE_SESSION_TIME_COMPONENTS)
        end

        Utils.send_appium_version_to_zombie(params, @device, appium_version)
      end
    end

    get '/selenium_command' do
      BrowserStack.logger.info("Received GET selenium_command request with params: #{params}")
      execute_fire_cmd(params)
    end

    post '/selenium_command' do
      # Incase no data is present in the POST request body
      return 400 if request.body.nil?

      request_body_str = request.body.read
      return 400 if request_body_str == ""

      request_body_json = JSON.parse(request_body_str)
      request_body_json_str = {}
      # This is done because in JSON.parse the integers/boolean/others are all parsed.
      # But in GET it used to be in string form. This is done to keep all in sync and tamper minimal.
      request_body_json.each do |key, value|
        request_body_json_str[key] = value.to_s
      end
      params.merge!(request_body_json_str)

      BrowserStack.logger.info("Received POST selenium_command request with params: #{params}")
      execute_fire_cmd(params)
    end

    get '/snapshot_hub' do
      file = params[:file]
      bucket = params[:bucket]
      session_id = folder = params[:folder]
      orientation = params[:orientation]
      key_id = params[:key]
      secret_key = params[:secret]
      check_black_screenshot = (params[:instrumentBlackScreenshot].to_s.downcase == "true")

      return 400 if file.nil? || file.empty? || bucket.nil? || bucket.empty? || folder.nil? || folder.empty?

      file_name = "https://s3.amazonaws.com/#{bucket}/#{folder}/#{file}.jpeg"

      begin
        pid = Process.fork do
          Process.setproctitle("puma: cluster worker snapshot_hub #{@device}:#{session_id}")
          DeviceManager.get_screenshot(@device, session_id, orientation, file_name, key_id, secret_key, true, check_black_screenshot)
          Process.exit
        end
        Process.detach pid
        BrowserStack.logger.info("Snapshot started for device: #{@device}")
        "done"
      rescue => e
        status 500
        BrowserStack.logger.error("Exception in /snapshot_hub #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
        e.message
      end
    end

    get '/pacfile' do
      device_pacfile = File.join(@@pacfile_dir, "pacfile_#{@device}")
      if File.exists?(device_pacfile)
        BrowserStack.logger.info("Serving pacfile for device: #{@device}")
        status 200
        send_file device_pacfile
      else
        file = File.join(@@templates_dir, "static_pac.pac")
        status 200
        BrowserStack.logger.error("Default pacfile is set")
        send_file file
      end
    end

    get '/reset_auth_keys' do
      # This is called via railsApp post cleanup to send
      # webrtc_session_id ( vnc_key ) and peer server url
      # Which the mobile connected to before hand in order to reduce the
      # webrtc handshake time

      connect_to_peer_stat = Benchmark.measure do
        DeviceManager.connect_to_peer(@device, params)
      end
      BrowserStack.logger.info("Connect to peer took #{connect_to_peer_stat.real * 1000} ms")
      status 200
    rescue => e
      status 500
      BrowserStack.logger.error("Exception in /reset_auth_keys #{@device}: #{e.message}\n" + e.backtrace.join("\n"))
      e.message
    end

    get '/remove_device' do
      device_check_running = system("ps aux | grep -v grep | grep -v sudo | grep -c device_check.rb")
      halt "Device check is running" if device_check_running
      DeviceManager.remove_device(@device)
      "done"
    end

    get '/snapshot' do
      orientation = params[:orientation]
      begin
        use_wda = false
        image_type = "image/png"
        if params[:cheap_stream] == "true"
          image_type = "image/jpeg"
          current_device_config = DeviceManager.device_configuration_check(@device)
          wda_port = current_device_config["webdriver_port"].to_i
          use_wda = WdaClient.new(wda_port).cached_running?
        end
        img_data = DeviceManager.get_screenshot_uncached(@device, orientation, use_wda, params[:cheap_stream])
        # img_data is in TIFF format, so set the content-type to image/png to render inline
        content_type image_type
        headers['Refresh'] = '0.1' if params[:cheap_stream]
        img_data
      rescue => e
        status 500
        headers['Refresh'] = '1' if params[:cheap_stream] # Refresh automatically in 1s when we get exceptions amidst cleanup
        BrowserStack.logger.error(
          "Exception in /snapshot #{@device}: #{e.message} \n#{e.backtrace.join("\n")}"
        )
        e.message
      end
    end

    get '/logs' do
      type = params["type"] || "cleanup"
      lines = params["lines"] || 100
      lines = 5000 if lines.to_i > 5000

      case type
      when "realmobile"
        cmd = "tail -n #{lines} #{@@server_config['logging_root']}/realmobile_plist.log"
      when "cleanup"
        cmd = "tail -n #{lines} #{@@server_config['logging_root']}/cleanup_#{@device}.log"
      end
      `#{cmd}`.gsub(/(?:\n\r?|\r\n?)/, '<br>')
    end

    get '/device_logs' do
      <add static dirc file>
      session_id = params['session_id']
      device = params['device']
      return 400, { error: 'Missing required parameter: session_id' }.to_json if session_id.nil? || session_id.empty?
      return 400, { error: 'Missing required parameter: device' }.to_json if device.nil? || device.empty?

      log_file_path = "/var/log/browserstack/app_log_#{device}.log"
      start_pos = [params['start_pos'].to_i, 0].max
      num_lines = 10000

      begin
        unless File.exist?(log_file_path)
          BrowserStack.logger.info("Device log file not found: #{log_file_path}")
          return 200, { meta: { start_pos: start_pos, end_pos: start_pos }, value: [] }.to_json
        end
        file_size = File.size(log_file_path)
        if start_pos >= file_size
          BrowserStack.logger.info("Start position #{start_pos} is beyond file size #{file_size}")
          return 200, { meta: { start_pos: start_pos, end_pos: start_pos }, value: [] }.to_json
        end
        timezone_offset = IdeviceUtils.ideviceinfo(device, "TimeZoneOffsetFromUTC")[0].to_s.to_f.to_i
        result = read_device_logs(log_file_path, start_pos, num_lines, timezone_offset)

        BrowserStack.logger.info("actual_start_pos: #{result[:actual_start_pos]}, actual_end_pos: #{result[:actual_end_pos]}")
        return 200, { meta: { start_pos: result[:actual_start_pos], end_pos: result[:actual_end_pos] }, value: result[:logs] }.to_json
      rescue => e
        BrowserStack.logger.error("Exception in /device_logs for device #{device}: #{e.message}\n#{e.backtrace.join("\n")}")
        return 500, { error: "Error retrieving device logs: #{e.message}" }.to_json
      end
    end

    # Helper method to read device logs from file and parse timestamps
    def read_device_logs(log_file_path, start_pos, num_lines, timezone_offset)
      logs = []
      actual_start_pos = start_pos
      actual_end_pos = start_pos
      lines_read = 0

      File.open(log_file_path, 'r') do |file|
        file.seek(start_pos)
        actual_start_pos = file.pos
        while lines_read < num_lines && (line = file.gets)
          if (time_match = line.match(XCTEST_DEVICE_LOG_TIME_MATCH_REGEX))
            time_str = time_match[1]
            begin
              parsed_time_with_offset = Time.parse("#{Time.now.year} #{time_str}") - timezone_offset
              timestamp_ms = (parsed_time_with_offset.to_f * 1000).to_i
            rescue => e
              timestamp_ms = (Time.now.to_f * 1000).to_i  # Fallback to current time
            end
            log_entry = {
              timestamp: timestamp_ms,
              level: "ALL",
              message: line.strip
            }
            logs << log_entry
          else
            log_entry = {
              timestamp: (Time.now.to_f * 1000).to_i,
              level: "ALL",
              message: line.strip
            }
            logs << log_entry
          end
          lines_read += 1
          actual_end_pos = file.pos
        end
      end

      {
        logs: logs,
        actual_start_pos: actual_start_pos,
        actual_end_pos: actual_end_pos
      }
    end

    get '/machine_data' do
      static_conf = begin
        JSON.parse(File.read(@@static_conf_file))
      rescue
        {}
      end
      {
        branch: `cd /usr/local/.browserstack/realmobile; git branch | grep \\* | awk '{print $NF}';`.strip,
        rails_endpoint: static_conf['rails_endpoint'].sub(%r{^https?://}, '')
      }.to_json
    end

    get '/cleanup_failure_reason' do
      DeviceManager.get_cleanup_failure_reason(@device)
    end

    #TODO: change this to /set_gpsLocation
    get '/set_geolocation' do
      return 500, "param missing" if (params["latitude"].nil? || params["latitude"].empty?) || (params["longitude"].nil? || params["longitude"].empty?)

      session_params = begin
        Utils.read_json_file("#{@@server_config['state_files_dir']}/#{params['device']}_session")
      rescue
        ""
      end
      if session_params != ""
        push_to_cls(session_params, 'location_change', '', { "location_change" => "changed" })
        # eds
        if session_params["genre"] == "app_live_testing"
          Utils.send_to_eds({
            session_id: session_params["app_live_session_id"],
            product: {
              location_change_count: 1
            }
          }, EdsConstants::APP_LIVE_TEST_SESSIONS, true)
        end
      end
      DeviceManager.set_gpslocation(@device, params)
    end

    #TODO: Remove this endpoint after railsApp is deployed
    get '/set_ios_settings' do # rubocop:todo Metrics/BlockLength
      session_id = params['app_live_session_id'] || params['live_session_id']
      BrowserStack.logger.info("Setting ios_setting: #{params['ios_setting']} for #{@device}, called by product #{params['product']}, for session #{session_id}")
      begin
        ip = File.read(@@server_config['ip_file']).strip
      rescue
        ip = nil
      end
      setting = params['ios_setting']
      pid = Process.fork do
        case setting
        when "timezone"
          DeviceManager.set_timezone(@@devices_conf["devices"][@device.to_s], @device, params)
        when "change_mode"
          DeviceManager.change_mode(@@devices_conf["devices"][@device.to_s], @device, params)
        when "assistive_touch"
          DeviceManager.toggle_assistive_touch(@@devices_conf["devices"][@device.to_s], @device, params)
        when "device_passcode"
          DeviceManager.toggle_passcode(@device, params)
        when "offline_mode"
          DeviceManager.toggle_offline_mode(ip, @@devices_conf["devices"][@device.to_s], @device, params)
        when "low_power_mode"
          DeviceManager.toggle_low_power_mode(@@devices_conf["devices"][@device.to_s], @device, params)
        when "prevent_cross_site_tracking"
          DeviceManager.switch_prevent_cross_site_tracking(@@devices_conf["devices"][@device.to_s], @device, params)
        when "accessibility_setting_display_and_textsize", "accessibility_setting_voice_over", "accessibility_setting_motion"
          DeviceManager.update_accessibility_settings(@device, params)
        when "safari_settings"
          DeviceManager.safari_settings(@@devices_conf["devices"][@device.to_s], @device, params)
        end
      end
      Process.detach(pid)
      return 200, {}.to_json
    rescue => e
      BrowserStack.logger.error("Exception in set_ios_settings #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
      return 500, { error: "Error in set_ios_settings #{e.message}" }.to_json
    end

    post '/set_ios_settings' do # rubocop:todo Metrics/BlockLength
      json_data = request.body.read
      data = json_data.nil? || json_data.empty? ? '{}' : json_data
      params = JSON.parse(data)

      session_id = params['app_live_session_id'] || params['live_session_id'] || params['app_automate_session_id']
      product = params['product']
      BrowserStack.logger.info("Setting ios_setting: #{params['ios_setting']} for #{@device}, called by product #{params['product']}, for session #{session_id} with params #{params}")
      ip = Utils.ip(@@server_config)
      setting = params['ios_setting']

      # In 12HourTime we are using idevice gem for changing device time format programatically,
      # idevice gem internally calls Objective C functions, turns out since MacOS High Seirra
      # we cannot call these methods from forked processes, due to which even though function was
      # working fine, child status was returned as SIGABRT ( signal 6 ), this is well known problem
      # and is documented: https://blog.phusion.nl/2017/10/13/why-ruby-app-servers-break-on-macos-high-sierra-and-what-can-be-done-about-it/
      # As a workaround, we can set OBJC_DISABLE_INITIALIZE_FORK_SAFETY env variable to YES, but that requires blocking deploy
      # For now, have added additional condition for 12HourTime to be called directly, instead from forked process. This anyways does not
      # make much difference for App Automate product from functionality persepctive, instead saves extra forking of process.
      begin
        if product == "app_automate"
          case setting
          when "date_time"
            date_time_settings = params['date_time']
            begin
              DeviceManager.date_time_setting(@device, params)
              return 200, {}.to_json
            rescue DateTimeException => e
              BrowserStack.logger.error("Error setting date_time: #{e.message}")
              return 500, { error: "Error in setting #{setting}", error_code: e.code }.to_json
            rescue => e
              BrowserStack.logger.error("Error setting date_time: #{e.message}")
              return 500, { error: "Error in setting #{setting}" }.to_json
            end
          end
        end
      rescue
        # Ignore error
      end

      pid = Process.fork do
        case setting
        when "timezone"
          DeviceManager.set_timezone(@@devices_conf["devices"][@device.to_s], @device, params)
        when "change_mode"
          DeviceManager.change_mode(@@devices_conf["devices"][@device.to_s], @device, params)
        when "assistive_touch"
          DeviceManager.toggle_assistive_touch(@@devices_conf["devices"][@device.to_s], @device, params)
        when "device_passcode"
          DeviceManager.toggle_passcode(@device, params)
        when "offline_mode"
          DeviceManager.toggle_offline_mode(ip, @@devices_conf["devices"][@device.to_s], @device, params)
        when "date_time"
          DeviceManager.date_time_setting(@device, params)
        when "prevent_cross_site_tracking"
          DeviceManager.switch_prevent_cross_site_tracking(@@devices_conf["devices"][@device.to_s], @device, params)
        when "device_name"
          DeviceManager.change_device_name(@device, params)
        when "safari_settings"
          DeviceManager.safari_settings(@@devices_conf["devices"][@device.to_s], @device, params)
        when "low_power_mode"
          DeviceManager.toggle_low_power_mode(@@devices_conf["devices"][@device.to_s], @device, params)
        when "accessibility_setting_display_and_textsize", "accessibility_setting_voice_over", "accessibility_setting_motion"
          DeviceManager.update_accessibility_settings(@device, params)
        when "location_services"
          DeviceSettingsUtil.handle_device_setting(@device, 'location_services', params['value'], params['app_display_name'], session_id)
        when "add_card"
          Secure::ApplePay.setup_wallet_and_assistive_touch(@device, params)
        end
      end
      if product == "app_automate" || params["app_live_session_id"].eql?("qa-no-session")
        Process.wait(pid)
        if $CHILD_STATUS.success?
          return 200, {}.to_json
        else
          BrowserStack.logger.error("Error from child process, status: #{$CHILD_STATUS}")
          return 500, { error: "Error in setting #{setting}" }.to_json
        end
      else
        Process.detach(pid)
        return 200, {}.to_json
      end

    rescue => e
      BrowserStack.logger.error("Exception in set_ios_settings #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
      return 500, { error: "Error in set_ios_settings #{e.message}" }.to_json
    end

    post '/biometric_user_option' do
      json_data = request.body.read
      data = json_data.nil? || json_data.empty? ? '{}' : json_data
      params = JSON.parse(data)

      product = params['product']
      session_id = params["#{product}_session_id"] || ""
      BrowserStack.logger.info("biometric_user_option  #{params['biometric_user_option']} for #{@device}, called by product #{product}, session_id : #{session_id} params #{params}")
      biometric_user_option = params['biometric_user_option'].to_s
      session_params = Utils.read_json_file("#{@@server_config['state_files_dir']}/#{@device}_session")
      session_params[BIOMETRIC_USER_OPTION] = biometric_user_option
      Utils.write_to_file("#{@@server_config['state_files_dir']}/#{@device}_session", session_params.to_json)
      return 200, {}.to_json
    rescue => e
      BrowserStack.logger.error("Exception in biometric_user_option #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
      return 500, { error: "Error in fetching biometric_user_option #{e.message}" }.to_json
    end

    get '/get_ios_settings_state' do
      BrowserStack.logger.info("Getting ios_setting states: for #{@device}")
      resp = DeviceManager.get_ios_states(@device)
      resp.merge!(DeviceManager.get_accessibility_states(@device, params)) if params["product"] == "live_testing" || params["product"] == "app_live_testing"
      return 200, resp.to_json
    end

    get '/setup_voiceover' do # rubocop:todo Metrics/BlockLength
      BrowserStack.logger.info("[VOICEOVER] /setup_voiceover begin")
      # setting these otherwise data is not pushed to cls
      params["genre"] = params["product"]
      params["genre"] == "live_testing" ? params["live_session_id"] = params["session_id"] : params["app_live_session_id"] = params["session_id"]

      is_successful = true
      t0 = Time.now.to_f
      @device = params['device']
      BrowserStack.logger.info("Setup VoiceOver : for #{@device}")
      device_state = DeviceState.new(@device)
      device_state.touch_ios_voiceover_automation_running_file
      voiceover_helper = BrowserStack::VoiceoverHelper.new
      params['miniBluetoothName'] = voiceover_helper.setup(@device, params["session_id"])
      t1 = Time.now.to_f

      # Pass it to DeviceManager
      response = DeviceManager.setup_voiceover(@@devices_conf["devices"][@device.to_s], @device, params)
      deviceBluetoothAddress = response["value"]["deviceBluetoothAddress"]
      deviceBluetoothAddress = deviceBluetoothAddress.downcase.gsub!(':', '-')
      t2 = Time.now.to_f

      if voiceover_helper.device_disconnected_after_voiceover_setup?(deviceBluetoothAddress)
        BrowserStack.logger.info("Failed to connect #{@device} to mini via bluetooth...tearing down voiceover")
        DeviceManager.teardown_voiceover(@@devices_conf["devices"][@device.to_s], @device, params)
        is_successful = false
      end

      # all time in ms
      setup_voiceover_hash = {
        is_successful: is_successful,
        overall_setup_voiceover: ((t2 - t0) * 1000.0).to_i,
        setup_voiceover_time_split: {
          devicemanager_setup_voiceover: {
            overall_devicemanager_setup_voiceover: ((t2 - t1) * 1000.0).to_i,
            devicemanager_setup_voiceover_time_split: response["value"]["devicemanager_setup_voiceover_time_split"]
          }
        }
      }
      BrowserStack.logger.info("[VOICEOVER] /setup_voiceover #{setup_voiceover_hash}")
      push_to_cls(params, 'setup_voiceover', '', setup_voiceover_hash)
      BrowserStack.logger.info("[VOICEOVER] /setup_voiceover end")
      if is_successful
        return 200, {}.to_json
      else
        return 500, {}.to_json
      end
    rescue => e
      BrowserStack.logger.error("[VOICEOVER] /setup_voiceover Exception in /setup_voiceover #{@device}: #{e.message}\n" + e.backtrace.join("\n"))
      setup_voiceover_hash = {
        is_successful: false,
        exception_message: e.message,
        exception_backtrace: e.backtrace.join("\n")
      }
      push_to_cls(params, 'setup_voiceover', '', setup_voiceover_hash)
      return 500, {}.to_json
    ensure
      device_state.remove_ios_voiceover_automation_running_file
    end

    get '/teardown_voiceover' do
      BrowserStack.logger.info("[VOICEOVER] /teardown_voiceover begin")
      t0 = Time.now.to_f
      @device = params['device']
      BrowserStack.logger.info("Teardown VoiceOver : for #{@device}")
      device_state = DeviceState.new(@device)
      device_state.touch_ios_voiceover_automation_running_file
      DeviceManager.teardown_voiceover(@@devices_conf["devices"][@device.to_s], @device, params)
      t1 = Time.now.to_f

      # all time in ms
      teardown_voiceover_hash = {
        is_successful: true,
        overall_teardown_voiceover: ((t1 - t0) * 1000.0).to_i
      }
      BrowserStack.logger.info("[VOICEOVER] /teardown_voiceover #{teardown_voiceover_hash}")

      # setting these otherwise data is not pushed to cls
      params["genre"] = params["product"]
      params["genre"] == "live_testing" ? params["live_session_id"] = params["session_id"] : params["app_live_session_id"] = params["session_id"]
      push_to_cls(params, 'teardown_voiceover', '', teardown_voiceover_hash)
      BrowserStack.logger.info("[VOICEOVER] /teardown_voiceover end")
      # update response in case of failure
      return 200, {}.to_json
    rescue => e
      BrowserStack.logger.error("[VOICEOVER] /teardown_voiceover Exception in /teardown_voiceover #{@device}: #{e.message}\n" + e.backtrace.join("\n"))
      teardown_voiceover_hash = {
        is_successful: false,
        exception_message: e.message,
        exception_backtrace: e.backtrace.join("\n")
      }
      push_to_cls(params, 'teardown_voiceover', '', teardown_voiceover_hash)
      return 500, {}.to_json
    ensure
      device_state.remove_ios_voiceover_automation_running_file
    end

    # This API gets called on the voiceover bluetooth servers
    # TODO: Add additional checks to verify if this is actually a bluetooth server
    get '/voiceover_init' do
      session_id = params[:session_id]
      device_id = params[:device_id]
      BrowserStack.logger.info("[VOICEOVER] /voiceover_init begin with session_id: #{session_id}, device_id: #{device_id}")
      voiceover_helper = BrowserStack::VoiceoverHelper.new
      mini_bluetooth_name = voiceover_helper.start_bluetooth_and_interaction_server
      BrowserStack.logger.info("[VOICEOVER] /voiceover_init end")
      return 200, { mini_bluetooth_name: mini_bluetooth_name }.to_json
    rescue => e
      BrowserStack.logger.error("[VOICEOVER] Exception in /voiceover_init : #{e.message}\n" + e.backtrace.join("\n"))
      return 500, { error: "Error in /voiceover_init #{e.message}" }.to_json
    end

    # This API gets called on the voiceover bluetooth servers
    # Used to check if a given device address is connected via bluetooth
    get '/voiceover_bluetooth_connection_status' do
      BrowserStack.logger.info("[VOICEOVER] /voiceover_bluetooth_connection_status begin")
      address = params[:address]
      blueutil_helper = BrowserStack::BlueutilHelper.new
      is_disconnected = blueutil_helper.device_address_disconnected?(address)
      BrowserStack.logger.info("[VOICEOVER] /bigsur_device_address_disconnected end")
      return 200, { disconnected: is_disconnected }.to_json
    rescue => e
      BrowserStack.logger.error("[VOICEOVER] Exception in /voiceover_bluetooth_connection_status: #{e.message}\n" + e.backtrace.join("\n"))
      return 500, { error: "Error in voiceover_bluetooth_connection_status #{e.message}" }.to_json
    end

    # This API gets called on the voiceover bluetooth servers
    # It creates a file in STATE_FILES_DIR
    # This is used in cleanup for triggering disable voiceover UI automation and also doing voiceover_cleanup
    get '/voiceover_mark_device_used' do
      BrowserStack.logger.info("[VOICEOVER] /voiceover_mark_device_used begin")
      device_id = params[:device_id]
      device_state = DeviceState.new(device_id)
      device_state.touch_voiceover_used_file
      voiceover_helper = BrowserStack::VoiceoverHelper.new
      voiceover_helper.write_session_params_to_voiceover_used_file(device_id, params)
      BrowserStack.logger.info("[VOICEOVER] /voiceover_mark_device_used end")
      return 200, { success: "true" }.to_json
    rescue => e
      BrowserStack.logger.error("[VOICEOVER] Exception in /voiceover_mark_device_used: #{e.message}\n" + e.backtrace.join("\n"))
      return 500, { error: "Error in voiceover_mark_device_used #{e.message}" }.to_json
    end

    # This API gets called on the voiceover bluetooth servers
    # Used for performing voiceover cleanup ( disable mini bluetooth and kill interaction server) if no sessions are running
    get '/voiceover_cleanup' do
      BrowserStack.logger.info("[VOICEOVER] /voiceover_cleanup begin")
      device_id = params[:device_id]
      voiceover_helper = BrowserStack::VoiceoverHelper.new
      voiceover_helper.cleanup(device_id)
      BrowserStack.logger.info("[VOICEOVER] /voiceover_cleanup end")
      return 200, { success: "true" }.to_json
    rescue => e
      BrowserStack.logger.error("[VOICEOVER] Exception in /voiceover_cleanup: #{e.message}\n" + e.backtrace.join("\n"))
      return 500, { error: "Error in voiceover_cleanup #{e.message}" }.to_json
    end

    post '/voiceover_reconnect_bluetooth' do # rubocop:todo Metrics/BlockLength
      BrowserStack.logger.info("[VOICEOVER] /voiceover_reconnect_bluetooth begin")
      request_body = request.body.read
      data = request_body.nil? || request_body.empty? ? '{}' : request_body
      data = JSON.parse(data)
      udid = data['udid']
      return unless udid

      @device = udid
      device_config = @@devices_conf["devices"][@device.to_s]
      device_version = device_config["device_version"].to_f
      # Do not trigger reconnect for iOS < 15, since bluetooth is already stable on iOS 14
      return unless device_version.to_f >= 15

      t0 = Time.now.to_f
      is_successful = true
      session_id = OSUtils.get_session_id(@device)
      session_file = DeviceManager.session_file(@device)
      session_params = JSON.parse(File.read(session_file))
      genre = session_params["genre"]
      genre == "live_testing" ? params["live_session_id"] = session_id : params["app_live_session_id"] = session_id

      # Show banner and blue loader to client
      case genre
      when 'live_testing'
        BrowserStack.logger.info("[VOICEOVER] /voiceover_reconnect_bluetooth Sending pusher event for live")
        Utils.notify_pusher_for_live("voiceover_bluetooth_reconnect_started", session_params, @device)
      when 'app_live_testing'
        BrowserStack.logger.info("[VOICEOVER] /voiceover_reconnect_bluetooth Sending pusher event for app_live")
        Utils.notify_pusher("voiceover_bluetooth_reconnect_started", session_params, @device)
      end

      device_state = DeviceState.new(@device)
      device_state.touch_ios_voiceover_automation_running_file

      # Get Mini Bluetooth Name
      bluetoothDetails = JSON.parse(`system_profiler SPBluetoothDataType -json`)["SPBluetoothDataType"][0]
      params['miniBluetoothName'] = bluetoothDetails["local_device_title"]["general_name"]

      # Pass it to DeviceManager
      resp = DeviceManager.voiceover_reconnect_bluetooth(@@devices_conf["devices"][@device.to_s], @device, params)
      BrowserStack.logger.info("[VOICEOVER] /voiceover_reconnect_bluetooth WDA response: #{resp}")

      if !resp['value'].nil? && !resp['value']['status'].nil? && resp['value']['status'] == 'failure'
        is_successful = false
        BrowserStack.logger.info("[VOICEOVER] /voiceover_reconnect_bluetooth WDA request failed, response was : #{resp}")
      end

      final_pusher_event = is_successful ? "voiceover_bluetooth_reconnect_success" : "voiceover_bluetooth_reconnect_failure"

      # Remove blue loader and show success / failure message to client
      case genre
      when 'live_testing'
        Utils.notify_pusher_for_live(final_pusher_event, session_params, @device)
      when 'app_live_testing'
        Utils.notify_pusher(final_pusher_event, session_params, @device)
      end

      t1 = Time.now.to_f

      # all time in ms
      voiceover_reconnect_bluetooth_hash = {
        is_successful: is_successful,
        overall_reconnect_bluetooth_voiceover: ((t1 - t0) * 1000.0).to_i,
        wda_response_hash: resp
      }
      BrowserStack.logger.info("[VOICEOVER] /voiceover_reconnect_bluetooth voiceover_reconnect_bluetooth_hash : #{voiceover_reconnect_bluetooth_hash}")

      push_to_cls(params, 'voiceover_reconnect_bluetooth', '', voiceover_reconnect_bluetooth_hash)

      BrowserStack.logger.info("[VOICEOVER] /voiceover_reconnect_bluetooth_hash end")
      return 200, {}.to_json
    rescue => e
      BrowserStack.logger.error("[VOICEOVER] /voiceover_reconnect_bluetooth Exception in /voiceover_reconnect_bluetooth #{@device}: #{e.message}\n" + e.backtrace.join("\n"))
      voiceover_reconnect_bluetooth_hash = {
        is_successful: false,
        exception_message: e.message,
        exception_backtrace: e.backtrace.join("\n"),
        wda_response_hash: resp
      }
      push_to_cls(params, 'voiceover_reconnect_bluetooth', '', voiceover_reconnect_bluetooth_hash)
      return 500, {}.to_json
    ensure
      device_state.remove_ios_voiceover_automation_running_file
    end

    #TODO: remove this endpoint once rails changes deployed for DarkMode
    get '/set_timezone' do
      BrowserStack.logger.info("Setting timezone for #{@device}, called by product #{params['product']}, for session #{params['session_id']}")
      pid = Process.fork do
        DeviceManager.set_timezone(@@devices_conf["devices"][@device.to_s], @device, params)
      end
      Process.detach(pid)
      return 200, {}.to_json
    rescue => e
      BrowserStack.logger.error("Exception in set_timezone #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
      return 500, { error: "Error in set_timezone #{e.message}" }.to_json
    end

    get '/kill_all_apps' do
      pid = Process.fork do
        idevice = BrowserStack::IPhone.new(@@devices_conf["devices"][@device.to_s], @device)
        idevice.kill_all_apps
      end
      Process.detach(pid)
      return 200, {}.to_json
    rescue => e
      BrowserStack.logger.error("Exception in kill_all_apps error #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
      return 500, { error: "Error in kill_all_apps #{e.message}" }.to_json
    end

    get '/uninstall_user_apps' do
      pid = Process.fork do
        BrowserStack::IPhone.uninstall_user_apps(@device)
      rescue => e
        BrowserStack.logger.error("Exception  in uninstall_user_apps for session #{params[:app_live_session_id]} error #{@device}: #{e.message} \n")
      end
      Process.detach(pid)
      return 200, {}.to_json
    rescue => e
      BrowserStack.logger.error("Exception  in uninstall_user_apps for session #{params[:app_live_session_id]} error #{@device}: #{e.message} \n")
      return 500, { error: "Error in uninstall_user_apps #{e.message}" }.to_json
    end

    post '/shake' do
      json_data = request.body.read
      data = json_data.nil? || json_data.empty? ? '{}' : json_data
      data = JSON.parse(data)

      params = data
      session_id = params['app_automate_session_id']
      BrowserStack.logger.info("Performing Shake on #{@device}, called by product #{params['product']}, for session #{session_id} with params #{params}")

      shake_object = Shake.new(@device, session_id)
      res = shake_object.shake_device
      return 500, { status: res }.to_json unless res

      return 200, { status: res }.to_json
    end

    post '/execute_apple_pay' do
      json_data = request.body.read
      data = json_data.nil? || json_data.empty? ? '{}' : json_data
      data = JSON.parse(data)

      params = data
      session_id = params['session_id']
      BrowserStack.logger.info("Performing Apple Pay on #{@device}, called by product #{params['product']}, for session #{session_id} with params #{params}")

      apple_pay_executor = ApplePayExecutor.new(@device, session_id, params['product'])
      res = apple_pay_executor.trigger_apple_pay
      return 500, { status: res }.to_json unless res

      return 200, { status: res }.to_json
    end

    post '/prefill_apple_pay_detail' do
      BrowserStack.logger.info "Request received for prefill_apple_pay_detail with params: #{params}"
      json_data = request.body.read
      data = json_data.nil? || json_data.empty? ? '{}' : json_data
      data = JSON.parse(data)

      params = data
      session_id = params['session_id']
      BrowserStack.logger.info("Prefilling Apple Pay Details on #{@device}, called by product #{params['product']}, for session #{session_id} with params #{params}")

      apple_pay_prefill_detail_helper = ApplePayPrefillDetailHelper.new(@device, { product: params['product'], session_id: params['session_id'] })
      res, code = apple_pay_prefill_detail_helper.execute(params['applePayDetails'])
      return 500, { status: res, code: code }.to_json unless res

      return 200, { status: res }.to_json
    end

    get '/user_installed_apps' do
      display_name = params["display_name"]
      show_display_name = !(display_name.nil? || display_name.empty?)
      user_installed_apps = IdeviceUtils.list_user_installed_apps(
        @device,
        display_name: show_display_name,
        timeout: 10
      )
      return 200, { apps: user_installed_apps }.to_json
    end

    # this is specific to pm tools integration
    get '/user_installed_app_details' do
      FileUtils.touch("/tmp/pm_tools_used_#{@device}") unless !params[:is_geo_restricted].nil? && params[:is_geo_restricted] == "true"
      return 200, { apps: IdeviceUtils.list_user_installed_apps_details(@device) }.to_json
    end

    get '/log_bridge_commands' do
      DeviceManager.log_bridge_commands(@device, params)
    end

    get '/update_device_locale_and_region' do
      idevice = BrowserStack::IPhone.new(@@devices_conf["devices"][@device.to_s], @device)
      set_browserstack_env = params["set_browserstack_env"] == "true"
      idevice.launch_app_with_locale(params["locale"], params["region"], set_browserstack_env)
      return 200, {}.to_json
    rescue => e
      BrowserStack.logger.error("Exception in update_device_locale error #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
      return 500, { error: "Error in update_device_locale #{e.message}" }.to_json
    end

    get '/update_device_log_level' do
      begin
        log_level = Integer(params["log_level"])
      rescue
        return 400, { error: "Invalid log_level: #{params[:log_level]}" }.to_json
      end
      update_device_log_file_dir = "#{TMP_DIR_PATH}/AL_minimum_devicelog_level"
      update_device_log_file_path = "#{update_device_log_file_dir}/#{@device}"
      BrowserStack.logger.info "Writing log_level: #{log_level} to file: #{update_device_log_file_path}"
      cmd = "mkdir -p #{update_device_log_file_dir}; echo #{log_level} > #{update_device_log_file_path}"
      system(cmd)
      return 200, {}.to_json
    end

    get '/relaunch_app_with_locale' do
      idevice = BrowserStack::IPhone.new(@@devices_conf["devices"][@device.to_s], @device)
      idevice.relaunch_app_with_locale(params["bundle_id"])
      return 200, {}.to_json
    rescue => e
      BrowserStack.logger.error("Exception in relaunch_app_with_locale error #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
      return 500, { error: "Error in relaunch_app_with_locale #{e.message}" }.to_json
    end

    # Called via app-accessibility rule engine
    get '/accessibility_info' do
      BrowserStack.logger.info("Getting accessibility info")
      first_scan = params['first_scan'].to_s == "true"
      async_mode = params['async_mode'].to_s == "true"
      focus_order_timeout = params['focus_order_timeout']&.to_i || 5
      automate_obj = {}
      automate_obj['async_mode'] = async_mode
      automate_obj['auth_token'] = params['auth_token']
      automate_obj['test_run_uuid'] = params['test_uuid']
      automate_obj['th_auth_token'] = params['th_auth_token']
      automate_obj['th_build_uuid'] = params['build_uuid']
      automate_obj['region'] = params['region']
      automate_obj['s3_access_key'] = params['s3_access_key']
      automate_obj['s3_secret_key'] = params['s3_secret_key']
      automate_obj['s3_bucket'] = params['s3_bucket']
      automate_obj['scan_timestamp'] = params['scan_timestamp']
      automate_obj['command'] = params['command']

      info_obj = AppAccessibility.new(params['device'], params['session_id'], params['product'], first_scan, focus_order_timeout, automate_obj)
      res = info_obj.fetch_accessibility_info
      return 200, res.to_json(max_nesting: 400)
    rescue => e
      BrowserStack.logger.error("Exception in getting accessibility info for #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
      BrowserStack::Zombie.push_logs("accessibility-info-failure", e.message, { "backtrace" => e.backtrace.join("\n") })
      return 500, { error: "Error in getting accessibility info #{e.message}" }.to_json
    end

    get '/toggle_continuous_scanning' do
      pusher = {
        pusher_url: params['pusher_url'],
        pusher_channel: params['pusher_channel'],
        pusher_token: params['pusher_token']
      }
      rule_engine_callback = {
        session_id: params['session_id'],
        bs_user_id: params['bs_user_id'],
        report_id: params['report_id'],
        rule_engine_subdomain: params['rule_engine_subdomain']
      }

      info_obj = AppAccessibility.new(
        params['device'],
        params['session_id'],
        params['product']
      )
      res = info_obj.broadcast_continuous_scanning_session(
        pusher,
        rule_engine_callback,
        params['mode']
      )
      BrowserStack.logger.info("response is : #{res}")
      res = res.to_json
      [200, res]
    rescue => e
      # Log + return 500 on error
      BrowserStack.logger.error("Exception setting up continuous scanning: #{e.message}\n#{e.backtrace.join("\n")}")
      [500, { msg: "Error in continuous scanning: #{e.message}" }.to_json]
    end

    # DO NOT CALL THIS DIRECTLY, unless you know what you are doing.
    # This is called via JavaScript which is injected into the user's website.
    get '/accept_alert' do
      DeviceManager.accept_alert(@device, params, @@automate_per_device_config["ALLOW_POPUP_TAP_COORDINATES"])
    end

    # New frameworks shouldnt use this endpoint and should instead use the POST request.
    get(%r{/start_(xcuitest|fluttertest|maestro)_session}) do
      params[:event_hash] = {
        absolute_start_time: (Time.now.to_f * 1000).to_i
      }

      begin
        params['test_framework'] = params[:captures].first # The capture group from the endpoint
        output = DeviceManager.start_xctest_session(@device, params)
        Utils.mark_event_end('firecmd_time', params[:event_hash])
        Utils.send_to_eds(params, EdsConstants::APP_AUTOMATE_TEST_SESSIONS) if params[:edsHost] && params[:edsPort] && params[:edsKey]
        return 500, output.to_json if output.include? :error

        return 200, {}.to_json
      rescue FireCMDException => e
        status 500
        BrowserStack.logger.error("FireCMDException Exception in /start_#{params['test_framework']}_session #{@device}: #{e.message}\n #{e.backtrace.join('\n')}")
        { error: e.message.to_s, type: e.type.to_s, kind: e.kind, meta_data: e.meta_data }.to_json
      rescue => e
        status 500
        BrowserStack.logger.error("Exception in /start_#{params['test_framework']}_session #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
        { error: "Got Error in start_#{params['test_framework']}_session #{e.message}" }.to_json
      end
    end

    post(%r{/start_(xcuitest|fluttertest|maestro)_session}) do
      begin
        parsed_json_body = JSON.parse(request.body.read)
      rescue JSON::ParserError
        halt 400, "POST body JSON parse exception"
      end

      params.merge!(parsed_json_body)

      params[:event_hash] = {
        absolute_start_time: (Time.now.to_f * 1000).to_i
      }
      begin
        params['test_framework'] = params[:captures].first # The capture group from the endpoint
        params['execution_flow'] = "xctestrun" if params['test_framework'] == "xcuitest" && params['singleRunnerInvocation'].to_s == 'true'
        output = DeviceManager.start_xctest_session(@device, params)
        MCSPT.start_session_for_framework(params, @device)
        Utils.mark_event_end('firecmd_time', params[:event_hash])
        Utils.send_to_eds(params, EdsConstants::APP_AUTOMATE_TEST_SESSIONS) if params[:edsHost] && params[:edsPort] && params[:edsKey]
        return 500, output.to_json if output.include? :error

        return 200, {}.to_json
      rescue FireCMDException => e
        status 500
        BrowserStack.logger.error("FireCMDException Exception in /start_#{params['test_framework']}_session #{@device}: #{e.message}\n #{e.backtrace.join('\n')}")
        { error: e.message.to_s, type: e.type.to_s, kind: e.kind, meta_data: e.meta_data }.to_json
      rescue => e
        status 500
        BrowserStack.logger.error("Exception in /start_#{params['test_framework']}_session #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
        { error: "Got Error in start_#{params['test_framework']}_session #{e.message}" }.to_json
      end
    end

    get '/start_earlgrey_session' do
      params[:event_hash] = {
        absolute_start_time: (Time.now.to_f * 1000).to_i
      }
      begin
        params['test_framework'] = "earlgrey"
        output = DeviceManager.start_xctest_session(@device, params)
        Utils.mark_event_end('firecmd_time', params[:event_hash])
        Utils.send_to_eds(params, EdsConstants::APP_AUTOMATE_TEST_SESSIONS) if params[:edsHost] && params[:edsPort] && params[:edsKey]
        return 500, output.to_json if output.include? :error

        return 200, {}.to_json
      rescue => e
        status 500
        BrowserStack.logger.error("Exception in /start_earlgrey_session #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
        { error: "Got Error in start_xctest_session #{e.message}" }.to_json
      end
    end

    get '/timeout_session' do
      output = DeviceManager.timeout_xctest_session(@device, params)
      return 500, output.to_json if output.include? :error

      return 200, {}.to_json
    rescue => e
      status 500
      BrowserStack.logger.error("Exception in /timeout_session #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
      { error: "Got Error in timeout_session #{e.message}" }.to_json
    end

    get '/update_network' do
      return 200, {}.to_json if @device_state.network_simulation_file_present?

      begin
        @device_state.touch_network_simulation_file
        output = {}
        session_id = params[:live_session_id] || params[:automate_session_id] || params[:app_live_session_id]
        session_file = MobileSessionInfo.file_path(params[:device])
        is_running_session = BrowserStack::OSUtils.safe_execute("cat", [session_file]).include?(session_id)
        BrowserStack.logger.info("Update network sessionId #{session_id}. Running session check - #{is_running_session}")
        output = DeviceManager.apply_network_simulation(@device, params) if is_running_session
        if output.include? :error
          status_code = 500
          response_message = output.to_json
        else
          status_code = 200
          response_message = {}.to_json
        end
      rescue => e
        status_code = 500
        BrowserStack.logger.error("Exception in /update_network #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
        response_message = { error: "Got Error in update_network #{e.message}" }.to_json
      ensure
        @device_state.remove_network_simulation_file
      end
      return status_code, response_message
    end

    get '/get_screenshot' do
      image = DeviceManager.get_screenshot_uncached(@device, params[:orientation])
      response = { 'screenshot': Base64.strict_encode64(image) }
      BrowserStack::Zombie.push_logs("get-screenshot-success", "", { "session_id" => params[:app_live_session_id] })
      return 200, response.to_json
    rescue Exception => e
      BrowserStack::Zombie.push_logs("get-screenshot-error", "", { "session_id" => params[:app_live_session_id] })
      BrowserStack.logger.error("Exception in /get_screenshot #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
      return 500, { error: "Error getting screenshot #{e.message}" }.to_json
    end

    post '/app_live/get_snapshot_details' do
      request_body = JSON.parse(request.body.read)
      params.merge!(request_body)
      session_id = params[:app_live_session_id]

      session_details_file = "#{STATE_FILES_DIR}/al_session_#{session_id}"

      # No App-Live running sessions found.
      halt 404, {}, { error: true, message: "No Terminal found" }.to_json unless File.exist?(session_details_file)

      session_details = JSON.parse(File.read(session_details_file))

      @device = session_details['device']

      if params[:only_screenshot]
        image = DeviceManager.get_screenshot_uncached(@device, params[:orientation])
        response = { 'screenshot': Base64.strict_encode64(image) }
        BrowserStack::Zombie.push_logs("get-screenshot-success", "", { "session_id" => params[:app_live_session_id] })
        return 200, response.to_json
      else
        response = DeviceManager.get_snapshot_details(@device, params)
        BrowserStack::Zombie.push_logs("get-snapshot-details-success", "", { "session_id" => params[:app_live_session_id] })
        return 200, response.to_json
      end
    rescue Exception => e
      BrowserStack::Zombie.push_logs("get-snapshot-details-error", "", { "session_id" => params[:app_live_session_id] })
      BrowserStack.logger.error("Exception in /get_snapshot_details #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
      return 500, { error: true, message: "Error getting snapshot details." }.to_json
    end

    post '/app_live/inject_pfx_file' do
      request.body.rewind
      request_body_hash = JSON.parse(request.body.read, symbolize_names: true)
      params.merge!(request_body_hash)
      session_id = params[:app_live_session_id]
      session_details_file_path = "#{STATE_FILES_DIR}/al_session_#{session_id}"

      halt 404, {}, { success: false, message: "No Session found" }.to_json unless File.exist?(session_details_file_path)

      # Bad request if certificate details are not present
      halt 401, {}, { success: false, message: "No CertDetails Present" }.to_json if params[:certificate_details].nil? || params[:password].nil?

      session_details = JSON.parse(File.read(session_details_file_path))
      device = session_details["device"]
      certificate_details = params[:certificate_details]

      # Bad request if certificate details not present
      if certificate_details[:media_s3_url].nil? ||
        certificate_details[:filename].nil? ||
        certificate_details[:filetype].nil?
        halt 401, {}, {
          success: false,
          message: "Certificate Details Invalid"
        }.to_json
      end

      certificate_hash = { s3_url: certificate_details[:media_s3_url], filename: certificate_details[:filename], filetype: certificate_details[:filetype], password: params[:password] }.to_json
      custom_certificate_helper = CustomCertificate.new(device, session_id, certificate_hash,
                                                        BrowserStack.logger)
      custom_certificate_helper.install_pfx_certificate
      return 200, { success: true }.to_json
    rescue Exception => e
      return 500, { success: false, message: "Invalid Password" }.to_json if e.message.match?(INCORRECT_CERTIFICATE_PASSWORD_REGEX)

      # Want generic exceptions in a specific format so that we can log it to EDS via railsApp
      return 500, { success: false, message: e.message }.to_json
    end

    post '/app_live/install_ca_certs' do
      request.body.rewind
      request_body_hash = JSON.parse(request.body.read , symbolize_names: true)
      params.merge!(request_body_hash)
      session_id = params[:app_live_session_id]
      session_details_file_path = "#{STATE_FILES_DIR}/al_session_#{session_id}"

      unless File.exist?(session_details_file_path)
        halt 404, {}, {
          success: false,
          message: "No Session found"
        }.to_json
      end

      # Bad request if the command is not present or is not an array
      if params[:certificate_details].nil? || !params[:certificate_details].is_a?(Array)
        halt 401, {}, {
          success: false,
          message: "Invalid certificates"
        }.to_json
      end

      session_details = JSON.parse(File.read(session_details_file_path))

      device = session_details["device"]

      certificates = params[:certificate_details]

      custom_certificates_helper = CustomCertificate.new(device, session_id, certificates, APP_LIVE)
      custom_certificates_helper.install_all_custom_ca_certs
      return 200, { success: true }.to_json
    rescue Exception => e
      # Want generic exceptions in a specific format so that we can log it to EDS via railsApp
      return 500, { success: false, message: "#{e.message} :: #{e.backtrace.join("\n")}" }.to_json
    end

    get '/get_snapshot_details' do
      # Used for UI debugging in AppLive

      response = DeviceManager.get_snapshot_details(@device, params)
      BrowserStack::Zombie.push_logs("get-snapshot-details-success", "", { "session_id" => params[:app_live_session_id] })
      return 200, response.to_json
    rescue Exception => e
      BrowserStack::Zombie.push_logs("get-snapshot-details-error", "", { "session_id" => params[:app_live_session_id] })
      BrowserStack.logger.error("Exception in /get_snapshot_details #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
      return 500, { error: "Error getting snapshot details #{e.message}" }.to_json
    end

    get '/reset_app' do
      DeviceManager.reset_app(@device, params[:automate_session_id])
      return 200, {}.to_json
    rescue => e
      status 500
      BrowserStack.logger.error("Exception in /reset_app #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
      { error: "Got Error in resetApp #{e.message}" }.to_json
    end

    get '/app_strings' do
      BrowserStack.logger.info "Received request for app_strings with params #{params}"
      response = DeviceManager.app_strings(@device, params[:automate_session_id], params[:language], params[:string_file])

      return 200, response.to_json
    rescue AppStringsCommandError => e
      BrowserStack.logger.error("Exception in /app_strings #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
      BrowserStack::Zombie.push_logs("app-strings-command-failed", e.message.to_s, { "session_id" => params[:automate_session_id], "device" => @device })

      return 500, e.message.to_json
    rescue => e
      BrowserStack.logger.error("Exception in /app_strings #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
      BrowserStack::Zombie.push_logs("app-strings-command-failed", e.message.to_s, { "session_id" => params[:automate_session_id], "device" => @device })

      error_message = "An unknown server-side error occurred while processing the command. Original error: Something went wrong with app_strings app Command. Please try again or contact BrowserStack Support."
      return 500, error_message.to_json
    end

    get '/install_app' do
      custom_message = DeviceManager.install_app(@device, params[:automate_session_id], params[:app_hashed_id])

      response = custom_message.empty? ? {} : { message: custom_message }

      return 200, response.to_json
    rescue AppInstallCommandFailedException => e
      BrowserStack.logger.info("Exception in /install_app #{@device}: " + e.message)
      BrowserStack::Zombie.push_logs("app-install-command-failed", e.message.to_s, { "session_id" => params[:automate_session_id], "device" => @device })

      return 500, { error: e.message }.to_json
    rescue => e
      BrowserStack.logger.info("Exception in /install_app #{@device}: " + e.message)
      BrowserStack::Zombie.push_logs("app-install-command-failed", e.message.to_s, { "session_id" => params[:automate_session_id], "device" => @device })

      return 500, { error: "An unknown server-side error occurred while processing the command. Original error: Something went wrong with install app Command. Please try again or contact BrowserStack Support" }.to_json
    end

    get(%r{/stop_(xcuitest|fluttertest|maestro)_session}) do
      params['test_framework'] = params[:captures].first # The capture group from the endpoint
      output = DeviceManager.stop_xctest_session(@device, params)
      return 200, {}.to_json
    rescue => e
      BrowserStack.logger.error("Exception in /stop_#{params['test_framework']}_session #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
      status 500
      { error: "Got Error in stop_#{params['test_framework']}_session #{e.message}" }.to_json
    end

    get '/stop_earlgrey_session' do
      params['test_framework'] = "earlgrey"
      output = DeviceManager.stop_xctest_session(@device, params)
      return 200, {}.to_json
    rescue => e
      BrowserStack.logger.error("Exception in /stop_earlgrey_session #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
      status 500
      { error: "Got Error in stop_earlgrey_session #{e.message}" }.to_json
    end

    get '/hit_wda_endpoint' do
      hit_wda do |webdriver_port, wda_endpoint|
        wda_client = WdaClient.new(webdriver_port)
        wda_client.send :make_request, 'GET', wda_endpoint, {}, 30
      end
    rescue => e
      status 500
      { error: "Got Error in GET /hit_wda_endpoint #{e.message}" }.to_json
    end

    post '/hit_wda_endpoint' do
      hit_wda do |webdriver_port, wda_endpoint|
        json_data = request.body.read
        data = json_data.nil? || json_data.empty? ? '{}' : json_data
        data = JSON.parse(data)
        wda_client = WdaClient.new(webdriver_port)
        wda_client.send :make_request, 'POST', wda_endpoint, data
      end
    rescue => e
      status 500
      { error: "Got Error in POST /hit_wda_endpoint #{e.message}" }.to_json
    end

    # This API is called by iOSInteractionServer process which is acting as a bluetooth server for iOS voiceover feature
    post '/push_to_cls' do
      request_body = request.body.read
      data = request_body.nil? || request_body.empty? ? '{}' : request_body
      data = JSON.parse(data)
      message = data['message']
      json_data = data['json_data'] || {}
      udid = data['udid']
      voiceover_used_file_path = "#{STATE_FILES_DIR}/voiceover_used_#{udid}"
      session_params = JSON.parse(File.read(voiceover_used_file_path))
      BrowserStack.logger.info("/push_to_cls data :: #{data}, message :: #{message}, json_data :: #{json_data}")
      push_to_cls(session_params, message, '', json_data)
      return 200, {}.to_json
    end

    # Recycle means a VM will be restarted and all the changes made will be discarded
    delete '/recycle' do
      if OSUtils.virtual_machine?
        OSUtils.shutdown_machine(time: "+1", message: 'Receive "Recycle Virtual Machine" command')

        status 200
      else
        status 422

        { error: "Can't recycle, it's not a virtual machine" }.to_json
      end
    end

    # Endpoint to remotely restart device MOB-3297
    get '/restart_device' do
      DeviceManager.restart_device(@device)
      return 200, {}.to_json
    rescue => e
      status 500
      BrowserStack.logger.error("Exception in /restart_device #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
      { error: "Got Error in restart_device #{e.message}" }.to_json
    end

    # This endpoint is used by QA for SLA jobs
    # See: libimobiledevice_helper.rb in BStackAutomation repo.
    post '/libimobiledevice' do
      check_basic_auth
      request_body = request.body.read
      halt 400, "Request params are empty" if request_body.nil? || request_body.empty?
      begin
        request_json = JSON.parse(request_body)
      rescue JSON::ParserError
        halt 400, "JSON parse exception"
      end

      device = request_json['device']
      utility = request_json['utility']
      args = request_json['args']
      timeout = request_json['timeout'] || 5
      halt 400, "device or utility is empty" if device.nil? || device.empty? || utility.nil? || utility.empty?

      halt 400, "Unknown utility #{utility}" unless utility.start_with?('idevice') ||
                                                    utility == 'ios-deploy'

      begin
        # Input a utility binary name string like "idevice_id"
        # return the path assigned to IDEVICE_ID in constants.rb
        utility_path = Object.const_get(utility.upcase)
      rescue NameError
        halft 400, "Binary path not found for #{utility}"
      end

      device_id_switch = utility.include?('idevice') ? '-u' : '-i'

      # Eg. ideviceinstaller -u 000001231230-123123 -l
      command = "#{utility_path} #{device_id_switch} #{device} #{args}"
      begin
        output, status = BrowserStack::OSUtils.execute(command, true, timeout: timeout)
        { output: output, exitstatus: status }.to_json
      rescue OSUtilsError => e
        { command: command, error: e.message }.to_json
      end
    end

    post '/build_and_install_browserstack_app' do
      check_basic_auth
      request_body = request.body.read
      data = request_body.nil? || request_body.empty? ? '{}' : request_body
      data = JSON.parse(data)
      device_id = data['device_id']
      return 400, { error: "device_id param missing" }.to_json if device_id.nil?

      BrowserStack.logger.info "Initiating build and install BrowserStack app for device #{device_id}"
      BrowserStackAppHelper.build_and_install_browserstack_app(device_id)
      return 200, { success: "Installed BrowserStack app" }.to_json
    rescue => e
      status 500, { error: "Error in POST /build_and_install_browserstack_app #{e.message}" }.to_json
    end

    def check_basic_auth
      return if authorized?

      headers['WWW-Authenticate'] = 'Basic realm="Restricted"'
      halt 401, "Not authorized\n"
    end

    def authorized?
      @auth ||= Rack::Auth::Basic::Request.new(request.env)
      @auth.provided? && @auth.basic? && (@auth.credentials == [@@static_conf['sinatra_auth_user'], @@static_conf['sinatra_auth_pass']])
    end

    post '/run_tests_xcuitest' do
      check_basic_auth
      request_body = request.body.read
      data = request_body.nil? || request_body.empty? ? '{}' : request_body
      data = JSON.parse(data)
      device_id = data['device_id']
      return 400, { error: "device_id param missing" }.to_json if device_id.nil?

      class_name = data['class_name']
      function_name = data['function_name']
      environment_variables = data['environment_variables'].nil? ? {} : data['environment_variables']

      if class_name && function_name
        BrowserStack.logger.info("checking params #{device_id} , test_name 1.#{class_name} and 2.#{function_name}")
        if class_name.include?('SimTests') || class_name.include?('SettingsAppTests')
          BrowserStack.logger.info("Killing idevicesyslogs to disable device restrictions like opening Phone app in live.")
          cmd = "ps -ef | grep #{device_id} | grep idevicesyslog | awk '{print $2}' | xargs -I{} kill {}"
          BrowserStack::OSUtils.execute(cmd)
        end
        response = BrowserStackAppHelper.run_xcui_test(device_id, 70, class_name, function_name, environment_variables: environment_variables)
        return 200, { response: response }.to_json
      else
        BrowserStack.logger.info("Running Cleanup Tests - XCUITest: All /QATests on #{device_id}")
        BrowserStack.logger.info("Killing idevicesyslogs to disable device restrictions like opening Settings app.")
        cmd = "ps -ef | grep #{device_id} | grep idevicesyslog | awk '{print $2}' | xargs -I{} kill {}"
        BrowserStack::OSUtils.execute(cmd)
        suite_type = data['suite_type'].nil? ? 'validation' : data['suite_type']
        subsuite_type = data['subsuite_type'].nil? ? 'all' : data['subsuite_type']
        session_id = SecureRandom.hex
        BrowserStackAppHelper.run_cleanup_tests(device_id, suite_type, subsuite_type, session_id)
      end
    rescue => e
      status 500, { error: "Error in POST /run_cleanup_tests_xcuitest #{e.message}" }.to_json
    end

    # Endpoint for injecting a new image for app products
    get '/inject_image' do
      start_at = Time.now
      data_hash = {
        'inject_image' => [
          {
            'timestamp' => start_at
          }
        ]
      }
      begin
        BrowserStack.logger.info("inject_image called for device #{@device} session #{params[:session_id]}")
        session_info = DeviceManager.session_file_contents(@device)
        app_bundle_id = session_info['app_testing_bundle_id']
        ImageInjector.inject_media(@device, params, app_bundle_id)
        BrowserStack::Zombie.push_logs("inject-image-success", "", { "session_id" => params[:session_id], "device" => @device, "url" => params[:product] })
        data_hash["inject_image"][0]["status"] = 'success'
        status 200
      rescue => e
        BrowserStack::Zombie.push_logs("inject-image-failure", e.message.to_s, { "session_id" => params[:session_id], "device" => @device, "url" => params[:product] })
        status 500
        BrowserStack.logger.error("Exception in /inject_image #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
        { error: "Got Error in inject_image #{e.message}" }.to_json
        data_hash["inject_image"][0]["status"] = 'failure'
      ensure
        data_hash["inject_image"][0]["time_taken"] = Time.now - start_at
        BrowserStack.logger.info("inject_image: Update_app_patching_data_to_state_file: called for device #{@device} and session #{params[:session_id]} with file_name #{@device}")
        AppPatchingUtil.update_app_patching_data_to_state_file(@device, data_hash)
      end
    end

    get '/inject_media' do
      start_at = Time.now
      data_hash = {
        'inject_media' => [
          {
            'timestamp' => start_at
          }
        ]
      }
      begin
        BrowserStack.logger.info("inject_image called for device #{@device} session #{params[:session_id]}")
        session_info = DeviceManager.session_file_contents(@device)
        app_bundle_id = session_info['app_testing_bundle_id']
        media_format = params[:format]
        product = params[:product]
        is_async = media_format == ".mp4" && product == "app_live"
        ImageInjector.inject_media(@device, params, app_bundle_id, params, is_async)
        BrowserStack::Zombie.push_logs("inject-image-success", "", { "session_id" => params[:session_id], "device" => @device, "url" => params[:product] })
        data_hash["inject_media"][0]["status"] = 'success'
        status 200
      rescue => e
        BrowserStack::Zombie.push_logs("inject-image-failure", e.message.to_s, { "session_id" => params[:session_id], "device" => @device, "url" => params[:product] })
        status 500
        BrowserStack.logger.error("Exception in /inject_image #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
        { error: "Got Error in inject_image #{e.message}" }.to_json
        data_hash["inject_media"][0]["status"] = 'failure'
      ensure
        data_hash["inject_media"][0]["time_taken"] = Time.now - start_at
        BrowserStack.logger.info("inject_media: Update_app_patching_data_to_state_file: called for device #{@device} and session #{params[:session_id]} with file_name #{@device}")
        AppPatchingUtil.update_app_patching_data_to_state_file(@device, data_hash)
      end
    end

    get '/get_rtc_data' do
      dup_session_params = JSON.parse(File.read(BrowserStack::IPhone.session_file(@device)))
      BrowserStack.logger.info("get rtc data called for #{@device} #{dup_session_params.to_json}")
      return 200, dup_session_params.to_json
    end

    post '/update_app_settings' do # rubocop:todo Metrics/BlockLength
      if @device_state.update_app_settings_file_present?
        response = BrowserStack::AppSettingsUtil.get_response("error", "multiple_invocations")
        return 200, response.to_json
      end

      begin
        @device_state.touch_update_app_settings_file
        status_code = 200
        response = {}

        request_body = request.body.read
        raise AppSettingsError.new("Invalid request", BROWSERSTACK_ERROR_STRING, "invalid_request") if request_body.nil? || request_body.empty?

        begin
          params = JSON.parse(request_body)
          BrowserStack.logger.info("Update App settings sessionId #{params['session_id']}. Body: #{params}")
        rescue JSON::ParserError
          raise AppSettingsError.new("Invalid request structure", BROWSERSTACK_ERROR_STRING, "invalid_request")
        end

        session_info = DeviceManager.session_file_contents(@device)
        is_running_session = !session_info.nil? && !session_info.empty? && session_info['session_id'] == params["session_id"] ? true : false
        BrowserStack.logger.info("Update App settings sessionId #{params['session_id']}. Running session check - #{is_running_session}")

        if is_running_session
          app_path = session_info['downloaded_app_path']
          app_bundle_id = session_info['app_testing_bundle_id']
          params['app_testing_bundle_id'] = app_bundle_id
          params["update_app_settings"] = params["update_app_settings"].to_json
          device_config = @@devices_conf["devices"][@device.to_s]
          params["os_version"] = device_config["device_version"].to_f

          incoming_app_settings = JSON.parse(params["update_app_settings"]) rescue {} # rubocop:todo Style/RescueModifier
          # ignoring any possible error since DSL validation will fail anyways

          if incoming_app_settings && \
            incoming_app_settings['Permission Settings'] && \
            incoming_app_settings['Permission Settings']['Location'] && \
            @device_state.device_location_off_file_present?
            raise AppSettingsError.new("Device Location Off", BROWSERSTACK_ERROR_STRING, "device_location_off")
          end

          #incase app was upgraded mid session
          app_path = session_info['upgraded_app']['app_path'] if session_info['upgraded_app'] && (session_info['upgraded_app']['bundle_id'] == app_bundle_id)

          parsed_app_settings_dsl = BrowserStack::AppSettingsUtil.validate(app_path, params)
          begin
            BrowserStack::AppSettingsUtil.update_settings(@device, parsed_app_settings_dsl, params)
          ensure
            #always foreground the users app
            BrowserStack::AppSettingsUtil.foreground_user_app(@device, params)
          end

        else
          raise AppSettingsError.new("Session not running", BROWSERSTACK_ERROR_STRING, "session_not_running")
        end

        response = BrowserStack::AppSettingsUtil.get_response("success")
      rescue AppSettingsError => e
        BrowserStack.logger.error("Error Update App settings: #{e.message} #{e.kind} #{e.type} #{e.backtrace}")
        response = BrowserStack::AppSettingsUtil.get_response("error", e.kind, e.meta_data)
      rescue => e
        BrowserStack.logger.error("Error Update App settings: #{e.message} #{e.backtrace}")
        response = BrowserStack::AppSettingsUtil.get_response("error", "internal_error")
      ensure
        @device_state.remove_update_app_settings_file
        begin
          response[:stop_called] = true if File.exists?(DeviceManager.stop_params_file(@device))
        rescue
          false
        end
      end
      return status_code, response.to_json
    end

    get '/get_patch_type' do
      session_id = ""
      begin
        session_params = Utils.read_json_file("#{@@server_config['state_files_dir']}/#{@device}_session")
        session_id = session_params['app_live_session_id'] || session_params['automate_session_id']
        BrowserStack.logger.info("get_patch_type called for device #{@device} session #{session_id}")
        app_patch_type = session_params[APP_PATCH_TYPE]
        biometric_user_option_accessible = session_params[BIOMETRIC_USER_OPTION_ACCESSIBLE].to_s == "true" ? BIOMETRIC_USER_OPTION_ACCESSIBLE : ''
        password_visibility_patch = session_params["password_visibility_patch"].to_s == "true" ? PASSWORD_VISIBILITY_PATCH : ''
        keychain_biometrics = session_params[KEYCHAIN_BIOMETRICS].to_s == "true" ? KEYCHAIN_BIOMETRICS : ''
        app_patch_type = app_patch_type.nil? || app_patch_type.empty? ? "[biometrics, camera, passcode, #{password_visibility_patch}, keychain, #{keychain_biometrics}, #{biometric_user_option_accessible}]" : "[#{app_patch_type}, #{password_visibility_patch}, #{keychain_biometrics}, #{biometric_user_option_accessible}]"
        BrowserStack.logger.info("get_patch_type #{@device} session #{session_id} Returning app_patch_type #{app_patch_type}")
        app_patch_type = "[#{BONJOUR_CONNECTION}]" if session_params["is_app_accessibility"].to_s.downcase == "true"
        return 200, app_patch_type
      rescue => e
        BrowserStack.logger.error("Exception in /get_patch_type #{@device}  session #{session_id}: #{e.message} \n#{e.backtrace.join("\n")}")
        return 200, "[biometrics, camera, passcode]"
      end
    end

    get '/device_logger_port' do
      session_id = ""
      begin
        session_params = Utils.read_json_file("#{@@server_config['state_files_dir']}/#{@device}_session")
        current_device = DeviceManager.device_configuration_check(@device)
        session_id = session_params['app_live_session_id']
        BrowserStack.logger.info("device_logger_port called for device #{@device} session #{session_id}")
        port = current_device["device_logger_port"]
        return 200, port
      rescue => e
        BrowserStack.logger.error("Exception in /device_logger_port #{@device}  session #{session_id}: #{e.message} \n#{e.backtrace.join("\n")}")
        return 200, "45690"
      end
    end

    get '/biometric_value' do
      session_id = ""
      begin
        session_params = Utils.read_json_file("#{@@server_config['state_files_dir']}/#{@device}_session")
        session_id = session_params['app_live_session_id'] || session_params['automate_session_id']
        BrowserStack.logger.info("biometric_value called for device #{@device} session #{session_id}")
        biometric_value = session_params[BIOMETRIC_USER_OPTION]
        return 200, biometric_value
      rescue => e
        BrowserStack.logger.error("Exception in /biometric_value #{@device}  session #{session_id}: #{e.message} \n#{e.backtrace.join("\n")}")
        return 200, "pass"
      end
    end

    post '/xcuitest/inject-media' do # rubocop:todo Metrics/BlockLength
      session_id = ""
      start_at = Time.now
      data_hash = { 'xcuitest_inject_media' => [{ 'timestamp' => start_at }] }
      begin
        session_info = DeviceManager.session_file_contents(@device)
        session_id = session_info['automate_session_id']
        custom_medias = session_info['camera_injection_media'] || []
        custom_medias = JSON.parse(custom_medias)
        data = JSON.parse(request.body.read)
        BrowserStack.logger.info("/xcuitest/inject-media called for device #{@device} session #{session_id} with #{data} and params #{params}")
        media = custom_medias.find do |custom_media|
          parsed_media = JSON.parse(custom_media)
          parsed_media["filename"] == data["mediaID"]
        end
        if media.nil?
          data_hash["xcuitest_inject_media"][0]["status"] = 'bad_request'
          return 400, { "status": "failed", "message": "Please check if media is correct" }.to_json
        else
          media = JSON.parse(media)
          params = {
            session_id: session_id,
            file_url: media['s3_url'],
            media_hashed_id: media['media_id'].split('//').last.to_s,
            format: ".#{media['filename'].split('.').last}",
            product: session_info['genre']
          }
          ImageInjector.inject_media(@device, params)
          BrowserStack::Zombie.push_logs("inject-image-success", "", { "session_id" => session_id, "device" => @device, "url" => session_info['genre'] })
          data_hash["xcuitest_inject_media"][0]["status"] = 'success'
          return 200, { "status": "success", "message": "Sucessfuly injected media." }.to_json
        end
      rescue => e
        BrowserStack::Zombie.push_logs("inject-image-failure", e.message.to_s, { "session_id" => session_id, "device" => @device, "url" => session_info['genre'] })
        BrowserStack.logger.error("Exception in /inject_image #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
        data_hash["xcuitest_inject_media"][0]["status"] = 'failure'
        return 500, { "status": "failed", error: "Something Went Wrong on Server Side" }.to_json
      ensure
        data_hash["xcuitest_inject_media"][0]["time_taken"] = Time.now - start_at
        BrowserStack.logger.info("xcuitest_inject_media: Update_app_patching_data_to_state_file: called for device #{@device} and session #{session_id} with file_name #{@device}")
        AppPatchingUtil.update_app_patching_data_to_state_file(@device, data_hash)
      end
    end

    post '/app_percy/screenshot' do
      data = JSON.parse(request.body.read)
      devices_json = @@devices_conf['devices']
      BrowserStack.logger.info("data = #{data}")
      screenshot_manager = AppPercy::ScreenshotManager.new(params[:device], devices_json)
      AppPercyGCSManager.project_id = data['project_id']
      tiles_array = screenshot_manager.take_screenshot(data['appium_session_id'], data['screenshot_type'], data['build_id'],
                                                       data['scale_factor'], data['options'])
      BrowserStack.logger.info("tiles = #{tiles_array}")

      BrowserStack::Zombie.push_logs('app_percy_screenshot_1', '', {
        "device" => @device.to_s,
        "genre" => params["genre"],
        "session_id" => data['appium_session_id'] || "unknown",
        "team" => 'app-percy'
      })

      return 200, tiles_array.to_json

    rescue => e
      BrowserStack.logger.error("#{e.message} \n #{e.backtrace[0]}")

      BrowserStack::Zombie.push_logs('app_percy_screenshot_0',
                                     "Percy Screenshot Failed: #{e.backtrace.join("\n")}",
                                     {
                                       "device" => @device.to_s,
                                       "genre" => params["genre"],
                                       "session_id" => data['appium_session_id'] || "unknown",
                                       "team" => 'app-percy'
                                     })
      return 500, e.message.to_json
    end

    post '/percy/start_server' do
      data = JSON.parse(request.body.read)
      sess = Percy::PercySession.new(@device)
      return sess.start_server(data["env"])
    rescue => e
      BrowserStack.logger.error("#{e.message} \n #{e.backtrace.join("\n")}")
      return 500, e.message
    end

    post '/percy/stop_server' do
      FileUtils.rm_f("#{STATE_FILES_DIR}/#{@device}_env")
      sess = Percy::PercySession.new(@device)
      return sess.stop_server
    rescue => e
      BrowserStack.logger.error("#{e.message} \n #{e.backtrace.join("\n")}")
      return 500, e.message
    end

    post '/percy/start_jackproxy' do
      data = JSON.parse(request.body.read)
      sess = Percy::PercySession.new(@device, data['image_tag'])
      return sess.start_jackproxy(
        data['params'],
        data['proxy_map'],
        data['debug_data'],
        data['proxy_map_url'],
        data['url_suffix'],
        renderer_key: data['renderer_key']
      )
    rescue RendererKeyError => e
      BrowserStack.logger.error("#{e.message} \n")
      return 403, e.message
    rescue => e
      BrowserStack.logger.error("#{e.message} \n #{e.backtrace.join("\n")}")
      return 500, e.message
    end

    get '/percy/dom_metadata_finalize' do
      sess = Percy::PercySession.new(@device)
      data = {}
      data['project_id'] = params['project_id']
      data['build_id'] = params['build_id']
      return sess.percy_dom_metadata_finalize_handler(data)
    rescue => e
      BrowserStack.logger.error("#{e.message} \n #{e.backtrace[0]}")
      return 500, e.message
    end

    post '/percy/dom_metadata_upload' do
      sess = Percy::PercySession.new(@device)
      data = JSON.parse(request.body.read)
      return sess.percy_dom_metadata_upload_handler(data)
    rescue => e
      BrowserStack.logger.error("#{e.message} \n #{e.backtrace[0]}")
      return 500, e.message
    end

    post '/percy/stop_jackproxy' do
      sess = Percy::PercySession.new(@device)
      data = JSON.parse(request.body.read)
      return sess.stop_jackproxy(data['debug_data'], renderer_key: data['renderer_key'])
    rescue RendererKeyError => e
      BrowserStack.logger.error("#{e.message} \n")
      return 403, e.message
    rescue => e
      BrowserStack.logger.error("#{e.message} \n #{e.backtrace.join("\n")}")
      return 500, e.message
    end

    get '/percy/clean_mobile' do
      sess = Percy::PercySession.new(@device)
      return sess.clean_mobile(renderer_key: params['renderer_key'])
    rescue RendererKeyError => e
      BrowserStack.logger.error("#{e.message} \n")
      return 403, e.message
    rescue => e
      BrowserStack.logger.error("#{e.message} \n #{e.backtrace.join("\n")}")
      return 500, e.message
    end

    post '/percy/capture_tile' do
      data = JSON.parse(request.body.read)

      sess = Percy::PercySession.new(@device)
      GCSManager.project_id = data['percy_project']
      sess.tiles_manager.capture_tile(data['appium_session_id'], data['build_id'], data['seq_no'], data['strip'],
                                      data['bucket'], renderer_key: data['renderer_key'])
      return 200
    rescue RendererKeyError => e
      BrowserStack.logger.error("#{e.message} \n")
      return 403, e.message
    rescue => e
      BrowserStack.logger.error("#{e.message} \n #{e.backtrace[0]}")
      return 500, e.message
    end

    get '/percy/capture_finalize' do
      sess = Percy::PercySession.new(@device)
      return sess.tiles_manager.finalize(params[:expected_tiles].to_i, renderer_key: params['renderer_key']).map(&:to_h).to_json
    rescue RendererKeyError => e
      BrowserStack.logger.error("#{e.message} \n")
      return 403, e.message
    rescue => e
      BrowserStack.logger.error("#{e.message} \n #{e.backtrace[0]}")
      return 500, e.message
    end

    post '/percy/health_check' do
      sess = Percy::PercySession.new(@device)
      status = sess.health_check?
      return 200 if status

      return 500
    end

    get '/percy/is_running' do
      return Percy::PercySession.running?(@device)
    rescue => e
      BrowserStack.logger.error("#{e.message} \n #{e.backtrace[0]}")
      return 500, e.message
    end

    get '/percy/keep_alive' do
      res = Percy::PercySession.keep_alive(@device)
      return 422, { "status" => "Could not process since session file is absent" }.to_json unless res

      return 200, { "status" => "Successfully touched session file" }.to_json
    rescue => e
      BrowserStack.logger.error("#{e.message} \n #{e.backtrace[0]}")
      return 500, e.message
    end

    get '/percy/minified_cleanup' do
      idevice = BrowserStack::IPhone.new(@@devices_conf["devices"][@device.to_s], @device)
      idevice.percy_minified_cleanup(@device)
      return 200, { "status" => "Successfully cleaned the safari device" }.to_json
    rescue => e
      BrowserStack.logger.error("Exception in safari_cleanup error #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
      return 500, { error: "Error in safari_cleanup #{e.message}" }.to_json
    end

    get '/percy/minified_cleanup_done' do
      minified_flow_instrumentation_file = "/tmp/minified_flow_instrumentation_#{@device}.txt"
      content = nil
      File.open(minified_flow_instrumentation_file, "r") do |f|
        content = f.read.to_s
      end
      return 200, { "status" => true }.to_json if content.include?('success completed')

      return 200, { "status" => false }.to_json
    end

    post '/percy/screenshot' do
      sess = Percy::PercySession.new(@device)
      data = JSON.parse(request.body.read)
      devices_json = @@devices_conf['devices']
      return sess.percy_screenshot_handler(data, devices_json: devices_json)
    end

    post '/percy/setup_automate_session' do
      sess = Percy::PercySession.new(@device)
      data = JSON.parse(request.body.read)
      return sess.percy_setup_automate_session_handler(data)
    end

    get '/launch_user_app_settings' do
      bundle_id = params[:bundle_id]
      app_live_settings_v3 = !params[:app_live_settings_v3].nil? ? params[:app_live_settings_v3].to_s : "false"
      return 400, { message: "bundle_id missing" }.to_json if bundle_id.to_s.empty?

      device_state = DeviceState.new(@device)
      return 400, { message: "Settings Launch already running" }.to_json if device_state.al_user_settings_launch_file_present?

      # ensure we dont handle multiple launch requests
      device_state.touch_al_user_settings_launch_file

      resp = DeviceManager.launch_user_app_settings(@device, bundle_id, app_live_settings_v3)
      return resp[:status], { message: resp[:message] }.to_json

    rescue => e
      BrowserStack.logger.error("Error while launching settings: #{e.message} #{e.backtrace}")
      return 500, { message: "Failed to launch settings: #{e.message}" }.to_json
    ensure
      # ensure we remove settings launch request
      device_state.remove_al_user_settings_launch_file
    end

    get '/media/push_to_device' do
      BrowserStack.logger.info("inject_file called for device #{@device} session #{params[:session_id]}, use_pusher: #{params[:use_pusher]}")

      # Since we are using pusher, we can then spawn this code so that railsApp doesn't have to wait for injection to complete
      # This is done for maintaing backwards compatability
      if params[:use_pusher].to_s == 'true'
        device_injection_media_dir = FileInjector.get_injection_file_dir(@device)
        FileUtils.mkdir_p(device_injection_media_dir)
        File.write(File.join(device_injection_media_dir, "params.json"), params.to_json)
        pid = Process.fork do
          cmd = "#{RUBY_BINARY_PATH} #{DIR_HOME}/lib/utils/file_injector.rb 'inject_file' #{@device}"
          exec(cmd)
        end
        Process.detach(pid)
      else
        FileInjector.inject_file(@device, params)
      end

      status 200
    rescue => e
      status 500
      BrowserStack.logger.error("Exception in /media/push_to_device #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
    end

    get '/cleanup_stats' do
      device = params[:device]
      return 400, { message: "device not found" }.to_json if device.nil?

      last_cleanup_stats_file = "#{@@server_config['state_files_dir']}/last_cleanup_stats_#{device}.json"
      return 400, { error: "Last clean up stats for device #{device} not found" }.to_json unless File.exist?(last_cleanup_stats_file)

      last_cleanup_stats = JSON.parse(File.read(last_cleanup_stats_file))
      return 200, last_cleanup_stats.to_json
    end

    get '/upload_crash_logs' do
      CrashLog.log_info("/upload_crash_logs For device #{@device} session #{params[:session_id]}")
      num_crash_reports = CrashLog.generate_and_upload(@device, params, @@server_config)[:num_crash_reports]
      CrashLog.log_info("generate and upload successful For device #{@device} session #{params[:session_id]}")
      return 200, { num_crash_reports: num_crash_reports }.to_json
    rescue => e
      CrashLog.log_error("Exception in /upload_crash_logs for device#{@device} and session_id #{params[:session_id]}: #{e.message} \n#{e.backtrace.join("\n")}")
      status 500
    end

    get '/download_files' do
      @params[:product] = APP_LIVE if @params[:product] == APP_LIVE_TESTING
      download_file = DownloadFile.new(@device, @params[:session_id], @params[:product])
      begin
        download_file.log(:info, "/download_files For device #{@device} session #{@params[:session_id]}")
        pid = Process.fork do
          response = download_file.generate_and_upload(params, @@server_config)
          Utils.notify_pusher(response[:message], params, @uuid, { meta_data: { download_files_url: response[:url] }
                                                                                .to_json }, @params[:product])
        rescue => e
          Utils.notify_pusher(e.message.to_s, @params, @uuid, nil, @params[:product])
        end
        Process.detach(pid)
        return 200, {}.to_json
      rescue => e
        download_file.log(:error, "#{download_file} failed for device: #{@device} and session: #{params[:session_id]}, #{e.message} #{e.backtrace.join("\n")}")
        return 500, { message: "Download Files Failed" }.to_json
      end
    end

    get '/apps_opened_during_session' do
      # to be used by QA to detect app detection in regression suite
      device = params[:device]
      apps_opened = IosWatcher.apps_opened_during_session_set(device)
      return 200, { apps_opened: apps_opened.to_a }.to_json
    rescue => e
      BrowserStack.logger.error("Exception in /apps_opened_during_session for #{@device}: #{e.message} \n#{e.backtrace.join("\n")}")
      return 500, { message: "Fetch apps opened during session failed" }.to_json
    end

    get '/device_info' do
      current_device_time = IdeviceUtils.device_time(@device)
      current_device_timezone = IdeviceUtils.timezone(@device)
      current_device_name = IdeviceUtils.idevice_name(@device)
      return 200, { current_device_time: current_device_time, current_device_timezone: current_device_timezone, current_device_name: current_device_name }.to_json
    rescue => e
      BrowserStack.logger.error("Exception in /device_info for device#{@device} and session_id #{params[:session_id]}: #{e.message} \n#{e.backtrace.join("\n")}")
      return 500, { message: "Fetch iDeviceInfo Failed" }.to_json
    end

    get '/get_device_injected_file' do
      BrowserStack.logger.info("checking injecting file #{@device} session #{params[:session_id]}")
      file_uploaded = OSUtils.execute("#{IOS_DEPLOY} --id #{@device} --bundle_id=\"#{CHROME_BUNDLE_ID}\"  --list=\"#{FILE_PATH}\"")
      return 200, { file_uploaded: file_uploaded }.to_json
    end

    get '/get_state_file_content' do
      state_file_name = params[:state_file_name]
      device_id = params[:device_id]
      get_static_files_data = OSUtils.execute("cat #{STATE_FILES_DIR}/#{state_file_name}_#{device_id}")
      BrowserStack.logger.info("checking content of state file #{get_static_files_data}")
      return 200, { get_static_files_data: get_static_files_data }.to_json
    end

    get '/trigger_interaction_sync_script' do
      BrowserStack.logger.info("Triggering test #{params['test_id']} for Interaction Sync Instrumentation with params #{params.inspect}")
      Thread.bs_run do
        InteractionSyncStabilityTester::InteractionSyncTestManager.new('iOS', params['device_name'], params['device_version'], params['browser'], params['automation_name'], params['selenium_port'], params['url'], params['test_id']).execute_script
      rescue => e
        return 500, { message: "Script Trigger Failed: #{e.message}" }.to_json
      end
      return 200, { message: "Script Trigger Successful" }.to_json
    end

    get '/get_state_file' do
      state_file_name = params[:state_file_name]
      get_static_files_list = OSUtils.execute("ls #{STATE_FILES_DIR} | grep '#{state_file_name}'")
      BrowserStack.logger.info("checking state files #{get_static_files_list}")
      return 200, { get_static_files_list: get_static_files_list }.to_json
    end

    get '/drain_nomad_allocations' do
      BrowserStack.logger.info("Draining Nomad Allocations For This Host")
      lock_file = '/tmp/nomad_drain.lock'
      wait_min = 2
      time_window = wait_min * 60

      if File.exist?(lock_file)
        last_run_time = File.mtime(lock_file)
        if Time.now - last_run_time < time_window
          BrowserStack.logger.info("Another Drain Happened in Last #{wait_min} Minutes. Skipping execution")
          return 429, { status: "Nomad Drain Skipped", message: "Drain Happened in Last #{wait_min} Minutes" }.to_json
        else
          BrowserStack.logger.info("Nomad Allocations Drain Allowed")
          drain_nomad_allocations
          FileUtils.touch(lock_file)
          return 202, { status: "Nomad Allocations Drain Request Accepted", message: "Drain Will Happen Async" }.to_json
        end
      else
        BrowserStack.logger.info("Lock File Doesn't Exist. Nomad Allocations Drain Allowed")
        drain_nomad_allocations
        FileUtils.touch(lock_file)
        return 202, { status: "Nomad Allocations Drain Request Accepted", message: "Drain Will Happen Async"  }.to_json
      end
    rescue => e
      BrowserStack.logger.error("Exception in /drain_nomad_allocations : #{e.message} \n#{e.backtrace.join("\n")}")
      return 500, { status: "Exception while draining : #{e.message}", message: e.backtrace.join("\n") }.to_json
    end

    def drain_nomad_allocations
      ENV['NOMAD_TOKEN'] = @@static_conf['nomad_drain_token']
      system("(gtimeout 180 nomad node drain -self -enable; nomad node eligibility -enable -self) &")
      BrowserStack.logger.info("Nomad Allocations Drain & Religibility Completed")
    rescue => e
      BrowserStack.logger.error("Exception in triggering Nomad commands: #{e.message} \n#{e.backtrace.join("\n")}")
    end

    post '/toggle_private_device_feature' do
      device = params["device"]&.strip
      return [400, { error: '"device" Not provided or Empty' }.to_json] if device.nil? || device.empty?

      data = begin
        JSON.parse(request.body.read || '{}')
      rescue JSON::ParserError
        {}
      end

      feature = data["feature"]&.strip
      action = data["action"]&.strip

      BrowserStack.logger.info("Feature Toggle Request :: Device: #{device}, Feature: #{feature}, Action: #{action}")

      allowed_actions = ['GRANT_ACCESS', 'REVOKE_ACCESS']

      return [400, { error: '"feature" Not provided or Empty' }.to_json] if feature.nil? || feature.empty?
      return [400, { error: '"action" Not provided or Empty' }.to_json] if action.nil? || action.empty?
      return [400, { error: "Invalid Action, Allowed Actions are: #{allowed_actions.join(', ')}" }.to_json] unless allowed_actions.include?(action)

      DeviceManager.toggle_private_device_feature(device, feature, action)

      [200, { message: "Action #{action} successfully taken on Feature #{feature}" }.to_json]
    rescue => e
      BrowserStack.logger.error("Error Toggling Private Device Feature: #{e.message}")
      [500, { error: 'Internal Server Error in Toggling Private Device Feature' }.to_json]
    end

    post '/add_device_to_private_cloud' do # rubocop:todo Metrics/BlockLength
      @device = params["device"]&.strip
      BrowserStack.logger.info("Received device parameter: #{@device}")
      return [400, { error: '"device" not provided or empty' }.to_json] if @device.nil? || @device.empty?

      device_state = DeviceState.new(@device)

      BrowserStack.logger.info("Device Addition Request :: Device: #{@device}")
      device_addition_steps = ["create_dedicated_file", "force_install_mdm_profile"]
      device_addition_steps.each do |step|
        case step
        when "create_dedicated_file"
          begin
            device_state = DeviceState.new(@device)
            unless device_state.dedicated_device_file_present?
              BrowserStack.logger.info "Creating dedicated device file for - #{@device}"
              device_state.touch_dedicated_device_file
            end
          rescue
            BrowserStack.logger.error("Error during #{step} for device #{@device}")
            return [500, { error: "Internal server error during #{step}" }.to_json]
          end
        when "force_install_mdm_profile"
          begin
            BrowserStack.logger.info "Forcing MDM profile installation for - #{@device}"
            configuration_profiles_manager = ConfigurationProfilesManager.new(@device, BrowserStack.logger)
            configuration_profiles_manager.remove_profile(:restrictions, nil, remove_via: :automatic)
            device_state.touch_force_install_mdm_profiles_file
            device_state.touch_enable_restriction_feature_file
          rescue => e
            BrowserStack.logger.error("Error during #{step} for device #{@device}: #{e.message}")
            return [500, { error: "Internal server error during #{step}: #{e.message}" }.to_json]
          end
        else
          return [400, { error: "Unhandled step: #{step}" }.to_json]
        end
      end
      [200, { message: "Successfully completed all actions for device #{@device}" }.to_json]
    rescue
      BrowserStack.logger.error("Error adding device to private cloud")
      [500, { error: 'Internal Server Error in adding device to private cloud' }.to_json]
    end

    get '/sim_details' do
      device = params['device']&.strip
      return 400, { success: false, message: 'Device parameter is missing.', sim_details: {} }.to_json unless device

      sim_slot = params['sim_slot']&.to_i
      return 400, { success: false, message: 'Invalid sim_slot. It must be 1 or 2.', sim_details: {} }.to_json if sim_slot && ![1, 2].include?(sim_slot)

      begin
        device_sim_details = DeviceSIMHelper.retrieve_sim_details_from_device(device, '')
        sim_details = device_sim_details.each_with_object({}) do |sim, details|
          details[sim['sim_slot'].to_s] = { imei: sim['imei'], iccid: sim['iccid'] }
        end
      rescue
        sim_details = {}
      end

      if sim_slot
        sim_info = sim_details[sim_slot.to_s]
        return 200, { success: true, message: 'SIM details retrieved successfully.', sim_details: { sim_slot.to_s => sim_info } }.to_json if sim_info
      elsif !sim_details.empty?
        return 200, { success: true, message: 'SIM details retrieved successfully.', sim_details: sim_details }.to_json
      end

      return 200, { success: true, message: 'SIM card not found.', sim_details: {} }.to_json
    rescue => e
      BrowserStack.logger.error("Error fetching sim details for #{device}: #{e.message}")
      return 500, { success: false, message: "Error fetching sim details for device: #{device}" }.to_json
    end

    post '/sim_details' do
      device = params['device']&.strip
      return 400, { success: false, message: 'Device parameter is missing.' }.to_json unless device

      sim_details = JSON.parse(request.body.read)
      DeviceSIMHelper.validate_sim_details(sim_details)

      sim_info_file = "#{@@server_config['state_files_dir']}/sim_info_#{device}"

      if File.exist?(sim_info_file)
        DeviceSIMHelper.update_existing_sim_details(sim_info_file, sim_details, device)
      else
        DeviceSIMHelper.create_new_sim_details(sim_info_file, sim_details, device)
      end

      return 200, { success: true, message: "Sim details added for device #{device}" }.to_json
    rescue SimValidationError => e
      BrowserStack.logger.error("SimValidationError for #{device}: #{e.message}")
      return 400, { success: false, message: "SimValidationError for device #{device}: #{e.message}" }.to_json
    rescue => e
      BrowserStack.logger.error("Error writing sim details file for device #{device}: #{e.message}")
      return 500, { success: false, message: "Error updating Sim details for device #{device}: #{e.message}" }.to_json
    end

    def delete_sim_info(device)
      device_state = DeviceState.new(device)
      device_state.remove_sim_info_file

      BrowserStack.logger.info("Sim details file deleted for device #{device}")
      [200, { success: true, message: "Sim details deleted for #{device}" }.to_json]
    end

    def delete_slot_specific_sim_info(updated_sim_details, sim_slot, device)
      device_state = DeviceState.new(device)

      if updated_sim_details.empty?
        device_state.remove_sim_info_file
      else
        device_state.write_to_sim_info_file(updated_sim_details.to_json)
      end

      BrowserStack.logger.info("Sim details for slot #{sim_slot} deleted for device #{device}")
      [200, { success: true, message: "Sim details for slot #{sim_slot} deleted for #{device}" }.to_json]
    end

    delete '/sim_details' do
      device = params["device"]&.strip
      sim_slot = params["sim_slot"]&.strip
      return 400, { success: false, message: 'Device parameter is missing.' }.to_json unless device

      device_state = DeviceState.new(device)

      if device_state.sim_info_file_present?
        if sim_slot
          sim_details = JSON.parse(device_state.read_sim_info_file)
          updated_sim_details = sim_details.reject { |detail| detail["sim_slot"].to_s == sim_slot }

          if updated_sim_details.size < sim_details.size
            response = delete_slot_specific_sim_info(updated_sim_details, sim_slot, device)
            return response[0], response[1]
          else
            BrowserStack.logger.info("No sim details found for slot #{sim_slot} on device #{device}")
            return 400, { success: false, message: "No sim details found for slot #{sim_slot} on device #{device}" }.to_json
          end
        else
          response = delete_sim_info(device)
          return response[0], response[1]
        end
      else
        BrowserStack.logger.info("Sim details file not found/details doesn't exist for given params")
        return 400, { success: false, message: "Sim details not found" }.to_json
      end
    rescue => e
      BrowserStack.logger.error("Error deleting sim details file for device #{device}: #{e.message}")
      return 500, { success: false, message: "Error deleting Sim details for #{device}: #{e.message}" }.to_json
    end
  end
end
