require 'English'
require 'fileutils'
require 'cleanup_status_db'

require_relative 'device_manager'
require_relative 'iphone'
require 'browserstack_logger'
require_relative '../lib/configuration'
require_relative '../lib/ios_influxdb_client'
require_relative '../lib/utils/ios_mdm_service_client'
require_relative '../lib/utils/idevice_utils'
require_relative '../lib/utils/alerter'
require_relative '../lib/utils/http_utils'
require_relative '../lib/utils/osutils'
require_relative '../lib/network_simulator'
require_relative '../lib/privoxy_manager'
require_relative '../lib/helpers/wda_client'
require_relative '../lib/helpers/cleanup_helpers'
require_relative '../lib/models/device_state'
require_relative '../lib/helpers/pfctl_helper'
require_relative '../lib/recover_device'
require_relative '../lib/device_logger_metric'
require_relative '../lib/app_percy/cli_manager'
require_relative '../lib/device_setup/full_cleanup/mdm_cleanup_manager'
require_relative '../lib/utils/custom_mdm_manager'
require 'bsenv'

if ARGV.empty?
  msg = "No device specified.. Exiting cleanup"
  puts msg
  abort(msg) # STDERROR
end

conf = BrowserStack::Configuration.new
server_config = conf.all

device = ARGV[0]
Thread.current[:device_id] = device

hooter = Hooter.new
cleanup_type = ARGV[1].to_s
min_flow_start_time = ARGV[2].to_s.to_i

IdeviceUtils.configure(server_config)
BrowserStack::IosMdmServiceClient.configure

log_file = "#{server_config['logging_root']}/cleanup_#{device}.log"
session_id = BrowserStack::OSUtils.get_session_id(device)
logger_params = {
  component: 'cleanup.rb',
  session_id: session_id
}
BrowserStack.init_logger(log_file, logger_params)
$stderr.reopen(log_file, "a")
$stderr.sync = true

influxdb_client = BrowserStack::IosInfluxdbClient.new(BrowserStack.logger)
BrowserStack::IosInfluxdbClient.component = 'cleanup'

DeviceManager.configure server_config
BrowserStack::Alerter.configure server_config
reboot_wait_time = server_config['reboot_wait_time']

device_configuration = DeviceManager.device_configuration_check device
device_name = begin
  device_configuration['device_name']
rescue
  ""
end
device_version = begin
  device_configuration['device_version']
rescue
  ""
end
device_port = begin
  device_configuration['port']
rescue
  ""
end

# File to track how many times cleanup was done (i.e. retried)
cleanupdone_file = DeviceManager.cleanup_done_file(device)
cleanup_count = begin
  File.read(cleanupdone_file).to_i
rescue
  0
end

# File to track when cleanup was started
cleanupstarted_file = DeviceManager.cleanup_started_file(device)

# Find session genre of last session, used for logging
session_genre = begin
  JSON.parse(File.read("#{server_config['state_files_dir']}/#{device}_session"))["genre"]
rescue
  nil
end

zombie_data = {
  "os" => "ios_njb",
  "device" => device,
  "terminal_type" => "realMobile",
  "browser" => device_name,
  "browser_version" => device_version,
  "session_id" => session_id,
  "genre" => session_genre
}

device_state = DeviceState.new(device)

zombie_cleanup_tag = cleanup_type == "full_cleanup" ? "full_cleanup" : "cleanup"
zombie_cleanup_tag = "first_cleanup" if device_state.first_cleanup_file_present?
zombie_cleanup_tag = "dedicated_#{zombie_cleanup_tag}" if device_state.dedicated_cleanup_file_present?

this_run_start_time = Time.now

if File.exist?(cleanupstarted_file) && cleanup_count != 0
  # If cleanup has already started, that means this file will exist
  start_time = File.ctime(cleanupstarted_file)
else
  # For the first run in cleanup, need to create this file.
  File.write(cleanupstarted_file, this_run_start_time.to_i)
  start_time = this_run_start_time
end

def remove_session_specific_files(udid, debugger_port)
  # This is done at the end of cleanup to support the cases when device reboots with some exception
  # and the cleanup things are maintained so for every reboot cleanup things are persisted
  opened_things = ["appstore", "testflight", "dialer_app"]
  opened_things.each do |opened_thing|
    # Mostly touched from device-logger based on usage
    opened_file_name = "/tmp/#{opened_thing}_opened_#{udid}"
    File.delete(opened_file_name) if File.exist?(opened_file_name)
  end
  tab_switch_file = "/tmp/tab_switches_#{debugger_port}"
  File.delete(tab_switch_file) if File.exist?(tab_switch_file)
  device_state = DeviceState.new(udid)
  unless device_state.dedicated_cleanup_file_present?
    app_tracking_dir = "/tmp/#{udid}_installed_apps"
    FileUtils.remove_dir(app_tracking_dir) if File.exist?(app_tracking_dir)
  end
end

def abort_if_cleanup_not_possible(device)
  ret, cleanup_not_possible_reason = CleanupHelpers.cleanup_possible?(device)
  unless ret
    BrowserStack.logger.error("Cleanup not possible for #{device} because of #{cleanup_not_possible_reason}, Aborting and requesting setup to device check.")
    CleanupHelpers.abort_and_request_setup!(device, cleanup_not_possible_reason)
  end
end

def minified_cleanup_flow(device, device_state, cleanup_type, session_id, instrumentation_file, min_flow_start_time)
  time_since_start = Time.now.to_i - min_flow_start_time
  File.open(instrumentation_file, "a") { |f| f.puts "truncate_privoxy_logs_started #{time_since_start}" }
  BrowserStack.logger.info("[minified_essential_time] truncate_privoxy_logs_started #{time_since_start}")
  PrivoxyManager.truncate_privoxy_logs(device)

  device_rebooted = DeviceManager.reboot_file(device)
  if File.exist?(device_rebooted)
    device_state.remove_minimized_cleanup_reserved_file
    device_state.remove_preserve_app_state_reserved_file
    BrowserStack.logger.error("device_rebooted detected, #{device_rebooted} file exists. Aborting reserved sessions")
    raise "#{OFFLINE_REASON_MINIFIED_PREFIX} : device rebooted"
  end

  DeviceManager.minified_cleanup_async(device, cleanup_type, session_id, min_flow_start_time)
end

def instrument_minfied_cleanup(minified_instrumentation_file, min_flow_start_time, zombie_data)
  instrumentation_data = {}
  text = File.open(minified_instrumentation_file).read
  text.gsub!(/\r?\n/, "\n")
  text.each_line do |line|
    name = line.split[0]
    val = line.split[1].to_f.round(2)
    BrowserStack.logger.info("[minified_essential_time] Name : #{name}, Time : #{val}")
    instrumentation_data[name] = val
  end
  BrowserStack.logger.info("[minified_essential_time] instrumentation_data is #{instrumentation_data}")

  FileUtils.rm_f(minified_instrumentation_file)

  # Using user_os to store genre because it was always null for this kind
  BrowserStack::Zombie.push_logs(
    "ios_minified_cleanup",
    (Time.now.to_i - min_flow_start_time).to_s,
    zombie_data.merge("data" => instrumentation_data.to_s, "user_os" => zombie_data["genre"])
  )
end

def unclean_lockdown_file(device)
  "/tmp/unclean_lockdown_#{device}"
end

def unclean_not_on_ideviceinfo_file(device, server_config)
  "#{server_config['state_files_dir']}/unclean_not_on_ideviceinfo_#{device}"
end

def log_last_cleanup_stats(device, cleanup_type, execution_time)
  last_cleanup_stats_file = "#{STATE_FILES_DIR}/last_cleanup_stats_#{device}.json"
  last_cleanup_file = "#{STATE_FILES_DIR}/last_cleanup_#{device}"
  last_cleanup_stats = { cleanup_type: cleanup_type, cleanup_time: "#{execution_time} seconds" }
  File.open(last_cleanup_stats_file, "w") { |f| f.write(last_cleanup_stats.to_json) }
  FileUtils.touch(last_cleanup_file)
end

def can_trigger_full_cleanup?(device)
  mdm_cleanup_manager = FullCleanup::MDMCleanupManager.new(device, BrowserStack.logger)
  mdm_cleanup_manager.device_eligible?
end

is_minified_flow = device_state.minimized_cleanup_reserved_file_present?
minified_instrumentation_file = "/tmp/minified_flow_instrumentation_#{device}.txt"

begin
  BrowserStack.logger.info("Cleanup: #{cleanup_type} Started for device: #{device}. Attempt: #{cleanup_count}. Minified Flow : #{is_minified_flow}")

  recover_device = RecoverDevice.new(device, device_configuration['device_ecid'])
  begin
    recover_device.attempt if cleanup_type != "full_cleanup"
  rescue RecoverDevice::DeviceNotOnIdeviceInfo,
         RecoverDevice::DeviceNotOnUSB,
         RecoverDevice::DeviceCtlError,
         RecoverDevice::RecoverDeviceTimedOut => e
    if File.exists?(unclean_lockdown_file(device))
      FileUtils.rm(unclean_lockdown_file(device))
      FileUtils.touch(unclean_not_on_ideviceinfo_file(device, server_config))
    end

    raise MobileCheckException, e.message
  rescue RecoverDevice::DeviceInLockdown => e
    if File.exists?(unclean_not_on_ideviceinfo_file(device, server_config))
      FileUtils.rm(unclean_not_on_ideviceinfo_file(device, server_config))
      FileUtils.touch(unclean_lockdown_file(device))
    end
    raise MobileCheckException, e.message
  end

  if device_state.offline_mode_file_present? # offline mode needs to be disabled even for minified flow otherwise cleanup would be unsuccessful
    BrowserStack.logger.info("Disabling Offline Mode in Cleanup")
    ip = File.read '/usr/local/.browserstack/whatsmyip'
    pfctl = PFCTLHelper.new(ip, device, session_id, session_genre)
    pfctl.disable_offline_mode
    BrowserStack::Zombie.push_logs("ios-offlinemode-cleanup", nil, { "data" => "Online", "device" => device, "session_id" => session_id, "user_os" => session_genre })
  end

  if CustomMDMManager.is_custom_mdm_device?(device)
    iphone = BrowserStack::IPhone.new(device_configuration, device)
    iphone.check_and_clear_passcode
  end

  if device_state.dedicated_device_file_present? && device_name.include?('iPad')
    BrowserStack.logger.info("Resetting network simulation for dedicated cloud iPad")
    privoxy_port = device_configuration['selenium_port'].to_i + server_config["privoxy_listen_port_offset"].to_i
    simulator = NetworkSimulator.new(device, privoxy_port)
    simulator.reset_network_simulation
  end

  AppPercy::CLIManager.force_stop(device_port)
  if is_minified_flow
    minified_cleanup_flow(device, device_state, cleanup_type, session_id, minified_instrumentation_file, min_flow_start_time)
  else
    abort_if_cleanup_not_possible(device)

    count = BrowserStack::OSUtils.execute("ps aux | grep #{device} | grep cleanup.rb | grep -v \"grep\\|#{$PROCESS_ID}\" | wc -l").strip

    if count.to_i > 0
      BrowserStack::Zombie.push_logs("multiple_cleanups", count, zombie_data)
      influxdb_client.event(device, 'multiple-cleanups', subcomponent: 'cleanup-script')
    end
    privoxy_port = device_configuration['selenium_port'].to_i + server_config["privoxy_listen_port_offset"].to_i
    simulator = NetworkSimulator.new(device, privoxy_port)
    simulator.reset_throttling_rules

    PrivoxyManager.truncate_privoxy_logs(device)
    device_rebooted = DeviceManager.reboot_file(device)
    if File.exist?(device_rebooted)
      reboot_file_mtime_diff = Time.now - File.mtime(device_rebooted)
      if reboot_file_mtime_diff < reboot_wait_time
        wait_time = reboot_wait_time - reboot_file_mtime_diff
        BrowserStack.logger.info("Reboot in process.. Waiting for #{wait_time}")
        sleep wait_time
      end
      idevice = BrowserStack::IPhone.new(device_configuration, device)
      DeviceManager.poll_device_after_reboot(device, idevice)
      FileUtils.remove_file(device_rebooted) if File.exist?(device_rebooted)
    end

    DeviceManager.cleanup_async(device, cleanup_type, session_id)
  end

  # Stop device logger metric monitoring
  BrowserStack.logger.info("Stopping device logger metric monitoring")
  DeviceLoggerMetric.new(device, session_id).stop_monitoring

  # On a successful cleanup, this file won't exist
  if !File.exist?(cleanupdone_file)
    endtime = Time.now
    execution_time = (endtime - start_time).round
    hooter.send_time("cleanup_time", { "platform" => "ios_njb", "cleanup_type" => cleanup_type })
    BrowserStack::Zombie.push_logs("cleanup", cleanup_type, zombie_data.merge("data" => execution_time))

    if File.exist?("/tmp/upgrade_firmware_#{device}")
      if is_minified_flow  # Need to exit minified cleanup if iphone_firmware_upgrade detected
        device_state.remove_minimized_cleanup_reserved_file
        device_state.remove_preserve_app_state_reserved_file
        BrowserStack.logger.error("iphone_firmware_upgrade detected, /tmp/upgrade_firmware_#{device} file exists. Aborting reserved sessions")
        raise "#{OFFLINE_REASON_MINIFIED_PREFIX} : iphone_firmware_upgrade detected"
      end
      BrowserStack::OSUtils.execute("bash /usr/local/.browserstack/realmobile/scripts/iphone_firmware_upgrade/upgrade.sh #{device} #{device_name} #{server_config['download_endpoint']}")
      FileUtils.rm_f("/tmp/upgrade_firmware_#{device}")
    end

    if is_minified_flow
      File.open(minified_instrumentation_file, "a") { |f| f.puts "remove_session_specific_files_started #{Time.now.to_i - min_flow_start_time}" }
      BrowserStack.logger.info("[minified_essential_time] remove_session_specific_files_started #{Time.now.to_i - min_flow_start_time}")
    end

    remove_session_specific_files(device, device_configuration['debugger_port'])

    FileUtils.rm_f(DeviceManager.cleanup_requested_file(device)) # _f because it's not always there
    CleanupStatusDb::Cleanup.success(device)

    BrowserStack.logger.info("Cleanup: #{cleanup_type} Completed for device: #{device}")
  else
    # This block is reached if no exception was raised during cleanup, but the cleanupdone_file was not deleted,
    # This happens when the maximum retries are reached for cleanup.
    BrowserStack.logger.info("Cleanup: #{cleanup_type} Not Completed Successfully for device: #{device}")
  end

  # Delete this file as no longer required. A new file will be created for another cleanup.
  FileUtils.rm_f(cleanupstarted_file)

  # Push overall cleanup execution time to zombie
  endtime ||= Time.now
  overall_execution_time = (endtime - start_time).round
  BrowserStack::Zombie.push_logs(
    "ios_#{zombie_cleanup_tag}_overall_execution_time",
    "",
    zombie_data.merge("data" => overall_execution_time, "user_os" => session_genre) # Using user_os to store genre because it was always null for this kind
  )

  BrowserStack::Zombie.push_logs(
    "ios_#{zombie_cleanup_tag}_count",
    "",
    zombie_data.merge("data" => cleanup_count, "user_os" => session_genre) # Using user_os to store genre because it was always null for this kind
  )

rescue => e
  BrowserStack.logger.error("Exception in /cleanup #{device}: #{e.message} \n#{e.backtrace.join("\n")}")
  if is_minified_flow
    device_state.remove_minimized_cleanup_reserved_file
    device_state.remove_preserve_app_state_reserved_file
    BrowserStack.logger.error("!!!Aborting reserved sessions!!!")
  end

  cleanup_failure_prefix = ""
  cleanup_failure_prefix = "full cleanup " if cleanup_type == "full_cleanup"
  cleanup_failure_prefix = "first cleanup " if cleanup_type == "first_cleanup"

  DeviceManager.write_cleanup_failure_reason(device, device_name, cleanup_failure_prefix + e.message)
  BrowserStack::Zombie.push_logs("mdm-fatal-exception", e.message, { device: device }) if e.instance_of?(MdmApiException) || e.instance_of?(MdmApiFatalException)

  case e.message
  when /Lockdown error/
    BrowserStack.logger.error "Exiting cleanup until device recovers from lockdown"
  when /Not on ideviceinfo/
    BrowserStack.logger.error "Exiting cleanup until device comes on ideviceinfo"
  when /enterprise app in bad state/
    BrowserStack.logger.error "Exiting cleanup: enterprise app is in bad state"
  when /device_check not run/
    BrowserStack.logger.error "device_check not run"
  when /Jailbroken with/
    BrowserStack.logger.error "Exiting cleanup, manual intervention due to jailbreak will be needed."
  when /#{OFFLINE_REASON_NEED_LANGUAGE_RESET}/
    BrowserStack.logger.error "Exiting cleanup, #{OFFLINE_REASON_NEED_LANGUAGE_RESET}"
  when /#{OFFLINE_REASON_RE_MDM_DEVICE}/
    BrowserStack.logger.error "Exiting cleanup, #{OFFLINE_REASON_RE_MDM_DEVICE}"
  when /#{OFFLINE_REASON_FILES_APP_MISSING}/
    BrowserStack.logger.error "Exiting cleanup, #{OFFLINE_REASON_FILES_APP_MISSING}"
    if can_trigger_full_cleanup?(device)
      device_state.touch_mdm_full_cleanup_file
      BrowserStack.logger.info "Triggering Full cleanup"
      BrowserStack::HttpUtils.test_url_code("http://localhost:45671/cleanup?device=#{device}&self_cleanup=true&full_cleanup=#{cleanup_type == 'full_cleanup'}")
    end
  when /#{OFFLINE_REASON_UNABLE_TO_UNINSTALL_APP}/
    BrowserStack.logger.error "Exiting cleanup, #{OFFLINE_REASON_UNABLE_TO_UNINSTALL_APP} app"
  when /#{OFFLINE_REASON_FILES_APP_RESTRICTED}/
    BrowserStack.logger.error "Exiting cleanup, #{OFFLINE_REASON_FILES_APP_RESTRICTED}"
  when /#{OFFLINE_REASON_PLUGGED_IN_WRONG_MACHINE}/
    BrowserStack.logger.error "Exiting cleanup, #{OFFLINE_REASON_PLUGGED_IN_WRONG_MACHINE}"
  when /Device not on mdm/
    BrowserStack.logger.error "Exiting cleanup as device is not on MDM"
  when /#{OFFLINE_REASON_DEVICE_STUCK_WITH_APPLE_ID}/
    BrowserStack.logger.error "Exiting cleanup, #{OFFLINE_REASON_DEVICE_STUCK_WITH_APPLE_ID}"
    if can_trigger_full_cleanup?(device)
      device_state.touch_mdm_full_cleanup_file
      BrowserStack.logger.info "Triggering Full cleanup"
      BrowserStack::HttpUtils.test_url_code("http://localhost:45671/cleanup?device=#{device}&self_cleanup=true&full_cleanup=#{cleanup_type == 'full_cleanup'}")
    end
  when /#{OFFLINE_REASON_MINIFIED_PREFIX}/
    BrowserStack.logger.error "Exiting minified cleanup"
    raise e
  when /device is not on usb/
    BrowserStack.logger.error "Exiting cleanup and marking as manual fix as device is not on USB"
  when /No backup found/
    BrowserStack.logger.error "Exiting Full Cleanup as Backup is not found to restore"
  when /cfgutilbackupmanager doesn't implement/
    BrowserStack.logger.error "Exiting Full Cleanup as #{e.message}"
  when /#{OFFLINE_REASON_APPLE_PAY_SENSITIVE_DATA}/
    BrowserStack.logger.error "Exiting cleanup as apple pay setup is required to clear user sensitive data on device"
  when /#{OFFLINE_REASON_NOT_ON_DEVICECTL}/
    BrowserStack.logger.error "Exiting cleanup, #{OFFLINE_REASON_NOT_ON_DEVICECTL}"
  when /#{OFFLINE_REASON_DEVICE_NOT_PAIRED}/
    BrowserStack.logger.error "Exiting cleanup, #{OFFLINE_REASON_DEVICE_NOT_PAIRED}"
    if can_trigger_full_cleanup?(device)
      device_state.touch_mdm_full_cleanup_file
      BrowserStack.logger.info "Triggering Full cleanup"
      BrowserStack::HttpUtils.test_url_code("http://localhost:45671/cleanup?device=#{device}&self_cleanup=true&full_cleanup=#{cleanup_type == 'full_cleanup'}")
    end
  when /#{OFFLINE_REASON_NO_DDI}/
    BrowserStack.logger.error "Exiting cleanup, #{OFFLINE_REASON_NO_DDI}"
  when /#{OFFLINE_REASON_DEVICE_NOT_TRUSTED}/
    BrowserStack.logger.error "Exiting cleanup, #{OFFLINE_REASON_DEVICE_NOT_TRUSTED}"
  when /#{OFFLINE_REASON_APPLE_ID_PRESENT}/
    BrowserStack.logger.error "Exiting cleanup, #{OFFLINE_REASON_APPLE_ID_PRESENT}"
    if can_trigger_full_cleanup?(device)
      device_state.touch_mdm_full_cleanup_file
      BrowserStack.logger.info "Triggering Full cleanup"
      BrowserStack::HttpUtils.test_url_code("http://localhost:45671/cleanup?device=#{device}&self_cleanup=true&full_cleanup=#{cleanup_type == 'full_cleanup'}")
    end
  when /#{OFFLINE_REASON_CHROME_CLEANUP_FAILURE}/
    BrowserStack.logger.error "Exiting cleanup, #{e.message}"
    if can_trigger_full_cleanup?(device)
      device_state.touch_mdm_full_cleanup_file
      BrowserStack.logger.info "Triggering Full cleanup"
      BrowserStack::HttpUtils.test_url_code("http://localhost:45671/cleanup?device=#{device}&self_cleanup=true&full_cleanup=#{cleanup_type == 'full_cleanup'}")
    end
  else
    # Was the problem WDA not running? Device check can fix that.
    # wda_client = WdaClient.new(device_configuration['webdriver_port'])
    # BrowserStack.logger.info "Checking if WDA was running... (#{wda_client.inspect})"
    # if wda_client.running?
    #   BrowserStack.logger.info "WDA seems to be running fine"
    # else
    #   BrowserStack.logger.error " WDA not running! Asking device check to setup the device again and to retry cleanup later"
    #   DeviceManager.request_setup(device)
    #   DeviceManager.request_cleanup(device)
    #   exit! 0
    # end

    if File.exist? "/tmp/debug_flag_cleanup_failures"
      # TODO: Remove the tmp_screenshot_file code in next deploy
      tmp_screeenshot_file = "/tmp/cleanup_failure_snapshot_#{device}"
      FileUtils.rm_f tmp_screeenshot_file
      screenshot_file = "/tmp/cleanup_failure_snapshot_#{device}.png"
      FileUtils.rm_f screenshot_file

      IdeviceUtils.screenshot(device, screenshot_file)
    end

    if e.message == OFFLINE_REASON_MDM_LOCK_ERROR
      sleep_time = [5**(cleanup_count + 1), 1000].min
      BrowserStack.logger.info("Locking failed for #{@uuid}, sleepin for #{sleep_time} seconds before failing")
      sleep(sleep_time)
    end
    if BSEnv.debug?
      BrowserStack.logger.info("Not restarting cleanup as debug mode is active...")
      return
    end

    unless IdeviceUtils.apple_tv_device?(device) && e.message.downcase.include?("wda is not running")
      BrowserStack.logger.info "Rebooting device"
      # Reboot the device and wait
      DeviceManager.reboot_and_wait(device)
      BrowserStack.logger.info "Unlocking, after reboot in cleanup.rb"
      BrowserStack::IPhone.unlock_device(device, 20)
    end
    BrowserStack::HttpUtils.test_url_code("http://localhost:45671/cleanup?device=#{device}&self_cleanup=true&full_cleanup=#{cleanup_type == 'full_cleanup'}")
  end
ensure
  # Push retry count and cleanup time of latest retry to zombie
  error_message = e.nil? ? "" : e.message

  endtime ||= Time.now
  single_execution_time = endtime - this_run_start_time
  instrument_minfied_cleanup(minified_instrumentation_file, min_flow_start_time, zombie_data) if is_minified_flow

  BrowserStack.logger.info("ios_#{zombie_cleanup_tag}_single_execution_time : #{single_execution_time}")
  BrowserStack::Zombie.push_logs(
    "ios_#{zombie_cleanup_tag}_single_execution_time",
    error_message,
    zombie_data.merge("data" => single_execution_time, "user_os" => session_genre) # Using user_os to store genre because it was always null for this kind
  )
  log_last_cleanup_stats(device, cleanup_type, single_execution_time)
end
