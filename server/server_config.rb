rackup 'real_mobile.ru'

# https://www.rubydoc.info/gems/puma/Puma/DSL#prune_bundler-instance_method
# This allows us to install new gems with just a phased-restart. Otherwise you
# need to take the master process down each time.
prune_bundler

port 45671

workers 4

worker_shutdown_timeout 300

persistent_timeout 75

tag 'server'

path_to_cert = File.expand_path('/usr/local/.browserstack/ssl_cert/mobile_host_cert.crt')
path_to_key = File.expand_path('/usr/local/.browserstack/ssl_cert/mobile_host_key.key')

if File.exist?(path_to_cert) && File.exist?(path_to_key)
  puts "SSL certificate and key found, starting Puma with SSL on port 45672"
  ssl_bind '0.0.0.0', '45672', {
    cert: path_to_cert,
    key: path_to_key
  }
else
  puts "SSL certificate and key not found, unable starting Puma with SSL on port 45672"
end

# Until we update ruby version to 2.5 we will have forking issues in new mac mini
# For reference https://github.com/puma/puma/issues/1421
if /darwin/ =~ RUBY_PLATFORM
  before_fork do
    require 'fiddle'
    # Dynamically load Foundation.framework, ~implicitly~ initialising
    # the Objective-C runtime before any forking happens in Puma
    Fiddle.dlopen '/System/Library/Frameworks/Foundation.framework/Foundation'
  end
end
