require 'English'
require 'fileutils'
require 'shellwords'

require_relative 'device_manager'
require_relative '../lib/utils/osutils'
require_relative '../lib/cleanup_iphone'
require_relative '../lib/dummy_app_trust'
require_relative '../lib/permit_photos_access'
require_relative '../lib/utils/idevice_utils'
require_relative '../lib/configuration'
require_relative '../lib/utils/utils'
require_relative '../lib/utils/http_utils'
require_relative '../lib/utils/zombie'
require_relative '../lib/utils/web_driver_agent'
require_relative '../lib/utils/device_logger'
require_relative '../lib/utils/custom_mdm_manager'
require_relative '../lib/utils/xcode_utils'
require_relative '../lib/helpers/mobile_config_helper'
require_relative '../lib/apps/chrome'
require_relative '../lib/apps/launcher'
require_relative '../lib/apps/redirect'
require_relative '../lib/install_app'
require_relative '../lib/helpers/automation'
require_relative '../lib/utils/helpers'
require_relative '../lib/utils/ios_watcher'
require_relative '../lib/utils/jailbreak_detector'
require_relative '../lib/utils/wda_version'
require_relative '../scripts/wda_download'
require_relative '../lib/utils/mobile_tampered_api'
require_relative '../lib/utils/time_recorder'
require_relative '../lib/checks/check_device'
require_relative '../lib/custom_exceptions'
require_relative '../lib/cleanup_telephony'
require_relative '../config/constants'
require_relative '../lib/helpers/browserstack_app_helper'
require_relative '../lib/helpers/experiments_helper'
require_relative '../lib/helpers/wda_client'
require_relative '../lib/version'
require_relative '../lib/ios_influxdb_client'
require_relative '../lib/models/device_state'
require_relative '../lib/models/lockdown_device_state'
require_relative '../lib/overridden/string'
require_relative './cleanup_fixes'
require_relative '../lib/wrappers/cfg_util'
require_relative '../lib/helpers/timezone_helper'
require_relative '../lib/helpers/sms_helper'
require_relative '../lib/helpers/appearance_helper'
require_relative '../lib/helpers/call_logs_helper'
require_relative '../lib/helpers/passcode_helper'
require_relative '../lib/helpers/prevent_cross_site_tracking_helper'
require_relative '../lib/upgraded_device_check_state'
require_relative '../lib/helpers/safari_settings_helper'
require_relative '../lib/helpers/custom_contacts'
require_relative '../lib/helpers/battery'
require_relative '../lib/utils/socks5_forwarder'
require_relative '../lib/helpers/apple_pay_session_data'
require_relative '../lib/device_setup/developer_mode/check_and_enable'
require_relative '../lib/device_setup/appstore_installer/appstore_installer'
require_relative '../lib/apps/chromium'
require_relative '../lib/utils/devicectl'
require_relative '../lib/utils/pymobiledevice'
require_relative '../lib/helpers/apple_business_manager_helper'
require_relative '../lib/helpers/custom_certificate_helper'
require_relative '../lib/utils/idevice_ffi/language_util'
require_relative '../lib/utils/provisioning_profile_cleaner'
require_relative '../lib/helpers/data_report_helper'
require_relative '../lib/models/ios_device'
require_relative '../lib/device_setup/full_cleanup/mdm_cleanup_manager'
require_relative '../lib/device_setup/full_cleanup/full_cleanup_state'

require '/usr/local/.browserstack/mobile-common/camera_check/ios_camera_check'
require '/usr/local/.browserstack/mobile-common/camera_check/camera_timer'
require '/usr/local/.browserstack/mobile-common/camera_check/slack_client'
require '/usr/local/.browserstack/mobile-common/utils/auth_key_util'

require 'net/http'
require 'uri'
require 'json'
require 'timeout'
require 'digest'
require 'network_helper'

module BrowserStack
  class IPhone # rubocop:todo Metrics/ClassLength
    include BrowserStack::TimeRecorder
    include AuthKeyUtil

    time_methods :app_cleanup,
                 :appium_automation,
                 :automation_cleanup,
                 :bounce_testflight,
                 :check_and_install_enterprise_dummy_app,
                 :check_device_language,
                 :check_if_cameras_are_blocked,
                 :check_internet_sharing,
                 :check_lockdown,
                 :check_safari_tabs,
                 :clean_device_keychain,
                 :cleanup_device,
                 :cleanup_iphone_with_timeout,
                 :cleanup_live_testing_setup,
                 :contacts_app_cleanup,
                 :dismiss_system_popups,
                 :install_required_apps_if_not_present,
                 :install_testflight,
                 :kill_launcher_app,
                 :kill_xcode_build_and_iproxy,
                 :lock_device_from_wda_with_fallback,
                 :lock_device_successful,
                 :open_enterprise_keychain_app,
                 :open_photos_app,
                 :phased_reboot_device,
                 :post_automation_cleanup,
                 :post_reboot,
                 :pre_automation_cleanup,
                 :press_home,
                 :dedicated_automation_cleanup,
                 :dedicated_xcui_automation,
                 :restart_webkit_proxy,
                 :test_launch_appium_with_new_wda,
                 :verify_backup_preloaded_files,
                 :install_configuration_profiles,
                 :xcui_automation,
                 :reset_socks5_forwarder,
                 :remove_unnecessary_profiles,
                 :check_and_remove_custom_ca_certificates,
                 :check_and_clear_passcode,
                 :check_and_reset_date,
                 :check_and_remove_custom_certificate,
                 :check_needs_full_reset,
                 :check_replay_kit_still_running,
                 :check_device_logger_reliability,
                 :check_for_stale_mitm_process,
                 :check_wifi_status,
                 :check_allow_settings_app_disabled,
                 :cfgutil_instrumentation,
                 :get_device_details_for_app_trust,
                 :handle_jailbreaking_app,
                 :check_apple_id_present,
                 :clean_multitasking_tabs,
                 :handle_preload_media,
                 :disable_chrome_popups,
                 :instrument_available_storage,
                 :ensure_enterprise_app_uninstalled,
                 :cleanup_app_testing_files,
                 :full_cleanup_mdm,
                 :dedicated_cleanup,
                 :first_cleanup,
                 :cleanup,
                 :capture_rsd_values,
                 :post_cleanup_success,
                 :dismiss_appstore_popup,
                 :testflight_cleanup,
                 :grant_location_permission_wda,
                 :disable_low_power_mode,
                 :enable_prevent_cross_site_tracking,
                 :check_and_instrument_font_size_settings,
                 :reset_safari_settings,
                 :setup_voiceover_persistent_settings,
                 :start_stop_safari,
                 :apple_pay_sensitive_data_cleanup,
                 :run_apple_pay_cleanup,
                 :set_safari_default_browser_in_eu,
                 :launch_appium_and_stop_xcui,
                 :clean_calender_events

    time_class_methods :uninstall_wda,
                       :cleanup_telephony_files

    SAFARI_APP = "com.apple.mobilesafari".freeze
    SETTINGS_APP = "com.apple.Preferences".freeze
    APPSTORE_APP = "com.apple.AppStore".freeze
    CAMERA_APP = "com.apple.camera".freeze
    PHONE_APP = "com.apple.mobilephone".freeze
    TESTFLIGHT_APP = "com.apple.TestFlight".freeze
    FILES_APP = 'com.apple.DocumentsApp'.freeze
    DUMMY_APP_BUNDLE_ID = "com.browserstack.enterpriseDummy".freeze
    WEBDRIVERAGENT_RUNNER_BUNDLE_ID = "com.apple.test.WebDriverAgentRunner-Runner".freeze
    DUMMY_APP_PATH = "/usr/local/.browserstack/enterpriseDummy.app".freeze
    BROWSERSTACK_APP = "com.browserstack.app".freeze
    TRANSLATE_APP = "com.apple.Translate".freeze
    PASSWORD_APP = "com.apple.Passwords".freeze

    @@config = Configuration.new.all

    attr_reader :uuid, :session_file, :device_state
    alias device_id uuid

    def initialize(device_config, uuid)
      @device_config = device_config
      @uuid = uuid

      Zombie.configure

      @unclean_lockdown_file = "/tmp/unclean_lockdown_#{@uuid}"
      @unclean_not_on_ideviceinfo_file = "#{@@config['state_files_dir']}/unclean_not_on_ideviceinfo_#{uuid}"
      @unclean_bad_enterprise_app = "/tmp/unclean_bad_enterprise_app_#{uuid}"
      @dismiss_chrome_popup = Chrome.dismiss_chrome_popup_file(@@config['config_root'], uuid)
      @device_logger_popups_file = "/tmp/device_logger_reported_untrust_#{uuid}"
      @known_apps = @@config['known_apps']
      @known_app_display_names = @@config['known_app_display_names']
      @app_updates = @@config['app_updates']
      @influxdb_client = BrowserStack::IosInfluxdbClient.new(BrowserStack.logger)
      @session_file = "#{@@config['state_files_dir']}/#{uuid}_session"
      @device_state = DeviceState.new(uuid)
      @lockdown_device_state = LockdownDeviceState.new(uuid)
      @proxy_pac_url = device_state.proxy_pac_url(NetworkHelper::NetworkSetup.new.get_ip)
      @dedicated_device = @device_state.dedicated_cleanup_file_present?
      @dedicated_first_cleanup_device = DeviceManager.is_first_cleanup?(uuid) && ["ap-southeast-2c", "us-west-1d", "us-east-1e"].include?(@@config['static_conf']['sub_region'])
      @dedicated_cleanup_config = @dedicated_device ? @device_state.read_dedicated_cleanup_file.split(",").map { |x| x.gsub("\n", "").strip } : []
      # Used in cleanup to check whether to rely on device-logger to skip certain steps or not
      @device_logger_reliable = false
    end

    def ios_device
      @ios_device ||= BrowserStack::IosDevice.new(@uuid,
                                                  "iPhone",
                                                  BrowserStack.logger)
    end

    def clean_calender_events
      ios_device.clear_local_calendar_events
    rescue => e
      BrowserStack.logger.info("Error in clearing calendar events #{e.message}")
    end

    def session_params
      begin
        @session_params ||= Utils.read_json_file(@session_file)
      rescue Errno::ENOENT => e
        BrowserStack.logger.info("No session file found, Product specific tasks won't run! #{e.message}")
        @session_params ||= {}
      end
      @session_params
    end

    def genre
      @genre ||= session_params["genre"].to_s
    end

    def session_id
      @session_id ||= (session_params["live_session_id"] || session_params["automate_session_id"] || session_params["app_live_session_id"] || session_params["session_id"]).to_s
    end

    def wda_client
      @wda_client ||= WdaClient.new(@device_config['webdriver_port'])
    end

    def check_device
      @check_device ||= CheckDevice.new(device_id, REDIS_CLIENT)
    end

    def device_name
      @device_config["device_name"]
    end

    def device_type
      @device_config["device_name"].match(/iPad/i).nil? ? "iPhone" : "iPad"
    end

    def lock_device
      BrowserStack.logger.info("Locking Device")
      if lock_device_successful
        BrowserStack.logger.info("Lock device successful")
        nil
      else
        BrowserStack.logger.error("Lock device unsuccessful")
        "lock device failed"
      end
    end

    def lock_device_from_wda_with_fallback
      BrowserStack.logger.info("Locking Device with WDA")
      wda_client.lock_device(device_version)
      BrowserStack.logger.info("Lock device with WDA successful")
      nil
    rescue
      BrowserStack.logger.error("Lock device with WDA unsuccessful")
      zombie("lock_device_from_wda_with_fallback", "WDA failed, falling back to XCUI", { "genre" => genre })
      BrowserStack.logger.info("Falling back to MDM lock")
      lock_device
    end

    def unlock_device(timeout = 20)
      BrowserStack::IPhone.unlock_device(uuid, timeout)
    end

    # This is only pressing the home button. Check ensure_homescreen for
    # ensuring on homescreen and disabling interactions for 5 seconds
    def press_home
      wda_client.homescreen
    end

    def launch_app(display_name, bundle_id, locale, region, retries = 0, set_browserstack_env = false)
      # This function has retries because of the race between LiveDriverAgent start and launch_app.
      if retries == 25
        BrowserStack::Zombie.push_logs("app-live-ipa-launch-failed", "After 25 retries for bundle_id: #{bundle_id} display_name: #{display_name}", { "session_id" => session_id, "device" => @uuid })
        return
      end

      locale ||= "en-US"
      region ||= "en-US"

      begin
        if set_browserstack_env
          wda_client.launch_apps_with_locale([bundle_id], locale, region, "no", "yes")
        elsif can_be_launched_by_name?(display_name, locale, region)
          wda_client.launch_app(display_name)
        else
          wda_client.launch_apps_with_locale([bundle_id], locale, region)
        end
      rescue => e
        BrowserStack.logger.error("error in launching #{e.message}")
        sleep 2
        launch_app(display_name, bundle_id, locale, region, retries + 1, set_browserstack_env)
      end
    end

    def self.kill_app(uuid, app_bundle_id)
      # Check if instruments is supported
      if less_than_ios_12?(uuid)
        BrowserStack.logger.info "Killing app with instruments"
        OSUtils.execute("/usr/local/bin/gtimeout -s KILL 10 /usr/bin/instruments -t \"Activity Monitor\" -l 100 -D kill_iphone_#{uuid}_#{app_bundle_id} -w #{uuid} #{app_bundle_id}; rm -rf kill_iphone_#{uuid}_#{app_bundle_id}.trace")
      else
        BrowserStack.logger.info "Killing app with wda"
        idevice = get_current_device(uuid)
        begin
          idevice.wda_client.kill_apps([app_bundle_id])
        rescue => e
          BrowserStack.logger.error "Failed to kill #{app_bundle_id} app with error #{e.message} => uuid : #{uuid}"
        end
      end
    end

    def self.unlock_device(uuid, timeout = 20)
      # Check if instruments is supported
      if less_than_ios_12?(uuid)
        unlock_using_instruments(uuid, timeout)
      else
        unlock_using_wda(uuid)
      end
    end

    def self.get_current_device(uuid)
      current_device_config = DeviceManager.device_configuration_check(uuid)
      IPhone.new(current_device_config, uuid)
    end

    def self.unlock_using_instruments(uuid, timeout)
      BrowserStack.logger.info("Using instruments to unlock device")
      BrowserStack.logger.info("unlocking device #{uuid} with timeout #{timeout}")
      unlock_command = "/usr/local/bin/gtimeout -s KILL #{timeout} /usr/bin/instruments -v -t \"Activity Monitor\" -l 100 -D kill_iphone -w #{uuid}"
      process_out = OSUtils.execute("#{unlock_command} com.apple.Preferences 2>&1")
      process_termsig = $CHILD_STATUS.termsig.to_s
      process_exitstatus = $CHILD_STATUS.exitstatus.to_s
      process_exited = $CHILD_STATUS.exited?

      # This is a good indicator of phones that fail to unlock. Monitoring it,
      # maybe we can even raise an exception (failed to unlock) in the future
      # if it's reliable enough.
      if process_out.include?('Specified target process is invalid: com.apple.Preferences')
        BrowserStack.logger.warn('Possible failure while trying to unlock the device!')
        BrowserStack::Zombie.push_logs("possible_failure_to_unlock", "", { "device" => uuid })
      end

      if ["255", "250"].include?(process_exitstatus)
        BrowserStack.logger.info("Retrying unlock with com.apple.AppStore")
        OSUtils.execute("#{unlock_command} com.apple.AppStore")
        process_termsig = $CHILD_STATUS.termsig.to_s
        process_exitstatus = $CHILD_STATUS.exitstatus.to_s
        process_exited = $CHILD_STATUS.exited?
      end

      offline_reason = nil
      if !["", "0"].include?(process_termsig) || (process_exited && !["0", "253"].include?(process_exitstatus))
        BrowserStack.logger.info("unlocking device for #{uuid} returned termsig --#{process_termsig}-- and exitstatus --#{process_exitstatus}--")
        BrowserStack::Zombie.push_logs("unlock-device-stats", process_exitstatus, { "device" => uuid, "data" => timeout, "url" => process_termsig })
        influxdb_client = BrowserStack::IosInfluxdbClient.new(BrowserStack.logger)
        influxdb_client.event(uuid, 'unlock-device-failed', is_error: true)
        offline_reason = "unlock device failed"
      end
      `rm -rf kill_iphone.trace`
      offline_reason
    end

    def self.unlock_using_wda(uuid)
      BrowserStack.logger.info("Using wda to unlock device")
      idevice = get_current_device(uuid)
      idevice.wda_client.unlock_device
    end

    def self.kill_safari_using_wda(uuid)
      BrowserStack.logger.info("Using wda to kill safari")
      idevice = get_current_device(uuid)
      idevice.wda_client.kill_apps(["com.apple.mobilesafari"])
    end

    def self.start_safari_using_wda(uuid)
      BrowserStack.logger.info("Using wda to start safari")
      idevice = get_current_device(uuid)
      idevice.wda_client.launch_app_with_bundle_id("com.apple.mobilesafari")
    end

    def self.uninstall_wda(device)
      BrowserStack.logger.info("Uninstalling WebDriverAgentRunner-Runner")
      IdeviceUtils.uninstall_app(device, WEBDRIVERAGENT_RUNNER_BUNDLE_ID)

      response = nil
      if IdeviceUtils.check_app_with_bundle_id_exists(device, WEBDRIVERAGENT_RUNNER_BUNDLE_ID)
        response = "failed to uninstall wda"
        BrowserStack.logger.error("Could not uninstall WebDriverAgentRunner for uid: #{device}")
      end
      response
    end

    def dismiss_appstore_popup(appium_port)
      BrowserStack.logger.info("Allowing Location Services for AppStore")
      cleanup_iphone.dismiss_appstore_popup
    end

    def already_dismissed_file
      # File to check if the location services Allow/Disallow popup is settled or not
      # File exists => no need to run automation to accept the alert
      "#{@@config['config_root']}/already_dismissed/already_dismissed_#{uuid}"
    end

    def kill_all_apps
      BrowserStack.logger.info "kill_all_apps called"
      all_installed_apps = begin
        IdeviceUtils.list_user_installed_apps(uuid)
      rescue
        []
      end
      push_to_cls(session_params, "kill_apps", '', { "kill_apps" => all_installed_apps.length })
      if genre == "app_live_testing"
        Utils.send_to_eds({
          session_id: session_id,
          product: {
            kill_app_count: 1
          }
        }, EdsConstants::APP_LIVE_TEST_SESSIONS, true, req_params: session_params)
      end
      begin
        wda_client.kill_apps(all_installed_apps)
      rescue => e
        BrowserStack.logger.error "Failed to kill apps #{all_installed_apps} with error #{e.message}"
      end
    end

    def launch_app_with_locale(locale, region, set_browserstack_env = false)
      BrowserStack.logger.info "launch_app_with_locale called"
      all_installed_apps = begin
        IdeviceUtils.list_user_installed_apps(uuid)
      rescue
        []
      end
      begin
        start_time = Time.now
        Utils.write_to_file("/tmp/#{uuid}_current_locale", "#{locale},#{region}")
        set_browserstack_env = set_browserstack_env ? "yes" : "no"
        wda_client.launch_apps_with_locale(all_installed_apps, locale, region, "yes", set_browserstack_env, 50)
        push_to_cls(session_params, "locale_change", '', { "locale_change" => "#{locale}, #{region}", "time_taken" => Time.now - start_time })
        if genre == "app_live_testing"
          Utils.send_to_eds({
            session_id: session_id,
            product: {
              language_change_count: 1
            }
          }, EdsConstants::APP_LIVE_TEST_SESSIONS, true)
        end
      rescue => e
        BrowserStack.logger.error "Failed to launch apps with locale for #{all_installed_apps} with error #{e.message}"
      end
    end

    def relaunch_app_with_locale(bundle_id)
      BrowserStack.logger.info "Request received from device-logger to relaunch"
      locale = begin
        Utils.read_first_line("/tmp/#{uuid}_current_locale")
      rescue
        ""
      end
      if !locale.empty? && locale != "en-US,en-US"
        all_installed_apps = begin
          IdeviceUtils.list_user_installed_apps(uuid)
        rescue
          []
        end
        language, region = locale.split(',')
        if all_installed_apps.include?(bundle_id) && !language.nil? && !region.nil?
          begin
            BrowserStack.logger.info "relaunch_app_with_locale called for #{bundle_id}"
            wda_client.launch_apps_with_locale([bundle_id], language, region)
            Utils.notify_pusher("app_tapped_with_locale", session_params, uuid)
            push_to_cls(session_params, "app_locale_flicker", "", { "locale" => locale, "bundle_id" => bundle_id })
          rescue => e
            BrowserStack.logger.error "Failed to relaunch app with locale for #{bundle_id} with error #{e.message}"
          end
        end
      end
    end

    def percy_minified_cleanup(device)
      minified_flow_instrumentation_file = "/tmp/minified_flow_instrumentation_#{device}.txt"
      File.open(minified_flow_instrumentation_file, "w") do |f|
        f.puts "percy safari cleanup started #{Time.now.to_i}"
        BrowserStack.logger.info("[minified_essential_time] safari_cleanup_started #{Time.now.to_i}")
        install_xcuit_test_app(device)
        cleanup_iphone.safari_cleanup
        f.puts "success completed"
      end
    end

    def install_xcuit_test_app(device)
      is_installed = BrowserStackAppHelper.browserstack_test_suite_present?(device)
      unless is_installed
        BrowserStack.logger.info "Installing browserStack test suite"
        BrowserStackAppHelper.check_and_install_browserstack_test_suite(device)
      end
    end

    # rubocop:todo Metrics/AbcSize
    def minified_cleanup(cleanup_type, min_flow_start_time, perform_aut_cleanup, perform_aa_cleanup)
      minified_flow_instrumentation_file = "/tmp/minified_flow_instrumentation_#{uuid}.txt"
      # Remove the device logs file before cleanup of device
      FileUtils.rm_f("/var/log/browserstack/app_log_#{uuid}.log")

      check_device_logger_reliability

      File.open(minified_flow_instrumentation_file, "a") do |f|
        f.puts "check_needs_full_reset_started #{Time.now.to_i - min_flow_start_time}"
        BrowserStack.logger.info("[minified_essential_time] check_needs_full_reset_started #{Time.now.to_i - min_flow_start_time}")
        check_needs_full_reset

        f.puts "check_lockdown_started #{Time.now.to_i - min_flow_start_time}"
        BrowserStack.logger.info("[minified_essential_time] check_lockdown_started #{Time.now.to_i - min_flow_start_time}")
        check_lockdown

        if perform_aa_cleanup
          f.puts "app_cleanup_started #{Time.now.to_i - min_flow_start_time}"
          BrowserStack.logger.info("[minified_essential_time] app_cleanup_started #{Time.now.to_i - min_flow_start_time}")
          minified_app_cleanup unless device_state.preserve_app_state_reserved_file_present?
        end

        if perform_aut_cleanup
          f.puts "safari cleanup started #{Time.now.to_i - min_flow_start_time}"
          BrowserStack.logger.info("[minified_essential_time] safari_cleanup_started #{Time.now.to_i - min_flow_start_time}")
          cleanup_iphone.safari_cleanup

          f.puts "downloads cleanup started #{Time.now.to_i - min_flow_start_time}"
          BrowserStack.logger.info("[minified_essential_time] downloads_cleanup_started #{Time.now.to_i - min_flow_start_time}")
          files_cleanup if need_files_app_cleanup?
        end

        f.puts "check_allow_settings_app_disabled_started #{Time.now.to_i - min_flow_start_time}"
        BrowserStack.logger.info("[minified_essential_time] check_allow_settings_app_disabled_started #{Time.now.to_i - min_flow_start_time}")
        check_allow_settings_app_disabled

        f.puts "archive_and_truncate_appium_logs_started #{Time.now.to_i - min_flow_start_time}"
        BrowserStack.logger.info("[minified_essential_time] archive_and_truncate_appium_logs_started #{Time.now.to_i - min_flow_start_time}")
        DeviceManager.archive_and_truncate_appium_logs(@device_config["selenium_port"])
      end

      FileUtils.rm_f(DeviceManager.session_start_file(uuid).to_s)
    rescue => e
      BrowserStack.logger.error "Error in cleanup - #{e.message} Trace: #{e.backtrace}"
      raise e
    end
    # rubocop:enable Metrics/AbcSize

    def need_passcode_cleared?
      device_state.passcode_file_present? || IdeviceUtils.has_passcode?(uuid) || CustomMDMManager.is_custom_mdm_device?(uuid)
    end

    def check_and_clear_passcode
      retries_left = 3
      begin
        cleanup_iphone.clear_passcode if need_passcode_cleared?
      rescue => e
        retries_left -= 1
        retry if retries_left > 0
        raise "manual fix required: Device has passcode set - #{e}"
      end
    end

    def check_and_remove_custom_certificate
      return unless device_state.custom_certificate_installed_file_present?

      custom_certificate_helper = CustomCertificate.new(uuid, "cleanup")
      custom_certificate_helper.remove_pfx_certificate
    end

    def check_and_remove_custom_ca_certificates
      return unless File.exists?("#{STATE_FILES_DIR}/app_live_custom_certificate_#{uuid}.mobileconfig")

      custom_certificate_helper = CustomCertificate.new(uuid, "cleanup")
      custom_certificate_helper.remove_all_custom_ca_certs
    rescue => e
      BrowserStack.logger.error "Error in removing ca certificates - #{e.message} Trace: #{e.backtrace}"
      Zombie.push_logs("ios_remove_all_ca_certificates_failed", e.message.to_s, { "device" => @uuid })
      raise e
    end

    def check_and_reset_date
      cleanup_iphone.date_cleanup
    rescue => e
      BrowserStack.logger.error "Error in date cleanup - #{e.message} Trace: #{e.backtrace}"
    end

    def check_for_stale_mitm_process
      BrowserStack.logger.info "Checking for stale mitm process"
      mitm_process_running_ports = BrowserStack::OSUtils.execute("ps aux | grep mitm | sed -n 's/.*--listen-port \\([0-9]*\\).*/\\1/p'").split("\n")
      current_device_mitm_port = "4#{@device_config['port']}"
      if mitm_process_running_ports.include?(current_device_mitm_port)
        BrowserStack.logger.info "Stale mitm process found, killing..."
        BrowserStack::Zombie.push_logs("stale-mitm-in-cleanup", "", { "device" => device_id  })
        BrowserStack::OSUtils.execute("ps aux | grep mitm | grep #{current_device_mitm_port} | grep -v grep | awk '{print $2}' | xargs kill -9")
      end
    end

    def cleanup(cleanup_type) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength, Metrics/PerceivedComplexity, Metrics/CyclomaticComplexity
      check_and_clear_passcode
      check_and_reset_date if @device_state.custom_date_file_present?
      check_and_remove_custom_certificate
      check_and_remove_custom_ca_certificates
      # Remove the device logs file before cleanup of device
      FileUtils.rm_f("/var/log/browserstack/app_log_#{uuid}.log")
      check_needs_full_reset
      check_lockdown
      check_device.check_platform_version_consistency(device_version)
      check_replay_kit_still_running

      # Important: Run these only after checking if device isn't in lockdown otherwise they will
      # give some false positive results
      check_device.check_device_supervised
      check_device_logger_reliability
      check_device_language
      check_for_stale_mitm_process
      check_internet_sharing if @@config['internet_sharing_enabled']
      check_wifi_status if device_version.to_f <= 14.5

      # removing auth file for this session
      device_name == "iPhone14,5" && delete_auth_key_state_file(session_id, @uuid)

      check_allow_settings_app_disabled
      reset_socks5_forwarder

      cfgutil_instrumentation if cfgutil_instrumentation_required?

      dismiss_system_popups
      device_details = get_device_details_for_app_trust

      if device_details[:device_type] == "iPhone"
        clean_call_records = File.exist?(BrowserStack::IPhone.clean_call_records_file(uuid))
        clean_message_records = File.exist?(BrowserStack::IPhone.clean_message_records_file(uuid))
      end
      telephony = CleanupTelephony.new(uuid, device_version, @device_config["device_name"], @device_config["selenium_port"],
                                       @device_config["webdriver_port"], clean_call_records, clean_message_records)
      begin
        # Call should be disconnected before UI Automation is run.
        # Exception is rescued so that cleanup doesnt break in case iproxy is not up as it will
        # be handled in device restart anyway.
        telephony.disconnect_call
      rescue => e
        BrowserStack.logger.info "Failed to disconnect call: #{e.message}"
      end

      # Locking device before cleanup to ensure no popups are present
      raise OFFLINE_REASON_MDM_LOCK_ERROR if device_version.to_f >= 18.3 && !lock_device_from_wda_with_fallback.nil?

      DeveloperMode.check_and_enable(@uuid) if device_version.to_i >= 16
      cleanup_live_testing_setup
      kill_xcode_build_and_iproxy

      # Restore default appium plist and uninstall non-default wda.
      # During cleanup, the default wda will get installed on the device.
      appium_server.start_server_for_version(@@config['default_appium_version'], true)

      BrowserStack::IPhone.uninstall_wda(uuid)

      wda_version = @@config['default_wda_version'] || @@config['default_appium_version']
      wda_checker = WDAVersion.new(device_id, wda_version, device_version)

      if wda_checker.outdated_version?
        BrowserStack.logger.warn("Outdated WDA version for the default appium: #{@@config['default_appium_version']}")
        wda_checker.install_wda_version(force_install: true)
      elsif @device_state.resign_wda_file_present?
        # We touch this from appium_server.rb in case Appium is unable to install and run WDA
        if @device_state.resign_wda_file_updated_at < wda_checker.wda_signed_at
          BrowserStack.logger.warn("Ignoring resign WDA file as WDA is already resigned")
          # The resign will be deleted automatically once Appium runs successfully
        else
          BrowserStack.logger.warn("Resign WDA file is present, will re-setup WDA")
          wda_checker.install_wda_version(force_install: true)
        end
      end

      DeviceManager.archive_and_truncate_appium_logs(@device_config["selenium_port"])

      File.delete(BrowserStack::IPhone.app_live_free_user_file(uuid)) if File.exists?(BrowserStack::IPhone.app_live_free_user_file(uuid))

      handle_jailbreaking_app if device_version.to_f < 17.0

      # Installs BrowserStack app and Runner app
      BrowserStackAppHelper.handle_cleanup_tasks(uuid)

      # Locking device before cleanup to ensure no popups are present
      raise OFFLINE_REASON_MDM_LOCK_ERROR if device_version.to_f < 18.3 && !lock_device_successful

      check_apple_id_present

      cleanup_start_time = Time.now

      if device_state.mobile_config_installed_file_present?
        mobile_conf_helper = BrowserStack::MobileConfigHelper.new(uuid, @device_config)
        mobile_conf_helper.remove_mobile_configs
      end

      app_cleanup

      contacts_app_cleanup if ["live_testing", "app_live_testing", "app_automate", nil].include?(genre)

      BrowserStack.logger.info "Cleanup Genre: #{genre}"

      telephony.cleanup

      BrowserStack.logger.info "AppStore was used: #{was_used_in_session?('appstore')}"

      clean_multitasking_tabs if device_state.multitasking_view_opened_file_present?

      # Chrome, Launcher apps are required for the cleanup automation to run
      # so check for them before running cleanup
      install_required_apps_if_not_present

      handle_preload_media

      cleanup_device

      dismiss_appstore_popup(@device_config['selenium_port']) unless File.exists?(already_dismissed_file)
      clean_device_keychain if ["app_automate"].include?(genre) || was_used_in_session?("developer_keychain")

      # Run this after cleanup_device as cleanup_device has the UI automation to enable Safari web inspector
      # which is required for ios webkit debug proxy to work
      restart_webkit_proxy if need_restart_webkit_proxy?

      disable_chrome_popups

      cleanup_iphone.chrome_cleanup if need_chrome_cleanup?

      check_safari_tabs

      # clear local calendar events even though calendar app is blocked
      # Make sure this function is not called directly after a function which
      # creates an IdeviceUtils object. If due to some constraints you still have to,
      # then please make sure the time interval is >5secs b/w your function and this(clean_calender_events) function.
      clean_calender_events

      crash_logs_cleanup_thread = cleanup_iphone.crash_logs_cleanup

      cleanup_iphone.check_and_enforce_configuration_profiles

      kill_launcher_app if ["app_automate", "live_testing", "automate", "selenium", "js_testing"].include?(genre)
      cleanup_iphone.geoguard_cleanup if device_version.to_f < 17.0
      cleanup_iphone.message_notification_cleanup

      phased_reboot_device

      # TODO: Move this into automation phase of cleanup
      begin
        check_if_cameras_are_blocked
      rescue => e
        BrowserStack.logger.error "Error doing the camera_check: #{e.message}, #{e.backtrace}"
      end

      # Check and Instrumentation for available storage
      # More info here: https://browserstack.atlassian.net/browse/MOBPE-371
      instrument_available_storage

      # TODO: Move this into automation phase of cleanup
      open_photos_app if @media_files_preloaded
      cleanup_iphone.clear_clipboard if device_version.to_f >= 15.0

      attempts = 0
      begin
        attempts += 1
        press_home
      rescue => e
        BrowserStack.logger.info("error on press home while wda is on foreground, retrying: #{e}")
        retry if attempts < 2
        raise e
      end
      lock_device_from_wda_with_fallback # Locking device after cleanup to conserve battery

      BrowserStack.logger.info "Cleanup successful for #{uuid}. Cleanup time = #{Time.now - cleanup_start_time} seconds."
      if device_version.to_i >= IOS_WATCHER_MIN_IOS_VERSION
        # clean at end of cleanup
        IosWatcher.clear_logs(uuid)
        device_state.remove_watcher_unreliable_file
        device_state.remove_watcher_unreliable_off_usb_file
        device_state.remove_end_call_optimised_file
      end

      ensure_enterprise_app_uninstalled
      cleanup_app_testing_files
      FileUtils.rm_f(DeviceManager.session_start_file(uuid).to_s)
      @device_state.remove_skip_enterprise_dummy_automation_file

      begin
        crash_logs_cleanup_thread&.join
      rescue => e
        BrowserStack.logger.error "Thread execution failed: #{e.message} Trace: #{e.backtrace}"
        BrowserStack::Zombie.push_logs(
          "crash-logs-thread-execution-failed",
          "Crash logs thread execution failed: #{e.message}",
          { "device" => device_id , "session_id" => session_id }
        )
        raise "Crash logs thread execution failed: #{e.message}"
      end
    rescue => e
      BrowserStack.logger.error "Error in cleanup - #{e.message} Trace: #{e.backtrace}"
      CleanupFixes.handle_cleanup_exception(e, uuid)
      raise e
    ensure
      cleanup_iphone.save_completed_steps_state
    end

    def full_cleanup_mdm
      BrowserStack.logger.info "Full Cleanup MDM: Starting"
      FullCleanup::MDMCleanupManager.new(uuid, BrowserStack.logger).perform
    rescue => e
      BrowserStack.logger.info "Full Cleanup MDM: Error occured: #{e}"
      raise e
    end

    def first_cleanup
      BrowserStack.logger.info "Full Cleanup MDM: Starting First Cleanup"
      FullCleanup::MDMCleanupManager.new(uuid, BrowserStack.logger).first_cleanup
    rescue => e
      BrowserStack.logger.info "Full Cleanup MDM: Error occured in First Cleanup: #{e}"
      raise e
    end

    def full_cleanup # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
      BrowserStack.logger.info "Full Cleanup: Starting"
      FileUtils.rm_f("/var/log/browserstack/app_log_#{uuid}.log")
      check_lockdown
      check_device.check_platform_version_consistency(device_version)

      DeviceManager.archive_and_truncate_appium_logs(@device_config["selenium_port"])

      cleanup_live_testing_setup
      kill_xcode_build_and_iproxy

      cleanup_start_time = Time.now

      # Triggering Erase and Restore Flow
      cleanup_iphone.erase_and_restore

      appium_server.start_server_for_version(@@config['default_appium_version'], true)
      BrowserStack::IPhone.uninstall_wda(uuid)

      # Installs BrowserStack app and Runner app
      BrowserStackAppHelper.handle_cleanup_tasks(uuid)
      device_details = get_device_details_for_app_trust

      # Force Re-Enroll to MDM and Reinstall the proxy profile as it varies from device to device in URL Field post restore
      # Since these are in cleanup_iphone, they will not be executed again if they are successful once
      cleanup_iphone.force_re_enroll_to_mdm("Full Cleanup")
      cleanup_iphone.install_proxy_profile("Full Cleanup")

      cleanup_iphone.install_rigid_restrictions_profile

      begin
        result = check_and_install_enterprise_dummy_app(device_details, @@config["dist_name"])
        unless result
          FileUtils.touch(@unclean_bad_enterprise_app)
          raise "enterprise app in bad state"
        end
      rescue => e
        BrowserStack.logger.info "Full Cleanup: [#{uuid}] Failed to install and trust enterprise app: #{e.message}"
        raise e
      end

      if device_state.mobile_config_installed_file_present?
        mobile_conf_helper = BrowserStack::MobileConfigHelper.new(uuid, @device_config)
        mobile_conf_helper.remove_mobile_configs
      end

      install_required_apps_if_not_present

      # Install Necessary Apps Like Files, Translate and testflight apps through MDM
      handle_app_store_sign_in
      # TODO: Add More System Apps to this list that we unblock in future to reinstall them
      cleanup_iphone.install_first_party_system_apps(["files", "apple_wallet", "translate"], install_via: :MDM)

      pre_automation_cleanup

      if device_version.to_i == 10 || Gem::Version.new(device_version) >= Gem::Version.new(14.2)
        settings_driver = test_launch_appium_with_new_wda
        if BrowserStackAppHelper.browserstack_test_suite_present?(device_id) && Gem::Version.new(device_version) >= Gem::Version.new(14.2)
          # We also need to uninstall completely the xcui suite
          IdeviceUtils.uninstall_app(device_id, BrowserStackAppHelper.bstack_test_app_bundle_id)
        end
      end

      appium_automation(settings_driver)

      post_automation_cleanup

      restart_webkit_proxy

      # The notifications setting is only activated after the we open app and handle notifications popup upon fresh install
      testflight_automation = Automation::TestFlight.new(uuid, @device_config)
      testflight_automation.initial_launch

      # TODO: Move this into automation phase of cleanup
      begin
        check_if_cameras_are_blocked
      rescue => e
        BrowserStack.logger.error "Full Cleanup: Error doing the camera_check: #{e.message}, #{e.backtrace}"
      end

      # Opening a driver here to have WDA connection open for direct calls
      appium_server.driver

      press_home

      BrowserStack.logger.info "Full Cleanup: Successful for #{uuid}. Cleanup time = #{Time.now - cleanup_start_time} seconds."
      File.delete(BrowserStack::IPhone.app_live_free_user_file(uuid)) if File.exists?(BrowserStack::IPhone.app_live_free_user_file(uuid))
      cleanup_app_testing_files
      device_check_state = BrowserStack::UpgradedDeviceCheckState.new(uuid)
      check_allow_settings_app_disabled
      FileUtils.rm_f(DeviceManager.session_start_file(uuid).to_s)
      system_apps_present?
      apple_id_cleanup
      device_state.remove_app_store_login_file
    rescue => e
      BrowserStack.logger.error "Full Cleanup: Error in cleanup - #{e.message} Trace: #{e.backtrace}"
      CleanupFixes.handle_cleanup_exception(e, uuid)
      raise e
    ensure
      cleanup_iphone.save_completed_steps_state
    end

    def backup_installed_apps_data(uuid)
      BrowserStack.logger.info("ALL_INSTALLED_APPS file transfer begins")
      FileUtils.cp("#{TMP_DIR_PATH}/#{uuid}_installed_apps/ALL_INSTALLED_APPS", "#{@@config['state_files_dir']}/#{uuid}_installed_apps_data")
    rescue => e
      BrowserStack.logger.error("ALL_INSTALLED_APPS file absent #{e.message}")
    end

    def dedicated_cleanup # rubocop:todo Metrics/AbcSize, Metrics/MethodLength, Metrics/PerceivedComplexity, Metrics/CyclomaticComplexity
      # Separate cleanup method for dedicated devices based on state file.
      # We perform fewer steps than normal cleanup to maintain device state across session and reduce cleanup times.
      # Cleanup steps that we skip are mentioned here https://browserstack.atlassian.net/wiki/spaces/PROD/pages/3567976821/iOS+Cleanup+Control+for+Private+Devices.
      DeveloperMode.check_and_enable(uuid) if @dedicated_first_cleanup_device && device_version.to_i >= 16

      backup_installed_apps_data(uuid)
      check_and_clear_passcode
      # Remove the device logs file before cleanup of device
      FileUtils.rm_f("/var/log/browserstack/app_log_#{uuid}.log")
      check_lockdown
      check_device.check_platform_version_consistency(device_version)
      check_replay_kit_still_running

      # Important: Run these only after checking if device isn't in lockdown otherwise they will
      # give some false positive results
      check_device.check_device_supervised
      check_device_logger_reliability
      check_device_language
      check_for_stale_mitm_process
      check_internet_sharing if @@config['internet_sharing_enabled']

      check_wifi_status

      check_allow_settings_app_disabled
      reset_socks5_forwarder

      dismiss_system_popups
      device_details = get_device_details_for_app_trust

      telephony = CleanupTelephony.new(uuid, device_version, @device_config["device_name"], @device_config["selenium_port"],
                                       @device_config["webdriver_port"], false, false)

      begin
        # Call should be disconnected before UI Automation is run.
        # Exception is rescued so that cleanup doesnt break in case iproxy is not up as it will
        # be handled in device restart anyway.
        telephony.disconnect_call
      rescue => e
        BrowserStack.logger.info "Failed to disconnect call: #{e.message}"
      end

      cleanup_live_testing_setup
      kill_xcode_build_and_iproxy

      # Restore default appium plist and uninstall non-default wda.
      # During cleanup, the default wda will get installed on the device.
      appium_server.start_server_for_version(@@config['default_appium_version'], true)
      BrowserStack::IPhone.uninstall_wda(uuid)

      DeviceManager.archive_and_truncate_appium_logs(@device_config["selenium_port"])

      File.delete(BrowserStack::IPhone.app_live_free_user_file(uuid)) if File.exists?(BrowserStack::IPhone.app_live_free_user_file(uuid))

      # Locking device before cleanup to ensure no popups are present
      raise OFFLINE_REASON_MDM_LOCK_ERROR unless lock_device_successful

      handle_jailbreaking_app

      cleanup_start_time = Time.now

      # Installs BrowserStack app and Runner app
      BrowserStackAppHelper.handle_cleanup_tasks(uuid)

      if device_state.mobile_config_installed_file_present? && !CustomMDMManager.is_custom_mdm_device?(uuid)
        mobile_conf_helper = BrowserStack::MobileConfigHelper.new(uuid, @device_config)
        mobile_conf_helper.remove_mobile_configs
      end

      BrowserStack.logger.info "Cleanup Genre: #{genre}"
      BrowserStack.logger.info "AppStore was used: #{was_used_in_session?('appstore')}"

      # Chrome, Launcher apps are required for the cleanup automation to run
      # so check for them before running cleanup
      install_required_apps_if_not_present
      wda_checker = WDAVersion.new(uuid, @@config['default_wda_version'], device_version)
      wda_checker.install_wda_version if wda_checker.outdated_version?
      wda_checker.install_wda_version(force_install: true) if device_state.resign_wda_file_present?

      cleanup_device

      if @dedicated_first_cleanup_device
        is_apple_pay_device = Secure::ApplePay.apple_pay_device?(@uuid)
        grant_location_permission_wda(driver) if !is_apple_pay_device && (Gem::Version.new(device_version) == Gem::Version.new(15.6))
      end

      dismiss_appstore_popup(@device_config['selenium_port']) unless File.exists?(already_dismissed_file)
      clean_device_keychain if ["app_automate"].include?(genre) || was_used_in_session?("developer_keychain")

      # Run this after cleanup_device as cleanup_device has the UI automation to enable Safari web inspector
      # which is required for ios webkit debug proxy to work
      cleanup_iphone.enable_safari_web_inspector if !device_state.enable_safari_web_inspector_file_present? && @dedicated_first_cleanup_device
      restart_webkit_proxy

      if @dedicated_cleanup_config.include?("clean_browser")
        check_safari_tabs

        disable_chrome_popups

        cleanup_iphone.chrome_cleanup
      end

      cleanup_iphone.crash_logs_cleanup

      cleanup_iphone.check_and_enforce_configuration_profiles

      kill_launcher_app if ["app_automate", "live_testing", "automate", "selenium", "js_testing"].include?(genre)

      phased_reboot_device

      # TODO: Move this into automation phase of cleanup
      begin
        check_if_cameras_are_blocked
      rescue => e
        BrowserStack.logger.error "Error doing the camera_check: #{e.message}, #{e.backtrace}"
      end

      press_home
      lock_device_from_wda_with_fallback # Locking device after cleanup to conserve battery

      BrowserStack.logger.info "Cleanup successful for #{uuid}. Cleanup time = #{Time.now - cleanup_start_time} seconds."
      cleanup_app_testing_files
      FileUtils.rm_f(DeviceManager.session_start_file(uuid).to_s)
    rescue => e
      BrowserStack.logger.error "Error in cleanup - #{e.message} Trace: #{e.backtrace}"
      CleanupFixes.handle_cleanup_exception(e, uuid)
      raise e
    ensure
      cleanup_iphone.save_completed_steps_state
    end

    def post_cleanup_success
      # Files should be removed here
      mark_first_cleanup_done if device_version.to_i >= 18 || @dedicated_first_cleanup_device
      device_state.remove_full_cleanup_file
      device_state.remove_mdm_full_cleanup_file
      device_state.remove_first_cleanup_file
      device_state.remove_xctest_session_timedout_file
      device_state.remove_manual_cleanup_file
      device_state.remove_device_logger_pid_file
      device_state.remove_device_logger_session_end_pid_file
      device_state.remove_wifi_enabled_file
      device_state.remove_xcuitest_result_bundle_zip_file
      device_state.remove_cleanupdone_file
      cleanup_iphone.clear_completed_steps_state
      full_cleanup_state.clear_completed_steps_state
      full_cleanup_state.clear_steps_count_file
      # Uncomment this line once we want to run full cleanup after 10 quick cleanups
      full_cleanup_state.increment_cleanup_count
    end

    def mark_first_cleanup_done
      @lockdown_device_state.set_first_cleanup_done_key(device_build_version)
    rescue => e
      BrowserStack.logger.error "Failed to set first cleanup state: #{e}"
      BrowserStack::Zombie.push_logs("first-cleanup-done-lockdown-failed", e, { "device" => uuid, "device_version" => device_version.to_f })
    ensure
      @device_state.write_to_first_cleanup_completed_file(device_build_version)
    end

    def data_reporter
      @data_reporter ||= DataReportHelper.new("cleanup-manager", device: device_id)
    end

    def cfgutil_instrumentation
      #check only for iPhone 14 and 15 devices

      #instrumentation if state file absent
      supervision_identity_details_fetched_file_present = device_state.supervision_identity_details_fetched_file_present?
      #data has been instrumented already
      return if supervision_identity_details_fetched_file_present

      cfgutil_installed = ios_device.cfgutil_installed?
      org_name = ios_device.supervision_identity_details if cfgutil_installed
    rescue => e
      BrowserStack.logger.error "Failed to check if device_uses_cfgutil_managed_profiles?, error: #{e}, #{e.backtrace.join("\n")}"
      error = e.message
      false
    ensure
      unless supervision_identity_details_fetched_file_present
        data_reporter.report({
          "action" => "cfgutil_supervision_identity_details",
          "error" => error,
          "supervision_identity_name" => org_name,
          "cfgutil_installed" => cfgutil_installed
        })
        device_state.touch_supervision_identity_details_fetched_file
      end
    end

    def cfgutil_instrumentation_required?
      devices = ['iPhone16,1', 'iPhone16,2', 'iPhone15,5', 'iPhone15,4', 'iPhone14,7', 'iPhone15,2', 'iPhone15,3', 'iPhone14,8']
      # iPhone 15 Pro, iPhone 15 Pro Max, iPhone 15 Plus, iPhone 15, iPhone 14, iPhone 14 Pro, iPhone 14 Pro Max, iPhone 14 Plus
      Gem::Version.new(device_version) >= Gem::Version.new('16.0') && devices.include?(device_name)
    end

    def remove_unnecessary_profiles
      cleaner = BrowserStack::ProvisioningProfileCleaner.new(@uuid)
      cleaner.check_and_clean
    end

    def automation_cleanup # rubocop:todo Metrics/AbcSize
      remove_unnecessary_profiles
      xcui_automation
      testflight_cleanup if Gem::Version.new(device_version) >= Gem::Version.new(11) && need_testflight_cleanup?

      driver = launch_appium_and_stop_xcui

      appium_automation(driver)

      # Opening a driver here to have WDA connection open for direct calls
      driver = appium_server.driver unless Gem::Version.new(device_version) >= Gem::Version.new("16.0")

      cleanup_iphone.reset_time_zone_to_utc if cleanup_iphone.timezone_reset_to_utc_needed?
      cleanup_iphone.disable_dark_mode if need_disable_dark_mode?
      # WDA automation to reset dark mode if turned-on on the device
      if device_state.dark_mode_file_present?
        session_id
        BrowserStack::SwitchMode.new(@uuid, @session_id).change_appearance_to(BrowserStack::SwitchMode::LIGHT_MODE)
      end
      # Configure Assistive Touch Menu for iOS 16 device if not done already for AA Shake gesture support
      cleanup_iphone.configure_assistive_touch if need_configure_assistive_touch?

      # Disable Low Power Mode
      disable_low_power_mode if device_state.low_power_mode_file_present?

      # Check if the device supports Apple Pay
      is_apple_pay_device = Secure::ApplePay.apple_pay_device?(@uuid)

      # For iOS 15.6, we need to grant location permission to WDA app in order to use driver.location - MOBFR-458
      grant_location_permission_wda(driver) if !is_apple_pay_device && (Gem::Version.new(device_version) == Gem::Version.new(15.6))
      # WDA automation to enable prevent cross-site tracking if disabled on the device
      enable_prevent_cross_site_tracking
      cleanup_iphone.reset_accessibility_settings
      # MOBPE-997 The following step has to be after reset_accssibility_settings because we want to instrument if text size was not set properly with WDA
      check_and_instrument_font_size_settings
      cleanup_iphone.reset_contacts_app if session_params["enable_mobile_addressbook_app_access"]
      reset_safari_settings

      # Adding this as part of https://browserstack.atlassian.net/browse/APS-10716 and https://browserstack.atlassian.net/browse/APS-11690 - Safari was failing on first try in case of ios 17.4 and above.
      start_stop_safari if Gem::Version.new(device_version) >= Gem::Version.new("17.4") && Gem::Version.new(device_version) < Gem::Version.new("18.0")

      apple_id_cleanup if Gem::Version.new(device_version) >= Gem::Version.new("16.0")

      # Execute cleanup automations meant for Apple Pay device if necessary
      if is_apple_pay_device
        # To be called only in case of Apple Pay Sensitive Data Cleanup Failure
        apple_pay_sensitive_data_cleanup if device_state.apple_pay_sensitive_data_cleanup_failure_file_present?

        itunes_login_status = IosMdmServiceClient.get_itunes_login_status(@uuid, REDIS_CLIENT)
        if itunes_login_status
          BrowserStack.logger.warn("AppStore or Testflight login detected on apple pay device, attempting cleanup")
          run_apple_pay_cleanup
        end

        cleanup_iphone.evaluate_apple_pay
      end
      setup_voiceover_persistent_settings
    end

    def setup_voiceover_persistent_settings
      is_voiceover_device = BrowserStack::VoiceoverHelper.voiceover_device?(@uuid)

      if is_voiceover_device
        voiceover_data_to_push = { event_name: "setVoiceOverPersistentSettings", product: "live", team: "live" }
        begin
          BrowserStack.logger.info("It is a voiceover device. Running WDA set_voiceover_persistent_settings")
          wda_client.set_voiceover_persistent_settings
          event_json = { session_id: session_id, set_voiceover_persistent_settings: "success" }
          voiceover_data_to_push.merge!({ event_json: event_json })
          Utils.send_to_eds(voiceover_data_to_push, "web_events", true)
        rescue => e
          BrowserStack.logger.error "#{e.class.name} #{e.message}: #{e.backtrace.join("\n")}"
          event_json = { session_id: session_id, set_voiceover_persistent_settings: "failed" }
          voiceover_data_to_push.merge!({ event_json: event_json })
          Utils.send_to_eds(voiceover_data_to_push, "web_events", true)
        end
      else
        BrowserStack.logger.info("Not a voiceover device. Not running WDA set_voiceover_persistent_settings")
      end
    end

    def apple_pay_sensitive_data_cleanup
      if @device_state.apple_pay_data_file_present?
        apple_pay_cleanup_obj = Secure::ApplePaySessionData.new(@session_id, @uuid)
        apple_pay_cleanup_obj.run_and_report_cleanup("settings", should_set_passcode: true, should_add_card_to_wallet: true, skip_ios_njb: true, invoked_from: "cleanup")
        device_state.remove_apple_pay_data_file
      end
      check_and_clear_passcode
      device_state.remove_apple_pay_sensitive_data_cleanup_failure_file
    end

    def run_apple_pay_cleanup
      attempts ||= 1
      apple_pay_cleanup_obj = Secure::ApplePaySessionData.new(@session_id, @uuid)

      if @device_state.apple_pay_data_file_present?
        res = apple_pay_cleanup_obj.run_and_report_cleanup("appstore")
        BrowserStack.logger.info("Apple Pay appstore and testflight cleanup after apple pay session response - #{res}")
      else
        res = apple_pay_cleanup_obj.run_and_report_cleanup("appstore", "non-apple-pay-session")
        BrowserStack.logger.info("Apple Pay appstore and testflight cleanup after non apple pay session response - #{res}")
      end
    rescue => e
      # Doing this only for iOS >= 18.0 for now, but might be useful for older versions too.
      if attempts < 2 && device_version.to_f >= 18.0
        handle_app_store_popups
        attempts += 1
        retry
      end
      raise e
    end

    def check_and_instrument_font_size_settings
      # This check will run everyday only for iOS>=15.4, and only once for lower iOS versions - MOBPE-997
      return if Gem::Version.new(device_version) < Gem::Version.new(14.0) && device_state.set_default_font_size_file_present?
      return unless device_state.set_default_font_size_file_older_than_days?(CLEANUP_STEPS_FREQ[:set_default_font_size])

      if device_version.to_f < 18.0
        BrowserStackAppHelper.check_and_install_browserstack_test_suite(@uuid)
      elsif device_font_size == Secure::AccessibilitySettingsHelper.default_font_size
        return
      end

      # Sometimes cleanup completed steps file can have this -> When cleanup ran this step and then failed afterwards at a different step
      # When cleanup is re-run after a day, it will again try to run this step and then fail because xcui test will be skipped, as its already in completed steps file
      # Hence we need to unmark this method
      cleanup_iphone.unmark_method(:set_default_font_size)
      xcui_output = cleanup_iphone.set_default_font_size
      if device_version.to_f < 18.0
        # Push to zombie if text size was modified and WDA failed to bring it back to default
        if !xcui_output.nil? && xcui_output.include?("Text size changed")
          BrowserStack.logger.warn("Text size was changed, resetting back to default")
          Zombie.push_logs("text-size-incorrect", "xcui_output: #{xcui_output}", { "device" => @uuid })
        end
        # Note - In existing cleanup we only uninstall this app for iOS >= 14.2
        IdeviceUtils.uninstall_app(device_id, BrowserStackAppHelper.bstack_test_app_bundle_id) if Gem::Version.new(device_version) >= Gem::Version.new(14.2)
      else
        # Push to zombie as text size is modified and WDA failed to bring it back to default
        BrowserStack.logger.warn("Text size was changed, resetting back to default")
        Zombie.push_logs("text-size-incorrect", "xcui_output: #{xcui_output}", { "device" => @uuid })
      end
    end

    def dedicated_automation_cleanup
      BrowserStack::CustomMDMManager.new(uuid, BrowserStack.logger).manage_setup
      dedicated_xcui_automation
      testflight_cleanup if Gem::Version.new(device_version) >= Gem::Version.new(11) && need_testflight_cleanup?
      settings_driver = launch_appium_and_stop_xcui

      appium_automation(settings_driver)

      # Opening a driver here to have WDA connection open for direct calls
      appium_server.driver

      cleanup_iphone.reset_time_zone_to_utc if cleanup_iphone.timezone_reset_to_utc_needed?

      # WDA automation to enable prevent cross-site tracking if disabled on the device
      enable_prevent_cross_site_tracking
      cleanup_iphone.reset_accessibility_settings
      # MOBPE-997 The following step has to be after reset_accssibility_settings because we want to instrument if text size was not set properly with WDA
      check_and_instrument_font_size_settings
      reset_safari_settings
    end

    def launch_appium_and_stop_xcui
      # XCUI tests and WDA are both launched via `xcodebuild test` which creates conflicts between them.
      # The following is giving the "xcode test baton" back to WDA
      # More here: https://browserstack.atlassian.net/wiki/spaces/ENG/pages/2198146733/Conflicts+between+WDA+and+XCUI+tests
      settings_driver = nil
      if device_version.to_i == 10 || Gem::Version.new(device_version) >= Gem::Version.new(14.2)
        settings_driver = test_launch_appium_with_new_wda
        should_uninstall_browserstack_test_suite = Gem::Version.new(device_version) >= Gem::Version.new(14.2) && Gem::Version.new(device_version) < Gem::Version.new(17.0)
        if should_uninstall_browserstack_test_suite && BrowserStackAppHelper.browserstack_test_suite_present?(device_id)
          # We also need to uninstall completely the xcui suite
          IdeviceUtils.uninstall_app(device_id, BrowserStackAppHelper.bstack_test_app_bundle_id)
        end
      end
      settings_driver
    end

    def check_apple_id_present
      BrowserStack.logger.info("Checking if apple id is present")
      raise OFFLINE_REASON_APPLE_ID_PRESENT if @device_state.device_logger_apple_id_present_file_present?
    end

    def handle_jailbreaking_app
      # Check if the user rooted the phone. If so,
      #  1. Ban the user immediately (we do this with the mobile_tampered endpoint)
      #  2. Abort cleanup, we don't want to destroy any clues that would help
      #     us debugging it, like browser history.
      possible_jailbreaking_app = JailbreakDetector.check_jailbroken(uuid) if @@config['platform_category'] != 'ios_njb_13_4'
      if possible_jailbreaking_app
        BrowserStack.logger.warn("JAILBREAKING APP FOUND!!! #{possible_jailbreaking_app}")
        answer = MobileTamperedAPI.ban_last_user!("/usr/local/.browserstack/state_files/#{uuid}_session")
        BrowserStack.logger.info("mobile_tampered endpoint returned #{answer.inspect}")
        BrowserStack.logger.warn("Aborting cleanup raising a exception...")
        raise "manual fix required: Jailbroken with #{possible_jailbreaking_app}"
      end
    end

    def enterprise_dummy_app_cleanup
      if @device_state.skip_enterprise_dummy_automation_file_present?
        BrowserStack.logger.info("Skipping Enterprise dummy automation")
        return
      end
      device_details = get_device_details_for_app_trust

      device_logger_reported_untrust = device_logger_reported_untrust?

      if device_logger_reported_untrust
        enterprise_app_trust = EnterpriseAppTrust.new(uuid, @device_config, device_details, @@config["dist_name"])
        device_logger_reported_untrust = false if dummy_app_present? && enterprise_app_trust.verify_app_trust
      end

      if File.exists?(@unclean_bad_enterprise_app) || !dummy_app_present? || device_logger_reported_untrust
        log_trust_automation_reason(dummy_app_present?, device_logger_reported_untrust)
        send_trust_report_to_zombie("untrusted", false) if device_logger_reported_untrust
        begin
          result = check_and_install_enterprise_dummy_app(device_details, @@config["dist_name"])
          if !result
            FileUtils.touch(@unclean_bad_enterprise_app)
            raise "enterprise app in bad state"
          else
            send_trust_report_to_zombie("trusted", true) if device_logger_reported_untrust
            FileUtils.rm_f(@unclean_bad_enterprise_app)
          end
        rescue => e
          BrowserStack.logger.info "[#{uuid}] Failed to install and trust enterprise app: #{e.message}"
          raise e
        end
      end
    end

    def grant_location_permission_wda(driver)
      BrowserStack.logger.info "Trying to fetch location using appium to check if permission needs to be granted"
      begin
        driver.driver.location
      rescue Selenium::WebDriver::Error::UnknownError => e
        case e.message
        when /An unknown server-side error occurred while processing the command. Original error: Location service must be set to 'Always' in order to retrive the current geolocation data. Please set it up manually via 'Settings > Privacy > Location Services -> WebDriverAgentRunner-Runner'/
          BrowserStack.logger.info "Enabling Location Permission for WDA app"
          driver.find_element(:name, "Privacy").click
          driver.find_element(:name, "Location Services").click
          driver.find_element(:name, "WebDriverAgentRunner-Runner").click
          driver.find_element(:name, "Always").click
          driver.reset
        else
          raise "error granting location permission for WDA #{e.message}"
        end
      end
    end

    def disable_low_power_mode
      session_id
      Battery.new(@uuid, @session_id).change_low_power_mode
    end

    def enable_prevent_cross_site_tracking
      if device_state.prevent_cross_site_tracking_disabled_file_present?
        session_id
        res = BrowserStack::PreventCrossSiteTracking.new(@uuid, @session_id).switch("enable")
      end
    end

    def reset_safari_settings
      if device_state.safari_settings_file_present?
        session_id
        state = {
          prevent_cross_site_tracking: "enable",
          block_popups: "enable"
        }
        res = SafariSettings.new(@uuid, @session_id).switch(state)
        device_state.remove_safari_settings_file
      end
    end

    def start_stop_safari
      BrowserStack.logger.info("Starting and stopping safari for ios > 17.4")
      BrowserStack::IPhone.start_safari_using_wda(device_id)
      BrowserStack::IPhone.kill_safari_using_wda(device_id)
    end

    def disable_testflight_notifications
      if Gem::Version.new(device_version) >= Gem::Version.new(15.0)
        # Using XCUI
        cleanup_iphone.disable_testflight_notifications
      else
        # Using Appium
        # TODO: Check the automation for all njb devices and use XCUI for all.
        testflight_automation = Automation::TestFlight.new(@uuid, @device_config)
        testflight_automation.disable_notifications
      end
    end

    def set_safari_default_browser_in_eu
      cleanup_iphone.set_safari_default_browser_in_eu
    end

    def mark_disable_auto_lock_pending
      @device_state.remove_disable_auto_lock_file
      cleanup_iphone.mark_step_pending(:disable_auto_lock)
      cleanup_iphone.save_completed_steps_state
    end

    # TODO: move to a separate class: TestFlight
    def testflight_cleanup # rubocop:todo Metrics/AbcSize

      abm = BrowserStack::AppleBusinessManagerHelper.new(@uuid, 'testflight')
      is_device_eligible = abm.device_eligible?(@device_config["region"])
      # Uninstall testflight app if statefile is present
      begin
        if device_state.sign_in_to_app_store_popup_detected_file_present?
          IdeviceUtils.uninstall_app(@uuid, IPhone::TESTFLIGHT_APP)
          device_state.remove_sign_in_to_app_store_popup_detected_file
        end
        IdeviceUtils.uninstall_app(@uuid, IPhone::TESTFLIGHT_APP) if !is_device_eligible && IdeviceUtils.check_app_with_bundle_id_exists(@uuid, IPhone::TESTFLIGHT_APP)
      rescue => e
        BrowserStack.logger.error "Failed to uninstall testflight app with error #{e.message}"
      end

      # adding code to install testflight via mdm, currently this is not GA
      # if we want to disable notifications, then we have to open the app right after installing and click on dont allow

      if is_device_eligible
        testflight_installed = false
        testflight_installed = abm.install_app_via_mdm_using_vpp unless abm.app_installed_with_retries?(1)

        dir_name = OSUtils.ensure_path_exists "/usr/local/.browserstack/config/installed_TestFlight"
        testflight_installed_file = "#{dir_name}/installed_TestFlight_#{@uuid}"
        notifications_disabled_file = "#{dir_name}/disabled_TestFlight_notifs_#{@uuid}"
        notifications_opened_file = "/tmp/testflight_notification_opened_#{@uuid}"

        BrowserStack.logger.info "TestFlight is already installed on device."
        FileUtils.touch(testflight_installed_file)

        # We don't need to run Disable Notifications automation where Notifications Profile is installed
        if Utils.new_notifications_flow_enabled?(@uuid)
          FileUtils.touch(notifications_disabled_file)
          FileUtils.rm_f(notifications_opened_file)
        end

        disable_testflight_notifications unless File.exists?(notifications_disabled_file)
        disable_testflight_notifications if File.exists?(notifications_opened_file)

        # Bouncing Testflight app in case some logging info are left in sign in dialog after a session (MOB-4328)
        bounce_testflight if (Gem::Version.new(device_version) >= Gem::Version.new('16.0') && Gem::Version.new(device_version) < Gem::Version.new('16.3')) && !@dedicated_device
      end
    rescue => e
      BrowserStack.logger.error "Error in testflight_cleanup: #{e.message}, #{e.backtrace}"
      BrowserStack::Zombie.push_logs("testflight-cleanup-failed", e.message, { "session_id" => session_id, "device" => @uuid })
      raise e
    end

    def apple_id_cleanup
      detected_by_xcuitest = false
      detected_by_wda = nil
      detected_by_devicelogger = device_state.device_logger_detected_apple_id_signed_in_file_present?
      account_id = ""
      my_card_detected = nil
      siri_accounts_detected = nil

      if Gem::Version.new(device_version) >= Gem::Version.new("16.0")
        wda_response = cleanup_iphone.apple_id_signout(use_wda: true)
        detected_by_wda = wda_response&.dig('value', 'apple_id_detected') == 'true'
        check_apple_id_popup if Gem::Version.new(device_version) >= Gem::Version.new("17.4") && detected_by_wda
        return
      end

      output = cleanup_iphone.apple_id_signout
      return if output.nil?

      account_match_data = /\[APPLE_ID_SIGNED_IN\] Account: (.*)/.match(output)

      if account_match_data
        account_id = account_match_data.captures.first
        detected_by_xcuitest = true

        # MobilePhone app - XCUI cleanups
        # We do this only when Apple Login is detected
        if device_version.to_f >= 13.3 && device_type == "iPhone"
          clear_contact_accounts_output = cleanup_iphone.clear_contact_accounts
          my_card_detected = clear_contact_accounts_output&.include?("tap My Card")
          siri_accounts_detected = clear_contact_accounts_output&.include?("Removing account:")
        end
      end
    rescue => e
      zombie("apple-id-cleanup-failed", e.message)
      raise e
    ensure
      zombie("apple-id-signed-in", account_id, { "data" => { "xcuitest_detected": detected_by_xcuitest, "device_logger_detected": detected_by_devicelogger, "wda_detected": detected_by_wda, "my_card_detected": my_card_detected, "siri_accounts_detected": siri_accounts_detected } }) if detected_by_devicelogger || detected_by_xcuitest || detected_by_wda
    end

    def check_apple_id_popup
      BrowserStack.logger.info("Checking apple id popup for security issue")
      raise OFFLINE_REASON_APPLE_ID_PRESENT if cleanup_iphone.apple_id_popup_present?(use_wda: true)
    end

    def push_pwa_list_to_zombie(xcui_output)
      return if xcui_output.nil? || xcui_output.empty?

      unknown_apps_list = xcui_output.string_between_markers("List of unknownApps iOS: [", "]")
      if !unknown_apps_list.nil? && !unknown_apps_list.strip.empty?
        BrowserStack.logger.info("Unknown apps found: #{unknown_apps_list}")
        zombie('pwa-and-unknown-apps-list', '', { 'data' => unknown_apps_list })
      end

      if xcui_output.include?("Duplicate known apps found")
        known_apps = xcui_output[/Duplicate known apps found.*/].gsub("Duplicate known apps found: ", "")
        BrowserStack.logger.info("Duplicate known apps are found, one of them is most likely to be a PWA: #{known_apps}")
        zombie('duplicate-apps-found', '', { 'data' => known_apps })
      end
    end

    def handle_pwa_cleanup_error(e)
      pwa_cleanup_failure_count = begin
        device_state.read_pwa_cleanup_failure_count_file.to_i
      rescue
        1
      end
      pwa_cleanup_failure_count += 1
      BrowserStack.logger.warn("PWA XCUI automation failure count: #{pwa_cleanup_failure_count}")
      push_pwa_list_to_zombie(e.test_output)

      device_state.write_to_pwa_cleanup_failure_count_file(pwa_cleanup_failure_count)
      # Don't want to contact MDM frequently & we don't have many devices going offline because
      # of MDM restriction being removed that's why doing it on 100 failures.
      install_configuration_profiles if pwa_cleanup_failure_count % 100 == 0
    end

    def clean_pwa_and_waiting_state_apps
      xcui_output = cleanup_iphone.clean_pwa_and_waiting_state_apps
      device_state.remove_pwa_cleanup_failure_count_file
      push_pwa_list_to_zombie(xcui_output)
    rescue BrowserStackTestExecutionError => e
      if e.message.include?("timed out")
        BrowserStack.logger.info("Retrying PWA cleanup due to time out")
        xcui_output = cleanup_iphone.clean_pwa_and_waiting_state_apps(240)
        device_state.remove_pwa_cleanup_failure_count_file
        push_pwa_list_to_zombie(xcui_output)
      else
        raise e
      end
    rescue e
      handle_pwa_cleanup_error(e)
      raise e
    end

    def files_cleanup
      xcui_output = cleanup_iphone.delete_downloads
      return if xcui_output.nil?

      deleted_items_count_line = xcui_output[/Deleted items count:.*/]
      return if deleted_items_count_line.nil?

      deleted_items_count = deleted_items_count_line[/\d+/]
      BrowserStack.logger.debug("XCUI deleted downloads count line: '#{deleted_items_count_line}' | count: #{deleted_items_count}")
      zombie('xcui-deleted-downloads-count', '', { 'data' => deleted_items_count })
    rescue => e
      # if Files app is not installed we can keep the device offline with this
      # reason and not retry cleanup
      raise OFFLINE_REASON_FILES_APP_MISSING unless files_app_installed?
      raise OFFLINE_REASON_FILES_APP_RESTRICTED if files_app_restricted?(e)

      # If Files app is present then raise the original exception
      raise e
    end

    def sandbox_account_cleanup
      detected_by_xcuitest = false
      detected_by_devicelogger = device_state.device_logger_detected_sandbox_signed_in_file_present?
      improved_logline_detected_by_devicelogger = device_state.device_logger_detected_sandbox_signin_logline_file_present?
      popup_logline_detected_by_devicelogger = device_state.device_logger_detected_sandbox_signin_popup_file_present?

      output = cleanup_iphone.sign_out_sandbox_accounts
      return if output.nil?

      detected_by_xcuitest = true if output.to_s.match(/Tap on Apple ID/)

      if detected_by_devicelogger || detected_by_xcuitest || improved_logline_detected_by_devicelogger || popup_logline_detected_by_devicelogger
        zombie("sandbox-apple-id-signed-in", "",
               { "data" => { "xcuitest_detected": detected_by_xcuitest, "device_logger_detected": detected_by_devicelogger, "improved_logline_detected_by_devicelogger": improved_logline_detected_by_devicelogger, "popup_logline_detected_by_devicelogger": popup_logline_detected_by_devicelogger } })
      end
    end

    # Wallet can be populated from all products
    # This should be done after apple id signout
    # Note: iPads don't support Wallet app
    # https://browserstack.atlassian.net/browse/MOB-9398
    #
    # Currently wallet is enabled only for newer ios versions, therefore we
    # can only clean them
    # For others we need to fix XCUI automation to clean them
    # Some devices with unclean wallet:
    # f7772d9d40c955e48466aed116c6691e1cc8fd61,
    # af0fcaad8c1176803ac0cf7a6a11691886b21f59, d87e60b022b8d03e116464d63df1bed387f7d807,
    def apple_wallet_cleanup
      output = cleanup_iphone.apple_wallet_cleanup
      # TODO: Remove this zombie logging
      passes_removed = output.to_s.scan(/Removing a pass/).count
      zombie("apple_wallet_passes_removed", passes_removed.to_s) if passes_removed > 0

      device_state.remove_device_logger_detected_apple_wallet_launched_file
      device_state.touch_force_clean_apple_wallet_file
    end

    def location_services_cleanup
      # Enabling location services should come before safari_cleanup because sometimes
      # the location pop-up isn't dismissed by location services XCUI automation so
      # bookmarks/favorites cleanup will fail.
      # Cleaning Safari after this will clear the history & remove the pop-up.
      cleanup_iphone.enable_location_services if device_state.location_services_enabled_file_older_than_days?(CLEANUP_STEPS_FREQ[:enable_location_services]) || @device_state.device_location_off_file_present?
    rescue BrowserStackTestExecutionError => e
      uninstall_apps_if_present(e)
      # Raise the exception to reinstall the apps and retry this step
      raise e
    end

    def push_view_setting_zombie
      xcui_output = cleanup_iphone.reset_view_to_standard
      return if xcui_output.nil? || xcui_output.empty? || xcui_output[/View is zoomed/].nil?

      BrowserStack.logger.warn("push_view_setting_zombie: View is set to zoomed on #{device_id} #{xcui_output}")
      Zombie.push_logs("view-zoomed", "xcui_output: #{xcui_output[/View is zoomed/]}", { "device" => device_id })
    end

    def mdm_re_enrollment
      return if CustomMDMManager.is_custom_mdm_device?(@uuid)

      re_enroll_success = cleanup_iphone.re_enroll_device_to_mdm
      if re_enroll_success
        # In MDM Re-Enrollment all the profiles will be removed. So Installing it again
        BrowserStack.logger.info("Installing MDM Profiles after enrollment")
        install_configuration_profiles
      end
    end

    def xcui_automation # rubocop:todo Metrics/CyclomaticComplexity, Metrics/AbcSize, Metrics/MethodLength, Metrics/PerceivedComplexity
      cleanup_iphone.fix_enlarged_font_size
      cleanup_iphone.install_launcher_and_bs_app
      cleanup_iphone.date_time_cleanup if need_date_time_cleanup?
      cleanup_iphone.disable_standby_mode if need_standby_mode_disable?
      # For less than iOS 14, we use ifuse to preload media. Making this the first automation to prevent any alert conflicts.
      preload_media_ios_njb_app if needs_media_preload? && device_version.to_f >= 14.1

      cleanup_iphone.install_rigid_restrictions_profile

      # Disable iOS voiceover and do proper cleanup if it was enabled in last session
      # Else other UI Automations might start breaking
      cleanup_iphone.clean_voiceover
      cleanup_iphone.download_file_cleanup

      # Log apps opened on mobile
      cleanup_iphone.log_ios_opened_apps

      # SIM Cleanup
      is_sim_cleanup_required = need_sim_cleanup?
      BrowserStack.logger.info("SIM cleanup required: #{is_sim_cleanup_required}")
      cleanup_iphone.sim_cleanup if is_sim_cleanup_required

      # SIM Validation
      sim_validation_required = sim_validation?
      BrowserStack.logger.info("SIM validation required: #{sim_validation_required}")
      cleanup_iphone.validate_sim_device if sim_validation_required

      # BrowserStack app
      cleanup_iphone.grant_access_photos_permission

      # Files app
      files_cleanup if need_files_app_cleanup?

      # Set Safari as Default Browser
      set_safari_default_browser_in_eu if need_set_safari_default_browser_in_eu?

      # Settings
      push_view_setting_zombie if device_version.to_f >= 13.6 && device_version.to_f < 18 && need_reset_view_to_standard?
      apple_id_cleanup unless Gem::Version.new(device_version) >= Gem::Version.new("16.0")
      location_services_cleanup
      cleanup_iphone.check_sim_signal_strength if need_check_sim_signal_strength?
      cleanup_iphone.safari_cleanup if need_safari_cleanup?
      cleanup_iphone.disable_government_notifications
      handle_app_store_popups if need_handle_app_store_popups?
      sandbox_account_cleanup if need_sandbox_account_cleanup?
      cleanup_iphone.disable_airplane_mode_from_settings
      cleanup_iphone.disable_low_power_mode if need_low_power_mode_disabled?
      cleanup_iphone.enable_wifi if need_enable_wifi? && (device_state.keep_wifi_enabled_file_present? || device_version.to_i <= 13)
      clean_pwa_and_waiting_state_apps if need_pwa_and_waiting_state_apps_cleanup?
      cleanup_iphone.reset_keyboard_settings if need_reset_keyboard_settings? && device_version.to_f < 18.0
      cleanup_iphone.siri_contacts_cleanup if need_siri_contacts_cleanup?
      cleanup_iphone.safari_remote_automation if need_safari_remote_automation?
      cleanup_iphone.disable_paint_timing if device_state.paint_timing_enabled_file_present?
      cleanup_iphone.clean_imessages_app if need_imessages_app_cleanup? || need_imessages_app_cleanup_for_non_sim_devices?
      cleanup_iphone.remove_extra_keyboards if need_remove_extra_keyboards?
      set_time_to_utc if need_to_set_time_to_utc?
      cleanup_iphone.disable_auto_lock if device_state.disable_auto_lock_file_older_than_days?(CLEANUP_STEPS_FREQ[:disable_auto_lock])
      cleanup_iphone.enable_safari_web_inspector unless device_state.enable_safari_web_inspector_file_present?
      cleanup_iphone.disable_safari_websocket if need_safari_websocket_disable?
      cleanup_iphone.disable_bluetooth unless device_state.disabled_bluetooth_file_present?
      cleanup_iphone.clear_third_party_accounts if device_state.third_party_account_sign_in_file_present? && device_version.to_f < 18.0
      cleanup_iphone.clean_safari_url_bar_position if need_safari_url_bar_position_cleanup? && device_version.to_f < 18.0
      cleanup_iphone.clear_call_logs if need_call_logs_cleanup?
      cleanup_iphone.favorite_contact_cleanup if need_favorite_contact_cleanup?
      cleanup_iphone.enable_redirect_extension if need_redirect_extension_cleanup?
      cleanup_iphone.chromium_cleanup if need_chromium_cleanup?
      cleanup_iphone.disable_testflight_background_refresh if need_disable_testflight_background_refresh?
      cleanup_iphone.clean_stored_password if need_clean_stored_password?
      cleanup_iphone.clean_device_theme if need_theme_cleanup?
      cleanup_iphone.disable_stage_manager if need_disable_stage_manager?
      cleanup_iphone.disable_apple_intelligence if need_disable_apple_intelligence?

      set_launcher_app_permissions

      begin
        cleanup_iphone.disconnect_wifi if device_version.to_i > 10 && device_version.to_i < 17 && @@config['internet_sharing_enabled'] && device_state.wifi_enabled_file_present?
      rescue BrowserStackTestExecutionError => e
        raise WifiError, "wifi connected to unknown network" unless connected_to_browserstack_wifi?(e.test_output)
        raise e if auto_join_present?(e)
      end

      apple_wallet_cleanup if need_apple_wallet_cleanup?
      cleanup_iphone.enable_apple_wallet_enable_double_click_side_button if need_apple_wallet_enable_double_click_side_button?

      begin
        cleanup_iphone.check_global_proxy_installed if device_version.to_f < 18.0 && need_install_global_proxy_profile? && !cleanup_iphone.configuration_profiles_manager.device_uses_cfgutil_managed_profiles?
      rescue MdmProfileException
        install_configuration_profiles
        device_state.touch_check_global_proxy_file
      end

      # Safari app - XCUI cleanups
      safari_app_automations = {
        "clean_safari_fav_and_bookmarks" => false,
        "clear_reading_list" => false,
        "clean_safari_tab_groups" => false,
        "clean_safari_history" => false
      }
      if device_version.to_f >= 13.3
        # idevicebookmark doesn't work, clean using UI automation
        if need_safari_bookmarks_cleanup? || need_safari_favorites_cleanup?
          # Clean both bookmarks and favorites, since favorites can also be added via the bookmarks tab
          safari_app_automations["clean_safari_fav_and_bookmarks"] = true
        end
      else
        # TODO: Fix https://github.com/browserstack/mobile-tools/tree/master/idevicebookmark for iOS 13.3+
        cleanup_iphone.clean_bookmarks_favorites
      end

      # TODO: Fix https://browserstack.atlassian.net/browse/MOBPL-5915
      safari_app_automations["clear_reading_list"] = true if need_safari_reading_list_cleared?
      safari_app_automations["clean_safari_tab_groups"] = true if need_safari_tab_groups_cleanup?
      safari_app_automations["clean_safari_history"] = true if need_clean_safari_app_history?
      cleanup_iphone.clean_safari_app(safari_app_automations) unless safari_app_automations.values.select { |val| val }.empty?
      device_state.touch_force_clean_history_from_safari_app_file
      device_state.remove_device_logger_detected_safari_website_settings_opened_file
      device_state.remove_device_logger_detected_safari_vfx_used_file

      # Kill apps to prevent inconsistent state, check MOB-8062
      cleanup_iphone.kill_apps if need_kill_apps?
    end

    def dedicated_xcui_automation # rubocop:todo Metrics/AbcSize, Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity, Metrics/MethodLength
      if @dedicated_first_cleanup_device
        cleanup_iphone.fix_enlarged_font_size
        cleanup_iphone.disable_standby_mode if need_standby_mode_disable?
      end
      cleanup_iphone.install_launcher_and_bs_app
      mdm_re_enrollment
      # BrowserStack app
      cleanup_iphone.grant_access_photos_permission

      # Disable iOS voiceover and do proper cleanup if it was enabled in last session
      # Else other UI Automations might start breaking
      cleanup_iphone.clean_voiceover

      # Settings
      cleanup_iphone.check_sim_signal_strength if need_check_sim_signal_strength?
      cleanup_iphone.safari_cleanup if @dedicated_cleanup_config.include?("clean_browser") && need_safari_cleanup?
      cleanup_iphone.disable_government_notifications
      cleanup_iphone.disable_airplane_mode_from_settings
      cleanup_iphone.enable_wifi if need_enable_wifi?
      cleanup_iphone.safari_remote_automation if @dedicated_cleanup_config.include?("clean_browser") && need_safari_remote_automation?
      set_time_to_utc if device_state.set_time_to_utc_file_older_than_days?(CLEANUP_STEPS_FREQ[:set_time_to_utc])
      cleanup_iphone.disable_auto_lock if device_state.disable_auto_lock_file_older_than_days?(CLEANUP_STEPS_FREQ[:disable_auto_lock])
      if @dedicated_first_cleanup_device
        cleanup_iphone.configure_assistive_touch
        cleanup_iphone.install_preloaded_files
        cleanup_iphone.download_file_cleanup
      end
      if @dedicated_cleanup_config.include?("clean_browser")
        cleanup_iphone.enable_safari_web_inspector unless device_state.enable_safari_web_inspector_file_present?
        cleanup_iphone.disable_safari_websocket if need_safari_websocket_disable?
        cleanup_iphone.clean_safari_url_bar_position if need_safari_url_bar_position_cleanup?
      end

      cleanup_iphone.enable_redirect_extension if @dedicated_first_cleanup_device && need_redirect_extension_cleanup?

      set_launcher_app_permissions

      begin
        cleanup_iphone.disconnect_wifi if device_version.to_i > 10 && @@config['internet_sharing_enabled'] && device_state.wifi_enabled_file_present? && !device_state.keep_wifi_enabled_file_present?
        cleanup_iphone.enable_wifi if device_state.keep_wifi_enabled_file_present? && !wifi_enabled?
      rescue BrowserStackTestExecutionError => e
        raise WifiError, "wifi connected to unknown network" unless connected_to_browserstack_wifi?(e.test_output)
        raise e if auto_join_present?(e)
      end

      begin
        cleanup_iphone.check_global_proxy_installed if device_state.check_global_proxy_file_older_than_days?(CLEANUP_STEPS_FREQ[:check_global_proxy_installed]) && !cleanup_iphone.configuration_profiles_manager.device_uses_cfgutil_managed_profiles?
      rescue MdmProfileException
        install_configuration_profiles
        device_state.touch_check_global_proxy_file
      end

      if @dedicated_cleanup_config.include?("clean_browser")
        # Safari app - XCUI cleanups
        if device_version.to_f >= 13.3
          # idevicebookmark doesn't work, clean using UI automation
          if need_safari_bookmarks_cleanup? || need_safari_favorites_cleanup?
            # Clean both bookmarks and favorites, since favorites can also be added via the bookmarks tab
            cleanup_iphone.clean_safari_fav_and_bookmarks
          end
        else
          # TODO: Fix https://github.com/browserstack/mobile-tools/tree/master/idevicebookmark for iOS 13.3+
          cleanup_iphone.clean_bookmarks_favorites
        end
        cleanup_iphone.clear_reading_list if need_safari_reading_list_cleared?
        cleanup_iphone.clean_safari_tab_groups if need_safari_tab_groups_cleanup?
      end

      # Kill apps to prevent inconsistent state, check MOB-8062
      # cleanup_iphone.kill_apps
    end

    def set_launcher_app_permissions
      # Launcher app
      if device_state.launcher_photos_permission_file_present?
        output = cleanup_iphone.grant_access_photos_permission_launcher
        if session_id && genre == GENRE_APP_AUTOMATE
          permission_granted_in_cleanup = false
          permission_granted_in_cleanup ||= !output.match(/Photos alert found/).nil?
          permission_granted_in_cleanup ||= !output.match(/Photos access not granted/).nil?
          Zombie.push_logs("launcher-perm-cleanup", "", { "device" => @uuid, "session_id" => session_id }) if permission_granted_in_cleanup
        end
      end
    end

    def test_launch_appium_with_new_wda
      OSUtils.kill_wda_xcodebuild(@uuid)
      IdeviceUtils.uninstall_app(uuid, 'com.facebook.WebDriverAgentRunner.xctrunner')
      settings_driver = appium_server.driver(app: 'settings')
      if Gem::Version.new(device_version) >= Gem::Version.new("16.0")
        settings_driver
      else
        settings_driver.driver_quit
        nil
      end
    end

    def auto_join_present?(error)
      # If the XCUI test find out that the Auto Join switch is not present, it means that
      # for some reason Wi-Fi configuration profile was not present on the device during setup.
      # We want to track these devices so that we can fix it manually.
      #
      # Assertion Failure raised in XCUI:
      # Assertion Failure: SettingsUITests.swift:92: XCTAssertTrue failed - Auto Join switch not present
      if !error.test_output.nil? && error.test_output.include?("Auto Join switch not present")
        BrowserStack.logger.warn("Auto Join switch was not present during xctest.")
        zombie("aa-disconn-wifi-auto-join-missing")
        return false
      end
      true
    end

    def connected_to_browserstack_wifi?(output)
      # Wifi disconnect test will fail when device is connect to non BLT wifi network
      # Updating the reason for such failures so that hosting team can fix them manually while we are working on a root fix
      # TODO: Once the root fix (disconnect from any wifi network) is deployed, this can be removed
      unless output.nil?
        wifi_name_matcher = output.match(/label: 'Wi-Fi', value: ([\w ]+)/)
        return false if !wifi_name_matcher.nil? && wifi_name_matcher[1] != @@config['static_conf']['ssid']
      end
      true
    end

    def uninstall_apps_if_present(error)
      # If the XCUI test is unable to launch the Launcher app then uninstall both Chrome & Launcher
      # because maybe the provisioning certificate used to sign them has expired and the apps
      # are not usable.
      #
      # Sometimes, pop-ups are disabled in Chrome by the user so the allow location pop-up
      # doesn't appear. Uninstalling Chrome will restore the settings to the default.
      #
      # Uninstalling them will cause them to get reinstalled during the next cleanup retry.
      #
      # Exception raised in XCUI:
      # Failed to launch com.browserstack.Launcher: The operation couldn’t be completed.
      if !error.test_output.nil? && !error.test_output.include?("is unknown to FrontBoard")
        BrowserStack.logger.warn("Failed to enable location services for Safari/Chrome, uninstalling Chrome & Launcher.")
        IdeviceUtils.uninstall_app(device_id, Launcher::BUNDLE_ID)
        IdeviceUtils.uninstall_app(device_id, Chrome::BUNDLE_ID)

        user_installed_apps = IdeviceUtils.list_apps(device_id, attempts: 2)
        apps = [Launcher.new, Chrome.new]
        apps.each do |app|
          app.update_app_version_using_ios_version(device_version.to_i)
          # On some devices I've seen that even after running the uninstall command the app still remains on the device
          # unless we manually long press the app icon & tap the 'X' icon to uninstall.
          # Adding this to check how often it happens.
          raise "#{OFFLINE_REASON_UNABLE_TO_UNINSTALL_APP} #{app.bundle_id}" if app.present_on_device?(user_installed_apps)
        end
        Zombie.push_logs("apps_uninstalled_to_enable_location", "#{error.class} - #{error.message}", { "device" => device_id })
      else
        BrowserStack.logger.info("Chrome/Launcher not present to enable location services")
      end
    end

    def appium_automation(driver=nil)
      BrowserStack.logger.info("Doing cleaning using appium")

      # Need to allow local network access after reinstalling WDA
      if Gem::Version.new(device_version) >= Gem::Version.new(14.2)
        begin
          skip_driver_quit = Gem::Version.new(device_version) >= Gem::Version.new("16.0")
          Automation.allow_wda_local_network(device_id, wda_client, driver, skip_driver_quit)
        rescue => e
          raise e.class, "allow_wda_local_network failed #{e.message}"
        end
      end
    end

    # TODO: move to a separate class: TestFlight
    def bounce_testflight
      testflight_driver = appium_server.driver(app: 'com.apple.TestFlight')
      begin
        testflight_driver.find_element(:name, "Don’t Allow").click
        BrowserStack.logger.info("Testflight popup handled")
      rescue
        BrowserStack.logger.info("No popup while bouncing testflight")
      end
      testflight_driver.reset
      testflight_driver.driver_quit
    end

    def kill_device_processes
      CheckPlist.unload_service("ios_webkit_debug_proxy_#{@device_config['debugger_port']}", ServiceType.SystemService)
      CheckPlist.unload_service("appium_#{@device_config['port']}", ServiceType.UserService, "app")
      OSUtils.execute("ps aux | grep #{uuid} | grep -v \"grep\\|#{$PROCESS_ID}\" | awk '{print $2}' | sudo xargs kill -9 ")
    end

    def restart_processes_after_reboot
      CheckPlist.load_service("ios_webkit_debug_proxy_#{@device_config['debugger_port']}", ServiceType.SystemService)
      CheckPlist.load_service("appium_#{@device_config['port']}", ServiceType.UserService, "app")
    end

    def reboot
      IdeviceUtils.reboot(uuid)
    end

    def online?
      device_on_usb? && (unique_id == uuid)
    end

    def self.drop_call(iproxyPort)
      2.times do
        BrowserStack.logger.info "call lock.. to drop call"
        HttpUtils.get_response("http://localhost:#{iproxyPort}/lock")
      end
    end

    def get_device_details_for_app_trust
      device_details = {}
      device_details[:device] = @device
      device_details[:os_version] = device_version
      device_details[:device_type] = device_type
      device_details[:appium_port] = @device_config['selenium_port']
      device_details
    end

    def trust_client_enterprise_app(dist_name = "")
      device_details = get_device_details_for_app_trust

      client_enterprise_app_trust = EnterpriseAppTrust.new(@uuid, @device_config, device_details, dist_name)
      BrowserStack.logger.info("Doing client enterprise app trust using appium")
      raise 'Appium port to send trust commands not found' if device_details[:appium_port].nil?

      begin
        2.times do
          result = client_enterprise_app_trust.perform_action('trust')
          return true if result

          BrowserStack.logger.info "EnterpriseApp trust returned false. Retrying!"
        end
      rescue => e
        BrowserStack.logger.info "EnterpriseApp trust Got exception for #{@uuid}: #{e.message}:\n Retrying ! Locking phone and Retrying trust (to remove any sim card popups)"
        begin
          return lock_unlock_trust_retry(client_enterprise_app_trust, 'trust')
        rescue => e
          Zombie.push_logs("xcode-build-failure", e.message.to_s, { "device" => @uuid }) if e.message.include?("xcodebuild failed")
          influxdb_client = BrowserStack::IosInfluxdbClient.new(BrowserStack.logger)
          influxdb_client.event(@uuid, 'xcode-build-failure', component: 'server', subcomponent: 'trust-client-enterprise-app', is_error: true)
          raise "EnterpriseApp trust failed for #{@uuid}: #{e.message}!"
        end
      end
      raise FireCMDException, "EnterpriseApp trust failed for #{@uuid}: All retries exhausted!"
    end

    def handle_untrust_and_delete_client_enterprise_app(bundle_id)
      device_details = get_device_details_for_app_trust
      begin
        unlock_device
        BrowserStack.logger.info "[#{uuid}] starting client enterprise app untrust and delete"
        untrust_start_time = Time.now
        raise "untrust_and_delete returned false after possible retries" unless untrust_and_delete_client_enterprise_app(device_details, bundle_id)

        BrowserStack.logger.info "[#{uuid}] Client EnterpriseApp untrust and delete successful, time taken: #{Time.now - untrust_start_time}"
        FileUtils.rm_rf(client_enterprise_app_file)
      rescue => e
        BrowserStack.logger.info "[#{uuid}] client enterprise app untrust and delete failed. marking device offline: #{e.message} #{e.backtrace}"
        BrowserStack::Zombie.push_logs("enterprise-untrust-failed", "#{e.message}-#{bundle_id}", { "session_id" => session_id, "device" => uuid })
        raise "Client's enterprise app untrust failed"
      end
    end

    def untrust_and_delete_client_enterprise_app(device_details, bundle_id)
      unless IdeviceUtils.check_app_with_bundle_id_exists(uuid, bundle_id)
        BrowserStack.logger.info "App with bundle_id #{bundle_id} not present on the device. Skipping untrust."
        raise "Cannot untrust. App with bundle_id #{bundle_id} not found."
      end
      client_enterprise_app_trust = EnterpriseAppTrust.new(uuid, @device_config, device_details, '', bundle_id)
      begin
        2.times do
          result = client_enterprise_app_trust.perform_action('untrust')
          return true if result && !IdeviceUtils.check_app_with_bundle_id_exists(uuid, bundle_id)

          BrowserStack.logger.info "EnterpriseApp untrust_and_delete returned false. Retrying!"
        end
      rescue => e
        BrowserStack.logger.info "EnterpriseApp trust Got exception for #{uuid}: #{e.message}:\n Retrying ! Locking phone and Retrying untrust (to remove any sim card popups)"
        begin
          return lock_unlock_trust_retry(client_enterprise_app_trust, 'untrust')
        rescue => e
          Zombie.push_logs("xcode-build-failure", e.message.to_s, { "device" => uuid }) if e.message.include?("xcodebuild failed")
          @influxdb_client.event(uuid, 'xcode-build-failure', component: 'cleanup', subcomponent: 'untrust-delete-client-app', is_error: true)
          raise "EnterpriseApp untrust_and_delete failed for #{uuid}: #{e.message}!"
        end
      end
      false
    end

    def self.get_file_details(media)
      [media["s3_url"], media["filename"], media["filetype"]]
    end

    # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
    # TODO: This code should not be part of IPhone class, that too on the class level
    def self.download_and_push_custom_media(custom_media, framework, device, session_id, bundle, feature_usage)
      data_to_push = { event_name: "FileInjection", product: "app_automate", os: IdeviceUtils.os(device), team: "device_features" }
      begin
        if IdeviceUtils.device_version(device) >= Gem::Version.new(17.0)
          device_state = DeviceState.new(device)
          device_state.touch_custom_media_cleanup_file
        end

        custom_media_files = JSON.parse(custom_media)
        FileUtils.mkdir_p(custom_media_folder(device))
        photos_count = 0
        videos_count = 0
        documents_count = 0
        image_push_time = []
        video_push_time = []
        document_push_time = []
        custom_media_files.each do |media|
          media = JSON.parse(media)
          file_url, file_name, file_type = get_file_details(media)

          photos_count += 1 if file_type == "image"
          videos_count += 1 if file_type == "video"
          documents_count += 1 if file_type == "documents"
          file_path = File.join(custom_media_folder(device), file_name)
          begin
            BrowserStack::HttpUtils.download(file_url, file_path, { retry_count: 3, timeout: 20 }, "custom_media")
          rescue
            raise "Media Download Failed"
          end
          check_and_push_media_files(file_type, file_path, file_name, device, bundle, session_id, feature_usage)
          media_push_start = Time.now
          case file_type
          when "image"
            image_push_time << (Time.now - media_push_start).round(2)
          when "video"
            video_push_time << (Time.now - media_push_start).round(2)
          when "documents"
            document_push_time << (Time.now - media_push_start).round(2)
          end
        end
        check_for_successful_media_push("Photos", photos_count, device)
        check_for_successful_media_push("Videos", videos_count, device)
        check_for_successful_media_push("Custom_Files", documents_count, device, bundle, "/Documents")

        BrowserStackAppHelper.refresh_gallery(device, session_id) if photos_count > 0 || videos_count > 0

        BrowserStack.logger.info "Media push to device time, image_push_time: #{image_push_time}, video_push_time: #{video_push_time}, document_push_time: #{document_push_time}"
        if documents_count != 0
          event_json = { session_id: session_id, file_injection: "success" }
          data_to_push.merge!({ event_json: event_json })
          Utils.send_to_eds(data_to_push, "web_events", true)
        end
        BrowserStack::Zombie.push_logs("push-media-to-device-time", "", { "os" => "ios_njb", "device" => device, "browser" => "", "terminal_type" => "realMobile", "data" => { "image_push_time": image_push_time, "video_push_time": video_push_time, "document_push_time": document_push_time }, "session_id" => session_id })
        sleep(10) if framework == "xcuitest" # wait till app is launched as we have removed sleep of 10 seconds from ios-njb app
      rescue CustomContactsError => e
        raise e
      rescue => e
        if documents_count != 0
          event_json = { session_id: session_id, file_injection: "failed" }
          data_to_push.merge!({ event_json: event_json })
          Utils.send_to_eds(data_to_push, "web_events", true)
          BrowserStack::Zombie.push_logs("inject-file-failure", e.message.to_s, { "session_id" => session_id, "device" => device, "url" => "app_automate" })
        end
        BrowserStack.logger.error("Failure occured during download_and_push_custom_media with error: #{e.message}")
        raise FireCMDException, e.message
      ensure
        BrowserStack::OSUtils.execute("rm -rf #{custom_media_folder(device)}")
      end
      [photos_count, videos_count, feature_usage]
    end

    # rubocop:enable Metrics/AbcSize, Metrics/MethodLength

    def self.get_file_extension(file_name)
      ".#{file_name.split('.').last}"
    rescue
      ""
    end

    def self.check_and_push_media_files(file_type, file_path, file_name, device, bundle, session_id, feature_usage)
      extension = get_file_extension(file_name)
      file_name_array = file_name.split('.')
      file_name_array.pop
      real_file_name = file_name_array.join('.')
      media_type = ""
      case file_type
      when "image"
        media_type = "Photos"
      when "video"
        media_type = "Videos"
      when "documents"
        media_type = "Custom_Files"
      end

      if ["Photos", "Videos"].include?(media_type)
        push_media_file_to_device(Shellwords.escape(file_name), extension, Shellwords.escape(file_path), media_type, device, BROWSERSTACK_APP)
      elsif ["Custom_Files"].include?(media_type)
        push_media_file_to_device(Shellwords.escape(real_file_name), extension, Shellwords.escape(file_path), media_type, device, bundle)
      elsif ['contact'].include?(file_type)
        data_to_push = { event_name: "LoadCustomContacts", product: "app_automate", os: IdeviceUtils.os(device), team: "device_features" }
        event_json = { session_id: session_id, device: device }
        begin
          CustomContacts.load_custom_contacts(device, file_path)
          event_json.merge!({ load_custom_contacts: "success" })
          data_to_push.merge!({ event_json: event_json })
          Utils.send_to_eds(data_to_push, "web_events", true)
          feature_usage["customMedia_contacts"] = { success: true, exception: "" }
        rescue CustomContactsError => e
          event_json.merge!({ load_custom_contacts: "failed", error_reason: e.message.to_s })
          data_to_push.merge!({ event_json: event_json })
          Utils.send_to_eds(data_to_push, "web_events", true)
          BrowserStack::Zombie.push_logs("load-custom-contacts-failure", e.message.to_s, { "os" => "ios_njb", "device" => device, "browser" => "", "terminal_type" => "realMobile", "session_id" => session_id })
          raise e
        end
      end
    end

    def self.push_media_file_to_device(file_name, extension, file_path, media_type, device, bundle = BROWSERSTACK_APP, status = false, timeout: 20)
      BrowserStack::OSUtils.execute("/usr/local/bin/gtimeout -s KILL #{timeout} #{IOS_DEPLOY} --id #{device} -1 #{bundle} --upload #{file_path} --to \"Documents/#{media_type}/#{file_name}_#{(Time.now.to_f * 1000).to_i}#{extension}\"", status)
    end

    def self.custom_media_folder(device)
      "/tmp/custom_media_#{device}"
    end

    def self.check_for_successful_media_push(media_type, num_of_media, device, bundle = BROWSERSTACK_APP, path = nil)
      cmd = BrowserStack::OSUtils.execute("/usr/local/bin/gtimeout -s KILL 20 #{IOS_DEPLOY} --id #{device} -1 #{bundle} --list#{!path.nil? ? "=#{path}" : ''} | grep #{media_type} | cut -d \"/\" -f4")
      items_copied_to_device = cmd.to_s.split("\n")
      items_copied_to_device.delete("")
      raise "#{media_type} not successfully copied to device" if items_copied_to_device.length != num_of_media
    end

    def handle_contacts_cleanup
      BrowserStack.logger.info "Delete Contacts."
      OSUtils.execute_retry IdeviceUtils.method(:delete_contacts), uuid, retries: 2
      BrowserStack.logger.info "Load Contacts."
      OSUtils.execute_retry IdeviceUtils.method(:load_contacts), uuid, retries: 2
    end

    def verify_contacts_count
      contacts_count, status_xml = OSUtils.execute_retry IdeviceUtils.method(:get_contacts_count_xml), uuid, retries: 2
      BrowserStack.logger.info "Number of contacts found from plist on #{device_id}: #{contacts_count} "
      contacts_count == 40
    end

    # TODO: Move to cleanup_iphone.rb & add to around_method to avoid doing it again on cleanup retry
    def contacts_app_cleanup
      # initial count check to reduce cleanup time for version >13.2
      # TODO: fix idevicecontacts to handle multiple executions sequentially without timeout
      BrowserStack.logger.info "contacts_app_cleanup started for #{device_id}"
      unless need_contacts_cleanup?
        BrowserStack.logger.info "contacts are not modified or os version doesn't support contacts_cleanup"
        return
      end

      handle_contacts_cleanup
      device_state.remove_contacts_modified_file if device_state.contacts_modified_file_present?
      BrowserStack.logger.info "Contacts App cleanup done!"
    rescue => e
      BrowserStack.logger.info("contacts_app_cleanup exception: #{e.message} #{e.backtrace}")
      raise ContactsCleanUpError, e.message
    end

    def self.get_media_type(file_name)
      allowed_image_formats = [".jpg", ".jpeg", ".png", ".gif", ".bmp"]
      allowed_video_formats = [".mp4", ".mov"]
      extension = File.extname(file_name)
      if allowed_image_formats.include? extension
        return "Photos"
      elsif allowed_video_formats.include? extension
        return "Videos"
      end

      nil
    end

    def self.get_device_provisioning_profile(uuid)
      ppuid_file = "#{@@config['config_root']}/ppuid_#{uuid}"
      ppuid_config = File.read(ppuid_file)
      ppuid_config.lines[2].strip

    end

    def self.app_live_free_user_file(uuid)
      "/tmp/free_user_#{uuid}"
    end

    # Wrapper function to call the wda/xcui test to set the timezone of device
    def self.change_time_zone(uuid, timezone, timezone_mdm, session_id, product)
      device_conf = get_device_config(uuid)
      timezone_helper = BrowserStack::TimezoneHelper.new(uuid, session_id, product, device_conf)
      timezone_helper.change_time_zone(timezone, timezone_mdm)
    end

    def files_app_installed?

      installed_apps = IdeviceUtils.list_apps(device_id, kind: :all, attempts: 2)
      files_app = installed_apps.find { |app| app[:bundle_id] == FILES_APP }
      BrowserStack.logger.info("Files app for #{device_id} (#{device_name}) v#{device_version}: #{files_app.inspect}")
      !files_app.nil?
    rescue => e
      BrowserStack.logger.error("Can't fetch version of files app: #{e.message}; #{e.backtrace}")
      true # return true if unable to check to avoid false positives

    end

    def install_required_apps_if_not_present # rubocop:disable Metrics/AbcSize
      apps = [Launcher.new, Chrome.new]
      apps << Redirect.new if device_version.to_f >= 15
      begin
        user_installed_apps = IdeviceUtils.list_apps(device_id, attempts: 2)
      rescue => e
        # Sometimes list_apps fails and raises an exception which causes devices to go offline
        # Also, most of the time the apps are present, this is just to cover the corner case
        # when the apps aren't present
        BrowserStack.logger.warn("Unable to check list of installed apps: #{e.message}; #{e.backtrace}. Returning...")
        return
      end

      apps.each do |app|
        app.update_app_version_using_ios_version(device_version.to_i)
        installed_app = InstalledApp.new(uuid, app.bundle_id)

        next unless installed_app.reinstall?(latest_version: app.version) ||
          !app.present_on_device?(user_installed_apps)

        BrowserStack::Zombie.push_logs(
          "#{app.name}_not_installed",
          app.version,
          {
            "os" => "ios_njb",
            "device" => uuid,
            "terminal_type" => "realMobile"
          }
        )
        BrowserStack.logger.info("Device: #{uuid} | #{app.app_folder_name} installing during cleanup")
        app.setup(uuid, self.class.get_device_provisioning_profile(uuid), true)
      end

      if device_version.to_f >= 14
        chromium_manager = Chromium.new(uuid)
        chromium_manager.ensure_install unless user_installed_apps.any? { |app| app[:bundle_id] == chromium_manager.bundle_id }
      end

    end

    def handle_app_store_sign_in
      return if device_state.app_store_login_file_present? # Skip Signing Flow if already signed in

      driver = Automation.open_app_store_and_continue(uuid, device_version)
      InstallApp.sign_in_via_app_store(driver, device_version, @@config["apple_id"], @@config["apple_password"])
      device_state.touch_app_store_login_file
    end

    def system_apps_present?
      user_apps = BrowserStack::OSUtils.execute("/usr/local/bin/gtimeout -s KILL 20 #{IOS_DEPLOY} --id #{device_id} -B").split(/\n+/)
      unless ([FILES_APP, TRANSLATE_APP] - user_apps).empty?
        cleanup_iphone.unmark_method(:install_first_party_system_apps)
        raise "System Apps Not installed"
      end
    end

    def appium_server
      @appium_server ||= AppiumServer.new(uuid, @device_config)
    end

    def cleanup_iphone
      @cleanup_iphone ||= CleanupIphone.new(
        uuid,
        @device_config["selenium_port"],
        device_version,
        @device_config["device_name"],
        @device_config["webdriver_port"],
        @@config["orientation-lock-value"] || 0,
        session_id
      )
    end

    def full_cleanup_state
      @full_cleanup_state ||= FullCleanup::FullCleanupState.new(uuid)
    end

    def trust_client_enterprise_app_via_browserstack_app(retry_count = 2)
      BrowserStack.logger.info("Doing client enterprise app trust using Browserstack NJB App")
      retry_count.times do
        result = BrowserStackAppHelper.trust_client_certificate(@uuid)
        return true if result

        BrowserStack.logger.info "EnterpriseApp trust returned false. Retrying!"
      rescue => e
        BrowserStack.logger.info "EnterpriseApp trust Got exception for #{@uuid}: #{e.message}:\n Retrying!"
      end
      false
    end

    def device_version
      @device_config["device_version"]
    end

    def device_build_version
      @device_config["device_build_version"]
    end

    def check_wifi_status
      device_state.touch_wifi_enabled_file if wifi_enabled?
    end

    def check_allow_settings_app_disabled
      if device_state.allow_settings_app_file_present?
        device_state.remove_allow_settings_app_file
        zombie("settings-file-deleted-cleanup")
      end

      if device_state.allow_settings_app_count_file_present?
        device_state.remove_allow_settings_app_count_file
        zombie("settings-count-file-deleted-cleanup")
      end

    end

    def self.less_than_ios_12?(uuid)
      device_config = get_device_config(uuid)
      return device_config["device_version"].to_i < 12 if device_config && !device_config.empty?

      true
    end

    def self.get_device_config(uuid)
      devices = begin
        JSON.parse(File.read(@@config['config_json_file']))["devices"]
      rescue
        {}
      end
      devices[uuid]
    end

    # HOTFIX to set session_params on the class too,
    # Not sure why we have so many class level methods :-/
    def self.get_session_params(device)
      session_file = "#{@@config['state_files_dir']}/#{device}_session"
      Utils.read_json_file(session_file)
    end

    def self.uninstall_user_apps(uuid)
      BrowserStack.logger.info "uninstall_user_apps_called"
      all_installed_apps = IdeviceUtils.list_user_installed_apps(uuid, timeout: 10)
      session_params = get_session_params(uuid)
      push_to_cls(session_params, "uninstall_apps", '', { "uninstall_apps" => all_installed_apps.length })
      genre = session_params["genre"]
      if genre == "app_live_testing"
        Utils.send_to_eds({
          session_id: session_params["app_live_session_id"],
          product: {
            uninstall_app_count: 1
          }
        }, EdsConstants::APP_LIVE_TEST_SESSIONS, true)
      end
      begin
        all_installed_apps.each do |bundle_id|
          IdeviceUtils.uninstall_app(uuid, bundle_id)
        end
        # We can verify if uninstallation was successful by listing all the user_apps again
        if IdeviceUtils.list_user_installed_apps(uuid, timeout: 10).empty?
          Utils.notify_pusher("uninstallation_done", session_params, uuid)
        else
          Utils.notify_pusher("uninstallation_failed", session_params, uuid)
        end
      rescue => e
        BrowserStack.logger.error "Failed to uninstall apps #{all_installed_apps} with error #{e.message}"

        Utils.notify_pusher("uninstallation_failed", session_params, uuid)
      end
    end

    def self.session_file(uuid)
      "#{@@config['state_files_dir']}/#{uuid}_session"
    end

    def self.clean_call_records_file(device)
      "#{@@config['state_files_dir']}/clean_call_records_#{device}"
    end

    def self.clean_message_records_file(device)
      "#{@@config['state_files_dir']}/clean_message_records_#{device}"
    end

    def self.cleanup_telephony_files(device)
      FileUtils.rm_f(BrowserStack::IPhone.clean_call_records_file(device)) if File.exists?(BrowserStack::IPhone.clean_call_records_file(device))
      FileUtils.rm_f(BrowserStack::IPhone.clean_message_records_file(device)) if File.exists?(BrowserStack::IPhone.clean_message_records_file(device))
    end

    def restart_webkit_proxy
      port = @device_config["debugger_port"]
      BrowserStack.logger.info "Restarting ios_webkit_proxy for port #{port}"
      OSUtils.kill_process("ios_webkit_debug_proxy", port)
      sleep 2
      attempts = 0
      while begin
        HttpUtils.test_url_code("http://localhost:#{port}/")
      rescue
        0
      end != 200
        BrowserStack.logger.info "ios_webkit_debug_proxy not running on port #{port}"
        if attempts > 20
          @device_state.remove_enable_safari_web_inspector_file # redo web inspector setup on next cleanup
          raise "ios_webkit_debug_proxy not working after #{attempts} attempts"
        end
        attempts += 1
        sleep 0.5
      end
      device_state.remove_restart_webkit_proxy_file if device_state.restart_webkit_proxy_file_present?
      device_state.touch_restart_webkit_proxy_file
      BrowserStack.logger.info "Webkit Proxy started on port #{port}"
    end

    # temporary fix, see mobpe-391
    # launcher app has been witnessed using 40GB+ of data over time, reinstalling clears that data
    def reinstall_launcher_app
      BrowserStack.logger.info "Reinstalling launcher app in an attempt to free up space"
      IdeviceUtils.uninstall_app(uuid, 'com.browserstack.Launcher')
      BrowserStackAppHelper.build_and_install_launcher_app(device_id)
      set_launcher_app_permissions
      BrowserStack.logger.info "launcher-app install cmd executed"
    end

    def get_available_storage
      disk_usage_output = IdeviceUtils.ideviceinfo(uuid, "AmountDataAvailable", "com.apple.disk_usage").first
      return if disk_usage_output.nil? || disk_usage_output == ""

      disk_usage_output.to_i / (1024.0 * 1024 * 1024)
    end

    def instrument_available_storage
      data_available_in_gb = get_available_storage
      BrowserStack.logger.info("Data available on this device: #{data_available_in_gb} GB")
      return if data_available_in_gb.nil? || data_available_in_gb == ""

      # Push to zombie if the available storage on the device is less than 1 GB
      if data_available_in_gb <= 1
        zombie('ios_available_storage', '', { 'data' => data_available_in_gb })

        # reinstall launcher app to free up space, tmp fix until we find root cause of launcher app data growing over time
        reinstall_launcher_app

        data_available_after_reinstall = get_available_storage
        return if data_available_after_reinstall.nil? || data_available_after_reinstall == ""

        if data_available_after_reinstall <= 1
          # Mark manual fix offline for iPhone 11, iPhone 11 Pro, iPhone 12 and iPhone XS device in case of low storage
          raise "manual fix required: no space left on this device, needs factory reset" if ["iPhone13,2", "iPhone12,1", "iPhone12,3", "iPhone11,2", "iPad11,6"].include?(device_name)
        else
          data_recovered = data_available_after_reinstall - data_available_in_gb
          BrowserStack.logger.info("Space recovered on this device: #{data_recovered} GB")
        end
      end
    rescue => e
      BrowserStack.logger.info("Error getting available storage on the device: #{e.message}; #{e.backtrace}")
      zombie('ios_available_storage_failed', e.message)

      case e.message
      when /manual fix required/
        raise e
      end
    end

    def capture_rsd_values
      return if device_version.to_i < 17

      PyMobileDevice::Developer.update_rsd_values(device_id)
    end

    def check_replay_kit_still_running
      return unless device_state.replay_kit_running_file_present?

      device_state.remove_replay_kit_running_file
      raise OFFLINE_REASON_REPLAY_KIT_STILL_RUNNING
    end

    def cleanup_app_testing_files
      FileUtils.rm_f("/tmp/app_session_#{uuid}")
      FileUtils.rm_rf("/tmp/#{uuid}_user_app")
      FileUtils.rm_rf(client_enterprise_app_file)
    end

    def check_device_language
      begin
        device_language = IdeviceUtils.device_language(device_id)
        BrowserStack.logger.info("#{device_id} device language is: #{device_language}")
      rescue => e
        BrowserStack.logger.error("Error checking device language #{e.message}: #{e.backtrace}")
        device_language = 'en'
      end
      zombie("device-language", "", { "data" => device_language })
      return unless need_language_addition?

      device_language = 'en' if device_version.to_i < 15 && device_language == 'en-US'
      language_addition_output = add_language_to_device(device_language)
      return if language_addition_output

      BrowserStack.logger.info(OFFLINE_REASON_NEED_LANGUAGE_RESET)
      raise OFFLINE_REASON_NEED_LANGUAGE_RESET
    end

    def reset_socks5_forwarder
      if @device_state.icloud_access_file_present?
        Privoxy::Socks5Forwarder.new(uuid).reset_privoxy
        @device_state.remove_icloud_access_file
      end
    end

    # At the start of the session, device-logger tails some device logs & then writes its pid
    # to a file. In cleanup we're comparing that pid with the current pid of the device-logger
    # to check if device-logger process crashed on not.
    #
    # We're writing the pid from device-logger itself & not from the platform code because
    # we want to write the pid after tailing some device logs to make sure device-logger is
    # actually tailing device & is not just serving /health checks.
    # rubocop:disable Metrics/AbcSize
    def check_device_logger_reliability
      if device_state.session_bm_file_present?
        BrowserStack.logger.info("encountered a BM, thus marking DL as reliable to avoid all cleanup steps")
        @device_logger_reliable = true
        device_state.remove_session_bm_file
        zombie('bm-occurred')
        return
      end

      unless device_state.device_logger_pid_file_present?
        BrowserStack.logger.info("Not relying on device-logger for cleanup as its pid file isn't present")
        @device_logger_reliable = false

        # Do not push to zombie for manual cleanup because device_logger_pid_file won't be there
        # most of the time
        zombie('device-logger-pid-file-absent') unless device_state.manual_cleanup_file_present?
        BrowserStack.logger.info("Rely on device-logger for cleanup: #{@device_logger_reliable}")
        return
      end

      isSessionSpecificDL = @@config['static_conf']['session_specific_device_logger'] == "true"
      device_logger_start_session_pid = device_state.device_logger_pid_file_to_array.first
      if isSessionSpecificDL
        BrowserStack.logger.info("Checking Device Logger Reliability For New Device Logger")
        unless device_state.device_logger_session_end_pid_file_present?
          BrowserStack.logger.info("Device Logger Session End Pid File NOT Present")
          @device_logger_reliable = false
          zombie('device-logger-session-end-pid-file-absent') unless device_state.manual_cleanup_file_present?
          BrowserStack.logger.info("Rely on device-logger for cleanup: #{@device_logger_reliable}")
          return
        end
        device_logger_pid_at_session_end = device_state.read_device_logger_session_end_pid_file
        BrowserStack.logger.info("Device-logger PID at session end: #{device_logger_pid_at_session_end} & at the session start: #{device_logger_start_session_pid}")
        @device_logger_reliable = device_logger_pid_at_session_end.to_i == device_logger_start_session_pid.to_i
      else
        BrowserStack.logger.info("Checking Device Logger Reliability For Old Device Logger")
        device_logger_current_pids = OSUtils.execute("ps -ef | grep '[d]evice-logger' | awk '{print $2}'")
                                            .split
        # In case multiple PIDs are added ot the file due to some bug check the first one
        BrowserStack.logger.info("Device-logger current PIDs: #{device_logger_current_pids} & at the session start: #{device_logger_start_session_pid}")
        @device_logger_reliable = device_logger_current_pids.include?(device_logger_start_session_pid)
      end
      BrowserStack.logger.info("Rely on device-logger for cleanup: #{@device_logger_reliable}")
      zombie("cleanup-device-logger-reliability", "", { "data" => @device_logger_reliable.to_s })
    end
    # rubocop:enable Metrics/AbcSize

    def device_on_usb?
      IdeviceUtils.idevices.include?(uuid)
    rescue
      false
    end

    def app_cleanup # rubocop:todo Metrics/AbcSize
      if genre
        BrowserStack.logger.info "In app_cleanup for app_testing session"
        ([session_params['app_testing_bundle_id'], session_params['app_details_bundle_id'], session_params['test_suite_bundle_id']] + session_params["other_app_bundle_ids"].to_a + session_params['mid_session_app_bundle_ids'].to_a).each do |bundle_id|
          next unless !bundle_id.nil? && !bundle_id.empty? && !['undefined', IPhone::APPSTORE_APP, IPhone::TESTFLIGHT_APP, IPhone::FILES_APP, IPhone::SAFARI_APP].include?(bundle_id)

          if needs_enterprise_app_cleanup?(bundle_id) && device_version.to_i == 10
            handle_untrust_and_delete_client_enterprise_app(bundle_id)
          elsif device_version.to_i >= 18 && needs_enterprise_app_cleanup?(bundle_id)
            IosMdmServiceClient.remove_application(uuid, bundle_id) if app_actually_present?(bundle_id)
          else
            IdeviceUtils.uninstall_app(uuid, bundle_id)
          end
          BrowserStack.logger.info "Uninstall attempted of #{bundle_id}"
        end

        # List user installed apps
        uploaded_apps, all_installed_apps, testflight_apps, updated_apps = get_all_installed_apps
        # TODO: Remove this call to ideviceinstaller
        other_apps_installed = begin
          IdeviceUtils.list_user_installed_apps(uuid, attempts: 2)
        rescue
          []
        end
        other_apps_installed = (other_apps_installed + all_installed_apps).uniq

        # we are adding this condition here to fix a bug,
        # in which multiple chromium versions are present on the device
        # and pwa cleanup is getting triggered in each cleanup
        chromium_apps_installed = begin
          IdeviceUtils.list_installed_chromium_apps(uuid, attempts: 2)
        rescue
          []
        end
        other_apps_installed = (other_apps_installed + chromium_apps_installed).uniq

        # Removing chromium bundle as we don't have constant bundle for chromium.
        chromium_bundle_id = Chromium.new(@uuid).bundle_id
        other_apps_installed.delete(chromium_bundle_id) if other_apps_installed.include? chromium_bundle_id

        other_apps_installed.delete(GEOGUARD_BUNDLE_ID) if other_apps_installed.include? GEOGUARD_BUNDLE_ID

        other_apps_installed.each do |app|
          # This step is required for all the products as there are distribution
          # tools which can attempt to install app from URLs which results in waiting
          # state apps. One of such distribution tool that leads to waiting state apps
          # is Applivery.
          BrowserStack.logger.info "User installed #{app}. Uninstalling it"
          IdeviceUtils.uninstall_app(uuid, app)
        end

        # Reset the AppStore app for app live sessions (genre=app_live_testing)
        BrowserStack::IPhone.kill_app(uuid, IPhone::APPSTORE_APP) unless ["live_testing", "js_testing", "automate", "app_automate"].include? genre

        send_user_apps_to_cls(uploaded_apps, all_installed_apps, testflight_apps, updated_apps) if ['app_automate', 'app_live_testing'].include?(genre)
        send_user_apps_to_eds(uploaded_apps, all_installed_apps, testflight_apps, updated_apps) if genre == 'app_live_testing'
      end
    end

    private

    # Everything that doesn't involve UI automation & has to run before UI automation should go here
    def pre_automation_cleanup
      cleanup_iphone.disable_assistive_touch
      cleanup_iphone.reset_gps_location

      # We want to enroll the device before enable_pwa tries to remove restrictions.
      cleanup_iphone.re_enroll_device_to_mdm
      cleanup_iphone.enable_pwa if ExperimentsHelper.enable_pwa?(device_version) && !device_state.pwa_enabled_file_present?
      if device_state.force_install_mdm_profiles_file_present?
        install_configuration_profiles
        device_state.remove_force_install_mdm_profiles_file
      end
    end

    # Everything that doesn't involve UI automation & has to run after UI automation should go here
    def post_automation_cleanup
      wda_client.kill_apps(['com.apple.MobileSafari', 'com.apple.TestFlight'])

      cleanup_iphone.idevice_name(device_type)
    end

    # Gets the home screen layout using cfgutil & ignores the known apps.
    # The remaining apps are either PWAs or waiting state apps that have to be cleaned using XCUI.
    # @return - true if cfgutil is not supported to clean the waiting state apps
    def need_pwa_and_waiting_state_apps_cleanup?
      return true unless ExperimentsHelper.enable_pwa?(device_version)

      begin
        home_screen_layout = cfgutil.get_icon_layout
      rescue => e
        BrowserStack.logger.info("Error getting home screen layout using cfgutil: #{e.message}; #{e.backtrace}")
        zombie('cfgutil-exec-failed', e.message)
        return true
      end

      all_apps = remove_home_screen_folders!(home_screen_layout).flatten
      unknown_apps = all_apps - @@config['known_apps_bundle_id']
      chromium_bundle_id = Chromium.new(@uuid).bundle_id
      unknown_apps.delete(chromium_bundle_id) if unknown_apps.include? chromium_bundle_id

      unknown_apps.delete(GEOGUARD_BUNDLE_ID) if unknown_apps.include? GEOGUARD_BUNDLE_ID

      if unknown_apps.empty?
        zombie("skipping-waiting-apps-pwa-cleanup")
        return false
      end

      BrowserStack.logger.info("Unknown apps found on home screen: #{unknown_apps}")
      zombie('unknown-apps-detect', '', { 'data' => unknown_apps })

      true
    end

    # The folder on the home screen is an array where the first entry is the name of the folder.
    # We don't have to remove the folders in cleanup so ignoring them
    # See: https://configautomation.com/cfgutil-man-page.html
    def remove_home_screen_folders!(home_screen_layout)
      home_screen_layout.each do |page|
        page.each do |icon|
          # Icon is an array if its a folder
          icon.shift if icon.is_a?(Array)
        end
      end
    end

    def cfgutil
      @cfgutil ||= CFGUtil.new(udid: uuid)
    end

    def need_files_app_cleanup?
      return false if device_version.to_i < 13

      return true unless device_logger_reliable?
      return true if force_clean_files_app?

      if device_state.device_logger_detected_file_download_triggered_file_present?
        BrowserStack.logger.info("Device logger detected file(s) download triggered, proceeding with cleanup")
        return true
      end

      BrowserStack.logger.info("Skipping files app cleanup")
      zombie("skipping-files-app-cleanup")
      false
    end

    def force_clean_files_app?
      if device_state.force_clean_files_app_file_older_than_days?(CLEANUP_STEPS_FREQ[:force_clean_files_app])
        BrowserStack.logger.info("Force cleaning files app")
        true
      else
        false
      end
    end

    def files_app_restricted?(e)
      return false unless e.instance_of?(BrowserStackTestExecutionError)

      e.test_output.include?("com.apple.DocumentsApp because it is restricted")
    end

    def set_time_to_utc
      xcui_output = cleanup_iphone.set_time_to_utc
      return if xcui_output.nil?

      device_utc_timestamp = xcui_output.string_between_markers("Time interval since 1970:", "\n").to_i
      device_time_drift = (device_utc_timestamp - Time.now.to_i).abs

      # We want to check whether there's any drift in the device time when we set the
      # device time setting to manual. The drift in time might occur because the clock
      # inside the device might not be as accurate as the server's clock from where the
      # iOS device fetches the time.
      # A couple of minutes of drift is fine as we aren't pushing directly from the ios-njb-app.
      zombie('idevice-time-drift', '', { 'data' => device_time_drift })
    end

    def need_safari_bookmarks_cleanup?
      return true unless device_logger_reliable?
      return true if need_force_clean_safari_bookmarks?

      if device_state.device_logger_detected_safari_bookmarks_launched_file_present?
        BrowserStack.logger.info("Device logger detected safari add bookmark launch for app session, proceeding with cleanup")
        return true
      end

      BrowserStack.logger.info("Skipping Safari Bookmark cleanup")
      zombie("skipping-safari-bookmarks-cleanup")
      false
    end

    def need_force_clean_safari_bookmarks?
      if device_state.force_clean_safari_bookmarks_file_older_than_days?(CLEANUP_STEPS_FREQ[:force_clean_safari_bookmarks])
        BrowserStack.logger.info("Force cleaning safari-bookmarks")
        true
      else
        false
      end
    end

    def need_safari_favorites_cleanup?
      return true unless device_logger_reliable?
      return true if need_force_clean_safari_favorites?

      if device_state.device_logger_detected_safari_favorites_launched_file_present?
        BrowserStack.logger.info("Device logger detected safari add favorites launch for app session, proceeding with cleanup")
        return true
      end

      BrowserStack.logger.info("Skipping Safari Favorites cleanup")
      zombie("skipping-safari-favorites-cleanup")
      false
    end

    def need_force_clean_safari_favorites?
      if device_state.force_clean_safari_favorites_file_older_than_days?(CLEANUP_STEPS_FREQ[:force_clean_safari_favorites])
        BrowserStack.logger.info("Force cleaning safari-favorites")
        true
      else
        false
      end
    end

    def need_safari_tab_groups_cleanup?
      return false if device_version.to_i < 15
      return true unless device_logger_reliable?
      return true if need_force_clean_safari_tab_groups?

      if device_state.device_logger_detected_safari_tab_groups_opened_file_present?
        BrowserStack.logger.info("Safari New Tab Group Popup detected. Proceeding with Safari Tab Groups cleanup.")
        device_state.remove_device_logger_detected_safari_tab_groups_opened_file
        return true
      end

      BrowserStack.logger.info("Skipping Safari tab groups cleanup.")
      false
    end

    def need_force_clean_safari_tab_groups?
      if device_state.force_clean_safari_tab_groups_file_older_than_days?(CLEANUP_STEPS_FREQ[:force_clean_safari_tab_groups])
        BrowserStack.logger.info("Force cleaning Safari tab groups")
        device_state.touch_force_clean_safari_tab_groups_file
        true
      else
        false
      end
    end

    def need_clean_safari_app_history?
      return false if device_version.to_i < 18
      return true unless device_logger_reliable?
      return true if need_force_clean_safari_app_history?

      if device_state.device_logger_detected_safari_website_settings_opened_file_present?
        BrowserStack.logger.info("Safari Website Settings Opened. Proceeding with Safari App History cleanup.")
        return true
      end

      BrowserStack.logger.info("Skipping Safari App History cleanup.")
      false
    end

    def need_force_clean_safari_app_history?
      if device_state.force_clean_history_from_safari_app_file_older_than_days?(CLEANUP_STEPS_FREQ[:force_clean_history_from_safari_app])
        BrowserStack.logger.info("Force cleaning Safari App History")
        true
      else
        false
      end
    end

    def need_safari_websocket_disable?
      return false if device_version.to_i >= 17
      return false if device_version.to_i < 15
      return true if device_state.safari_websocket_file_clean_on_weekend?(CLEANUP_STEPS_FREQ[:safari_experimental_feature_websocket_disable])

      false
    end

    def need_standby_mode_disable?
      return false if device_version.to_i < 17
      return false if device_type == "iPad"
      return true if device_state.standby_mode_file_clean_on_weekend?(CLEANUP_STEPS_FREQ[:disable_standby_mode])

      false
    end

    def need_safari_url_bar_position_cleanup?
      return false if device_version.to_i < 15
      return true if device_state.device_logger_detected_safari_url_bar_changed_file_present? || !device_logger_reliable?

      false
    end

    def need_force_clean_apple_id?
      if device_state.force_clean_apple_id_file_older_than_days?(CLEANUP_STEPS_FREQ[:force_clean_apple_id])
        BrowserStack.logger.info("Force cleaning apple_id")
        true
      else
        false
      end
    end

    def need_redirect_extension_cleanup?
      return true if device_state.enable_redirect_extension_file_older_than_days?(CLEANUP_STEPS_FREQ[:enable_redirect_extension])

      false
    end

    def need_orientation_lock_cleanup?
      return true unless device_logger_reliable?
      return true if device_state.device_logger_detected_orientation_lock_changed_file_present?
      return true if need_force_check_orientation_lock?

      false
    end

    def need_force_check_orientation_lock?
      if device_state.force_check_orientation_lock_file_clean_on_weekend?(CLEANUP_STEPS_FREQ[:check_orientation_lock])
        BrowserStack.logger.info("Force checking orientation lock")
        true
      else
        false
      end
    end

    def need_sandbox_account_cleanup?
      return true unless device_logger_reliable?
      return true if need_force_clean_sandbox_account?

      return true if device_state.device_logger_detected_sandbox_signin_logline_file_present?
      return true if device_state.device_logger_detected_sandbox_signin_popup_file_present?
      return true if device_state.device_logger_detected_sandbox_signed_in_file_present?

      false
    end

    def need_force_clean_sandbox_account?
      if device_state.force_clean_sandbox_account_file_older_than_days?(CLEANUP_STEPS_FREQ[:force_clean_sandbox_account])
        BrowserStack.logger.info("Force cleaning sandbox_account")
        true
      else
        false
      end
    end

    def need_testflight_cleanup?
      return true if device_state.sign_in_to_app_store_popup_detected_file_present?
      return true unless device_logger_reliable?
      return true if need_force_clean_testflight?

      if was_used_in_session?("testflight")
        BrowserStack.logger.info("Testflight app launch detected. Proceeding with testflight cleanup.")
        return true
      end

      BrowserStack.logger.info("Skipping testflight cleanup.")
      false
    end

    def need_force_clean_testflight?
      if device_state.force_clean_testflight_file_older_than_days?(CLEANUP_STEPS_FREQ[:force_clean_testflight])
        BrowserStack.logger.info("Force cleaning testflight")
        device_state.touch_force_clean_testflight_file
        true
      else
        false
      end
    end

    def need_safari_cleanup? #rubocop:todo Metrics/AbcSize
      # In iOS, for automate/percy only Safari is supported
      return true if ['automate', PERCY].include?(genre)
      return true unless device_logger_reliable?
      return true if need_force_clean_safari?

      if device_version.to_f >= 16.2 && device_version.to_f <= 17.0
        processes = PyMobileDevice::Processes.list_processes(device_id)
        return true if processes.empty?
        return true if processes.include?("MobileSafari")
      end

      if device_state.safari_live_session_file_present?
        BrowserStack.logger.info("Need Safari cleanup as it was a Safari Live session")
        return true
      end

      if device_state.device_logger_detected_safari_launched_file_present?
        BrowserStack.logger.info("Device logger detected safari launch for app session, proceeding with cleanup")
        return true
      end

      if device_version.to_i >= IOS_WATCHER_MIN_IOS_VERSION
        return true if device_state.watcher_unreliable_file_present? || device_state.watcher_unreliable_off_usb_file_present?

        opened_apps = IosWatcher.apps_opened_during_session_set(device_id)
        return true if opened_apps.empty? # no apps were detected by watcher

        if opened_apps.any? { |app| app.downcase.include?('safari') } # catch all cases of safari
          BrowserStack.logger.info("Watcher detected safari")
          zombie("watcher-detected-safari") # logging to catch cases where watcher watched but others didnt
          return true
        end
      else
        # returning true if watcher is not supported to run safari cleanup
        BrowserStack.logger.info("Watcher not supported on os version #{device_version}")
        return true
      end

      BrowserStack.logger.info("Skipping Safari cleanup")
      zombie("skipping-safari-cleanup")
      false
    end

    def need_chrome_cleanup?
      if device_version.to_i >= 17
        return true if device_state.watcher_unreliable_file_present? || device_state.watcher_unreliable_off_usb_file_present?

        opened_apps = IosWatcher.apps_opened_during_session_set(device_id)
        return true if opened_apps.empty? # no apps were detected by watcher

        if opened_apps.any? { |app| app.downcase.include?('chrome') } # catch all cases of chrome
          BrowserStack.logger.info("Watcher detected chrome")
          zombie("watcher-detected-chrome") # logging to catch cases where watcher watched but others didnt
          return true
        end
      elsif ["live_testing", "app_live_testing", nil].include?(genre)
        return true
      end
      return true unless device_logger_reliable? # needed for QA to run their tests
      return true if device_state.chrome_cleanup_required_file_present?

      BrowserStack.logger.info("Skipping Chrome cleanup")
      false
    end

    def need_chromium_cleanup?
      return false if device_version.to_f < 14.0
      return true if need_force_clean_chromium?
      return true if ['automate'].include?(genre) && session_params['browserName'] && session_params['browserName'].include?('chromium')
      return true unless device_logger_reliable?

      if device_state.device_logger_detected_chromium_launched_file_present?
        BrowserStack.logger.info("Device logger detected chromium launched, proceeding with cleanup")
        return true
      end

      BrowserStack.logger.info("Skipping Chromium cleanup")
      false
    end

    def need_apple_wallet_cleanup?
      if device_version.to_i >= 13 && device_type != "iPad" && device_state.device_logger_detected_apple_wallet_launched_file_present?
        BrowserStack.logger.info("Device logger detected apple wallet launch, proceeding with cleanup")
        return true
      end

      if device_version.to_i >= 13 && device_type != "iPad" && device_state.force_clean_apple_wallet_file_older_than_days?(CLEANUP_STEPS_FREQ[:force_clean_apple_wallet])
        BrowserStack.logger.info("Force cleaning apple wallet")
        return true
      end

      BrowserStack.logger.info("Skipping Apple Wallet cleanup")
      zombie("skipping-apple-wallet-cleanup")
      false
    end

    def need_apple_wallet_enable_double_click_side_button?
      return false unless Secure::ApplePay.apple_pay_device?(@uuid)

      device_state.apple_wallet_double_click_side_button_enabled_file_clean_on_weekend?(CLEANUP_STEPS_FREQ[:wallet_double_click_side_button])
    end

    def need_force_clean_safari?
      if device_state.force_clean_safari_file_older_than_days?(CLEANUP_STEPS_FREQ[:force_clean_safari])
        BrowserStack.logger.info("Force cleaning safari")
        true
      else
        false
      end
    end

    def need_force_clean_chromium?
      if device_state.force_clean_chromium_file_older_than_days?(CLEANUP_STEPS_FREQ[:force_clean_chromium])
        BrowserStack.logger.info("Force cleaning chromium")
        true
      else
        false
      end
    end

    def need_configure_assistive_touch?
      if device_version.to_i >= 16
        assistive_touch_configuration = @lockdown_device_state.get_assistive_touch_configuration_key.to_s.chomp
        if assistive_touch_configuration.empty?
          BrowserStack.logger.info("iOS 16 and above detected without assistive touch configuration state file, configuring assistive touch menu")
          return true
        end
      end
      false
    end

    def need_kill_apps?
      return true unless device_logger_reliable?
      return true if need_force_kill_apps?

      app_store_opened = was_used_in_session?("appstore")
      dialer_app_opened = was_used_in_session?("dialer_app")
      if device_type == "iPhone"
        app_store_opened || dialer_app_opened
      else
        # device_type == "iPad"
        app_store_opened
      end
    end

    def need_force_kill_apps?
      if device_state.force_kill_apps_file_older_than_days?(CLEANUP_STEPS_FREQ[:force_kill_apps])
        BrowserStack.logger.info("Force killing apps")
        true
      else
        false
      end
    end

    def fetch_delete_media_log_file
      BrowserStack.logger.info("Fetching delete_media.log from the device")
      delete_media_log = "/tmp/delete_media_#{device_id}/Documents/delete_media.log"
      result, status = BrowserStack::OSUtils.execute(
        "/usr/local/bin/gtimeout -s KILL 20 #{IOS_DEPLOY} "\
        "-I --id #{device_id} -1 #{BROWSERSTACK_APP_BUNDLE_ID} "\
        "-w Documents/delete_media.log --to /tmp/delete_media_#{device_id}", true
      )
      if status == 0
        delete_media_log_lines = File.read(delete_media_log)
        File.readlines(delete_media_log).each do |line|
          BrowserStack.logger.info(line)
        end
      end
    rescue => e
      BrowserStack.logger.error("Parsing delete media file failed")
    ensure
      FileUtils.rm_f(delete_media_log)
    end

    def preload_media_ios_njb_app
      is_ios15 = device_version.to_f >= 15.0
      timeout = is_ios15 ? 60 : 10
      using_alternate_media_clean = device_version.to_f >= ALTERNATE_MEDIA_CLEANUP_MIN_IOS_SUPPORT_VERSION
      timeout = 70 if using_alternate_media_clean
      optimised_flow = ios_device.device_eligible_for_optimised_flow?
      cleanup_iphone.preload_media_ios_njb_app(timeout, XCUITEST_CAPTURE_SYSLOG, optimised_flow)
    rescue => e
      # This test will always fail as we forcefully kill the test after 10 seconds.
      # We just need to launch the BrowserStack app which will kill itself after preloading media.
      # Has different affect on some ios15 devices, timeout is too short for the external app to start. MOB-9601
      msg = "Started loading media using the ios-njb-app; #{e.message}, #{e.backtrace}"
      is_ios15 ? BrowserStack.logger.error(msg) : BrowserStack.logger.info(msg)

      num_cleanup_retries = begin
        File.read(device_state.send(:cleanupdone_file)).to_i
      rescue
        0
      end
      device_state.touch_reinstall_browserstack_app_file if num_cleanup_retries > 2 # Trigger re-install of bs app if preload media is consistently failing
      if optimised_flow
        BrowserStack.logger.info("Optimised Preload media failed with error #{e.message}")
        cleanup_iphone.unmark_method(:install_preloaded_files)
        raise e
      end
      if using_alternate_media_clean
        BrowserStack.logger.info("Preload media failed. Should throw error as this method deletes user's photos after session")
        BrowserStack.logger.error(msg)
        raise e unless cleanup_iphone.photos_sqlite_state_consistent?
      end
    ensure
      BrowserStack.logger.info("Populating delete media logs")
      fetch_delete_media_log_file
    end

    # NOTE: this needs to come before open_photos_app automation
    # Note: this needs to come after the dismiss_system_popups to ensure no sim card popup.
    # Sometimes this causes photos app to crash on opening, so we need to trial open it in cleanup.
    def handle_preload_media
      verify_backup_preloaded_files
      if device_version.to_i >= 13
        @media_files_preloaded = cleanup_iphone.install_preloaded_files
      else
        cleanup_iphone.install_preloaded_files_legacy
      end
      cleanup_iphone.cleanup_custom_media
    end

    def check_needs_full_reset
      return unless device_state.apple_id_sign_in_requested_file_present?

      file_created_at = device_state.apple_id_sign_in_requested_file_created_at
      BrowserStack.logger.info("Apple ID sign in requested file created at: #{file_created_at}")

      device_re_mdmed_at = Time.parse(IosMdmServiceClient.mdm_last_seen(uuid))
      BrowserStack.logger.info("Device remdmed at: #{device_re_mdmed_at}")

      if device_re_mdmed_at > file_created_at
        BrowserStack.logger.info("Device can be brought online as it was re-provisioned")
        device_state.remove_apple_id_sign_in_requested_file
        device_state.remove_check_global_proxy_file # remove this file so that cleanup can run this check to install all the mdm profiles
      else
        raise OFFLINE_REASON_DEVICE_STUCK_WITH_APPLE_ID
      end
    end

    def device_logger_reliable?
      @device_logger_reliable
    end

    def install_configuration_profiles
      BrowserStack.logger.info("Force installing configuration profiles")
      # Install MDM and CFGUTIL profiles
      if cleanup_iphone.configuration_profiles_manager.device_uses_cfgutil_managed_profiles?
        cleanup_iphone.configuration_profiles_enforcer.enforce_configuration_profiles(true)
      else
        check_device.check_mdm_settings(
          @proxy_pac_url,
          true,
          1,
          @@config['mdm_profiles_required'],
          device_name,
          device_version,
          '',
          0
        )
      end

      Zombie.push_logs('force-installed-mdm-profiles', '', { 'device' => device_id })
    end

    def device_logger_reported_untrust?
      device_logger_reported_untrust = false
      if File.exist?(@device_logger_popups_file)
        file_contents = File.read(@device_logger_popups_file).strip
        # Known values for the file - SBAppProfileNotTrustedAlertItem, SBAppProfileNeedsValidationAlertItem
        BrowserStack.logger.info "File contents of popup file: #{file_contents}"
        if ["SBAppProfileNotTrustedAlertItem", ""].include?(file_contents)
          # File contents will be empty at the time of this deploy as before
          # we just used to touch the file and not write the popup reason
          # Sending to zombie will be done after this block
          device_logger_reported_untrust = true
        else
          # Sending to zombie for other popups except untrust popup
          BrowserStack::Zombie.push_logs(
            "unknown_device_popup",
            file_contents,
            "os" => "ios_njb",
            "device" => uuid,
            "browser" => "",
            "terminal_type" => "realMobile",
            "session_id" => session_id,
            "data" => genre
          )
          @influxdb_client.event(uuid, 'unknown-device-popup', component: 'cleanup', subcomponent: 'session-popups', is_error: true)
          FileUtils.rm_f(@device_logger_popups_file)
        end
      end
      device_logger_reported_untrust
    end

    def zombie(kind, error = '', data = {})
      defaults = {
        "os" => "ios_njb",
        "device" => @uuid,
        "terminal_type" => "realMobile",
        "os_version" => device_version,
        "session_id" => session_id,
        "user_os" => genre
      }
      BrowserStack::Zombie.push_logs(kind, error, defaults.merge(data))
    end

    def add_language_to_device(current_lang)
      additional_language = get_additional_language(current_lang)
      BrowserStack.logger.info("Additional language to be added: #{additional_language}")
      language_util = LanguageUtil.new({ device_id: device_id })
      begin
        language_util.update_language(additional_language)
      rescue => e
        BrowserStack.logger.error("Error adding language to the device #{e.message}: #{e.backtrace}")
        zombie(
          "alanguage-addition-failed",
          "#{e.class} - #{e.message}"
        )
      end
      language_util.update_language(current_lang)
      BrowserStack.logger.info("Device language set back to default sub region language: #{current_lang}")
      # lockdownclient process to set the language is carried out in the background
      # so we need to wait for some time to get the language set
      # There is no way of determining when exactly the language is set
      # find_language retruns the new language set on the device even before it is actually set, so sleep is inevitable
      sleep 10
      current_default_lang = language_util.find_language
      BrowserStack.logger.info("#{device_id} device language is: #{current_default_lang}")

      device_state.touch_language_file if current_default_lang == current_lang
      true if current_default_lang.include?('en')

    rescue => e
      current_default_lang = language_util.find_language
      BrowserStack.logger.error("Error adding language to the device #{e.message}: #{e.backtrace}")
      zombie(
        "language-addition-failed",
        "#{e.class} - #{e.message}"
      )
      true if current_default_lang.include?('en')
    end

    def get_additional_language(device_language)
      device_language != 'en-GB' ? 'en-GB' : 'en-IN'
    end

    def need_language_addition?
      device_version.to_i >= 13 && device_state.language_file_clean_on_weekend?(CLEANUP_STEPS_FREQ[:language_addition])
    end

    def can_be_launched_by_name?(display_name, locale, region)
      display_name && locale == "en-US" && region == "en-US"
    end

    def cleanup_iphone_with_timeout
      # TODO: hotfix, this was 5 min, too short
      Timeout.timeout(15 * 60) do
        pre_automation_cleanup
        if @dedicated_device
          dedicated_automation_cleanup
        else
          automation_cleanup
        end
        post_automation_cleanup
        # TODO: move to automation_cleanup
        cleanup_iphone.clean_orientation if need_orientation_lock_cleanup?
        BrowserStack.logger.info "Device Cleanup automation steps done"
        true
      end
    end

    def lock_device_successful
      case device_version.to_i
      when 10, 11
        mdm_lock_device
      else
        begin
          BrowserStack.logger.info("Locking device with XCUI")
          BrowserStackAppHelper.run_ui_test(@uuid, :lock_device, session_id: nil)
        rescue => e
          BrowserStack.logger.error("Error - locking device with XCUI: #{e.message}")
          BrowserStack.logger.info("Falling back to MDM lock")
          mdm_lock_device
        end
      end
      true
    end

    def mdm_lock_device
      BrowserStack.logger.info("Initiating Lock request to MDM for device #{@uuid}")
      zombie("mdm_lock_command")

      response = IosMdmServiceClient.lock_device(@uuid)
      true
    rescue => e
      BrowserStack.logger.error("Error locking device with MDM: #{e.message} !")
      BrowserStack::Zombie.push_logs(
        "mdm_lock_failed",
        "",
        "os" => "ios_njb",
        "device" => @uuid,
        "terminal_type" => "realMobile"
      )
      false
    end

    def post_reboot
      BrowserStack.logger.info "Remove NO-SIM popup.."
      unlock_device
      lock_device
      unlock_device
      BrowserStack.logger.info "Restart appium.."
      appium_server.start_server_for_version(@@config['default_appium_version'], true)
      BrowserStack.logger.info "Get driver to ensure wda running"
      appium_server.driver
    end

    def verify_backup_preloaded_files
      # No need to do all this for ios 13 as photos.sqlite is already included in bundle
      return if device_version.to_i >= 13

      # download from s3 (handled in machine_check)
      return unless File.exist?(@@config['bs_media'])

      BrowserStack.logger.info "Checking if phone backup is present.."
      return if File.exist?("#{@@config['photo_data']}/#{uuid}/Photos.sqlite")

      BrowserStack.logger.info "Backup not found.. Copying media files.."
      cleanup_iphone.copy_media

      BrowserStack.logger.info "Rebooting.."
      DeviceManager.reboot_and_wait(uuid)

      BrowserStack.logger.info "Post reboot checks.."
      post_reboot

      BrowserStack.logger.info "Creating backup.."
      cleanup_iphone.backup_preloaded_files
    end

    # TODO: Refactor this.
    def get_all_installed_apps
      uploaded_apps = begin
        File.read("/tmp/#{uuid}_installed_apps/UPLOADED_APPS")
      rescue
        ""
      end.split(/\n+/).uniq
      all_installed_apps = begin
        File.read("/tmp/#{uuid}_installed_apps/ALL_INSTALLED_APPS")
      rescue
        ""
      end.split(/\n+/).uniq
      testflight_apps = begin
        File.read("/tmp/#{uuid}_installed_apps/TESTFLIGHT_APPS")
      rescue
        ""
      end.split(/\n+/).uniq

      system_apps = begin
        IdeviceUtils.list_apps(udid, kind: :system, attempts: 2).map { |app| app[:bundle_id] }
      rescue
        []
      end
      updated_apps = system_apps & all_installed_apps
      BrowserStack.logger.info "system_apps #{system_apps} all_installed_apps #{all_installed_apps}"

      # removing these two app updates explicitly in case logic above fails to do so
      all_installed_apps -= @app_updates
      [uploaded_apps, all_installed_apps - @known_apps - updated_apps, testflight_apps, updated_apps]
    end

    def send_user_apps_to_cls(uploaded_apps, all_installed_apps, testflight_apps, updated_apps)
      store_apps = all_installed_apps - uploaded_apps - testflight_apps
      push_to_cls(session_params, "store_apps", '', { "store_apps" => store_apps }) unless store_apps.empty?
      push_to_cls(session_params, "testflight_apps", '', { "testflight_apps" => testflight_apps }) unless testflight_apps.empty?
      push_to_cls(session_params, "uploaded_apps", '', { "uploaded_apps" => uploaded_apps }) unless uploaded_apps.empty?
      push_to_cls(session_params, "updated_apps", '', { "updated_apps" => updated_apps }) unless updated_apps.empty?
    end

    def send_user_apps_to_eds(uploaded_apps, all_installed_apps, testflight_apps, updated_apps)
      store_apps = all_installed_apps - uploaded_apps - testflight_apps
      data = {
        product: {
          store_app_count: store_apps,
          testflight_app_count: testflight_apps,
          uploaded_app_count: uploaded_apps,
          updated_app_count: updated_apps
        },
        session_id: session_id
      }
      Utils.send_to_eds(data, EdsConstants::APP_LIVE_TEST_SESSIONS, true)
      # sending bundle ids to web_events
      web_event_data = {
        event_name: "bundle_ids_installed",
        store_apps: store_apps,
        testflight_app: testflight_apps,
        uploaded_app: uploaded_apps,
        updated_app: updated_apps,
        session_id: session_id,
        platform: "realios"
      }
      Utils.send_to_eds(web_event_data, EdsConstants::APP_LIVE_WEB_EVENTS, true)
    end

    def minified_app_cleanup # rubocop:todo Metrics/AbcSize
      return unless genre

      BrowserStack.logger.info "In minified_app_cleanup for app_testing session"
      minified_app_cleanup_start_time = Time.now
      begin
        # We do not have any way of clllearing the app data on iOS for now hence uninstalling all the user apps,
        # these will be installed in firecmd.
        ([session_params['app_testing_bundle_id'], session_params['app_details_bundle_id'], session_params['test_suite_bundle_id']] + session_params["other_app_bundle_ids"].to_a + session_params['mid_session_app_bundle_ids'].to_a).each do |bundle_id|
          if !bundle_id.nil? && !bundle_id.empty? && !['undefined', IPhone::APPSTORE_APP, IPhone::TESTFLIGHT_APP, IPhone::FILES_APP, IPhone::SAFARI_APP].include?(bundle_id)
            IdeviceUtils.uninstall_app(uuid, bundle_id)
            BrowserStack.logger.info "Uninstall attempted of #{bundle_id}"
          end
        end
      rescue => e
        BrowserStack.logger.error("Exception while installing #{error_tag}: #{e.message}, backtrace: #{e.backtrace.join("\n")}")
        raise "Exiting minified flow as uninstall_and_reinstall_app excepted with #{e.message}"
      end
      BrowserStack.logger.info("MINIFIED FLOW : Proc uninstall_and_reinstall_app took #{Time.now - minified_app_cleanup_start_time}s")
    end

    def needs_enterprise_app_cleanup?(bundle_id)
      is_client_enterprise_app? && [session_params['app_testing_bundle_id'], session_params['app_details_bundle_id']].include?(bundle_id)
    end

    def app_actually_present?(bundle_id)
      apps = IdeviceUtils.list_user_installed_apps(uuid, attempts: 2)
      apps.include?(bundle_id)
    end

    def dismiss_system_popups
      if ['app_live_testing'].include?(genre)
        BrowserStack.logger.info "Pressing home twice to dismiss system popups if there are any for: #{genre}"
      else
        BrowserStack.logger.info "Not dismissing popups for genre: #{genre}"
      end

      begin
        unlock_device(20)
        if device_version.to_f < 17.0
          press_home

          # TODO: Relook this, and see if it is required or not
          sleep 2
          press_home
        end
      rescue => e
        BrowserStack::Zombie.push_logs(
          "home_press_error",
          "",
          "os" => "ios_njb",
          "device" => uuid,
          "browser" => "",
          "terminal_type" => "realMobile",
          "data" => e.message,
          "session_id" => session_id
        )
        @influxdb_client.event(uuid, 'home-press-error', component: 'cleanup', subcomponent: 'dismiss-popups', is_error: true)
      end
    end

    def cleanup_device # rubocop:todo Metrics/AbcSize
      unlock_device(20)
      cleanup_iphone_with_timeout
    rescue AppleIDVerificationException
      raise
    rescue => e
      BrowserStack.logger.info "Cleanup Got exception for #{uuid}: #{e.message}:\n Retrying ! Locking phone and Retrying cleanup (to remove any sim card popups)"

      # appium main.js is stuck at this point, so killing appium related stuff
      if e.message.match("execution expired") && e.backtrace.join("\n").match("start_driver")
        BrowserStack::Zombie.push_logs("cleanup-appium-stuck", "", { "device" => uuid })
        # uninstalling wda as appium is stuck uninstalling wda
        BrowserStack::IPhone.uninstall_wda(uuid)

        %w[xcodebuild iproxy appium.*main.js].each do |process|
          BrowserStack.logger.info "killing #{process}"
          OSUtils.kill_process(process, uuid, 9)
        end
        # sleep for main.js to come up
        sleep 30
      end

      begin
        2.times do
          if device_version.to_f >= 18.3
            break if lock_device_from_wda_with_fallback.nil?
          elsif lock_device_successful
            break
          end

          sleep 10
        end

        unlock_device(20)
        cleanup_iphone_with_timeout
      rescue Selenium::WebDriver::Error::UnknownError => e
        BrowserStack.logger.error "Error in iPhone::cleanup_device #{e.message} #{e.backtrace}"
        case e.message
        when /xcodebuild failed with code 65/
          raise "xcodebuild failed with code 65"
        when /if you are using useXctestrunFile capability then you need to have/
          raise "xctestrunfile missing"
        else
          raise "webdriver build error #{e.message}"
        end
      end

    end

    def kill_launcher_app
      BrowserStack.logger.info "kill_launcher_app called => uuid : #{uuid}"
      begin
        wda_client.kill_apps([Launcher::BUNDLE_ID])
      rescue => e
        BrowserStack.logger.error "Failed to kill #{Launcher::BUNDLE_ID} app with error #{e.message} => uuid : #{uuid}"
      end
    end

    def is_client_enterprise_app?
      File.exists?(client_enterprise_app_file)
    end

    def client_enterprise_app_file
      "/tmp/client_enterprise_app_#{uuid}"
    end

    def need_set_safari_default_browser_in_eu?
      return false if device_version.to_f < 17.5 && device_version.to_f >= 18.2
      return false unless ["eu-central-1", "eu-west-1"].include?(@device_config["region"])

      return true if device_state.force_set_safari_default_browser_in_eu_file_present?

      return false if device_state.set_safari_default_browser_in_eu_file_present?

      apple_id_signed_in_detected_by_devicelogger = !device_logger_reliable? || device_state.device_logger_detected_apple_id_signed_in_file_present?
      true if apple_id_signed_in_detected_by_devicelogger
    end

    def need_safari_reading_list_cleared?
      return true if device_version.to_i == 10

      # return true unless device_logger_reliable?

      File.exist? "/tmp/safari_reading_list_opened_#{uuid}"
    end

    def needs_media_preload?
      device_state.needs_preload_media_file_present?
    end

    def need_low_power_mode_disabled?
      File.exist? "/tmp/low_power_mode_opened_#{uuid}"
    end

    def need_enable_wifi?
      device_state.enabled_wifi_file_older_than_days?(CLEANUP_STEPS_FREQ[:enable_wifi])
    end

    def need_reset_keyboard_settings?
      device_state.reset_keyboard_settings_file_older_than_days?(CLEANUP_STEPS_FREQ[:reset_keyboard_settings])
    end

    def need_handle_app_store_popups?
      device_state.app_store_popup_handled_file_clean_on_weekend?(CLEANUP_STEPS_FREQ[:handle_app_store_popups])
    end

    # Need to do once per device
    def need_siri_contacts_cleanup?
      device_version.to_i >= 11 && device_state.siri_contacts_cleanup_file_older_than_days?(CLEANUP_STEPS_FREQ[:siri_contacts_cleanup])
    end

    def need_disable_dark_mode?
      device_version.to_i >= 13 && device_state.disable_dark_mode_file_clean_on_weekend?(CLEANUP_STEPS_FREQ[:disable_dark_mode])
    end

    def need_safari_remote_automation?
      device_version.to_f >= 15.0 && device_state.safari_remote_automation_file_older_than_days?(CLEANUP_STEPS_FREQ[:safari_remote_automation])
    end

    def need_imessages_app_cleanup?
      device_version.to_f >= 13.0 && device_state.sim_enabled_file_present?
    end

    def need_imessages_app_cleanup_for_non_sim_devices?
      # Running this for all non sim devices periodically
      device_state.clean_sms_file_older_than_days?(CLEANUP_STEPS_FREQ[:clean_sms]) && device_version.to_i >= 13
    end

    def need_remove_extra_keyboards?
      # TODO: enable for all OS versions
      [12, 13, 14, 15, 16, 17].include?(device_version.to_i) && device_state.remove_extra_keyboards_file_clean_on_weekend?(CLEANUP_STEPS_FREQ[:keyboard_reset])
    end

    def need_call_logs_cleanup?
      device_version.to_f >= 13.0 && device_state.call_logs_file_present?
    end

    def need_date_time_cleanup?
      device_version.to_f >= 13.0 && (device_state.custom_time_file_present? || device_state.custom_date_file_present? || device_state.date_time_automatic_file_present? || device_state.custom_time_format_set_file_present?)
    end

    def need_favorite_contact_cleanup?
      device_state.favorite_contact_file_present?
    end

    def need_sim_cleanup?
      DeviceSIMHelper.sim?(@uuid) && !device_state.dedicated_device_file_present?
    end

    def sim_validation?
      DeviceSIMHelper.sim_config_present?(@uuid) && !device_state.dedicated_device_file_present? && device_state.sim_validation_check_file_older_than_days?(1)
    end

    def need_check_sim_signal_strength?
      return false unless DeviceSIMHelper.sim?(@uuid) && device_state.dedicated_device_file_present?

      # check if sim signal strength file is present and older than 60 minutes
      if !device_state.sim_signal_strength_file_present? || device_state.sim_signal_strength_file_older_than_minutes?(60)
        device_state.touch_sim_signal_strength_file # touch the file, so that every 60 minutes we check the signal strength
        return true
      end

      false
    end

    def need_contacts_cleanup?
      return true if device_version.to_f <= 13.2

      device_state.contacts_modified_file_present? || !verify_contacts_count
    end

    def need_disable_testflight_background_refresh?
      device_version.to_f >= 16.0 && ["eu-central-1", "eu-west-1"].include?(@device_config["region"]) && device_state.disable_testflight_background_refresh_file_clean_on_weekend?(CLEANUP_STEPS_FREQ[:disable_testflight_background_refresh])
    end

    def need_clean_stored_password?
      device_version_float = device_version.to_f
      device_version_float >= 14.0 && device_version_float < 18.0 &&
        (device_state.device_logger_detected_password_added_file_present? ||
        device_state.clean_stored_password_file_clean_on_weekend?(CLEANUP_STEPS_FREQ[:clean_stored_password]))
    end

    # TODO: Dead code, can be removed later and it's related methods.
    def need_clean_passwords_app?
      device_version_float = device_version.to_f
      if device_version_float >= 18.0 && !IdeviceUtils.check_app_with_bundle_id_exists(device_id, PASSWORD_APP)
        BrowserStack.logger.info("Password app not present, proceeding with installation of password app")
        cleanup_iphone.install_first_party_system_apps(["passwords"], install_via: :MDM)
      end
      device_version_float >= 18.0 &&
      (device_state.device_logger_detected_password_added_file_present? ||
        device_state.clean_stored_password_file_clean_on_weekend?(CLEANUP_STEPS_FREQ[:clean_stored_password])) &&
        IdeviceUtils.check_app_with_bundle_id_exists(device_id, PASSWORD_APP)
    end

    def need_disable_apple_intelligence?
      device_version_float = device_version.to_f
      return false if device_version_float < 18.2

      device_state.disable_apple_intelligence_file_older_than_days?(CLEANUP_STEPS_FREQ[:disable_apple_intelligence])
    end

    def need_restart_webkit_proxy?
      device_version_float = device_version.to_f
      # NOTE: will be true only when device_version is not 18.3
      return true if device_version_float < 18.3 || device_version_float >= 18.4

      begin
        status = HttpUtils.test_url_code("http://localhost:#{port}/")
      rescue
        status = 0
      end
      return true if status != 200

      device_state.restart_webkit_proxy_file_older_than_days?(CLEANUP_STEPS_FREQ[:restart_webkit_proxy])
    end

    def need_theme_cleanup?
      device_version_float = device_version.to_f

      return false if device_version_float < 18.0

      return true unless device_logger_reliable?

      # check for state file : theme_change_attempted_DEVICEID
      device_state.theme_change_attempted_file_present? || device_state.theme_change_cleaned_file_clean_on_weekend?(CLEANUP_STEPS_FREQ[:theme_change_cleaned])

    end

    def need_disable_stage_manager?
      device_version_float = device_version.to_f
      device_version_float >= 16.0 && device_type == "iPad" &&
      !["iPad13,19", "iPad12,2"].include?(device_name) &&
        (device_state.device_logger_detected_stage_manager_enabled_file_present? ||
        device_state.disable_stage_manager_file_clean_on_weekend?(CLEANUP_STEPS_FREQ[:disable_stage_manager]))
    end

    def need_install_global_proxy_profile?
      device_state.check_global_proxy_file_clean_on_weekend?(CLEANUP_STEPS_FREQ[:check_global_proxy_installed])
    end

    def need_to_set_time_to_utc?
      device_state.set_time_to_utc_file_clean_on_weekend?(CLEANUP_STEPS_FREQ[:set_time_to_utc])
    end

    def need_reset_view_to_standard?
      device_state.reset_view_to_standard_file_clean_on_weekend?(CLEANUP_STEPS_FREQ[:reset_view_to_standard])
    end

    def wifi_enabled?
      wifi_status = "Disabled" # defaulting to false in case of WDA errors
      begin
        response = wda_client.get_wifi_status
        wifi_status = response["value"] || "Disabled"
      rescue => e
        BrowserStack.logger.info "[wifi_enabled?] error: #{e.message} #{e.backtrace}"
      end
      wifi_status == "Enabled"
    end

    def check_internet_sharing
      check_device.check_internet_sharing(@device_config['webdriver_port'])
    rescue MobileCheckException => e
      BrowserStack.logger.error "Internet sharing exception! #{e.message}"
    end

    def kill_xcode_build_and_iproxy
      BrowserStack.logger.info "Restarting xcodebuild"
      OSUtils.kill_process("xcodebuild", uuid.to_s)
      BrowserStack.logger.info "Restarting iproxy"
      OSUtils.kill_process("iproxy", uuid.to_s)
    end

    def unique_id
      unique_device_id_output = IdeviceUtils.ideviceinfo(uuid, "UniqueDeviceID").first
      if unique_device_id_output.include?("No device found with udid") ||
        unique_device_id_output.include?("Device #{uuid} not found!")
        nil
      else
        unique_device_id_output
      end
    end

    def check_lockdown
      BrowserStack.logger.info("LockDown Checking: #{uuid}")
      info_output = IdeviceUtils.ideviceinfo(uuid, "ProductType").first
      if info_output.include?("No device found with udid") || info_output.include?("Device #{uuid} not found!")
        BrowserStack.logger.error "Device not on idevice info, cleanup can't continue"
        FileUtils.touch(@unclean_not_on_ideviceinfo_file)
        FileUtils.rm_f(@unclean_lockdown_file)
        raise "Not on ideviceinfo"
      elsif $CHILD_STATUS.exitstatus != 0
        lockdown_code = info_output.split.last
        if ['-18', '-19'].include?(lockdown_code)
          BrowserStack.logger.error "I have trust issue # #{lockdown_code}"
        else
          BrowserStack.logger.error("ideviceinfo error: #{info_output} - check manually")
        end
        FileUtils.touch(@unclean_lockdown_file)
        FileUtils.rm_f(@unclean_not_on_ideviceinfo_file)
        raise "Lockdown error #{lockdown_code}"
      end
      FileUtils.rm_f(@unclean_not_on_ideviceinfo_file)
      FileUtils.rm_f(@unclean_lockdown_file)
    end

    def cleanup_live_testing_setup
      BrowserStack::WebDriverAgent.stop(uuid)
      BrowserStack::DeviceLogger.destroy(uuid)
    end

    # rubocop:todo Metrics/AbcSize
    def check_and_install_enterprise_dummy_app(device_details, dist_name)
      trust_start_time = Time.now

      BrowserStack.logger.info "[#{uuid}] enterprise app not present, installing and trusting app"

      install_enterprise_dummy_app

      if dummy_app_present?
        begin
          BrowserStack.logger.info "[#{uuid}] starting enterprise app trust"
          if !trust_dummy_app(device_details, dist_name)
            raise "dummy app trusting failed"
          else
            BrowserStack.logger.info "[#{uuid}] enterprise trust successful, time taken: #{Time.now - trust_start_time}"
            true
          end
        rescue => e
          BrowserStack.logger.info "[#{uuid}] failure: #{e.message} #{e.backtrace}"

          delete_enterprise_dummy_app
          if dummy_app_present?
            5.times do
              sleep 1
              install_enterprise_dummy_app # adding retry as ios-deploy sometimes doesnt uninstall directly, installing again and uninstalling works somehow
              delete_enterprise_dummy_app
              break unless dummy_app_present?
            end
            if dummy_app_present? # if it still doesnt work mark device offline and check
              BrowserStack.logger.info "[#{uuid}] retry failed to delete the app as well, marking device offline"
              return false
            else
              BrowserStack.logger.info "[#{uuid}] retry was successful in deleting the app, rebooting the phone and starting cleaup again"
            end
          end
          raise CleanupError, e.message
        end
      else
        raise CleanupError, "dummy app installation failed"
      end
    end

    # rubocop:enable Metrics/AbcSize

    def dummy_app_present?
      result = IdeviceUtils.check_app_with_bundle_id_exists(uuid, DUMMY_APP_BUNDLE_ID)
      BrowserStack.logger.info "[#{uuid}] enterprise app present?: #{result}"
      result
    end

    def install_enterprise_dummy_app
      BrowserStack.logger.info "[#{uuid}] starting enterprise app installation"
      IdeviceUtils.install_app(uuid, DUMMY_APP_PATH)
    end

    def delete_enterprise_dummy_app
      BrowserStack.logger.info "starting enterprise app uninstallation"
      BrowserStack::OSUtils.execute("#{IOS_DEPLOY} -9 --id #{uuid} -1 #{DUMMY_APP_BUNDLE_ID}")
    end

    def lock_unlock_trust_retry(app_trust, action)
      unless lock_device_successful
        sleep 10
        lock_device_successful
      end
      unlock_device
      app_trust.perform_action(action)
    end

    def trust_dummy_app_via_appium(device_details, dist_name)
      raise 'Appium port to send trust commands not found' if device_details[:appium_port].nil?

      dummy_app_trust = EnterpriseAppTrust.new(uuid, @device_config, device_details, dist_name)
      BrowserStack.logger.info("Doing dummy app trust using appium")

      begin
        unlock_device(20)
        return dummy_app_trust.perform_action('trust')
      rescue => e
        BrowserStack.logger.info "App Trust Got exception for #{uuid}: #{e.message}:\n Retrying ! Locking phone and Retrying trust (to remove any sim card popups)"
        begin
          return lock_unlock_trust_retry(dummy_app_trust, 'trust')
        rescue => e
          # Alerter.send("Appium Trust Failed twice for #{@uuid} : #{e.message}","Please check Trust Failed twice for #{@uuid} : #{e.backtrace.join("\n")}!")
          Zombie.push_logs("xcode-build-failure", e.message.to_s, { "device" => uuid }) if e.message.include?("xcodebuild failed")
          @influxdb_client.event(uuid, 'xcode-build-failure', component: 'cleanup', subcomponent: 'trust-dummy-app', is_error: true)
          raise "App Trust failed twice #{e.message}!"
        end
      end
      false
    end

    def disable_chrome_popups
      # TODO: Move this into automation phase of cleanup
      if File.exists?(@dismiss_chrome_popup) && @@config['platform_category'] != 'ios_njb_13_4'
        begin
          Chrome.dismiss_chrome_popup(uuid)
        rescue => e
          BrowserStack.logger.error "Error dismissing chrome popup: #{e.message}, #{e.backtrace}"
          raise "chrome popup dismissal failed"
        end
        FileUtils.rm_f(@dismiss_chrome_popup)
      end
    end

    def trust_dummy_app(device_details, dist_name)
      return trust_dummy_app_via_appium(device_details, dist_name) if Gem::Version.new(device_version) < Gem::Version.new(11)

      cleanup_iphone.trust_dummy_app_via_xcui
      true
    end

    def phased_reboot_file
      dir_name = "/usr/local/.browserstack/config/phased_reboots"
      FileUtils.mkdir_p(dir_name) unless Dir.exist? dir_name
      "#{dir_name}/#{uuid}_phased_reboot_done_new"
    end

    def phased_reboot_device
      reason_to_reboot = nil
      reason_to_reboot = "AppStore support on Applive" unless File.exist?(phased_reboot_file)
      reason_to_reboot ||= "Needs phased reboot file touched" if @device_state.needs_phased_reboot_file_present?

      # Adding below to reboot device only once a day for v2 video device reboots
      if @device_state.v2_video_needs_phased_reboot_file_present? && @device_state.day_wise_phased_reboot_file_older_than_days?(CLEANUP_STEPS_FREQ[:phased_reboot])
        reason_to_reboot ||= "V2 video needs phased reboot"
        @device_state.remove_v2_video_needs_phased_reboot_file
      end
      if reason_to_reboot.nil?
        BrowserStack.logger.info "No reboot required"
      else
        Zombie.push_logs("phased-reboot-requested", reason_to_reboot, { "device" => uuid })
        BrowserStack.logger.info "Rebooting device for #{reason_to_reboot}"
        DeviceManager.reboot_and_wait(uuid)

        BrowserStack.logger.info "Creating phased reboot done file"
        FileUtils.touch(phased_reboot_file)

        device_state.remove_needs_phased_reboot_file

        BrowserStack.logger.info "Post reboot checks.."
        post_reboot
        @device_state.touch_day_wise_phased_reboot_file
      end
    end

    def handle_app_store_popups
      BrowserStack.logger.info "handle_app_store_popups Started"
      AppStoreInstaller.handle_app_store_popups(@uuid)
      device_state.touch_app_store_popup_handled_file
      BrowserStack.logger.info "handle_app_store_popups Completed"
    rescue => e
      BrowserStack.logger.error "handle_app_store_popups Failed reason: #{e.message}"
    end

    def check_if_cameras_are_blocked # rubocop:todo Metrics/AbcSize

      BrowserStack.logger.info "Checking if it's time for a camera check (UUID: #{uuid})"
      if CameraTimer.is_it_time? uuid
        BrowserStack.logger.info "It is time for camera check!"
      else
        BrowserStack.logger.info "Nope, too soon for camera check to run"
        return
      end

      BrowserStack.logger.info "Acquiring a driver for camera check"
      driver = Automation.get_driver_for_app(uuid, CAMERA_APP)

      BrowserStack.logger.info "Performing the camera check"
      begin
        cameras_are_unblocked = IOSCameraCheck.are_cameras_unblocked? driver, uuid, device_version.to_f
        if cameras_are_unblocked
          screenshot_path = cameras_are_unblocked
          BrowserStack.logger.info "Camera exposed (#{screenshot_path})!"
          ip = File.read '/usr/local/.browserstack/whatsmyip'
          payload = {
            ip: ip,
            udid: uuid, # mind the spelling,
            screenshot_path: screenshot_path,
            scp_url: "app@#{ip}:#{screenshot_path}",
            serial_number: @device_config["device_serial"],
            name: @device_config["device_name"]
          }
          BrowserStack.logger.info "Payload: #{payload})"
          BrowserStack.logger.info "POSTing to Slack..."
          SlackClient.alert(payload)
        else
          BrowserStack.logger.info "No problems with the cameras."
        end
      rescue => e
        BrowserStack.logger.info "Error in camera check #{e.message}"
      end

      BrowserStack.logger.info "Updating camera check timestamp"
      CameraTimer.update_timestamp uuid

      BrowserStack.logger.info "Camera check done. Goodbye.."
    end

    def was_used_in_session?(thing)
      # Mostly the things passed are detected in Device-logger per session basis
      File.exists?("/tmp/#{thing}_opened_#{uuid}")
    end

    def open_photos_app # rubocop:todo Metrics/AbcSize
      BrowserStack.logger.info("Bounce photos app")

      if device_version.to_i < 13
        # This approach was already present, adding a new approach for ios 13 as at first launch,
        # the app several times crashes which leads to wda failure and resulting into device getting offline
        begin
          Timeout.timeout(10) do
            BrowserStack.logger.info("Launching photos app via appium")
            Automation.bounce_app(uuid, "com.apple.mobileslideshow")
            BrowserStack.logger.info("Photos app closed")
          end
        rescue
          @influxdb_client.event(uuid, 'open-photos-failed', component: 'cleanup', subcomponent: 'open-photos-app', is_error: true)
          BrowserStack.logger.info("Photos app not opening")
          raise 'Bounce Photos app unsuccessful'
        end
      elsif device_version.to_f < 14.1
        retries = 1
        begin
          Timeout.timeout(10) do
            BrowserStack.logger.info("Launching photos app via wda")
            wda_client.launch_app_with_bundle_id("com.apple.mobileslideshow")
            BrowserStack.logger.info("Photos app launched.")
          end
        rescue => e
          if retries > 0
            BrowserStack.logger.info("Retrying to open photos app.")
            retries -= 1
            retry
          end
          @influxdb_client.event(uuid, 'open-photos-failed', component: 'cleanup', subcomponent: 'open-photos-app', is_error: true)
          BrowserStack.logger.info("Photos app launch via wda failed #{e.message} #{e.backtrace}.")
          raise 'Bounce Photos app unsuccessful'
        end
      else
        BrowserStack.logger.info("No need to bounce photos app for iOS #{device_version}")
      end

      # For ios 14.0 onwards we need to kill it too, or the thumbnails won't regenerate.
      # EDIT: They regenerating when adding media, but not when deleting.
      if device_version.to_f >= 14.0
        BrowserStack.logger.info("Killing photos app via wda")
        wda_client.kill_apps(['com.apple.mobileslideshow'])
      end
    end

    # Enterprise app on launch clears the device keychain contents for Enterprise signed apps
    def open_enterprise_keychain_app
      BrowserStack.logger.info "Enterprise Keychain app"
      begin
        response = wda_client.unlock_device
        BrowserStack.logger.info("WDA unlock_device response for #{uuid} - #{response.inspect}")

        response = if @@config['platform_category'] == 'ios_njb_11'
                     wda_client.launch_app_with_app_display_name("enterpriseDummy")
                   else
                     wda_client.launch_app_with_bundle_id(DUMMY_APP_BUNDLE_ID)
                   end

        BrowserStack.logger.info("WDA launchapp enterpriseDummy app response for #{uuid} - #{response.inspect}")

      rescue RuntimeError => e
        BrowserStack::Zombie.push_logs("wda_enterprise_keychain_launch_error", "", { "os" => "ios_njb", "device" => uuid, "terminal_type" => "realMobile", "data" => e.message, "session_id" => session_id })
        BrowserStack.logger.error("Enterprise keychain launch error for #{uuid} - #{e.message} - #{e.backtrace}")
      ensure
        file_name = "/tmp/enterprise_keychain_opened_#{uuid}"
        File.delete file_name if File.exists?(file_name)
      end
      BrowserStack.logger.info "Enterprise Keychain app closed"
    end

    # WDA's clean_keychain endpoint clears the device keychain contents for Dev signed apps
    def clean_device_keychain

      BrowserStack.logger.info("WDA clean_keychain for #{uuid}")
      response = wda_client.clean_keychain
      BrowserStack.logger.info("WDA clean_keychain response for #{uuid} - #{response.inspect}")
    rescue RuntimeError => e
      BrowserStack::Zombie.push_logs("wda_keychain_clean_error", "", { "os" => "ios_njb", "device" => uuid, "terminal_type" => "realMobile", "data" => e.message, "session_id" => session_id })
      BrowserStack.logger.error("WDA clean_keychain error for #{uuid} - #{e.message} - #{e.backtrace}")
    ensure
      file_name = "/tmp/developer_keychain_opened_#{uuid}"
      File.delete file_name if File.exists?(file_name)

    end

    def send_trust_report_to_zombie(status, del_flag)
      BrowserStack::Zombie.push_logs("dummy_app_trust", "", { "os" => "ios_njb", "device" => uuid, "browser" => "", "terminal_type" => "realMobile", "data" => status, "session_id" => session_id })
      FileUtils.rm_f(@device_logger_popups_file) if del_flag
    end

    def log_trust_automation_reason(dummy_app_present, device_logger_reported_untrust)
      trust_automation_reason = "Manual file touch" if File.exists?(@unclean_bad_enterprise_app)
      trust_automation_reason = "DummyApp is not present" unless dummy_app_present
      trust_automation_reason = "DeviceLogger reported untrust" if device_logger_reported_untrust
      BrowserStack.logger.info("Trust automation reason: #{trust_automation_reason}")
    end

    def waiting_apps_logged_file
      # If file exists => all the waiting apps were successfully reported
      "#{@@config['config_root']}/waiting_apps_logged_new_#{uuid}"
    end

    def default_appium_version_session
      session_appium_working_dir = PlistBuddy.get_value_of_key("#{@@config['plist_dir_user']}/appium_#{@device_config['selenium_port']}.plist", "WorkingDirectory").strip
      session_appium_working_dir.include?(@@config['default_appium_version'])
    end

    def check_safari_tabs # rubocop:todo Metrics/AbcSize
      # Logs the active tab details after cleanup - this should ideally log empty list always
      tabs = []
      begin
        port = @device_config["debugger_port"]
        url = "http://localhost:#{port}/json"
        response = BrowserStack::HttpUtils.make_get_request(url, 5)
        if response.status.to_i == 200
          tabs = JSON.parse(response.body)
          BrowserStack.logger.info "Active tab details for #{uuid} - #{tabs}"
        else
          BrowserStack.logger.error "Non 200 response for #{uuid} in getting active tab details - #{response.status}"
        end
      rescue => e
        BrowserStack.logger.error "Error for #{uuid} in getting active tab details from webkit_proxy #{e.message}"
        return
      end

      tab_urls = tabs.map { |tab| tab["url"] }.uniq
      if tab_urls.empty?
        # All good - safari is clean
        BrowserStack.logger.info "Safari tabs are cleaned #{uuid}"
      else
        # Empty string is for almost all the iphone 7 devices
        # about:blank is in the case of many devices but less in number - does not really contribute to unclean device
        unknown_urls = tab_urls.reject { |tab_url| ["", "about:blank"].include?(tab_url) }
        if !unknown_urls.empty?
          BrowserStack.logger.error "Some safari tabs still present for #{uuid} - #{tab_urls}"
          BrowserStack::Zombie.push_logs(
            "unknown_safari_tabs",
            "",
            "os" => "ios_njb",
            "device" => uuid,
            "browser" => "",
            "terminal_type" => "realMobile",
            "session_id" => session_id,
            "data" => tab_urls.to_s
          )
          @influxdb_client.event(uuid, 'unknown-safari-tabs', component: 'cleanup', subcomponent: 'check-safari-tabs', is_error: true)
          raise "Safari tabs are not cleaned"
        else
          BrowserStack.logger.info "Ignoring known safari tabs for #{uuid} - #{tab_urls}"
        end
      end
    end

    def ensure_enterprise_app_uninstalled
      return if device_version.to_i < 18

      unless is_client_enterprise_app?
        BrowserStack.logger.info "[iOSEnterpriseFlow] Skipping this step since no file found"
        return
      end

      installed_apps = ([session_params['app_testing_bundle_id'], session_params['app_details_bundle_id'], session_params['test_suite_bundle_id']] + session_params["other_app_bundle_ids"].to_a + session_params['mid_session_app_bundle_ids'].to_a)

      apps = IdeviceUtils.list_user_installed_apps(uuid, attempts: 2)
      unless ( apps & installed_apps ).empty?
        installed_apps.each do |bundle_id|
          IosMdmServiceClient.remove_application(uuid, bundle_id)
        end
        apps = nil
        3.times do
          sleep 3
          apps = IdeviceUtils.list_user_installed_apps(uuid, attempts: 2)
          break if ( apps & installed_apps ).empty?
        end

        raise "Enterpise App Cleanup Failed" unless apps.empty?
      end
    end

    def clean_multitasking_tabs
      IdeviceUtils.uninstall_app(device_id, Chrome::BUNDLE_ID) if device_state.chrome_cleanup_required_file_present?
      device_state.touch_device_logger_detected_safari_tab_groups_opened_file if device_state.device_logger_detected_safari_launched_file_present?
      device_state.remove_multitasking_view_opened_file
    end

    def device_font_size
      (PyMobileDevice::Developer::Accessibility.get_setting(@uuid, "DYNAMIC_TYPE") * 11).to_i
    end
  end
end
