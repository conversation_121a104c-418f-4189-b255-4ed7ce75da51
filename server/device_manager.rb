require 'English'
require 'json'
require 'timeout'
require 'rmagick'
require 'securerandom'
require 'fileutils'
require 'benchmark'
require 'cleanup_status_db'
require 'so_timeout_util'
require 'concurrent-ruby'
require 'shellwords'
require 'base64'
require 'nokogiri'
require_relative '../lib/cleanup/apple_cleanup'
require_relative 'iphone'
require_relative '../lib/utils/zombie'
require_relative '../lib/app_automate/port_manager'
require_relative '../lib/utils/osutils'
require_relative '../lib/utils/utils'
require_relative '../lib/utils/idevice_utils'
require_relative '../lib/convert_request'
require_relative '../lib/upload_request'
require_relative '../lib/ios_vid_capturer'
require_relative '../lib/checks/check_plist'
require_relative '../lib/service_type'
require_relative '../lib/allow_popups'
require_relative '../lib/handle_cross_site_tracking'
require_relative '../lib/privoxy_manager'
require_relative '../lib/network_simulator'
require_relative '../lib/mitm_proxy'
require_relative '../lib/session/session_factory'
require_relative '../lib/session/session'
require_relative '../lib/helpers/wda_client'
require_relative '../lib/utils/http_utils'
require_relative '../lib/utils/helpers'
require_relative '../lib/helpers/video_rec_manager'
require_relative '../lib/checks/check_device'
require_relative '../lib/utils/web_driver_agent'
require_relative '../lib/utils/hooter'
require_relative '../lib/utils/custom_mdm_manager'
require_relative '../lib/utils/battery_metrics/battery_metrics_publisher'
require_relative '../lib/utils/ios_mdm_service_client'
require_relative '../lib/utils/configuration_profiles_manager'
require_relative '../lib/helpers/testflight'
require_relative '../lib/helpers/airplane_mode_automation'
require_relative '../lib/helpers/local_testing_chrome_extension_helper'
require_relative '../lib/custom_exceptions'
require_relative '../config/constants'
require_relative '../lib/configuration'
require_relative '../lib/utils/time_recorder'
require_relative '../lib/video_utility'
require_relative '../lib/ios_influxdb_client'
require_relative '../lib/utils/xcode_build_utils'
require_relative '../lib/utils/image_injector'
require_relative '../lib/overridden/thread'
require_relative '../lib/models/device_state'
require_relative '../lib/automate_funnel'
require_relative '../lib/utils/app_settings_util'
require_relative '../lib/privoxy_push_repeater'
require_relative '../lib/utils/base64_utils'
require_relative '../lib/device_params'
require_relative '../lib/percy/percy_session'
require_relative '../lib/helpers/assistive_touch_helper'
require_relative '../lib/utils/screenshot_util'
require_relative '../lib/utils/redis'
require_relative '../lib/helpers/passcode_helper'
require_relative '../lib/helpers/apple_pay'
require_relative '../lib/helpers/voiceover_helper'
require_relative '../lib/helpers/pfctl_helper'
require_relative '../lib/utils/mdm_profile_restrictions'
require_relative '../lib/helpers/prevent_cross_site_tracking_helper'
require_relative '../lib/helpers/safari_settings_helper'
require_relative '../lib/utils/file_injector'
require_relative '../lib/helpers/appearance_helper'
require_relative '../lib/utils/socks5_forwarder'
require_relative '../lib/device_setup/backup_manager/backup_manager_factory'
require_relative '../lib/helpers/accessibility_settings_helper'
require_relative '../lib/helpers/date_time_helper'
require_relative '../lib/app_automate/frameworks/xctestrun/xctestrun_manager'
require_relative '../lib/utils/apple_tv_utils'
require_relative '../lib/utils/app_analytics_util'
require_relative '../lib/utils/pymobiledevice'
require_relative '../lib/helpers/apple_pay_session_data'
require_relative '../lib/helpers/location_simulator'
require_relative '../lib/session/xctest_session'
require_relative '../lib/session/maestro_session'
require_relative 'mobile_cspt'
require_relative '../lib/utils/ios_watcher'
require_relative '../lib/helpers/custom_certificate_helper'
require_relative '../ai-proxy/ai_proxy'
require_relative '../lib/utils/devicectl'
require_relative '../lib/models/ios_device'
require_relative '../lib/wrappers/cfg_util'
require_relative '../pwios-proxy/pwios_proxy'
require_relative '../lib/helpers/dedicated_video_rec_manager'

MOBILE_COMMON_ROOT = "/usr/local/.browserstack/mobile-common"
APPIUM_BRIDGE_START_MARKER = ">>>>BrowserStack>>>>"
APPIUM_BRIDGE_STOP_MARKER = "<<<<BrowserStack<<<<"
WEBDRIVERAGENT_RUNNER_BUNDLE_ID = "com.apple.test.WebDriverAgentRunner-Runner"
SOTIMEOUT_PROCESS_TIMEOUT = 120
require "#{MOBILE_COMMON_ROOT}/mobile_session_info/lib/mobile_session_info"
require "#{MOBILE_COMMON_ROOT}/utils/log_parse_util"
require "#{MOBILE_COMMON_ROOT}/utils/app_patching_util"

Dir["../lib/session/*.rb"].each { |file| require_relative file }

include Magick
include LogParseUtil
include AppPatchingUtil

class DeviceManager # rubocop:todo Metrics/ClassLength
  include BrowserStack::TimeRecorder
  TIMEOUT_CMD = "/usr/local/bin/gtimeout"
  @@devices_under_restart = Concurrent::Set.new

  time_class_methods :archive_and_truncate_appium_logs,
                     :check_and_load_plist,
                     :clear_xcuitest_derived_data,
                     :kill_all_forked_processes,
                     :kill_stray_video_recording_processes,
                     :kill_app_download_process,
                     :kill_app_install_process,
                     :local_testing_chrome_extension_cleanup,
                     :device_configuration_check_cleanup_async,
                     :ensure_xcuitest_session_stop,
                     :stop_battery_metrics_publisher,
                     :stop_video_recording_and_upload_video,
                     :stop_mitmproxy_and_upload_networklogs,
                     :clear_apple_pay_data,
                     :cleanup_async

  class << self
    def configure(settings)
      @@settings = settings
      @@device_info_file = settings['config_json_file']
      PrivoxyManager.configure(settings)
      @@screenshot_dir = settings['screenshot_dir']
      FileUtils.mkdir_p(@@screenshot_dir) unless File.directory?(@@screenshot_dir) # TODO: @abdul remove in the next deploy
      @@automate_screenshots_folder_convert = settings['files_to_convert_dir']
      @@automate_screenshots_folder_upload = settings['images_to_upload_dir']
      @@temp_screenshot_suffix_png = settings['temp_screenshot_suffix_png']
      @@temp_screenshot_suffix_jpeg = settings['temp_screenshot_suffix_jpeg']
      @@environment = settings['environment']
      @@log_file = "#{settings['logging_root']}/#{@@environment}.log"
      DeviceParams.configure
      BrowserStack::Zombie.configure
    end

    def influxdb_client
      @@influxdb_client ||= BrowserStack::IosInfluxdbClient.new(BrowserStack.logger)
    end

    def load_config
      @@device_info = File.read(@@device_info_file)
    end

    def all_devices
      load_config # always load
      config = JSON.parse(@@device_info)

      if config
        config['devices']
      else
        {}
      end
    end

    # TODO: Remove this method, left because of a race condition.
    def video_params_file(device)
      "/tmp/video_params_#{device}"
    end

    def stop_params_file(device)
      "/tmp/stop_params_#{device}"
    end

    def write_cleanup_failure_reason(device, model, error)
      error = error.downcase

      error_summaries = [
        'locking device unsuccessful', 'enterprise app in bad state', 'appium not working',
        'ios_webkit_debug_proxy not working', "chrome popup dismissal failed",
        'not on ideviceinfo', "undefined method \`displayed?' for nil:nilclass",
        "undefined method \`send_keys' for nil:nilclass", "orientation lock incorrect",
        "Client's enterprise app untrust failed", "Failed to kill the app during cleanup",
        "umount command failed", "ifuse failed", "testflight installation failed",
        "allow_wda_local_network failed"
      ]

      error_summaries.each do |error_summary|
        error = error_summary if error.include? error_summary
      end

      error = "#{error[0..80]}..." if error.length > 80

      retry_count = begin
        File.read(cleanup_done_file(device)).to_i
      rescue
        0
      end

      BrowserStack.logger.info("Updating cleanup failure reason to #{error}")
      BrowserStack::Zombie.push_logs('ios_njb_cleanup_failure', { "error" => error, "retry_count" => retry_count }.to_json, { "device" => device, "data" => model })
      CleanupStatusDb::Cleanup.failure(device, error)
    end

    def get_cleanup_failure_reason(deviceid)
      row = CleanupStatusDb::Cleanup[deviceid]
      return '' unless row

      row[:failure_reason]
    end

    def connected_devices
      devices = IdeviceUtils.idevices
      devices_cleanup_off_usb = Dir["/tmp/unclean_off_adb_*"].gsub('/tmp/unclean_off_adb_', '').split("\n")
      devices_cleanup = Dir["#{@@settings['state_files_dir']}/cleanupdone_*"].gsub("#{@@settings['state_files_dir']}/cleanupdone_", '').split("\n")
      (devices + devices_cleanup - devices_cleanup_off_usb).uniq
    end

    def device_configuration_check(device)
      configure(BrowserStack::Configuration.conf) unless defined?(@@device_info_file)

      devices = all_devices

      raise "FATAL: Malformed JSON file : #{@@device_info_file}" if devices.nil?

      raise "FATAL: Cannot find device #{device}." if devices[device].nil?

      BrowserStack.logger.info "Device: #{device} is free for this request"

      devices[device]
    end

    def device_configuration_check_cleanup_async(device)
      device_configuration_check(device)
    end

    def force_public_cleanup(device, params)
      FileUtils.touch("#{STATE_FILES_DIR}/forced_public_cleanup_#{device}") if params["trigger_public_cleanup"].to_s == "true"
    end

    def start(device, params) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength, Metrics/PerceivedComplexity, Metrics/CyclomaticComplexity
      raise "Cannot start session. #{device} in use by another user or cleanup." if device_in_use?(device, params)

      params["enableSim"] = params["enableSim"].to_s == "true" && DeviceSIMHelper.sim?(device)
      params["enable_sim_live"] = params["enable_sim_live"].to_s == "true" && DeviceSIMHelper.sim?(device)
      params["is_ios_screen_reader_public_cloud_device"] = params["is_ios_screen_reader_public_cloud_device"] == "true" && BrowserStack::VoiceoverHelper.voiceover_device?(device)
      BrowserStack::VoiceoverHelper.pre_voiceover_setup(device, params) if Utils.bridgecloud_ios_voiceover_session?(params) || params["is_ios_screen_reader_public_cloud_device"].to_s == "true"

      force_public_cleanup(device, params)
      params['apple_pay_session'] = params['apple_pay_session'].to_s == "true" && Secure::ApplePay.apple_pay_device?(device)
      BrowserStack.logger.info "Apple pay session condition = #{params['apple_pay_session']}"
      device_state = DeviceState.new(device)
      extract_device_logger_params(device, device_state, params)
      check_dedicated_device_session(device_state, params, device)
      check_and_set_cleanup_policy(device, params, device_state)
      check_and_give_settings_app_access(device, params)

      remove_proxy_setup(device, params) if !params[:extraAccess].nil? && params[:extraAccess].include?("remove_proxy")
      check_and_enable_wifi(device_state, params)
      check_and_start_ios_watcher(device, params)
      CustomMDMManager.new(device, BrowserStack.logger).backup_unlock_token
      Thread.bs_run { start_battery_metrics_publisher(device, params["session_id"]) }

      # Enable SMS access via device logger
      params["sms_app_access_enabled"] = 'true' if params["enableSim"] || params["enable_sim_live"]

      # Communicate DL that session is an app a11y session
      params["is_app_accessibility"] = 'true' if params["is_app_accessibility"].to_s.downcase == "true" || params[:is_app_accessibility].to_s.downcase == "true"
      BrowserStack.logger.info "App Acessibility session condition = #{params['is_app_accessibility']}"

      FileUtils.touch(session_start_file(device).to_s)
      FileUtils.touch(BrowserStack::IPhone.app_live_free_user_file(device)) if (params["app_live_lft"] == "true" && params["app_live_store_access_blocked"] == "true") || params["is_app_store_threshold_breached"] == "true"
      # Stop video recording services which are running
      kill_video_recording_processes(device)
      write_session_info(device, params)
      current_device = device_configuration_check(device)

      push_to_cls(params, "platform_received_start", "", {})

      # check if the request was a non-start request
      if ["switcher", "local", "keyboard-layout", "ip_change"].include?(params['loader_trigger']) && File.exist?(session_file(device))
        geturl_stat = Benchmark.measure('geturl_stat') do
          debugger_port = current_device['debugger_port']
          url = get_url(device, debugger_port)

          # if `get_url` return empty preserve last url
          params['url'] = Utils.valid_live_url?(url) ? url : params['url']
          if !debugger_port.nil? && !Utils.valid_live_url?(url)
            hooter = Hooter.new
            hooter.send(current_device['device_name'], 'geturl_error', params['device_browser']) if params[:is_app_accessibility].to_s.downcase != "true"

            BrowserStack::Zombie.push_logs("ios_get_url_error", params["device_browser"], {
              "device" => device,
              "genre" => params["genre"],
              "session_id" => params["live_session_id"] || "unknown"
            }, nil, params)
          end
        end
        params['session_start_events']['geturl'] = (geturl_stat.real * 1000).to_i
      end

      session = BrowserStack::SessionFactory.for(params, current_device)
      session.start

      # bs_run creates a new thread & copies logging params to it
      Thread.bs_run { log_device_internet_connection(device, params) }

      check_and_give_icloud_access(device, params, device_state)

      port = current_device['selenium_port']
      { port: port }
    end

    def check_and_enable_wifi(device_state, params)
      if !params[:extraAccess].nil? && params[:extraAccess].include?("enable_wifi")
        device_state.touch_keep_wifi_enabled_file
      elsif device_state.keep_wifi_enabled_file_present?
        device_state.remove_keep_wifi_enabled_file
      end
    end

    def ios_watcher_supported?(device)
      current_device_config = device_configuration_check(device)
      device_version = current_device_config['device_version'].to_i
      device_version >= IOS_WATCHER_MIN_IOS_VERSION
    end

    def check_and_start_ios_watcher(device, params)
      session_id = params['session_id'] || params['app_live_session_id'] || params['live_session_id'] || params["automate_session_id"] || params['app_automate_session_id']
      if ios_watcher_supported?(device)
        BrowserStack.logger.info("Starting ios watcher for device #{device}")
        IosWatcher.start_capture(device, session_id)
      end
    end

    def check_and_stop_ios_watcher(device, session_id, record_metrics)
      if ios_watcher_supported?(device)
        BrowserStack.logger.info("Stopping ios watcher for device #{device}")
        IosWatcher.stop_capture(device)
        if record_metrics
          current_device_config = device_configuration_check(device)
          device_version = current_device_config['device_version'].to_i
          IosWatcher.record_metrics(device, device_version, session_id)
        end
      end
    end

    def restart(device, params)
      current_device = device_configuration_check(device)
      session = BrowserStack::SessionFactory.for(params, current_device)
      session.restart
    end

    def extract_device_logger_params(device, device_state, params)
      # Extra Access is only for private cloud session
      params[:extraAccess] = nil if params["is_dedicated_cloud_session"].to_s != "true"

      # Return here for public cloud session
      return if params[:extraAccess].nil?

      split_params = params[:extraAccess]&.split(",")
      BrowserStack.logger.info("Private Cloud Params : #{split_params}")

      params["icloud_access_enabled"] = 'true' if device_state.dedicated_device_file_present?
      params["creative_offline_mode_enabled"] = 'true' if split_params&.include?("creative_offline_mode")
      params["custom_mdm"] = 'true' if split_params&.include?("custom_mdm")
      apply_restriction_changes(device, device_state)
      if split_params&.include?("custom_mdm") && !CustomMDMManager.is_custom_mdm_device?(device)
        device_state.touch_custom_mdm_perform_setup_file
      elsif CustomMDMManager.is_custom_mdm_device?(device) && !split_params&.include?("custom_mdm")
        device_state.touch_custom_mdm_remove_setup_file
      end
    end

    def apply_restriction_changes(device, device_state)
      if device_state.enable_restriction_feature_file_present?
        restrictions = ConfigurationProfilesManager.new(device, BrowserStack.logger)
        BrowserStack.logger.info("Feature change detected applying restricitions")
        install_via = :automatic

        if CustomMDMManager.is_custom_mdm_device?(device)
          if restrictions.device_uses_cfgutil_managed_profiles?(verify_cfgutil: true)
            BrowserStack.logger.info("Installing via cfgutil for customMDM device")
            install_via = :cfgutil
          else
            BrowserStack.logger.info("Skipping for customMDM device as cfgutil is not supported")
            device_state.remove_enable_restriction_feature_file
            return
          end
        end
        restrictions.install_profile(:restrictions, install_via: install_via)
        BrowserStack.logger.info("Installation complete")
        device_state.remove_enable_restriction_feature_file
      end
    rescue Exception => e
      device_state.touch_enable_restriction_feature_file
      BrowserStack.logger.error("Unable to apply restrictions for #{device} #{e} will try again in next session start")
    end

    def check_and_enable_feature(device_state, enabled_features, feature)
      # feature enabled if first time mark for profile install via enable_restriction_feature_file
      if enabled_features.include?(feature)
        BrowserStack.logger.info("Enabling Feature : #{feature}")
        device_state.touch_enable_restriction_feature_file unless device_state.send("#{feature}_enabled_file_present?") || device_state.enable_restriction_feature_file_present?
        device_state.send("touch_#{feature}_enabled_file")
        return true
      end

      # feature disabled if first time mark for profile install via enable_restriction_feature_file
      BrowserStack.logger.info("Disabling Feature : #{feature}")
      device_state.touch_enable_restriction_feature_file if device_state.send("#{feature}_enabled_file_present?") && !device_state.enable_restriction_feature_file_present?
      device_state.send("remove_#{feature}_enabled_file")
      false
    end

    def remove_proxy_setup(device, params)
      BrowserStack.logger.info("Removing HTTP Proxy For Device #{device}")
      ConfigurationProfilesManager.unset_proxy(device, REDIS_CLIENT, BrowserStack.logger)
    rescue Exception => e
      BrowserStack.logger.error("Failed to unset_proxy for #{device} #{e}")
    end

    def restore_installed_apps_data_to_tmp(device, device_state, params)
      return unless params[:dedicated_cleanup] == "true"

      BrowserStack.logger.info("ALL_INSTALLED_APPS file transfer begins")
      FileUtils.cp("#{STATE_FILES_DIR}/#{device}_installed_apps_data", "#{TMP_DIR_PATH}/#{device}_installed_apps/ALL_INSTALLED_APPS")
    rescue => e
      BrowserStack.logger.error("ALL_INSTALLED_APPS file absent #{e.message}")
    end

    # rubocop:disable Metrics/MethodLength
    def check_dedicated_device_session(device_state, params, device)
      custom_mdm = CustomMDMManager.is_custom_mdm_device?(device) || device_state.custom_mdm_remove_setup_file_present?
      file_exists = device_state.dedicated_device_file_present? || custom_mdm
      if params["is_dedicated_cloud_session"].to_s != "true"
        if file_exists
          unless custom_mdm
            BrowserStack.logger.info "Stray dedicated_device file found for - #{device}. Deleting it"
            device_state.remove_dedicated_device_file
            device_state.remove_dedicated_cleanup_file
            device_state.remove_dedicated_video_state_file
            FileUtils.rm_f("#{STATE_FILES_DIR}/#{device}_installed_apps_data")

            BrowserStack.logger.info "Deleting Extra access state files for - #{device}"
            EXTRA_ACCESS_RESTRICTION_KEYS.each do |feature|
              device_state.send("remove_#{feature}_enabled_file")
            end

            device_state.touch_force_install_mdm_profiles_file
          end

          reason = custom_mdm ? "custom_mdm" : "dedicated-device"
          message = "Stray #{reason} file found"
          begin
            session_id = params['automate_session_id'] || params["live_session_id"]
            genre = params["genre"]
          rescue => e
            session_id = "unknown"
            genre = ""
          end
          BrowserStack::Zombie.push_logs(
            "ios-stray-#{reason}-file",
            message,
            {
              "session_id" => session_id,
              "team" => "bridgecloud",
              "data" => {
                "device" => device,
                "genre" => genre
              }
            },
            nil, params
          )
          raise message
        end
      else
        restore_installed_apps_data_to_tmp(device, device_state, params)
        touch_dedicated_device_file(device, device_state)
        dedicated_video_rec_manager = DedicatedVideoRecManager.new(device)
        dedicated_video_rec_manager.write_session_video_file('SESSION_START', params["session_id"]) if device_state.dedicated_cleanup_file_present?
      end
    end
    # rubocop:enable Metrics/MethodLength

    def touch_dedicated_device_file(device, device_state)
      unless device_state.dedicated_device_file_present?
        BrowserStack.logger.info "Creating dedicated device file for - #{device}"
        device_state.touch_dedicated_device_file
      end
    end

    def start_battery_metrics_publisher(device, session_id)
      battery_metrics_publisher = BatteryMetricsPublisher.new(device, session_id)
      battery_metrics_publisher.start
    rescue => e
      BrowserStack.logger.error("Couldn't start battery metrics publisher: #{e.message}")
    end

    def stop_battery_metrics_publisher(device, session_id)
      battery_metrics_publisher = BatteryMetricsPublisher.new(device, session_id)
      battery_metrics_publisher.stop_and_push_metrics
    rescue => e
      BrowserStack.logger.error("Couldn't start battery metrics publisher: #{e.message}")
    end

    def connect_to_peer(device, params)
      raise "Cannot connect to peer #{device} in session or cleanup" if device_in_use?(device, params)
      return if params[:enable_post_cleanup_peer_flow] == "false"

      wda_params = {}
      config_file = Configuration.new.all
      wda_params[:peer_server_url] = params[:peer_server_url]
      wda_params[:webrtc_session_id] = params[:vnc_key]
      wda_params[:terminal_ip] = params[:terminal_ip]
      wda_params[:device] = params[:device]
      wda_params[:cls_servers] = []
      wda_params[:cls_servers] << { "type" => "CLS", "host" => config_file['cls_url'], "port" => config_file['cls_port'] }
      current_device = device_configuration_check(device)
      wda_port = current_device['webdriver_port']
      BrowserStack.logger.info "Connecting to peer with following params #{wda_params.inspect}"
      wda_client = WdaClient.new(wda_port)
      wda_client.connect_to_peer(wda_params)
      raise "WDA stopped running #{device}" unless wda_client.running?
    end

    def device_in_use?(device, params)
      if device_in_cleanup?(device)
        BrowserStack.logger.info "Device #{device} is cleaning, cleanupdone file exists"
        return true
      end

      # Only return true if the device is already being used by someone else (with a different user ID)
      # Users can switch browser on the same device, we don't want to prevent session starts for this
      if device_in_other_users_session?(device, params)
        BrowserStack.logger.info "Device #{device} is being used by someone else, session file with different user ID exists"
        return true
      end

      false
    end

    def device_in_other_users_session?(device, params)
      if device_in_session?(device) && !session_file_contents(device).empty?
        BrowserStack.logger.info "Checking if device is being used by another user"

        # Compare the user ID in the existing session file with the request params
        # If they don't match we are trying to share the same device for 2 different users
        old_user_id = session_file_contents(device)["user_id"].to_i
        new_user_id = params[:user_id].to_i

        if old_user_id != new_user_id
          BrowserStack.logger.error "Device being shared by two users!"
          device_session_utc = File.mtime(session_file(device)).utc
          BrowserStack.logger.error "Session file modified at: #{device_session_utc}"
          BrowserStack::Zombie.push_logs(
            "terminal_sharing_detected",
            "User ID mismatch",
            {
              "old_session_user_id" => old_user_id,
              "new_session_user_id" => new_user_id,
              "device_session_utc" => device_session_utc
            }
          )
          return true
        end
      end

      false
    end

    def update_app_on_device(device, params)
      current_device = device_configuration_check(device)
      session_params = Utils.read_json_file(session_file(device))
      session = BrowserStack::AppLiveSession.new(session_params, current_device)
      session.update_app(params)
      port = current_device['selenium_port']
      { port: port }
    end

    def check_patch_and_parse(stop_genre, device, params)
      session_file_contents = File.exists?(session_file(device)) ? JSON.parse(File.read(session_file(device))) : {}
      session_id = params[:app_live_session_id] || params[:automate_session_id] || params[:live_session_id]
      instrumented_app_session = (stop_genre == "app_live_testing" && session_file_contents[APP_PATCH_TYPE] && !session_file_contents[APP_PATCH_TYPE].empty?) || (stop_genre == "app_automate" && ALLOWED_APP_PATCH_TYPES.include?(session_file_contents[APP_PATCH_TYPE].to_s))
      parse_app_patching_logs_and_push_to_eds(BrowserStack::DeviceLogger.app_device_logs_file(device), session_id, params, "ios", @@log_file, true) if instrumented_app_session
    rescue => e
      BrowserStack.logger.error("Exception in parsing app patcher logs from device logs #{session_id}, #{e.message} #{e.backtrace}")
    end

    def stop(device, params, component_breakdown = "")
      check_wda_iproxy_status(device, params)
      current_device = device_configuration_check(device)
      stop_genre = params[:genre] || 'selenium'
      device_version = begin
        current_device['device_version'].to_f
      rescue
        "0"
      end

      check_patch_and_parse(stop_genre, device, params)

      if stop_genre == 'app_live_testing' && device_version >= 12.0
        all_settings_page_visited = get_settings_pages_visited(device, params)
        session_id = params[:app_live_session_id]
        BrowserStack.logger.info("Settings pages visited #{all_settings_page_visited} to for device #{device} #{session_id}")
        if Utils.array_and_non_empty?(all_settings_page_visited)
          eds_data = {
            event_name: "AL_settings_page_visited",
            session_id: session_id,
            pages_visited: all_settings_page_visited,
            platform: "realios",
            team: "app_live"
          }
          Utils.send_to_eds(eds_data, "web_events", true, req_params: params)
        end
      end

      if ['selenium', 'app_automate', 'playwright'].include? stop_genre
        selenium_stop(device, params, component_breakdown)
      else
        session = BrowserStack::SessionFactory.for(params, current_device)
        session.stop
      end

      'Stop: done'
    end

    def blocked_devices_count
      devices_list = all_devices.keys
      blocked_devices = 0
      devices_list.each do |device|
        blocked_devices += 1 unless cleanup_done?(device)
      end
      blocked_devices
    end

    def cleanup_done?(device)
      !File.exists?(cleanup_done_file(device)) && !File.exists?(session_file(device))
    end

    def device_in_cleanup?(device)
      File.exist?(cleanup_done_file(device))
    end

    def device_in_session?(device)
      File.exist?(session_file(device))
    end

    def check_and_load_plist(name, type)
      return if BrowserStack::OSUtils.is_plist_loaded?(name, type)

      BrowserStack::CheckPlist.configure("#{@@settings['mobile_root']}/templates/", @@settings['plist_dir_system'], @@settings['plist_dir_user'])
      BrowserStack::CheckPlist.load_service(name, type, @@settings['user'])
    end

    def check_and_give_icloud_access(device, params, device_state)
      if params["icloud_access_enabled"] == 'true'
        socks5_forwarder = Privoxy::Socks5Forwarder.new(device)
        device_state.touch_icloud_access_file
        socks5_forwarder.forward_traffic(["*.icloud.com", "updates-http.cdn-apple.com", "updates.cdn-apple.com"], params)
      end
    end

    def stop_video_recording_and_upload_video(device, video_params, wda_port = nil)
      genre = video_params[:genre] || video_params['genre']
      prefix_genre = Utils.get_prefix_genre(genre)
      start_at = Time.now
      error = ""
      VideoRecManager.new(device, video_params, wda_port).stop_rec
      stop_time = Time.now - start_at
    rescue Timeout::Error => e
      error = "#{ e.class } - #{ e.message }"
      BrowserStack.logger.error("Video Recording Failed : Timeout, #{e.message} #{e.backtrace}")
      BrowserStack::Zombie.push_logs("#{prefix_genre}ios-video-rec-timeout", "Video render timeout", { "session_id" => video_params['video_session_id'] })
    rescue => e
      error = "#{ e.class } - #{ e.message }"
      BrowserStack.logger.error("Video Recording Failed #{video_params['video_session_id']} #{e.message} #{e.backtrace}")
      BrowserStack::Zombie.push_logs("#{prefix_genre}ios-video-rec-failure", "Video render exception #{e.message}", { "session_id" => video_params['video_session_id'] })
    else
      BrowserStack::Zombie.push_logs("#{prefix_genre}ios-video-render-time", "#{stop_time} seconds.", { "session_id" => video_params['video_session_id'] })
    ensure
      BrowserStack.logger.error("Removing test launch file")
      FileUtils.rm(video_test_launch_file(device)) if File.exist?(video_test_launch_file(device))
      BrowserStack::Zombie.push_logs("timed-DeviceManager#stop_video_recording", error, { "device" => device, 'data' => stop_time })
    end

    def clear_xcuitest_derived_data(device)
      Dir.glob(File.join("/tmp", "#{device}_xcuitest_derived_data*")).each do |dir_name|
        FileUtils.rm_rf(dir_name)
      end
    end

    def kill_stray_video_recording_processes(device, video_params)
      BrowserStack.logger.info("Killing stray video recording services")
      genre = video_params[:genre] || video_params["genre"]
      vid_capturer = IosVidCapturer.new(device, '', @@settings, nil, genre, BrowserStack.logger)
      # Kill any running video recording process
      VIDEO_RECORDING_PROCESSES.each do |process|
        next unless BrowserStack::OSUtils.is_process_running?(process, device)

        BrowserStack::Zombie.push_logs("ios-video-rec-stray-process", "Process: #{process} still running", { "session_id" => video_params['video_session_id'] })
        BrowserStack.logger.info("Process: #{process} still running, stop not received ??")
        BrowserStack::OSUtils.kill_process(process, device, "KILL")
      end

      vid_capturer.clean_stale_session_folders_from_workspace
    end

    def kill_video_recording_processes(device)
      BrowserStack.logger.info("Killing Video recording services")
      VIDEO_RECORDING_PROCESSES.each do |process|
        if BrowserStack::OSUtils.is_process_running?(process, device)
          BrowserStack.logger.info("Killing Process: #{process}")
          BrowserStack::OSUtils.kill_process(process, device, "KILL")
        end
      end
    end

    def minified_cleanup_async(device, cleanup_type, session_id, min_flow_start_time) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength

      minified_flow_instrumentation_file = "/tmp/minified_flow_instrumentation_#{device}.txt"

      current_device_config = device_configuration_check(device)

      idevice = BrowserStack::IPhone.new(current_device_config, device)
      stop_battery_metrics_publisher(device, session_id)

      session_params = {}
      session_params = JSON.parse(File.read(session_file(device))) if File.exists?(session_file(device))

      genre = session_params["genre"].to_s
      perform_aut_cleanup = [GENRE_AUTOMATE, GENRE_SELENIUM, ""].include?(genre)
      perform_aa_cleanup = [GENRE_APP_AUTOMATE, ""].include?(genre)

      File.open(minified_flow_instrumentation_file, "a") { |f| f.puts "video_upload_request_started #{Time.now.to_i - min_flow_start_time}" }
      BrowserStack.logger.info("[minified_essential_time] video_upload_request_started #{Time.now.to_i - min_flow_start_time}")
      video_params = {}
      params_file = if File.exists?(stop_params_file(device))
                      stop_params_file(device)
                    elsif File.exist?(video_params_file(device))
                      video_params_file(device)
                    end

      BrowserStack.logger.info("params file exists?: #{params_file}")
      if params_file
        begin
          stop_params = JSON.parse(File.read(params_file))
          if stop_params["automate_session_id"].to_s != session_id.to_s
            BrowserStack.logger.warn("automate_session_id from params file does not match with cleanup session_id. automate_session_id: #{stop_params['automate_session_id']}, session_id: #{session_id}")
            BrowserStack::Zombie.push_logs("cleanup_session_mismatch", "automate_session_id: #{stop_params['automate_session_id']}", { "device" => device, "session_id" => session_id })
          else
            if stop_params["video"].to_s == 'true'
              # Used before cleanup ends to ensure that video recording process has been killed.
              # TODO: Remove this.
              video_params = stop_params

              video_v2_params = stop_params.select { |key, _value| key.to_s.start_with? "video_v2_" }
              if !video_v2_params.nil? && !video_v2_params.empty?
                video_v2_params = video_v2_params.transform_keys { |k| k.to_s.gsub("video_v2_", "") }
                stop_params.merge!({ "video_params_v2" => video_v2_params.to_json })
              end
              wda_port = current_device_config['webdriver_port']
              stop_video_recording_and_upload_video(device, stop_params, wda_port)
            end

            BrowserStack.logger.info("networkLogs value: #{stop_params['networkLogs']}")

            if stop_params["networkLogs"].to_s == "true"
              File.open(minified_flow_instrumentation_file, "a") { |f| f.puts "network_logs_upload_request_started #{Time.now.to_i - min_flow_start_time}" }
              BrowserStack.logger.info("[minified_essential_time] network_logs_upload_request_started #{Time.now.to_i - min_flow_start_time}")
              mitm = MitmProxy.new(device, stop_params, @@settings)
              mitm.stop_proxy(current_device_config)
              mitm.capture_har
            end
          end
        rescue JSON::ParserError => e
          # Don't print e.inspect, it has sensitive data.
          BrowserStack.logger.error("Exception while parsing the params file (#{params_file}). Backtrace: #{e.backtrace}")
        rescue => e
          BrowserStack.logger.error("Exception while performing /stop pending items. #{e.inspect}")
        ensure
          File.delete(params_file)
        end
      end

      idevice.minified_cleanup(cleanup_type, min_flow_start_time, perform_aut_cleanup, perform_aa_cleanup)

      if perform_aa_cleanup
        File.open(minified_flow_instrumentation_file, "a") { |f| f.puts "delete_APPS_DOWNLOAD_FOLDER_started #{Time.now.to_i - min_flow_start_time}" }
        BrowserStack.logger.info("[minified_essential_time] delete_APPS_DOWNLOAD_FOLDER_started #{Time.now.to_i - min_flow_start_time}")
        session_files = Dir.glob("#{APPS_DOWNLOAD_FOLDER}/*/#{session_id}_*.session")
        FileUtils.rm_f(session_files)
        BrowserStack.logger.info("Deleting session file from downloaded app: #{session_files}")

        # Deleting zip file as part of fallback if any error occurs between zip created and upload
        FileUtils.rm_rf("/tmp/crash_report_#{session_id}.zip")
      end

      # TODO: check if this is needed and will this kill appium? [high start time and instability]
      File.open(minified_flow_instrumentation_file, "a") { |f| f.puts "kill_all_forked_processes_started #{Time.now.to_i - min_flow_start_time}" }
      BrowserStack.logger.info("[minified_essential_time] kill_all_forked_processes_started #{Time.now.to_i - min_flow_start_time}")
      # Kill any forked process on platform for device
      kill_all_forked_processes(device)

      File.open(minified_flow_instrumentation_file, "a") { |f| f.puts "kill_app_download_process #{Time.now.to_i - min_flow_start_time}" }
      BrowserStack.logger.info("[minified_essential_time] kill_app_download_process #{Time.now.to_i - min_flow_start_time}")
      kill_app_download_process(device)

      File.open(minified_flow_instrumentation_file, "a") { |f| f.puts "kill_app_install_process #{Time.now.to_i - min_flow_start_time}" }
      BrowserStack.logger.info("[minified_essential_time] kill_app_install_process #{Time.now.to_i - min_flow_start_time}")
      kill_app_install_process(device)
      # Commenting this for now, will uncomment if we see stability issues around video
      # File.open(instrumentation_file, "a") {|f| f.puts "kill_stray_video_recording_processes_started #{Time.now.to_i - min_flow_start_time}"}
      # BrowserStack.logger.info("[minified_essential_time] kill_stray_video_recording_processes_started #{Time.now.to_i - min_flow_start_time}")
      # kill_stray_video_recording_processes(device, video_params)

      MobileSessionInfo.delete(device)
      Utils.with_lock(session_file(device)) { FileUtils.rm_rf(session_file(device)) }
      FileUtils.rm_rf(cleanup_done_file(device))

      File.open(minified_flow_instrumentation_file, "a") { |f| f.puts "post_cleanup_success_started #{Time.now.to_i - min_flow_start_time}" }
      BrowserStack.logger.info("[minified_essential_time] post_cleanup_success_started #{Time.now.to_i - min_flow_start_time}")
      idevice.post_cleanup_success
    rescue => e
      BrowserStack.logger.error "#{e.class} in cleanup_async [minified flow] - #{e.message} Trace: #{e.backtrace[0..5]}"
      raise "#{OFFLINE_REASON_MINIFIED_PREFIX} : unknown error"
    end

    # Ensuring session stop for xcuitest before proceeding ahead with cleanup
    def ensure_xcuitest_session_stop(device, session_id, current_device_config)

      start_at = Time.now
      session_params = JSON.parse(File.read(session_file(device)))

      if session_params.include?("genre") && session_params["genre"] == GENRE_APP_AUTOMATE
        terminal_ip = File.read '/usr/local/.browserstack/whatsmyip'
        test_framework = session_params["test_framework"]
        captures = test_framework == "maestro" ? ["maestro"] : ["xcuitest"]
        params = {
          'session_genre' => session_params["genre"],
          'device' => device,
          'terminal_ip' => terminal_ip,
          'automate_session_id' => session_id,
          'session_start_events' => {},
          'test_framework' => test_framework,
          'captures' => captures,
          'test_suite_bundle_id' => session_params["test_suite_bundle_id"],
          'wda_port' => current_device_config['webdriver_port']
        }

        if test_framework == "maestro"
          maestro_session = BrowserStack::MaestroSession.new(params, current_device_config)
          maestro_session.ensure_session_stop
        else
          xctest_session = BrowserStack::XCTestSession.new(params, current_device_config)
          xctest_session.ensure_session_stop
        end

        stop_time = Time.now - start_at
      end
    rescue => e
      BrowserStack.logger.info("Error in performing ensure stop for xcuitest #{e.message}")
      BrowserStack::Zombie.push_logs("xctest-cleanup-ensure-stop-err", e.message.to_s, { "session_id" => session_id })
    else
      BrowserStack::Zombie.push_logs("xctest-cleanup-ensure-stop-time", "#{stop_time} seconds.", { "session_id" => session_id })

    end

    def cleanup_async(device, cleanup_type, session_id) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength, Metrics/PerceivedComplexity, Metrics/CyclomaticComplexity
      Timeout.timeout(20 * 60) do # Units in seconds # rubocop:todo Metrics/BlockLength
        current_device_config = device_configuration_check_cleanup_async(device)
        ensure_xcuitest_session_stop(device, session_id, current_device_config) if File.exists?("/tmp/#{device}-xctest.pid")
        video_params = {}
        params_file = if File.exists?(stop_params_file(device))
                        stop_params_file(device)
                      elsif File.exist?(video_params_file(device))
                        video_params_file(device)
                      end

        # Same caching app logic for browserstack app
        BrowserStackAppHelper.cache_browserstack_app(device)

        # needed for iOS 17 xcode issue
        BrowserStackAppHelper.cache_as_test_app(device)

        BrowserStack.logger.info("Killing stale ifuse processes")
        # Kill process in Uninterrupted Sleep state with -9 signal
        # Need to kill twice to work, not sure why :-/
        OSUtils.execute("ps aux | grep ifuse | grep Us | grep #{device} | awk '{print $2}' | xargs -I{} sudo kill -9 {}")
        OSUtils.execute("ps aux | grep ifuse | grep Us | grep #{device} | awk '{print $2}' | xargs -I{} sudo kill -9 {}")

        OSUtils.execute("ps aux | grep ifuse | grep #{device} | awk '{print $2}' | xargs -I{} sudo kill {}")
        OSUtils.execute("ps aux | grep ifuse | grep #{device} | awk '{print $2}' | xargs -I{} sudo kill {}")

        stop_battery_metrics_publisher(device, session_id)

        BrowserStack.logger.info("params file exists?: #{params_file}")
        if params_file
          begin
            stop_params = JSON.parse(File.read(params_file))
            if stop_params["automate_session_id"].to_s != session_id.to_s
              BrowserStack.logger.warn("automate_session_id from params file does not match with cleanup session_id. automate_session_id: #{stop_params['automate_session_id']}, session_id: #{session_id}")
              BrowserStack::Zombie.push_logs("cleanup_session_mismatch", "automate_session_id: #{stop_params['automate_session_id']}", { "device" => device, "session_id" => session_id })
            else
              if stop_params["video"].to_s == 'true'
                # Used before cleanup ends to ensure that video recording process has been killed.
                # TODO: Remove this.
                video_params = stop_params
                video_v2_params = stop_params.select { |key, _value| key.to_s.start_with? "video_v2_" }
                if !video_v2_params.nil? && !video_v2_params.empty?
                  video_v2_params = video_v2_params.transform_keys { |k| k.to_s.gsub("video_v2_", "") }
                  stop_params.merge!({ "video_params_v2" => video_v2_params.to_json })
                end
                device_state = DeviceState.new(device)
                if device_state.xctest_v2_video_file_present?
                  stop_params.merge!({ "video_params_v2" => device_state.read_xctest_v2_video_file })
                  device_state.remove_xctest_v2_video_file
                end
                wda_port = current_device_config['webdriver_port']
                stop_video_recording_and_upload_video(device, stop_params, wda_port)
              end

              stop_mitmproxy_and_upload_networklogs(device, stop_params, current_device_config)
            end
          rescue JSON::ParserError => e
            # Don't print e.inspect, it has sensitive data.
            BrowserStack.logger.error("Exception while parsing the params file (#{params_file}). Backtrace: #{e.backtrace}")
          rescue => e
            BrowserStack.logger.error("Exception while performing /stop pending items. #{e.inspect}")
          ensure
            File.delete(params_file)
          end
        end
        device_state = DeviceState.new(device)
        BrowserStack.logger.info("Next step is to clear apple pay session data, device: #{device}, is apple pay device: #{Secure::ApplePay.apple_pay_device?(device)}, is state file present: #{device_state.apple_pay_data_file_present?}")
        is_apple_pay_device_and_session = (Secure::ApplePay.apple_pay_device?(device) || Secure::ApplePay.dedicated_cloud_apple_pay_device?(device)) && device_state.apple_pay_data_file_present?
        clear_apple_pay_data(device, session_id, device_state) if is_apple_pay_device_and_session

        # Clean up local testing chrome extension
        BrowserStack.logger.info("Cleaning up LocalTestingChromeExtension..")
        local_testing_chrome_extension_cleanup(device, session_id)

        # If params are not present, still kill mitm process
        MitmProxy.stop_proxy(device, session_id)

        # ensure rm of app folder
        FileUtils.rm_rf("/tmp/#{device}_app")

        # ensure rm of test folder
        FileUtils.rm_rf("/tmp/#{device}_test")

        # ensure rm of other_apps folder
        FileUtils.rm_rf(Dir.glob("/tmp/#{device}_other_apps_*"))

        session_files = Dir.glob("#{APPS_DOWNLOAD_FOLDER}/*/#{session_id}_*.session")
        FileUtils.rm_f(session_files)
        BrowserStack.logger.info("Deleting session file from downloaded app: #{session_files}")

        # Clean all the files and setup done for image injection
        ImageInjector.cleanup(device)

        # Clean all the files and setup done for file injection
        FileInjector.cleanup(device)

        # Deleting zip file as part of fallback if any error occurs between zip created and upload
        FileUtils.rm_rf("/tmp/crash_report_#{session_id}.zip")

        # Deleting ALL_INSTALLED_APPS file
        FileUtils.rm_rf("/tmp/#{device}_installed_apps/ALL_INSTALLED_APPS") if @is_app_testing

        # Kill any forked process on platform for device
        kill_all_forked_processes(device)

        # Kill any app downloads continuing int he background for AA
        kill_app_download_process(device)

        # Kill any app installs
        kill_app_install_process(device)

        if File.exists?(session_file(device)) && cleanup_type != "full_cleanup"
          begin
            session_params = JSON.parse(File.read(session_file(device)))

            if session_params.include?("genre") && session_params["genre"] == "app_live_testing"
              Utils.upload_app_live_logs(session_params, device)
            elsif session_params["genre"] == PERCY
              Percy::PercySession.new(device).cleanup
              cleanup_type = 'quick_cleanup'
            end
          rescue JSON::ParserError => e
            BrowserStack.logger.error("Failed to parse session params file: #{e.message} #{e.backtrace.join("\n")}")
            BrowserStack.logger.info("Deleting session params file for device #{device}")
            File.delete(session_file(device))
          end
        end

        cleanup_count = begin
          File.read(cleanup_done_file(device)).to_i
        rescue
          0
        end
        BrowserStack.logger.info("Cleanup is being called again nth time: #{cleanup_count}")

        if cleanup_count > 5
          BrowserStack.logger.info("Too many #{cleanup_type}s done: #{cleanup_count}. Process exiting...")
          return
        end
        BrowserStack.logger.info("Running cleanup for time: #{cleanup_count}")
        File.write(cleanup_done_file(device), (cleanup_count + 1))

        check_and_load_plist("appium_#{current_device_config['selenium_port']}", ServiceType.UserService)
        check_and_load_plist("ios_webkit_debug_proxy_#{current_device_config['debugger_port']}", ServiceType.SystemService)

        kill_stray_video_recording_processes(device, video_params)

        # Stopping the proxy check first before resetting privoxy
        BrowserStack.logger.info("Stopping proxy checker")
        cmd = "/Users/<USER>/.rvm/rubies/ruby-2.7.2/bin/ruby /usr/local/.browserstack/realmobile/scripts/proxy_check.rb \"stop\" \"#{device}\""
        BrowserStack::OSUtils.execute(cmd)
        PrivoxyManager.reset_proxy(device, current_device_config)

        device_state = DeviceState.new(device)

        device_state.remove_fallback_to_v1_file if device_state.fallback_to_v1_file_present?
        device_name = current_device_config["device_name"]
        if AppleTVUtils.apple_tv_device?(device)
          cleanup = Cleanup::TvosCleanup.new(udid: device)
          cleanup.perform
        else
          idevice = BrowserStack::IPhone.new(current_device_config, device)

          if cleanup_type == "full_cleanup"
            # Call full cleanup here
            idevice.full_cleanup_mdm
          elsif device_state.dedicated_cleanup_file_present?
            idevice.dedicated_cleanup
          elsif cleanup_type == "first_cleanup"
            idevice.first_cleanup
          else
            idevice.cleanup(cleanup_type)
          end

          idevice.capture_rsd_values
          clear_xcuitest_derived_data(device)
          BrowserStack::IPhone.cleanup_telephony_files(device)
          idevice.post_cleanup_success
        end
        MobileSessionInfo.delete(device)
        Utils.with_lock(session_file(device)) { FileUtils.rm_rf(session_file(device)) }
      end
    rescue Timeout::Error => e
      BrowserStack.logger.error "Timeout in cleanup_async - #{e.message} Trace: #{e.backtrace[0..5]}"
      BrowserStack.logger.error "Last expression before timeout - #{e.backtrace[0..5].find { |line| line.include?('realmobile') }}"
      raise e
    rescue => e
      BrowserStack.logger.error "#{e.class} in cleanup_async - #{e.message} Trace: #{e.backtrace[0..5]}"
      raise e
    end

    def is_first_cleanup?(device)
      device_config = device_configuration_check(device)
      device_state = DeviceState.new(device)
      start_time = Time.now
      error = ""

      last_known_build_number = if device_state.first_cleanup_completed_file_present?
                                  device_state.read_first_cleanup_completed_file.to_s.chomp
                                else
                                  lockdown_device_state = LockdownDeviceState.new(device)
                                  lockdown_device_state.get_first_cleanup_done_key.to_s.chomp
                                end

      BrowserStack.logger.info "Last known build number is: #{last_known_build_number}"
      last_known_build_number.empty?
    rescue => e
      BrowserStack.logger.error "#{e.class} in is_first_cleanup? - #{e.message} Trace: #{e.backtrace[0..5]}"
      error = "#{e.class} - #{e.message}"
      false
    ensure
      begin
        device_version = device_config['device_version'].to_f
        BrowserStack::Zombie.push_logs("timed-DeviceManager#is_first_cleanup?", error, { "device" => device, 'data' => Time.now - start_time, "browser_version" => device_version }) if device_version >= 18 || device_state.dedicated_cleanup_file_present?
      rescue => e
        BrowserStack.logger.error "Error in ensure block of is_first_cleanup?: #{e.message}"
      end
    end

    def first_cleanup?(device)
      device_config = device_configuration_check(device)
      device_version = device_config['device_version'].to_f
      device_name = device_config['device_name']

      # Condition to check if first cleanup is applicable
      return false if device_version < 18 || !FIRST_CLEANUP_ENABLED_DEVICES.include?(device_name)

      # Call the reusable logic function
      is_first_cleanup?(device)
    end

    def stop_mitmproxy_and_upload_networklogs(device, stop_params, current_device_config)
      BrowserStack.logger.error("networkLogs value: #{stop_params['networkLogs']}")

      if stop_params["networkLogs"].to_s == "true"
        mitm = MitmProxy.new(device, stop_params, @@settings)
        mitm.stop_proxy(current_device_config)
        mitm.capture_har
      end
    end

    def kill_all_forked_processes(device)
      forked_file_pids = Dir.glob(Utils.all_forked_files_filter(device))
      BrowserStack.logger.info("forked_file_pids : #{forked_file_pids}")
      forked_file_pids.each do |forked_pid|
        pgpid = Utils.read_first_line(forked_pid)
        if !pgpid.nil? && !pgpid.empty?
          # negative signal kills the group instead
          begin
            Process.kill(-9, pgpid.to_i)
          rescue
            nil
          end
        end
        FileUtils.rm_f(forked_pid)
      end
    end

    def kill_app_download_process(device)
      pid_file = "/tmp/app_download_pid_#{device}"
      return unless File.exist?(pid_file)

      pid = File.read(pid_file)

      return if pid.empty? || pid.nil?

      BrowserStack.logger.info("download_file pid : #{pid}")
      Process.kill(9, pid.to_i)
    rescue
      BrowserStack.logger.info("kill app download process failed")
    end

    def kill_app_install_process(device)
      BrowserStack.logger.info("Killing app install process")

      # Delete the app install log file
      app_install_result_log_file = get_async_app_install_result_log_file(device)
      if File.exist?(app_install_result_log_file)
        BrowserStack.logger.info("Deleting app install result log file - #{app_install_result_log_file}")
        BrowserStack.logger.info("App install result log file content - #{File.read(app_install_result_log_file)}")
        File.delete(app_install_result_log_file)
      end

      # Kill the app install process
      app_install_pid_file = get_app_install_pid_file(device)
      if File.exist?(app_install_pid_file) && !File.zero?(app_install_pid_file)
        pid = File.read(app_install_pid_file).strip
        unless pid.empty?
          BrowserStack.logger.info("Attempting to kill process with PID: #{pid}")

          begin
            Process.kill(9, pid.to_i)
            BrowserStack.logger.info("Killed process with PID: #{pid}")
          rescue Errno::ESRCH
            BrowserStack.logger.info("Process #{pid} already terminated or not found.")
          end
        end
        BrowserStack.logger.info("Deleting app install PID file - #{app_install_pid_file}")
        File.delete(app_install_pid_file)
      end
    rescue
      BrowserStack.logger.warn("kill app install process failed: #{e.message}")
      [app_install_result_log_file, pid_file].each do |file|
        if file && File.exist?(file)
          BrowserStack.logger.info("Cleaning up file: #{file}")
          File.delete(file)
        end
      end
    end

    def local_testing_chrome_extension_cleanup(device_id,  session_id)
      helper = LocalTestingChromeExtension.new(device_id, session_id)
      helper.cleanup
    end

    def reboot_and_wait(device, wait_time=30)
      start_time = Time.now
      error = ""
      current_device_config = device_configuration_check(device)
      idevice = BrowserStack::IPhone.new(
        current_device_config,
        device
      )

      FileUtils.touch(reboot_file(device).to_s)
      # idevice.kill_device_processes
      idevice.reboot
      sleep wait_time
      current_device_config = device_configuration_check(device)
      device_version = current_device_config['device_version'].to_f
      if device_version >= 18
        BrowserStack.logger.info("Waiting for device to be available on idevice_id and devicectl")
        wait_until_device(:on_idevice_id, device, idevice)
        wait_until_device(:on_devicectl, device, idevice)
        BrowserStack.logger.info("Device available on idevice_id and devicectl")
      end
      poll_device_after_reboot(device, idevice)
      # idevice.restart_processes_after_reboot
    rescue => e
      BrowserStack.logger.error("Error while rebooting #{e.message} \n#{e.backtrace.join("\n")}")
      error = "#{ e.class } - #{ e.message }"
    ensure
      BrowserStack::Zombie.push_logs("timed-DeviceManager#reboot_and_wait", error, { "device" => device, 'data' => Time.now - start_time })
    end

    def wait_until_device(action, ios_device, idevice)
      online_check_count = 63
      online_check_count.times do |i|
        case action
        when :on_idevice_id
          return nil if idevice.device_on_usb?
        when :on_devicectl
          return nil if on_devicectl?(ios_device)
        end
        BrowserStack.logger.info("Device still not #{action} #{@device_id}. Check number: #{i}")
        sleep 2
      end
      raise "recover device timeout: device still not #{action}"
    end

    def devicectl_state(device)
      state = DeviceCtl::List.device_state(device)
      BrowserStack.logger.info("Device state: #{state}")
      state
    end

    def on_devicectl?(device)
      device_state = devicectl_state(device)
      return false if device_state.nil? || device_state.empty? || device_state.include?("No device") || device_state.include?("unavailable")

      true
    end

    def poll_device_after_reboot(device, idevice)
      # online_check_count * 6 = 90secs. Device does not come up by then we exit.
      online_check_count = 45
      device_state = DeviceState.new(device)
      is_full_cleanup = device_state.mdm_full_cleanup_file_present?

      online_check_count.times do |i|
        is_online = if is_full_cleanup
                      idevice.device_on_usb?
                    else
                      idevice.online?
                    end

        if is_online
          BrowserStack.logger.info "Device rebooted and up #{device}"
          FileUtils.remove_file(reboot_file(device).to_s)
          return nil
        end
        BrowserStack.logger.info "Device still not online #{device}. Check number: #{i}"

        reset_usb device if i == 40
        sleep 2
      end
      raise "Manual fix required: couldn't restart the device"
    end

    def clear_apple_pay_data(device, session_id, device_state)
      BrowserStack.logger.info("Calling clean apple pay data")
      cleanup_request_time = Benchmark.measure do
        Secure::AssistiveTouchHelper.new(device, session_id).switch("disable")
        Secure::ApplePaySessionData.new(session_id,  device).run_and_report_cleanup("settings")
      end
      BrowserStack.logger.info("Apple Pay session data cleanup request took: #{cleanup_request_time.real * 1000} ms")
      device_state.remove_apple_pay_data_file
      device_state.remove_apple_pay_sensitive_data_cleanup_failure_file
    rescue => e
      BrowserStack.logger.info("Apple Pay Data Cleanup Failed:#{e.message} \n#{e.backtrace.join("\n")}")
      device_state.touch_apple_pay_sensitive_data_cleanup_failure_file
    end

    def reset_usb(did)
      reset_done = BrowserStack::OSUtils.execute("ioreg -p IOUSB -l -b | grep -E '@|kUSBSerialNumberString|USB Address|USB Serial Number' | grep -C1 #{did.tr('-', '')} | grep Address | awk '{print $NF}' | xargs /usr/local/.browserstack/deps/usb_reset")
      BrowserStack.logger.info("Reset info for #{did} : #{reset_done}")
    rescue => e
      BrowserStack.logger.error("Exception in reset_usb: #{e.message} \n#{e.backtrace.join("\n")}")
    end

    def write_session_info(device, params, session_subtype = nil)
      BrowserStack.logger.info("Writing session info for #{device}")
      MobileSessionInfo.new(device, params, session_subtype).save
    rescue => e
      BrowserStack.logger.error("Exception in MobileSessionInfo #{device}: #{e.message} \n#{e.backtrace.join("\n")}")
    end

    def check_and_set_appium_version(params)
      appium_version = params[:appium].to_s
      if Gem::Version.new(appium_version) >= Gem::Version.new('2.0.0')
        appium_version = params[:automationName] == 'xcuitest' ? "#{appium_version}.#{params[:automationVersion]}" : "#{appium_version}.#{params[:app_automate_custom_params][:default_xcuitest_wda]}"
      end
      @@settings['appium_roots'].keys.include?(appium_version) ? appium_version : @@settings['default_appium_version'].to_s
    end

    def init_feature_usage(params)
      feature_usage = {}
      features_to_track = %w[airplaneMode customMedia gpsLocation geoLocation resignFalse customNetwork otherApps midSessionInstallApps orientation customMedia_contacts install_custom_ca_certificates]
      features_to_track.each do |feature|
        feature_usage[feature] = {
          success: "disabled",
          exception: ""
        }
      end
      check_to_disable_features = ["networkLogs", "deviceLogs"]
      check_to_disable_features.each do |feature|
        feature_usage[feature] = { success: "disabled", exception: "" } if params[feature].to_s != "true"
      end
      feature_usage
    end

    def validate_custom_media_sync(device, photos_count, videos_count, params) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
      custom_media_log = "/tmp/media_sync_#{device}/Documents/media_sync.log"
      sleep(5) # initial sleep
      begin
        retries ||= 7
        BrowserStack.logger.info "Custom Media Sync Retry Left: #{retries}"
        result, status = BrowserStack::OSUtils.execute("/usr/local/bin/gtimeout -s KILL 20 #{IOS_DEPLOY} -I --id #{device} -1 #{BROWSERSTACK_APP_BUNDLE_ID} -w Documents/media_sync.log --to /tmp/media_sync_#{device}", true)
        if status != 0
          BrowserStack.logger.info "[custom media sync] Command Execution Failed with status #{status}"
          raise "[custom media sync] Command Execution Failed"
        end
        result = BrowserStack::OSUtils.execute("grep -c 'File write successfully' #{custom_media_log}")
        raise "[media_sync.log] File write exception" if result.to_i == 0

        sync_done = BrowserStack::OSUtils.execute("grep 'Success: Media sync done' #{custom_media_log}")
        upload_error = BrowserStack::OSUtils.execute("grep 'Error: Some error occured during' #{custom_media_log}")
        raise "Custom media not synced" if sync_done.to_s.empty? && upload_error.to_s.empty?
      rescue
        if retries > 0
          retries -= 1
          sleep(5)
          retry
        end
      ensure
        _, status = BrowserStack::OSUtils.execute("rm -rf /tmp/media_sync_#{device}", return_status = true)
        BrowserStack.logger.info "Custom Media log cleanup failed [/tmp/media_sync_#{device}]" if status != 0
        if photos_count.to_i > 0 && videos_count.to_i == 0
          sync_done = sync_done.to_s.strip
          media_sync_count = sync_done.gsub(/[^0-9]/, '').to_i || 0
          BrowserStack.logger.info "Post media sync, #{media_sync_count} photos synced"
          if photos_count != media_sync_count
            params[:feature_usage]["customMedia"] = { success: false, exception: "custom_media sync failed" }
            kind = @is_app_testing ? "aa-custom-media-verification-failed" : "aut-custom-media-verification-failed"
            BrowserStack::Zombie.push_logs(kind, "custom_media sync failed", { "session_id" => params['session_id'], "device" => device, "url" => "automate" })
            raise FireCMDException.new("Expected #{photos_count} photos to sync, but only synced #{media_sync_count} photos", BROWSERSTACK_ERROR_STRING, "some_unknown_error_occured")
          end
        end
        BrowserStack.logger.info "Expected #{photos_count} photos to sync"
        unless upload_error.to_s.empty?
          params[:feature_usage]["customMedia"] = { success: false, exception: "custom_media upload failed" }
          raise FireCMDException.new(upload_error, BROWSERSTACK_ERROR_STRING, "custom_media_upload_failed")
        end
      end
    end

    # Send the rtc start command to WDA to start the RTC connection state and
    # connection to the peer server.
    # @param current_device [Hash] Device information on which current
    # session is running.
    # @param params [Hash] Request hash params
    # @param device [Hash] Device on which test is executed.
    def start_rtc_request(current_device, params, device) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
      if Gem::Version.new(current_device["device_version"]) < Gem::Version.new("18.0") ||
        !params.key?("non_replay_kit_params")
        params = update_streaming_params_from_platform(current_device["device_name"], params)
      else
        begin
          streaming_params = JSON.parse(params["non_replay_kit_params"])
          params.merge!({
            "width" => streaming_params["captureWidth"].to_i,
            "height" => streaming_params["captureHeight"].to_i
          })
          DataReportHelper.new("streaming-params").report(
            {
              device: device,
              result: 'success',
              streaming_params: streaming_params
            },
            eds_only: true
          )
        rescue => e
          BrowserStack.logger.info(
            "non_replay_kit_params failed for device #{device} #{e.message}"
          )
          BrowserStack::Zombie.push_logs(
            "non_replay_kit_params", "non_replay_kit_params from rails failed for device",
            {
              "device" => device,
              "data" => {
                "non_replay_kit_params" => params["non_replay_kit_params"],
                "error_message" => e.message
              }
            }
          )
          params = update_streaming_params_from_platform(current_device["device_name"], params)
        end
      end
      BrowserStack.logger.info(
        "Using #{params['width']} width and #{params['height']} height for rtc params"
      )

      params.merge!({
        "debugger_ip" => current_device["zotac_host"],
        "debugger_port" => current_device["debugger_port"],
        "device_version" => current_device["device_version"],
        "device_static_name" => current_device["device_name"]
      })

      mini_ip = params[:terminal_ip]
      params[:useReplayKit] = params['use_replay_kit_for_interaction']
      params['automate_session_id'] = params['webrtc_session_id']
      BrowserStack.logger.info "Params terminal ip: #{mini_ip}"
      Utils.write_to_file_with_lock(session_file(device), params.to_json)
      if mini_ip
        BrowserStack.logger.info "Launching WDA for interactive session #{params.inspect}"
        # Use for running the wda for interaction streaming
        Thread.bs_run do
          BrowserStack::WebDriverAgent.launch(device, mini_ip, params)
        end
      else
        BrowserStack.logger.info "Terminal IP is not present not starting interactive session: #{mini_ip}"
      end
    end

    def fire_cmd_start(device, params) # rubocop:todo Metrics/CyclomaticComplexity, Metrics/AbcSize, Metrics/MethodLength, Metrics/PerceivedComplexity
      raise FireCMDException.new("Cannot start session. #{device} in use by another user or cleanup.", BROWSERSTACK_ERROR_STRING, "device_already_in_use") if device_in_use?(device, params)

      device_state = DeviceState.new(device)
      check_and_set_cleanup_policy(device, params, device_state)
      check_dedicated_device_session(device_state, params, device)
      extract_device_logger_params(device, device_state, params)
      check_and_give_settings_app_access(device, params)

      remove_proxy_setup(device, params) if !params[:extraAccess].nil? && params[:extraAccess].include?("remove_proxy")

      check_and_enable_wifi(device_state, params)
      check_and_start_ios_watcher(device, params)
      CustomMDMManager.new(device, BrowserStack.logger).backup_unlock_token
      Thread.bs_run { start_battery_metrics_publisher(device, params["session_id"]) }

      if device_state.minimized_cleanup_unreserved_file_present?
        device_state.remove_minimized_cleanup_unreserved_file
        device_state.remove_minimized_cleanup_reserved_file
        device_state.remove_preserve_app_state_reserved_file
        raise FireCMDException.new("Cannot start session. Regular Cleanup was supposed to happen on #{device}", BROWSERSTACK_ERROR_STRING, 'incorrect_terminal_allocated_minified_flow')
      end

      firecmd_start_time = Time.now
      @is_app_testing = params[:genre].to_s.eql?('app_automate')
      params["app_automate_custom_params"] = @is_app_testing ? parse_app_automate_custom_params(params) : {}
      params["async_app_download_install"] = params["app_automate_custom_params"]["async_app_download_install"].to_s == "true"
      params["patchApp"] = params["app_automate_custom_params"]["patchApp"].to_s == "true"
      params["preInstallApp"] = params["app_automate_custom_params"]["preInstallApp"].to_s == "true"
      params["install_via_ideviceinstaller"] = params["app_automate_custom_params"]["install_via_ideviceinstaller"].to_s == "true"
      params["skip_one_wda"] = params["app_automate_custom_params"]["skip_one_wda"].to_s == "true"
      params["enable_contacts_app_access"] = params["app_automate_custom_params"]["enable_contacts_app_access"].to_s == "true"
      params["enable_test_flight_app"] = params["app_automate_custom_params"]["enable_test_flight_app"].to_s == "true"
      params["flutter_port"] = params["app_automate_custom_params"]["flutter_port"].to_i
      params["subregion_app_caching_enabled"] =
        params["app_automate_custom_params"]["subregion_app_caching_proxy_enabled"].to_s == "true"
      params["mobile_cspt_app_metrics_enabled"] = params["app_automate_custom_params"]["mobile_cspt_app_metrics_enabled"].to_s == "true"
      if params["app_automate_custom_params"]["mdm_enterprise_app_install"].to_s == "true"
        params["mdm_enterprise_app_install"] = true
        params["display_name"] = params["app_automate_custom_params"]["display_name"]
      end
      params[APP_PATCH_TYPE] = params["app_automate_custom_params"][APP_PATCH_TYPE]
      params[:sensor_mocking] = true if ALLOWED_APP_PATCH_TYPES.include?(params[APP_PATCH_TYPE])
      video_rec_logger = VideoRecProcessLogger.new(device, params['automate_session_id'])
      is_minimized_flow = false
      params["preserve_app_state"] = false
      if params[:reserveDevice].to_s == "true" || params["reserveDevice"].to_s == "true"
        is_minimized_flow = device_state.minimized_cleanup_reserved_file_present?
        # minimized_cleanup_reserved_file should not be touched everytime, as the last modified time of this file is used to identify if the reserved session flow duration is less than DEVICE_RESERVED_UNDER_MINIFIED_FLOW_THRESHOLD
        device_state.touch_minimized_cleanup_reserved_file unless is_minimized_flow
        params["preserve_app_state"] = true if @is_app_testing && device_state.preserve_app_state_reserved_file_present?
        BrowserStack.logger.info("[FIRECMD] is_minimized_flow : #{is_minimized_flow}, minimized_cleanup_reserved_file_present : #{device_state.minimized_cleanup_reserved_file_present?} preserve_app_state : #{params['preserve_app_state']}")
      end

      feature_usage = init_feature_usage(params)
      params[:feature_usage] = feature_usage

      response_hash = {}
      video_sync_hash = {
        "start_firecmd_request" => Time.now.to_f
      }
      automate_funnel = AutomateFunnel.new
      Utils.mark_event_start('fire_cmd.write_session_info', params[:event_hash])
      automate_funnel.mark_block_start('iOSFireCMD')
      automate_funnel.mark_breakup_start

      current_device = device_configuration_check(device)

      # Adding this as part of https://browserstack.atlassian.net/browse/APS-10716 - Safari was failing on first try in case of ios 17.4 and above.
      # have moved to server/iphone.rb -> automation_cleanup, will clear this if all goes well.
      if params[:genre].to_s.eql?('automate') && ['iphone', 'ipad'].include?(params["browserName"].to_s) && current_device['device_version'].to_f >= 17.4 && !params["device"].nil? && params["iosSafariStartStop"].to_s == "true"
        BrowserStack.logger.info("Starting and stopping safari for #{device} - #{current_device['device_version'].to_f} - #{params['browserName']}")
        BrowserStack::IPhone.start_safari_using_wda(device)
        BrowserStack::IPhone.kill_safari_using_wda(device)
      end

      params.merge!("iproxyPort" => current_device["webdriver_port"].to_i)

      # Not supporting Interactive session with Apple TV devices for now
      allowed_webrtc_streaming = params["webrtc_session_id"] && !AppleTVUtils.apple_tv_device?(device)

      # Starting the webrtc endpoint for interactive session in ios
      Utils.write_to_file_with_lock(session_file(device), params.to_json) unless allowed_webrtc_streaming

      automate_funnel.mark_block_start('DeviceSetup')
      FileUtils.touch(session_start_file(device))
      write_session_info(device, params)

      # bs_run creates a new thread & copies logging params to it
      Thread.bs_run { log_device_internet_connection(device, params) }
      Utils.mark_event_end('fire_cmd.write_session_info', params[:event_hash])

      if allowed_webrtc_streaming
        Utils.mark_event_start('fire_cmd.ios_interactive_session', params[:event_hash])
        ios_interactive_session_tag = 'iosInteractiveSession'
        automate_funnel.mark_block_start(ios_interactive_session_tag)
        start_rtc_request(current_device, params, device)
        automate_funnel.mark_block_end(ios_interactive_session_tag)
        Utils.mark_event_end('fire_cmd.ios_interactive_session', params[:event_hash])
      end

      appium_port = current_device['selenium_port']
      wda_port = current_device['webdriver_port']
      webkit_port = current_device['debugger_port']
      device_name = current_device['device_name']
      device_version = current_device['device_version']
      device_unlocked = AppleTVUtils.apple_tv_device?(device)

      check_and_enable_paint_timing(device, params) if params[:paint_timing_enable] && params[:paint_timing_enable] == "true" && device_version.to_i >= 14 && device_version.to_i < 18

      # This is a deprecated code block, we have currently removed airplane-mode from documentation as well.
      if params[:genre] == "app_automate" && params['network_airplane_mode'] && params['network_airplane_mode'].to_s == 'true'
        airplane_response = enable_airplane_mode(device, params, current_device, device_unlocked)
        BrowserStack.logger.info "Unlocking, via enabling airplane mode - fire_cmd_start"
        if airplane_response
          # error condition
          params[:feature_usage]["airplaneMode"] = { success: false, exception: airplane_response[:error][0, 100] }
          return airplane_response
        end
        params[:feature_usage]["airplaneMode"] = { success: true }
        device_unlocked = true # enable_airplane_mode unlocks the device
      end

      # check for allowed_webrtc_streaming -> since we are calling BrowserStack::WebDriverAgent.launch
      # in function start_rtc_request, and starting appium unlocks the screen.
      if !device_unlocked && !allowed_webrtc_streaming
        Utils.mark_event_start('fire_cmd.unlock_device', params[:event_hash])
        BrowserStack::IPhone.unlock_device(device, 20)
        Utils.mark_event_end('fire_cmd.unlock_device', params[:event_hash])
      end

      # if timezone is passed
      if params["timezone"] && !is_minimized_flow
        Utils.mark_event_start('fire_cmd.set_timezone_time', params[:event_hash])
        begin
          BrowserStack::IPhone.change_time_zone(device, params["timezone"], params["timezone_mdm"], params['automate_session_id'], params[:genre])
          params[:feature_usage]["timezone"] = { success: true }
        rescue => e
          BrowserStack.logger.info("[FIRECMD] Update timezone failed: message : #{e.message}\n backtrace: #{e.backtrace.join('\n')}")
          params[:feature_usage]["timezone"] = { success: false, exception: e.message[0, 100] }
          raise FireCMDException.new(e.message[0, 100], BROWSERSTACK_ERROR_STRING, "setting_timezone_failed")
        ensure
          Utils.mark_event_end('fire_cmd.set_timezone_time', params[:event_hash])
        end
      end

      params["enableSim"] = params["enableSim"].to_s == "true" && DeviceSIMHelper.sim?(device)
      if params['enableSim']
        BrowserStack.logger.info("Starting SIM setup")
        params["sms_app_access_enabled"] = 'true'
        sim_setup(device, params)
      end

      params["enableApplePay"] = params["enableApplePay"].to_s == "true" && Secure::ApplePay.apple_pay_device?(device)
      params["enableApplePay"] = true if Secure::ApplePay.dedicated_cloud_apple_pay_device?(device)
      if params['enableApplePay']
        BrowserStack.logger.info("Starting Apple Pay setup")
        apple_pay_setup(device, params)
        BrowserStack.logger.info("Apple Pay setup done")
      end

      if params['enablePasscode'].to_s == "true"
        BrowserStack.logger.info("enable device passcode = #{params['enablePasscode']}")
        enable_passcode(device, params)
      end

      if params['customCertificateFile']
        BrowserStack.logger.info("Installing Custom Certificate")
        install_custom_certificate(device, params)
      end

      if params['updateIosDeviceSettings']
        BrowserStack.logger.info("updating Ios Device Settings as #{params['updateIosDeviceSettings']}")
        update_device_settings(device, params)
      end
      # If allowPopups is true
      Utils.mark_event_start('fire_cmd.allow_popups', params[:event_hash])
      if params["safariAllowPopups"] && params["safariAllowPopups"] == "true"
        if AllowPopups.do(device, device_version, params[:genre])
          BrowserStack.logger.info "Enabled popups for this session"
        else
          BrowserStack.logger.error("[FIRECMD] Failed to AllowPopup")
          automate_funnel.mark_block_end('DeviceSetup', 'failure', 'Failed to AllowPopup')
          automate_funnel.mark_breakup_end
          automate_funnel.mark_block_end('iOSFireCMD', 'failure', 'Failed to AllowPopup')
          response_hash['funnel_data'] = automate_funnel.generate_data_json
          response_hash[:error] = "Failed to AllowPopup"
          return response_hash
        end
        sleep 2 # Do not start video rec immediately, wait for session Delete to complete. In some cases the Settings app is visible momentarily in the beginning of the video.
      end
      Utils.mark_event_end('fire_cmd.allow_popups', params[:event_hash])

      # Disable Prevent cross-site tracking
      Utils.mark_event_start('fire_cmd.enable_cross_site_tracking', params[:event_hash])
      if should_enable_cross_site_tracking?(params) && device_version.to_i >= 11
        if HandleCrossSiteTracking.do(device, device_version)
          BrowserStack.logger.info "Enable cross-site tracking for this session"
        else
          BrowserStack.logger.info("[FIRECMD] Failed to enable cross site tracking.")
        end
        sleep 2
      end
      Utils.mark_event_end('fire_cmd.enable_cross_site_tracking', params[:event_hash])

      # get current orientation of the device
      unless AppleTVUtils.apple_tv_device?(device)
        Utils.mark_event_start('fire_cmd.get_orientation', params[:event_hash])
        get_orientation(device)
        Utils.mark_event_end('fire_cmd.get_orientation', params[:event_hash])
      end
      is_v2_recording = false
      v2_recording_triggered = false
      if params[:video].to_s == 'true' && params['video_params_v2']
        record_params = begin
          JSON.parse(params['video_params_v2'])
        rescue
          {}
        end
        is_v2_recording = !record_params.nil? && (record_params['v2'] == 'true')
      end

      # not starting AI Proxy in case of App Automate session
      is_ai_enabled = false
      begin
        is_ai_enabled = !params['ai_enabled_session'].nil? && JSON.parse(params['ai_enabled_session'])['enabled'] == true && !@is_app_testing
      rescue JSON::ParserError => e
        BrowserStack.logger.info "Some error happened in enabling AI. Exception details: #{e.message}"
        is_ai_enabled = false
      end
      if is_ai_enabled
        begin
          appium_port = current_device["selenium_port"]
          ai_proxy_port = appium_port.to_i + AI_PROXY_PORT_OFFSET
          BrowserStack.logger.info "Starting AI Proxy"
          ai_proxy = AIProxy.new(params, ai_proxy_port, appium_port, device)
          is_ai_proxy_started = ai_proxy.start_proxy
        rescue => e
          BrowserStack.logger.info "Some error happened in starting AI Proxy. Exception details: #{e.inspect}"
        end
      end

      unless is_minimized_flow
        appium_version = check_and_set_appium_version(params)
        BrowserStack.logger.info "[FIRECMD] Check and Set Appium : appium_version: #{appium_version} "
        appiumThread = nil
        if appium_version != @@settings['default_appium_version'].to_s &&
          (params["resignApp"].to_s != 'false' || params["mdm_enterprise_app_install"] || params["skip_platform_enterprise_flow"] == "true") &&
          !params["update_app_settings"]

          if is_v2_recording
            v2_recording_triggered = true
            start_v2_video_recording(params, automate_funnel, video_sync_hash, device, wda_port)
          end

          BrowserStack.logger.info("Starting appium switch in parallel with #{appium_version}")
          # bs_run creates a new thread & copies logging params to it
          appiumThread = Thread.bs_run do
            switch_appium(params, device, current_device, appium_version)
          end
        end
      end

      automate_funnel.mark_block_end('DeviceSetup', 'success')
      if params[:genre] == "app_automate"
        Utils.mark_event_start('fire_cmd.collect_wda_process_metrics', params[:event_hash])
        collect_wda_process_metrics(device, params)
        Utils.mark_event_end('fire_cmd.collect_wda_process_metrics', params[:event_hash])
      end

      if is_minimized_flow
        BrowserStack.logger.info("Removing latitude, longitude, networkSimulation, custom_media from params as we are in MINIFIED FLOW")
        ["latitude", "longitude", :networkSimulation, "networkSimulation", "custom_media"].each do |param_to_remove|
          params.delete(param_to_remove)
        end
        BrowserStack.logger.info("MINIFIED FLOW updated params : #{params}")
      end

      enable_async_main_app_install = is_async_install_required?(params, is_minimized_flow)
      download_and_install_thread = nil

      BrowserStack.logger.info("[ASYNC FLOW] [FIRE CMD] enable_async_main_app_install: #{enable_async_main_app_install} ")

      if params["s3_app_url"].to_s != ""
        begin
          if params["s3_app_url"].include?("testflight.apple.com")
            BrowserStack.logger.info "Detected TestFlight URL: #{params['s3_app_url']}"
            unless device_unlocked
              Utils.mark_event_start('fire_cmd.unlock_device', params[:event_hash])
              BrowserStack::IPhone.unlock_device(device, 20)
              Utils.mark_event_end('fire_cmd.unlock_device', params[:event_hash])
              device_unlocked = true
            end
            Utils.mark_event_start('fire_cmd.testflight_main_app_installation', params[:event_hash])
            testflight_automation = Automation::TestFlight.new(device, params['automate_session_id'])
            testflight_automation.install_app_via_public_link(params["s3_app_url"])
            FileUtils.touch("/tmp/testflight_in_app_automate_opened_#{device}")
            Utils.mark_event_end('fire_cmd.testflight_main_app_installation', params[:event_hash])
          elsif params["mdm_enterprise_app_install"]
            Utils.mark_event_start('fire_cmd.perform_mdm_enterprise_app_install', params[:event_hash])
            app_details = { url: params['s3_app_url'], bundle_id: params['app_testing_bundle_id'], display_name: params['display_name'] }
            perform_mdm_enterprise_app_install(params['automate_session_id'], device, app_details, params)
            params.merge!(app_type: "main")
            downloaded_app_path = download_and_install_app(device, app_details, true, params['automate_session_id'], false, params, true)
            params["downloaded_app_path"] = downloaded_app_path
            Utils.write_to_file_with_lock(session_file(device), params.to_json)
            Utils.mark_event_end('fire_cmd.perform_mdm_enterprise_app_install', params[:event_hash])
          elsif enable_async_main_app_install
            params["enable_async_main_app_install"] = enable_async_main_app_install
            download_and_install_thread = Thread.bs_run do
              BrowserStack.logger.info("[ASYNC FLOW] [FIRE CMD] asyncly installing the application")
              install_main_app(params, device)
            end
          elsif params[:genre] == "app_automate" && params["preInstallApp"]
            BrowserStack.logger.info("Skipping app download and install as preInstallApp is set")
          else
            install_main_app(params, device)
          end
        rescue FireCMDException => e
          take_screenshot(params)
          BrowserStack.logger.info("[FIRECMD] App installation failed: message : #{e.message}\n backtrace: #{e.backtrace.join('\n')}")
          return { error: e.message.to_s, kind: "app_download_install_failure", type: e.type.to_s }
        rescue => e
          take_screenshot(params)
          BrowserStack.logger.info("[FIRECMD] App installation failed: message : #{e.message}\n backtrace: #{e.backtrace.join('\n')}")
          return { error: e.message.to_s, kind: "app_download_install_failure", type: BROWSERSTACK_ERROR_STRING }
        end
      end

      if params[:genre] == "app_automate" && params["mobile_cspt_app_metrics_enabled"]
        Utils.mark_event_start('fire_cmd.start_mcspt_measurement', params[:event_hash])
        bundle_id = fetch_bundle_id_from_session_file(device, params['session_id'])
        MCSPT.platform_perf_measurement('start', 'app_automate', params['automate_session_id'], params[:user_id], [bundle_id], device)
        device_state.touch_aa_mcspt_measurement_enabled_file
        Utils.mark_event_end('fire_cmd.start_mcspt_measurement', params[:event_hash])
      end

      Utils.mark_event_start('fire_cmd.download_and_push_custom_media', params[:event_hash])
      if params["custom_media"]
        bundle_id = fetch_bundle_id_from_session_file(device, params['session_id'])
        BrowserStack.logger.info "[FIRECMD] download_and_push_custom_media"
        begin
          photos_count, videos_count, params[:feature_usage] = BrowserStack::IPhone.download_and_push_custom_media(params["custom_media"], "appium", device, params['automate_session_id'], bundle_id, params[:feature_usage]) # photos_count & videos_count are being used in validate_custom_media
        rescue CustomContactsError => e
          BrowserStack.logger.info("[CustomContactsError] Custom Contacts update failed ERROR : #{e}\n message : #{e.message}\n, kind : #{e.kind}, type : #{e.type} \n backtrace: #{e.backtrace.join('\n')}")
          params[:feature_usage]["customMedia_contacts"] = { success: false, exception: "custom_media contacts load failed" }
          return { error: e.message.to_s, kind: e.kind, type: e.type.to_s }
        rescue FireCMDException => e
          BrowserStack.logger.info("[FIRECMD] Custom Media Files download/update failed: message : #{e.message}\n backtrace: #{e.backtrace.join('\n')}")
          params[:feature_usage]["customMedia"] = { success: false, exception: "custom_media download/update failed" }
          kind = @is_app_testing ? "aa-custom-media-download-push-failed" : "aut-custom-media-download-push-failed"
          BrowserStack::Zombie.push_logs(kind, e.message.to_s, { "session_id" => params['session_id'], "device" => device, "url" => "automate" })
          return { error: e.message.to_s, kind: "custom_media_download_update_failed", type: e.type.to_s }
        end
      end
      Utils.mark_event_end('fire_cmd.download_and_push_custom_media', params[:event_hash])

      update_app_settings(params, device, device_version) if params["update_app_settings"] && !params["mdm_enterprise_app_install"]

      Utils.mark_event_start('fire_cmd.other_apps_download_and_install_time', params[:event_hash])
      if params["other_apps"]
        begin
          params["other_app_bundle_ids"] = download_and_install_dependent_apps(params["other_apps"], device, params, params['automate_session_id'])
          Utils.write_to_file_with_lock(session_file(device), params.to_json)
          write_session_info(device, params)
          params[:feature_usage]["otherApps"] = { success: true }
        rescue FireCMDException => e
          BrowserStack.logger.info("[FIRECMD] Other Apps installation failed: message : #{e.message}\n backtrace: #{e.backtrace.join('\n')}")
          params[:feature_usage]["otherApps"] = { success: false, exception: e.message[0, 100] }
          return { error: e.message.to_s, kind: "other_apps_install_failure", type: e.type.to_s }

        rescue => e
          BrowserStack.logger.info("[FIRECMD] Other Apps installation failed: message : #{e.message}\n backtrace: #{e.backtrace.join('\n')}")
          params[:feature_usage]["otherApps"] = { success: false, exception: e.message[0, 100] }
          return { error: e.message.to_s, kind: "other_apps_install_failure", type: BROWSERSTACK_ERROR_STRING }
        end
      end
      Utils.mark_event_end('fire_cmd.other_apps_download_and_install_time', params[:event_hash])

      Utils.mark_event_start('fire_cmd.mid_session_apps_download_time', params[:event_hash])
      if params["mid_session_install_apps"]
        begin
          params["mid_session_app_bundle_ids"] = download_and_install_dependent_apps(params["mid_session_install_apps"], device, params, params['automate_session_id'], "mid_session_install_apps")
          Utils.write_to_file_with_lock(session_file(device), params.to_json)
          write_session_info(device, params)
          params[:feature_usage]["midSessionInstallApps"] = { success: true }
        rescue FireCMDException => e
          BrowserStack.logger.info("[FIRECMD] Mid Session Apps Download failed: message : #{e.message}\n backtrace: #{e.backtrace.join('\n')}")
          params[:feature_usage]["midSessionInstallApps"] = { success: false, exception: e.message[0, 100] }
          return { error: e.message.to_s, kind: "mid_session_apps_download_failure", type: e.type.to_s }

        rescue => e
          BrowserStack.logger.info("[FIRECMD] Mid Session Apps Download failed: message : #{e.message}\n backtrace: #{e.backtrace.join('\n')}")
          params[:feature_usage]["midSessionInstallApps"] = { success: false, exception: e.message[0, 100] }
          return { error: e.message.to_s, kind: "mid_session_apps_download_failure", type: BROWSERSTACK_ERROR_STRING }
        end
      end
      Utils.mark_event_end('fire_cmd.mid_session_apps_download_time', params[:event_hash])

      Utils.mark_event_start('fire_cmd.get_chromium_bundle_id', params[:event_hash])
      if params["chromium_bundle_id"].to_s == "true"
        begin
          chromium_manager = Chromium.new(device)
          bundle_id = chromium_manager.bundle_id
          response_hash.merge!({ chromium_bundle_id: bundle_id })
        rescue => e
          BrowserStack.logger.info("[FIRECMD] Get Chromium bundle id failed: message : #{e.message}\n backtrace: #{e.backtrace.join('\n')}")
          return { error: e.message.to_s, kind: "get_chromium_bundle_id_failed", type: BROWSERSTACK_ERROR_STRING }
        end
      end
      Utils.mark_event_end('fire_cmd.get_chromium_bundle_id', params[:event_hash])
      automate_funnel.mark_block_start('LocalTunnelSetup')
      Utils.mark_event_start('fire_cmd.local_tunnel_setup', params[:event_hash])
      Utils.mark_event_start('fire_cmd.setup_local', params[:event_hash])
      begin
        session_id = params["automate_session_id"]
        PrivoxyManager.write_privoxy_log_head(device, current_device, session_id)
        BrowserStack.logger.info("Starting setup_local for #{device}")
        uninstall_app_thread = BrowserStack::Session.setup_local(params, current_device)
        params[:feature_usage]["geoLocation"] = { success: true } if params[:geoLocation]
      rescue => e
        BrowserStack.logger.info("[FIRECMD] Error Setting Proxy: message : #{e.message}\n backtrace: #{e.backtrace.join('\n')}")
        automate_funnel.mark_block_end('LocalTunnelSetup', 'failure', 'local_setup_failed')
        automate_funnel.mark_breakup_end
        automate_funnel.mark_block_end('iOSFireCMD', 'failure', 'local_setup_failed')
        response_hash['funnel_data'] = automate_funnel.generate_data_json
        response_hash[:error] = e.message.to_s
        response_hash[:kind] = "local_setup_failed"
        response_hash[:type] = BROWSERSTACK_ERROR_STRING
        return response_hash
      end
      Utils.mark_event_end('fire_cmd.setup_local', params[:event_hash])
      check_and_give_icloud_access(device, params, device_state)
      begin
        Utils.mark_event_start('fire_cmd.setup_mitm', params[:event_hash])
        setup_mitm(device, current_device, params) if params[:networkLogs] == 'true' || params[:acceptInsecureCerts] == 'true' || params[:cameraInjection] == 'true'
      rescue => e
        BrowserStack.logger.info("[FIRECMD] MITM setup failed: message : #{e.message}\n backtrace: #{e.backtrace.join('\n')}")
        return { error: e.message.to_s, kind: "mitm_setup_failed", type: BROWSERSTACK_ERROR_STRING }
      ensure
        Utils.mark_event_end('fire_cmd.setup_mitm', params[:event_hash])
        Utils.mark_event_end('fire_cmd.local_tunnel_setup', params[:event_hash])
      end
      automate_funnel.mark_block_end('LocalTunnelSetup', 'success')

      # trust client enterprise app
      if params["resignApp"].to_s == 'false' && params["skip_platform_enterprise_flow"] != "true" && !params["mdm_enterprise_app_install"]
        Utils.mark_event_start('fire_cmd.bypass_resigning', params[:event_hash])
        Utils.track_feature_usage("aa_resign_app_false", params, "attempt")
        begin
          trust_client_enterprise_app(device, params['automate_session_id'], params["downloaded_app_path"])
        rescue FireCMDException => e
          BrowserStack.logger.info("[FIRECMD] Enterprise App trust failed: message : #{e.message}\n backtrace: #{e.backtrace.join('\n')}")
          params[:feature_usage]["resignFalse"] = { success: false, exception: e.message[0, 100] }
          Utils.track_feature_usage("aa_resign_app_false", params, "failure")
          return { error: e.message.to_s, kind: "enterprise_app_trust_failed", type: e.type.to_s }
        ensure
          Utils.mark_event_end('fire_cmd.bypass_resigning', params[:event_hash])
        end
        params[:feature_usage]["resignFalse"] = { success: true }
      end

      # RESIGN APP CHECK
      # This is done so that, in case for resignApp false, we have our own appium script for enterprise trust which are tested on default
      # if we don't do this then testing scope increase as well as brings uncertainty
      # So we for resignApp false we let the appium script to run on default appium and after that we switch to new appium
      # if resignApp is not false we switch appium in parallel
      #
      # UPDATE APP SETTINGS CHECK
      # - Doing this only after main app is successfully downloaded.
      # - Since we need the app to be present on machine and wda running to perform automation for updating app settings.
      if appium_version != @@settings['default_appium_version'].to_s &&
        ((params["resignApp"].to_s == 'false' && !params["mdm_enterprise_app_install"] && params["skip_platform_enterprise_flow"] != "true") || params["update_app_settings"]) && !is_minimized_flow

        if is_v2_recording
          v2_recording_triggered = true
          start_v2_video_recording(params, automate_funnel, video_sync_hash, device, wda_port)
        end

        BrowserStack.logger.info("Starting appium switch in parallel with #{appium_version} when resignApp is false")
        # bs_run creates a new thread & copies logging params to it
        appiumThread = Thread.bs_run do
          switch_appium(params, device, current_device, appium_version)
        end
      end

      device_logger_setup(device, params) unless params["mdm_enterprise_app_install"] && params["update_app_settings"]

      if appiumThread
        Utils.mark_event_start('fire_cmd.join_appium_thread', params[:event_hash])
        appiumThread.join
        BrowserStack.logger.info("Joined the appium thread to main thread")
        Utils.mark_event_end('fire_cmd.join_appium_thread', params[:event_hash])
      end

      # For apps built with YouIEngine framework, we need to do port forwarding.
      # See the techspec here
      # https://browserstack.atlassian.net/wiki/spaces/ENG/pages/1750958250/You.I.Engine+as+AutomationName+in+Appium
      if params["automationName"].to_s.downcase == "youiengine" && !params["youiengine_driver_port"].nil? && !params["youiEngineAppPort"].nil?
        Utils.mark_event_start('fire_cmd.youiengine_port_forwarding', params[:event_hash])
        begin
          PortManager.forward_port(device, params["youiengine_driver_port"], params["youiEngineAppPort"], "YouIEngine")
        rescue FireCMDException => e
          BrowserStack.logger.info("[FIRECMD] [YouIEngine] iProxy failed to boot: message: #{e.message}\n backtrace: #{e.backtrace.join('\n')}")
          return { error: e.message.to_s, kind: "port_forwarding_failure", type: e.type.to_s }
        rescue => e
          BrowserStack.logger.info("[FIRECMD] [YouIEngine] iProxy failed to boot: message: #{e.message}\n backtrace: #{e.backtrace.join('\n')}")
          return { error: e.message.to_s, kind: "port_forwarding_failure", type: BROWSERSTACK_ERROR_STRING }
        ensure
          Utils.mark_event_end('fire_cmd.youiengine_port_forwarding', params[:event_hash])
        end
      end

      if params["automationName"].to_s.downcase == "flutter" && !params["flutter_port"].nil?
        Utils.mark_event_start('fire_cmd.flutter_port_forwarding', params[:event_hash])
        begin
          PortManager.forward_port(device, params["flutter_port"], params["flutter_port"], "Flutter")
        rescue FireCMDException => e
          BrowserStack.logger.info("[FIRECMD] [FLUTTER] iProxy failed to boot: message: #{e.message}\n backtrace: #{e.backtrace.join('\n')}")
          return { error: e.message.to_s, kind: "port_forwarding_failure", type: e.type.to_s }
        rescue => e
          BrowserStack.logger.info("[FIRECMD] [FLUTTER] iProxy failed to boot: message: #{e.message}\n backtrace: #{e.backtrace.join('\n')}")
          return { error: e.message.to_s, kind: "port_forwarding_failure", type: BROWSERSTACK_ERROR_STRING }
        ensure
          Utils.mark_event_end('fire_cmd.flutter_port_forwarding', params[:event_hash])
        end
      end

      Thread.bs_run { remove_wda_memory_limit(device, params) } if params[:genre] == 'app_automate' && params['app_automate_custom_params']['remove_wda_memory_limit'].to_s.downcase == 'true'

      if params[:video].to_s == 'true' && !is_v2_recording
        Utils.mark_event_start('fire_cmd.start_video_recording', params[:event_hash])
        automate_funnel.mark_block_start('VideoRecording')
        if File.exist?(video_test_launch_file(device))
          test_launch_debug_id = begin
            File.read(video_test_launch_file(device)).chomp
          rescue
            nil
          end
          BrowserStack.logger.info("Video test launch debug id : #{test_launch_debug_id} , session_id : #{params['automate_session_id']} ")
        end
        # Sometimes we need to unlock device otherwise we get a black screen during the inital part of video recording - MOBFR-298, MOBFR-411
        WdaClient.new(wda_port).unlock_device unless AppleTVUtils.apple_tv_device?(device)

        VideoRecManager.new(device, params, wda_port).start_rec
        video_sync_hash["video_start_time"] = Time.now.to_f
        automate_funnel.mark_block_end('VideoRecording', 'success')
        Utils.mark_event_end('fire_cmd.start_video_recording', params[:event_hash])
      elsif is_v2_recording
        start_v2_video_recording(params, automate_funnel, video_sync_hash, device, wda_port) unless v2_recording_triggered
      end

      Utils.mark_event_start('fire_cmd.stop_video_logger', params[:event_hash])
      video_rec_logger.stop if is_v2_recording
      Utils.mark_event_end('fire_cmd.stop_video_logger', params[:event_hash])

      Utils.mark_event_start('fire_cmd.setup_geo_location', params[:event_hash])
      automate_funnel.mark_block_start('GeoLocation')
      if !params["latitude"].nil? && !params["latitude"].empty? && !params["longitude"].nil? && !params["longitude"].empty?
        set_gpslocation(device, params)
        params[:feature_usage]["gpsLocation"] = { success: true }
      end
      automate_funnel.mark_block_end('GeoLocation', 'success')
      Utils.mark_event_end('fire_cmd.setup_geo_location', params[:event_hash])

      if download_and_install_thread
        Utils.mark_event_start('fire_cmd.join_download_and_install_thread', params[:event_hash])
        begin
          check_download_install_pid_status(device)
          download_and_install_thread.join
          BrowserStack.logger.info("[ASYNC FLOW] [FIRE CMD] Joined the download_and_install_thread thread to main thread")
        rescue FireCMDException => e
          take_screenshot(params)
          BrowserStack.logger.info("[FIRECMD] App installation failed: message : #{e.message}\n backtrace: #{e.backtrace.join('\n')}")
          return { error: e.message.to_s, kind: "app_download_install_failure", type: e.type.to_s }
        rescue => e
          take_screenshot(params)
          BrowserStack.logger.info("[FIRECMD] App installation failed: message : #{e.message}\n backtrace: #{e.backtrace.join('\n')}")
          return { error: e.message.to_s, kind: "app_download_install_failure", type: BROWSERSTACK_ERROR_STRING }
        ensure
          Utils.mark_event_end('fire_cmd.join_download_and_install_thread', params[:event_hash])
        end
      end

      Utils.mark_event_start('fire_cmd.apply_network_simulation', params[:event_hash])
      automate_funnel.mark_block_start('NetworkSimulation')
      if params[:networkSimulation].to_s == 'true'
        output = apply_network_simulation(device, params)
        params[:feature_usage]["customNetwork"] = if output.include? :error
                                                    { success: false, exception: output[:error][0, 100] }
                                                  else
                                                    { success: true }
                                                  end
      end
      automate_funnel.mark_block_end('NetworkSimulation', 'success')
      Utils.mark_event_end('fire_cmd.apply_network_simulation', params[:event_hash])

      # check if custom media successfully synced
      Utils.mark_event_start('fire_cmd.validate_custom_media', params[:event_hash])
      if params["custom_media"]
        validate_custom_media_sync(device, photos_count, videos_count, params)
        params[:feature_usage]["customMedia"] = { success: true }
      end
      Utils.mark_event_end('fire_cmd.validate_custom_media', params[:event_hash])

      # check if mdm app was successfully synced
      Utils.mark_event_start('fire_cmd.validate_mdm_app_install', params[:event_hash])
      if params["mdm_enterprise_app_install"]
        validate_mdm_app_install(device, params)
        params[:feature_usage]["resignFalse"] = { success: true }
      end
      Utils.mark_event_end('fire_cmd.validate_mdm_app_install', params[:event_hash])
      if params["mdm_enterprise_app_install"] && params["update_app_settings"]
        update_app_settings(params, device, device_version)
        device_logger_setup(device, params)
      end

      video_sync_hash["end_firecmd_request"] = Time.now.to_f
      video_sync_hash["session_id"] = params['automate_session_id']

      FileUtils.mkdir_p(video_workspace_for_session(device, params['automate_session_id'])) unless Dir.exist?(video_workspace_for_session(device, params['automate_session_id']))
      File.write(video_sync_data_file(device, params['automate_session_id']), video_sync_hash.to_json)

      extension_uninstalled = BrowserStack::Session.extension_uninstalled?(params, uninstall_app_thread).to_s == "true"
      return { error: "Redirect extension uninstall failed", kind: "local_setup_failed", type: BROWSERSTACK_ERROR_STRING } unless extension_uninstalled

      session_setup_time = {
        "downloading_app" => params[:event_hash]["app_download_time"] || 0,
        "installing_app" => params[:event_hash]["app_install_time"] || 0,
        "setting_up_appium" => 0
      }

      if params["other_apps"] || params["mid_session_install_apps"]
        session_setup_time.merge!({
          "downloading_and_installing_dependent_apps" => params[:event_hash]["fire_cmd.other_apps_download_and_install_time"].to_i + params[:event_hash]["fire_cmd.mid_session_apps_download_time"].to_i
        })
      end

      if params['update_app_settings']
        session_setup_time.merge!({
          "update_app_settings" => {
            "duration" => params[:event_hash]["fire_cmd.update_app_settings_time"],
            "meta_data" => params['update_app_settings']
          }
        })
      end

      if params["resignApp"].to_s == 'false' && params["skip_platform_enterprise_flow"] != "true"
        session_setup_time.merge!({
          "trusting_enterprise_certificate" => params[:event_hash]["fire_cmd.bypass_resigning"]
        })
      end

      session_setup_time.merge!({
        "setting_up_network_connection" => params[:event_hash]["fire_cmd.local_tunnel_setup"]
      })

      if params[:event_hash]["fire_cmd.testflight_main_app_installation"]
        session_setup_time.merge!({
          "downloading_app_via_testflight" => params[:event_hash]["fire_cmd.testflight_main_app_installation"]
        })
      end

      session_setup_time['setting_up_appium'] = params[:event_hash]["fire_cmd.appium_create_time"] if appium_version != @@settings['default_appium_version'].to_s

      # check whether xcodebuild process is running for this device, ideally there should be already one running (from cleanup wda flow)
      xcodebuild_process_running = !BrowserStack::OSUtils.execute("ps aux | grep #{device} | grep [x]codebuild").empty?

      response_hash.merge!(video_sync_hash)
      response_hash.merge!({ port: appium_port, wda_port: wda_port, xcodebuild_process_running: xcodebuild_process_running.to_s })
      response_hash.merge!({
        "platform_time_stats" => session_setup_time
      })

      Utils.mark_event_start('fire_cmd.playwright_ios_setup', params[:event_hash])
      is_playwright = false
      begin
        is_playwright = !params['is_playwright'].nil? && JSON.parse(params['is_playwright']) == true && !@is_app_testing
      rescue JSON::ParserError => e
        BrowserStack.logger.info "Some error happened in starting PWIOS. Exception details: #{e.message}"
        is_playwright = false
      end

      if is_playwright
        begin
          appium_port = current_device["selenium_port"]
          pwios_proxy_port = appium_port.to_i + PWIOS_PROXY_PORT_OFFSET
          BrowserStack.logger.info "Starting PWIOS Proxy"
          pwios_proxy = PWIOSProxy.new(params, pwios_proxy_port, appium_port, device)
          is_pwios_proxy_started = pwios_proxy.start_proxy
          playwright_url = pwios_proxy.playwright_url

          response_hash["playwright_url"] = playwright_url unless playwright_url.nil?
          BrowserStack.logger.info "pwios_output: playwright_url #{playwright_url}"
        rescue => e
          BrowserStack.logger.info "Some error happened in starting PWIOS Proxy. Exception details: #{e.inspect}"
        end
      end
      Utils.mark_event_end('fire_cmd.playwright_ios_setup', params[:event_hash])

      automate_funnel.mark_breakup_end
      automate_funnel.mark_block_end('iOSFireCMD', 'success')
      response_hash['funnel_data'] = automate_funnel.generate_data_json

      firecmd_end_time = Time.now
      firecmd_execution_time = firecmd_end_time - firecmd_start_time
      BrowserStack.logger.info("[FIRECMD] firecmd_execution_time : #{firecmd_execution_time}")

      response_hash

    rescue FireCMDException => e
      raise e
    rescue => e
      BrowserStack.logger.info("[FIRECMD] #{params['automate_session_id']} #{device} Failed with unknown error: #{e.message} Trace: #{e.backtrace}")
      raise FireCMDException.new(e.message, BROWSERSTACK_ERROR_STRING, UNKNOWN_FIRECMD_BROWSERSTACK_ERROR_KIND)
    ensure
      download_and_install_thread.kill if defined?(download_and_install_thread) && download_and_install_thread&.alive?
    end

    def is_async_install_required?(params, is_minimized_flow)
      params["async_app_download_install"].to_s.downcase == "true" && !(is_minimized_flow || (params["resignApp"].to_s == 'false') || params["update_app_settings"])
    end

    def install_main_app(params, device)
      app_details = { url: params['s3_app_url'], id: params['app_testing_bundle_id'], timeout: params['appDownloadTimeout'] }
      params.merge!(app_type: "main")
      downloaded_app_path = download_and_install_app(device, app_details, true, params['automate_session_id'], false, params)
      params["downloaded_app_path"] = downloaded_app_path
      Utils.write_to_file_with_lock(session_file(device), params.to_json)
      write_session_info(device, params)
    end

    def device_logger_setup(device, params)
      BrowserStack.logger.debug("Starting device logger for device #{device}")
      Utils.mark_event_start('fire_cmd.setup_and_start_device_logging', params[:event_hash])
      Utils.mark_event_start('fire_cmd.device_logs.clear_workspace', params[:event_hash])
      BrowserStack::DeviceLogger.clean_workspace(device)
      BrowserStack::DeviceLogger.initialize(device, params['session_id'])
      Utils.mark_event_end('fire_cmd.device_logs.clear_workspace', params[:event_hash])

      Utils.mark_event_start('fire_cmd.device_logs.initialize_device', params[:event_hash])
      BrowserStack::DeviceLogger.start(device)
      Utils.mark_event_end('fire_cmd.device_logs.initialize_device', params[:event_hash])
      Utils.mark_event_start('fire_cmd.device_logs.start_app_logs', params[:event_hash])
      BrowserStack::DeviceLogger.start_app_logs(device, params)
      Utils.mark_event_end('fire_cmd.device_logs.start_app_logs', params[:event_hash])
      Utils.mark_event_end('fire_cmd.setup_and_start_device_logging', params[:event_hash])
    end

    def check_download_install_pid_status(device)
      download_pid_file = "/tmp/app_download_pid_#{device}"
      install_pid_file = get_app_install_pid_file(device)
      if File.exist?(download_pid_file)
        pid = File.read(download_pid_file)
        BrowserStack::OSUtils.execute("ps -p #{pid} -o state=")
      end

      if File.exist?(install_pid_file)
        pid = File.read(install_pid_file)
        BrowserStack::OSUtils.execute("ps -p #{pid} -o state=")
      end
    rescue => e
      BrowserStack.logger.error("Error while checking download and install pid states for #{device} message: #{e.message} #{e.backtrace}")
    end

    def update_app_settings(params, device, device_version)
      Utils.mark_event_start('fire_cmd.update_app_settings_time', params[:event_hash])
      begin
        params["is_firecmd"] = true
        params["session_id"] = params['automate_session_id']
        params["os_version"] = device_version
        params['is_non_default_appium'] = (check_and_set_appium_version(params) != @@settings['default_appium_version'].to_s)
        session_info = session_file_contents(device)
        app_path = session_info["downloaded_app_path"]
        parsed_app_settings_dsl = BrowserStack::AppSettingsUtil.validate(app_path, params)
        begin
          BrowserStack::AppSettingsUtil.update_settings(device, parsed_app_settings_dsl, params)
        rescue AppSettingsError => e
          raise e
        ensure
          params[:feature_usage]["app_setting"] = { success: true }
        end
      rescue AppSettingsError => e
        BrowserStack.logger.info("[FIRECMD] Update App installation settings failed: message : #{e.type} #{e.kind} #{e.message}\n backtrace: #{e.backtrace.join('\n')}")
        params[:feature_usage]["app_setting"] = { success: false, exception: e.kind }
        raise FireCMDException.new(e.message, e.type, e.kind, e.meta_data)
      ensure
        Utils.mark_event_end('fire_cmd.update_app_settings_time', params[:event_hash])
      end
    end

    def set_gpslocation(device, params)
      BrowserStack.logger.info("Setting location with latitude as #{params['latitude']} and longitude as #{params['longitude']}")
      LocationSimulator.new(device).simulate(params["latitude"], params["longitude"])
      {}.to_json
    end

    def change_mode(device_config, device, params)
      product = params['product']
      BrowserStack.logger.info("Change Mode request from product:#{params['product']}")
      session_id = product == "live_testing" ? params["live_session_id"] : params["app_live_session_id"]
      eds_table = product == "live_testing" ? EdsConstants::LIVE_TEST_SESSIONS : EdsConstants::APP_LIVE_TEST_SESSIONS
      Utils.send_to_eds({
        session_id: session_id,
        product: {
          ios_darkmode_toggled: true
        }
      }, eds_table, true)
      response = BrowserStack::SwitchMode.new(device, session_id, product).change_appearance_to(params['mode'])
      if response == false
        # need to change pusher channel based on product
        product == "live_testing" ? Utils.notify_pusher_for_live("ChangeModeException", params, device) : Utils.notify_pusher("ChangeModeException", params, device)
        return
      end
      product == "live_testing" ? Utils.notify_pusher_for_live("ChangeModeSetSuccess", params, device) : Utils.notify_pusher("ChangeModeSetSuccess", params, device)
      relaunch_user_app(device_config, device)
    end

    def setup_voiceover(device_config, device, params)
      BrowserStack.logger.info("[VOICEOVER] /dm_setup_voiceover begin")
      BrowserStack.logger.info("Inside Device Manager Setup VoiceOver : for #{device}")
      # Get bluetooth name
      bluetooth_name = params['miniBluetoothName']

      t0 = Time.now.to_f
      # Send the request to WDA
      wda_client = WdaClient.new(device_config["webdriver_port"].to_i)
      response = wda_client.setup_voiceover(bluetooth_name)

      t1 = Time.now.to_f

      response["value"]["devicemanager_setup_voiceover_time_split"] = {
        wda_setup_voiceover: ((t1 - t0) * 1000.0).to_i
      }
      BrowserStack.logger.info("[VOICEOVER] /dm_setup_voiceover end with response: #{response}")
      response
    end

    def teardown_voiceover(device_config, device, params)
      wda_client = WdaClient.new(device_config["webdriver_port"].to_i)
      wda_client.teardown_voiceover
    end

    def voiceover_reconnect_bluetooth(device_config, device, params)
      bluetooth_name = params['miniBluetoothName']
      wda_client = WdaClient.new(device_config["webdriver_port"].to_i)
      wda_client.voiceover_reconnect_bluetooth(bluetooth_name)
    end

    def toggle_low_power_mode(device_config, device, params)
      unless params["app_live_session_id"].eql? "qa-no-session"
        Utils.send_to_eds(
          {
            session_id: params["app_live_session_id"],
            product: {
              ios_low_power_mode_toggled: true
            }
          },
          EdsConstants::APP_LIVE_TEST_SESSIONS,
          true
        )
      end
      response = Battery.new(device, params['app_live_session_id']).change_low_power_mode(params['mode'])
      unless response
        if params["app_live_session_id"].eql? "qa-no-session"
          raise "LowPowerModeException"
        else
          Utils.notify_pusher("LowPowerModeException", params, device)
        end

        return
      end
      Utils.notify_pusher("LowPowerModeSetSuccess", params, device) unless params["app_live_session_id"].eql? "qa-no-session"
      relaunch_user_app(device_config, device)
    end

    def switch_prevent_cross_site_tracking(device_config, device, params)
      Utils.send_to_eds({
        session_id: params["live_session_id"],
        product: {
          prevent_cross_site_tracking_toggled: true
        }
      }, EdsConstants::LIVE_TEST_SESSIONS, true)
      response = BrowserStack::PreventCrossSiteTracking.new(device, params['live_session_id']).switch(params['toggle'])
      if response == false
        Utils.notify_pusher_for_live("PreventCrossSiteTrackingException", params, device)
        return
      end
      Utils.notify_pusher_for_live("PreventCrossSiteTrackingSuccess", params, device)
    end

    def safari_settings(device_config, device, params)
      Utils.send_to_eds({
        session_id: params["live_session_id"],
        product: {
          safari_settings: true
        }
      }, EdsConstants::LIVE_TEST_SESSIONS, true)
      response = SafariSettings.new(device, params['live_session_id']).switch(params['safari_settings'])
      Utils.notify_pusher_for_live("SafariSettingsStatus", params, device, response.to_json)
    end

    def set_timezone(device_config, device, timezone_params)
      timezone_params['timezone'] = "UTC" if timezone_params['timezone'] == 'GMT'
      timezone_params['timezone'] = timezone_params['timezone'].gsub('_', ' ')
      timezone_params['timezone_mdm'] = timezone_params['timezone_mdm']
      product = timezone_params['product']
      session_id = product == "live_testing" ? timezone_params["live_session_id"] : timezone_params["app_live_session_id"]
      eds_table = product == "live_testing" ? EdsConstants::LIVE_TEST_SESSIONS : EdsConstants::APP_LIVE_TEST_SESSIONS
      Utils.send_to_eds({
        session_id: session_id,
        product: {
          ios_timezone_changed: true
        }
      }, eds_table, true)
      begin
        BrowserStack::IPhone.change_time_zone(device, timezone_params["timezone"], timezone_params['timezone_mdm'], session_id, product)
      rescue => e
        BrowserStack.logger.error("Exception while timezone automation: #{device}: #{e.message}")
        product == "live_testing" ? Utils.notify_pusher_for_live("TimezonesException", timezone_params, device) : Utils.notify_pusher("TimezonesException", timezone_params, device)
        return
      end
      product == "live_testing" ? Utils.notify_pusher_for_live("TimezoneSetSuccess", timezone_params, device) : Utils.notify_pusher("TimezoneSetSuccess", timezone_params, device)
      relaunch_user_app(device_config, device)
    end

    def toggle_assistive_touch(device_config, device, params)
      Utils.send_to_eds({
        session_id: params["app_live_session_id"],
        product: {
          assistive_touch_toggled: true
        }
      }, EdsConstants::APP_LIVE_TEST_SESSIONS, true)
      response = Secure::AssistiveTouchHelper.new(device, params['app_live_session_id'], params['product']).switch(params['mode'])
      if response == false
        Utils.notify_pusher("ToggleAssistiveTouchException", params, device)
        return
      end
      Utils.notify_pusher("ToggleAssistiveTouchSetSuccess", params, device)
      relaunch_user_app(device_config, device)
    end

    def toggle_passcode(device, passcode_params)
      Utils.send_to_eds({
        session_id: passcode_params["app_live_session_id"],
        product: {
          ios_devicepasscode_toggled: true
        }
      }, EdsConstants::APP_LIVE_TEST_SESSIONS, true)
      begin
        ios_phone = Secure::Passcode.new(device, passcode_params["app_live_session_id"], passcode_params["product"])
        BrowserStack.logger.info("Mode received #{passcode_params['mode']} for the device #{device}")
        if passcode_params["mode"] == "enable"
          device_state = DeviceState.new(device)
          return if device_state.passcode_file_present?

          ios_phone.passcode_settings(REDIS_CLIENT)
          raise "Failed to set passcode" unless ios_phone.set_passcode
        else
          ios_phone.clear_passcode(REDIS_CLIENT)
        end
        Utils.notify_pusher("DevicePasscodeSuccess", passcode_params, device)
      rescue => e
        BrowserStack.logger.error("Exception while toggling passcode: #{device}: #{e.message}")
        Utils.notify_pusher("DevicePasscodeFailure", passcode_params, device)
      end
    end

    def toggle_offline_mode(ip, device_config, device, params)
      product = params['product']
      BrowserStack.logger.info("Offline Mode request from product:#{params['product']}")
      session_id = product == "live_testing" ? params["live_session_id"] : params["app_live_session_id"]
      eds_table = product == "live_testing" ? EdsConstants::LIVE_TEST_SESSIONS : EdsConstants::APP_LIVE_TEST_SESSIONS
      Utils.send_to_eds({
        session_id: session_id,
        product: {
          ios_offlinemode_toggled: true
        }
      }, eds_table, true)

      device_state = DeviceState.new(device)
      session_params = session_file_contents(device)

      pfctl = PFCTLHelper.new(ip, device, session_id, product)

      creative_offline_mode_enabled = device_state.dedicated_device_file_present? && session_params["creative_offline_mode_enabled"].to_s == "true" && !device_config['sim_details'].empty?

      if creative_offline_mode_enabled
        apply_creative_offline_mode(device, params["mode"], session_id)
      elsif params["mode"] == "Online"
        pfctl.disable_offline_mode
      else
        pfctl.enable_offline_mode
      end

      BrowserStack::Zombie.push_logs("ios-offlinemode-toggle-success", nil, { "data" => params[:mode], "device" => device, "session_id" => session_id, "user_os" => product })
      product == "live_testing" ? Utils.notify_pusher_for_live("OfflineModeToggleSuccess", params, device) : Utils.notify_pusher("OfflineModeToggleSuccess", params, device)
    rescue => e
      BrowserStack::Zombie.push_logs("ios-offlinemode-toggle-failure", e.message, { "device" => device, "session_id" => params["app_live_session_id"], "user_os" => product })
      product == "live_testing" ? Utils.notify_pusher_for_live("OfflineModeToggleException", params, device) : Utils.notify_pusher("OfflineModeToggleException", params, device)
      raise e
    end

    def apply_creative_offline_mode(device, mode, session_id)
      BrowserStack.logger.info("Toggling Creative Offline with Mode #{mode}")
      network_simulator = get_network_simulator(device)
      dc = CheckDevice.new(device, REDIS_CLIENT)
      if mode == "Online"
        network_simulator.apply_online_mode
        dc.check_device_internet_during_session("USB", "Mid Session", session_id)
      else
        network_simulator.apply_offline_mode
        dc.check_device_internet_during_session("Mobile Data", "Mid Session", session_id)
      end
    end

    def update_accessibility_settings(device, params)
      product = params['product']
      session_id = product == "live_testing" ? params["live_session_id"] : params["app_live_session_id"]
      _send_eds_update_for_accessibility(product, session_id, params)
      accessibility_settings_helper = Secure::AccessibilitySettingsHelper.new(device, session_id, params["product"])
      BrowserStack.logger.info("Modes received #{params['mode']} for the device #{device} for accessibility setting #{params['ios_setting']}")
      state = accessibility_settings_helper.update_accessibility_settings(params['mode'])
      BrowserStack::Zombie.push_logs("accessibility-change-success", nil, { "data" => params[:mode], "device" => device, "session_id" => session_id, "user_os" => product })
      if params["app_live_session_id"].eql?("qa-no-session") && state[:pusher_message].include?("Failed")
        raise "UpdateA11ySettingsException"
      elsif params["app_live_session_id"] != "qa-no-session"
        product == "live_testing" ? Utils.notify_pusher_for_live(state[:pusher_message], params, device) : Utils.notify_pusher(state[:pusher_message], params, device)
      end
    rescue => e
      BrowserStack::Zombie.push_logs("accessibility-change-failure", nil, { "data" => params[:mode], "device" => device, "session_id" => session_id, "user_os" => product })
      BrowserStack.logger.error("Exception while updating accessibility settings for device: #{device}: #{e.message} and stacktrace #{e.backtrace}")
      if params["app_live_session_id"].eql? "qa-no-session"
        raise e
      else
        product == "live_testing" ? Utils.notify_pusher_for_live("AccessibilitySettingsException", params, device) : Utils.notify_pusher("AccessibilitySettingsException", params, device)
      end
    end

    def _send_eds_update_for_accessibility(product, session_id, params)
      unless params["app_live_session_id"].eql? "qa-no-session"
        eds_table = product == "live_testing" ? EdsConstants::LIVE_TEST_SESSIONS : EdsConstants::APP_LIVE_TEST_SESSIONS
        Utils.send_to_eds({
          session_id: session_id,
          product: {
            accessibility_settings: true
          }
        }, eds_table, true)
      end
    end

    def date_time_setting(device, params) # rubocop:todo Metrics/MethodLength
      product = params['product']
      if product == "app_live_testing"
        type = params["date_time"]["type"]
        product_params = {}
        case type
        when "automatic", "time"
          product_params[:ios_date_time_changed] = true
        when "date"
          product_params[:ios_date_changed] = true
        end
        Utils.send_to_eds({ session_id: params["app_live_session_id"], product: product_params },
                          EdsConstants::APP_LIVE_TEST_SESSIONS, true)
      end
      result = false
      error_code = nil
      send_to_pusher = %w[app_live_testing live_testing].include? product

      begin
        date_time_helper = DateTimeHelper.new(device, params)
        settings = params["date_time"]

        case settings["type"]
        when "automatic"
          enabled = settings["automatic"].to_s == "true"
          result = date_time_helper.automatic(enabled, send_to_pusher: send_to_pusher)
        when "time"
          result = date_time_helper.time(settings["time"], send_to_pusher: send_to_pusher)
        when "date"
          result, error_code = date_time_helper.change_date(settings["date"], send_to_pusher: send_to_pusher)
        when "12HourTime"
          time_format = settings["value"].to_s == "true" ? TWELVE_HOUR_TIME_FORMAT : TWENTY_FOUR_HOUR_TIME_FORMAT
          result = date_time_helper.time_format(time_format, send_to_pusher: send_to_pusher)
        end
      rescue => e
        DateTimeHelper.log "Error while processing date time setting: #{e} #{e.backtrace.join("\n")}"
        if product == "app_live_testing"
          send_to_pusher && Utils.notify_pusher("DateTimeFailure", params, device)
        else
          send_to_pusher && Utils.notify_pusher_for_live("DateTimeFailure", params, device)
        end
      ensure
        raise DateTimeException.new "Error in setting date time", error_code, 500 unless result
      end
    end

    def change_device_name(device_id, params)
      BrowserStack.logger.info("Device name called with params:#{params} for device #{device_id}")
      unless params["app_live_session_id"].eql? "qa-no-session"
        Utils.send_to_eds({
          session_id: params["app_live_session_id"],
          product: {
            ios_device_name_changed: true
          }
        }, EdsConstants::APP_LIVE_TEST_SESSIONS, true)
      end

      IdeviceUtils.idevice_name(device_id, name: params["new_device_name"])
      BrowserStack.logger.info("Successfully changed device name for device #{device_id} with device name #{params['new_device_name']}")
      Utils.notify_pusher("ChangeDeviceNameSuccess", params, device_id) unless params["app_live_session_id"].eql? "qa-no-session"
    rescue => e
      BrowserStack.logger.error("Change device name failed with #{e.message} \n#{e.backtrace.join("\n")}")
      BrowserStack::Zombie.push_logs("change-device-name-failure", e.message, { "device" => device_id, "session_id" => params["app_live_session_id"] })
      Utils.notify_pusher("ChangeDeviceNameException", params, device_id) unless params["app_live_session_id"].eql? "qa-no-session"
      raise e
    end

    def relaunch_user_app(device_config, device)
      BrowserStack.logger.info("Relaunching user installed app")
      locale = Utils.read_first_line("/tmp/#{device}_current_locale") if File.exist?("/tmp/#{device}_current_locale")
      locale = "en-US,en-US" if locale.nil? || locale.empty?
      language, region = locale.split(',')
      all_installed_apps = begin
        IdeviceUtils.list_user_installed_apps(device)
      rescue
        []
      end
      if !all_installed_apps.empty? && !language.nil? && !region.nil?
        BrowserStack.logger.info("User installed apps: #{all_installed_apps}")
        wda_client = WdaClient.new(device_config["webdriver_port"].to_i)
        wda_client.launch_apps_with_locale([all_installed_apps[-1]], language, region)
      end
    end

    def get_ios_states(device)
      response = {
        dark_mode: false,
        assistive_touch_mode: false,
        offline_mode: false,
        device_passcode: false,
        xss_toggle: true,
        device_name: IdeviceUtils.idevice_name(device),
        date_time: {
          automatic: false,
          current_date: DateTimeHelper.new(device, {}).current_date_on_device_hash,
          initial_date: DateTimeHelper.new(device, {}).initial_date_on_device_hash
        },
        low_power_mode: false,
        safari_settings: {
          prevent_cross_site_tracking: true,
          block_popups: true
        }
      }
      device_state = DeviceState.new(device)
      response[:dark_mode] = true if device_state.dark_mode_file_present?
      response[:assistive_touch_mode] = true if device_state.assistive_touch_opened_file_present?
      response[:offline_mode] = true if device_state.offline_mode_file_present?
      response[:device_passcode] = true if device_state.passcode_file_present?
      response[:xss_toggle] = false if device_state.prevent_cross_site_tracking_disabled_file_present?
      response[:low_power_mode] = true if device_state.low_power_mode_file_present?
      response[:date_time][:automatic] = true if device_state.date_time_automatic_file_present?
      response[:device_name] = IdeviceUtils.idevice_name(device)
      safari_settings_state = SafariSettings.get_current_state(device_state)
      response[:safari_settings][:prevent_cross_site_tracking] = safari_settings_state[:prevent_cross_site_tracking] == "enable"
      response[:safari_settings][:block_popups] = safari_settings_state[:block_popups] == "enable"
      response[:card_network] = File.read("#{STATE_FILES_DIR}/apple_pay_data_#{device}") if device_state.apple_pay_data_file_present?
      response
    end

    def get_accessibility_states(device, params)
      device = Secure::AccessibilitySettingsHelper.new(device, params["live_session_id"], params["product"])
      device.load_current_state
    end

    def archive_and_truncate_appium_logs(port)
      Utils.truncate_log_file(appium_log_path(port))
    rescue => e
      BrowserStack.logger.error "Cannot archive and truncate log file: #{e.message}. Just truncating log file for now"
      File.truncate(appium_log_path(port), 0)
    end

    def appium_log_path(port)
      File.join(@@settings["logging_root"], "appium_#{port}.log")
    end

    def session_appium_logs(session_id)
      "/tmp/appium_processed_#{session_id}.log"
    end

    def get_screenshot_uncached(device, orientation, use_wda=false, cheap_stream='false')
      file_name = @@screenshot_dir + "/snapshot_tmp_#{device}.png"
      file_name.gsub!("png", "jpeg") if cheap_stream == "true"
      current_device_config = device_configuration_check(device)
      device_version = current_device_config['device_version'].to_i
      if use_wda
        current_device_config = device_configuration_check(device)
        wda_port = current_device_config["webdriver_port"].to_i
        ScreenshotsUtil.capture_screenshot_via_wda(file_name, device, wda_port)
      elsif device_version >= 17
        PyMobileDevice::Developer.screenshot(device, file_name)
      else
        IdeviceUtils.screenshot(device, file_name)
      end

      img = Image.read(file_name).first
      width = img.columns
      height = img.rows
      is_portrait = height > width

      if is_portrait
        if orientation.to_s == "landscape"
          img.rotate!(-90)
          img.write(file_name)
        end

        if orientation.to_s == "landscape_clockwise"
          img.rotate!(90)
          img.write(file_name)
        end
      end

      # Compress images for faster streaming
      system("#{IMAGEMAGICK_CONVERT} #{file_name} -quality 20 #{file_name}") if cheap_stream == "true"

      data = File.read(file_name)
      File.delete(file_name)
      data
    end

    def get_screenshot(device, session_id, orientation = "portrait", upload_as = nil, key_id = nil, secret_key = nil, use_wda = false, check_black_screenshot = false) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength, Metrics/PerceivedComplexity, Metrics/CyclomaticComplexity
      retries = 2
      request_for_debug_screenshot = upload_as && !upload_as.include?("screenshot_on_failure")
      start_time = Time.now
      timeout = 3
      use_cache = false
      touch_file = @@screenshot_dir + "/screenshot_touch_file_#{device}"
      current_device_config = device_configuration_check(device)
      device_version = current_device_config['device_version'].to_i
      use_wda ||= device_version >= 17
      temp_screenshot_suffix = use_wda ? @@temp_screenshot_suffix_jpeg : @@temp_screenshot_suffix_png
      file_name = @@screenshot_dir + "/#{device}_#{SecureRandom.hex(4)}.#{temp_screenshot_suffix}" # If you change the formatting please make sure, that it will be picked up by the cleaner in image_converter_process.rb
      screenshot_lock_file = @@screenshot_dir + "/#{device}_#{SecureRandom.hex(4)}_screenshot.lock"
      instrumentation_file = "#{@@screenshot_dir}/#{@@settings['screenshot_instrumentation_prefix']}#{session_id}"

      if request_for_debug_screenshot
        BrowserStack.logger.info "updating instrumentation file #{instrumentation_file} called for #{session_id}"
        Utils.update_screenshot_instrumentation_with_lock(instrumentation_file, nil, "capture", {})
        BrowserStack.logger.info "updating instrumentation file done #{instrumentation_file} called for #{session_id}"
      end

      if File.exist?(touch_file) && Time.now - File.mtime(touch_file) < 1
        touch_file_contents = File.read(touch_file)
        file_name_touched, screenshot_lock_file_touched = touch_file_contents.split(":::")

        if File.exist?(screenshot_lock_file_touched)
          use_cache = true
          screenshot_lock_file = screenshot_lock_file_touched
          file_name = file_name_touched
        end
      end

      # If video is recording, then try using the latest screenshot present in the video recording workspace, instead of taking a new one for iOS 12 and above.
      video_recording_workspace = @@settings['video_recording_workspace'] + "#{device}/#{session_id}/"

      unless use_cache
        begin
          Utils.write_to_file(touch_file, "#{file_name}:::#{screenshot_lock_file}")
          Utils.write_to_file(screenshot_lock_file, "pending_capture")

          Timeout.timeout(timeout) do
            start_at = Time.now

            if use_wda
              wda_port = current_device_config["webdriver_port"].to_i
              ScreenshotsUtil.capture_screenshot_via_wda(file_name, device, wda_port)
            else
              BrowserStack.logger.info "Running idevice screenshot #{device} => #{file_name}"
              IdeviceUtils.screenshot(device, file_name)
            end

            File.delete(screenshot_lock_file)
            BrowserStack.logger.info "Screenshot captured in #{Time.now - start_at} seconds."
          end

        rescue Timeout::Error => e
          File.delete(screenshot_lock_file) if File.readable?(screenshot_lock_file)
          BrowserStack.logger.error("Timeout in /snapshot_hub for session #{session_id} #{device} with use_wda: #{use_wda}. Timeout expired of #{timeout} seconds)")
          use_wda = false
          retries -= 1
          Utils.update_screenshot_states_with_lock(instrumentation_file, "capture", "failed", "execution_timeout") if request_for_debug_screenshot && retries > 0
          retry if retries > 0
          if request_for_debug_screenshot
            time_taken = (Time.now - start_time).round(2)
            Utils.update_screenshot_instrumentation_with_lock(instrumentation_file, "capture", "failed", { stage_time_taken: time_taken })
          end
          raise "Timeout error for #{session_id} Device:#{device}: #{e.message}"

        rescue => e
          File.delete(screenshot_lock_file) if File.readable?(screenshot_lock_file)
          BrowserStack.logger.error("Exception in /snapshot_hub for session #{session_id} #{device} with use_wda: #{use_wda}: #{e.message}")
          use_wda = false
          retries -= 1
          if request_for_debug_screenshot && retries > 0
            bucket = Utils.get_error_bucket(e.message)
            Utils.update_screenshot_states_with_lock(instrumentation_file, "capture", "failed", bucket)
          end
          retry if retries > 0
          if request_for_debug_screenshot
            time_taken = (Time.now - start_time).round(2)
            Utils.update_screenshot_instrumentation_with_lock(instrumentation_file, "capture", "failed", { stage_time_taken: time_taken })
          end
          raise "Exception in capture for #{session_id} Device:#{device}: #{e.message}"
        end
      end

      if upload_as.nil? || upload_as.empty?
        return File.read(file_name)
      elsif use_wda
        if request_for_debug_screenshot
          time_taken = (Time.now - start_time).round(2)
          Utils.update_screenshot_instrumentation_with_lock(instrumentation_file, "capture", "upload", { stage_time_taken: time_taken })
        end
        upload_request = UploadRequest.new(@@automate_screenshots_folder_upload)
        upload_request.start(file_name, upload_as, screenshot_lock_file, session_id, start_time, key_id, secret_key, true, check_black_screenshot) # since wda screenshots are already rotated we move it directly to upload
      else
        if request_for_debug_screenshot
          time_taken = (Time.now - start_time).round(2)
          Utils.update_screenshot_instrumentation_with_lock(instrumentation_file, "capture", "convert", { stage_time_taken: time_taken })
        end
        ConvertRequest.new(@@automate_screenshots_folder_convert).start(device, file_name, upload_as, screenshot_lock_file, session_id, start_time, orientation, key_id, secret_key, use_wda, check_black_screenshot)
      end

      file_name
    end

    # add xpath attribute to every node in xml tree
    #
    # @param xmltree [String]
    # @return [Nokogiri::XML::Node] new xml tree with xpath attribute
    def add_xpath_to_xml_tree(xmltree)
      xmlDoc = Nokogiri::XML(xmltree)
      root = xmlDoc.root
      add_xpath_to_xml_node(root)
    rescue => e
      BrowserStack.logger.error("Unable to parse xml tree to add xpath", e)
    end

    # add xpath attribute to the node and its children recursively
    #
    # @param node [Nokogiri::XML::Node]
    # @return [Nokogiri::XML::Node] xml node having xpath attribute
    def add_xpath_to_xml_node(node)
      node.set_attribute('xpath', node.path)
      node.children.each do |child_node|
        add_xpath_to_xml_node(child_node)
      end
      node
    end

    def get_snapshot_details(device, params)
      timeout = (params["timeout"] || 30).to_i
      delta = (params["delta"] || 3).to_i
      session_params = JSON.parse(File.read(session_file(device)))
      wda = WdaClient.new(session_params["iproxyPort"])
      response = wda.snapshot_details(timeout, timeout + delta)
      response_value = response["value"]

      screenshot = response_value["screenshot"].gsub(/\r\n/, "")
      xmltree = response_value["xmltree"].gsub(/\n\t/, " ").gsub(/>\s*</, "><")

      xmltree_with_xpath = ""

      # Add xpath-attribute to the xml tree
      xpath_addition_stats = Benchmark.measure do
        xmltree_with_xpath.concat(add_xpath_to_xml_tree(xmltree).to_s)
      end
      BrowserStack.logger.info("Adding xpath to xml source tree took #{xpath_addition_stats.real} seconds")

      {
        screenshot: screenshot,
        xmltree: xmltree_with_xpath
      }
    end

    def launch_user_app_settings(device, bundle_id, app_live_settings_v3="false")
      start_at = Time.now
      current_device_config = device_configuration_check(device)
      device_version = current_device_config['device_version'].to_f
      return { status: 400, message: "Currently not supported for #{device_version} devices" } if device_version < 12.0

      user_apps = IdeviceUtils.list_user_installed_apps_details(device)
      installed_app = user_apps.select { |apps| apps[:bundle_id] == bundle_id }
      return { status: 400, message: "Invalid bundle_id, app not installed" } if installed_app.empty?

      display_name = installed_app.first[:display_name]
      return { status: 400, message: "Incorrect display name for app" } if display_name.to_s.empty?

      return { status: 403, message: "Forbidden, cannot open settings for internal apps" } if IOS_SETTINGS_INTERNAL_CELL_NAMES.include? display_name

      wda_port = current_device_config["webdriver_port"].to_i
      wda_client = WdaClient.new(wda_port)
      launch_url = nil
      # launch_url = "App-prefs:#{bundle_id}" if device_version >= 14.4
      resp = wda_client.open_settings(launch_url, display_name, app_live_settings_v3)
      return { status: 403, message: "WDA response: #{resp['value']['error']}" } if !resp['value'].nil? && resp['value']['status'] == 'error'

      BrowserStack.logger.info "Succesfully Launched settings for user app in #{Time.now - start_at} seconds."
      { status: 200, message: "OK" }
    end

    def sim_setup(device, params)
      Utils.mark_event_start('fire_cmd.sim_setup_time', params[:event_hash])
      genre = @is_app_testing ? GENRE_APP_AUTOMATE : GENRE_AUTOMATE
      sim_helper = DeviceSIMHelper.new(device, params['automate_session_id'], genre)
      result = sim_helper.session_setup
      unless result
        BrowserStack.logger.info("[FIRECMD] SIM Setup failed for session: #{params['automate_session_id']} ")
        params[:feature_usage]["public_sim"] = { success: false }
      end
      Utils.mark_event_end('fire_cmd.sim_setup_time', params[:event_hash])
      raise FireCMDException.new("SIM setup failed", BROWSERSTACK_ERROR_STRING, "sim_setup_failed") unless result
    end

    def apple_pay_setup(device, params)
      BrowserStack.logger.info("[FIRECMD] Apple Pay Setup started for session: #{params['automate_session_id']} ")
      Utils.mark_event_start('fire_cmd.apple_pay_setup_time', params[:event_hash])

      Secure::ApplePay.setup(device, params)

      params[:feature_usage]["enableApplePay"] = { success: true }
      BrowserStack.logger.info("[FIRECMD] Apple Pay Setup done for session: #{params['automate_session_id']} ")
    rescue => e
      params[:feature_usage]["enableApplePay"] = { success: false, exception: e.message[0, 100] }
      BrowserStack.logger.info("[FIRECMD] Error is Setting up Apple pay for session: #{params['automate_session_id']}, error: #{e.message}")
      raise FireCMDException.new("Internal Server Error", BROWSERSTACK_ERROR_STRING, "apple_pay_setup_failed")
    ensure
      Utils.mark_event_end('fire_cmd.apple_pay_setup_time', params[:event_hash])
    end

    def enable_passcode(device, params)
      Utils.mark_event_start('fire_cmd.set_passcode_time', params[:event_hash])
      passcode_helper = Secure::Passcode.new(device, params['automate_session_id'])
      begin
        passcode_helper.passcode_settings(REDIS_CLIENT)
        raise "Failed to set passcode" unless passcode_helper.set_passcode

        BrowserStack.logger.info("Device Passcode Enabled for session = #{params['automate_session_id']}")
        params[:feature_usage]["enable_passcode"] = { success: true }
      rescue => e
        BrowserStack.logger.error("Failed to enable Device Passcode for session =  #{params['automate_session_id']}")
        params[:feature_usage]["enable_passcode"] = { success: false, exception: e.message[0, 100] }
        BrowserStack::Zombie.push_logs("set_passcode_failed", e.message, { "device" => device, "session_id" => params["automate_session_id"] })
        raise FireCMDException.new(e.message[0, 100], BROWSERSTACK_ERROR_STRING, "set_passcode_failed")
      ensure
        Utils.mark_event_end('fire_cmd.set_passcode_time', params[:event_hash])
      end
    end

    def install_custom_certificate(device, params)
      Utils.mark_event_start('fire_cmd.install_custom_certificate_time', params[:event_hash])

      begin
        custom_certificate_details = parse_certificate_details(params['customCertificateFile'])
        raise "Invalid custom certificate details" if custom_certificate_details.empty?

        BrowserStack.logger.info("[install_custom_certificate] custom certificate details = #{custom_certificate_details}")
        filetype = custom_certificate_details['filetype']
        BrowserStack.logger.info("[install_custom_certificate] Certificate filetype: #{filetype}")

        if ['certificates_cer_ios', 'certificates_cer_android'].include?(filetype)
          install_custom_ca_certificate(device, params, custom_certificate_details)
        else
          install_pfx_certificate(device, params)
        end
      rescue => e
        BrowserStack.logger.error("Failed to process custom certificate with error: #{e.message} for session #{params['app_automate_session_id']}")
        params[:feature_usage]["install_custom_certificate"] = { success: false, exception: e.message[0, 100] }

        if e.message == "Invalid custom certificate details"
          raise FireCMDException.new(e.message, USER_ERROR_STRING, "certificate_install_invalid_details")
        else
          raise e
        end
      ensure
        Utils.mark_event_end('fire_cmd.install_custom_certificate_time', params[:event_hash])
      end
    end

    def parse_certificate_details(certificate_file_json)
      custom_certificate_details = JSON.parse(certificate_file_json)

      # Check if the result is a string that looks like JSON (double-encoded)
      begin
        if custom_certificate_details.is_a?(String) && custom_certificate_details.start_with?('{')
          BrowserStack.logger.info("[install_custom_certificate] Detected double-encoded JSON, parsing again")
          custom_certificate_details = JSON.parse(custom_certificate_details)
        end
      rescue => e
        BrowserStack.logger.error("[install_custom_certificate] Error parsing double-encoded JSON: #{e.message}")
        # Return the original parsed JSON if the second parse fails
      end

      custom_certificate_details
    end

    def install_pfx_certificate(device, params)
      BrowserStack.logger.info("[install_custom_certificate] Detected PFX certificate, proceeding with install_pfx_certificate")
      begin
        custom_certificate_details = JSON.parse(params['customCertificateFile'])
        raise "Invalid custom certificate details" if custom_certificate_details.empty?

        custom_certificate_helper = CustomCertificate.new(device, params["automate_session_id"], custom_certificate_details, params["product"])
        custom_certificate_helper.install_pfx_certificate
        params[:feature_usage]["install_pfx_certificate"] = { success: true }
      rescue => e
        BrowserStack.logger.error("Failed to install PFX certificate with error: #{e.message} for session #{params['app_automate_session_id']}")
        params[:feature_usage]["install_pfx_certificate"] = { success: false, exception: e.message[0, 100] }
        # The password for the certificate \stomcertificate.pfx\u201d is incorrect
        if e.message.match?(INCORRECT_CERTIFICATE_PASSWORD_REGEX)
          raise FireCMDException.new(e.message[0, 100], USER_ERROR_STRING, "certificate_install_invalid_password")
        else
          raise FireCMDException.new(e.message[0, 100], BROWSERSTACK_ERROR_STRING, "browserstack_certificate_install_failure")
        end
      end
    end

    def install_custom_ca_certificate(device, params, custom_ca_cert_file)
      Utils.mark_event_start('fire_cmd.install_custom_ca_certificates_time', params[:event_hash])
      cert_array = []
      if custom_ca_cert_file.is_a?(Hash)
        # Format the hash into the expected structure
        cert = {
          media_s3_url: custom_ca_cert_file['s3_url'] || custom_ca_cert_file['media_s3_url'],
          filename: custom_ca_cert_file['filename'],
          filetype: custom_ca_cert_file['filetype'] || 'certificates_cer_android'
        }
        cert_array << cert
      else
        raise "Unexpected certificate format: #{custom_ca_cert_file.class}"
      end

      BrowserStack.logger.info("[install_custom_ca_certificate] Prepared certificate array: #{cert_array}")
      custom_certificate_helper = CustomCertificate.new(device, params["automate_session_id"] || params["app_automate_session_id"], cert_array, params["product"])
      custom_certificate_helper.install_all_custom_ca_certs
      params[:feature_usage]["install_custom_ca_certificates"] = { success: true }
      BrowserStack.logger.info("[install_custom_ca_certificate] Successfully installed custom CA certificates for session #{params['app_automate_session_id']}")
    rescue => e
      BrowserStack.logger.error("Failed to install custom CA certificates with error: #{e.message} for session #{params['app_automate_session_id']}")
      params[:feature_usage]["install_custom_ca_certificates"] = { success: false, exception: e.message[0, 100] }
      raise FireCMDException.new(e.message[0, 100], BROWSERSTACK_ERROR_STRING, "browserstack_ca_certificate_install_failure")
    ensure
      Utils.mark_event_end('fire_cmd.install_custom_ca_certificates_time', params[:event_hash])
    end

    def update_device_settings(device, params)
      Utils.mark_event_start('fire_cmd.update_ios_device_settings_time', params[:event_hash])
      update_settings_params = JSON.parse(params['updateIosDeviceSettings'].gsub('=>', ':'))
      update_params = update_settings_params.keys
      BrowserStack.logger.info("[updateIosDeviceSettings] updating device settings with params = #{update_settings_params}")
      begin
        params[:feature_usage]["updateIosDeviceSettings"] = []
        update_params.each do |setting|
          case setting
          when "darkMode"
            enable_dark_mode(device, params) if update_settings_params[setting].to_s == "true"
          end
        end
      rescue => e
        BrowserStack.logger.error("Error Updating Device settings = #{params['automate_session_id']} #{e.message}")
        params[:feature_usage]["updateIosDeviceSettings"] << { "darkMode": { success: "false", exception: e.message[0, 100] } }
        BrowserStack::Zombie.push_logs("update_ios_device_settings_failed", e.message, { "device" => device, "session_id" => params["automate_session_id"] })
        raise FireCMDException.new(e.message[0, 100], BROWSERSTACK_ERROR_STRING, "update_ios_device_settings_failed")
      ensure
        Utils.mark_event_end('fire_cmd.update_ios_device_settings_time', params[:event_hash])
      end
    end

    def enable_dark_mode(device, params)
      BrowserStack.logger.info("[updateIosDeviceSettings] enabling dark mode")
      response = BrowserStack::SwitchMode.new(device, params['automate_session_id']).change_appearance_to(BrowserStack::SwitchMode::DARK_MODE)
      BrowserStack.logger.info("Dark mode enabled = #{response}")
      params[:feature_usage]["updateIosDeviceSettings"] << { "darkMode": { success: response } }
    end

    def session_file(device)
      "#{@@settings['state_files_dir']}/#{device}_session"
    end

    def session_file_contents(device)
      JSON.parse(File.read(session_file(device)))
    rescue Errno::ENOENT # File not found
      ""
    end

    def reboot_file(device)
      "/tmp/reboot_#{device}"
    end

    def session_start_file(device)
      "#{@@settings['state_files_dir']}/#{device}_session_start_indicator"
    end

    def cleanup_started_file(device)
      "#{@@settings['state_files_dir']}/cleanupstarted_#{device}"
    end

    # @deprecated Use DeviceState for this file
    def cleanup_done_file(device)
      "#{@@settings['state_files_dir']}/cleanupdone_#{device}"
    end

    def video_workspace_for_session(device, session_id)
      "#{@@settings['video_recording_workspace']}#{device}/#{session_id}"
    end

    def video_sync_data_file(device, session_id)
      "#{video_workspace_for_session(device, session_id)}/video_sync_data"
    end

    def video_test_launch_file(device)
      "/tmp/video_test_launch_#{device}"
    end

    def setup_requested_file(device)
      "#{@@settings['state_files_dir']}/setup_requested_#{device}"
    end

    def cleanup_requested_file(device)
      "#{@@settings['state_files_dir']}/cleanup_requested_#{device}"
    end

    def webdriver_agent_pid_file(device)
      "/tmp/webdriveragent_#{device}"
    end

    def request_setup(device)
      BrowserStack::Zombie.push_logs("setup_requested", device)
      BrowserStack.logger.warn("Requesting a setup for device '#{device}'")
      FileUtils.touch(setup_requested_file(device))
    end

    def setup_requested?(device)
      File.exist?(setup_requested_file(device))
    end

    def request_cleanup(device)
      BrowserStack.logger.info("Requesting a cleanup for device '#{device}'")
      FileUtils.touch(cleanup_requested_file(device))
    end

    def device_setup_completed?(device)
      device_config = device_configuration_check(device)
      return true if device_config["device_name"].start_with?("AppleTV")

      webkit_port = device_config["debugger_port"]
      File.exist?("/Library/LaunchDaemons/ios_webkit_debug_proxy_#{webkit_port}.plist")
    end

    def get_orientation(device)
      current_device = device_configuration_check(device)
      wda_port = current_device["webdriver_port"].to_i
      begin
        BrowserStack.logger.info "Getting orientation for device #{device} on wda_port #{wda_port}"
        WdaClient.new(wda_port).get_orientation
        BrowserStack.logger.info "Getting orientation successful for device #{device}"
      rescue => e
        is_wda_running = WdaClient.new(wda_port).running?
        BrowserStack.logger.error "Failed getting orientation for device #{device} on wda_port #{wda_port}, wda running : #{is_wda_running} with error #{e.message} and stacktrace #{e.backtrace}"
      end
    end

    def set_orientation(device, orientation, params) # rubocop:todo Metrics/AbcSize
      current_device = device_configuration_check(device)
      wda_port = current_device["webdriver_port"].to_i
      device_version = current_device['device_version'].to_f
      begin
        BrowserStack.logger.info "Setting orientation to #{orientation} for device #{device} on wda_port #{wda_port}"
        WdaClient.new(wda_port).set_orientation(orientation.to_s.downcase)
        BrowserStack.logger.info "Setting orientation successful for device #{device}"
        params[:feature_usage]["orientation"] = { success: true }
      rescue => e
        is_wda_running = WdaClient.new(wda_port).running?
        BrowserStack.logger.error "Failed setting orientation to #{orientation} for device #{device} on wda_port #{wda_port}, wda running : #{is_wda_running} with error #{e.message} and stacktrace #{e.backtrace}"
        if device_version >= 18.0 && e.message.include?("waitForQuiescenceIncludingAni")
          BrowserStack.logger.info "Setting orientation successful for device #{device}"
          params[:feature_usage]["orientation"] = { success: true }
        else
          BrowserStack::Zombie.push_logs("ios-njb-orientation-failed", params[:genre], { "session_id" => params['automate_session_id'], "device" => device, "url" => is_wda_running.to_s, "data" => e.message, "error_stacktrace" => e.backtrace })
          influxdb_client.event(device, 'ios-njb-orientation-failed', component: 'server', subcomponent: 'trust-client-enterprise-app', is_error: true)
          params[:feature_usage]["orientation"] = { success: false, exception: e.message[0, 100] }
        end
      end
    end

    def is_sim_popup_present?(device, selenium_port)
      BrowserStack.logger.info("Checking the Appium logs for Foreground Activity for device #{device} on port #{selenium_port}")
      test_worked = `tail -200 /var/log/browserstack/appium_#{selenium_port}.log | grep -i 'Continuing to run tests in the background with task ID'`
      test_worked.empty?
    end

    def remove_device(device)
      load_config # always load
      config = JSON.parse(@@device_info)
      config['devices'].delete(device)
      Utils.write_config_with_lock(@@device_info_file, config.to_json)
      device_state = DeviceState.new(device)

      begin
        dirs_to_check = [STATE_FILES_DIR, '/var/log/browserstack', @@settings['config_root'], "#{@@settings['config_root']}/custom_device_configs/", "/tmp/"]
        dirs_to_check.each do |d|
          Dir.glob("/#{d}/*").select { |device_file| /#{device}/.match(device_file) }.each do |file|
            FileUtils.rm_rf(file)
          end
        end
      rescue => e
        BrowserStack.logger.info("Unable to remove file: #{e.message}")
      end
    end

    # This is not generic as of now, works only for the alert which comes when opening a popup. It says "This site is attempting to open a pop-up window.."
    def accept_alert(device, params, allow_popup_tap_coordinates)
      begin
        session_file = JSON.parse(File.read(session_file(device)))
        automate_session_id = session_file["automate_session_id"]
      rescue => e
        BrowserStack.logger.info("Failed to read session file for /accept_alert #{e.message} #{e.backtrace.join("\n")}")
        return
      end

      begin
        current_device = device_configuration_check(device)
        return unless current_device

        tap_coordinates = allow_popup_tap_coordinates[current_device["device_name"]]

        unless tap_coordinates
          BrowserStack.logger.info("Tap coordinates not configured for #{current_device.inspect}, aborting.")
          return
        end

        wda = WdaClient.new(current_device["webdriver_port"])

        # Hoping that system popup will come in some time.
        # If we don't wait (or if the popup doesn't show up after 500 ms), we will click on the user's website :-)
        sleep 0.5

        # Tap on "Allow" to accept the popup, the coordinates will differ depending on the device.
        wda.tap(tap_coordinates["x"], tap_coordinates["y"])

        BrowserStack.logger.info("Successfully 'Allowed' popup for session #{automate_session_id}")
      rescue => e
        BrowserStack.logger.info("Exception while accepting alert #{e.message} #{e.backtrace.join("\n")}for session #{automate_session_id}")
      end
    end

    def start_xctest_session(device, params) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength, Metrics/PerceivedComplexity, Metrics/CyclomaticComplexity
      raise FireCMDException.new("Cannot start session. #{device} in use by another user or cleanup.", BROWSERSTACK_ERROR_STRING, "device_already_in_use") if device_in_use?(device, params)

      current_device = device_configuration_check(device)
      params['wda_port'] = current_device['webdriver_port']
      session_info_test_framework = get_session_info_test_framework(params['test_framework'])
      params[APP_PATCH_TYPE] = 'biometrics' if (params[:biometricFrameworkSession]).to_s.downcase == "true"
      params[APP_PATCH_TYPE] = 'camera' if (params[:enableCameraImageInjection]).to_s.downcase == "true"
      params[:sensor_mocking] = true if params[:enableCameraImageInjection].to_s.downcase == "true" && ALLOWED_APP_PATCH_TYPES.include?(params[APP_PATCH_TYPE])

      Utils.write_to_file_with_lock(session_file(device), params.to_json)
      write_session_info(device, params, session_info_test_framework)

      device_state = DeviceState.new(device)
      check_dedicated_device_session(device_state, params, device)
      check_and_set_cleanup_policy(device, params, device_state)

      remove_proxy_setup(device, params) if !params[:extraAccess].nil? && params[:extraAccess].include?("remove_proxy")

      check_and_enable_wifi(device_state, params)
      CustomMDMManager.new(device, BrowserStack.logger).backup_unlock_token
      Thread.bs_run { start_battery_metrics_publisher(device, params["session_id"]) }

      # Stop video recording services which are running
      kill_video_recording_processes(device)
      # bs_run creates a new thread & copies logging params to it
      Thread.bs_run { log_device_internet_connection(device, params) }
      feature_usage = init_feature_usage(params)
      params[:feature_usage] = feature_usage
      @is_app_testing = params[:genre].to_s.eql?('app_automate')
      params["app_automate_custom_params"] = parse_app_automate_custom_params(params)
      params["subregion_app_caching_enabled"] =
        params["app_automate_custom_params"]["subregion_app_caching_proxy_enabled"].to_s == "true"
      begin
        if (["xcuitest", "maestro"].include? params['test_framework']) && params['app_s3_url']
          app_details = { url: params['app_s3_url'], id: params['app_details_bundle_id'], timeout: params['app_download_timeout'], bundle_id: params['app_details_bundle_id'], display_name: params['display_name'] }
          params.merge!({ app_type: "main" })
          if params["mdm_enterprise_app_install"]
            perform_mdm_enterprise_app_install(params['automate_session_id'], device, app_details, params)
            downloaded_app_path = download_and_install_app(device, app_details, true, params['automate_session_id'], params['test_framework'], params, true)
          else
            downloaded_app_path = download_and_install_app(device, app_details, true, params['automate_session_id'], params['test_framework'], params)
          end
          BrowserStack.logger.info "Downloaded app path: #{downloaded_app_path}"
          params["downloaded_app_path"] = downloaded_app_path
          push_to_cls(params, 'app_install_done', '', { "device_id" => device, "data" => params })
        end

        params['xctests'] = []
        if params['test_framework'] == "maestro"
          test_details = { url: params['test_suite_s3_url'], timeout: params['test_suite_download_timeout'], filename: params['test_suite_name'] }
          downloaded_test_app_path = download_maestro_test_suite(device, test_details, params['automate_session_id'], params)
          BrowserStack.logger.info "Downloaded test app path: #{downloaded_test_app_path}"
          params["downloaded_test_app_path"] = downloaded_test_app_path
          test_params_maestro = {}
          begin
            test_params_maestro = JSON.parse(params['test_params']) if params['test_params']
          rescue => e
            BrowserStack.logger.error("Exception raised while parsing test params:\n #{e.class.name} #{e.message}: #{e.backtrace.join("\n")}")
          end
          params['execute'] = test_params_maestro["execute"]
          push_to_cls(params, 'test_app_install_done', '', { "device_id" => device, "data" => params })
        else
          test_details = { url: params['test_suite_s3_url'], id: params['test_suite_bundle_id'], timeout: params['test_suite_download_timeout'] }
          params.merge!({ app_type: "test" })
          downloaded_test_app_path = download_and_install_app(device, test_details, false, params['automate_session_id'], params['test_framework'], params)
          BrowserStack.logger.info "Downloaded test app path: #{downloaded_test_app_path}"
          params["downloaded_test_app_path"] = downloaded_test_app_path
          push_to_cls(params, 'test_app_install_done', '', { "device_id" => device, "data" => params })
        end
      rescue FireCMDException => e
        push_to_cls(params, "#{params[:app_type]}_app_install_failed", e.message.to_s, { "device_id" => device, "data" => "" })
        BrowserStack.logger.error("[FIRECMD][#{params['test_framework'].upcase}] App installation failed: message: #{e.message}\n backtrace: #{e.backtrace.join("\n")}")
        return { error: e.message.to_s, kind: "#{params[:app_type]}_app_download_install_failure", type: e.type.to_s, meta_data: e.meta_data }
      rescue => e
        push_to_cls(params, "#{params[:app_type]}_app_install_failed", e.message.to_s, { "device_id" => device, "data" => "" })
        BrowserStack.logger.info("[FIRECMD][#{params['test_framework'].upcase}] App installation failed: message : #{e.message}\n backtrace: #{e.backtrace.join('\n')}")
        return { error: "App installation failed: #{e.message}" }
      end
      BrowserStack.logger.info("App & Test App installed on device #{device}")

      if params["other_apps"]
        begin
          Utils.mark_event_start('other_apps_download_and_install_time', params[:event_hash])
          params["other_app_bundle_ids"] = download_and_install_dependent_apps(params["other_apps"], device, params, params['automate_session_id'])
          push_to_cls(params, 'other_app_install_done', '', { "device_id" => device, "data" => params, "other_apps" => params["other_app_bundle_ids"] })
          Utils.write_to_file_with_lock(session_file(device), params.to_json)
          write_session_info(device, params, session_info_test_framework)
          Utils.mark_event_end('other_apps_download_and_install_time', params[:event_hash])
          params[:feature_usage]["otherApps"] = { success: true }
        rescue FireCMDException => e
          push_to_cls(params, "other_app_install_failed", e.message.to_s, { "device_id" => device, "data" => params })
          BrowserStack.logger.error("[FIRECMD][#{params['test_framework'].upcase}] Other Apps installation failed: message #{e.message}\n backtrace: #{e.backtrace.join("\n")}")
          return { error: e.message.to_s, kind: "other_apps_install_failure", type: e.type, meta_data: e.meta_data }
        rescue => e
          push_to_cls(params, "other_app_install_failed", e.message.to_s, { "device_id" => device, "data" => params, "other_apps" => params["other_apps"] })
          BrowserStack.logger.info("[FIRECMD][#{params['test_framework'].upcase}] Other Apps installation failed: message : #{e.message}\n backtrace: #{e.backtrace.join('\n')}")
          params[:feature_usage]["otherApps"] = { success: false, exception: e.message[0, 100] }
          return { error: "Other Apps installation failed: #{e.message}" }
        end
        BrowserStack.logger.info("Other Apps installed on device #{device}")
      end

      # Apple pay setup
      params["enableApplePay"] = params["enableApplePay"].to_s == "true" && Secure::ApplePay.apple_pay_device?(device)
      if params['enableApplePay']
        BrowserStack.logger.info("Starting Apple Pay setup for XCUITest")
        apple_pay_setup(device, params)
        BrowserStack.logger.info("Apple Pay setup done for XCUITest")
      end

      if params["custom_media"]
        begin
          Utils.mark_event_start('custom_media_download_and_push_time', params[:event_hash])
          bundle_id = fetch_bundle_id_from_session_file(device, params['session_id'])
          BrowserStack.logger.info("[aa_file_push] Bundle_id = #{bundle_id}")
          photos, videos, params[:feature_usage] = BrowserStack::IPhone.download_and_push_custom_media(params["custom_media"], params['test_framework'], device, params['automate_session_id'], bundle_id, params[:feature_usage])
          Utils.mark_event_end('custom_media_download_and_push_time', params[:event_hash])
          if (photos || videos) != 0
            push_to_cls(params, 'custom_media_download_and_push_done', '', { "device_id" => device, "data" => params, "custom_media" => params["custom_media"] })
            params[:feature_usage]["customMedia"] = { success: true }
          end
        rescue CustomContactsError => e
          BrowserStack.logger.info("[CustomContactsError] Custom Contacts update failed ERROR : #{e}\n message : #{e.message}\n backtrace: #{e.backtrace.join('\n')}")
          params[:feature_usage]["customMedia_contacts"] = { success: false, exception: "custom_media contacts load failed" }
          return { error: e.message.to_s, kind: e.kind, type: e.type.to_s }
        rescue FireCMDException => e
          push_to_cls(params, "custom_media_download_and_push_failed", e.message.to_s, { "device_id" => device, "data" => params, "custom_media" => params["custom_media"] })
          BrowserStack.logger.info("[FIRECMD][#{params['test_framework'].upcase}] Custom Media Files download/update failed: message : #{e.message}\n backtrace: #{e.backtrace.join('\n')}")
          params[:feature_usage]["customMedia"] = { success: false, exception: "custom_media download/update failed" }
          return { error: e.message.to_s, kind: "custom_media_download_update_failed", type: e.type.to_s }
        rescue => e
          push_to_cls(params, "custom_media_download_and_push_failed", e.message.to_s, { "device_id" => device, "data" => params, "custom_media" => params["custom_media"] })
          BrowserStack.logger.info("[FIRECMD][#{params['test_framework'].upcase}] Custom Media Files download/update failed: message : #{e.message}\n backtrace: #{e.backtrace.join('\n')}")
          params[:feature_usage]["customMedia"] = { success: false, exception: "custom_media_download_update_failed" }
          return { error: "Custom Media Files download/update failed: #{e.message}" }
        end
      end

      # trust client enterprise app
      if params["resignApp"].to_s == 'false' && params["skip_platform_enterprise_flow"] != "true" && params["mdm_enterprise_app_install"] != "true"
        Utils.mark_event_start('fire_cmd.bypass_resigning', params[:event_hash])
        begin
          trust_client_enterprise_app(device, params['automate_session_id'], downloaded_app_path)
        rescue FireCMDException => e
          BrowserStack.logger.info("[FIRECMD] Enterprise App trust failed: message : #{e.message}\n backtrace: #{e.backtrace.join('\n')}")
          params[:feature_usage]["resignFalse"] = { success: false, exception: e.message[0, 100] }
          return { error: e.message.to_s, kind: "enterprise_app_trust_failed", type: e.type.to_s }
        ensure
          Utils.mark_event_end('fire_cmd.bypass_resigning', params[:event_hash])
        end
        params[:feature_usage]["resignFalse"] = { success: true }
      end

      if params[:latitude] && params[:longitude]
        set_gpslocation(device, params)
        params[:feature_usage]["gpsLocation"] = { success: true }
      end

      if params['test_framework'] == "maestro"
        begin
          Utils.mark_event_start('install_maestro_ui_runner_app', params[:event_hash])
          device_version = begin
            current_device['device_version'].to_i
          rescue
            0
          end
          install_maestro_ui_runner(device, device_version)
        rescue => e
          BrowserStack.logger.info("[FIRECMD][#{params['test_framework'].upcase}] UI Runner install failed : #{e.message}\n backtrace: #{e.backtrace.join('\n')}")
          return { error: "Maestro UI Runner install failed: #{e.message}" }
        ensure
          Utils.mark_event_end('install_maestro_ui_runner_app', params[:event_hash])
        end
      end

      begin
        Utils.mark_event_start('tunnel_setup', params[:event_hash])
        BrowserStack::Session.setup_local(params, current_device)
        params[:feature_usage]["geoLocation"] = { success: true } if params[:geoLocation]
        Utils.mark_event_end('tunnel_setup', params[:event_hash])
        push_to_cls(params, 'tunnel_setup_done', '', { "device" => device, "data" => params })
      rescue => e
        push_to_cls(params, 'tunnel_setup_failed', e.message.to_s, { "device" => device, "data" => params })
        BrowserStack.logger.info("[FIRECMD][#{params['test_framework'].upcase}] Error Setting Proxy: message : #{e.message}\n backtrace: #{e.backtrace.join('\n')}")
        return { error: "Error Setting Proxy #{e.message}" }
      end

      BrowserStack::IPhone.unlock_device(device, 20)

      if params["deviceLogs"].to_s == 'true'
        BrowserStack.logger.info("Starting device logger for device #{device}")
        BrowserStack::DeviceLogger.clean_workspace(device)
        BrowserStack::DeviceLogger.initialize(device, params['session_id'])
        BrowserStack::DeviceLogger.start(device)
        params['app_testing_bundle_id'] = params['app_details_bundle_id'] || params['test_suite_bundle_id']
        BrowserStack::DeviceLogger.start_app_logs(device, params)
        BrowserStack.logger.info("Device logger setup for device #{device} completed")
      end

      if params[:networkSimulation].to_s == 'true'
        Utils.mark_event_start('networking_simulation', params[:event_hash])
        output = apply_network_simulation(device, params)
        if output.include? :error
          params[:feature_usage]["customNetwork"] = { success: false, exception: output[:error][0, 100] }
        elsif @is_app_testing
          params[:feature_usage]["customNetwork"] = { success: true }
        end
        Utils.mark_event_end('networking_simulation', params[:event_hash])
      end

      set_orientation(device, params[:orientation], params) if params[:orientation]

      begin
        if params['mdm_enterprise_app_install'] == "true"
          params['app_testing_bundle_id'] = params['app_details_bundle_id']
          validate_mdm_app_install(device, params)
          params[:feature_usage]["resignFalse"] = { success: true }
        end
      rescue => e
        BrowserStack.logger.info("[FIRECMD] Enterprise App install failed: message : #{e.message}\n backtrace: #{e.backtrace.join('\n')}")
        params[:feature_usage]["resignFalse"] = { success: false, exception: e.message[0, 100] }
        return { error: e.message.to_s, kind: "enterprise_app_install_failed", type: e.type.to_s }
      end
      Utils.mark_event_start('start_sessions', params[:event_hash])
      begin
        params_str = Base64Utils.encode_to_base64(params.to_json)
        current_device_str = Base64Utils.encode_to_base64(current_device.to_json)
        settings_str = Base64Utils.encode_to_base64(@@settings.to_json)
        xctest_pid = spawn "#{RUBY_BINARY_PATH} spawn_xctest.rb --params '#{params_str}' --current_device '#{current_device_str}' --settings '#{settings_str}'"
        Process.detach(xctest_pid)
        Utils.write_to_file("/tmp/#{device}-xctest.pid", xctest_pid)

        # TODO: The below code will increase start time by 3 secs but should not get impact user perceived time since spawn_xctest process is running asynchronously.
        #  This is a temporary fix to cover failures during deploy eventually this needs to be handled in a better way.
        pid_status_retry = 0
        running_status = true
        while pid_status_retry < 3
          running_status = !BrowserStack::OSUtils.grep_process_details(xctest_pid).empty?
          BrowserStack.logger.info("#{xctest_pid} Running: #{running_status} for session: #{params['automate_session_id']}")
          break unless running_status

          sleep(1)
          pid_status_retry += 1
        end

        raise "Failed to spawn xctest: Process killed or terminated" unless running_status

      rescue => e
        raise "Failed to spawn xctest: #{e.message}"
      end
      Utils.mark_event_end('start_sessions', params[:event_hash])
      push_to_cls(params, 'session_start_done', '', { "device_id" => device, "data" => params, "xctest_pid" => xctest_pid.to_s })
      {}
    rescue FireCMDException => e
      raise e
    rescue => e
      BrowserStack.logger.info("[FIRECMD] #{params['automate_session_id']} #{device} Failed with unknown error: #{e.message} Trace: #{e.backtrace}")
      raise FireCMDException.new(e.message, BROWSERSTACK_ERROR_STRING, UNKNOWN_FIRECMD_BROWSERSTACK_ERROR_KIND)
    end

    def timeout_xctest_session(device, params)
      current_device = device_configuration_check(device)
      xctest_session = BrowserStack::XCTestSession.new(params, current_device)
      xctest_pid = fork do
        xctest_session.timeout
      end
      Process.detach(xctest_pid)
      Utils.write_to_file("/tmp/#{device}-xctesttimeout.pid", xctest_pid)
      {}
    end

    def install_maestro_ui_runner(device, device_version)
      maestro_runner = MaestroUIRunner.new
      ppuid_file = PpuidFile.new(device).ppuid
      maestro_runner.update_app_version_using_ios_version(device_version)
      maestro_runner.setup(device, ppuid_file)
    end

    def get_network_simulator(device)
      current_device = device_configuration_check(device)
      privoxy_port = current_device['selenium_port'].to_i + @@settings["privoxy_listen_port_offset"].to_i
      NetworkSimulator.new(device, privoxy_port)
    end

    def fetch_bundle_id_from_session_file(device, session_id)
      bundle_id = ''
      if File.exist?(DeviceManager.session_file(device))
        session_file_contents = JSON.parse(File.read(DeviceManager.session_file(device)))
        BrowserStack.logger.info("session_file details #{session_file_contents}")

        bundle_id = session_file_contents['app_testing_bundle_id'] if session_id.to_s == session_file_contents['automate_session_id'].to_s
      end

      bundle_id
    end

    def send_network_simulation_logs(device, params)
      json_to_send = if params[:network_simulation_profile_name] == "custom_profile"
                       { "network_simulation" => params[:network_simulation_profile_name],
                         "values" => { "upload" => params[:network_bw_upld], "download" => params[:network_bw_dwld],
                                       "latency" => params[:network_latency], "pk_loss" => params[:network_pk_loss] } }
                     else
                       { "network_simulation" => params[:network_simulation_profile_name] }
                     end
      session_params = JSON.parse(File.read(session_file(device)))
      push_to_cls(session_params, "network_simulation", '', json_to_send)
      # for eds
      eds_table = params[:app_live_session_id] ? EdsConstants::APP_LIVE_TEST_SESSIONS : EdsConstants::LIVE_TEST_SESSIONS
      json_to_send[:session_id] = session_params["live_session_id"] || session_params["app_live_session_id"]
      json_to_send[:product] = {}
      json_to_send[:product][:simulation_profile_used] = json_to_send["network_simulation"]
      json_to_send.delete("network_simulation")
      json_to_send.delete("values")
      Utils.send_to_eds(json_to_send, eds_table, true)
    end

    def apply_network_simulation(device, params)
      result = {}
      session_id = params[:app_live_session_id] || params[:automate_session_id] || params[:live_session_id]
      send_network_simulation_logs(device, params) if params[:app_live_session_id] || params[:live_session_id]
      simulator = get_network_simulator(device)

      begin
        # Step 1: Reset any existing network simulation Rules
        simulator.reset_network_simulation

        # Step 2: Apply network simulation Rules as per params
        if !params[:network_reset].nil? && params[:network_reset].to_s == "true"
          BrowserStack.logger.info("Reset Network already Done. Skipping")
        elsif !params[:network_wifi].nil? && params[:network_wifi].to_s == "false"
          # This would be extended to network_mode settings in future.
          # Network_wifi does not imply we are turning wifi off, but refers to internet connection in general.
          # We assume, the devices are in disconnected wifi state already.
          simulator.apply_network_mode("offline")
        else
          simulator.setup_throttling_rules(params)
        end
      rescue => e
        simulator.reset_network_simulation
        BrowserStack.logger.info("Exception while enabling network simulation: #{e.message}, backtrace: #{e.backtrace}")
        BrowserStack::Zombie.push_logs("network-simulation-error", params[:genre], { "session_id" => session_id, "device" => device })
        session_params = JSON.parse(File.read(session_file(device)))
        push_to_cls(session_params, "network-simulation-error", e.message.to_s, '') if session_id
        result = { error: "Error Setting Network simulation #{e.message}" }
      end
      result
    end

    def log_bridge_commands(device, params)
      begin
        appium_logs_append_marker(device, params)
      rescue => e
        BrowserStack.logger.info("Exception while adding marker: #{e.message}")
        BrowserStack::Zombie.push_logs("appium-marker-failed", e.message.to_s, { "session_id" => params['automate_session_id'], "device" => device })
      end
      [200, {}.to_json]
    end

    def setup_mitm_proxy(device, current_device, params)
      setup_mitm(device, current_device, params)
    end

    # Purpose: This is for overriding appium's reset method.
    # Function: This uninstalls and then installs it again.
    def reset_app(device, automate_session_id)
      BrowserStack.logger.info("reset_app invoked for device : #{device}  and automate_session_id #{automate_session_id}")
      app_path = nil
      if File.exists?(session_file(device))
        session_file = JSON.parse(File.read(session_file(device)))
        BrowserStack.logger.info("session_file details #{session_file}")
        app_path = session_file['downloaded_app_path'] if automate_session_id.to_s == session_file['automate_session_id'].to_s
      end

      raise ":App not found at : #{app_path}, for reset." unless File.exist?(app_path.to_s)

      reinstall_app(device, { path: app_path, retry_count: 0, session_id: automate_session_id })
    rescue => e
      BrowserStack.logger.info("Exception while resetting_app #{e.message}, backtrace: #{e.backtrace}")
      raise e
    end

    def app_strings(device, automate_session_id, language, string_file)
      BrowserStack.logger.info("app_strings called for device: #{device} and automate_session_id #{automate_session_id}} with language #{language} and string_file #{string_file}")

      raise "Session File does not exist for device: #{device} and automate_session_id #{automate_session_id}" unless File.exist?(session_file(device))

      app_path = nil
      session_file = JSON.parse(File.read(session_file(device)))
      BrowserStack.logger.info("session_file details #{session_file}")

      app_path = session_file['downloaded_app_path'] if automate_session_id.to_s == session_file['automate_session_id'].to_s

      raise "App not found at : #{app_path}, for app_strings." unless File.exist?(app_path.to_s)

      lproj_path = "#{app_path}/#{language}.lproj"

      raise AppStringsCommandError, "No '#{lproj_path}' resource file has been found for App" unless Dir.exist? lproj_path

      resource_paths = []

      unless string_file.empty?
        lproj_path = "#{lproj_path}/#{string_file}"

        raise AppStringsCommandError, "No '#{lproj_path}' resource file has been found for App" unless File.exist? lproj_path

        resource_paths << lproj_path
      end

      # Find all .strings and .stringdict files under
      resource_paths = Dir.glob(["#{lproj_path}/*.stringsdict", "#{lproj_path}/*.strings"]) if resource_paths.empty?

      response = {}
      return response if resource_paths.empty?

      BrowserStack.logger.info "Fetching strings content for :#{resource_paths}"

      resource_paths.each do |resource_path|
        strings_content = Utils.parse_plist_and_convert_to_json(resource_path)
        response.merge!(strings_content)
      rescue => e
        BrowserStack.logger.info "Exception occurred while parsing file #{resource_path} #{e.message} #{e.backtrace}"
        raise AppStringsCommandError, "Exception occurred while parsing file #{resource_path}"
      end

      response
    end

    def install_app(device, automate_session_id, app_hashed_id)
      BrowserStack.logger.info("install_app invoked for device : #{device}  and automate_session_id #{automate_session_id}")
      app_path = nil
      if File.exist?(session_file(device))
        session_file_contents = JSON.parse(File.read(session_file(device)))
        BrowserStack.logger.info("session_file details #{session_file_contents}")

        if automate_session_id.to_s == session_file_contents['automate_session_id'].to_s
          duplicate_other_apps = session_file_contents['duplicate_other_apps_details']

          if duplicate_other_apps && duplicate_other_apps[app_hashed_id]
            is_app_upgrade = true
            app_path = duplicate_other_apps[app_hashed_id]["app_path"]
            bundle_id = duplicate_other_apps[app_hashed_id]["bundle_id"]
          end
        end

        unique_other_apps = session_file_contents['unique_other_apps_details']

        return "The app_url 'bs://#{app_hashed_id}' passed in the installApp command is already installed on the device" if unique_other_apps&.key?(app_hashed_id)
      end

      raise AppInstallCommandFailedException, "An unknown server-side error occurred while processing the command. Original error: The app_url value 'bs://#{app_hashed_id}' is invalid." if app_path.nil? || app_path.empty? || !File.exist?(app_path.to_s)

      install_app_and_verify(device, { path: app_path, id: bundle_id, retry_count: 1, session_id: automate_session_id, app_type: "dependent" })

      # the following is done to support app settings updates on mid session upgraded app
      if is_app_upgrade
        BrowserStack::AppSettingsUtil.parse_settings_bundle(app_path, { "device_id" => device, "session_id" => automate_session_id }) #TODO: This need to be fixed for mdm app install flow
        session_file_contents["upgraded_app"] = duplicate_other_apps[app_hashed_id]
        Utils.write_to_file_with_lock(session_file(device), session_file_contents.to_json)
      end

      ""
    end

    def crash_reports(device, automate_session_id, test_id = nil, zip_crash_report = true)
      test_id ||= automate_session_id
      crash_reports_start_time = Time.now.to_f
      BrowserStack.logger.info("crash_reports called for device: #{device} and automate_session_id #{automate_session_id}")

      raise "Session File does not exist for device: #{device} and automate_session_id #{automate_session_id}" unless File.exist?(session_file(device))

      app_path = nil
      test_app_path = nil
      session_file = JSON.parse(File.read(session_file(device)))

      app_path = session_file['downloaded_app_path'] if automate_session_id.to_s == session_file['automate_session_id'].to_s
      test_app_path = session_file['downloaded_test_app_path'] if automate_session_id.to_s == session_file['automate_session_id'].to_s

      raise "App not found at : #{app_path}, for crash reports." unless File.exist?(app_path.to_s)

      app_binary_name = PlistBuddy.get_value_of_key("#{Shellwords.escape(app_path)}/Info.plist", "CFBundleExecutable").strip
      test_app_binary_name = PlistBuddy.get_value_of_key("#{Shellwords.escape(test_app_path)}/Info.plist", "CFBundleExecutable").strip if test_app_path

      app_binaries = [app_binary_name]
      app_binaries.push(test_app_binary_name) if test_app_binary_name

      num_crash_reports = IdeviceUtils.get_crash_report_from_device(device, app_binaries, test_id, zip_crash_report).length

      crash_reports_total_time = (Time.now.to_f - crash_reports_start_time).to_i
      BrowserStack.logger.info("crash_reports_total_time :#{crash_reports_total_time}")
      [num_crash_reports, app_binary_name]
    end

    def stop_xctest_session(device, params)
      begin
        if params[:test_framework] == "maestro"
          maestro_session = BrowserStack::MaestroSession.new(params, device)
          maestro_session.force_stop
        else
          xctest_session = BrowserStack::XCTestSession.new(params, device)
          xctest_session.force_stop
        end

      rescue => e
        BrowserStack::Zombie.push_logs("#{params['test_framework']}_stop_session_fail", e.message, { "session_id" => params['automate_session_id'], "device" => device })
        raise e
      end
      {}
    end

    # Checks wda and iproxy status for app automate sessions and if it fails then sends data to zombie
    def check_wda_iproxy_status(device, params)
      # Check is only for automate or app automate
      return unless %w[app_automate selenium].include?(params[:genre])

      current_device = device_configuration_check(device)
      webdriver_port = current_device["webdriver_port"]
      wda_client = WdaClient.new(webdriver_port)
      is_wda_running, wda_status_description = wda_client.describe_running

      is_wda_port_listening = BrowserStack::OSUtils.is_port_listening?(webdriver_port)
      session_id = params['automate_session_id']

      BrowserStack.logger.info("[check_wda_iproxy_status][#{session_id}] : webdriver_port : #{webdriver_port} ; is_wda_running : #{is_wda_running} ; is_iproxy_running? : #{is_wda_port_listening}")

      data = "session_type: #{params[:genre]} ; wda_running: #{is_wda_running} ; iproxy_running: #{is_wda_port_listening}"
      data += " ; wda_status_description: #{wda_status_description}" unless is_wda_running

      syslog_parse_result_file = "#{APP_AUTOMATE_PARSER_DIR}/results_#{params[:automate_session_id]}.log"

      if !is_wda_running || !is_wda_port_listening
        BrowserStack::Zombie.push_logs(
          "wda_iproxy_check_fail",
          data,
          { "session_id" => params['automate_session_id'], "device" => device },
          nil, params
        )
        eds_data = {
          secondary_diagnostic_reason: data,
          hashed_id: session_id,
          timestamp: Time.now.to_i
        }
        Utils.send_to_eds(eds_data, EdsConstants::APP_AUTOMATE_TEST_SESSIONS, true)
        Utils.push_syslog_parse_results(syslog_parse_result_file, session_id, nil)
      end
    rescue => e
      BrowserStack.logger.info("[check_wda_iproxy_status][#{params['automate_session_id']}] Exception #{e.message}, backtrace: #{e.backtrace}")
    end

    def get_settings_pages_visited(device, params)
      return nil if params[:genre] != "app_live_testing"

      current_device = device_configuration_check(device)
      webdriver_port = current_device["webdriver_port"]
      wda_client = WdaClient.new(webdriver_port)
      is_wda_running = begin
        wda_client.running?
      rescue
        false
      end
      is_wda_port_listening = BrowserStack::OSUtils.is_port_listening?(webdriver_port)
      session_id = params['app_live_session_id']

      BrowserStack.logger.info("[check_wda_iproxy_status][#{session_id}] : webdriver_port : #{webdriver_port} ; is_wda_running : #{is_wda_running} ; is_iproxy_running? : #{is_wda_port_listening}")

      if !is_wda_running || !is_wda_port_listening
        BrowserStack::Zombie.push_logs(
          "al_wda_not_running_in_stop",
          "session_type: #{params[:genre]} ; wda_running: #{is_wda_running} ; iproxy_running: #{is_wda_port_listening}",
          { "session_id" => session_id, "device" => device },
          nil, params
        )
        return nil
      end
      response = wda_client.get_settings_pages_visited
      pages_visited = begin
        response["value"]
      rescue
        nil
      end
      pages_visited = nil unless pages_visited.is_a? Array
      pages_visited
    rescue => e
      BrowserStack.logger.info("[get_settings_pages_visited][#{session_id}] Exception #{e.message}, backtrace: #{e.backtrace}")
    end

    def get_url(device, debugger_port, device_version = nil)
      # moved this out of server.rb
      url = ""
      return url if debugger_port.nil?

      # left for future reference
      # Since this function is called in start request
      # removing this logic and catching all exception
      # to avoid BMs
      # errors = [
      #  Timeout::Error,
      #  Errno::EINVAL,
      #  Errno::ECONNRESET,
      #  EOFError,
      #  Net::HTTPBadResponse,
      #  Net::HTTPHeaderSyntaxError,
      #  Net::ProtocolError,
      #  Net::ReadTimeout
      #]
      begin
        http = Net::HTTP.new("127.0.0.1", 443)
        http.use_ssl = true
        http.read_timeout = 2
        http.verify_mode = OpenSSL::SSL::VERIFY_NONE
        resp, _data = http.get("/json?debug_port=#{debugger_port}")
      rescue => e
        BrowserStack.logger.error("Exception in /json #{device}: #{e.message} \n#{e.backtrace.join("\n")}")
      else
        json = resp.body
        unless json.nil?
          hash = begin
            JSON.parse(json)
          rescue
            {}
          end
          url = hash[0]["url"] unless hash[0].nil? || hash[0]["title"] == "ServiceWorker"
          url = hash[-1]["url"] if !hash[-1].nil? && hash[-1]["thumbnailUrl"] != "/thumb/" && hash[-1]["title"] != "ServiceWorker"
          url = url.to_s.start_with?('data:') ? '' : url.to_s # Remove data urls
          url.gsub!('bs-local.com', 'localhost') # Convert the proxied localhost url back to localhost
        end
      end
      url
    end

    def backfill_app(options) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
      backfill_attempts = 1
      begin
        # In case of resign, we backfill the app with the new certificate and download the new app from signed app url in the response
        BrowserStack.logger.info("Backfill needed for app with hashed_id #{options[:app_hashed_id]}. Requesting codesigner on #{options[:codesigner_host]}")
        backfill_signed_app_url = options.key?(:backfill_signed_app_url) ? options[:backfill_signed_app_url] : nil

        post_data_hash = {
          data: {
            certificate: options[:certificate],
            app_url: options[:unsigned_app_url],
            is_framework: options[:is_framework],
            app_hashed_id: options[:app_hashed_id],
            product: options[:product],
            rails_host: options[:rails_host],
            skipSigningFrameworks: options[:skipSigningFrameworks],
            from_platform: true,
            is_tvos: options[:is_tvos],
            browserstackInjectorVersion: options[:browserstackInjectorVersion]
          }
        }
        post_data_hash[:data][:app_uploader_identifier] = options[:app_uploader_identifier] if options[:app_uploader_identifier].to_s != ""
        post_data_hash[:data][:user_id] = options[:user_id] if options[:user_id].to_s != ""
        post_data_hash[:data][:keyChainPatching] = options[:keyChainPatching] if options[:keyChainPatching].to_s != ""
        post_data_hash[:data][:s3_config] = options[:s3_config] if options[:s3_config].to_s != ""

        BrowserStack.logger.info("Backfill data: #{post_data_hash}")
        is_patch_app_request = options[:patchApp].to_s == "true"
        codesigner_host = is_patch_app_request ? "#{options[:codesigner_host]}/patchApp" : "#{options[:codesigner_host]}/backfill"
        BrowserStack.logger.info("Backfill: Codesigner host #{codesigner_host}")
        start_at = Time.now
        response = BrowserStack::HttpUtils.send_post(codesigner_host, post_data_hash, nil, true, { read_timeout: 180 })
        resign_time = Time.now - start_at
        AppAnalyticsUtil.instrument_time(:app_resigning_duration_ms, (resign_time.to_f * 1000).to_i, options[:app_live_session_id])
        if response.code.to_i == 200
          response_body = JSON.parse(response.body)
          BrowserStack.logger.info("Backfill: Codesigner response body #{response_body}")
          url = is_patch_app_request ? options[:patched_app_download_url] : backfill_signed_app_url
          url ||= response_body["s3PresignedUrl"]
        end

        data_hash = {
          'inject_app' => [
            {
              'timestamp' => start_at,
              'time_taken' => resign_time
            }
          ],
          'appFramework' => options[:appFramework]
        }
        if url
          data_hash["inject_app"][0]["status"] = 'success'
          BrowserStack.logger.info("backfill_app: update_app_patching_data_to_state_file: called for file_name #{options[:device_id]}")
          BrowserStack::Zombie.push_logs("backfill-success", options[:product], { "data" => { "certificate" => options[:certificate], "app_hashed_id" => options[:app_hashed_id], "time" => resign_time, "isPatchApp" => is_patch_app_request.to_s } }) if options[:codesigner_host].to_s.match(/browserstack/)
          BrowserStack.logger.info("Backfill: Done. Received signed app s3Url #{url}")
          BrowserStack.logger.info("Backfill: Done. Cloudfront signed app s3Url #{backfill_signed_app_url}")
        else
          data_hash["inject_app"][0]["status"] = 'failure'
          BrowserStack.logger.info("backfill_app: update_app_patching_data_to_state_file: called for file_name #{options[:device_id]}")
          BrowserStack::Zombie.push_logs("backfill-failure", options[:product], { "data" => { "certificate" => options[:certificate], "app_hashed_id" => options[:app_hashed_id], "time" => resign_time, "isPatchApp" => is_patch_app_request.to_s } }) if options[:codesigner_host].to_s.match(/browserstack/)
          BrowserStack.logger.info("Backfill: Non 200 response with response body #{response}")
        end
        AppPatchingUtil.update_app_patching_data_to_state_file(options[:device_id], data_hash)
        backfill_signed_app_url.nil? ? url : backfill_signed_app_url
      rescue *Utils.network_exceptions => e
        backfill_attempts += 1
        if options[:secondary_codesigner_host] && backfill_attempts <= 2 && options[:product] == "app_automate"
          options[:codesigner_host] = options[:secondary_codesigner_host]
          BrowserStack.logger.info("Backfill: Error: #{e.message} #{options[:device_id]}")
          BrowserStack.logger.info("Backfill: Retrying backfill request with options: #{options}, attempt: #{backfill_attempts}, #{options[:device_id]}")
          retry
        end
        raise e
      end
    end

    def using_production_codesigner?(host)
      host.to_s.match(/browserstack/) ? true : false
    end

    def patch_app(options) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
      BrowserStack.logger.info("patch_app was called with options: #{options}")
      post_data_hash = {
        data: {
          certificate: options[:certificate],
          app_url: options[:app_url],
          app_hashed_id: options[:app_hashed_id],
          product: options[:product],
          rails_host: options[:rails_host],
          skipSigningFrameworks: options[:skipSigningFrameworks],
          s3_config: options[:s3_config],
          appFramework: options[:appFramework],
          browserstackInjectorVersion: options[:browserstackInjectorVersion],
          enable_bonjour: options[:enable_bonjour],
          bonjour_service_type: options[:bonjour_service_type],
          network_usage_description: options[:network_usage_description]
        }
      }

      post_data_hash[:data][:user_id] = options[:user_id] if options[:user_id].to_s != ""
      codesigner_host = "#{options[:codesigner_host]}/patchApp"
      BrowserStack.logger.info("Requesting codesigner on: #{codesigner_host}, with PatchApp data: #{post_data_hash}")
      start_at = Time.now
      response = BrowserStack::HttpUtils.send_post(codesigner_host, post_data_hash, nil, true, { read_timeout: 180 })
      patchapp_time = Time.now - start_at
      if response.code.to_i == 200
        response_body = JSON.parse(response.body)
        BrowserStack.logger.info("PatchApp: Codesigner response body: #{response_body}")
        url = options[:patched_app_download_url] || response_body["s3PresignedUrl"]
      end
      zombie_data = {
        "data" => {
          "certificate" => options[:certificate],
          "app_hashed_id" => options[:app_hashed_id],
          "skipSigningFrameworks" => options[:skipSigningFrameworks],
          "patchapp_time" => patchapp_time,
          "appFramework" => options[:appFramework]
        }
      }

      data_hash = {
        'inject_app' => [
          {
            'timestamp' => start_at,
            'time_taken' => patchapp_time
          }
        ],
        "appFramework" => options[:appFramework]
      }
      if url
        data_hash["inject_app"][0]["status"] = 'success'
        BrowserStack.logger.info("patch_app: update_app_patching_data_to_state_file: called for file_name #{options[:device_id]}")
        BrowserStack::Zombie.push_logs("app-live-patchapp-success", options[:product], zombie_data) if using_production_codesigner?(options[:codesigner_host])
        BrowserStack.logger.info("PatchApp was done. Biometric instrumented app s3Url #{url}")
      else
        zombie_data["data"]["codesigner_response_code"] = response.code
        data_hash["inject_app"][0]["status"] = 'failure'
        BrowserStack.logger.info("patch_app: update_app_patching_data_to_state_file: called for file_name #{options[:device_id]}")
        BrowserStack::Zombie.push_logs("app-live-patchapp-failed", options[:product], zombie_data) if using_production_codesigner?(options[:codesigner_host])
        BrowserStack.logger.info("/patchApp: Non 200 response with response body #{response}")
      end
      begin
        AppPatchingUtil.update_app_patching_data_to_state_file(options[:device_id], data_hash)
      rescue => e
        BrowserStack.logger.error "patch_app: update_app_patching_data_to_state_file: failed: #{e.message} #{e.backtrace}"
        raise e
      end
      url
    end

    def default_xctestrun_file(device)
      device_config = device_configuration_check(device)
      device_version = device_config['device_version'].to_f
      default_appium_version = @@settings['default_appium_version'].to_s
      default_appium_version = @@settings['default_wda_version'].to_s || default_appium_version
      Utils.get_web_driver_agent_xctestrun_file(device_config, default_appium_version)
    end

    # restarts the device using MDM Api [ uses file path ]
    def restart_device(device)
      # will throw if device not in device list
      device_configuration_check(device)
      # bs_run creates a new thread & copies logging params to it
      return Thread.bs_run { restart_device_job(device) } unless devices_under_restart.add?(device).nil?

      BrowserStack.logger.info "Device restart for device = #{device} skipped because already under restart"
      nil
    end

    def devices_under_restart
      @@devices_under_restart
    end

    def switch_appium(params, device, current_device, appium_version, log_level = 'debug')
      Utils.mark_event_start('fire_cmd.appium_create_time', params[:event_hash])
      device_version = device_configuration_check(device)['device_version'].to_f
      skip_one_wda = params['skip_one_wda']
      skip_wda_uninstall = !skip_one_wda && @@settings['default_wda_version'].to_s != ""
      unless skip_wda_uninstall
        BrowserStack::WebDriverAgent.stop(device)
        BrowserStack::IPhone.uninstall_wda(device)
      end

      appium_server = BrowserStack::AppiumServer.new(device, current_device)
      if Gem::Version.new(appium_version) >= Gem::Version.new('2.0.0')
        appium_server.start_server_for_version(appium_version, false, 30, log_level, params["automationName"], params["automationVersion"], skip_one_wda)
      else
        appium_server.start_server_for_version(appium_version, false, 30, log_level, nil, nil, skip_one_wda)
      end

      Utils.mark_event_end('fire_cmd.appium_create_time', params[:event_hash])
    rescue => e
      BrowserStack.logger.error "Unable to switch appium : #{device} due to error: #{e.message} Trace: #{e.backtrace}"
      raise FireCMDException.new(e.message, BROWSERSTACK_ERROR_STRING, "appium_switch_failure")
    end

    def is_syslog_hanged?(device, params)
      syslog_file_path = "/var/log/browserstack/syslog_#{device}.log"
      begin
        return unless File.file?(syslog_file_path)

        # check if syslog running
        idevicesyslog_process_running = !BrowserStack::OSUtils.execute("ps aux | grep #{device} | grep idevicesyslog | grep -v grep").empty?

        # fetch last log line of syslog
        parse_time = BrowserStack::OSUtils.execute("tail -1 #{syslog_file_path} | awk '{print $3 \" \" $4}'").chomp
        # parse last log line
        # [app_automate 729414a9cecc433a9286b50478baf2a573f3cbca 2022-04-19 09:01:40:460] Apr 19 09:01:40 healthappd(Summaries)[431]
        parse_time.delete_suffix!(']') # 18:23:20:948
        last_log_time = Time.parse(parse_time).getutc
        current_time = Time.now.getutc
        diff = (current_time - last_log_time).to_i
        BrowserStack::Zombie.push_logs('ios_syslog_check', idevicesyslog_process_running, { "device" => device, "data" => { "diff" => diff, "last_log_time" => last_log_time }, "genre" => params[:genre], "session_id" => params['automate_session_id'] })
      rescue => e
        BrowserStack.logger.error("Exception while parsing syslogs: #{e.message} \n#{e.backtrace.join("\n")}")
      end
    end

    def check_and_set_cleanup_policy(device, params, device_state)
      if params[:dedicated_cleanup] == "true"
        BrowserStack.logger.info("Setting dedicated cleanup")
        cleanup_configuration = params.key?(:dedicated_cleanup_config) ? params[:dedicated_cleanup_config] : ""
        device_state.write_to_dedicated_cleanup_file(cleanup_configuration)
        CustomMDMManager.new(device, BrowserStack.logger).manage_setup
      elsif device_state.dedicated_cleanup_file_present? || CustomMDMManager.is_custom_mdm_device?(device) || device_state.custom_mdm_remove_setup_file_present?
        reason = device_state.dedicated_cleanup_file_present? ? "dedicated-cleanup" : "custom_mdm"
        if reason == "dedicated-cleanup"
          BrowserStack.logger.info("Deleting #{reason} file")
          device_state.remove_dedicated_cleanup_file
          device_state.remove_dedicated_video_state_file
        end
        message = "Stray #{reason} file found"
        BrowserStack::Zombie.push_logs(
          "ios-stray-#{reason}-file",
          message,
          {
            "session_id" => params[:automate_session_id],
            "team" => "bridgecloud",
            "data" => {
              "device" => device,
              "genre" => params[:genre]
            }
          },
          nil, params
        )
        Utils.send_cleanup_request(device, "#{params[:genre]} start", reason)
        raise message
      end
    end

    def create_manifest_file(session_id, app_details, manifest_file_path)
      data = {
        download_url: app_details[:url],
        bundle_id: app_details[:bundle_id],
        bundle_version: 1,
        display_name: app_details[:display_name]
      }
      manifest_erb_filename = "#{__dir__}/../templates/mdm_app_install_manifest.erb"

      manifest_contents = ERB.new(File.open(manifest_erb_filename).read).result(ErbBinding.new(data).get_binding)
      Utils.write_to_file(manifest_file_path, manifest_contents)

      BrowserStack.logger.info("Writing Manifest contents: #{manifest_contents} to #{manifest_file_path}")
    end

    def upload_manifest_file(manifest_file_path, params)
      Utils.mark_event_start('fire_cmd.upload_manifest_file', params[:event_hash])

      key = params['stats_aws_key']
      secret = params['stats_aws_secret']
      bucket = params['stats_aws_bucket']
      session_id = params['automate_session_id']
      aws_region = bucket == "bs-stag" || params['stats_aws_region'] == "us-east-1" ? "" : "-#{params['stats_aws_region']}"
      region = bucket == "bs-stag" || params['stats_aws_region'] == "us-east-1" ? nil : (params['stats_aws_region']).to_s
      s3_url = "https://s3#{aws_region}.amazonaws.com/#{bucket}/#{session_id}/#{session_id}-manifest.xml"

      ret, error = Utils.upload_file_to_s3(key, secret, "application/xml", manifest_file_path, "public-read", s3_url, session_id, params['genre'], region, 300)

      if !ret && !error.empty?
        BrowserStack::Zombie.push_logs("manifest-upload-failure", "Failed to upload manifest file: #{error}, ret: #{ret}", { "session_id" => session_id })
        raise "Error while uploading manifest file to S3: #{error}"
      end

      BrowserStack.logger.info("uploaded manifest to: #{s3_url}, ret: #{ret}, error: #{error}")

      Utils.mark_event_end('fire_cmd.upload_manifest_file', params[:event_hash])
      s3_url
    end

    def request_mdm_for_install_app(session_id, device, s3_url)
      @start_mdm_install_app = Time.now
      BrowserStack::IosMdmServiceClient.configure
      ret = BrowserStack::IosMdmServiceClient.install_enterprise_application(device, s3_url)

      BrowserStack.logger.info("Request Complete to MDM: #{ret}, Started at: #{@start_mdm_install_app}")
    end

    def perform_mdm_enterprise_app_install(session_id, device, app_details, params)

      Utils.mark_event_start('fire_cmd.perform_mdm_enterprise_app_install', params[:event_hash])
      manifest_file_path = "/tmp/#{session_id}_manifest.xml"
      create_manifest_file(session_id, app_details, manifest_file_path)
      s3_url = upload_manifest_file(manifest_file_path, params)
      request_mdm_for_install_app(session_id, device, s3_url)
    rescue => e
      BrowserStack.logger.info "MDM enterprise app install failed"
      BrowserStack.logger.error "#{e.class.name} #{e.message}: #{e.backtrace.join("\n")}"
      raise "MDM enterprise app install failed"
    ensure
      FileUtils.rm_rf(manifest_file_path)
      Utils.mark_event_end('fire_cmd.perform_mdm_enterprise_app_install', params[:event_hash])

    end

    def validate_mdm_app_install(device, params)
      Utils.mark_event_start('fire_cmd.validate_mdm_app_install', params[:event_hash])
      sleep(5) # initial sleep
      begin
        retries ||= 1
        if IdeviceUtils.app_installed?(device, params['app_testing_bundle_id'])
          BrowserStack.logger.info("App installed successfully")
          BrowserStack.logger.info("validate_mdm_app_install done")
          FileUtils.touch "/tmp/client_enterprise_app_#{device}" # Makes is_client_enterprise_app? true for cleanup
        else
          raise "App not installed"
        end
      rescue
        if retries < 10
          sleep(3 * retries)
          retries += 1
          retry
        else
          raise "[FIRECMD] App not found"
        end
      end
      Utils.mark_event_end('fire_cmd.validate_mdm_app_install', params[:event_hash])
    end

    def check_and_give_settings_app_access(device, params)
      current_device_config = device_configuration_check(device)
      device_version = current_device_config['device_version']
      backup_manager = BackupManagerFactory.for(device, device_version)
      device_state = DeviceState.new(device)
      enable_settings = device_state.dedicated_device_file_present? || (params[:ios_unrestricted].to_s.downcase == "true" && backup_manager.supported?)
      return unless enable_settings

      `ps -ef | grep #{device} | grep idevicesyslog | awk '{print $2}' | xargs -I{} kill {}`

      dedicated_cloud_device = device_state.dedicated_device_file_present?.to_s
      custom_mdm_enabled = CustomMDMManager.is_custom_mdm_device?(device).to_s

      settings_access = `#{RUBY_BINARY_PATH} #{BSTACK_REALMOBILE_BASE}/lib/helpers/wda_client.rb #{device} settings_access true #{dedicated_cloud_device} #{custom_mdm_enabled} false 2>&1`
      BrowserStack.logger.info("Enabling Settings Access: for #{device} with dedicated_cloud_device=#{dedicated_cloud_device}, custom_mdm_enabled=#{custom_mdm_enabled}. Output: #{settings_access}")
    end

    def toggle_private_device_feature(device, feature, action)
      device_state = DeviceState.new(device)

      case action
      when 'GRANT_ACCESS'
        if feature == 'custom_mdm'
          device_state.remove_custom_mdm_remove_setup_file
          device_state.touch_custom_mdm_perform_setup_file
        end
      when 'REVOKE_ACCESS'
        if feature == 'custom_mdm'
          device_state.remove_custom_mdm_perform_setup_file
          device_state.touch_custom_mdm_remove_setup_file
        end
      end
    end

    private

    def update_streaming_params_from_platform(device_name, params)
      width, height = DeviceParams.get_device_width_and_height(device_name)

      params.merge!({
        "width" => width,
        "height" => height
      })

      params
    end

    def should_enable_cross_site_tracking?(params)
      params["preventCrossSiteTracking"] && params["preventCrossSiteTracking"] == "true" && params[:genre] == 'automate'
    end

    def restart_device_job(device)
      BrowserStack.logger.info "Starting restart for device = #{device}"
      result = if CustomMDMManager.is_custom_mdm_device?(device)
                 CustomMDMManager.new(device, BrowserStack.logger).reboot
               else
                 BrowserStack::IosMdmServiceClient.restart_device(device)
               end
      BrowserStack.logger.info "Device restart for device = #{device} Successful" unless result.nil?
    ensure
      devices_under_restart.delete(device)
    end

    def take_screenshot(params)
      # Upload Screenshot to S3 for debugging
      screenshot_s3_path = "https://s3.amazonaws.com/testautomation/#{params['automate_session_id']}/screenshot_on_failure.jpeg"
      get_screenshot(params['device'], params["automate_session_id"], "portrait", screenshot_s3_path)
    rescue => e # This is changed because the above Exception e was overwritten.
      BrowserStack.logger.info("[FIRECMD] Unable to upload failure screenshot to S3: #{e.message}\n bracktrace: #{e.backtrace.join("\n")}")
    end

    def appium_logs_append_marker(device, params)
      current_device = device_configuration_check(device)
      appium_log_file = appium_log_path(current_device['selenium_port'])
      marker = appium_marker(params[:log_type])
      BrowserStack.logger.info("echoing \"#{marker} #{params[:bridge_name]}\" to #{appium_log_file}")
      `echo "\n#{marker} #{params[:bridge_name]}" >> #{appium_log_file}`
    end

    def appium_marker(log_type)
      log_type == "start" ? APPIUM_BRIDGE_START_MARKER : APPIUM_BRIDGE_STOP_MARKER
    end

    def create_upload_request_logs(device, params, request_file, type)
      temp_type = params[:genre] == 'playwright' ? "appium" : type
      s3_params = {
        session_id: params['automate_session_id'],
        aws_key: params["#{temp_type}logs_aws_key"],
        aws_secret: params["#{temp_type}logs_aws_secret"],
        aws_bucket: params["#{temp_type}logs_aws_bucket"],
        aws_region: params["#{temp_type}logs_aws_region"],
        aws_storage_class: params["#{temp_type}logs_aws_storage_class"],
        zip_nw_logs_ios_aut: params["zip_nw_logs_ios_aut"],
        zip_appium_logs_ios_aut: params["zip_appium_logs_ios_aut"],
        zip_nw_logs_ios_app_aut: params["zip_nw_logs_ios_app_aut"],
        zip_appium_logs_ios_app_aut: params["zip_appium_logs_ios_app_aut"],
        genre: params[:genre] || params["genre"]
      }
      Utils.create_upload_request(request_file, type, device, s3_params, @@settings, nil, params["genre"])
    end

    def process_appium_logs(device, session_id)
      current_device = device_configuration_check(device)
      FileUtils.cp(appium_log_path(current_device['selenium_port']), session_appium_logs(session_id))
      remove_unwanted_text(session_id)
    end

    def remove_unwanted_text(session_id)
      session_appium_logs_file = session_appium_logs(session_id)

      # Removing text between markers inserted by Selenium Hub
      `sed -i '.orig' '/^#{APPIUM_BRIDGE_START_MARKER}/,/^#{APPIUM_BRIDGE_STOP_MARKER}/d' #{session_appium_logs_file}`

      # Removing keychain path, password, platformVersion from Appium Logs
      # ['keychainPath', 'keychainPassword', 'platformVersion'].each { |k| `sed -i '.orig' '/#{k}/d' #{session_appium_logs_file}` }
      ['keychainPath', 'keychainPassword', 'platformVersion', 'percy_automate_script'].each do |k|
        args = ["-i", ".orig", "/#{k}/d", session_appium_logs_file]
        args.collect!(&:to_s) # To convert anything that's integer to string
        system("sed", *args)
      end

      sed_backup_file = "#{session_appium_logs_file}.orig"

      # On Mac, sed expects backup file extension for replacing file in-place
      FileUtils.rm_rf(sed_backup_file)
    rescue => e
      BrowserStack.logger.info("Exception while processing appium logs: #{session_id} #{e.message}")
      BrowserStack::Zombie.push_logs("appium-marker-failed", e.message.to_s, { "session_id" => session_id, "device" => device })
    end

    def trust_client_enterprise_app(device, session_id, app_path)
      raise FireCMDException.new("Device parameter cannot be nil", BROWSERSTACK_ERROR_STRING, "enterprise_app_trust_failed") if device.nil?
      raise FireCMDException.new("App path parameter cannot be nil", BROWSERSTACK_ERROR_STRING, "enterprise_app_trust_failed") if app_path.nil?

      device_config = device_configuration_check(device)
      FileUtils.touch "/tmp/client_enterprise_app_#{device}"

      idevice = BrowserStack::IPhone.new(device_config, device)

      dist_name = XcodeBuildUtils.get_team_name_from_profile("#{app_path}/embedded.mobileprovision")
      begin
        BrowserStack.logger.info "starting client enterprise app trust"
        trust_start_time = Time.now

        raise "app trust returned false after possible retries" unless idevice.trust_client_enterprise_app(dist_name)

        BrowserStack.logger.info "client enterprise app trust successful, time taken: #{Time.now - trust_start_time}"
      rescue => e
        BrowserStack.logger.info "client enterprise app trust failed: #{e.message} #{e.backtrace}"
        BrowserStack::Zombie.push_logs("enterprise-trust-failed", e.message.to_s, { "session_id" => session_id, "device" => device })
        raise FireCMDException, e.message
      end
    end

    def setup_mitm(device, current_device, params)
      # Change Privoxy port
      current_privoxy_port, new_privoxy_port = PrivoxyManager.change_port(device, current_device)
      BrowserStack.logger.info "Changed Privoxy port from #{current_privoxy_port} to #{new_privoxy_port} for session #{params['automate_session_id']}"

      # Start Mitmproxy on <current_privoxy_port> with upstream set as <new_privoxy_port>
      # So, the traffic from mitmproxy goes to Privoxy.
      MitmProxy.new(device, params, @@settings).boot(current_privoxy_port, new_privoxy_port)
      BrowserStack.logger.info "Launched mitmproxy to listen on #{current_privoxy_port} with upstream as #{new_privoxy_port} for session #{params['automate_session_id']}"
    end

    def get_folder_path_details_from_s3_url(app_url)
      #  URI.path: /df51c5df4eccf7fb381d9faee9a5a93f8ecd20c4_production_29_17062020/df51c5df4eccf7fb381d9faee9a5a93f8ecd20c4_production_29_17062020.ipa
      #  URI.path with instrumented suffix: /df51c5df4eccf7fb381d9faee9a5a93f8ecd20c4_production_29_17062020/df51c5df4eccf7fb381d9faee9a5a93f8ecd20c4_production_29_17062020_instrumented_1.ipa

      # In future, the urls will be
      #  app_url: https://browserstack-userapps-stag-use1.s3.amazonaws.com/data/df51c5df4eccf7fb381d9faee9a5a93f8ecd20c4_production_29_17062020/df51c5df4eccf7fb381d9faee9a5a93f8ecd20c4_production_29_17062020.ipa
      #  URI.path: /data/df51c5df4eccf7fb381d9faee9a5a93f8ecd20c4_production_29_17062020/df51c5df4eccf7fb381d9faee9a5a93f8ecd20c4_production_29_17062020.ipa
      #  URI.path with instrumented suffix: /data/df51c5df4eccf7fb381d9faee9a5a93f8ecd20c4_production_29_17062020/df51c5df4eccf7fb381d9faee9a5a93f8ecd20c4_production_29_17062020.ipa_instrumented_1.ipa

      app_identifier_cert_name = begin
        URI.parse(app_url).path.split('/').last(2).first
      rescue
        nil
      end
      app_identifier_cert_name_instrumented = begin
        URI.parse(app_url).path.split('/').last
      rescue
        nil
      end

      app_identifier = begin
        app_identifier_cert_name.split("_").first
      rescue
        nil
      end
      cert_name = begin
        app_identifier_cert_name.split("#{app_identifier}_")[1]
      rescue
        nil
      end
      instrumented_suffix = begin
        app_identifier_cert_name_instrumented.match(/_instrumented_\d+/).to_s
      rescue
        nil
      end

      [app_identifier, cert_name, instrumented_suffix]
    end

    def check_for_download_state_file(download_folder, state)
      Dir.glob("#{download_folder}/*.#{state}")
    end

    def wait_for_uuid_folder(download_folder, retries)
      total_received_retries = retries
      while retries > 0
        BrowserStack.logger.info("[APP_NOT_FOUND] Waiting for a complete file. Retries Left: #{retries}")
        # Of the format: ["/tmp/apps/hashedid_cert/uuid.complete"]
        completed_files = check_for_download_state_file(download_folder, "complete")
        uuid = begin
          completed_files.first.split('/').last.split('.').first
        rescue
          nil
        end
        return uuid, (total_received_retries - retries) if !completed_files.nil? && !completed_files.empty?

        sleep 1
        retries -= 1
      end
      [nil, (total_received_retries - retries)]
    end

    def update_app_and_verify(device, options)
      retry_count = options[:retry_count] || 0
      # setting up timeout to 7 min in case of update app by using ios deploy
      # timeout was added to command as it was getting stuck sometimes leading to puma worker stuck

      update_timeout = 420
      update_output, update_exit_code = IdeviceUtils.update_app(device, options[:path], update_timeout)
      installed_version = nil
      3.times do
        installed_version = IdeviceUtils.app_version(device, options[:id], attempts: 1)
        break if options[:desired_version] == installed_version
      end
      is_updated = update_exit_code.to_i == 0 && options[:desired_version] == installed_version

      if !is_updated && retry_count != 0
        BrowserStack.logger.error("ios-deploy failure: Retrying. Update output - #{update_output}. Installed Version - #{installed_version}. Desired Version - #{options[:desired_version]}")
        options[:retry_count] -= 1
        update_app_and_verify(device, options)
      elsif is_updated && retry_count == 0
        BrowserStack.logger.info("ios-deploy App Update successful after retry")
        BrowserStack::Zombie.push_logs("app_update_retry_successful", "", { "session_id" => options[:session_id], "device" => device })
      elsif !is_updated
        BrowserStack.logger.error("ios-deploy failure: After Retry. Update output - #{update_output}. Installed Version - #{installed_version}. Desired Version - #{options[:desired_version]}")
        update_error = begin
          # https://github.com/browserstack/realmobile/pull/1990
          update_output[/Error.*AMDeviceSecureInstallApplication/].gsub(/Error(.*):/, '').gsub(/AMDeviceSecureInstallApplication/, '').strip
        rescue
          ""
        end
        BrowserStack::Zombie.push_logs("app_update_retry_failed", "update-#{update_exit_code}, verify-#{installed_version}-#{options[:desired_version]}, update-error #{update_error}", { "session_id" => options[:session_id], "device" => device })
        raise AppUpdateError, "ios-deploy failed: update-#{update_exit_code}, verify-#{installed_version}-#{options[:desired_version]}, update-error #{update_error}"
      else
        BrowserStack.logger.info("ios-deploy App Update successful")
      end
    end

    #Backfill Request
    # {
    #   "backfill" => {
    #     "app" => {
    #       "unsigned_app_url" => app_unsigned_url,
    #       "app_hashed_id" => app_hashed_id,
    #       "codesigner_host" => codesigner_host,
    #       "certificate" => certificate_name,
    #       "rails_host" => rails_env_host
    #     },
    #     "other_apps" => [
    #       {
    #         "unsigned_app_url" => app_unsigned_url,
    #         "app_hashed_id" => app_hashed_id,
    #         "codesigner_host" => codesigner_host,
    #         "certificate" => certificate_name,
    #         "rails_host" => rails_env_host
    #       },
    #       {
    #         "unsigned_app_url" => app_unsigned_url,
    #         "app_hashed_id" => app_hashed_id,
    #         "codesigner_host" => codesigner_host,
    #         "certificate" => certificate_name,
    #         "rails_host" => rails_env_host
    #       },
    #     ],
    #     "mid_session_install_apps" => [
    #       {
    #         "unsigned_app_url" => app_unsigned_url,
    #         "app_hashed_id" => app_hashed_id,
    #         "codesigner_host" => codesigner_host,
    #         "certificate" => certificate_name,
    #         "rails_host" => rails_env_host
    #       },
    #       {
    #         "unsigned_app_url" => app_unsigned_url,
    #         "app_hashed_id" => app_hashed_id,
    #         "codesigner_host" => codesigner_host,
    #         "certificate" => certificate_name,
    #         "rails_host" => rails_env_host
    #       },
    #     ],
    #   }
    # }

    # Returns a valid backfill object from params for the specific app type.
    def get_backfill_obj(params)
      app_type = params[:app_type]
      backfill_request_json = params["backfill"] ? JSON.parse(params["backfill"]) : nil
      # Return nil in case of no backfill object present in the params.
      return nil if !app_type || !backfill_request_json
      return backfill_request_json["app"] if app_type == 'main'
      return backfill_request_json["test_app"] if app_type == 'test'

      if app_type == 'dependent'
        dependent_apps_backfill_object = (backfill_request_json["other_apps"].to_a + backfill_request_json["mid_session_install_apps"].to_a)
        return (dependent_apps_backfill_object.empty? ? nil : dependent_apps_backfill_object)
      end
      nil
    end

    def get_app_caching_proxy_url(s3_url)
      uri = URI.parse(s3_url)
      original_host = uri.host.dup
      uri.host.replace(SUB_REGION_APP_CACHING_PROXY_HOST)
      uri.scheme = "http"
      [original_host, uri.to_s]
    end

    def download_maestro_test_suite(device, test_details, session_id, params) # rubocop:todo Metrics/CyclomaticComplexity, Metrics/AbcSize, Metrics/MethodLength, Metrics/PerceivedComplexity
      identifier = begin
        URI.parse(test_details[:url]).path.split('/').last(2).first
      rescue
        nil
      end
      raise "app_identifier or cert_name is nil from s3 url" if identifier.nil?

      download_timeout = test_details[:timeout] || 30
      download_folder = "#{APPS_DOWNLOAD_FOLDER}/#{identifier}"
      total_retries = 0
      if File.exist?(download_folder)
        folder_list = Dir.glob("#{download_folder}/*/")
        started_downloads = check_for_download_state_file(download_folder, "starting")

        if folder_list.empty?
          # if no folder is there and no .starting file then go ahead with downloading the app
          unless started_downloads.empty?
            uuid, total_retries = wait_for_uuid_folder(download_folder, 7)
            if uuid.nil?
              BrowserStack.logger.info("[#{session_id}] All the retries exhausted while waiting for download to get completed, no dir was present initially.")
              BrowserStack::Zombie.push_logs("app-download-multiple", params[:genre], { "session_id" => session_id, "data" => { "device" => device, "app_identifier": identifier, "url": "no-folder-present" } })
            else
              BrowserStack.logger.info("[#{session_id}] Folder found after total #{total_retries} when only starting file was present.")
            end
          end
        else
          uuid, total_retries = wait_for_uuid_folder(download_folder, 2)
          if uuid.nil?
            BrowserStack.logger.info("[#{session_id}] All the retries exhausted while waiting for download to get completed, dir was present.")
            BrowserStack::Zombie.push_logs("app-download-multiple", params[:genre], { "session_id" => session_id, "data" => { "device" => device, "app_identifier": identifier, "url": "folder-present" } })
          else
            BrowserStack.logger.info("[#{session_id}] Folder found after total #{total_retries} when only folder was already present but complete file not present.")
          end
        end
      end

      is_test_suite_download_required = uuid.nil? ? true : false

      if !is_test_suite_download_required
        Utils.set_event_value('test_download_time', params[:event_hash], 0)
        BrowserStack::Zombie.push_logs("app-download-reused", params[:genre], { "session_id" => session_id, "data" => { "device" => device, "app_identifier": identifier, "url": total_retries } })
        BrowserStack.logger.info("[#{session_id}] App already downloaded in #{download_folder}/#{uuid}")
      else
        FileUtils.mkdir_p(download_folder)
        uuid = SecureRandom.uuid
        FileUtils.touch("#{download_folder}/#{uuid}.starting")
        download_path = "#{download_folder}/#{uuid}.zip"

        BrowserStack.logger.info("[#{session_id}] App Downloaded for the first time to #{download_path}")

        error_tag = "Test App"
        begin
          # download app
          t1 = Time.now.to_f
          Utils.mark_event_start('test_download_time', params[:event_hash])
          headers_file = "#{APP_DOWNLOAD_HEADER_FILE_PREFIX_PATH}_#{session_id}_#{identifier}"
          download_from_s3_fallback_flag = false
          begin
            begin
              # Fallback to normal download without caching proxy if either
              # is_app_testing is False or subregion_app_caching_enabled is false
              # Both Have to be true to proceed with App Caching Proxy
              raise StandardError, "App Caching not enabled" unless
                @is_app_testing && params["subregion_app_caching_enabled"]

              original_host, proxy_url = get_app_caching_proxy_url(test_details[:url])
              BrowserStack.logger.info "[App Automate] App Caching enabled flow. " \
              "Proxy URL: #{proxy_url} | Original host: #{original_host}"

              BrowserStack::HttpUtils.download(proxy_url, download_path,
                                               { retry_count: 0,
                                                 timeout: download_timeout,
                                                 kill_in_cleanup: true,
                                                 device_id: device,
                                                 header: "X-Domain: #{original_host}",
                                                 dump_headers_to_file: headers_file,
                                                 is_app_automate: @is_app_testing })
            rescue => e
              if File.file?(headers_file)
                BrowserStack.logger.info "[App Automate] Found app download headers file. Deleting"
                File.delete(headers_file)
              end
              download_from_s3_fallback_flag = true
              BrowserStack.logger.info "[App Automate] Falling back to s3 download flow, Error: #{e.message}"
              BrowserStack::HttpUtils.download( test_details[:url], download_path, { retry_count: 1, timeout: download_timeout, kill_in_cleanup: true, device_id: device } )
            end
          rescue => e
            BrowserStack.logger.error "[App Automate] Maestro Test suite download failed, Error: #{e.message} backtrace: #{e.backtrace.join("\n")}"
            raise "#{error_tag} App Download Failed"
          end
          BrowserStack::Zombie.push_logs("app-#{params[:app_type]}-app-download-time", params[:genre], { "session_id" => session_id, "data" => { "device" => device, "app_download_time" => ((Time.now.to_f - t1) * 1000).to_i } })
          # Instrument App Caching Metrics
          if @is_app_testing && params["subregion_app_caching_enabled"]
            cache_hit_status = ""
            if !download_from_s3_fallback_flag && File.file?(headers_file)
              File.read(headers_file).split("\n").each do |line|
                next unless line.match("X-Cache-Status")

                cache_hit_status = line.match("HIT") ? "HIT" : "MISS"
                break
              end
              BrowserStack.logger.info "[App Automate] Cache Hit Status: #{cache_hit_status}"
            end
            BrowserStack::Zombie.push_logs("app-#{params[:app_type]}-app-caching-status", params[:genre],
                                           { "session_id" => session_id,
                                             "data" => { "device" => device,
                                                         "cache_hit_status" => cache_hit_status,
                                                         "app_identifier" => identifier,
                                                         "download_fallback_hit" => download_from_s3_fallback_flag } })
          end
          if File.file?(headers_file)
            BrowserStack.logger.info "[App Automate] Found app download headers file. Deleting"
            File.delete(headers_file)
          end
        rescue Exception => e
          BrowserStack.logger.error("Exception while downloading #{error_tag}: #{e.message}, backtrace: #{e.backtrace.join("\n")}")
          BrowserStack::Zombie.push_logs("ipa-download-failed", e.message.to_s, { "session_id" => session_id, "device" => device })
          influxdb_client.event(device, 'ipa-download-failed', component: 'server', subcomponent: 'download-install-app', is_error: true)
          raise FireCMDException, e.message
        ensure
          Utils.mark_event_end('test_download_time', params[:event_hash])
        end
      end

      zip_path = nil
      begin
        extract_path = "#{download_folder}/#{uuid}"
        if is_test_suite_download_required
          # unarchive
          exit_code = BrowserStack::OSUtils.unarchive(download_path, extract_path)
          raise "#{error_tag} Unarchive Failed" if exit_code.to_i != 0

          BrowserStack.logger.info("[#{session_id}] App Extracted to #{extract_path}")
          FileUtils.rm(download_path)
          BrowserStack.logger.info("[#{session_id}] Deleted App file from #{download_path}")
        end

        zip_path = Dir.glob("#{extract_path}/*")[0]

        FileUtils.touch("#{download_folder}/#{uuid}.complete")

        FileUtils.touch("#{download_folder}/#{session_id}_#{uuid}.session")
        BrowserStack.logger.info("Session file: #{download_folder}/#{session_id}_#{uuid}.session touched.")
        zip_path
      rescue => e
        BrowserStack.logger.error("Exception while installing #{error_tag}: #{e.message}, backtrace: #{e.backtrace.join("\n")}")
        # Deleting app since it should not be re-used due to install failure
        BrowserStack.logger.info("Deleting cached ipa folder!")
        FileUtils.rm_rf(download_folder)
        BrowserStack::Zombie.push_logs("ipa-install-failed", e.message.to_s, { "session_id" => session_id, "device" => device })
        raise FireCMDException, e.message
      ensure
        Utils.mark_event_end('test_install_time', params[:event_hash])
      end
    end

    def download_and_install_app(device, app_details, is_app_url, session_id, test_framework, params, mdm_enterprise_app_install = false) # rubocop:todo Metrics/CyclomaticComplexity, Metrics/AbcSize, Metrics/MethodLength, Metrics/PerceivedComplexity
      begin
        # Check if backfill is required, if yes send for codesigning and get the latest codesigned app.
        backfill_request_json = get_backfill_obj(params)
        if backfill_request_json && !params["download_retry_skip_backfill"] && !mdm_enterprise_app_install
          Utils.mark_event_start("app_backfill_time", params[:event_hash])
          codesigner_host = backfill_request_json["codesigner_host"]
          unless codesigner_host
            BrowserStack.logger.error("Codesigner host is nil")
            raise "Codesigner host is nil in backfill request"
          end
          options = {}
          options[:is_framework] = (params[:app_type] == "test" || (params['test_framework'] == 'fluttertest')).to_s
          options[:test_framework] = params['test_framework'] || ''
          options[:app_uploader_identifier] = backfill_request_json["app_uploader_identifier"] if backfill_request_json["app_uploader_identifier"].to_s != ""
          options[:user_id] = params[:user_id] if params[:user_id].to_s != ""
          options.merge!({
            backfill: true,
            unsigned_app_url: backfill_request_json["unsigned_app_url"],
            certificate: backfill_request_json["certificate"],
            app_hashed_id: backfill_request_json["app_hashed_id"],
            product: params["genre"],
            codesigner_host: codesigner_host,
            rails_host: backfill_request_json["rails_host"],
            skipSigningFrameworks: backfill_request_json["skipSigningFrameworks"],
            patchApp: backfill_request_json["patchApp"],
            appFramework: backfill_request_json["appFramework"],
            device_id: device,
            keyChainPatching: backfill_request_json["keyChainPatching"],
            is_tvos: backfill_request_json["is_tvos"],
            backfill_signed_app_url: backfill_request_json["backfill_signed_app_url"],
            browserstackInjectorVersion: backfill_request_json["browserstackInjectorVersion"],
            patched_app_download_url: backfill_request_json["patched_app_download_url"]
          })
          options[:s3_config] = backfill_request_json["s3_config"] if backfill_request_json["s3_config"].to_s != ""
          options[:secondary_codesigner_host] = backfill_request_json["secondary_codesigner_host"] if backfill_request_json["secondary_codesigner_host"].to_s != ""
          BrowserStack.logger.info("params for backfill request are - #{options}")
          app_details[:url] = backfill_app(options)
          Utils.mark_event_end("app_backfill_time", params[:event_hash])
        end
      rescue => e
        Utils.mark_event_end("app_backfill_time", params[:event_hash])
        BrowserStack.logger.error("Exception while performing backfill #{is_app_url ? 'App' : 'Test App'}: #{e.message}, backtrace: #{e.backtrace.join("\n")}")
        raise FireCMDException, e.message[0, 100]
      end

      download_timeout = app_details[:timeout] || 30
      app_identifier, cert_name, instrumented_suffix = get_folder_path_details_from_s3_url(app_details[:url])
      raise "app_identifier or cert_name is nil from s3 url" if app_identifier.nil? || cert_name.nil?
      raise "patchApp required but s3 url doesnt have instrumented version" if params["patchApp"] && (instrumented_suffix.nil? || instrumented_suffix.empty?)

      # TODO: What is app_identifier or cert_name is null
      BrowserStack.logger.info("[#{session_id}] Obtained from S3 Url - App Identifier: #{app_identifier}, cert_name: #{cert_name}, instrumented_suffix #{instrumented_suffix}")

      download_folder = "#{APPS_DOWNLOAD_FOLDER}/#{app_identifier}_#{cert_name}#{instrumented_suffix}"

      total_retries = 0
      if File.exist?(download_folder)
        folder_list = Dir.glob("#{download_folder}/*/")
        started_downloads = check_for_download_state_file(download_folder, "starting")

        if folder_list.empty?
          # if no folder is there and no .starting file then go ahead with downloading the app
          unless started_downloads.empty?
            uuid, total_retries = wait_for_uuid_folder(download_folder, 7)
            if uuid.nil?
              BrowserStack.logger.info("[#{session_id}] All the retries exhausted while waiting for download to get completed, no dir was present initially.")
              BrowserStack::Zombie.push_logs("app-download-multiple", params[:genre], { "session_id" => session_id, "data" => { "device" => device, "app_identifier": app_identifier, "url": "no-folder-present" } })
            else
              BrowserStack.logger.info("[#{session_id}] Folder found after total #{total_retries} when only starting file was present.")
            end
          end
        else
          uuid, total_retries = wait_for_uuid_folder(download_folder, 2)
          if uuid.nil?
            BrowserStack.logger.info("[#{session_id}] All the retries exhausted while waiting for download to get completed, dir was present.")
            BrowserStack::Zombie.push_logs("app-download-multiple", params[:genre], { "session_id" => session_id, "data" => { "device" => device, "app_identifier": app_identifier, "url": "folder-present" } })
          else
            BrowserStack.logger.info("[#{session_id}] Folder found after total #{total_retries} when only folder was already present but complete file not present.")
          end
        end
      end

      is_app_download_required = uuid.nil? ? true : false

      if !is_app_download_required
        case params[:app_type]
        when "main"
          Utils.set_event_value('app_download_time', params[:event_hash], 0)
        when "test"
          Utils.set_event_value('test_download_time', params[:event_hash], 0)
        end
        BrowserStack::Zombie.push_logs("app-download-reused", params[:genre], { "session_id" => session_id, "data" => { "device" => device, "app_identifier": app_identifier, "url": total_retries } })
        BrowserStack.logger.info("[#{session_id}] App already downloaded in #{download_folder}/#{uuid}")
      else
        FileUtils.mkdir_p(download_folder)
        uuid = SecureRandom.uuid
        FileUtils.touch("#{download_folder}/#{uuid}.starting")
        download_path = is_app_url ? "#{download_folder}/#{uuid}.ipa" : "#{download_folder}/#{uuid}.zip"

        BrowserStack.logger.info("[#{session_id}] App Downloaded for the first time to #{download_path}")

        error_tag = is_app_url ? "App" : "Test App"
        begin
          # download app
          t1 = Time.now.to_f
          case params[:app_type]
          when "main"
            Utils.mark_event_start('app_download_time', params[:event_hash])
          when "test"
            Utils.mark_event_start('test_download_time', params[:event_hash])
          end
          headers_file = "#{APP_DOWNLOAD_HEADER_FILE_PREFIX_PATH}_#{session_id}_#{app_identifier}"
          download_from_s3_fallback_flag = false
          begin
            begin
              # Fallback to normal download without caching proxy if either
              # is_app_testing is False or subregion_app_caching_enabled is false
              # Both Have to be true to proceed with App Caching Proxy
              raise StandardError, "App Caching not enabled" unless
                @is_app_testing && params["subregion_app_caching_enabled"]

              original_host, proxy_url = get_app_caching_proxy_url(app_details[:url])
              BrowserStack.logger.info "[App Automate] App Caching enabled flow. " \
              "Proxy URL: #{proxy_url} | Original host: #{original_host}"

              BrowserStack::HttpUtils.download(proxy_url, download_path,
                                               { retry_count: 0,
                                                 timeout: download_timeout,
                                                 kill_in_cleanup: true,
                                                 device_id: device,
                                                 header: "X-Domain: #{original_host}",
                                                 dump_headers_to_file: headers_file,
                                                 is_app_automate: @is_app_testing })
            rescue => e
              BrowserStack.logger.error("Exception while downloading #{error_tag}: #{e.message}, backtrace: #{e.backtrace.join("\n")}")
              if File.file?(headers_file)
                BrowserStack.logger.info "[App Automate] Found app download headers file. Deleting"
                File.delete(headers_file)
              end
              download_from_s3_fallback_flag = true
              BrowserStack.logger.info "[App Automate] Falling back to s3 download flow, Error: #{e.message}"
              BrowserStack::HttpUtils.download( app_details[:url], download_path, { retry_count: 1, timeout: download_timeout, kill_in_cleanup: true, device_id: device } )
            end
          rescue => e
            BrowserStack.logger.error("Exception while downloading #{error_tag}: #{e.message}, backtrace: #{e.backtrace.join("\n")}")
            raise "#{error_tag} App Download Failed"
          end
          BrowserStack::Zombie.push_logs("app-#{params[:app_type]}-app-download-time", params[:genre], { "session_id" => session_id, "data" => { "device" => device, "app_download_time" => ((Time.now.to_f - t1) * 1000).to_i } })
          # Instrument App Caching Metrics
          if @is_app_testing && params["subregion_app_caching_enabled"]
            cache_hit_status = ""
            if !download_from_s3_fallback_flag && File.file?(headers_file)
              File.read(headers_file).split("\n").each do |line|
                next unless line.match("X-Cache-Status")

                cache_hit_status = line.match("HIT") ? "HIT" : "MISS"
                break
              end
              BrowserStack.logger.info "[App Automate] Cache Hit Status: #{cache_hit_status}"
            end
            BrowserStack::Zombie.push_logs("app-#{params[:app_type]}-app-caching-status", params[:genre],
                                           { "session_id" => session_id,
                                             "data" => { "device" => device,
                                                         "cache_hit_status" => cache_hit_status,
                                                         "app_identifier" => app_identifier,
                                                         "download_fallback_hit" => download_from_s3_fallback_flag } })
          end
          if File.file?(headers_file)
            BrowserStack.logger.info "[App Automate] Found app download headers file. Deleting"
            File.delete(headers_file)
          end
        rescue Exception => e
          BrowserStack.logger.error("Exception while downloading #{error_tag}: #{e.message}, backtrace: #{e.backtrace.join("\n")}")
          BrowserStack::Zombie.push_logs("ipa-download-failed", e.message.to_s, { "session_id" => session_id, "device" => device })
          influxdb_client.event(device, 'ipa-download-failed', component: 'server', subcomponent: 'download-install-app', is_error: true)
          raise FireCMDException, e.message
        ensure
          case params[:app_type]
          when "main"
            Utils.mark_event_end('app_download_time', params[:event_hash])
          when "test"
            Utils.mark_event_end('test_download_time', params[:event_hash])
          end
        end
      end

      begin
        extract_path = "#{download_folder}/#{uuid}"
        if is_app_download_required
          # unarchive
          Utils.mark_event_start("#{params[:app_type]}_app_unarchive_time", params[:event_hash])
          exit_code = BrowserStack::OSUtils.unarchive(download_path, extract_path)
          Utils.mark_event_end("#{params[:app_type]}_app_unarchive_time", params[:event_hash])
          raise "#{error_tag} Unarchive Failed" if exit_code.to_i != 0

          BrowserStack.logger.info("[#{session_id}] App Extracted to #{extract_path}")
        end

        # app path
        app_regex = is_app_url ? "Payload/*.app" : "*.app"
        app_path = Dir.glob("#{extract_path}/#{app_regex}")[0]
        app_mobileprovision_path = "#{app_path}/embedded.mobileprovision"

        # mobileprovision
        ppuid_file = File.read("#{@@settings['config_root']}/ppuid_#{device}").split

        branch = ppuid_file[0]

        if app_details[:url].match(/_#{branch}/)
          mobileprovision_uuid = ppuid_file[2]
          mobileprovision_path = "#{@@settings['provisioning_profile_dir']}/#{mobileprovision_uuid}.mobileprovision"
          unless Utils.is_md5_checksum_equal?(mobileprovision_path, app_mobileprovision_path)
            FileUtils.rm_f(app_mobileprovision_path) # Replacing user's mobileprovision with ours
            FileUtils.cp(mobileprovision_path, app_mobileprovision_path)
          end
        end

        if is_app_download_required
          FileUtils.rm(download_path)
          BrowserStack.logger.info("[#{session_id}] Deleted App file from #{download_path}")
        end

        FileUtils.touch("#{download_folder}/#{uuid}.complete")

        FileUtils.touch("#{download_folder}/#{session_id}_#{uuid}.session")
        BrowserStack.logger.info("Session file: #{download_folder}/#{session_id}_#{uuid}.session touched.")

        unless is_app_url
          cmd_output = BrowserStack::OSUtils.execute("ls #{extract_path}/xctest_*")
          cmd_output_for_old_xcui_uploads = BrowserStack::OSUtils.execute("ls #{extract_path}/xcui_tests.log")
          output = cmd_output_for_old_xcui_uploads.to_s.empty? ? cmd_output : cmd_output_for_old_xcui_uploads
          tests = output.split("\n")
          test_params = {}

          begin
            test_params = JSON.parse(params['test_params']) if params['test_params']
            tests = [] if test_params['is_dynamic_xcuitest'].to_s == "true"
          rescue => e
            BrowserStack.logger.info("Exception raised while parsing test params:\n #{e.class.name} #{e.message}: #{e.backtrace.join("\n")}")
          end

          if params['execution_flow'] == "xctestrun"
            xctestrun_file = Dir.glob("#{extract_path}/*.xctestrun")[0]
            if xctestrun_file
              dest_xctestrun_path = XCTestrunManager.file_path(device)
              FileUtils.cp(xctestrun_file, dest_xctestrun_path)
              BrowserStack.logger.info("Copied xctestrun file from the testSuite to: #{dest_xctestrun_path}")
            end
          end

          tests.each do |log_file|
            if test_framework == "xcuitest" && log_file == "#{extract_path}/xcui_tests.log"
              app_path = Dir.glob("#{extract_path}/*.app")[0]
              xctest = File.basename(app_path).gsub("-Runner.app", "")
            else
              xctest = log_file.partition("#{extract_path}/xctest_").last.partition(".log").first
            end
            params['xctests'].push(xctest)
            FileUtils.cp(log_file, "/tmp/#{device}_xctest_#{xctest}")
            BrowserStack::OSUtils.execute("cp #{extract_path}/product_module_name /tmp/#{device}_product_module_name")
          end

          begin
            if test_params['is_dynamic_xcuitest'].to_s == "true"
              # creating an empty testlog file for dynamic xcuitests
              product_module_names = test_params['only-testing'].map { |test_name| test_name.split("/").first }.uniq
              product_module_names.each do |product_module_name|
                FileUtils.touch("/tmp/#{device}_xctest_#{product_module_name}")
                params['xctests'].push(product_module_name)
              end
            end
          rescue => e
            BrowserStack.logger.info("Exception raised while trying to check for dynamic test cases:\n #{e.class.name} #{e.message}: #{e.backtrace.join("\n")}")
          end
        end

        case params[:app_type]
        when "main"
          # only supporting appSettingsUpdate for main app
          BrowserStack::AppSettingsUtil.parse_settings_bundle(app_path, params) unless ["xcuitest", "fluttertest"].include?(params['test_framework'])
          install_time_event = 'app_install_time'
        when "test"
          install_time_event = 'test_install_time'
        end

        return app_path if mdm_enterprise_app_install

        Utils.mark_event_start(install_time_event, params[:event_hash])

        t1 = Time.now.to_f
        BrowserStack.logger.info("preserve_app_state : #{params['preserve_app_state']}") # NEED TO REMOVE THIS LATER [For Debugging]
        if DeviceState.new(device).dedicated_cleanup_file_present? && IdeviceUtils.app_installed?(device, app_details[:id], attempts: 2)
          if params[:forceReinstall].to_s == "true"
            reinstall_app(device, { path: app_path, id: app_details[:id], retry_count: 1, session_id: session_id, app_type: params[:app_type] })
            return app_path
          end
          if Gem::Version.new(params[:app_testing_app_version]) == Gem::Version.new(IdeviceUtils.app_version(device, app_details[:id], attempts: 2))
            BrowserStack.logger.info "Skip app installation as it is already installed on device and this is private cleanup policy"
            return app_path
          end
          begin
            options = { path: app_path, id: app_details[:id], retry_count: 1, session_id: session_id, app_type: params[:app_type], desired_version: params[:app_testing_app_version] }
            update_app_and_verify(device, options)
            return app_path
          rescue AppUpdateError => e
            BrowserStack.logger.error("Exception in updating app, will fallback to install #{e}")
            reinstall_app(device, { path: app_path, id: app_details[:id], retry_count: 1, session_id: session_id, app_type: params[:app_type] })
            return app_path
          end
        end
        install_app_and_verify(device, { path: app_path, id: app_details[:id], retry_count: 1, kill_in_cleanup: params["enable_async_main_app_install"], session_id: session_id, app_type: params[:app_type], install_via_ideviceinstaller: params["install_via_ideviceinstaller"] }) if !params[:skip_install] && params["preserve_app_state"].to_s != "true"

        BrowserStack::Zombie.push_logs("app-#{params[:app_type]}-app-install-time", "", { "session_id" => session_id, "data" => { "device" => device, "app_install_time" => ((Time.now.to_f - t1) * 1000).to_i } })
        app_path
      rescue => e
        BrowserStack.logger.error("Exception while installing #{error_tag}: #{e.message}, backtrace: #{e.backtrace.join("\n")}")
        # Deleting app since it should not be re-used due to install failure
        BrowserStack.logger.info("Deleting cached ipa folder!")
        FileUtils.rm_rf(download_folder)

        if @is_app_testing && params["subregion_app_caching_enabled"] && is_subregion_caching_error?(e.message.to_s)
          params = params.dup
          params["subregion_app_caching_enabled"] = false
          params["download_retry_skip_backfill"] = true # Skip backfill request download_and_install retry is hit
          BrowserStack::Zombie.push_logs("ipa-app-caching-retry-hit", e.message.to_s, { "session_id" => session_id, "device" => device })
          download_and_install_app(device, app_details, is_app_url, session_id, test_framework, params)
        else
          BrowserStack::Zombie.push_logs("ipa-install-failed", e.message.to_s, { "session_id" => session_id, "device" => device })
          if (params["resignApp"].to_s == 'false' && params["skip_platform_enterprise_flow"] != "true") && is_user_error?(e.message.to_s)
            raise FireCMDException.new(e.message, USER_ERROR_STRING)
          else
            raise FireCMDException, e.message
          end
        end
      ensure
        # Commenting this as app will can be reused while reset, and it will be deleted in cleanup
        # delete folder
        # FileUtils.rm_rf(download_folder)
        case params[:app_type]
        when "main"
          Utils.mark_event_end('app_install_time', params[:event_hash])
        when "test"
          Utils.mark_event_end('test_install_time', params[:event_hash])
        end
      end
    end

    def is_user_error?(error_message)
      (error_message =~ /install-253/) && (error_message !~ /BrowserStack Error/)
    end

    def is_subregion_caching_error?(error_message)
      SUBREGION_CACHING_RELATED_INSTALL_ERRORS.any? { |e| e =~ error_message }
    end

    def get_session_info_test_framework(test_framework)
      test_framework == "xcuitest" ? "xcui" : test_framework
    end

    def download_and_install_dependent_apps(dependent_apps, device, params, session_id, app_type = "other_apps") # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
      bundle_ids = []
      duplicate_other_apps = {}
      unique_other_apps = {}
      params.merge!({ app_type: "dependent" })
      other_apps_backfill_request = get_backfill_obj(params)
      other_apps_backfill_bundle_ids = {}
      if other_apps_backfill_request
        BrowserStack.logger.info("Backfill request for other apps #{other_apps_backfill_request}")
        other_apps_backfill_request.each do |app|
          BrowserStack.logger.info("processing the app #{app}")
          other_apps_backfill_bundle_ids[app["bundle_id"]] = app
        end
        BrowserStack.logger.info("Others/Mid Session apps bundle_ids for backfill #{other_apps_backfill_bundle_ids}")
      end

      JSON.parse(dependent_apps).each do |dependent_app| # rubocop:todo Metrics/BlockLength
        bundle_id = dependent_app["bundle_id"]
        app_hashed_id = dependent_app["hashed_id"]
        skip_app_installation = (bundle_id == params['app_testing_bundle_id']) || bundle_ids.include?(bundle_id) || app_type == "mid_session_install_apps"
        bundle_ids << bundle_id
        if other_apps_backfill_request && other_apps_backfill_bundle_ids.key?(bundle_id)
          # Backfill case
          app = other_apps_backfill_bundle_ids[bundle_id]
          codesigner_host = app["codesigner_host"]

          unless codesigner_host
            BrowserStack.logger.error("Codesigner host is nil")
            raise "Codesigner host is nil in Backfill request"
          end

          options = {
            backfill: true,
            unsigned_app_url: app["unsigned_app_url"],
            certificate: app["certificate"],
            app_hashed_id: app["app_hashed_id"],
            product: params["genre"],
            is_framework: "false", # other apps are apps installed on device additionally, so is_framework is always false
            codesigner_host: codesigner_host,
            rails_host: app["rails_host"],
            device_id: device
          }
          options[:s3_config] = app["s3_config"] if app["s3_config"].to_s != ""
          options[:app_uploader_identifier] = app["app_uploader_identifier"] if app["app_uploader_identifier"].to_s != ""
          options[:user_id] = params[:user_id] if params[:user_id].to_s != ""
          options[:secondary_codesigner_host] = app["secondary_codesigner_host"] if app["secondary_codesigner_host"].to_s != ""
          dependent_app["download_url"] = backfill_app(options)
        end
        app_details = { url: dependent_app["download_url"], id: dependent_app["bundle_id"], timeout: dependent_app["download_timeout"] }
        app_params = { app_type: "dependent", skip_install: skip_app_installation }
        app_params.merge!({ "preserve_app_state" => true }) if params["preserve_app_state"].to_s == "true"
        app_params.merge!({ "subregion_app_caching_enabled" => true }) if params["subregion_app_caching_enabled"].to_s == "true"
        other_app_download_path = download_and_install_app(device, app_details, true, session_id, false, app_params)

        if skip_app_installation
          duplicate_other_apps[app_hashed_id] = { app_path: other_app_download_path, bundle_id: bundle_id }
        else
          unique_other_apps[app_hashed_id] = { app_path: other_app_download_path, bundle_id: bundle_id }
        end
      end
      # key: duplicate_other_apps_details is same as key: downloaded_other_apps_details in mobile repo
      # key: unique_other_apps_details is same as key: installed_other_apps_details in mobile repo
      params['duplicate_other_apps_details'] = duplicate_other_apps
      params['unique_other_apps_details'] = unique_other_apps
      bundle_ids
    rescue => e
      BrowserStack.logger.error("Exception while installing other apps: #{e.message}, backtrace: #{e.backtrace.join("\n")}")
      raise FireCMDException, e.message
    end

    def session_appium_logs_device(device)
      device_config = begin
        all_devices[device]
      rescue
        nil
      end
      return nil unless device_config

      appium_port = device_config["selenium_port"]
      "#{@@settings['logging_root']}/appium_#{appium_port}.log"
    end

    def check_and_push_failure_reason(device, params) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength
      device_state = DeviceState.new(device)
      appium_log_file = session_appium_logs_device(device)
      return unless appium_log_file

      session_id = params['video_session_id']
      @is_app_testing = params[:genre].to_s.eql?('app_automate')
      kind = @is_app_testing ? "app-" : ""

      if params[:last_request] && params[:reason]
        params[:sotimeout] = {
          last_request: params[:last_request],
          reason: params[:reason],
          last_raw_log: params[:last_raw_log],
          session_limit: params[:session_limit]
        }
        begin
          # reason object returned from AppiumLogParser.check_so_timeout_reason
          # {
          #     :reason => String - of the format "#SOTIMEOUT-bucket-diagnostic_reason",
          #     :validations => Array list of validators that passed,
          #     :data => Hash of marker
          # }
          Timeout.timeout(SOTIMEOUT_PROCESS_TIMEOUT) do
            reason_obj = SoTimeoutUtil::AppiumLogParser.new("ios_njb", appium_log_file, params).check_so_timeout_reason
            kind += reason_obj[:reason].split("-").first(2).join("-")
            BrowserStack::Zombie.push_logs(kind, params[:genre], { "data" => { "s" => params[:sotimeout], "v" => reason_obj[:validations], "r" => reason_obj[:reason], "f" => reason_obj[:is_false_hub_timeout].to_s }, "session_id" => session_id, "device" => device, "url" => reason_obj[:is_false_hub_timeout].to_s, "user_id" => params[:user_id] })
            Utils.send_to_eds(params, @is_app_testing ? EdsConstants::APP_AUTOMATE_TEST_SESSIONS : EdsConstants::AUTOMATE_TEST_SESSIONS, false, "secondary_diagnostic_reason", reason_obj[:reason], true)
            Utils.send_product_stability_reason_to_eds(params, session_id, "error-socket-timeout") if @is_app_testing
          end
        rescue Timeout::Error => e
          BrowserStack.logger.info("SOTIMEOUT: Parsing timedout: #{e.message}")
          BrowserStack::Zombie.push_logs("#{kind}SOTIMEOUT-check-timeout", params[:genre], { "data" => { "s" => params[:sotimeout], "e" => e.message }, "session_id" => session_id, "device" => device, "user_id" => params[:user_id] })
        rescue => e
          BrowserStack.logger.info("SOTIMEOUT: Exception while processing failure reason: #{e.message}")
          BrowserStack::Zombie.push_logs("#{kind}SOTIMEOUT-check-failed", params[:genre], { "data" => { "s" => params[:sotimeout], "e" => e.message }, "session_id" => session_id, "device" => device, "user_id" => params[:user_id] })
        end
      end

      # this is defined in mobile_commons/utils/log_parse_util
      BrowserStack.logger.info('Parsing appium logs for stability')
      parse_appium_logs_for_stability_reason(appium_log_file, session_id, params, @@log_file)

      xcodebuild_log_file = BrowserStack::OSUtils.execute("grep \"Log file for xcodebuild\" #{appium_log_file} | tail -1 | awk '{print $NF}'").strip

      wda_signing_error_message = 'Unable to launch com.facebook.WebDriverAgentRunner.xctrunner because it has an invalid code signature'
      if File.foreach(appium_log_file).any? { |line| line.include?(wda_signing_error_message) }
        IdeviceUtils.screenshot(device, "/tmp/wda_start_error_#{device}_#{session_id}.png")
        appium_version = session_file_contents(device)['appium']
        provisioning_branch = File.readlines("#{@@settings['config_root']}/ppuid_#{device}")[0]
        zombie_data = { 'appium_version' => appium_version, 'provisioning_branch' => provisioning_branch }
        BrowserStack.logger.warn("WDA not signed start error")
        BrowserStack::Zombie.push_logs("wda-not-signed-start-error", params[:genre], "session_id" => session_id, "device" => device, 'data' => zombie_data)
        device_state.touch_minimized_cleanup_unreserved_file if device_state.minimized_cleanup_reserved_file_present?
        if !xcodebuild_log_file.empty? && File.readable?(xcodebuild_log_file)
          xcodebuild_log_file_contents = File.read(xcodebuild_log_file)
          File.open("#{@@settings['logging_root']}/xcodebuild_failures_#{device}.log", 'w+') do |file|
            file.write("\n\n#{'*' * 20} #{Time.now.utc} Xcodebuild appium failures for session_id: #{session_id} #{'*' * 20}\n\n")
            file.write(xcodebuild_log_file_contents)
          end
        end
      end

      return if xcodebuild_log_file.empty?

      # Check if there was a no sim card popup for this session
      popup = BrowserStack::OSUtils.execute("grep -C2 \"Got user testing notification with payload\" '#{xcodebuild_log_file}' | grep controllerTitle | tail -1")
      if popup.strip.match(/no sim card/i)
        BrowserStack::Zombie.push_logs("sim-card-popup-njb", params[:genre], { "session_id" => params['video_session_id'], "device" => device })
        Utils.send_product_stability_reason_to_eds(params, session_id, "error-platform-issues") if @is_app_testing
        device_state.touch_minimized_cleanup_unreserved_file if device_state.minimized_cleanup_reserved_file_present?
      end
    end

    def log_appium_memory_usage(device, params)
      process_details, status = BrowserStack::OSUtils.execute("ps aux | grep \"#{device}\" | grep \"appium\"", true)
      if status == 0
        memory_used = process_details.split[5].to_i
        BrowserStack.logger.info("Memory usage for appium: #{memory_used}KB")
        BrowserStack::Zombie.push_logs('appium_memory_usage', memory_used.to_s, { "device" => device, "session_id" => params[:automate_session_id] })
      else
        BrowserStack.logger.error("Could not find appium process for device")
      end
    rescue => e
      BrowserStack.logger.error("Error in log_appium_memory_usage: #{e.message}")
    end

    def process_device_logs(params, app_bundle_id, device_log_path)
      session_id = params[:automate_session_id]
      parse_device_logs_for_stability_reason(device_log_path, session_id, params, app_bundle_id, @@log_file)
    end

    def selenium_stop(device, params, component_breakdown = "") # rubocop:todo Metrics/AbcSize, Metrics/MethodLength, Metrics/PerceivedComplexity, Metrics/CyclomaticComplexity

      is_minified_flow = params[:reserveDevice].to_s == 'true'
      preserve_app_state = is_minified_flow && (params[:preserve_app_state].to_s == "true")

      async_process_dir = "/tmp/#{device}/#{params['automate_session_id']}"
      if is_minified_flow
        FileUtils.mkdir_p(async_process_dir) unless Dir.exist? async_process_dir
        FileUtils.touch("#{async_process_dir}/async_#{params['automate_session_id']}")
        FileUtils.touch("#{async_process_dir}/async_har_#{params['automate_session_id']}")
      end

      component_start = stop_start = Time.now
      collect_wda_process_metrics(device, params) if params[:genre] == "app_automate"
      check_and_push_failure_reason(device, params)
      log_appium_memory_usage(device, params)

      component_time = (Time.now - component_start).round(2)
      BrowserStack.logger.info "check_and_push_failure_reason done in: #{component_time}"
      component_breakdown += "check_and_push_failure_reason:#{component_time},"

      feature_usage = {}

      if params[:appiumLogs].to_s == 'true' || params[:genre] == 'playwright'
        component_start = Time.now

        process_appium_logs(device, params['automate_session_id'])
        create_upload_request_logs(device, params, session_appium_logs(params['automate_session_id']), "appium")

        component_time = (Time.now - component_start).round(2)
        BrowserStack.logger.info "process_appium_logs done in: #{component_time}"
        component_breakdown += "process_appium_logs:#{component_time},"
      else
        feature_usage["appiumLogs"] = { success: "disabled", exception: "" }
      end

      if params[:genre] == 'playwright'
        component_start = Time.now

        session_pwios_proxy_logs = BrowserStack::DeviceLogger.pwios_proxy_logs_file(device)

        create_upload_request_logs(device, params, session_pwios_proxy_logs, "device")

        component_time = (Time.now - component_start).round(2)
        BrowserStack.logger.info "session_pwios_proxy_logs done in: #{component_time}"
        component_breakdown += "session_pwios_proxy_logs:#{component_time},"
      end

      if params[:genre] == 'app_automate' && params[:devicelogs].to_s == 'true'
        component_start = Time.now

        session_device_logs = BrowserStack::DeviceLogger.app_device_logs_file(device)
        app_bundle_id = begin
          session_file_contents(device)['app_testing_bundle_id']
        rescue
          ""
        end
        process_device_logs(params, app_bundle_id, session_device_logs)
        create_upload_request_logs(device, params, session_device_logs, "device")
        is_syslog_hanged?(device, params) if params[:genre] == 'app_automate'

        component_time = (Time.now - component_start).round(2)
        BrowserStack.logger.info "session_device_logs done in: #{component_time}"
        component_breakdown += "session_device_logs:#{component_time},"
      end

      if params[:genre] == 'app_automate' && (params[:networkLogs].to_s == 'true' || params[:acceptInsecureCerts].to_s == "true")
        component_start = Time.now

        mitmdump_file = File.join(@@settings['logging_root'], "mitmproxy_#{device}.log")
        parse_mitmdump_logs(mitmdump_file, params['automate_session_id'], params)

        component_time = (Time.now - component_start).round(2)
        BrowserStack.logger.info "session_mitmdump_logs done in: #{component_time}"
        component_breakdown += "session_mitmdump_logs:#{component_time},"
      end
      device_state = DeviceState.new(device)
      if params[:genre] == 'app_automate' && device_state.aa_mcspt_measurement_enabled_file_present?
        app_bundle_id = begin
          session_file_contents(device)['app_testing_bundle_id']
        rescue
          ""
        end
        MCSPT.platform_perf_measurement('stop', 'app_automate', params['automate_session_id'], params[:user_id], [app_bundle_id], device)
        device_state.remove_aa_mcspt_measurement_enabled_file
      end

      # stop the ai proxy
      begin
        if params[:genre] != 'app_automate'
          current_device_config = device_configuration_check(device)
          appium_port = current_device_config["selenium_port"]
          ai_proxy_port = appium_port.to_i + AI_PROXY_PORT_OFFSET
          Utils.kill_ws_proxy(ai_proxy_port, current_device_config["ip"])
          BrowserStack.logger.info("[stop] calling kill ai_proxy on #{ai_proxy_port}")
        end
      rescue
        BrowserStack.logger.info("[stop] failed killing ai_proxy")
      end

      # stop the pwios proxy
      begin
        if params[:genre] == 'playwright'
          current_device_config = device_configuration_check(device)
          appium_port = current_device_config["selenium_port"]
          pwios_proxy_port = appium_port.to_i + PWIOS_PROXY_PORT_OFFSET
          Utils.kill_ws_proxy(pwios_proxy_port, current_device_config["ip"])
          BrowserStack.logger.info("[stop] calling kill pwios_proxy on #{pwios_proxy_port}")
        end
      rescue
        BrowserStack.logger.info("[stop] failed killing pwios_proxy")
      end

      component_start = Time.now

      BrowserStack::DeviceLogger.destroy(device)

      component_time = (Time.now - component_start).round(2)
      BrowserStack.logger.info "device_logger_destroy done in: #{component_time}"
      component_breakdown += "device_logger_destroy:#{component_time},"

      if params[:genre] == GENRE_APP_AUTOMATE
        syslog_parse_result_file = "#{APP_AUTOMATE_PARSER_DIR}/results_#{params[:automate_session_id]}.log"
        Utils.push_syslog_parse_results(syslog_parse_result_file, params[:automate_session_id], device)
        device_state = DeviceState.new(device)

        unless is_minified_flow
          # this is done so that in case of errors the consecutive cleanup is not a  minimized cleanup.
          device_state.remove_minimized_cleanup_reserved_file
          device_state.remove_preserve_app_state_reserved_file
          crash_logs_feature_usage = process_crash_logs(device, params)
          feature_usage.merge!(crash_logs_feature_usage) unless crash_logs_feature_usage.empty?
        end
        device_state.touch_preserve_app_state_reserved_file if preserve_app_state
        settings_screenshot_file = "/tmp/update_settings_screenshot_#{params[:automate_session_id]}.jpeg"
        Utils.create_upload_request_with_metadata(settings_screenshot_file, params, "automation_screenshot_upload", "appium", nil, @@settings) if File.exists? settings_screenshot_file
      end

      component_start = Time.now
      # Touch a file at screenshot_instrumentation_dir with content as session_id and genre
      # screenshot_instrumentation_process.rb will poll at this directory to push data around debug_screenshots
      screenshot_session_file = @@settings["screenshot_instrumentation_dir"] + "/#{params['automate_session_id']}.json"
      if params[:debug].to_s == 'true' && !File.exist?(screenshot_session_file)
        Utils.touch_file_and_create_parents(screenshot_session_file)
        Utils.write_to_file(screenshot_session_file, { genre: params[:genre], session_id: params['automate_session_id'] }.to_json)
      end

      # Write params to file.
      # Cleanup relies on these params to perform certain actions which used to happen in /stop
      # Like: Uploading Video, Uploding NetworkLogs, etc.
      File.write(stop_params_file(device), params.to_json)

      PortManager.stop_forwarding_port(device, params["youiengine_driver_port"], 'YouIEngine') if params["youiengine_driver_port"]
      PortManager.stop_forwarding_port(device, params["flutter_port"], 'Flutter') if params["flutter_port"]

      component_time = (Time.now - component_start).round(2)
      BrowserStack.logger.info "stop_forwarding_port done in: #{component_time}"
      component_breakdown += "stop_forwarding_port:#{component_time},"

      unless feature_usage.empty?
        event_hash = {
          feature_usage: feature_usage,
          hashed_id: params[:automate_session_id],
          timestamp: Time.now.to_i
        }

        event = params[:genre] == 'app_automate' ? EdsConstants::APP_AUTOMATE_TEST_SESSIONS : EdsConstants::AUTOMATE_SESSION_TIME_COMPONENTS
        Utils.send_to_eds(event_hash, event, true)
      end

      total_time = (Time.now - stop_start).round(2)
      BrowserStack.logger.info "Stop done in: #{total_time}, time_breakdown: #{component_breakdown}"
      component_breakdown += "total_time:#{total_time}"
      BrowserStack::Zombie.push_logs("ios-stop-time", total_time.to_s, { "device" => device, "session_id" => params[:automate_session_id], "data" => component_breakdown }) if is_minified_flow
      PrivoxyPushRepeater.push_privoxy_logs(params)
    end

    def process_crash_logs(device, params) # rubocop:todo Metrics/AbcSize
      BrowserStack.logger.info("Crash Report Processing")
      crash_reports_block_start_time = Time.now.to_f
      num_crash_reports = 0
      feature_usage = {}
      crash_reports_dir = Utils.get_crash_reports_dir_path(device)
      begin
        begin
          num_crash_reports, app_binary_name = DeviceManager.crash_reports(device, params[:automate_session_id])
          feature_usage["crashLogs"] = { success: "true", exception: "", num_crash_reports: 0 } if num_crash_reports == 0
        rescue => e
          feature_usage["crashLogs"] = { success: "false", exception: e.message }
          BrowserStack.logger.error("Error while fetching crash logs Error: #{e.message}")
          kind = e.message.include?("App not found") ? "idevicecrashreport-app-not-found" : "idevicecrashreport-fetch-failed"
          BrowserStack::Zombie.push_logs(kind, e.message, { "device" => device, "session_id" => params[:automate_session_id] })
        end
        BrowserStack.logger.info "Number of crash reports #{num_crash_reports}"

        if num_crash_reports > 0
          # this is defined in mobile_commons/utils/log_parse_util
          # parse crash logs for errors and push to eds/zombie
          parse_crash_logs_for_stability_reason(crash_reports_dir, params, app_binary_name) if Dir.exist?(crash_reports_dir)
          metadata = { num_crash_reports: num_crash_reports.to_s } # uploading integer was a problem in metadata to S3
          crash_report_path = "/tmp/crash_report_#{params[:automate_session_id]}.zip"
          Utils.create_upload_request_with_metadata(crash_report_path, params, "crash-report", "crash", metadata, @@settings)

          session_data = { session_id: params[:automate_session_id], auth_key: params["rails_callback_auth_key"], custom_data: { crash_logs: num_crash_reports } }
          send_post(params["rails_callback_url"], session_data, nil, true)
        end
        feature_usage
      rescue => e
        BrowserStack.logger.error("Error while processing crash logs Error: #{e.message}")
        feature_usage["crashLogs"] = { success: "false", exception: e.message }
        feature_usage
      ensure
        # deleting crash_log_dir
        FileUtils.rm_rf(crash_reports_dir)

        crash_reports_block_total_time = (Time.now.to_f - crash_reports_block_start_time).to_i
        BrowserStack.logger.info("crash_reports_block_total_time :#{crash_reports_block_total_time}")
        # end of crash logs block
      end
    end

    # rubocop:disable Metrics/AbcSize, Metrics/MethodLength, Metrics/PerceivedComplexity
    def install_app_and_verify(device, options = {})
      retry_count = options[:retry_count] || 0
      kill_in_cleanup = options[:kill_in_cleanup] || false
      install_via_ideviceinstaller = options[:install_via_ideviceinstaller] || false
      args = ["--id", device.to_s, "-b", options[:path]]
      # setting up timeout to 7 min in case of install app by using ios deploy
      # timeout was added to command as it was getting stuck sometimes leading to puma worker stuck

      install_timeout = 420
      # install_output, install_exit_code = BrowserStack::OSUtils.safe_execute(IOS_DEPLOY.to_s, ["--id", device.to_s, "-b", options[:path]], return_status = true, timeout: install_timeout)
      install_output = ""
      install_exit_code = nil
      term_code = nil

      if kill_in_cleanup
        BrowserStack.logger.info("[ASYNC FLOW] [INS & VER] begin install process")
        timeout_command_list = [TIMEOUT_CMD, "-k5", install_timeout.to_s]
        args.collect!(&:to_s) # converting all elements in an array to string

        if install_via_ideviceinstaller
          BrowserStack.logger.info("[ASYNC FLOW] [INS & VER] Using ideviceinstaller for app installation")
          command_list = [*timeout_command_list, "ideviceinstaller", "-u", device.to_s, "-i", options[:path]]
        else
          BrowserStack.logger.info("[ASYNC FLOW] [INS & VER] Using ios-deploy for app installation")
          command_list = [*timeout_command_list, IOS_DEPLOY.to_s, *args]
        end

        app_install_result_log_file = get_async_app_install_result_log_file(device)
        app_install_pid_file = get_app_install_pid_file(device)
        pid = Process.spawn(*command_list, out: app_install_result_log_file, err: app_install_result_log_file)
        Utils.write_to_file(app_install_pid_file, pid.to_s)
        begin
          Timeout.timeout(install_timeout + 2) do
            _, status = Process.wait2(pid)
            install_exit_code = status.exitstatus
            term_code = status.termsig
          end
        rescue Timeout::Error
          BrowserStack.logger.error("[ASYNC FLOW] [INS & VER] Install process timed out")
          kill_app_install_process(device)
          install_exit_code = -1
        ensure
          BrowserStack.logger.info("[ASYNC FLOW] [INS & VER] install_exit_code: #{install_exit_code}")
          if File.exist?(app_install_result_log_file)
            install_output = File.read(app_install_result_log_file)
            File.delete(app_install_result_log_file)
            BrowserStack.logger.info("[ASYNC FLOW] [INS & VER] app install output: #{install_output}")
          end

          File.delete(app_install_pid_file) if File.exist?(app_install_pid_file)
        end
      elsif install_via_ideviceinstaller
        BrowserStack.logger.info("[INS & VER] Using ideviceinstaller for app installation")
        install_output, install_exit_code = BrowserStack::OSUtils.safe_execute("ideviceinstaller", ["-u", device.to_s, "-i", options[:path]], return_status = true, timeout: install_timeout)
      else
        BrowserStack.logger.info("[INS & VER] Using ios-deploy for app installation")
        install_output, install_exit_code = BrowserStack::OSUtils.safe_execute(IOS_DEPLOY.to_s, args, return_status = true, timeout: install_timeout)
      end

      # Verify the app is installed
      verify_exit_code, verify_output = nil
      3.times do
        verify_output, verify_exit_code = BrowserStack::OSUtils.safe_execute(IOS_DEPLOY.to_s, ["--id", device.to_s, "-e", "-1", options[:id]], return_status = true)
        break if verify_exit_code.to_i == 0
      end

      is_installed = verify_exit_code.to_i == 0 &&
                    (install_exit_code.to_i == 0 || install_output.include?("Could not send a message to the device")) &&
                    term_code.nil?

      if !is_installed && retry_count != 0 && term_code.nil?
        BrowserStack.logger.error("ios-deploy failure: Retrying. Install output - #{install_output}. Verification Output - #{verify_output}")
        options[:retry_count] -= 1
        install_app_and_verify(device, options)
      elsif is_installed && retry_count == 0
        BrowserStack.logger.info("ios-deploy App Install successful after retry")
        BrowserStack::Zombie.push_logs("app_install_retry_successful", "", { "session_id" => options[:session_id], "device" => device })
        append_bundle_id_to_file(device, options[:id])
      elsif !is_installed
        handle_install_failure(device, options, install_output, install_exit_code, verify_output, verify_exit_code, term_code)
      end
    end
    # rubocop:enable Metrics/AbcSize, Metrics/MethodLength, Metrics/PerceivedComplexity

    def handle_install_failure(device, options, install_output, install_exit_code, verify_output, verify_exit_code, term_code)
      if !term_code.nil?
        install_error, bstack_errors = get_install_errors(install_output)
        BrowserStack.logger.error("ios-deploy failure: with termsig - #{term_code}. Install output - #{install_output}. Verification Output - #{verify_output}. Bstack Errors- #{bstack_errors}")
        raise "ios-deploy failed: install-#{install_exit_code},verify-#{verify_exit_code}, Install process terminated in cleanup with termsig #{term_code}"
      elsif verify_exit_code.to_i == 0 && IdeviceUtils.app_installed?(device, options[:id], attempts: 2)
        BrowserStack.logger.info("ios-deploy App Install not successful but verification succeeded with ideviceinstaller")
        BrowserStack::Zombie.push_logs("app_verify_v2_successful", "", { "session_id" => options[:session_id], "device" => device })
      else
        BrowserStack.logger.error("ios-deploy failure: After Retry. Install output - #{install_output}. Verification Output - #{verify_output}")
        install_error, bstack_errors = get_install_errors(install_output)
        BrowserStack::Zombie.push_logs("app_install_retry_failed", "install-#{install_exit_code},verify-#{verify_exit_code}, install-error #{install_error} bstack-errors #{bstack_errors}", { "session_id" => options[:session_id], "device" => device })
        install_error = "- BrowserStack Error" unless bstack_errors.empty?
        raise "ios-deploy failed: install-#{install_exit_code},verify-#{verify_exit_code}, install-error #{install_error}"
      end
    end

    def get_async_app_install_result_log_file(device)
      "/tmp/async_app_install_result_#{device}.log"
    end

    def get_app_install_pid_file(device)
      "/tmp/app_install_pid_#{device}"
    end

    def append_bundle_id_to_file(device, bundle_id)
      all_installed_apps = "#{TMP_DIR_PATH}/#{device}_installed_apps/ALL_INSTALLED_APPS"

      FileUtils.mkdir_p(all_installed_apps) unless File.directory?(all_installed_apps)
      File.open(all_installed_apps, 'a') do |file|
        file.puts(bundle_id)
      end
      BrowserStack.logger.info("Successfully appended bundle_id: #{bundle_id} to file: #{all_installed_apps}}")
    rescue => e
      BrowserStack.logger.error("Failed to append bundle_id: #{bundle_id} to file: #{all_installed_apps}. Error: #{e.message}")
    end

    def get_install_errors(install_output)
      install_error = begin
        install_output[/Error.*AMDeviceSecureInstallApplication/].gsub(/Error(.*):/, '').gsub(/AMDeviceSecureInstallApplication/, '').strip
      rescue
        ""
      end
      bstack_errors = install_output.scan(/#{BSTACK_APP_INSTALL_ERROR_PATTERNS.join('|')}/)
      [install_error, bstack_errors.join(",")]
    end

    # This function uninstalls then installs the app.
    # params:
    # device: device id
    # options :
    #   path: app_path
    #   retry_count
    #   session_id: automation session id
    def reinstall_app(device, options = {})
      retry_count = options[:retry_count] || 0
      reinstall_output, reinstall_exit_code = BrowserStack::OSUtils.safe_execute(IOS_DEPLOY.to_s, ["--id", device.to_s, "--uninstall", "--bundle", options[:path]], return_status = true)

      BrowserStack.logger.info("ReInstall output : #{reinstall_output}")

      is_reinstalled = (reinstall_exit_code.to_i == 0)

      if is_reinstalled
        if retry_count > 0
          BrowserStack.logger.info("ios-deploy App ReInstall successful after retry")
          BrowserStack::Zombie.push_logs("app_reinstall_retry_successful", "", { "session_id" => options[:session_id], "device" => device })
        else
          BrowserStack.logger.info("ios-deploy App ReInstall successful")
        end
      elsif retry_count < 1
        # Max retry count is 1 here
        BrowserStack.logger.error("ios-deploy failure: Retrying. ReInstall output - #{reinstall_output} .")
        options[:retry_count] += 1
        reinstall_app(device, options)
      else
        BrowserStack.logger.error("ios-deploy failure: After Retry. ReInstall output - #{reinstall_output}.")
        BrowserStack::Zombie.push_logs("app_reinstall_retry_failed", "reinstall-#{reinstall_exit_code}", { "session_id" => options[:session_id], "device" => device })
        influxdb_client.event(device, 'app-reinstall-retry-failed', component: 'server', subcomponent: 'reinstall-app', is_error: true)
        raise "ios-deploy failed"
      end
    end

    def enable_airplane_mode(device, params, current_device_config, device_unlocked)
      BrowserStack.logger.info("Enable Airplane mode for device #{device}")
      unless device_unlocked
        Utils.mark_event_start('fire_cmd.unlock_device', params[:event_hash])
        BrowserStack::IPhone.unlock_device(device, 20)
        Utils.mark_event_end('fire_cmd.unlock_device', params[:event_hash])
        device_unlocked = true
      end

      Utils.mark_event_start('fire_cmd.enable_airplane_mode', params[:event_hash])
      retries = 0
      2.times do
        airplane_toggle_automator = Automation::AirplaneModeAutomation.new(device)
        airplane_toggle_automator.toggle_airplane_mode # detailed info on this function in airplane_mode_automation.rb
        break if IdeviceUtils.airplane_mode_on?(device)

        retries += 1
        sleep 1
      end
      Utils.mark_event_end('fire_cmd.enable_airplane_mode', params[:event_hash])
      if IdeviceUtils.airplane_mode_on?(device)
        BrowserStack.logger.info("Airplane mode enabled successfully for device #{device} in #{retries} retries.")
        nil
      else
        BrowserStack.logger.info("Airplane mode was not enabled successfully for device #{device} in #{retries} retries.")
        raise FireCMDException, "ERROR: Airplane mode not enabled for device #{device}"
      end
    rescue => e
      BrowserStack.logger.error("Errored while enable airplane automation with message #{e.message} and backtrace #{e.backtrace.join("\n")}")
      BrowserStack::Zombie.push_logs('ios-airplane-mode-set-failed', e.message, { "device" => device, "session_id" => params["automate_session_id"] })
      { error: e.message.to_s, kind: "airplane_mode_set_failed", type: BROWSERSTACK_ERROR_STRING }
    end

    def log_device_internet_connection(device, params)
      hooter = Hooter.new
      current_device = device_configuration_check(device)
      wda = WdaClient.new(current_device["webdriver_port"].to_i)
      response = wda.device_ip
      ip_address = response["value"] || ""
      # reject any other string except ip adress and log na
      if /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/ =~ ip_address
        # when device is on wifi connection DHCP assign a ip of format 169.*.*.*
        # where as when device is on internet sharing it get an ip in range 192.168.*.*
        connection_type = (ip_address.start_with?("169.") ? "wifi" : "internet_sharing")
        hooter.send_internet_type("device_internet_onstart", { "internet_through" => connection_type, "genre" => params["genre"] || "na" }) if params[:is_app_accessibility].to_s.downcase != "true"
        push_to_cls(params, "device_internet_onstart", '', { "internet_through" => connection_type })
        BrowserStack.logger.info "Device Internet Type Successfully logged on Session Start => device : #{device} , connection_type: #{connection_type} "
      else
        hooter.send_internet_type("device_internet_onstart", { "internet_through" => "na", "genre" => params["genre"] || "na" }) if params[:is_app_accessibility].to_s.downcase != "true"
        push_to_cls(params, "device_internet_onstart", '', { "internet_through" => "na", "wda_response" => response.to_s, "ip_address" => ip_address })
        BrowserStack.logger.error "Unable to send connection type info on device at session start :  #{device} "
      end

      # This is a temporary block treated as a fallback to touch the wifi_enabled
      # file if wifi is enabled. We would be using this file to disconnect from wifi.
      idevice = BrowserStack::IPhone.new(current_device, device)
      device_version = begin
        current_device['device_version'].to_f
      rescue
        0
      end
      BrowserStack.logger.info "Checking wifi status for device #{device} with version #{device_version}"
      idevice.check_wifi_status if device_version <= 14.5
    rescue => e
      BrowserStack.logger.error "Unable to send connection type info on device at session start :  #{device}  due to error #{e.message} Trace: #{e.backtrace}"
    end

    def start_v2_video_recording(params, automate_funnel, video_sync_hash, device, wda_port)
      BrowserStack.logger.info "Starting V2 video recording for #{params['automate_session_id']}"
      Utils.mark_event_start('fire_cmd.start_video_recording', params[:event_hash])
      automate_funnel.mark_block_start('VideoRecording')

      VideoRecManager.new(device, params, wda_port).start_rec
      video_sync_hash["video_start_time"] = Time.now.to_f

      automate_funnel.mark_block_end('VideoRecording', 'success')
      Utils.mark_event_end('fire_cmd.start_video_recording', params[:event_hash])
    end

    def parse_app_automate_custom_params(params)
      params["app_automate_custom_params"] ? JSON.parse(params["app_automate_custom_params"]) : {}
    rescue => e
      BrowserStack.logger.warn("Failed to parse app_automate_custom_params as json: #{params['app_automate_custom_params']} Error: #{e}")
      {}
    end

    def check_and_enable_paint_timing(device, params)
      is_installed = BrowserStackAppHelper.browserstack_test_suite_present?(device)
      unless is_installed
        BrowserStack.logger.info "Installing browserStack test suite"
        BrowserStackAppHelper.check_and_install_browserstack_test_suite(device)
      end
      BrowserStackAppHelper.run_ui_test(device, :safari_experimental_feature_paint_timing_enable, session_id: params[:session_id])
      FileUtils.touch("#{@@settings['state_files_dir']}/paint_timing_enabled_#{device}")
      BrowserStack.logger.info "experimental feature paint timing enabled successfully"
    rescue => e
      BrowserStack::Zombie.push_logs("paint-timing-enabling-failed", "reason: #{e.message}", { "session_id" => params[:session_id], "device" => device })
      BrowserStack.logger.error("paint timing enabling failed, exception: #{e.message}, stacktrace: #{e.backtrace}")
    end

    def collect_wda_process_metrics(device, params)
      current_device = device_configuration_check(device)
      wda_port = current_device['webdriver_port']
      result = WdaClient.new(wda_port).get_metrics
      BrowserStack::Zombie.push_logs("process-metrics", nil, { "data" => result, "device" => device, "session_id" => params['automate_session_id'] })
    rescue => e
      is_wda_running = WdaClient.new(wda_port).running?
      BrowserStack.logger.error "Failed fetching WDA metrics for device #{device} on wda_port #{wda_port}, wda running : #{is_wda_running} with error #{e.message} and stacktrace #{e.backtrace}"
    end

    def remove_wda_memory_limit(device, params)
      log_type = ""
      reason = ""
      begin
        PyMobileDevice::Developer.remove_memory_limit_for_bundle_id(WDA_BUNDLE_ID, device)
        log_type = "remove-wda-memory-limit-success"
      rescue => e
        log_type = "remove-wda-memory-limit-failed"
        reason = "reason: #{e.message}"
      end

      BrowserStack::Zombie.push_logs(log_type, reason, { "session_id" => params[:session_id], "device" => device }) unless log_type.empty?
    rescue => e
      BrowserStack.logger.error("Error while removing memory limit for WDA: #{e.message}: #{e.backtrace&.join("\n")}")
    end
  end
end
