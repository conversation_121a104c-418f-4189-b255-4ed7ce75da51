require 'sinatra'
require 'net/http'
require 'logger'
require 'open3'
require 'static_conf'

require 'browserstack_logger'
require_relative '../lib/configuration'
require_relative '../config/constants'

include StaticConf

module BrowserStack
  class DeviceServer < Sinatra::Base

    configure do
      enable :logging

      set :clean_trace, true

      # configuration from common file location
      conf = Configuration.new
      @@server_config = conf.all

      @@environment = @@server_config['environment']
      @@static_conf = @@server_config['static_conf']

      # make sure the dir to serve static files is present/created
      @@templates_dir = @@server_config['templates_dir']
      @@pacfile_dir = @@server_config['pacfile_dir']
      @@log_file = "#{@@server_config['logging_root']}/device_server.log"
      FileUtils.mkdir_p @@pacfile_dir

      if @@environment == 'prod'
        #Spit stdout and stderr to a file during production
        #in case something goes wrong
        $stdout.reopen(@@log_file, 'a')
        $stdout.sync = true
        $stderr.reopen($stdout)
      end

      logger_params = { device_id: 'N/A', component: 'N/A', type: 'server', session_id: 'N/A' }
      BrowserStack.init_logger(@@log_file, logger_params)
    end

    DEVICE_FILTER_PATH = ['/pacfile']

    before do
      BrowserStack.logger.params[:device] = params["device"]
      BrowserStack.logger.params[:component] = request.path_info
      BrowserStack.logger.params[:type] = 'server'

      session_id = params[:live_session_id] || params[:automate_session_id] || params[:app_live_session_id]
      BrowserStack.logger.params[:session_id] = session_id

      BrowserStack.logger.info "New request with params: #{params}"
    end

    DEVICE_FILTER_PATH.each do |path|
      before path do
        @device = params["device"]
        halt 400 if @device.nil? || @device.empty?

        headers['Access-Control-Allow-Methods'] = 'GET, POST'
        headers['Access-Control-Allow-Origin'] = '*'
      end
    end

    register StaticConf

    def check_basic_auth
      return if authorized?

      headers['WWW-Authenticate'] = 'Basic realm="Restricted"'
      halt 401, "Not authorized\n"
    end

    def authorized?
      @auth ||= Rack::Auth::Basic::Request.new(request.env)
      @auth.provided? && @auth.basic? && (@auth.credentials == [@@static_conf['sinatra_auth_user'], @@static_conf['sinatra_auth_pass']])
    end

    get '/' do
      ":-)"
    end

    get '/pacfile' do
      device_pacfile = File.join(@@pacfile_dir, "pacfile_#{@device}")
      if File.exists?(device_pacfile)
        BrowserStack.logger.info("Serving pacfile for device: #{@device}")
        status 200
        send_file device_pacfile
      else
        file = File.join(@@templates_dir, "static_pac.pac")
        status 200
        BrowserStack.logger.error("Default pacfile is set")
        send_file file
      end
    end

    post '/cfgutil' do
      check_basic_auth
      request_body = request.body.read
      halt 400, "Need cfgutil command args" if request_body.nil? || request_body.empty?

      request_json = JSON.parse(request_body)

      output, error, status = Open3.capture3(TIMEOUT_COMMAND, request_json['timeout'].to_s, CFGUTIL_COMMAND, '--verbose', '--format', request_json['format'], *request_json['command_args'])

      { 'output' => output, 'exitstatus' => status.exitstatus, 'error' => error }.to_json
    end
  end
end
