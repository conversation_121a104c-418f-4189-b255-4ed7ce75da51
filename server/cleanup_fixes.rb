require_relative '../config/constants'
require_relative '../lib/utils/osutils'
require_relative '../lib/helpers/browserstack_app_helper'

# Contains functionality to fix cleanup errors
module CleanupFixes
  # Store errors message partial and function sym in hash for lookup func_def(:rebuild_run_xcuittest, 1)
  EXCEPTION_MESSAGE_MAP ||= {
    'An element could not be located on the page using the given search parameters.': { func: :rebuild_run_xcuittest, retries: 1 }
  }.freeze
  STATE_FILES_PATH = BrowserStack::OSUtils.ensure_path_exists("#{STATE_FILES_DIR}/cleanup_fixes/").freeze
  BrowserStack::OSUtils.execute("chown -R app:wheel #{STATE_FILES_PATH}")

  # Generates a statefile based on func name and args given
  def generate_state_filename(func_sym, *args)
    state_name = args.empty? ? func_sym : "#{func_sym}_#{args.join('_')}"
    "#{STATE_FILES_PATH}#{state_name}"
  end

  # Checks state file for handled function, statefile just contains a count of number of times function has been attempted
  # Returns false if number of retries has been exceeded, true otherwise and will increment the counter.
  def state_handler(func, reties, *args)
    file_path = generate_state_filename(func, *args)
    begin
      current_retries = File.exist?(file_path) ? Integer(File.read(file_path)) : 0
    rescue ArgumentError => e
      raise e unless e.message.include? 'invalid value for Integer'

      current_retries = 0
    end
    return false if current_retries >= reties

    current_retries += 1

    File.open(file_path, 'w') { |f| f.write(current_retries) }
    true
  end

  # Main handler function, takes exception object
  def handle_cleanup_exception(exception, *args, **kwargs)
    handled = false
    EXCEPTION_MESSAGE_MAP.each do |lookup, func|
      next unless exception.message.include? lookup.to_s

      func_sym = func[:func]
      retries = func[:retries]
      cls_func_string = "#{self.class.name}.#{func_sym}()"
      BrowserStack.logger.info("Attempting exception fix for '#{exception.message}', using #{cls_func_string}")

      retries_already_attempted = state_handler(func_sym, retries, *args)
      unless retries_already_attempted
        BrowserStack.logger.info("Not attemping fix, retries already exceeded: #{retries}; remove '#{generate_state_filename(func_sym, *args)}' to enable retries")
        break
      end

      BrowserStack.logger.info("Running #{cls_func_string}")
      handler = CleanupFixes.method(func_sym)
      handler.call(*args, **kwargs)
      handled = true
    end
    handled
  end

  # private

  # Rebuilds and runs a single xcuittest, this is used to force click popups in the
  # event of an exception
  def rebuild_run_xcuittest(device_uuid, test_case=:clean_game_center)
    BrowserStackAppHelper.build_and_install_browserstack_app(device_uuid)
    BrowserStackAppHelper.run_ui_test(device_uuid, test_case.to_sym)
  rescue NameError => e
    raise e unless e.message.include? 'uninitialized class variable @@category in BrowserStack::Zombie'
  end

  module_function :handle_cleanup_exception, :generate_state_filename, :state_handler, :rebuild_run_xcuittest
end
