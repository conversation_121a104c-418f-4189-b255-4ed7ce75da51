require 'optparse'
require 'json'
require 'browserstack_logger'
require_relative '../lib/session/xctest_session'
require_relative '../lib/app_automate/frameworks/xctestrun/xctestrun_session'
require_relative '../lib/app_automate/frameworks/xctestrun/xctest_log_split_manager'
require_relative '../lib/app_automate/frameworks/xctestrun/xctest_log_process'
require_relative '../lib/app_automate/frameworks/xctestrun/xctest_instru_log_parse'
require_relative '../lib/app_automate/frameworks/xctestrun/xctest_device_log_split'
require_relative '../lib/app_automate/frameworks/xctestrun/xctest_video_log_split'
require_relative '../lib/app_automate/frameworks/xctestrun/xctest_network_log_split'
require_relative '../lib/app_automate/frameworks/xctestrun/xctest_crash_log_split'
require_relative '../lib/configuration'
require_relative 'device_manager'
require_relative '../lib/utils/base64_utils'

if $PROGRAM_NAME == __FILE__
  @conf = Configuration.new
  @server_config = @conf.all
  BrowserStack.init_logger("/#{@server_config['logging_root']}/#{@server_config['environment']}.log")
  BrowserStack.logger.params[:component] = 'server.rb'
  BrowserStack.logger.params[:subcomponent] = 'spawn_xctest.rb'

  @options = {
    xctest_params: {},
    xctest_current_device: {},
    device_manager_settings: {}
  }

  def parser
    OptionParser.new do |opts|
      opts.on('--params xctest_params', String, 'XC_test session params') do |xc_params|
        base64_decoded = BrowserStack::Base64Utils.decode_from_base64(xc_params)
        @options[:xctest_params] = JSON.parse(base64_decoded)
      end

      opts.on('--current_device current_device_config', String, 'Device config') do |device_config|
        base64_decoded = BrowserStack::Base64Utils.decode_from_base64(device_config)
        @options[:xctest_current_device] = JSON.parse(base64_decoded)
      end

      opts.on('--settings config_json_settings', String, 'Device check config json') do |config_json_settings|
        base64_decoded = BrowserStack::Base64Utils.decode_from_base64(config_json_settings)
        @options[:device_manager_settings] = JSON.parse(base64_decoded)
      end
    rescue JSON::ParserError => e
      BrowserStack.logger.error("JSON parse error for xctest_spawn #{e.message}")
    end
  end

  begin
    parser.parse!
    if @options[:xctest_params].empty? || @options[:xctest_current_device].empty? || @options[:device_manager_settings].empty?
      puts "Too few arguments. Required: device_name, xctest-params, current_device info and device_manager global settings"
      BrowserStack.logger.error("Too few arguments for xctest_spawn")
      exit 1
    end

    device_id = @options[:xctest_params]["device"]

    BrowserStack.logger.info("Spawning xcui test for device: #{device_id}")
    # due to spawn, device_manager needs to be configured afresh
    DeviceManager.configure @options[:device_manager_settings]

    # 'execution_flow' key should determine, which class to trigger for running xctests. One of: default, xctestrun, fluttertest
    if @options[:xctest_params]['execution_flow'] == "xctestrun"
      xctestrun_session = BrowserStack::XCTestrunSession.new(@options[:xctest_params], @options[:xctest_current_device])
      xctestrun_session.setup_config(@options[:device_manager_settings])
      xctestrun_session.start
    elsif @options[:xctest_params]["test_framework"] == "maestro"
      xctest_session = BrowserStack::MaestroSession.new(@options[:xctest_params], @options[:xctest_current_device])
      xctest_session.setup_config(@options[:device_manager_settings])
      xctest_session.start
    else
      xctest_session = BrowserStack::XCTestSession.new(@options[:xctest_params], @options[:xctest_current_device])
      xctest_session.setup_config(@options[:device_manager_settings])
      xctest_session.start
    end

    # reset subcomponenet back from XCTestSession
    BrowserStack.logger.params[:subcomponent] = 'spawn_xctest.rb'
    BrowserStack.logger.info("#{@options[:xctest_params]['test_framework'].upcase} test started on device: #{device_id}")

  rescue => e
    BrowserStack.logger.error("Parser failed while starting xctest_session in xctest_spawn #{e.message}")
  end
end
