---
{{- with secret "/kv/ios/mdm" }}
mdm_auth_username: "{{ .Data.data.user }}"
mdm_auth_password: "{{ .Data.data.password }}"
{{- end }}
appium_keychain_password: "{{ with secret "/kv/ios/appium-keychain" }}{{ .Data.data.password }}{{ end }}"
fastlane_match_password: "{{ with secret "/kv/ios/fastlane-match" }}{{ .Data.data.password }}{{ end }}"
{{- with secret "/kv/infra-api" }}
infra_api_user: "{{ .Data.data.user }}"
infra_api_pass: "{{ .Data.data.password }}"
{{- end }}
{{- $subregion := key "subregion" }}
{{- with secret (print "/kv/ios/" $subregion "/apple") }}
apple_id: "{{ .Data.data.id }}"
apple_password: "{{ .Data.data.password }}"
{{- end }}
{{- with secret "/kv/inventory-api" }}
inventory_api_user: "{{ .Data.data.user }}"
inventory_api_pass: "{{ .Data.data.password }}"
{{- end }}
{{- with secret "/kv/telephony" }}
telephony_service_username: "{{ .Data.data.user }}"
telephony_service_password: "{{ .Data.data.password }}"
{{- end }}
eds_api_key: "{{ with secret "/kv/eds-api" }}{{ .Data.data.key }}{{ end }}"
{{- with secret "/kv/adhoc-repeater-session" }}
adhoc_repeater_session_user: "{{ .Data.data.user }}"
adhoc_repeater_session_password: "{{ .Data.data.password }}"
{{- end }}
