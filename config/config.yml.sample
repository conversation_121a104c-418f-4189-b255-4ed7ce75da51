environment: "development"
proxy_whitelisted_hosts:
  - mdm.bsstag.com
  - defaulthost

appium_roots:
  {
    "1.6.5": "/usr/local/.browserstack/appium_1.6.5_bstack",
    "1.7.0": "/usr/local/.browserstack/appium_1.7.0_bstack",
    "1.7.1": "/usr/local/.browserstack/appium_1.7.1_bstack",
    "1.7.2": "/usr/local/.browserstack/appium_1.7.2_bstack",
    "1.8.0": "/usr/local/.browserstack/appium_1.8.0_bstack"
  }
default_appium_version: "1.7.0"

send_alerts_to:
  - someone

# MDM
mdm_server_url: "http://localhost:8080"
profile_check_limit: 5

# Redis config
redis_url: "127.0.0.1"

webdriver_agent_project_paths:
  {
    "1.6.5": "/usr/local/.browserstack/appium_1.6.5_bstack/node_modules/appium-xcuitest-driver/WebDriverAgent/WebDriverAgent.xcodeproj",
    "1.7.0": "/usr/local/.browserstack/appium_1.7.0_bstack/node_modules/appium-xcuitest-driver/WebDriverAgent/WebDriverAgent.xcodeproj",
    "1.7.1": "/usr/local/.browserstack/appium_1.7.1_bstack/node_modules/appium-xcuitest-driver/WebDriverAgent/WebDriverAgent.xcodeproj",
    "1.7.2": "/usr/local/.browserstack/appium_1.7.2_bstack/node_modules/appium-xcuitest-driver/WebDriverAgent/WebDriverAgent.xcodeproj",
    "1.8.0": "/usr/local/.browserstack/appium_1.8.0_bstack/node_modules/appium-xcuitest-driver/WebDriverAgent/WebDriverAgent.xcodeproj"
  }

app_updates:
  - "com.apple.videos"
  - "com.apple.tv"

known_apps:
  - "com.browserstack.enterpriseDummy"
  - "com.browserstack.Launcher"
  - "com.browserstack.Redirect"
  - "com.apple.test.WebDriverAgentRunner-Runner"
  - "com.google.chrome.ios"
  - "com.apple.TestFlight"
  - "com.browserstack.app"
  - "com.apple.test.BrowserStackUITests-Runner"
  - "com.browserstack.BrowserStackUITests.xctrunner"

known_app_display_names:
  - "App Store"
  - "Phone"
  - "Safari"
  - "Settings"
  - "Camera"
  - "Photos"
  - "PhotoBooth"
  - "Photo Booth"
  - "enterpriseDummy"
  - "TestFlight"
  - "Launcher"
  - "Redirect"
  - "Chrome"
  - "QRCode"
  - "WebDriverAgentRunner-Runner"
  - "WebDriverAgentRunner"
  - "BrowserStack"
  - "BrowserStackUITests-Runner"

developer_disk_images:
  -  "11.1"
  -  "11.2"
  -  "11.3"
  -  "11.4"
  -  "12.0"

download_endpoint: "194.165.161.241"

bluetooth_check_disabled: true

platform_category: 'ios_njb_11'
machine_env: 'staging'

# This is not the actual DC GPS location but an approximate location,
# Just so we are in the same timezone
dc_gps_location:
  latitude: 0
  longitude: 0

# idevice_utils config, Possible values can be found at idevice_utils.rb::IDEVICE_CONNECTION_TYPES
idevice_connection_type: 'usb'