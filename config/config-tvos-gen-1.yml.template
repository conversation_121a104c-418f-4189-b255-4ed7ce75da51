environment: "{{ key "environment" }}"
proxy_whitelisted_hosts:
{{- range key "ios/proxy-whitelisted-hosts" | split "\n" }}
    - "{{ . }}"
{{- end }}

appium_roots:
  {
    "*******.12.2": "/usr/local/.browserstack/appium_2.0.0_bstack"
  }

default_appium_version: "*******.12.2"

send_alerts_to:
{{- range key "ios/send-alerts-to" | split "\n" }}
  - {{ . }}
{{- end }}

mdm_server_url: "{{ key "ios/mdm-server-url" }}"
profile_check_limit: {{ key "ios/profile-check-limit" }}

redis_url: "{{ key "ios/redis-url" }}"

webdriver_agent_project_paths:
  {
    "*******.12.2": "/usr/local/.browserstack/appium_2.0.0_bstack/packages/appium/xcuitest/4.12.2/node_modules/appium-xcuitest-driver/node_modules/appium-webdriveragent/WebDriverAgent.xcodeproj"
  }

app_updates:
  - "com.apple.videos"
  - "com.apple.tv"

known_apps:
  - "com.browserstack.enterpriseDummy"
  - "com.google.chrome.ios"
  - "com.browserstack.Launcher"
  - "com.browserstack.Redirect"
  - "com.apple.TestFlight"
  - "com.browserstack.app"
  - "com.browserstack.BrowserStackUITests.xctrunner"
  - "com.facebook.WebDriverAgentRunner.xctrunner"

known_app_display_names:
  - "App Store"
  - "Phone"
  - "Safari"
  - "Settings"
  - "Camera"
  - "Photos"
  - "PhotoBooth"
  - "Photo Booth"
  - "enterpriseDummy"
  - "TestFlight"
  - "Launcher"
  - "Redirect"
  - "Chrome"
  - "QRCode"
  - "WebDriverAgentRunner-Runner"
  - "WebDriverAgentRunner"
  - "BrowserStack"
  - "BrowserStackUITests-Runner"

developer_disk_images:
  - "16.0"
  - "16.1"

developer_disk_image_platform: "AppleTVOS.platform"

download_endpoint: "{{ key "download-endpoint" }}"
wda_source: "{{ key "wda-source" }}"

platform_category: "smart_tv_mac_mini_16"
machine_env: "{{ key "environment" }}"
bluetooth_check_disabled: true

# This is not the actual DC GPS location but an approximate location,
# Just so we are in the same timezone
dc_gps_location:
{{ key "ios/dc-gps-location" | parseJSON | toYAML | indent 2 }}

# idevice_utils config
idevice_connection_type: 'network'

default_platform: 'tvos'