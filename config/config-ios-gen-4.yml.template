environment: "{{ key "environment" }}"
proxy_whitelisted_hosts:
{{- range key "ios/proxy-whitelisted-hosts" | split "\n" }}
    - "{{ . }}"
{{- end }}

appium_roots:
  {
    "1.17.0": "/usr/local/.browserstack/appium_1.17.0_bstack",
    "1.18.0": "/usr/local/.browserstack/appium_1.18.0_bstack",
    "1.19.1": "/usr/local/.browserstack/appium_1.19.1_bstack",
    "1.20.2": "/usr/local/.browserstack/appium_1.20.2_bstack",
    "1.21.0": "/usr/local/.browserstack/appium_1.21.0_bstack",
    "1.22.0": "/usr/local/.browserstack/appium_1.22.0_bstack",
    "*******.4.2": "/usr/local/.browserstack/appium_2.0.0_bstack",
    "*******.12.2": "/usr/local/.browserstack/appium_2.0.0_bstack",
    "*******.21.6": "/usr/local/.browserstack/appium_2.0.0_bstack",
    "*******.32.23": "/usr/local/.browserstack/appium_2.0.1_bstack",
    "*******.14.0": "/usr/local/.browserstack/appium_2.4.1_bstack",
    "*******.16.2": "/usr/local/.browserstack/appium_2.6.0_bstack",
    "********.28.3": "/usr/local/.browserstack/appium_2.12.1_bstack",
    "********.3.3": "/usr/local/.browserstack/appium_2.15.0_bstack"
  }

default_appium_version: "1.21.0"
default_wda_version: "********.3.3"

send_alerts_to:
{{- range key "ios/send-alerts-to" | split "\n" }}
  - {{ . }}
{{- end }}

mdm_server_url: "{{ key "ios/mdm-server-url" }}"
profile_check_limit: {{ key "ios/profile-check-limit" }}

redis_url: "{{ key "ios/redis-url" }}"
mdm_redis_port: "{{ key "ios/mdm-redis-port" }}"

webdriver_agent_project_paths:
  {
    "1.17.0": "/usr/local/.browserstack/appium_1.17.0_bstack/node_modules/appium-webdriveragent/WebDriverAgent.xcodeproj",
    "1.18.0": "/usr/local/.browserstack/appium_1.18.0_bstack/node_modules/appium-webdriveragent/WebDriverAgent.xcodeproj",
    "1.19.1": "/usr/local/.browserstack/appium_1.19.1_bstack/node_modules/appium-webdriveragent/WebDriverAgent.xcodeproj",
    "1.20.2": "/usr/local/.browserstack/appium_1.20.2_bstack/node_modules/appium-webdriveragent/WebDriverAgent.xcodeproj",
    "1.21.0": "/usr/local/.browserstack/appium_1.21.0_bstack/node_modules/appium-webdriveragent/WebDriverAgent.xcodeproj",
    "1.22.0": "/usr/local/.browserstack/appium_1.22.0_bstack/node_modules/appium-webdriveragent/WebDriverAgent.xcodeproj",
    "*******.4.2": "/usr/local/.browserstack/appium_2.0.0_bstack/packages/appium/xcuitest/4.4.2/node_modules/appium-xcuitest-driver/node_modules/appium-webdriveragent/WebDriverAgent.xcodeproj",
    "*******.12.2": "/usr/local/.browserstack/appium_2.0.0_bstack/packages/appium/xcuitest/4.12.2/node_modules/appium-xcuitest-driver/node_modules/appium-webdriveragent/WebDriverAgent.xcodeproj",
    "*******.21.6": "/usr/local/.browserstack/appium_2.0.0_bstack/packages/appium/xcuitest/4.21.6/node_modules/appium-xcuitest-driver/node_modules/appium-webdriveragent/WebDriverAgent.xcodeproj",
    "*******.32.23": "/usr/local/.browserstack/appium_2.0.1_bstack/packages/appium/xcuitest/4.32.23/node_modules/appium-xcuitest-driver/node_modules/appium-webdriveragent/WebDriverAgent.xcodeproj",
    "*******.14.0": "/usr/local/.browserstack/appium_2.4.1_bstack/packages/appium/xcuitest/5.14.0/node_modules/appium-xcuitest-driver/node_modules/appium-webdriveragent/WebDriverAgent.xcodeproj",
    "*******.16.2": "/usr/local/.browserstack/appium_2.6.0_bstack/packages/appium/xcuitest/7.16.2/node_modules/appium-xcuitest-driver/node_modules/appium-webdriveragent/WebDriverAgent.xcodeproj",
    "********.28.3": "/usr/local/.browserstack/appium_2.12.1_bstack/packages/appium/xcuitest/7.28.3/node_modules/appium-xcuitest-driver/node_modules/appium-webdriveragent/WebDriverAgent.xcodeproj",
    "********.3.3": "/usr/local/.browserstack/appium_2.15.0_bstack/packages/appium/xcuitest/8.3.3/node_modules/appium-xcuitest-driver/node_modules/appium-webdriveragent/WebDriverAgent.xcodeproj"
  }

app_updates:
  - "com.apple.videos"
  - "com.apple.tv"

known_apps:
  - "com.browserstack.enterpriseDummy"
  - "com.google.chrome.ios"
  - "com.browserstack.Launcher"
  - "com.browserstack.Redirect"
  - "com.apple.TestFlight"
  - "com.browserstack.app"
  - "com.browserstack.BrowserStackUITests.xctrunner"
  - "com.facebook.WebDriverAgentRunner.xctrunner"
  - "browserstack.chromium"

known_app_display_names:
  - "App Store"
  - "Phone"
  - "Safari"
  - "Settings"
  - "Camera"
  - "Photos"
  - "PhotoBooth"
  - "Photo Booth"
  - "enterpriseDummy"
  - "TestFlight"
  - "Launcher"
  - "Redirect"
  - "Chrome"
  - "QRCode"
  - "WebDriverAgentRunner-Runner"
  - "WebDriverAgentRunner"
  - "BrowserStack"
  - "BrowserStackUITests-Runner"
  - "Chromium"

developer_disk_images:
  -  "15.0"
  -  "15.1"
  -  "15.2"
  -  "15.4"
  -  "15.5"
  -  "15.6"

download_endpoint: "{{ key "download-endpoint" }}"
wda_source: "{{ key "wda-source" }}"

platform_category: "ios_njb_15"
machine_env: "{{ key "environment" }}"
bluetooth_check_disabled: true

# This is not the actual DC GPS location but an approximate location,
# Just so we are in the same timezone
dc_gps_location:
{{ key "ios/dc-gps-location" | parseJSON | toYAML | indent 2 }}
