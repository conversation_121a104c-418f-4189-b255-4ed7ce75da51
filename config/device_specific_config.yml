# Refer to https://www.theiphonewiki.com/wiki/Models for
# finding the Product type of each new device that needs to be added.

# iPhone SE
iPhone8,4:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 180.0, y: 570.0 }
      end: { x: 180.0, y: 450.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 380
    height: 672

#iPhone 6
iPhone7,2:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 180.0, y: 670.0 }
      end: { x: 180.0, y: 600.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 380
    height: 672

# iPhone 7
iPhone9,1:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 180.0, y: 670.0 }
      end: { x: 180.0, y: 600.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 380
    height: 672

# iPhone 7
iPhone9,3:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 180.0, y: 670.0 }
      end: { x: 180.0, y: 600.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 380
    height: 672

# iPhone 8
iPhone10,1:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 180.0, y: 670.0 }
      end: { x: 180.0, y: 600.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 500
    height: 890

# iPhone 8
iPhone10,4:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 180.0, y: 670.0 }
      end: { x: 180.0, y: 600.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 470
    height: 836

# iPhone 8 Plus
iPhone10,2:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 180.0, y: 740.0 }
      end: { x: 180.0, y: 650.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 414
    height: 736

# iPhone 8 Plus
iPhone10,5:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 180.0, y: 740.0 }
      end: { x: 180.0, y: 650.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 414
    height: 736

# iPhone X
iPhone10,3:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 360.0, y: 0.0 }
      end: { x: 360.0, y: 50.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 380
    height: 824

# iPhone X
iPhone10,6:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 360.0, y: 0.0 }
      end: { x: 360.0, y: 50.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 380
    height: 824

# iPhone XS
iPhone11,2:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 360.0, y: 0.0 }
      end: { x: 360.0, y: 50.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 380
    height: 824

# iPhone XS Max
iPhone11,6:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 360.0, y: 0.0 }
      end: { x: 360.0, y: 50.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 380
    height: 824

# iPhone XR
iPhone11,8:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 360.0, y: 0.0 }
      end: { x: 360.0, y: 50.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 380
    height: 824

#iPhone 11
iPhone12,1:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 360.0, y: 0.0 }
      end: { x: 360.0, y: 50.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 414
    height: 896

# iPhone 11 Pro
iPhone12,3:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 360.0, y: 0.0 }
      end: { x: 360.0, y: 50.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 380
    height: 824

# iPhone 11 Pro Max
iPhone12,5:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 360.0, y: 0.0 }
      end: { x: 360.0, y: 50.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 420
    height: 900

# iPhone 12 Mini
iPhone13,1:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 360.0, y: 0.0 }
      end: { x: 360.0, y: 50.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 412
    height: 893

# iPhone 12
iPhone13,2:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 360.0, y: 0.0 }
      end: { x: 360.0, y: 50.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 414
    height: 896

# iPhone 12 Pro
iPhone13,3:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 360.0, y: 0.0 }
      end: { x: 360.0, y: 50.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 380
    height: 824

# iPhone 12 Pro Max
iPhone13,4:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 360.0, y: 0.0 }
      end: { x: 360.0, y: 50.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 420
    height: 900

# iPhone SE 2020
iPhone12,8:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 180.0, y: 670.0 }
      end: { x: 180.0, y: 600.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 380
    height: 672

# iPhone SE 2022
iPhone14,6:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 180.0, y: 670.0 }
      end: { x: 180.0, y: 600.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 380
    height: 672

# iPhone 13
iPhone14,5:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 360.0, y: 0.0 }
      end: { x: 360.0, y: 50.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 414
    height: 896

# iPhone 13 Mini
iPhone14,4:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 360.0, y: 0.0 }
      end: { x: 360.0, y: 50.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 412
    height: 893

# iPhone 13 Pro Max
iPhone14,3:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 360.0, y: 0.0 }
      end: { x: 360.0, y: 50.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 420
    height: 900

# iPhone 13 Pro
iPhone14,2:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 360.0, y: 0.0 }
      end: { x: 360.0, y: 50.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 380
    height: 824

# iPhone 14
iPhone14,7:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 360.0, y: 0.0 }
      end: { x: 360.0, y: 50.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 414
    height: 896

# iPhone 14 Plus
iPhone14,8:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 360.0, y: 0.0 }
      end: { x: 360.0, y: 50.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 420
    height: 900

# iPhone 14 Pro Max
iPhone15,3:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 360.0, y: 0.0 }
      end: { x: 360.0, y: 50.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 414
    height: 896

# iPhone 14 Pro
iPhone15,2:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 360.0, y: 0.0 }
      end: { x: 360.0, y: 50.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 414
    height: 896

# iPhone 15
iPhone15,4:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 360.0, y: 0.0 }
      end: { x: 360.0, y: 50.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 414
    height: 896

# iPhone 15 Plus
iPhone15,5:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 360.0, y: 0.0 }
      end: { x: 360.0, y: 50.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 420
    height: 900

# iPhone 15 Pro Max
iPhone16,2:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 360.0, y: 0.0 }
      end: { x: 360.0, y: 50.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 414
    height: 896

# iPhone 15 Pro
iPhone16,1:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 360.0, y: 0.0 }
      end: { x: 360.0, y: 50.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 414
    height: 896

# iPhone 16
iPhone17,3:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 360.0, y: 0.0 }
      end: { x: 360.0, y: 50.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 414
    height: 896

# iPhone 16 Pro
iPhone17,1:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 360.0, y: 0.0 }
      end: { x: 360.0, y: 50.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 414
    height: 896

# iPhone 16 Plus
iPhone17,4:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 360.0, y: 0.0 }
      end: { x: 360.0, y: 50.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 420
    height: 900

# iPhone 16 Pro Max
iPhone17,2:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 360.0, y: 0.0 }
      end: { x: 360.0, y: 50.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 414
    height: 896

# iPad Pro
iPad6,3:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 180.0, y: 1030.0 }
      end: { x: 180.0, y: 900.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 768
    height: 1024

# iPad Pro
iPad6,4:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 180.0, y: 1030.0 }
      end: { x: 180.0, y: 900.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 768
    height: 1024

# iPad 5th
iPad6,11:
  control_centre:
    show:
    - os_version: "15.0"
      start: { x: 1500.0, y: 10.0 }
      end: { x: 1500.0, y: 400.0 }
    - os_version: "*"
      start: { x: 180.0, y: 1030.0 }
      end: { x: 180.0, y: 900.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 768
    height: 1024

# iPad 5th
iPad6,12:
  control_centre:
    show:
    - os_version: "15.0"
      start: { x: 1500.0, y: 10.0 }
      end: { x: 1500.0, y: 400.0 }
    - os_version: "*"
      start: { x: 180.0, y: 1030.0 }
      end: { x: 180.0, y: 900.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 768
    height: 1024

# iPad 6th
iPad7,6:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 180.0, y: 1030.0 }
      end: { x: 180.0, y: 900.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 768
    height: 1024

# iPad 7th
iPad7,11:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 1610.0, y: 0.0 }
      end: { x: 500.0, y: 900.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 768
    height: 1024

# iPad 7th
iPad7,12:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 1610.0, y: 0.0 }
      end: { x: 500.0, y: 900.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 768
    height: 1024

# iPad Mini 2
iPad4,4:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 180.0, y: 1030.0 }
      end: { x: 180.0, y: 900.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 768
    height: 1024

# iPhone 6S
iPhone8,1:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 180.0, y: 670.0 }
      end: { x: 180.0, y: 600.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 380
    height: 672

# iPhone 6S Plus
iPhone8,2:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 180.0, y: 740.0 }
      end: { x: 180.0, y: 650.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 414
    height: 736

# iPad Pro 12.9 inch
iPad7,2:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 660.0, y: 1362.0 }
      end: { x: 660.0, y: 964.0 }
    dismiss:
    - os_version: "*"
      x: 474.0
      y: 1075.0
  streaming_params:
    width: 1024
    height: 1366

#iPad mini4
iPad5,2:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 180.0, y: 1030.0 }
      end: { x: 180.0, y: 900.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 768
    height: 1024

# iPad Pro 11 inch, using resolution/2.4, anything higher is breaking the stream
iPad8,3:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 760.0, y: 10.0 }
      end: { x: 760.0, y: 600.0 }
    dismiss:
    - os_version: "*"
      x: 80.0
      y: 80.0
  streaming_params:
    width: 695
    height: 995

# iPad Pro 12.9 inch 3rd generation, resolution/2
iPad8,7:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 1020.0, y: 10.0 }
      end: { x: 1020.0, y: 200.0 }
    dismiss:
    - os_version: "*"
      x: 80.0
      y: 80.0
  streaming_params:
    width: 1024
    height: 1366

#iPad Mini 2019
iPad11,2:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 1020.0, y: 10.0 }
      end: { x: 1020.0, y: 200.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 768
    height: 1024

#iPad Air 4
iPad13,1:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 1020.0, y: 10.0 }
      end: { x: 1020.0, y: 200.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 768
    height: 1105

#iPad Air 4
iPad13,2:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 1020.0, y: 10.0 }
      end: { x: 1020.0, y: 200.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 768
    height: 1105

#iPad Air 2019
iPad11,3:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 1020.0, y: 10.0 }
      end: { x: 1020.0, y: 200.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 768
    height: 1024

#iPad Air 2019
iPad11,4:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 1020.0, y: 10.0 }
      end: { x: 1020.0, y: 200.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 768
    height: 1024

# iPad 8th gen 2020 10.2 inch
iPad11,6:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 1020.0, y: 10.0 }
      end: { x: 1020.0, y: 200.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 768
    height: 1024

# iPad 8th gen 2020 10.2 inch
iPad11,7:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 1020.0, y: 10.0 }
      end: { x: 1020.0, y: 200.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 768
    height: 1024

# iPad Pro 12.9 inch 2020, resolution/2 otherwise streaming breaks
iPad8,11:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 1020.0, y: 10.0 }
      end: { x: 1020.0, y: 200.0 }
    dismiss:
    - os_version: "*"
      x: 80.0
      y: 80.0
  streaming_params:
    width: 1024
    height: 1366

# iPad Pro 12.9 inch 2020, resolution/2 otherwise streaming breaks
iPad8,12:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 1020.0, y: 10.0 }
      end: { x: 1020.0, y: 200.0 }
    dismiss:
    - os_version: "*"
      x: 80.0
      y: 80.0
  streaming_params:
    width: 1024
    height: 1366

# iPad Pro 11 inch 2020, using resolution/2.4, anything higher is breaking the stream
iPad8,10:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 760.0, y: 10.0 }
      end: { x: 760.0, y: 600.0 }
    dismiss:
    - os_version: "*"
      x: 80.0
      y: 80.0
  streaming_params:
    width: 695
    height: 995

# iPad Pro 11 inch 3rd Generation, 2021
iPad13,4:
  control_centre:
    show:
    - os_version: "18"
      start: { x: 760.0, y: 10.0 }
      end: { x: 760.0, y: 600.0 }
      tap: { x: 807.0, y: 342.0 } # for iOS 18, click here too
    - os_version: "*"
      start: { x: 760.0, y: 10.0 }
      end: { x: 760.0, y: 600.0 }
    dismiss:
    - os_version: "*"
      x: 80.0
      y: 80.0
  streaming_params:
    width: 695
    height: 995

# iPad Pro 11 inch 3rd Generation, 2021
iPad13,5:
  control_centre:
    show:
    - os_version: "18"
      start: { x: 760.0, y: 10.0 }
      end: { x: 760.0, y: 600.0 }
      tap: { x: 807.0, y: 342.0 }
    - os_version: "*"
      start: { x: 760.0, y: 10.0 }
      end: { x: 760.0, y: 600.0 }
    dismiss:
    - os_version: "*"
      x: 80.0
      y: 80.0
  streaming_params:
    width: 695
    height: 995

# iPad Pro 11 inch 3rd Generation, 2021
iPad13,6:
  control_centre:
    show:
    - os_version: "18"
      start: { x: 760.0, y: 10.0 }
      end: { x: 760.0, y: 600.0 }
      tap: { x: 807.0, y: 342.0 }
    - os_version: "*"
      start: { x: 760.0, y: 10.0 }
      end: { x: 760.0, y: 600.0 }
    dismiss:
    - os_version: "*"
      x: 80.0
      y: 80.0
  streaming_params:
    width: 695
    height: 995

# iPad Pro 11 inch 3rd Generation, 2021
iPad13,7:
  control_centre:
    show:
    - os_version: "18"
      start: { x: 760.0, y: 10.0 }
      end: { x: 760.0, y: 600.0 }
      tap: { x: 807.0, y: 342.0 }
    - os_version: "*"
      start: { x: 760.0, y: 10.0 }
      end: { x: 760.0, y: 600.0 }
    dismiss:
    - os_version: "*"
      x: 80.0
      y: 80.0
  streaming_params:
    width: 695
    height: 995

# iPad Pro 11 inch, 2022
iPad14,3:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 760.0, y: 10.0 }
      end: { x: 760.0, y: 600.0 }
    dismiss:
    - os_version: "*"
      x: 80.0
      y: 80.0
  streaming_params:
    width: 695
    height: 995

# iPad Pro 11 inch, 2022
iPad14,4:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 760.0, y: 10.0 }
      end: { x: 760.0, y: 600.0 }
    dismiss:
    - os_version: "*"
      x: 80.0
      y: 80.0
  streaming_params:
    width: 695
    height: 995

# iPad Pro 12.9 inch 2021, resolution/2 otherwise streaming breaks
iPad13,8:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 1020.0, y: 10.0 }
      end: { x: 1020.0, y: 200.0 }
    dismiss:
    - os_version: "*"
      x: 80.0
      y: 80.0
  streaming_params:
    width: 746
    height: 995
iPad13,9:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 1020.0, y: 10.0 }
      end: { x: 1020.0, y: 200.0 }
    dismiss:
    - os_version: "*"
      x: 80.0
      y: 80.0
  streaming_params:
    width: 746
    height: 995
iPad13,10:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 1020.0, y: 10.0 }
      end: { x: 1020.0, y: 200.0 }
    dismiss:
    - os_version: "*"
      x: 80.0
      y: 80.0
  streaming_params:
    width: 746
    height: 995
iPad13,11:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 1020.0, y: 10.0 }
      end: { x: 1020.0, y: 200.0 }
    dismiss:
    - os_version: "*"
      x: 80.0
      y: 80.0
  streaming_params:
    width: 746
    height: 995

# iPad 9th Gen 2021
iPad12,1:
  control_centre:
    show:
    - os_version: "18"
      start: { x: 1020.0, y: 10.0 }
      end: { x: 1020.0, y: 200.0 }
      tap: { x: 784.0, y: 387.0 }
    - os_version: "*"
      start: { x: 1020.0, y: 10.0 }
      end: { x: 1020.0, y: 200.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 768
    height: 1024
iPad12,2:
  control_centre:
    show:
    - os_version: "18"
      start: { x: 1020.0, y: 10.0 }
      end: { x: 1020.0, y: 200.0 }
      tap: { x: 784.0, y: 387.0 }
    - os_version: "*"
      start: { x: 1020.0, y: 10.0 }
      end: { x: 1020.0, y: 200.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 768
    height: 1024
# iPad Mini 2021
iPad14,1:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 1020.0, y: 10.0 }
      end: { x: 1020.0, y: 200.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 744
    height: 1133
iPad14,2:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 1020.0, y: 10.0 }
      end: { x: 1020.0, y: 200.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 744
    height: 1133
# iPad Pro 12.9 2022
iPad14,5:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 1020.0, y: 10.0 }
      end: { x: 1020.0, y: 200.0 }
    dismiss:
    - os_version: "*"
      x: 80.0
      y: 80.0
  streaming_params:
    width: 746
    height: 995
iPad14,6:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 1020.0, y: 10.0 }
      end: { x: 1020.0, y: 200.0 }
    dismiss:
    - os_version: "*"
      x: 80.0
      y: 80.0
  streaming_params:
    width: 746
    height: 995
iPad13,16:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 1020.0, y: 10.0 }
      end: { x: 1020.0, y: 200.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 768
    height: 1100
iPad13,17:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 1020.0, y: 10.0 }
      end: { x: 1020.0, y: 200.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 768
    height: 1100
iPad13,18:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 1020.0, y: 10.0 }
      end: { x: 1020.0, y: 200.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 768
    height: 1100
iPad13,19:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 1020.0, y: 10.0 }
      end: { x: 1020.0, y: 200.0 }
    dismiss:
    - os_version: "*"
      x: 180.0
      y: 10.0
  streaming_params:
    width: 768
    height: 1100
# iPad Pro 11 inch, 2024
iPad16,3:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 760.0, y: 10.0 }
      end: { x: 760.0, y: 600.0 }
    dismiss:
    - os_version: "*"
      x: 80.0
      y: 80.0
  streaming_params:
    width: 695
    height: 1007
iPad16,4:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 760.0, y: 10.0 }
      end: { x: 760.0, y: 600.0 }
    dismiss:
    - os_version: "*"
      x: 80.0
      y: 80.0
  streaming_params:
    width: 695
    height: 1007
# iPad Air 13.0 2024
iPad14,10:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 1020.0, y: 10.0 }
      end: { x: 1020.0, y: 200.0 }
    dismiss:
    - os_version: "*"
      x: 80.0
      y: 80.0
  streaming_params:
    width: 746
    height: 995
iPad14,11:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 1020.0, y: 10.0 }
      end: { x: 1020.0, y: 200.0 }
    dismiss:
    - os_version: "*"
      x: 80.0
      y: 80.0
  streaming_params:
    width: 746
    height: 995
# iPad Pro 13 inch, 2024
iPad16,5:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 1020.0, y: 10.0 }
      end: { x: 1020.0, y: 200.0 }
    dismiss:
    - os_version: "*"
      x: 80.0
      y: 80.0
  streaming_params:
    width: 746
    height: 995
iPad16,6:
  control_centre:
    show:
    - os_version: "*"
      start: { x: 1020.0, y: 10.0 }
      end: { x: 1020.0, y: 200.0 }
    dismiss:
    - os_version: "*"
      x: 80.0
      y: 80.0
  streaming_params:
    width: 746
    height: 995
