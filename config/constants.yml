---
logging_root: "/var/log/browserstack"

# Stores the data regarding each device currently connected.
config_json_file: "/usr/local/.browserstack/config/config.json"

#Configuration settings
static_conf_file: "/usr/local/.browserstack/config/static_conf.json"

#Absolute path of the config directory
config_root: "/usr/local/.browserstack/config"

selenium_default_port_start: 8079
webdriver_agent_default_port_start: 8399
webkit_default_port_start: 27752
insecure_websocket_proxy_port: 45650

# PacFile Path
pacfile_dir: "/usr/local/.browserstack/config"
templates_dir: "/usr/local/.browserstack/realmobile/templates"

#Device Check log file
device_log_file_name: "mobile_poll.log"

#Machine Check log file
machine_log_file_name: "device_poll.log"

# Apple's new intermediate cert SHA-1 hash
apple_new_intermediate_cert_sha1: "06EC06599F4ED0027CC58956B4D3AC1255114F35"
apple_intermediate_cert_name: "Apple Worldwide Developer Relations Certification Authority"
apple_intermediate_cert_file_md5sum: "08a45128fa238443623421dd2c9887ab"
system_keychain_path: "/Library/Keychains/System.keychain"
browserstack_keychain_path: "/Users/<USER>/Library/Keychains/Browserstack.keychain-db"

#File to store the IP address
ip_file: "/usr/local/.browserstack/whatsmyip"

#Timeout for device check
checks_timeout: 600

#s3Curl binary
s3_curl_bin_path: "/usr/local/bin/s3curl"

#Absolute path of the plist directory
plist_dir_system: "/Library/LaunchDaemons/"
plist_dir_user: "/Library/LaunchAgents/"

#Absolute path of the realMobile directory
mobile_root: "/usr/local/.browserstack/realmobile"

#Specify custom rails endpoint
custom_rails_endpoint: "http://localhost:3000"

# make sure that switching to user doesn't require password
user: "app"

screenshot_old_dir: "/tmp"
screenshot_dir: "/usr/local/.browserstack/files_to_be_processed/raw_files"
assistive_touch_gesture_button_templates: "/usr/local/.browserstack/realmobile/lib/helpers/automate_gestures/templates"
automate_screenshots_folder_converter: "/usr/local/.browserstack/automate_screenshot/META_CONVERT"
automate_screenshots_folder_uploader: "/usr/local/.browserstack/automate_screenshot/META_UPLOADER"
images_to_upload_dir: "/usr/local/.browserstack/files_to_be_processed/files_to_upload/IMAGE"
videos_to_upload_dir: "/usr/local/.browserstack/files_to_be_processed/files_to_upload/VIDEO"
other_files_to_upload_dir: "/usr/local/.browserstack/files_to_be_processed/files_to_upload/OTHERS"
network_files_to_upload_dir: "/usr/local/.browserstack/files_to_be_processed/files_to_upload/NETWORK"
files_to_convert_dir: "/usr/local/.browserstack/files_to_be_processed/files_to_convert_new_dir"
screenshot_instrumentation_dir: "/usr/local/.browserstack/files_to_be_processed/screenshot_instrumentation"
screenshot_instrumentation_prefix: "screenshot_instrumentation_"
temp_screenshot_suffix_png: "png_delete_me"
temp_screenshot_suffix_jpeg: "jpeg_delete_me"

appium_keychain: "/Users/<USER>/Library/Keychains/Browserstack.keychain-db"
provisioning_profile_dir: "/Users/<USER>/Library/MobileDevice/Provisioning Profiles"
fastlane_match_git_url: "**************:browserstack/realmobile-certificates.git"
fastlane_match_app_identifier: "*"

alerter_http_url: "https://alert-external.browserstack.com/alert"

infra_api: "http://inventory.bsstag.com/pushdata"

deployui_endpoint: "http://deploy.bsstag.com:9091"

# MDM
mdm_queue_timeout: 100

# Privoxy Config
privoxy_socket_timeout: 200
privoxy_max_client_connections: 256
privoxy_keep_alive_timeout: 60
privoxy_server_timeout: 50
privoxy_listen_port_offset: 40000
privoxy_conf_dir: "/usr/local/.browserstack/privoxy"
privoxy_logs_dir: "/var/log/browserstack/privoxy"
privoxy_templates_dir: "/usr/local/.browserstack/privoxy/templates"
privoxy_templates_content: "BROWSERSTACK DEFAULT TEMPLATE FILE"

# Socat Config
socat_listen_port_offset: 41000

# browserstack media
bs_media: "/usr/local/.browserstack/media"
photo_data: "/usr/local/.browserstack/photo_data"
bs_media_ios13: "/usr/local/.browserstack/media_ios13"
bs_media_ios14: "/usr/local/.browserstack/media_ios14"
bs_media_multiversion: '/usr/local/.browserstack/deps/media_ios_multiversion' # Used for ios 13 onward

# preloaded contacts
contacts_data_path: "/usr/local/.browserstack/realmobile/templates/contacts_data.plist"
contacts_metadata_path: "/usr/local/.browserstack/realmobile/templates/contacts_metadata.plist"

# Redis config
redis_port: 6379
redis_timeout: 3

# Developer Disk Images Path
developer_disk_images_path: "/Applications/Xcode.app/Contents/Developer/Platforms"

#developer symbols path
developer_symbols_path: "/Users/<USER>/Library/Developer/Xcode/iOS DeviceSupport/"

# Video recording stuff
video_recording_workspace: "/tmp/"
video_recording_part_size: 50
video_recording_fps: 5
video_max_ffmpeg_processes: 2

xcode_versions:
  - "8.2"
  - "8.3.3"
  - "9.0"
  - "10.0"
  - "10.1"
  - "11.5"
  - "12.1"
  - "12.4"
  - "13.0"
  - "13.3"
  - "14.0"
  - "15.0"
  - "16.0"
  - "16.2"

device_mount_point: "/usr/local/.browserstack/device_mount"
device_logger_path: "/usr/local/.browserstack/device-logger"
udp_server_path: "/usr/local/.browserstack/udp-server"
reboot_wait_time: 60

dist_name: "BinaryLife Inc"

# internet sharing
internet_sharing_enabled: true

# block device if it has third party apps
strict_app_check: false

# enable checking for the restrictions profile
enable_restrictions: true

# This is enable sending network logs to eds
network_logs_send_to_eds: true

mdm_profiles_required:
  "Proxy":
    type: "com.apple.proxy.http.global"
  "Restrictions":
    type: "com.apple.applicationaccess"
    uuid: "95E5F163-06FD-4083-80FD-13CA0EE898A2"
  "Setup Assistant":
    type: "com.apple.SetupAssistant.managed"
    uuid: "8876810A-8673-11ED-A1EB-0242AC120002"
  "MitmProxy_Root_Certificate":
    type: "com.apple.security.root"
    uuid: "BE41F7D3-1719-4EAD-B84D-9716EF7DC487"
    relative_path: ".mitmproxy/mitmproxy-ca-cert.cer"
    payload_identifier_suffix: ""
  "MitmProxy_5_Root_Certificate":
    type: "com.apple.security.root"
    uuid: "29D2F004-23CE-4367-87F6-E8A46A80D4E1"
    relative_path: ".mitmproxy_5_new/mitmproxy-ca-cert.cer"
    payload_identifier_suffix: "_5_new"
  "Notifications":
    type: "com.apple.notificationsettings"
    uuid: "B217C34B-45B3-437E-950C-6708F701B44A"

:wifi_profile_data:
  :prefix: "wifi."
  :latest_version: 1

:cfgutil_profiles_required:
  :proxy:
    :prefix: "proxy."
    :latest_version: 1
  :restrictions:
    :prefix: "restrictions."
    :latest_version: 13
  :setup assistant:
    :prefix: "setup_assistant."
    :latest_version: 1
  :mitmproxy_root_certificate:
    :prefix: "mitmproxy."
    :latest_version: 1
  :mitmproxy_5_root_certificate:
    :prefix: "mitmproxy5."
    :latest_version: 1
  :notifications:
    :prefix: "notifications."
    :latest_version: 1

mitmdump_dir: "/tmp/mitmproxy_dump_files"

sec_cert_path: "/usr/local/.browserstack/ssl_cert"

orientation-lock-value: 0

# CLS Prod URL
cls_url: "logs.browserstack.com"
cls_port: 41234

# CLS Staging URL
cls_staging_url: "logs.bsstag.com"
# port fot CLS staging is same as prod (`cls_port`)

# locallogs
locallogs_url: "locallogs.browserstack.com"
locallogs_port: 9998

s3_base_url: "http://s3.amazonaws.com/bs-mobile/deploy/realios/"
app_cache_dir: "/usr/local/.browserstack/config/apps/cache"
signed_app_dir: "/usr/local/.browserstack/config/apps/signed"
s3_timeout: "300"
chromium_timeout: "900"

location_service_url: "https://live.browserstack.com/getlocation.html"

machine_blocked_in_rails_file: "/usr/local/.browserstack/machine_blocked"
force_machine_block_file: "/tmp/force_machine_block"

machine_inventory_ip: 'https://mobile-inventory.bsstag.com'
machine_inventory_port: 443

provisioning_profile_service: 'https://provisioning-server.browserstack.com'

# SSH key that enigma uses to automate access grants.
access_grant_ssh_key: "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC4H6DgVblPnAYT77nCsEYkPwjOvDlodLvQ+ow2nzKOygXqFkXPoIpasi7+pZVvJ7e4oc9jJBSgHY/nsExESdiZ8PJQ2IcpjN1HCfsn457wwJ19xdJcDn6IG6Wo9ODaUt6ICTInQWr0t4BvswjCGgVp6mkg5z9iN2RSQ467G69cEAh8l2XZSydqStZquCKmEZP8JbjuWtl+6A0o+8YEUMRuX3Kob8weW4ViMuDWEsNbRjQICuLLtLdjhDaea+N7CdGVLDQwKBw5NYjWXx6j9P07QuTRSQgzgTdvskFmySNkrAHPUw9+3CLZwmiJ9/Bh4Pd2AWvmMMmXSOH5lI2xWPzX <EMAIL>"

browserstack_app_dir: "/usr/local/.browserstack/browserstack-provisioned-apps"
browserstack_app_xcodeproj: "/usr/local/.browserstack/ios-njb-app/BrowserStack.xcodeproj"

eds:
  hostname: 'eds.browserstack.com'
  port_udp: '8553'
  port_tcp: '443'

version_specific_apps_installed:
  "com.google.chrome.ios":
    latest: "92.0.4515.90"
    12: "86.0.4240.93"
    10: "64.0.3282.112"
    11: "75.0.3770.70"
  "com.browserstack.Launcher":
    latest: "15"
    10: "1"
    11: "1"
    12: "1"
    18: "17"
  "com.browserstack.Redirect":
    latest: "2"
  "dev.mobile.maestro-driver-iosUITests.xctrunner":
    latest: "1"

state_files_dir: "/usr/local/.browserstack/state_files"
telephony_service_endpoint: "http://telephony-mobile-001-ec2-euw-prod.browserstack.com"
pac_profile_payload_id: "com.apple.proxy.http.global.45368A7E-0295-4DE0-BAEB-BB57E9B4F241"
mdm_restrictions_profile_payload_id: "com.apple.applicationaccess.D54C1EC5-65CE-47CE-BF8A-FE01B0223141"
mdm_setup_assistant_profile_payload_id: "com.browserstack.setupassistant.B217C34B-45B3-437E-950C-6708F701B44A"
mdm_notifications_profile_payload_id: "com.browserstack.notifications.B217C34B-45B3-437E-950C-6708F701B44A"
mdm_pfx_certificate_profile_rooted_payload_uuid: "4EFFC993-F99E-4C73-96F2-6E0EAAEEA1E1"
enterprise_dummy_min_version: "4.0"
test_flight_min_version: "2.6"
devtools_proxy_path: "/usr/local/.browserstack/mobile-devtools/devtools-proxy"
devtools_port: 443
appium_version_for_js_testing_in_ios_13: "1.13.0"
webdriver_agent_builds_path: "/usr/local/.browserstack/wda"
mobile_config_ssl_prefix: "com.browserstack.ssl."
mobile_config_ID_name: "PayloadIdentifier"
max_retries_ssl_cert: 5

xcui_class_function_map:
  :allow_photos_popup:
    :class: BrowserStackUITests
    :function: testTapAllowPopUp
  :disable_voiceover:
    :class: SettingsUITests
    :function: testDisableVoiceOver
  :apple_id_signout:
    :class: SettingsUITests
    :function: testAppleIDSignOut
  :clean_pwa_and_waiting_state_apps:
    :class: HomeScreenUITests
    :function: testCleanPWAAndAppsInWaitingState
  :clean_device_theme:
    :class: HomeScreenUITests
    :function: testCleanDeviceTheme
  :enable_geoguard:
    :class: HomeScreenUITests
    :function: enableLocationPermissionsForGeoGuard
  :clean_safari_app:
    :class: SafariUITests
    :function: testCleanSafariApp
  :clean_safari_fav_and_bookmarks:
    :class: SafariUITests
    :function: testCleanBookmarksFavorites
  :clear_safari_reading_list:
    :class: SafariUITests
    :function: testClearReadingList
  :clean_safari_tab_groups:
    :class: SafariUITests
    :function: testCleanTabGroups
  :clean_safari_url_bar_position:
    :class: SettingsUITests
    :function: testCleanUrlPosition
  :delete_downloads:
    :class: FilesUITests
    :function: testDeleteDownloads
  :disable_airplane_mode:
    :class: SettingsUITests
    :function: testDisableAirplaneMode
  :disable_assistive_touch:
    :class: SettingsUITests
    :function: testDisableAssistiveTouch
  :disable_dark_mode:
    :class: SettingsUITests
    :function: testDisableDarkMode
  :disable_focus_mode:
    :class: HomeScreenUITests
    :function: disableFocusMode
  :safari_remote_automation:
    :class: SettingsUITests
    :function: testSafariRemoteAutomation
  :disable_government_notifications:
    :class: SettingsUITests
    :function: testDisableGovernmentNotifications
  :disable_low_power_mode:
    :class: SettingsUITests
    :function: testDisableLowPowerMode
  :enable_focus_mode:
    :class: HomeScreenUITests
    :function: enableFocusMode
  :enable_wifi:
    :class: SettingsUITests
    :function: testEnableWiFi
  :siri_contacts_cleanup:
    :class: SettingsUITests
    :function: testSiriContactsCleanup
  :disconnect_wifi:
    :class: SettingsUITests
    :function: testDisconnectWifi
  :enable_location_services:
    :class: SettingsUITests
    :function: testEnableLocationService
  :get_global_proxy_url:
    :class: SettingsUITests
    :function: testGetGlobalProxyUrl
  :launch_browserstack_app:
    :class: BrowserStackUITests
    :function: testLaunchBrowserStackApp
  :lock_device:
    :class: LockTests
    :function: testLockDevice
  :disable_standby_mode:
    :class: SettingsUITests
    :function: testDisableStandByMode
  :preload_media_ios_njb_app:
    :class: BrowserStackUITests
    :function: testPreloadMedia
  :set_default_font_size:
    :class: SettingsUITests
    :function: testSetDefaultFontSize
  :reset_keyboard_settings:
    :class: SettingsUITests
    :function: testResetKeyboardDictionary
  :safari_experimental_feature_paint_timing_enable:
    :class: SettingsUITests
    :function: testSafariExperimentalFeaturePaintTimingEnable
  :safari_experimental_feature_paint_timing_disable:
    :class: SettingsUITests
    :function: testSafariExperimentalFeaturePaintTimingDisable
  :safari_experimental_feature_websocket_disable:
    :class: SettingsUITests
    :function: testSafariExperimentalFeatureWebSocketDisable
  :safari_cleanup:
    :class: SettingsUITests
    :function: testSafariCleanup
  :signout_sandbox_accounts:
    :class: SettingsUITests
    :function: testSignOutSandboxAccounts
  :trust_dummy_app_cert:
    :class: SettingsUITests
    :function: testTrustDummyAppCertificate
  :trust_enterprise_cert:
    :class: SettingsUITests
    :function: testTrustAppCertificate
  :apple_wallet_cleanup:
    :class: WalletUITests
    :function: testCleanWallet
  :wallet_double_click_side_button:
    :class: SettingsUITests
    :function: testEnableWalletDoubeClickSideButton
  :clear_contact_accounts:
    :class: MobilePhoneUITests
    :function: testClearContactsAccounts
  :call_logs_cleanup:
    :class: MobilePhoneUITests
    :function: testClearCallLogs
  :favorite_contact_cleanup:
     :class: MobilePhoneUITests
     :function: testClearFavoriteContacts
  :reset_keyboard_language:
    :class: SettingsUITests
    :function: testResetKeyboardLanguage
  :set_time_to_utc:
    :class: SettingsUITests
    :function: testSetTimeToUTC
  :sms_cleanup:
    :class: MessagesUITests
    :function: testMessagesAppCleanup
  :disable_auto_lock:
    :class: SettingsUITests
    :function: testDisableAutoLock
  :enable_safari_web_inspector:
    :class: SettingsUITests
    :function: testEnableSafariWebInspector
  :disable_bluetooth:
    :class: SettingsUITests
    :function: testDisableBluetooth
  :clear_third_party_accounts:
    :class: SettingsUITests
    :function: testSignOutThirdParty
  :change_time_zone:
    :class: SettingsUITests
    :function: testChangeTimeZone
  :kill_apps:
    :class: SettingsUITests
    :function: testKillApps
  :disable_testflight_notifications:
    :class: SettingsUITests
    :function: testDisableTestFlightNotification
  :testflight_login:
    :class: TestFlightUITests
    :function: testLogin
  :reset_view_to_standard:
    :class: SettingsUITests
    :function: testResetViewToStandard
  :launcher_allow_photos_popup:
    :class: LauncherUITests
    :function: testTapAllowPopUp
  :stop_videorecord:
    :class: LauncherUITests
    :function: testStopVideoRecording
  :stop_videorecord_lock_unlock:
    :class: LauncherUITests
    :function: testStopVideoRecordingLockUnlock
  :mdm_auto_enrollment:
    :class: MDMAutoEnrollment
    :function: testMDMAutoEnrollment
  :enable_sim:
    :class: SettingsUITests
    :function: testEnableSIM
  :disable_sim:
    :class: SettingsUITests
    :function: testDisableSIM
  :disable_esim:
    :class: SettingsUITests
    :function: testDisableESIM
  :enable_esim:
    :class: SettingsUITests
    :function: testEnableESIM
  :enable_call_forwarding:
    :class: SettingsUITests
    :function: testEnableCallForwarding
  :clear_sms_notifications:
    :class: SettingsUITests
    :function: testToggleSMSNotifications
  :install_downloaded_profile:
    :class: SettingsUITests
    :function: testInstallDownloadedProfile
  :enable_redirect_extension:
    :class: SettingsUITests
    :function: enableRedirectExtension
  :change_safari_default_download_location:
    :class: SettingsUITests
    :function: testChangeDefaultDownloadLocation
  :dissmiss_appstore_popups:
    :class: AppStoreUITests
    :function: testDissmissPopups
  :sign_in_to_appstore:
    :class: AppStoreUITests
    :function: testSignInToAppStore
  :navigate_to_app:
    :class: AppStoreUITests
    :function: testNavigateToApp
  :sign_out_of_appstore:
    :class: AppStoreUITests
    :function: testSignOutOfAppStore
  :clear_transaction_defaults:
    :class: SettingsUITests
    :function: testClearTransactionDefaults
  :disable_testflight_background_refresh:
    :class: SettingsUITests
    :function: testStopBackgroundAppRefreshForTestFlight
  :fetch_sim_signal_strength:
    :class: SettingsUITests
    :function: testGetSIMSignalStrength
  :clean_stored_password:
    :class: SettingsUITests
    :function: testCleanStoredPassword
  :disable_stage_manager:
    :class: SettingsUITests
    :function: testDisableStageManager
  :clean_password_app:
    :class: PasswordsUITests
    :function: testDeletePasswords
  :set_safari_default_browser_in_eu:
    :class: SafariUITests
    :function: testSetSafariDefaultBrowserInEU
  :disable_apple_intelligence:
    :class: SettingsUITests
    :function: testDisableAppleIntelligence
  :restore_app_store:
    :class: SettingsUITests
    :function: testRestoreAppStore 
developer_symbols:
  - "10.3.1"
  - "10.3.2"
  - "10.3.3"
  - "11.0"
  - "11.0.1"
  - "11.0.3"
  - "11.1"
  - "11.1.1"
  - "11.1.2"
  - "11.2"
  - "11.2.1"
  - "11.2.2"
  - "11.2.5"
  - "11.2.6"
  - "11.3"
  - "11.3.1"
  - "11.4"
  - "11.4.1"
  - "12.0"
  - "12.0.1"
  - "12.0.1arm64e"
  - "12.1"
  - "12.1.1"
  - "12.1.2"
  - "12.1.2arm64e"
  - "12.1arm64e"
  - "12.1.3"
  - "12.1.4"
  - "12.2"
  - "12.3"
  - "12.3.1"
  - "12.3.2"
  - "12.4"
  - "12.4.1"
  - "13.0"
  - "13.0arm64e"
  - "13.1"
  - "13.1.1arm64e"
  - "13.1.2arm64e"
  - "13.2arm64e"
  - "13.2.2arm64e"
  - "13.2.3arm64e"
  - "13.1.3"
  - "13.3arm64e"
  - "13.3.1arm64e"
  - "13.4arm64e"
  - "13.4.1arm64e"
  - "13.4.5arm64e"
  - "13.5.1"
  - "13.6arm64e"
  - "13.6.1arm64e"
  - "13.7arm64e"
  - "14.0arm64e"
  - "14.0.1arm64e"
  - "14.1arm64e"
  - "14.2arm64e"
  - "14.2.1arm64e"
  - "14.3arm64e"
  - "14.4arm64e"
  - "14.4.1arm64e"
  - "14.4.2"
  - "14.4.2arm64e"
  - "14.5arm64e"
  - "14.5.1arm64e"
  - "14.6arm64e"
  - "14.7.1arm64e"
  - "15.0arm64e"
  - "15.0.2arm64e"
  - "15.1arm64e"
  - "15.1.1arm64e"
  - "15.2arm64e"
  - "15.2.1arm64e"
  - "15.3arm64e"
  - "15.3.1arm64e"
  - "15.4arm64e"
  - "15.4.1arm64e"
  - "15.5arm64e"
  - "15.6arm64e"
  - "15.6.1arm64e"
  - "16.0arm64e"
  - "16.0.1arm64e"
  - "16.0.2arm64e"
  - "16.0.3arm64e"
  - "16.1arm64e"
  - "16.1.1arm64e"
  - "16.1.2arm64e"
  - "16.2arm64e"
  - "16.3arm64e"
  - "16.3.1arm64e"
  - "16.4arm64e"
  - "16.4.1arm64e"
  - "16.5arm64e"
  - "16.5.1arm64e"
  - "16.6.1arm64e"
  - "17.0arm64e"


xcui_cleanup_tests_class_function_map:
  :settings_website_cache_cleaned:
    :class: SettingsAppTests
    :function: testWebsiteCacheCleaned
  :apps_orientation_test:
    :class: OrientationTests
    :function: testOrientationIsPortrait
  # https://browserstack.atlassian.net/browse/MOBCR-3541
  # :safari_empty_reading_list:
  #   :class: CleanSafariBrowser
  #   :function: testSafariNoReadingList
  :safari_no_history_link:
    :class: CleanSafariBrowser
    :function: testSafariNoHistoryLinkPresent
  :safari_no_bookmarks_link:
    :class: CleanSafariBrowser
    :function: testSafariNoBookmarksLinkPresent
  :safari_no_account_login:
    :class: CleanSafariBrowser
    :function: testSafariBrowserAccountLogout
  :safari_no_tab_groups:
    :class: CleanSafariBrowser
    :function: testSafariNoTabGroups
  :safari_distracting_element_cleaned:
    :class: CleanSafariBrowser
    :function: testDistractingElementIsCleanedInSafari
  :chrome_empty_reading_list:
    :class: CleanChromeBrowser
    :function: testChromeNoReadingList
  :chrome_empty_bookmarks:
    :class: CleanChromeBrowser
    :function: testChromeNoBookmarks
  :chrome_empty_history:
    :class: CleanChromeBrowser
    :function: testChromeNoHistory
  :chrome_no_account:
    :class: CleanChromeBrowser
    :function: testChromeSignedOut
  :chromium_empty_history:
    :class: CleanChromiumBrowser
    :function: testChromiumNoHistoryPresent
  :chromium_empty_bookmark:
    :class: CleanChromiumBrowser
    :function: testChromiumNoBookmarksPresent
  :chromium_empty_readinglist:
    :class: CleanChromiumBrowser
    :function: testChromiumNoReadingListPresent
  :chromium_empty_downloads:
    :class: CleanChromiumBrowser
    :function: testChromiumNoDownloadsPresent
  :chromium_empty_account:
    :class: CleanChromiumBrowser
    :function: testChromiumNoAccountPresent
  :chromium_empty_tabs:
    :class: CleanChromiumBrowser
    :function: testChromiumTabCount
  :apps_appstore_no_credentials:
    :class: AppStoreTests
    :function: testNoCredentialsInAppStore
  :settings_airplane_mode_off:
    :class: SettingsAppTests
    :function: testAirplaneModeOff
  :settings_siri_suggesstions_off:
    :class: SettingsAppTests
    :function: testSiriSuggesstionsOff
  :settings_assistive_touch_off:
    :class: SettingsAppTests
    :function: testAssistiveTouchOff
  :settings_low_power_mode_off:
    :class: SettingsAppTests
    :function: testLowPowerModeOff
  :settings_no_apple_id:
    :class: SettingsAppTests
    :function: testNoAppleID
  :settings_keyboard_suggestions_off:
    :class: SettingsAppTests
    :function: testKeyboardSuggestionsOff
  :settings_keyboard_language_english:
    :class: SettingsAppTests
    :function: testKeyboardLanguageEnglish
  :apps_testflight_no_credentials:
    :class: TestFlightTests
    :function: testNoCredentialsInTestFlight
  :apps_download_folder_empty:
    :class: DownloadCleanupTests
    :function: testNoDownloads
  :apps_preloaded_images_exist:
    :class: PreloadedImagesTests
    :function: testPreloadedImagesList
  :settings_date_and_time_cleaned:
    :class: SettingsAppTests
    :function: testDateAndTimeSetToDefault
  :settings_previously_added_password_is_cleaned:
    :class: SettingsAppTests
    :function: testPreviouslyAddedPasswordIsCleaned
  :apps_preloaded_contacts:
    :class: PhoneTest
    :function: testAllPreLoadedContactsArePresent
  :apps_home_screen_theme_and_size_cleaned:
    :class: SettingsAppTests
    :function: testThemeAndSizeReseted
  :apps_check_password_app_is_present:
    :class: SettingsAppTests
    :function: testPasswordAppIsPresent
  :safari_reader_mode_disabled:
    :class: SettingsAppTests
    :function: testReaderModeAutomaticallyIsDisabledForEncylopediaWebsite

xcui_cleanup_population_tests_class_function_map:
  :apps_populate_clipboard:
    :class: CleanSafariBrowser
    :function: testPopulateClipboard
  :safari_generate_history_and_cookies:
    :class: CleanSafariBrowser
    :function: testSafariGenerateHistoryAndCookies
  :safari_generate_bookmarks:
    :class: CleanSafariBrowser
    :function: testSafariGenerateBookmarks
  :safari_generate_favorites:
    :class: CleanSafariBrowser
    :function: testSafariGenerateFavorites
  :safari_generate_readinglist:
    :class: CleanSafariBrowser
    :function: testSafariGenerateReadingList
  :safari_generate_multipletabs:
    :class: CleanSafariBrowser
    :function: testSafariGenerateMultipleTabs
  :safari_generate_downloads:
    :class: CleanSafariBrowser
    :function: testSafariGenerateDownloads
  :safari_generate_password:
    :class: CleanSafariBrowser
    :function: testPasswordGenerate
  :safari_generate_tab_group:
    :class: CleanSafariBrowser
    :function: testSafariGenerateTabGroup
  :safari_enable_reader_mode_automatically:
    :class: CleanSafariBrowser
    :function: testGenerateReaderModeAutomaticallyForWebsite
  :safari_hide_distracting_element:
    :class: CleanSafariBrowser
    :function: testHideDistractingElementInSafari
  :safari_generate_bookmarks_via_more_option:
    :class: CleanSafariBrowser
    :function: testSafariGenerateBookmarksViaMoreOption
  :safari_generate_favorites_via_more_option:
    :class: CleanSafariBrowser
    :function: testSafariGenerateFavoritesViaMoreOptions
  :safari_generate_readinglis_via_more_option:
    :class: CleanSafariBrowser
    :function: testSafariGenerateReadingListViaMoreOptions
  :chrome_generate_history_and_cookies:
    :class: CleanChromeBrowser
    :function: testChromeGenerateHistoryAndCookies
  :chrome_generate_bookmarks:
    :class: CleanChromeBrowser
    :function: testChromeGenerateBookmarks
  :chrome_generate_readinglist:
    :class: CleanChromeBrowser
    :function: testChromeGenerateReadingList
  :chrome_generate_multipletabs:
    :class: CleanChromeBrowser
    :function: testChromeGenerateMultipleTabs
  :chrome_generate_downloads:
    :class: CleanChromeBrowser
    :function: testChromeGenerateDownloads
  :chromium_generate_history_and_cookies:
    :class: CleanChromiumBrowser
    :function: testChromiumGenerateHistoryAndCookies
  :chromium_generate_bookmarks:
    :class: CleanChromiumBrowser
    :function: testChromiumGenerateBookmarks
  :cchromium_generate_readinglist:
    :class: CleanChromiumBrowser
    :function: testChromiumGenerateReadingList
  :chromium_generate_multipletabs:
    :class: CleanChromiumBrowser
    :function: testChromiumGenerateMultipleTabs
  :chromium_generate_downloads:
    :class: CleanChromiumBrowser
    :function: testChromiumGenerateDownloads
  :apps_preloaded_generate_images_via_camera:
    :class: PreloadedImagesTests
    :function: testPopulateImageAndVideoViaCameraClick
  :apps_set_orientation_to_landscape_mode:
    :class: OrientationTests
    :function: testSetOrientationToLandScape
  :apps_change_home_screen_theme_and_size:
    :class: SettingsAppTests
    :function: testChangeThemeAndSize

known_apps_bundle_id:
  - "com.apple.AAUIViewService"
  - "com.apple.AccountAuthenticationDialog"
  - "com.apple.AdPlatformsDiagnostics"
  - "com.apple.AdSheetPhone"
  - "com.apple.AMSEngagementViewService"
  - "com.apple.appleaccount.AACredentialRecoveryDialog"
  - "com.apple.appleseed.FeedbackAssistant"
  - "com.apple.AppSettings"
  - "com.apple.AppShareUIService"
  - "com.apple.AppSSOUIService"
  - "com.apple.AppStore"
  - "com.apple.AskPermissionUI"
  - "com.apple.assistivetouchd"
  - "com.apple.AuthenticationServicesUI"
  - "com.apple.AuthKitUIService"
  - "com.apple.AXUIViewService"
  - "com.apple.BarcodeScanner"
  - "com.apple.BluetoothUIService"
  - "com.apple.Bridge"
  - "com.apple.BusinessChatViewService"
  - "com.apple.calculator"
  - "com.apple.camera"
  - "com.apple.carkit.DNDBuddy"
  - "com.apple.CarPlayApp"
  - "com.apple.CarPlaySettings"
  - "com.apple.CarPlaySplashScreen"
  - "com.apple.CarPlayTemplateUIHost"
  - "com.apple.CarPlayWallpaper"
  - "com.apple.ChargingViewService"
  - "com.apple.CheckerBoard"
  - "com.apple.clips"
  - "com.apple.ClipViewService"
  - "com.apple.CloudKit.ShareBear"
  - "com.apple.CMPluginApp"
  - "com.apple.CMViewSrvc"
  - "com.apple.commandandcontrol"
  - "com.apple.CompanionAuthViewService"
  - "com.apple.compass"
  - "com.apple.CompassCalibrationViewService"
  - "com.apple.CoreAuthUI"
  - "com.apple.CredentialSharingService"
  - "com.apple.CTCarrierSpaceAuth"
  - "com.apple.ctkui"
  - "com.apple.CTNotifyUIService"
  - "com.apple.DataActivation"
  - "com.apple.datadetectors.DDActionsService"
  - "com.apple.DemoApp"
  - "com.apple.DeviceOMatic"
  - "com.apple.Diagnostics"
  - "com.apple.DiagnosticsService"
  - "com.apple.DocumentsApp"
  - "com.apple.dt.XcodePreviews"
  - "com.apple.EscrowSecurityAlert"
  - "com.apple.FacebookAccountMigrationDialog"
  - "com.apple.facetime"
  - "com.apple.family"
  - "com.apple.FCAuthenticationUI"
  - "com.apple.fieldtest"
  - "com.apple.findmy"
  - "com.apple.Fitness"
  - "com.apple.FontInstallViewService"
  - "com.apple.FTMInternal"
  - "com.apple.fullkeyboardaccess"
  - "com.apple.gamecenter.GameCenterUIService"
  - "com.apple.gamecenter.widgets"
  - "com.apple.GameCenterRemoteAlert"
  - "com.apple.Greenfield-iPad"
  - "com.apple.HDSViewService"
  - "com.apple.Health"
  - "com.apple.Health.Sleep"
  - "com.apple.HealthENBuddy"
  - "com.apple.HealthENLauncher"
  - "com.apple.HealthPrivacyService"
  - "com.apple.Home"
  - "com.apple.Home.HomeControlService"
  - "com.apple.Home.HomeUIService"
  - "com.apple.iad.iAdOptOut"
  - "com.apple.iBooks"
  - "com.apple.icloud.FindMyDevice.FindMyExtensionContainer"
  - "com.apple.icloud.spnfcurl"
  - "com.apple.iCloudDriveApp"
  - "com.apple.icq"
  - "com.apple.iMessageAppsViewService"
  - "com.apple.iMovie"
  - "com.apple.InCallService"
  - "com.apple.ios.StoreKitUIService"
  - "com.apple.Keynote"
  - "com.apple.LoginUI"
  - "com.apple.Magnifier"
  - "com.apple.MailCompositionService"
  - "com.apple.Maps"
  - "com.apple.measure"
  - "com.apple.MobileAddressBook"
  - "com.apple.mobilecal"
  - "com.apple.mobilegarageband"
  - "com.apple.mobilemail"
  - "com.apple.mobileme.fmf1"
  - "com.apple.mobileme.fmip1"
  - "com.apple.mobilenotes"
  - "com.apple.mobilephone"
  - "com.apple.MobileReplayer"
  - "com.apple.mobilesafari"
  - "com.apple.mobileslideshow"
  - "com.apple.MobileSMS"
  - "com.apple.mobilesms.compose"
  - "com.apple.mobilesms.notification"
  - "com.apple.MobileStore"
  - "com.apple.mobiletimer"
  - "com.apple.MTLReplayer"
  - "com.apple.Music"
  - "com.apple.MusicUIService"
  - "com.apple.news"
  - "com.apple.Numbers"
  - "com.apple.Pages"
  - "com.apple.PaperBoard"
  - "com.apple.Passbook"
  - "com.apple.PassbookBanner"
  - "com.apple.PassbookSecureUIService"
  - "com.apple.PassbookStub"
  - "com.apple.PassbookUIService"
  - "com.apple.PeopleViewService"
  - "com.apple.Photo-Booth"
  - "com.apple.Photos.PhotosUIService"
  - "com.apple.PhotosViewService"
  - "com.apple.Playgrounds"
  - "com.apple.podcasts"
  - "com.apple.PreBoard"
  - "com.apple.Preferences"
  - "com.apple.PrintKit.Print-Center"
  - "com.apple.PublicHealthRemoteUI"
  - "com.apple.purplebuddy"
  - "com.apple.reminders"
  - "com.apple.RemoteiCloudQuotaUI"
  - "com.apple.RemotePaymentPassActionsService"
  - "com.apple.SafariViewService"
  - "com.apple.screensharingserver"
  - "com.apple.ScreenSharingViewService"
  - "com.apple.ScreenshotServicesService"
  - "com.apple.ScreenTimeUnlock"
  - "com.apple.ScreenTimeWidgetApplication"
  - "com.apple.ServerDocuments"
  - "com.apple.share"
  - "com.apple.SharedWebCredentialViewService"
  - "com.apple.SharingViewService"
  - "com.apple.shortcuts"
  - "com.apple.shortcuts.runtime"
  - "com.apple.sidecar"
  - "com.apple.sidecar.camera"
  - "com.apple.SIMSetupUIService"
  - "com.apple.siri"
  - "com.apple.SiriViewService"
  - "com.apple.SleepLockScreen"
  - "com.apple.social.SLGoogleAuth"
  - "com.apple.social.SLYahooAuth"
  - "com.apple.Spotlight"
  - "com.apple.springboard"
  - "com.apple.stocks"
  - "com.apple.store.Jolly"
  - "com.apple.StoreDemoViewService"
  - "com.apple.SubcredentialUIService"
  - "com.apple.susuiservice"
  - "com.apple.TencentWeiboAccountMigrationDialog"
  - "com.apple.test.BrowserStackUITests-Runner"
  - "com.apple.test.WebDriverAgentRunner-Runner"
  - "com.apple.TestFlight"
  - "com.apple.tips"
  - "com.apple.Translate"
  - "com.apple.TrustMe"
  - "com.apple.tv"
  - "com.apple.TVAccessViewService"
  - "com.apple.TVRemoteUIService"
  - "com.apple.videos"
  - "com.apple.VoiceMemos"
  - "com.apple.VoiceOverTouch"
  - "com.apple.VSViewService"
  - "com.apple.weather"
  - "com.apple.webapp"
  - "com.apple.webapp1"
  - "com.apple.WebContentFilter.remoteUI.WebContentAnalysisUI"
  - "com.apple.WebSheet"
  - "com.browserstack.app"
  - "com.browserstack.BrowserStackUITests.xctrunner"
  - "com.browserstack.enterpriseDummy"
  - "com.browserstack.Launcher"
  - "com.browserstack.Redirect"
  - "com.facebook.WebDriverAgentRunner.xctrunner"
  - "com.google.chrome.ios"
  - "com.geocomply.oobee"
  - "com.apple.journal"
