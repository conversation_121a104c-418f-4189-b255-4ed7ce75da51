{
    "platform": "ios_njb",
    "ssid": "{{ key "ios/wifi-ssid" }}",
    "setup_env": "Non Jailbreak",
    "env": "{{ key "environment" }}",
    "zombie_url": "{{ key "ios/zombie-url" }}",
    "region": "{{ key "region" }}",
    "sub_region": "{{ key "subregion" }}",
    "rails_endpoint": "{{ key "ios/rails-endpoint" }}",
    "session_specific_device_logger": "{{ key "ios/session-specific-device-logger" }}",
    "cls_url": "{{ key "cls-url" }}",
    "wifi_password": "{{ with secret "/kv/dc-wifi" }}{{ .Data.data.password }}{{ end }}",
    {{- with secret "/kv/admin-terminals"}}
    "admin_terminals_user": "{{ .Data.data.user }}",
    "admin_terminals_pass": "{{ .Data.data.password }}",
    {{- end }}
    {{- with secret "/kv/sinatra" }}
    "sinatra_auth_user": "{{ .Data.data.user }}",
    "sinatra_auth_pass": "{{ .Data.data.password }}",
    {{- end }}
    {{- with secret "/kv/wda-auth" }}
    "wda_auth_pass": "{{ .Data.data.password }}",
    {{- end }}
    {{- with secret "/kv/telephony" }}
    "telephony_pass": "{{ .Data.data.password }}",
    {{- end }}
    {{- with secret "/kv/staging-rails" }}
    "staging_rails_user": "{{ .Data.data.user }}",
    "staging_rails_pass": "{{ .Data.data.password }}",
    {{- end }}
    {{- with secret "/kv/camera-check/slack-token" }}
    "camera_check_slack_token": "{{ .Data.data.value }}",
    {{- end }}
    {{- with secret "/kv/camera-check/slack-channel" }}
    "camera_check_slack_channel": "{{ .Data.data.value }}",
    {{- end }}
    {{- with secret "/kv/nomad" }}
    "nomad_drain_token": "{{ .Data.data.drain_token }}",
    {{- end }}
    {{- with secret "/kv/consul-lock" }}
    "consul_lock_token": "{{ .Data.data.acl_token }}"
    {{- end }}
}
