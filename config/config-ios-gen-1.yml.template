environment: "{{ key "environment" }}"
proxy_whitelisted_hosts:
{{- range key "ios/proxy-whitelisted-hosts" | split "\n" }}
    - "{{ . }}"
{{- end }}

appium_roots:
  {
    "1.6.5": "/usr/local/.browserstack/appium_1.6.5_bstack",
    "1.7.0": "/usr/local/.browserstack/appium_1.7.0_bstack",
    "1.7.1": "/usr/local/.browserstack/appium_1.7.1_bstack",
    "1.7.2": "/usr/local/.browserstack/appium_1.7.2_bstack",
    "1.8.0": "/usr/local/.browserstack/appium_1.8.0_bstack",
    "1.9.1": "/usr/local/.browserstack/appium_1.9.1_bstack",
    "1.10.1": "/usr/local/.browserstack/appium_1.10.1_bstack",
    "1.11.1": "/usr/local/.browserstack/appium_1.11.1_bstack",
    "1.12.1": "/usr/local/.browserstack/appium_1.12.1_bstack",
    "1.13.0": "/usr/local/.browserstack/appium_1.13.0_bstack",
    "1.14.0": "/usr/local/.browserstack/appium_1.14.0_bstack",
    "1.15.0": "/usr/local/.browserstack/appium_1.15.0_bstack",
    "1.16.0": "/usr/local/.browserstack/appium_1.16.0_bstack"
  }

default_appium_version: "1.7.0"

send_alerts_to:
{{- range key "ios/send-alerts-to" | split "\n" }}
  - {{ . }}
{{- end }}

mdm_server_url: "{{ key "ios/mdm-server-url" }}"
profile_check_limit: {{ key "ios/profile-check-limit" }}

redis_url: "{{ key "ios/redis-url" }}"
mdm_redis_port: "{{ key "ios/mdm-redis-port" }}"

webdriver_agent_project_paths:
  {
    "1.6.5": "/usr/local/.browserstack/appium_1.6.5_bstack/node_modules/appium-xcuitest-driver/WebDriverAgent/WebDriverAgent.xcodeproj",
    "1.7.0": "/usr/local/.browserstack/appium_1.7.0_bstack/node_modules/appium-xcuitest-driver/WebDriverAgent/WebDriverAgent.xcodeproj",
    "1.7.1": "/usr/local/.browserstack/appium_1.7.1_bstack/node_modules/appium-xcuitest-driver/WebDriverAgent/WebDriverAgent.xcodeproj",
    "1.7.2": "/usr/local/.browserstack/appium_1.7.2_bstack/node_modules/appium-xcuitest-driver/WebDriverAgent/WebDriverAgent.xcodeproj",
    "1.8.0": "/usr/local/.browserstack/appium_1.8.0_bstack/node_modules/appium-xcuitest-driver/WebDriverAgent/WebDriverAgent.xcodeproj",
    "1.9.1": "/usr/local/.browserstack/appium_1.9.1_bstack/node_modules/appium-xcuitest-driver/WebDriverAgent/WebDriverAgent.xcodeproj",
    "1.10.1": "/usr/local/.browserstack/appium_1.10.1_bstack/node_modules/appium-xcuitest-driver/WebDriverAgent/WebDriverAgent.xcodeproj",
    "1.11.1": "/usr/local/.browserstack/appium_1.11.1_bstack/node_modules/appium-xcuitest-driver/WebDriverAgent/WebDriverAgent.xcodeproj",
    "1.12.1": "/usr/local/.browserstack/appium_1.12.1_bstack/node_modules/appium-xcuitest-driver/WebDriverAgent/WebDriverAgent.xcodeproj",
    "1.13.0": "/usr/local/.browserstack/appium_1.13.0_bstack/node_modules/appium-webdriveragent/WebDriverAgent.xcodeproj",
    "1.14.0": "/usr/local/.browserstack/appium_1.14.0_bstack/node_modules/appium-xcuitest-driver/node_modules/appium-webdriveragent/WebDriverAgent.xcodeproj",
    "1.15.0": "/usr/local/.browserstack/appium_1.15.0_bstack/node_modules/appium-webdriveragent/WebDriverAgent.xcodeproj",
    "1.16.0": "/usr/local/.browserstack/appium_1.16.0_bstack/node_modules/appium-webdriveragent/WebDriverAgent.xcodeproj"
  }

app_updates:
  - "com.apple.videos"
  - "com.apple.tv"

known_apps:
  - "com.browserstack.enterpriseDummy"
  - "com.apple.test.WebDriverAgentRunner-Runner"
  - "com.google.chrome.ios"
  - "com.browserstack.Launcher"
  - "com.browserstack.Redirect"
  - "com.apple.TestFlight"
  - "com.browserstack.app"
  - "com.apple.test.BrowserStackUITests-Runner"

known_app_display_names:
  - "App Store"
  - "Phone"
  - "Safari"
  - "Settings"
  - "Camera"
  - "Photos"
  - "PhotoBooth"
  - "Photo Booth"
  - "enterpriseDummy"
  - "TestFlight"
  - "Launcher"
  - "Redirect"
  - "Chrome"
  - "QRCode"
  - "WebDriverAgentRunner-Runner"
  - "WebDriverAgentRunner"
  - "BrowserStack"
  - "BrowserStackUITests-Runner"

developer_disk_images:
  -  "11.1"
  -  "11.2"
  -  "11.3"
  -  "11.4"

download_endpoint: "{{ key "download-endpoint" }}"
wda_source: "{{ key "wda-source" }}"

platform_category: "ios_njb_11"
machine_env: "{{ key "environment" }}"
bluetooth_check_disabled: {{ key "ios/bluetooth-check-disabled" }}

# This is not the actual DC GPS location but an approximate location,
# Just so we are in the same timezone
dc_gps_location:
{{ key "ios/dc-gps-location" | parseJSON | toYAML | indent 2 }}
