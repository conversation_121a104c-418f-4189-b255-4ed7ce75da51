require 'yaml'
require 'dotenv'
Dotenv.load('/usr/local/.browserstack/realmobile/.env')
constants = YAML.load_file("#{__dir__}/constants.yml")

BS_DIR = '/usr/local/.browserstack'
BATTERY_METRICS_DIR = "#{BS_DIR}/battery_metrics"
BS_HELPER_SCRIPT = "#{BS_DIR}/bshelper.sh"
NIX_SERVICE_FILES = "#{BS_DIR}/nix/services"

# For all steps which need manual intervention,
# we should not retry cleanup on failure. Refer rescue block in cleanup.rb
OFFLINE_REASON_INVALID_IPROXY_VERSION = "invalid iproxy version".freeze
OFFLINE_REASON_DEVICE_NOT_SUPERVISED = "manual fix required: device not supervised".freeze
OFFLINE_REASON_NEED_LANGUAGE_RESET = "Manual step required: set device language to English".freeze
OFFLINE_REASON_APPLE_PAY_SENSITIVE_DATA = "Apple Pay sensitive data present on device, require apple pay setup for reattempt".freeze
OFFLINE_REASON_RE_MDM_DEVICE = "Manual step required: re-MDM device".freeze
OFFLINE_REASON_FILES_APP_MISSING = "Manual installation required: files app missing".freeze
OFFLINE_REASON_FILES_APP_RESTRICTED = "Manual step required: needs factory reset due to files app restricted".freeze
OFFLINE_REASON_UNABLE_TO_UNINSTALL_APP = "Manual step required: needs factory reset due to unable to uninstall".freeze
OFFLINE_REASON_MDM_LOCK_ERROR = 'Locking Device Unsuccessful - MdmLockError'.freeze
OFFLINE_REASON_DEVICE_CHECK_THREAD_CRASHED = 'Device check thread crashed'.freeze
OFFLINE_REASON_DEVICE_CHECK_THREAD_TIMED_OUT = 'Device check thread timed out'.freeze
OFFLINE_REASON_PLUGGED_IN_WRONG_MACHINE = 'manual fix required: device plugged in wrong machine'.freeze
OFFLINE_REASON_DEVICE_STUCK_WITH_APPLE_ID = 'Manual step required: Erase All Contents and Settings'.freeze
OFFLINE_REASON_MINIFIED_PREFIX = 'minified cleanup'.freeze
OFFLINE_REASON_REPLAY_KIT_STILL_RUNNING = 'ReplayKit still running, device needs to reboot'.freeze
OFFLINE_REASON_ERASE_RESTORE_TIMEDOUT = 'Erase and Restore Timed out'
OFFLINE_REASON_DEVICE_NOT_TRUSTED = "manual fix required: device not trusted".freeze
SIM_DC_SETUP_FAILURE_ERROR = "SIM DC setup failing on device".freeze
SIM_ENGINEERING_SETUP_FAILURE_ERROR = "SIM Engineering setup failing on device".freeze
OFFLINE_REASON_NOT_ON_DEVICECTL = "manual fix required: device not on devicectl, reboot mac mini and trust device"
OFFLINE_REASON_DEVICE_NOT_PAIRED = "manual fix required: device not paired, click the trust popup".freeze
OFFLINE_REASON_NO_DDI = "manual fix required: DDI not enabled".freeze
OFFLINE_REASON_APPLE_ID_PRESENT = "manual fix required: Apple id present, devices needs to be resetted".freeze
OFFLINE_REASON_CHROME_CLEANUP_FAILURE = "chrome cleanup failed".freeze

VIDEO_UPLOADER = "video_uploader".freeze
IMAGE_UPLOADER = "image_uploader".freeze
FILE_CONVERTER = "file_converter".freeze
OTHER_FILES_UPLOADER = "other_files_uploader".freeze
NETWORK_FILES_UPLOADER = "network_files_uploader".freeze
SCREENSHOT_INSTRUMENTATION = "screenshot_instrumentation".freeze
SCREENSHOT_TIMEOUT_THRESHOLD = 120

NODE_20 = ENV['NODE_20'].freeze
NODE_16 = ENV['NODE_16'].freeze
NODE_14 = ENV['NODE_14'].freeze
DEVICE_LOGGER_NODE = ENV['DEVICE_LOGGER_NODE'].freeze
NODE_10 = ENV['NODE_10'].freeze
SOCAT = ENV['SOCAT'].freeze || 'socat'.freeze
PSTREE = ENV["PSTREE"].freeze || 'pstree'.freeze
UHUBCTL = '/usr/local/.browserstack/realmobile/deps/bin/uhubctl'
FFMPEG = ENV["FFMPEG"].freeze || 'ffmpeg'.freeze
FFPROBE = ENV["FFPROBE"].freeze || 'ffprobe'.freeze
IOS_DEPLOY = ENV["IOS_DEPLOY"].freeze || 'ios-deploy'.freeze
IMAGEMAGICK_CONVERT = ENV["IMAGEMAGICK_CONVERT"].freeze || 'convert'.freeze

APP_DOWNLOAD_HEADER_FILE_PREFIX_PATH = '/tmp/app_download_header_file'
SUB_REGION_APP_CACHING_PROXY_HOST = "lb-app-caching-proxy.service.prod.mobile.browserstack.com:45901"
SUBREGION_CACHING_RELATED_INSTALL_ERRORS = [/Unarchive Failed/].freeze

# Device Logger Constatns
DEVICE_LOGGER_ENTRYPOINT = '/usr/local/.browserstack/device-logger/main.js'

# idevice binaries
IDEVICE_ID = 'idevice_id'
IDEVICEINFO = 'ideviceinfo'
IDEVICENAME = 'idevicename'
IDEVICEDATE = 'idevicedate'
IDEVICEDIAGNOSTICS = 'idevicediagnostics'
IDEVICEPAIR = 'idevicepair'
IDEVICESCREENSHOT = 'idevicescreenshot'
IDEVICEIMAGEMOUNTER = 'ideviceimagemounter'
IDEVICEINSTALLER = '/usr/local/.browserstack/realmobile/deps/bin/ideviceinstaller'
IDEVICEDEBUG = 'idevicedebug'
IDEVICELOCATION = '/usr/local/.browserstack/realmobile/deps/bin/idevicelocation'
IDEVICEBOOKMARK = '/usr/local/.browserstack/realmobile/deps/bin/idevicebookmark'
IDEVICECONTACTS = '/usr/local/.browserstack/realmobile/deps/bin/idevicecontacts'
IDEVICESYSLOG = '/usr/local/bin/idevicesyslog'
IDEVICECRASHREPORT = '/usr/local/.browserstack/realmobile/deps/bin/idevicecrashreport'
PYMOBILEDEVICE3 = '/usr/local/.browserstack/realmobile/deps/bin/pymobiledevice3'

IRECOVERY = '/usr/local/.browserstack/realmobile/deps/bin/irecovery'
IPROXY = '/usr/local/.browserstack/realmobile/deps/bin/iproxy'

# Sample media for injection
SAMPLE_MEDIA_DIR = '/usr/local/.browserstack/deps/sample_media'

FILE_CONVERTER_STALE_FILE_DELETE_THRESHOLD = 1800
SCREENSHOT_INSTRUMENTATION_STALE_FILE_DELETE_THRESHOLD = 6 * 60 * 60 # Since for some users session can run for 6 hours so ideally we should delete this file only after 6 hours
FILE_UPLOADER_STALE_FILE_DELETE_THRESHOLD = 12 * 60 * 60
SCREENSHOT_INSTRUMENTATION_FILE_PICK_THRESHOLD = 20
DEVICE_RESERVED_UNDER_MINIFIED_FLOW_THRESHOLD = 65
AUT_MINIFIED_CLEANUP_MAX_THRESHOLD = 120
AA_MINIFIED_CLEANUP_MAX_THRESHOLD = 30
MAIN_CLEANUP_TERMINATION_THRESHOLD = 30 * 60
BROWSERSTACK_ERROR_STRING = "browserstack_error".freeze
USER_ERROR_STRING = "user_error".freeze
UNKNOWN_FIRECMD_BROWSERSTACK_ERROR_KIND = "unknown_platform_exception".freeze

APP_AUTOMATE_USER_ERRORS = ["This application or a bundle it contains has the same bundle identifier as this application or another bundle that it contains. Bundle identifiers must be unique", "A signed resource has been added, modified, or deleted", "This application is not built for this device", "The application does not have a valid signature", "The application bundle does not contain an executable",
                            "App extensions must define either NSExtensionMainStoryboard or NSExtensionPrincipalClass keys in the NSExtension dictionary in their Info.plist", "There was an internal API error",
                            "This app contains an app extension that specifies an extension point identifier that is not supported on this version of iOS for the value of the NSExtensionPointIdentifier key in its Info.plist", "The bundle's Info.plist does not contain a CFBundleVersion key or its value is not a string."].freeze
APP_AUTOMATE_APPSETTINGS_USER_ERROR_KINDS = [""]
BROWSERSTACK_APP_BUNDLE_ID = "com.browserstack.app".freeze
WDA_BUNDLE_ID = "com.facebook.WebDriverAgentRunner.xctrunner".freeze
WDA = "WebDriverAgentRunner-Runner"
TEMPLATES_DIR = constants["templates_dir"].freeze
STATE_FILES_DIR = constants["state_files_dir"].freeze
REBUILD_BROWSERSTACK_APP_PATH_PREFIX = File.join(STATE_FILES_DIR, "rebuild_browserstack_app_")
REDOWNLOAD_IOS13_MEDIA = File.join(STATE_FILES_DIR, "redownload_ios13_media")
REDOWNLOAD_IOS_MULTIVERSION_MEDIA = File.join(STATE_FILES_DIR, "redownload_ios_multiversion_media")
INSTALL_LAUNCHER_AND_BS_APP_PATH = File.join(STATE_FILES_DIR, "bs_launcher_install")
VPP_TOKEN_EXPIRY_FILE = File.join(STATE_FILES_DIR, "vpp_token_expiry")
VPP_ENROLLED_DEVICE_LIST_FILE = File.join(STATE_FILES_DIR, "optimised_vpp_enrolled_device_list_file.json")
GENRE_AUTOMATE = "automate".freeze
GENRE_SELENIUM = "selenium".freeze
GENRE_APP_AUTOMATE = "app_automate".freeze

APP_AUTOMATE_PARSER_DIR = "/tmp/aa_parser".freeze #This path is also used by device-logger to generate the syslog parsed greps and stores it in a file, which is then consumed by realmobile

APPS_DOWNLOAD_FOLDER = "/tmp/apps"
FEATURE_CUSTOM_MEDIA = "custom_media".freeze

CONFIG_ROOT = constants['config_root'].freeze
CONFIG_JSON_FILE = constants['config_json_file'].freeze

IFUSE = '/usr/local/bin/ifuse'.freeze
USER = 'app'.freeze

MOBILE_ROOT = constants['mobile_root'].freeze

INSTALL_PHASE = {
  name: "install_phase", # Please ensure this is unique.
  timeout: 1800 # in seconds
}.freeze

BSTACK_APP_INSTALL_ERROR_PATTERNS = ["Could not read from the device"]
# in case of app store login failures, wait atleast this amount of minutes
# before retrying to login
APPSTORE_LOGIN_ATTEMPT_DELAY = 42

BS_DIR_PATH = '/usr/local/.browserstack'
CONFIG_DIR_PATH = "#{BS_DIR_PATH}/config"
TMP_DIR_PATH = '/tmp'
ALREADY_DISMISSED_DIR_PATH = "/usr/local/.browserstack/config/already_dismissed"
TOOLS_DIRECTORY_PATH = "#{BS_DIR_PATH}/tools"
STATIC_CONF_PATH = "#{CONFIG_DIR_PATH}/static_conf.json"
LAST_INJECTED_IMAGE_PATH = 'last_injected_image'
INJECTION_MEDIA_DIR = '/tmp/injection_media'
SENSOR_MOCKER_HOST = 'sensormockerdata.browserstack.com'
HID_MOCKER_HOST = 'hidmocker.browserstack.com'
DEVICE_ID_HEADER = 'HTTP_X_BSTACKNONCE'
RUBY_BINARY_PATH = '/Users/<USER>/.rvm/rubies/ruby-2.7.2/bin/ruby'
BSTACK_REALMOBILE_BASE = "/usr/local/.browserstack/realmobile"
CHROME_BUNDLE_ID = 'com.google.chrome.ios'
GEOGUARD_BUNDLE_ID = 'com.geocomply.oobee'
CHROMIUM_BUNDLE_ID = 'browserstack.chromium.p'
MESSAGE_APP_BUNDLE_ID = 'com.apple.MobileSMS'
PRODUCTION_SITE = 'browserstack.com'
LOGGING_DIR = '/var/log/browserstack'
MOBILE_COMMON_ROOT = "#{BS_DIR_PATH}/mobile-common"
IOS_WATCHER_DETECTED_APPS_FILE_PREFIX = "#{STATE_FILES_DIR}/ioswatcher_detected_apps"
IOS_WATCHER_MIN_IOS_VERSION = 15
IS_CANARY_FILE = "#{CONFIG_DIR_PATH}/is_canary"
ENV_START_REQUEST_MIDDLEWARE_PATHS = [
  "/start",
  "/app_start",
  "/selenium_command",
  "/start_xcuitest_session",
  "/start_fluttertest_session",
  "/start_earlgrey_session",
  "/start_maestro_session",
  "/percy/start_server"
].freeze
ENV_NON_START_REQUEST_MIDDLEWARE_PATHS = [
  "/stop",
  "/cleanup",
  "/percy/stop_server",
  "/percy/minified_cleanup"
].freeze

START_REQUESTS_PATHS = [
  # "/start",
  # "/app_start",
  "/selenium_command",
  "/start_xcuitest_session"
  # "/start_fluttertest_session",
  # "/start_earlgrey_session",
  # "/percy/start_server"
].freeze

FILE_INJECTION_SUCCESS = "FILE_INJECTION_SUCCESS"
FILE_INJECTION_FAILED = "FILE_INJECTION_FAILED"

AI_PROXY_PORT_OFFSET = 5555 #update this to 5555
PWIOS_PROXY_PORT_OFFSET = 6666

BATTERY_METRICS_TO_INSTRUMENT = {
  battery_temperature: {
    plist_key: "IORegistry:VirtualTemperature",
    multiplier: 0.01
  },
  voltage: {
    plist_key: "IORegistry:Voltage",
    multiplier: 1
  },
  max_capacity: {
    plist_key: "IORegistry:MaxCapacity",
    multiplier: 1
  }
}

ZIP = ".zip"

XCPARSE_PATH = '/usr/local/.browserstack/developer-tools/xcparse/xcparse'

DEBUG_SCREENSHOTS_SCALEFACTOR = 2.0
DEBUG_SCREENSHOTS_COMPRESSION_QUALITY = 0.75

CRASH_LOG_TAG = "[crash_logs_al]"

# In Days
CLEANUP_STEPS_FREQ = {
  disable_government_notifications: 10,
  disable_dark_mode: 10,
  safari_remote_automation: 5,
  enable_wifi: 1,
  check_global_proxy_installed: 7,
  enable_location_services: 2,
  keyboard_reset: 10,
  force_clean_safari: 3,
  force_clean_apple_id: 1,
  force_clean_sandbox_account: 1,
  force_clean_safari_bookmarks: 3,
  force_clean_safari_favorites: 3,
  force_clean_files_app: 3,
  force_kill_apps: 1,
  set_time_to_utc: 10,
  check_orientation_lock: 10,
  enable_safari_web_inspector: 7,
  safari_experimental_feature_websocket_disable: 7,
  disable_standby_mode: 7,
  disable_auto_lock: 1,
  re_mdm_periodic_check: 5,
  configuration_profile_periodic_check: 1,
  reset_view_to_standard: 10,
  reset_keyboard_settings: 1,
  force_clean_apple_wallet: 3,
  preload_media_with_reboot: 1,
  enable_redirect_extension: 1,
  force_clean_testflight: 1,
  force_clean_safari_tab_groups: 1,
  force_clean_history_from_safari_app: 7,
  siri_contacts_cleanup: 1,
  phased_reboot: 1,
  set_default_font_size: 1,
  force_clean_chromium: 3,
  language_addition: 7,
  wallet_double_click_side_button: 10,
  handle_app_store_popups: 10,
  disable_testflight_background_refresh: 7,
  clean_stored_password: 7,
  clean_sms: 1,
  theme_change_cleaned: 7,
  disable_stage_manager: 7,
  disable_apple_intelligence: 1,
  restart_webkit_proxy: 1
}

APP_LIVE = "app_live"
APP_LIVE_TESTING = "app_live_testing"
LIVE = "live"
LIVE_TESTING = "live_testing"
MAX_TERMINAL_BLOCK_TIME = 4 * 60 * 60 # 4 hours
APP_PATCH_TYPE = 'app_patch_type'
BIOMETRIC_USER_OPTION_ACCESSIBLE = "biometricUserOptionAccessible"
APP_PATCH_ALL = 'all'
ALLOWED_APP_PATCH_TYPES = ['all', 'biometrics', 'camera', 'passcode', 'keychain']
PASSWORD_VISIBILITY_PATCH = 'password_visibility_patch'
KEYCHAIN_BIOMETRICS = 'keychainBioAuth'
BONJOUR_CONNECTION = 'appA11yBonjour'
BIOMETRIC_USER_OPTION = 'biometricUserOption'
APP_PRODUCTS_GENRE = ['app_live_testing', 'app_automate']
IOS_SETTINGS_INTERNAL_CELL_NAMES = ["Sign in to your iPhone", "Start Using iCloud", "Airplane Mode", "Wi-Fi",
                                    "Bluetooth", "Mobile Data", "Notifications", "Sounds & Haptics", "Do Not Disturb", "Screen Time", "General",
                                    "Control Centre", "Display & Brightness", "Home Screen", "Accessibility", "Siri & Search",
                                    "Touch ID & Passcode", "Emergency SOS", "Exposure Notifications", "Battery", "Privacy",
                                    "iTunes & App Store", "App Store", "Passwords", "Accounts & Passwords", "Contacts", "Calendar",
                                    "Reminders", "Phone", "Messages", "Safari", "Translate", "Measure", "Health", "Photos", "Camera",
                                    "Game Center", "Developer", "Chrome", "BrowserStack", "TestFlight"]
PWA_MIN_IOS_SUPPORT_VERSION = 14.0
TIMEOUT_COMMAND = 'gtimeout'
CFGUTIL_COMMAND = 'cfgutil'
CFGUTIL_ENDPOINT = 'http://localhost:45691/cfgutil'
OFFLINE_ZOMBIE_PUSH_INTERVAL = rand(10...15).freeze # To push offline data tp BQ only once every 10th to 15th device check
PERCY = 'percy'
PERCY_SESSION_TIMEOUT = 300

CONFIG_PATH = "#{BS_DIR_PATH}/realmobile/config"
PF_CONFIGURATION_DIR = "#{BS_DIR_PATH}/custom_pf_configuration"
PF_ORIGINAL_CONF = "/etc/pf.conf"
CUSTOM_PF_CONF = "#{PF_CONFIGURATION_DIR}/custom_pf.conf"
OFFLINE_MODE_DEVICES_CONF = "#{PF_CONFIGURATION_DIR}/device_conf.json"
PROVISIONING_PROFILE_DIR = '/Users/<USER>/Library/MobileDevice/Provisioning Profiles'
PROVISIONING_PROFILE_CONFIG = "#{CONFIG_ROOT}/provisioning_profile_config.json"
WS_PROXY_DEVICES_CONF = "#{BS_DIR_PATH}/realmobile/config/insecure_websocket_mapping.json"
S3_BASE_URL = 'https://s3.us-east-1.amazonaws.com/bs-stag'

#video recording related processes
VIDEO_RECORDING_PROCESSES = ["video_recording"]

#passcode
DEVICE_PASSCODE = "123456"
TIMEZONE_MIN_OS_VERSION = 12
TIMEZONE_MDM_MIN_VERSION = 14

#sim flow constants
SIM_COMPARISON_CHECK_INTERVAL = 60

AUTOMATE_APP_AUTOMATE_RTC_PARAMS = [
  "video",
  "video_file",
  "video_disable_watermark",
  "video_height",
  "video_width",
  "peer_server_url",
  "ice_servers",
  "webrtc_session_id",
  "use_replay_kit_for_interaction",
  "use_rtc_app",
  "use_rtc_app_audio",
  "videoV2Enabled",
  "appiumios",
  "iosVersion",
  "browser",
  "version",
  "device",
  "deviceName",
  "genre",
  "custom_replay_kit_params",
  "automationName",
  "video_params_v2",
  "appium",
  "orientation",
  "autoAcceptAlerts",
  "automate_session_id",
  "browserName",
  "browserVersion",
  "user_id",
  "terminal_ip",
  "logging",
  "session_id",
  "video_session_id",
  "syncTimeWithNTP",
  "forceChangeJar",
  "xmsJar",
  "xmxJar",
  "restart_svc",
  "doNotSetProxy",
  "width",
  "height",
  "debugger_ip",
  "debugger_port",
  "device_version",
  "device_static_name",
  "useReplayKit",
  "iproxy_port",
  "cls_servers",
  "mini_ip",
  "allow_long_press",
  "enable_settings_app_access",
  "enable_settings_app_access_v2",
  "useMetalAPI",
  "doMouseClickSync",
  "startFallbackStream",
  "startDelay",
  "device_name",
  "ios_mouse_interaction_improvement",
  "ios_hid_keyboard",
  "ios_merge_queuing_for_typing",
  "setScrollPerformVelocity",
  "setScrollDropDuration",
  "mainThreadBlockedInterval",
  "device_browser",
  "use_blank_url",
  "url",
  "use_ios_connect_time_improvement_logic",
  "log_wda_connect_time_buckets",
  "use_ios_connect_time_improvement_logic_phase_2",
  "use_ios_connect_time_improvement_logic_phase_3"
]

APP_LIVE_RTC_PARAMS_BLACKLIST = [
  "app_testing_aws_bucket",
  "app_testing_aws_key",
  "app_testing_aws_region",
  "app_testing_aws_secret",
  "auth_token",
  "authenticity_token"
]

# bits: <voiceover_terminal> <google_pay_terminal> <private_unrestricted_terminals> <esim> <full_cleanup_supported> <audio_injection_terminal> <apple_pay> <physical_sim> <app_terminal> <automate_terminal> <live_terminal>
# physical_sim: 0001111 : 15
# apple_pay: 0010111 : 23
# full_cleanup_supported: 1000111 : 71
# esim: 10000111 : 135
# voiceover_reserved_device: 10000000000 : 1024
# voiceover_shared_device: 10000000111 : 1031
POOL_MASK = {
  sim_enabled_device: "0001111".to_i(2),
  apple_pay_device: "0010111".to_i(2),
  full_cleanup_supported: "1000111".to_i(2),
  esim_enabled_device: "10000111".to_i(2),
  voiceover_reserved_device: "10000000000".to_i(2), # Devices reserved exclusively for voiceover sessions
  voiceover_shared_device: "10000000111".to_i(2) # Devices for voiceover sessions which can be shared by other products
}

SIM_CARRIER_MAPPING = {
  "com.apple.O2_Prepaid_UK": {
    "index_for_disabling": 1,
    "label_for_disabling": "vodafone IE",
    "index_for_enabling": 2,
    "label_for_enabling": "3"
  },
  "com.apple.Vodafone_uk": {
    "index_for_disabling": 2,
    "label_for_disabling": "IRL - METEOR",
    "index_for_enabling": 1,
    "label_for_enabling": "vodafone IE"
  }
}

# new method to delete and load media using browserstack app
ALTERNATE_MEDIA_CLEANUP_MIN_IOS_SUPPORT_VERSION = 14.1

# map of devices which support the new optimised flow
OPTIMISED_PRELOAD_MEDIA_SUPPORT_VERSION = {
  min_os_version: 18.3
}

# Optimised approach to get photos authorization
OPTIMISED_PHOTOS_AUTHORIZATION_SUPPORT_VERSION = 18.0

# list of apple pay devices on the machine
APPLE_PAY_DEVICES = begin
  File.readlines('/usr/local/.browserstack/realmobile/config/custom_devices/apple_pay_devices').map(&:split)
rescue
  []
end

APPLE_PAY_PAYMENT_NETWORKS = ['amex', 'visa', 'discover', 'mastercard']

# list of dedicated cloud apple pay devices
DEDICATED_CLOUD_APPLE_PAY_DEVICES = begin
  File.readlines('/usr/local/.browserstack/realmobile/config/custom_devices/dedicated_cloud_apple_pay_devices').map(&:split)
rescue
  []
end

# sandbox cards used in apple wallet
SANDBOX_CARDS = begin
  JSON.parse(File.read('/usr/local/.browserstack/realmobile/config/custom_devices/sandbox_cards.json'))
rescue
  {}
end

# higher memory devices
HIGHER_MEMORY_DEVICES = begin
  JSON.parse(File.read("#{CONFIG_PATH}/custom_devices/high_memory_devices.json"))
rescue
  {}
end

# voiceover devices
VOICEOVER_DEVICES = begin
  JSON.parse(File.read("#{CONFIG_PATH}/custom_devices/voiceover_devices.json"))
rescue
  {}
end

APPLE_PAY_SANDBOX_MAPPING = begin
  JSON.parse(File.read("#{CONFIG_PATH}/custom_devices/apple_pay_sandbox_mapping.json"))
rescue
  {}
end

VPP_TOKEN = begin
  File.read("#{CONFIG_PATH}/vpp_token").strip.gsub(/^"|"$/, '')
rescue
  ""
end

# currently number of preloaded contacts in ios is 40, so the new custom contacts start from 41
FIRST_CUSTOM_CONTACT_ID = 41

# Whether to capture syslogs during an xcuitest
XCUITEST_CAPTURE_SYSLOG = false

DOWNLOAD_FILE_TAG = "[download_file]"

# Download file path
FILE_PATH = "Documents/Injected Files/".freeze

# Default limit for download file zip (in mb). Overridden by params sent by railsApp
DEFAULT_DOWNLOAD_FILE_SIZE_LIMIT = 50

# Limit on allowed no of configs,targets and allowed no of code coverage artifacts list present in user uploaded v2 format xctestrun file.
# This upper limit is arbitrary. Assuming maximum of 100 targets & confgurations.
MAX_CONFIGS = 100
MAX_TARGETS = 100
MAX_CODE_COVERAGE_BUNDLES = 500

#xctestrun file formate version
XCTESTRUN_FORMAT_VERSION_KEY = "__xctestrun_metadata__:FormatVersion".freeze
V2_XCTESTRUN = "2".freeze
V1_XCTESTRUN = "1".freeze
MAESTRO_CLI_VERSION = "v2".freeze

# Time format
TWENTY_FOUR_HOUR_TIME_FORMAT = "24h"
TWELVE_HOUR_TIME_FORMAT = "12h"

# Can be removed once we have moved apps and devices from prod_60 to prod_61 provision branch
# provisioning metrics data of a device will be push to zombie once in a day
PROVISIONING_PROFILE_METRICS_PUSH_ZOMBIE_INTERVAL = 1

# handle multiple versions of Mitm
MITM_PROXIES = {
  MITM4: {
    bin: '/usr/local/bin/mitmdump', # installed via brew
    conf: '/Users/<USER>/.mitmproxy'
  },
  MITM5: {
    bin: ENV['MITMDUMP_5'],
    conf: '/Users/<USER>/.mitmproxy_5'
  },
  mitm_5_new: {
    bin: ENV['MITMDUMP_5'],
    conf: '/Users/<USER>/.mitmproxy_5_new'
  },
  MITM10: {
    bin: ENV['MITMDUMP_10'],
    conf: '/Users/<USER>/.mitmproxy_5_new'
  }
}.freeze

ROOT_CA_LIST = %w[MitmProxy_Root_Certificate MitmProxy_5_Root_Certificate]
MITM_5_CERT_VERSION = 2

# REGEX for customCertificate capability incoorect password matching
INCORRECT_CERTIFICATE_PASSWORD_REGEX = /The\s+password\s+for\s+the\s+certificate.*incorrect/

# Common app paths with required permissions for xcuitest on ios17+
XCTEST_TEST_APP_PATH_IOS17 = "/usr/local/.browserstack/xctest_session_apps_ios17"
XCTEST_TEST_APP_NAME_IOS17 = "test_app.app"
XCTEST_TEST_APP_COREDEVICE_SERVICE_CACHE_PATH = "/Users/<USER>/Library/Containers/com.apple.CoreDevice.CoreDeviceService/Data/Library/Caches/AppInstallationBinaryDeltas/"

# Xcuitest LogSplitting
XCTEST_TEST_MATCH_REGEX = /^Test Case '-\[(.*)\]' ([A-Za-z0-9_-]+)(?: \((\d+(?:\.\d+)?) seconds\))?/
XCTEST_TEST_START_TIME_REGEX = /^\s+t =\s+0.00s Start Test at (.*)/
XCTEST_FINAL_STATUS_MATCH_REGEX = /(?<=TEST EXECUTE )(?:(SUCCEEDED|FAILED))/
XCTEST_DEVICE_LOG_TIME_MATCH_REGEX = /^([A-Za-z]{3}\s+\d{1,2}\s\d{2}:\d{2}:\d{2})/
BSTACK_TEST_ERROR_RETRY_MARKER = "Retrying BrowserStack Errored Tests"
XCTEST_NETWORK_LOG_BASE_SIZE = 140 # Bytes
XCTEST_LOG_SPLIT_TEST_DEFAULT_STATUS = "failed"
XCTEST_TEST_VALIDATION_FAILED = "test_validation_failed"
XCTEST_DEVICE_LOG_BOUNDARY_NOT_VALID = "invalid_device_log_boundary"
XCTEST_VIDEO_LOG_BOUNDARY_NOT_VALID = "invalid_video_log_boundary"
XCTEST_NETWORK_LOG_BOUNDARY_NOT_VALID = "invalid_network_log_boundary"
XCTEST_INSTRU_LOG_PARSE_FAILURES = "instru_log_parse_failures"
XCTEST_INSTRU_LOG_SPLIT_FAILURES = "instru_log_split_failures"
XCTEST_DEVICE_LOG_SPLIT_FAILURES = "device_log_split_failures"
XCTEST_CRASH_LOG_SPLIT_FAILURES = "crash_log_split_failures"
XCTEST_DEVICE_LOG_SPLIT_ERROR = "device_log_split_error"
XCTEST_CRASH_LOG_SPLIT_ERROR = "crash_log_split_error"
XCTEST_VIDEO_LOG_SPLIT_ERROR = "video_log_split_error"
XCTEST_VIDEO_LOG_SPLIT_FAILURES = "video_log_split_failures"
XCTEST_NETWORK_LOG_SPLIT_ERROR = "network_log_split_error"
XCTEST_NETWORK_LOG_SPLIT_FALLBACK = "network_log_split_fallback"
XCTEST_NETWORK_LOG_SPLIT_FAILURES = "network_log_split_failures"
XCTEST_FEATURE_INSTRU_LOG_PARSE = "instrumentationLogParsing"
XCTEST_FEATURE_INSTRU_LOG_SPLIT = "instrumentationLogSplitting"
XCTEST_FEATURE_CRASH_LOG_SPLIT = "crashLogSplitting"
XCTEST_FEATURE_DEVICE_LOG_SPLIT = "deviceLogSplitting"
XCTEST_FEATURE_VIDEO_LOG_SPLIT = "videoLogSplitting"
XCTEST_FEATURE_NETWORK_LOG_SPLIT = "networkLogSplitting"
XCTEST_UNKNOWN_TEST_STATUS = "unknown_status"
XCTEST_PREV_TEST_STATUS_MISSING = "prev_test_status_missing"
XCTEST_TEST_START_MISSING = "test_start_missing"
XCTEST_TEST_START_TIME_MISSING = "test_start_time_missing"
XCTEST_TEST_START_INSTRU_BOUND_MISSING = "test_instru_log_start_bound_missing"
XCTEST_PREV_TEST_DURATION_MISSING = "prev_test_duration_missing"
XCTEST_PREV_TEST_START_TIME_MISSING = "prev_test_start_time_missing"
XCTEST_PREV_TEST_START_TIME_ERROR = "prev_test_start_time_error"
XCTEST_TEST_START_END_MISMATCH = "prev_test_start_end_mismatch"
MCSPT_DEVICE_FILES_DIR = '/usr/local/.browserstack/csp_devices'.freeze

APP_A11Y_TAG = "[APP_ACCESSIBILITY]"
APP_A11Y_TEAM = "app_a11y_dev"

MICROMDM_ENROLLMENT_PROFILE_IDENTIFIER = "com.github.micromdm.micromdm.enroll"
S3_STAGING_BUCKET = "bs-stag"
S3_PLATFORM_BUCKET = "bs-platform"
LOCALHOST_IP = "127.0.0.1"
BROADCASTHOST_IP = "***************"
BLOCKED_DOMAIN_PAGE_IP = "**************"

GROUP_DOMAIN_BLOCKING_FLAG = "#{STATE_FILES_DIR}/enable_group_domain_blocking"
CLEANED_STATE_FILE = "#{STATE_FILES_DIR}/domain_block_whitelist_cleaned"
DOMAINS_CONFIG_JSON_FILE = "#{STATE_FILES_DIR}/group_based_domain_status.json"
BLOCK_DOMAINS_FILE = "#{STATE_FILES_DIR}/privoxy_blocked_domains.txt"
WHITELIST_DOMAINS_FILE = "#{STATE_FILES_DIR}/privoxy_whitelisted_domains.txt"
ETC_HOST_DOMAINS = "#{STATE_FILES_DIR}/etc_host_domains.txt"
DOMAIN_STATUS_JSON_FILE = "#{STATE_FILES_DIR}/domain_status.json"
IOS_BACKUP_DIR = "#{STATE_FILES_DIR}/ios_backup".freeze
IOS_BACKUP_CLEANUP_FLAG = "#{STATE_FILES_DIR}/weekly_backup_file_cleanup".freeze

APP_DETAILS = {
  testflight: {
    itunes_store_id: 899247664, # this is adamId
    bundle_id: "com.apple.TestFlight",
    app_name: "TestFlight"
  },
  geoguard: {
    itunes_store_id: 921993633,
    bundle_id: GEOGUARD_BUNDLE_ID
  },
  files: {
    bundle_id: "com.apple.DocumentsApp",
    app_name: 'Files',
    app_url: "https://apps.apple.com/us/app/files/id1232058109"
  },
  messages: {
    bundle_id: "com.apple.MobileSMS",
    app_name: "Messages",
    app_url: "https://apps.apple.com/us/app/messages/id1146560473"
  },
  apple_wallet: {
    bundle_id: "com.apple.Passbook",
    app_name: "Wallet",
    app_url: "https://apps.apple.com/us/app/apple-wallet/id1160481993"
  },
  find_my: {
    bundle_id: "com.apple.findmy",
    app_name: "Find My",
    app_url: "https://apps.apple.com/us/app/find-my/id1514844621"
  },
  fitness: {
    bundle_id: "com.apple.Fitness",
    app_name: "Fitness",
    app_url: "https://apps.apple.com/us/app/fitness/id1208224953"
  },
  translate: {
    bundle_id: "com.apple.Translate",
    app_name: "Translate",
    app_url: "https://apps.apple.com/us/app/translate/id1514844618"
  },
  weather: {
    bundle_id: "com.apple.weather",
    app_name: "Weather",
    app_url: "https://apps.apple.com/us/app/weather/id1069513131"
  },
  magnifier: {
    bundle_id: "com.apple.Magnifier",
    app_name: "Magnifier",
    app_url: "https://apps.apple.com/us/app/magnifier/id1563316278"
  },
  measure: {
    bundle_id: "com.apple.measure",
    app_name: "Measure",
    app_url: "https://apps.apple.com/us/app/measure/id1383426740"
  },
  photo_booth: {
    bundle_id: "com.apple.Photo-Booth",
    app_name: "Photo Booth",
    app_url: "https://apps.apple.com/us/app/photo-booth/id1208226939"
  },
  passwords: {
    bundle_id: "com.apple.Passwords",
    app_name: "Passwords",
    app_url: "https://apps.apple.com/us/app/passwords/id6473799789"
  },
  safari: {
    bundle_id: "com.apple.mobilesafari",
    app_name: "Safari",
    app_url: "https://apps.apple.com/us/app/safari/id1146562112"
  },
  camera: {
    bundle_id: "com.apple.camera",
    app_name: "Camera",
    app_url: "https://apps.apple.com/us/app/camera/id1584216193"
  },
  photos: {
    bundle_id: "com.apple.mobileslideshow",
    app_name: "Photos",
    app_url: "https://apps.apple.com/us/app/photos/id1584215428"
  }
}

SETTINGS_PREFS = {
  sandbox_signout_prefs: {
    min_os_version: 17.0,
    rules: [
      {
        device_name: /^(iPhone|iPad).*/,
        os_version: /^18\.\d+(\.\d+)?$/,
        rule: "include",
        pref: "prefs:root=DEVELOPER_SETTINGS"
      },
      {
        device_name: /^(iPhone|iPad).*/,
        os_version: /^(17\.\d+(\.\d+)?|18\.[0-1](\.\d+)?)$/,
        rule: "include",
        pref: "prefs:root=STORE"
      }
      # {
      #   device_name: /^iPhone14,7$/,
      #   os_version: /^20\.0$/,
      #   rule: "exclude"
      # },
      # {
      #   device_name: /^iPhone15,3$/,
      #   os_version: /^(18\.4|18\.6)$/,
      #   rule: "exclude"
      # }
    ]
  },
  location_service_prefs: {
    min_os_version: 17.0,
    rules: [
      {
        device_name: /^(iPhone|iPad).*/,
        os_version: /^(17\.\d+(\.\d+)?|18\.\d+(\.\d+)?)$/,
        rule: "include",
        pref: "prefs:root=Privacy"
      }
    ]
  },
  disable_standby_prefs: {
    min_os_version: 18.0,
    rules: [
      {
        device_name: /^iPhone.*/,
        os_version: /^18\.\d+(\.\d+)?$/,
        rule: "include",
        pref: "prefs:root=AMBIENT"
      }
    ]
  },
  disable_autolock_prefs: {
    min_os_version: 18.0,
    rules: [
      {
        device_name: /^(iPhone|iPad).*/,
        os_version: /^18\.\d+(\.\d+)?$/,
        rule: "include",
        pref: "prefs:root=DISPLAY&path=AUTOLOCK"
      }
    ]
  },
  disable_government_notifications_prefs: {
    min_os_version: 18.0,
    rules: [
      {
        device_name: /^(iPhone|iPad).*/,
        os_version: /^18\.\d+(\.\d+)?$/,
        rule: "include",
        pref: "prefs:root=NOTIFICATIONS_ID"
      }
    ]
  },
  disable_low_power_mode_prefs: {
    min_os_version: 18.0,
    rules: [
      {
        device_name: /^(iPhone|iPad).*/,
        os_version: /^18\.\d+(\.\d+)?$/,
        rule: "include",
        pref: "prefs:root=BATTERY_USAGE"
      }
    ]
  },
  safari_remote_automation_prefs: {
    min_os_version: 18.0,
    rules: [
      {
        device_name: /^(iPhone|iPad).*/,
        os_version: /^18\.\d+(\.\d+)?$/,
        rule: "include",
        pref: "App-prefs:com.apple.mobilesafari"
      }
    ]
  },
  disable_testflight_background_refresh_prefs: {
    min_os_version: 18.0,
    rules: [
      {
        device_name: /^(iPhone|iPad).*/,
        os_version: /^18\.\d+(\.\d+)?$/,
        rule: "include",
        pref: "prefs:root=General&path=AUTO_CONTENT_DOWNLOAD"
      }
    ]
  },
  disable_stage_manager_pref: {
    min_os_version: 18.0,
    rules: [
      {
        device_name: /^(iPhone|iPad).*/,
        os_version: /^18\.\d+(\.\d+)?$/,
        rule: "include",
        pref: "prefs:root=com.apple.MultitaskingAndGesturesSettings"
      }
    ]
  },
  enable_redirect_extension_prefs: {
    min_os_version: 18.0,
    rules: [
      {
        device_name: /^(iPhone|iPad).*/,
        os_version: /^18\.\d+(\.\d+)?$/,
        rule: "include",
        pref: "prefs:root=SAFARI&path=WEB_EXTENSIONS"
      }
    ]
  },
  disable_apple_intelligence_prefs: {
    min_os_version: 18.1,
    rules: [
      {
        device_name: /^(iPhone|iPad).*/,
        os_version: /^18\.1(\.\d+)?$/,
        rule: "include",
        pref: "prefs:root=SIRI"
      }
    ]
  },
  set_time_to_UTC_prefs: {
    min_os_version: 18.0,
    rules: [
      {
        device_name: /^(iPhone|iPad).*/,
        os_version: /^18\.\d+(\.\d+)?$/,
        rule: "include",
        pref: "prefs:root=General&path=DATE_AND_TIME"
      }
    ]
  },
  change_sim_state_prefs: {
    min_os_version: 18.0,
    rules: [
      {
        device_name: /^iPhone.*/,
        os_version: /^18\.\d+(\.\d+)?$/,
        rule: "include",
        pref: "prefs:root=MOBILE_DATA_SETTINGS_ID"
      }
    ]
  }
}

VPP_URLS = {
  assets: "https://vpp.itunes.apple.com/mdm/v2/assets",
  assignments: "https://vpp.itunes.apple.com/mdm/v2/assignments",
  status: "https://vpp.itunes.apple.com/mdm/v2/status",
  associate: "https://vpp.itunes.apple.com/mdm/v2/assets/associate",
  disassociate: "https://vpp.itunes.apple.com/mdm/v2/assets/disassociate" #not used as of now
}

VPP_CHECKS = {
  regions: ['ap-south-1', 'us-east-1', 'us-west-1', 'eu-west-1', 'eu-central-1', 'ap-southeast-2'],
  min_os_version: 16.0,
  device_ids: [
    # Add device id in this array if you want to cover a specific device
  ],
  mobileprovision_branches: [
    "production_99_10012025",
    "production_98_07012025",
    "production_92_18092024",
    "production_91_18092024",
    "production_88_27082024",
    "production_110_30012025",
    "production_109_25112024",
    "production_100_17012025",
    "production_111_11022025",
    "production_122_02062025"
  ]
}

FULL_CLEANUP_CHECKS = {
  regions: ['ap-south-1', 'us-east-1', 'us-west-1', 'eu-west-1', 'eu-central-1', 'ap-southeast-2'],
  min_os_version: 17.4,
  max_os_version: 18.2,
  devices: [
    'iPhone15,4', # iPhone 15
    'iPhone15,5', # iPhone 15 Plus
    'iPhone16,1', # iPhone 15 Pro
    'iPhone16,2', # iPhone 15 Pro Max
    'iPhone17,3', # iPhone 16
    'iPhone17,2', # iPhone 16 Pro Max
    'iPhone17,1', # iPhone 16 Pro
    'iPhone17,4'  # iPhone 16 Plus
  ],
  device_ids: [
    # Temp fix - Adding iPhone 13/14 family iOS devices
    '00008110-001609E23CD1801E',
    '00008110-000575180CE1401E',
    '00008110-0011588801DA401E',
    '00008110-001804862EDA801E',
    '00008110-001564260AE1401E',
    '00008110-0010052E0292401E',
    '00008110-0011046E3C03801E',
    '00008110-000478E034F9801E',
    '00008110-000544AC3483801E',
    '00008110-000C050C0ED9401E',
    '00008110-0009652A1146401E',
    '00008110-000A053C0E6A401E',
    '00008110-000115A23AEA401E',
    '00008110-000C55813611401E',
    '00008110-001608642E08401E',
    '00008110-001C29810A2A801E',
    '00008110-0016456C3691401E',
    '00008110-000645A214BA401E',
    '00008110-001E61921A86801E',
    '00008110-00165C9E1407801E',
    '00008110-001641C922D9801E',
    '00008110-000125483A11801E',
    '00008110-000159A22106801E',
    '00008110-000159320CBA401E',
    '00008110-001118A8028B801E',
    '00008110-001039140C86801E',
    '00008110-00160D880213801E',
    '00008110-000A145834C3401E',
    '00008110-0004303934B9401E',
    '00008110-001169E93AD9401E',
    '00008110-000A11DC36F2401E',
    '00008110-001668623443401E',
    '00008110-001A2C1802A0201E',
    '00008110-001C78823E42401E'
  ],
  frequent_device_ids: [
    #Canary
    "00008140-000A69CA110B001C",
    "00008140-000C3D680882201C",
    "00008140-00040D940E93001C",
    "00008140-000135121113001C",
    "00008140-0016492E3493001C",
    "00008140-000A42823C8B001C",
    "00008140-000458E0210B001C",
    # iPhone 16
    # ************
    "00008140-0002344C1400801C",
    "00008140-001E5820028B001C",
    # **************
    "00008140-00020498262B001C",
    "00008140-0009642A3A2B001C",
    # ************** v18.1
    "00008140-0005646C1113001C",
    "00008140-000669E02E8B001C",
    "00008140-00195DE81A6B001C",

    # *************** iphone 16 v18.2 eu-west-1c
    "00008140-001634493607001C",
    "00008140-001449441113001C",
    # *************** iphone 16 v18.2 eu-west-1c canary
    "00008140-00091098012B001C",
    # ************* iphone 16 v18.2 us-west-1e canary
    "00008140-001A1C4E1E2B001C",
    # ************* iphone 16 v18.2 us-west-1d
    "00008140-001261D8262B001C",
    "00008140-001424A03493001C",

    # iPhone 16 Pro
    # **************
    "00008140-001478E03E33001C",
    "00008140-000E3D8C14E0801C",
    # *************
    "00008140-000615541407001C",
    "00008140-000C24D214C0801C",
    # ************** iphone 16 pro v18.2 ap-south-1c
    "00008140-001A35D01AC2801C",
    # *************** iphone 16 pro v18.2 ap-south-1c
    "00008140-001014540244801C",

    # ***************	iPhone 16 18.3 eu-west-1c
    "00008140-0002302A0A87001C",
    "00008140-001222402E0B001C",
    # *************** iphone 16 18.3 eu-west-1c canary
    "00008140-000E64DA0C93001C",
    # ************* iphone 16 18.3 us-west-1d
    "00008140-001210890E2B001C",
    "00008140-00166C2A3E07001C",
    # ************** iphone 16 18.3 ap-south-1c canary
    "00008140-00015D08148B001C",
    # ************* iphone 16 18.3 ap-south-1c
    "00008140-0001294A2E8B001C",
    "00008140-00114302212B001C",

    # iPhone 16 Plus
    # ************
    "00008140-000223EC146B001C",
    "00008140-001120540A6B001C",
    # ***************
    "00008140-0002423A210B001C",
    "00008140-000E4C241E8B001C",

    # iPhone 16 Pro Max
    # **************
    "00008140-000144861A80801C",
    "00008140-000A218411FB001C",
    "00008140-001E74DC2EC0801C",

    # *************
    "00008140-00051920369B001C",
    "00008140-000E0CA636FB001C",

    # iPhone 15
    # ***************
    "00008120-001A79AA2EE9A01E",
    "00008120-001149A90AE9A01E",
    "00008120-000E18DE2269A01E",
    "00008120-000264A01AE9A01E",
    # **************
    "00008120-001A6C3C2E38201E",
    "00008120-0012556A3C80201E",
    "00008120-0014384E1E22201E",
    "00008120-000928A20C04201E",

    # iPhone 15 Plus
    # *************
    "00008120-00165D481479A01E",
    # **************
    "00008120-000404842639A01E",
    # **************
    "00008120-001A15C81479A01E",
    # **************
    "00008120-001831281479A01E",
    # **************
    "00008120-0001790C3AE9A01E",
    "00008120-001835D83EB9A01E",
    # ***************
    "00008120-000218EE36F1A01E",
    # ***************
    "00008120-0014450C3AE9A01E",
    # ***************
    "00008120-0008690C3AE9A01E",

    # iPhone 15 Pro
    # *************
    "00008130-000C541E1461001C",
    "00008130-000605012E46001C",
    "00008130-001639241E46001C",
    "00008130-00046D1E1491001C",
    # *************
    "00008130-000625211430001C",
    "00008130-001202593460001C",
    "00008130-0018446A21C0001C",
    "00008130-0014593E0E09001C",
    "00008130-000A25091AF0001C",
    # *************
    "00008130-0001445C1E60001C",
    "00008130-000854D826C0001C",
    "00008130-001274123EF8001C",
    "00008130-0018654C0289001C",
    "00008130-000E403C1461001C",
    "00008130-001178AA21C0001C",
    # *************
    "00008130-0001348814C0001C",
    "00008130-001878490CD2001C",
    "00008130-001604A91AF0001C",
    "00008130-000128923EF8001C",
    "00008130-00023D811430001C",
    "00008130-000978A91AF0001C",
    "00008130-000C35811430001C",

    # iPhone 15 Pro Max
    # *************
    "00008130-001955A83641001C",
    "00008130-000105AA0C81001C",
    "00008130-001145D91E46001C",
    "00008130-001234C82246001C",
    "00008130-001C05592E09001C"
  ]
}

FIRST_CLEANUP_ENABLED_DEVICES = ["iPhone17,1", "iPhone17,2", "iPhone17,3", "iPhone17,4"]

EXTRA_ACCESS_RESTRICTION_KEYS = []

# JIRA: https://browserstack.atlassian.net/browse/AAP-14985
# Hosts for downloading apps should resolve directly on the device hence, ensure this constant stays updated with the latest CloudFront distribution keys
# Ref: https://github.com/browserstack/bsconfigurations/blob/6f2dab74b60b6e5613fd0d15a823da6bef484e7f/railsapp_templates/vars/prod.yml#L306
# ToDo: Remove this hardcoding and instead make it dynamic based on s3_app_url received via firecmd
PRIVOXY_WHITELISTED_APP_HOSTS = ["browserstack-userapps-prod-use1.s3.amazonaws.com", "djctfedtzsr9c.cloudfront.net", "d2899os21fcuir.cloudfront.net", "dbl4dfjz25vtl.cloudfront.net", "d24pypnh5401rz.cloudfront.net", "d1hzy56lk2ryp.cloudfront.net"]

PRELOADED_MEDIA_HASHES = [
  "edcb8a7fd30bb34c327d5ce9665e9940",
  "8a35414a5ef91c81c373da4bfef2ed7b",
  "294ce9812e1790e6430124e4214c1455",
  "c802e07b89853f80e0397d7aebe19fb1",
  "4a7f3b5528f8e0afaf98d35efebed2a0",
  "ce9b635bab8eca2294ed58346bab318e",
  "5d54bc46b74ad60d58ae40455b22174a",
  "bcfc2900ed75efefc45354ae1f0e6fd6",
  "2f6005b1abe49269842f54e8683aebc4"
]

SECRET_SCANNING_WHITELISTED_REQUESTS = {
  "/snapshot_hub" => {
    "query_hash" => [
      "key"
    ]
  }
}.freeze

FEATURE_FLAGS_CONFIG_PATH = "#{CONFIG_PATH}/feature_flags.json"

IOS_WATCHER_RESTRICTED_ACTIONS = {
  end_call: {
    state_description: "Foreground Running",
    displayID: "com.apple.InCallService"
  }
}

AA_FEATURE_USAGE = "aa_feature_usage"
