environment: "{{ key "environment" }}"
proxy_whitelisted_hosts:
{{- range key "ios/proxy-whitelisted-hosts" | split "\n" }}
    - "{{ . }}"
{{- end }}

appium_roots:
  {
    "1.9.1": "/usr/local/.browserstack/appium_1.9.1_bstack",
    "1.10.1": "/usr/local/.browserstack/appium_1.10.1_bstack",
    "1.11.1": "/usr/local/.browserstack/appium_1.11.1_bstack",
    "1.12.1": "/usr/local/.browserstack/appium_1.12.1_bstack",
    "1.13.0": "/usr/local/.browserstack/appium_1.13.0_bstack",
    "1.14.0": "/usr/local/.browserstack/appium_1.14.0_bstack",
    "1.15.0": "/usr/local/.browserstack/appium_1.15.0_bstack",
    "1.16.0": "/usr/local/.browserstack/appium_1.16.0_bstack",
    "1.17.0": "/usr/local/.browserstack/appium_1.17.0_bstack",
    "1.18.0": "/usr/local/.browserstack/appium_1.18.0_bstack",
    "1.19.1": "/usr/local/.browserstack/appium_1.19.1_bstack",
    "1.20.2": "/usr/local/.browserstack/appium_1.20.2_bstack",
    "1.21.0": "/usr/local/.browserstack/appium_1.21.0_bstack",
    "1.22.0": "/usr/local/.browserstack/appium_1.22.0_bstack"
  }

default_appium_version: "1.17.0"

send_alerts_to:
{{- range key "ios/send-alerts-to" | split "\n" }}
  - {{ . }}
{{- end }}

mdm_server_url: "{{ key "ios/mdm-server-url" }}"
profile_check_limit: {{ key "ios/profile-check-limit" }}

redis_url: "{{ key "ios/redis-url" }}"
mdm_redis_port: "{{ key "ios/mdm-redis-port" }}"

webdriver_agent_project_paths:
  {
    "1.9.1": "/usr/local/.browserstack/appium_1.9.1_bstack/node_modules/appium-xcuitest-driver/WebDriverAgent/WebDriverAgent.xcodeproj",
    "1.10.1": "/usr/local/.browserstack/appium_1.10.1_bstack/node_modules/appium-xcuitest-driver/WebDriverAgent/WebDriverAgent.xcodeproj",
    "1.11.1": "/usr/local/.browserstack/appium_1.11.1_bstack/node_modules/appium-xcuitest-driver/WebDriverAgent/WebDriverAgent.xcodeproj",
    "1.12.1": "/usr/local/.browserstack/appium_1.12.1_bstack/node_modules/appium-xcuitest-driver/WebDriverAgent/WebDriverAgent.xcodeproj",
    "1.13.0": "/usr/local/.browserstack/appium_1.13.0_bstack/node_modules/appium-webdriveragent/WebDriverAgent.xcodeproj",
    "1.14.0": "/usr/local/.browserstack/appium_1.14.0_bstack/node_modules/appium-xcuitest-driver/node_modules/appium-webdriveragent/WebDriverAgent.xcodeproj",
    "1.15.0": "/usr/local/.browserstack/appium_1.15.0_bstack/node_modules/appium-webdriveragent/WebDriverAgent.xcodeproj",
    "1.16.0": "/usr/local/.browserstack/appium_1.16.0_bstack/node_modules/appium-webdriveragent/WebDriverAgent.xcodeproj",
    "1.17.0": "/usr/local/.browserstack/appium_1.17.0_bstack/node_modules/appium-webdriveragent/WebDriverAgent.xcodeproj",
    "1.18.0": "/usr/local/.browserstack/appium_1.18.0_bstack/node_modules/appium-webdriveragent/WebDriverAgent.xcodeproj",
    "1.19.1": "/usr/local/.browserstack/appium_1.19.1_bstack/node_modules/appium-webdriveragent/WebDriverAgent.xcodeproj",
    "1.20.2": "/usr/local/.browserstack/appium_1.20.2_bstack/node_modules/appium-webdriveragent/WebDriverAgent.xcodeproj",
    "1.21.0": "/usr/local/.browserstack/appium_1.21.0_bstack/node_modules/appium-webdriveragent/WebDriverAgent.xcodeproj",
    "1.22.0": "/usr/local/.browserstack/appium_1.22.0_bstack/node_modules/appium-webdriveragent/WebDriverAgent.xcodeproj"
  }

app_updates:
  - "com.apple.videos"
  - "com.apple.tv"

known_apps:
  - "com.browserstack.enterpriseDummy"
  - "com.apple.test.WebDriverAgentRunner-Runner"
  - "com.google.chrome.ios"
  - "com.browserstack.Launcher"
  - "com.browserstack.Redirect"
  - "com.apple.TestFlight"
  - "com.browserstack.app"
  - "com.apple.test.BrowserStackUITests-Runner"

known_app_display_names:
  - "App Store"
  - "Phone"
  - "Safari"
  - "Settings"
  - "Camera"
  - "Photos"
  - "PhotoBooth"
  - "Photo Booth"
  - "enterpriseDummy"
  - "TestFlight"
  - "Launcher"
  - "Redirect"
  - "Chrome"
  - "QRCode"
  - "WebDriverAgentRunner-Runner"
  - "WebDriverAgentRunner"
  - "BrowserStack"
  - "BrowserStackUITests-Runner"

developer_disk_images:
  -  "12.0"
  -  "12.1"
  -  "12.2"
  -  "12.3"
  -  "12.4"
  -  "13.0"
  -  "13.1"
  -  "13.2"
  -  "13.3"

download_endpoint: "{{ key "download-endpoint" }}"
wda_source: "{{ key "wda-source" }}"

platform_category: "ios_njb_12"
machine_env: "{{ key "environment" }}"
bluetooth_check_disabled: true

# This is not the actual DC GPS location but an approximate location,
# Just so we are in the same timezone
dc_gps_location:
{{ key "ios/dc-gps-location" | parseJSON | toYAML | indent 2 }}
