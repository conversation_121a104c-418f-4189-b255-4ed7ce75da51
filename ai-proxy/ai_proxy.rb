require_relative '../lib/proxy/base_proxy'

class AIProxy < BaseProxy
  NODE_PATH = NODE_20
  AI_PROXY_START_TIMEOUT = 15
  AI_PROXY_START_MAX_TIMEOUT = 15

  AI_PROXY_CONFIG = {
    "ai_proxy_path" => "#{BS_DIR_PATH}/deps/automate-ai-proxy/v1",
    "log_path_wo_device" => "#{LOGGING_DIR}/ai-proxy"
  }.freeze

  def initialize(params, ai_proxy_port, selenium_port, device)
    super(params, ai_proxy_port, selenium_port, device)
    @ai_proxy_log_path = "#{AI_PROXY_CONFIG['log_path_wo_device']}_#{@device}.log"

    BrowserStack.logger.info(
      "Setting AI Proxy iOS properties: "\
      "@request_params #{@request_params}, "\
      "@device #{@device}, "\
      "@ai_proxy_port #{@proxy_port}, "\
      "@terminal_ip #{@terminal_ip}, "\
      "@selenium_port #{@upstream_port}, "\
      "@ai_proxy_log_path #{@ai_proxy_log_path} "\
    )
  end

  def start_proxy
    if proxy_running?
      BrowserStack.logger.info "AI Proxy is already running at #{@proxy_port}, killing it"
      Utils.kill_ws_proxy(@proxy_port, @terminal_ip)
    else
      BrowserStack.logger.info "AI Proxy is not running at #{@proxy_port}"
    end

    system("sudo chmod 666 #{AI_PROXY_CONFIG['ai_proxy_path']}/lib/config/config.json")
    ai_proxy_config_path = "#{AI_PROXY_CONFIG['ai_proxy_path']}/lib/config/config.json"
    ai_proxy_config_json_data = Utils.set_ws_proxy_config(
      ai_proxy_config_path,
      @terminal_ip,
      @selenium_port,
      @upstream,
      @proxy_port,
      @request_params
    )
    BrowserStack.logger.info "Setting ai_proxy_config_json_data as #{ai_proxy_config_json_data}"

    pid = Process.fork do
      command = "NODE_ENV=prod "\
      "PORT=#{@proxy_port} "\
      "UPSTREAM=#{@upstream} "\
      "HOST=#{@terminal_ip} "\
      "#{NODE_PATH} #{AI_PROXY_CONFIG['ai_proxy_path']}/cluster.js > #{@ai_proxy_log_path} 2>&1"
      BrowserStack.logger.info "Starting AI Proxy with the command #{command}"
      system(command)
    end
    Process.detach(pid)

    Timeout.timeout(AI_PROXY_START_TIMEOUT) do
      AI_PROXY_START_MAX_TIMEOUT.times do |attempt|
        sleep(1)
        if proxy_running?
          BrowserStack.logger.info(
            "AI proxy running on port #{@proxy_port}"
          )
          return true
        else
          BrowserStack.logger.info(
            "Attempt to start AI proxy: #{attempt}"
          )
        end
      end
    end
  rescue Timeout::Error => e
    BrowserStack.logger.info "Timeout unable to start AI proxy for session_id :#{@session_id} - #{e.message}"
    push_to_zombie(
      'start_ai_proxy',
      'Timeout unable to start AI proxy',
      { "session_id" => @session_id }
    )
    false
  rescue => e
    BrowserStack.logger.info "Some Error occurred while in start_ai_proxy: "\
    "#{e.inspect} #{e.message} #{e.backtrace.join("\n")}"
    push_to_zombie(
      'start_ai_proxy',
      'Some Error occurred while in start_ai_proxy',
      { "session_id" => @session_id, "message" => e.message, "backtrace" => e.backtrace.join("\n") }
    )
    false
  end

  private

  def process_name
    'node'
  end

  def platform_name
    'ios'
  end
end
