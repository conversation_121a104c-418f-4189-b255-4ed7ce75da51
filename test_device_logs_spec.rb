#!/usr/bin/env ruby

# Simple test runner to verify our device_logs tests work
# This bypasses bundle dependency issues

require 'stringio'

# Mock classes and modules
module BrowserStack
  class << self
    def logger
      @logger ||= MockLogger.new
    end
  end
end

class MockLogger
  def info(message)
    puts "INFO: #{message}"
  end

  def error(message)
    puts "ERROR: #{message}"
  end
end

# Mock IdeviceUtils
class IdeviceUtils
  def self.ideviceinfo(device, query)
    case query
    when "TimeZoneOffsetFromUTC"
      ["-25200"] # -7 hours in seconds
    else
      ["unknown"]
    end
  end
end

# Mock File operations
class MockFile
  def self.exist?(path)
    case path
    when "/var/log/browserstack/app_log_test_device.log"
      true
    else
      false
    end
  end

  def self.size(path)
    2000
  end

  def self.open(path, mode)
    content = "Dec 15 10:30:45 TestApp[123]: First log message\n" \
              "Dec 15 10:30:46 TestApp[123]: Second log message\n" \
              "Some log without timestamp\n" \
              "Dec 15 10:30:47 TestApp[123]: Third log message\n"

    mock_file = StringIO.new(content)
    yield mock_file
  end
end

# Mock the read_device_logs method
def read_device_logs(log_file_path, start_pos, num_lines, timezone_offset)
  logs = []
  lines_read = 0

  content = "Dec 15 10:30:45 TestApp[123]: First log message\n" \
            "Dec 15 10:30:46 TestApp[123]: Second log message\n" \
            "Some log without timestamp\n" \
            "Dec 15 10:30:47 TestApp[123]: Third log message\n"

  content.split("\n").each_with_index do |line, index|
    break if lines_read >= num_lines

    # Regex from constants.rb
    time_match = line.match(/^([A-Za-z]{3}\s+\d{1,2}\s\d{2}:\d{2}:\d{2})/)

    if time_match
      time_str = time_match[1]
      begin
        full_time_str = "#{Time.now.year} #{time_str}"
        parsed_time = Time.parse(full_time_str)
        parsed_time_with_offset = parsed_time - timezone_offset
        timestamp_ms = (parsed_time_with_offset.to_f * 1000).to_i
      rescue => e
        timestamp_ms = (Time.now.to_f * 1000).to_i
      end

      log_entry = {
        timestamp: timestamp_ms,
        level: "ALL",
        message: line.strip
      }
      logs << log_entry
    else
      log_entry = {
        timestamp: (Time.now.to_f * 1000).to_i,
        level: "ALL",
        message: line.strip
      }
      logs << log_entry
    end

    lines_read += 1
  end

  {
    logs: logs,
    actual_start_pos: start_pos,
    actual_end_pos: start_pos + content.length
  }
end

# Test the device logs functionality
def test_device_logs_endpoint
  puts "Testing device logs endpoint functionality..."

  # Test 1: Missing session_id
  puts "\n1. Testing missing session_id parameter"
  device = "test_device"
  session_id = nil

  if session_id.nil? || session_id.empty?
    puts "✓ PASS: Correctly identified missing session_id"
    response = "{ \"error\": \"Missing required parameter: session_id\" }"
    puts "   Response: #{response}"
  else
    puts "✗ FAIL: Should have caught missing session_id"
  end

  # Test 2: Missing device
  puts "\n2. Testing missing device parameter"
  device = nil
  session_id = "test_session_123"

  if device.nil? || device.empty?
    puts "✓ PASS: Correctly identified missing device"
    response = "{ \"error\": \"Missing required parameter: device\" }"
    puts "   Response: #{response}"
  else
    puts "✗ FAIL: Should have caught missing device"
  end

  # Test 3: File does not exist
  puts "\n3. Testing non-existent log file"
  device = "nonexistent_device"
  session_id = "test_session_123"
  log_file_path = "/var/log/browserstack/app_log_#{device}.log"
  start_pos = 0

  unless MockFile.exist?(log_file_path)
    puts "✓ PASS: Correctly identified missing log file"
    BrowserStack.logger.info("Device log file not found: #{log_file_path}")
    response = "{ \"meta\": { \"start_pos\": #{start_pos}, \"end_pos\": #{start_pos} }, \"value\": [] }"
    puts "   Response: #{response}"
  else
    puts "✗ FAIL: Should have identified missing log file"
  end

  # Test 4: Successful log reading
  puts "\n4. Testing successful log reading"
  device = "test_device"
  session_id = "test_session_123"
  log_file_path = "/var/log/browserstack/app_log_#{device}.log"
  start_pos = 0

  if MockFile.exist?(log_file_path)
    puts "✓ PASS: Log file exists"
    file_size = MockFile.size(log_file_path)
    puts "   File size: #{file_size}"

    if start_pos < file_size
      puts "✓ PASS: Start position is valid"

      timezone_offset = IdeviceUtils.ideviceinfo(device, "TimeZoneOffsetFromUTC")[0].to_s.to_f.to_i
      puts "   Timezone offset: #{timezone_offset}"

      result = read_device_logs(log_file_path, start_pos, 10000, timezone_offset)
      puts "   Logs read: #{result[:logs].length}"
      puts "   Start pos: #{result[:actual_start_pos]}, End pos: #{result[:actual_end_pos]}"

      response = "{ \"meta\": { \"start_pos\": #{result[:actual_start_pos]}, \"end_pos\": #{result[:actual_end_pos]} }, \"value\": [logs...] }"

      puts "✓ PASS: Successfully processed logs"
      puts "   Sample log entry: #{result[:logs].first}"
    else
      puts "✗ FAIL: Invalid start position"
    end
  else
    puts "✗ FAIL: Log file should exist for test_device"
  end

  puts "\n5. Testing timezone parsing failure"
  begin
    # Simulate IdeviceUtils failure
    def IdeviceUtils.ideviceinfo(device, query)
      raise StandardError.new("ideviceinfo failed")
    end

    timezone_offset = IdeviceUtils.ideviceinfo(device, "TimeZoneOffsetFromUTC")[0].to_s.to_f.to_i
    puts "✗ FAIL: Should have raised an exception"
  rescue => e
    puts "✓ PASS: Correctly caught timezone parsing failure"
    BrowserStack.logger.error("Exception in /device_logs for device #{device}: #{e.message}")
    response = "{ \"error\": \"Error retrieving device logs: #{e.message}\" }"
    puts "   Response: #{response}"
  end

  puts "\n=== All tests completed ==="
end

# Run the tests
test_device_logs_endpoint
