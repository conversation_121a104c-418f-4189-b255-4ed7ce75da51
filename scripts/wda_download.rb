require 'optparse'
require 'json'
require_relative '../lib/utils/wda_version'
require_relative '../lib/utils/osutils'
require_relative '../lib/configuration'
require_relative '../config/constants'

# Downloads and installs Web Driver Agent (Appium) from the download endpoint
class WdaDownload
  include BrowserStack

  def initialize(devices: [], appiums: [], ignore_errors: false)
    @server_config = Configuration.new.all
    @device_config = JSON.parse(File.read(CONFIG_JSON_FILE))
    @wda_source = @server_config['wda_source']
    @devices = devices
    @appiums = appiums
    @ignore_errors = ignore_errors
  end

  def download_and_install
    connected_devices = @device_config['devices'].keys
    if connected_devices.empty?
      raise 'Cannot deploy to specified devices: No devices connected' unless @devices.empty?

      exit 0
    end

    # Add all devices/appiums if not specified in arguments
    @devices = connected_devices if @devices.empty?
    @appiums = @server_config['appium_roots'].keys if @appiums.empty?

    raise 'Unable to get device ids or appiums from config' if @devices.empty? || @appiums.empty?

    # Main install for devices/appiums
    @devices.each do |device_id|
      appium_path = @server_config['default_wda_version'] ? 'webdriver_agent_project_paths' : 'appium_roots'
      appiums_for_device = @appiums.empty? ? @server_config[appium_path].keys : @appiums
      appiums_for_device.each do |appium|
        wda_version = WDAVersion.new(device_id, appium, ios_version(device_id))
        wda_version.install_wda_version
      rescue => e
        if @ignore_errors
          puts "Skipping device: #{device_id} due to error: #{e.message} #{e.backtrace.join("\n")}"
        else
          raise e
        end
      end
    end
  end

  def ios_version(device_id)
    config = @device_config['devices'][device_id]
    config.nil? ? OSUtils.device_ios_version(device_id) : config['device_version']
  end
end

if $PROGRAM_NAME == __FILE__
  options = {
    devices: [],
    appium_version: [],
    force: false
  }

  BrowserStack.init_logger('/var/log/browserstack/wda_install.log')

  OptionParser.new do |opts|
    opts.banner = 'Usage: wda_download.rb [--device <id>] [--appium <version>]'

    opts.on('--device ID', String, 'Install on specific device') do |id|
      options[:devices].push id
    end

    opts.on('--appium VERSION', String, 'Install specific Appium version') do |version|
      options[:appium_version].push version
    end
  end.parse!

  wda_download = WdaDownload.new(
    devices: options[:devices],
    appiums: options[:appium_version],
    ignore_errors: true
  )

  wda_download.download_and_install
end
