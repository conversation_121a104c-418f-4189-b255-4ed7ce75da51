#!ruby
#
require 'timeout'

require 'browserstack_logger'
require 'network_helper'
require_relative '../lib/utils/idevice_utils'
require_relative '../lib/utils/utils'
require_relative '../lib/utils/http_utils'

require_relative '../lib/configuration'

module BrowserStack
  class InventoryData

    def initialize
      @server_config = Configuration.new.all
      static_conf = @server_config['static_conf']
      @region = static_conf['region'] || 'eu-west-1'
      @infra_api = @server_config['infra_api']
      IdeviceUtils.configure(@server_config)
      logger_params = { component: 'inventory_push.rb' }
      log_file = "#{@server_config['logging_root']}/inventory_push.log"
      BrowserStack.init_logger(log_file, logger_params)
    end

    def process_device(device)
      inventory_config_file = "#{@server_config['config_root']}/inventory_data_#{device}.json"

      return Utils.read_json_file(inventory_config_file) if File.exists?(inventory_config_file)

      return nil if File.exists?("/tmp/reboot_#{device}")

      deviceinfo = IdeviceUtils.ideviceinfo(device)
      device_name = device_version = device_serial = ''
      deviceinfo.each do |info|
        next if info.split(':').length < 2

        key = info.split(':')[0]
        val = info.split(':')[1].strip
        device_name = val if key == 'ProductType'
        device_serial = val if key == 'SerialNumber'
        device_version = val if key == 'ProductVersion'
      end
      data = {
        ip: NetworkHelper::NetworkSetup.new.get_ip,
        category: "mobile",
        team: "mobile",
        owner: "BrowserStack",
        region: @region,
        memory: "-",
        cpu: "-",
        serial_number: device_serial,
        model: device_name,
        os_name: "#{IdeviceUtils.os_case_sensitive(device)}-#{device_version}",
        child_ip: "N/A",
        disk: "-"
      }

      Utils.write_to_file(inventory_config_file, data.to_json)
      data
    end

    def build_data
      connected_devices = IdeviceUtils.idevices
      connected_devices.each do |device|
        BrowserStack.logger.info("proceeding with #{device}")
        data = process_device(device)
        begin
          HttpUtils.send_post(@infra_api, data, nil, true) unless data.nil?
        rescue Timeout::Error => e
          BrowserStack.logger.error("Timeout for: #{device} - #{e.backtrace}")
        rescue Exception => e
          BrowserStack.logger.error(e.message)
        end
      end
    end
  end

  class << self
    def push_inventory_data
      inventory = InventoryData.new
      inventory.build_data
    end
  end
end

BrowserStack.push_inventory_data
