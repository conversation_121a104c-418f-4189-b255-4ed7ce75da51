REALMOBILE="/usr/local/.browserstack/realmobile/"
DOWNLOAD_ENDPOINT=$(cat $REALMOBILE/config/config.yml | grep download_endpoint | awk '{print $2}'| tr -d "\"")
DEVICES=$(gtimeout 10 bash /usr/local/.browserstack/bshelper.sh devices)
if [[ $? != 0 ]]; then
	echo "idevice_id -l is hung in this machine. Exiting..."
else
	sudo launchctl unload /Library/LaunchDaemons/com.browserstack.machine_check.plist
	launchctl unload /Library/LaunchAgents/com.browserstack.device_check.plist
	for device in $DEVICES; do
		device_type=$(bash /usr/local/.browserstack/bshelper.sh devices $device | awk '{print $2}')
		bash $REALMOBILE/scripts/iphone_firmware_upgrade/upgrade.sh $device $device_type $DOWNLOAD_ENDPOINT
	done
	sudo launchctl load /Library/LaunchDaemons/com.browserstack.machine_check.plist
fi
