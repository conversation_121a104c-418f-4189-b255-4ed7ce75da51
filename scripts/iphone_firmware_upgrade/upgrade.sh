#!/bin/sh
DEVICEID=$1
DEVICE_TYPE=$2
DOWNLOAD_ENDPOINT=${3:-**************}

if [ "$#" -ne 3 ]; then
  echo "DEVICEID and DEVICE_TYPE is required to proceed."
  exit 255
fi

if [[ $DEVICE_TYPE == "iPhone11,2" ]]; then
  IPSW_FILE_NAME="iPhone11,2_12.1_16B92_Restore.ipsw"
  MD5="699f9f8dc894644e54ff41c7afcb4e95"
elif [[ $DEVICE_TYPE == "iPhone11,6" ]]; then
  IPSW_FILE_NAME="iPhone11,4,iPhone11,6_12.1_16B92_Restore.ipsw"
  MD5="8d709d415be565c802264cad897f0ef5"
else
  echo "Device not to be upgraded."
  exit 1
fi

cd /tmp/
if [[ ! -f $IPSW_FILE_NAME ]]; then
  wget "http://$DOWNLOAD_ENDPOINT/$IPSW_FILE_NAME"
fi

md5_downloaded=$(md5 -q $IPSW_FILE_NAME )
echo "Downloaded md5: $md5_downloaded , Expected md5: $MD5"
if [[ ! "$MD5" == "$md5_downloaded" ]]; then
  echo "MD5 mismatch. Please delete the ipsw and execute the script again."
  exit 2
fi
os_version=$(ideviceinfo -u $DEVICEID | grep ProductVersion | awk '{print $2}')
if [[ ${os_version:0:4} == "12.0" ]]; then
  echo "idevicerestore -u $DEVICEID $IPSW_FILE_NAME"
  idevicerestore -u $DEVICEID $IPSW_FILE_NAME
  sleep 120
  exit 0
else
  echo "Device $DEVICEID already up-to-date on $os_version"
  exit 1
fi

