require 'rubygems'
require 'json'
require_relative '../lib/utils/zombie'

class ProxyCheck # rubocop:todo Metrics/ClassLength
  class << self
  end

  # To save the last response
  LAST_CHECKED_VALUES = {
    "toggles" => 0,
    "last_check_for_localhost" => ""
  }

  DATA = []

  def self.init(args)
    @@device = args[0]
    @@genre = args[1]
    @@session_id = args[2]
    @@privoxy_host = "localhost"
    @@privoxy_port = args[3]
    @@privoxy_conf = args[4]
    @@user_id = args[5]
    @@tunnel_proxy_host = args[6]
    @@url_to_hit = "http://ping-local.browserstack.com/local-debug/http/"
    @@proxy_check_logpath = "/var/log/browserstack/proxy_check_#{@@device}.log"
    @@proxy_check_filepath = "/tmp/proxy_check_#{@@device}"
    @@machine = get_machine_ip

    # This is to handle errors from the curl utility
    serverlog = File.open(@@proxy_check_logpath, "a")
    $stdout.reopen(serverlog)
    $stdout.sync = true
    $stderr.reopen(serverlog)
    $stderr.sync = true

    BrowserStack::Zombie.configure
  end

  def self.get_machine_ip
    config = JSON.parse(File.read('/usr/local/.browserstack/config/config.json'))
    device = config["devices"][@@device]
    device["ip"]
  end

  def self.log(message)
    puts "[#{Time.now.utc}][#{@@genre}][#{@@session_id}][#{@@privoxy_port}] - #{message}"
  end

  def self.start_pinger(*args)
    # Initialising the logger
    init(args)
    conf = File.read(@@privoxy_conf)
    log "Starting pinger for config"
    print "#{conf}\n"
    File.open(@@proxy_check_filepath, "w") {}

    response_for_localhost = get_response_for_localhost
    log "Initial response for localhost #{response_for_localhost}"

    verify_proxy

    # privoxy config should update automatically but according to docs might take a couple of requests to do so
    every_n_seconds([0.2, 0.5, 1, 2, 4]) do
      check_connection("response_for_localhost", get_response_for_localhost, "last_check_for_localhost")
    end
  end

  def self.verify_proxy
    data = {
      "machine_ip" => @@machine,
      "proxy_host" => @@tunnel_proxy_host
    }
    url = "https://local.browserstack.com/local-debug/https"

    count = 1
    while count <= 3
      started_at = Time.now.to_f
      res = `curl --tlsv1.2 -sS --connect-timeout 3 --max-time 5 --fail -x #{@@privoxy_host}:#{@@privoxy_port} #{url} 2>&1`
      if /\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}/.match(res) || count == 3
        data["response"] = res
        data["response_time"] = ((Time.now.to_f - started_at) * 1000).to_i
        data["retry"] = count
        break
      end
      count += 1
      sleep(3)
    end
    json_data = JSON.generate(data)
    log "IP verification data - #{json_data}"

    BrowserStack::Zombie.push_logs("proxy-ip-checker", "", { "data" => json_data, "browser" => @@genre, "device" => @@device, "session_id" => @@session_id, "user_id" => @@user_id })
  end

  def self.stop_pinger(device)
    `rm -f /tmp/proxy_check_#{device}`
  end

  def self.should_stop_pinger?
    unless File.exist?(@@proxy_check_filepath)
      data_to_push = DATA.first(5)
      BrowserStack::Zombie.push_logs("proxy-checker", filename, { "data" => data_to_push.join(','), "browser_version" => @@genre, "user_browser" => LAST_CHECKED_VALUES["toggles"], "session_id" => @@session_id, "user_id" => @@user_id }) if data_to_push.length > 2
      return true
    end
    false
  end

  def self.every_n_seconds(time_interval_array)
    loop do
      time_interval = time_interval_array.shift
      time_interval_array << time_interval if time_interval_array.empty?
      break if should_stop_pinger?

      before = Time.now
      yield
      interval = time_interval - (Time.now - before)
      sleep(interval) if interval > 0
    end
  end

  def self.check_connection(kind, value, last_check_key)
    last_value = LAST_CHECKED_VALUES[last_check_key]
    if value != last_value
      LAST_CHECKED_VALUES["toggles"] += 1 if last_value != ""
      LAST_CHECKED_VALUES[last_check_key] = value

      DATA << "#{Time.now.utc}-#{kind}-#{value}"
      log "#{kind} #{value}"
      check_for_privoxy_process if value.match(/refused/)
    end
  end

  def self.get_response_for_localhost
    `curl -sS --connect-timeout 1 --max-time 3 --fail -x #{@@privoxy_host}:#{@@privoxy_port} #{@@url_to_hit} 2>&1`
  end

  def self.check_for_privoxy_process
    process_data = `ps -ef | grep #{@@privoxy_conf} | grep -v grep | grep -i privoxy`
    log "Process data : #{process_data}"
  end
end

# Usage:
# ruby proxy_check.rb start #{device_id} #{genre} #{session_id} #{privoxy_port} #{privoxy_conf_path} #{user_id} #{tunnel_proxy_host}
# ruby proxy_check.rb stop #{device_id}
if __FILE__ == $PROGRAM_NAME
  case ARGV[0]
  when "start"
    ProxyCheck.start_pinger(ARGV[1], ARGV[2], ARGV[3], ARGV[4], ARGV[5], ARGV[6], ARGV[7])
  when "stop"
    ProxyCheck.stop_pinger(ARGV[1])
  else
    puts "Invalid params passed"
  end
end
