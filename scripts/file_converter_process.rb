# Description: This file performs preprocessing on files, currently only on images and only rotation is being done, but other kinds of preprocessing on files before uploading can be done here too.
require 'rmagick'
require 'fileutils'
require 'browserstack_logger'
require_relative '../lib/file_processing_queue'
require_relative '../lib/utils/osutils'
require_relative '../lib/utils/utils'
require_relative '../config/constants'
require_relative '../lib/custom_exceptions'

include Magick

$conf = BrowserStack::Configuration.new.all
$temp_screenshot_suffix = $conf['temp_screenshot_suffix_png']
$screenshot_grace_period = (ARGV[0] || 0.5).to_f

def create_upload_request(upload_request_file, img_file_name, upload_dest, session_id, start_time, check_black_screenshot, key_id=nil, secret_key=nil)
  uploader_queue_directory = $conf["images_to_upload_dir"]
  file_name = "#{uploader_queue_directory}/#{upload_request_file}"
  json_data = {
    file_name: img_file_name,
    dest: upload_dest,
    upload_type: 'screenshot',
    key_id: key_id,
    secret_key: secret_key,
    session_id: session_id,
    start_time: start_time,
    check_black_screenshot: check_black_screenshot
  }
  dir_name = File.dirname(file_name)
  FileUtils.mkdir_p(dir_name) unless Dir.exist? dir_name
  Utils.write_to_file(file_name, json_data.to_json)
  BrowserStack.logger.info("Upload request created as #{file_name}")
end

def process(request_file) # rubocop:todo Metrics/AbcSize
  convert_start_time = Time.now
  time_in_queue = begin
    (Time.now - File.mtime(request_file)).round(2)
  rescue
    -1
  end
  data = JSON.parse(File.read(request_file))
  input_file = data['file_name']
  output_file = "#{input_file[0..input_file.rindex('.') - 1]}#{(Time.now.to_f * 1000).to_i}.jpeg"
  orientation = data['orientation']
  session_id = data['session_id']
  start_time = data['start_time']
  instrumentation_file = Utils.get_screenshot_instrumentation_file($conf, session_id)
  key_id = data['key_id']
  secret_key = data['secret_key']
  use_wda = data['use_wda']
  check_black_screenshot = data['check_black_screenshot']

  if File.exist?(data["screenshot_lock_file"])
    BrowserStack.logger.error("Screenshot currently in progress.")

    sleep $screenshot_grace_period
    # Raising exception here so that retry is triggerred.
    raise "Screenshot currently in progress!"
  end

  unless File.readable?(input_file)
    BrowserStack.logger.error("Image at #{input_file} is not readable/non-existent.")

    raise NonRetryableRequestException, "Image at #{input_file} doesn't exist"
  end

  img = Image.read(input_file).first

  img.rotate!(-90) if use_wda.to_s != "true" && orientation == 'landscape'

  img.write(output_file)

  upload_request_file = File.basename(request_file).gsub("_lock", "") # filename.json_lock, filename will be a random id, very less chances of collision
  if File.exist? instrumentation_file # This check so that we push data only for debug screenshots
    time_taken = (Time.now - convert_start_time).round(2)
    Utils.update_screenshot_instrumentation_with_lock(instrumentation_file, "convert", "upload", { stage_time_taken: time_taken, queue_time: time_in_queue })
  end
  create_upload_request(upload_request_file, output_file, data['dest'], session_id, start_time, check_black_screenshot, key_id, secret_key)
  # Not deleting the input file here, as other workers might need this
  # File.delete input_file
end

queue = BrowserStack::FileProcessingQueue.new(FILE_CONVERTER, "files_to_convert_dir", 2)
logger_params = { component: "FileConverter" }
log_file = "#{$conf['logging_root']}/FileConverter.log"
BrowserStack.init_logger(log_file, logger_params)

# Removing stale image files
Thread.new do
  screenshot_dir = $conf["screenshot_dir"]
  loop do
    BrowserStack.logger.info("Removing stale files matching #{screenshot_dir + "/*.#{$temp_screenshot_suffix}"} & #{$conf['screenshot_old_dir'] + "/*.#{$temp_screenshot_suffix}"}")
    # TODO: @abdul remove the 2nd arg (/tmp/*) in Dir[] in the next deploy, it's just to handle the transient state when some screenshots files are in the old dir & some in the new dir
    Dir[screenshot_dir + "/*.#{$temp_screenshot_suffix}", $conf["screenshot_old_dir"] + "/*.#{$temp_screenshot_suffix}"].each do |file|
      if Time.now - File.mtime(file) > FILE_CONVERTER_STALE_FILE_DELETE_THRESHOLD
        BrowserStack.logger.info("Deleting stale file: #{file} ( #{Time.now - File.mtime(file)} seconds old )")
        File.delete file
      end
    end
    sleep 30
  end
end

queue.run do |request_file|
  process(request_file)
end
