#!/bin/bash --login

ROOT_DIR=/usr/local/.browserstack/realmobile
PUSH_TO_ZOMBIE="$ROOT_DIR/lib/utils/push_to_zombie.rb"
BUNDLE="$(which bundle)"

log() {
  echo "$(date -u) $1"
}


log "================ Realmobile Server Starting Now (:45671) ==========="

rvm use 2.7.2
cd $ROOT_DIR/server

$BUNDLE exec $PUSH_TO_ZOMBIE "ios" "puma-master-starting" "starting" &
bundle exec puma --config $ROOT_DIR/server/server_config.rb --debug
$BUNDLE exec $PUSH_TO_ZOMBIE "ios" "puma-master-died" "died" &

log "================ Realmobile Server Died (:45671) ==========="
