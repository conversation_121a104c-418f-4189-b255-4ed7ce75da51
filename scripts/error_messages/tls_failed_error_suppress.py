"""
This script suppresses the 502 Bad Gateway messages, mitmproxy sends if the server is not responsing
correctly. This fix has been introduced to fix the local testing not working with iOS 18.2 issue
found during developing support. https://browserstack.atlassian.net/browse/MOBPL-6508
"""

import logging
 
class TLSFailedErrorSuppress:
    
    # Kills the flow instead of returning a Bad Gateway response when client is trying to open a
    # secure connection on a non-secure port.
    def error(self, flow):
        if flow.error and flow.error.msg == "The remote server does not speak TLS.":
            flow.kill()


addons = [TLSFailedErrorSuppress()]
