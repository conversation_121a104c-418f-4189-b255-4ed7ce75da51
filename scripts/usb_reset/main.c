// REF:
// - https://libusb.info/
// - https://github.com/stawel/magiczna-palka/blob/56da5e5e85ae6e3b422442008d93b17b792daeaa/tools/oddane/odbiornik/3rd-party/stm32_usb_101/make/linux/ex_libusb.c

// This is a C binary that uses libusb to reset the iPhone / iPad devices connected to mac mini
// It needs the usb address that can be fetched from ioreg
// Pass the address as 1st argument
// how to run: ./usb_reset address

#include <libusb.h>
#include <stdio.h>
#include <unistd.h>
#include <stdlib.h>
#include <stdbool.h>

static void error(char const *, int);
static int init_usb();
static int find_device(libusb_context *);
static bool check_device(libusb_device *);
static bool valid_address(const char *);

static struct libusb_device_descriptor device_descriptor;
static int address_to_reset;

// Entry Point
int main(int argc, char *argv[]) {
  if (argc < 2) {
    fprintf(stderr, "You must provide the device address as an argument for me to to reset it.\n");
    return 1;
  }

  if (!valid_address(argv[1])){
    fprintf(stderr, "Device address must to be a short int\n");
    return 1;
  }

  char *addr = argv[1];
  address_to_reset = atoi(addr);
  return init_usb();
}

int init_usb() {
  libusb_context *context;
  int exit_code;

  exit_code = libusb_init(&context);

  if (exit_code) {
    error("libusb_init", exit_code);
  }
  else {
    exit_code = find_device(context);
    libusb_exit(context);
  }
  return exit_code;
}

int find_device(libusb_context *context) {
  libusb_device **device_list, *device;
	libusb_device_handle *handle;
  ssize_t count, i;
  int exit_code;

  // Get the connected devices list
  count = libusb_get_device_list(context, &device_list);

  if (count < 0) {
    error("libusb_get_device_list", count);
    return count;
  }

  // Loop through the connected devices and find the devices with passed address
  for (device = NULL, i = 0; i < count; i++) {
    if (check_device(device_list[i])) {
      device = device_list[i];

      printf("Device %d found.\n", address_to_reset);
      break;
    }
  }

  libusb_free_device_list(device_list, 1);

  if (! device) {
    fprintf(stderr, "Could not find the device you wanted to reset. Carefully check the USB address in the ioreg output again. :(\n");
    return 1;
  }

  // Open the device we found
  exit_code = libusb_open(device, &handle);

  if (exit_code < 0)
    error("libusb_open", exit_code);


  if (exit_code == 0) {
    // Reset the device. Real Deal is here
    libusb_reset_device(handle);
    sleep(1);
    printf("Reset done\n");
    libusb_close(handle);
  }

  return 1;
}

bool check_device(libusb_device *device) {
	const uint8_t device_address = libusb_get_device_address(device); // Returns the device_address contained by the device
  printf("Found a connected device with address %d\n", device_address);
  return device_address == address_to_reset;
}

void error(char const *function_name, int error_code) {
  fprintf(stderr, "Error in %s, error code %d\n", function_name, error_code);
}

bool valid_address(const char *arg) {
  char *endptr;
  int errno = 0;
  int value = strtol(arg, &endptr, 10);

  if (arg == endptr) return false; // no conversion occurred.
  if (value < 0 || value > SHRT_MAX) return false; // outside `short int` range
  if (*endptr != '\0') return false; // Extra junk at end

  return true;
}
