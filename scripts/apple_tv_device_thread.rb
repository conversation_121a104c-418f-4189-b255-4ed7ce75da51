require 'fileutils'

require_relative 'device_thread'
require_relative '../lib/device_setup/developer_mode/check_and_enable'

module BrowserStack
  class AppleTVDeviceThread < BrowserStack::DeviceThread # rubocop:todo Metrics/ClassLength
    def ppuid_independent_checks
      # TODO: check why would we ever have a nil or empty config?
      present_in_config = !(@config.nil? || @config.empty?)
      device_check_lock_file = "/tmp/device_check_local_lock_#{@device}"
      FileUtils.touch("/tmp/unclean_bad_enterprise_app_#{@device}") unless present_in_config
      FileUtils.touch("/tmp/orientation_lock_opened_#{@device}") unless present_in_config
      device_name = present_in_config ? @config["device_name"] : @device_name
      device_version = present_in_config ? @config["device_version"] : @device_version

      dc.check_all_contents_and_settings_erased
      dc.check_platform_version_consistency(@device_version)
      dc.check_device_supervised
      @battery_level = dc.check_battery(device_name)
      dc.airplane_mode
      dc.check_device_date
      dc.check_developer_image_mounted(@device_version, @@developer_disk_images_path)
      dc.verify_iproxy_version_for_device(device_version)
      dc.check_media_backup(@server_config, @device)
      config_for_privoxy = present_in_config ? @config : { "selenium_port" => @selenium_port, "ip" => @@ip }
      FileUtils.touch(device_check_lock_file)
      unless device_in_use?
        dc.ensure_privoxy_running_plist(
          @@user,
          @server_config,
          config_for_privoxy,
          @@logging_root
        )
      end

      begin
        FileUtils.rm(device_check_lock_file)
      rescue
        nil
      end
    end

    def ppuid_dependent_checks(new_config) # rubocop:todo Metrics/AbcSize
      ret_val = {}
      # Can remove for Apple TV?
      wda_xctestrun_file = begin
        Utils.get_web_driver_agent_xctestrun_file(new_config, @@default_appium_version)
      rescue
        nil
      end
      raise "No xctestrunfile found for appium plist" if wda_xctestrun_file.nil?

      install_phase_result = check_install_phase
      return install_phase_result if install_phase_result['online'] == false # Must perform equality check as nil value represents true

      dc.ensure_appium_server_running unless device_in_use?

      # MDM Checks
      present_in_config = !(@config.nil? || @config.empty?)
      profile_check_counter = begin
        @config["profile_check_counter"]
      rescue
        0
      end

      begin
        # Check if mdm server is up and mark device offline if SSL cert on the mdm server has expired.
        dc.check_mdm_server
        # Check the state of the device on mdm
        # 1. When force re-install of mdm profiles is expected.
        # 2. Every 15th device check (basis the profile_check_counter, currently set as 15).
        force_install = !present_in_config || @device_state.force_install_mdm_profiles_file_present?
        dc.check_mdm_settings(@device_state.proxy_pac_url(@@ip), force_install, @@profile_check_limit, @@mdm_profiles_required, @device_name, @device_version, @ex_offline_reason, profile_check_counter)
        @device_state.remove_force_install_mdm_profiles_file if force_install
      # rescue MdmApiFatalException first as it's a subclass of MdmApiException
      rescue MdmApiFatalException => e
        BrowserStack.logger.error("mdm api fatal exception for device #{@device} #{e.message} #{e.backtrace}")
        Zombie.push_logs("mdm-fatal-exception", e.message, { device: @device })
        raise MobileCheckException, "#{OFFLINE_REASON_DEVICE_NOT_SUPERVISED}: #{e.message}"
      rescue MdmApiException => e
        # Not raising MDM errors into device check errors, as we do not want device availability to be dependent on MDM
        Zombie.push_logs("mdm-exception", e.message, { device: @device })
        BrowserStack.logger.error("mdm api exception for device #{@device} #{e.message} #{e.backtrace}")
        @influxdb_client.event(@device, 'mdm-exception', subcomponent: 'ppuid-dependent-checks', is_error: true)
        # Unless it is a new device or is already offline with mdm reason
        raise MobileCheckException, "#{OFFLINE_REASON_DEVICE_NOT_SUPERVISED}: #{e.message}" if !present_in_config || @ex_offline_reason.include?(OFFLINE_REASON_DEVICE_NOT_SUPERVISED)
      end

      ret_val
    end

    private

    def check_install_phase
      install_phase_result = {}
      provision_profile = @device_state.ppuid_file_to_array[2].strip

      if install_phase_running?
        install_phase_result['offline_reason'] = begin
          if device_state.install_phase_failed_file_present?
            failure_reason = device_state.read_install_phase_failed_file
            "install-phase failed: #{failure_reason}"
          else
            'install-phase is still running'
          end
        end
        install_phase_result['online'] = false
        return install_phase_result
      end

      # Trigger install phase if install phase requirements are not empty
      install_check = InstallCheck.new(
        @device, @device_version, provision_profile, @config, full_check: @device_check_lock
      )
      install_phase_requirement = install_check.check_requirements

      if install_phase_requirement.empty?
        BrowserStack.logger.info("No need to trigger install-phase for #{@device}. Skipping install-phase...")
        device_state.remove_install_phase_failed_file
      else
        install_phase_result['online'] = false
        component_to_install = install_phase_requirement.first

        BrowserStack.logger.info("Triggering install-phase for #{@device} due to #{component_to_install}")
        trigger_install_phase_reason = trigger_install_phase(component_to_install, provision_profile)
        install_phase_result['offline_reason'] = begin
          if device_state.install_phase_failed_file_present?
            failure_reason = device_state.read_install_phase_failed_file
            "install-phase failed: #{failure_reason}"
          else
            trigger_install_phase_reason
          end
        end

        return install_phase_result
      end
      install_phase_result
    end
  end
end
