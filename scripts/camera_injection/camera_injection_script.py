# Mitmproxy: 4.0.4
# Python:    3.6.5
# OpenSSL:   OpenSSL 1.1.0h-fips  27 Mar 2018
# Platform:  Linux-4.16.3-301.fc28.x86_64-x86_64-with-fedora-28-Twenty_Eight

# usage
# mitmdump -p $LISTEN_PORT -s ./mitm.py

# Load in the javascript to inject.
with open('/usr/local/.browserstack/state_files/camera_injection/injection_script.js', 'r') as f:
    injected_javascript = f.read()

import os
import re
import urllib.parse

from bs4 import BeautifulSoup
from mitmproxy import ctx, http


class Injector:

    def load(self, loader):
        loader.add_option(
            "script", str, "", "My Script Tag"
        )

    def response(self, flow: http.HTTPFlow) -> None:
        flow.response.headers.pop("Content-Security-Policy", None)
        if "browserstack-custom-media" in flow.request.url or "browserstack-userapps-stag" in flow.request.url:
            flow.response.headers.add("Access-Control-Allow-Origin","*")
            flow.response.headers.pop("Content-Type","application/octet-stream")
            flow.response.headers.add("Content-Type","video/mp4")

        if flow.response.headers.get("content-type",'').find("text/html") != -1:
            html = BeautifulSoup(flow.response.content, "html.parser")
            if html.head:
                script = html.new_tag('script', type='text/javascript')
                script.string = injected_javascript
                html.head.insert(0, script)
                flow.response.text = str(html)

addons = [Injector()]
