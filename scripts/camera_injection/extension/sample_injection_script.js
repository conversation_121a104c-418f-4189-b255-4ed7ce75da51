const PRINT_DEBUG_LOG = false;

const LANG_TYPE = navigator.language; // en, en-US, ja

const video = document.createElement("video");
const canvas = document.createElement("canvas");
let keepAnimation = false;
let keepSound = false;
const canvasCtx = canvas.getContext("2d");
let audioCtx = null;
let audioOutput = null;
let audioGain = null;

function _debuglog(var_args) {
  console.log(`[BrowserStack][CameraInjection] ${var_args}`);
}

function _startVideoFileStream(withVideo, withAudio) {
  return new Promise((resolve, reject) => {
    _debuglog(`Inside startVideoFileStream withVideo: ${withVideo}, withAudio: ${withAudio}`)
    fetch("some_replacable_url")
      .then((response) => response.blob())
      .then((myBlob) => {
        url = URL.createObjectURL(myBlob);
        video.src = url;
        video.type = "video/mp4";
        video.loop = true;
        video.muted = true;
        video.playsInline = true;

        video
          .play()
          .then(() => {
            function loop() {
              canvasCtx.drawImage(video, 0, 0);
              window.requestAnimationFrame(loop);
            }

            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;

            const stream = returnStreamBasedOnBrowser();

            if (!stream) {
              reject("video Capture ERROR");
            }


            _debuglog(`Created Media Stream with videoTracksLength: ${stream.getVideoTracks().length}, audioTracksLength: ${stream.getAudioTracks().length}`);

            if (!withVideo && stream.getVideoTracks().length > 0) {
              // remove video track
              _debuglog("Remove video track from video");
              const videoTrack = stream.getVideoTracks()[0];
              stream.removeTrack(videoTrack);
              videoTrack.stop();
            }

            if (!withAudio && stream.getAudioTracks().length > 0) {
              // remove audio track
              _debuglog("Remove audio track from video");
              const audioTrack = stream.getAudioTracks()[0];
              stream.removeTrack(audioTrack);
              audioTrack.stop();
            }

            loop();
            resolve(stream);
          })
          .catch((err) => {
            _debuglog(`Failed video stream with error: ${err}`)
            reject(err)
          });
      });
  });
}

function returnStreamBasedOnBrowser() {
  let userAgent = navigator.userAgent;
  if (userAgent.match(/firefox/i)) {
    return video.mozCaptureStream();
  }
  return canvas.captureStream();
}

function _modifiedGetUserMedia(constraints) {
  _debuglog(`Inside getUserMedia with constraints: ${JSON.stringify(constraints)}`)
  // --- video constraints ---
  const withVideo = !!constraints.video;

  // --- audio constraints ---
  const withAudio = !!constraints.audio;

  // --- bypass for desktop capture ---
  if (constraints?.video?.mandatory?.chromeMediaSource === "desktop") {
    _debuglog("use device for Desktop Capture");
    return navigator.mediaDevices._getUserMedia(constraints);
  }

  _debuglog("Starting video stream");
  return _startVideoFileStream(withVideo, withAudio).catch((err) => {
  });
}

function _setupCanvasSize(constraints) {
  if (constraints.video?.advanced) {
    constraints.video?.advanced.forEach((item) => {
      if (item.width?.min) {
        canvas.width = item.width.min;
      }
      if (item.height?.min) {
        canvas.height = item.height.min;
      }
    });
    video.width = canvas.width;
    video.height = canvas.height;
    return;
  }
  video.width = canvas.width;
  video.height = canvas.height;
}

MediaDevices.prototype.getUserMedia = _modifiedGetUserMedia;
