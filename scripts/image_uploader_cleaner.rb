require 'fileutils'
require_relative '../lib/configuration'
require_relative '../config/constants'
require_relative '../lib/utils/utils'
require_relative '../lib/stale_file_cleaner'

require 'browserstack_logger'

module BrowserStack
  class ImageUploader<PERSON>leaner
    def handle_timedout_request(request_file)
      time_in_queue = begin
        (Time.now - File.mtime(request_file)).round(2)
      rescue
        -1
      end
      data = JSON.parse(File.read(request_file))
      session_id = data['session_id']
      instrumentation_file = Utils.get_screenshot_instrumentation_file(@conf, session_id)
      Utils.update_screenshot_instrumentation_with_lock(instrumentation_file, "upload", "failed", { queue_time: time_in_queue }) if File.exist? instrumentation_file
      begin
        File.delete(data['file_name'])
        BrowserStack.logger.info("Deleted file #{data['file_name']} ")
      rescue => e
        BrowserStack.logger.error("Exception while deleting file #{data['file_name']} reason #{e.message}")
      end
    end

    def initialize
      @conf = BrowserStack::Configuration.new.all
    end

    # delete stale upload request files
    def clean_request_files
      request_file_cleaner = BrowserStack::StaleFileCleaner.new(IMAGE_UPLOADER, @conf["images_to_upload_dir"], "*.json", FILE_UPLOADER_STALE_FILE_DELETE_THRESHOLD)
      request_file_cleaner.process_files do |request_file|
        handle_timedout_request(request_file)
      end
    end

    # delete stale images
    def clean_image_files
      screenshot_file_cleaner = BrowserStack::StaleFileCleaner.new(IMAGE_UPLOADER, @conf["screenshot_dir"], "*.#{@conf['temp_screenshot_suffix_jpeg']}", FILE_UPLOADER_STALE_FILE_DELETE_THRESHOLD + 10)
      screenshot_file_cleaner.process_files
    end
  end
end

if __FILE__ == $PROGRAM_NAME
  image_upload_cleaner = BrowserStack::ImageUploaderCleaner.new
  image_upload_cleaner.clean_request_files
  image_upload_cleaner.clean_image_files
end
