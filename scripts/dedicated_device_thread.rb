require 'fileutils'

require_relative 'device_thread'
require_relative '../lib/device_setup/developer_mode/check_and_enable'
require_relative '../lib/helpers/dedicated_video_rec_manager'
require_relative '../config/constants'

module BrowserStack
  class DedicatedDeviceThread < BrowserStack::DeviceThread
    def ppuid_independent_checks # rubocop:todo Metrics/AbcSize
      # TODO: check why would we ever have a nil or empty config?
      present_in_config = !(@config.nil? || @config.empty?)
      device_check_lock_file = "/tmp/device_check_local_lock_#{@device}"
      FileUtils.touch("/tmp/unclean_bad_enterprise_app_#{@device}") unless present_in_config
      FileUtils.touch("/tmp/orientation_lock_opened_#{@device}") unless present_in_config
      device_name = present_in_config ? @config["device_name"] : @device_name
      device_version = present_in_config ? @config["device_version"] : @device_version

      dc.check_platform_version_consistency(@device_version)
      dc.check_device_supervised
      @battery_level = dc.check_battery(device_name)
      dc.airplane_mode
      dc.check_device_date
      DeveloperMode.check_and_enable(@device) if device_version.to_i >= 16
      dc.check_developer_image_mounted(@device_version, @@developer_disk_images_path)
      dc.check_developer_symbols(@device_version, @@developer_symbols_path)
      dc.verify_iproxy_version_for_device(device_version)
      dc.check_media_backup(@server_config, @device)
      dc.verify_device_firewall_rules(@server_config, @config)
      config_for_privoxy = present_in_config ? @config : { "selenium_port" => @selenium_port, "ip" => @@ip }
      FileUtils.touch(device_check_lock_file)
      unless device_in_use?
        dc.ensure_privoxy_running_plist(
          @@user,
          @server_config,
          config_for_privoxy,
          @@logging_root
        )
      end

      begin
        FileUtils.rm(device_check_lock_file)
      rescue
        nil
      end
      dc.ensure_ios_webkit_debug_proxy_running_plist @@user, @device, @port_webkit, @@logging_root
      dc.devtools_proxy_server_running? @@devtools_port
      dc.ensure_no_sim_alert(@idevice) if ["not on ideviceinfo", "Lockdown issue # -8"].include? @ex_offline_reason
      dedicated_video_rec_manager = DedicatedVideoRecManager.new(@device)
      dedicated_video_rec_manager.write_session_video_file('DEVICE_THREAD')
    end

    def update_sim_details(temp_json)
      temp_json['sim_details'] = DeviceSIMHelper.dedicated_device_sim_info(@device)

      if DeviceSIMHelper.sim?(@device) &&
        (!@device_state.sim_conflict_file_present? ||
         @device_state.sim_conflict_file_older_than_minutes?(SIM_COMPARISON_CHECK_INTERVAL))

        sim_conflict_details = DeviceSIMHelper.compare_sim_details(@device) || {}
        old_sim_conflict_details = temp_json['sim_conflict_details'] || {}
        temp_json['sim_conflict_details'] = sim_conflict_details
        temp_json['sim_conflict_last_updated_time'] = Time.now.strftime("%Y-%m-%d %H:%M:%S") if old_sim_conflict_details != sim_conflict_details
        @device_state.write_to_sim_conflict_file(JSON.generate(sim_conflict_details))
      end
    rescue => e
      BrowserStack.logger.error("Error updating sim details: #{e.message} #{e.backtrace}")
    end
  end
end
