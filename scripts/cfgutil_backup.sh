#!/bin/bash --login
set -euxo pipefail

DEVICE=$1
get_ecid () {
    cfgutil list-devices | grep "$DEVICE" | awk '{print $4;}'
}
ECID=$(get_ecid)
BACKUP_DIR="/Users/<USER>/Library/Application Support/MobileSync/Backup"
echo "ECID is $ECID"

# Remove Backup Directory for the UDID
rm -rf  "$BACKUP_DIR/$DEVICE/"
echo "Removed Backup Folder"

# Regenerate Backup using cfgutil
cfgutil -e "$ECID" backup
echo "Backed up successfully"

cd "$BACKUP_DIR"/"$DEVICE"

# Zip the backup
zip -r /tmp/"upload_backup_$DEVICE.zip" .
echo "Zipping Completed"


get_model () {
    bash /usr/local/.browserstack/bshelper.sh config $DEVICE | jq '.device_name' | tr -d '"'
}

get_version () {
    bash /usr/local/.browserstack/bshelper.sh config $DEVICE | jq '.device_version' | tr -d '"'
}

get_sub_region () {
    bash /usr/local/.browserstack/bshelper.sh config $DEVICE | jq '.sub_region' | tr -d '"'
}

MODEL=$(get_model)
VERSION=$(get_version)
SUB_REGION=$(get_sub_region)
UPLOAD_URL="http://s3.amazonaws.com/bs-mobile/ios-njb-backups/"$MODEL"_"$VERSION"_"$SUB_REGION"_backup_v2.zip"

# Upload Backup to S3
echo "Uploading to $UPLOAD_URL"
/usr/local/bin/s3curl --id bs --put='/tmp/upload_backup_'$DEVICE'.zip' $UPLOAD_URL
echo "Backup Uploaded successfully"
