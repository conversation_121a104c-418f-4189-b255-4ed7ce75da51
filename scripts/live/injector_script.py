"""
<AUTHOR>
This inline script can be used to inject scripts in webpage.

example cmdline invocation:
mitmdump -s proxy2.py --set interaction_sync_host=interaction-server.browserstack.com --set user_id=318 --set interaction_script=/abc/edf/123.js
"""

from mitmproxy import ctx

class Injector:
    def load(self, loader):
        loader.add_option(
            "interaction_sync_host", str, "", "HOST for interaction server"
        )
        loader.add_option(
            "user_id", str, "", "User ID for connecting to interaction server"
        )
        loader.add_option(
            "interaction_script", str, "", "The location JS script that needs to be injected"
        )
        loader.add_option(
            "tab_id", str, "", "The tab_id used to contain interactions"
        )
        loader.add_option(
            "session_id", str, "", "The session_id used to contain interactions"
        )
        loader.add_option(
            "device_id", str, "", "The device_id used to contain interactions"
        )
        loader.add_option(
            "override_csp", str, "", "The override_csp is used to decide if CSP header is to be overrided or not"
        )

    def configure(self, updates):
        with open(ctx.options.interaction_script, 'r') as file:
            interaction_script = file.read()
            interaction_script = interaction_script.replace("{%HOST%}", ctx.options.interaction_sync_host)
            interaction_script = interaction_script.replace("{%USER_ID%}", ctx.options.user_id)
            interaction_script = interaction_script.replace("{%TAB_ID%}", ctx.options.tab_id)
            interaction_script = interaction_script.replace("{%SESSION_ID%}", ctx.options.session_id)
            interaction_script = interaction_script.replace("{%DEVICE_ID%}", ctx.options.device_id)
            self.interaction_script = interaction_script

    def response(self, flow):
        log_data = f"{flow.request.url}, user_id: {ctx.options.user_id}, tab_id: {ctx.options.tab_id}, session_id: {ctx.options.session_id}, device_id: {ctx.options.device_id}"
        try:
            if (
                ('browserstack.com/browser-sync/socket.io/?user_id' in flow.request.url) or
                (not 'content-type' in flow.response.headers) or 
                ('content-type' in flow.response.headers) and (not ("html" in flow.response.headers['content-type']))
                ):
                return
            data = flow.response.content.decode()
            index = data.find('</html>')
            if index == -1:
                index = data.find('</body>')
                if index == -1:
                    return
            data = data[:index] + f"<script id='browserstack-interaction-sync'>{self.interaction_script}</script>" +data[index:]
            flow.response.content = data.encode('utf-8')
            if ctx.options.override_csp == 'true':
                flow.response.headers['content-security-policy'] = ''
            ctx.log.info(f"[injector_script] successful for {log_data}")
        except Exception as e:
            ctx.log.error(f"[injector_script] failed for {log_data} with error #{e}")

addons = [Injector()]
