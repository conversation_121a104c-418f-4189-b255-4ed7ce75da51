require 'fileutils'
require 'browserstack_logger'
require_relative '../lib/file_processing_queue'
require_relative '../lib/utils/utils'
require_relative '../config/constants'
require_relative '../lib/utils/zombie'
require_relative '../lib/utils/helpers'
require_relative '../lib/overridden/thread'

class ScreenShotInstrumentation
  attr_reader :conf

  def initialize
    @conf = BrowserStack::Configuration.new.all
    logger_params = { component: "ScreenshotInstrumentation" }
    log_file = "#{@conf['logging_root']}/screenshot_instrumentation.log"
    BrowserStack.init_logger(log_file, logger_params)

    FileUtils.mkdir_p(@conf["screenshot_instrumentation_dir"] ) unless Dir.exist? @conf["screenshot_instrumentation_dir"]
  end

  def process(request_file)
    BrowserStack.logger.info("Got request for: #{request_file}")
    data = JSON.parse(File.read(request_file))
    session_id = data["session_id"]
    genre = data["genre"]

    instrumentation_file = Utils.get_screenshot_instrumentation_file(@conf, session_id)
    instrumentation_data = begin
      JSON.parse(File.read(instrumentation_file))
    rescue
      {}
    end

    unless instrumentation_data.empty?
      calculate_and_push_performance_metrics(instrumentation_data, session_id, genre)
      calculate_and_push_feature_stability_metrics(instrumentation_data, session_id, genre) unless (Time.now - File.mtime(request_file)) / 60 > 30
    end

    File.delete(instrumentation_file) if File.exist?(instrumentation_file)
    File.delete("#{instrumentation_file}.lock") if File.exist?("#{instrumentation_file}.lock")
  end

  def calculate_and_push_feature_stability_metrics(data, session_id, genre)

    feature_usage = {}
    data_to_push = {
      total_screenshots: data["total_screenshots"],
      capture_stage: data["capture_stage"],
      convert_stage: data["convert_stage"],
      upload_stage: data["upload_stage"],
      convert_failed: data["convert_failed"],
      upload_failed: data["upload_failed"],
      capture_failed: data["capture_failed"],
      capture_failed_buckets: data["capture_failed_buckets"],
      success_stage: data["success_stage"],
      timeout_stage: data["timeout_stage"],
      total_black_screenshots: data["total_black_screenshots"],
      total_non_black_screenshots: data["total_non_black_screenshots"],
      error_check_black_screenshots: data["error_check_black_screenshots"]
    }

    feature_usage["debug_screenshots"] = data_to_push
    data_to_push_to_eds = {
      feature_usage: feature_usage,
      hashed_id: session_id,
      timestamp: Time.now.to_i
    }

    eds_event = ['selenium', 'js_testing', 'automate'].include?(genre) ? EdsConstants::AUTOMATE_TEST_SESSIONS : EdsConstants::APP_AUTOMATE_TEST_SESSIONS
    Utils.send_to_eds(data_to_push_to_eds, eds_event, true)
  rescue => e
    BrowserStack.logger.error "#{session_id} send_feature_usage_to_eds, Error: #{e.message} #{e.backtrace}"
    kind = genre == "app_automate" ? "app-" : ""
    zombie_data = {
      "session_id" => session_id,
      "data" => {
        "url" => "Failed to push screenshot instrumentation data"
      }
    }
    BrowserStack::Zombie.push_logs("#{kind}feature-usage-failed", e.message, zombie_data)

  end

  def calculate_and_push_performance_metrics(data, session_id, genre) # rubocop:todo Metrics/AbcSize
    json_data = {
      avg_screenshot_time: data["total_time"] / ((data["success_stage"].to_i + data["timeout_stage"].to_i).nonzero? || 1),
      avg_screenshot_capture_time: data["total_capture_time"] / ((data["total_screenshots"] - data["capture_stage"]).nonzero? || 1),
      avg_convert_process_time: data["total_convert_time"] / ((data["total_screenshots"] - data["convert_stage"] - data["capture_failed"]).nonzero? || 1),
      avg_upload_process_time: data["total_upload_time"] / ((data["total_screenshots"] - data["upload_stage"] - data["capture_failed"] - data["convert_failed"]).nonzero? || 1),
      avg_convert_queue_time: data["total_convert_queue_time"] / ((data["total_screenshots"] - data["convert_stage"] - data["capture_failed"]).nonzero? || 1),
      avg_upload_queue_time: data["total_upload_queue_time"] / ((data["total_screenshots"] - data["upload_stage"] - data["capture_failed"] - data["convert_failed"]).nonzero? || 1),
      max_screenshot_time: data["max_time"],
      max_screenshot_capture_time: data["max_capture_time"],
      max_screenshot_convert_time: data["max_convert_time"],
      max_screenshot_upload_time: data["max_upload_time"],
      max_convert_queue_time: data["max_convert_queue_time"],
      max_upload_queue_time: data["max_upload_queue_time"]
    }

    params = {
      "genre" => genre,
      "automate_session_id" => session_id
    }

    push_to_cls(params, "screenshot_instrumentation", nil, json_data)
    BrowserStack.logger.info("Data pushed to CLS: #{json_data}")
  end
end

if __FILE__ == $PROGRAM_NAME
  screenshot_instrumentation = ScreenShotInstrumentation.new
  queue = BrowserStack::FileProcessingQueue.new(SCREENSHOT_INSTRUMENTATION, "screenshot_instrumentation_dir")

  #Removing stale files
  Thread.bs_run do
    screenshot_dir = screenshot_instrumentation.conf["screenshot_dir"]
    loop do
      BrowserStack.logger.info("Removing stale files matching #{screenshot_dir + "/#{screenshot_instrumentation.conf['screenshot_instrumentation_prefix']}*"} & #{screenshot_instrumentation.conf['screenshot_old_dir'] + "/#{screenshot_instrumentation.conf['screenshot_instrumentation_prefix']}*"}")
      # TODO: @abdul remove the 2nd arg (/tmp/*) in Dir[] in the next deploy, it's just to handle the transient state when some screenshots files are in the old dir & some in the new dir
      Dir[screenshot_dir + "/#{screenshot_instrumentation.conf['screenshot_instrumentation_prefix']}*", screenshot_instrumentation.conf["screenshot_old_dir"] + "/#{screenshot_instrumentation.conf['screenshot_instrumentation_prefix']}*"].each do |file|
        if Time.now - File.mtime(file) > SCREENSHOT_INSTRUMENTATION_STALE_FILE_DELETE_THRESHOLD
          BrowserStack.logger.info("Deleting stale file: #{file} ( #{Time.now - File.mtime(file)} seconds old )")
          File.delete file
        end
      end
      sleep 30
    end
  end

  queue.run do |request_file|
    screenshot_instrumentation.process(request_file)
  end
end
