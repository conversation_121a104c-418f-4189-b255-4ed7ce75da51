"""
This inline script can be used to dump flows as HAR files.

Please make sure this script is same as the one present here:
    https://github.com/mitmproxy/mitmproxy/blob/master/examples/complex/har_dump.py
Choose the file present in the branch/tag applicable for your mitmproxy version.

ALSO MAKE SURE THAT YOU IGNORE bsstag.com URLs

example cmdline invocation:
mitmdump -s ./har_dump.py --set hardump=./dump.har --set testDetails="<pathToTestDetailsJson>"

filename endwith '.zhar' will be compressed:
mitmdump -s ./har_dump.py --set hardump=./dump.zhar --set testDetails="<pathToTestDetailsJson>

Splitting Logs Logic:
- Assuming, tests' start_time as always increasing. This pattern matches the flow file timing order.
- Test Details Schema: { "test_name": { "start_time": "", "network_log_boundary": { "start": "", "end": "" } } }
- Maintain 2 objects to track which tests are we processing: CURR_TEST, NEXT_TEST
- TEST_HAR object contains the actual har body for CURR_TEST.
- HAR object contains the concatenation of all TEST_HAR objects.
- Inside response() hook:
    - If CURR_TEST is null
        - Find out CURR_TEST & NEXT_TEST
    - If response_time < CURR_TEST.START_TIME
        - Add this nwcall to TEST_HAR. This could happen only for the first test.
    - If response_time >= CURR_TEST.START_TIME
        - If NEXT_TEST is null
            - Update TEST_HAR with this nwcall. This could happen only for last test.
            - In done() hook, dump all contents of TEST_HAR.
        - If response_time < NEXT_TEST.START_TIME
            - Update TEST_HAR
        - If response_time >= NEXT_TEST.START_TIME
            - Dump & Reset TEST_HAR. Update CURR_TEST.network_log_boundary.
            - Update CURR_TEST and NEXT_TEST
            - Goto its parent if condition(line 27).
"""


import json
import base64
import zlib
import os
import re
import typing  # noqa

from datetime import datetime
from datetime import timezone

import mitmproxy

from mitmproxy import connection
from mitmproxy import version
from mitmproxy import ctx
from mitmproxy.utils import strutils
from mitmproxy.net.http import cookies
import urllib.request


HAR: bytes = bytes()
TEST_HAR: typing.Dict = {}

TEST_DETAILS: typing.Dict = {}
TEST_DETAILS_LIST: typing.List = []

CURR_TEST: typing.Dict = {}
CURR_TEST_INDEX: int = 0

NEXT_TEST: typing.Dict = {}
NEXT_TEST_INDEX: int = CURR_TEST_INDEX + 1

try:
    ip = open("/usr/local/.browserstack/whatsmyip").read().strip()
except FileNotFoundError:
    ip = urllib.request.urlopen('https://ident.me').read().decode('utf8')

mitmproxy.ctx.log(f"IP address {ip}")
ip_match = re.compile("http://" + ip + ":")

# A list of server seen till now is maintained so we can avoid
# using 'connect' time for entries that use an existing connection.
SERVERS_SEEN: typing.Set[connection.Server] = set()


def load(l):
    l.add_option(
        "hardump",
        str,
        "",
        "Save a HAR file with all flows on exit. "
        "You may select particular flows by setting save_stream_filter.",
        "For mitmdump, enabling this option will mean that flows are kept in memory. ",
    )
    l.add_option(
        "customHardump",
        str,
        "",
        "Save a custom HAR file with all flows on exit. "
        "You may select particular flows by setting save_stream_filter.",
        "For mitmdump, enabling this option will mean that flows are kept in memory. ",
    )
    l.add_option(
        "captureContent",
        bool,
        False,
        "Capture content body",
        "You may select particular flows by setting save_stream_filter.",
    )
    l.add_option(
        "testDetails",
        str,
        "",
        "Test Details for nwLogs Splitting"
        "For XCUITest Log Splitting",
        "This script parses dumpfile to get the networkLogs splitting",
    )


def configure(updated):
    global TEST_DETAILS_LIST, TEST_DETAILS, CURR_TEST, CURR_TEST_INDEX, NEXT_TEST, NEXT_TEST_INDEX
    if ctx.options.testDetails:
        try:
            with open(ctx.options.testDetails, 'r') as test_details_file:
                test_json = json.load(test_details_file)
            TEST_DETAILS.update(test_json)
        except Exception as err:
            exit("Could not read test details: " + str(err))

    TEST_DETAILS_LIST = list(TEST_DETAILS.values())
    reset_test_har()

    # For first request, initialise CURR_TEST and NEXT_TEST
    CURR_TEST_INDEX = 0
    CURR_TEST, CURR_TEST_INDEX = fetch_next_valid_test(CURR_TEST_INDEX)

    NEXT_TEST_INDEX = CURR_TEST_INDEX + 1
    NEXT_TEST, NEXT_TEST_INDEX = fetch_next_valid_test(NEXT_TEST_INDEX)


def response(flow):
    """
       Called when a server response has been received.
    """

    global CURR_TEST, CURR_TEST_INDEX, TEST_HAR

    # This is a fatal error. Do not proceed to map requests to test.
    if not CURR_TEST:
        exit("CURR_TEST not defined " + str(CURR_TEST_INDEX))

    if flow.request.url and (re.match(r"http[s]*:\/\/mobile-.*\.(browserstack|bsstag)\.com(:*\d*\/*\w*)$|http[s]*:\/\/sensormockerdata\.(browserstack|bsstag)\.com(:*\d*\/*\w*)|http[s]*:\/\/percy\.cli.*$", flow.request.url) is not None or ip_match.match(flow.request.url) is not None):
        return

    # -1 indicates that these values do not apply to current request
    ssl_time = -1
    connect_time = -1

    if flow.server_conn and flow.server_conn not in SERVERS_SEEN:
        try:
            connect_time = (flow.server_conn.timestamp_tcp_setup -
                            flow.server_conn.timestamp_start)
        except:
            connect_time = 0
        
        if flow.server_conn.timestamp_tls_setup is not None:
            try:
                ssl_time = (flow.server_conn.timestamp_tls_setup -
                            flow.server_conn.timestamp_tcp_setup)
            except:
                ssl_time = 0

        SERVERS_SEEN.add(flow.server_conn)

    # Calculate raw timings from timestamps. DNS timings can not be calculated
    # for lack of a way to measure it. The same goes for HAR blocked.
    # mitmproxy will open a server connection as soon as it receives the host
    # and port from the client connection. So, the time spent waiting is actually
    # spent waiting between request.timestamp_end and response.timestamp_start
    # thus it correlates to HAR wait instead.
    send_time = receive_time = wait_time = -1
    try:
        send_time = (flow.request.timestamp_end - flow.request.timestamp_start)
        receive_time = (flow.response.timestamp_end - flow.response.timestamp_start)
        wait_time = (flow.response.timestamp_start - flow.request.timestamp_end)
    except:
        send_time = receive_time = wait_time = 0

    timings_raw = {
        'send': send_time,
        'receive': receive_time,
        'wait': wait_time,
        'connect': connect_time,
        'ssl': ssl_time,
    }

    # HAR timings are integers in ms, so we re-encode the raw timings to that format.
    timings = dict([(k, int(1000 * v)) for k, v in timings_raw.items()])

    # full_time is the sum of all timings.
    # Timings set to -1 will be ignored as per spec.
    full_time = sum(v for v in timings.values() if v is not None and v >= 0)

    started_date_time = datetime.fromtimestamp(flow.request.timestamp_start, timezone.utc).isoformat()

    # Response body size and encoding
    response_body_size = len(flow.response.raw_content)
    response_body_decoded_size = len(flow.response.get_content(strict=False))
    response_body_compression = response_body_decoded_size - response_body_size

    entry = {
        "startedDateTime": started_date_time,
        "time": full_time,
        "request": {
            "method": flow.request.method,
            "url": flow.request.url,
            "httpVersion": flow.request.http_version,
            "cookies": format_request_cookies(flow.request.cookies.fields),
            "headers": name_value(flow.request.headers),
            "queryString": name_value(flow.request.query or {}),
            "headersSize": len(str(flow.request.headers)),
            "bodySize": len(flow.request.content),
        },
        "response": {
            "status": flow.response.status_code,
            "statusText": flow.response.reason,
            "httpVersion": flow.response.http_version,
            "cookies": format_response_cookies(flow.response.cookies.fields),
            "headers": name_value(flow.response.headers),
            "content": {
                "size": response_body_size if ctx.options.captureContent else 0,
                "compression": response_body_compression if ctx.options.captureContent else 0,
                "mimeType": flow.response.headers.get('Content-Type', '')
            },
            "redirectURL": flow.response.headers.get('Location', ''),
            "headersSize": len(str(flow.response.headers)),
            "bodySize": response_body_size,
        },
        "cache": {},
        "timings": timings,
    }

    # if captureContent is false do not capture content text
    if(ctx.options.captureContent):
        # Store binary data as base64
        if strutils.is_mostly_bin(flow.response.get_content(strict=False)):
            entry["response"]["content"]["text"] = base64.b64encode(flow.response.get_content(strict=False)).decode()
            entry["response"]["content"]["encoding"] = "base64"
        else:
            entry["response"]["content"]["text"] = flow.response.get_text(strict=False)

    if flow.request.method in ["POST", "PUT", "PATCH"]:
        params = [
            {"name": a, "value": b}
            for a, b in flow.request.urlencoded_form.items(multi=True)
        ]
        entry["request"]["postData"] = {
            "mimeType": flow.request.headers.get("Content-Type", ""),
            "text": flow.request.get_text(strict=False),
            "params": params
        }

    if flow.server_conn.connected:
        addr = flow.server_conn.peername or flow.server_conn.address
        entry["serverIPAddress"] = str(addr[0])

    request_timestamp = datetime.fromtimestamp(flow.request.timestamp_start, timezone.utc).timestamp()

    # Request happened before test execution. Ideally this should never happen.
    # To handle all requests, pushing it to first TEST_HAR object
    if request_timestamp < epoch(CURR_TEST['start_time']):
        TEST_HAR["log"]["entries"].append(entry)

    # Request happened after current test execution. It may or may not belong to this test.
    if request_timestamp >= epoch(CURR_TEST['start_time']):
        map_request_to_test(request_timestamp, entry)


def done():
    """
        Called once on script shutdown, after any other events.
    """
    global TEST_DETAILS
    start_bound, end_bound = dump_test_har()
    update_test_details(start_bound, end_bound)
    handle_dangling_tests()

    if ctx.options.customHardump:
        if ctx.options.customHardump == '-':
            mitmproxy.ctx.log(HAR)
        else:
            raw: bytes = HAR
            if ctx.options.customHardump.endswith('.zhar'):
                raw = zlib.compress(HAR, 9)

            with open(os.path.expanduser(ctx.options.customHardump), "wb") as f:
                f.write(raw)

    test_details_dump: str = json.dumps(TEST_DETAILS, separators=(',', ':'))
    if ctx.options.testDetails:
        with open(ctx.options.testDetails, "wb") as f:
            f.write(test_details_dump.encode())
        mitmproxy.ctx.log("Dumped test details to %s" % ctx.options.testDetails)


def format_cookies(cookie_list):
    rv = []

    for name, value, attrs in cookie_list:
        cookie_har = {
            "name": name,
            "value": value,
        }

        # HAR only needs some attributes
        for key in ["path", "domain", "comment"]:
            if key in attrs:
                cookie_har[key] = attrs[key]

        # These keys need to be boolean!
        for key in ["httpOnly", "secure"]:
            cookie_har[key] = bool(key in attrs)

        # Expiration time needs to be formatted
        expire_ts = cookies.get_expiration_ts(attrs)
        if expire_ts is not None:
            cookie_har["expires"] = datetime.fromtimestamp(expire_ts, timezone.utc).isoformat()

        rv.append(cookie_har)

    return rv


def format_request_cookies(fields):
    return format_cookies(cookies.group_cookies(fields))


def format_response_cookies(fields):
    return format_cookies((c[0], c[1][0], c[1][1]) for c in fields)


def name_value(obj):
    """
        Convert (key, value) pairs to HAR format.
    """
    return [{"name": k, "value": v} for k, v in obj.items()]


def map_request_to_test(request_timestamp, entry):
    global TEST_HAR, NEXT_TEST

    if not NEXT_TEST:
        # If Next Test isn't defined, its the last valid test of the sequence. So, place this request in CURR_TEST.
        TEST_HAR["log"]["entries"].append(entry)
    elif request_timestamp >= epoch(NEXT_TEST['start_time']):
        # If request lies beyond the next test. So, dump and reset TEST_HAR.
        start_bound, end_bound = dump_test_har()
        update_test_details(start_bound, end_bound)
        reset_test_har()
        update_curr_and_next_test_objects()
        map_request_to_test(request_timestamp, entry)
    else:
        # If request lies between CURR_TEST and NEXT_TEST
        TEST_HAR["log"]["entries"].append(entry)


def dump_test_har():
    """
        Dump Contents of TEST_HAR(json) to HAR(bytes).
        If a test does not contain any nw Call, still dump the contents of base_har to HAR.
    """
    global TEST_HAR, HAR
    
    old_har_size = len(HAR)
    test_har_string: str = json.dumps(TEST_HAR, separators=(',', ':'))
    HAR += test_har_string.encode() # Default UTF-8 encoding
    new_har_size = len(HAR)

    return [old_har_size, new_har_size]


def update_test_details(start_bound, end_bound):
    """
        Update nwLogs boundaries at test_details object
    """
    global CURR_TEST, TEST_DETAILS

    if start_bound is None or end_bound is None:
        return None

    test_name = CURR_TEST["name"]

    if not (test_name and TEST_DETAILS[test_name]):
        return None

    TEST_DETAILS[test_name]["network_log_boundary"]["start"] = start_bound
    TEST_DETAILS[test_name]["network_log_boundary"]["end"] = end_bound


def handle_dangling_tests():
    """
        When all requests are exhausted but not all tests were processed.
        Map all these dangling tests to an empty nwCall har
    """
    global TEST_DETAILS_LIST, CURR_TEST_INDEX, CURR_TEST

    # If All Tests are processed.
    tests_count = len(TEST_DETAILS_LIST)
    if CURR_TEST_INDEX >= tests_count - 1:
        return None

    # Process from the next test as CURR_TEST has been processed in done() hook.
    CURR_TEST_INDEX += 1
    CURR_TEST, CURR_TEST_INDEX = fetch_next_valid_test(CURR_TEST_INDEX)

    # Store the base har body only once and use it for subsequent tests.
    reset_test_har()
    start_bound, end_bound = dump_test_har()

    while CURR_TEST_INDEX < tests_count:
        update_test_details(start_bound, end_bound)
        CURR_TEST_INDEX += 1
        CURR_TEST, CURR_TEST_INDEX = fetch_next_valid_test(CURR_TEST_INDEX)


def reset_test_har():
    """
        Base JSON har which needs to created for each test.
    """
    global TEST_HAR
    TEST_HAR.update({
        "log": {
            "version": "1.2",
            "creator": {
                "name": "mitmproxy har_dump",
                "version": "0.1",
                "comment": "mitmproxy version %s" % version.MITMPROXY
            },
            "entries": []
        }
    })


def update_curr_and_next_test_objects():
    """
        Fetches the next set of CURR_TEST and NEXT_TEST.
    """
    global CURR_TEST_INDEX, CURR_TEST, NEXT_TEST_INDEX, NEXT_TEST

    CURR_TEST_INDEX += 1
    CURR_TEST, CURR_TEST_INDEX = fetch_next_valid_test(CURR_TEST_INDEX)

    NEXT_TEST_INDEX = CURR_TEST_INDEX + 1
    NEXT_TEST, NEXT_TEST_INDEX = fetch_next_valid_test(NEXT_TEST_INDEX)


def fetch_next_valid_test(index):
    """
        Fetches the next test in the list with a valid start_time.
        test_obj is valid only if start_time key is present.
    """
    test_obj = fetch_test(index)
    while (test_obj is not None and test_obj['start_time'] is None):
        index += 1
        test_obj = fetch_test(index)

    return [test_obj, index]


def fetch_test(index):
    """
        Fetches curr_test and next_test
    """
    global TEST_DETAILS_LIST
    if index <= len(TEST_DETAILS_LIST) - 1:
        return TEST_DETAILS_LIST[index]
    else:
        return None


def epoch(test_time):
    return datetime.strptime(test_time, "%Y-%m-%d %H:%M:%S %z").timestamp()
