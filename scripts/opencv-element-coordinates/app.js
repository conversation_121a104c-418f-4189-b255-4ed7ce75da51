try {
    const cv2 = require('opencv4nodejs');
    const fs = require("fs");

    element = process.argv[2]
    image = process.argv[3]
    template = process.argv[4]
    dp_density = process.argv[5]

    if (!dp_density) dp_density = 3

    if (fs.existsSync(image)) {
        img = cv2.imread(image)
    } else {
        throw new Error('screenshot image file does not exist');
    }

    const returnMatchedCoordinates = (templatePath, img, dp_density) => {
        img = img.cvtColor(cv2.COLOR_BGR2GRAY);
        const template = cv2.imread(templatePath, cv2.IMREAD_GRAYSCALE);
        const h = template.rows;
        const w = template.cols;
      
        let bestMatch = null;
        let bestVal = -1;
      
        // Loop through multiple scales
        for (let scale = 0.5; scale <= 1.5; scale += 0.1) {
          const scaledTemplate = template.resize(Math.round(h * scale), Math.round(w * scale));
          const scaledH = scaledTemplate.rows;
          const scaledW = scaledTemplate.cols;
      
          // Skip scales where template is larger than the image
          if (scaledH > img.rows || scaledW > img.cols) {
            continue;
          }
      
          // Perform template matching
          const matched = img.matchTemplate(scaledTemplate, cv2.TM_CCOEFF_NORMED);
          const minMax = matched.minMaxLoc();
      
          if (minMax.maxVal > bestVal) {
            bestVal = minMax.maxVal;
            bestMatch = {
              maxLoc: minMax.maxLoc,
              scale,
              width: scaledW,
              height: scaledH
            };
          }
        }
        // Calculate coordinates for the best match
        const { maxLoc, width, height } = bestMatch;
        const xCoordinate = Math.floor((maxLoc.x + Math.floor(width / 2)) / dp_density);
        const yCoordinate = Math.floor((maxLoc.y + Math.floor(height / 2)) / dp_density);

        return [xCoordinate, yCoordinate];
    }

    switch(element) {
        case "assistive_touch_bubble":
            gray = img.cvtColor(cv2.COLOR_BGR2GRAY)
            // Apply Gaussian Blur
            ksize = new cv2.Size(15, 15); // Kernel size (15x15)
            sigmaX = 0; // Standard deviation in X direction
            blurred = gray.gaussianBlur(ksize, sigmaX)
            minDist = 50
            param1 = 50
            param2 = 30
            minRadius = 30
            maxRadius = 60
            circles = blurred.houghCircles(cv2.HOUGH_GRADIENT, 1, minDist, param1, param2, minRadius, maxRadius)
            console.log(Math.floor(circles[0].x / dp_density), Math.floor(circles[0].y / dp_density))

            break;

        case "shake_button":
            if (fs.existsSync(template)) {
                template = cv2.imread(template)
                const h = template.cols
                const w = template.rows
                const matched = img.matchTemplate(template, cv2.TM_CCORR_NORMED);
                const minMax = matched.minMaxLoc()
                max_loc = minMax.maxLoc
                console.log(Math.floor((max_loc.x + Math.floor(w/2))/dp_density), Math.floor((max_loc.y + Math.floor(h/2))/dp_density))
            } else {
                throw new Error('template image file does not exist');
            }
            break;
        
        case "apple_pay_button":
            if (fs.existsSync(template)) {
                [x_coordinate, y_coordinate] = returnMatchedCoordinates(template, img, dp_density)
                console.log(x_coordinate, y_coordinate)
            } else {
                throw new Error('template image file does not exist');
            }
            break;

        case "confirm_apple_pay_button":
            if (fs.existsSync(template)) {
                [x_coordinate, y_coordinate] = returnMatchedCoordinates(template, img, dp_density)
                console.log(x_coordinate, y_coordinate)
            } else {
                throw new Error('template image file does not exist');
            }
            break;

        default: console.log("unsupported", "element")

    }
} catch(e) {
    console.log(e.stack || e)
}
