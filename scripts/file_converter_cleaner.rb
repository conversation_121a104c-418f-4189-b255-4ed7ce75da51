require 'fileutils'
require_relative '../lib/configuration'
require_relative '../config/constants'
require_relative '../lib/utils/utils'
require_relative '../lib/stale_file_cleaner'

require 'browserstack_logger'

module BrowserStack
  class FileConverterCleaner
    def handle_timedout_request(request_file)
      time_in_queue = begin
        (Time.now - File.mtime(request_file)).round(2)
      rescue
        -1
      end
      data = JSON.parse(File.read(request_file))
      session_id = data['session_id']
      instrumentation_file = Utils.get_screenshot_instrumentation_file(@conf, session_id)
      Utils.update_screenshot_instrumentation_with_lock(instrumentation_file, "convert", "failed", { queue_time: time_in_queue }) if File.exist? instrumentation_file
    end

    def initialize
      @conf = BrowserStack::Configuration.new.all
      file_cleaner = BrowserStack::StaleFileCleaner.new(FILE_CONVERTER, @conf["files_to_convert_dir"], "*.json", FILE_CONVERTER_STALE_FILE_DELETE_THRESHOLD)
      file_cleaner.process_files do |request_file|
        handle_timedout_request(request_file)
      end
    end
  end
end

BrowserStack::FileConverterCleaner.new if __FILE__ == $PROGRAM_NAME
