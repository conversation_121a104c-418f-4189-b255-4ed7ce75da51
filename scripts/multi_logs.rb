require_relative '../lib/utils/osutils'
require_relative '../lib/configuration'

if ARGV[0] == '--help'
  s = "
Usage: multilogs [-I] [device_id] [space separated logfiles]
      Options:
        --help      display this help and exit
      Commands:
        -I          tail all logs in a single window
        -d          pass device id to get device specific logs
Sample:
      multilogs -d <udid> cleanup dc
      multilogs -I -d <udid> cleanup dc install_phase test_delete_downloads
  "
  puts s
  exit
end

args = ARGV
BASE_LOGS_DIR = "/var/log/browserstack/".freeze
MULTITAIL_BIN_PATH = "/usr/local/.browserstack/realmobile/deps/bin/multitail".freeze

display_type = "windowed"
if args[0] == "-I"
  display_type = "combined"
  args.shift
end

device_id = nil
if args[0] == '-d'
  args.shift
  device_id = args[0]
  args.shift
end

files = []

args.each do |arg|
  case arg
  when "cleanup"
    files << File.join(BASE_LOGS_DIR, "cleanup_#{device_id}.log")
  when "dc"
    files << File.join(BASE_LOGS_DIR, "device_poll.log")
  when "mc"
    files << File.join(BASE_LOGS_DIR, "mobile_poll.log")
  when "iphase"
    files << File.join(BASE_LOGS_DIR, "install_phase_#{device_id}.log")
  when "appium"
    appium_port = JSON.parse(File.read(BrowserStack::Configuration['config_json_file']))["devices"][device_id]['selenium_port']
    file = BrowserStack::OSUtils.execute("ls #{BASE_LOGS_DIR} | grep appium_#{appium_port}.log").split('\n')[0]
    files << File.join(BASE_LOGS_DIR, file)
  else
    if device_id.nil?
      files << File.join(BASE_LOGS_DIR, "#{arg}.log")
    else
      file = BrowserStack::OSUtils.execute("ls #{BASE_LOGS_DIR} | grep #{arg}_#{device_id}.log").split('\n')[0]
      files << File.join(BASE_LOGS_DIR, file)
    end
  end
end

colours = ['cyan', 'yellow', 'magenta', 'red', 'blue', 'green']
colours = colours.shuffle

cmd = []
cmd << "-s 2" if files.length > 3
files.compact.each_with_index do |item, index|
  cmd += ["-ci #{colours[index % 6]}"]
  cmd += ["-I"] if index != 0 && display_type == 'combined'
  cmd += [item]
end

system("#{MULTITAIL_BIN_PATH} #{cmd.join(' ')}", out: $stdout, exception: true)

