#!ruby
#
require 'json'
require 'dotenv/load'
require 'logger'
require 'socket'
require 'timeout'
require 'net/http'
require 'static_conf'

require_relative 'device_thread'
require_relative 'dedicated_device_thread'
require_relative 'apple_tv_device_thread'
require 'browserstack_logger'
require_relative '../lib/utils/idevice_utils'
require_relative '../lib/utils/osutils'
require_relative '../lib/check_runner/device_check_runner'
require_relative '../lib/utils/alerter'
require_relative '../lib/ios_influxdb_client'
require_relative '../lib/configuration'
require_relative '../config/constants'
require_relative '../lib/models/device_state'
require_relative '../lib/helpers/first_device_check_helper'

module BrowserStack # rubocop:todo Metrics/ModuleLength
  class << self
    def post_data(url, data, retry_post=true, post_timeout=60, auth=nil) # rubocop:todo Metrics/AbcSize
      if File.exist?("/tmp/skip_add_mobile")
        BrowserStack.logger.info("Skipping add_mobile request. File /tmp/skip_add_mobile found")
        return
      end
      uri = URI.parse(url)
      begin
        Timeout.timeout(post_timeout) do
          BrowserStack.logger.info("Post to #{url}")
          request = Net::HTTP::Post.new(url)
          request['Content-Type'] = 'application/json'
          request.body = data
          request.basic_auth auth['user'], auth['pass'] unless auth.nil?
          http = Net::HTTP.new(uri.host, uri.port)
          http.read_timeout = 30
          response = http.request(request)
          BrowserStack.logger.info("Response code: #{response.code}, Body: #{response.body}")
          if response.code.to_i != 200
            if retry_post
              OSUtils.flush_dns
              BrowserStack.logger.warn("post data to #{url} rejected with response #{response.code}, retrying")
              post_data(url, data, false)
            else
              BrowserStack.logger.info("post data to #{url} rejected with response #{response.code}")
            end
          end
        end
      rescue => e
        if retry_post
          OSUtils.flush_dns
          BrowserStack.logger.info("post data to #{url} failed #{e.message}, retrying")
          post_data(url, data, false)
        else
          BrowserStack.logger.info("post data to #{url} failed #{e.message}")
        end
      end
    end

    def update_config(config, env, machine_ip) # rubocop:todo Metrics/AbcSize
      BrowserStack.logger.info("Updating the config")
      Utils.write_config_with_lock(@config_json_file, config.to_json)
      begin
        devices_conf = config['devices']
        env_devices_conf = {}

        devices_conf.each do |device_id, device_config|
          custom_rails_endpoint = @static_conf.device_rails_endpoint(device_id)
          # temp change to handle the transition from www.browserstack.com to terminals.browserstack.com
          # TODO: remove this change
          next if custom_rails_endpoint&.include?(PRODUCTION_SITE) && @static_conf["rails_endpoint"]&.include?(PRODUCTION_SITE)
          # Device can have it's own rails_endpoint derived from exception part of the config
          next if custom_rails_endpoint == @static_conf["rails_endpoint"]

          env_devices_conf[custom_rails_endpoint] ||= {}
          env_devices_conf[custom_rails_endpoint][device_id] = device_config.dup
          devices_conf[device_id]['online'] = false
          devices_conf[device_id]['offline_reason'] = "Moved to #{@static_conf.device_rails_endpoint(device_id)}"
        end

        unless env_devices_conf.empty?
          env_devices_conf.each do |environment, devices_config|
            environment = "#{environment}.bsstag.com" unless environment =~ /^localhost(:[0-9]+)?$/ || environment.include?(".")
            environment = environment.sub(%r{^http:[/\\]+}, '') # Eliminate http://, http:\\ and similar from the beginning.
            rails_endpoint = "http://#{environment}/admin/add_mobile_v2?auth=selenium"
            t0 = Time.now
            post_data(rails_endpoint, devices_config.to_json)
          end
        end

        # Finally post residual hash to rails_endpoint mentioned in static_conf
        conf_endpoint = @static_conf['rails_endpoint'] || @server_config['custom_rails_endpoint']
        rails_endpoint = "#{conf_endpoint}/admin/add_mobile_v2?auth=selenium"
        t0 = Time.now
        post_data(rails_endpoint, devices_conf.to_json)

        # This is to track the time devices takes to come online after cleanup.
        intermediate_offline_time(devices_conf)
      rescue => e
        BrowserStack.logger.error("Error posting to browserstack: #{e}")
      end
    end

    def restart_servers
      BrowserStack.logger.info("New devices detected, restarting puma server")
      BrowserStack::Zombie.push_logs("puma_restarted_from_device_check", "")
      OSUtils.execute("bash #{BS_HELPER_SCRIPT} restart_servers") # Restart servers to reload device_conf variable in server.rb - MOBPE-1212
    end

    def intermediate_offline_time(devices_conf)
      devices_conf.each do |device_id, _device_config|
        last_cleanup_file = "#{STATE_FILES_DIR}/last_cleanup_#{device_id}"
        next unless File.exists?(last_cleanup_file)

        offline_time = (Time.now - File.mtime(last_cleanup_file)).round
        BrowserStack.logger.info("#{device_id} - Device remained offline for: #{offline_time} seconds")

        current_device_state = if devices_conf[device_id]['online']
                                 'online'
                               else
                                 devices_conf[device_id]['offline_reason']
                               end

        BrowserStack.logger.info("#{device_id}  #{current_device_state}- state: #{devices_conf[device_id]['online']} - reason: #{devices_conf[device_id]['offline_reason']}")

        BrowserStack::Zombie.push_logs("intermediate-offline-time", "", { "device" => device_id, "data" => { "offline_time" => offline_time, "device_state" => current_device_state } })
        File.delete(last_cleanup_file)
      end
    end

    # ------------------------------------------------- #
    # This is the main entry point of this code...      #
    # ------------------------------------------------- #
    def device_check # rubocop:todo Metrics/AbcSize
      start_time = Time.now
      conf = Configuration.new
      @server_config = conf.all

      @environment = @server_config['environment']
      @static_conf_file = @server_config['static_conf_file']
      @config_json_file = @server_config['config_json_file']
      @static_conf = @server_config['static_conf']
      machine_ip = begin
        File.read(@server_config['ip_file'])
      rescue
        ""
      end

      check = BrowserStack::DeviceCheckRunner.new(@server_config)
      if check.running? # this also checks for stuck device_checks as well and tries to kill the old instance
        BrowserStack.logger.info("Another instance of device_check is already running")
        `bundle exec /usr/local/.browserstack/realmobile/lib/utils/push_to_zombie.rb "ios_njb" "multiple device_check stuck" "#{machine_ip}"` if OSUtils.execute("ps aux | grep [d]evice_check | wc -l").to_i > 2
        exit 101
      end

      # Mac os likes to randomly disable firewalls, which causes device check exception alerts, so we ensure its up here
      BrowserStack::OSUtils.start_firewall

      new_devices, devices_data = check.perform

      configuration_json = {}
      configuration_json['selenium_port'] = check.selenium_port
      configuration_json['webkit_proxy_port'] = check.webkit_proxy_port
      configuration_json['webdriver_agent_port'] = check.webdriver_agent_port

      config = {}
      config['devices'] = devices_data
      config['configuration'] = configuration_json

      update_config(config, @environment, machine_ip)
      restart_servers unless new_devices.empty?
      execution_time = (Time.now - start_time).round
      BrowserStack.logger.info("Device check execution time: #{execution_time} seconds")
      ip = begin
        File.read(@server_config['ip_file'])
      rescue
        ""
      end
      BrowserStack::Zombie.push_logs("device_check_timer", execution_time, { "os" => "ios_njb", "device" => "", "browser" => "", "terminal_type" => "realMobile", "data" => ip }) unless ip.empty?
      BrowserStack.logger.info("Final config: #{config}")
      BrowserStack.logger.close
    end
  end
end

BrowserStack::IosInfluxdbClient.component = 'device-check'
BrowserStack.device_check
