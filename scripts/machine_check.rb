#!ruby
#
require 'json'
require 'socket'
require 'timeout'
require 'net/http'

require_relative 'device_thread'
require_relative 'dedicated_device_thread'
require 'browserstack_logger'
require_relative '../lib/utils/idevice_utils'
require_relative '../lib/utils/osutils'
require_relative '../lib/check_runner/machine_check_runner'
require_relative '../lib/ios_influxdb_client'

require_relative '../lib/configuration'

module BrowserStack
  class << self
    def machine_check
      conf = Configuration.new
      @server_config = conf.all

      @environment = @server_config['environment']
      @static_conf_file = @server_config['static_conf_file']
      @config_json_file = @server_config['config_json_file']
      @static_conf = @server_config['static_conf']

      mcr = MachineCheckRunner.new(@server_config)
      mcr.perform
    end
  end
end

BrowserStack::IosInfluxdbClient.component = 'machine-check'
BrowserStack.machine_check
