fpath+=(/usr/local/.browserstack/realmobile/scripts/auto_completion)
autoload -U compinit
compinit
setopt completealiases
compdef _gnu_generic ios-deploy
compdef _gnu_generic ifuse
compdef _gnu_generic idevice_id
compdef _gnu_generic idevicedebug
compdef _gnu_generic idevicepair
compdef _gnu_generic ideviceimagemounter
compdef _gnu_generic idevicecrashreport
compdef _gnu_generic ideviceenterrecovery
compdef _gnu_generic idevicename
compdef _gnu_generic ideviceprovision
compdef _gnu_generic idevicedebugserverproxy
compdef _gnu_generic idevicesyslog
compdef _gnu_generic ideviceinfo
compdef _gnu_generic idevicenotificationproxy
compdef _gnu_generic idevicesetlocation
compdef _gnu_generic ideviceinstaller
compdef _gnu_generic idevicebackup
compdef _gnu_generic idevicelocation
compdef _gnu_generic idevicescreenshot
compdef _gnu_generic idevicedate
compdef _gnu_generic idevicecontacts
compdef _gnu_generic idevicediagnostics
compdef _gnu_generic idevicevideoproxy
compdef _gnu_generic idevicebookmark
compdef _gnu_generic idevicebackup2
