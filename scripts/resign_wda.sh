# !/bin/bash

# Script to re-sign pre-built WDA for one device after download on host
# Usage
# ./resign_wda.sh source identity profile entitlements keychain_pwd keychain_db
#
#   source         .app folder location
#   identity       signing certificate (40 chars)
#   profile        path to the provision profile file
#   entitlements   path to entitlements plist
#   keychain_pwd   password for keychain_db
#   keychain_db    path to keychain
#
# Derived from: https://github.com/browserstack/codesigner/blob/master/worker/codesigner.sh

set -x
set -euo pipefail

log() {
  date -u "+%F %T UTC: $*"
}

log "Resigning WDA"

if [ $# -lt 6 ]; then
    echo "usage: $0 source identity profile entitlements keychain_pwd keychain_db" >&2
    exit 1
fi

APP_FOLDER="$1"
CERTIFICATE="$2"
NEW_PROVISION="$3"
ENTITLEMENTS="$4"
KEYCHAIN_PASSWD="$5"
KEYCHAIN_DB="$6"


# Replace the embedded mobile provisioning profile
log "Adding the new provision: $NEW_PROVISION"
cp "$NEW_PROVISION" "$APP_FOLDER/embedded.mobileprovision"

# Resign the application
log "Resigning application using certificate: $CERTIFICATE"

# Always unlock login keychain before running codesign
# This is also added to bashrc for login shells
security unlock-keychain -p $KEYCHAIN_PASSWD $KEYCHAIN_DB
log "Keychain unlocked"


log "Listing valid codesigning identities"
security find-identity -v -p codesigning ~/Library/Keychains/Browserstack.keychain-db

[ -e "$APP_FOLDER"/_CodeSignature ] && rm -rf "$APP_FOLDER"/_CodeSignature
log "_CodeSignature folder removed"
FRAMEWORKS_DIRECTORY="$APP_FOLDER"/Frameworks
PLUGINS_DIRECTORY="$APP_FOLDER"/PlugIns

# Resign all .framework and .dylib files at once
find "$APP_FOLDER" \( -name "*.framework" -o -name "*.dylib" \) -print0 | xargs -0 -I{} | sed 's/^/"/g' | sed 's/$/"/g' | tr '\n' ' '| xargs /usr/bin/codesign -f -s "$CERTIFICATE" --entitlements "$ENTITLEMENTS"

# Resign everything in Plugins and Frameworks directory except frameworks and dylibs
ls -d "$FRAMEWORKS_DIRECTORY"/* "$PLUGINS_DIRECTORY"/* | grep -vE "\.(framework|dylib)$" | xargs -I{} | sed 's/^/"/g' | sed 's/$/"/g' | tr '\n' ' ' | xargs /usr/bin/codesign -f -s "$CERTIFICATE" --entitlements "$ENTITLEMENTS"

# Resign the .app folder with the provided certificate
/usr/bin/codesign -f -s "$CERTIFICATE" "$APP_FOLDER" --entitlements $ENTITLEMENTS

log "Resigning Finished"
