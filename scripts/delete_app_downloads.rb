require 'fileutils'
require_relative '../lib/utils/osutils'
require_relative '../lib/configuration'
require_relative '../config/constants'

require 'browserstack_logger'

module BrowserStack
  HOUR = 60 * 60
  class DeleteAppDownloads # rubocop:todo Metrics/ClassLength
    def size_above_threshold?
      size_details = OSUtils.execute("du -sh #{APPS_DOWNLOAD_FOLDER}")

      # FIXME: constants should not be mutable
      size = begin
        size_details.split(APPS_DOWNLOAD_FOLDER).first.gsub!(/\s+/, "").to_s
      rescue
        nil
      end

      return true if size.nil? || size.empty?
      return (!['B', 'K', 'M'].include?(size[-1]) || (size[-1] == 'G' && size[0...-1].to_f > 6.0) ) if ['B', 'K', 'M', 'G'].include?(size[-1])

      true
    end

    def delete_stale_logs_stability_files
      # Delete logs stability metrics file.
      logs_stability_files = Dir.glob("/tmp/*_xctest_logs_stability")

      BrowserStack.logger.info("Found the following stale logs_stability_files : #{logs_stability_files}")

      logs_stability_files.each do |file_name|
        created_at_logs_stability_file = Time.now.to_i - File.mtime(file_name).to_i
        if created_at_logs_stability_file > (12 * HOUR)
          BrowserStack.logger.info("Deleting stale file: #{file_name}.")
          FileUtils.rm_f(file_name)
        end
      end
    end

    def delete_files(uuid, common_app_folder, session_files)
      FileUtils.rm_f("#{common_app_folder}/#{uuid}.complete")
      FileUtils.rm_f("#{common_app_folder}/#{uuid}.starting")
      session_files.each { |session_file| FileUtils.rm_f(session_file) }
      FileUtils.rm_rf("#{common_app_folder}/#{uuid}")
    end

    def handle_empty_common_app_folder(common_app_folder)
      starting_files = Dir.glob("#{common_app_folder}/*.starting")
      any_download_pending = false
      starting_files.each do |starting_file|
        if (Time.now.to_i - File.mtime(starting_file).to_i) < (HOUR / 2)
          any_download_pending = true
          break
        end
      end
      unless any_download_pending
        FileUtils.rm_rf(common_app_folder)
        BrowserStack.logger.info("No downloads found. Deleted folder: #{common_app_folder}")
      end
    end

    def handle_uuid_dir(common_app_folder, uuid, uuid_folder, machine_ip) # rubocop:todo Metrics/AbcSize
      if File.exist?("#{common_app_folder}/#{uuid}.complete")
        session_files = Dir.glob("#{common_app_folder}/*#{uuid}.session")
        last_updated_complete_file = Time.now.to_i - File.mtime("#{common_app_folder}/#{uuid}.complete").to_i

        if session_files.empty? && (last_updated_complete_file > HOUR || size_above_threshold?)
          BrowserStack.logger.info("No Session files found. Found last_updated_complete_file: #{last_updated_complete_file}")
          delete_files(uuid, common_app_folder, session_files)
        elsif !session_files.empty? && last_updated_complete_file > (24 * HOUR)
          `/usr/local/.browserstack/realmobile/lib/utils/push_to_zombie.rb "ios_njb" "common-app-stale-delete" "#{machine_ip}"`
          BrowserStack.logger.info("Deleting App Download files. Found last_updated_complete_file: #{last_updated_complete_file}")
          delete_files(uuid, common_app_folder, session_files)
        end
      elsif File.exist?("#{common_app_folder}/#{uuid}.starting")
        starting_file_timestamp = File.mtime("#{common_app_folder}/#{uuid}.starting").to_i
        if (Time.now.to_i - starting_file_timestamp) > (HOUR / 2)
          BrowserStack.logger.info("Download Pending for a long time, last updated .starting file: #{starting_file_timestamp}")
          FileUtils.rm_f("#{common_app_folder}/#{uuid}.starting")
          FileUtils.rm_rf(uuid_folder)
        end
      else
        session_files = Dir.glob("#{common_app_folder}/*#{uuid}.session")
        last_updated_app_dir = Time.now.to_i - File.mtime(common_app_folder).to_i

        if session_files.empty?
          BrowserStack.logger.info("No .session, .complete, .starting files found. Deleting App with uuid: #{uuid}")
          delete_files(uuid, common_app_folder, session_files)
        elsif !session_files.empty? && last_updated_app_dir > (24 * HOUR)
          BrowserStack.logger.info("Deleting App Download files. Found last_updated_app_dir: #{last_updated_app_dir}")
          delete_files(uuid, common_app_folder, session_files)
          `/usr/local/.browserstack/realmobile/lib/utils/push_to_zombie.rb "ios_njb" "common-app-no-state-delete" "#{machine_ip}"`
        end
      end
    end

    # FIXME: initialize should not be the main method...
    def initialize
      conf = BrowserStack::Configuration.new
      @server_config = conf.all
      log_file = "#{@server_config['logging_root']}/app_cleanup.log"
      machine_ip = begin
        File.read(@server_config['ip_file'])
      rescue
        ""
      end
      BrowserStack.init_logger(log_file)

      folders_list = Dir.glob("#{APPS_DOWNLOAD_FOLDER}/*/")

      delete_stale_logs_stability_files

      folders_list.each do |common_app_folder|
        # Removing last forward slash / from each of the output of Dir.glob("base_path/*/")
        # ensures we do not do full_folder_path/uuid//uuid.starting
        common_app_folder.gsub!(%r{/$}, "")

        uuid_folder_list = Dir.glob("#{common_app_folder}/*/")
        uuid_folder_list.each { |uuid_folder_path| uuid_folder_path.gsub!(%r{/$}, "") }

        BrowserStack.logger.info("Folders found inside #{common_app_folder} : #{uuid_folder_list.join(', ')}")

        begin
          if uuid_folder_list.empty?
            handle_empty_common_app_folder(common_app_folder)
          else
            uuid_folder_list.each do |uuid_folder|
              uuid = begin
                uuid_folder.split('/').last
              rescue
                nil
              end
              next if uuid.nil? || uuid.empty?

              handle_uuid_dir(common_app_folder, uuid, uuid_folder, machine_ip)
            end
          end
        rescue => e
          BrowserStack.logger.info "Exception occurred while handling app: #{common_app_folder}. #{e.message}"
          `/usr/local/.browserstack/realmobile/lib/utils/push_to_zombie.rb "ios_njb" "common-app-delete-exception" "#{machine_ip}"`
        end
      end
    end
  end
end

BrowserStack::DeleteAppDownloads.new if __FILE__ == $PROGRAM_NAME

