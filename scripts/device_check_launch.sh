#!/bin/bash --login
ROOT_DIR="/usr/local/.browserstack/realmobile"
PUSH_TO_ZOMBIE="$ROOT_DIR/lib/utils/push_to_zombie.rb"
PUSH_TO_INFLUXDB_SCRIPT="$ROOT_DIR/lib/ios_influxdb_client.rb"

BUNDLE="$(which bundle)"

echo "================ Device check launcher called ($(date)) ==========="

# Key for the github certificates repo
eval $(ssh-agent -s)
ssh-add /Users/<USER>/.ssh/id_rsa_mobilegithub

rvm use 2.7.2;

# <PERSON>lane needs the keychain
security unlock-keychain -p "gushed7hiring" /Users/<USER>/Library/Keychains/Browserstack.keychain-db;

echo '------------ Running actual device_check.rb'
cd $ROOT_DIR
gtimeout -s KILL 3600 bundle exec ruby $ROOT_DIR/scripts/device_check.rb;
exit_status=$?
if [[ $exit_status -eq 124 ]]; then
  $BUNDLE exec $PUSH_TO_ZOMBIE "ios_njb" "device-check" "timeout";
  $BUNDLE exec ruby $PUSH_TO_INFLUXDB_SCRIPT "device-check-timeout" "device-check" "device-check-launch" "NA" "true"
elif [[ $exit_status -eq 101 ]]; then
  echo ". . . . Previous device check still running . . . .";
elif [[ $exit_status -ne 0 ]]; then
  $BUNDLE exec $PUSH_TO_ZOMBIE "ios_njb" "device-check" "error";
  $BUNDLE exec ruby $PUSH_TO_INFLUXDB_SCRIPT "device-check-error" "device-check" "device-check-launch" "NA" "true"
fi
echo '------------ done running device_check.rb'

# Kill the ssh-agent, not needed anymore
ssh-agent -k

echo "================ Device check launcher finished ($(date)) ==========="
