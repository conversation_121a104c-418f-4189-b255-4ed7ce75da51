require 'English'
require 'time'
require 'fileutils'
require 'net/http'
require 'timeout'
require 'cleanup_status_db'
require 'device_fork_executor'

require_relative '../config/constants'
require_relative '../lib/appium_server'
require_relative '../lib/apps/chrome'
require_relative '../lib/apps/launcher'
require_relative '../lib/apps/redirect'
require_relative '../lib/checks/check_device'
require_relative '../lib/checks/machine'
require_relative '../lib/configuration'
require_relative '../lib/custom_exceptions'
require_relative '../lib/device_setup/install_check'
require_relative '../lib/device_setup/installer'
require_relative '../lib/helpers/testflight'
require_relative '../lib/helpers/apple_pay'
require_relative '../lib/helpers/voiceover_helper'
require_relative '../lib/ios_influxdb_client'
require_relative '../lib/models/device_state'
require_relative '../lib/provisioning/provisioning_manager'
require_relative '../lib/recover_device'
require_relative '../lib/upgraded_device_check_state'
require_relative '../lib/utils/hooter'
require_relative '../lib/utils/idevice_utils'
require_relative '../lib/utils/ios_mdm_service_client'
require_relative '../lib/utils/telephony'
require_relative '../lib/utils/utils'
require_relative '../lib/utils/wda_version'
require_relative '../lib/utils/xcode_utils'
require_relative '../server/device_manager'
require_relative '../server/iphone'
require_relative '../lib/device_setup/backup_manager/backup_manager_factory'
require_relative '../lib/helpers/browserstack_app_helper'

module BrowserStack
  OFFLINE_REBOOT_TIME = 1 * 60 * 60 # 1 hour
  DEDICATED_OFFLINE_REBOOT_TIME = 3 * 60 * 60 # 3 hours for dedicated devices
  OFFLINE_REBOOT_MESSAGE = 'Device offline for over the specified limit - rebooting.'

  class DeviceThread # rubocop:todo Metrics/ClassLength
    attr_reader :redis_client, :dc, :device_state

    # Constant for all devices...
    class << self
      #FIXME: ... this is bullshit
      def configure(user,
                    ip,
                    static_conf,
                    config_root,
                    logging_root,
                    default_appium_version,
                    appium_root,
                    appium_keychain,
                    appium_keychain_password,
                    devtools_port,
                    hostname,
                    region,
                    sub_region,
                    checks_timeout,
                    infra_api,
                    developer_disk_images_path,
                    developer_symbols_path,
                    web_driver_agents,
                    profile_check_limit,
                    internet_sharing_enabled,
                    strict_app_check,
                    mdm_profiles_required)
        @@user = user
        @@ip = ip
        @@static_conf = static_conf
        @@devtools_port = devtools_port
        @@hostname = hostname
        @@region = region
        @@sub_region = sub_region
        @@timeout = checks_timeout
        @@config_root = config_root
        @@logging_root = logging_root
        @@default_appium_version = default_appium_version
        @@appium_root = appium_root
        @@infra_api = infra_api
        @@appium_keychain = appium_keychain
        @@appium_keychain_password = appium_keychain_password
        @@developer_disk_images_path = developer_disk_images_path
        @@developer_symbols_path = developer_symbols_path
        @@profile_check_limit = profile_check_limit
        @@web_driver_agents = web_driver_agents
        @@internet_sharing_enabled = internet_sharing_enabled
        @@strict_app_check = strict_app_check
        @@mdm_profiles_required = mdm_profiles_required
      end
    end

    # Device specific init.
    def initialize(config, device, device_check_lock, port, port_webkit, port_webdriver, redis_client)
      @config = config
      @device_check_lock = device_check_lock
      @device = device
      @port = port
      @port_webkit = port_webkit
      @port_webdriver = port_webdriver
      @redis_client = redis_client
      conf = Configuration.new
      @server_config = conf.all
      @idevice = BrowserStack::IPhone.new(@config, @device)
      if @config&.key?("online")
        @ex_online_state = @config["online"]
        @ex_offline_reason = @config["offline_reason"]
      else
        @ex_online_state = false # New device's ex_online_state is false
        @ex_offline_reason = "new device"
      end
      @influxdb_client = BrowserStack::IosInfluxdbClient.new(BrowserStack.logger)
      @device_state = DeviceState.new(@device)
      @dc = CheckDevice.new(@device, @redis_client)
    end

    def get_device_info
      deviceinfo = IdeviceUtils.ideviceinfo(@device)
      device_version = device_build_version = device_serial = mac_address = imsi = iccid = chipid = ''
      deviceinfo.each do |info|
        next if info.split(':').length < 2

        key = info.split(':', 2)[0]
        val = info.split(':', 2)[1].strip
        @device_name = val if key == 'ProductType'
        device_serial = val if key == 'SerialNumber'
        device_version = val if key == 'ProductVersion'
        device_build_version = val if key == 'BuildVersion'
        mac_address = val if key == 'WiFiAddress'
        imsi = val if key == 'InternationalMobileSubscriberIdentity'
        iccid = val if key == 'IntegratedCircuitCardIdentity2'
        chipid = val if key == 'UniqueChipID'
      end
      imsi = (@device_name.start_with? "iPad") ? "" : imsi # rubocop:todo Style/TernaryParentheses
      if @config && ((!@device_name.start_with? "iPad") && (@config["imsi"] != imsi))
        BrowserStack.logger.info("storing imsi: #{imsi} for #{@device}")
        telephony = Telephony.new(@server_config["telephony_service_endpoint"], @server_config["telephony_service_username"], @server_config["telephony_service_password"])
        telephony.push_imsi(@device, imsi)
      end
      [device_serial, device_version, device_build_version, mac_address, imsi, iccid, chipid]
    end

    def valid_device_info?(value)
      value && !value.strip.empty? && value.downcase != 'unknown'
    end

    def fetch_device_chipset
      begin
        output = IdeviceUtils.ideviceinfo(@device, "HardwarePlatform")
        chipset = output.is_a?(Array) && !output.empty? ? output[0].strip : nil
        BrowserStack.logger.info("Detected chipset: #{chipset}")
      rescue => e
        BrowserStack.logger.error("Error fetching chipset: #{e.message}")
        chipset = nil
      end

      valid_device_info?(chipset) ? chipset : nil
    end

    def fetch_device_storage
      begin
        disk_space_bytes = IdeviceUtils.disk_usage(@device)
        # Defensive: ensure disk_space_bytes is not nill
        raise "Invalid disk space value: #{disk_space_bytes.inspect}" if disk_space_bytes.nil?

        disk_space_gb = (disk_space_bytes.to_f / (1000 * 1000 * 1000)).to_i
        storage_value = "#{disk_space_gb}GB"
        BrowserStack.logger.info("Detected Storage: #{storage_value}")
      rescue => e
        BrowserStack.logger.error("Error fetching Storage: #{e.message}")
        storage_value = nil
      end

      valid_device_info?(storage_value) ? storage_value : nil
    end

    def fetch_device_resolution
      begin
        display_output = DeviceCtl::Device.displays(@device)
        native_size_match = display_output.match(/nativeSize:\s*\(([\d.]+),\s*([\d.]+)\)/)
        orientation_match = display_output.match(/currentOrientation:\s*(\w+)/)

        if native_size_match
          width = native_size_match[1].to_i
          height = native_size_match[2].to_i

          resolution = if orientation_match && orientation_match[1].downcase == "rot90"
                         "#{height}x#{width}"  # swap for rot90
                       else
                         "#{width}x#{height}"
                       end

          BrowserStack.logger.info("Detected resolution: #{resolution}")
        else
          resolution = nil
        end
      rescue => e
        BrowserStack.logger.error("Error fetching iOS resolution: #{e.message}")
        resolution = nil
      end

      valid_device_info?(resolution) ? resolution : nil
    end

    def get_machine_version
      vers = OSUtils.macos_version

      if Gem::Version.new(vers) >= Gem::Version.new('13.0')
        'ven'
      elsif Gem::Version.new(vers) >= Gem::Version.new('12.0')
        'mty'
      elsif Gem::Version.new(vers) >= Gem::Version.new('11.0')
        'bsr'
      else
        ''
      end
    end

    def inventory_file
      "#{@@config_root}/data_#{@device}.json"
    end

    def location_services_enabled_file
      "#{@@config_root}/location_services_enabled_#{@device}"
    end

    def unclean_lockdown_file
      "/tmp/unclean_lockdown_#{@device}"
    end

    def unclean_not_on_ideviceinfo_file
      "#{@server_config['state_files_dir']}/unclean_not_on_ideviceinfo_#{@device}"
    end

    def write_inventory_data
      BrowserStack.logger.info("writing inventory data for #{@device}")
      data = {}
      data['owner'] = 'BrowserStack'
      data['team'] = 'mobile'
      data['region'] = @@region
      data['infra_api'] = @@infra_api
      data['serial_number'] = @device_serial
      Utils.write_to_file(inventory_file, data.to_json)
    end

    def check_connected
      ecid = @config.nil? ? nil : @config['device_ecid']
      recover_device = RecoverDevice.new(@device, ecid)

      begin
        recover_device.attempt

        # send device to cleanup after recovery if unclean
        # TODO: we should ideally send every device to cleanup after recovery
        if needs_cleanup?
          reason = 'cleaning recovered unclean device'
          clean_recovered_device(reason)
          raise MobileCheckException, reason
        end
      rescue RecoverDevice::DeviceNotOnIdeviceInfo,
             RecoverDevice::DeviceNotOnUSB,
             RecoverDevice::DeviceCtlError,
             RecoverDevice::RecoverDeviceTimedOut => e
        if File.exists?(unclean_lockdown_file)
          FileUtils.rm(unclean_lockdown_file)
          FileUtils.touch(unclean_not_on_ideviceinfo_file)
        end

        raise MobileCheckException, e.message
      rescue RecoverDevice::DeviceInLockdown => e
        if File.exists?(unclean_not_on_ideviceinfo_file)
          FileUtils.rm(unclean_not_on_ideviceinfo_file)
          FileUtils.touch(unclean_lockdown_file)
        end
        raise MobileCheckException, e.message
      end
    end

    def needs_cleanup?
      File.exists?(unclean_lockdown_file) || File.exists?(unclean_not_on_ideviceinfo_file)
    end

    def clean_recovered_device(reason)
      send_cleanup(reason)
      FileUtils.rm(unclean_lockdown_file) if File.exists?(unclean_lockdown_file)
      FileUtils.rm(unclean_not_on_ideviceinfo_file) if File.exists?(unclean_not_on_ideviceinfo_file)
    end

    def send_cleanup_for_offline_reason?(reason)
      reason.include?("device not supervised") ||
      reason.include?("device is not using internet sharing") ||
      reason.downcase.include?("wda is not running")
    end

    def send_cleanup(reason, full_cleanup = false)
      # Touching manual cleanup file because rails only retries cleanup on a predefined set of
      # offline reasons.
      # Refer https://github.com/browserstack/railsApp/blob/81d9b5c14b4f7233709ac45d4bf334de17611f5b/app/models/terminal.rb#L1494-L1498
      device_state.touch_manual_cleanup_file

      BrowserStack.logger.info("(device: #{@device}) sending cleanup for reason: #{reason}")
      FileUtils.rm_f(setup_requested_file) # Cleanup refuses to run if this file exists
      uri = URI('http://localhost:45680/cleanup')
      uri_params = { device: @device,
                     retry_cleanup: true,
                     read_without_lock: true,
                     from: "device_check",
                     reason: reason,
                     full_cleanup: full_cleanup }
      uri.query = URI.encode_www_form(uri_params)
      res = Net::HTTP.get_response(uri)
      if res.is_a?(Net::HTTPSuccess)
        BrowserStack.logger.info "(device: #{@device}) success : #{res.body}"
        Zombie.push_logs("device-check-cleanup", reason, { device: @device })
        @influxdb_client.event(@device, 'device-check-cleanup')
      else
        BrowserStack.logger.info "(device: #{@device}) failed : #{res.body}"
      end
    end

    # Used for Full cleanup flow
    def backup_checks
      return unless @backup_manager.supported?

      # Refer to: https://github.com/browserstack/realmobile/pull/3737
      raise MobileCheckException, "MobileSync symlink doesn't exist" unless @backup_manager.machine_provisioned?

      BrowserStack.logger.info "Running backup checks: #{@ex_offline_reason}"

      if @ex_offline_reason.start_with?("Missing or incorrect backup files for device") ||
          @ex_offline_reason.start_with?("New backup files available")
        begin
          BrowserStack.logger.info "Retrieving Backup"
          @backup_manager.retrieve_backup # fetch backup file from DE if not present
          @device_state.remove_force_backup_redownload_file

          if @backup_manager.backup_ready?
            BrowserStack.logger.info "Backup downloaded successfully"
            return
          else
            raise MobileCheckException, "Backup not ready after download, please fix"
          end
        rescue BackupManager::IOSBackupManagerError => e
          BrowserStack.logger.info "Failed to retrieve backup: #{e.message} #{e.backtrace}"
          raise MobileCheckException, "Failed to retrieve backup: #{e.message}" # CAUTION: Add retry, but make sure to add a threshold for retries.
        end
      end

      if @device_state.force_backup_redownload_file_present?
        @backup_manager.clean
        raise MobileCheckException, "New backup files available"
      end

      if @backup_manager.backup_ready?
        BrowserStack.logger.info "Backup already downloaded and ready"
      else
        raise MobileCheckException, "Missing or incorrect backup files for device"
      end
    end

    def ppuid_independent_checks # rubocop:todo Metrics/AbcSize
      # TODO: check why would we ever have a nil or empty config?
      present_in_config = !(@config.nil? || @config.empty?)
      device_check_lock_file = "/tmp/device_check_local_lock_#{@device}"
      FileUtils.touch("/tmp/unclean_bad_enterprise_app_#{@device}") unless present_in_config
      FileUtils.touch("/tmp/orientation_lock_opened_#{@device}") unless present_in_config
      device_name = present_in_config ? @config["device_name"] : @device_name
      device_version = present_in_config ? @config["device_version"] : @device_version

      dc.check_all_contents_and_settings_erased
      dc.check_platform_version_consistency(@device_version)
      dc.check_device_supervised
      dc.check_rsd_values if device_version.to_i >= 17
      @battery_level = dc.check_battery(device_name)
      dc.airplane_mode
      dc.check_device_date
      DeveloperMode.check_and_enable(@device) if device_version.to_i >= 16
      dc.check_developer_image_mounted(@device_version, @@developer_disk_images_path)
      dc.check_developer_symbols(@device_version, @@developer_symbols_path)
      dc.verify_iproxy_version_for_device(device_version)
      dc.check_media_backup(@server_config, @device)
      dc.verify_device_firewall_rules(@server_config, @config)
      config_for_privoxy = present_in_config ? @config : { "selenium_port" => @selenium_port, "ip" => @@ip }
      FileUtils.touch(device_check_lock_file)
      unless device_in_use?
        dc.ensure_privoxy_running_plist(
          @@user,
          @server_config,
          config_for_privoxy,
          @@logging_root
        )
      end

      begin
        FileUtils.rm(device_check_lock_file)
      rescue
        nil
      end
      dc.ensure_ios_webkit_debug_proxy_running_plist @@user, @device, @port_webkit, @@logging_root
      dc.devtools_proxy_server_running? @@devtools_port
      dc.ensure_no_sim_alert(@idevice) if ["not on ideviceinfo", "Lockdown issue # -8"].include? @ex_offline_reason
    end

    def wda_xctestrun_checks
      ret_val = {
        "web_driver_agent_xctestrun_file" => {}
      }

      @@web_driver_agents.each_key do |appium_version|
        # Create missing xctestrun files
        wda_version = WDAVersion.new(
          @device,
          appium_version,
          @device_version
        )
        platform_version = BrowserStack::XcodeUtils.get_platform_version
        appium_version_xctestrun_file = wda_version.xctestrun_file_template(platform_version)
        begin
          wda_version.xctestrun_fixup
        rescue XCTestRunFileMissing
          BrowserStack.logger.warn "xctestrun files are not downloaded yet, install phase should trigger on this shortly."
        end

        ret_val["web_driver_agent_xctestrun_file"][appium_version] = appium_version_xctestrun_file
      end

      ret_val
    end

    def ppuid_dependent_checks(new_config) # rubocop:todo Metrics/AbcSize, Metrics/MethodLength, Metrics/PerceivedComplexity, Metrics/CyclomaticComplexity
      dc.check_internet_sharing(@port_webdriver) if @@internet_sharing_enabled

      ret_val = {}
      wda_version = @server_config['default_wda_version'] || @@default_appium_version
      wda_xctestrun_file = begin
        Utils.get_web_driver_agent_xctestrun_file(new_config, wda_version)
      rescue
        nil
      end
      raise "No xctestrunfile found for appium plist" if wda_xctestrun_file.nil?

      unless device_in_use?
        appium_server = AppiumServer.new(@device, new_config)
        appium_server.start_server_for_version(@@default_appium_version)
      end

      provision_profile = @device_state.ppuid_file_to_array[2].strip

      if install_phase_running?
        ret_val['offline_reason'] = if device_state.install_phase_failed_file_present?
                                      failure_reason = device_state.read_install_phase_failed_file
                                      "install-phase failed: #{failure_reason}"
                                    else
                                      'install-phase is still running'
                                    end
        ret_val['online'] = false
        return ret_val
      end

      # Same caching app logic for browserstack app
      BrowserStackAppHelper.cache_browserstack_app(@device)

      # BEGIN: Temporary hack for iOS 17 to handle the app caching in xcode
      # Install the browserstack app from \
      # /usr/local/.browserstack/xctest_session_apps_ios17/device_id/ folder \
      # with app name as test_app.app
      # TODO: Remove this once apple fixes the issue with xcode caching
      BrowserStackAppHelper.cache_as_test_app(@device)
      # END: Temporary hack for iOS 17 to handle the app caching in xcode

      # Trigger install phase if install phase requirements are not empty
      install_check = InstallCheck.new(
        @device, @device_version, provision_profile, @config, full_check: @device_check_lock
      )
      install_phase_requirement = install_check.check_requirements

      if install_phase_requirement.empty?
        BrowserStack.logger.info("No need to trigger install-phase for #{@device}. Skipping install-phase...")
        device_state.remove_install_phase_failed_file
      else
        ret_val['online'] = false
        component_to_install = install_phase_requirement.first

        BrowserStack.logger.info("Triggering install-phase for #{@device} due to #{component_to_install}")
        trigger_install_phase_reason = trigger_install_phase(component_to_install, provision_profile)
        ret_val['offline_reason'] = if device_state.install_phase_failed_file_present?
                                      failure_reason = device_state.read_install_phase_failed_file
                                      "install-phase failed: #{failure_reason}"
                                    else
                                      trigger_install_phase_reason
                                    end

        return ret_val
      end

      # MDM Checks
      present_in_config = !(@config.nil? || @config.empty?)
      profile_check_counter = begin
        @config["profile_check_counter"]
      rescue
        0
      end

      begin
        # Check if mdm server is up and mark device offline if SSL cert on the mdm server has expired.
        dc.check_mdm_server
        # Check the state of the device on mdm
        # 1. When force re-install of mdm profiles is expected.
        # 2. Every 15th device check (basis the profile_check_counter, currently set as 15).
        force_install = !present_in_config || @device_state.force_install_mdm_profiles_file_present?
        dc.check_mdm_settings(@device_state.proxy_pac_url(@@ip), force_install, @@profile_check_limit, @@mdm_profiles_required, @device_name, @device_version, @ex_offline_reason, profile_check_counter)
        @device_state.remove_force_install_mdm_profiles_file if force_install
      # rescue MdmApiFatalException first as it's a subclass of MdmApiException
      rescue MdmApiFatalException => e
        BrowserStack.logger.error("mdm api fatal exception for device #{@device} #{e.message} #{e.backtrace}")
        Zombie.push_logs("mdm-fatal-exception", e.message, { device: @device })
        raise MobileCheckException, "#{OFFLINE_REASON_DEVICE_NOT_SUPERVISED}: #{e.message}"
      rescue MdmApiException => e
        # Not raising MDM errors into device check errors, as we do not want device availability to be dependent on MDM
        Zombie.push_logs("mdm-exception", e.message, { device: @device })
        BrowserStack.logger.error("mdm api exception for device #{@device} #{e.message} #{e.backtrace}")
        @influxdb_client.event(@device, 'mdm-exception', subcomponent: 'ppuid-dependent-checks', is_error: true)
        # Unless it is a new device or is already offline with mdm reason
        raise MobileCheckException, "#{OFFLINE_REASON_DEVICE_NOT_SUPERVISED}: #{e.message}" if !present_in_config || @ex_offline_reason.include?(OFFLINE_REASON_DEVICE_NOT_SUPERVISED)
      end

      #FIXME: make this work with multiple prov profiles, this relies on the first prov profile being right
      wda_version = @server_config['default_wda_version'] || @@default_appium_version
      dc.ensure_webdriveragentrunner_version(@device, @config, wda_version)
      begin
        dc.ensure_xcodebuild_running(@ex_offline_reason, @idevice, @device, @port_webdriver) unless device_in_use?
      rescue MobileCheckException => e
        if e.message.include?("max retries reached")
          # <Instrumentation added on Thu Dec 19 12:59:15 GMT 2019>
          instrumentation__xctestrun_file = begin
            Utils.get_web_driver_agent_xctestrun_file(@config, wda_version)
          rescue
            "<error getting it>"
          end

          instrumentation__xctestrun_file_contents = begin
            File.read(instrumentation__xctestrun_file).split("\n").length
          rescue
            "<error>"
          end
          Zombie.push_logs("using-cleanup-to-fix-xcodebuild", @device, {
            data: {
              model: @device_name,
              xctestrun_file: instrumentation__xctestrun_file,
              xctestrun_file_contents: instrumentation__xctestrun_file_contents,
              default_appium: @@default_appium_version || "variable is falsey"
            }.to_json
          })
          @influxdb_client.event(@device, 'using-cleanup-to-fix-xcodebuild', subcomponent: 'fix-xcodebuild', is_error: true)
          # </Instrumentation>
          send_cleanup(e.message)
        end
        raise e
      end

      dc.check_device_internet(@port_webdriver, @device_version) unless device_in_use?
      dc.check_insecure_websocket_proxy_map(@port_webdriver, @device_version, @selenium_port)
      ret_val
    end

    def install_phase_running?
      begin
        install_phase_executor = DeviceForkExecutor.new(@device, INSTALL_PHASE[:name], STATE_FILES_DIR, BrowserStack.logger)
      rescue JSON::ParserError => e
        # TODO: Remove this rescue block when we figure out why the JSON is being written twice to the state file:
        # {
        #   "pid": 92384,
        #   "started_at": "2021-09-23 14:53:50 UTC",
        #   "thread_id": 17460
        # }
        # {
        #   "pid": 92384,
        #   "started_at": "2021-09-23 14:53:50 UTC",
        #   "thread_id": 17460
        # }
        # FIXME: https://browserstack.atlassian.net/browse/MOB-4435
        BrowserStack.logger.error("Unable to check if install phase is running: #{e.message} #{e.backtrace}")
        FileUtils.rm_f(File.join(STATE_FILES_DIR, "#{INSTALL_PHASE[:name]}_#{@device}.json"))
        # Returning true so that we don't spawn another process for install phase because we don't actually know
        # if the process is running or not as parsing the state file failed which contains the PID of install phase.
        return true
      end

      if install_phase_executor.process_running?
        issue_cleanup_if_install_phase_is_stuck(install_phase_executor.executor)
        true
      else
        BrowserStack.logger.info("install-phase is not running")
        false
      end
    end

    def issue_cleanup_if_install_phase_is_stuck(executor)
      # Device should eventually come online after multiple cycles.
      started_at = executor.started_at
      running_time = Time.now - started_at
      BrowserStack.logger.info("install-phase is still running on the device... pid: #{executor.pid}, since: #{started_at} for (#{running_time} seconds)")
      install_phase_time_bound = INSTALL_PHASE[:timeout] # seconds

      return if running_time < install_phase_time_bound

      BrowserStack.logger.warn("install-phase has been running for more than #{install_phase_time_bound} seconds, killing it assuming it hanged. Triggering cleanup.")
      executor.reset!
      send_cleanup('install-phase is stuck')
    end

    def trigger_install_phase(component_to_install, provision_profile)
      executor = DeviceForkExecutor.new(
        @device,
        INSTALL_PHASE[:name],
        STATE_FILES_DIR,
        BrowserStack.logger,
        { subcomponent: "install_phase" }
      )

      spawned, metadata = executor.run_once do
        push_logs_to_zombie("#{component_to_install} installation")
        installer = Installer.new(@device, @device_version, provision_profile, @config || {})
        installer.trigger_install
      end

      if spawned
        BrowserStack.logger.info("Spawned install-phase process for the device... pid: #{metadata[:pid]}, at: #{metadata[:started_at]}")
        offline_reason = "Triggered install-phase due to reason: #{component_to_install} installation"
      else
        # Ideally this should never be executed
        BrowserStack.logger.error("install-phase is not triggered as it's already running. metadata: #{metadata}")
        offline_reason = "Tried to trigger another install-phase"
      end
      offline_reason
    end

    def push_logs_to_zombie(offline_reason, data = {})
      # TODO: push only changes in state, add filtering maybe
      Zombie.push_logs("mobile-offline", offline_reason, data) if offline_reason != "Device under cleaning" && offline_reason != @ex_offline_reason

      if offline_reason != "Device under cleaning"
        # All offline reasons should have some code assigned to them, then we can push to influx
        @influxdb_client.event(@device, 'mobile-offline', subcomponent: 'push-logs-to-zombie', is_error: true)
      end
    end

    def reboot_if_still_offline(device, temp_json, offline_reboot_time) # rubocop:todo Metrics/AbcSize
      return if temp_json['offline_reason'] !~ /#{INTERNET_DOWN_MESSAGE}/ && !@device_state.dedicated_device_file_present?

      now = Time.now
      last_online = temp_json['last_online'] ? Time.parse(temp_json['last_online']) : now
      last_rebooted = temp_json['last_rebooted'] ? Time.parse(temp_json['last_rebooted']) : nil
      offline_reboot_time = @device_state.dedicated_device_file_present? ? DEDICATED_OFFLINE_REBOOT_TIME : offline_reboot_time
      if last_online < now - offline_reboot_time && (!last_rebooted || last_rebooted < last_online)
        BrowserStack.logger.info(OFFLINE_REBOOT_MESSAGE)
        IdeviceUtils.reboot(device) unless @device_state.dedicated_device_file_present? # reboot only public devices
        temp_json['last_rebooted'] = now.to_s unless @device_state.dedicated_device_file_present? # update last rebooted only for public devices
        begin
          if @device_state.dedicated_device_file_present?

            # Skip reboot if the device was rebooted within the last 24 hours
            if last_rebooted && (now - last_rebooted) < 24 * 60 * 60
              BrowserStack.logger.info "Device was rebooted within last 24 hours, skipping reboot for dedicated device #{device}"
              return
            end

            # Skip reboot if the device has been offline for more than 3 days and was rebooted in the last 2 days
            if (now - last_online) > 3 * 24 * 60 * 60 && last_rebooted && (now - last_rebooted) < 2 * 24 * 60 * 60
              BrowserStack.logger.info "Device was rebooted in last 2 days after being offline for over 3 days, skipping reboot for dedicated device #{device}"
              return
            end

            # Perform the reboot
            DeviceManager.reboot_and_wait(device) # Using reboot_and_wait to ensure the device is back online
            temp_json['last_rebooted'] = now.to_s
            BrowserStack.logger.info "Rebooted dedicated device #{device} and performing cleanup"
            send_cleanup("Device was offline over threshold and now rebooted, sending device #{device} for cleanup")
          end
        rescue => e
          BrowserStack.logger.error "Error while handling dedicated device reboot: #{e.message} #{e.backtrace}"
        end
      end
    end

    def update_last_online(temp_json)
      # TODO: The int -> str conversion here is to migrate old values to the new format
      # Remove after all configs have been updated.
      temp_json['last_online'] = Time.at(temp_json['last_online']).to_s if temp_json['last_online'].is_a? Integer
    end

    # Primary method containing the flow of device check
    def run # rubocop:todo Metrics/CyclomaticComplexity, Metrics/AbcSize, Metrics/MethodLength, Metrics/PerceivedComplexity
      BrowserStack.logger.info "(device: #{@device}) run started for #{@device}"
      BrowserStack.logger.info "#{@device} holding device check lock" if @device_check_lock

      temp_json = @config || {}
      Thread.current[@device] = temp_json

      temp_json['device_chipset'] = valid_device_info?(temp_json['device_chipset']) ? temp_json['device_chipset'] : fetch_device_chipset
      temp_json['device_storage'] = valid_device_info?(temp_json['device_storage']) ? temp_json['device_storage'] : fetch_device_storage
      temp_json['device_resolution'] = valid_device_info?(temp_json['device_resolution']) ? temp_json['device_resolution'] : fetch_device_resolution

      # New devices need to run cleanup, part of the setup is there. Schedule one:
      device_state.touch_cleanup_requested_file unless @config
      device_state.touch_cleanup_requested_file if IdeviceUtils.apple_tv_device?(@device) && cleanup_failure_reason.downcase.include?("wda is not running")
      @backup_manager = BackupManagerFactory.for(@device, temp_json["device_version"] || "")

      temp_json['ip'] = @@ip
      temp_json['port'] ||= @port
      temp_json['selenium_port'] = @selenium_port = @port

      temp_json['dev_tool_port'] ||= @@devtools_port
      @debugger_port = temp_json['debugger_port'] ||= @port_webkit

      temp_json['webdriver_port'] = @port_webdriver

      @device_name = temp_json['device_name'] ||= 'unknown'

      temp_json['zotac_host'] = @@hostname if temp_json['zotac_host'] != @@hostname
      temp_json['os'] = IdeviceUtils.os(@device)
      temp_json['region'] = @@region
      temp_json['sub_region'] = @@sub_region
      temp_json['for_live'] ||= false # initial release is not for live (only automate)
      temp_json['pool_mask'] = if DeviceSIMHelper.esim?(@device) && DeviceSIMHelper.public_sim?(@device)
                                 POOL_MASK[:esim_enabled_device]
                               elsif DeviceSIMHelper.physical_sim_ready?(@device) && DeviceSIMHelper.public_sim?(@device)
                                 POOL_MASK[:sim_enabled_device]
                               elsif Secure::ApplePay.apple_pay_configured?(@device)
                                 POOL_MASK[:apple_pay_device]
                               elsif BrowserStack::VoiceoverHelper.voiceover_device?(@device)
                                 BrowserStack::VoiceoverHelper.reserved_voiceover_device?(@device_name) ? POOL_MASK[:voiceover_reserved_device] : POOL_MASK[:voiceover_shared_device]
                               elsif @backup_manager.supported?
                                 POOL_MASK[:full_cleanup_supported]
                               else
                                 @@static_conf['pool_mask']
                               end

      temp_json['active_session'] = "false"
      temp_json['app_live_network_logs_port'] = @debugger_port + 100
      temp_json['device_logger_port'] ||= "2#{@port.to_i + 400}"

      # Update ppuid files of all devices to use the latest one
      if @device_state.ppuid_file_present?
        BrowserStack.logger.info "Updating ppuid file if needed"
        ppuid_branch, team_id, ppuid_hash = ppuid_file.contents
        latest_ppuid_hash = ProvisioningManager.read_profile_config[ppuid_branch]["ppuid"]
        ppuid_file.write(ppuid_branch, team_id, latest_ppuid_hash) unless latest_ppuid_hash.eql?(ppuid_hash)
      end

      if device_in_use?
        BrowserStack.logger.info "(device: #{@device}) cleanup|session|session_start lock file exists for #{@device} not running .. marking offline"
        temp_json['online'] = false
        temp_json['offline_reason'] = if @device_state.manual_cleanup_file_present?
                                        "manual cleanup"
                                      else
                                        "Device under cleaning"
                                      end
        temp_json['cleanup_failure_reason'] = cleanup_failure_reason if need_offline_detail?(temp_json)
        temp_json['active_session'] = (!File.exists?(cleanup_done_file) && File.exists?(session_file)).to_s
        push_offline_reason_to_zombie(temp_json)
        reboot_if_still_offline(@device, temp_json, DEDICATED_OFFLINE_REBOOT_TIME) if @device_state.dedicated_device_file_present? && (Time.now.to_i - Time.parse(temp_json['last_online']).to_i > DEDICATED_OFFLINE_REBOOT_TIME) && File.exist?(cleanup_done_file)
        return temp_json
      end

      begin
        check_connected
      rescue MobileCheckException => e
        temp_json['online'] = false
        temp_json['offline_reason'] = e.message || e.reason
        BrowserStack.logger.warn "Mobile check exception, marking offline as #{temp_json['offline_reason']}"
        push_logs_to_zombie(temp_json['offline_reason'], { "device" => @device, "data" => @device_name })
        push_offline_reason_to_zombie(temp_json)
        return temp_json
      end

      if @device_state.minimized_cleanup_reserved_file_present?
        BrowserStack.logger.info("Skipping device check as #{@device} is a reserved device.")
        if @device_state.minimized_cleanup_reserved_file_older_than_minutes?(DEVICE_RESERVED_UNDER_MINIFIED_FLOW_THRESHOLD)
          @device_state.remove_minimized_cleanup_reserved_file
          @device_state.remove_preserve_app_state_reserved_file
          temp_json['online'] = false
          temp_json['offline_reason'] = 'unclean_reserved_device_needs_releasing'
          temp_json['cleanup_failure_reason'] = ''
          push_logs_to_zombie(temp_json['offline_reason'], { "device" => @device, "data" => @device_name })
          push_offline_reason_to_zombie(temp_json)
          BrowserStack.logger.info("unclean_reserved_device_needs_releasing : remove_minimized_cleanup_reserved_file")
        end
        return temp_json
      end

      temp_json['testflight_installed'] = dc.testflight_installed? if @device_check_lock

      temp_json['current_appium_version'] = @@default_appium_version

      @device_serial, @device_version, @device_build_version, @mac_address, @imsi, @iccid, @chipid = get_device_info
      temp_json['machine_version'] = nil

      update_sim_details(temp_json)

      if Utils.empty?(temp_json['device_name']) || Utils.empty?(temp_json['device_serial']) || Utils.empty?(temp_json['device_version']) || Utils.empty?(temp_json['device_ecid'])
        temp_json['device_name'] = @device_name
        temp_json['device_serial'] = @device_serial
        temp_json['device_version'] = @device_version
        # Converting the chipID to hex which corresponds to the ecid returned by cfgutil list command
        # tools like cfgutil and irecovery identify devices on this hex value hence storing it like that
        temp_json['device_ecid'] = "0x#{@chipid.to_i.to_s(16).rjust(13, '0').upcase}"
        @backup_manager.clean if @backup_manager.supported? # In case some residual files are present after removedevice
      end

      temp_json['device_ecid'] = "0x#{@chipid.to_i.to_s(16).rjust(13, '0').upcase}" if temp_json['device_ecid'].length < 15

      unless @device_build_version.empty?
        # This means device was upgraded
        if !Utils.empty?(temp_json['device_build_version']) && temp_json['device_build_version'] != @device_build_version
          notify_device_upgraded(temp_json['device_build_version'])

          device_check_state = BrowserStack::UpgradedDeviceCheckState.new(@device)
          device_check_state.discard_old_states # Get rid of old state files
          @backup_manager.clean if @backup_manager.supported? # Get rid of the old backup files
        end
        temp_json['device_build_version'] = @device_build_version
      end

      if temp_json['device_version'] != @device_version && !@device_version.empty?
        # device upgraded
        temp_json['device_version'] = @device_version
      end

      temp_json['mac_address'] = @mac_address if temp_json['mac_address'] != @mac_address && !@mac_address.empty?

      temp_json['imsi'] = @imsi if temp_json['imsi'] != @imsi && !@imsi.empty?
      temp_json['iccid'] = @iccid if temp_json['iccid'] != @iccid && !@iccid.empty?

      temp_json["profile_check_counter"] = (temp_json["profile_check_counter"].to_i % 1000)

      begin
        temp_json['online'] = true
        temp_json['offline_reason'] = ''

        write_inventory_data unless File.file?(inventory_file)

        BrowserStack.logger.info "(device: #{@device}) Beginning phone checks"

        backup_checks
        ppuid_independent_checks

        temp_json['battery_level'] = @battery_level

        # device check lock ensures that during a certificate rotation,
        # devices go offline and recover one by one.
        # If a provisioning profile gets updated, a full install check is needed
        # to resign apps if necessary. Full install check also requires device check lock.
        # The rotate certificate file can be used to force an immediate rotation (for dev use).
        if @device_check_lock || @device_state.rotate_certificate_file_present?
          # Ensure device is provisioned & ppuid is valid
          provisioning_manager = ProvisioningManager.new(@device)
          provisioning_manager.update(@device_name) if provisioning_manager.problem_detected?
          @device_state.remove_needs_provisioning_file
        elsif @device_state.needs_provisioning_file_present?
          # Devices which require setup will fail ppuid dependent checks, and must
          # wait offline until they have been provisioned successfully.
          raise 'provisioning: waiting for device check lock'
        end

        if @device_state.ppuid_file_present?
          new_hash = wda_xctestrun_checks
          temp_json.deep_merge!(new_hash)
          unless device_in_use?
            new_hash2 = ppuid_dependent_checks(temp_json)
            temp_json.deep_merge!(new_hash2)
          end
        end
      rescue MobileCheckException, MdmApiException => e
        BrowserStack.logger.error "(device: #{@device}) Exception while checking #{@device}: #{e.backtrace}"
        Zombie.push_logs("mdm-fatal-exception", e.message, { device: @device }) if e.instance_of?(MdmApiException)
        temp_json['online'] = false
        temp_json['offline_reason'] = e.message || e.reason

        push_logs_to_zombie(temp_json['offline_reason'], { "device" => @device, "data" => @device_name })
      rescue => e
        Zombie.push_logs("mdm-fatal-exception", e.message, { device: @device }) if e.instance_of?(MdmApiFatalException)
        BrowserStack.logger.error "(device: #{@device}) Exception while checking #{@device}: #{e}: #{e.backtrace}"
        temp_json['online'] = false
        temp_json['offline_reason'] = e.to_s[0..75]
      ensure
        begin
          FileUtils.rm("/tmp/device_check_local_lock_#{@device}")
        rescue
          nil
        end
      end

      if device_in_use?
        BrowserStack.logger.info "(device: #{@device}) cleanup|session|session_start lock file exists for #{@device} marking offline"
        temp_json['online'] = false
        temp_json['offline_reason'] = 'Device under cleaning'
      end

      update_last_online(temp_json)
      if temp_json['online']
        temp_json["last_online"] = Time.now.to_s
      else
        reboot_if_still_offline(@device, temp_json, OFFLINE_REBOOT_TIME)
      end

      if @device_state.ppuid_file_present?
        temp_json['mobileprovision_branch'] = begin
          @device_state.ppuid_file_to_array[0].to_s
        rescue
          ""
        end
      end

      temp_json['location_enabled'] = "true" if temp_json['location_enabled'].nil? && File.exists?(location_services_enabled_file)

      temp_json = state_change_action(temp_json)

      if setup_requested? && temp_json['online']
        # Remove this before firing a cleanup. Cleanup refuses to run if a
        # setup has been requested.
        FileUtils.rm(setup_requested_file)
      end

      should_send_cleanup = send_cleanup_for_offline_reason?(temp_json['offline_reason'])
      if cleanup_requested? && (temp_json['online'] == true || should_send_cleanup)
        BrowserStack.logger.info "cleanup requested for #{@device}, sending to cleanup"
        temp_json['online'] = false
        temp_json['offline_reason'] = 'Device under cleaning' unless should_send_cleanup
        send_cleanup('unclean device file present')
      end

      if need_offline_detail?(temp_json)
        temp_json['cleanup_failure_reason'] = cleanup_failure_reason
      else
        temp_json.delete('cleanup_failure_reason')
      end

      if device_state.undergoing_update_file_present?
        temp_json['online'] = false
        temp_json['offline_reason'] = "Device under update"
      end

      # Push offline reason to zombie
      push_offline_reason_to_zombie(temp_json)

      # Push installed apps info to zombie
      begin
        push_installed_apps
      rescue => e
        BrowserStack.logger.info("Failed to push installed apps info to BQ for device #{@device} #{e.message} #{e.backtrace}")
      end

      if dc.invalid_custom_mdm_device?
        temp_json['online'] = false
        temp_json['offline_reason'] = "CustomMDM check failed"
      end

      temp_json
    end

    # *********************************************
    # Updates for device state change
    # *********************************************
    def state_change_action(temp_json)
      # If device is offline post being online
      if !temp_json['online'] && @ex_online_state == true
        push_logs_to_zombie(
          temp_json['offline_reason'], { 'device' => @device, 'data' => @device_name }
        )
      end

      # If device is online
      temp_json['profile_check_counter'] = temp_json['profile_check_counter'] + 1 if temp_json['online']

      temp_json
    end

    def device_in_use?
      # TODO: Check for mtime too, sometimes lockfile needs to be deleted manually if the cleanup crashes.
      # => The device gets stuck in cleaning until manually deleted
      return false if setup_requested?

      %i[cleanup_done_file session_file session_start_file].each do |file|
        next unless File.exists?(send(file))

        BrowserStack.logger&.info("Device is in use because #{file} : #{send(file)} exists")

        return true
      end
      false
    end

    def setup_requested?
      File.exists?(setup_requested_file)
    end

    def cleanup_requested?
      device_state.cleanup_requested_file_present?
    end

    def need_offline_detail?(temp_json)
      (cleanup_failure_reason != "") && (['manual cleanup', 'Device under cleaning'].include?(temp_json['offline_reason']) ||
        File.exist?("#{STATE_FILES_DIR}/cleanupdone_#{@device}")) # Also check the cleanupdone file because it offline_reason might not have been updated
    end

    def session_file
      "#{@server_config['state_files_dir']}/#{@device}_session"
    end

    def session_start_file
      "#{@server_config['state_files_dir']}/#{@device}_session_start_indicator"
    end

    def cleanup_done_file
      "#{@server_config['state_files_dir']}/cleanupdone_#{@device}"
    end

    def setup_requested_file
      "#{@server_config['state_files_dir']}/setup_requested_#{@device}"
    end

    def cleanup_failure_reason
      row = CleanupStatusDb::Cleanup[@device]
      return '' unless row

      row[:failure_reason]
    end

    def push_offline_reason_to_zombie(temp_json)

      return if temp_json['online'] ||
                temp_json['device_check_count'].nil? || # Device check hasn't run for this device before
                (temp_json['device_check_count'] % OFFLINE_ZOMBIE_PUSH_INTERVAL) != 1

      offline_reason = temp_json['offline_reason']

      if ["Device under cleaning", "manual cleanup"].include? offline_reason
        return if temp_json['cleanup_failure_reason'].nil?

        offline_reason = temp_json['cleanup_failure_reason']
      end

      offline_reasons_to_exclude = ["Moved"]
      return if offline_reasons_to_exclude.any? { |word| offline_reason.include?(word) }

      trimmed_offline_reason = offline_reason.sub(/low battery.*/, "low battery").sub(/timeout.*/, "timeout")
      zombie_data = { "device" => @device, "data" => @device_name }

      BrowserStack.logger.info("Pushing offline reason: #{trimmed_offline_reason} to zombie")
      Zombie.push_logs("mobile_offline_reason", trimmed_offline_reason, zombie_data)
    rescue => e
      BrowserStack.logger.info("Failed to push offline reason to BQ for device #{@device} #{e.message} #{e.backtrace}")

    end

    def notify_device_upgraded(old_build_version)
      BrowserStack.logger.info("BuildVersion upgrade detected, old_build_version: #{old_build_version}, new_build_version: #{@device_build_version}")
      Zombie.push_logs("device_upgraded", '', {
        old_build_version: old_build_version,
        new_build_version: @device_build_version
      })
    end

    def update_sim_details(temp_json)
      device_is_dedicated = @device_state.dedicated_device_file_present?
      temp_json['sim_details'] = DeviceSIMHelper.sim_info(@device, force_check: device_is_dedicated)
    rescue => e
      BrowserStack.logger.error("Error updating sim details: #{e.message} #{e.backtrace}")
    end

    def installed_apps
      installed_apps_path = @device_state.installed_apps_file

      return {} unless File.exist?(installed_apps_path)

      begin
        JSON.parse(File.read(installed_apps_path))
      rescue JSON::ParserError
        {}
      end
    end

    def ppuid_file
      @ppuid_file ||= PpuidFile.new(@device)
    end

    # Can be removed once we have moved apps and devices from prod_60 to prod_61 provision branch
    def push_installed_apps
      if !@device_state.update_provisioning_profile_metrics_file_present? ||
        @device_state.update_provisioning_profile_metrics_file_older_than_days?(PROVISIONING_PROFILE_METRICS_PUSH_ZOMBIE_INTERVAL)
        provision_branch = ""
        provision_branch = ppuid_file.branch_name if @device_state.ppuid_file_present?
        BrowserStack::Zombie.push_logs(
          "provisioning-profile-metrics",
          "",
          {
            "device" => @device,
            "data" => {
              "apps": installed_apps,
              "provision_branch": provision_branch
            }
          }
        )
        @device_state.touch_update_provisioning_profile_metrics_file
      end
    end
  end
end
