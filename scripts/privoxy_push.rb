# this script has to run before privoxy starts
unless ARGV[0]
  puts "device_id is required"
  exit 1
end

unless ARGV[1]
  puts "session_id is required"
  exit 1
end

$stdout.sync = true

device_id = ARGV[0]
session_id = ARGV[1]

logfile = "/var/log/browserstack/privoxy_#{device_id}.log"

puts "[#{Time.now.utc}] ---- Tail Starting for device_id: #{device_id}, session_id: #{session_id} ----"

begin
  IO.popen("tail -0f #{logfile}") do |io|
    io.each do |line|
      new_line = "[#{Time.now.utc.strftime('%Y-%m-%d %H:%M:%S')}] [#{device_id}] [#{session_id}] #{line}"
      puts new_line
    end
  end
rescue Exception => e
  puts e.message
  puts e.backtrace
end