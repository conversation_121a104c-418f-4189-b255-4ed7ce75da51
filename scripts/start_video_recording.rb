require_relative '../server/device_manager'
require 'browserstack_logger'
require_relative '../lib/configuration'
require_relative '../lib/utils/http_utils'
require_relative '../lib/custom_exceptions'
require_relative '../lib/helpers/browserstack_app_helper'
require_relative '../lib/utils/ios_mdm_service_client'
require_relative '../lib/utils/idevice_utils'
require_relative '../lib/utils/alerter'
require_relative '../lib/ios_vid_capturer'
require_relative '../lib/utils/zombie'

# TODO : Add Checks for cmd args

device = ARGV[0]
session_id = ARGV[1]
orientation = ARGV[2]
genre = ARGV[3]
wda_port = ARGV[4]
rec_start_time = ARGV[5]

conf = BrowserStack::Configuration.new
server_config = conf.all
IdeviceUtils.configure(server_config)
BrowserStack::IosMdmServiceClient.configure
DeviceManager.configure server_config
BrowserStack::Alerter.configure server_config
log_file = "#{server_config['logging_root']}/video_recorder_#{device}.log"
log_params = { component: 'start_video_recording.rb' }
BrowserStack.init_logger(log_file, log_params)

BrowserStack::Zombie.configure

capturer = IosVidCapturer.new(device, session_id, server_config, orientation, genre, BrowserStack.logger)
device_version = DeviceManager.all_devices[device]['device_version'].to_f
capturer.record(wda_port, rec_start_time, device_version)
