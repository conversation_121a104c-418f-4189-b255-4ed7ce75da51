const fs = require('fs');
const net = require('net');
const path = require('path');
const { logWithTime, pushToPager } = require('./util');

const deviceConfigFile = path.join('/usr/local/.browserstack/realmobile/config/insecure_websocket_mapping.json');
let deviceToProxyMap = {};
updateDeviceConfig();

function WsConnection(clientToProxySocket) {
  this.clientToProxySocket = clientToProxySocket;

  // Set upstream proxy for socket
  this.deviceIp = clientToProxySocket.remoteAddress.split(':').slice(-1)[0];
  this.upstreamHost = "127.0.0.1";
  this.upstreamPort = deviceToProxyMap[this.deviceIp];
  this.log(`Selected upstream proxy port ${this.upstreamPort} for ${this.deviceIp}`);
}

WsConnection.prototype.log = function(message) {
  let logObject = {
    sourceDeviceIp: this.deviceIp,
    upstreamHost: this.upstreamHost,
    upstreamPort: this.upstreamPort,
    destinationAddress: this.serverAddress,
    destinationPort: this.serverPort,
    message: message
  }
  logWithTime(JSON.stringify(logObject));
}

WsConnection.prototype.clientSocketDataHandler = function(data) {
  this.requestPacket = data.toString();
  this.log(this.requestPacket);

  try {
    // Return 200 directly if request matches health check path
    if (this.requestPacket.includes(" /websocket_proxy_health")) {
      this.clientToProxySocket.write('HTTP/1.1 200 OK\r\n\r\n');
      this.clientToProxySocket.end();
      return;
    }

    // Parsing HOST from HTTP
    destination = parseHostPort(this.requestPacket);
    this.log(`Got request to ${destination.serverAddress}:${destination.serverPort}`);
    this.serverAddress = destination.serverAddress;
    this.serverPort = destination.serverPort;

    // Create connection with upstream proxy to forward request
    this.proxyToUpstreamSocket = net.createConnection (
      {
        host: this.upstreamHost,
        port: this.upstreamPort || 80
      },
      this.upstreamProxyConnectHandler.bind(this))
    .on('error', this.upstreamSocketErrorHandler.bind(this))
    .on('end', this.upstreamSocketEndHandler.bind(this))
    .on('close', this.upstreamSocketCloseHandler.bind(this));
  } catch(err) {
    this.log(`Unhandled exception while handling request ${err} ${err.stack}`);
    pushToPager("InsecureWSProxy-exception", `Unhandled exception while handling request`, `${err}`);
  }
}

WsConnection.prototype.clientSocketErrorHandler = function(err) {
  this.log(`client to proxy socket error: ${err}`);
  if (this.proxyToUpstreamSocket) {
    this.proxyToUpstreamSocket.destroy();
  }
}

WsConnection.prototype.clientSocketEndHandler = function() {
  if (this.proxyToUpstreamSocket) {
    this.proxyToUpstreamSocket.destroy();
  }
}

WsConnection.prototype.clientSocketCloseHandler = function(err) {
  this.log(`client to proxy socket closed. with_error: ${err}`);
  if (this.proxyToUpstreamSocket) {
    this.proxyToUpstreamSocket.destroy();
  }
}

WsConnection.prototype.upstreamSocketErrorHandler = function(err) {
  this.log(`proxy to server error: ${err}`);
  if (this.clientToProxySocket) this.clientToProxySocket.end();
}

WsConnection.prototype.upstreamSocketEndHandler = function() {
  if (this.clientToProxySocket) this.clientToProxySocket.end();
}

WsConnection.prototype.upstreamSocketCloseHandler = function(err) {
  this.log(`proxy to server closed. with_error: ${err}`);
  if (this.clientToProxySocket) this.clientToProxySocket.end();
}

WsConnection.prototype.upstreamProxyConnectHandler = function() {
  // Attach response listener to proxy socket
  this.proxyToUpstreamSocket.once ('data', this.upstreamProxyResponseHandler.bind(this));

  // Sending first CONNECT request packet to upstream proxy in order to establish tunnel between service and dest-server
  this.proxyToUpstreamSocket.write(Buffer.from(`CONNECT ${this.serverAddress}:${this.serverPort} HTTP/1.1\r\nHost: ${this.serverAddress}\r\nConnection: keep-alive\r\nProxy-Connection: keep-alive\r\n\r\n`));
}

WsConnection.prototype.upstreamProxyResponseHandler = function(res) {
  // Process CONNECT response from upstream porxy
  try {
    const resString = res.toString();
    if (resString && resString.includes("200 Connection established")) {
      // Send second packet with actual ws request and headers on CONNECT success
      // Making second request packet compatible as per RFC to send after CONNECT request
      let httpPacket = Buffer.from(this.requestPacket.replace(`ws://${this.serverAddress}:${this.serverPort}`, ""));
      httpPacket = Buffer.from(httpPacket.toString().replace(`ws://${this.serverAddress}`, ""));
      this.proxyToUpstreamSocket.write(httpPacket);

      // Pipe client socket to upstream tunnel
      this.clientToProxySocket.pipe(this.proxyToUpstreamSocket);
      this.proxyToUpstreamSocket.pipe(this.clientToProxySocket);
    } else {
      this.log(`Connect request returned non-200 response ${resString.substr(0, 40)}`);
    }
  } catch(err) {
    this.log(`Unhandled exception while establishing tunnel ${err} ${err.stack}`);
    pushToPager("InsecureWSProxy-exception", `Unhandled exception while establishing tunnel`, `${err}`);
  }
}

const parseHostPort = (request) => {
  let serverPort, serverAddress;

  serverAddress = request.toLowerCase()
    .split('host: ')[1];
  serverAddress = serverAddress    
    .split('\r\n')[0];

  const serverAddressSplit = serverAddress.split(':');

  serverAddress = serverAddressSplit[0];
  serverPort = parseInt(serverAddressSplit[1] || '80');

  return {
    serverAddress,
    serverPort
  };
}

function updateDeviceConfig() {
  try {
    const data = fs.readFileSync(deviceConfigFile);

    deviceToProxyMap = JSON.parse(data.toString());
  } catch(err) {
    logWithTime(`Error while reading device config ${err} ${err.stack}`);
    pushToPager("InsecureWSProxy-config-error", "Exception while parsing device config file", `${err}`);
  }
}


// Update device to proxy map when config file gets updated
const configWatcher = fs.watchFile(deviceConfigFile, function () {
  updateDeviceConfig();
});

process.on('disconnect', function() {
  fs.unwatchFile(deviceConfigFile, configWatcher);
});

module.exports = {
  WsConnection
};
