
const cluster = require('cluster');
const fs = require('fs');
const path = require('path');
const { logWithTime, pushToPager } = require('./util');

const codeDir = path.join(__dirname, '/');
const workerFilePath = path.join(codeDir, 'proxy_service.js');
const timeouts = {};

const forceKill = (worker) => {
  if (worker && !worker.isDead()) {
    logWithTime(`Worker ${worker.process.pid} is ${worker.state}, Force Killing it`);
    worker.kill('SIGUSR2');
  }
};

const checkIfAlive = (newWorker, oldWorkers, retries = 0) => {
  if (!newWorker.isDead()) {
    if (newWorker.state !== "listening" && retries < 20) {
      retries++;
      setTimeout(checkIfAlive, 500, newWorker, oldWorkers, retries);
    } else {
      disconnectOldWorkers(oldWorkers);
    }
  } else {
    logWithTime(`New worker with pid ${newWorker.process.pid} died before listening`);
  }
}

const disconnectOldWorkers = (oldWorkers) => {
  Object.keys(oldWorkers).forEach(key => {
    let oldWorker = oldWorkers[key];
    oldWorker.disconnect();
    logWithTime(`Worker ${oldWorker.process.pid} disconnected`);
    timeouts[oldWorker.process.pid] = setTimeout(forceKill, 60 * 60 * 1000, oldWorker);
  });
}

const reload = () => {
  const oldWorkers = { ...cluster.workers };
  const newWorker = cluster.fork();
  logWithTime(`Worker ${newWorker.process.pid} staterd`);
  setTimeout(checkIfAlive, 500, newWorker, oldWorkers);
};

if (cluster.isMaster) {
  var restart_txt_directory = path.join(__dirname, 'tmp');
  var restart_txt_path = path.join(restart_txt_directory, 'restart.txt');

  reload();

  cluster.on('exit', (worker, code, signal) => {
    let errorMessage = `Worker ${worker.process.pid} died with code: ${code} signal: ${signal} and is disconnected ${worker.exitedAfterDisconnect}`;
    logWithTime(errorMessage);
    if (worker.exitedAfterDisconnect || signal === 'SIGUSR2' || code == 2) {
      clearTimeout(timeouts[worker.process.pid]);
      delete timeouts[worker.process.pid];
    } else {
      reload();
      logWithTime(`Worker ${worker.process.pid} died unexpectedly`);
      sendUnexpectedRestartEvent(errorMessage);
    }
  });

  process.on('SIGINT', function () {
    logWithTime("Caught interrupt signal");
    Object.keys(cluster.workers).forEach(key => {
      cluster.workers[key].kill('SIGINT');
    })
    process.exit();
  });

  process.on('unhandledRejection', (reason, p) => {
    logWithTime(`Got unhandled rejection ${reason}`);
    logWithTime(p);
    pushToPager("InsecureWSProxy-master-exception", `Got unhandled rejection from master`, `${reason}`);
  });

  process.on('uncaughtException', (error) => {
    logWithTime(`Got uncaught exception ${error} ${error.stack}`);
    pushToPager("InsecureWSProxy-master-exception", "Got uncaught exception from master", error.message);
  });

  if (!fs.existsSync(restart_txt_directory)) {
    fs.mkdirSync(restart_txt_directory);
  }

  fs.closeSync(fs.openSync(restart_txt_path, 'w'));

  fs.watchFile(restart_txt_path, function () {
    logWithTime("Reloading Cluster");
    reload();
  });

} else {
  const worker = require(workerFilePath);
}

function sendUnexpectedRestartEvent(errorMessage) {
  logWithTime("[CRITICAL] Unable to restart the process. This is critical please check.");
  logWithTime(errorMessage);
  var kind = 'InsecureWSProxy-Crash';
  pushToPager(kind, "Insecure websocket proxy crash", errorMessage);
}
