const net = require('net');
const { WsConnection } = require('./ws_connection');
const { logWithTime, pushToPager } = require('./util');
const port = 45650;

const server = net.createServer();

const handleWSConnection = (clientToProxySocket) => {
  const wsConnection = new WsConnection(clientToProxySocket);
  wsConnection.clientToProxySocket.once('data', wsConnection.clientSocketDataHandler.bind(wsConnection));

  wsConnection.clientToProxySocket.on('error', wsConnection.clientSocketErrorHandler.bind(wsConnection));
  wsConnection.clientToProxySocket.on('end', wsConnection.clientSocketEndHandler.bind(wsConnection));
  wsConnection.clientToProxySocket.on('close', wsConnection.clientSocketCloseHandler.bind(wsConnection));
};

process.on('unhandledRejection', (reason, p) => {
  logWithTime(`Got unhadled rejection ${reason}`);
  logWithTime(p);
  pushToPager("InsecureWSProxy-exception", `Got unhadled rejection`, `${reason}`);
});

process.on('uncaughtException', (error) => {
  logWithTime(`Got uncaught exception ${error} ${error.stack}`);
  pushToPager("InsecureWSProxy-exception", "Got uncaught exception from worker", error.message);
});

server.on ('connection', handleWSConnection);

server.listen(port, () => {
  logWithTime(`server is listening on ${port}`);
});
