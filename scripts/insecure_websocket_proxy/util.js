const dgram = require('dgram');
const udpClient = dgram.createSocket("udp4");
const os = require('os');

// TODO: Use dwh_node package. Task: https://browserstack.atlassian.net/browse/MOBIN-1149
const pagerProdHost = "pager.browserstack.com";
const pagerProdPort = 8553;

logWithTime = function(data) {
  var currentDate = '[' + new Date().toUTCString() + '] ';
  console.log(currentDate, data);
};

pushToPager = function(kind, error, data="") {
  var pagerPayload = JSON.stringify({
    kind,
    error,
    data: data.substring(0, 40),
    device: os.hostname(),
    team: "local"
  });
  var _packet = Buffer.from(pagerPayload);
  udpClient.send(_packet, 0, _packet.length, pagerProdPort, pagerProdHost);
}

module.exports = {
  logWithTime,
  pushToPager
}
