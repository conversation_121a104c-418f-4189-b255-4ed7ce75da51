#!/bin/bash
set -e

export WDA_BSTACK_UTILITIES_BRANCH=$1
echo "WDA_BSTACK_UTILITIES_BRANCH: $WDA_BSTACK_UTILITIES_BRANCH"

if [[ -n "$WDA_BSTACK_UTILITIES_BRANCH" ]]; then
  export WDA_BSTACK_UTILITIES_LATEST_COMMIT_ID=$(git ls-remote **************:browserstack/wda-bstack-utilities.git "$WDA_BSTACK_UTILITIES_BRANCH" | awk '{print $1}')
  echo "WDA_BSTACK_UTILITIES_LATEST_COMMIT_ID: $WDA_BSTACK_UTILITIES_LATEST_COMMIT_ID"
fi

# Navigate to 'wda' directory
mkdir -p /usr/local/.browserstack/wda
cd /usr/local/.browserstack/wda

REPO_URL="**************:browserstack/wda-forge.git"
FOLDER="/usr/local/.browserstack/wda/wda-forge"

if [ -d "$FOLDER" ] && [ -d "$FOLDER/.git" ]; then
  echo "Folder '$FOLDER' is a git repository..."
else
  echo "Folder '$FOLDER' is not a git repository or does not exist. Removing and cloning..."
  rm -rf "$FOLDER"
  git clone --depth 1 "$REPO_URL" "$FOLDER"
fi

cd "$FOLDER" || { echo "Failed to enter '$FOLDER' directory"; exit 1; }

# Get Xcode version
XCODE_VERSION=$(xcodebuild -version | awk 'NR==1 {print $2}' | cut -d'.' -f1)
IOS_GENERATION=$((XCODE_VERSION - 9))
echo "XCODE_VERSION: $XCODE_VERSION"
echo "IOS_GENERATION: $IOS_GENERATION"

bro packages install --verbose packages-ios-gen-"$IOS_GENERATION".nix

# Run the Ruby script
cd /usr/local/.browserstack/realmobile || { echo "Failed to enter 'realmobile' directory"; exit 1; }
bundle exec ruby scripts/wda_download.rb
