# LTE -> Local Testing Extension
# Note: This script will work only for mitmproxy versions >= 7

# This script is being used to intercept all HTTP(S) requests from remote device
# These intercepted requests are eventually sent to local testing chrome extension to make fetch API calls and return responses
# HTTPS requests are converted to HTTP request and sent to privoxy -> repeater -> chrome extension
# This conversion is done since we need to send the request data in plaintext format to chrome extension

# For mitmproxy verisons < 7, upstream_cert = false flag must be used to prevent TLS handshake between proxy -> server
# This flag stops working when we define an upstream proxy (In our case, we define privoxy as the upstream proxy)
# Because of this reason, this script will fail to work on mimtproxy verisons <= 7

# For mitmproxy verisons >= 7, connection_strategy = lazy flag must be used to prevent TLS handshake between proxy -> server
# For this reason, this script will work only for mitmproxy versions >= 7

# Command for testing purpose:
# mitmdump -s local_testing_extension_script.py --set connection_strategy=lazy --set upstream_cert=false --ssl-insecure

import os
import re
import urllib.parse
import urllib.request
from urllib.error import HTTPError
import gzip
import json
from io import BytesIO
from mitmproxy import http
from mitmproxy import ctx
import base64
import sys
import signal
import time
from mitmproxy.script import concurrent

try:
    from mitmproxy.http import HTTPResponse as MITMResponse
    ctx.log.info("mitmproxy.http HTTPResponse is available")
except ImportError:
    from mitmproxy.http import Response as MITMResponse
    ctx.log.info("mitmproxy.http HTTPResponse is not available. Using Response instead")

log_prefix = "bstack_lte: "

def calculate_percentile(data, percentile):
    """
    Calculate the percentile value from a list of data.

    Args:
        data (list): List of numerical data.
        percentile (float): Percentile value to calculate (between 0 and 100).

    Returns:
        float: Percentile value.
    """
    data.sort()
    index = (percentile / 100) * (len(data) - 1)
    if index.is_integer():
        return data[int(index)]
    else:
        floor_index = int(index // 1)
        ceil_index = floor_index + 1
        interpolation = index % 1
        return (1 - interpolation) * data[floor_index] + interpolation * data[ceil_index]

def save_stats():
    if len(addons[0].request_times) > 0:
        data = addons[0].request_times
        p1 = calculate_percentile(data, 1)
        p25 = calculate_percentile(data, 25)
        p50 = calculate_percentile(data, 50)  # Median
        p75 = calculate_percentile(data, 75)
        p99 = calculate_percentile(data, 99)

        p1 = round(p1, 2)
        p25 = round(p25, 2)
        p50 = round(p50, 2)
        p75 = round(p75, 2)
        p99 = round(p99, 2)

        stats = {
            "P1": p1,
            "P25": p25,
            "P50": p50,
            "P75": p75,
            "P99": p99,
            "total_requests": len(addons[0].request_times),
            "total_failed_requests": addons[0].failed_requests_count
        }

        with open(ctx.options.request_stats_output_file, "w") as f:
            json.dump(stats, f)
            f.write('\n')
        ctx.log.info(f"{log_prefix} Saved stats: {stats} to file: {ctx.options.request_stats_output_file}")

class LTEHelper:

    def __init__(self):
        self.request_times = []
        self.failed_requests_count = 0
        signal.signal(signal.SIGTERM, self.signal_handler)

    def load(self, loader):
        loader.add_option(
            "privoxy_port", str, "", "My Script Tag"
        )
        loader.add_option(
            "request_stats_output_file", str, "", "File path to log the time taken (in form of percentiles) by all the requests"
        )

    def make_request_log_line(self, flow):
        return f"---->{flow.request.method} : {flow.request.scheme}://{flow.request.host} : {flow.request.path}]<-----"

    @concurrent
    def request(self, flow: http.HTTPFlow) -> None:
        try:
            flow.request.timestamp_start = time.time()
            request_log_line = self.make_request_log_line(flow)
            # blacklisted_domains = ["google"] and not any(dom in flow.request.host for dom in blacklisted_domains)
            if flow.request.scheme == "https":
                ctx.log.info(f"{log_prefix} Started handling request: {request_log_line}")
                modified_url = flow.request.url.replace("https", "http")
                modified_headers = flow.request.headers
                modified_headers['lte-request-scheme'] = flow.request.scheme

                proxy_host = "http://localhost:" + ctx.options.privoxy_port
                proxy_support = urllib.request.ProxyHandler({'http': proxy_host})
                opener = urllib.request.build_opener(proxy_support)
                urllib.request.install_opener(opener)

                if flow.request.content:
                    post_data = flow.request.content
                    request = urllib.request.Request(modified_url, data=post_data, method=flow.request.method, headers=modified_headers)
                else:
                    request = urllib.request.Request(modified_url, method=flow.request.method, headers=modified_headers)

                try:
                    response = urllib.request.urlopen(request, timeout=40) #nosec
                    content = response.read()
                    ctx.log.info(f"{log_prefix} Final Response: {content[:20]}, response status code: {response.status} for request: {request_log_line}")
                    response_headers = {}
                    for key, value in response.headers.items():
                        response_headers[key] = value

                    flow.response = MITMResponse.make(
                        status_code=response.status,
                        content=content,
                        headers=response_headers
                    )
                    ctx.log.info(f"{log_prefix} Ended handling request, response status code: {response.status} for request: {request_log_line}")
                except HTTPError as e:
                    content = e.read()
                    response_headers = {}
                    for key, value in e.headers.items():
                        response_headers[key] = value
                    
                    ctx.log.info(f"{log_prefix} HTTPError :: Final Response: {content[:20]}, response status code: {e.code} for request: {request_log_line}")
                    flow.response = MITMResponse.make(
                        status_code=e.code,
                        content=content,
                        headers=response_headers
                    )
                except Exception as e:
                    error_status_code = None
                    error_content = ''

                    # Check if the exception has status code and content attributes
                    if hasattr(e, 'code'):
                        error_status_code = e.code
                    if hasattr(e, 'read'):
                        try:
                            error_content = e.read()
                        except:
                            error_content = ''

                    if error_status_code is None:
                        ctx.log.info(f"{log_prefix} Last Exception Block :: EXCEPTION while calling urlopen, exception: {str(e)} for request: {request_log_line}")
                        self.failed_requests_count += 1
                        flow.kill()
                    else:
                        ctx.log.info(f"{log_prefix} Last Exception Block :: Final Response: {error_content[:20]}, response status code: {error_status_code} for request: {request_log_line}")
                        response_headers = {}
                        for key, value in e.headers.items():
                            response_headers[key] = value
                        flow.response = MITMResponse.make(
                            status_code=error_status_code,
                            content=error_content,
                            headers=response_headers
                        )
        except Exception as e:
            request_log_line = self.make_request_log_line(flow)
            ctx.log.info(f"{log_prefix} EXCEPTION in request() function :: {str(e)} for request: {request_log_line}")
            self.failed_requests_count += 1
            flow.kill()

    @concurrent
    def response(self, flow: http.HTTPFlow) -> None:
        if flow.request.timestamp_start:
            elapsed_time = round(time.time() - flow.request.timestamp_start, 2)
            self.request_times.append(elapsed_time)

        # To decode base64 encoded responses coming from local testing extension
        # This handles cases for both HTTP and HTTPS requests
        request_log_line = self.make_request_log_line(flow)
        if flow.response.headers.get("bstack-base64-encoded") == "true":
            ctx.log.info(f"{log_prefix} Decoding base64 for request: {request_log_line}")
            flow.response.content = base64.b64decode(flow.response.content)

    def signal_handler(self, sig, frame):
        ctx.log.info(f"{log_prefix} signal_handler called with sig: {sig}")
        if sig == signal.SIGTERM:
            # Perform cleanup actions here
            ctx.log.info(f"{log_prefix} Exiting...calling save_stats")
            save_stats()
            sys.exit(0)

addons = [LTEHelper()]
