source 'https://rubygems.org'

# These env flags are required to gem install ffi (1.11.1)
# https://github.com/ffi/ffi/issues/651#issuecomment-434973801
ENV['LDFLAGS'] = '-L/usr/local/opt/libffi/lib'
ENV['PKG_CONFIG_PATH'] = '/usr/local/opt/libffi/lib/pkgconfig'
gem 'sinatra', '2.2.4'
gem 'sinatra-config-file'
gem 'rack-test'
gem 'sys-filesystem'
gem 'appium_lib', '11.0.0'
gem 'xcodeproj'
gem 'fastlane'
gem 'redis'
gem 'faraday'
gem 'net-ssh', '4.2.0'
gem 'net-scp'
gem 'sequel'
gem 'sqlite3', '~>1.5.0'
gem 'concurrent-ruby'
gem 'json', '~>2.5.1'
gem 'google-cloud-storage'
gem 'jmespath', '~> 1.6.1'
gem 'nokogiri', '~> 1.15.0'
gem 'websocket-client-simple', '0.3.0'

gem 'puma'
gem 'plist'
gem 'dotenv'
gem 'idevice', :path => 'dependencies/idevice'

git '**************:browserstack/mobile-common.git', branch: 'master' do
  gem 'aws_s3_wrapper'
  gem 'browserstack_logger'
  gem 'cleanup_status_db'
  gem 'download_bundle_id_config'
  gem 'device_fork_executor'
  gem 'mobile_influxdb_client'
  gem 'network_helper'
  gem 'server_info'
  gem 'so_timeout_util'
  gem 'static_conf'
  gem 'mitmproxybuilder'
  gem 'bsenv'
  gem 'app_percy_common'
  gem 'sirenlogs'
  gem 'ios_toolkit'
  gem 'percy_on_automate_common'
  gem 'interaction_sync_stability_tester'
  gem 'reboot'
  gem 'browserstack_utils'
  gem 'env_middleware'
end

gem 'rmagick', '~> 5.5.0'
gem 'hoothoot', :git => 'ssh://**************/browserstack/ruby-hoothoot.git', :branch => 'master'
gem 'bsdwh', '0.1.48', :git => 'ssh://**************/browserstack/dwh_ruby.git', :branch => 'master'

group :development, :test do
  gem 'rspec', '~>3.7'
  gem 'webmock', '~>3.4'
  gem 'timecop'
  gem 'pry'
  gem 'pry-remote'
  gem 'pry-nav'
  gem 'simplecov', require: false
  gem 'rubocop', require: false
end

gem "irb", "~> 1.3", require: false
gem 'vpim', '~> 13.11', '>= 13.11.11'
