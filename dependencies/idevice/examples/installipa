#!/usr/bin/env ruby
#
# Copyright (c) 2013 <PERSON> - Bluebox Security
#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

require 'bundler/setup'
require 'idevice'

ipa_path = ARGV.shift
if ipa_path.nil? or ARGV.shift
  $stderr.puts "Usage: #{File.basename} /path/to/an.ipa"
  exit(1)
end

ipa = Pathname(ipa_path)

idev = Idevice::Idevice.attach

remote_path = Pathname("PublicStaging").join(ipa.basename)

afc = Idevice::AFCClient.attach(idevice:idev)
$stderr.puts "[.] Uploading #{ipa} to #{remote_path}"
afc.mkdir(remote_path.dirname.to_s) rescue nil
afc.put_path(ipa.to_s, remote_path.to_s)

instpxy = Idevice::InstProxyClient.attach(idevice:idev)
$stderr.puts "[.] Requesting installation of #{remote_path}"
finished = false
instpxy.install(remote_path.to_s) do |name, status|
  p [name, status]
  finished = (status["Error"] or status["Status"] == "Complete")
end

while not finished
  #nop
end
