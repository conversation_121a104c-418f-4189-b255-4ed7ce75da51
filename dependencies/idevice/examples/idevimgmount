#!/usr/bin/env ruby
#
# Copyright (c) 2013 <PERSON> - Bluebox Security
#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.


require 'bundler/setup'
require 'idevice'
require 'pathname'

progname = File.basename($0)

dmgpath = ARGV.shift
sigpath = ARGV.shift

if dmgpath.nil? or ARGV.shift
  $stderr.puts "usage: #{progname} /path/to/DeveloperDiskImage.dmg [/path/to/DeveloperDiskImage.dmg.signature]"
  exit(1)
end

dmg = Pathname(dmgpath)
signature = Pathname(sigpath || "#{dmgpath}.signature")

idev = Idevice::Idevice.attach()

imgmnt = Idevice::ImageMounterClient.attach(idevice:idev, label:progname)
begin
  # upload the dmg
  afc = Idevice::AFCClient.attach(idevice:idev, label:progname)
  dstdmg = "PublicStaging/staging.dimage"
  $stderr.puts "[+] Uploading #{dmg} to #{dstdmg}"
  afc.put_path(dmg.to_s, dstdmg)

  # mount the image as a Developer image using the provided signature
  $stderr.puts "[+] Mounting Developer image"
  imgmnt.mount_image dstdmg.to_s, signature.read(), "Developer"

  exit(0)
ensure
  $stderr.puts "[.] hanging up"
  imgmnt.hangup rescue nil
end
