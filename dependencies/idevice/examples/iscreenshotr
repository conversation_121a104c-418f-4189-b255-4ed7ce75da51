#!/usr/bin/env ruby
#
# Copyright (c) 2013 <PERSON> - Bluebox Security
#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.


require 'bundler/setup'
require 'idevice'

outname = ARGV.shift || "iscreenshot.tiff"

begin
  sshotr = Idevice::ScreenShotrClient.attach()
rescue Idevice::LockdownError
  $stderr.puts "[-] Error: unable to connect to screenshotr",
               "    Hint: the DeveloperTools dmg must be mounted on device"
  exit(1)
end

image = sshotr.take_screenshot
ret = File.open(outname, "w") {|f| f.write image }
puts "[+] wrote #{ret} bytes to #{outname}"
exit(0)
