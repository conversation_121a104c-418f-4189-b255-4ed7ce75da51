#!/usr/bin/env ruby
#
# Copyright (c) 2013 <PERSON> - Bluebox Security
#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.


require 'bundler/setup'
require 'idevice'
require 'pp'

dumpdomains = %W{
  com.apple.disk_usage
  com.apple.disk_usage.factory
  com.apple.mobile.battery
  com.apple.iqagent
  com.apple.purplebuddy
  com.apple.PurpleBuddy
  com.apple.mobile.chaperone
  com.apple.mobile.third_party_termination
  com.apple.mobile.lockdownd
  com.apple.mobile.lockdown_cache
  com.apple.xcode.developerdomain
  com.apple.international
  com.apple.mobile.data_sync
  com.apple.mobile.tethered_sync
  com.apple.mobile.mobile_application_usage
  com.apple.mobile.backup
  com.apple.mobile.nikita
  com.apple.mobile.restriction
  com.apple.mobile.user_preferences
  com.apple.mobile.sync_data_class
  com.apple.mobile.software_behavior
  com.apple.mobile.iTunes.SQLMusicLibraryPostProcessCommands
  com.apple.mobile.iTunes.accessories
  com.apple.mobile.internal
  com.apple.mobile.wireless_lockdown
  com.apple.fairplay
  com.apple.iTunes
  com.apple.mobile.iTunes.store
  com.apple.mobile.iTunes
}

ldc = Idevice::LockdownClient.attach
dumpdomains.each do |domain|
  puts "[+] Dumping domain: #{domain}"
  pp ldc.get_value(domain, nil)
  puts
end
