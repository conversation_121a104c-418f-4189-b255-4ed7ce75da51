#!/usr/bin/env ruby
#
# Copyright (c) 2013 <PERSON> - Bluebox Security
#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.


require 'open3'
require 'bundler/setup'
require 'idevice'

outfile = ARGV.shift
if outfile.nil? or ARGV.shift
  $stderr.puts "Usage: #{File.basename $0} path/to/output.cpio" 
  exit(1)
end

frc = Idevice::FileRelayClient.attach
$stderr.puts "[.] Requesting crash reports"
len = 0
File.open(outfile, 'w') do |outf|
  Open3.popen3("cpio -t") do |w,r,e|
    begin
      frc.request_sources("CrashReporter") do |chunk|
        w.write(chunk)
        outf.write(chunk)
        len += chunk.size
      end
    ensure
      w.close
      puts "[+] Wrote #{len} bytes to #{outfile}",
           "Contents:",
           r.read

      r.close
    end
  end
end

