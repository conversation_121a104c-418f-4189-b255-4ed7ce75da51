#!/usr/bin/env ruby
#
# Copyright (c) 2013 <PERSON> - Bluebox Security
#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.


require 'bundler/setup'
require 'idevice'
require 'json'

idev = Idevice::Idevice.attach
ldcli = Idevice::LockdownClient.attach(idevice:idev)
diag_ldsvc = ldcli.start_service("com.apple.iosdiagnostics.relay")
diag_relay = idev.connect(diag_ldsvc[:port])

diag_relay.send_lockdown_message(Request: "All")
puts JSON.pretty_generate(diag_relay.receive_lockdown_message())
diag_relay.send_lockdown_message(Request: "Goodbye")
diag_relay.disconnect
