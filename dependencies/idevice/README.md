# Idevice

Ruby FFI bindings for libimobiledevice

The ruby Idevice library was written primarily as a research tool for
prototyping iOS tools that use USB as well as a tool to aid in 
reverse-engineering new areas of the iOS USB protocols.

## Versions

The ruby idevice package version number corresponds to the libimobiledevice
version it is built for. Where necessary, an 4th version digit will be
appended for rubygem-specific fixes and features within a given
libimobiledevice version cycle.

## Requirements

- libplist - https://github.com/libimobiledevice/libplist
- libimobiledevice - https://github.com/libimobiledevice/libimobiledevice

## Installation

Add this line to your application's Gemfile:

    gem 'idevice'

And then execute:

    $ bundle

Or install it yourself as:

    $ gem install idevice

## Usage

See 

- https://github.com/emonti/idevice/tree/master/examples
- https://github.com/emonti/idevice/tree/master/spec

## Contact

Eric Monti - <EMAIL> (Author)

This code is released under the ASF license by Bluebox Security
- https://www.bluebox.com

## Issues / Contributing

Please submit pull requests using github.
Report issues at https://github.com/emonti/idevice/issues


## License


    Copyright (c) 2013 <PERSON> - <PERSON> Security

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.


Note: The above license applies strictly to the ruby bindings, and does
not apply to libimobiledevice nor libplist or any other of their dependent
libraries as defined by their respective owners. See below.

### libimobiledevice and libplist Copyright Notices

Notice: libplist and libimobiledevice are both licensed under LGPL v2.1

* libimobiledevice: Copyright (c) 2008 Zach C. All Rights Reserved.

* libplist: Copyright (c) 2009 Jonathan Beck All Rights Reserved.

See LICENSE.lgpl.txt for more information.
