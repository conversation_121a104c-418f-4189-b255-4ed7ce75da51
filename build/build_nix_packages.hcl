# Variables will be substituted:
#
#   [[RELEASE_TAG]] - a release tag chosen when the deploy submitted

# Download specific mobile repo in NOMAD_ALLOC_DIR directory

job "ios_build_nix_packages" {
  type = "batch"

  meta {
    release = "[[RELEASE_TAG]]"
  }

  constraint {
    attribute = "${meta.mobile-platform}"
    value     = "ios"
  }

  constraint {
    attribute = "${meta.package-builder}"
    value     = "true"
  }

  spread {
    attribute = "${meta.ios-generation-architecture}"
  }

  # don't use inlined variables after this line
  group "nix_build" {

    count = 9

    vault {
      policies = ["kv-all"]
    }

    # Saves the code update script to the machine
    task "build_and_upload" {
      driver = "raw_exec"

      env {
        GITHUB_KEY           = "${NOMAD_SECRETS_DIR}/github"
        GIT_SSH_COMMAND      = "$(nix-build --no-out-link '<nixpkgs>' -I nixpkgs=https://github.com/NixOS/nixpkgs/archive/nixos-24.05.tar.gz -A openssh)/bin/ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no -i $GITHUB_KEY -F /dev/null"
        HOME                 = "/Users/<USER>"
        BUILDER_USER_NAME    = "app"
        RELEASE              = "${NOMAD_META_release}"
        NIX_CONF_DIR         = "${NOMAD_TASK_DIR}"
        ios_platform_version = "${meta.ios-platform-version}"
        BUILD_REPO           = "realmobile"
      }

      template {
        data        = <<-EOH
        extra-substituters = http://caching-proxy-server.service.prod.mobile.browserstack.com:3128/package-storage
        require-sigs = false
        extra-platforms = x86_64-darwin aarch64-darwin
        EOH
        destination = "local/nix.conf"
      }

      template {
        data        = <<-EOH
        {{ with secret "/kv/s3/bs-mobile" }}
        AWS_ACCESS_KEY_ID={{ .Data.data.access_key_id }}
        AWS_SECRET_ACCESS_KEY={{ .Data.data.secret_access_key }}
        {{ end }}
        EOH
        destination = "secrets/aws.env"
        env         = true
      }

      template {
        data        = <<EOH
{{ with secret "/kv/github/ssh-key" }}{{ .Data.data.value }}{{ end }}
        EOH
        destination = "secrets/github"
        perms       = "600"
      }

      template {
        data        = <<-EOH
        {{ with secret "/kv/github/token" }}
        GITHUB_TOKEN={{ .Data.data.value }}
        {{ end }}
        EOH
        destination = "secrets/github.env"
        env         = true
      }

      template {
        data = <<-EOH
        #!/bin/bash
        set -xeo pipefail

        ########################
        ##  Setup environment ##
        ########################

        export PATH="$PATH:/usr/local/bin:/opt/homebrew/bin/"

        # Add nix to environment
        . ${HOME}/.nix-profile/etc/profile.d/nix.sh

        # Add rvm to environment
        [[ -s "/Users/<USER>/.rvm/scripts/rvm" ]] && source "/Users/<USER>/.rvm/scripts/rvm"

        ########################
        ###  Build packages  ###
        ########################

        # Clone and checkout realmobile repo
        cd ${NOMAD_TASK_DIR}
        sudo chown ${BUILDER_USER_NAME} $GITHUB_KEY
        rm -rf ${BUILD_REPO}
        git clone -b $RELEASE --single-branch --depth 1 ssh://**************/browserstack/${BUILD_REPO}.git
        cd ${BUILD_REPO}

        # Build and upload to S3 packages storage
        bro packages upload packages-${ios_platform_version}.nix --verbose 2>&1 | tee -a /var/log/browserstack/nix_packages_builder.log

        ####################
        ####  Clean up  ####
        ####################

        rm $GITHUB_KEY
        EOH

        destination = "local/build_script.sh"
      }

      template {
        data        = "/usr/bin/sudo -E -u ${BUILDER_USER_NAME} /bin/bash local/build_script.sh"
        destination = "local/run_build_as_user.sh"
      }

      config {
        command = "/bin/sh"
        args    = ["local/run_build_as_user.sh"]
      }
    }

  }
}
