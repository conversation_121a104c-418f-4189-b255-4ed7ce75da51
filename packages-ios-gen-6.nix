let
  nixpkgs_repo = import (builtins.fetchGit {
    url = "**************:browserstack/bs-nixpkgs.git";
    rev = "412c2c81e9bf6f10cee1a5417e3d746885101ab6";
  });
  pkgs = nixpkgs_repo { };
  rosetta_pkgs = nixpkgs_repo { system = "x86_64-darwin"; };

  packages =
    pkgs.callPackage ./packages/dotenv/ios-gen-6.nix { inherit rosetta_pkgs; }
    ++ pkgs.callPackage ./packages/deps/ios-gen-6.nix { }
    ++ pkgs.callPackage ./packages/percy/percy-setup.nix { }
    ++ pkgs.callPackage ./packages/developer-tools { }
    ++ pkgs.callPackage ./packages/ios-upgrades { }
    ++ rosetta_pkgs.callPackage ./packages/opencv-element-coordinates { }
    ++ rosetta_pkgs.callPackage ./packages/usb-reset { }
    ++ rosetta_pkgs.callPackage ./packages/appium/ios-gen-6.nix { native_pkgs = pkgs; }
    ++ pkgs.callPackage ./packages/packages-all-generations.nix { };

in pkgs.bs-tools.broInstallerManifest (pkgs.lib.flatten packages)
