require_relative "../lib/utils/osutils"
require_relative 'helpers/mock_logger'
require_relative 'spec_helper'

# Actual test
describe 'OSUtils.execute' do
  before(:each) do
    logger = MockLogger.new
    allow(BrowserStack).to receive(:logger).and_return(MockLogger.new)
  end

  it "logs the command" do
    BrowserStack::OSUtils.execute 'echo foo bar'
    expect(BrowserStack.logger.cached_info_logs.join(' ')).to include('echo foo bar')
  end
  it "logs stdout" do
    BrowserStack::OSUtils.execute 'echo foobar | wc -c'
    expect(BrowserStack.logger.cached_info_logs.join(' ')).to include('7')
  end
  it "logs the exit status" do
    BrowserStack::OSUtils.execute 'exit 27'
    expect(BrowserStack.logger.cached_info_logs.join(' ')).to include('27')
  end
  it "returns stdout of the command" do
    out = BrowserStack::OSUtils.execute 'echo hello'
    expect(out).to be_eql "hello\n"
  end
  it "can return the Process::Status of the execution" do
    msg, status = BrowserStack::OSUtils.execute('echo msg; exit 24', true)
    expect(msg).to be_eql "msg\n"
    expect(status).to be_a_kind_of Integer
    expect(status).to be_equal(24)
  end
  it "trims the logged output of the command if it's huge" do
    huge_text = 'foo_bar_' * 10000
    File.write '/tmp/huge_text', huge_text
    msg = BrowserStack::OSUtils.execute('cat /tmp/huge_text')
    expect(msg).to be_eql(huge_text) # Doesn't trim the returned output
    logs = BrowserStack.logger.cached_info_logs.join ' '
    expect(logs).to include('foo_bar_') # Trims the output of the logged output
    expect(logs.length).to be < 2000
  end

  describe '#execute_retry' do
    subject { BrowserStack::OSUtils }
    let(:execute_ref) { BrowserStack::OSUtils.method(:execute) }
    it 'Retries Execute after fail and passes' do
      expect(subject).to receive(:execute).and_return(["", "1"]).exactly(1)
      expect(subject).to receive(:execute).and_return(["", "0"]).exactly(1)
      expect do
        subject.execute_retry(execute_ref, 'echo "Hi"')
      end.not_to raise_error
    end
    it 'Retries Execute and fails' do
      expect(subject).to receive(:execute).and_return(["", "1"]).exactly(2)
      expect do
        subject.execute_retry(execute_ref, 'echo "Hi"')
      end.to raise_error
    end
    it 'Retries Execute after custom number of times and passes' do
      expect(subject).to receive(:execute).and_return(["", "1"]).exactly(3)
      expect(subject).to receive(:execute).and_return(["", "0"]).exactly(1)
      expect do
        subject.execute_retry(execute_ref, 'echo "Hi"', retries: 4)
      end.not_to raise_error
    end
  end
end
