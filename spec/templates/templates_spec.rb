require 'digest'
require_relative '../../config/constants'

EXPECTED_TEMPLATE_MD5 = [
  ["earlgrey_xctestrun.erb", "cd9acbf98178560be30d8a8865907776"],
  ["fluttertest_xctestrun.erb", "f95b8e2baa14cd68f332e4c0648c4dbe"],
  ["fluttertest_xctestrun_with_test_host_path.erb", "ce0bdbd45a5f8e7157400e8a149662fe"],
  ["generic_pac_profile_plist.erb", "e417ec77a94646d00bd3ce00c360fffe"],
  ["generic_pacfile.erb", "628aeb54d7b258d7227d98b5fa192a64"],
  ["generic_pacfile_ios17.erb", "06ab312f9c8504368f236b7667a77550"],
  ["generic_paction.erb", "90f707d3fd2d7d316c8e463d2c2ec96f"],
  ["generic_plist.erb", "b3a6454dacc79ac970a6c0e0ade4a0fe"],
  ["generic_privoxy_conf.erb", "9e9625aad316171854c4bfa6910b5434"],
  ["mitmproxy.erb", "a081a40f30ec0471db9e07b564e6b473"],
  ["notifications.erb", "3f2447d551ae84cb0d5170fe1fccb640"],
  ["restrictions.erb", "7d836c4a9f4aa7224f8fdff36f7b9925"],
  ["dedicated_restrictions.erb", "3a65623831d0dbaf67efc2232ccd4e57"],
  ["setup_assistant.erb", "1baf178af74afb28aa12608b4cad7836"],
  ["wifi.erb", "7ba986f3004084b9268db8f20b682072"],
  ["xcuitest_xctestrun.erb", "1ac2ba7d2412c92b6ff71881c6663fd7"],
  ["xcuitest_xctestrun_with_test_host_path.erb", "4c06a7d18a7ac47f364570e5b3dd95ce"],
  ["pfx_to_mobileconfig.erb", "9f5428c9c342e396cb0d753525d78a1f"],
  ["mdm_app_install_manifest.erb", "214b7d87f1bbc349b8f7edd5ccbd5a8a"],
  ["maestro_xctestrun.erb", "02fdf56314653fcef49622614d1ac282"]
]

def actual_template_md5
  result = []
  Dir.foreach(TEMPLATES_DIR) do |filename|
    if filename.include?(".erb")
      md5 = Digest::MD5.file "#{TEMPLATES_DIR}/#{filename}"
      result.append([filename, md5.to_s])
    end
  end
  result.sort_by { |k| k[0] }
end

def sort_template_md5
  EXPECTED_TEMPLATE_MD5.sort_by { |k| k[0] }
end

describe 'template erb files' do
  it "do not change the .erb files unless absolutely required" do
    # Update the md5 to pass this rspec and make sure any
    # changes here are deployed in batching mode.
    # Use `actual_template_md5` to generate the new EXPECTED_TEMPLATE_MD5
    expect(actual_template_md5).to eql sort_template_md5
  end
end
