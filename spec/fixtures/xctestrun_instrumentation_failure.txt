Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild test-without-building -xctestrun budget_keeper_ready.xctestrun -destination id=********-000C506A14F8001E -derivedDataPath /Users/<USER>/BrowserStack/rought/xctestplan/tmp

User defaults from command line:
    IDEDerivedDataPathOverride = /Users/<USER>/BrowserStack/rought/xctestplan/tmp
    IDEPackageSupportUseBuiltinSCM = YES

Testing started
Test Suite 'All tests' started at 2022-12-15 19:58:14.923
Test Suite 'BudgetKeeperTests.xctest' started at 2022-12-15 19:58:14.923
Test Suite 'AccountsViewModelUnitTest' started at 2022-12-15 19:58:14.923
Test Case '-[BudgetKeeperTests.AccountsViewModelUnitTest testAccountsListEmpty]' started.
Test Case '-[BudgetKeeperTests.AccountsViewModelUnitTest testAccountsListEmpty]' passed (0.006 seconds).
Test Case '-[BudgetKeeperTests.AccountsViewModelUnitTest testAddNewAccount]' started.
Test Case '-[BudgetKeeperTests.AccountsViewModelUnitTest testAddNewAccount]' passed (0.004 seconds).
Test Case '-[BudgetKeeperTests.AccountsViewModelUnitTest testDeleteAccount]' started.
Test Case '-[BudgetKeeperTests.AccountsViewModelUnitTest testDeleteAccount]' passed (0.000 seconds).
Test Case '-[BudgetKeeperTests.AccountsViewModelUnitTest testMultipleAccounts]' started.
Test Case '-[BudgetKeeperTests.AccountsViewModelUnitTest testMultipleAccounts]' passed (0.000 seconds).
Test Case '-[BudgetKeeperTests.AccountsViewModelUnitTest testUpdateBalance]' started.
Test Case '-[BudgetKeeperTests.AccountsViewModelUnitTest testUpdateBalance]' passed (0.000 seconds).
Test Suite 'AccountsViewModelUnitTest' passed at 2022-12-15 19:58:14.935.
	 Executed 5 tests, with 0 failures (0 unexpected) in 0.011 (0.011) seconds
Test Suite 'BudgetKeeperTests.xctest' passed at 2022-12-15 19:58:14.935.
	 Executed 5 tests, with 0 failures (0 unexpected) in 0.011 (0.012) seconds
Test Suite 'All tests' passed at 2022-12-15 19:58:14.935.
	 Executed 5 tests, with 0 failures (0 unexpected) in 0.011 (0.012) seconds
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
2022-12-15 19:58:15.307502+0530 BudgetKeeperUITests-Runner[8075:1387047] Running tests...
Test Suite 'All tests' started at 2022-12-15 19:58:33.824
Test Suite 'BudgetKeeperUITests.xctest' started at 2022-12-15 19:58:33.825
Test Suite 'AccountsViewUITest' started at 2022-12-15 19:58:33.825
Test Case '-[BudgetKeeperUITests.AccountsViewUITest testAddAccount]' started.
    t =     0.00s Start Test at 2022-12-15 19:58:33.826
    t =     0.13s Set Up
    t =     0.13s     Open com.avinash.BudgetKeeper
    t =     0.21s         Launch com.avinash.BudgetKeeper
    t =     0.27s             Setting up automation session
    t =     0.34s             Wait for com.avinash.BudgetKeeper to idle
    t =     1.46s Tap "add_account" Button
    t =     1.46s     Wait for com.avinash.BudgetKeeper to idle
    t =     1.55s     Find the "add_account" Button
    t =     1.63s     Check for interrupting elements affecting "add_account" Button
    t =     1.67s     Synthesize event
    t =     2.14s     Wait for com.avinash.BudgetKeeper to idle
    t =     2.58s Find the StaticText
    t =     2.61s Tap TextField
    t =     2.61s     Wait for com.avinash.BudgetKeeper to idle
    t =     2.71s     Find the TextField
    t =     2.75s     Check for interrupting elements affecting "Enter the name" TextField
    t =     2.78s     Synthesize event
    t =     3.15s     Wait for com.avinash.BudgetKeeper to idle
    t =     3.64s Type 'Savings' into TextField
    t =     3.64s     Wait for com.avinash.BudgetKeeper to idle
    t =     3.74s     Find the TextField
    t =     3.80s     Check for interrupting elements affecting "Enter the name" TextField
    t =     3.84s     Synthesize event
    t =     4.23s     Wait for com.avinash.BudgetKeeper to idle
    t =     4.33s Tap "save" Button
    t =     4.33s     Wait for com.avinash.BudgetKeeper to idle
    t =     4.40s     Find the "save" Button
    t =     4.46s     Check for interrupting elements affecting "save" Button
    t =     4.49s     Synthesize event
    t =     4.85s     Wait for com.avinash.BudgetKeeper to idle
    t =     5.26s Get number of matches for: Descendants matching type Cell
    t =     5.39s Find the Button
/Users/<USER>/BrowserStack/rought/xctestplan/BudgetKeeper/starter/BudgetKeeperUITests/AccountsViewUITest.swift:56: error: -[BudgetKeeperUITests.AccountsViewUITest testAddAccount] : XCTAssertEqual failed: ("Savings, $0.00") is not equal to ("Savings
₹0.00")
    t =     5.52s Tear Down
Test Case '-[BudgetKeeperUITests.AccountsViewUITest testAddAccount]' failed (5.722 seconds).
Test Case '-[BudgetKeeperUITests.AccountsViewUITest testDeleteAccount]' started.
    t =     0.00s Start Test at 2022-12-15 19:58:39.550
    t =     0.10s Set Up
    t =     0.10s     Open com.avinash.BudgetKeeper
    t =     0.17s         Launch com.avinash.BudgetKeeper
    t =     0.17s             Terminate com.avinash.BudgetKeeper:8079
    t =     1.35s             Setting up automation session
    t =     1.39s             Wait for com.avinash.BudgetKeeper to idle
    t =     2.50s Tap "add_account" Button
    t =     2.50s     Wait for com.avinash.BudgetKeeper to idle
    t =     2.60s     Find the "add_account" Button
    t =     2.66s     Check for interrupting elements affecting "add_account" Button
    t =     2.69s     Synthesize event
    t =     3.05s     Wait for com.avinash.BudgetKeeper to idle
    t =     3.46s Tap TextField
    t =     3.46s     Wait for com.avinash.BudgetKeeper to idle
    t =     3.56s     Find the TextField
    t =     3.59s     Check for interrupting elements affecting "Enter the name" TextField
    t =     3.62s     Synthesize event
    t =     3.98s     Wait for com.avinash.BudgetKeeper to idle
    t =     4.45s Type 'Savings' into TextField
    t =     4.45s     Wait for com.avinash.BudgetKeeper to idle
    t =     4.55s     Find the TextField
    t =     4.61s     Check for interrupting elements affecting "Enter the name" TextField
    t =     4.64s     Synthesize event
    t =     5.05s     Wait for com.avinash.BudgetKeeper to idle
    t =     5.14s Tap "save" Button
    t =     5.14s     Wait for com.avinash.BudgetKeeper to idle
    t =     5.22s     Find the "save" Button
    t =     5.28s     Check for interrupting elements affecting "save" Button
    t =     5.31s     Synthesize event
    t =     5.66s     Wait for com.avinash.BudgetKeeper to idle
    t =     6.05s Swipe left Cell
    t =     6.05s     Wait for com.avinash.BudgetKeeper to idle
    t =     6.15s     Find the Cell
    t =     6.17s     Check for interrupting elements affecting Cell
    t =     6.21s     Synthesize event
    t =     6.74s     Wait for com.avinash.BudgetKeeper to idle
    t =     7.48s Tap Button
    t =     7.49s     Wait for com.avinash.BudgetKeeper to idle
    t =     7.58s     Find the Button
    t =     7.62s     Check for interrupting elements affecting "Savings, $0.00" Button
    t =     7.65s     Synthesize event
    t =     8.01s     Wait for com.avinash.BudgetKeeper to idle
    t =     8.76s Get number of matches for: Descendants matching type Cell
/Users/<USER>/BrowserStack/rought/xctestplan/BudgetKeeper/starter/BudgetKeeperUITests/AccountsViewUITest.swift:69: error: -[BudgetKeeperUITests.AccountsViewUITest testDeleteAccount] : XCTAssertEqual failed: ("1") is not equal to ("0")
    t =     8.97s Tear Down
Test Case '-[BudgetKeeperUITests.AccountsViewUITest testDeleteAccount]' failed (9.169 seconds).
Test Case '-[BudgetKeeperUITests.AccountsViewUITest testMultipleAccounts]' started.
    t =     0.00s Start Test at 2022-12-15 19:58:48.721
    t =     0.10s Set Up
    t =     0.10s     Open com.avinash.BudgetKeeper
    t =     0.18s         Launch com.avinash.BudgetKeeper
    t =     0.18s             Terminate com.avinash.BudgetKeeper:8080
    t =     1.34s             Setting up automation session
    t =     1.38s             Wait for com.avinash.BudgetKeeper to idle
    t =     2.49s Tap "add_account" Button
    t =     2.50s     Wait for com.avinash.BudgetKeeper to idle
    t =     2.60s     Find the "add_account" Button
    t =     2.66s     Check for interrupting elements affecting "add_account" Button
    t =     2.68s     Synthesize event
    t =     3.04s     Wait for com.avinash.BudgetKeeper to idle
    t =     3.45s Tap TextField
    t =     3.45s     Wait for com.avinash.BudgetKeeper to idle
    t =     3.54s     Find the TextField
    t =     3.58s     Check for interrupting elements affecting "Enter the name" TextField
    t =     3.61s     Synthesize event
    t =     3.97s     Wait for com.avinash.BudgetKeeper to idle
    t =     4.44s Type 'Savings' into TextField
    t =     4.44s     Wait for com.avinash.BudgetKeeper to idle
    t =     4.54s     Find the TextField
    t =     4.60s     Check for interrupting elements affecting "Enter the name" TextField
    t =     4.64s     Synthesize event
    t =     5.03s     Wait for com.avinash.BudgetKeeper to idle
    t =     5.13s Tap "save" Button
    t =     5.13s     Wait for com.avinash.BudgetKeeper to idle
    t =     5.20s     Find the "save" Button
    t =     5.26s     Check for interrupting elements affecting "save" Button
    t =     5.29s     Synthesize event
    t =     5.65s     Wait for com.avinash.BudgetKeeper to idle
    t =     6.04s Tap "add_account" Button
    t =     6.04s     Wait for com.avinash.BudgetKeeper to idle
    t =     6.13s     Find the "add_account" Button
    t =     6.17s     Check for interrupting elements affecting "add_account" Button
    t =     6.20s     Synthesize event
    t =     6.55s     Wait for com.avinash.BudgetKeeper to idle
    t =     6.97s Tap TextField
    t =     6.97s     Wait for com.avinash.BudgetKeeper to idle
    t =     7.06s     Find the TextField
    t =     7.11s     Check for interrupting elements affecting "Enter the name" TextField
    t =     7.13s     Synthesize event
    t =     7.49s     Wait for com.avinash.BudgetKeeper to idle
    t =     7.95s Type 'Salary' into TextField
    t =     7.95s     Wait for com.avinash.BudgetKeeper to idle
    t =     8.05s     Find the TextField
    t =     8.10s     Check for interrupting elements affecting "Enter the name" TextField
    t =     8.13s     Synthesize event
    t =     8.52s     Wait for com.avinash.BudgetKeeper to idle
    t =     8.62s Tap "save" Button
    t =     8.62s     Wait for com.avinash.BudgetKeeper to idle
    t =     8.69s     Find the "save" Button
    t =     8.75s     Check for interrupting elements affecting "save" Button
    t =     8.78s     Synthesize event
    t =     9.14s     Wait for com.avinash.BudgetKeeper to idle
    t =     9.53s Tap Cell
    t =     9.53s     Wait for com.avinash.BudgetKeeper to idle
    t =     9.62s     Find the Cell
    t =     9.65s     Check for interrupting elements affecting Cell
    t =     9.69s     Synthesize event
    t =    10.04s     Wait for com.avinash.BudgetKeeper to idle
    t =    10.44s Tap TextField
    t =    10.45s     Wait for com.avinash.BudgetKeeper to idle
    t =    10.54s     Find the TextField
    t =    10.59s     Check for interrupting elements affecting "0.00" TextField
    t =    10.61s     Synthesize event
    t =    10.98s     Wait for com.avinash.BudgetKeeper to idle
    t =    11.42s Type '7620' into TextField
    t =    11.43s     Wait for com.avinash.BudgetKeeper to idle
    t =    11.53s     Find the TextField
    t =    11.58s     Check for interrupting elements affecting "0.00" TextField
    t =    11.61s     Synthesize event
    t =    11.96s     Wait for com.avinash.BudgetKeeper to idle
    t =    12.06s Tap "save" Button
    t =    12.06s     Wait for com.avinash.BudgetKeeper to idle
    t =    12.14s     Find the "save" Button
    t =    12.20s     Check for interrupting elements affecting "save" Button
    t =    12.23s     Synthesize event
    t =    12.59s     Wait for com.avinash.BudgetKeeper to idle
    t =    12.97s Tap Cell
    t =    12.97s     Wait for com.avinash.BudgetKeeper to idle
    t =    13.06s     Find the Cell
    t =    13.10s     Check for interrupting elements affecting Cell
    t =    13.13s     Synthesize event
    t =    13.50s     Wait for com.avinash.BudgetKeeper to idle
    t =    13.89s Tap TextField
    t =    13.89s     Wait for com.avinash.BudgetKeeper to idle
    t =    13.98s     Find the TextField
    t =    14.02s     Check for interrupting elements affecting "0.00" TextField
    t =    14.05s     Synthesize event
    t =    14.40s     Wait for com.avinash.BudgetKeeper to idle
    t =    14.86s Type '5455' into TextField
    t =    14.86s     Wait for com.avinash.BudgetKeeper to idle
    t =    14.95s     Find the TextField
    t =    15.01s     Check for interrupting elements affecting "0.00" TextField
    t =    15.05s     Synthesize event
    t =    15.39s     Wait for com.avinash.BudgetKeeper to idle
    t =    15.49s Tap "save" Button
    t =    15.49s     Wait for com.avinash.BudgetKeeper to idle
    t =    15.57s     Find the "save" Button
    t =    15.62s     Check for interrupting elements affecting "save" Button
    t =    15.65s     Synthesize event
    t =    16.01s     Wait for com.avinash.BudgetKeeper to idle
    t =    16.38s Get number of matches for: Descendants matching type Cell
    t =    16.51s Find the Button
/Users/<USER>/BrowserStack/rought/xctestplan/BudgetKeeper/starter/BudgetKeeperUITests/AccountsViewUITest.swift:118: error: -[BudgetKeeperUITests.AccountsViewUITest testMultipleAccounts] : XCTAssertEqual failed: ("Savings, $7620.00") is not equal to ("Savings
₹7620.00")
    t =    16.62s Tear Down
Test Case '-[BudgetKeeperUITests.AccountsViewUITest testMultipleAccounts]' failed (16.824 seconds).
Test Case '-[BudgetKeeperUITests.AccountsViewUITest testUpdateBalance]' started.
    t =     0.00s Start Test at 2022-12-15 19:59:05.546
    t =     0.10s Set Up
    t =     0.10s     Open com.avinash.BudgetKeeper
    t =     0.17s         Launch com.avinash.BudgetKeeper
    t =     0.17s             Terminate com.avinash.BudgetKeeper:8081
    t =     1.37s             Setting up automation session
    t =     1.41s             Wait for com.avinash.BudgetKeeper to idle
    t =     2.52s Tap "add_account" Button
    t =     2.52s     Wait for com.avinash.BudgetKeeper to idle
    t =     2.62s     Find the "add_account" Button
    t =     2.68s     Check for interrupting elements affecting "add_account" Button
    t =     2.70s     Synthesize event
    t =     3.06s     Wait for com.avinash.BudgetKeeper to idle
    t =     3.49s Tap TextField
    t =     3.49s     Wait for com.avinash.BudgetKeeper to idle
    t =     3.59s     Find the TextField
    t =     3.62s     Check for interrupting elements affecting "Enter the name" TextField
    t =     3.65s     Synthesize event
    t =     4.00s     Wait for com.avinash.BudgetKeeper to idle
    t =     4.46s Type 'Savings' into TextField
    t =     4.46s     Wait for com.avinash.BudgetKeeper to idle
    t =     4.56s     Find the TextField
    t =     4.62s     Check for interrupting elements affecting "Enter the name" TextField
    t =     4.66s     Synthesize event
    t =     5.05s     Wait for com.avinash.BudgetKeeper to idle
    t =     5.16s Tap "save" Button
    t =     5.16s     Wait for com.avinash.BudgetKeeper to idle
    t =     5.23s     Find the "save" Button
    t =     5.29s     Check for interrupting elements affecting "save" Button
    t =     5.32s     Synthesize event
    t =     5.67s     Wait for com.avinash.BudgetKeeper to idle
    t =     6.08s Tap Cell
    t =     6.08s     Wait for com.avinash.BudgetKeeper to idle
    t =     6.17s     Find the Cell
    t =     6.19s     Check for interrupting elements affecting Cell
    t =     6.23s     Synthesize event
    t =     6.60s     Wait for com.avinash.BudgetKeeper to idle
    t =     6.99s Find the StaticText
    t =     7.01s Tap TextField
    t =     7.01s     Wait for com.avinash.BudgetKeeper to idle
    t =     7.10s     Find the TextField
    t =     7.13s     Check for interrupting elements affecting "0.00" TextField
    t =     7.16s     Synthesize event
    t =     7.52s     Wait for com.avinash.BudgetKeeper to idle
    t =     7.95s Type '-120' into TextField
    t =     7.95s     Wait for com.avinash.BudgetKeeper to idle
    t =     8.05s     Find the TextField
    t =     8.11s     Check for interrupting elements affecting "0.00" TextField
    t =     8.14s     Synthesize event
    t =     8.49s     Wait for com.avinash.BudgetKeeper to idle
    t =     8.59s Tap "save" Button
    t =     8.59s     Wait for com.avinash.BudgetKeeper to idle
    t =     8.67s     Find the "save" Button
    t =     8.71s     Check for interrupting elements affecting "save" Button
    t =     8.74s         Wait for com.apple.springboard to idle
    t =     9.12s     Synthesize event
    t =     9.49s     Wait for com.avinash.BudgetKeeper to idle
    t =     9.86s Find the Button
/Users/<USER>/BrowserStack/rought/xctestplan/BudgetKeeper/starter/BudgetKeeperUITests/AccountsViewUITest.swift:86: error: -[BudgetKeeperUITests.AccountsViewUITest testUpdateBalance] : XCTAssertEqual failed: ("Savings, -$120.00") is not equal to ("Savings
-₹120.00")
    t =     9.99s Tear Down
Test Case '-[BudgetKeeperUITests.AccountsViewUITest testUpdateBalance]' failed (10.194 seconds).
Test Suite 'AccountsViewUITest' failed at 2022-12-15 19:59:15.740.
	 Executed 4 tests, with 4 failures (0 unexpected) in 41.910 (41.915) seconds
Test Suite 'BudgetKeeperUITests.xctest' failed at 2022-12-15 19:59:15.742.
	 Executed 4 tests, with 4 failures (0 unexpected) in 41.910 (41.918) seconds
Test Suite 'All tests' failed at 2022-12-15 19:59:15.745.
	 Executed 4 tests, with 4 failures (0 unexpected) in 41.910 (41.921) seconds
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
2022-12-15 19:59:15.580 xcodebuild[10086:2415983] [MT] IDETestOperationsObserverDebug: 65.968 elapsed -- Testing started completed.
2022-12-15 19:59:15.580 xcodebuild[10086:2415983] [MT] IDETestOperationsObserverDebug: 0.000 sec, +0.000 sec -- start
2022-12-15 19:59:15.580 xcodebuild[10086:2415983] [MT] IDETestOperationsObserverDebug: 65.968 sec, +65.968 sec -- end

Test session results, code coverage, and logs:
	/Users/<USER>/BrowserStack/rought/xctestplan/tmp/Logs/Test/Test-Transient Testing-2022.12.15_19-58-09-+0530.xcresult

Failing tests:
	AccountsViewUITest.testAddAccount()
	AccountsViewUITest.testDeleteAccount()
	AccountsViewUITest.testMultipleAccounts()
	AccountsViewUITest.testUpdateBalance()

** TEST EXECUTE FAILED **
