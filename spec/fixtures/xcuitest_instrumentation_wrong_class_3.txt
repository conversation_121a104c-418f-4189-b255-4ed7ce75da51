Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -xctestrun /tmp/00008110-000A1180010B801E_xctestrun.xml -destination id=00008110-000A1180010B801E test-without-building "-only-testing:LowesUITests/LowesProductionSmokeTest/test_execute_cartMerge_validateCartMerge" -derivedDataPath /tmp/00008110-000A1180010B801E_xcuitest_derived_data_5

User defaults from command line:
    IDEDerivedDataPathOverride = /tmp/00008110-000A1180010B801E_xcuitest_derived_data_5
    IDEPackageSupportUseBuiltinSCM = YES
    XCTHTestRunSpecificationPath = /tmp/00008110-000A1180010B801E_xctestrun.xml

2022-08-12 06:51:57.883 LowesUITests-Runner[1104:44722] Running tests...
Test Suite 'Selected tests' started at 2022-08-12 06:51:57.989
Test Suite 'LowesUITests.xctest' started at 2022-08-12 06:51:57.990
Test Suite 'LowesProductionSmokeTest' started at 2022-08-12 06:51:57.990
Test Case '-[LowesUITests.LowesProductionSmokeTest test_execute_cartMerge_validateCartMerge]' started.
    t =     0.00s Start Test at 2022-08-12 06:51:57.990
    t =     0.04s Set Up
    t =     0.05s Open com.lowes.consumer.m9
    t =     0.09s     Launch com.lowes.consumer.m9
    t =     0.09s         Terminate com.lowes.consumer.m9:1091
    t =     1.24s         Setting up automation session
    t =     1.28s         Wait for com.lowes.consumer.m9 to idle
    t =     2.38s Checking existence of `"LaunchScreen" Image`
    t =    12.40s Checking existence of `"LaunchScreen" Image`
    t =    14.74s Checking existence of `"PopoverDismissRegion" Other`
    t =    14.90s     Ignoring failure to get hierarchy for remote element in process 1108 (Error getting main window kAXErrorServerNotFound)
    t =    16.06s Checking `Expect predicate `exists == 1` for object "Account" Button`
    t =    16.06s     Checking existence of `"Account" Button`
    t =    16.20s         Ignoring failure to get hierarchy for remote element in process 1108 (Error getting main window kAXErrorServerNotFound)
    t =    16.21s Tap "Account" Button
    t =    16.21s     Wait for com.lowes.consumer.m9 to idle
    t =    16.27s     Find the "Account" Button
    t =    16.37s         Ignoring failure to get hierarchy for remote element in process 1108 (Error getting main window kAXErrorServerNotFound)
    t =    16.38s     Check for interrupting elements affecting "lblAccountTabBar" Button
    t =    16.46s         Ignoring failure to get hierarchy for remote element in process 1108 (Error getting main window kAXErrorServerNotFound)
    t =    16.47s     Synthesize event
    t =    16.61s     Wait for com.lowes.consumer.m9 to idle
    t =    17.84s Checking `Expect predicate `exists == 1` for object Cell`
    t =    17.84s     Checking existence of `Cell`
    t =    17.90s Tap Cell
    t =    17.90s     Wait for com.lowes.consumer.m9 to idle
    t =    17.97s     Find the Cell
    t =    18.02s     Check for interrupting elements affecting "loginCell" Cell
    t =    18.04s     Synthesize event
    t =    18.20s     Wait for com.lowes.consumer.m9 to idle
    t =    34.79s Checking `Expect predicate `exists == 1` for object "Email" TextField`
    t =    34.79s     Checking existence of `"Email" TextField`
    t =    34.98s Tap "Email" TextField
    t =    34.98s     Wait for com.lowes.consumer.m9 to idle
    t =    35.04s     Find the "Email" TextField
    t =    35.20s     Check for interrupting elements affecting "Email" TextField
    t =    35.31s     Synthesize event
    t =    35.48s     Wait for com.lowes.consumer.m9 to idle
    t =    40.58s Type 'AustinCBullard@day...' into "Email" TextField
    t =    40.58s     Wait for com.lowes.consumer.m9 to idle
    t =    40.65s     Find the "Email" TextField
    t =    40.82s     Check for interrupting elements affecting "Email" TextField
    t =    40.95s     Synthesize event
    t =    41.60s     Wait for com.lowes.consumer.m9 to idle
    t =    42.68s Checking `Expect predicate `exists == 1` for object "password" SecureTextField`
    t =    42.69s     Checking existence of `"password" SecureTextField`
    t =    42.88s Tap "password" SecureTextField
    t =    42.88s     Wait for com.lowes.consumer.m9 to idle
    t =    42.95s     Find the "password" SecureTextField
    t =    43.14s     Check for interrupting elements affecting "password" SecureTextField
    t =    43.29s     Synthesize event
    t =    43.44s     Wait for com.lowes.consumer.m9 to idle
LowesUITests/DataLoader.swift:41: Fatal error: Unexpectedly found nil while unwrapping an Optional value
2022-08-12 06:53:08.273 LowesUITests-Runner[1122:45840] Running tests...

Restarting after unexpected exit, crash, or test timeout in LowesProductionSmokeTest.test_execute_cartMerge_validateCartMerge(); summary will include totals from previous launches.

Test Suite 'Selected tests' started at 2022-08-12 06:53:08.432
Test Suite 'LowesUITests.xctest' started at 2022-08-12 06:53:08.433
Test Suite 'LowesProductionSmokeTest' started at 2022-08-12 06:53:08.433
Test Suite 'LowesProductionSmokeTest' failed at 2022-08-12 06:53:08.433.
	 Executed 1 test, with 1 failure (0 unexpected) in 0.000 (0.000) seconds
Test Suite 'LowesUITests.xctest' passed at 2022-08-12 06:53:08.433.
	 Executed 0 tests, with 0 failures (0 unexpected) in 0.000 (0.000) seconds
Test Suite 'Selected tests' failed at 2022-08-12 06:53:08.433.
	 Executed 1 test, with 1 failure (0 unexpected) in 0.000 (0.001) seconds
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
2022-08-12 06:53:08.407 xcodebuild[47315:73187659] [MT] IDETestOperationsObserverDebug: 71.589 elapsed -- Testing started completed.
2022-08-12 06:53:08.407 xcodebuild[47315:73187659] [MT] IDETestOperationsObserverDebug: 0.000 sec, +0.000 sec -- start
2022-08-12 06:53:08.407 xcodebuild[47315:73187659] [MT] IDETestOperationsObserverDebug: 71.589 sec, +71.589 sec -- end

Test session results, code coverage, and logs:
	/tmp/00008110-000A1180010B801E_xcuitest_derived_data_5/Logs/Test/Test-Transient Testing-2022.08.12_06-51-56-+0000.xcresult

Failing tests:
	LowesUITests:
		LowesProductionSmokeTest.test_execute_cartMerge_validateCartMerge()

** TEST EXECUTE FAILED **

Testing started