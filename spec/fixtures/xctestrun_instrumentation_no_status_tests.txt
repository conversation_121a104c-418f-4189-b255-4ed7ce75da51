Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild test-without-building -destination id=00008110-001E759634F8801E -xctestrun /tmp/00008110-001E759634F8801E_xctestrun.xctestrun -only-test-configuration RetryUntilFailure -derivedDataPath /tmp/00008110-001E759634F8801E_xcuitest_derived_data -resultBundlePath /tmp/00008110-001E759634F8801E_xcresult_bundle/dummyModule/dummyClass/dummyTest -resultBundleVersion 3

User defaults from command line:
    IDEBuildOperationResultBundlePath = /tmp/00008110-001E759634F8801E_xcresult_bundle/dummyModule/dummyClass/dummyTest
    IDEDerivedDataPathOverride = /tmp/00008110-001E759634F8801E_xcuitest_derived_data
    IDEPackageSupportUseBuiltinSCM = YES
    IDERequestedResultBundleFormatVersion = 3
    XCTHTestRunSpecificationPath = /tmp/00008110-001E759634F8801E_xctestrun.xctestrun

Writing result bundle at path:
	/tmp/00008110-001E759634F8801E_xcresult_bundle/dummyModule/dummyClass/dummyTest

2023-07-09 15:20:48.026907+0000 BullsEyeUITests-Runner[887:32709] Running tests...
2023-07-09 15:20:48.091693+0000 BullsEyeUITests-Runner[887:32709] Test bundle Info.plist at /var/containers/Bundle/Application/1217A546-73DD-43CE-BD30-8C4E3F9184C2/BullsEyeUITests-Runner.app/PlugIns/BullsEyeUITests.xctest/Info.plist specified BullsEyeUITests.CustomTestObserver for NSPrincipalClass, but no class matching that name was found.
Test Suite 'Selected tests' started at 2023-07-09 15:20:48.112
Test Suite 'BullsEyeUITests.xctest' started at 2023-07-09 15:20:48.113
Test Suite 'RandomlyFailingTest' started at 2023-07-09 15:20:48.113
Test Case '-[BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch002]' started (Iteration 1 of 5).
    t =     0.00s Start Test at 2023-07-09 15:20:48.113
    t =     0.04s Set Up
2023-07-09 15:20:48.156500+0000 BullsEyeUITests-Runner[887:32709] Triggered Setup Phase at: : 1688916048
    t =     0.04s     Open io.bharti.BullsEye
    t =     0.08s         Launch io.bharti.BullsEye
    t =     0.21s             Setting up automation session
    t =     0.24s             Wait for io.bharti.BullsEye to idle
    t =     1.30s Find the "Slide" Button
    t =     1.35s Checking existence of `"Get as close as you can to: " StaticText`
    t =     1.36s Checking existence of `"Guess where the slider is: " StaticText`
    t =     1.37s Tap "Type" Button
    t =     1.37s     Wait for io.bharti.BullsEye to idle
    t =     1.44s     Find the "Type" Button
    t =     1.45s     Check for interrupting elements affecting "Type" Button
    t =     1.47s     Synthesize event
    t =     1.63s     Wait for io.bharti.BullsEye to idle
    t =     2.04s Checking existence of `"Guess where the slider is: " StaticText`
    t =     2.07s Checking existence of `"Get as close as you can to: " StaticText`
2023-07-09 15:20:50.199573+0000 BullsEyeUITests-Runner[887:32709] Epoch Time for the Test: 1688916050
2023-07-09 15:20:50.199699+0000 BullsEyeUITests-Runner[887:32709] Failing Assertion: : 0
/Users/<USER>/BrowserStack/BullsEyeIosApp/sample-swift-project-with-parallel-ui-test/BullsEyeUITests/BullsEyeUITests.swift:82: error: -[BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch002] : XCTAssertTrue failed
    t =     2.18s Tear Down
2023-07-09 15:20:50.288311+0000 BullsEyeUITests-Runner[887:32709] Triggered teardown Phase at: : 1688916050
    t =     2.18s     Terminate io.bharti.BullsEye:889
Test Case '-[BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch004]' started (Iteration 1 of 5).
    t =     0.00s Start Test at 2023-07-09 15:20:51.353
    t =     0.07s Set Up
2023-07-09 15:20:51.425755+0000 BullsEyeUITests-Runner[887:32709] Triggered Setup Phase at: : 1688916051
    t =     0.07s     Open io.bharti.BullsEye
    t =     0.13s         Launch io.bharti.BullsEye
    t =     0.23s             Setting up automation session
    t =     0.26s             Wait for io.bharti.BullsEye to idle
    t =     1.33s Find the "Slide" Button
    t =     1.38s Find the "Type" Button
    t =     1.39s Checking existence of `"Guess where the slider is: " StaticText`
    t =     1.41s Checking existence of `"Get as close as you can to: " StaticText`
    t =     1.42s Tap "Slide" Button
    t =     1.42s     Wait for io.bharti.BullsEye to idle
    t =     1.50s     Find the "Slide" Button
    t =     1.52s     Check for interrupting elements affecting "Slide" Button
    t =     1.54s     Synthesize event
    t =     1.69s     Wait for io.bharti.BullsEye to idle
    t =     2.11s Checking existence of `"Get as close as you can to: " StaticText`
    t =     2.13s Checking existence of `"Guess where the slider is: " StaticText`
2023-07-09 15:20:53.502822+0000 BullsEyeUITests-Runner[887:32709] Epoch Time for the Test: 1688916053
2023-07-09 15:20:53.503154+0000 BullsEyeUITests-Runner[887:32709] Success Assertion: : 1
    t =     2.15s Tear Down
2023-07-09 15:20:53.505224+0000 BullsEyeUITests-Runner[887:32709] Triggered teardown Phase at: : 1688916053
    t =     2.15s     Terminate io.bharti.BullsEye:890
Test Case '-[BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch004]' passed (3.311 seconds).
Test Case '-[BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch004]' started (Iteration 2 of 5).
    t =     0.00s Start Test at 2023-07-09 15:20:54.665
    t =     0.08s Set Up
2023-07-09 15:20:54.740406+0000 BullsEyeUITests-Runner[887:32709] Triggered Setup Phase at: : 1688916054
    t =     0.08s     Open io.bharti.BullsEye
    t =     0.13s         Launch io.bharti.BullsEye
    t =     0.22s             Setting up automation session
    t =     0.25s             Wait for io.bharti.BullsEye to idle
    t =     1.32s Find the "Slide" Button
    t =     1.37s Checking existence of `"Get as close as you can to: " StaticText`
    t =     1.38s Checking existence of `"Guess where the slider is: " StaticText`
    t =     1.40s Tap "Type" Button
    t =     1.40s     Wait for io.bharti.BullsEye to idle
    t =     1.47s     Find the "Type" Button
    t =     1.49s     Check for interrupting elements affecting "Type" Button
    t =     1.51s     Synthesize event
    t =     1.67s     Wait for io.bharti.BullsEye to idle
    t =     2.08s Checking existence of `"Guess where the slider is: " StaticText`
    t =     2.10s Checking existence of `"Get as close as you can to: " StaticText`
2023-07-09 15:20:56.783371+0000 BullsEyeUITests-Runner[887:32709] Epoch Time for the Test: 1688916056
2023-07-09 15:20:56.783482+0000 BullsEyeUITests-Runner[887:32709] Failing Assertion: : 0
/Users/<USER>/BrowserStack/BullsEyeIosApp/sample-swift-project-with-parallel-ui-test/BullsEyeUITests/BullsEyeUITests.swift:82: error: -[BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch004] : XCTAssertTrue failed
    t =     2.20s Tear Down
2023-07-09 15:20:56.860171+0000 BullsEyeUITests-Runner[887:32709] Triggered teardown Phase at: : 1688916056
    t =     2.20s     Terminate io.bharti.BullsEye:891
Test Case '-[BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch004]' failed (3.344 seconds).
Test Case '-[BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch029]' started (Iteration 2 of 5).
    t =     0.00s Start Test at 2023-07-09 15:23:00.695
    t =     0.08s Set Up
2023-07-09 15:23:00.768840+0000 BullsEyeUITests-Runner[887:32709] Triggered Setup Phase at: : 1688916180
    t =     0.08s     Open io.bharti.BullsEye
    t =     0.14s         Launch io.bharti.BullsEye
    t =     0.24s             Setting up automation session
    t =     0.25s             Wait for io.bharti.BullsEye to idle
    t =     1.32s Find the "Slide" Button
    t =     1.37s Checking existence of `"Get as close as you can to: " StaticText`
    t =     1.39s Checking existence of `"Guess where the slider is: " StaticText`
    t =     1.40s Tap "Type" Button
    t =     1.40s     Wait for io.bharti.BullsEye to idle
    t =     1.48s     Find the "Type" Button
    t =     1.51s     Check for interrupting elements affecting "Type" Button
    t =     1.53s     Synthesize event
    t =     1.68s     Wait for io.bharti.BullsEye to idle
    t =     2.09s Checking existence of `"Guess where the slider is: " StaticText`
    t =     2.12s Checking existence of `"Get as close as you can to: " StaticText`
2023-07-09 15:23:02.828344+0000 BullsEyeUITests-Runner[887:32709] Epoch Time for the Test: 1688916182
2023-07-09 15:23:02.828553+0000 BullsEyeUITests-Runner[887:32709] Failing Assertion: : 0
/Users/<USER>/BrowserStack/BullsEyeIosApp/sample-swift-project-with-parallel-ui-test/BullsEyeUITests/BullsEyeUITests.swift:82: error: -[BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch029] : XCTAssertTrue failed
    t =     2.22s Tear Down
2023-07-09 15:23:02.909719+0000 BullsEyeUITests-Runner[887:32709] Triggered teardown Phase at: : 1688916182
    t =     2.22s     Terminate io.bharti.BullsEye:939
Test Suite 'RandomlyFailingTest' failed at 2023-07-09 15:23:04.038.
	 Executed 41 tests, with 24 failures (0 unexpected) in 135.882 (135.925) seconds
Test Suite 'BullsEyeUITests.xctest' failed at 2023-07-09 15:23:04.040.
	 Executed 41 tests, with 24 failures (0 unexpected) in 135.882 (135.927) seconds
Test Suite 'Selected tests' failed at 2023-07-09 15:23:04.042.
	 Executed 41 tests, with 24 failures (0 unexpected) in 135.882 (135.930) seconds
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
2023-07-09 15:23:03.993 xcodebuild[58107:102650067] [MT] IDETestOperationsObserverDebug: 138.222 elapsed -- Testing started completed.
2023-07-09 15:23:03.993 xcodebuild[58107:102650067] [MT] IDETestOperationsObserverDebug: 0.000 sec, +0.000 sec -- start
2023-07-09 15:23:03.993 xcodebuild[58107:102650067] [MT] IDETestOperationsObserverDebug: 138.222 sec, +138.222 sec -- end

Test session results, code coverage, and logs:
	/tmp/00008110-001E759634F8801E_xcresult_bundle/dummyModule/dummyClass/dummyTest

Failing tests:
	BullsEyeUITests:
		RandomlyFailingTest.testGameStyleSwitch002()
		RandomlyFailingTest.testGameStyleSwitch004()
		RandomlyFailingTest.testGameStyleSwitch005()
		RandomlyFailingTest.testGameStyleSwitch006()
		RandomlyFailingTest.testGameStyleSwitch007()
		RandomlyFailingTest.testGameStyleSwitch008()
		RandomlyFailingTest.testGameStyleSwitch010()
		RandomlyFailingTest.testGameStyleSwitch011()
		RandomlyFailingTest.testGameStyleSwitch012()
		RandomlyFailingTest.testGameStyleSwitch013()
		RandomlyFailingTest.testGameStyleSwitch014()
		RandomlyFailingTest.testGameStyleSwitch015()
		RandomlyFailingTest.testGameStyleSwitch016()
		RandomlyFailingTest.testGameStyleSwitch017()
		RandomlyFailingTest.testGameStyleSwitch018()
		RandomlyFailingTest.testGameStyleSwitch020()
		RandomlyFailingTest.testGameStyleSwitch021()
		RandomlyFailingTest.testGameStyleSwitch022()
		RandomlyFailingTest.testGameStyleSwitch023()
		RandomlyFailingTest.testGameStyleSwitch024()
		RandomlyFailingTest.testGameStyleSwitch025()
		RandomlyFailingTest.testGameStyleSwitch027()
		RandomlyFailingTest.testGameStyleSwitch028()
		RandomlyFailingTest.testGameStyleSwitch029()

** TEST EXECUTE FAILED **

Testing started
Testing started for configuration 'RetryUntilFailure'