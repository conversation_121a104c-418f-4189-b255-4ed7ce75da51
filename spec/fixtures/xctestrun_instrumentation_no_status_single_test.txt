Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -xctestrun /tmp/00008110-001A24AA3E69801E_xctestrun.xml -destination id=00008110-001A24AA3E69801E test-without-building -derivedDataPath /tmp/00008110-001A24AA3E69801E_xcuitest_derived_data_12 "-only-testing:SkrillPayments-UITests/LoyaltyRedeemTests/testRedeemSweepstake" -resultBundlePath /tmp/00008110-001A24AA3E69801E_xcresult_bundle/SkrillPayments-UITests/LoyaltyRedeemTests/testRedeemSweepstake_1690767929 -resultBundleVersion 3

User defaults from command line:
    IDEBuildOperationResultBundlePath = /tmp/00008110-001A24AA3E69801E_xcresult_bundle/SkrillPayments-UITests/LoyaltyRedeemTests/testRedeemSweepstake_1690767929
    IDEDerivedDataPathOverride = /tmp/00008110-001A24AA3E69801E_xcuitest_derived_data_12
    IDEPackageSupportUseBuiltinSCM = YES
    IDERequestedResultBundleFormatVersion = 3
    XCTHTestRunSpecificationPath = /tmp/00008110-001A24AA3E69801E_xctestrun.xml

Writing result bundle at path:
	/tmp/00008110-001A24AA3E69801E_xcresult_bundle/SkrillPayments-UITests/LoyaltyRedeemTests/testRedeemSweepstake_1690767929

2023-07-31 01:45:31.201 SkrillPayments-UITests-Runner[4340:148198] Running tests...
Test Suite 'Selected tests' started at 2023-07-31 01:45:31.599
Test Suite 'SkrillPayments-UITests.xctest' started at 2023-07-31 01:45:31.600
Test Suite 'LoyaltyRedeemTests' started at 2023-07-31 01:45:31.600
Test Case '-[SkrillPayments_UITests.LoyaltyRedeemTests testRedeemSweepstake]' started.
    t =     0.00s Start Test at 2023-07-31 01:45:31.602
2023-07-31 01:45:31.971 SkrillPayments-UITests-Runner[4344:148494] Running tests...

Restarting after unexpected exit, crash, or test timeout in LoyaltyRedeemTests.testRedeemSweepstake(); summary will include totals from previous launches.

Test Suite 'Selected tests' started at 2023-07-31 01:45:34.925
Test Suite 'SkrillPayments-UITests.xctest' started at 2023-07-31 01:45:34.926
Test Suite 'LoyaltyRedeemTests' started at 2023-07-31 01:45:34.927
Test Suite 'LoyaltyRedeemTests' failed at 2023-07-31 01:45:34.927.
	 Executed 1 test, with 1 failure (0 unexpected) in 0.000 (0.000) seconds
Test Suite 'SkrillPayments-UITests.xctest' passed at 2023-07-31 01:45:34.927.
	 Executed 0 tests, with 0 failures (0 unexpected) in 0.000 (0.001) seconds
Test Suite 'Selected tests' failed at 2023-07-31 01:45:34.928.
	 Executed 1 test, with 1 failure (0 unexpected) in 0.000 (0.002) seconds
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
2023-07-31 01:45:34.980 xcodebuild[53956:505988869] [MT] IDETestOperationsObserverDebug: 4.409 elapsed -- Testing started completed.
2023-07-31 01:45:34.980 xcodebuild[53956:505988869] [MT] IDETestOperationsObserverDebug: 0.000 sec, +0.000 sec -- start
2023-07-31 01:45:34.980 xcodebuild[53956:505988869] [MT] IDETestOperationsObserverDebug: 4.409 sec, +4.409 sec -- end

Test session results, code coverage, and logs:
	/tmp/00008110-001A24AA3E69801E_xcresult_bundle/SkrillPayments-UITests/LoyaltyRedeemTests/testRedeemSweepstake_1690767929

Failing tests:
	SkrillPayments-UITests:
		LoyaltyRedeemTests.testRedeemSweepstake()

** TEST EXECUTE FAILED **

Testing started