Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -xctestrun /tmp/00008101-001935503468001E_xctestrun.xml -destination id=00008101-001935503468001E test-without-building -derivedDataPath /tmp/00008101-001935503468001E_xcuitest_derived_data_1

User defaults from command line:
    IDEDerivedDataPathOverride = /tmp/00008101-001935503468001E_xcuitest_derived_data_1
    IDEPackageSupportUseBuiltinSCM = YES

2023-08-24 07:28:47.681 xcodebuild[19507:2498227642]  DVTDownloadable: Unexpected error attempting to parse downloadable from index: Error Domain=DVTDownloadableErrors Code=16 "Unable to read in downloadable data. See underlying errors for details." UserInfo={DownloadablePlist={
    authentication = virtual;
    category = simulator;
    contentType = diskImage;
    dictionaryVersion = 2;
    fileSize = 7354535384;
    hostRequirements =     {
        minHostVersion = "13.4";
        minXcodeVersion = "15.0";
    };
    identifier = "com.apple.dmg.xrSimulatorSDK1_0_b1";
    name = "xrOS 1 beta Simulator Runtime";
    platform = "com.apple.platform.xros";
    simulatorVersion =     {
        buildUpdate = 21N5165g;
        version = "1.0";
    };
    source = "https://download.developer.apple.com/Developer_Tools/visionOS_1_beta/visionOS_1_beta_Simulator_Runtime.dmg";
    version = "*******";
}, NSLocalizedDescription=Unable to read in downloadable data. See underlying errors for details., DVTUnderlyingErrorsKey=(
    "Error Domain=DVTDownloadableErrors Code=23 \"'com.apple.platform.xros' is an unknown platform identifier.\" UserInfo={NSLocalizedDescription='com.apple.platform.xros' is an unknown platform identifier.}"
)}
2023-08-24 07:28:47.681 xcodebuild[19507:2498227642]  DVTDownloadable: Unexpected error attempting to parse downloadable from index: Error Domain=DVTDownloadableErrors Code=16 "Unable to read in downloadable data. See underlying errors for details." UserInfo={DownloadablePlist={
    authentication = virtual;
    category = simulator;
    contentType = diskImage;
    dictionaryVersion = 2;
    fileSize = 7220726735;
    hostRequirements =     {
        minHostVersion = "13.4";
        minXcodeVersion = "15.0";
    };
    identifier = "com.apple.dmg.xrSimulatorSDK1_0_b2";
    name = "xrOS 2 beta Simulator Runtime";
    platform = "com.apple.platform.xros";
    simulatorVersion =     {
        buildUpdate = 21N5207f;
        version = "1.0";
    };
    source = "https://download.developer.apple.com/Developer_Tools/visionOS_1_beta_2_Simulator_Runtime/visionOS_1_beta_2_Simulator_Runtime.dmg";
    version = "*******";
}, NSLocalizedDescription=Unable to read in downloadable data. See underlying errors for details., DVTUnderlyingErrorsKey=(
    "Error Domain=DVTDownloadableErrors Code=23 \"'com.apple.platform.xros' is an unknown platform identifier.\" UserInfo={NSLocalizedDescription='com.apple.platform.xros' is an unknown platform identifier.}"
)}
2023-08-24 07:28:50.950320+0000 Runner[4124:320575] [VERBOSE-2:FlutterDarwinContextMetalImpeller.mm(37)] Using the Impeller rendering backend.
2023-08-24 07:28:50.955596+0000 Runner[4124:320575] Warning: Unable to create restoration in progress marker file
2023-08-24 07:28:50.976341+0000 Runner[4124:320871] flutter: 00:00 +0: get text local test
2023-08-24 07:28:51.140012+0000 Runner[4124:320871] flutter: WebView is loading (progress : 10%)
2023-08-24 07:28:51.170107+0000 Runner[4124:320575] [Sandbox] Could not enable Mach bootstrap, errno = 1.
2023-08-24 07:28:51.170179+0000 Runner[4124:320575] [Sandbox] Could not enable Mach bootstrap, errno = 1.
2023-08-24 07:28:51.237189+0000 Runner[4124:320871] flutter: allowing navigation to NavigationRequest(url: http://bs-local.com:10500/, isForMainFrame: true)
2023-08-24 07:28:51.279774+0000 Runner[4124:320871] flutter: Page started loading: http://bs-local.com:10500/
2023-08-24 07:28:51.287269+0000 Runner[4124:320575] [Process] 0x106814e18 - [pageProxyID=6, webPageID=7, PID=4125] WebPageProxy::didFailProvisionalLoadForFrame: frameID=3, isMainFrame=1, domain=NSURLErrorDomain, code=-1022, isMainFrame=1
2023-08-24 07:28:51.287930+0000 Runner[4124:320871] flutter: WebView is loading (progress : 100%)
2023-08-24 07:28:51.363906+0000 Runner[4124:320872] fopen failed for data file: errno = 2 (No such file or directory)
2023-08-24 07:28:51.363937+0000 Runner[4124:320872] Errors found! Invalidating cache...
2023-08-24 07:29:01.582277+0000 Runner[4124:320871] flutter: Expected: exactly one matching node in the widget tree
2023-08-24 07:29:01.582609+0000 Runner[4124:320871] flutter:   Actual: _TextContainingFinder:<zero widgets with text containing Directory listing for / (ignoring offstage widgets)>
2023-08-24 07:29:01.582780+0000 Runner[4124:320871] flutter:    Which: means none were found but one was expected
2023-08-24 07:29:01.582948+0000 Runner[4124:320871] flutter:
2023-08-24 07:29:01.583952+0000 Runner[4124:320871] flutter: #0      fail (package:matcher/src/expect/expect.dart:149)
2023-08-24 07:29:01.584147+0000 Runner[4124:320871] flutter: #1      _expect (package:matcher/src/expect/expect.dart:144)
2023-08-24 07:29:01.584305+0000 Runner[4124:320871] flutter: #2      expect (package:matcher/src/expect/expect.dart:56)
2023-08-24 07:29:01.584459+0000 Runner[4124:320871] flutter: #3      expect (package:flutter_test/src/widget_tester.dart:454)
2023-08-24 07:29:01.584615+0000 Runner[4124:320871] flutter: #4      main.<anonymous closure> (file:///Users/<USER>/Downloads/my_local_app/integration_test/app_test.dart:83)
2023-08-24 07:29:01.584754+0000 Runner[4124:320871] flutter: <asynchronous suspension>
2023-08-24 07:29:01.584857+0000 Runner[4124:320871] flutter: #5      testWidgets.<anonymous closure>.<anonymous closure> (package:flutter_test/src/widget_tester.dart:165)
2023-08-24 07:29:01.584957+0000 Runner[4124:320871] flutter: <asynchronous suspension>
2023-08-24 07:29:01.585060+0000 Runner[4124:320871] flutter: #6      TestWidgetsFlutterBinding._runTestBody (package:flutter_test/src/binding.dart:982)
2023-08-24 07:29:01.585164+0000 Runner[4124:320871] flutter: <asynchronous suspension>
2023-08-24 07:29:01.585326+0000 Runner[4124:320871] flutter: 00:10 +0: get text local test [E]
2023-08-24 07:29:01.585441+0000 Runner[4124:320871] flutter:   Test failed. See exception logs above.
  The test description was: get text local test
2023-08-24 07:29:01.585568+0000 Runner[4124:320871] flutter:
2023-08-24 07:29:01.585837+0000 Runner[4124:320871] flutter: 00:10 +0 -1: get other tests
2023-08-24 07:29:01.708843+0000 Runner[4124:320871] flutter: 00:10 +0 -1: get text local test
2023-08-24 07:29:01.708919+0000 Runner[4124:320871] flutter: WebView is loading (progress : 10%)
2023-08-24 07:29:01.722471+0000 Runner[4124:320575] [Sandbox] Could not enable Mach bootstrap, errno = 1.
2023-08-24 07:29:01.722552+0000 Runner[4124:320575] [Sandbox] Could not enable Mach bootstrap, errno = 1.
2023-08-24 07:29:01.753627+0000 Runner[4124:320871] flutter: allowing navigation to NavigationRequest(url: http://bs-local.com:10500/, isForMainFrame: true)
2023-08-24 07:29:01.765320+0000 Runner[4124:320871] flutter: Page started loading: http://bs-local.com:10500/
2023-08-24 07:29:01.769848+0000 Runner[4124:320575] [Process] 0x10213b218 - [pageProxyID=18, webPageID=19, PID=4132] WebPageProxy::didFailProvisionalLoadForFrame: frameID=3, isMainFrame=1, domain=NSURLErrorDomain, code=-1022, isMainFrame=1
2023-08-24 07:29:01.770741+0000 Runner[4124:320871] flutter: WebView is loading (progress : 100%)
2023-08-24 07:29:12.163464+0000 Runner[4124:320871] flutter: 00:21 +0 -1: get other tests
2023-08-24 07:29:12.163683+0000 Runner[4124:320871] flutter: Expected: exactly one matching node in the widget tree
2023-08-24 07:29:12.163818+0000 Runner[4124:320871] flutter:   Actual: _TextContainingFinder:<zero widgets with text containing Directory listing for / (ignoring offstage widgets)>
2023-08-24 07:29:12.163938+0000 Runner[4124:320871] flutter:    Which: means none were found but one was expected
2023-08-24 07:29:12.164038+0000 Runner[4124:320871] flutter:
2023-08-24 07:29:12.164474+0000 Runner[4124:320871] flutter: #0      fail (package:matcher/src/expect/expect.dart:149)
2023-08-24 07:29:12.164590+0000 Runner[4124:320871] flutter: #1      _expect (package:matcher/src/expect/expect.dart:144)
2023-08-24 07:29:12.164693+0000 Runner[4124:320871] flutter: #2      expect (package:matcher/src/expect/expect.dart:56)
2023-08-24 07:29:12.164798+0000 Runner[4124:320871] flutter: #3      expect (package:flutter_test/src/widget_tester.dart:454)
2023-08-24 07:29:12.164903+0000 Runner[4124:320871] flutter: #4      main.<anonymous closure> (file:///Users/<USER>/Downloads/my_local_app/integration_test/app_test.dart:83)
2023-08-24 07:29:12.165004+0000 Runner[4124:320871] flutter: <asynchronous suspension>
2023-08-24 07:29:12.165105+0000 Runner[4124:320871] flutter: #5      testWidgets.<anonymous closure>.<anonymous closure> (package:flutter_test/src/widget_tester.dart:165)
2023-08-24 07:29:12.165205+0000 Runner[4124:320871] flutter: <asynchronous suspension>
2023-08-24 07:29:12.165306+0000 Runner[4124:320871] flutter: #6      TestWidgetsFlutterBinding._runTestBody (package:flutter_test/src/binding.dart:982)
2023-08-24 07:29:12.165408+0000 Runner[4124:320871] flutter: <asynchronous suspension>
2023-08-24 07:29:12.165552+0000 Runner[4124:320871] flutter: 00:21 +0 -1: get other tests [E]
2023-08-24 07:29:12.165838+0000 Runner[4124:320871] flutter:   Test failed. See exception logs above.
  The test description was: get other tests
2023-08-24 07:29:12.166456+0000 Runner[4124:320871] flutter:
2023-08-24 07:29:12.167059+0000 Runner[4124:320871] flutter: 00:21 +0 -2: (tearDownAll)
2023-08-24 07:29:12.168089+0000 Runner[4124:320871] flutter: 00:21 +1 -2: Some tests failed.
Test Suite 'All tests' started at 2023-08-24 07:29:13.033
Test Suite 'RunnerTests.xctest' started at 2023-08-24 07:29:13.036
Test Suite 'RunnerTests' started at 2023-08-24 07:29:13.037
Test Case '-[RunnerTests testGetTextLocalTest]' started.
/Users/<USER>/Downloads/my_local_app/ios/RunnerTests/RunnerTests.m:5: error: -[RunnerTests testGetTextLocalTest] : ((success) is true) failed
Test Case '-[RunnerTests testGetTextLocalTest]' passed (0.031 seconds).
Test Case '-[RunnerTests testGetOtherTests]' started.
/Users/<USER>/Downloads/my_local_app/ios/RunnerTests/RunnerTests.m:5: error: -[RunnerTests testGetOtherTests] : ((success) is true) failed
Test Case '-[RunnerTests testGetOtherTests]' failed (0.002 seconds).
Test Suite 'RunnerTests' failed at 2023-08-24 07:29:13.071.
	 Executed 2 tests, with 1 failures (0 unexpected) in 0.033 (0.035) seconds
Test Suite 'RunnerTests.xctest' failed at 2023-08-24 07:29:13.072.
	 Executed 2 tests, with 1 failures (0 unexpected) in 0.033 (0.036) seconds
Test Suite 'All tests' failed at 2023-08-24 07:29:13.072.
	 Executed 2 tests, with 1 failures (0 unexpected) in 0.033 (0.039) seconds
2023-08-24 07:29:13.150 xcodebuild[19507:2498227620] [MT] IDETestOperationsObserverDebug: 23.259 elapsed -- Testing started completed.
2023-08-24 07:29:13.151 xcodebuild[19507:2498227620] [MT] IDETestOperationsObserverDebug: 0.000 sec, +0.000 sec -- start
2023-08-24 07:29:13.151 xcodebuild[19507:2498227620] [MT] IDETestOperationsObserverDebug: 23.259 sec, +23.259 sec -- end

Test session results, code coverage, and logs:
	/tmp/00008101-001935503468001E_xcuitest_derived_data_1/Logs/Test/Test-Transient Testing-2023.08.24_07-28-49-+0000.xcresult

Failing tests:
	-[RunnerTests testGetOtherTests]

** TEST EXECUTE FAILED **

Testing started