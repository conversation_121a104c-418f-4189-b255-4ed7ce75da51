Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -xctestrun /tmp/00008110-000A048C0209801E_xctestrun.xml -destination id=00008110-000A048C0209801E test-without-building -derivedDataPath /tmp/00008110-000A048C0209801E_xcuitest_derived_data_1 "-only-testing:BullsEyeUITests/RandomlyFailingTest/testGameStyleSwitch002" -test-iterations 5 -resultBundlePath /tmp/00008110-000A048C0209801E_xcresult_bundle/BullsEyeUITests/RandomlyFailingTest/testGameStyleSwitch002_1688662541 -resultBundleVersion 3

User defaults from command line:
    IDEBuildOperationResultBundlePath = /tmp/00008110-000A048C0209801E_xcresult_bundle/BullsEyeUITests/RandomlyFailingTest/testGameStyleSwitch002_1688662541
    IDEDerivedDataPathOverride = /tmp/00008110-000A048C0209801E_xcuitest_derived_data_1
    IDEPackageSupportUseBuiltinSCM = YES
    IDERequestedResultBundleFormatVersion = 3
    XCTHTestRunSpecificationPath = /tmp/00008110-000A048C0209801E_xctestrun.xml

Running tests repeatedly 5 times.

Writing result bundle at path:
	/tmp/00008110-000A048C0209801E_xcresult_bundle/BullsEyeUITests/RandomlyFailingTest/testGameStyleSwitch002_1688662541

2023-07-06 16:55:45.373 BullsEyeUITests-Runner[6935:250034] Running tests...
2023-07-06 16:55:45.441 BullsEyeUITests-Runner[6935:250034] Test bundle Info.plist at /var/containers/Bundle/Application/C06FAA07-9A6E-4685-8BE6-6B1E2D35707D/BullsEyeUITests-Runner.app/PlugIns/BullsEyeUITests.xctest/Info.plist specified BullsEyeUITests.CustomTestObserver for NSPrincipalClass, but no class matching that name was found.
Test Suite 'Selected tests' started at 2023-07-06 16:55:45.482
Test Suite 'BullsEyeUITests.xctest' started at 2023-07-06 16:55:45.482
Test Suite 'RandomlyFailingTest' started at 2023-07-06 16:55:45.483
Test Case '-[BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch002]' started (Iteration 1 of 5).
    t =     0.00s Start Test at 2023-07-06 16:55:45.483
    t =     0.04s Set Up
2023-07-06 16:55:45.523 BullsEyeUITests-Runner[6935:250034] Triggered Setup Phase at: : 1688662545
    t =     0.04s     Open io.bharti.BullsEye
    t =     0.08s         Launch io.bharti.BullsEye
    t =     0.20s             Setting up automation session
    t =     0.25s             Wait for io.bharti.BullsEye to idle
    t =     1.32s Find the "Slide" Button
    t =     1.37s Checking existence of `"Get as close as you can to: " StaticText`
    t =     1.38s Checking existence of `"Guess where the slider is: " StaticText`
    t =     1.40s Tap "Type" Button
    t =     1.40s     Wait for io.bharti.BullsEye to idle
    t =     1.47s     Find the "Type" Button
    t =     1.50s     Check for interrupting elements affecting "Type" Button
    t =     1.52s     Synthesize event
    t =     1.68s     Wait for io.bharti.BullsEye to idle
    t =     2.09s Checking existence of `"Guess where the slider is: " StaticText`
    t =     2.12s Checking existence of `"Get as close as you can to: " StaticText`
2023-07-06 16:55:47.616 BullsEyeUITests-Runner[6935:250034] Epoch Time for the Test: 1688662547
2023-07-06 16:55:47.616 BullsEyeUITests-Runner[6935:250034] Success Assertion: : 1
    t =     2.13s Tear Down
2023-07-06 16:55:47.618 BullsEyeUITests-Runner[6935:250034] Triggered teardown Phase at: : 1688662547
    t =     2.14s     Terminate io.bharti.BullsEye:6936
Test Case '-[BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch002]' passed (3.223 seconds).
Test Case '-[BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch002]' started (Iteration 2 of 5).
    t =     0.00s Start Test at 2023-07-06 16:55:48.706
    t =     0.07s Set Up
2023-07-06 16:55:48.776 BullsEyeUITests-Runner[6935:250034] Triggered Setup Phase at: : 1688662548
    t =     0.07s     Open io.bharti.BullsEye
    t =     0.13s         Launch io.bharti.BullsEye
    t =     0.23s             Setting up automation session
    t =     0.26s             Wait for io.bharti.BullsEye to idle
    t =     1.33s Find the "Slide" Button
    t =     1.37s Find the "Type" Button
    t =     1.38s Checking existence of `"Guess where the slider is: " StaticText`
    t =     1.39s Checking existence of `"Get as close as you can to: " StaticText`
    t =     1.40s Tap "Slide" Button
    t =     1.40s     Wait for io.bharti.BullsEye to idle
    t =     1.47s     Find the "Slide" Button
    t =     1.49s     Check for interrupting elements affecting "Slide" Button
    t =     1.51s     Synthesize event
    t =     1.66s     Wait for io.bharti.BullsEye to idle
    t =     2.08s Checking existence of `"Get as close as you can to: " StaticText`
    t =     2.10s Checking existence of `"Guess where the slider is: " StaticText`
2023-07-06 16:55:50.814 BullsEyeUITests-Runner[6935:250034] Epoch Time for the Test: 1688662550
2023-07-06 16:55:50.814 BullsEyeUITests-Runner[6935:250034] Failing Assertion: : 0
/Users/<USER>/BrowserStack/BullsEyeIosApp/sample-swift-project-with-parallel-ui-test/BullsEyeUITests/BullsEyeUITests.swift:82: error: -[BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch002] : XCTAssertTrue failed
    t =     2.21s Tear Down
2023-07-06 16:55:50.913 BullsEyeUITests-Runner[6935:250034] Triggered teardown Phase at: : 1688662550
    t =     2.21s     Terminate io.bharti.BullsEye:6938
Test Case '-[BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch002]' failed (3.281 seconds).
Test Case '-[BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch002]' started (Iteration 3 of 5).
    t =     0.00s Start Test at 2023-07-06 16:55:51.989
    t =     0.07s Set Up
2023-07-06 16:55:52.058 BullsEyeUITests-Runner[6935:250034] Triggered Setup Phase at: : 1688662552
    t =     0.07s     Open io.bharti.BullsEye
    t =     0.12s         Launch io.bharti.BullsEye
    t =     0.24s             Setting up automation session
    t =     0.27s             Wait for io.bharti.BullsEye to idle
    t =     1.34s Find the "Slide" Button
    t =     1.39s Checking existence of `"Get as close as you can to: " StaticText`
    t =     1.40s Checking existence of `"Guess where the slider is: " StaticText`
    t =     1.42s Tap "Type" Button
    t =     1.42s     Wait for io.bharti.BullsEye to idle
    t =     1.54s     Find the "Type" Button
    t =     1.57s     Check for interrupting elements affecting "Type" Button
    t =     1.60s     Synthesize event
    t =     1.75s     Wait for io.bharti.BullsEye to idle
    t =     2.16s Checking existence of `"Guess where the slider is: " StaticText`
    t =     2.19s Checking existence of `"Get as close as you can to: " StaticText`
2023-07-06 16:55:54.192 BullsEyeUITests-Runner[6935:250034] Epoch Time for the Test: 1688662554
2023-07-06 16:55:54.192 BullsEyeUITests-Runner[6935:250034] Failing Assertion: : 0
/Users/<USER>/BrowserStack/BullsEyeIosApp/sample-swift-project-with-parallel-ui-test/BullsEyeUITests/BullsEyeUITests.swift:82: error: -[BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch002] : XCTAssertTrue failed
    t =     2.29s Tear Down
2023-07-06 16:55:54.279 BullsEyeUITests-Runner[6935:250034] Triggered teardown Phase at: : 1688662554
    t =     2.29s     Terminate io.bharti.BullsEye:6940
Test Case '-[BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch002]' failed (3.445 seconds).
Test Case '-[BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch002]' started (Iteration 4 of 5).
    t =     0.00s Start Test at 2023-07-06 16:55:55.435
    t =     0.07s Set Up
2023-07-06 16:55:55.503 BullsEyeUITests-Runner[6935:250034] Triggered Setup Phase at: : 1688662555
    t =     0.07s     Open io.bharti.BullsEye
    t =     0.12s         Launch io.bharti.BullsEye
    t =     0.23s             Setting up automation session
    t =     0.26s             Wait for io.bharti.BullsEye to idle
    t =     1.34s Find the "Slide" Button
    t =     1.39s Find the "Type" Button
    t =     1.40s Checking existence of `"Guess where the slider is: " StaticText`
    t =     1.42s Checking existence of `"Get as close as you can to: " StaticText`
    t =     1.43s Tap "Slide" Button
    t =     1.43s     Wait for io.bharti.BullsEye to idle
    t =     1.50s     Find the "Slide" Button
    t =     1.52s     Check for interrupting elements affecting "Slide" Button
    t =     1.55s     Synthesize event
    t =     1.70s     Wait for io.bharti.BullsEye to idle
    t =     2.11s Checking existence of `"Get as close as you can to: " StaticText`
    t =     2.14s Checking existence of `"Guess where the slider is: " StaticText`
2023-07-06 16:55:57.586 BullsEyeUITests-Runner[6935:250034] Epoch Time for the Test: 1688662557
2023-07-06 16:55:57.586 BullsEyeUITests-Runner[6935:250034] Success Assertion: : 1
    t =     2.15s Tear Down
2023-07-06 16:55:57.589 BullsEyeUITests-Runner[6935:250034] Triggered teardown Phase at: : 1688662557
    t =     2.16s     Terminate io.bharti.BullsEye:6942
Test Case '-[BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch002]' passed (3.310 seconds).
Test Case '-[BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch002]' started (Iteration 5 of 5).
    t =     0.00s Start Test at 2023-07-06 16:55:58.746
    t =     0.07s Set Up
2023-07-06 16:55:58.814 BullsEyeUITests-Runner[6935:250034] Triggered Setup Phase at: : 1688662558
    t =     0.07s     Open io.bharti.BullsEye
    t =     0.12s         Launch io.bharti.BullsEye
    t =     0.23s             Setting up automation session
    t =     0.27s             Wait for io.bharti.BullsEye to idle
    t =     1.33s Find the "Slide" Button
    t =     1.38s Checking existence of `"Get as close as you can to: " StaticText`
    t =     1.39s Checking existence of `"Guess where the slider is: " StaticText`
    t =     1.40s Tap "Type" Button
    t =     1.40s     Wait for io.bharti.BullsEye to idle
    t =     1.48s     Find the "Type" Button
    t =     1.50s     Check for interrupting elements affecting "Type" Button
    t =     1.52s     Synthesize event
    t =     1.68s     Wait for io.bharti.BullsEye to idle
    t =     2.09s Checking existence of `"Guess where the slider is: " StaticText`
    t =     2.12s Checking existence of `"Get as close as you can to: " StaticText`
2023-07-06 16:56:00.877 BullsEyeUITests-Runner[6935:250034] Epoch Time for the Test: 1688662560
2023-07-06 16:56:00.877 BullsEyeUITests-Runner[6935:250034] Failing Assertion: : 0
/Users/<USER>/BrowserStack/BullsEyeIosApp/sample-swift-project-with-parallel-ui-test/BullsEyeUITests/BullsEyeUITests.swift:82: error: -[BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch002] : XCTAssertTrue failed
    t =     2.21s Tear Down
2023-07-06 16:56:00.958 BullsEyeUITests-Runner[6935:250034] Triggered teardown Phase at: : 1688662560
    t =     2.21s     Terminate io.bharti.BullsEye:6943
Test Case '-[BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch002]' failed (3.356 seconds).
Test Suite 'RandomlyFailingTest' failed at 2023-07-06 16:56:02.103.
	 Executed 5 tests, with 3 failures (0 unexpected) in 16.615 (16.620) seconds
Test Suite 'BullsEyeUITests.xctest' failed at 2023-07-06 16:56:02.105.
	 Executed 5 tests, with 3 failures (0 unexpected) in 16.615 (16.622) seconds
Test Suite 'Selected tests' failed at 2023-07-06 16:56:02.107.
	 Executed 5 tests, with 3 failures (0 unexpected) in 16.615 (16.625) seconds
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
2023-07-06 16:56:02.128 xcodebuild[48813:16348024] [MT] IDETestOperationsObserverDebug: 18.835 elapsed -- Testing started completed.
2023-07-06 16:56:02.128 xcodebuild[48813:16348024] [MT] IDETestOperationsObserverDebug: 0.000 sec, +0.000 sec -- start
2023-07-06 16:56:02.129 xcodebuild[48813:16348024] [MT] IDETestOperationsObserverDebug: 18.835 sec, +18.835 sec -- end

Test session results, code coverage, and logs:
	/tmp/00008110-000A048C0209801E_xcresult_bundle/BullsEyeUITests/RandomlyFailingTest/testGameStyleSwitch002_1688662541

Failing tests:
	BullsEyeUITests:
		RandomlyFailingTest.testGameStyleSwitch002()

** TEST EXECUTE FAILED **

Testing started