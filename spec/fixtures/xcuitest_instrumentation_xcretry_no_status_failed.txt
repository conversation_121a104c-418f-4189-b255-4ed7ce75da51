Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -xctestrun /tmp/00008020-0015394E3CD3002E_xctestrun.xml -destination id=00008020-0015394E3CD3002E test-without-building -derivedDataPath /tmp/00008020-0015394E3CD3002E_xcuitest_derived_data_1 "-only-testing:BullsEyeUITests/BullsEyeUITests/testGameStyleSwitch001" -retry-tests-on-failure -test-iterations 3

User defaults from command line:
    IDEDerivedDataPathOverride = /tmp/00008020-0015394E3CD3002E_xcuitest_derived_data_1
    IDEPackageSupportUseBuiltinSCM = YES
    XCTHTestRunSpecificationPath = /tmp/00008020-0015394E3CD3002E_xctestrun.xml

Retrying tests on failure. Running tests repeatedly up to 3 times.

2023-04-19 06:37:27.806 BullsEyeUITests-Runner[467:11076] Running tests...
Test Suite 'Selected tests' started at 2023-04-19 06:37:27.906
Test Suite 'BullsEyeUITests.xctest' started at 2023-04-19 06:37:27.907
Test Suite 'BullsEyeUITests' started at 2023-04-19 06:37:27.907
Test Case '-[BullsEyeUITests.BullsEyeUITests testGameStyleSwitch001]' started (Iteration 1 of 3).
    t =     0.00s Start Test at 2023-04-19 06:37:27.908
    t =     0.06s Set Up
2023-04-19 06:37:27.968 BullsEyeUITests-Runner[467:11076] Triggered Setup Phase at: : 1681886247
    t =     0.06s     Open io.manish.BullsEye
    t =     0.12s         Launch io.manish.BullsEye
    t =     0.35s             Setting up automation session
    t =     0.37s             Wait for io.manish.BullsEye to idle
    t =     1.46s Find the "Slide" Button
    t =     1.53s Checking existence of `"Get as close as you can to: " StaticText`
    t =     1.55s Checking existence of `"Guess where the slider is: " StaticText`
    t =     1.58s Tap "Type" Button
    t =     1.58s     Wait for io.manish.BullsEye to idle
    t =     1.66s     Find the "Type" Button
    t =     1.69s     Check for interrupting elements affecting "Type" Button
    t =     1.71s     Synthesize event
    t =     1.90s     Wait for io.manish.BullsEye to idle
    t =     2.31s Checking existence of `"Guess where the slider is: " StaticText`
    t =     2.36s Checking existence of `"Get as close as you can to: " StaticText`
2023-04-19 06:37:30.291 BullsEyeUITests-Runner[467:11076] Epoch Time for the Test: 1681886250
2023-04-19 06:37:30.291 BullsEyeUITests-Runner[467:11076] Failing Assertion: : 0
/Users/<USER>/XCODEProjects/PaysafeIssue/sample-swift-project-with-parallel-ui-test/BullsEyeUITests/BullsEyeUITests.swift:80: error: -[BullsEyeUITests.BullsEyeUITests testGameStyleSwitch001] : XCTAssertTrue failed
    t =     2.49s Tear Down
2023-04-19 06:37:30.395 BullsEyeUITests-Runner[467:11076] Triggered teardown Phase at: : 1681886250
    t =     2.49s     Terminate io.manish.BullsEye:468
Test Case '-[BullsEyeUITests.BullsEyeUITests testGameStyleSwitch001]' failed (3.562 seconds).
Test Case '-[BullsEyeUITests.BullsEyeUITests testGameStyleSwitch001]' started (Iteration 2 of 3).
    t =     0.00s Start Test at 2023-04-19 06:37:31.471
    t =     0.08s Set Up
2023-04-19 06:37:31.553 BullsEyeUITests-Runner[467:11076] Triggered Setup Phase at: : 1681886251
    t =     0.08s     Open io.manish.BullsEye
    t =     0.16s         Launch io.manish.BullsEye
    t =     0.32s             Setting up automation session
    t =     0.34s             Wait for io.manish.BullsEye to idle
    t =     1.44s Find the "Slide" Button
    t =     1.51s Find the "Type" Button
    t =     1.53s Checking existence of `"Guess where the slider is: " StaticText`
    t =     1.55s Checking existence of `"Get as close as you can to: " StaticText`
    t =     1.57s Tap "Slide" Button
    t =     1.57s     Wait for io.manish.BullsEye to idle
    t =     1.66s     Find the "Slide" Button
    t =     1.69s     Check for interrupting elements affecting "Slide" Button
    t =     1.72s     Synthesize event
    t =     1.86s     Wait for io.manish.BullsEye to idle
    t =     2.28s Checking existence of `"Get as close as you can to: " StaticText`
    t =     2.32s Checking existence of `"Guess where the slider is: " StaticText`
2023-04-19 06:37:33.815 BullsEyeUITests-Runner[467:11076] Epoch Time for the Test: 1681886253
2023-04-19 06:37:33.816 BullsEyeUITests-Runner[467:11076] Success Assertion: : 1
    t =     2.35s Tear Down
2023-04-19 06:37:33.817 BullsEyeUITests-Runner[467:11076] Triggered teardown Phase at: : 1681886253
    t =     2.35s     Terminate io.manish.BullsEye:470
Test Suite 'BullsEyeUITests' failed at 2023-04-19 06:37:34.940.
	 Executed 2 tests, with 1 failure (0 unexpected) in 7.030 (7.034) seconds
Test Suite 'BullsEyeUITests.xctest' failed at 2023-04-19 06:37:34.942.
	 Executed 2 tests, with 1 failure (0 unexpected) in 7.030 (7.036) seconds
Test Suite 'Selected tests' failed at 2023-04-19 06:37:34.944.
	 Executed 2 tests, with 1 failure (0 unexpected) in 7.030 (7.038) seconds
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
2023-04-19 06:37:34.976 xcodebuild[52947:1022358196] [MT] IDETestOperationsObserverDebug: 8.133 elapsed -- Testing started completed.
2023-04-19 06:37:34.976 xcodebuild[52947:1022358196] [MT] IDETestOperationsObserverDebug: 0.000 sec, +0.000 sec -- start
2023-04-19 06:37:34.976 xcodebuild[52947:1022358196] [MT] IDETestOperationsObserverDebug: 8.133 sec, +8.133 sec -- end

Test session results, code coverage, and logs:
	/tmp/00008020-0015394E3CD3002E_xcuitest_derived_data_1/Logs/Test/Test-Transient Testing-2023.04.19_06-37-26-+0000.xcresult

Testing started