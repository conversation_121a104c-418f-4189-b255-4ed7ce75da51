2020-10-02 09:03:33:455 - [debug] [iProxy@00008030:8403] Connection was refused to port 8403
2020-10-02 09:03:33:456 - [WD Proxy] Got response with unknown status: {"code":"ECONNRESET"}
2020-10-02 09:03:33:948 - [debug] [BaseDriver] Event 'wdaSessionAttempted' logged at 1601629413948 (09:03:33 GMT+0000 (Coordinated Universal Time))
2020-10-02 09:03:33:948 - [debug] [XCUITest] Sending createSession command to WDA
2020-10-02 09:03:33:949 - [debug] [XCUITest] Failed to create WDA session (Cannot call proxyCommand without WDA driver active). Retrying...
2020-10-02 09:03:34:457 - [debug] [WD Proxy] Matched '/status' to command name 'getStatus'
2020-10-02 09:03:34:457 - [debug] [WD Proxy] Proxying [GET /status] to [GET http://127.0.0.1:8403/status] with no body
2020-10-02 09:03:34:462 - [debug] [iProxy@00008030:8403] Connection was refused to port 8403
2020-10-02 09:03:34:462 - [WD Proxy] Got response with unknown status: {"code":"ECONNRESET"}
2020-10-02 09:03:34:948 - [debug] [BaseDriver] Event 'wdaSessionAttempted' logged at 1601629414948 (09:03:34 GMT+0000 (Coordinated Universal Time))
2020-10-02 09:03:34:948 - [debug] [XCUITest] Sending createSession command to WDA
2020-10-02 09:03:34:949 - [debug] [XCUITest] Failed to create WDA session (Cannot call proxyCommand without WDA driver active). Retrying...
2020-10-02 09:03:35:559 - [debug] [WD Proxy] Matched '/status' to command name 'getStatus'
2020-10-02 09:03:35:559 - [debug] [WD Proxy] Proxying [GET /status] to [GET http://127.0.0.1:8403/status] with no body
2020-10-02 09:03:35:566 - [debug] [iProxy@00008030:8403] Connection was refused to port 8403
2020-10-02 09:03:35:567 - [WD Proxy] Got response with unknown status: {"code":"ECONNRESET"}
2020-10-02 09:03:35:952 - [debug] [BaseDriver] Event 'wdaSessionAttempted' logged at 1601629415952 (09:03:35 GMT+0000 (Coordinated Universal Time))
2020-10-02 09:03:35:952 - [debug] [XCUITest] Sending createSession command to WDA
2020-10-02 09:03:35:952 - [debug] [XCUITest] Failed to create WDA session (Cannot call proxyCommand without WDA driver active). Retrying...
2020-10-02 09:03:36:317 - [Xcode] 2020-10-02 09:03:36.317 xcodebuild[51733:80762830] Error Domain=com.apple.platform.iphoneos Code=-12 "Unable to launch com.facebook.WebDriverAgentRunner.xctrunner" UserInfo={NSLocalizedDescription=Unable to launch com.facebook.WebDriverAgentRunner.xctrunner, NSUnderlyingError=0x7f86e589ffe0 {Error Domain=com.apple.dt.deviceprocesscontrolservice Code=2 "Request to launch com.facebook.WebDriverAgentRunner.xctrunner failed." UserInfo={NSLocalizedFailureReason=The operation couldn’t be completed. Unable to launch com.facebook.WebDriverAgentRunner.xctrunner because it has an invalid code signature, inadequate entitlements or its profile has not been explicitly trusted by the user. : Failed to launch process with bundle identifier 'com.facebook.WebDriverAgentRunner.xctrunner'., NSLocalizedDescription=Request to launch com.facebook.WebDriverAgentRunner.xctrunner failed., NSUnderlyingError=0x7f86e7c81af0 {Error Domain=FBSOpenApplicationErrorDomain Code=3 "Unable to launch com.facebook.WebDriverAgentRunner.xctrunner because it has an invalid code signature, inadequate entitlements or its profile has not been explicitly trusted by the user." UserInfo={BSErrorCodeDescription=Security, NSLocalizedFailureReason=Unable to launch com.facebook.WebDriverAgentRunner.xctrunner because it has an invalid code signature, inadequate entitlements or its profile has not been explicitly trusted by the user.}}}}}
2020-10-02 09:03:36:317 - [Xcode]
2020-10-02 09:03:36:357 - [Xcode]
2020-10-02 09:03:36:357 - [Xcode] Test session results, code coverage, and logs:
2020-10-02 09:03:36:357 - [Xcode]       /Users/<USER>/Library/Developer/Xcode/DerivedData/temporary-ccapwluujdvtbubmsrkuvtdcxbee/Logs/Test/Test-Transient Testing-2020.10.02_09-03-28-+0000.xcresult
2020-10-02 09:03:36:357 - [Xcode]
2020-10-02 09:03:36:357 - [Xcode]
2020-10-02 09:03:36:358 - [Xcode] Testing started on 'iPhone'
2020-10-02 09:03:36:358 - [Xcode]
2020-10-02 09:03:36:358 - [Xcode] Testing failed:
2020-10-02 09:03:36:358 - [Xcode]       WebDriverAgentRunner:
2020-10-02 09:03:36:358 - [Xcode]               WebDriverAgentRunner-Runner.app encountered an error (Failed to install or launch the test runner. (Underlying error: Unable to launch com.facebook.WebDriverAgentRunner.xctrunner. (Underlying error: Request to launch com.facebook.WebDriverAgentRunner.xctrunner failed. The operation couldn’t be completed. Unable to launch com.facebook.WebDriverAgentRunner.xctrunner because it has an invalid code signature, inadequate entitlements or its profile has not been explicitly trusted by the user. : Failed to launch process with bundle identifier 'com.facebook.WebDriverAgentRunner.xctrunner'. (Underlying error: The operation couldn’t be completed. Unable to launch com.facebook.WebDriverAgentRunner.xctrunner because it has an invalid code signature, inadequate entitlements or its profile has not been explicitly trusted by the user.))))
2020-10-02 09:03:36:358 - [Xcode]
2020-10-02 09:03:36:358 - [Xcode] ** TEST EXECUTE FAILED **
2020-10-02 09:03:36:358 - [Xcode]
2020-10-02 09:03:36:358 - [Xcode]
2020-10-02 09:03:36:366 - [WebDriverAgent] xcodebuild exited with code '65' and signal 'null'