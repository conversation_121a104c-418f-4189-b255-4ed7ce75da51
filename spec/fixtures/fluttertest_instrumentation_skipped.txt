Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -xctestrun /tmp/00008101-001258CC1A00001E_xctestrun.xml -destination id=00008101-001258CC1A00001E test-without-building -derivedDataPath /tmp/00008101-001258CC1A00001E_xcuitest_derived_data_1

User defaults from command line:
    IDEDerivedDataPathOverride = /tmp/00008101-001258CC1A00001E_xcuitest_derived_data_1
    IDEPackageSupportUseBuiltinSCM = YES

2023-08-24 07:33:55.205 xcodebuild[46897:2498279820]  DVTDownloadable: Unexpected error attempting to parse downloadable from index: Error Domain=DVTDownloadableErrors Code=16 "Unable to read in downloadable data. See underlying errors for details." UserInfo={DownloadablePlist={
    authentication = virtual;
    category = simulator;
    contentType = diskImage;
    dictionaryVersion = 2;
    fileSize = 7354535384;
    hostRequirements =     {
        minHostVersion = "13.4";
        minXcodeVersion = "15.0";
    };
    identifier = "com.apple.dmg.xrSimulatorSDK1_0_b1";
    name = "xrOS 1 beta Simulator Runtime";
    platform = "com.apple.platform.xros";
    simulatorVersion =     {
        buildUpdate = 21N5165g;
        version = "1.0";
    };
    source = "https://download.developer.apple.com/Developer_Tools/visionOS_1_beta/visionOS_1_beta_Simulator_Runtime.dmg";
    version = "*******";
}, NSLocalizedDescription=Unable to read in downloadable data. See underlying errors for details., DVTUnderlyingErrorsKey=(
    "Error Domain=DVTDownloadableErrors Code=23 \"'com.apple.platform.xros' is an unknown platform identifier.\" UserInfo={NSLocalizedDescription='com.apple.platform.xros' is an unknown platform identifier.}"
)}
2023-08-24 07:33:55.205 xcodebuild[46897:2498279820]  DVTDownloadable: Unexpected error attempting to parse downloadable from index: Error Domain=DVTDownloadableErrors Code=16 "Unable to read in downloadable data. See underlying errors for details." UserInfo={DownloadablePlist={
    authentication = virtual;
    category = simulator;
    contentType = diskImage;
    dictionaryVersion = 2;
    fileSize = 7220726735;
    hostRequirements =     {
        minHostVersion = "13.4";
        minXcodeVersion = "15.0";
    };
    identifier = "com.apple.dmg.xrSimulatorSDK1_0_b2";
    name = "xrOS 2 beta Simulator Runtime";
    platform = "com.apple.platform.xros";
    simulatorVersion =     {
        buildUpdate = 21N5207f;
        version = "1.0";
    };
    source = "https://download.developer.apple.com/Developer_Tools/visionOS_1_beta_2_Simulator_Runtime/visionOS_1_beta_2_Simulator_Runtime.dmg";
    version = "*******";
}, NSLocalizedDescription=Unable to read in downloadable data. See underlying errors for details., DVTUnderlyingErrorsKey=(
    "Error Domain=DVTDownloadableErrors Code=23 \"'com.apple.platform.xros' is an unknown platform identifier.\" UserInfo={NSLocalizedDescription='com.apple.platform.xros' is an unknown platform identifier.}"
)}
2023-08-24 07:34:00.136723+0000 Runner[5326:429837] [VERBOSE-2:FlutterDarwinContextMetalImpeller.mm(37)] Using the Impeller rendering backend.
2023-08-24 07:34:00.142479+0000 Runner[5326:429837] Warning: Unable to create restoration in progress marker file
2023-08-24 07:34:00.153819+0000 Runner[5326:430164] flutter: 00:00 +0: start-to-end test Addition test
2023-08-24 07:34:00.153929+0000 Runner[5326:430164] flutter: 00:00 +1: (tearDownAll)
2023-08-24 07:34:00.166139+0000 Runner[5326:430164] flutter: 00:00 +2: All tests passed!
Test Suite 'All tests' started at 2023-08-24 07:34:00.190
Test Suite 'All tests' passed at 2023-08-24 07:34:00.191.
	 Executed 0 tests, with 0 failures (0 unexpected) in 0.000 (0.001) seconds
2023-08-24 07:34:00.279 xcodebuild[46897:2498279790] [MT] IDETestOperationsObserverDebug: 1.367 elapsed -- Testing started completed.
2023-08-24 07:34:00.279 xcodebuild[46897:2498279790] [MT] IDETestOperationsObserverDebug: 0.000 sec, +0.000 sec -- start
2023-08-24 07:34:00.279 xcodebuild[46897:2498279790] [MT] IDETestOperationsObserverDebug: 1.367 sec, +1.367 sec -- end

Test session results, code coverage, and logs:
	/tmp/00008101-001258CC1A00001E_xcuitest_derived_data_1/Logs/Test/Test-Transient Testing-2023.08.24_07-33-58-+0000.xcresult

** TEST EXECUTE SUCCEEDED **

Testing started