Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -xctestrun /tmp/00008120-00145C6C3AA2201E_xctestrun.xctestrun -destination id=00008120-00145C6C3AA2201E test-without-building -derivedDataPath /tmp/00008120-00145C6C3AA2201E_xcuitest_derived_data_2 "-only-testing:TIDAL-UITests/TIDALUITestsBlock/test_UserCanBlockAndUnblockArtistFromVideo"

User defaults from command line:
    IDEDerivedDataPathOverride = /tmp/00008120-00145C6C3AA2201E_xcuitest_derived_data_2
    IDEPackageSupportUseBuiltinSCM = YES

Test Suite 'Selected tests' started at 2024-09-11 18:30:02.614.
Test Suite 'TIDAL-UITests.xctest' started at 2024-09-11 18:30:02.614.
Test Suite 'TIDALUITestsBlock' started at 2024-09-11 18:30:02.615.
Test Case '-[AWSDeviceFarmiOSReferenceAppUITestsSwift.WebViewTest testSSLCert]' started.
    t =     0.00s Start Test at 2024-09-11 18:30:02.615
    t =     0.01s Set Up
    t =     0.02s     Open com.aspiro.TIDAL
    t =     0.02s         Launch com.aspiro.TIDAL
    t =     0.09s             Setting up automation session
    t =     0.15s             Wait for com.aspiro.TIDAL to idle
    t =    62.65s Tap "myCollectionTabbarItem" Button
    t =    62.65s     Wait for com.aspiro.TIDAL to idle
    t =    62.68s     Find the "myCollectionTabbarItem" Button
    t =    62.73s     Check for interrupting elements affecting "myCollectionTabbarItem" Button
    t =    62.78s     Synthesize event
    t =    63.08s     Wait for com.aspiro.TIDAL to idle
    t =    63.43s Find the "myCollectionTabbarItem" Button
    t =    63.50s Tear Down
    t =    63.50s     Terminate com.aspiro.TIDAL:4089
Test Case '-[AWSDeviceFarmiOSReferenceAppUITestsSwift.WebViewTest testSSLCert]' failed (64.594 seconds).
Test Suite 'TIDALUITestsBlock' passed at 2024-09-11 18:31:07.209.
	 Executed 1 test, with 1 failures (0 unexpected) in 64.594 (64.595) seconds
Test Suite 'TIDAL-UITests.xctest' passed at 2024-09-11 18:31:07.212.
	 Executed 1 test, with 1 failures (0 unexpected) in 64.594 (64.598) seconds
Test Suite 'Selected tests' passed at 2024-09-11 18:31:07.214.
	 Executed 1 test, with 1 failures (0 unexpected) in 64.594 (64.600) seconds
Test Case '-[AWSDeviceFarmiOSReferenceAppUITestsSwift.WebViewTest testSSLCert]' started.
    t =     0.00s Start Test at 2024-09-11 18:30:02.615
    t =     0.01s Set Up
    t =     0.02s     Open com.aspiro.TIDAL
    t =     0.02s         Launch com.aspiro.TIDAL
    t =     0.09s             Setting up automation session
    t =     0.15s             Wait for com.aspiro.TIDAL to idle
    t =    62.65s Tap "myCollectionTabbarItem" Button
    t =    62.65s     Wait for com.aspiro.TIDAL to idle
    t =    62.68s     Find the "myCollectionTabbarItem" Button
    t =    62.73s     Check for interrupting elements affecting "myCollectionTabbarItem" Button
    t =    62.78s     Synthesize event
    t =    63.08s     Wait for com.aspiro.TIDAL to idle
    t =    63.43s Find the "myCollectionTabbarItem" Button
    t =    63.50s Tear Down
    t =    63.50s     Terminate com.aspiro.TIDAL:4089
Test Case '-[AWSDeviceFarmiOSReferenceAppUITestsSwift.WebViewTest testSSLCert]' passed (64.594 seconds).
Test Suite 'TIDALUITestsBlock' passed at 2024-09-11 18:31:07.209.
	 Executed 1 test, with 0 failures (0 unexpected) in 64.594 (64.595) seconds
Test Suite 'TIDAL-UITests.xctest' passed at 2024-09-11 18:31:07.212.
	 Executed 1 test, with 0 failures (0 unexpected) in 64.594 (64.598) seconds
Test Suite 'Selected tests' passed at 2024-09-11 18:31:07.214.
	 Executed 1 test, with 0 failures (0 unexpected) in 64.594 (64.600) seconds
_Concurrency/CheckedContinuation.swift:187: Fatal error: SWIFT TASK CONTINUATION MISUSE: transferFile(to:fromPathOnRemoteDevice:in:forUsername:) tried to resume its continuation more than once, throwing CoreDeviceError(errorCode: 3, errorUserInfo: ["NSUnderlyingError": Mercury.XPCError(errorCode: 1000, errorUserInfo: ["XPCConnectionDescription": "<SystemXPCPeerConnection 0x60000014c000> { <connection: 0x6000032ec1e0> { name = com.apple.CoreDevice.CoreDeviceService, listener = false, pid = 0, euid = 4294967295, egid = 4294967295, asid = 4294967295 } }", "NSLocalizedDescription": "The connection was interrupted."]), "NSLocalizedDescription": "An error occurred while communicating with a remote process."])!
