User defaults from command line:
    IDEDerivedDataPathOverride = /tmp/00008020-000619E914D2002E_xcuitest_derived_data_1
    IDETestRunOnlyIdentifiers = (
    "AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest/testEnvVariable"
)
    IDETestRunSpecificationPath = /tmp/00008020-000619E914D2002E_xctestrun.xml

2020-11-20 20:49:35.499 xcodebuild[53046:2893761]  IDETestOperationsObserverDebug: Writing diagnostic log for test session to:
/tmp/00008020-000619E914D2002E_xcuitest_derived_data_1/Logs/Test/Test-Transient Testing-2020.11.20_20-49-35-+0000.xcresult/1_Test/Diagnostics/AWSDeviceFarmiOSReferenceAppUITestsSwift-0DE62D89-8CF7-4345-ADEC-307C9A5AE385/AWSDeviceFarmiOSReferenceAppUITestsSwift-FBA57609-5431-483A-A782-4E255A96E541/Session-AWSDeviceFarmiOSReferenceAppUITestsSwift-2020-11-20_204935-tk6D21.log
2020-11-20 20:49:35.500 xcodebuild[53046:2893526] [MT] IDETestOperationsObserverDebug: (E98F9126-964F-47B1-87FC-7A9D6332A5E7) Beginning test session AWSDeviceFarmiOSReferenceAppUITestsSwift-E98F9126-964F-47B1-87FC-7A9D6332A5E7 at 2020-11-20 20:49:35.499 with Xcode 10B61 on target 📱<DVTiOSDevice (0x7f91845b7570), iPhone, iPhone, 12.1 (16B92), 00008020-000619E914D2002E> {
		deviceSerialNumber:         FFWXJ3VHKPH2
		identifier:                 00008020-000619E914D2002E
		deviceClass:                iPhone
		deviceName:                 iPhone
		deviceIdentifier:           00008020-000619E914D2002E
		productVersion:             12.1
		buildVersion:               16B92
		deviceSoftwareVersion:      12.1 (16B92)
		deviceArchitecture:         arm64e
		deviceTotalCapacity:        59481223168
		deviceAvailableCapacity:    55073615872
		deviceIsTransient:          NO
		ignored:                    NO
		deviceIsBusy:               NO
		deviceIsPaired:             YES
		deviceIsActivated:          YES
		deviceActivationState:      Activated
		isPasscodeLocked:           NO
		deviceType:                 <DVTDeviceType:0x7f918465c9b0 Xcode.DeviceType.iPhone>
		supportedDeviceFamilies:    (
    1
)
		applications:              (null)
		provisioningProfiles:      (null)
		hasInternalSupport:        NO
		isSupportedOS:             YES
		developerDiskMountError:   (null)
(null)
	bootArgs:                  (null)
		connected:                 yes
		isWirelessEnabled:         no
		connectionType:            direct
		hostname:                  (null)
		bonjourServiceName:        e0:33:8e:d1:e4:f7@fe80::e233:8eff:fed1:e4f7._apple-mobdev2._tcp.local.
		} (12.1 (16B92))
2020-11-20 20:49:39.221 AWSDeviceFarmiOSReferenceAppUITestsSwift-Runner[2004:193797] Running tests...
Test Suite 'Selected tests' started at 2020-11-20 20:49:39.375
Test Suite 'AWSDeviceFarmiOSReferenceAppUITestsSwift.xctest' started at 2020-11-20 20:49:39.375
Test Suite 'WebViewTest' started at 2020-11-20 20:49:39.375
Test Case '-[AWSDeviceFarmiOSReferenceAppUITestsSwift.WebViewTest testEnvVariable]' started.
    t =     0.00s Start Test at 2020-11-20 20:49:39.376
    t =     0.02s Set Up
    t =     0.02s     Open Amazon.AWSDeviceFarmiOSReferenceApp.bstack
    t =     0.03s         Launch Amazon.AWSDeviceFarmiOSReferenceApp.bstack
    t =     0.28s             Wait for Amazon.AWSDeviceFarmiOSReferenceApp.bstack to idle
    t =     1.38s     Tap "HTTP" Button
    t =     1.38s         Wait for Amazon.AWSDeviceFarmiOSReferenceApp.bstack to idle
    t =     1.43s         Find the "HTTP" Button
    t =     1.43s             Requesting snapshot of accessibility hierarchy for app with pid 2008
    t =     1.49s             Find: Descendants matching type TabBar
    t =     1.49s             Find: Descendants matching type Button
    t =     1.49s             Find: Elements matching predicate '"HTTP" IN identifiers'
    t =     1.50s         Check for interrupting elements affecting "HTTP" Button
    t =     1.50s         Synthesize event
    t =     1.61s         Wait for Amazon.AWSDeviceFarmiOSReferenceApp.bstack to idle
Fatal error: Unexpectedly found nil while unwrapping an Optional value: file /Users/<USER>/Downloads/aws-device-farm-xctest-ui-tests-for-ios-sample-app/AWSDeviceFarmiOSReferenceAppUITestsSwift/Web/WebViewTest.swift, line 91
2020-11-20 20:49:48.536 xcodebuild[53046:2893526] [MT] IDETestOperationsObserverDebug: 13.047 elapsed -- Testing started completed.
2020-11-20 20:49:48.536 xcodebuild[53046:2893526] [MT] IDETestOperationsObserverDebug: 0.000 sec, +0.000 sec -- start
2020-11-20 20:49:48.536 xcodebuild[53046:2893526] [MT] IDETestOperationsObserverDebug: 13.047 sec, +13.047 sec -- end
2020-11-20 20:49:48.537 xcodebuild[53046:2893526] Error Domain=IDETestOperationsObserverErrorDomain Code=5 "Test runner exited." UserInfo={NSLocalizedDescription=Test runner exited., NSLocalizedRecoverySuggestion=If you believe this error represents a bug, please attach the result bundle at /tmp/00008020-000619E914D2002E_xcuitest_derived_data_1/Logs/Test/Test-Transient Testing-2020.11.20_20-49-35-+0000.xcresult}

Testing failed:
	testEnvVariable() encountered an error (Test runner exited.)
** TEST EXECUTE FAILED **

Testing started on 'iPhone'