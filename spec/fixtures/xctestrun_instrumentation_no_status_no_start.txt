Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -xctestrun /tmp/00008110-0012719634F8801E_xctestrun.xml -destination id=00008110-0012719634F8801E test-without-building -derivedDataPath /tmp/00008110-0012719634F8801E_xcuitest_derived_data_1 "-only-testing:BullsEyeUITests/AlwaysFailingTest/testGameStyleSwitch" -test-iterations 5 -resultBundlePath /tmp/00008110-0012719634F8801E_xcresult_bundle/BullsEyeUITests/AlwaysFailingTest/testGameStyleSwitch_1688662477 -resultBundleVersion 3

User defaults from command line:
    IDEBuildOperationResultBundlePath = /tmp/00008110-0012719634F8801E_xcresult_bundle/BullsEyeUITests/AlwaysFailingTest/testGameStyleSwitch_1688662477
    IDEDerivedDataPathOverride = /tmp/00008110-0012719634F8801E_xcuitest_derived_data_1
    IDEPackageSupportUseBuiltinSCM = YES
    IDERequestedResultBundleFormatVersion = 3
    XCTHTestRunSpecificationPath = /tmp/00008110-0012719634F8801E_xctestrun.xml

Running tests repeatedly 5 times.

Writing result bundle at path:
	/tmp/00008110-0012719634F8801E_xcresult_bundle/BullsEyeUITests/AlwaysFailingTest/testGameStyleSwitch_1688662477

2023-07-06 16:54:43.733 BullsEyeUITests-Runner[3947:178324] Running tests...
2023-07-06 16:54:43.792 BullsEyeUITests-Runner[3947:178324] Test bundle Info.plist at /var/containers/Bundle/Application/D80FBF0B-8249-426F-8544-EEDB7518AB52/BullsEyeUITests-Runner.app/PlugIns/BullsEyeUITests.xctest/Info.plist specified BullsEyeUITests.CustomTestObserver for NSPrincipalClass, but no class matching that name was found.
Test Suite 'Selected tests' started at 2023-07-06 16:54:43.801
Test Suite 'BullsEyeUITests.xctest' started at 2023-07-06 16:54:43.801
Test Suite 'AlwaysFailingTest' started at 2023-07-06 16:54:43.802
    t =     0.00s Start Test at 2023-07-06 16:54:43.802
    t =     0.04s Set Up
    t =     0.04s     Open io.bharti.BullsEye
    t =     0.08s         Launch io.bharti.BullsEye
    t =     0.22s             Setting up automation session
    t =     0.27s             Wait for io.bharti.BullsEye to idle
    t =     1.34s Find the "Slide" Button
    t =     1.39s Checking existence of `"Get as close as you can to: " StaticText`
/Users/<USER>/BrowserStack/BullsEyeIosApp/sample-swift-project-with-parallel-ui-test/BullsEyeUITests/BullsEyeUITests.swift:461: error: -[BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch] : XCTAssertFalse failed
    t =     1.49s Tear Down
Test Case '-[BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch]' failed (1.696 seconds).
Test Case '-[BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch01]' started (Iteration 2 of 5).
    t =     0.00s Start Test at 2023-07-06 16:54:45.500
    t =     0.07s Set Up
    t =     0.07s     Open io.bharti.BullsEye
    t =     0.13s         Launch io.bharti.BullsEye
    t =     0.13s             Terminate io.bharti.BullsEye:3948
    t =     1.30s             Setting up automation session
    t =     1.32s             Wait for io.bharti.BullsEye to idle
    t =     2.39s Find the "Slide" Button
    t =     2.44s Checking existence of `"Get as close as you can to: " StaticText`
/Users/<USER>/BrowserStack/BullsEyeIosApp/sample-swift-project-with-parallel-ui-test/BullsEyeUITests/BullsEyeUITests.swift:461: error: -[BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch] : XCTAssertFalse failed
    t =     2.54s Tear Down
Test Case '-[BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch01]' failed (1.66 seconds).
Test Case '-[BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch02]' started (Iteration 1 of 5).
    t =     0.00s Start Test at 2023-07-06 16:54:45.500
    t =     0.07s Set Up
    t =     0.07s     Open io.bharti.BullsEye
    t =     0.13s         Launch io.bharti.BullsEye
    t =     0.13s             Terminate io.bharti.BullsEye:3948
    t =     1.30s             Setting up automation session
    t =     1.32s             Wait for io.bharti.BullsEye to idle
    t =     2.39s Find the "Slide" Button
    t =     2.44s Checking existence of `"Get as close as you can to: " StaticText`
/Users/<USER>/BrowserStack/BullsEyeIosApp/sample-swift-project-with-parallel-ui-test/BullsEyeUITests/BullsEyeUITests.swift:461: error: -[BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch] : XCTAssertFalse failed
    t =     2.54s Tear Down
    t =     0.00s Start Test at 2023-07-06 16:54:48.246
    t =     0.08s Set Up
    t =     0.08s     Open io.bharti.BullsEye
    t =     0.13s         Launch io.bharti.BullsEye
    t =     0.13s             Terminate io.bharti.BullsEye:3950
    t =     1.31s             Setting up automation session
    t =     1.33s             Wait for io.bharti.BullsEye to idle
    t =     2.41s Find the "Slide" Button
    t =     2.46s Checking existence of `"Get as close as you can to: " StaticText`
/Users/<USER>/BrowserStack/BullsEyeIosApp/sample-swift-project-with-parallel-ui-test/BullsEyeUITests/BullsEyeUITests.swift:461: error: -[BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch] : XCTAssertFalse failed
    t =     2.55s Tear Down
Test Case '-[BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch03]' failed (2.755 seconds).
Test Case '-[BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch04]' started (Iteration 1 of 5).
    t =     0.00s Start Test at 2023-07-06 16:54:50.500
    t =     0.07s Set Up
    t =     0.07s     Open io.bharti.BullsEye
    t =     0.13s         Launch io.bharti.BullsEye
    t =     0.13s             Terminate io.bharti.BullsEye:3948
    t =     1.30s             Setting up automation session
    t =     1.32s             Wait for io.bharti.BullsEye to idle
    t =     2.39s Find the "Slide" Button
    t =     2.44s Checking existence of `"Get as close as you can to: " StaticText`
/Users/<USER>/BrowserStack/BullsEyeIosApp/sample-swift-project-with-parallel-ui-test/BullsEyeUITests/BullsEyeUITests.swift:461: error: -[BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch] : XCTAssertFalse failed
    t =     2.54s Tear Down
Test Case '-[BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch05]' started (Iteration 1 of 5).
    t =     0.00s Start Test at 2023-07-06 16:59:50.500
    t =     0.07s Set Up
    t =     0.07s     Open io.bharti.BullsEye
    t =     0.13s         Launch io.bharti.BullsEye
    t =     0.13s             Terminate io.bharti.BullsEye:3948
    t =     1.30s             Setting up automation session
    t =     1.32s             Wait for io.bharti.BullsEye to idle
    t =     2.39s Find the "Slide" Button
    t =     2.44s Checking existence of `"Get as close as you can to: " StaticText`
/Users/<USER>/BrowserStack/BullsEyeIosApp/sample-swift-project-with-parallel-ui-test/BullsEyeUITests/BullsEyeUITests.swift:461: error: -[BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch] : XCTAssertFalse failed
    t =     2.54s Tear Down
Test Case '-[BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch05]' failed (1.66 seconds).
Test Case '-[BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch06]' started (Iteration 1 of 5).
    t =     0.07s Set Up
    t =     0.07s     Open io.bharti.BullsEye
    t =     0.13s         Launch io.bharti.BullsEye
    t =     0.13s             Terminate io.bharti.BullsEye:3948
    t =     1.30s             Setting up automation session
    t =     1.32s             Wait for io.bharti.BullsEye to idle
    t =     2.39s Find the "Slide" Button
    t =     2.44s Checking existence of `"Get as close as you can to: " StaticText`
/Users/<USER>/BrowserStack/BullsEyeIosApp/sample-swift-project-with-parallel-ui-test/BullsEyeUITests/BullsEyeUITests.swift:461: error: -[BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch] : XCTAssertFalse failed
    t =     2.54s Tear Down
Test Case '-[BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch06]' failed (1.66 seconds).


    t =     0.07s Set Up
    t =     0.07s     Open io.bharti.BullsEye
    t =     0.13s         Launch io.bharti.BullsEye
    t =     0.13s             Terminate io.bharti.BullsEye:3948
    t =     1.30s             Setting up automation session
    t =     1.32s             Wait for io.bharti.BullsEye to idle
    t =     2.39s Find the "Slide" Button
    t =     2.44s Checking existence of `"Get as close as you can to: " StaticText`
/Users/<USER>/BrowserStack/BullsEyeIosApp/sample-swift-project-with-parallel-ui-test/BullsEyeUITests/BullsEyeUITests.swift:461: error: -[BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch] : XCTAssertFalse failed
    t =     2.54s Tear Down
Test Case '-[BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch07]' failed (1.66 seconds).

Test Suite 'AlwaysFailingTest' failed at 2023-07-06 16:59:59.000.
	 Executed 5 tests, with 5 failures (0 unexpected) in 12.754 (12.759) seconds
Test Suite 'BullsEyeUITests.xctest' failed at 2023-07-06 16:54:56.562.
	 Executed 5 tests, with 5 failures (0 unexpected) in 12.754 (12.761) seconds
Test Suite 'Selected tests' failed at 2023-07-06 16:54:56.564.
	 Executed 5 tests, with 5 failures (0 unexpected) in 12.754 (12.763) seconds
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
2023-07-06 16:54:56.480 xcodebuild[80045:380259902] [MT] IDETestOperationsObserverDebug: 15.424 elapsed -- Testing started completed.
2023-07-06 16:54:56.481 xcodebuild[80045:380259902] [MT] IDETestOperationsObserverDebug: 0.000 sec, +0.000 sec -- start
2023-07-06 16:54:56.481 xcodebuild[80045:380259902] [MT] IDETestOperationsObserverDebug: 15.424 sec, +15.424 sec -- end

Test session results, code coverage, and logs:
	/tmp/00008110-0012719634F8801E_xcresult_bundle/BullsEyeUITests/AlwaysFailingTest/testGameStyleSwitch_1688662477

Failing tests:
	BullsEyeUITests:
		AlwaysFailingTest.testGameStyleSwitch()

** TEST EXECUTE FAILED **

Testing started