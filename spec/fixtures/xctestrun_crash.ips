-------------------------------------
Translated Report (Full Report Below)
-------------------------------------

Incident Identifier: 073E7658-3A0A-45D4-A2DA-317275FFAB58
CrashReporter Key:   dedd649bf307bf2dea997e733d22721d2c5fcae9
Hardware Model:      iPad14,2
Process:             AWSDeviceFarmiOSReferenceApp [1822]
Path:                /private/var/containers/Bundle/Application/B88D471A-D62E-414D-BAF0-06C2209B1B56/AWSDeviceFarmiOSReferenceApp.app/AWSDeviceFarmiOSReferenceApp
Identifier:          AWSDeviceFarmiOSReferenceApp.xcuitests
Version:             1.0 (1)
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd [1]
Coalition:           AWSDeviceFarmiOSReferenceApp.xcuitests [873]

Date/Time:           2023-12-16 00:02:14.7024 +0000
Launch Time:         2025-05-01 02:21:11.4298 +0000
OS Version:          iPhone OS 15.5 (19F77)
Release Type:        User
Baseband Version:    1.61.00
Report Version:      104

Exception Type:  EXC_BREAKPOINT (SIGTRAP)
Exception Codes: 0x0000000000000001, 0x000000010079f9c4
Exception Note:  EXC_CORPSE_NOTIFY
Termination Reason: SIGNAL 5 Trace/BPT trap: 5
Terminating Process: exc handler [1822]

Triggered by Thread:  0

Thread 0 name:   Dispatch queue: com.apple.main-thread
Thread 0 Crashed:
0   AWSDeviceFarmiOSReferenceApp  	       0x10079f9c4 0x100794000 + 47556
1   UIKitCore                     	       0x18381b9bc -[UIApplication sendAction:to:from:forEvent:] + 100
2   UIKitCore                     	       0x183946374 -[UIControl sendAction:to:forEvent:] + 128
3   UIKitCore                     	       0x1836c3368 -[UIControl _sendActionsForEvents:withEvent:] + 356
4   UIKitCore                     	       0x183760078 -[UIButton _sendActionsForEvents:withEvent:] + 160
5   UIKitCore                     	       0x1839f152c -[UIControl touchesEnded:withEvent:] + 520
6   UIKitCore                     	       0x1834c85ec -[UIWindow _sendTouchesForEvent:] + 980
7   UIKitCore                     	       0x1834f9870 -[UIWindow sendEvent:] + 4408
8   UIKitCore                     	       0x1836a7288 -[UIApplication sendEvent:] + 824
9   UIKit                         	       0x1f113f4bc -[UIApplicationAccessibility sendEvent:] + 100
10  UIKitCore                     	       0x1834cd150 __dispatchPreprocessedEventFromEventQueue + 7856
11  UIKitCore                     	       0x1834c1ea8 __processEventQueue + 6616
12  UIKitCore                     	       0x1843122e0 updateCycleEntry + 176
13  UIKitCore                     	       0x183b2f97c _UIUpdateSequenceRun + 84
14  UIKitCore                     	       0x1841b5c48 schedulerStepScheduledMainSection + 144
15  UIKitCore                     	       0x1841b5410 runloopSourceCallback + 92
16  CoreFoundation                	       0x180f98414 __CFRUNLOOP_IS_CALLING_OUT_TO_A_SOURCE0_PERFORM_FUNCTION__ + 28
17  CoreFoundation                	       0x180fa91a0 __CFRunLoopDoSource0 + 208
18  CoreFoundation                	       0x180ee2694 __CFRunLoopDoSources0 + 268
19  CoreFoundation                	       0x180ee805c __CFRunLoopRun + 828
20  CoreFoundation                	       0x180efbbc8 CFRunLoopRunSpecific + 600
21  GraphicsServices              	       0x19d02f374 GSEventRunModal + 164
22  UIKitCore                     	       0x18386b648 -[UIApplication _run] + 1100
23  UIKitCore                     	       0x1835ecd90 UIApplicationMain + 364
24  AWSDeviceFarmiOSReferenceApp  	       0x1007a1f28 0x100794000 + 57128
25  dyld                          	       0x1009edce4 start + 520

Thread 1:
0   libsystem_pthread.dylib       	       0x1f0ed3e54 start_wqthread + 0

Thread 2 name:  com.apple.uikit.eventfetch-thread
Thread 2:
0   libsystem_kernel.dylib        	       0x1b7d884a0 mach_msg_trap + 8
1   libsystem_kernel.dylib        	       0x1b7d88ae4 mach_msg + 76
2   CoreFoundation                	       0x180ee3d30 __CFRunLoopServiceMachPort + 372
3   CoreFoundation                	       0x180ee81bc __CFRunLoopRun + 1180
4   CoreFoundation                	       0x180efbbc8 CFRunLoopRunSpecific + 600
5   Foundation                    	       0x1826ea464 -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 236
6   Foundation                    	       0x18272be2c -[NSRunLoop(NSRunLoop) runUntilDate:] + 92
7   UIKitCore                     	       0x1837e5770 -[UIEventFetcher threadMain] + 524
8   Foundation                    	       0x18273a43c __NSThread__start__ + 808
9   libsystem_pthread.dylib       	       0x1f0ed49ac _pthread_start + 148
10  libsystem_pthread.dylib       	       0x1f0ed3e68 thread_start + 8

Thread 3:
0   libsystem_pthread.dylib       	       0x1f0ed3e54 start_wqthread + 0


Thread 0 crashed with ARM Thread State (64-bit):
    x0: 0x0000000129530d00   x1: 0x00000001cbbaead7   x2: 0x0000000129530d00   x3: 0x0000000283dcb300
    x4: 0x0000000283dcb300   x5: 0x0000000283dcb300   x6: 0x000000000000006e   x7: 0x0000000000000000
    x8: 0x000000000000000a   x9: 0x0000000000000002  x10: 0x000000013657715f  x11: 0x03ff000127f70000
   x12: 0x0000000000000302  x13: 0x0000000127f73010  x14: 0x0000000180b8c000  x15: 0x00000001da1a9b88
   x16: 0x00000001da1a9b88  x17: 0x1037f481834d1604  x18: 0x0000000000000000  x19: 0x0000000283dcb300
   x20: 0x0000000129530d00  x21: 0x00000001007abc42  x22: 0x0000000129519200  x23: 0x00000001007abc42
   x24: 0x00000001da1be800  x25: 0x0000000000000000  x26: 0x00000001cafaa4d7  x27: 0x0000000283dcb300
   x28: 0x0000000000000001   fp: 0x000000016f668ef0   lr: 0x000000010079f9c4
    sp: 0x000000016f668ef0   pc: 0x000000010079f9c4 cpsr: 0x00001000
   far: 0x0000000103998000  esr: 0xf2000001 (Breakpoint) brk 1

Binary Images:
       0x100794000 -        0x1007affff AWSDeviceFarmiOSReferenceApp arm64  <73ad2330311d3c88a82b57bab2d44d4e> /private/var/containers/Bundle/Application/B88D471A-D62E-414D-BAF0-06C2209B1B56/AWSDeviceFarmiOSReferenceApp.app/AWSDeviceFarmiOSReferenceApp
       0x183357000 -        0x184bf2fff UIKitCore arm64e  <3ed35565456d33cbb5546c567fa81585> /System/Library/PrivateFrameworks/UIKitCore.framework/UIKitCore
       0x1f110d000 -        0x1f1246fff UIKit arm64e  <843645745373346d959393a1572d3983> /System/Library/AccessibilityBundles/UIKit.axbundle/UIKit
       0x180edd000 -        0x181332fff CoreFoundation arm64e  <5198fb5756453b34a49ff32b52256cf3> /System/Library/Frameworks/CoreFoundation.framework/CoreFoundation
       0x19d02e000 -        0x19d036fff GraphicsServices arm64e  <260f066ec5de3844967e483985448f21> /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices
       0x1009d4000 -        0x100a2bfff dyld arm64e  <7c9c7851823738a7b1eb9cd2deb4b746> /usr/lib/dyld
       0x1f0ed3000 -        0x1f0edefff libsystem_pthread.dylib arm64e  <1026e3a4d3c03b0da552f185d6772a29> /usr/lib/system/libsystem_pthread.dylib
       0x1b7d87000 -        0x1b7dbcfff libsystem_kernel.dylib arm64e  <1db00c916ad8384085030c6fbffa8e0b> /usr/lib/system/libsystem_kernel.dylib
       0x1826d1000 -        0x1829dbfff Foundation arm64e  <ac6ad2f3c7b138aa858857036a663180> /System/Library/Frameworks/Foundation.framework/Foundation

EOF

-----------
Full Report
-----------

{"app_name":"AWSDeviceFarmiOSReferenceApp","timestamp":"2025-05-01 02:21:27.00 +0000","app_version":"1.0","slice_uuid":"73ad2330-311d-3c88-a82b-57bab2d44d4e","build_version":"1","platform":2,"bundleID":"AWSDeviceFarmiOSReferenceApp.xcuitests","share_with_app_devs":0,"is_first_party":0,"bug_type":"309","os_version":"iPhone OS 15.5 (19F77)","incident_id":"073E7658-3A0A-45D4-A2DA-317275FFAB58","name":"AWSDeviceFarmiOSReferenceApp"}
{
  "uptime" : 6800,
  "procLaunch" : "2025-05-01 02:21:11.4298 +0000",
  "procRole" : "Foreground",
  "version" : 2,
  "userID" : 501,
  "deployVersion" : 210,
  "modelCode" : "iPad14,2",
  "procStartAbsTime" : 163469794896,
  "coalitionID" : 873,
  "osVersion" : {
    "isEmbedded" : true,
    "train" : "iPhone OS 15.5",
    "releaseType" : "User",
    "build" : "19F77"
  },
  "captureTime" : "2025-05-01 02:21:27.7024 +0000",
  "incident" : "073E7658-3A0A-45D4-A2DA-317275FFAB58",
  "bug_type" : "309",
  "pid" : 1822,
  "procExitAbsTime" : 163860273883,
  "cpuType" : "ARM-64",
  "procName" : "AWSDeviceFarmiOSReferenceApp",
  "procPath" : "\/private\/var\/containers\/Bundle\/Application\/B88D471A-D62E-414D-BAF0-06C2209B1B56\/AWSDeviceFarmiOSReferenceApp.app\/AWSDeviceFarmiOSReferenceApp",
  "bundleInfo" : {"CFBundleShortVersionString":"1.0","CFBundleVersion":"1","CFBundleIdentifier":"AWSDeviceFarmiOSReferenceApp.xcuitests"},
  "storeInfo" : {"deviceIdentifierForVendor":"3C98EF89-79CE-4816-8D36-77B75F825AF1","thirdParty":true},
  "parentProc" : "launchd",
  "parentPid" : 1,
  "coalitionName" : "AWSDeviceFarmiOSReferenceApp.xcuitests",
  "crashReporterKey" : "dedd649bf307bf2dea997e733d22721d2c5fcae9",
  "basebandVersion" : "1.61.00",
  "isCorpse" : 1,
  "exception" : {"codes":"0x0000000000000001, 0x000000010079f9c4","rawCodes":[1,4302961092],"type":"EXC_BREAKPOINT","signal":"SIGTRAP"},
  "termination" : {"flags":0,"code":5,"namespace":"SIGNAL","indicator":"Trace\/BPT trap: 5","byProc":"exc handler","byPid":1822},
  "faultingThread" : 0,
  "threads" : [{"triggered":true,"id":45051,"threadState":{"x":[{"value":4988275968},{"value":7712991959,"objc-selector":"retain"},{"value":4988275968},{"value":10802213632},{"value":10802213632},{"value":10802213632},{"value":110},{"value":0},{"value":10},{"value":2},{"value":5206667615},{"value":287948906140467200},{"value":770},{"value":4965478416},{"value":6454558720},{"value":7954144136,"symbolLocation":0,"symbol":"OBJC_CLASS_$_UIButton"},{"value":7954144136,"symbolLocation":0,"symbol":"OBJC_CLASS_$_UIButton"},{"value":1168671465416758788,"symbolLocation":1168671458918924288,"symbol":"-[UIView(UIKitManual) retain]"},{"value":0},{"value":10802213632},{"value":4988275968},{"value":4303010882},{"value":4988178944},{"value":4303010882},{"value":7954229248,"symbolLocation":0,"symbol":"UIApp"},{"value":0},{"value":7700391127,"objc-selector":"objectAtIndex:"},{"value":10802213632},{"value":1}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4302961092},"cpsr":{"value":4096},"fp":{"value":6163959536},"sp":{"value":6163959536},"esr":{"value":4060086273,"description":"(Breakpoint) brk 1"},"pc":{"value":4302961092,"matchesCrashFrame":1},"far":{"value":4355358720}},"queue":"com.apple.main-thread","frames":[{"imageOffset":47556,"imageIndex":0},{"imageOffset":4999612,"symbol":"-[UIApplication sendAction:to:from:forEvent:]","symbolLocation":100,"imageIndex":1},{"imageOffset":6222708,"symbol":"-[UIControl sendAction:to:forEvent:]","symbolLocation":128,"imageIndex":1},{"imageOffset":3588968,"symbol":"-[UIControl _sendActionsForEvents:withEvent:]","symbolLocation":356,"imageIndex":1},{"imageOffset":4231288,"symbol":"-[UIButton _sendActionsForEvents:withEvent:]","symbolLocation":160,"imageIndex":1},{"imageOffset":6923564,"symbol":"-[UIControl touchesEnded:withEvent:]","symbolLocation":520,"imageIndex":1},{"imageOffset":1512940,"symbol":"-[UIWindow _sendTouchesForEvent:]","symbolLocation":980,"imageIndex":1},{"imageOffset":1714288,"symbol":"-[UIWindow sendEvent:]","symbolLocation":4408,"imageIndex":1},{"imageOffset":3474056,"symbol":"-[UIApplication sendEvent:]","symbolLocation":824,"imageIndex":1},{"imageOffset":206012,"symbol":"-[UIApplicationAccessibility sendEvent:]","symbolLocation":100,"imageIndex":2},{"imageOffset":1532240,"symbol":"__dispatchPreprocessedEventFromEventQueue","symbolLocation":7856,"imageIndex":1},{"imageOffset":1486504,"symbol":"__processEventQueue","symbolLocation":6616,"imageIndex":1},{"imageOffset":16495328,"symbol":"updateCycleEntry","symbolLocation":176,"imageIndex":1},{"imageOffset":8227196,"symbol":"_UIUpdateSequenceRun","symbolLocation":84,"imageIndex":1},{"imageOffset":15068232,"symbol":"schedulerStepScheduledMainSection","symbolLocation":144,"imageIndex":1},{"imageOffset":15066128,"symbol":"runloopSourceCallback","symbolLocation":92,"imageIndex":1},{"imageOffset":766996,"symbol":"__CFRUNLOOP_IS_CALLING_OUT_TO_A_SOURCE0_PERFORM_FUNCTION__","symbolLocation":28,"imageIndex":3},{"imageOffset":836000,"symbol":"__CFRunLoopDoSource0","symbolLocation":208,"imageIndex":3},{"imageOffset":22164,"symbol":"__CFRunLoopDoSources0","symbolLocation":268,"imageIndex":3},{"imageOffset":45148,"symbol":"__CFRunLoopRun","symbolLocation":828,"imageIndex":3},{"imageOffset":125896,"symbol":"CFRunLoopRunSpecific","symbolLocation":600,"imageIndex":3},{"imageOffset":4980,"symbol":"GSEventRunModal","symbolLocation":164,"imageIndex":4},{"imageOffset":5326408,"symbol":"-[UIApplication _run]","symbolLocation":1100,"imageIndex":1},{"imageOffset":2710928,"symbol":"UIApplicationMain","symbolLocation":364,"imageIndex":1},{"imageOffset":57128,"imageIndex":0},{"imageOffset":105700,"symbol":"start","symbolLocation":520,"imageIndex":5}]},{"id":45052,"frames":[{"imageOffset":3668,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":6}]},{"id":45056,"name":"com.apple.uikit.eventfetch-thread","frames":[{"imageOffset":5280,"symbol":"mach_msg_trap","symbolLocation":8,"imageIndex":7},{"imageOffset":6884,"symbol":"mach_msg","symbolLocation":76,"imageIndex":7},{"imageOffset":27952,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":372,"imageIndex":3},{"imageOffset":45500,"symbol":"__CFRunLoopRun","symbolLocation":1180,"imageIndex":3},{"imageOffset":125896,"symbol":"CFRunLoopRunSpecific","symbolLocation":600,"imageIndex":3},{"imageOffset":103524,"symbol":"-[NSRunLoop(NSRunLoop) runMode:beforeDate:]","symbolLocation":236,"imageIndex":8},{"imageOffset":372268,"symbol":"-[NSRunLoop(NSRunLoop) runUntilDate:]","symbolLocation":92,"imageIndex":8},{"imageOffset":4777840,"symbol":"-[UIEventFetcher threadMain]","symbolLocation":524,"imageIndex":1},{"imageOffset":431164,"symbol":"__NSThread__start__","symbolLocation":808,"imageIndex":8},{"imageOffset":6572,"symbol":"_pthread_start","symbolLocation":148,"imageIndex":6},{"imageOffset":3688,"symbol":"thread_start","symbolLocation":8,"imageIndex":6}]},{"id":45106,"frames":[{"imageOffset":3668,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":6}]}],
  "usedImages" : [
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4302913536,
    "size" : 114688,
    "uuid" : "73ad2330-311d-3c88-a82b-57bab2d44d4e",
    "path" : "\/private\/var\/containers\/Bundle\/Application\/B88D471A-D62E-414D-BAF0-06C2209B1B56\/AWSDeviceFarmiOSReferenceApp.app\/AWSDeviceFarmiOSReferenceApp",
    "name" : "AWSDeviceFarmiOSReferenceApp"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6496284672,
    "size" : 25804800,
    "uuid" : "3ed35565-456d-33cb-b554-6c567fa81585",
    "path" : "\/System\/Library\/PrivateFrameworks\/UIKitCore.framework\/UIKitCore",
    "name" : "UIKitCore"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 8339378176,
    "size" : 1286144,
    "uuid" : "*************-346d-9593-93a1572d3983",
    "path" : "\/System\/Library\/AccessibilityBundles\/UIKit.axbundle\/UIKit",
    "name" : "UIKit"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6458036224,
    "size" : 4546560,
    "uuid" : "5198fb57-5645-3b34-a49f-f32b52256cf3",
    "path" : "\/System\/Library\/Frameworks\/CoreFoundation.framework\/CoreFoundation",
    "name" : "CoreFoundation"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6929178624,
    "size" : 36864,
    "uuid" : "260f066e-c5de-3844-967e-483985448f21",
    "path" : "\/System\/Library\/PrivateFrameworks\/GraphicsServices.framework\/GraphicsServices",
    "name" : "GraphicsServices"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4305272832,
    "size" : 360448,
    "uuid" : "7c9c7851-8237-38a7-b1eb-9cd2deb4b746",
    "path" : "\/usr\/lib\/dyld",
    "name" : "dyld"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 8337043456,
    "size" : 49152,
    "uuid" : "1026e3a4-d3c0-3b0d-a552-f185d6772a29",
    "path" : "\/usr\/lib\/system\/libsystem_pthread.dylib",
    "name" : "libsystem_pthread.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 7379382272,
    "size" : 221184,
    "uuid" : "1db00c91-6ad8-3840-8503-0c6fbffa8e0b",
    "path" : "\/usr\/lib\/system\/libsystem_kernel.dylib",
    "name" : "libsystem_kernel.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6483152896,
    "size" : 3190784,
    "uuid" : "ac6ad2f3-c7b1-38aa-8588-57036a663180",
    "path" : "\/System\/Library\/Frameworks\/Foundation.framework\/Foundation",
    "name" : "Foundation"
  }
],
  "sharedCache" : {
  "base" : 6454558720,
  "size" : 2512863232,
  "uuid" : "9a98697b-d449-3891-8838-d18acc286b96"
},
  "vmSummary" : "ReadOnly portion of Libraries: Total=773.8M resident=0K(0%) swapped_out_or_unallocated=773.8M(100%)\nWritable regions: Total=584.2M written=0K(0%) resident=0K(0%) swapped_out=0K(0%) unallocated=584.2M(100%)\n\n                                VIRTUAL   REGION \nREGION TYPE                        SIZE    COUNT (non-coalesced) \n===========                     =======  ======= \nActivity Tracing                   256K        1 \nCG raster data                      32K        1 \nColorSync                           64K        4 \nCoreAnimation                      592K       21 \nFoundation                          16K        1 \nKernel Alloc Once                   32K        1 \nMALLOC                           580.7M       49 \nMALLOC guard page                  128K        8 \nSTACK GUARD                         64K        4 \nStack                             2640K        4 \nVM_ALLOCATE                        176K        3 \n__AUTH                            4007K      452 \n__AUTH_CONST                      22.6M      610 \n__CTF                               756        1 \n__DATA                            14.7M      617 \n__DATA_CONST                      22.7M      633 \n__DATA_DIRTY                      2529K      511 \n__FONT_DATA                          4K        1 \n__LINKEDIT                       185.6M       22 \n__OBJC_CONST                      5660K      423 \n__OBJC_RO                         91.3M        1 \n__OBJC_RW                         3472K        1 \n__TEXT                           588.2M      643 \n__UNICODE                          592K        1 \ndyld private memory               1024K        1 \nmapped file                      165.5M       14 \nshared memory                       48K        3 \n===========                     =======  ======= \nTOTAL                              1.7G     4031 \n",
  "legacyInfo" : {
  "threadTriggered" : {
    "queue" : "com.apple.main-thread"
  }
},
  "trialInfo" : {
  "rollouts" : [
    {
      "rolloutId" : "601d9415f79519000ccd4b69",
      "factorPackIds" : {
        "SIRI_TEXT_TO_SPEECH" : "618455f04b3765609b8b78c2"
      },
      "deploymentId" : 240000311
    }
  ],
  "experiments" : [

  ]
}
}

