User defaults from command line:
    IDEDerivedDataPathOverride = /tmp/00008020-000619E914D2002E_xcuitest_derived_data_2
    IDETestRunOnlyIdentifiers = (
    "AWSDeviceFarmiOSReferenceAppUITestsSwift/EditTextTest/testEditText"
)
    IDETestRunSpecificationPath = /tmp/00008020-000619E914D2002E_xctestrun.xml

2020-11-20 20:49:53.086 xcodebuild[53580:2894974]  IDETestOperationsObserverDebug: Writing diagnostic log for test session to:
/tmp/00008020-000619E914D2002E_xcuitest_derived_data_2/Logs/Test/Test-Transient Testing-2020.11.20_20-49-53-+0000.xcresult/1_Test/Diagnostics/AWSDeviceFarmiOSReferenceAppUITestsSwift-B67D913F-FA81-4A32-97BD-90CCBA4A7175/AWSDeviceFarmiOSReferenceAppUITestsSwift-2A184FB8-6063-454B-A43C-210633E4B824/Session-AWSDeviceFarmiOSReferenceAppUITestsSwift-2020-11-20_204953-NzNBZo.log
2020-11-20 20:49:53.087 xcodebuild[53580:2894482] [MT] IDETestOperationsObserverDebug: (BD892588-34DD-481C-897D-5E041F95FF18) Beginning test session AWSDeviceFarmiOSReferenceAppUITestsSwift-BD892588-34DD-481C-897D-5E041F95FF18 at 2020-11-20 20:49:53.086 with Xcode 10B61 on target 📱<DVTiOSDevice (0x7f91e4a3ed50), iPhone, iPhone, 12.1 (16B92), 00008020-000619E914D2002E> {
		deviceSerialNumber:         FFWXJ3VHKPH2
		identifier:                 00008020-000619E914D2002E
		deviceClass:                iPhone
		deviceName:                 iPhone
		deviceIdentifier:           00008020-000619E914D2002E
		productVersion:             12.1
		buildVersion:               16B92
		deviceSoftwareVersion:      12.1 (16B92)
		deviceArchitecture:         arm64e
		deviceTotalCapacity:        59481223168
		deviceAvailableCapacity:    55072870400
		deviceIsTransient:          NO
		ignored:                    NO
		deviceIsBusy:               NO
		deviceIsPaired:             YES
		deviceIsActivated:          YES
		deviceActivationState:      Activated
		isPasscodeLocked:           NO
		deviceType:                 <DVTDeviceType:0x7f91e44d06c0 Xcode.DeviceType.iPhone>
		supportedDeviceFamilies:    (
    1
)
		applications:              (null)
		provisioningProfiles:      (null)
		hasInternalSupport:        NO
		isSupportedOS:             YES
		developerDiskMountError:   (null)
(null)
	bootArgs:                  (null)
		connected:                 yes
		isWirelessEnabled:         no
		connectionType:            direct
		hostname:                  (null)
		bonjourServiceName:        e0:33:8e:d1:e4:f7@fe80::e233:8eff:fed1:e4f7._apple-mobdev2._tcp.local.
		} (12.1 (16B92))
2020-11-20 20:49:56.553 AWSDeviceFarmiOSReferenceAppUITestsSwift-Runner[2025:194137] Running tests...
Test Suite 'Selected tests' started at 2020-11-20 20:49:56.611
Test Suite 'AWSDeviceFarmiOSReferenceAppUITestsSwift.xctest' started at 2020-11-20 20:49:56.611
Test Suite 'EditTextTest' started at 2020-11-20 20:49:56.611
Test Case '-[AWSDeviceFarmiOSReferenceAppUITestsSwift.EditTextTest testEditText]' started.
    t =     0.00s Start Test at 2020-11-20 20:49:56.611
    t =     0.02s Set Up
    t =     0.02s     Open Amazon.AWSDeviceFarmiOSReferenceApp.bstack
    t =     0.04s         Launch Amazon.AWSDeviceFarmiOSReferenceApp.bstack
    t =     0.04s             Terminate Amazon.AWSDeviceFarmiOSReferenceApp.bstack:2008
    t =     1.19s             Wait for Amazon.AWSDeviceFarmiOSReferenceApp.bstack to idle
    t =     2.30s Tap "Inputs" Button
    t =     2.30s     Wait for Amazon.AWSDeviceFarmiOSReferenceApp.bstack to idle
    t =     2.35s     Find the "Inputs" Button
    t =     2.35s         Requesting snapshot of accessibility hierarchy for app with pid 2029
    t =     2.40s         Find: Descendants matching type TabBar
    t =     2.40s         Find: Descendants matching type Button
    t =     2.41s         Find: Elements matching predicate '"Inputs" IN identifiers'
    t =     2.41s     Check for interrupting elements affecting "Inputs" Button
    t =     2.41s     Synthesize event
    t =     2.53s     Wait for Amazon.AWSDeviceFarmiOSReferenceApp.bstack to idle
    t =     2.57s Double tap TextView
    t =     2.57s     Wait for Amazon.AWSDeviceFarmiOSReferenceApp.bstack to idle
    t =     2.60s     Find the TextView
    t =     2.60s         Requesting snapshot of accessibility hierarchy for app with pid 2029
    t =     2.63s     Check for interrupting elements affecting "I am a textview" TextView
    t =     2.64s     Synthesize event
    t =     3.08s     Wait for Amazon.AWSDeviceFarmiOSReferenceApp.bstack to idle
    t =     3.52s Tap "Select All" MenuItem
    t =     3.52s     Wait for Amazon.AWSDeviceFarmiOSReferenceApp.bstack to idle
    t =     3.85s     Find the "Select All" MenuItem
    t =     3.85s         Requesting snapshot of accessibility hierarchy for app with pid 2029
    t =     3.95s         Find: Descendants matching type MenuItem
    t =     3.95s         Find: Elements matching predicate '"Select All" IN identifiers'
    t =     3.96s     Check for interrupting elements affecting "Select All" MenuItem
    t =     3.98s     Synthesize event
    t =     4.07s     Wait for Amazon.AWSDeviceFarmiOSReferenceApp.bstack to idle
    t =     4.61s Type 'Something' into "I am a textview" TextView
    t =     4.61s     Wait for Amazon.AWSDeviceFarmiOSReferenceApp.bstack to idle
    t =     4.67s     Find the "I am a textview" TextView
    t =     4.67s         Requesting snapshot of accessibility hierarchy for app with pid 2029
    t =     4.75s     Check for interrupting elements affecting "I am a textview" TextView
    t =     4.76s     Synthesize event
    t =     4.94s     Wait for Amazon.AWSDeviceFarmiOSReferenceApp.bstack to idle
    t =     5.01s Find the "I am a textview" TextView
    t =     5.01s     Requesting snapshot of accessibility hierarchy for app with pid 2029
    t =     5.08s Tear Down
Test Case '-[AWSDeviceFarmiOSReferenceAppUITestsSwift.EditTextTest testEditText]' passed (5.283 seconds).
Test Suite 'EditTextTest' passed at 2020-11-20 20:50:01.897.
	 Executed 1 test, with 0 failures (0 unexpected) in 5.283 (5.286) seconds
Test Suite 'AWSDeviceFarmiOSReferenceAppUITestsSwift.xctest' passed at 2020-11-20 20:50:01.901.
	 Executed 1 test, with 0 failures (0 unexpected) in 5.283 (5.290) seconds
Test Suite 'Selected tests' passed at 2020-11-20 20:50:01.907.
	 Executed 1 test, with 0 failures (0 unexpected) in 5.283 (5.296) seconds


Test session results and logs:
	/tmp/00008020-000619E914D2002E_xcuitest_derived_data_2/Logs/Test/Test-Transient Testing-2020.11.20_20-49-53-+0000.xcresult

2020-11-20 20:50:01.921 xcodebuild[53580:2894482] [MT] IDETestOperationsObserverDebug: 8.850 elapsed -- Testing started completed.
2020-11-20 20:50:01.921 xcodebuild[53580:2894482] [MT] IDETestOperationsObserverDebug: 0.000 sec, +0.000 sec -- start
2020-11-20 20:50:01.921 xcodebuild[53580:2894482] [MT] IDETestOperationsObserverDebug: 8.850 sec, ****** sec -- end
** TEST EXECUTE SUCCEEDED **

Testing started on 'iPhone'