2019-11-13 01:18:26:273 - [HTTP] --> POST /wd/hub/session
2019-11-13 01:18:26:273 - [HTTP] {"desiredCapabilities":{"automationName":"XCUITest","os_version":"12","browserstack.debug":"true","browserstack.networklogs":"true","noReset":true,"deviceName":"iphone","platformName":"ios","acceptSslCert":false,"osVersion":"12","device":"iphone","browserstack.tunnelIdentifier":"","platform":"MAC","browserstack.appiumLogs":"true","browserstack.minOSVersion":"10.0","bundleID":"com.covvered.app","browserstack.deviceLogs":"true","version":"","mobile":{"browser":"mobile","version":"iPhone XS-12.1"},"orig_os":"ios","64bit":false,"browserstack.video":"true","browserstack.video.disableWaterMark":"false","proxy_type":"node","realMobile":"true","udid":"00008020-0019312C1A50003A","appium_port":8086,"safariInitialUrl":"http://mobile-internet-check.browserstack.com","webkitResponseTimeout":20000,"newCommandTimeout":100,"orientation":"PORTRAIT","deviceOrientation":"portrait","browserstack.ie.noFlash":"false","acceptSslCerts":false,"wda_port":8406,"bundleId":"com.covvered.app"}}
2019-11-13 01:18:26:274 - [debug] [MJSONWP] Calling AppiumDriver.createSession() with args: [{"automationName":"XCUITest","os_version":"12","browserstack.debug":"true","browserstack.networklogs":"true","noReset":true,"deviceName":"iphone","platformName":"ios","acceptSslCert":false,"osVersion":"12","device":"iphone","browserstack.tunnelIdentifier":"","platform":"MAC","browserstack.appiumLogs":"true","browserstack.minOSVersion":"10.0","bundleID":"com.covvered.app","browserstack.deviceLogs":"true","version":"","mobile":{"browser":"mobile","version":"iPhone XS-12.1"},"orig_os":"ios","64bit":false,"browserstack.video":"true","browserstack.video.disableWaterMark":"false","proxy_type":"node","realMobile":"true","udid":"00008020-0019312C1A50003A","appium_port":8086,"safariInitialUrl":"http://mobile-internet-check.browserstack.com","webkitResponseTimeout":20000,"newCommandTimeout":100,"orientation":"PORTRAIT","deviceOrientation":"portrait","browserstack.ie.noFlash":"false","acceptSslCerts":false,"wda_port":8406,"bundleId":"com.covvered.app"},null,null]
2019-11-13 01:18:26:274 - [debug] [BaseDriver] Event 'newSessionRequested' logged at 1573607906274 (01:18:26 GMT+0000 (Coordinated Universal Time))
2019-11-13 01:18:26:275 - [Appium] Creating new XCUITestDriver (v2.97.0) session
2019-11-13 01:18:26:275 - [Appium] Capabilities:
2019-11-13 01:18:26:276 - [Appium]   platformVersion: 12.0
2019-11-13 01:18:26:276 - [Appium]   useXctestrunFile: true
2019-11-13 01:18:26:276 - [Appium]   bootstrapPath: /usr/local/.browserstack/config/wda_derived_data_00008020-0019312C1A50003A_ba28f66ee73e3768159e010410236349/Build/Products
2019-11-13 01:18:26:276 - [Appium]   orientation: PORTRAIT
2019-11-13 01:18:26:276 - [Appium]   browserstack.isTargetBased: false
2019-11-13 01:18:26:276 - [Appium]   udid: 00008020-0019312C1A50003A
2019-11-13 01:18:26:276 - [Appium]   automationName: XCUITest
2019-11-13 01:18:26:277 - [Appium]   os_version: 12
2019-11-13 01:18:26:277 - [Appium]   browserstack.debug: true
2019-11-13 01:18:26:277 - [Appium]   browserstack.networklogs: true
2019-11-13 01:18:26:277 - [Appium]   noReset: true
2019-11-13 01:18:26:277 - [Appium]   deviceName: iphone
2019-11-13 01:18:26:277 - [Appium]   platformName: ios
2019-11-13 01:18:26:277 - [Appium]   acceptSslCert: false
2019-11-13 01:18:26:278 - [Appium]   osVersion: 12
2019-11-13 01:18:26:278 - [Appium]   device: iphone
2019-11-13 01:18:26:278 - [Appium]   browserstack.tunnelIdentifier: 
2019-11-13 01:18:26:278 - [Appium]   platform: MAC
2019-11-13 01:18:26:278 - [Appium]   browserstack.appiumLogs: true
2019-11-13 01:18:26:278 - [Appium]   browserstack.minOSVersion: 10.0
2019-11-13 01:18:26:278 - [Appium]   bundleID: com.covvered.app
2019-11-13 01:18:26:281 - [Appium]   browserstack.deviceLogs: true
2019-11-13 01:18:26:281 - [Appium]   version: 
2019-11-13 01:18:26:281 - [Appium]   mobile: {
2019-11-13 01:18:26:281 - [Appium]     browser: mobile
2019-11-13 01:18:26:281 - [Appium]     version: iPhone XS-12.1
2019-11-13 01:18:26:281 - [Appium]   }
2019-11-13 01:18:26:282 - [Appium]   orig_os: ios
2019-11-13 01:18:26:282 - [Appium]   64bit: false
2019-11-13 01:18:26:282 - [Appium]   browserstack.video: true
2019-11-13 01:18:26:282 - [Appium]   browserstack.video.disableWaterMark: false
2019-11-13 01:18:26:282 - [Appium]   proxy_type: node
2019-11-13 01:18:26:282 - [Appium]   realMobile: true
2019-11-13 01:18:26:282 - [Appium]   appium_port: 8086
2019-11-13 01:18:26:283 - [Appium]   safariInitialUrl: http://mobile-internet-check.browserstack.com
2019-11-13 01:18:26:283 - [Appium]   webkitResponseTimeout: 20000
2019-11-13 01:18:26:283 - [Appium]   newCommandTimeout: 100
2019-11-13 01:18:26:283 - [Appium]   deviceOrientation: portrait
2019-11-13 01:18:26:283 - [Appium]   browserstack.ie.noFlash: false
2019-11-13 01:18:26:283 - [Appium]   acceptSslCerts: false
2019-11-13 01:18:26:283 - [Appium]   wda_port: 8406
2019-11-13 01:18:26:284 - [Appium]   bundleId: com.covvered.app
2019-11-13 01:18:26:292 - [BaseDriver] The following capabilities were provided, but are not recognized by appium: browserstack.isTargetBased, os_version, browserstack.debug, browserstack.networklogs, acceptSslCert, osVersion, device, browserstack.tunnelIdentifier, platform, browserstack.appiumLogs, browserstack.minOSVersion, bundleID, browserstack.deviceLogs, version, mobile, orig_os, 64bit, browserstack.video, browserstack.video.disableWaterMark, proxy_type, realMobile, appium_port, deviceOrientation, browserstack.ie.noFlash, acceptSslCerts, wda_port.
2019-11-13 01:18:26:293 - [BaseDriver] Session created with session id: 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:18:26:306 - [debug] [XCUITest] Current user: 'app'
2019-11-13 01:18:26:337 - [debug] [XCUITest] Xcode version set to '10.0' 
2019-11-13 01:18:26:337 - [debug] [XCUITest] iOS SDK Version set to '12.0'
2019-11-13 01:18:26:338 - [debug] [BaseDriver] Event 'xcodeDetailsRetrieved' logged at 1573607906337 (01:18:26 GMT+0000 (Coordinated Universal Time))
2019-11-13 01:18:26:943 - [debug] [XCUITest] Available devices: 25886e7623bb9d37ff0ba8e3cb71de262b933370, 00008020-000368441E68002E, 00008020-0015358A0238002E, 00008020-0019312C1A50003A, 00008020-00040D243468002E, c40c8a518065abb8086ddbc1ef1df5fffbbc0ca9, 00008020-000639940AD0003A
2019-11-13 01:18:26:943 - [debug] [XCUITest] Creating iDevice object with udid '00008020-0019312C1A50003A'
2019-11-13 01:18:26:944 - [XCUITest] Determining device to run tests on: udid: '00008020-0019312C1A50003A', real device: true
2019-11-13 01:18:26:945 - [debug] [BaseDriver] Event 'appConfigured' logged at 1573607906945 (01:18:26 GMT+0000 (Coordinated Universal Time))
2019-11-13 01:18:26:946 - [debug] [BaseDriver] Event 'resetStarted' logged at 1573607906946 (01:18:26 GMT+0000 (Coordinated Universal Time))
2019-11-13 01:18:26:946 - [debug] [XCUITest] Reset: fullReset not set. Leaving as is
2019-11-13 01:18:26:946 - [debug] [BaseDriver] Event 'resetComplete' logged at 1573607906946 (01:18:26 GMT+0000 (Coordinated Universal Time))
2019-11-13 01:18:26:947 - [debug] [iOSLog] Attempting iOS device log capture via libimobiledevice idevicesyslog
2019-11-13 01:18:26:947 - [debug] [iOSLog] Starting iOS device log capture with: 'idevicesyslog'
2019-11-13 01:18:27:124 - [debug] [XCUITest] Crash reports root '/Users/<USER>/Library/Logs/CrashReporter/MobileDevice/iPhone (2)' does not exist. Got nothing to gather.
2019-11-13 01:18:27:125 - [debug] [BaseDriver] Event 'logCaptureStarted' logged at 1573607907124 (01:18:27 GMT+0000 (Coordinated Universal Time))
2019-11-13 01:18:27:125 - [XCUITest] Setting up real device
2019-11-13 01:18:27:886 - [XCUITest] Using WDA path: '/usr/local/.browserstack/config/wda_derived_data_00008020-0019312C1A50003A_ba28f66ee73e3768159e010410236349/Build/Products'
2019-11-13 01:18:27:887 - [XCUITest] Using WDA agent: '/usr/local/.browserstack/config/wda_derived_data_00008020-0019312C1A50003A_ba28f66ee73e3768159e010410236349/Build/Products/WebDriverAgent.xcodeproj'
2019-11-13 01:18:27:901 - [debug] [XCUITest] No obsolete cached processes from previous WDA sessions listening on port 8406 have been found
2019-11-13 01:18:27:902 - [debug] [JSONWP Proxy] Matched '/status' to command name 'getStatus'
2019-11-13 01:18:27:902 - [debug] [JSONWP Proxy] Proxying [GET /status] to [GET http://localhost:8406/status] with no body
2019-11-13 01:18:27:918 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : {\n    \"state\" : \"success\",\n    \"os\" : {\n      \"name\" : \"iOS\",\n      \"version\" : \"12.1\",\n      \"sdkVersion\" : \"12.0\"\n    },\n    \"ios\" : {\n      \"simulatorVersion\" : \"12.1\",\n      \"ip\" : \"************\"\n    },\n    \"build\" : {\n      \"time\" : \"Oct 12 2019 07:50:40\",\n      \"productBundleIdentifier\" : \"com.facebook.WebDriverAgentRunner\"\n    }\n  },\n  \"sessionId\" : \"910DD6AA-D6FC-4544-8908-176B0AE91A06\",\n  \"status\" : 0\n}"
2019-11-13 01:18:27:919 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:18:27:919 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:18:27:919 - [debug] [XCUITest] Upgrade timestamp of the currently bundled WDA: null
2019-11-13 01:18:27:920 - [debug] [XCUITest] Upgrade timestamp of the WDA on the device: undefined
2019-11-13 01:18:27:920 - [XCUITest] Will reuse previously cached WDA instance at 'http://localhost:8406/' with 'com.facebook.WebDriverAgentRunner'. Set the wdaLocalPort capability to a value different from 8406 if this is an undesired behavior.
2019-11-13 01:18:27:920 - [debug] [XCUITest] Trying to start WebDriverAgent 1 times with 10000ms interval
2019-11-13 01:18:27:920 - [debug] [BaseDriver] Event 'wdaStartAttempted' logged at 1573607907920 (01:18:27 GMT+0000 (Coordinated Universal Time))
2019-11-13 01:18:27:921 - [XCUITest] Using provided WebdriverAgent at 'http://localhost:8406/'
2019-11-13 01:18:27:921 - [debug] [BaseDriver] Event 'wdaSessionAttempted' logged at 1573607907921 (01:18:27 GMT+0000 (Coordinated Universal Time))
2019-11-13 01:18:27:921 - [debug] [XCUITest] Sending createSession command to WDA
2019-11-13 01:18:27:922 - [debug] [JSONWP Proxy] Matched '/status' to command name 'getStatus'
2019-11-13 01:18:27:922 - [debug] [JSONWP Proxy] Proxying [GET /status] to [GET http://localhost:8406/status] with no body
2019-11-13 01:18:27:939 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : {\n    \"state\" : \"success\",\n    \"os\" : {\n      \"name\" : \"iOS\",\n      \"version\" : \"12.1\",\n      \"sdkVersion\" : \"12.0\"\n    },\n    \"ios\" : {\n      \"simulatorVersion\" : \"12.1\",\n      \"ip\" : \"************\"\n    },\n    \"build\" : {\n      \"time\" : \"Oct 12 2019 07:50:40\",\n      \"productBundleIdentifier\" : \"com.facebook.WebDriverAgentRunner\"\n    }\n  },\n  \"sessionId\" : \"910DD6AA-D6FC-4544-8908-176B0AE91A06\",\n  \"status\" : 0\n}"
2019-11-13 01:18:27:940 - [debug] [JSONWP Proxy] Matched '/session' to command name 'createSession'
2019-11-13 01:18:27:940 - [debug] [JSONWP Proxy] Proxying [POST /session] to [POST http://localhost:8406/session] with body: {"desiredCapabilities":{"bundleId":"com.covvered.app","arguments":[],"environment":{},"shouldWaitForQuiescence":true,"shouldUseTestManagerForVisibilityDetection":false,"maxTypingFrequency":60,"shouldUseSingletonTestManager":true}}
2019-11-13 01:18:27:941 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:18:27:941 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:18:29:650 - [debug] [JSONWP Proxy] Got response with status 200: {"value":{"sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8","capabilities":{"device":"iphone","browserName":"Covvered","sdkVersion":"12.1","CFBundleIdentifier":"com.covvered.app"}},"sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8","status":0}
2019-11-13 01:18:29:651 - [debug] [BaseDriver] Event 'wdaSessionStarted' logged at 1573607909650 (01:18:29 GMT+0000 (Coordinated Universal Time))
2019-11-13 01:18:29:651 - [debug] [BaseDriver] Event 'wdaStarted' logged at 1573607909651 (01:18:29 GMT+0000 (Coordinated Universal Time))
2019-11-13 01:18:29:651 - [debug] [XCUITest] Setting initial orientation to 'PORTRAIT'
2019-11-13 01:18:29:656 - [debug] [JSONWP Proxy] Matched '/orientation' to command name 'setOrientation'
2019-11-13 01:18:29:656 - [debug] [JSONWP Proxy] Proxying [POST /orientation] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/orientation] with body: {"orientation":"PORTRAIT"}
2019-11-13 01:18:29:658 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:18:29:658 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:18:31:212 - [debug] [JSONWP Proxy] Got response with status 200: {"value":{},"sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8","status":0}
2019-11-13 01:18:31:213 - [debug] [BaseDriver] Event 'orientationSet' logged at 1573607911213 (01:18:31 GMT+0000 (Coordinated Universal Time))
2019-11-13 01:18:31:213 - [Appium] New XCUITestDriver session created successfully, session 7e9c172b-a548-420e-953b-2b1608c86a6b added to master session list
2019-11-13 01:18:31:214 - [debug] [BaseDriver] Event 'newSessionStarted' logged at 1573607911214 (01:18:31 GMT+0000 (Coordinated Universal Time))
2019-11-13 01:18:31:214 - [debug] [MJSONWP (7e9c172b)] Cached the protocol value 'MJSONWP' for the new session 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:18:31:217 - [HTTP] <-- POST /wd/hub/session 200 4944 ms - 1539
2019-11-13 01:18:31:217 - [HTTP] 
2019-11-13 01:18:31:218 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:18:31:218 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:18:31:409 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements
2019-11-13 01:18:31:409 - [HTTP] {"using":"accessibility id","value":"username"}
2019-11-13 01:18:31:410 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.findElements() with args: ["accessibility id","username","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:18:31:410 - [debug] [XCUITest] Executing command 'findElements'
2019-11-13 01:18:31:415 - [debug] [BaseDriver] Valid locator strategies for this request: xpath, id, name, class name, -ios predicate string, -ios class chain, accessibility id
2019-11-13 01:18:31:415 - [debug] [BaseDriver] Waiting up to 0 ms for condition
2019-11-13 01:18:31:416 - [debug] [JSONWP Proxy] Matched '/elements' to command name 'findElements'
2019-11-13 01:18:31:416 - [debug] [JSONWP Proxy] Proxying [POST /elements] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/elements] with body: {"using":"accessibility id","value":"username"}
2019-11-13 01:18:31:419 - [debug] [iProxy] t 8100
2019-11-13 01:18:31:419 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:420 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:420 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:420 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:420 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:420 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:421 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:421 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:421 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:421 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:421 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:421 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:421 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:422 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:422 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:422 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:422 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:422 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:422 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:422 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:422 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:422 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:423 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:423 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:423 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:423 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:423 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:423 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:423 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:423 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:424 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:424 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:424 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:424 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:424 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:424 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:424 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:424 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:425 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:425 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:425 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:425 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:425 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:425 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:425 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:425 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:426 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:426 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:426 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:426 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:426 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:426 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:426 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:426 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:426 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:427 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:427 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:427 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:427 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:427 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:427 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:427 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:427 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:427 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:427 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:428 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:428 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:428 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:428 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:428 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:428 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:428 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:428 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:428 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:429 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:429 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:429 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:429 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:429 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:429 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:429 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:429 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:430 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:432 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:432 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:432 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:432 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:433 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:433 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:433 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:433 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:433 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:433 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:433 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:434 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:434 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:434 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:434 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:434 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:434 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:434 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:434 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:435 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:435 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:435 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:435 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:435 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:436 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:436 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:436 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:436 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:436 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:437 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:437 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:437 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:437 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:437 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:437 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:438 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:438 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:438 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:438 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:438 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:438 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:438 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:438 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:439 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:439 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:439 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:439 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:439 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:439 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:439 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:440 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:440 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:440 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:440 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:440 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:440 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:440 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:441 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:441 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:441 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:441 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:441 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:441 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:441 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:441 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:442 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:442 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:442 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:442 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:442 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:442 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:443 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:443 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:443 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:443 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:443 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:443 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:444 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:444 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:444 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:444 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:445 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:445 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:445 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:445 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:446 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:449 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:449 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:449 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:449 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:449 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:450 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:450 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:450 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:451 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:451 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:451 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:451 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:451 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:451 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:451 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:452 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:452 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:452 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:452 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:452 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:453 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:453 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:453 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:453 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:453 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:454 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:454 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:454 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:454 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:455 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:456 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:463 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:464 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:464 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:465 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:465 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:465 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:465 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:465 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:465 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:465 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:465 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:466 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:466 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:466 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:466 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:466 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:466 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:466 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:466 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:18:31:467 - [debug] [iProxy] waiting for connection
2019-11-13 01:18:31:467 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:18:31:467 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:18:31:467 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:18:31:467 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:18:31:467 - [debug] [iProxy] a
2019-11-13 01:18:31:715 - [debug] [JSONWP Proxy] Got response with status 200: {"value":[{"ELEMENT":"4A000000-0000-0000-6A24-************"}],"sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8","status":0}
2019-11-13 01:18:31:717 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:18:31:717 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:18:31:720 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.findElements() result: [{"ELEMENT":"4A000000-0000-0000-6A24-************"}]
2019-11-13 01:18:31:722 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements 200 312 ms - 124
2019-11-13 01:18:31:722 - [HTTP] 
2019-11-13 01:18:31:877 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/4A000000-0000-0000-6A24-************/displayed
2019-11-13 01:18:31:877 - [HTTP] {}
2019-11-13 01:18:31:877 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:18:31:877 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:18:31:879 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/4A000000-0000-0000-6A24-************/displayed' to command name 'elementDisplayed'
2019-11-13 01:18:31:879 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/4A000000-0000-0000-6A24-************/displayed] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/4A000000-0000-0000-6A24-************/displayed] with body: {}
2019-11-13 01:18:32:138 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : true,\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:18:32:139 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:18:32:140 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/4A000000-0000-0000-6A24-************/displayed 200 263 ms - 76
2019-11-13 01:18:32:140 - [HTTP] 
2019-11-13 01:18:32:140 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:18:32:141 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:18:32:285 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements
2019-11-13 01:18:32:285 - [HTTP] {"using":"accessibility id","value":"username"}
2019-11-13 01:18:32:286 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.findElements() with args: ["accessibility id","username","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:18:32:286 - [debug] [XCUITest] Executing command 'findElements'
2019-11-13 01:18:32:288 - [debug] [BaseDriver] Valid locator strategies for this request: xpath, id, name, class name, -ios predicate string, -ios class chain, accessibility id
2019-11-13 01:18:32:288 - [debug] [BaseDriver] Waiting up to 0 ms for condition
2019-11-13 01:18:32:289 - [debug] [JSONWP Proxy] Matched '/elements' to command name 'findElements'
2019-11-13 01:18:32:289 - [debug] [JSONWP Proxy] Proxying [POST /elements] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/elements] with body: {"using":"accessibility id","value":"username"}
2019-11-13 01:18:32:558 - [debug] [JSONWP Proxy] Got response with status 200: {"value":[{"ELEMENT":"4A000000-0000-0000-6A24-************"}],"sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8","status":0}
2019-11-13 01:18:32:559 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:18:32:560 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:18:32:560 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.findElements() result: [{"ELEMENT":"4A000000-0000-0000-6A24-************"}]
2019-11-13 01:18:32:562 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements 200 276 ms - 124
2019-11-13 01:18:32:562 - [HTTP] 
2019-11-13 01:18:32:709 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/4A000000-0000-0000-6A24-************/clear
2019-11-13 01:18:32:710 - [HTTP] {}
2019-11-13 01:18:32:710 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.clear() with args: ["4A000000-0000-0000-6A24-************","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:18:32:710 - [debug] [XCUITest] Executing command 'clear'
2019-11-13 01:18:32:713 - [debug] [JSONWP Proxy] Matched '/element/4A000000-0000-0000-6A24-************/clear' to command name 'clear'
2019-11-13 01:18:32:713 - [debug] [JSONWP Proxy] Proxying [POST /element/4A000000-0000-0000-6A24-************/clear] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/4A000000-0000-0000-6A24-************/clear] with no body
2019-11-13 01:18:32:924 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:18:32:925 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:18:32:925 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"status\" : 0,\n  \"id\" : \"4A000000-0000-0000-6A24-************\",\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\"\n}"
2019-11-13 01:18:32:925 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.clear() result: null
2019-11-13 01:18:32:926 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/4A000000-0000-0000-6A24-************/clear 200 216 ms - 76
2019-11-13 01:18:32:926 - [HTTP] 
2019-11-13 01:18:33:087 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/4A000000-0000-0000-6A24-************/value
2019-11-13 01:18:33:087 - [HTTP] {"text":"<EMAIL>","value":["t","e","s","t","u","s","e","r","c","o","v","v","e","r","e","d","@","g","m","a","i","l",".","c","o","m"]}
2019-11-13 01:18:33:087 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.setValue() with args: [["t","e","s","t","u","s","e","r","c","o","v","v","e","r","e","d","@","g","m","a","i","l",".","c","o","m"],"4A000000-0000-0000-6A24-************","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:18:33:088 - [debug] [XCUITest] Executing command 'setValue'
2019-11-13 01:18:33:090 - [debug] [JSONWP Proxy] Matched '/element/4A000000-0000-0000-6A24-************/value' to command name 'setValue'
2019-11-13 01:18:33:090 - [debug] [JSONWP Proxy] Proxying [POST /element/4A000000-0000-0000-6A24-************/value] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/4A000000-0000-0000-6A24-************/value] with body: {"value":["t","e","s","t","u","s","e","r","c","o","v","v","e","r","e","d","@","g","m","a","i","l",".","c","o","m"]}
2019-11-13 01:18:35:463 - [debug] [JSONWP Proxy] Got response with status 200: {"status":0,"id":"4A000000-0000-0000-6A24-************","value":"","sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8"}
2019-11-13 01:18:35:464 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:18:35:464 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:18:35:464 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.setValue() result: null
2019-11-13 01:18:35:465 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/4A000000-0000-0000-6A24-************/value 200 2378 ms - 76
2019-11-13 01:18:35:465 - [HTTP] 
2019-11-13 01:18:35:661 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements
2019-11-13 01:18:35:662 - [HTTP] {"using":"accessibility id","value":"password"}
2019-11-13 01:18:35:662 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.findElements() with args: ["accessibility id","password","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:18:35:662 - [debug] [XCUITest] Executing command 'findElements'
2019-11-13 01:18:35:664 - [debug] [BaseDriver] Valid locator strategies for this request: xpath, id, name, class name, -ios predicate string, -ios class chain, accessibility id
2019-11-13 01:18:35:664 - [debug] [BaseDriver] Waiting up to 0 ms for condition
2019-11-13 01:18:35:665 - [debug] [JSONWP Proxy] Matched '/elements' to command name 'findElements'
2019-11-13 01:18:35:665 - [debug] [JSONWP Proxy] Proxying [POST /elements] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/elements] with body: {"using":"accessibility id","value":"password"}
2019-11-13 01:18:36:032 - [debug] [JSONWP Proxy] Got response with status 200: {"value":[{"ELEMENT":"4B000000-0000-0000-6A24-************"}],"sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8","status":0}
2019-11-13 01:18:36:033 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:18:36:033 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:18:36:033 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.findElements() result: [{"ELEMENT":"4B000000-0000-0000-6A24-************"}]
2019-11-13 01:18:36:034 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements 200 372 ms - 124
2019-11-13 01:18:36:034 - [HTTP] 
2019-11-13 01:18:36:179 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/4B000000-0000-0000-6A24-************/clear
2019-11-13 01:18:36:179 - [HTTP] {}
2019-11-13 01:18:36:180 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.clear() with args: ["4B000000-0000-0000-6A24-************","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:18:36:180 - [debug] [XCUITest] Executing command 'clear'
2019-11-13 01:18:36:183 - [debug] [JSONWP Proxy] Matched '/element/4B000000-0000-0000-6A24-************/clear' to command name 'clear'
2019-11-13 01:18:36:183 - [debug] [JSONWP Proxy] Proxying [POST /element/4B000000-0000-0000-6A24-************/clear] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/4B000000-0000-0000-6A24-************/clear] with no body
2019-11-13 01:18:36:391 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"status\" : 0,\n  \"id\" : \"4B000000-0000-0000-6A24-************\",\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\"\n}"
2019-11-13 01:18:36:392 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:18:36:392 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:18:36:392 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.clear() result: null
2019-11-13 01:18:36:393 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/4B000000-0000-0000-6A24-************/clear 200 214 ms - 76
2019-11-13 01:18:36:393 - [HTTP] 
2019-11-13 01:18:36:541 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/4B000000-0000-0000-6A24-************/value
2019-11-13 01:18:36:542 - [HTTP] {"text":"saule.vire","value":["s","a","u","l","e",".","v","i","r","e"]}
2019-11-13 01:18:36:542 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.setValue() with args: [["s","a","u","l","e",".","v","i","r","e"],"4B000000-0000-0000-6A24-************","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:18:36:542 - [debug] [XCUITest] Executing command 'setValue'
2019-11-13 01:18:36:545 - [debug] [JSONWP Proxy] Matched '/element/4B000000-0000-0000-6A24-************/value' to command name 'setValue'
2019-11-13 01:18:36:545 - [debug] [JSONWP Proxy] Proxying [POST /element/4B000000-0000-0000-6A24-************/value] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/4B000000-0000-0000-6A24-************/value] with body: {"value":["s","a","u","l","e",".","v","i","r","e"]}
2019-11-13 01:18:38:225 - [debug] [JSONWP Proxy] Got response with status 200: {"status":0,"id":"4B000000-0000-0000-6A24-************","value":"","sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8"}
2019-11-13 01:18:38:225 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:18:38:225 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:18:38:226 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.setValue() result: null
2019-11-13 01:18:38:226 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/4B000000-0000-0000-6A24-************/value 200 1685 ms - 76
2019-11-13 01:18:38:226 - [HTTP] 
2019-11-13 01:18:38:372 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/appium/device/hide_keyboard
2019-11-13 01:18:38:372 - [HTTP] {"strategy":"tapOutside"}
2019-11-13 01:18:38:373 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.hideKeyboard() with args: ["tapOutside",null,null,null,"7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:18:38:373 - [debug] [XCUITest] Executing command 'hideKeyboard'
2019-11-13 01:18:38:382 - [debug] [JSONWP Proxy] Proxying [POST /wda/keyboard/dismiss] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/wda/keyboard/dismiss] with no body
2019-11-13 01:19:02:511 - [BaseDriver] Shutting down because we waited 60 seconds for a command
2019-11-13 01:19:02:514 - [debug] [XCUITest] Not clearing log files. Use `clearSystemFiles` capability to turn on.
2019-11-13 01:19:02:516 - [Appium] Closing session, cause was 'New Command Timeout of 60 seconds expired. Try customizing the timeout using the 'newCommandTimeout' desired capability'
2019-11-13 01:19:02:517 - [Appium] Removing session a5708cf6-88d2-4435-8f76-a0d3213748d8 from our master session list
2019-11-13 01:19:58:061 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"Error Domain=com.facebook.WebDriverAgent Code=1 \\\"The keyboard on iPhone cannot be dismissed because of a known XCTest issue. Try to dismiss it in the way supported by your application under test.\\\" UserInfo={NSLocalizedDescription=The keyboard on iPhone cannot be dismissed because of a known XCTest issue. Try to dismiss it in the way supported by your application under test.}\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 13\n}"
2019-11-13 01:19:58:062 - [debug] [MJSONWP] Matched JSONWP error code 13 to UnknownError
2019-11-13 01:19:58:062 - [debug] [XCUITest] Cannot dismiss the keyboard using the native call. Trying to apply a workaround...
2019-11-13 01:19:58:062 - [debug] [BaseDriver] Waiting up to 0 ms for condition
2019-11-13 01:19:58:067 - [debug] [JSONWP Proxy] Matched '/element' to command name 'findElement'
2019-11-13 01:19:58:067 - [debug] [JSONWP Proxy] Proxying [POST /element] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element] with body: {"using":"class name","value":"XCUIElementTypeKeyboard"}
2019-11-13 01:19:58:069 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:19:58:069 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:19:58:367 - [debug] [JSONWP Proxy] Got response with status 200: {"value":{"ELEMENT":"57000000-0000-0000-6A24-************"},"sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8","status":0}
2019-11-13 01:19:58:367 - [debug] [XCUITest] Finding keyboard and clicking final button to close
2019-11-13 01:19:58:370 - [debug] [JSONWP Proxy] Matched '/element/57000000-0000-0000-6A24-************/attribute/visible' to command name 'getAttribute'
2019-11-13 01:19:58:370 - [debug] [JSONWP Proxy] Proxying [GET /element/57000000-0000-0000-6A24-************/attribute/visible] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/57000000-0000-0000-6A24-************/attribute/visible] with no body
2019-11-13 01:19:58:371 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:19:58:372 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:19:58:569 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : true,\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:19:58:569 - [debug] [BaseDriver] Waiting up to 0 ms for condition
2019-11-13 01:19:58:572 - [debug] [JSONWP Proxy] Matched '/element/57000000-0000-0000-6A24-************/elements' to command name 'findElementsFromElement'
2019-11-13 01:19:58:572 - [debug] [JSONWP Proxy] Proxying [POST /element/57000000-0000-0000-6A24-************/elements] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/57000000-0000-0000-6A24-************/elements] with body: {"using":"class name","value":"XCUIElementTypeButton"}
2019-11-13 01:19:58:573 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:19:58:573 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:19:58:998 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:19:58:998 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:19:58:998 - [debug] [JSONWP Proxy] Got response with status 200: {"value":[{"ELEMENT":"97000000-0000-0000-6A24-************"},{"ELEMENT":"********-0000-0000-6A24-************"}],"sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8","status":0}
2019-11-13 01:19:58:999 - [debug] [JSONWP Proxy] Matched '/element/********-0000-0000-6A24-************/click' to command name 'click'
2019-11-13 01:19:59:000 - [debug] [JSONWP Proxy] Proxying [POST /element/********-0000-0000-6A24-************/click] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/********-0000-0000-6A24-************/click] with body: {}
2019-11-13 01:19:59:837 - [debug] [JSONWP Proxy] Got response with status 200: {"status":0,"id":"********-0000-0000-6A24-************","value":"","sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8"}
2019-11-13 01:19:59:839 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.hideKeyboard() result: null
2019-11-13 01:19:59:840 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/appium/device/hide_keyboard 200 81465 ms - 76
2019-11-13 01:19:59:840 - [HTTP] 
2019-11-13 01:19:59:841 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:19:59:841 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:19:59:994 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements
2019-11-13 01:19:59:995 - [HTTP] {"using":"accessibility id","value":"signInButton"}
2019-11-13 01:19:59:995 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.findElements() with args: ["accessibility id","signInButton","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:19:59:996 - [debug] [XCUITest] Executing command 'findElements'
2019-11-13 01:19:59:997 - [debug] [BaseDriver] Valid locator strategies for this request: xpath, id, name, class name, -ios predicate string, -ios class chain, accessibility id
2019-11-13 01:19:59:998 - [debug] [BaseDriver] Waiting up to 0 ms for condition
2019-11-13 01:19:59:998 - [debug] [JSONWP Proxy] Matched '/elements' to command name 'findElements'
2019-11-13 01:19:59:999 - [debug] [JSONWP Proxy] Proxying [POST /elements] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/elements] with body: {"using":"accessibility id","value":"signInButton"}
2019-11-13 01:20:00:335 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:20:00:335 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:20:00:336 - [debug] [JSONWP Proxy] Got response with status 200: {"value":[{"ELEMENT":"48000000-0000-0000-6A24-************"}],"sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8","status":0}
2019-11-13 01:20:00:336 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.findElements() result: [{"ELEMENT":"48000000-0000-0000-6A24-************"}]
2019-11-13 01:20:00:338 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements 200 343 ms - 124
2019-11-13 01:20:00:338 - [HTTP] 
2019-11-13 01:20:00:503 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/touch/click
2019-11-13 01:20:00:503 - [HTTP] {"element":"48000000-0000-0000-6A24-************"}
2019-11-13 01:20:00:504 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.click() with args: ["48000000-0000-0000-6A24-************","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:20:00:504 - [debug] [XCUITest] Executing command 'click'
2019-11-13 01:20:00:507 - [debug] [JSONWP Proxy] Matched '/element/48000000-0000-0000-6A24-************/click' to command name 'click'
2019-11-13 01:20:00:507 - [debug] [JSONWP Proxy] Proxying [POST /element/48000000-0000-0000-6A24-************/click] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/48000000-0000-0000-6A24-************/click] with body: {}
2019-11-13 01:20:01:346 - [debug] [JSONWP Proxy] Got response with status 200: {"status":0,"id":"48000000-0000-0000-6A24-************","value":"","sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8"}
2019-11-13 01:20:01:347 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:20:01:347 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:20:01:348 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.click() result: ""
2019-11-13 01:20:01:349 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/touch/click 200 845 ms - 74
2019-11-13 01:20:01:349 - [HTTP] 
2019-11-13 01:20:01:505 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements
2019-11-13 01:20:01:506 - [HTTP] {"using":"xpath","value":"//*"}
2019-11-13 01:20:01:506 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.findElements() with args: ["xpath","//*","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:20:01:507 - [debug] [XCUITest] Executing command 'findElements'
2019-11-13 01:20:01:509 - [debug] [BaseDriver] Valid locator strategies for this request: xpath, id, name, class name, -ios predicate string, -ios class chain, accessibility id
2019-11-13 01:20:01:509 - [debug] [BaseDriver] Waiting up to 0 ms for condition
2019-11-13 01:20:01:510 - [debug] [JSONWP Proxy] Matched '/elements' to command name 'findElements'
2019-11-13 01:20:01:511 - [debug] [JSONWP Proxy] Proxying [POST /elements] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/elements] with body: {"using":"xpath","value":"//*"}
2019-11-13 01:21:51:229 - [debug] [JSONWP Proxy] Got response with status 200: {"value":[{"ELEMENT":"01000000-0000-0000-6A24-************"},{"ELEMENT":"05000000-0000-0000-6A24-************"},{"ELEMENT":"08000000-0000-0000-6A24-************"},{"ELEMENT":"0B000000-0000-0000-6A24-************"},{"ELEMENT":"03000000-0000-0000-6A24-************"},{"ELEMENT":"0F000000-0000-0000-6A24-************"},{"ELEMENT":"********-0000-0000-6A24-************"},{"ELEMENT":"1C000000-0000-0000-6A24-************"},{"ELEMENT":"********-0000-0000-6A24-************"},{"ELEMENT":"09000000-0000-0000-6A24-************"},{"ELEMENT":"50000000-0000-0000-6A24-************"},{"ELEMENT":"07000000-0000-0000-6A24-************"},{"ELEMENT":"0A000000-0000-0000-6A24-************"},{"ELEMENT":"0C000000-0000-0000-6A24-************"},{"ELEMENT":"0D000000-0000-0000-6A24-************"},{"ELEMENT":"10000000-0000-0000-6A24-************"},{"ELEMENT":"11000000-0000-0000-6A24-************"},{"ELEMENT":"19000000-0000-0000-6A24-************"},{"ELEMENT":"12000000-0000-0000-6A24-************"},{"ELEMENT":"13000000-0000-0000-6A24-000000...
2019-11-13 01:21:51:230 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:21:51:230 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:21:51:231 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.findElements() result: [{"ELEMENT":"01000000-0000-0000-6A24-************"},{"ELEMENT":"05000000-0000-0000-6A24-************"},{"ELEMENT":"08000000-0000-0000-6A24-************"},{"ELEMENT":"0B000000-0000-0000-6A24-************"},{"ELEMENT":"03000000-0000-0000-6A24-************"},{"ELEMENT":"0F000000-0000-0000-6A24-************"},{"ELEMENT":"********-0000-0000-6A24-************"},{"ELEMENT":"1C000000-0000-0000-6A24-************"},{"ELEMENT":"********-0000-0000-6A24-************"},{"ELEMENT":"09000000-0000-0000-6A24-************"},{"ELEMENT":"50000000-0000-0000-6A24-************"},{"ELEMENT":"07000000-0000-0000-6A24-************"},{"ELEMENT":"0A000000-0000-0000-6A24-************"},{"ELEMENT":"0C000000-0000-0000-6A24-************"},{"ELEMENT":"0D000000-0000-0000-6A24-************"},{"ELEMENT":"10000000-0000-0000-6A24-************"},{"ELEMENT":"11000000-0000-0000-6A24-************"},{"ELEMENT":"19000000-0000-0000-6A24-************"},{"ELEMENT":"12000000-0000-0000-6A24-************"},{"ELEMENT":"13000000-0000-0000-6A24-************"},...
2019-11-13 01:21:51:232 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements 200 109724 ms - 1450
2019-11-13 01:21:51:232 - [HTTP] 
2019-11-13 01:21:51:443 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/01000000-0000-0000-6A24-************/text
2019-11-13 01:21:51:448 - [HTTP] {}
2019-11-13 01:21:51:449 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:21:51:450 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:21:51:456 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/01000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:21:51:459 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/01000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/01000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:21:51:728 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:21:51:728 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:21:51:728 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"Covvered\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:21:51:729 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:21:51:730 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/01000000-0000-0000-6A24-************/text 200 286 ms - 82
2019-11-13 01:21:51:730 - [HTTP] 
2019-11-13 01:21:51:876 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/05000000-0000-0000-6A24-************/text
2019-11-13 01:21:51:877 - [HTTP] {}
2019-11-13 01:21:51:877 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:21:51:878 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:21:51:880 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/05000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:21:51:881 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/05000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/05000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:21:52:268 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:21:52:269 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:21:52:269 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:21:52:269 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:21:52:271 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/05000000-0000-0000-6A24-************/text 200 394 ms - 74
2019-11-13 01:21:52:271 - [HTTP] 
2019-11-13 01:21:52:416 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/08000000-0000-0000-6A24-************/text
2019-11-13 01:21:52:416 - [HTTP] {}
2019-11-13 01:21:52:417 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:21:52:417 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:21:52:420 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/08000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:21:52:423 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/08000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/08000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:21:52:808 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:21:52:808 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:21:52:808 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:21:52:808 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:21:52:809 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/08000000-0000-0000-6A24-************/text 200 393 ms - 74
2019-11-13 01:21:52:810 - [HTTP] 
2019-11-13 01:21:52:956 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0B000000-0000-0000-6A24-************/text
2019-11-13 01:21:52:956 - [HTTP] {}
2019-11-13 01:21:52:956 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:21:52:957 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:21:52:961 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0B000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:21:52:961 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0B000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/0B000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:21:53:382 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:21:53:382 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:21:53:382 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:21:53:383 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:21:53:383 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0B000000-0000-0000-6A24-************/text 200 427 ms - 74
2019-11-13 01:21:53:383 - [HTTP] 
2019-11-13 01:21:53:526 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/03000000-0000-0000-6A24-************/text
2019-11-13 01:21:53:526 - [HTTP] {}
2019-11-13 01:21:53:526 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:21:53:526 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:21:53:529 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/03000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:21:53:529 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/03000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/03000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:21:53:925 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:21:53:925 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:21:53:925 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:21:53:925 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:21:53:926 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/03000000-0000-0000-6A24-************/text 200 400 ms - 74
2019-11-13 01:21:53:926 - [HTTP] 
2019-11-13 01:21:54:072 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0F000000-0000-0000-6A24-************/text
2019-11-13 01:21:54:072 - [HTTP] {}
2019-11-13 01:21:54:073 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:21:54:073 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:21:54:076 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0F000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:21:54:076 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0F000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/0F000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:21:54:482 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"7 items added so far 0 warranties expiring soon 7 can be exchanged 7 can be returned 2 in warranty 5 no warranty Dashboard  Dashboard, tab, 1 of 2 My Home, tab, 2 of 2 <NAME_EMAIL>  Account details  Change password  Send feedback  Sign out Terms of service Privacy policy © Covvered 2019 - App version 1.0.0\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:21:54:485 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:21:54:485 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0F000000-0000-0000-6A24-************/text 200 413 ms - 452
2019-11-13 01:21:54:486 - [HTTP] 
2019-11-13 01:21:54:486 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:21:54:487 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:21:54:632 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/********-0000-0000-6A24-************/text
2019-11-13 01:21:54:632 - [HTTP] {}
2019-11-13 01:21:54:633 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:21:54:633 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:21:54:635 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/********-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:21:54:635 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/********-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/********-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:21:55:024 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:21:55:025 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:21:55:025 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"7 items added so far 0 warranties expiring soon 7 can be exchanged 7 can be returned 2 in warranty 5 no warranty Dashboard  Dashboard, tab, 1 of 2 My Home, tab, 2 of 2 <NAME_EMAIL>  Account details  Change password  Send feedback  Sign out Terms of service Privacy policy © Covvered 2019 - App version 1.0.0\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:21:55:025 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:21:55:026 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/********-0000-0000-6A24-************/text 200 394 ms - 452
2019-11-13 01:21:55:027 - [HTTP] 
2019-11-13 01:21:55:177 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/1C000000-0000-0000-6A24-************/text
2019-11-13 01:21:55:178 - [HTTP] {}
2019-11-13 01:21:55:178 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:21:55:178 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:21:55:181 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/1C000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:21:55:181 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/1C000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/1C000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:21:55:576 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"7 items added so far 0 warranties expiring soon 7 can be exchanged 7 can be returned 2 in warranty 5 no warranty Dashboard  Dashboard, tab, 1 of 2 My Home, tab, 2 of 2 <NAME_EMAIL>  Account details  Change password  Send feedback  Sign out Terms of service Privacy policy © Covvered 2019 - App version 1.0.0\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:21:55:577 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:21:55:577 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/1C000000-0000-0000-6A24-************/text 200 400 ms - 452
2019-11-13 01:21:55:578 - [HTTP] 
2019-11-13 01:21:55:578 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:21:55:578 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:21:55:798 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/********-0000-0000-6A24-************/text
2019-11-13 01:21:55:798 - [HTTP] {}
2019-11-13 01:21:55:798 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:21:55:799 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:21:55:800 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/********-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:21:55:801 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/********-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/********-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:21:56:189 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:21:56:189 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:21:56:189 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:21:56:190 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:21:56:191 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/********-0000-0000-6A24-************/text 200 392 ms - 74
2019-11-13 01:21:56:191 - [HTTP] 
2019-11-13 01:21:56:333 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/09000000-0000-0000-6A24-************/text
2019-11-13 01:21:56:333 - [HTTP] {}
2019-11-13 01:21:56:334 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:21:56:334 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:21:56:336 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/09000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:21:56:337 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/09000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/09000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:21:56:750 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:21:56:750 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:21:56:751 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/09000000-0000-0000-6A24-************/text 200 418 ms - 74
2019-11-13 01:21:56:751 - [HTTP] 
2019-11-13 01:21:56:752 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:21:56:752 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:21:56:902 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/50000000-0000-0000-6A24-************/text
2019-11-13 01:21:56:902 - [HTTP] {}
2019-11-13 01:21:56:902 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:21:56:902 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:21:56:904 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/50000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:21:56:904 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/50000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/50000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:21:57:286 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:21:57:286 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:21:57:287 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:21:57:287 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:21:57:287 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/50000000-0000-0000-6A24-************/text 200 386 ms - 74
2019-11-13 01:21:57:288 - [HTTP] 
2019-11-13 01:21:57:445 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/07000000-0000-0000-6A24-************/text
2019-11-13 01:21:57:445 - [HTTP] {}
2019-11-13 01:21:57:445 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:21:57:446 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:21:57:448 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/07000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:21:57:448 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/07000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/07000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:21:57:851 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:21:57:851 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:21:57:851 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/07000000-0000-0000-6A24-************/text 200 406 ms - 74
2019-11-13 01:21:57:852 - [HTTP] 
2019-11-13 01:21:57:852 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:21:57:852 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:21:57:997 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0A000000-0000-0000-6A24-************/text
2019-11-13 01:21:57:998 - [HTTP] {}
2019-11-13 01:21:57:998 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:21:57:998 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:21:58:001 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0A000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:21:58:002 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0A000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/0A000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:21:58:397 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:21:58:397 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:21:58:397 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:21:58:397 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:21:58:398 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0A000000-0000-0000-6A24-************/text 200 400 ms - 74
2019-11-13 01:21:58:398 - [HTTP] 
2019-11-13 01:21:58:544 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0C000000-0000-0000-6A24-************/text
2019-11-13 01:21:58:544 - [HTTP] {}
2019-11-13 01:21:58:545 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:21:58:546 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:21:58:548 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0C000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:21:58:548 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0C000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/0C000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:21:58:942 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:21:58:942 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:21:58:943 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0C000000-0000-0000-6A24-************/text 200 398 ms - 74
2019-11-13 01:21:58:943 - [HTTP] 
2019-11-13 01:21:58:943 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:21:58:943 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:21:59:085 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0D000000-0000-0000-6A24-************/text
2019-11-13 01:21:59:086 - [HTTP] {}
2019-11-13 01:21:59:086 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:21:59:087 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:21:59:089 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0D000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:21:59:089 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0D000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/0D000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:21:59:493 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:21:59:493 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:21:59:494 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0D000000-0000-0000-6A24-************/text 200 408 ms - 74
2019-11-13 01:21:59:494 - [HTTP] 
2019-11-13 01:21:59:494 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:21:59:495 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:21:59:636 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/10000000-0000-0000-6A24-************/text
2019-11-13 01:21:59:637 - [HTTP] {}
2019-11-13 01:21:59:637 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:21:59:637 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:21:59:640 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/10000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:21:59:640 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/10000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/10000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:00:047 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:00:047 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:00:048 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/10000000-0000-0000-6A24-************/text 200 411 ms - 74
2019-11-13 01:22:00:048 - [HTTP] 
2019-11-13 01:22:00:048 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:00:049 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:00:237 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/11000000-0000-0000-6A24-************/text
2019-11-13 01:22:00:237 - [HTTP] {}
2019-11-13 01:22:00:238 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:00:238 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:00:241 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/11000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:00:241 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/11000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/11000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:00:628 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:00:628 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:00:628 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:00:629 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:00:629 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/11000000-0000-0000-6A24-************/text 200 392 ms - 74
2019-11-13 01:22:00:629 - [HTTP] 
2019-11-13 01:22:00:817 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/19000000-0000-0000-6A24-************/text
2019-11-13 01:22:00:817 - [HTTP] {}
2019-11-13 01:22:00:818 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:00:818 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:00:820 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/19000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:00:821 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/19000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/19000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:01:108 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:01:108 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:01:108 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"5:22 PM\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:01:108 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:01:109 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/19000000-0000-0000-6A24-************/text 200 292 ms - 81
2019-11-13 01:22:01:109 - [HTTP] 
2019-11-13 01:22:01:252 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/12000000-0000-0000-6A24-************/text
2019-11-13 01:22:01:253 - [HTTP] {}
2019-11-13 01:22:01:253 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:01:253 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:01:255 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/12000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:01:255 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/12000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/12000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:01:645 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:01:645 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:01:646 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/12000000-0000-0000-6A24-************/text 200 393 ms - 74
2019-11-13 01:22:01:646 - [HTTP] 
2019-11-13 01:22:01:646 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:01:647 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:01:789 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/13000000-0000-0000-6A24-************/text
2019-11-13 01:22:01:790 - [HTTP] {}
2019-11-13 01:22:01:790 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:01:791 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:01:793 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/13000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:01:793 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/13000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/13000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:02:182 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:02:183 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:02:184 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/13000000-0000-0000-6A24-************/text 200 395 ms - 74
2019-11-13 01:22:02:184 - [HTTP] 
2019-11-13 01:22:02:185 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:02:185 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:02:333 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/14000000-0000-0000-6A24-************/text
2019-11-13 01:22:02:333 - [HTTP] {}
2019-11-13 01:22:02:334 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:02:334 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:02:336 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/14000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:02:337 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/14000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/14000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:02:725 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:02:725 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:02:726 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:02:726 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:02:727 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/14000000-0000-0000-6A24-************/text 200 394 ms - 74
2019-11-13 01:22:02:727 - [HTTP] 
2019-11-13 01:22:02:868 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/1A000000-0000-0000-6A24-************/text
2019-11-13 01:22:02:868 - [HTTP] {}
2019-11-13 01:22:02:869 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:02:869 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:02:872 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/1A000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:02:872 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/1A000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/1A000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:02:879 - [debug] [iProxy] ccepted connection, fd = 4
2019-11-13 01:22:02:879 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:879 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:879 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:879 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:879 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:880 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:880 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:880 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:880 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:880 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:880 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:881 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:881 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:881 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:881 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:881 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:881 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:881 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:882 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:882 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:882 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:882 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:882 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:882 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:883 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:883 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:883 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:883 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:884 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:884 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:884 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:884 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:884 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:884 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:885 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:885 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:887 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:888 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:888 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:888 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:888 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:888 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:888 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:888 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:888 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:889 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:889 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:889 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:889 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:889 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:889 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:889 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:889 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:889 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:889 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:890 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:890 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:890 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:890 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:890 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:890 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:890 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:890 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:891 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:891 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:891 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:891 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:891 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:891 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:891 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:891 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:891 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:892 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:892 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:892 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:892 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:892 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:892 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:892 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:892 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:892 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:893 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:893 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:893 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:893 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:893 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:893 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:893 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:893 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:894 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:894 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:894 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:894 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:894 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:894 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:894 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:894 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:895 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:895 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:895 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:895 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:895 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:895 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:895 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:895 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:895 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:896 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:896 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:896 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:896 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:896 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:896 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:896 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:896 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:897 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:897 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:897 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:897 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:897 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:897 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:898 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:899 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:899 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:899 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:899 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:899 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:899 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:900 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:900 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:900 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:900 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:900 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:900 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:900 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:900 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:901 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:901 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:901 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:901 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:901 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:901 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:901 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:901 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:901 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:902 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:902 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:902 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:902 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:902 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:902 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:902 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:902 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:903 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:903 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:903 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:903 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:903 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:903 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:903 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:903 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:904 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:904 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:904 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:904 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:904 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:904 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:904 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:904 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:905 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:905 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:905 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:905 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:905 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:905 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:905 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:905 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:905 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:906 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:906 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:906 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:906 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:906 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:906 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:906 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:906 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:907 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:907 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:907 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:907 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:907 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:907 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:907 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:908 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:908 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:908 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:908 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:908 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:908 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:908 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:909 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:909 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:909 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:909 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:909 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:909 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:909 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:910 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:910 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:910 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:910 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:910 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:910 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:910 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:911 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:911 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:911 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:911 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:911 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:02:911 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:02:911 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:02:912 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:02:912 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:02:912 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:02:912 - [debug] [iProxy] Nu
2019-11-13 01:22:03:162 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:03:162 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:03:163 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"No signal\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:03:163 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:03:164 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/1A000000-0000-0000-6A24-************/text 200 295 ms - 83
2019-11-13 01:22:03:164 - [HTTP] 
2019-11-13 01:22:03:306 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/1B000000-0000-0000-6A24-************/text
2019-11-13 01:22:03:307 - [HTTP] {}
2019-11-13 01:22:03:307 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:03:307 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:03:312 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/1B000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:03:312 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/1B000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/1B000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:03:599 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"Charging\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:03:599 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:03:599 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/1B000000-0000-0000-6A24-************/text 200 293 ms - 82
2019-11-13 01:22:03:600 - [HTTP] 
2019-11-13 01:22:03:600 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:03:600 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:03:745 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/15000000-0000-0000-6A24-************/text
2019-11-13 01:22:03:745 - [HTTP] {}
2019-11-13 01:22:03:747 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:03:747 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:03:750 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/15000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:03:750 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/15000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/15000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:04:127 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:04:127 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:04:127 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:04:128 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:04:128 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/15000000-0000-0000-6A24-************/text 200 383 ms - 74
2019-11-13 01:22:04:129 - [HTTP] 
2019-11-13 01:22:04:276 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/16000000-0000-0000-6A24-************/text
2019-11-13 01:22:04:276 - [HTTP] {}
2019-11-13 01:22:04:277 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:04:277 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:04:279 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/16000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:04:279 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/16000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/16000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:04:675 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:04:675 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:04:676 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/16000000-0000-0000-6A24-************/text 200 400 ms - 74
2019-11-13 01:22:04:676 - [HTTP] 
2019-11-13 01:22:04:677 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:04:677 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:04:819 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/17000000-0000-0000-6A24-************/text
2019-11-13 01:22:04:819 - [HTTP] {}
2019-11-13 01:22:04:820 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:04:820 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:04:822 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/17000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:04:822 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/17000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/17000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:05:206 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:05:206 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:05:206 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:05:206 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:05:207 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/17000000-0000-0000-6A24-************/text 200 388 ms - 74
2019-11-13 01:22:05:207 - [HTTP] 
2019-11-13 01:22:05:348 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0E000000-0000-0000-6A24-************/text
2019-11-13 01:22:05:348 - [HTTP] {}
2019-11-13 01:22:05:349 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:05:349 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:05:351 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0E000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:05:352 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0E000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/0E000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:05:750 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:05:751 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:05:751 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0E000000-0000-0000-6A24-************/text 200 403 ms - 74
2019-11-13 01:22:05:752 - [HTTP] 
2019-11-13 01:22:05:752 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:05:752 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:05:892 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements
2019-11-13 01:22:05:892 - [HTTP] {"using":"accessibility id","value":"contextMenu"}
2019-11-13 01:22:05:893 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.findElements() with args: ["accessibility id","contextMenu","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:22:05:893 - [debug] [XCUITest] Executing command 'findElements'
2019-11-13 01:22:05:896 - [debug] [BaseDriver] Valid locator strategies for this request: xpath, id, name, class name, -ios predicate string, -ios class chain, accessibility id
2019-11-13 01:22:05:896 - [debug] [BaseDriver] Waiting up to 0 ms for condition
2019-11-13 01:22:05:897 - [debug] [JSONWP Proxy] Matched '/elements' to command name 'findElements'
2019-11-13 01:22:05:898 - [debug] [JSONWP Proxy] Proxying [POST /elements] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/elements] with body: {"using":"accessibility id","value":"contextMenu"}
2019-11-13 01:22:06:272 - [debug] [JSONWP Proxy] Got response with status 200: {"value":[{"ELEMENT":"EA000000-0000-0000-6A24-************"}],"sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8","status":0}
2019-11-13 01:22:06:272 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:06:272 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:06:273 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.findElements() result: [{"ELEMENT":"EA000000-0000-0000-6A24-************"}]
2019-11-13 01:22:06:274 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements 200 381 ms - 124
2019-11-13 01:22:06:274 - [HTTP] 
2019-11-13 01:22:06:419 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/EA000000-0000-0000-6A24-************/displayed
2019-11-13 01:22:06:419 - [HTTP] {}
2019-11-13 01:22:06:420 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:06:420 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:06:423 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/EA000000-0000-0000-6A24-************/displayed' to command name 'elementDisplayed'
2019-11-13 01:22:06:423 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/EA000000-0000-0000-6A24-************/displayed] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/EA000000-0000-0000-6A24-************/displayed] with body: {}
2019-11-13 01:22:06:774 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : true,\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:06:774 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:06:775 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/EA000000-0000-0000-6A24-************/displayed 200 356 ms - 76
2019-11-13 01:22:06:775 - [HTTP] 
2019-11-13 01:22:06:775 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:06:776 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:06:919 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements
2019-11-13 01:22:06:919 - [HTTP] {"using":"accessibility id","value":"contextMenu"}
2019-11-13 01:22:06:919 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.findElements() with args: ["accessibility id","contextMenu","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:22:06:920 - [debug] [XCUITest] Executing command 'findElements'
2019-11-13 01:22:06:921 - [debug] [BaseDriver] Valid locator strategies for this request: xpath, id, name, class name, -ios predicate string, -ios class chain, accessibility id
2019-11-13 01:22:06:921 - [debug] [BaseDriver] Waiting up to 0 ms for condition
2019-11-13 01:22:06:923 - [debug] [JSONWP Proxy] Matched '/elements' to command name 'findElements'
2019-11-13 01:22:06:923 - [debug] [JSONWP Proxy] Proxying [POST /elements] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/elements] with body: {"using":"accessibility id","value":"contextMenu"}
2019-11-13 01:22:07:303 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:07:303 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:07:303 - [debug] [JSONWP Proxy] Got response with status 200: {"value":[{"ELEMENT":"EA000000-0000-0000-6A24-************"}],"sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8","status":0}
2019-11-13 01:22:07:304 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.findElements() result: [{"ELEMENT":"EA000000-0000-0000-6A24-************"}]
2019-11-13 01:22:07:304 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements 200 385 ms - 124
2019-11-13 01:22:07:305 - [HTTP] 
2019-11-13 01:22:07:451 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/touch/click
2019-11-13 01:22:07:452 - [HTTP] {"element":"EA000000-0000-0000-6A24-************"}
2019-11-13 01:22:07:453 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.click() with args: ["EA000000-0000-0000-6A24-************","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:22:07:453 - [debug] [XCUITest] Executing command 'click'
2019-11-13 01:22:07:455 - [debug] [JSONWP Proxy] Matched '/element/EA000000-0000-0000-6A24-************/click' to command name 'click'
2019-11-13 01:22:07:456 - [debug] [JSONWP Proxy] Proxying [POST /element/EA000000-0000-0000-6A24-************/click] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/EA000000-0000-0000-6A24-************/click] with body: {}
2019-11-13 01:22:08:400 - [debug] [JSONWP Proxy] Got response with status 200: {"status":0,"id":"EA000000-0000-0000-6A24-************","value":"","sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8"}
2019-11-13 01:22:08:400 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:08:401 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:08:401 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.click() result: ""
2019-11-13 01:22:08:402 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/touch/click 200 950 ms - 74
2019-11-13 01:22:08:402 - [HTTP] 
2019-11-13 01:22:08:571 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements
2019-11-13 01:22:08:571 - [HTTP] {"using":"accessibility id","value":"accountDetails"}
2019-11-13 01:22:08:572 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.findElements() with args: ["accessibility id","accountDetails","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:22:08:572 - [debug] [XCUITest] Executing command 'findElements'
2019-11-13 01:22:08:574 - [debug] [BaseDriver] Valid locator strategies for this request: xpath, id, name, class name, -ios predicate string, -ios class chain, accessibility id
2019-11-13 01:22:08:574 - [debug] [BaseDriver] Waiting up to 0 ms for condition
2019-11-13 01:22:08:575 - [debug] [JSONWP Proxy] Matched '/elements' to command name 'findElements'
2019-11-13 01:22:08:575 - [debug] [JSONWP Proxy] Proxying [POST /elements] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/elements] with body: {"using":"accessibility id","value":"accountDetails"}
2019-11-13 01:22:08:969 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:08:970 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:08:970 - [debug] [JSONWP Proxy] Got response with status 200: {"value":[{"ELEMENT":"DC000000-0000-0000-6A24-************"}],"sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8","status":0}
2019-11-13 01:22:08:971 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.findElements() result: [{"ELEMENT":"DC000000-0000-0000-6A24-************"}]
2019-11-13 01:22:08:971 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements 200 400 ms - 124
2019-11-13 01:22:08:971 - [HTTP] 
2019-11-13 01:22:09:163 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/DC000000-0000-0000-6A24-************/displayed
2019-11-13 01:22:09:163 - [HTTP] {}
2019-11-13 01:22:09:164 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:09:164 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:09:166 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/DC000000-0000-0000-6A24-************/displayed' to command name 'elementDisplayed'
2019-11-13 01:22:09:166 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/DC000000-0000-0000-6A24-************/displayed] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/DC000000-0000-0000-6A24-************/displayed] with body: {}
2019-11-13 01:22:09:535 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : true,\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:09:536 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:09:536 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/DC000000-0000-0000-6A24-************/displayed 200 373 ms - 76
2019-11-13 01:22:09:537 - [HTTP] 
2019-11-13 01:22:09:537 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:09:537 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:09:685 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements
2019-11-13 01:22:09:685 - [HTTP] {"using":"accessibility id","value":"accountDetails"}
2019-11-13 01:22:09:685 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.findElements() with args: ["accessibility id","accountDetails","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:22:09:686 - [debug] [XCUITest] Executing command 'findElements'
2019-11-13 01:22:09:688 - [debug] [BaseDriver] Valid locator strategies for this request: xpath, id, name, class name, -ios predicate string, -ios class chain, accessibility id
2019-11-13 01:22:09:688 - [debug] [BaseDriver] Waiting up to 0 ms for condition
2019-11-13 01:22:09:689 - [debug] [JSONWP Proxy] Matched '/elements' to command name 'findElements'
2019-11-13 01:22:09:689 - [debug] [JSONWP Proxy] Proxying [POST /elements] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/elements] with body: {"using":"accessibility id","value":"accountDetails"}
2019-11-13 01:22:09:971 - [debug] [JSONWP Proxy] Got response with status 200: {"value":[{"ELEMENT":"DC000000-0000-0000-6A24-************"}],"sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8","status":0}
2019-11-13 01:22:09:972 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.findElements() result: [{"ELEMENT":"DC000000-0000-0000-6A24-************"}]
2019-11-13 01:22:09:973 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements 200 288 ms - 124
2019-11-13 01:22:09:974 - [HTTP] 
2019-11-13 01:22:09:974 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:09:974 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:10:139 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/touch/click
2019-11-13 01:22:10:139 - [HTTP] {"element":"DC000000-0000-0000-6A24-************"}
2019-11-13 01:22:10:139 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.click() with args: ["DC000000-0000-0000-6A24-************","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:22:10:140 - [debug] [XCUITest] Executing command 'click'
2019-11-13 01:22:10:141 - [debug] [JSONWP Proxy] Matched '/element/DC000000-0000-0000-6A24-************/click' to command name 'click'
2019-11-13 01:22:10:142 - [debug] [JSONWP Proxy] Proxying [POST /element/DC000000-0000-0000-6A24-************/click] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/DC000000-0000-0000-6A24-************/click] with body: {}
2019-11-13 01:22:11:092 - [debug] [JSONWP Proxy] Got response with status 200: {"status":0,"id":"DC000000-0000-0000-6A24-************","value":"","sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8"}
2019-11-13 01:22:11:092 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:11:092 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:11:093 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.click() result: ""
2019-11-13 01:22:11:093 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/touch/click 200 955 ms - 74
2019-11-13 01:22:11:094 - [HTTP] 
2019-11-13 01:22:11:236 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements
2019-11-13 01:22:11:236 - [HTTP] {"using":"xpath","value":"//*"}
2019-11-13 01:22:11:237 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.findElements() with args: ["xpath","//*","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:22:11:237 - [debug] [XCUITest] Executing command 'findElements'
2019-11-13 01:22:11:239 - [debug] [BaseDriver] Valid locator strategies for this request: xpath, id, name, class name, -ios predicate string, -ios class chain, accessibility id
2019-11-13 01:22:11:239 - [debug] [BaseDriver] Waiting up to 0 ms for condition
2019-11-13 01:22:11:240 - [debug] [JSONWP Proxy] Matched '/elements' to command name 'findElements'
2019-11-13 01:22:11:240 - [debug] [JSONWP Proxy] Proxying [POST /elements] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/elements] with body: {"using":"xpath","value":"//*"}
2019-11-13 01:22:48:960 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:48:960 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:48:961 - [debug] [JSONWP Proxy] Got response with status 200: {"value":[{"ELEMENT":"01000000-0000-0000-6A24-************"},{"ELEMENT":"05000000-0000-0000-6A24-************"},{"ELEMENT":"08000000-0000-0000-6A24-************"},{"ELEMENT":"0B000000-0000-0000-6A24-************"},{"ELEMENT":"03000000-0000-0000-6A24-************"},{"ELEMENT":"0F000000-0000-0000-6A24-************"},{"ELEMENT":"********-0000-0000-6A24-************"},{"ELEMENT":"1C000000-0000-0000-6A24-************"},{"ELEMENT":"AB000000-0000-0000-6A24-************"},{"ELEMENT":"AC000000-0000-0000-6A24-************"},{"ELEMENT":"AD000000-0000-0000-6A24-************"},{"ELEMENT":"AE000000-0000-0000-6A24-************"},{"ELEMENT":"AF000000-0000-0000-6A24-************"},{"ELEMENT":"2A010000-0000-0000-6A24-************"},{"ELEMENT":"2B010000-0000-0000-6A24-************"},{"ELEMENT":"2C010000-0000-0000-6A24-************"},{"ELEMENT":"2D010000-0000-0000-6A24-************"},{"ELEMENT":"2E010000-0000-0000-6A24-************"},{"ELEMENT":"********-0000-0000-6A24-************"},{"ELEMENT":"********-0000-0000-6A24-000000...
2019-11-13 01:22:48:963 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.findElements() result: [{"ELEMENT":"01000000-0000-0000-6A24-************"},{"ELEMENT":"05000000-0000-0000-6A24-************"},{"ELEMENT":"08000000-0000-0000-6A24-************"},{"ELEMENT":"0B000000-0000-0000-6A24-************"},{"ELEMENT":"03000000-0000-0000-6A24-************"},{"ELEMENT":"0F000000-0000-0000-6A24-************"},{"ELEMENT":"********-0000-0000-6A24-************"},{"ELEMENT":"1C000000-0000-0000-6A24-************"},{"ELEMENT":"AB000000-0000-0000-6A24-************"},{"ELEMENT":"AC000000-0000-0000-6A24-************"},{"ELEMENT":"AD000000-0000-0000-6A24-************"},{"ELEMENT":"AE000000-0000-0000-6A24-************"},{"ELEMENT":"AF000000-0000-0000-6A24-************"},{"ELEMENT":"2A010000-0000-0000-6A24-************"},{"ELEMENT":"2B010000-0000-0000-6A24-************"},{"ELEMENT":"2C010000-0000-0000-6A24-************"},{"ELEMENT":"2D010000-0000-0000-6A24-************"},{"ELEMENT":"2E010000-0000-0000-6A24-************"},{"ELEMENT":"********-0000-0000-6A24-************"},{"ELEMENT":"********-0000-0000-6A24-************"},...
2019-11-13 01:22:48:964 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements 200 37726 ms - 2164
2019-11-13 01:22:48:964 - [HTTP] 
2019-11-13 01:22:49:175 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/01000000-0000-0000-6A24-************/text
2019-11-13 01:22:49:176 - [HTTP] {}
2019-11-13 01:22:49:176 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:49:177 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:49:183 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/01000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:49:184 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/01000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/01000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:49:411 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:49:412 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:49:412 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"Covvered\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:49:412 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:49:413 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/01000000-0000-0000-6A24-************/text 200 237 ms - 82
2019-11-13 01:22:49:413 - [HTTP] 
2019-11-13 01:22:49:561 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/05000000-0000-0000-6A24-************/text
2019-11-13 01:22:49:561 - [HTTP] {}
2019-11-13 01:22:49:562 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:49:562 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:49:564 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/05000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:49:565 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/05000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/05000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:49:849 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:49:849 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:49:850 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/05000000-0000-0000-6A24-************/text 200 289 ms - 74
2019-11-13 01:22:49:850 - [HTTP] 
2019-11-13 01:22:49:851 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:49:851 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:49:999 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/08000000-0000-0000-6A24-************/text
2019-11-13 01:22:49:999 - [HTTP] {}
2019-11-13 01:22:50:000 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:50:000 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:50:003 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/08000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:50:003 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/08000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/08000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:50:298 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:50:298 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:50:299 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:50:299 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:50:301 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/08000000-0000-0000-6A24-************/text 200 301 ms - 74
2019-11-13 01:22:50:301 - [HTTP] 
2019-11-13 01:22:50:447 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0B000000-0000-0000-6A24-************/text
2019-11-13 01:22:50:448 - [HTTP] {}
2019-11-13 01:22:50:448 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:50:448 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:50:451 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0B000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:50:452 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0B000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/0B000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:50:742 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:50:742 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:50:743 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0B000000-0000-0000-6A24-************/text 200 295 ms - 74
2019-11-13 01:22:50:743 - [HTTP] 
2019-11-13 01:22:50:744 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:50:744 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:50:889 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/03000000-0000-0000-6A24-************/text
2019-11-13 01:22:50:889 - [HTTP] {}
2019-11-13 01:22:50:890 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:50:891 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:50:898 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/03000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:50:899 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/03000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/03000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:51:208 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:51:209 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:51:209 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/03000000-0000-0000-6A24-************/text 200 321 ms - 74
2019-11-13 01:22:51:210 - [HTTP] 
2019-11-13 01:22:51:210 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:51:210 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:51:355 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0F000000-0000-0000-6A24-************/text
2019-11-13 01:22:51:355 - [HTTP] {}
2019-11-13 01:22:51:356 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:51:356 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:51:358 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0F000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:51:359 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0F000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/0F000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:51:674 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:51:675 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:51:675 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"7 items added so far 0 warranties expiring soon 7 can be exchanged 7 can be returned 2 in warranty 5 no warranty Dashboard  Dashboard, tab, 1 of 2 My Home, tab, 2 of 2 <NAME_EMAIL>  Account details  Change password  Send feedback  Sign out Terms of service Privacy policy © Covvered 2019 - App version 1.0.0 E-mail You cannot change your email address First name Last name Date of Birth 13 September 1998 Country Congo Save changes Account details\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:51:675 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:51:676 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0F000000-0000-0000-6A24-************/text 200 320 ms - 592
2019-11-13 01:22:51:677 - [HTTP] 
2019-11-13 01:22:51:821 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/********-0000-0000-6A24-************/text
2019-11-13 01:22:51:821 - [HTTP] {}
2019-11-13 01:22:51:822 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:51:822 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:51:826 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/********-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:51:827 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/********-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/********-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:52:115 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:52:115 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:52:115 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"7 items added so far 0 warranties expiring soon 7 can be exchanged 7 can be returned 2 in warranty 5 no warranty Dashboard  Dashboard, tab, 1 of 2 My Home, tab, 2 of 2 <NAME_EMAIL>  Account details  Change password  Send feedback  Sign out Terms of service Privacy policy © Covvered 2019 - App version 1.0.0 E-mail You cannot change your email address First name Last name Date of Birth 13 September 1998 Country Congo Save changes Account details\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:52:116 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:52:117 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/********-0000-0000-6A24-************/text 200 295 ms - 592
2019-11-13 01:22:52:117 - [HTTP] 
2019-11-13 01:22:52:259 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/1C000000-0000-0000-6A24-************/text
2019-11-13 01:22:52:260 - [HTTP] {}
2019-11-13 01:22:52:260 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:52:260 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:52:262 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/1C000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:52:262 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/1C000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/1C000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:52:565 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"7 items added so far 0 warranties expiring soon 7 can be exchanged 7 can be returned 2 in warranty 5 no warranty Dashboard  Dashboard, tab, 1 of 2 My Home, tab, 2 of 2 <NAME_EMAIL>  Account details  Change password  Send feedback  Sign out Terms of service Privacy policy © Covvered 2019 - App version 1.0.0 E-mail You cannot change your email address First name Last name Date of Birth 13 September 1998 Country Congo Save changes Account details\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:52:565 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:52:566 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/1C000000-0000-0000-6A24-************/text 200 306 ms - 592
2019-11-13 01:22:52:566 - [HTTP] 
2019-11-13 01:22:52:567 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:52:567 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:52:709 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/AB000000-0000-0000-6A24-************/text
2019-11-13 01:22:52:709 - [HTTP] {}
2019-11-13 01:22:52:709 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:52:709 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:52:712 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/AB000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:52:712 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/AB000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/AB000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:53:028 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:53:028 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:53:028 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"7 items added so far 0 warranties expiring soon 7 can be exchanged 7 can be returned 2 in warranty 5 no warranty Dashboard  Dashboard, tab, 1 of 2 My Home, tab, 2 of 2 <NAME_EMAIL>  Account details  Change password  Send feedback  Sign out Terms of service Privacy policy © Covvered 2019 - App version 1.0.0 E-mail You cannot change your email address First name Last name Date of Birth 13 September 1998 Country Congo Save changes Account details\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:53:029 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:53:029 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/AB000000-0000-0000-6A24-************/text 200 320 ms - 592
2019-11-13 01:22:53:029 - [HTTP] 
2019-11-13 01:22:53:177 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/AC000000-0000-0000-6A24-************/text
2019-11-13 01:22:53:177 - [HTTP] {}
2019-11-13 01:22:53:181 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:53:181 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:53:183 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/AC000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:53:183 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/AC000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/AC000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:53:465 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:53:465 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:53:466 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"7 items added so far 0 warranties expiring soon 7 can be exchanged 7 can be returned 2 in warranty 5 no warranty Dashboard  Dashboard, tab, 1 of 2 My Home, tab, 2 of 2 <NAME_EMAIL>  Account details  Change password  Send feedback  Sign out Terms of service Privacy policy © Covvered 2019 - App version 1.0.0 E-mail You cannot change your email address First name Last name Date of Birth 13 September 1998 Country Congo Save changes Account details\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:53:466 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:53:467 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/AC000000-0000-0000-6A24-************/text 200 289 ms - 592
2019-11-13 01:22:53:467 - [HTTP] 
2019-11-13 01:22:53:619 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/AD000000-0000-0000-6A24-************/text
2019-11-13 01:22:53:619 - [HTTP] {}
2019-11-13 01:22:53:619 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:53:620 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:53:622 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/AD000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:53:622 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/AD000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/AD000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:53:951 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:53:951 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:53:951 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"7 items added so far 0 warranties expiring soon 7 can be exchanged 7 can be returned 2 in warranty 5 no warranty Dashboard  Dashboard, tab, 1 of 2 My Home, tab, 2 of 2 <NAME_EMAIL>  Account details  Change password  Send feedback  Sign out Terms of service Privacy policy © Covvered 2019 - App version 1.0.0 E-mail You cannot change your email address First name Last name Date of Birth 13 September 1998 Country Congo Save changes Account details\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:53:951 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:53:952 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/AD000000-0000-0000-6A24-************/text 200 333 ms - 592
2019-11-13 01:22:53:952 - [HTTP] 
2019-11-13 01:22:54:094 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/AE000000-0000-0000-6A24-************/text
2019-11-13 01:22:54:094 - [HTTP] {}
2019-11-13 01:22:54:095 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:54:095 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:54:097 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/AE000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:54:097 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/AE000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/AE000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:54:390 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"7 items added so far 0 warranties expiring soon 7 can be exchanged 7 can be returned 2 in warranty 5 no warranty Dashboard  Dashboard, tab, 1 of 2 My Home, tab, 2 of 2 <NAME_EMAIL>  Account details  Change password  Send feedback  Sign out Terms of service Privacy policy © Covvered 2019 - App version 1.0.0\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:54:391 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:54:392 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/AE000000-0000-0000-6A24-************/text 200 297 ms - 452
2019-11-13 01:22:54:392 - [HTTP] 
2019-11-13 01:22:54:392 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:54:392 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:54:541 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/AF000000-0000-0000-6A24-************/text
2019-11-13 01:22:54:542 - [HTTP] {}
2019-11-13 01:22:54:542 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:54:542 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:54:544 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/AF000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:54:545 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/AF000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/AF000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:54:856 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:54:856 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:54:857 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:54:857 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:54:858 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/AF000000-0000-0000-6A24-************/text 200 316 ms - 74
2019-11-13 01:22:54:858 - [HTTP] 
2019-11-13 01:22:55:012 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/2A010000-0000-0000-6A24-************/text
2019-11-13 01:22:55:012 - [HTTP] {}
2019-11-13 01:22:55:012 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:55:012 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:55:017 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/2A010000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:55:017 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/2A010000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/2A010000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:55:298 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:55:298 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:55:298 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"E-mail You cannot change your email address First name Last name Date of Birth 13 September 1998 Country Congo Save changes Account details\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:55:299 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:55:300 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/2A010000-0000-0000-6A24-************/text 200 288 ms - 213
2019-11-13 01:22:55:300 - [HTTP] 
2019-11-13 01:22:55:443 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/2B010000-0000-0000-6A24-************/text
2019-11-13 01:22:55:443 - [HTTP] {}
2019-11-13 01:22:55:444 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:55:444 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:55:446 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/2B010000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:55:446 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/2B010000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/2B010000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:55:752 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:55:752 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:55:753 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/2B010000-0000-0000-6A24-************/text 200 309 ms - 74
2019-11-13 01:22:55:753 - [HTTP] 
2019-11-13 01:22:55:753 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:55:756 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:55:905 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/2C010000-0000-0000-6A24-************/text
2019-11-13 01:22:55:905 - [HTTP] {}
2019-11-13 01:22:55:906 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:55:906 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:55:908 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/2C010000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:55:908 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/2C010000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/2C010000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:56:238 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:56:238 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:56:238 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"E-mail You cannot change your email address First name Last name Date of Birth 13 September 1998 Country Congo Save changes Account details\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:56:239 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:56:239 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/2C010000-0000-0000-6A24-************/text 200 334 ms - 213
2019-11-13 01:22:56:240 - [HTTP] 
2019-11-13 01:22:56:384 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/2D010000-0000-0000-6A24-************/text
2019-11-13 01:22:56:384 - [HTTP] {}
2019-11-13 01:22:56:385 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:56:385 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:56:387 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/2D010000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:56:387 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/2D010000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/2D010000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:56:687 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:56:688 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:56:688 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"E-mail You cannot change your email address First name Last name Date of Birth 13 September 1998 Country Congo Save changes Account details\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:56:688 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:56:689 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/2D010000-0000-0000-6A24-************/text 200 305 ms - 213
2019-11-13 01:22:56:689 - [HTTP] 
2019-11-13 01:22:56:834 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/2E010000-0000-0000-6A24-************/text
2019-11-13 01:22:56:834 - [HTTP] {}
2019-11-13 01:22:56:835 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:56:837 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:56:840 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/2E010000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:56:840 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/2E010000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/2E010000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:57:143 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:57:143 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:57:143 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"Account details\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:57:144 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:57:145 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/2E010000-0000-0000-6A24-************/text 200 310 ms - 89
2019-11-13 01:22:57:145 - [HTTP] 
2019-11-13 01:22:57:291 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/********-0000-0000-6A24-************/text
2019-11-13 01:22:57:291 - [HTTP] {}
2019-11-13 01:22:57:292 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:57:292 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:57:294 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/********-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:57:294 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/********-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/********-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:57:613 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:57:613 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:57:616 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"Account details\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:57:617 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:57:617 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/********-0000-0000-6A24-************/text 200 326 ms - 89
2019-11-13 01:22:57:618 - [HTTP] 
2019-11-13 01:22:57:760 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/********-0000-0000-6A24-************/text
2019-11-13 01:22:57:760 - [HTTP] {}
2019-11-13 01:22:57:760 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:57:761 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:57:763 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/********-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:57:763 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/********-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/********-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:58:058 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:58:058 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:58:059 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:58:059 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:58:061 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/********-0000-0000-6A24-************/text 200 300 ms - 74
2019-11-13 01:22:58:061 - [HTTP] 
2019-11-13 01:22:58:205 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/********-0000-0000-6A24-************/text
2019-11-13 01:22:58:205 - [HTTP] {}
2019-11-13 01:22:58:206 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:58:206 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:58:208 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/********-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:58:209 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/********-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/********-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:58:425 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:58:425 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:58:426 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"Account details\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:58:426 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:58:427 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/********-0000-0000-6A24-************/text 200 221 ms - 89
2019-11-13 01:22:58:427 - [HTTP] 
2019-11-13 01:22:58:570 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/2F010000-0000-0000-6A24-************/text
2019-11-13 01:22:58:570 - [HTTP] {}
2019-11-13 01:22:58:571 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:58:571 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:58:573 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/2F010000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:58:573 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/2F010000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/2F010000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:58:859 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:58:859 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:58:860 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"E-mail You cannot change your email address First name Last name Date of Birth 13 September 1998 Country Congo Save changes\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:58:860 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:58:861 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/2F010000-0000-0000-6A24-************/text 200 290 ms - 197
2019-11-13 01:22:58:861 - [HTTP] 
2019-11-13 01:22:59:004 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/********-0000-0000-6A24-************/text
2019-11-13 01:22:59:004 - [HTTP] {}
2019-11-13 01:22:59:005 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:59:005 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:59:007 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/********-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:59:007 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/********-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/********-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:59:012 - [debug] [iProxy] mber of available devices == 7
2019-11-13 01:22:59:012 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:015 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:016 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:016 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:016 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:016 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:016 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:016 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:016 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:016 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:017 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:017 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:017 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:017 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:017 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:017 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:018 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:018 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:018 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:018 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:018 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:018 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:018 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:019 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:019 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:019 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:019 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:019 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:019 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:019 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:019 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:019 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:020 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:020 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:020 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:020 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:020 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:020 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:020 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:020 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:020 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:021 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:021 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:021 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:021 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:021 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:021 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:021 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:021 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:021 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:022 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:022 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:022 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:022 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:022 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:022 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:022 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:022 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:022 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:023 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:023 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:023 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:023 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:023 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:023 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:023 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:023 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:023 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:024 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:024 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:024 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:024 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:024 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:024 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:024 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:024 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:024 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:025 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:025 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:025 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:025 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:025 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:025 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:025 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:025 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:026 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:026 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:026 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:026 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:026 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:026 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:026 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:026 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:027 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:027 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:027 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:027 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:027 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:027 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:027 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:027 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:027 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:028 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:028 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:028 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:028 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:028 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:028 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:028 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:028 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:028 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:029 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:029 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:029 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:029 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:029 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:029 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:029 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:029 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:029 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:030 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:030 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:030 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:030 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:030 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:030 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:030 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:030 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:031 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:031 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:031 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:031 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:031 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:031 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:031 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:031 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:032 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:032 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:032 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:032 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:032 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:032 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:032 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:032 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:033 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:033 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:033 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:033 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:033 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:033 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:033 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:033 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:033 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:034 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:034 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:034 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:034 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:034 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:034 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:034 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:034 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:035 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:035 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:036 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:036 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:036 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:036 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:036 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:036 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:036 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:036 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:037 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:037 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:037 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:037 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:037 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:037 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:037 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:037 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:037 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:038 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:038 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:038 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:038 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:038 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:038 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:038 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:039 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:039 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:039 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:039 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:039 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:039 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:039 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:039 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:039 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:040 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:040 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:040 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:040 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:040 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:040 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:040 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:040 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:040 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:041 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:041 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:041 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:041 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:041 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:041 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:041 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:041 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:041 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:042 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:042 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:046 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:22:59:047 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:22:59:047 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:22:59:047 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:22:59:047 - [debug] [iProxy] waiting for connection
2019-11-13 01:22:59:047 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:22:59:047 - [debug] [iProxy] Requesting connecion 
2019-11-13 01:22:59:291 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:59:292 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:59:292 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/********-0000-0000-6A24-************/text 200 288 ms - 74
2019-11-13 01:22:59:293 - [HTTP] 
2019-11-13 01:22:59:293 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:59:293 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:59:443 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/09000000-0000-0000-6A24-************/text
2019-11-13 01:22:59:443 - [HTTP] {}
2019-11-13 01:22:59:443 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:59:444 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:59:446 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/09000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:59:446 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/09000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/09000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:22:59:762 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:22:59:762 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:22:59:763 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/09000000-0000-0000-6A24-************/text 200 320 ms - 74
2019-11-13 01:22:59:763 - [HTTP] 
2019-11-13 01:22:59:763 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:22:59:763 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:22:59:904 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/50000000-0000-0000-6A24-************/text
2019-11-13 01:22:59:905 - [HTTP] {}
2019-11-13 01:22:59:905 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:22:59:905 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:22:59:907 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/50000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:22:59:907 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/50000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/50000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:23:00:209 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:00:209 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:23:00:212 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:23:00:212 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:23:00:213 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/50000000-0000-0000-6A24-************/text 200 308 ms - 74
2019-11-13 01:23:00:213 - [HTTP] 
2019-11-13 01:23:00:359 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/07000000-0000-0000-6A24-************/text
2019-11-13 01:23:00:359 - [HTTP] {}
2019-11-13 01:23:00:360 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:23:00:360 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:23:00:362 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/07000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:23:00:362 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/07000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/07000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:23:00:671 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:23:00:672 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:23:00:672 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/07000000-0000-0000-6A24-************/text 200 313 ms - 74
2019-11-13 01:23:00:673 - [HTTP] 
2019-11-13 01:23:00:673 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:00:673 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:23:00:815 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0A000000-0000-0000-6A24-************/text
2019-11-13 01:23:00:815 - [HTTP] {}
2019-11-13 01:23:00:815 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:23:00:816 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:23:00:818 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0A000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:23:00:818 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0A000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/0A000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:23:01:122 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:01:122 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:23:01:123 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:23:01:123 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:23:01:124 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0A000000-0000-0000-6A24-************/text 200 308 ms - 74
2019-11-13 01:23:01:124 - [HTTP] 
2019-11-13 01:23:01:275 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0C000000-0000-0000-6A24-************/text
2019-11-13 01:23:01:276 - [HTTP] {}
2019-11-13 01:23:01:276 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:23:01:276 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:23:01:280 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0C000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:23:01:280 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0C000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/0C000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:23:01:583 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:01:583 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:23:01:583 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:23:01:583 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:23:01:584 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0C000000-0000-0000-6A24-************/text 200 308 ms - 74
2019-11-13 01:23:01:584 - [HTTP] 
2019-11-13 01:23:01:727 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0D000000-0000-0000-6A24-************/text
2019-11-13 01:23:01:727 - [HTTP] {}
2019-11-13 01:23:01:727 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:23:01:728 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:23:01:730 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0D000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:23:01:730 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0D000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/0D000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:23:02:062 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:23:02:062 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:23:02:062 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0D000000-0000-0000-6A24-************/text 200 335 ms - 74
2019-11-13 01:23:02:063 - [HTTP] 
2019-11-13 01:23:02:063 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:02:063 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:23:02:271 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/10000000-0000-0000-6A24-************/text
2019-11-13 01:23:02:271 - [HTTP] {}
2019-11-13 01:23:02:271 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:23:02:272 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:23:02:274 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/10000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:23:02:274 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/10000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/10000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:23:02:569 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:02:570 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:23:02:570 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:23:02:570 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:23:02:571 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/10000000-0000-0000-6A24-************/text 200 300 ms - 74
2019-11-13 01:23:02:571 - [HTTP] 
2019-11-13 01:23:02:714 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/11000000-0000-0000-6A24-************/text
2019-11-13 01:23:02:714 - [HTTP] {}
2019-11-13 01:23:02:715 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:23:02:715 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:23:02:717 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/11000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:23:02:717 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/11000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/11000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:23:03:025 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:23:03:025 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:23:03:025 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/11000000-0000-0000-6A24-************/text 200 311 ms - 74
2019-11-13 01:23:03:026 - [HTTP] 
2019-11-13 01:23:03:026 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:03:026 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:23:03:184 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/19000000-0000-0000-6A24-************/text
2019-11-13 01:23:03:184 - [HTTP] {}
2019-11-13 01:23:03:184 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:23:03:185 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:23:03:187 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/19000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:23:03:187 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/19000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/19000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:23:03:396 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:03:396 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:23:03:396 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"5:23 PM\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:23:03:397 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:23:03:397 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/19000000-0000-0000-6A24-************/text 200 213 ms - 81
2019-11-13 01:23:03:400 - [HTTP] 
2019-11-13 01:23:03:546 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/12000000-0000-0000-6A24-************/text
2019-11-13 01:23:03:546 - [HTTP] {}
2019-11-13 01:23:03:546 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:23:03:547 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:23:03:549 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/12000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:23:03:549 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/12000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/12000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:23:03:838 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:03:838 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:23:03:838 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:23:03:839 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:23:03:839 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/12000000-0000-0000-6A24-************/text 200 293 ms - 74
2019-11-13 01:23:03:839 - [HTTP] 
2019-11-13 01:23:03:997 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/13000000-0000-0000-6A24-************/text
2019-11-13 01:23:03:997 - [HTTP] {}
2019-11-13 01:23:03:997 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:23:03:998 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:23:04:000 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/13000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:23:04:000 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/13000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/13000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:23:04:293 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:23:04:293 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:23:04:295 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/13000000-0000-0000-6A24-************/text 200 298 ms - 74
2019-11-13 01:23:04:295 - [HTTP] 
2019-11-13 01:23:04:295 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:04:295 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:23:04:452 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/14000000-0000-0000-6A24-************/text
2019-11-13 01:23:04:452 - [HTTP] {}
2019-11-13 01:23:04:453 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:23:04:453 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:23:04:456 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/14000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:23:04:456 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/14000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/14000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:23:04:768 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:04:771 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:23:04:772 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:23:04:772 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:23:04:773 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/14000000-0000-0000-6A24-************/text 200 321 ms - 74
2019-11-13 01:23:04:773 - [HTTP] 
2019-11-13 01:23:04:961 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/1A000000-0000-0000-6A24-************/text
2019-11-13 01:23:04:961 - [HTTP] {}
2019-11-13 01:23:04:962 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:23:04:962 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:23:04:965 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/1A000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:23:04:966 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/1A000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/1A000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:23:05:186 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:05:186 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:23:05:187 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"No signal\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:23:05:187 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:23:05:188 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/1A000000-0000-0000-6A24-************/text 200 226 ms - 83
2019-11-13 01:23:05:188 - [HTTP] 
2019-11-13 01:23:05:336 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/1B000000-0000-0000-6A24-************/text
2019-11-13 01:23:05:336 - [HTTP] {}
2019-11-13 01:23:05:337 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:23:05:337 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:23:05:339 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/1B000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:23:05:339 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/1B000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/1B000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:23:05:577 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"Charging\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:23:05:577 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:23:05:578 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/1B000000-0000-0000-6A24-************/text 200 241 ms - 82
2019-11-13 01:23:05:578 - [HTTP] 
2019-11-13 01:23:05:578 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:05:578 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:23:05:725 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/15000000-0000-0000-6A24-************/text
2019-11-13 01:23:05:725 - [HTTP] {}
2019-11-13 01:23:05:725 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:23:05:726 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:23:05:730 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/15000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:23:05:731 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/15000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/15000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:23:06:039 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:06:039 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:23:06:040 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:23:06:040 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:23:06:041 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/15000000-0000-0000-6A24-************/text 200 316 ms - 74
2019-11-13 01:23:06:041 - [HTTP] 
2019-11-13 01:23:06:207 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/16000000-0000-0000-6A24-************/text
2019-11-13 01:23:06:207 - [HTTP] {}
2019-11-13 01:23:06:208 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:23:06:208 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:23:06:211 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/16000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:23:06:212 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/16000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/16000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:23:06:521 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:06:521 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:23:06:521 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:23:06:521 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:23:06:522 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/16000000-0000-0000-6A24-************/text 200 315 ms - 74
2019-11-13 01:23:06:522 - [HTTP] 
2019-11-13 01:23:06:665 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/17000000-0000-0000-6A24-************/text
2019-11-13 01:23:06:665 - [HTTP] {}
2019-11-13 01:23:06:666 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:23:06:666 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:23:06:668 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/17000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:23:06:668 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/17000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/17000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:23:06:978 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:06:978 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:23:06:979 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:23:06:979 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:23:06:980 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/17000000-0000-0000-6A24-************/text 200 315 ms - 74
2019-11-13 01:23:06:980 - [HTTP] 
2019-11-13 01:23:07:132 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0E000000-0000-0000-6A24-************/text
2019-11-13 01:23:07:132 - [HTTP] {}
2019-11-13 01:23:07:132 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:23:07:133 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:23:07:135 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0E000000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:23:07:135 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0E000000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/0E000000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:23:07:476 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:07:476 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:23:07:477 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:23:07:477 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:23:07:478 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/0E000000-0000-0000-6A24-************/text 200 346 ms - 74
2019-11-13 01:23:07:478 - [HTTP] 
2019-11-13 01:23:07:627 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements
2019-11-13 01:23:07:627 - [HTTP] {"using":"accessibility id","value":"firstName"}
2019-11-13 01:23:07:627 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.findElements() with args: ["accessibility id","firstName","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:23:07:628 - [debug] [XCUITest] Executing command 'findElements'
2019-11-13 01:23:07:629 - [debug] [BaseDriver] Valid locator strategies for this request: xpath, id, name, class name, -ios predicate string, -ios class chain, accessibility id
2019-11-13 01:23:07:630 - [debug] [BaseDriver] Waiting up to 0 ms for condition
2019-11-13 01:23:07:631 - [debug] [JSONWP Proxy] Matched '/elements' to command name 'findElements'
2019-11-13 01:23:07:631 - [debug] [JSONWP Proxy] Proxying [POST /elements] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/elements] with body: {"using":"accessibility id","value":"firstName"}
2019-11-13 01:23:07:934 - [debug] [JSONWP Proxy] Got response with status 200: {"value":[{"ELEMENT":"58010000-0000-0000-6A24-************"}],"sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8","status":0}
2019-11-13 01:23:07:935 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.findElements() result: [{"ELEMENT":"58010000-0000-0000-6A24-************"}]
2019-11-13 01:23:07:937 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements 200 310 ms - 124
2019-11-13 01:23:07:937 - [HTTP] 
2019-11-13 01:23:07:937 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:07:938 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:08:085 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/58010000-0000-0000-6A24-************/text
2019-11-13 01:23:08:086 - [HTTP] {}
2019-11-13 01:23:08:086 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:23:08:086 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:23:08:088 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/58010000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:23:08:088 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/58010000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/58010000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:23:08:295 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:08:295 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:23:08:295 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"TestAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:23:08:295 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:23:08:296 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/58010000-0000-0000-6A24-************/text 200 210 ms - 109
2019-11-13 01:23:08:296 - [HTTP] 
2019-11-13 01:23:08:443 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements
2019-11-13 01:23:08:443 - [HTTP] {"using":"accessibility id","value":"firstName"}
2019-11-13 01:23:08:444 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.findElements() with args: ["accessibility id","firstName","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:23:08:444 - [debug] [XCUITest] Executing command 'findElements'
2019-11-13 01:23:08:445 - [debug] [BaseDriver] Valid locator strategies for this request: xpath, id, name, class name, -ios predicate string, -ios class chain, accessibility id
2019-11-13 01:23:08:446 - [debug] [BaseDriver] Waiting up to 0 ms for condition
2019-11-13 01:23:08:447 - [debug] [JSONWP Proxy] Matched '/elements' to command name 'findElements'
2019-11-13 01:23:08:447 - [debug] [JSONWP Proxy] Proxying [POST /elements] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/elements] with body: {"using":"accessibility id","value":"firstName"}
2019-11-13 01:23:08:742 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:08:742 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:23:08:742 - [debug] [JSONWP Proxy] Got response with status 200: {"value":[{"ELEMENT":"58010000-0000-0000-6A24-************"}],"sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8","status":0}
2019-11-13 01:23:08:743 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.findElements() result: [{"ELEMENT":"58010000-0000-0000-6A24-************"}]
2019-11-13 01:23:08:744 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements 200 300 ms - 124
2019-11-13 01:23:08:744 - [HTTP] 
2019-11-13 01:23:08:889 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/touch/click
2019-11-13 01:23:08:889 - [HTTP] {"element":"58010000-0000-0000-6A24-************"}
2019-11-13 01:23:08:890 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.click() with args: ["58010000-0000-0000-6A24-************","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:23:08:891 - [debug] [XCUITest] Executing command 'click'
2019-11-13 01:23:08:896 - [debug] [JSONWP Proxy] Matched '/element/58010000-0000-0000-6A24-************/click' to command name 'click'
2019-11-13 01:23:08:896 - [debug] [JSONWP Proxy] Proxying [POST /element/58010000-0000-0000-6A24-************/click] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/58010000-0000-0000-6A24-************/click] with body: {}
2019-11-13 01:23:09:718 - [debug] [JSONWP Proxy] Got response with status 200: {"status":0,"id":"58010000-0000-0000-6A24-************","value":"","sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8"}
2019-11-13 01:23:09:718 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:09:719 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:23:09:719 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.click() result: ""
2019-11-13 01:23:09:720 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/touch/click 200 831 ms - 74
2019-11-13 01:23:09:720 - [HTTP] 
2019-11-13 01:23:09:866 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements
2019-11-13 01:23:09:866 - [HTTP] {"using":"accessibility id","value":"firstName"}
2019-11-13 01:23:09:867 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.findElements() with args: ["accessibility id","firstName","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:23:09:867 - [debug] [XCUITest] Executing command 'findElements'
2019-11-13 01:23:09:869 - [debug] [BaseDriver] Valid locator strategies for this request: xpath, id, name, class name, -ios predicate string, -ios class chain, accessibility id
2019-11-13 01:23:09:869 - [debug] [BaseDriver] Waiting up to 0 ms for condition
2019-11-13 01:23:09:870 - [debug] [JSONWP Proxy] Matched '/elements' to command name 'findElements'
2019-11-13 01:23:09:870 - [debug] [JSONWP Proxy] Proxying [POST /elements] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/elements] with body: {"using":"accessibility id","value":"firstName"}
2019-11-13 01:23:10:162 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:10:162 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:23:10:162 - [debug] [JSONWP Proxy] Got response with status 200: {"value":[{"ELEMENT":"58010000-0000-0000-6A24-************"}],"sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8","status":0}
2019-11-13 01:23:10:163 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.findElements() result: [{"ELEMENT":"58010000-0000-0000-6A24-************"}]
2019-11-13 01:23:10:164 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements 200 297 ms - 124
2019-11-13 01:23:10:164 - [HTTP] 
2019-11-13 01:23:10:313 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/58010000-0000-0000-6A24-************/clear
2019-11-13 01:23:10:313 - [HTTP] {}
2019-11-13 01:23:10:313 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.clear() with args: ["58010000-0000-0000-6A24-************","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:23:10:314 - [debug] [XCUITest] Executing command 'clear'
2019-11-13 01:23:10:316 - [debug] [JSONWP Proxy] Matched '/element/58010000-0000-0000-6A24-************/clear' to command name 'clear'
2019-11-13 01:23:10:317 - [debug] [JSONWP Proxy] Proxying [POST /element/58010000-0000-0000-6A24-************/clear] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/58010000-0000-0000-6A24-************/clear] with no body
2019-11-13 01:23:12:853 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:12:853 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:23:12:854 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"status\" : 0,\n  \"id\" : \"58010000-0000-0000-6A24-************\",\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\"\n}"
2019-11-13 01:23:12:855 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.clear() result: null
2019-11-13 01:23:12:856 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/58010000-0000-0000-6A24-************/clear 200 2543 ms - 76
2019-11-13 01:23:12:856 - [HTTP] 
2019-11-13 01:23:13:019 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/58010000-0000-0000-6A24-************/value
2019-11-13 01:23:13:020 - [HTTP] {"text":"TestAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA","value":["T","e","s","t","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A"]}
2019-11-13 01:23:13:020 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.setValue() with args: [["T","e","s","t","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A"],"58010000-0000-0000-6A24-************","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:23:13:021 - [debug] [XCUITest] Executing command 'setValue'
2019-11-13 01:23:13:023 - [debug] [JSONWP Proxy] Matched '/element/58010000-0000-0000-6A24-************/value' to command name 'setValue'
2019-11-13 01:23:13:023 - [debug] [JSONWP Proxy] Proxying [POST /element/58010000-0000-0000-6A24-************/value] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/58010000-0000-0000-6A24-************/value] with body: {"value":["T","e","s","t","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A"]}
2019-11-13 01:23:14:446 - [debug] [JSONWP Proxy] Got response with status 200: {"status":0,"id":"58010000-0000-0000-6A24-************","value":"","sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8"}
2019-11-13 01:23:14:447 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:14:447 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:23:14:448 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.setValue() result: null
2019-11-13 01:23:14:448 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/58010000-0000-0000-6A24-************/value 200 1429 ms - 76
2019-11-13 01:23:14:449 - [HTTP] 
2019-11-13 01:23:14:601 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements
2019-11-13 01:23:14:602 - [HTTP] {"using":"accessibility id","value":"lastName"}
2019-11-13 01:23:14:602 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.findElements() with args: ["accessibility id","lastName","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:23:14:603 - [debug] [XCUITest] Executing command 'findElements'
2019-11-13 01:23:14:605 - [debug] [BaseDriver] Valid locator strategies for this request: xpath, id, name, class name, -ios predicate string, -ios class chain, accessibility id
2019-11-13 01:23:14:605 - [debug] [BaseDriver] Waiting up to 0 ms for condition
2019-11-13 01:23:14:606 - [debug] [JSONWP Proxy] Matched '/elements' to command name 'findElements'
2019-11-13 01:23:14:606 - [debug] [JSONWP Proxy] Proxying [POST /elements] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/elements] with body: {"using":"accessibility id","value":"lastName"}
2019-11-13 01:23:14:955 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:14:955 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:23:14:956 - [debug] [JSONWP Proxy] Got response with status 200: {"value":[{"ELEMENT":"59010000-0000-0000-6A24-************"}],"sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8","status":0}
2019-11-13 01:23:14:956 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.findElements() result: [{"ELEMENT":"59010000-0000-0000-6A24-************"}]
2019-11-13 01:23:14:957 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements 200 356 ms - 124
2019-11-13 01:23:14:957 - [HTTP] 
2019-11-13 01:23:15:107 - [HTTP] --> GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/59010000-0000-0000-6A24-************/text
2019-11-13 01:23:15:107 - [HTTP] {}
2019-11-13 01:23:15:108 - [MJSONWP (7e9c172b)] Driver proxy active, passing request on via HTTP proxy
2019-11-13 01:23:15:108 - [debug] [XCUITest] Executing command 'proxyReqRes'
2019-11-13 01:23:15:110 - [debug] [JSONWP Proxy] Matched '/wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/59010000-0000-0000-6A24-************/text' to command name 'getText'
2019-11-13 01:23:15:111 - [debug] [JSONWP Proxy] Proxying [GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/59010000-0000-0000-6A24-************/text] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/59010000-0000-0000-6A24-************/text] with body: {}
2019-11-13 01:23:15:291 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"TestAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:23:15:291 - [JSONWP Proxy] Replacing sessionId A9142A75-B4E7-4D12-AB3A-94BE472FFCC8 with 7e9c172b-a548-420e-953b-2b1608c86a6b
2019-11-13 01:23:15:292 - [HTTP] <-- GET /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/59010000-0000-0000-6A24-************/text 200 184 ms - 109
2019-11-13 01:23:15:292 - [HTTP] 
2019-11-13 01:23:15:292 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:15:292 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:23:15:444 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements
2019-11-13 01:23:15:444 - [HTTP] {"using":"accessibility id","value":"lastName"}
2019-11-13 01:23:15:444 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.findElements() with args: ["accessibility id","lastName","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:23:15:445 - [debug] [XCUITest] Executing command 'findElements'
2019-11-13 01:23:15:446 - [debug] [BaseDriver] Valid locator strategies for this request: xpath, id, name, class name, -ios predicate string, -ios class chain, accessibility id
2019-11-13 01:23:15:446 - [debug] [BaseDriver] Waiting up to 0 ms for condition
2019-11-13 01:23:15:447 - [debug] [JSONWP Proxy] Matched '/elements' to command name 'findElements'
2019-11-13 01:23:15:448 - [debug] [JSONWP Proxy] Proxying [POST /elements] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/elements] with body: {"using":"accessibility id","value":"lastName"}
2019-11-13 01:23:15:758 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:15:758 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:23:15:758 - [debug] [JSONWP Proxy] Got response with status 200: {"value":[{"ELEMENT":"59010000-0000-0000-6A24-************"}],"sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8","status":0}
2019-11-13 01:23:15:759 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.findElements() result: [{"ELEMENT":"59010000-0000-0000-6A24-************"}]
2019-11-13 01:23:15:760 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements 200 316 ms - 124
2019-11-13 01:23:15:760 - [HTTP] 
2019-11-13 01:23:15:979 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/touch/click
2019-11-13 01:23:15:980 - [HTTP] {"element":"59010000-0000-0000-6A24-************"}
2019-11-13 01:23:15:980 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.click() with args: ["59010000-0000-0000-6A24-************","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:23:15:980 - [debug] [XCUITest] Executing command 'click'
2019-11-13 01:23:15:984 - [debug] [JSONWP Proxy] Matched '/element/59010000-0000-0000-6A24-************/click' to command name 'click'
2019-11-13 01:23:15:984 - [debug] [JSONWP Proxy] Proxying [POST /element/59010000-0000-0000-6A24-************/click] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/59010000-0000-0000-6A24-************/click] with body: {}
2019-11-13 01:23:16:879 - [debug] [JSONWP Proxy] Got response with status 200: {"status":0,"id":"59010000-0000-0000-6A24-************","value":"","sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8"}
2019-11-13 01:23:16:879 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:16:879 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:23:16:880 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.click() result: ""
2019-11-13 01:23:16:880 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/touch/click 200 901 ms - 74
2019-11-13 01:23:16:880 - [HTTP] 
2019-11-13 01:23:17:053 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements
2019-11-13 01:23:17:053 - [HTTP] {"using":"accessibility id","value":"lastName"}
2019-11-13 01:23:17:054 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.findElements() with args: ["accessibility id","lastName","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:23:17:054 - [debug] [XCUITest] Executing command 'findElements'
2019-11-13 01:23:17:055 - [debug] [BaseDriver] Valid locator strategies for this request: xpath, id, name, class name, -ios predicate string, -ios class chain, accessibility id
2019-11-13 01:23:17:056 - [debug] [BaseDriver] Waiting up to 0 ms for condition
2019-11-13 01:23:17:057 - [debug] [JSONWP Proxy] Matched '/elements' to command name 'findElements'
2019-11-13 01:23:17:057 - [debug] [JSONWP Proxy] Proxying [POST /elements] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/elements] with body: {"using":"accessibility id","value":"lastName"}
2019-11-13 01:23:17:419 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:17:419 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:23:17:420 - [debug] [JSONWP Proxy] Got response with status 200: {"value":[{"ELEMENT":"59010000-0000-0000-6A24-************"}],"sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8","status":0}
2019-11-13 01:23:17:421 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.findElements() result: [{"ELEMENT":"59010000-0000-0000-6A24-************"}]
2019-11-13 01:23:17:422 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements 200 368 ms - 124
2019-11-13 01:23:17:422 - [HTTP] 
2019-11-13 01:23:17:571 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/59010000-0000-0000-6A24-************/clear
2019-11-13 01:23:17:571 - [HTTP] {}
2019-11-13 01:23:17:571 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.clear() with args: ["59010000-0000-0000-6A24-************","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:23:17:572 - [debug] [XCUITest] Executing command 'clear'
2019-11-13 01:23:17:574 - [debug] [JSONWP Proxy] Matched '/element/59010000-0000-0000-6A24-************/clear' to command name 'clear'
2019-11-13 01:23:17:574 - [debug] [JSONWP Proxy] Proxying [POST /element/59010000-0000-0000-6A24-************/clear] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/59010000-0000-0000-6A24-************/clear] with no body
2019-11-13 01:23:19:985 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"status\" : 0,\n  \"id\" : \"59010000-0000-0000-6A24-************\",\n  \"value\" : \"\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\"\n}"
2019-11-13 01:23:19:985 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:19:985 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:23:19:986 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.clear() result: null
2019-11-13 01:23:19:986 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/59010000-0000-0000-6A24-************/clear 200 2415 ms - 76
2019-11-13 01:23:19:987 - [HTTP] 
2019-11-13 01:23:20:144 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/59010000-0000-0000-6A24-************/value
2019-11-13 01:23:20:144 - [HTTP] {"text":"TestAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA","value":["T","e","s","t","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A"]}
2019-11-13 01:23:20:144 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.setValue() with args: [["T","e","s","t","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A"],"59010000-0000-0000-6A24-************","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:23:20:144 - [debug] [XCUITest] Executing command 'setValue'
2019-11-13 01:23:20:147 - [debug] [JSONWP Proxy] Matched '/element/59010000-0000-0000-6A24-************/value' to command name 'setValue'
2019-11-13 01:23:20:147 - [debug] [JSONWP Proxy] Proxying [POST /element/59010000-0000-0000-6A24-************/value] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/59010000-0000-0000-6A24-************/value] with body: {"value":["T","e","s","t","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A","A"]}
2019-11-13 01:23:21:593 - [debug] [JSONWP Proxy] Got response with status 200: {"status":0,"id":"59010000-0000-0000-6A24-************","value":"","sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8"}
2019-11-13 01:23:21:594 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:23:21:594 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:23:21:594 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.setValue() result: null
2019-11-13 01:23:21:595 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/element/59010000-0000-0000-6A24-************/value 200 1451 ms - 76
2019-11-13 01:23:21:595 - [HTTP] 
2019-11-13 01:23:21:744 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/appium/device/hide_keyboard
2019-11-13 01:23:21:744 - [HTTP] {"strategy":"tapOutside"}
2019-11-13 01:23:21:745 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.hideKeyboard() with args: ["tapOutside",null,null,null,"7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:23:21:745 - [debug] [XCUITest] Executing command 'hideKeyboard'
2019-11-13 01:23:21:757 - [debug] [JSONWP Proxy] Proxying [POST /wda/keyboard/dismiss] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/wda/keyboard/dismiss] with no body
2019-11-13 01:24:41:518 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : \"Error Domain=com.facebook.WebDriverAgent Code=1 \\\"The keyboard on iPhone cannot be dismissed because of a known XCTest issue. Try to dismiss it in the way supported by your application under test.\\\" UserInfo={NSLocalizedDescription=The keyboard on iPhone cannot be dismissed because of a known XCTest issue. Try to dismiss it in the way supported by your application under test.}\",\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 13\n}"
2019-11-13 01:24:41:519 - [debug] [MJSONWP] Matched JSONWP error code 13 to UnknownError
2019-11-13 01:24:41:519 - [debug] [XCUITest] Cannot dismiss the keyboard using the native call. Trying to apply a workaround...
2019-11-13 01:24:41:519 - [debug] [BaseDriver] Waiting up to 0 ms for condition
2019-11-13 01:24:41:520 - [debug] [JSONWP Proxy] Matched '/element' to command name 'findElement'
2019-11-13 01:24:41:521 - [debug] [JSONWP Proxy] Proxying [POST /element] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element] with body: {"using":"class name","value":"XCUIElementTypeKeyboard"}
2019-11-13 01:24:41:521 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:24:41:522 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:24:41:823 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:24:41:824 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:24:41:824 - [debug] [JSONWP Proxy] Got response with status 200: {"value":{"ELEMENT":"57000000-0000-0000-6A24-************"},"sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8","status":0}
2019-11-13 01:24:41:825 - [debug] [XCUITest] Finding keyboard and clicking final button to close
2019-11-13 01:24:41:827 - [debug] [JSONWP Proxy] Matched '/element/57000000-0000-0000-6A24-************/attribute/visible' to command name 'getAttribute'
2019-11-13 01:24:41:827 - [debug] [JSONWP Proxy] Proxying [GET /element/57000000-0000-0000-6A24-************/attribute/visible] to [GET http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/57000000-0000-0000-6A24-************/attribute/visible] with no body
2019-11-13 01:24:42:013 - [debug] [JSONWP Proxy] Got response with status 200: "{\n  \"value\" : true,\n  \"sessionId\" : \"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8\",\n  \"status\" : 0\n}"
2019-11-13 01:24:42:013 - [debug] [BaseDriver] Waiting up to 0 ms for condition
2019-11-13 01:24:42:014 - [debug] [JSONWP Proxy] Matched '/element/57000000-0000-0000-6A24-************/elements' to command name 'findElementsFromElement'
2019-11-13 01:24:42:014 - [debug] [JSONWP Proxy] Proxying [POST /element/57000000-0000-0000-6A24-************/elements] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/57000000-0000-0000-6A24-************/elements] with body: {"using":"class name","value":"XCUIElementTypeButton"}
2019-11-13 01:24:42:015 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:24:42:015 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:24:42:448 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:24:42:448 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:24:42:449 - [debug] [JSONWP Proxy] Got response with status 200: {"value":[{"ELEMENT":"17020000-0000-0000-6A24-************"},{"ELEMENT":"22020000-0000-0000-6A24-************"}],"sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8","status":0}
2019-11-13 01:24:42:450 - [debug] [JSONWP Proxy] Matched '/element/22020000-0000-0000-6A24-************/click' to command name 'click'
2019-11-13 01:24:42:450 - [debug] [JSONWP Proxy] Proxying [POST /element/22020000-0000-0000-6A24-************/click] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/22020000-0000-0000-6A24-************/click] with body: {}
2019-11-13 01:24:42:455 - [debug] [iProxy] to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:455 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:455 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:455 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:455 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:455 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:455 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:456 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:456 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:456 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:456 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:456 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:456 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:456 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:456 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:457 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:457 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:458 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:459 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:461 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:462 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:462 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:462 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:462 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:463 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:463 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:463 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:463 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:463 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:463 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:463 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:463 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:464 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:464 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:464 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:464 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:464 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:464 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:464 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:464 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:464 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:465 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:465 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:465 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:465 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:465 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:465 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:465 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:465 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:465 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:466 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:466 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:466 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:466 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:466 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:466 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:466 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:466 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:466 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:467 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:467 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:467 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:467 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:467 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:467 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:467 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:467 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:467 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:469 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:469 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:469 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:469 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:469 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:469 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:469 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:470 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:470 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:470 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:471 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:471 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:471 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:472 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:472 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:472 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:472 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:472 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:472 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:472 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:472 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:473 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:473 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:473 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:473 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:473 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:473 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:473 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:473 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:474 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:474 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:474 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:474 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:474 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:474 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:474 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:474 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:474 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:475 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:475 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:475 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:475 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:475 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:475 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:475 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:475 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:475 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:475 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:476 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:476 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:476 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:476 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:476 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:476 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:476 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:476 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:477 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:477 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:477 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:477 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:477 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:477 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:478 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:478 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:478 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:478 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:478 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:478 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:479 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:479 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:479 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:479 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:479 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:479 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:480 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:480 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:480 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:480 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:480 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:481 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:482 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:482 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:482 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:482 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:482 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:483 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:483 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:483 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:483 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:483 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:483 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:484 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:484 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:484 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:485 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:485 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:485 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:485 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:485 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:485 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:486 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:486 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:486 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:486 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:486 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:486 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:487 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:487 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:487 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:487 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:487 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:488 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:488 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:488 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:488 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:488 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:488 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:488 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:492 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:492 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:492 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:492 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:492 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:492 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:492 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:493 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:493 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:493 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:493 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:493 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:493 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:493 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:493 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:493 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:494 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:494 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:494 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:494 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:494 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:494 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:494 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:494 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:495 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:495 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:495 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:495 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:495 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:495 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:495 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A50003A), port 8100
2019-11-13 01:24:42:495 - [debug] [iProxy] run_ctos_loop: fd = 4
2019-11-13 01:24:42:495 - [debug] [iProxy] run_stoc_loop: fd = 4
2019-11-13 01:24:42:496 - [debug] [iProxy] accepted connection, fd = 4
2019-11-13 01:24:42:496 - [debug] [iProxy] waiting for connection
2019-11-13 01:24:42:496 - [debug] [iProxy] Number of available devices == 7
2019-11-13 01:24:42:496 - [debug] [iProxy] Requesting connecion to device handle == 318 (serial: 00008020-0019312C1A
2019-11-13 01:24:43:298 - [debug] [JSONWP Proxy] Got response with status 200: {"status":0,"id":"22020000-0000-0000-6A24-************","value":"","sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8"}
2019-11-13 01:24:43:299 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:24:43:299 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:24:43:300 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.hideKeyboard() result: null
2019-11-13 01:24:43:300 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/appium/device/hide_keyboard 200 81554 ms - 76
2019-11-13 01:24:43:301 - [HTTP] 
2019-11-13 01:24:43:446 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements
2019-11-13 01:24:43:446 - [HTTP] {"using":"accessibility id","value":"updateMetadata"}
2019-11-13 01:24:43:447 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.findElements() with args: ["accessibility id","updateMetadata","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:24:43:447 - [debug] [XCUITest] Executing command 'findElements'
2019-11-13 01:24:43:450 - [debug] [BaseDriver] Valid locator strategies for this request: xpath, id, name, class name, -ios predicate string, -ios class chain, accessibility id
2019-11-13 01:24:43:451 - [debug] [BaseDriver] Waiting up to 0 ms for condition
2019-11-13 01:24:43:455 - [debug] [JSONWP Proxy] Matched '/elements' to command name 'findElements'
2019-11-13 01:24:43:455 - [debug] [JSONWP Proxy] Proxying [POST /elements] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/elements] with body: {"using":"accessibility id","value":"updateMetadata"}
2019-11-13 01:24:43:815 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:24:43:816 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:24:43:816 - [debug] [JSONWP Proxy] Got response with status 200: {"value":[{"ELEMENT":"39010000-0000-0000-6A24-************"}],"sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8","status":0}
2019-11-13 01:24:43:817 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.findElements() result: [{"ELEMENT":"39010000-0000-0000-6A24-************"}]
2019-11-13 01:24:43:818 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements 200 371 ms - 124
2019-11-13 01:24:43:818 - [HTTP] 
2019-11-13 01:24:43:962 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/touch/click
2019-11-13 01:24:43:962 - [HTTP] {"element":"39010000-0000-0000-6A24-************"}
2019-11-13 01:24:43:962 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.click() with args: ["39010000-0000-0000-6A24-************","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:24:43:962 - [debug] [XCUITest] Executing command 'click'
2019-11-13 01:24:43:965 - [debug] [JSONWP Proxy] Matched '/element/39010000-0000-0000-6A24-************/click' to command name 'click'
2019-11-13 01:24:43:965 - [debug] [JSONWP Proxy] Proxying [POST /element/39010000-0000-0000-6A24-************/click] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/element/39010000-0000-0000-6A24-************/click] with body: {}
2019-11-13 01:24:44:724 - [debug] [JSONWP Proxy] Got response with status 200: {"status":0,"id":"39010000-0000-0000-6A24-************","value":"","sessionId":"A9142A75-B4E7-4D12-AB3A-94BE472FFCC8"}
2019-11-13 01:24:44:724 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:24:44:725 - [debug] [iProxy] recv failed: Operation not permitted
2019-11-13 01:24:44:725 - [debug] [MJSONWP (7e9c172b)] Responding to client with driver.click() result: ""
2019-11-13 01:24:44:726 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/touch/click 200 764 ms - 74
2019-11-13 01:24:44:726 - [HTTP] 
2019-11-13 01:24:44:881 - [HTTP] --> POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements
2019-11-13 01:24:44:882 - [HTTP] {"using":"xpath","value":"//*"}
2019-11-13 01:24:44:882 - [debug] [MJSONWP (7e9c172b)] Calling AppiumDriver.findElements() with args: ["xpath","//*","7e9c172b-a548-420e-953b-2b1608c86a6b"]
2019-11-13 01:24:44:882 - [debug] [XCUITest] Executing command 'findElements'
2019-11-13 01:24:44:884 - [debug] [BaseDriver] Valid locator strategies for this request: xpath, id, name, class name, -ios predicate string, -ios class chain, accessibility id
2019-11-13 01:24:44:884 - [debug] [BaseDriver] Waiting up to 0 ms for condition
2019-11-13 01:24:44:887 - [debug] [JSONWP Proxy] Matched '/elements' to command name 'findElements'
2019-11-13 01:24:44:888 - [debug] [JSONWP Proxy] Proxying [POST /elements] to [POST http://localhost:8406/session/A9142A75-B4E7-4D12-AB3A-94BE472FFCC8/elements] with body: {"using":"Xpath","value":"//*"}
2019-11-13 01:28:54:872 - [HTTP] <-- POST /wd/hub/session/7e9c172b-a548-420e-953b-2b1608c86a6b/elements - - ms - -
2019-11-13 01:28:54:873 - [HTTP] 
2019-11-13 01:28:58:429 - [debug] [XCUITest] Connection to WDA timed out
2019-11-13 01:29:01:586 - [debug] [XCUITest] Connection to WDA timed out
