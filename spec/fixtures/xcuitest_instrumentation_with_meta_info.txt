Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -xctestrun /tmp/00008110-00021CE021F1801E_xctestrun.xml -destination id=00008110-00021CE021F1801E test-without-building -derivedDataPath /tmp/00008110-00021CE021F1801E_xcuitest_derived_data_11 "-only-testing:SkrillPayments-UITests/MoneyTransferCalculatorTests/testTransferLimits" -resultBundlePath /tmp/00008110-00021CE021F1801E_xcresult_bundle/SkrillPayments-UITests/MoneyTransferCalculatorTests/testTransferLimits_1685670727 -resultBundleVersion 3

User defaults from command line:
    IDEBuildOperationResultBundlePath = /tmp/00008110-00021CE021F1801E_xcresult_bundle/SkrillPayments-UITests/MoneyTransferCalculatorTests/testTransferLimits_1685670727
    IDEDerivedDataPathOverride = /tmp/00008110-00021CE021F1801E_xcuitest_derived_data_11
    IDEPackageSupportUseBuiltinSCM = YES
    IDERequestedResultBundleFormatVersion = 3
    XCTHTestRunSpecificationPath = /tmp/00008110-00021CE021F1801E_xctestrun.xml

Writing result bundle at path:
	/tmp/00008110-00021CE021F1801E_xcresult_bundle/SkrillPayments-UITests/MoneyTransferCalculatorTests/testTransferLimits_1685670727

2023-06-02 01:52:10.137 SkrillPayments-UITests-Runner[37425:2434549] Running tests...
Test Suite 'Selected tests' started at 2023-06-02 01:52:10.218
Test Suite 'SkrillPayments-UITests.xctest' started at 2023-06-02 01:52:10.218
Test Suite 'MoneyTransferCalculatorTests' started at 2023-06-02 01:52:10.218
Test Case '-[SkrillPayments_UITests.MoneyTransferCalculatorTests testTransferLimits]' started.
    t =     0.00s Start Test at 2023-06-02 01:52:10.219
    t =     0.04s Set Up
    t =     0.04s     Open com.skrill.ios.SkrillPaymentsLocalhost
    t =     0.08s         Launch com.skrill.ios.SkrillPaymentsLocalhost
    t =     0.41s             Setting up automation session
    t =     0.43s             Wait for com.skrill.ios.SkrillPaymentsLocalhost to idle
    t =     1.79s     Terminate com.skrill.ios.SkrillPaymentsLocalhost:37428
    t =     2.97s     Open com.skrill.ios.SkrillPaymentsLocalhost
    t =     3.04s         Launch com.skrill.ios.SkrillPaymentsLocalhost
    t =    15.41s     Synthesize event
    t =    15.56s     Wait for com.skrill.ios.SkrillPaymentsLocalhost to idle
    t =    15.65s Waiting 10.0s for "LoginAndRegistration.RepeatPinTitleLabel" NavigationBar to exist
2023-06-02 01:52:47.568 SkrillPayments-UITests-Runner[37449:2435502] Running tests...
2023-06-02 01:52:48.478 xcodebuild[67343:519278223] [MT] IDETestOperationsObserverDebug: 39.382 elapsed -- Testing started completed.
2023-06-02 01:52:48.478 xcodebuild[67343:519278223] [MT] IDETestOperationsObserverDebug: 0.000 sec, +0.000 sec -- start
2023-06-02 01:52:48.478 xcodebuild[67343:519278223] [MT] IDETestOperationsObserverDebug: 39.382 sec, +39.382 sec -- end

Test session results, code coverage, and logs:
	/tmp/00008110-00021CE021F1801E_xcresult_bundle/SkrillPayments-UITests/MoneyTransferCalculatorTests/testTransferLimits_1685670727

Testing failed:
	SkrillPayments-UITests:
		Run test suite MoneyTransferCalculatorTests encountered an error (Early unexpected exit, operation never finished bootstrapping - no restart will be attempted. (Underlying Error: The test runner exited with code 74 before finishing running tests. If you believe this error represents a bug, please attach the result bundle at /tmp/00008110-00021CE021F1801E_xcresult))

Failing tests:
	SkrillPayments-UITests:
		MoneyTransferCalculatorTests.testTransferLimits()

** TEST EXECUTE FAILED **

Testing started
