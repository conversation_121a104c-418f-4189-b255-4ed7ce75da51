Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -xctestrun /tmp/********-00144C4022BB802E_xctestrun.xml -destination id=********-00144C4022BB802E test-without-building -derivedDataPath /tmp/********-00144C4022BB802E_xcuitest_derived_data_38 "-only-testing:digitalwalletUITests/MyAccountUITests/testMyAccountDeletionScreen"

User defaults from command line:
    IDEDerivedDataPathOverride = /tmp/********-00144C4022BB802E_xcuitest_derived_data_38
    IDETestRunOnlyIdentifiers = (
    "digitalwalletUITests/MyAccountUITests/testMyAccountDeletionScreen"
)
    IDETestRunSpecificationPath = /tmp/********-00144C4022BB802E_xctestrun.xml

2022-12-16 00:02:10.948 xcodebuild[77417:*********]  IDETestOperationsObserverDebug: Writing diagnostic log for test session to:
/tmp/********-00144C4022BB802E_xcuitest_derived_data_38/Logs/Test/Test-Transient Testing-2022.12.16_00-02-10-+0000.xcresult/Staging/1_Test/Diagnostics/digitalwalletUITests-13F824BF-2487-4A42-9536-DAC484B2E700/digitalwalletUITests-********-1A37-444C-BDDA-572F057E9B31/Session-digitalwalletUITests-2022-12-16_000210-Aj2bnu.log
2022-12-16 00:02:10.948 xcodebuild[77417:*********] [MT] IDETestOperationsObserverDebug: (E250A91E-2DF7-4D54-9992-78B36F985550) Beginning test session digitalwalletUITests-E250A91E-2DF7-4D54-9992-78B36F985550 at 2022-12-16 00:02:10.948 with Xcode 12A7403 on target 📱<DVTiOSDevice (0x7face6d176a0), iPhone, iPhone, 14.0.1 (18A393), ********-00144C4022BB802E> {
		deviceSerialNumber:         FCHD52N4N70R
		identifier:                 ********-00144C4022BB802E
		deviceClass:                iPhone
		deviceName:                 iPhone
		deviceIdentifier:           ********-00144C4022BB802E
		productVersion:             14.0.1
		buildVersion:               18A393
		deviceSoftwareVersion:      14.0.1 (18A393)
		deviceArchitecture:         arm64e
		deviceTotalCapacity:        56234266624
		deviceAvailableCapacity:    28352737280
		deviceIsTransient:          NO
		ignored:                    NO
		deviceIsBusy:               NO
		deviceIsPaired:             YES
		deviceIsActivated:          YES
		deviceActivationState:      Activated
		isPasscodeLocked:           NO
		deviceType:                 <DVTDeviceType:0x7face63b61b0 Xcode.DeviceType.iPhone>
		supportedDeviceFamilies:    (
    1
)
		applications:              (null)
		provisioningProfiles:      (null)
		hasInternalSupport:        NO
		hasWritableSystem:         NO
		isSupportedOS:             YES
		bootArgs:                  (null)
		nextBootArgs:              (null)
		connected:                 YES
		isWirelessEnabled:         NO
		connectionType:            direct
		hostname:                  (null)
		bonjourServiceName:        d0:3f:aa:b6:a4:3b@fe80::d23f:aaff:feb6:a43b._apple-mobdev2._tcp.local.
		activeProxiedDevice:       (null)
		} (14.0.1 (18A393))
2022-12-16 00:02:11.071 xcodebuild[77417:*********] [MT] IDETestOperationsObserverDebug: (E250A91E-2DF7-4D54-9992-78B36F985550) Finished requesting crash reports. Continuing with testing.
2022-12-16 00:02:11.832 digitalwalletUITests-Runner[14184:1732328] Running tests...
Test Suite 'Selected tests' started at 2022-12-16 00:02:11.863
Test Suite 'digitalwalletUITests.xctest' started at 2022-12-16 00:02:11.863
Test Suite 'MyAccountUITests' started at 2022-12-16 00:02:11.863
Test Case '-[digitalwalletUITests.MyAccountUITests testMyAccountDeletionScreen]' started.
    t =     0.00s Start Test at 2022-12-16 00:02:11.864
    t =     0.02s Set Up
    t =     0.03s Open au.gov.vic.service.digitalwallet.citizen.local
    t =     0.05s     Launch au.gov.vic.service.digitalwallet.citizen.local
    t =     0.05s         Terminate au.gov.vic.service.digitalwallet.citizen.local:14182
    t =     1.20s         Wait for accessibility to load
    t =     1.20s         Setting up automation session
    t =     1.30s         Wait for au.gov.vic.service.digitalwallet.citizen.local to idle
    t =     2.37s Waiting 3.0s for TabBar to exist
    t =     3.38s     Checking `Expect predicate `exists == 1` for object TabBar`
    t =     3.38s         Checking existence of `TabBar`
    t =     3.42s Tap Button
    t =     3.42s     Wait for au.gov.vic.service.digitalwallet.citizen.local to idle
    t =     3.47s     Find the Button
    t =     4.55s         Find the Button (retry 1)
    t =     5.61s         Find the Button (retry 2)
    t =     5.67s Requesting snapshot of accessibility hierarchy for app with pid 14187
/Users/<USER>/actions-runner/_work/digitalwallet-ios/digitalwallet-ios/digitalwalletUITests/Helpers/XCUIApplication+UITest.swift:76: error: -[digitalwalletUITests.MyAccountUITests testMyAccountDeletionScreen] : Failed to Error Domain=com.apple.dt.xctest.ui-testing.error Code=10008 "No matches found for Element at index 3 from input {(
    Button, identifier: 'servicesTab', Selected,
    Button, identifier: 'walletTab',
    Button, identifier: 'accountTab'
)}" UserInfo={issues=NSMapTable {
}
, NSLocalizedDescription=No matches found for Element at index 3 from input {(
    Button, identifier: 'servicesTab', Selected,
    Button, identifier: 'walletTab',
    Button, identifier: 'accountTab'
)}}: No matches found for Element at index 3 from input {(
    Button, identifier: 'servicesTab', Selected,
    Button, identifier: 'walletTab',
    Button, identifier: 'accountTab'
)}
    t =     5.83s Tear Down
2022-12-16 00:02:17.652 xcodebuild[77417:*********] *** Assertion failure in -[XCTTestIdentifier initWithBundleName:className:methodName:], XCTTestIdentifier.m:18
Test Case '-[digitalwalletUITests.MyAccountUITests testMyAccountDeletionScreen]' failed (6.037 seconds).
Test Suite 'MyAccountUITests' failed at 2022-12-16 00:02:17.902.
	 Executed 1 test, with 1 failure (0 unexpected) in 6.037 (6.039) seconds
Test Suite 'digitalwalletUITests.xctest' failed at 2022-12-16 00:02:17.904.
	 Executed 1 test, with 1 failure (0 unexpected) in 6.037 (6.041) seconds
Test Suite 'Selected tests' failed at 2022-12-16 00:02:17.907.
	 Executed 1 test, with 1 failure (0 unexpected) in 6.037 (6.044) seconds
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
2022-12-16 00:02:17.876 xcodebuild[77417:*********] [MT] IDETestOperationsObserverDebug: 6.932 elapsed -- Testing started completed.
2022-12-16 00:02:17.876 xcodebuild[77417:*********] [MT] IDETestOperationsObserverDebug: 0.000 sec, +0.000 sec -- start
2022-12-16 00:02:17.876 xcodebuild[77417:*********] [MT] IDETestOperationsObserverDebug: 6.932 sec, +6.932 sec -- end

Test session results, code coverage, and logs:
	/tmp/********-00144C4022BB802E_xcuitest_derived_data_38/Logs/Test/Test-Transient Testing-2022.12.16_00-02-10-+0000.xcresult

** TEST EXECUTE FAILED **

Testing started
