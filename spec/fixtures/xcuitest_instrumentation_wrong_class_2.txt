User defaults from command line:
    IDEDerivedDataPathOverride = /tmp/2f1fd1aa6ec7cbaa1c6d38dd16c83492415dd4d8_xcuitest_derived_data_1
    IDETestRunOnlyIdentifiers = (
    "AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest/testSSLCertWrongName"
)
    IDETestRunSpecificationPath = /tmp/2f1fd1aa6ec7cbaa1c6d38dd16c83492415dd4d8_xctestrun.xml

2020-11-19 14:04:41.317 xcodebuild[93236:3386637]  IDETestOperationsObserverDebug: Writing diagnostic log for test session to:
/tmp/2f1fd1aa6ec7cbaa1c6d38dd16c83492415dd4d8_xcuitest_derived_data_1/Logs/Test/Test-Transient Testing-2020.11.19_14-04-41-+0000.xcresult/1_Test/Diagnostics/AWSDeviceFarmiOSReferenceAppUITestsSwift-D7F192DB-588A-4DA4-8B6D-95E4868B565D/AWSDeviceFarmiOSReferenceAppUITestsSwift-7E73E5A5-E5BA-486C-915E-D748E6EC8B68/Session-AWSDeviceFarmiOSReferenceAppUITestsSwift-2020-11-19_140441-IHWbKW.log
2020-11-19 14:04:41.321 xcodebuild[93236:3385905] [MT] IDETestOperationsObserverDebug: (48D33B53-BF51-4015-A215-97752C5F20D2) Beginning test session AWSDeviceFarmiOSReferenceAppUITestsSwift-48D33B53-BF51-4015-A215-97752C5F20D2 at 2020-11-19 14:04:41.319 with Xcode 10B61 on target 📱<DVTiOSDevice (0x7fb5848d0330), Steve Kearns’s iPhone, iPhone, 13.2 (17B84), 2f1fd1aa6ec7cbaa1c6d38dd16c83492415dd4d8> {
		deviceSerialNumber:         F4HY3H5MJC67
		identifier:                 2f1fd1aa6ec7cbaa1c6d38dd16c83492415dd4d8
		deviceClass:                iPhone
		deviceName:                 Steve Kearns’s iPhone
		deviceIdentifier:           2f1fd1aa6ec7cbaa1c6d38dd16c83492415dd4d8
		productVersion:             13.2
		buildVersion:               17B84
		deviceSoftwareVersion:      13.2 (17B84)
		deviceArchitecture:         arm64
		deviceTotalCapacity:        58871918592
		deviceAvailableCapacity:    54240329728
		deviceIsTransient:          NO
		ignored:                    NO
		deviceIsBusy:               NO
		deviceIsPaired:             YES
		deviceIsActivated:          YES
		deviceActivationState:      Activated
		isPasscodeLocked:           NO
		deviceType:                 <DVTDeviceType:0x7fb58474ea60 Xcode.DeviceType.iPhone>
		supportedDeviceFamilies:    (
    1
)
		applications:              (null)
		provisioningProfiles:      (null)
		hasInternalSupport:        NO
		isSupportedOS:             YES
		developerDiskMountError:   (null)
(null)
	bootArgs:                  (null)
		connected:                 yes
		isWirelessEnabled:         no
		connectionType:            direct
		hostname:                  (null)
		bonjourServiceName:        c0:e8:62:2c:7e:55@fe80::c2e8:62ff:fe2c:7e55._apple-mobdev2._tcp.local.
		} (13.2 (17B84))
2020-11-19 14:04:46.046 AWSDeviceFarmiOSReferenceAppUITestsSwift-Runner[1149:165186] Running tests...
Test Suite 'Selected tests' started at 2020-11-19 14:04:46.133
Test Suite 'AWSDeviceFarmiOSReferenceAppUITestsSwift.xctest' started at 2020-11-19 14:04:46.134
Test Suite 'AWSDeviceFarmiOSReferenceAppUITestsSwift.xctest' passed at 2020-11-19 14:04:46.134.
	 Executed 0 tests, with 0 failures (0 unexpected) in 0.000 (0.000) seconds
Test Suite 'Selected tests' passed at 2020-11-19 14:04:46.134.
	 Executed 0 tests, with 1 failure (0 unexpected) in 0.000 (0.001) seconds


Test session results and logs:
	/tmp/2f1fd1aa6ec7cbaa1c6d38dd16c83492415dd4d8_xcuitest_derived_data_1/Logs/Test/Test-Transient Testing-2020.11.19_14-04-41-+0000.xcresult

2020-11-19 14:04:46.142 xcodebuild[93236:3385905] [MT] IDETestOperationsObserverDebug: 4.858 elapsed -- Testing started completed.
2020-11-19 14:04:46.142 xcodebuild[93236:3385905] [MT] IDETestOperationsObserverDebug: 0.000 sec, +0.000 sec -- start
2020-11-19 14:04:46.142 xcodebuild[93236:3385905] [MT] IDETestOperationsObserverDebug: 4.858 sec, +4.858 sec -- end
** TEST EXECUTE SUCCEEDED **
