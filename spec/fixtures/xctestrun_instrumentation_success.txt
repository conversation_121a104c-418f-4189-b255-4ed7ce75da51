Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild test-without-building -xctestrun budget_keeper_single_ut.xctestrun -destination id=********-000C506A14F8001E -derivedDataPath /Users/<USER>/BrowserStack/rought/xctestplan/tmp

User defaults from command line:
    IDEDerivedDataPathOverride = /Users/<USER>/BrowserStack/rought/xctestplan/tmp
    IDEPackageSupportUseBuiltinSCM = YES

Testing started
Test Suite 'Selected tests' started at 2022-12-15 20:18:19.552
Test Suite 'BudgetKeeperTests.xctest' started at 2022-12-15 20:18:19.552
Test Suite 'AccountsViewModelUnitTest' started at 2022-12-15 20:18:19.552
Test Case '-[BudgetKeeperTests.AccountsViewModelUnitTest testAccountsListEmpty]' started.
Test Case '-[BudgetKeeperTests.AccountsViewModelUnitTest testAccountsListEmpty]' passed (0.001 seconds).
Test Suite 'AccountsViewModelUnitTest' passed at 2022-12-15 20:18:19.554.
	 Executed 1 test, with 0 failures (0 unexpected) in 0.001 (0.002) seconds
Test Suite 'BudgetKeeperTests.xctest' passed at 2022-12-15 20:18:19.554.
	 Executed 1 test, with 0 failures (0 unexpected) in 0.001 (0.002) seconds
Test Suite 'Selected tests' passed at 2022-12-15 20:18:19.554.
	 Executed 1 test, with 0 failures (0 unexpected) in 0.001 (0.003) seconds
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
2022-12-15 20:18:19.389 xcodebuild[14366:2441011] [MT] IDETestOperationsObserverDebug: 1.071 elapsed -- Testing started completed.
2022-12-15 20:18:19.389 xcodebuild[14366:2441011] [MT] IDETestOperationsObserverDebug: 0.000 sec, +0.000 sec -- start
2022-12-15 20:18:19.389 xcodebuild[14366:2441011] [MT] IDETestOperationsObserverDebug: 1.071 sec, +1.071 sec -- end

Test session results, code coverage, and logs:
	/Users/<USER>/BrowserStack/rought/xctestplan/tmp/Logs/Test/Test-Transient Testing-2022.12.15_20-18-18-+0530.xcresult

** TEST EXECUTE SUCCEEDED **
