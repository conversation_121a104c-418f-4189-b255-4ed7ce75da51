Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -xctestrun /tmp/00008130-000455E02E42001C_xctestrun.xctestrun -destination id=00008130-000455E02E42001C test-without-building -derivedDataPath /tmp/00008130-000455E02E42001C_xcuitest_derived_data_9 "-only-testing:UITests/BeneficiaryUITestsHHNO/testDeleteContact_TC4170_Acceptance_HHNO" -test-repetition-relaunch-enabled YES -retry-tests-on-failure -test-iterations 2 -resultBundlePath /tmp/00008130-000455E02E42001C_xcresult_bundle/UITests/BeneficiaryUITestsHHNO/testDeleteContact_TC4170_Acceptance_HHNO_1728357606 -resultBundleVersion 3

User defaults from command line:
    IDEBuildOperationResultBundlePath = /tmp/00008130-000455E02E42001C_xcresult_bundle/UITests/BeneficiaryUITestsHHNO/testDeleteContact_TC4170_Acceptance_HHNO_1728357606
    IDEDerivedDataPathOverride = /tmp/00008130-000455E02E42001C_xcuitest_derived_data_9
    IDEPackageSupportUseBuiltinSCM = YES
    IDERequestedResultBundleFormatVersion = 3

Retrying tests on failure. Running tests repeatedly up to 2 times.

Writing result bundle at path:
	/tmp/00008130-000455E02E42001C_xcresult_bundle/UITests/BeneficiaryUITestsHHNO/testDeleteContact_TC4170_Acceptance_HHNO_1728357606

Test Suite 'Selected tests' started at 2024-10-08 03:20:13.992.
Test Suite 'UITests.xctest' started at 2024-10-08 03:20:13.993.
Test Suite 'BeneficiaryUITestsHHNO' started at 2024-10-08 03:20:13.993.
Test Case '-[NBUITest.BeneficiaryUITestsHHNO testDeleteContact_TC4170_Acceptance_HHNO]' started.
    t =     0.00s Start Test at 2024-10-08 03:20:13.994
    t =     0.01s Set Up
    t =     0.01s     Open com.nordea.nb.dev
    t =     0.01s         Launch com.nordea.nb.dev
    t =     0.01s             Terminate com.nordea.nb.dev:4046
    t =     1.12s             Setting up automation session
    t =     1.15s             Wait for com.nordea.nb.dev to idle
Testing failed:
	UITests-Runner encountered an error (Failed to establish communication with the test runner. (Underlying Error: A connection to this device could not be established. (Underlying Error: The operation couldn’t be completed. Input/output error)))

Failing tests:
	BeneficiaryUITestsHHNO.testDeleteContact_TC4170_Acceptance_HHNO()

** TEST EXECUTE FAILED **

Testing started