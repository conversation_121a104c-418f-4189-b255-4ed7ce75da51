<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-/Apple/DTD PLIST 1.0/EN" "http:/www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>xctest</key>
    <dict>
      <key>CommandLineArguments</key>
      <array>


      </array>
      <key>IsUITestBundle</key>
      <true/>
      <key>UseDestinationArtifacts</key>
      <true/>
      <key>IsXCTRunnerHostedTestBundle</key>
      <true/>
      <key>EnvironmentVariables</key>
      <dict>

      </dict>
      <key>ProductModuleName</key>
      <string>product_module_name</string>
      <key>SystemAttachmentLifetime</key>
      <string>keepAlways</string>
      <key>TestHostBundleIdentifier</key>
      <string></string>
      <key>TestingEnvironmentVariables</key>
      <dict>
        <key>DYLD_FRAMEWORK_PATH</key>
        <string>/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks</string>
        <key>DYLD_LIBRARY_PATH</key>
        <string>/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks</string>
        <key>XCODE_DBG_XPC_EXCLUSIONS</key>
        <string>com.apple.dt.xctestSymbolicator</string>
      </dict>
      <key>ToolchainsSettingValue</key>
      <array/>
      <key>UITargetAppCommandLineArguments</key>
      <array/>
      <key>UITargetAppMainThreadCheckerEnabled</key>
      <false/>
      <key>UserAttachmentLifetime</key>
      <string>keepAlways</string>
      <key>UITargetAppBundleIdentifier</key>
      <string>app_details_bundle_id</string>
      <key>TestBundleDestinationRelativePath</key>
      <string>__TESTHOST__/PlugIns/xctest.xctest</string>
    </dict>
  </dict>
</plist>
