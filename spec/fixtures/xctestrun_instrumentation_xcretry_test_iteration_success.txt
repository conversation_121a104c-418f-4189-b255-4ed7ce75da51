Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -xctestrun /tmp/00008110-000524A03C87801E_xctestrun.xml -destination id=00008110-000524A03C87801E test-without-building -derivedDataPath /tmp/00008110-000524A03C87801E_xcuitest_derived_data_1 "-only-testing:BullsEyeUITests/AlwaysPassingTest/testGameStyleSwitch" -test-iterations 5 -resultBundlePath /tmp/00008110-000524A03C87801E_xcresult_bundle/BullsEyeUITests/AlwaysPassingTest/testGameStyleSwitch_1688662450 -resultBundleVersion 3

User defaults from command line:
    IDEBuildOperationResultBundlePath = /tmp/00008110-000524A03C87801E_xcresult_bundle/BullsEyeUITests/AlwaysPassingTest/testGameStyleSwitch_1688662450
    IDEDerivedDataPathOverride = /tmp/00008110-000524A03C87801E_xcuitest_derived_data_1
    IDEPackageSupportUseBuiltinSCM = YES
    IDERequestedResultBundleFormatVersion = 3
    XCTHTestRunSpecificationPath = /tmp/00008110-000524A03C87801E_xctestrun.xml

Running tests repeatedly 5 times.

Writing result bundle at path:
	/tmp/00008110-000524A03C87801E_xcresult_bundle/BullsEyeUITests/AlwaysPassingTest/testGameStyleSwitch_1688662450

2023-07-06 16:54:14.306 BullsEyeUITests-Runner[663:15773] Running tests...
2023-07-06 16:54:14.372 BullsEyeUITests-Runner[663:15773] Test bundle Info.plist at /var/containers/Bundle/Application/9D9EE33E-571F-4CFE-8748-042A136BB471/BullsEyeUITests-Runner.app/PlugIns/BullsEyeUITests.xctest/Info.plist specified BullsEyeUITests.CustomTestObserver for NSPrincipalClass, but no class matching that name was found.
Test Suite 'Selected tests' started at 2023-07-06 16:54:14.445
Test Suite 'BullsEyeUITests.xctest' started at 2023-07-06 16:54:14.446
Test Suite 'AlwaysPassingTest' started at 2023-07-06 16:54:14.446
Test Case '-[BullsEyeUITests.AlwaysPassingTest testGameStyleSwitch]' started (Iteration 1 of 5).
    t =     0.00s Start Test at 2023-07-06 16:54:14.446
    t =     0.05s Set Up
    t =     0.05s     Open io.bharti.BullsEye
    t =     0.08s         Launch io.bharti.BullsEye
    t =     0.18s             Setting up automation session
    t =     0.23s             Wait for io.bharti.BullsEye to idle
    t =     1.30s Find the "Slide" Button
    t =     1.36s Checking existence of `"Get as close as you can to: " StaticText`
    t =     1.37s Checking existence of `"Guess where the slider is: " StaticText`
    t =     1.39s Tap "Type" Button
    t =     1.39s     Wait for io.bharti.BullsEye to idle
    t =     1.46s     Find the "Type" Button
    t =     1.47s     Check for interrupting elements affecting "Type" Button
    t =     1.49s     Synthesize event
    t =     1.64s     Wait for io.bharti.BullsEye to idle
    t =     2.05s Checking existence of `"Guess where the slider is: " StaticText`
    t =     2.08s Checking existence of `"Get as close as you can to: " StaticText`
    t =     2.09s Tear Down
Test Case '-[BullsEyeUITests.AlwaysPassingTest testGameStyleSwitch]' passed (2.296 seconds).
Test Case '-[BullsEyeUITests.AlwaysPassingTest testGameStyleSwitch]' started (Iteration 2 of 5).
    t =     0.00s Start Test at 2023-07-06 16:54:16.744
    t =     0.08s Set Up
    t =     0.08s     Open io.bharti.BullsEye
    t =     0.14s         Launch io.bharti.BullsEye
    t =     0.14s             Terminate io.bharti.BullsEye:665
    t =     1.38s             Setting up automation session
    t =     1.39s             Wait for io.bharti.BullsEye to idle
    t =     2.46s Find the "Slide" Button
    t =     2.50s Find the "Type" Button
    t =     2.52s Checking existence of `"Guess where the slider is: " StaticText`
    t =     2.53s Checking existence of `"Get as close as you can to: " StaticText`
    t =     2.54s Tap "Slide" Button
    t =     2.54s     Wait for io.bharti.BullsEye to idle
    t =     2.61s     Find the "Slide" Button
    t =     2.63s     Check for interrupting elements affecting "Slide" Button
    t =     2.65s     Synthesize event
    t =     2.81s     Wait for io.bharti.BullsEye to idle
    t =     3.23s Checking existence of `"Get as close as you can to: " StaticText`
    t =     3.25s Checking existence of `"Guess where the slider is: " StaticText`
    t =     3.27s Tear Down
Test Case '-[BullsEyeUITests.AlwaysPassingTest testGameStyleSwitch]' passed (3.480 seconds).
Test Case '-[BullsEyeUITests.AlwaysPassingTest testGameStyleSwitch]' started (Iteration 3 of 5).
    t =     0.00s Start Test at 2023-07-06 16:54:20.225
    t =     0.08s Set Up
    t =     0.08s     Open io.bharti.BullsEye
    t =     0.14s         Launch io.bharti.BullsEye
    t =     0.14s             Terminate io.bharti.BullsEye:666
    t =     1.36s             Setting up automation session
    t =     1.40s             Wait for io.bharti.BullsEye to idle
    t =     2.48s Find the "Slide" Button
    t =     2.52s Checking existence of `"Get as close as you can to: " StaticText`
    t =     2.54s Checking existence of `"Guess where the slider is: " StaticText`
    t =     2.55s Tap "Type" Button
    t =     2.56s     Wait for io.bharti.BullsEye to idle
    t =     2.65s     Find the "Type" Button
    t =     2.67s     Check for interrupting elements affecting "Type" Button
    t =     2.70s     Synthesize event
    t =     2.86s     Wait for io.bharti.BullsEye to idle
    t =     3.27s Checking existence of `"Guess where the slider is: " StaticText`
    t =     3.30s Checking existence of `"Get as close as you can to: " StaticText`
    t =     3.32s Tear Down
Test Case '-[BullsEyeUITests.AlwaysPassingTest testGameStyleSwitch]' passed (3.524 seconds).
Test Case '-[BullsEyeUITests.AlwaysPassingTest testGameStyleSwitch]' started (Iteration 4 of 5).
    t =     0.00s Start Test at 2023-07-06 16:54:23.750
    t =     0.09s Set Up
    t =     0.09s     Open io.bharti.BullsEye
    t =     0.15s         Launch io.bharti.BullsEye
    t =     0.15s             Terminate io.bharti.BullsEye:672
    t =     1.36s             Setting up automation session
    t =     1.39s             Wait for io.bharti.BullsEye to idle
    t =     2.47s Find the "Slide" Button
    t =     2.52s Find the "Type" Button
    t =     2.54s Checking existence of `"Guess where the slider is: " StaticText`
    t =     2.55s Checking existence of `"Get as close as you can to: " StaticText`
    t =     2.56s Tap "Slide" Button
    t =     2.56s     Wait for io.bharti.BullsEye to idle
    t =     2.63s     Find the "Slide" Button
    t =     2.65s     Check for interrupting elements affecting "Slide" Button
    t =     2.67s     Synthesize event
    t =     2.82s     Wait for io.bharti.BullsEye to idle
    t =     3.23s Checking existence of `"Get as close as you can to: " StaticText`
    t =     3.26s Checking existence of `"Guess where the slider is: " StaticText`
    t =     3.27s Tear Down
Test Case '-[BullsEyeUITests.AlwaysPassingTest testGameStyleSwitch]' passed (3.482 seconds).
Test Case '-[BullsEyeUITests.AlwaysPassingTest testGameStyleSwitch]' started (Iteration 5 of 5).
    t =     0.00s Start Test at 2023-07-06 16:54:27.233
    t =     0.09s Set Up
    t =     0.09s     Open io.bharti.BullsEye
    t =     0.14s         Launch io.bharti.BullsEye
    t =     0.14s             Terminate io.bharti.BullsEye:673
    t =     1.35s             Setting up automation session
    t =     1.37s             Wait for io.bharti.BullsEye to idle
    t =     2.44s Find the "Slide" Button
    t =     2.49s Checking existence of `"Get as close as you can to: " StaticText`
    t =     2.50s Checking existence of `"Guess where the slider is: " StaticText`
    t =     2.52s Tap "Type" Button
    t =     2.52s     Wait for io.bharti.BullsEye to idle
    t =     2.59s     Find the "Type" Button
    t =     2.61s     Check for interrupting elements affecting "Type" Button
    t =     2.63s     Synthesize event
    t =     2.78s     Wait for io.bharti.BullsEye to idle
    t =     3.20s Checking existence of `"Guess where the slider is: " StaticText`
    t =     3.22s Checking existence of `"Get as close as you can to: " StaticText`
    t =     3.24s Tear Down
Test Case '-[BullsEyeUITests.AlwaysPassingTest testGameStyleSwitch]' passed (3.448 seconds).
Test Suite 'AlwaysPassingTest' passed at 2023-07-06 16:54:30.682.
	 Executed 5 tests, with 0 failures (0 unexpected) in 16.230 (16.236) seconds
Test Suite 'BullsEyeUITests.xctest' passed at 2023-07-06 16:54:30.684.
	 Executed 5 tests, with 0 failures (0 unexpected) in 16.230 (16.238) seconds
Test Suite 'Selected tests' passed at 2023-07-06 16:54:30.686.
	 Executed 5 tests, with 0 failures (0 unexpected) in 16.230 (16.240) seconds
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
2023-07-06 16:54:30.679 xcodebuild[9435:1663246] [MT] IDETestOperationsObserverDebug: 18.229 elapsed -- Testing started completed.
2023-07-06 16:54:30.679 xcodebuild[9435:1663246] [MT] IDETestOperationsObserverDebug: 0.000 sec, +0.000 sec -- start
2023-07-06 16:54:30.679 xcodebuild[9435:1663246] [MT] IDETestOperationsObserverDebug: 18.229 sec, +18.229 sec -- end

Test session results, code coverage, and logs:
	/tmp/00008110-000524A03C87801E_xcresult_bundle/BullsEyeUITests/AlwaysPassingTest/testGameStyleSwitch_1688662450

** TEST EXECUTE SUCCEEDED **

Testing started