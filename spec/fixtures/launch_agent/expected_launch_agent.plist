<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
      <key>EnvironmentVariables</key>
      <dict>
        <key>PATH</key>
        <string>/custom/prepend/path:/bin:/usr/bin:/usr/local/bin:/opt/homebrew/bin:/custom/path</string>
        <key>rvmsudo_secure_path</key>
        <string>0</string>
        
        <key>LANG</key> <string>en_IE.UTF-8</string>
        <key>TERM</key> <string>xterm-256color</string>
        
        
          <key>APPIUM_HOME</key>
          <string>/path/to/appium/home</string>
        
      </dict>
      <key>UserName</key>
        <string>app</string>
        <key>Label</key>
        <string>example_program</string>
        
        <key>ProgramArguments</key>
        <array>
          
              <string>run_command</string>
          
              <string>arg1</string>
          
              <string>arg2</string>
          
        </array>
        
        
        <key>SessionCreate</key> <true/>
        
        <key>RunAtLoad</key>
        <true/>
        <key>KeepAlive</key>
        <false/>
        <key>StandardOutPath</key>
        <string>/var/log/browserstack/example_program.log</string>
        <key>StandardErrorPath</key>
        <string>/var/log/browserstack/example_program.log</string>
        
          
          
          
              <key>WorkingDirectory</key>
              <string>/my/working/dir</string>
          
        
        
          <key>StartCalendarInterval</key>
          <dict>
          
            <key>Minute</key>
            <integer>60</integer>
          
          </dict>
         
    </dict>
</plist>
