{"devices": {"00008030-000A65590183802E": {"ip": "***************", "port": 8080, "selenium_port": 8080, "dev_tool_port": 443, "debugger_port": 27753, "webdriver_port": 8400, "zotac_host": "mobile-194-165-164-148.browserstack.com", "os": "ios", "region": "eu-west-1", "sub_region": "eu-west-1c", "for_live": false, "pool_mask": null, "active_session": "true", "app_live_network_logs_port": 27853, "current_appium_version": "1.17.0", "device_name": "iPhone12,8", "device_serial": "F17CL2NMPLJQ", "device_version": "13.4.1", "profile_check_counter": 21, "online": false, "offline_reason": "Device under cleaning", "device_check_count": 663, "mac_address": "90:8c:43:ba:4e:bc", "battery_level": 100, "web_driver_agent_xctestrun_file": {"1.17.0": "/usr/local/.browserstack/config/wda_derived_data_00008030-000A65590183802E_1.17.0/Build/Products/WebDriverAgentRunner_iphoneos13.5-arm64.xctestrun", "1.18.0": "/usr/local/.browserstack/config/wda_derived_data_00008030-000A65590183802E_1.18.0/Build/Products/WebDriverAgentRunner_iphoneos13.5-arm64.xctestrun"}, "last_online": "2020-10-05 17:09:00 +0000", "mobileprovision_branch": "production_29_17062020", "location_enabled": "true", "app_info": {"com.browserstack.Launcher": {"version": "1", "installed_at": "2020-06-24 16:29:09 +0000"}, "com.google.chrome.ios": {"version": "80.0.3987.95", "installed_at": "2020-06-25 17:46:58 +0000"}}, "node_path": "/nix/store/path-to-node-package/bin/node", "platform_version": "13.5"}, "00008030-001611080C05802E": {"ip": "***************", "port": 8082, "selenium_port": 8082, "dev_tool_port": 443, "debugger_port": 27755, "webdriver_port": 8402, "zotac_host": "mobile-194-165-164-148.browserstack.com", "os": "ios", "region": "eu-west-1", "sub_region": "eu-west-1c", "for_live": false, "pool_mask": null, "active_session": "false", "app_live_network_logs_port": 27855, "current_appium_version": "1.17.0", "device_name": "iPhone12,8", "device_serial": "F17CL1G4PLJQ", "device_version": "12.1", "profile_check_counter": 985, "online": true, "offline_reason": "", "device_check_count": 663, "mac_address": "90:8c:43:bb:59:1b", "battery_level": 100, "web_driver_agent_xctestrun_file": {"1.17.0": "/usr/local/.browserstack/config/wda_derived_data_00008030-001611080C05802E_1.17.0/Build/Products/WebDriverAgentRunner_iphoneos13.5-arm64.xctestrun", "1.18.0": "/usr/local/.browserstack/config/wda_derived_data_00008030-001611080C05802E_1.18.0/Build/Products/WebDriverAgentRunner_iphoneos13.5-arm64.xctestrun"}, "last_online": "2020-10-05 17:11:31 +0000", "mobileprovision_branch": "production_29_17062020", "location_enabled": "true", "app_info": {"com.browserstack.Launcher": {"version": "1", "installed_at": "2020-06-24 16:29:08 +0000"}, "com.google.chrome.ios": {"version": "80.0.3987.95", "installed_at": "2020-06-25 17:50:15 +0000"}}, "node_path": "/nix/store/path-to-node-package/bin/node", "platform_version": "13.5"}, "00008027-000A78561181802E": {"ip": "***************", "port": 8083, "selenium_port": 8083, "dev_tool_port": 443, "debugger_port": 27756, "webdriver_port": 8403, "zotac_host": "mobile-194-165-164-148.browserstack.com", "os": "ios", "region": "eu-west-1", "sub_region": "eu-west-1c", "for_live": false, "pool_mask": null, "active_session": "false", "app_live_network_logs_port": 27856, "current_appium_version": "1.17.0", "device_name": "iPad8,12", "device_serial": "DMPCF056PV1P", "device_version": "13.4", "profile_check_counter": 370, "online": true, "offline_reason": "", "device_check_count": 663, "mac_address": "3c:bf:60:7b:37:98", "battery_level": 100, "mobileprovision_branch": "production_26_31032020", "web_driver_agent_xctestrun_file": {"1.17.0": "/usr/local/.browserstack/config/wda_derived_data_00008027-000A78561181802E_1.17.0/Build/Products/WebDriverAgentRunner_iphoneos13.5-arm64.xctestrun", "1.18.0": "/usr/local/.browserstack/config/wda_derived_data_00008027-000A78561181802E_1.18.0/Build/Products/WebDriverAgentRunner_iphoneos13.5-arm64.xctestrun"}, "last_online": "2020-10-05 17:11:31 +0000", "location_enabled": "true", "app_info": {"com.browserstack.Launcher": {"version": "1", "installed_at": "2020-06-24 16:29:09 +0000"}, "com.google.chrome.ios": {"version": "80.0.3987.95", "installed_at": "2020-06-25 17:53:24 +0000"}}, "node_path": "/nix/store/path-to-node-package/bin/node", "platform_version": "13.5"}, "00008027-000C19012106802E": {"ip": "***************", "port": 8085, "selenium_port": 8085, "dev_tool_port": 443, "debugger_port": 27758, "webdriver_port": 8405, "zotac_host": "mobile-194-165-164-148.browserstack.com", "os": "ios", "region": "eu-west-1", "sub_region": "eu-west-1c", "for_live": false, "pool_mask": null, "active_session": "false", "app_live_network_logs_port": 27858, "current_appium_version": "1.17.0", "device_name": "iPad8,12", "device_serial": "DMPCC194PV1P", "device_version": "11.1", "profile_check_counter": 934, "online": true, "offline_reason": "", "device_check_count": 663, "mac_address": "3c:bf:60:7e:81:69", "battery_level": 100, "mobileprovision_branch": "production_26_31032020", "web_driver_agent_xctestrun_file": {"1.17.0": "/usr/local/.browserstack/config/wda_derived_data_00008027-000C19012106802E_1.17.0/Build/Products/WebDriverAgentRunner_iphoneos13.5-arm64.xctestrun", "1.18.0": "/usr/local/.browserstack/config/wda_derived_data_00008027-000C19012106802E_1.18.0/Build/Products/WebDriverAgentRunner_iphoneos13.5-arm64.xctestrun"}, "last_online": "2020-10-05 17:11:34 +0000", "location_enabled": "true", "app_info": {"com.browserstack.Launcher": {"version": "1", "installed_at": "2020-06-24 16:33:34 +0000"}, "com.google.chrome.ios": {"version": "80.0.3987.95", "installed_at": "2020-06-25 17:55:08 +0000"}}, "node_path": "/nix/store/path-to-node-package/bin/node", "platform_version": "13.5"}, "00008027-000A31A02181802E": {"ip": "***************", "port": 8084, "selenium_port": 8084, "dev_tool_port": 443, "debugger_port": 27757, "webdriver_port": 8404, "zotac_host": "mobile-194-165-164-148.browserstack.com", "os": "ios", "region": "eu-west-1", "sub_region": "eu-west-1c", "for_live": false, "pool_mask": null, "active_session": "false", "app_live_network_logs_port": 27857, "current_appium_version": "1.17.0", "device_name": "iPad8,12", "device_serial": "DMPCF0R0PV1P", "device_version": "10.1", "profile_check_counter": 294, "online": true, "offline_reason": "", "device_check_count": 663, "mac_address": "3c:bf:60:7b:dd:ea", "battery_level": 100, "mobileprovision_branch": "production_26_31032020", "web_driver_agent_xctestrun_file": {"1.17.0": "/usr/local/.browserstack/config/wda_derived_data_00008027-000A31A02181802E_1.17.0/Build/Products/WebDriverAgentRunner_iphoneos13.5-arm64.xctestrun", "1.18.0": "/usr/local/.browserstack/config/wda_derived_data_00008027-000A31A02181802E_1.18.0/Build/Products/WebDriverAgentRunner_iphoneos13.5-arm64.xctestrun"}, "last_online": "2020-10-05 17:11:32 +0000", "location_enabled": "true", "app_info": {"com.browserstack.Launcher": {"version": "1", "installed_at": "2020-06-24 16:29:10 +0000"}, "com.google.chrome.ios": {"version": "80.0.3987.95", "installed_at": "2020-09-15 13:55:11 +0000"}}, "node_path": "/nix/store/path-to-node-package/bin/node", "platform_version": "13.5"}, "00008027-001069940181802E": {"ip": "***************", "port": 8086, "selenium_port": 8086, "dev_tool_port": 443, "debugger_port": 27759, "webdriver_port": 8406, "zotac_host": "mobile-194-165-164-148.browserstack.com", "os": "ios", "region": "eu-west-1", "sub_region": "eu-west-1c", "for_live": false, "pool_mask": null, "active_session": "false", "app_live_network_logs_port": 27859, "current_appium_version": "1.17.0", "device_name": "", "device_serial": "DMPCD0WWPV1P", "device_version": "14.0", "mac_address": "3c:bf:60:7c:ce:8a", "profile_check_counter": 799, "online": true, "offline_reason": "", "battery_level": 100, "web_driver_agent_xctestrun_file": {"1.17.0": "/usr/local/.browserstack/config/wda_derived_data_00008027-001069940181802E_1.17.0/Build/Products/WebDriverAgentRunner_iphoneos13.5-arm64.xctestrun", "1.18.0": "/usr/local/.browserstack/config/wda_derived_data_00008027-001069940181802E_1.18.0/Build/Products/WebDriverAgentRunner_iphoneos13.5-arm64.xctestrun"}, "mobileprovision_branch": "production_26_31032020", "location_enabled": "true", "device_check_count": 663, "app_info": {"com.google.chrome.ios": {"version": "80.0.3987.95", "installed_at": "2020-09-07 11:54:35 +0000"}, "com.browserstack.Launcher": {"version": "1", "installed_at": "2020-09-07 11:54:36 +0000"}}, "last_online": "2020-10-05 17:11:32 +0000", "node_path": "/nix/store/path-to-node-package/bin/node", "platform_version": "13.5"}, "00008020-000859E23C80402E": {"ip": "***************", "port": 8087, "selenium_port": 8087, "dev_tool_port": 443, "debugger_port": 27760, "webdriver_port": 8407, "zotac_host": "mobile-194-165-164-148.browserstack.com", "os": "ios", "region": "eu-west-1", "sub_region": "eu-west-1c", "for_live": false, "pool_mask": null, "active_session": "false", "app_live_network_logs_port": 27860, "current_appium_version": "1.17.0", "device_name": "iPad11,4", "device_serial": "F9FD51FGLMVY", "device_version": "13.5.1", "mac_address": "00:5b:94:ba:86:4f", "profile_check_counter": 1, "online": false, "offline_reason": "Device under cleaning", "battery_level": 100, "device_check_count": 663, "node_path": "/nix/store/path-to-node-package/bin/node", "platform_version": "13.5", "web_driver_agent_xctestrun_file": {"1.17.0": "/usr/local/.browserstack/config/wda_derived_data_00008020-000859E23C80402E_1.17.0/Build/Products/WebDriverAgentRunner_iphoneos13.5-arm64.xctestrun", "1.18.0": "/usr/local/.browserstack/config/wda_derived_data_00008020-000859E23C80402E_1.18.0/Build/Products/WebDriverAgentRunner_iphoneos13.5-arm64.xctestrun"}, "app_info": {}, "mobileprovision_branch": "production_27_10042020", "last_online": "2020-09-08 12:14:03 +0000", "cleanup_failure_reason": "start driver failed: unknown app bundle com.apple.testflight"}}, "configuration": {"selenium_port": 8079, "webkit_proxy_port": 27752, "webdriver_agent_port": 8399}}