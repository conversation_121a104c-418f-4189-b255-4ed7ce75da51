Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild test-without-building -destination id=00008140-001C04591082201C -xctestrun /tmp/00008140-001C04591082201C_xctestrun.xctestrun -retry-tests-on-failure -test-iterations 2 -derivedDataPath /tmp/00008140-001C04591082201C_xcuitest_derived_data -resultBundlePath /tmp/00008140-001C04591082201C_xcresult_bundle/dummyModule/dummyClass/dummyTest_1732964412 -resultBundleVersion 3

User defaults from command line:
    IDEBuildOperationResultBundlePath = /tmp/00008140-001C04591082201C_xcresult_bundle/dummyModule/dummyClass/dummyTest_1732964412
    IDEDerivedDataPathOverride = /tmp/00008140-001C04591082201C_xcuitest_derived_data
    IDEPackageSupportUseBuiltinSCM = YES
    IDERequestedResultBundleFormatVersion = 3

Retrying tests on failure. Running tests repeatedly up to 2 times.

Writing result bundle at path:
	/tmp/00008140-001C04591082201C_xcresult_bundle/dummyModule/dummyClass/dummyTest_1732964412

Test Suite 'All tests' started at 2024-11-30 11:00:15.149.
Test Suite 'BStackMediaAppUITests.xctest' started at 2024-11-30 11:00:15.149.
Test Suite 'BStackMediaAppUITests' started at 2024-11-30 11:00:15.149.
Test Case '-[BStackMediaAppUITests.BStackMediaAppUITests testGeoLocationText]' started (Iteration 1 of 2).
    t =     0.00s Start Test at 2024-11-30 11:00:15.150
    t =     0.01s Set Up
    t =     0.01s     Open com.bsstag.BStackMediaApp
    t =    61.95s Collecting debug information to assist test failure triage
    t =    61.95s     Requesting snapshot of accessibility hierarchy for app with pid 34
    t =    62.05s Find the "geoLocation" StaticText
2024-11-30 11:01:17.233 BStackMediaAppUITests-Runner[1657:105666] {"status":"success","country":"Ireland","countryCode":"IE","region":"L","regionName":"Leinster","city":"Dublin","zip":"R93","lat":53.3382,"lon":-6.2591,"timezone":"Europe/Dublin","isp":"BT Communications Ireland Limited","org":"ESATNET","as":"AS2110 BT Communications Ireland Limited","query":"**************"}
2024-11-30 11:01:17.233 BStackMediaAppUITests-Runner[1657:105666] Saudi Arabia
2024-11-30 11:01:17.233 BStackMediaAppUITests-Runner[1657:105666] SA
/Users/<USER>/workspace/qa-common-scripts/mobile_apps/ios/BStackMediaApp/BStackMediaAppUITests/BStackMediaAppUITests.swift:233: error: -[BStackMediaAppUITests.BStackMediaAppUITests testGeoLocationText] : XCTAssertTrue failed
    t =    62.13s Tear Down
Test Case '-[BStackMediaAppUITests.BStackMediaAppUITests testGeoLocationText]' failed (62.334 seconds).
Test Case '-[BStackMediaAppUITests.BStackMediaAppUITests testGeoLocationText]' started (Iteration 2 of 2).
    t =     0.00s Start Test at 2024-11-30 11:01:17.486
    t =     0.02s Set Up
    t =     0.02s     Open com.bsstag.BStackMediaApp
    t =     0.03s         Launch com.bsstag.BStackMediaApp
    t =    28.83s     Checking `Expect predicate `existsNoRetry == 1` for object "Allow Full Access" Button`

Restarting after unexpected exit, crash, or test timeout; summary will include totals from previous launches.

Test Suite 'Selected tests' started at 2024-11-30 11:02:37.954.
Test Suite 'BStackMediaAppUITests.xctest' started at 2024-11-30 11:02:37.954.
Test Suite 'BStackMediaAppUITests' started at 2024-11-30 11:02:37.954.
Test Case '-[BStackMediaAppUITests.BStackMediaAppUITests testGPSLocationOtherApp]' started (Iteration 1 of 2).
    t =     0.00s Start Test at 2024-11-30 11:02:37.954
    t =     0.01s Set Up
    t =     0.01s     Open com.bsstag.BStackMediaApp
    t =     0.01s         Launch com.bsstag.BStackMediaApp
    t =     0.01s             Terminate com.bsstag.BStackMediaApp:1667
    t =     1.11s             Setting up automation session
    t =     1.19s             Wait for com.bsstag.BStackMediaApp to idle
    t =     2.24s Open com.bsstag.GPSLocation
    t =     2.25s     Launch com.bsstag.GPSLocation
/Users/<USER>/workspace/qa-common-scripts/mobile_apps/ios/BStackMediaApp/BStackMediaAppUITests/BStackMediaAppUITests.swift:239: error: -[BStackMediaAppUITests.BStackMediaAppUITests testGPSLocationOtherApp] : Failed to launch com.bsstag.GPSLocation: The request to open "com.bsstag.GPSLocation" failed. (Underlying Error: The operation couldn’t be completed. Application info provider (FBSApplicationLibrary) returned nil for "com.bsstag.GPSLocation")
    t =     2.30s Tear Down
Test Case '-[BStackMediaAppUITests.BStackMediaAppUITests testGPSLocationOtherApp]' failed (2.505 seconds).
Test Case '-[BStackMediaAppUITests.BStackMediaAppUITests testGPSLocationOtherApp]' started (Iteration 2 of 2).
    t =     0.00s Start Test at 2024-11-30 11:02:40.465
    t =     0.02s Set Up
    t =     0.02s             Open com.bsstag.BStackMediaApp
    t =     0.04s                 Launch com.bsstag.BStackMediaApp
    t =     0.04s                     Terminate com.bsstag.BStackMediaApp:1691
    t =     1.24s                     Setting up automation session
    t =     1.29s                     Wait for com.bsstag.BStackMediaApp to idle
    t =     2.32s         Open com.bsstag.GPSLocation
    t =     2.34s             Launch com.bsstag.GPSLocation
/Users/<USER>/workspace/qa-common-scripts/mobile_apps/ios/BStackMediaApp/BStackMediaAppUITests/BStackMediaAppUITests.swift:239: error: -[BStackMediaAppUITests.BStackMediaAppUITests testGPSLocationOtherApp] : Failed to launch com.bsstag.GPSLocation: The request to open "com.bsstag.GPSLocation" failed. (Underlying Error: The operation couldn’t be completed. Application info provider (FBSApplicationLibrary) returned nil for "com.bsstag.GPSLocation")
    t =     2.37s Tear Down
Test Case '-[BStackMediaAppUITests.BStackMediaAppUITests testGPSLocationOtherApp]' failed (2.577 seconds).
Test Case '-[BStackMediaAppUITests.BStackMediaAppUITests testGPSLocationText]' started (Iteration 1 of 2).
    t =     0.00s Start Test at 2024-11-30 11:02:43.043
    t =     0.02s Set Up
    t =    34.84s                     Checking `Expect predicate `existsNoRetry == 1` for object "Allow Full Access" Button`

Restarting after unexpected exit, crash, or test timeout; summary will include totals from previous launches.

Test Suite 'Selected tests' started at 2024-11-30 11:04:09.696.
Test Suite 'BStackMediaAppUITests.xctest' started at 2024-11-30 11:04:09.696.
Test Suite 'BStackMediaAppUITests' started at 2024-11-30 11:04:09.696.
Test Case '-[BStackMediaAppUITests.BStackMediaAppUITests testMediaText]' started (Iteration 1 of 2).
    t =     0.00s Start Test at 2024-11-30 11:04:09.696
    t =     0.01s Set Up
    t =    12.73s     Wait for com.apple.springboard to idle
    t =    16.10s Find the "photostext" StaticText
2024-11-30 11:04:25.816 BStackMediaAppUITests-Runner[1712:110020] 7
2024-11-30 11:04:25.816 BStackMediaAppUITests-Runner[1712:110020] 4
/Users/<USER>/workspace/qa-common-scripts/mobile_apps/ios/BStackMediaApp/BStackMediaAppUITests/BStackMediaAppUITests.swift:216: error: -[BStackMediaAppUITests.BStackMediaAppUITests testMediaText] : XCTAssertTrue failed
    t =    16.18s Tear Down
Test Case '-[BStackMediaAppUITests.BStackMediaAppUITests testMediaText]' failed (16.382 seconds).
Test Case '-[BStackMediaAppUITests.BStackMediaAppUITests testMediaText]' started (Iteration 2 of 2).
    t =     0.00s Start Test at 2024-11-30 11:04:26.081
    t =     0.02s Set Up
    t =     0.02s     Open com.bsstag.BStackMediaApp
    t =    28.70s Collecting debug information to assist test failure triage
    t =    28.70s     Requesting snapshot of accessibility hierarchy for app with pid 34
    t =    31.81s Find the "photostext" StaticText
2024-11-30 11:04:57.911 BStackMediaAppUITests-Runner[1712:110020] 4
/Users/<USER>/workspace/qa-common-scripts/mobile_apps/ios/BStackMediaApp/BStackMediaAppUITests/BStackMediaAppUITests.swift:216: error: -[BStackMediaAppUITests.BStackMediaAppUITests testMediaText] : XCTAssertTrue failed
    t =    31.85s Tear Down
Test Case '-[BStackMediaAppUITests.BStackMediaAppUITests testMediaText]' failed (32.058 seconds).
Test Suite 'BStackMediaAppUITests' failed at 2024-11-30 11:04:58.139.
	 Executed 2 tests, with 2 failures (0 unexpected) in 48.441 (48.443) seconds
Test Suite 'BStackMediaAppUITests.xctest' failed at 2024-11-30 11:04:58.143.
	 Executed 2 tests, with 2 failures (0 unexpected) in 48.441 (48.447) seconds
Test Suite 'Selected tests' failed at 2024-11-30 11:04:58.144.
	 Executed 2 tests, with 2 failures (0 unexpected) in 48.441 (48.448) seconds
2024-11-30 11:07:03.993 xcodebuild[1646:2839362] [MT] IDETestOperationsObserverDebug: 410.465 elapsed -- Testing started completed.
2024-11-30 11:07:03.993 xcodebuild[1646:2839362] [MT] IDETestOperationsObserverDebug: 0.000 sec, +0.000 sec -- start
2024-11-30 11:07:03.993 xcodebuild[1646:2839362] [MT] IDETestOperationsObserverDebug: 410.465 sec, +410.465 sec -- end

Test session results, code coverage, and logs:
	/tmp/00008140-001C04591082201C_xcresult_bundle/dummyModule/dummyClass/dummyTest_1732964412

Failing tests:
	BStackMediaAppUITests.testGeoLocationText()
	BStackMediaAppUITests.testGeoLocationText()
	BStackMediaAppUITests.testGPSLocationOtherApp()
	BStackMediaAppUITests.testGPSLocationText()
	BStackMediaAppUITests.testMediaText()

** TEST EXECUTE FAILED **

Testing started
