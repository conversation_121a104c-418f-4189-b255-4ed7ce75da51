<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<array>
	<dict>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIApplicationShortcutItems</key>
		<array>
			<dict>
				<key>UIApplicationShortcutItemTitle</key>
				<string>IDS_IOS_APPLICATION_SHORTCUT_NEWTAB_TITLE</string>
				<key>UIApplicationShortcutItemType</key>
				<string>OpenNewTab</string>
				<key>UIApplicationShortcutItemIconFile</key>
				<string>quick_action_new_tab</string>
			</dict>
			<dict>
				<key>UIApplicationShortcutItemTitle</key>
				<string>IDS_IOS_APPLICATION_SHORTCUT_NEWINCOGNITOTAB_TITLE</string>
				<key>UIApplicationShortcutItemType</key>
				<string>OpenIncognitoTab</string>
				<key>UIApplicationShortcutItemIconFile</key>
				<string>quick_action_new_incognito_tab</string>
			</dict>
			<dict>
				<key>UIApplicationShortcutItemTitle</key>
				<string>IDS_IOS_APPLICATION_SHORTCUT_VOICE_SEARCH_TITLE</string>
				<key>UIApplicationShortcutItemType</key>
				<string>OpenVoiceSearch</string>
				<key>UIApplicationShortcutItemIconFile</key>
				<string>quick_action_voice_search</string>
			</dict>
			<dict>
				<key>UIApplicationShortcutItemTitle</key>
				<string>IDS_IOS_APPLICATION_SHORTCUT_QR_SCANNER_TITLE</string>
				<key>UIApplicationShortcutItemType</key>
				<string>OpenQRScanner</string>
				<key>UIApplicationShortcutItemIconFile</key>
				<string>quick_action_qr_scanner</string>
			</dict>
		</array>
		<key>ITSEncryptionExportComplianceCode</key>
		<string>1ae3a51e-8dc3-4c12-8f0c-35cf8b22c6a2</string>
		<key>BreakpadProduct</key>
		<string>Chrome_iOS</string>
		<key>DTPlatformVersion</key>
		<string>11.2</string>
		<key>DTSDKBuild</key>
		<string>15C107</string>
		<key>UIStatusBarStyle</key>
		<string>UIStatusBarStyleLightContent</string>
		<key>BreakpadReportInterval</key>
		<string>3600</string>
		<key>BreakpadProductDisplay</key>
		<string>Chrome</string>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>BreakpadVersion</key>
		<string>64.0.3282.112</string>
		<key>NSCameraUsageDescription</key>
		<string>IDS_IOS_CAMERA_USAGE_DESCRIPTION</string>
		<key>ProfileValidated</key>
		<true/>
		<key>SCMRevision</key>
		<string>7ceafc6ca46e012a6dbb5933d52fae00eda1f3ca-refs/branch-heads/3282@{#576}</string>
		<key>CFBundleDisplayName</key>
		<string>Chrome</string>
		<key>SignerIdentity</key>
		<string>iPhone Developer: Browserstack Mobile (236WWMU8WM)</string>
		<key>CFBundleDocumentTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeName</key>
				<string>PDF</string>
				<key>LSItemContentTypes</key>
				<array>
					<string>com.adobe.pdf</string>
				</array>
				<key>LSHandlerRank</key>
				<string>Alternate</string>
			</dict>
		</array>
		<key>DTXcodeBuild</key>
		<string>9C40b</string>
		<key>UIApplicationDelegate</key>
		<string>MainApplicationDelegate</string>
		<key>EnvironmentVariables</key>
		<dict>
			<key>CFFIXED_USER_HOME</key>
			<string>/private/var/mobile/Containers/Data/Application/B2A0AF9F-4025-43A0-A736-7A0BFB632723</string>
			<key>TMPDIR</key>
			<string>/private/var/mobile/Containers/Data/Application/B2A0AF9F-4025-43A0-A736-7A0BFB632723/tmp</string>
			<key>HOME</key>
			<string>/private/var/mobile/Containers/Data/Application/B2A0AF9F-4025-43A0-A736-7A0BFB632723</string>
		</dict>
		<key>KSChannelChromeScheme</key>
		<string>googlechrome-stable</string>
		<key>SequenceNumber</key>
		<integer>642</integer>
		<key>IsDemotedApp</key>
		<false/>
		<key>CFBundleNumericVersion</key>
		<integer>0</integer>
		<key>CFBundleIdentifier</key>
		<string>com.google.chrome.ios</string>
		<key>Path</key>
		<string>/private/var/containers/Bundle/Application/9A1E9205-E6E6-4D88-AC73-6235919AE3EC/com.google.chrome.ios-64.0.3282.112.app</string>
		<key>NSUserActivityTypes</key>
		<array>
			<string>com.google.chrome.handoff</string>
		</array>
		<key>UIDeviceFamily</key>
		<array>
			<integer>1</integer>
			<integer>2</integer>
		</array>
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>chromium</string>
			<string>chromiums</string>
			<string>comgooglemaps</string>
			<string>googleapp</string>
			<string>googlechrome</string>
			<string>googlechromes</string>
			<string>googledevicepolicy</string>
			<string>googledocs</string>
			<string>googledrive</string>
			<string>googlegmail</string>
			<string>googlephotos</string>
			<string>googleplaymusic</string>
			<string>googlesheets</string>
			<string>googleslides</string>
			<string>gplus</string>
			<string>inbox-gmail</string>
			<string>vnd.google.calendar</string>
			<string>gr</string>
		</array>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleIcons</key>
		<dict>
			<key>CFBundlePrimaryIcon</key>
			<dict>
				<key>UIPrerenderedIcon</key>
				<true/>
				<key>CFBundleIconFiles</key>
				<array>
					<string>AppIcon29x29</string>
					<string>AppIcon40x40</string>
					<string>AppIcon60x60</string>
					<string>AppIcon76x76</string>
					<string>AppIcon83.5x83.5</string>
				</array>
				<key>CFBundleIconName</key>
				<string>AppIcon</string>
			</dict>
		</dict>
		<key>CFBundleSupportedPlatforms</key>
		<array>
			<string>iPhoneOS</string>
		</array>
		<key>IsUpgradeable</key>
		<true/>
		<key>GOOGLE_CLIENT_ID_MAIN</key>
		<string>77185425430-b2b4gm566k352mn3ug9bhk7l425q76i1.apps.googleusercontent.com</string>
		<key>SSOAuthURLScheme</key>
		<string>com.google.sso.chrome.stable</string>
		<key>ITSAppUsesNonExemptEncryption</key>
		<true/>
		<key>MinimumOSVersion</key>
		<string>10.0</string>
		<key>NSBluetoothPeripheralUsageDescription</key>
		<string>IDS_IOS_BLUETOOTH_USAGE_DESCRIPTION</string>
		<key>NSPhotoLibraryAddUsageDescription</key>
		<string>IDS_IOS_PHOTO_LIBRARY_ADD_USAGE_DESCRIPTION</string>
		<key>CFBundleName</key>
		<string>Chrome</string>
		<key>CFBundleShortVersionString</key>
		<string>64.3282.112</string>
		<key>UIBackgroundModes</key>
		<array>
			<string>fetch</string>
			<string>audio</string>
		</array>
		<key>CFBundleExecutable</key>
		<string>Chrome</string>
		<key>UTImportedTypeDeclarations</key>
		<array>
			<dict>
				<key>UTTypeConformsTo</key>
				<array>
					<string>public.url</string>
					<string>org.appextension.find-login-action</string>
				</array>
				<key>UTTypeIdentifier</key>
				<string>org.appextension.chrome-password-action</string>
				<key>UTTypeDescription</key>
				<string>Chrome Password Fill by App Extension Action</string>
			</dict>
			<dict>
				<key>UTTypeConformsTo</key>
				<array>
					<string>public.url</string>
				</array>
				<key>UTTypeIdentifier</key>
				<string>org.appextension.find-login-action</string>
				<key>UTTypeDescription</key>
				<string>1Password Find Login Action</string>
			</dict>
		</array>
		<key>ApplicationType</key>
		<string>User</string>
		<key>BreakpadSkipConfirm</key>
		<string>YES</string>
		<key>BreakpadURL</key>
		<string>https://clients2.google.com/cr/report</string>
		<key>Container</key>
		<string>/private/var/mobile/Containers/Data/Application/B2A0AF9F-4025-43A0-A736-7A0BFB632723</string>
		<key>UIPrerenderedIcon</key>
		<true/>
		<key>UIRequiresPersistentWiFi</key>
		<true/>
		<key>BuildMachineOSBuild</key>
		<string>16G29</string>
		<key>DTPlatformName</key>
		<string>iphoneos</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>IDS_IOS_MICROPHONE_USAGE_DESCRIPTION</string>
		<key>BreakpadSendAndExit</key>
		<string>YES</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>IDS_IOS_LOCATION_WHEN_IN_USE_USAGE_DESCRIPTION</string>
		<key>CFBundleVersion</key>
		<string>64.0.3282.112</string>
		<key>UIApplicationShortcutWidget</key>
		<string>com.google.chrome.ios.ContentTodayExtension</string>
		<key>CFBundleDevelopmentRegion</key>
		<string>English</string>
		<key>KSProductID</key>
		<string>com.google.chrome.ios</string>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleURLName</key>
				<string>com.google.chrome.ios</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>googlechrome</string>
					<string>googlechromes</string>
					<string>googlechrome-x-callback</string>
					<string>googlechrome-stable</string>
					<string>com.google.sso.chrome.stable</string>
				</array>
			</dict>
		</array>
		<key>DTCompiler</key>
		<string>com.apple.compilers.llvm.clang.1_0</string>
		<key>DTSDKName</key>
		<string>iphoneos11.2</string>
		<key>NSAppTransportSecurity</key>
		<dict>
			<key>NSAllowsArbitraryLoads</key>
			<true/>
		</dict>
		<key>Entitlements</key>
		<dict>
			<key>keychain-access-groups</key>
			<array>
				<string>8XPT93F9Q7.*</string>
			</array>
			<key>application-identifier</key>
			<string>8XPT93F9Q7.*</string>
			<key>get-task-allow</key>
			<true/>
			<key>com.apple.developer.team-identifier</key>
			<string>8XPT93F9Q7</string>
		</dict>
		<key>DTPlatformBuild</key>
		<string>15C107</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>IDS_IOS_PHOTO_LIBRARY_USAGE_DESCRIPTION</string>
		<key>DTXcode</key>
		<string>0920</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
	</dict>
	<dict>
		<key>UIRequiredDeviceCapabilities</key>
		<array>
			<string>armv7</string>
		</array>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>Entitlements</key>
		<dict>
			<key>keychain-access-groups</key>
			<array>
				<string>8XPT93F9Q7.*</string>
			</array>
			<key>application-identifier</key>
			<string>8XPT93F9Q7.*</string>
			<key>get-task-allow</key>
			<true/>
			<key>com.apple.developer.team-identifier</key>
			<string>8XPT93F9Q7</string>
		</dict>
		<key>DTPlatformVersion</key>
		<string>11.0</string>
		<key>CFBundleName</key>
		<string>Launcher</string>
		<key>DTSDKName</key>
		<string>iphoneos11.0</string>
		<key>ApplicationType</key>
		<string>User</string>
		<key>Container</key>
		<string>/private/var/mobile/Containers/Data/Application/06006E7E-B3AE-4F10-9F43-D99556B745D3</string>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>CFBundleDisplayName</key>
		<string>Launcher</string>
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>googlechrome</string>
			<string>googlechromes</string>
		</array>
		<key>DTSDKBuild</key>
		<string>15A372</string>
		<key>CFBundleShortVersionString</key>
		<string>1.0</string>
		<key>CFBundleSupportedPlatforms</key>
		<array>
			<string>iPhoneOS</string>
		</array>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>BuildMachineOSBuild</key>
		<string>16G29</string>
		<key>SignerIdentity</key>
		<string>iPhone Developer: Browserstack Mobile (236WWMU8WM)</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>EnvironmentVariables</key>
		<dict>
			<key>CFFIXED_USER_HOME</key>
			<string>/private/var/mobile/Containers/Data/Application/06006E7E-B3AE-4F10-9F43-D99556B745D3</string>
			<key>TMPDIR</key>
			<string>/private/var/mobile/Containers/Data/Application/06006E7E-B3AE-4F10-9F43-D99556B745D3/tmp</string>
			<key>HOME</key>
			<string>/private/var/mobile/Containers/Data/Application/06006E7E-B3AE-4F10-9F43-D99556B745D3</string>
		</dict>
		<key>CFBundleDevelopmentRegion</key>
		<string>en</string>
		<key>CFBundleNumericVersion</key>
		<integer>16809984</integer>
		<key>CFBundleVersion</key>
		<string>1</string>
		<key>IsDemotedApp</key>
		<false/>
		<key>MinimumOSVersion</key>
		<string>9.0</string>
		<key>SequenceNumber</key>
		<integer>643</integer>
		<key>IsUpgradeable</key>
		<true/>
		<key>DTXcodeBuild</key>
		<string>9A235</string>
		<key>DTPlatformBuild</key>
		<string>15A372</string>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIDeviceFamily</key>
		<array>
			<integer>1</integer>
			<integer>2</integer>
		</array>
		<key>CFBundleIdentifier</key>
		<string>com.browserstack.Launcher</string>
		<key>DTXcode</key>
		<string>0900</string>
		<key>CFBundleExecutable</key>
		<string>Launcher</string>
		<key>ProfileValidated</key>
		<true/>
		<key>********************</key>
		<string>Main</string>
		<key>Path</key>
		<string>/private/var/containers/Bundle/Application/2F3FE22A-F889-4FF6-A6DB-31F4B6A4F3B1/com.browserstack.Launcher-1.app</string>
		<key>DTPlatformName</key>
		<string>iphoneos</string>
		<key>DTCompiler</key>
		<string>com.apple.compilers.llvm.clang.1_0</string>
	</dict>
	<dict>
		<key>UIRequiredDeviceCapabilities</key>
		<array>
			<string>armv7</string>
		</array>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>Entitlements</key>
		<dict>
			<key>keychain-access-groups</key>
			<array>
				<string>8XPT93F9Q7.*</string>
			</array>
			<key>application-identifier</key>
			<string>8XPT93F9Q7.*</string>
			<key>get-task-allow</key>
			<false/>
			<key>com.apple.developer.team-identifier</key>
			<string>8XPT93F9Q7</string>
		</dict>
		<key>DTPlatformVersion</key>
		<string>10.3</string>
		<key>CFBundleName</key>
		<string>enterpriseDummy</string>
		<key>DTSDKName</key>
		<string>iphoneos10.3</string>
		<key>ApplicationType</key>
		<string>User</string>
		<key>UIStatusBarStyle</key>
		<string>UIStatusBarStyleLightContent</string>
		<key>Container</key>
		<string>/private/var/mobile/Containers/Data/Application/9FCC6531-96FF-42E9-A7BD-8D2478B8BE08</string>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>CFBundleDisplayName</key>
		<string>enterpriseDummy</string>
		<key>DTSDKBuild</key>
		<string>14E8301</string>
		<key>CFBundleShortVersionString</key>
		<string>1.0</string>
		<key>CFBundleSupportedPlatforms</key>
		<array>
			<string>iPhoneOS</string>
		</array>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
		</array>
		<key>BuildMachineOSBuild</key>
		<string>16C67</string>
		<key>SignerIdentity</key>
		<string>iPhone Distribution: BinaryLife Inc</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>EnvironmentVariables</key>
		<dict>
			<key>CFFIXED_USER_HOME</key>
			<string>/private/var/mobile/Containers/Data/Application/9FCC6531-96FF-42E9-A7BD-8D2478B8BE08</string>
			<key>TMPDIR</key>
			<string>/private/var/mobile/Containers/Data/Application/9FCC6531-96FF-42E9-A7BD-8D2478B8BE08/tmp</string>
			<key>HOME</key>
			<string>/private/var/mobile/Containers/Data/Application/9FCC6531-96FF-42E9-A7BD-8D2478B8BE08</string>
		</dict>
		<key>CFBundleDevelopmentRegion</key>
		<string>en</string>
		<key>CFBundleNumericVersion</key>
		<integer>16809984</integer>
		<key>CFBundleVersion</key>
		<string>1</string>
		<key>IsDemotedApp</key>
		<false/>
		<key>MinimumOSVersion</key>
		<string>10.3</string>
		<key>SequenceNumber</key>
		<integer>4256</integer>
		<key>IsUpgradeable</key>
		<true/>
		<key>DTXcodeBuild</key>
		<string>8E3004b</string>
		<key>DTPlatformBuild</key>
		<string>14E8301</string>
		<key>UILaunchStoryboardName</key>
		<string>Main</string>
		<key>UIDeviceFamily</key>
		<array>
			<integer>1</integer>
		</array>
		<key>CFBundleIdentifier</key>
		<string>com.browserstack.enterpriseDummy</string>
		<key>DTXcode</key>
		<string>0833</string>
		<key>CFBundleExecutable</key>
		<string>enterpriseDummy</string>
		<key>ProfileValidated</key>
		<true/>
		<key>********************</key>
		<string>Main</string>
		<key>Path</key>
		<string>/private/var/containers/Bundle/Application/6EC5E62F-4AF6-49C3-A69F-E530E8546A1F/enterpriseDummy.app</string>
		<key>DTPlatformName</key>
		<string>iphoneos</string>
		<key>DTCompiler</key>
		<string>com.apple.compilers.llvm.clang.1_0</string>
	</dict>
	<dict>
		<key>UIRequiredDeviceCapabilities</key>
		<array>
			<string>armv7</string>
		</array>
		<key>DTCompiler</key>
		<string>com.apple.compilers.llvm.clang.1_0</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>Entitlements</key>
		<dict>
			<key>com.apple.developer.associated-domains</key>
			<array>
				<string>applinks:beta.itunes.apple.com</string>
				<string>activitycontinuation:beta.itunes.apple.com</string>
				<string>applinks:testflight.apple.com</string>
				<string>activitycontinuation:testflight.apple.com</string>
			</array>
			<key>com.apple.security.exception.mach-lookup.global-name</key>
			<array>
				<string>com.apple.appconduitd.device-connection</string>
				<string>com.apple.companionappd</string>
				<string>com.apple.misagent</string>
				<string>com.apple.lsd.xpc</string>
				<string>com.apple.lsd.install</string>
			</array>
			<key>com.apple.appstored.install-apps</key>
			<true/>
			<key>com.apple.private.tcc.allow</key>
			<array>
				<string>kTCCServiceAddressBook</string>
			</array>
			<key>com.apple.itunesstored.kbsync</key>
			<true/>
			<key>com.apple.companionappd.connect.allow</key>
			<true/>
			<key>com.apple.private.MobileGestalt.AllowedProtectedKeys</key>
			<array>
				<string>UniqueDeviceID</string>
				<string>BuildVersion</string>
			</array>
			<key>com.apple.private.canModifyAppLinkPermissions</key>
			<true/>
			<key>com.apple.private.contactsui</key>
			<true/>
			<key>application-identifier</key>
			<string>243LU875E5.com.apple.TestFlight</string>
			<key>com.apple.private.coreservices.canmaplsdatabase</key>
			<true/>
			<key>com.apple.multitasking.systemappassertions</key>
			<true/>
			<key>com.apple.private.canGetAppLinkInfo</key>
			<true/>
			<key>com.apple.private.mis.beta-install</key>
			<true/>
			<key>com.apple.security.exception.files.absolute-path.read-write</key>
			<array>
				<string>/private/var/mobile/Media/iTunes_Control/iTunes/</string>
				<string>/private/var/mobile/Library/Cookies/</string>
			</array>
			<key>com.apple.accounts.appleaccount.fullaccess</key>
			<true/>
			<key>com.apple.Contacts.database-allow</key>
			<true/>
			<key>com.apple.nano.nanoregistry</key>
			<true/>
			<key>com.apple.private.mobileinstall.allowedSPI</key>
			<array>
				<string>UpdateSinfForLaunchServices</string>
				<string>InstallForLaunchServices</string>
				<string>UninstallForLaunchServices</string>
			</array>
			<key>fairplay-client</key>
			<string>*********</string>
			<key>com.apple.companionappd.profiles.allow</key>
			<true/>
			<key>com.apple.springboard.launchapplications</key>
			<true/>
			<key>com.apple.itunesstored.private</key>
			<true/>
			<key>previous-application-identifiers</key>
			<array>
				<string>VT6C486PNU.com.apple.TestFlight</string>
			</array>
			<key>aps-environment</key>
			<string>production</string>
			<key>com.apple.developer.team-identifier</key>
			<string>243LU875E5</string>
			<key>com.apple.authkit.client.private</key>
			<true/>
			<key>com.apple.springboard.opensensitiveurl</key>
			<true/>
		</dict>
		<key>DTPlatformVersion</key>
		<string>12.1</string>
		<key>CFBundleName</key>
		<string>TestFlight</string>
		<key>DTSDKName</key>
		<string>iphoneos12.1.internal</string>
		<key>DTAppStoreToolsBuild</key>
		<string>10E121a</string>
		<key>ApplicationType</key>
		<string>User</string>
		<key>CFBundleIcons</key>
		<dict>
			<key>CFBundlePrimaryIcon</key>
			<dict>
				<key>CFBundleIconFiles</key>
				<array>
					<string>AppIcon20x20</string>
					<string>AppIcon29x29</string>
					<string>AppIcon40x40</string>
					<string>AppIcon60x60</string>
					<string>AppIcon76x76</string>
					<string>AppIcon83.5x83.5</string>
				</array>
				<key>CFBundleIconName</key>
				<string>AppIcon</string>
			</dict>
		</dict>
		<key>Container</key>
		<string>/private/var/mobile/Containers/Data/Application/7F600F17-2C80-4F26-B53F-9E2BC91CC9D9</string>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>CFBundleDisplayName</key>
		<string>TestFlight</string>
		<key>DTSDKBuild</key>
		<string>16B91</string>
		<key>HasSettingsBundle</key>
		<true/>
		<key>CFBundleShortVersionString</key>
		<string>2.2.1</string>
		<key>CFBundleSupportedPlatforms</key>
		<array>
			<string>iPhoneOS</string>
		</array>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>BuildMachineOSBuild</key>
		<string>17A405001</string>
		<key>SignerIdentity</key>
		<string>Apple iPhone OS Application Signing</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>EnvironmentVariables</key>
		<dict>
			<key>CFFIXED_USER_HOME</key>
			<string>/private/var/mobile/Containers/Data/Application/7F600F17-2C80-4F26-B53F-9E2BC91CC9D9</string>
			<key>TMPDIR</key>
			<string>/private/var/mobile/Containers/Data/Application/7F600F17-2C80-4F26-B53F-9E2BC91CC9D9/tmp</string>
			<key>HOME</key>
			<string>/private/var/mobile/Containers/Data/Application/7F600F17-2C80-4F26-B53F-9E2BC91CC9D9</string>
		</dict>
		<key>MinimumOSVersion</key>
		<string>9.0</string>
		<key>IsDemotedApp</key>
		<false/>
		<key>CFBundleVersion</key>
		<string>6</string>
		<key>DTPlatformBuild</key>
		<string>16B91</string>
		<key>CFBundleDevelopmentRegion</key>
		<string>en</string>
		<key>SequenceNumber</key>
		<integer>4268</integer>
		<key>ApplicationDSID</key>
		<integer>12020680668</integer>
		<key>IsUpgradeable</key>
		<true/>
		<key>DTXcodeBuild</key>
		<string>10B61</string>
		<key>CFBundleNumericVersion</key>
		<integer>100696064</integer>
		<key>UIDeviceFamily</key>
		<array>
			<integer>1</integer>
			<integer>2</integer>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>CFBundleIdentifier</key>
		<string>com.apple.TestFlight</string>
		<key>DTXcode</key>
		<string>1010</string>
		<key>UIApplicationShortcutItems</key>
		<array>
			<dict>
				<key>UIApplicationShortcutItemTitle</key>
				<string>Redeem Code</string>
				<key>UIApplicationShortcutItemType</key>
				<string>com.apple.TestFlight.shortcut-item.redeem-code</string>
				<key>UIApplicationShortcutItemIconFile</key>
				<string>icon_redeem-code</string>
			</dict>
			<dict>
				<key>UIApplicationShortcutItemTitle</key>
				<string>Update All</string>
				<key>UIApplicationShortcutItemType</key>
				<string>com.apple.TestFlight.shortcut-item.update-all</string>
				<key>UIApplicationShortcutItemIconFile</key>
				<string>icon_update-all</string>
			</dict>
		</array>
		<key>CFBundleExecutable</key>
		<string>TestFlight</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>UISupportedDevices</key>
		<array>
			<string>iPad11,1</string>
			<string>iPad11,2</string>
			<string>iPad11,3</string>
			<string>iPad11,4</string>
			<string>iPad4,1</string>
			<string>iPad4,2</string>
			<string>iPad4,3</string>
			<string>iPad4,4</string>
			<string>iPad4,5</string>
			<string>iPad4,6</string>
			<string>iPad4,7</string>
			<string>iPad4,8</string>
			<string>iPad4,9</string>
			<string>iPad5,1</string>
			<string>iPad5,2</string>
			<string>iPad5,3</string>
			<string>iPad5,4</string>
			<string>iPad6,11</string>
			<string>iPad6,12</string>
			<string>iPad6,3</string>
			<string>iPad6,4</string>
			<string>iPad6,7</string>
			<string>iPad6,8</string>
			<string>iPad7,1</string>
			<string>iPad7,2</string>
			<string>iPad7,3</string>
			<string>iPad7,4</string>
			<string>iPad7,5</string>
			<string>iPad7,6</string>
			<string>iPad8,1</string>
			<string>iPad8,2</string>
			<string>iPad8,3</string>
			<string>iPad8,4</string>
			<string>iPad8,5</string>
			<string>iPad8,6</string>
			<string>iPad8,7</string>
			<string>iPad8,8</string>
		</array>
		<key>Path</key>
		<string>/private/var/containers/Bundle/Application/B037CC57-6D33-4510-BDE3-D186BE3728D7/TestFlight.app</string>
		<key>DTPlatformName</key>
		<string>iphoneos</string>
		<key>ITSAppUsesNonExemptEncryption</key>
		<false/>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleURLName</key>
				<string>com.apple.TestFlight.scheme</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>itms-beta</string>
				</array>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
			</dict>
		</array>
	</dict>
	<dict>
		<key>UIRequiredDeviceCapabilities</key>
		<array>
			<string>armv7</string>
		</array>
		<key>UIRequiresFullScreen</key>
		<true/>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>Entitlements</key>
		<dict>
			<key>keychain-access-groups</key>
			<array>
				<string>8XPT93F9Q7.*</string>
			</array>
			<key>application-identifier</key>
			<string>8XPT93F9Q7.*</string>
			<key>get-task-allow</key>
			<true/>
			<key>com.apple.developer.team-identifier</key>
			<string>8XPT93F9Q7</string>
		</dict>
		<key>DTPlatformVersion</key>
		<string>12.0</string>
		<key>CFBundleName</key>
		<string>WebDriverAgentRunner-Runner</string>
		<key>DTSDKName</key>
		<string>iphoneos12.0.internal</string>
		<key>ApplicationType</key>
		<string>User</string>
		<key>Container</key>
		<string>/private/var/mobile/Containers/Data/Application/EF7302F8-7854-4DBC-AB11-4C5717D73886</string>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>CFBundleDisplayName</key>
		<string>WebDriverAgentRunner-Runner</string>
		<key>UIBackgroundModes</key>
		<array>
			<string>unboundedTaskCompletion</string>
			<string>continuous</string>
		</array>
		<key>DTSDKBuild</key>
		<string>16A342</string>
		<key>CFBundleShortVersionString</key>
		<string>1.0</string>
		<key>CFBundleAllowMixedLocalizations</key>
		<true/>
		<key>CFBundleSupportedPlatforms</key>
		<array>
			<string>iPhoneOS</string>
		</array>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>BuildMachineOSBuild</key>
		<string>18A348</string>
		<key>SignerIdentity</key>
		<string>iPhone Developer: Browserstack Mobile (236WWMU8WM)</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>EnvironmentVariables</key>
		<dict>
			<key>CFFIXED_USER_HOME</key>
			<string>/private/var/mobile/Containers/Data/Application/EF7302F8-7854-4DBC-AB11-4C5717D73886</string>
			<key>TMPDIR</key>
			<string>/private/var/mobile/Containers/Data/Application/EF7302F8-7854-4DBC-AB11-4C5717D73886/tmp</string>
			<key>HOME</key>
			<string>/private/var/mobile/Containers/Data/Application/EF7302F8-7854-4DBC-AB11-4C5717D73886</string>
		</dict>
		<key>CFBundleDevelopmentRegion</key>
		<string>en</string>
		<key>CFBundleNumericVersion</key>
		<integer>16809984</integer>
		<key>CFBundleVersion</key>
		<string>1</string>
		<key>IsDemotedApp</key>
		<false/>
		<key>MinimumOSVersion</key>
		<string>8.0</string>
		<key>SequenceNumber</key>
		<integer>4276</integer>
		<key>IsUpgradeable</key>
		<true/>
		<key>DTXcodeBuild</key>
		<string>10A242</string>
		<key>DTPlatformBuild</key>
		<string>16A342</string>
		<key>UIDeviceFamily</key>
		<array>
			<integer>1</integer>
			<integer>2</integer>
		</array>
		<key>CFBundleIdentifier</key>
		<string>com.apple.test.WebDriverAgentRunner-Runner</string>
		<key>DTXcode</key>
		<string>1000</string>
		<key>CFBundleExecutable</key>
		<string>WebDriverAgentRunner-Runner</string>
		<key>ProfileValidated</key>
		<true/>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>Path</key>
		<string>/private/var/containers/Bundle/Application/155C0733-5093-4FA5-B98C-1FCC83B4483D/WebDriverAgentRunner-Runner.app</string>
		<key>DTPlatformName</key>
		<string>iphoneos</string>
		<key>DTCompiler</key>
		<string>com.apple.compilers.llvm.clang.1_0</string>
	</dict>
	<dict>
		<key>UIRequiredDeviceCapabilities</key>
		<array>
			<string>arm64</string>
		</array>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>Entitlements</key>
		<dict>
			<key>keychain-access-groups</key>
			<array>
				<string>8XPT93F9Q7.*</string>
			</array>
			<key>application-identifier</key>
			<string>8XPT93F9Q7.*</string>
			<key>get-task-allow</key>
			<true/>
			<key>com.apple.developer.team-identifier</key>
			<string>8XPT93F9Q7</string>
		</dict>
		<key>DTPlatformVersion</key>
		<string>12.0</string>
		<key>CFBundleName</key>
		<string>BrowserStack</string>
		<key>DTSDKName</key>
		<string>iphoneos12.0</string>
		<key>ApplicationType</key>
		<string>User</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>BrowserStack App needs permission to add photos to your library.</string>
		<key>Container</key>
		<string>/private/var/mobile/Containers/Data/Application/001DBDF9-693D-4656-B2C4-503786A66CC7</string>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>CFBundleDisplayName</key>
		<string>BrowserStack</string>
		<key>DTSDKBuild</key>
		<string>16A366</string>
		<key>CFBundleShortVersionString</key>
		<string>1.0</string>
		<key>CFBundleSupportedPlatforms</key>
		<array>
			<string>iPhoneOS</string>
		</array>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>BuildMachineOSBuild</key>
		<string>18A391</string>
		<key>SignerIdentity</key>
		<string>iPhone Developer: Browserstack Mobile (236WWMU8WM)</string>
		<key>UIFileSharingEnabled</key>
		<true/>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleDevelopmentRegion</key>
		<string>en</string>
		<key>CFBundleNumericVersion</key>
		<integer>16809984</integer>
		<key>CFBundleVersion</key>
		<string>1</string>
		<key>IsDemotedApp</key>
		<false/>
		<key>EnvironmentVariables</key>
		<dict>
			<key>CFFIXED_USER_HOME</key>
			<string>/private/var/mobile/Containers/Data/Application/001DBDF9-693D-4656-B2C4-503786A66CC7</string>
			<key>TMPDIR</key>
			<string>/private/var/mobile/Containers/Data/Application/001DBDF9-693D-4656-B2C4-503786A66CC7/tmp</string>
			<key>HOME</key>
			<string>/private/var/mobile/Containers/Data/Application/001DBDF9-693D-4656-B2C4-503786A66CC7</string>
		</dict>
		<key>SequenceNumber</key>
		<integer>2916</integer>
		<key>MinimumOSVersion</key>
		<string>10.0</string>
		<key>IsUpgradeable</key>
		<true/>
		<key>NSPhotoLibraryAddUsageDescription</key>
		<string>BrowserStack App needs permission to add photos to your library.</string>
		<key>DTXcodeBuild</key>
		<string>10A255</string>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIDeviceFamily</key>
		<array>
			<integer>1</integer>
			<integer>2</integer>
		</array>
		<key>DTPlatformBuild</key>
		<string>16A366</string>
		<key>CFBundleIdentifier</key>
		<string>com.browserstack.app</string>
		<key>DTXcode</key>
		<string>1000</string>
		<key>CFBundleExecutable</key>
		<string>BrowserStack</string>
		<key>ProfileValidated</key>
		<true/>
		<key>********************</key>
		<string>Main</string>
		<key>Path</key>
		<string>/private/var/containers/Bundle/Application/69133F23-D053-48EB-BFEF-30AB66BC26F7/BrowserStack.app</string>
		<key>DTPlatformName</key>
		<string>iphoneos</string>
		<key>DTCompiler</key>
		<string>com.apple.compilers.llvm.clang.1_0</string>
	</dict>
</array>
</plist>
