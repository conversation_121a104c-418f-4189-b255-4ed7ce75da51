<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<array>
	<dict>
		<key>UIRequiredDeviceCapabilities</key>
		<array>
			<string>arm64</string>
		</array>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>Entitlements</key>
		<dict>
			<key>com.apple.security.exception.mach-lookup.global-name</key>
			<array>
				<string>com.apple.accountsd.oopa</string>
			</array>
			<key>com.apple.private.accounts.authdialoghost</key>
			<true/>
			<key>com.apple.private.security.container-required</key>
			<true/>
			<key>DataProtectionClass</key>
			<string>NSFileProtectionComplete</string>
		</dict>
		<key>DTPlatformVersion</key>
		<string>11.0</string>
		<key>CFBundleName</key>
		<string>Fooer</string>
		<key>DTSDKName</key>
		<string>iphoneos11.0.internal</string>
		<key>ApplicationType</key>
		<string>User</string>
		<key>Container</key>
		<string>/private/var/mobile/Containers/Data/Application/02D879A8-4B35-400A-9C84-0702AD69724C</string>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>CFBundleDisplayName</key>
		<string>Fooer</string>
		<key>UIBackgroundModes</key>
		<array>
			<string>continuous</string>
		</array>
		<key>DTSDKBuild</key>
		<string>15A341</string>
		<key>CFBundleShortVersionString</key>
		<string>1.0</string>
		<key>CFBundleSupportedPlatforms</key>
		<array>
			<string>iPhoneOS</string>
		</array>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>BuildMachineOSBuild</key>
		<string>16B2657</string>
		<key>_UILaunchAlwaysFullScreen</key>
		<true/>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>EnvironmentVariables</key>
		<dict>
			<key>CFFIXED_USER_HOME</key>
			<string>/private/var/mobile/Containers/Data/Application/02D879A8-4B35-400A-9C84-0702AD69724C</string>
			<key>TMPDIR</key>
			<string>/private/var/mobile/Containers/Data/Application/02D879A8-4B35-400A-9C84-0702AD69724C/tmp</string>
			<key>HOME</key>
			<string>/private/var/mobile/Containers/Data/Application/02D879A8-4B35-400A-9C84-0702AD69724C</string>
		</dict>
		<key>CFBundleDevelopmentRegion</key>
		<string>en</string>
		<key>CFBundleNumericVersion</key>
		<integer>16809984</integer>
		<key>CFBundleVersion</key>
		<string>1.0</string>
		<key>IsDemotedApp</key>
		<false/>
		<key>MinimumOSVersion</key>
		<string>11.0</string>
		<key>SequenceNumber</key>
		<integer>36</integer>
		<key>IsUpgradeable</key>
		<false/>
		<key>DTXcodeBuild</key>
		<string>9M189u</string>
		<key>DTPlatformBuild</key>
		<string></string>
		<key>UIDeviceFamily</key>
		<array>
			<integer>1</integer>
		</array>
		<key>CFBundleIdentifier</key>
		<string>com.apple.foo</string>
		<key>DTXcode</key>
		<string>0900</string>
		<key>SBAppTags</key>
		<array>
			<string>hidden</string>
		</array>
		<key>CFBundleExecutable</key>
		<string>Fooer</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>Path</key>
		<string>/Applications/Fooer.app</string>
		<key>DTPlatformName</key>
		<string>iphoneos</string>
		<key>DTCompiler</key>
		<string>com.apple.compilers.llvm.clang.1_0</string>
	</dict>
	<dict>
		<key>UIRequiredDeviceCapabilities</key>
		<array>
			<string>arm64</string>
		</array>
		<key>DTCompiler</key>
		<string>com.apple.compilers.llvm.clang.1_0</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>Entitlements</key>
		<dict>
			<key>keychain-access-groups</key>
			<array>
				<string>apple</string>
				<string>com.apple.VideoSubscriberAccount</string>
			</array>
			<key>com.apple.locationd.effective_bundle</key>
			<true/>
			<key>com.apple.appstored.private</key>
			<true/>
			<key>com.apple.appstored.install-apps</key>
			<true/>
			<key>com.apple.symptoms.NetworkOfInterest</key>
			<true/>
			<key>com.apple.springboard.opensensitiveurl</key>
			<true/>
			<key>com.apple.appstored.xpc.updates</key>
			<true/>
			<key>com.apple.private.accounts.customaccesssinfo</key>
			<true/>
			<key>com.apple.telephony.cupolicy-rw-access</key>
			<true/>
			<key>adi-client</key>
			<string>*********</string>
			<key>com.apple.private.tcc.allow</key>
			<array>
				<string>kTCCServiceCamera</string>
				<string>kTCCServiceAddressBook</string>
				<string>kTCCServiceMediaLibrary</string>
			</array>
			<key>com.apple.security.exception.shared-preference.read-write</key>
			<array>
				<string>com.apple.AdLib</string>
				<string>com.apple.AppStore</string>
				<string>com.apple.Fitness</string>
				<string>com.apple.itunesstored</string>
			</array>
			<key>com.apple.accounts.appleidauthentication.defaultaccess</key>
			<true/>
			<key>com.apple.private.coreservices.canmaplsdatabase</key>
			<true/>
			<key>com.apple.authkit.client.internal</key>
			<true/>
			<key>dynamic-codesigning</key>
			<true/>
			<key>com.apple.itunesstored.metrics</key>
			<true/>
			<key>com.apple.security.exception.files.absolute-path.read-write</key>
			<array>
				<string>/private/var/mobile/Library/Application Support/com.apple.VideoSubscriberAccount</string>
				<string>/private/var/mobile/Library/Application Support/com.apple.VideoSubscriberAccount/</string>
			</array>
			<key>com.apple.private.ap.idmanager</key>
			<true/>
			<key>com.apple.security.system-group-containers</key>
			<array>
				<string>systemgroup.com.apple.VideoSubscriberAccount</string>
			</array>
			<key>com.apple.accounts.appleaccount.fullaccess</key>
			<true/>
			<key>com.apple.Contacts.database-allow</key>
			<true/>
			<key>com.apple.launchservices.receivereferrerrurl</key>
			<true/>
			<key>com.apple.security.exception.files.home-relative-path.read-write</key>
			<array>
				<string>/Library/Caches/com.apple.storeservices/</string>
				<string>/Library/com.apple.itunesstored/</string>
			</array>
			<key>com.apple.appstored.update-apps</key>
			<true/>
			<key>com.apple.itunesstored.private</key>
			<true/>
			<key>fairplay-client</key>
			<string>**********</string>
			<key>com.apple.symptom_analytics.query</key>
			<true/>
			<key>com.apple.CoreTelephony.DataUsageInfo.allow</key>
			<true/>
			<key>com.apple.frontboard.launchapplications</key>
			<true/>
			<key>com.apple.security.exception.mach-lookup.global-name</key>
			<array>
				<string>com.apple.askpermissiond</string>
				<string>com.apple.appstored.xpc.jobmanager</string>
				<string>com.apple.appstored.xpc.request</string>
				<string>com.apple.lsd.xpc</string>
				<string>com.apple.appstored.xpc.updates</string>
				<string>com.apple.ap.adtrackingd.idmanager</string>
				<string>com.apple.symptom_analytics</string>
				<string>com.apple.familycircle.agent</string>
			</array>
		</dict>
		<key>DTPlatformVersion</key>
		<string>11.0</string>
		<key>CFBundleName</key>
		<string>App Store</string>
		<key>DTSDKName</key>
		<string>iphoneos11.0.internal</string>
		<key>ApplicationType</key>
		<string>System</string>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<true/>
		<key>CFBundleIcons</key>
		<dict>
			<key>CFBundlePrimaryIcon</key>
			<dict>
				<key>CFBundleIconFiles</key>
				<array>
					<string>AppIcon60x60</string>
					<string>AppIcon76x76</string>
				</array>
				<key>CFBundleIconName</key>
				<string>AppIcon</string>
			</dict>
		</dict>
		<key>Container</key>
		<string>/private/var/mobile/Containers/Data/Application/E41ADD9F-6E42-49F8-9B91-E8D76DD75842</string>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>CFBundleDisplayName</key>
		<string>App Store</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>This information will be used to suggest apps that are more relevant to you.</string>
		<key>DTSDKBuild</key>
		<string>15A341</string>
		<key>CFBundleShortVersionString</key>
		<string>3.0</string>
		<key>CFBundleSupportedPlatforms</key>
		<array>
			<string>iPhoneOS</string>
		</array>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>BuildMachineOSBuild</key>
		<string>16B2657</string>
		<key>DTPlatformBuild</key>
		<string></string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>DTXcodeBuild</key>
		<string>9M189u</string>
		<key>CFBundleDevelopmentRegion</key>
		<string>en</string>
		<key>IsDemotedApp</key>
		<false/>
		<key>CFBundleVersion</key>
		<string>1</string>
		<key>EnvironmentVariables</key>
		<dict>
			<key>CFFIXED_USER_HOME</key>
			<string>/private/var/mobile/Containers/Data/Application/E41ADD9F-6E42-49F8-9B91-E8D76DD75842</string>
			<key>TMPDIR</key>
			<string>/private/var/mobile/Containers/Data/Application/E41ADD9F-6E42-49F8-9B91-E8D76DD75842/tmp</string>
			<key>HOME</key>
			<string>/private/var/mobile/Containers/Data/Application/E41ADD9F-6E42-49F8-9B91-E8D76DD75842</string>
		</dict>
		<key>MinimumOSVersion</key>
		<string>11.0</string>
		<key>SequenceNumber</key>
		<integer>18461</integer>
		<key>IsUpgradeable</key>
		<false/>
		<key>CFBundleNumericVersion</key>
		<integer>16809984</integer>
		<key>UIDeviceFamily</key>
		<array>
			<integer>1</integer>
			<integer>2</integer>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIOptOutOfRTL</key>
		<true/>
		<key>CFBundleIdentifier</key>
		<string>com.apple.AppStore</string>
		<key>DTXcode</key>
		<string>0900</string>
		<key>CFBundleExecutable</key>
		<string>AppStore</string>
		<key>Path</key>
		<string>/Applications/AppStore.app</string>
		<key>DTPlatformName</key>
		<string>iphoneos</string>
		<key>SBDomainsToPreheat</key>
		<array>
			<string>bag.itunes.apple.com</string>
			<string>silverbullet-external-ats.itunes.apple.com</string>
			<string>apps.itunes-nocookie.com</string>
			<string>itunes.apple.com</string>
		</array>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Viewer</string>
				<key>CFBundleURLName</key>
				<string>App Store URL</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>itms-apps</string>
					<string>itms-appss</string>
				</array>
			</dict>
			<dict>
				<key>CFBundleURLName</key>
				<string>Store Services URL</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>itms-services</string>
				</array>
			</dict>
		</array>
	</dict>
</array>
</plist>
