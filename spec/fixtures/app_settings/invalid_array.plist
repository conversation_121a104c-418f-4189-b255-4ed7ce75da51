<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<array>
    <dict>
        <key>PreferenceSpecifiers</key>
        <array>
            <dict>
            <key>Type</key>
            <string>PSSliderSpecifier</string>
            <key>Title</key>
            <string>child slider</string>
            <key>Key</key>
            <string>my_child_preference</string>
            <key>DefaultValue</key>
            <integer>15</integer>
            <key>MinimumValue</key>
            <integer>1</integer>
            <key>MaximumValue</key>
            <integer>60</integer>
            </dict>
            <dict>
                    <key>Type</key>
                    <string>PSToggleSwitchSpecifier</string>
                    <key>Title</key>
                    <string>Child Banner</string>
                    <key>Key</key>
                    <string>ChildOverrideMarketingBanner</string>
                    <key>DefaultValue</key>
                    <false/>
                </dict>
            <dict>
                       <key>Type</key>
                       <string>PSTextFieldSpecifier</string>
                       <key>Title</key>
                       <string>Server config</string>
                       <key>Key</key>
                       <string>serverConfig</string>
                       <key>DefaultValue</key>
                       <string>N/A</string>
                       <key>IsSecure</key>
                       <false/>
                       <key>KeyboardType</key>
                       <string>Alphabet</string>
                       <key>AutocapitalizationType</key>
                       <string>None</string>
                       <key>AutocorrectionType</key>
                       <string>No</string>
                   </dict>
            <dict>
                <key>DisplaySortedByTitle</key>
                <false/>
                <key>Type</key>
                <string>PSMultiValueSpecifier</string>
                <key>Title</key>
                <string>Explore Environment</string>
                <key>Key</key>
                <string>ChildExploreEnvironmentSelector</string>
                <key>DefaultValue</key>
                <string>MOD</string>
                <key>Titles</key>
                <array>
                    <string>DEV</string>
                    <string>QA</string>
                    <string>MOD</string>
                    <string>PROD</string>
                </array>
                <key>Values</key>
                <array>
                    <string>https://dev.explore-products-rates.ally.com</string>
                    <string>https://qa.explore-products-rates.ally.com</string>
                    <string>https://mod.explore-products-rates.ally.com</string>
                    <string>https://explore-products-rates.ally.com</string>
                </array>
            </dict>

            <dict>
                <key>New item</key>
                <string></string>
            </dict>
            <dict>
                <key>Type</key>
                <string>PSGroupSpecifier</string>
                <key>Title</key>
                <string>USE AT YOUR OWN RISK</string>
            </dict>
            <dict>
                <key>Type</key>
                <string>PSGroupSpecifier</string>
                <key>Title</key>
                <string>NO TECHNICAL SUPPORT IS PROVIDED FOR THIS SOFTWARE</string>
            </dict>
            <dict>
                <key>Type</key>
                <string>PSGroupSpecifier</string>
                <key>Title</key>
                <string>THIS SOFTWARE IS PROVIDED &quot;AS IS&quot; AND ANY EXPRESSED OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NAME, OR HER HEIRS, OR ASSIGNS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</string>
            </dict>
        </array>
        <key>StringsTable</key>
        <string>Child</string>
    </dict>
</array>
</plist>
