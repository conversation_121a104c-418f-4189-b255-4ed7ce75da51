{"Root": {"PreferenceSpecifiers": [{"DefaultValue": "N/A", "KeyboardType": "Alphabet", "Key": "serverConfig", "AutocorrectionType": "No", "Type": "PSTextFieldSpecifier", "IsSecure": false, "AutocapitalizationType": "None"}, {"DefaultValue": 15, "Key": "my_preference", "MinimumValue": 1, "Type": "PSSliderSpecifier", "Title": "slider", "MaximumValue": 60}, {"File": "child", "Type": "PSChildPaneSpecifier", "Title": "Child"}, {"DefaultValue": false, "Key": "ShowLoginBanner", "Type": "PSToggleSwitchSpecifier", "Title": "Show Login Banner"}, {"DefaultValue": false, "Key": "DuplicateShowLoginBanner", "Type": "PSToggleSwitchSpecifier", "Title": "Show Login Banner"}, {"DefaultValue": "N/A", "KeyboardType": "Alphabet", "Key": "serverConfig", "AutocorrectionType": "No", "Type": "PSTextFieldSpecifier", "Title": "Server config", "IsSecure": false, "AutocapitalizationType": "None"}, {"DefaultValue": "1.0.0 (1)", "Key": "CurrentAppVersion", "Type": "PSTitleValueSpecifier", "Title": "Current app version"}, {"DefaultValue": "", "Key": "BuildDate", "Type": "PSTitleValueSpecifier", "Title": "Build date"}, {"Type": "PSGroupSpecifier", "Title": "Developer Settings"}, {"Titles": ["DEV_CI", "CIT", "CIT_INT", "CIT_1", "CIT_1_INT", "CIT_2", "CIT_2_INT", "CIT_3", "CIT_3_INT", "CAP", "CAP_CI", "QA_1", "QA_1_CI", "QA_2", "QA_2_CI", "QA_3", "QA_3_CI", "QA_Fix", "QA_Fix_CI", "PROD", "PRODA_INT", "PRODB_INT", "PRODC_INT", "PRODD_INT", "PRODY_INT", "PRODZ_INT", "PRODA_Pilot", "PRODB_Pilot", "PRODC_Pilot", "PRODD_Pilot", "PRODY_Pilot", "PRODZ_Pilot", "PSP", "PSPA_INT", "PSPB_INT", "PSPC_INT", "PSPD_INT", "PSPY_INT", "PSPZ_INT", "PSPA_Pilot", "PSPB_Pilot", "PSPC_Pilot", "PSPD_Pilot", "PSPY_Pilot", "PSPZ_Pilot"], "DefaultValue": "CIT_2", "Values": ["DEV_CI", "CIT", "CIT_INT", "CIT_1", "CIT_1_INT", "CIT_2", "CIT_2_INT", "CIT_3", "CIT_3_INT", "CAP", "CAP_CI", "QA_1", "QA_1_CI", "QA_2", "QA_2_CI", "QA_3", "QA_3_CI", "QA_Fix", "QA_Fix_CI", "PROD", "PRODA_INT", "PRODB_INT", "PRODC_INT", "PRODD_INT", "PRODY_INT", "PRODZ_INT", "PRODA_Pilot", "PRODB_Pilot", "PRODC_Pilot", "PRODD_Pilot", "PRODY_Pilot", "PRODZ_Pilot", "PSP", "PSPA_INT", "PSPB_INT", "PSPC_INT", "PSPD_INT", "PSPY_INT", "PSPZ_INT", "PSPA_Pilot", "PSPB_Pilot", "PSPC_Pilot", "PSPD_Pilot", "PSPY_Pilot", "PSPZ_Pilot"], "Key": "EnvironmentSelector", "Type": "PSMultiValueSpecifier", "Title": "Environment", "DisplaySortedByTitle": false}, {"Titles": ["QA", "DEV"], "DefaultValue": "tapi-qa.tradeking.com", "Values": ["tapi-qa.tradeking.com", "tapi-dev.invest.ally.com"], "Key": "TAPITestEnvironmentSelector", "Type": "PSMultiValueSpecifier", "Title": "TAPI Test Environment", "DisplaySortedByTitle": false}, {"Titles": ["DEV", "QA", "MOD", "PROD"], "DefaultValue": "MOD", "Values": ["https://dev.explore-products-rates.ally.com", "https://qa.explore-products-rates.ally.com", "https://mod.explore-products-rates.ally.com", "https://explore-products-rates.ally.com"], "Key": "ExploreEnvironmentSelector", "Type": "PSMultiValueSpecifier", "Title": "Explore Environment", "DisplaySortedByTitle": false}, {"Values": ["On", "Off", "Adobe-controlled"], "DefaultValue": "Adobe-controlled", "Titles": ["On", "Off", "Adobe-controlled"], "Key": "allyMessengerSelector", "Type": "PSMultiValueSpecifier", "Title": "<PERSON>"}, {"DefaultValue": false, "Key": "PerformanceChartingMockData", "Type": "PSToggleSwitchSpecifier", "Title": "Performance Charting <PERSON>ck <PERSON>"}, {"DefaultValue": false, "Key": "SavingsToolkitMockData", "Type": "PSToggleSwitchSpecifier", "Title": "Savings Toolkit Mock Data"}, {"DefaultValue": false, "Key": "ShowCheckOrders", "Type": "PSToggleSwitchSpecifier", "Title": "Show Check Orders Status"}, {"DefaultValue": false, "Key": "CheckOrdersStatusMockData", "Type": "PSToggleSwitchSpecifier", "Title": "Check Orders Status Mock Data"}, {"DefaultValue": false, "Key": "ShowCheckOrders", "Type": "PSToggleSwitchSpecifier", "Title": "Show Check Orders Status"}, {"DefaultValue": false, "Key": "CheckOrdersStatusMockData", "Type": "PSToggleSwitchSpecifier", "Title": "Check Orders Status Mock Data"}, {"DefaultValue": true, "Key": "OTPAutoFill", "Type": "PSToggleSwitchSpecifier", "Title": "OTP Auto-Fill"}, {"DefaultValue": false, "Key": "ShowLoginBanner", "Type": "PSToggleSwitchSpecifier", "Title": "Show Login Banner"}, {"DefaultValue": false, "Key": "OverrideMarketingBanner", "Type": "PSToggleSwitchSpecifier", "Title": "Override Marketing Banner"}, {"Titles": ["None", "Transfers", "Card Controls", "Deposits", "Pay Bills", "<PERSON><PERSON>", "Manage CDs", "Manage CD Interest Disbursement", "Manage CD Edit Maturity Instructions", "Explore", "Find ATMs", "Statements", "Tax Forms", "Secure Messages", "Profile", "Paperless Settings", "<PERSON><PERSON>", "<PERSON> In<PERSON>t Settings", "<PERSON><PERSON><PERSON>", "Custom Alerts", "Automatic Alerts", "<PERSON>", "<PERSON>", "Activity (History)", "Activity (Scheduled)", "Order Checks", "Order Deposit Slips", "Notices", "Overdraft Transfer Service", "Edit Contact Information", "Edit Home Address", "Edit Mailing Address", "Change Password", "OTP Delivery Settings", "<PERSON>", "Forms", "Bank Messages", "Order Debit Card", "Report Lost/Stolen Debit Card", "Mortgage Lead Form", "insights"], "DefaultValue": "", "Values": ["", "transfers", "cardControls", "deposits", "payBills", "zelle", "manageCD", "manageCDInterestDisbursement", "manageCDEditMaturityInstructions", "explore", "findAtm", "statements", "taxForms", "secureMessages", "profile", "paperlessSettings", "loginSettings", "allyInvestSettings", "alerts", "customAlerts", "automaticAlerts", "allyAssist", "giveFeedback", "activityHistory", "activityScheduled", "orderChecks", "orderDepositSlips", "notices", "overdraftTransferService", "editCustomerInformation", "editCustomerHomeAddress", "editCustomerMailingAddress", "changePassword", "otpDeliverySettings", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forms", "bankMessages", "orderDebitCard", "reportLostDebitCard", "mortgageLead", "insights"], "Key": "MarketingBanner", "Type": "PSMultiValueSpecifier", "Title": "Marketing Banner", "DisplaySortedByTitle": false}, {"DefaultValue": true, "Key": "Enable<PERSON><PERSON>vey", "Type": "PSToggleSwitchSpecifier", "Title": "Enable Survey"}, {"DefaultValue": true, "Key": "UseAllyMessengerSandbox", "Type": "PSToggleSwitchSpecifier", "Title": "Use Ally Messenger Sandbox"}, {"DefaultValue": false, "Key": "ShouldResetPersistedData", "Type": "PSToggleSwitchSpecifier", "Title": "<PERSON>ache"}, {"DefaultValue": true, "Key": "DisplayInsightsTab", "Type": "PSToggleSwitchSpecifier", "Title": "Display Insights Tab"}, {"Type": "PSGroupSpecifier", "Title": "Invest Timeout (seconds)"}, {"DefaultValue": "14400", "KeyboardType": "NumberPad", "Key": "INVEST_TIMEOUT", "Type": "PSTextFieldSpecifier"}, {"Type": "PSGroupSpecifier"}, {"DefaultValue": "--", "Key": "AppVersion", "Type": "PSTitleValueSpecifier", "Title": "Version"}], "StringsTable": "Root"}, "child": {"PreferenceSpecifiers": [{"DefaultValue": 15, "Key": "my_child_preference", "MinimumValue": 1, "Type": "PSSliderSpecifier", "Title": "child slider", "MaximumValue": 60}, {"DefaultValue": false, "Key": "ChildOverrideMarketingBanner", "Type": "PSToggleSwitchSpecifier", "Title": "<PERSON> Banner"}, {"DefaultValue": "N/A", "KeyboardType": "Alphabet", "Key": "serverConfig", "AutocorrectionType": "No", "Type": "PSTextFieldSpecifier", "Title": "Server config", "IsSecure": false, "AutocapitalizationType": "None"}, {"Titles": ["DEV", "QA", "MOD", "PROD"], "DefaultValue": "MOD", "Values": ["https://dev.explore-products-rates.ally.com", "https://qa.explore-products-rates.ally.com", "https://mod.explore-products-rates.ally.com", "https://explore-products-rates.ally.com"], "Key": "ChildExploreEnvironmentSelector", "Type": "PSMultiValueSpecifier", "Title": "Explore Environment", "DisplaySortedByTitle": false}, {"New item": ""}, {"Type": "PSGroupSpecifier", "Title": "USE AT YOUR OWN RISK"}, {"Type": "PSGroupSpecifier", "Title": "NO TECHNICAL SUPPORT IS PROVIDED FOR THIS SOFTWARE"}, {"Type": "PSGroupSpecifier", "Title": "THIS SOFTWARE IS PROVIDED \"AS IS\" AND ANY EXPRESSED OR <PERSON><PERSON><PERSON>IED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NAME, OR HER HEIRS, OR <PERSON>SIGNS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE."}], "StringsTable": "Child"}}