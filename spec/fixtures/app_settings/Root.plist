<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>PreferenceSpecifiers</key>
	<array>
        <dict>
            <key>Type</key>
            <string>PSTextFieldSpecifier</string>
            <key>Key</key>
            <string>serverConfig</string>
            <key>DefaultValue</key>
            <string>N/A</string>
            <key>IsSecure</key>
            <false/>
            <key>KeyboardType</key>
            <string>Alphabet</string>
            <key>AutocapitalizationType</key>
            <string>None</string>
            <key>AutocorrectionType</key>
            <string>No</string>
        </dict>
        
        
        
        
        <dict>
        <key>Type</key>
        <string>PSSliderSpecifier</string>
        <key>Title</key>
        <string>slider</string>
        <key>Key</key>
        <string>my_preference</string>
        <key>DefaultValue</key>
        <integer>15</integer>
        <key>MinimumValue</key>
        <integer>1</integer>
        <key>MaximumValue</key>
        <integer>60</integer>
        </dict>
        <dict>
            <key>Type</key>
            <string>PSChildPaneSpecifier</string>
            <key>Title</key>
            <string>Child</string>
            <key>File</key>
            <string>child</string>
        </dict>
        
        <dict>
            <key>Type</key>
            <string>PSToggleSwitchSpecifier</string>
            <key>Title</key>
            <string>Show Login Banner</string>
            <key>Key</key>
            <string>ShowLoginBanner</string>
            <key>DefaultValue</key>
            <false/>
        </dict>
        <dict>
            <key>Type</key>
            <string>PSToggleSwitchSpecifier</string>
            <key>Title</key>
            <string>Show Login Banner</string>
            <key>Key</key>
            <string>DuplicateShowLoginBanner</string>
            <key>DefaultValue</key>
            <false/>
        </dict>
        
        <dict>
            <key>Type</key>
            <string>PSTextFieldSpecifier</string>
            <key>Title</key>
            <string>Server config</string>
            <key>Key</key>
            <string>serverConfig</string>
            <key>DefaultValue</key>
            <string>N/A</string>
            <key>IsSecure</key>
            <false/>
            <key>KeyboardType</key>
            <string>Alphabet</string>
            <key>AutocapitalizationType</key>
            <string>None</string>
            <key>AutocorrectionType</key>
            <string>No</string>
        </dict>
		<dict>
			<key>DefaultValue</key>
			<string>1.0.0 (1)</string>
			<key>Key</key>
			<string>CurrentAppVersion</string>
			<key>Title</key>
			<string>Current app version</string>
			<key>Type</key>
			<string>PSTitleValueSpecifier</string>
		</dict>
        
		<dict>
			<key>Type</key>
			<string>PSTitleValueSpecifier</string>
			<key>Title</key>
			<string>Build date</string>
			<key>Key</key>
			<string>BuildDate</string>
			<key>DefaultValue</key>
			<string></string>
		</dict>
		<dict>
			<key>Title</key>
			<string>Developer Settings</string>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
		</dict>
		<dict>
			<key>DisplaySortedByTitle</key>
			<false/>
			<key>Type</key>
			<string>PSMultiValueSpecifier</string>
			<key>Title</key>
			<string>Environment</string>
			<key>Key</key>
			<string>EnvironmentSelector</string>
			<key>DefaultValue</key>
			<string>CIT_2</string>
			<key>Titles</key>
			<array>
				<string>DEV_CI</string>
				<string>CIT</string>
				<string>CIT_INT</string>
				<string>CIT_1</string>
				<string>CIT_1_INT</string>
				<string>CIT_2</string>
				<string>CIT_2_INT</string>
				<string>CIT_3</string>
				<string>CIT_3_INT</string>
				<string>CAP</string>
				<string>CAP_CI</string>
				<string>QA_1</string>
				<string>QA_1_CI</string>
				<string>QA_2</string>
				<string>QA_2_CI</string>
				<string>QA_3</string>
				<string>QA_3_CI</string>
				<string>QA_Fix</string>
				<string>QA_Fix_CI</string>
				<string>PROD</string>
				<string>PRODA_INT</string>
				<string>PRODB_INT</string>
				<string>PRODC_INT</string>
				<string>PRODD_INT</string>
				<string>PRODY_INT</string>
				<string>PRODZ_INT</string>
				<string>PRODA_Pilot</string>
				<string>PRODB_Pilot</string>
				<string>PRODC_Pilot</string>
				<string>PRODD_Pilot</string>
				<string>PRODY_Pilot</string>
				<string>PRODZ_Pilot</string>
				<string>PSP</string>
				<string>PSPA_INT</string>
				<string>PSPB_INT</string>
				<string>PSPC_INT</string>
				<string>PSPD_INT</string>
				<string>PSPY_INT</string>
				<string>PSPZ_INT</string>
				<string>PSPA_Pilot</string>
				<string>PSPB_Pilot</string>
				<string>PSPC_Pilot</string>
				<string>PSPD_Pilot</string>
				<string>PSPY_Pilot</string>
				<string>PSPZ_Pilot</string>
			</array>
			<key>Values</key>
			<array>
				<string>DEV_CI</string>
				<string>CIT</string>
				<string>CIT_INT</string>
				<string>CIT_1</string>
				<string>CIT_1_INT</string>
				<string>CIT_2</string>
				<string>CIT_2_INT</string>
				<string>CIT_3</string>
				<string>CIT_3_INT</string>
				<string>CAP</string>
				<string>CAP_CI</string>
				<string>QA_1</string>
				<string>QA_1_CI</string>
				<string>QA_2</string>
				<string>QA_2_CI</string>
				<string>QA_3</string>
				<string>QA_3_CI</string>
				<string>QA_Fix</string>
				<string>QA_Fix_CI</string>
				<string>PROD</string>
				<string>PRODA_INT</string>
				<string>PRODB_INT</string>
				<string>PRODC_INT</string>
				<string>PRODD_INT</string>
				<string>PRODY_INT</string>
				<string>PRODZ_INT</string>
				<string>PRODA_Pilot</string>
				<string>PRODB_Pilot</string>
				<string>PRODC_Pilot</string>
				<string>PRODD_Pilot</string>
				<string>PRODY_Pilot</string>
				<string>PRODZ_Pilot</string>
				<string>PSP</string>
				<string>PSPA_INT</string>
				<string>PSPB_INT</string>
				<string>PSPC_INT</string>
				<string>PSPD_INT</string>
				<string>PSPY_INT</string>
				<string>PSPZ_INT</string>
				<string>PSPA_Pilot</string>
				<string>PSPB_Pilot</string>
				<string>PSPC_Pilot</string>
				<string>PSPD_Pilot</string>
				<string>PSPY_Pilot</string>
				<string>PSPZ_Pilot</string>
			</array>
		</dict>
		<dict>
			<key>DisplaySortedByTitle</key>
			<false/>
			<key>Type</key>
			<string>PSMultiValueSpecifier</string>
			<key>Title</key>
			<string>TAPI Test Environment</string>
			<key>Key</key>
			<string>TAPITestEnvironmentSelector</string>
			<key>DefaultValue</key>
			<string>tapi-qa.tradeking.com</string>
			<key>Titles</key>
			<array>
				<string>QA</string>
				<string>DEV</string>
			</array>
			<key>Values</key>
			<array>
				<string>tapi-qa.tradeking.com</string>
				<string>tapi-dev.invest.ally.com</string>
			</array>
		</dict>
		<dict>
			<key>DisplaySortedByTitle</key>
			<false/>
			<key>Type</key>
			<string>PSMultiValueSpecifier</string>
			<key>Title</key>
			<string>Explore Environment</string>
			<key>Key</key>
			<string>ExploreEnvironmentSelector</string>
			<key>DefaultValue</key>
			<string>MOD</string>
			<key>Titles</key>
			<array>
				<string>DEV</string>
				<string>QA</string>
				<string>MOD</string>
				<string>PROD</string>
			</array>
			<key>Values</key>
			<array>
				<string>https://dev.explore-products-rates.ally.com</string>
				<string>https://qa.explore-products-rates.ally.com</string>
				<string>https://mod.explore-products-rates.ally.com</string>
				<string>https://explore-products-rates.ally.com</string>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSMultiValueSpecifier</string>
			<key>Title</key>
			<string>Ally Messenger</string>
			<key>Key</key>
			<string>allyMessengerSelector</string>
			<key>DefaultValue</key>
			<string>Adobe-controlled</string>
			<key>Values</key>
			<array>
				<string>On</string>
				<string>Off</string>
				<string>Adobe-controlled</string>
			</array>
			<key>Titles</key>
			<array>
				<string>On</string>
				<string>Off</string>
				<string>Adobe-controlled</string>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
			<key>Title</key>
			<string>Performance Charting Mock Data</string>
			<key>Key</key>
			<string>PerformanceChartingMockData</string>
			<key>DefaultValue</key>
			<false/>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
			<key>Title</key>
			<string>Savings Toolkit Mock Data</string>
			<key>Key</key>
			<string>SavingsToolkitMockData</string>
			<key>DefaultValue</key>
			<false/>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
			<key>Title</key>
			<string>Show Check Orders Status</string>
			<key>Key</key>
			<string>ShowCheckOrders</string>
			<key>DefaultValue</key>
			<false/>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
			<key>Title</key>
			<string>Check Orders Status Mock Data</string>
			<key>Key</key>
			<string>CheckOrdersStatusMockData</string>
			<key>DefaultValue</key>
			<false/>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
			<key>Title</key>
			<string>Show Check Orders Status</string>
			<key>Key</key>
			<string>ShowCheckOrders</string>
			<key>DefaultValue</key>
			<false/>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
			<key>Title</key>
			<string>Check Orders Status Mock Data</string>
			<key>Key</key>
			<string>CheckOrdersStatusMockData</string>
			<key>DefaultValue</key>
			<false/>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
			<key>Title</key>
			<string>OTP Auto-Fill</string>
			<key>Key</key>
			<string>OTPAutoFill</string>
			<key>DefaultValue</key>
			<true/>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
			<key>Title</key>
			<string>Show Login Banner</string>
			<key>Key</key>
			<string>ShowLoginBanner</string>
			<key>DefaultValue</key>
			<false/>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
			<key>Title</key>
			<string>Override Marketing Banner</string>
			<key>Key</key>
			<string>OverrideMarketingBanner</string>
			<key>DefaultValue</key>
			<false/>
		</dict>
		<dict>
			<key>DisplaySortedByTitle</key>
			<false/>
			<key>Type</key>
			<string>PSMultiValueSpecifier</string>
			<key>Title</key>
			<string>Marketing Banner</string>
			<key>Key</key>
			<string>MarketingBanner</string>
			<key>DefaultValue</key>
			<string></string>
			<key>Titles</key>
			<array>
				<string>None</string>
				<string>Transfers</string>
				<string>Card Controls</string>
				<string>Deposits</string>
				<string>Pay Bills</string>
				<string>Zelle</string>
				<string>Manage CDs</string>
				<string>Manage CD Interest Disbursement</string>
				<string>Manage CD Edit Maturity Instructions</string>
				<string>Explore</string>
				<string>Find ATMs</string>
				<string>Statements</string>
				<string>Tax Forms</string>
				<string>Secure Messages</string>
				<string>Profile</string>
				<string>Paperless Settings</string>
				<string>Login Settings</string>
				<string>Ally Invest Settings</string>
				<string>Alerts</string>
				<string>Custom Alerts</string>
				<string>Automatic Alerts</string>
				<string>Ally Assist</string>
				<string>Give Feedback</string>
				<string>Activity (History)</string>
				<string>Activity (Scheduled)</string>
				<string>Order Checks</string>
				<string>Order Deposit Slips</string>
				<string>Notices</string>
				<string>Overdraft Transfer Service</string>
				<string>Edit Contact Information</string>
				<string>Edit Home Address</string>
				<string>Edit Mailing Address</string>
				<string>Change Password</string>
				<string>OTP Delivery Settings</string>
				<string>Ally Messenger</string>
				<string>Forms</string>
				<string>Bank Messages</string>
				<string>Order Debit Card</string>
				<string>Report Lost/Stolen Debit Card</string>
				<string>Mortgage Lead Form</string>
				<string>insights</string>
			</array>
			<key>Values</key>
			<array>
				<string></string>
				<string>transfers</string>
				<string>cardControls</string>
				<string>deposits</string>
				<string>payBills</string>
				<string>zelle</string>
				<string>manageCD</string>
				<string>manageCDInterestDisbursement</string>
				<string>manageCDEditMaturityInstructions</string>
				<string>explore</string>
				<string>findAtm</string>
				<string>statements</string>
				<string>taxForms</string>
				<string>secureMessages</string>
				<string>profile</string>
				<string>paperlessSettings</string>
				<string>loginSettings</string>
				<string>allyInvestSettings</string>
				<string>alerts</string>
				<string>customAlerts</string>
				<string>automaticAlerts</string>
				<string>allyAssist</string>
				<string>giveFeedback</string>
				<string>activityHistory</string>
				<string>activityScheduled</string>
				<string>orderChecks</string>
				<string>orderDepositSlips</string>
				<string>notices</string>
				<string>overdraftTransferService</string>
				<string>editCustomerInformation</string>
				<string>editCustomerHomeAddress</string>
				<string>editCustomerMailingAddress</string>
				<string>changePassword</string>
				<string>otpDeliverySettings</string>
				<string>allyMessenger</string>
				<string>forms</string>
				<string>bankMessages</string>
				<string>orderDebitCard</string>
				<string>reportLostDebitCard</string>
				<string>mortgageLead</string>
				<string>insights</string>
			</array>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
			<key>Title</key>
			<string>Enable Survey</string>
			<key>Key</key>
			<string>EnableSurvey</string>
			<key>DefaultValue</key>
			<true/>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
			<key>Title</key>
			<string>Use Ally Messenger Sandbox</string>
			<key>Key</key>
			<string>UseAllyMessengerSandbox</string>
			<key>DefaultValue</key>
			<true/>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
			<key>Title</key>
			<string>Clear Cache</string>
			<key>Key</key>
			<string>ShouldResetPersistedData</string>
			<key>DefaultValue</key>
			<false/>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSToggleSwitchSpecifier</string>
			<key>Title</key>
			<string>Display Insights Tab</string>
			<key>Key</key>
			<string>DisplayInsightsTab</string>
			<key>DefaultValue</key>
			<true/>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
			<key>Title</key>
			<string>Invest Timeout (seconds)</string>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSTextFieldSpecifier</string>
            <key>DefaultValue</key>
			<string>14400</string>
			<key>Key</key>
			<string>INVEST_TIMEOUT</string>
			<key>KeyboardType</key>
			<string>NumberPad</string>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
		</dict>
		<dict>
			<key>Type</key>
			<string>PSTitleValueSpecifier</string>
			<key>Title</key>
			<string>Version</string>
			<key>Key</key>
			<string>AppVersion</string>
			<key>DefaultValue</key>
			<string>--</string>
		</dict>
	</array>
	<key>StringsTable</key>
	<string>Root</string>
</dict>
</plist>
