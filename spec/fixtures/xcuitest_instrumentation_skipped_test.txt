Test Suite 'Selected tests' started at 2024-05-15 21:00:15.576.
Test Suite 'UITests.xctest' started at 2024-05-15 21:00:15.576.
Test Suite 'HomeNewsRotationUITests' started at 2024-05-15 21:00:15.576.
Test Case '-[UITests.HomeNewsRotationUITests sampleTestName]' started.
    t =     0.00s Start Test at 2024-05-15 21:00:15.576
    t =     0.05s Set Up
/Users/<USER>/github/_work/sky-sport-group-ios/sky-sport-group-ios/Sport/Tests/UITests/UITests/HomeNews/HomeNewsRotationUITests.swift:12: -[UITests.HomeNewsRotationUITests test_rotation_for_ipads_and_assert_contents] : Test skipped - Skipped rotation test on iPhone as not currently supported
    t =     0.05s Tear Down
Test Case '-[UITests.HomeNewsRotationUITests sampleTestName]' skipped (0.053 seconds).
Test Suite 'HomeNewsRotationUITests' passed at 2024-05-15 21:00:15.629.
	 Executed 1 test, with 1 test skipped and 0 failures (0 unexpected) in 0.053 (0.053) seconds
Test Suite 'UITests.xctest' passed at 2024-05-15 21:00:15.629.
	 Executed 1 test, with 1 test skipped and 0 failures (0 unexpected) in 0.053 (0.053) seconds
Test Suite 'Selected tests' passed at 2024-05-15 21:00:15.630.
	 Executed 1 test, with 1 test skipped and 0 failures (0 unexpected) in 0.053 (0.054) seconds
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
2024-05-15 21:00:15.666 xcodebuild[25960:253471603] [MT] IDETestOperationsObserverDebug: 2.136 elapsed -- Testing started completed.
2024-05-15 21:00:15.666 xcodebuild[25960:253471603] [MT] IDETestOperationsObserverDebug: 0.000 sec, +0.000 sec -- start
2024-05-15 21:00:15.666 xcodebuild[25960:253471603] [MT] IDETestOperationsObserverDebug: 2.136 sec, +2.136 sec -- end

Test session results, code coverage, and logs:
	/tmp/00008110-000C39811A11401E_xcuitest_derived_data_7/Logs/Test/Test-Transient Testing-2024.05.15_21-00-13-+0000.xcresult

** TEST EXECUTE SUCCEEDED **