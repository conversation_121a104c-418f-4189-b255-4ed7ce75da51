Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild test-without-building -destination id=00008120-001C4C3A3E5BC01E -xctestrun /tmp/00008120-001C4C3A3E5BC01E_xctestrun.xctestrun -derivedDataPath /tmp/00008120-001C4C3A3E5BC01E_xcuitest_derived_data -resultBundlePath /tmp/00008120-001C4C3A3E5BC01E_xcresult_bundle/dummyModule/dummyClass/dummyTest_1712844287 -resultBundleVersion 3

User defaults from command line:
    IDEBuildOperationResultBundlePath = /tmp/00008120-001C4C3A3E5BC01E_xcresult_bundle/dummyModule/dummyClass/dummyTest_1712844287
    IDEDerivedDataPathOverride = /tmp/00008120-001C4C3A3E5BC01E_xcuitest_derived_data
    IDEPackageSupportUseBuiltinSCM = YES
    IDERequestedResultBundleFormatVersion = 3

2024-04-11 14:04:48.422 xcodebuild[21504:318002221]  DVTDownloadable: Unexpected error attempting to parse downloadable from index: Error Domain=DVTDownloadableErrors Code=16 "Unable to read in downloadable data. See underlying errors for details." UserInfo={DownloadablePlist={
    authentication = virtual;
    category = simulator;
    contentType = diskImage;
    dictionaryVersion = 2;
    fileSize = 7354535384;
    hostRequirements =     {
        minHostVersion = "13.4";
        minXcodeVersion = "15.0";
    };
    identifier = "com.apple.dmg.xrSimulatorSDK1_0_b1";
    name = "xrOS 1 beta Simulator Runtime";
    platform = "com.apple.platform.xros";
    simulatorVersion =     {
        buildUpdate = 21N5165g;
        version = "1.0";
    };
    source = "https://download.developer.apple.com/Developer_Tools/visionOS_1_beta/visionOS_1_beta_Simulator_Runtime.dmg";
    version = "*******";
}, NSLocalizedDescription=Unable to read in downloadable data. See underlying errors for details., DVTUnderlyingErrorsKey=(
    "Error Domain=DVTDownloadableErrors Code=23 \"'com.apple.platform.xros' is an unknown platform identifier.\" UserInfo={NSLocalizedDescription='com.apple.platform.xros' is an unknown platform identifier.}"
)}
2024-04-11 14:04:48.422 xcodebuild[21504:318002221]  DVTDownloadable: Unexpected error attempting to parse downloadable from index: Error Domain=DVTDownloadableErrors Code=16 "Unable to read in downloadable data. See underlying errors for details." UserInfo={DownloadablePlist={
    authentication = virtual;
    category = simulator;
    contentType = diskImage;
    dictionaryVersion = 2;
    fileSize = 7220726735;
    hostRequirements =     {
        minHostVersion = "13.4";
        minXcodeVersion = "15.0";
    };
    identifier = "com.apple.dmg.xrSimulatorSDK1_0_b2";
    name = "xrOS 1 beta 2 Simulator Runtime";
    platform = "com.apple.platform.xros";
    simulatorVersion =     {
        buildUpdate = 21N5207f;
        version = "1.0";
    };
    source = "https://download.developer.apple.com/Developer_Tools/visionOS_1_beta_2_Simulator_Runtime/visionOS_1_beta_2_Simulator_Runtime.dmg";
    version = "*******";
}, NSLocalizedDescription=Unable to read in downloadable data. See underlying errors for details., DVTUnderlyingErrorsKey=(
    "Error Domain=DVTDownloadableErrors Code=23 \"'com.apple.platform.xros' is an unknown platform identifier.\" UserInfo={NSLocalizedDescription='com.apple.platform.xros' is an unknown platform identifier.}"
)}
2024-04-11 14:04:48.422 xcodebuild[21504:318002221]  DVTDownloadable: Unexpected error attempting to parse downloadable from index: Error Domain=DVTDownloadableErrors Code=16 "Unable to read in downloadable data. See underlying errors for details." UserInfo={DownloadablePlist={
    authentication = virtual;
    category = simulator;
    contentType = diskImage;
    dictionaryVersion = 2;
    fileSize = 7281422409;
    hostRequirements =     {
        minHostVersion = "13.4";
        minXcodeVersion = "15.0";
    };
    identifier = "com.apple.dmg.xrSimulatorSDK1_0_b3";
    name = "xrOS 1 beta 3 Simulator Runtime";
    platform = "com.apple.platform.xros";
    simulatorVersion =     {
        buildUpdate = 21N5233f;
        version = "1.0";
    };
    source = "https://download.developer.apple.com/Developer_Tools/visionOS_1_beta_3_Simulator_Runtime/visionOS_1_beta_3_Simulator_Runtime.dmg";
    version = "*******";
}, NSLocalizedDescription=Unable to read in downloadable data. See underlying errors for details., DVTUnderlyingErrorsKey=(
    "Error Domain=DVTDownloadableErrors Code=23 \"'com.apple.platform.xros' is an unknown platform identifier.\" UserInfo={NSLocalizedDescription='com.apple.platform.xros' is an unknown platform identifier.}"
)}
2024-04-11 14:04:48.423 xcodebuild[21504:318002221]  DVTDownloadable: Unexpected error attempting to parse downloadable from index: Error Domain=DVTDownloadableErrors Code=16 "Unable to read in downloadable data. See underlying errors for details." UserInfo={DownloadablePlist={
    authentication = virtual;
    category = simulator;
    contentType = diskImage;
    dictionaryVersion = 2;
    fileSize = 7071092104;
    hostRequirements =     {
        minHostVersion = "13.4";
        minXcodeVersion = "15.0";
    };
    identifier = "com.apple.dmg.xrSimulatorSDK1_0_b4";
    name = "xrOS 1 beta 4 Simulator Runtime";
    platform = "com.apple.platform.xros";
    simulatorVersion =     {
        buildUpdate = 21N5259j;
        version = "1.0";
    };
    source = "https://download.developer.apple.com/Developer_Tools/visionOS_1_beta_4_Simulator_Runtime/visionOS_1_beta_4_Simulator_Runtime.dmg";
    version = "*******";
}, NSLocalizedDescription=Unable to read in downloadable data. See underlying errors for details., DVTUnderlyingErrorsKey=(
    "Error Domain=DVTDownloadableErrors Code=23 \"'com.apple.platform.xros' is an unknown platform identifier.\" UserInfo={NSLocalizedDescription='com.apple.platform.xros' is an unknown platform identifier.}"
)}
2024-04-11 14:04:48.423 xcodebuild[21504:318002221]  DVTDownloadable: Unexpected error attempting to parse downloadable from index: Error Domain=DVTDownloadableErrors Code=16 "Unable to read in downloadable data. See underlying errors for details." UserInfo={DownloadablePlist={
    authentication = virtual;
    category = simulator;
    contentType = diskImage;
    dictionaryVersion = 2;
    fileSize = 7160992014;
    hostRequirements =     {
        minHostVersion = "13.4";
        minXcodeVersion = "15.0";
    };
    identifier = "com.apple.dmg.xrSimulatorSDK1_0_b6";
    name = "xrOS 1 beta 6 Simulator Runtime";
    platform = "com.apple.platform.xros";
    simulatorVersion =     {
        buildUpdate = 21N5300a;
        version = "1.0";
    };
    source = "https://download.developer.apple.com/Developer_Tools/visionOS_1_beta_6_Simulator_Runtime/visionOS_1_beta_6_Simulator_Runtime.dmg";
    version = "*******";
}, NSLocalizedDescription=Unable to read in downloadable data. See underlying errors for details., DVTUnderlyingErrorsKey=(
    "Error Domain=DVTDownloadableErrors Code=23 \"'com.apple.platform.xros' is an unknown platform identifier.\" UserInfo={NSLocalizedDescription='com.apple.platform.xros' is an unknown platform identifier.}"
)}
2024-04-11 14:04:48.424 xcodebuild[21504:318002221]  DVTDownloadable: Unexpected error attempting to parse downloadable from index: Error Domain=DVTDownloadableErrors Code=16 "Unable to read in downloadable data. See underlying errors for details." UserInfo={DownloadablePlist={
    authentication = virtual;
    category = simulator;
    contentType = diskImage;
    dictionaryVersion = 2;
    fileSize = 7163594938;
    hostRequirements =     {
        minHostVersion = "13.4";
        minXcodeVersion = "15.0";
    };
    identifier = "com.apple.dmg.xrSimulatorSDK1_0";
    name = "xrOS 1 Simulator Runtime";
    platform = "com.apple.platform.xros";
    simulatorVersion =     {
        buildUpdate = 21N305;
        version = "1.0";
    };
    source = "https://download.developer.apple.com/Developer_Tools/visionOS_1_Simulator_Runtime/visionOS_1_Simulator_Runtime.dmg";
    version = "*******";
}, NSLocalizedDescription=Unable to read in downloadable data. See underlying errors for details., DVTUnderlyingErrorsKey=(
    "Error Domain=DVTDownloadableErrors Code=23 \"'com.apple.platform.xros' is an unknown platform identifier.\" UserInfo={NSLocalizedDescription='com.apple.platform.xros' is an unknown platform identifier.}"
)}
2024-04-11 14:04:48.424 xcodebuild[21504:318002221]  DVTDownloadable: Unexpected error attempting to parse downloadable from index: Error Domain=DVTDownloadableErrors Code=16 "Unable to read in downloadable data. See underlying errors for details." UserInfo={DownloadablePlist={
    authentication = virtual;
    category = simulator;
    contentType = diskImage;
    dictionaryVersion = 2;
    fileSize = 6792905436;
    hostRequirements =     {
        minHostVersion = "13.4";
        minXcodeVersion = "15.0";
    };
    identifier = "com.apple.dmg.xrSimulatorSDK1_1_b1";
    name = "xrOS 1.1 beta Simulator Runtime";
    platform = "com.apple.platform.xros";
    simulatorVersion =     {
        buildUpdate = 21O5181e;
        version = "1.1";
    };
    source = "https://download.developer.apple.com/Developer_Tools/visionOS_1.1_beta_Simulator_Runtime/visionOS_1.1_beta_Simulator_Runtime.dmg";
    version = "*******";
}, NSLocalizedDescription=Unable to read in downloadable data. See underlying errors for details., DVTUnderlyingErrorsKey=(
    "Error Domain=DVTDownloadableErrors Code=23 \"'com.apple.platform.xros' is an unknown platform identifier.\" UserInfo={NSLocalizedDescription='com.apple.platform.xros' is an unknown platform identifier.}"
)}
2024-04-11 14:04:48.424 xcodebuild[21504:318002221]  DVTDownloadable: Unexpected error attempting to parse downloadable from index: Error Domain=DVTDownloadableErrors Code=16 "Unable to read in downloadable data. See underlying errors for details." UserInfo={DownloadablePlist={
    authentication = virtual;
    category = simulator;
    contentType = diskImage;
    dictionaryVersion = 2;
    fileSize = 6790205536;
    hostRequirements =     {
        minHostVersion = "13.4";
        minXcodeVersion = "15.0";
    };
    identifier = "com.apple.dmg.xrSimulatorSDK1_1_b2";
    name = "xrOS 1.1 beta 2 Simulator Runtime";
    platform = "com.apple.platform.xros";
    simulatorVersion =     {
        buildUpdate = 21O5188c;
        version = "1.1";
    };
    source = "https://download.developer.apple.com/Developer_Tools/visionOS_1.1_beta_2_Simulator_Runtime/visionOS_1.1_beta_2_Simulator_Runtime.dmg";
    version = "*******";
}, NSLocalizedDescription=Unable to read in downloadable data. See underlying errors for details., DVTUnderlyingErrorsKey=(
    "Error Domain=DVTDownloadableErrors Code=23 \"'com.apple.platform.xros' is an unknown platform identifier.\" UserInfo={NSLocalizedDescription='com.apple.platform.xros' is an unknown platform identifier.}"
)}
2024-04-11 14:04:48.424 xcodebuild[21504:318002221]  DVTDownloadable: Unexpected error attempting to parse downloadable from index: Error Domain=DVTDownloadableErrors Code=16 "Unable to read in downloadable data. See underlying errors for details." UserInfo={DownloadablePlist={
    authentication = virtual;
    category = simulator;
    contentType = diskImage;
    dictionaryVersion = 2;
    fileSize = 6796687016;
    hostRequirements =     {
        minHostVersion = "13.4";
        minXcodeVersion = "15.0";
    };
    identifier = "com.apple.dmg.xrSimulatorSDK1_1";
    name = "xrOS 1.1 Simulator Runtime";
    platform = "com.apple.platform.xros";
    simulatorVersion =     {
        buildUpdate = 21O209;
        version = "1.1";
    };
    source = "https://download.developer.apple.com/Developer_Tools/visionOS_1.1_Simulator_Runtime/visionOS_1.1_Simulator_Runtime.dmg";
    version = "*******";
}, NSLocalizedDescription=Unable to read in downloadable data. See underlying errors for details., DVTUnderlyingErrorsKey=(
    "Error Domain=DVTDownloadableErrors Code=23 \"'com.apple.platform.xros' is an unknown platform identifier.\" UserInfo={NSLocalizedDescription='com.apple.platform.xros' is an unknown platform identifier.}"
)}
Writing result bundle at path:
	/tmp/00008120-001C4C3A3E5BC01E_xcresult_bundle/dummyModule/dummyClass/dummyTest_1712844287


NHS: Logout: network available
2024-04-11 14:04:58.762795+0000 NHSOnline[8745:595225] [connection] nw_socket_handle_socket_event [C1.1.1:2] Socket SO_ERROR [61: Connection refused]
2024-04-11 14:04:58.763070+0000 NHSOnline[8745:595225] [connection] nw_socket_handle_socket_event [C1.1.2:2] Socket SO_ERROR [61: Connection refused]
2024-04-11 14:04:58.763189+0000 NHSOnline[8745:595225] Connection 1: received failure notification
2024-04-11 14:04:58.763213+0000 NHSOnline[8745:595225] Connection 1: failed to connect 1:61, reason -1
2024-04-11 14:04:58.763224+0000 NHSOnline[8745:595225] Connection 1: encountered error(1:61)
2024-04-11 14:04:58.763618+0000 NHSOnline[8745:595225] Task <921B182B-2117-4223-B3D7-20DEEE42C868>.<1> HTTP load failed, 0/0 bytes (error code: -1004 [1:61])
2024-04-11 14:04:58.764343+0000 NHSOnline[8745:595219] Task <921B182B-2117-4223-B3D7-20DEEE42C868>.<1> finished with error [-1004] Error Domain=NSURLErrorDomain Code=-1004 "Could not connect to the server." UserInfo={_kCFStreamErrorCodeKey=61, NSUnderlyingError=0x280c48360 {Error Domain=kCFErrorDomainCFNetwork Code=-1004 "(null)" UserInfo={_NSURLErrorNWPathKey=satisfied (Path is satisfied), interface: lo0, _kCFStreamErrorCodeKey=61, _kCFStreamErrorDomainKey=1}}, _NSURLErrorFailingURLSessionTaskErrorKey=LocalDataTask <921B182B-2117-4223-B3D7-20DEEE42C868>.<1>, _NSURLErrorRelatedURLSessionTaskErrorKey=(
    "LocalDataTask <921B182B-2117-4223-B3D7-20DEEE42C868>.<1>"
), NSLocalizedDescription=Could not connect to the server., NSErrorFailingURLStringKey=http://localhost:8080/v2/configuration, NSErrorFailingURLKey=http://localhost:8080/v2/configuration, _kCFStreamErrorDomainKey=1}

NHS: LogoutModel: Error,error(-1, nil, nil, Error Domain=NSURLErrorDomain Code=-1004 "Could not connect to the server." UserInfo={_kCFStreamErrorCodeKey=61, NSUnderlyingError=0x280c48360 {Error Domain=kCFErrorDomainCFNetwork Code=-1004 "(null)" UserInfo={_NSURLErrorNWPathKey=satisfied (Path is satisfied), interface: lo0, _kCFStreamErrorCodeKey=61, _kCFStreamErrorDomainKey=1}}, _NSURLErrorFailingURLSessionTaskErrorKey=LocalDataTask <921B182B-2117-4223-B3D7-20DEEE42C868>.<1>, _NSURLErrorRelatedURLSessionTaskErrorKey=(
    "LocalDataTask <921B182B-2117-4223-B3D7-20DEEE42C868>.<1>"
), NSLocalizedDescription=Could not connect to the server., NSErrorFailingURLStringKey=http://localhost:8080/v2/configuration, NSErrorFailingURLKey=http://localhost:8080/v2/configuration, _kCFStreamErrorDomainKey=1})
Logout V2ConfigurationGet Error
Trace: NHS-iOS-Native-270CD0BA-AEB6-4DFB-B37B-E975CC6D83DE-Logout V2ConfigurationGet Error
The operation couldn’t be completed. (NHSApi.ErrorResponse error 0.)
Get Minimum Supported Version Error
Trace: NHS-iOS-Native-9B561965-96F9-45DC-89B0-77EBA3773F08-Get Minimum Supported Version Error
The operation couldn’t be completed. (NHSApi.ErrorResponse error 0.)
Get Minimum Supported Version Error
Trace: NHS-iOS-Native-863B5A65-A967-4CD0-AD21-61EDE4564D34-Get Minimum Supported Version Error
The operation couldn’t be completed. (NHSApi.ErrorResponse error 0.)

NHS: BiometricAuthenticator: attemptBiometricLogin: status = NotRegistered
2024-04-11 14:04:59.113717+0000 NHSOnline[8745:594873] [TraitCollection] Class CKBrowserSwitcherViewController overrides the -traitCollection getter, which is not supported. If you're trying to override traits, you must use the appropriate API.
Test Suite 'All tests' started at 2024-04-11 14:05:00.761.
Test Suite 'UnitTests.xctest' started at 2024-04-11 14:05:00.761.
Test Suite 'ApiCookieTests' started at 2024-04-11 14:05:00.761.
Test Case '-[UnitTests.ApiCookieTests test_APICookie_clearSessionCookies]' started.
Test Case '-[UnitTests.ApiCookieTests test_APICookie_clearSessionCookies]' passed (0.002 seconds).
Test Case '-[UnitTests.ApiCookieTests test_APICookie_ReadCookieValue]' started.
Test Case '-[UnitTests.ApiCookieTests test_APICookie_ReadCookieValue]' passed (0.001 seconds).
Test Case '-[UnitTests.ApiCookieTests testCreateSessionCookie]' started.
Test Case '-[UnitTests.ApiCookieTests testCreateSessionCookie]' passed (0.002 seconds).
Test Case '-[UnitTests.ApiCookieTests testDeleteWKWebViewCookie]' started.
Test Case '-[UnitTests.ApiCookieTests testDeleteWKWebViewCookie]' passed (0.003 seconds).
Test Case '-[UnitTests.ApiCookieTests testGetAllCookies]' started.
Test Case '-[UnitTests.ApiCookieTests testGetAllCookies]' passed (0.000 seconds).
Test Case '-[UnitTests.ApiCookieTests testGetCookie]' started.
Test Case '-[UnitTests.ApiCookieTests testGetCookie]' passed (0.001 seconds).
Test Case '-[UnitTests.ApiCookieTests testSetCookie]' started.
Test Case '-[UnitTests.ApiCookieTests testSetCookie]' passed (0.005 seconds).
Test Suite 'ApiCookieTests' passed at 2024-04-11 14:05:00.775.
	 Executed 7 tests, with 0 failures (0 unexpected) in 0.013 (0.014) seconds