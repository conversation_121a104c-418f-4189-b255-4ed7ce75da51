Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild test-without-building -destination id=00008110-001C0D643445401E -xctestrun /tmp/00008110-001C0D643445401E_xctestrun.xctestrun -derivedDataPath /tmp/00008110-001C0D643445401E_xcuitest_derived_data -resultBundlePath /tmp/00008110-001C0D643445401E_xcresult_bundle/dummyModule/dummyClass/dummyTest -resultBundleVersion 3

User defaults from command line:
    IDEBuildOperationResultBundlePath = /tmp/00008110-001C0D643445401E_xcresult_bundle/dummyModule/dummyClass/dummyTest
    IDEDerivedDataPathOverride = /tmp/00008110-001C0D643445401E_xcuitest_derived_data
    IDEPackageSupportUseBuiltinSCM = YES
    IDERequestedResultBundleFormatVersion = 3

Writing result bundle at path:
	/tmp/00008110-001C0D643445401E_xcresult_bundle/dummyModule/dummyClass/dummyTest

2023-06-15 10:04:09.341537+0000 BullsEyeUITests-Runner[23694:3360858] Running tests...
Test Suite 'Selected tests' started at 2023-06-15 10:04:10.870
Test Suite 'BullsEyeUITests.xctest' started at 2023-06-15 10:04:10.871
Test Suite 'BullsEyeUITests' started at 2023-06-15 10:04:10.871
Test Case '-[BullsEyeUITests.BullsEyeUITests testGameStyleSwitch002]' started (Iteration 1 of 4).
    t =     0.00s Start Test at 2023-06-15 10:04:10.871
    t =     0.06s Set Up
2023-06-15 10:04:10.930421+0000 BullsEyeUITests-Runner[23694:3360858] Triggered Setup Phase at: : 1686823450
    t =     0.06s     Open io.manish.BullsEye
    t =     0.11s         Launch io.manish.BullsEye
    t =     0.11s             Terminate io.manish.BullsEye:23693
    t =     1.30s             Setting up automation session
    t =     1.34s             Wait for io.manish.BullsEye to idle
    t =     2.43s Find the "Slide" Button
    t =     2.49s Checking existence of `"Get as close as you can to: " StaticText`
    t =     2.51s Checking existence of `"Guess where the slider is: " StaticText`
    t =     2.52s Tap "Type" Button
    t =     2.52s     Wait for io.manish.BullsEye to idle
    t =     2.62s     Find the "Type" Button
    t =     2.65s     Check for interrupting elements affecting "Type" Button
    t =     2.67s     Synthesize event
    t =     3.03s     Wait for io.manish.BullsEye to idle
    t =     3.26s Checking existence of `"Guess where the slider is: " StaticText`
    t =     3.30s Checking existence of `"Get as close as you can to: " StaticText`
2023-06-15 10:04:14.186303+0000 BullsEyeUITests-Runner[23694:3360858] Epoch Time for the Test: 1686823454
2023-06-15 10:04:14.186562+0000 BullsEyeUITests-Runner[23694:3360858] Failing Assertion: : 0
/Users/<USER>/XCODEProjects/PaysafeIssue/sample-swift-project-with-parallel-ui-test/BullsEyeUITests/BullsEyeUITests.swift:82: error: -[BullsEyeUITests.BullsEyeUITests testGameStyleSwitch002] : XCTAssertTrue failed
    t =     3.42s Tear Down
2023-06-15 10:04:14.292640+0000 BullsEyeUITests-Runner[23694:3360858] Triggered teardown Phase at: : 1686823454
    t =     3.42s     Terminate io.manish.BullsEye:23695
Test Case '-[BullsEyeUITests.BullsEyeUITests testGameStyleSwitch002]' failed (4.558 seconds).
Test Case '-[BullsEyeUITests.BullsEyeUITests testGameStyleSwitch002]' started (Iteration 2 of 4).
    t =     0.00s Start Test at 2023-06-15 10:04:15.431
    t =     0.09s Set Up
2023-06-15 10:04:15.516445+0000 BullsEyeUITests-Runner[23694:3360858] Triggered Setup Phase at: : 1686823455
    t =     0.09s     Open io.manish.BullsEye
    t =     0.15s         Launch io.manish.BullsEye
    t =     0.26s             Setting up automation session
    t =     0.30s             Wait for io.manish.BullsEye to idle
    t =     1.38s Find the "Slide" Button
    t =     1.43s Find the "Type" Button
    t =     1.45s Checking existence of `"Guess where the slider is: " StaticText`
    t =     1.47s Checking existence of `"Get as close as you can to: " StaticText`
    t =     1.48s Tap "Slide" Button
    t =     1.48s     Wait for io.manish.BullsEye to idle
    t =     1.57s     Find the "Slide" Button
    t =     1.60s     Check for interrupting elements affecting "Slide" Button
    t =     1.62s     Synthesize event
    t =     1.97s     Wait for io.manish.BullsEye to idle
    t =     2.19s Checking existence of `"Get as close as you can to: " StaticText`
    t =     2.23s Checking existence of `"Guess where the slider is: " StaticText`
2023-06-15 10:04:17.677372+0000 BullsEyeUITests-Runner[23694:3360858] Epoch Time for the Test: 1686823457
2023-06-15 10:04:17.677580+0000 BullsEyeUITests-Runner[23694:3360858] Success Assertion: : 1
    t =     2.25s Tear Down
2023-06-15 10:04:17.679489+0000 BullsEyeUITests-Runner[23694:3360858] Triggered teardown Phase at: : 1686823457
    t =     2.25s     Terminate io.manish.BullsEye:23696
Test Case '-[BullsEyeUITests.BullsEyeUITests testGameStyleSwitch002]' passed (3.340 seconds).
Test Suite 'BullsEyeUITests' failed at 2023-06-15 10:04:18.771.
	 Executed 2 tests, with 1 failure (0 unexpected) in 7.898 (7.900) seconds
Test Suite 'BullsEyeUITests2' started at 2023-06-15 10:04:18.772
Test Case '-[BullsEyeUITests.BullsEyeUITests2 testGameStyleSwitch]' started (Iteration 1 of 4).
    t =     0.00s Start Test at 2023-06-15 10:04:18.773
    t =     0.08s Set Up
    t =     0.08s     Open io.manish.BullsEye
    t =     0.15s         Launch io.manish.BullsEye
    t =     0.25s             Setting up automation session
    t =     0.28s             Wait for io.manish.BullsEye to idle
    t =     1.38s Find the "Slide" Button
    t =     1.44s Checking existence of `"Get as close as you can to: " StaticText`
    t =     1.45s Checking existence of `"Guess where the slider is: " StaticText`
    t =     1.46s Tap "Type" Button
    t =     1.46s     Wait for io.manish.BullsEye to idle
    t =     1.56s     Find the "Type" Button
    t =     1.59s     Check for interrupting elements affecting "Type" Button
    t =     1.61s     Synthesize event
    t =     1.97s     Wait for io.manish.BullsEye to idle
    t =     2.19s Checking existence of `"Guess where the slider is: " StaticText`
    t =     2.22s Checking existence of `"Get as close as you can to: " StaticText`
    t =     2.24s Tear Down
Test Case '-[BullsEyeUITests.BullsEyeUITests2 testGameStyleSwitch]' passed (2.451 seconds).
Test Suite 'BullsEyeUITests2' passed at 2023-06-15 10:04:21.225.
	 Executed 1 test, with 0 failures (0 unexpected) in 2.451 (2.453) seconds
Test Suite 'BullsEyeUITests.xctest' failed at 2023-06-15 10:04:21.226.
	 Executed 3 tests, with 1 failure (0 unexpected) in 10.350 (10.355) seconds
Test Suite 'Selected tests' failed at 2023-06-15 10:04:21.228.
	 Executed 3 tests, with 1 failure (0 unexpected) in 10.350 (10.358) seconds
LLVM Profile Error: Failed to write file "default.profraw": Operation not permitted
2023-06-15 10:04:21.235 xcodebuild[56265:281389682] [MT] IDETestOperationsObserverDebug: 26.596 elapsed -- Testing started completed.
2023-06-15 10:04:21.235 xcodebuild[56265:281389682] [MT] IDETestOperationsObserverDebug: 0.000 sec, +0.000 sec -- start
2023-06-15 10:04:21.235 xcodebuild[56265:281389682] [MT] IDETestOperationsObserverDebug: 26.596 sec, +26.596 sec -- end

Test session results, code coverage, and logs:
	/tmp/00008110-001C0D643445401E_xcresult_bundle/dummyModule/dummyClass/dummyTest

** TEST EXECUTE SUCCEEDED **

Testing started
