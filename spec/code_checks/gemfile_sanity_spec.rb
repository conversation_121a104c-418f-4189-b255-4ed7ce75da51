require 'digest'
require_relative '../../config/constants'

# Update these hashes manually by running `md5sum` command
GEMFILE_CURRENT_MD5 = '8f6eec191638fa9b9020d46612a9a168'
GEMFILE_LOCK_CURRENT_MD5 = '78a3fa32b32b4083920d2111522bf9d9'

describe 'Gemfile checks: ' do
  if Digest::MD5.file("Gemfile") == GEMFILE_CURRENT_MD5
    puts "No changes in Gemfile, nothing to do"
    return
  end

  it 'Gemfile.lock should be modified if Gemfile is changed' do
    expect(Digest::MD5.file("Gemfile.lock")).to_not eq(GEMFILE_LOCK_CURRENT_MD5)
  end
end
