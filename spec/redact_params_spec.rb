require_relative "../lib/utils/utils"

describe 'Utils.redact_params' do
  it "Leaves untouched the hash when there is nothing to redact" do
    hash = { 'foo' => 'bar', 'baz' => 'bax' }
    expect(Utils.redact_params(hash)).to be_eql(hash)
  end

  it "puts [REDACTED] in the fields to redact" do
    hash = { 'app_store_username' => 'john doe',
             'app_store_password' => 'pazz',
             'unrelated_field' => 'value' }
    expected_output = { 'app_store_username' => '[REDACTED]',
                        'app_store_password' => '[REDACTED]',
                        'unrelated_field' => 'value' }
    expect(Utils.redact_params(hash)).to be_eql(expected_output)
  end
end
