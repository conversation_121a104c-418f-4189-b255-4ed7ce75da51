require 'date'

require 'json'
require_relative '../spec_helper'
require_relative '../../scripts/device_thread'
require_relative '../../lib/utils/idevice_utils'
require_relative '../../lib/utils/ios_mdm_service_client'
require_relative '../../lib/utils/web_driver_agent'
require_relative '../../lib/utils/utils'
require_relative '../../lib/utils/osutils'
require_relative '../../lib/utils/wda_version'
require_relative '../../config/constants'
require_relative '../../lib/helpers/browserstack_app_helper'

describe BrowserStack::DeviceThread do
  let(:device) { "1234" }
  let(:ecid) { "0x1234" }
  let(:device_state) { DeviceState.new(device) }
  let(:config_json) { JSON.parse(fixture('config.json')) }
  let(:static_conf_json) { JSON.parse(fixture('static_conf.json')) }
  let(:device_fork_executor) { double('device_fork_executor', { executor: nil }) }
  let(:fork_executor) { double('fork_executor', { pid: 404 }) }
  let(:provision_profile) { 'fake_provision_profile' }

  before do
    allow(DeviceState).to receive(:new).and_return(device_state)
    allow(DeviceManager).to receive(:is_first_cleanup?).and_return(false)
  end

  # TODO: this is assuming that we create an online phone. If we add specs, we
  # may need to test for offline phones and so on.
  let(:device_thread) do
    FileUtils.touch "/usr/local/.browserstack/config/ppuid_#{device}"
    BrowserStack::DeviceThread.configure("app",
                                         "0.1.2.3",
                                         static_conf_json,
                                         "/usr/local/.browserstack/config",
                                         "/var/log/browserstack",
                                         "1.7.0",
                                         "/usr/local/.browserstack/appium_1.7.0_bstack",
                                         "/Users/<USER>/Library/Keychains/Browserstack.keychain-db",
                                         "the_g_____________pass",
                                         443,
                                         "hostname",
                                         "region",
                                         "subregion",
                                         400,
                                         "inventory.url.example.com",
                                         "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/DeviceSupport/",
                                         "/Users/<USER>/Library/Developer/Xcode/iOS DeviceSupport/",
                                         { "1.6.5" => "path_to_165",
                                           "1.7.0" => "path_to_170" },
                                         5,
                                         false,
                                         false,
                                         { "Proxy" => { "type" => "com.apple.proxy.http.global" } })
    BrowserStack::DeviceThread.new(config_json,
                                   device,
                                   false,
                                   8082,
                                   27_755,
                                   8402,
                                   :redis_client)
  end

  describe '#send_cleanup' do
    context 'when a full cleanup is requested' do
      it 'first removes the setup requested file and then sends a cleanup request' do
        fakeresponse = double
        allow(fakeresponse).to receive(:body)

        expect(FileUtils).to receive(:rm_f).ordered
        expect(Net::HTTP).to receive(:get_response).ordered.and_return(fakeresponse)
        device_thread.send_cleanup 'reason'
      end
    end
  end

  describe '#get_device_info' do
    context 'when device info is requested' do
      let(:deviceinfo) { ["ProductType: iPad6,11", "SerialNumber: GCKVFRZSHLF8", "ProductVersion: 11.0.3", "BuildVersion: 15A432", "WiFiAddress: 68:ab:1e:d1:52:47", "InternationalMobileSubscriberIdentity:354005103732385", "UniqueChipID:8443A1E00402E"] }
      it 'should return device info with empty imsi when device is an iPad' do
        device_thread.instance_variable_set(:@device_name, "iPad 5th")
        expect(IdeviceUtils).to receive(:ideviceinfo).with(device).and_return(deviceinfo)
        expect(device_thread.get_device_info).to eq(["GCKVFRZSHLF8", "11.0.3", "15A432", "68:ab:1e:d1:52:47", "", "", "8443A1E00402E"])
      end

      it 'should return device info with right imsi when device is an iPhone' do
        deviceinfo = ["ProductType: iPhone12,3", "SerialNumber: GCKVFRZSHLF6", "ProductVersion: 11.0.3", "BuildVersion: 15A432", "WiFiAddress: 68:ab:1e:d1:52:47", "InternationalMobileSubscriberIdentity:354005103732385", "UniqueChipID:8443A1E00402E"]
        device_thread.instance_variable_set(:@device_name, "iPhone 11 Pro")
        expect(IdeviceUtils).to receive(:ideviceinfo).with(device).and_return(deviceinfo)
        expect(device_thread.get_device_info).to eq(["GCKVFRZSHLF6", "11.0.3", "15A432", "68:ab:1e:d1:52:47", "354005103732385", "", "8443A1E00402E"])
      end
    end
  end

  describe '#state_change_action' do
    let(:temp_json) do
      {
        'online' => false, 'profile_check_counter' => 0
      }
    end

    it 'pushes to zombie if device has just gone offline' do
      expected = temp_json.clone
      device_thread.instance_variable_set(:@ex_online_state, true)

      expect(device_thread).to receive(:push_logs_to_zombie)

      result = device_thread.state_change_action(temp_json)
      expect(result).to eql(expected)
    end

    it 'does not push to zombie if device is online' do
      temp_json['online'] = true
      expected = temp_json.clone
      expected['profile_check_counter'] = 1

      expect(device_thread).not_to receive(:push_logs_to_zombie)

      result = device_thread.state_change_action(temp_json)
      expect(result).to eql(expected)
    end

    it 'does not push to zombie if device is offline and was already offline' do
      temp_json['online'] = false
      expected = temp_json.clone
      device_thread.instance_variable_set(:@ex_online_state, false)

      expect(device_thread).not_to receive(:push_logs_to_zombie)

      result = device_thread.state_change_action(temp_json)
      expect(result).to eql(expected)
    end

    it 'increments profile check counter if device is online and idle' do
      temp_json['online'] = true
      expected = temp_json.clone
      expected['profile_check_counter'] = 1

      allow(device_thread).to receive(:push_logs_to_zombie)

      result = device_thread.state_change_action(temp_json)
      expect(result).to eql(expected)
    end
  end

  describe '#reboot_if_still_offline' do
    it 'reboots if offline with internet down and not rebooted for an hour or more' do
      temp_json = {
        'last_online' => (Time.now - 2).to_s,
        'last_rebooted' => (Time.now - 3).to_s,
        'offline_reason' => BrowserStack::INTERNET_DOWN_MESSAGE
      }
      device = '12345'
      offline_reboot_time = 1
      expect(IdeviceUtils).to receive(:reboot).with(device)
      device_thread.reboot_if_still_offline(device, temp_json, offline_reboot_time)
    end

    it 'does not reboot if offline reason is not internet-down' do
      temp_json = {
        'last_online' => (Time.now - 2).to_s,
        'last_rebooted' => (Time.now - 3).to_s,
        'offline_reason' => 'Just not feeling great'
      }
      device = '12345'
      offline_reboot_time = 1
      expect(IdeviceUtils).not_to receive(:reboot)
      device_thread.reboot_if_still_offline(device, temp_json, offline_reboot_time)
    end

    it 'does not reboot if it has been rebooted since going offline' do
      temp_json = { 'last_online' => (Time.now - 2).to_s, 'last_rebooted' => (Time.now - 1).to_s }
      device = '12345'
      offline_reboot_time = 1
      expect(IdeviceUtils).not_to receive(:reboot)
      device_thread.reboot_if_still_offline(device, temp_json, offline_reboot_time)
    end

    it 'does not reboot if last_online is null' do
      temp_json = { 'last_online' => nil, 'last_rebooted' => (Time.now - 3).to_s }
      device = '12345'
      offline_reboot_time = 2
      expect(IdeviceUtils).not_to receive(:reboot)
      device_thread.reboot_if_still_offline(device, temp_json, offline_reboot_time)
    end

    it 'does reboot if last_rebooted is null' do
      temp_json = {
        'last_online' => (Time.now - 2).to_s,
        'offline_reason' => BrowserStack::INTERNET_DOWN_MESSAGE
      }
      device = '12345'
      offline_reboot_time = 1
      expect(IdeviceUtils).to receive(:reboot).with(device)
      device_thread.reboot_if_still_offline(device, temp_json, offline_reboot_time)
    end

    context 'when device is dedicated' do
      before do
        allow(device_state).to receive(:dedicated_device_file_present?).and_return(true)
      end
      it 'does not reboot if last_online is within offline_reboot_time' do
        temp_json = { 'last_online' => (Time.now - 1).to_s, 'last_rebooted' => nil }
        device = '12345'
        offline_reboot_time = 2
        expect(IdeviceUtils).not_to receive(:reboot)
        device_thread.reboot_if_still_offline(device, temp_json, offline_reboot_time)
      end

      it 'does not reboot if last_rebooted is after last_online' do
        temp_json = { 'last_online' => (Time.now - 5).to_s, 'last_rebooted' => (Time.now - 2).to_s }
        device = '12345'
        offline_reboot_time = 4
        expect(IdeviceUtils).not_to receive(:reboot)
        device_thread.reboot_if_still_offline(device, temp_json, offline_reboot_time)
      end

      it 'does not reboot if reboot_days_count is already 3' do
        temp_json = {
          'last_online' => (Time.now - 5).to_s,
          'last_rebooted' => (Time.now - 6).to_s,
          'first_reboot_day' => (Date.today - 2).to_s,
          'reboot_days_count' => 3
        }
        device = '12345'
        offline_reboot_time = 4
        expect(IdeviceUtils).not_to receive(:reboot)
        device_thread.reboot_if_still_offline(device, temp_json, offline_reboot_time)
      end

      it 'does not reboot if last_rebooted is before offline_reboot_time' do
        temp_json = { 'last_online' => (Time.now - 5).to_s, 'last_rebooted' => (Time.now - 3).to_s }
        device = '12345'
        offline_reboot_time = 4
        expect(IdeviceUtils).not_to receive(:reboot)
        device_thread.reboot_if_still_offline(device, temp_json, offline_reboot_time)
      end

      it 'does not reboot if today device is already rebooted' do
        temp_json = {
          'last_online' => (Time.now - 5).to_s,
          'last_rebooted' => (Time.now - 6).to_s,
          'first_reboot_day' => Date.today.to_s,
          'reboot_days_count' => 1
        }
        device = '12345'
        offline_reboot_time = 4
        expect(IdeviceUtils).not_to receive(:reboot)
        device_thread.reboot_if_still_offline(device, temp_json, offline_reboot_time)
      end
    end
  end

  describe '#update_last_online' do
    it 'converts int datetime values to strings' do
      now = Time.now
      temp_json = { 'online' => false, 'last_online' => now.to_i }
      device_thread.update_last_online(temp_json)
      expect(temp_json['last_online']).to eql now.to_s
    end

    it 'leaves strings alone' do
      now = Time.now
      temp_json = { 'online' => false, 'last_online' => now.to_s }
      device_thread.update_last_online(temp_json)
      expect(temp_json['last_online']).to eql now.to_s
    end
  end

  describe '#wda_xctestrun_checks' do
    let(:wda_xctestrun_file) { "/tmp/wda_xctestrun_file" }
    let(:wda_app_folder) { "/tmp/wda_app_folder" }

    it 'checks fixes the xctestrun files' do
      wda_ver = double("wda_version")
      # This is called for each appium version
      expect(WDAVersion).to receive(:new).and_return(wda_ver).twice
      expect(BrowserStack::XcodeUtils).to receive(:get_platform_version).twice
      expect(wda_ver).to receive(:xctestrun_file_template).twice
      expect(wda_ver).to receive(:xctestrun_fixup).twice
      device_thread.wda_xctestrun_checks
    end
  end

  describe '#ppuid_independent_checks' do
    context 'for a new device connected' do
      subject { BrowserStack::DeviceThread.new({}, device, false, 8082, 27_755, 8402, :redis_client) }
      let(:orientation_lock_file) { "/tmp/orientation_lock_opened_#{device}" }

      before do
        cd = BrowserStack::CheckDevice
        FileUtils.rm_f(orientation_lock_file)
        # Functions do not need checking during rotation lock check
        allow_any_instance_of(cd).to receive(:check_device_supervised)
        allow_any_instance_of(cd).to receive(:check_battery)
        allow_any_instance_of(cd).to receive(:check_jailbroken)
        allow_any_instance_of(cd).to receive(:check_device_date)
        allow_any_instance_of(cd).to receive(:check_developer_image_mounted)
        allow_any_instance_of(cd).to receive(:check_developer_symbols)
        allow_any_instance_of(cd).to receive(:verify_iproxy_version_for_device)
        allow_any_instance_of(cd).to receive(:ensure_privoxy_running_plist)
        allow_any_instance_of(cd).to receive(:ensure_ios_webkit_debug_proxy_running_plist)
        allow_any_instance_of(cd).to receive(:devtools_proxy_server_running?)
      end

      it 'should create orientation lock file' do
        subject.ppuid_independent_checks
        expect(File.exist?(orientation_lock_file)).to be true
      end
    end
  end

  describe '#ppuid_dependent_checks' do
    let(:device_thread) { BrowserStack::DeviceThread.new(config_json, device, false, 8082, 27_755, 8402, :redis_client) }
    let(:ppuid_file) { device_state.send(:ppuid_file) }
    let(:install_check) { double('mock_install_check') }
    let(:cd) { double('mock_check_device') }
    let(:config) do
      { "port" => 8080 }
    end

    before do
      allow(BrowserStack::CheckDevice).to receive(:new).and_return(cd)

      # Functions do not need checking during rotation lock check
      allow(cd).to receive(:ensure_webdriveragentrunner_version)
      allow(cd).to receive(:ensure_xcodebuild_running)
      allow(cd).to receive(:check_device_internet)
      allow(cd).to receive(:check_insecure_websocket_proxy_map)
      allow(cd).to receive(:check_mdm_server)
      allow(cd).to receive(:check_mdm_settings)
      allow(cd).to receive(:check_internet_sharing)

      File.open(ppuid_file, 'a') do |f|
        f << "line 1\nline 2\n#{provision_profile}" # valid ppuid file needs 3 lines
      end

      mock_appium_server = instance_double('BrowserStack::AppiumServer', driver: nil, start_server_for_version: nil)
      allow(BrowserStack::AppiumServer).to receive(:new).and_return(mock_appium_server)
      allow(mock_appium_server).to receive(:running?).and_return(true)
      allow(Utils).to receive(:get_web_driver_agent_xctestrun_file).and_return("not nil")
    end

    after do
      File.truncate(ppuid_file, 0)
    end

    context 'install-phase is triggered' do
      it 'makes device offline if install-phase is already running' do
        expect(device_thread).to receive(:install_phase_running?).and_return(true)

        offline_hash = { 'offline_reason' => 'install-phase is still running',
                         'online' => false }
        expect(device_thread.ppuid_dependent_checks(config)).to eq(offline_hash)
      end

      it 'run installer if there is any installation is required' do
        allow(BrowserStackAppHelper).to receive(:cache_as_test_app).and_return(nil)
        allow(BrowserStackAppHelper).to receive(:cache_browserstack_app).and_return(nil)
        expect(device_thread).to receive(:install_phase_running?).and_return(false)
        expect(InstallCheck).to receive(:new)
          .with(device, any_args, provision_profile, config_json, full_check: anything)
          .and_return(install_check)
        install_phase_requirements = ['some installation required', 'some other component'].to_set
        expect(install_check).to receive(:check_requirements).and_return(install_phase_requirements)

        offline_reason = "Triggered install-phase due to reason: #{install_phase_requirements}"
        expect(device_thread).to receive(:trigger_install_phase).with(install_phase_requirements.first, provision_profile).and_return(offline_reason)

        offline_hash = {
          'offline_reason' => offline_reason,
          'online' => false
        }
        expect(device_thread.ppuid_dependent_checks(config)).to eq(offline_hash)
      end

      context 'install-phase is not triggered' do
        it "do not run installer if there isn't any installation required" do
          allow(BrowserStackAppHelper).to receive(:cache_as_test_app).and_return(nil)
          allow(BrowserStackAppHelper).to receive(:cache_browserstack_app).and_return(nil)
          expect(device_thread).to receive(:install_phase_running?).and_return(false)
          expect(InstallCheck).to receive(:new)
            .with(device, any_args, provision_profile, config_json, full_check: anything)
            .and_return(install_check)
          expect(install_check).to receive(:check_requirements).and_return(Set.new)

          expect(device_thread).not_to receive(:trigger_install_phase)

          expect(device_thread.ppuid_dependent_checks(config)).to eq({})
        end
      end
    end
  end

  describe '#install_phase_running?' do
    it 'should return true if DeviceForkExecutor is running' do
      expect(DeviceForkExecutor).to receive(:new)
        .with(device, INSTALL_PHASE[:name], STATE_FILES_DIR, BrowserStack.logger, any_args)
        .and_return(device_fork_executor)
      expect(device_fork_executor).to receive(:process_running?).and_return(true)
      expect(device_thread).to receive(:issue_cleanup_if_install_phase_is_stuck)

      expect(device_thread.install_phase_running?).to be_truthy
    end

    it 'should return false if DeviceForkExecutor is not running' do
      expect(DeviceForkExecutor).to receive(:new)
        .with(device, INSTALL_PHASE[:name], STATE_FILES_DIR, BrowserStack.logger, any_args)
        .and_return(device_fork_executor)
      expect(device_fork_executor).to receive(:process_running?).and_return(false)
      expect(device_thread).not_to receive(:issue_cleanup_if_install_phase_is_stuck)

      expect(device_thread.install_phase_running?).to be_falsey
    end
  end

  describe '#issue_cleanup_if_install_phase_is_stuck' do
    let(:install_phase_timeout) { 100 }

    context 'running time is greater than timeout' do
      it 'should kill the process & send the device to cleanup' do
        stub_const('INSTALL_PHASE', { timeout: install_phase_timeout })
        expect(fork_executor).to receive(:started_at).and_return(0)
        # Greater than timeout
        expect(Time).to receive(:now).and_return(101)

        expect(fork_executor).to receive(:reset!)
        expect(device_thread).to receive(:send_cleanup).with('install-phase is stuck')

        device_thread.issue_cleanup_if_install_phase_is_stuck(fork_executor)
      end
    end

    context 'running time is less than timeout' do
      it 'should not kill the process' do
        stub_const('INSTALL_PHASE', { timeout: install_phase_timeout })
        expect(fork_executor).to receive(:started_at).and_return(0)
        # Greater than timeout
        expect(Time).to receive(:now).and_return(99)

        expect(fork_executor).not_to receive(:reset!)
        expect(device_thread).not_to receive(:send_cleanup)

        device_thread.issue_cleanup_if_install_phase_is_stuck(fork_executor)
      end
    end
  end

  describe '#trigger_install_phase' do
    let(:installer) { double('installer') }
    let(:component_to_install) { 'com.google.chrome.ios' }
    before(:each) do
      expect(DeviceForkExecutor).to receive(:new)
        .with(device, INSTALL_PHASE[:name], STATE_FILES_DIR, any_args)
        .and_return(device_fork_executor)
    end
    context 'process spawned' do
      it 'should trigger install-phase' do
        allow(device_fork_executor).to receive(:run_once) do |&block|
          expect(Installer).to receive(:new).and_return(installer)
          expect(installer).to receive(:trigger_install)
          block.call
          [true, { pid: 200, started_at: 123 }]
        end

        expect(device_thread.trigger_install_phase(component_to_install, provision_profile))
          .to eq("Triggered install-phase due to reason: #{component_to_install} installation")
      end
    end

    context do
      it 'should not trigger install-phase' do
        allow(device_fork_executor).to receive(:run_once) do |&block|
          expect(Installer).to receive(:new).and_return(installer)
          expect(installer).to receive(:trigger_install)
          block.call
          [false, { pid: 404, started_at: 0 }]
        end

        expect(device_thread.trigger_install_phase(component_to_install, provision_profile))
          .to eq("Tried to trigger another install-phase")
      end
    end
  end

  describe "#push_offline_reason_to_zombie" do
    it "should not push to zombie if device is online" do
      expect(BrowserStack::Zombie).not_to receive(:push_logs)
      device_thread.push_offline_reason_to_zombie({ "online" => true })
    end

    it "should skip the push when its not the turn" do
      expect(BrowserStack::Zombie).not_to receive(:push_logs)
      device_thread.push_offline_reason_to_zombie({ "online" => false, "device_check_count" => OFFLINE_ZOMBIE_PUSH_INTERVAL })
    end

    it "should skip the push when device is under cleaning without any cleanup failures" do
      expect(BrowserStack::Zombie).not_to receive(:push_logs)
      temp_json = {
        "online" => false,
        "device_check_count" => OFFLINE_ZOMBIE_PUSH_INTERVAL + 1,
        "offline_reason" => "Device under cleaning"
      }
      device_thread.push_offline_reason_to_zombie(temp_json)
    end

    it "should skip the push when offline reason is from the list of excluded reasons" do
      expect(BrowserStack::Zombie).not_to receive(:push_logs)
      temp_json = {
        "online" => false,
        "device_check_count" => OFFLINE_ZOMBIE_PUSH_INTERVAL + 1,
        "offline_reason" => "Moved to k8s"
      }
      device_thread.push_offline_reason_to_zombie(temp_json)
    end

    it "should push the correct offline reason to BQ" do
      expect(BrowserStack::Zombie).to receive(:push_logs).with("mobile_offline_reason", "off adb",  { "device" => device, "data" => "device_name" })
      temp_json = {
        "online" => false,
        "device_check_count" => OFFLINE_ZOMBIE_PUSH_INTERVAL + 1,
        "offline_reason" => "off adb"
      }
      device_thread.instance_variable_set(:@device_name, "device_name")
      device_thread.push_offline_reason_to_zombie(temp_json)
    end

    it "should push the cleanup failure reason if present to BQ" do
      expect(BrowserStack::Zombie).to receive(:push_logs).with("mobile_offline_reason", "Device not on ADB: not connected",  { "device" => device, "data" => "device_name" })
      temp_json = {
        "online" => false,
        "device_check_count" => OFFLINE_ZOMBIE_PUSH_INTERVAL + 1,
        "offline_reason" => "Device under cleaning",
        "cleanup_failure_reason" => "Device not on ADB: not connected"
      }
      device_thread.instance_variable_set(:@device_name, "device_name")
      device_thread.push_offline_reason_to_zombie(temp_json)
    end
  end

  describe '#run' do
    let(:provisioning_manager) { double('mock_provisioning_manager') }
    let(:ppuid_file) { double('mock_ppuid_file') }
    let(:profile_config) { JSON.parse('{"production_61_22112023": {"ppuid": "MYPPUIDHASH", "team_id": "MYTEAM"}}') }

    before do
      allow(device_thread).to receive(:ppuid_file).and_return(ppuid_file)
      allow(ppuid_file).to receive(:contents).and_return(['production_61_22112023', 'MYTEAM', 'MYPPUIDHASH'])
      allow(ProvisioningManager).to receive(:read_profile_config).and_return(profile_config)
      allow(ProvisioningManager).to receive(:new).and_return(provisioning_manager)
      allow(provisioning_manager).to receive(:problem_detected?).and_return(false)
      allow(provisioning_manager).to receive(:update)

      # Nothing failing
      allow(device_thread).to receive(:check_connected).and_return(true)
      allow(device_thread).to receive(:write_inventory_data)
      allow(device_thread).to receive(:write_inventory_data)
      allow(device_thread).to receive(:ppuid_dependent_checks).and_return({})
      allow(device_thread).to receive(:ppuid_independent_checks)
      allow(device_thread).to receive(:wda_xctestrun_checks).and_return({})
      allow(device_thread).to receive(:push_offline_reason_to_zombie)
      allow(device_thread).to receive(:first_device_thread?).and_return(false)
      allow(ppuid_file).to receive(:branch_name)
    end

    context 'for a not new device' do
      let(:orientation_lock_file) { "/tmp/orientation_lock_opened_#{device}" }
      before { FileUtils.rm_f(orientation_lock_file) }

      it "doesn't touch file to open rotation lock" do
        device_thread.ppuid_independent_checks
        expect(File.exist?(orientation_lock_file)).to be false
      end

      it "doesn't schedule any cleanups" do
        expect(device_thread.cleanup_requested?).to be false
      end
    end

    context 'for a upgraded device' do
      let(:device_build_version) { 'NEW_BUILD_VERSION' }
      let(:upgraded_device_check_state) { BrowserStack::UpgradedDeviceCheckState.new(device) }
      let(:test_flight_automation_double) { double(Automation::TestFlight) }

      before do
        allow(Automation::TestFlight).to receive(:new).and_return(test_flight_automation_double)
        allow(test_flight_automation_double).to receive(:uninstall)
        allow(device_thread).to receive(:get_device_info).and_return(["111111111111", "11.4", device_build_version, "11:11:11:11:11:11", ""])
        allow(BrowserStack::UpgradedDeviceCheckState).to receive(:new).with(device).and_return(upgraded_device_check_state)
      end

      it "should notify on BQ" do
        expect(device_thread).to receive(:notify_device_upgraded)
        device_thread.run
      end

      it "should update the config with new Build Version" do
        new_json = device_thread.run
        expect(new_json['device_build_version']).to eq(device_build_version)
      end

      it "should call discard old states for upgraded device" do
        expect(upgraded_device_check_state).to receive(:discard_old_states)
        device_thread.run
      end
    end

    context 'for a brand new device' do
      before do
        @original_config = device_thread.instance_variable_get :@config
        device_thread.instance_variable_set :@config, nil
        allow(device_thread).to receive(:send_cleanup)
      end

      it 'schedules a cleanup' do
        device_thread.run
        expect(device_thread.cleanup_requested?).to be true
      end

      after do
        # Restore the stubbing
        device_thread.instance_variable_set :@config, @original_config
      end
    end

    context 'for an unclean off usb phone' do
      before do
        allow(device_thread)
          .to receive(:check_connected)
          .and_raise(MobileCheckException.new('unclean device not on ideviceinfo'))
      end

      it 'skips checks and marks the phone offline' do
        expect(device_thread).not_to receive(:ppuid_dependent_checks)
        expect(device_thread).not_to receive(:ppuid_independent_checks)
        expect(device_thread).not_to receive(:wda_xctestrun_checks)
        temp_json = device_thread.run
        expect(temp_json['online']).to be false
        expect(temp_json['offline_reason']).to be_eql 'unclean device not on ideviceinfo'
      end
    end

    context 'for a device_in_use' do
      let(:manual_cleanup_file) { device_state.send(:manual_cleanup_file) }
      before do
        FileUtils.touch manual_cleanup_file
        allow(device_thread)
          .to receive(:device_in_use?)
          .and_return(true)
      end

      it 'skips checks and marks the phone offline' do
        expect(device_thread).not_to receive(:ppuid_dependent_checks)
        expect(device_thread).not_to receive(:ppuid_independent_checks)
        expect(device_thread).not_to receive(:wda_xctestrun_checks)
        temp_json = device_thread.run
        expect(temp_json['online']).to be false
        expect(temp_json['offline_reason']).to be_eql 'manual cleanup'
      end
    end

    describe 'cleanup in reserved flow' do
      context 'when reserved flow expired/not_expired' do
        it 'device should be marked offline if minimized_cleanup_reserved_file_older_than_minutes? returns true' do
          expect(device_state).to receive(:minimized_cleanup_reserved_file_present?).and_return(true)
          expect(device_state).to receive(:minimized_cleanup_reserved_file_older_than_minutes?).and_return true
          expect(device_state).to receive(:remove_minimized_cleanup_reserved_file)
          expect(device_state).to receive(:remove_preserve_app_state_reserved_file)
          expect(device_thread).to receive(:push_logs_to_zombie)

          temp_json = device_thread.run
          expect(temp_json).to include({ "online" => false,
                                         "offline_reason" => 'unclean_reserved_device_needs_releasing' })
        end

        it 'device should not be marked offline if minimized_cleanup_reserved_file_older_than_minutes? returns false' do
          expect(device_state).to receive(:minimized_cleanup_reserved_file_present?).and_return(true)
          expect(device_state).to receive(:minimized_cleanup_reserved_file_older_than_minutes?).and_return false
          expect(device_state).to_not receive(:remove_minimized_cleanup_reserved_file)
          expect(device_state).to_not receive(:remove_preserve_app_state_reserved_file)
          expect(device_thread).to_not receive(:push_logs_to_zombie)

          temp_json = device_thread.run
          expect(temp_json).to_not include({ "online" => false,
                                             "offline_reason" => 'unclean_reserved_device_needs_releasing' })
        end
      end
    end

    context 'for an unclean phone that requested setup' do
      let(:cleanup_file) { device_state.send(:cleanup_requested_file) }
      let(:setup_file) { device_thread.setup_requested_file }

      before do
        FileUtils.touch setup_file
        FileUtils.touch cleanup_file
      end

      after do
        FileUtils.rm setup_file if File.exist? setup_file
        FileUtils.rm cleanup_file if File.exist? cleanup_file
      end

      it 'fixes it and then cleans it, not letting it go online' do
        expect(device_thread).to receive(:ppuid_dependent_checks).and_return({})
        expect(device_thread).to receive(:ppuid_independent_checks)
        expect(device_thread).to receive(:wda_xctestrun_checks).and_return({})
        expect(device_thread).to receive(:send_cleanup).with('unclean device file present')
        temp_json = device_thread.run
        expect(File.exist?(setup_file)).to be false # Setup done
        expect(File.exist?(cleanup_file)).to be true # Cleanup requested, but not done
        expect(temp_json['online']).to be false
        expect(temp_json['offline_reason']).to be_eql('Device under cleaning')
      end
    end

    context 'when a cleanup was requested' do
      let(:cleanup_file) { device_state.send(:cleanup_requested_file) }
      before { FileUtils.touch cleanup_file }
      after { FileUtils.rm cleanup_file if File.exist? cleanup_file }

      it 'requests one to the cleanup server, marks the phone offline, and leaves the file' do
        expect(device_thread).to receive(:send_cleanup).with('unclean device file present')
        temp_json = device_thread.run
        expect(temp_json['online']).to be false
        expect(temp_json['offline_reason']).to be_eql('Device under cleaning')
        expect(File.exist?(cleanup_file)).to be true
      end
    end

    context 'when device cleanup failed' do
      let(:cleanup_done_file) { device_thread.cleanup_done_file }

      before do
        FileUtils.touch cleanup_done_file
        allow(device_thread).to receive(:cleanup_failure_reason).and_return("cleanup failed")
      end

      after do
        FileUtils.rm cleanup_done_file if File.exist? cleanup_done_file
      end

      it 'adds cleanup failure reason to config json' do
        temp_json = device_thread.run
        expect(temp_json['cleanup_failure_reason']).to be_eql('cleanup failed')
      end
    end

    context 'when a setup was requested' do
      let(:setup_file) { device_thread.setup_requested_file }

      before { FileUtils.touch(setup_file) }
      after { FileUtils.rm_f(setup_file) }

      it 'will set up the phone and delete the file' do
        expect(device_thread).to receive(:ppuid_dependent_checks).and_return({})
        expect(device_thread).to receive(:ppuid_independent_checks)
        expect(device_thread).to receive(:wda_xctestrun_checks).and_return({})
        device_thread.run
        expect(File.exist?(setup_file)).to be false
      end

      context 'when device needs provisioning' do
        let(:needs_provisioning_file) { device_state.send(:needs_provisioning_file) }

        before { FileUtils.touch(needs_provisioning_file) }
        after { FileUtils.rm_f(needs_provisioning_file) }

        context 'when @device_check_lock = true' do
          before { device_thread.instance_variable_set(:@device_check_lock, true) }
          after { device_thread.instance_variable_set(:@device_check_lock, false) }

          it 'runs provisioning check and updates if @device_check_lock = true' do
            expect(provisioning_manager).to receive(:problem_detected?).and_return(true)
            expect(provisioning_manager).to receive(:update)
            expect(device_state).to receive(:remove_needs_provisioning_file)
            device_thread.run
          end
        end

        it 'sends device offline if @device_check_lock = false' do
          expect(provisioning_manager).not_to receive(:problem_detected?)
          temp_json = device_thread.run
          reason = 'provisioning: waiting for device check lock'
          expect(temp_json).to include({ "online" => false, "offline_reason" => reason })
        end
      end
    end

    context 'when @device_check_lock = true' do
      before { device_thread.instance_variable_set(:@device_check_lock, true) }
      after { device_thread.instance_variable_set(:@device_check_lock, false) }

      it 'runs provisioning check and updates profile if problem detected' do
        expect(provisioning_manager).to receive(:problem_detected?).and_return(true)
        expect(provisioning_manager).to receive(:update)
        expect(device_state).to receive(:remove_needs_provisioning_file)
        device_thread.run
      end

      it 'runs provisioning check and does nothing if no problem detected' do
        expect(provisioning_manager).to receive(:problem_detected?).and_return(false)
        expect(provisioning_manager).not_to receive(:update)
        expect(device_state).to receive(:remove_needs_provisioning_file)
        device_thread.run
      end
    end

    context 'when rotate certificate file is present' do
      before { device_state.touch_rotate_certificate_file }
      after { device_state.remove_rotate_certificate_file }

      it 'runs provisioning check and updates profile if problem detected' do
        expect(provisioning_manager).to receive(:problem_detected?).and_return(true)
        expect(provisioning_manager).to receive(:update)
        expect(device_state).to receive(:remove_needs_provisioning_file)
        device_thread.run
      end
    end

    context 'when no cleanup was requested' do
      it "won't clean up the phone" do
        expect(device_thread).not_to receive(:send_cleanup)
        device_thread.run
      end
    end
  end

  describe "#check_connected" do
    let(:recover_device) { double("recover_device") }
    let(:unclean_not_on_ideviceinfo_file) { "#{STATE_FILES_DIR}/unclean_not_on_ideviceinfo_#{device}" }
    let(:unclean_lockdown_file) { "/tmp/unclean_lockdown_#{device}" }

    before(:each) do
      allow(RecoverDevice).to receive(:new).with(device, ecid).and_return(recover_device)
    end

    context "when device is on usb" do
      it "should not call cleanup when needed" do
        expect(recover_device).to receive(:attempt).and_return(nil)
        expect(device_thread).to receive(:needs_cleanup?).and_return(true)
        expect(device_thread).to receive(:clean_recovered_device).and_return(nil)
        expect { device_thread.check_connected }.to raise_error(MobileCheckException, "cleaning recovered unclean device")
      end

      it "should not call cleanup if device was not in cleanup when then went off usb" do
        expect(recover_device).to receive(:attempt).and_return(nil)
        expect(device_thread).to receive(:needs_cleanup?).and_return(false)
        expect(device_thread).not_to receive(:clean_recovered_device)
        expect { device_thread.check_connected }.not_to raise_error
      end
    end

    context "when device is not on usb" do
      context "unclean" do
        it "should handle cleanup file when device in lockdown" do
          expect(recover_device).to receive(:attempt).and_raise(RecoverDevice::DeviceInLockdown.new("deivce is in lockdown"))
          expect(device_thread).not_to receive(:needs_cleanup?)
          expect(File).to receive(:exists?).with(unclean_not_on_ideviceinfo_file).and_return(true)
          expect(FileUtils).to receive(:rm).with(unclean_not_on_ideviceinfo_file)
          expect(FileUtils).to receive(:touch).with(unclean_lockdown_file)

          expect { device_thread.check_connected }.to raise_error(MobileCheckException, "deivce is in lockdown")
        end

        it "should handle cleanup file when not on usb" do
          expect(recover_device).to receive(:attempt).and_raise(RecoverDevice::DeviceNotOnUSB.new("deivce is not on usb"))
          expect(device_thread).not_to receive(:needs_cleanup?)
          expect(File).to receive(:exists?).with(unclean_lockdown_file).and_return(true)
          expect(FileUtils).to receive(:rm).with(unclean_lockdown_file)
          expect(FileUtils).to receive(:touch).with(unclean_not_on_ideviceinfo_file)

          expect { device_thread.check_connected }.to raise_error(MobileCheckException, "deivce is not on usb")
        end

        it "should handle cleanup file when device is not on ideviceinfo" do
          expect(recover_device).to receive(:attempt).and_raise(RecoverDevice::DeviceNotOnIdeviceInfo.new("deivce is not on ideviceinfo"))
          expect(device_thread).not_to receive(:needs_cleanup?)
          expect(File).to receive(:exists?).with(unclean_lockdown_file).and_return(true)
          expect(FileUtils).to receive(:rm).with(unclean_lockdown_file)
          expect(FileUtils).to receive(:touch).with(unclean_not_on_ideviceinfo_file)

          expect { device_thread.check_connected }.to raise_error(MobileCheckException, "deivce is not on ideviceinfo")
        end
      end

      context "clean" do
        before(:each) do
          expect(device_thread).not_to receive(:needs_cleanup?)
        end

        it "should not handle cleanup file when device in lockdown" do
          expect(recover_device).to receive(:attempt).and_raise(RecoverDevice::DeviceInLockdown.new("deivce is in lockdown"))
          expect(File).to receive(:exists?).with(unclean_not_on_ideviceinfo_file).and_return(false)
          expect(FileUtils).not_to receive(:rm).with(unclean_not_on_ideviceinfo_file)
          expect(FileUtils).not_to receive(:touch).with(unclean_lockdown_file)

          expect { device_thread.check_connected }.to raise_error(MobileCheckException, "deivce is in lockdown")
        end

        it "should not handle cleanup file when not on usb" do
          expect(recover_device).to receive(:attempt).and_raise(RecoverDevice::DeviceNotOnUSB.new("deivce is not on usb"))
          expect(File).to receive(:exists?).with(unclean_lockdown_file).and_return(false)
          expect(FileUtils).not_to receive(:rm).with(unclean_lockdown_file)
          expect(FileUtils).not_to receive(:touch).with(unclean_not_on_ideviceinfo_file)

          expect { device_thread.check_connected }.to raise_error(MobileCheckException, "deivce is not on usb")
        end

        it "should not handle cleanup file when device is not on ideviceinfo" do
          expect(recover_device).to receive(:attempt).and_raise(RecoverDevice::DeviceNotOnIdeviceInfo.new("deivce is not on ideviceinfo"))
          expect(File).to receive(:exists?).with(unclean_lockdown_file).and_return(false)
          expect(FileUtils).not_to receive(:rm).with(unclean_lockdown_file)
          expect(FileUtils).not_to receive(:touch).with(unclean_not_on_ideviceinfo_file)

          expect { device_thread.check_connected }.to raise_error(MobileCheckException, "deivce is not on ideviceinfo")
        end
      end
    end
  end
end
