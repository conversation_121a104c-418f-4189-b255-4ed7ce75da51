require_relative '../spec_helper'
require_relative '../../scripts/wda_download'
require_relative '../../config/constants'

describe WdaDownload do
  let(:subject) { WdaDownload.new }
  let(:device_config) { fixture('full_config.json') }
  let(:device_id) { '00008030-000A65590183802E' }

  before do
    allow(File).to receive(:read).and_call_original
    allow(File).to receive(:read).with(CONFIG_JSON_FILE).and_return(device_config)
    allow(WDAVersion).to receive(:initialize_logger)
    allow(BrowserStack::OSUtils).to receive(:macos_version).and_return('10.14.5')
  end

  describe '#download_and_install' do
    context 'when devices: [], appiums: []' do
      it 'should download all appiums for all devices' do
        # 7 devices in device config fixture file, 5 appiums in sample server config
        # So, 7 * 5 = 35 calls
        d = double
        expect(WDAVersion).to receive(:new).exactly(35).times.and_return(d)
        expect(d).to receive(:install_wda_version).exactly(35).times
        subject.download_and_install
      end
    end

    context 'when devices: [udid], appiums: []' do
      let(:udid) { '00008030-000A65590183802E' }

      it 'should only force download download appiums for specified device' do
        # 5 appiums in sample server config
        d = double
        expect(WDAVersion).to receive(:new).with(udid, anything, anything).exactly(5).times.and_return(d)
        expect(d).to receive(:install_wda_version).exactly(5).times

        subject = WdaDownload.new(devices: [udid])
        subject.download_and_install
      end
    end

    context 'when devices: [], appiums: [\'1.8.0\']' do
      let(:appium) { '1.8.0' }

      it 'should only download specified appiums for devices' do
        # 7 devices in fixture device config
        d = double
        expect(WDAVersion).to receive(:new).with(anything, appium, anything).exactly(7).times.and_return(d)
        expect(d).to receive(:install_wda_version).exactly(7).times

        subject = WdaDownload.new(appiums: [appium])
        subject.download_and_install
      end
    end

    context 'when devices: [udid], appiums: [\'1.14.0\']' do
      let(:udid) { '00008030-000A65590183802E' }
      let(:appium) { '1.8.0' }

      it 'should only download for specified appium and device and use specified wda' do
        d = double
        expect(WDAVersion).to receive(:new).with(udid, appium, anything).exactly(1).times.and_return(d)
        expect(d).to receive(:install_wda_version).exactly(1).times

        subject = WdaDownload.new(devices: [udid], appiums: [appium])
        subject.download_and_install
      end
    end
  end

  describe '#ios_version' do
    context 'when device is not in config' do
      it 'gets ios version from ideviceinfo' do
        expect(BrowserStack::OSUtils).to receive(:device_ios_version).and_return('12.1')
        subject.ios_version('1234')
      end
    end

    context 'when device is in config' do
      it 'gets ios version from config (not ideviceinfo)' do
        expect(BrowserStack::OSUtils).not_to receive(:device_ios_version)
        subject.ios_version('00008030-001611080C05802E')
      end
    end
  end
end
