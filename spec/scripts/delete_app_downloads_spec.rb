require 'date'

require 'json'
require_relative '../spec_helper'
require_relative '../../scripts/delete_app_downloads'
require_relative '../../config/constants'
require_relative "../../lib/utils/osutils"

describe BrowserStack::DeleteAppDownloads do
  let(:identifier) { "identifier" }
  let(:certname) { "certname" }
  let(:base_path) { "/tmp/apps" }
  let(:common_apps_folder) { "#{base_path}/#{identifier}_#{certname}/" }
  let(:common_apps_folder2) { "#{base_path}2/#{identifier}_#{certname}/" }

  before(:each) do
    expect(BrowserStack).to receive(:init_logger).and_return(true)

    # Required or tests will fail on second run.
    FileUtils.rm_rf(APPS_DOWNLOAD_FOLDER)
  end

  it "should delete files" do
    expect(FileUtils).to receive(:rm_f).with(/.complete$/).and_return(true)
    expect(FileUtils).to receive(:rm_f).with(/.starting$/).and_return(true)
    expect(FileUtils).to receive(:rm_f).with(/.session$/).exactly(2).times.and_return(true)
    expect(FileUtils).to receive(:rm_rf).and_return(true)

    BrowserStack::DeleteAppDownloads.new.delete_files("uuid", "common_app_folder", ["file1.session", "file2.session"])
  end

  shared_examples 'size_above_threshold?' do
    it 'should return if size limit is exceeded' do
      expect(BrowserStack::OSUtils).to receive(:execute).with("du -sh #{APPS_DOWNLOAD_FOLDER}").and_return("#{size} /tmp/apps")

      result = BrowserStack::DeleteAppDownloads.new.size_above_threshold?

      expect(result).to eq(expected_result)
    end
  end

  context "should return true if size is 6.5GB" do
    let(:expected_result) { true }
    let(:size) { "6.5G" }

    it_behaves_like 'size_above_threshold?'
  end

  context "should return true if the size format is unknown" do
    let(:expected_result) { true }
    let(:size) { "232P" }

    it_behaves_like 'size_above_threshold?'
  end

  context "should return false if size is 232KB" do
    let(:expected_result) { false }
    let(:size) { "232K" }

    it_behaves_like 'size_above_threshold?'
  end

  it "should delete files when no uuid folder found and all .starting files are >= 30 minutes older" do
    allow(File).to receive(:exist?).with(/.complete$/).and_return(false)
    allow(File).to receive(:exist?).with(/.starting$/).and_return(true)
    allow(Dir).to receive(:glob).and_return(["/tmp/session1_xctest_logs_stability", "/tmp/session2_xctest_logs_stability"], [common_apps_folder], [], ["uuid1.starting", "uuid2.starting"])
    allow(File).to receive(:mtime).and_return(Time.now - (13 * 60 * 60), Time.now - (13 * 60 * 60), Time.now - 31 * 60, Time.now - 40 * 60)
    expect(FileUtils).to receive(:rm_rf).exactly(3).times.and_return(true)

    BrowserStack::DeleteAppDownloads.new
  end

  it "should not delete files when no uuid folder found and atleast 1 .starting file is < 30 minutes older" do
    allow(Dir).to receive(:glob).and_return([common_apps_folder], [], ["uuid1.starting", "uuid2.starting"])
    allow(File).to receive(:mtime).and_return(Time.now - 31 * 60, Time.now - 20 * 60)
    expect_any_instance_of(BrowserStack::DeleteAppDownloads).to receive(:delete_stale_logs_stability_files).and_return(true)
    expect(FileUtils).not_to receive(:rm_rf)

    BrowserStack::DeleteAppDownloads.new
  end

  it "should delete files when uuid folder found, .complete file found, session files found with last updated timestamp > 24hrs" do
    allow(File).to receive(:exist?).with(/.complete$/).and_return(true)
    allow(Dir).to receive(:glob).and_return(
      [common_apps_folder],
      ["#{common_apps_folder}/uuid1/"],
      ["#{common_apps_folder}/sessionid_uuid1.session"]
    )
    expect_any_instance_of(BrowserStack::DeleteAppDownloads).to receive(:delete_stale_logs_stability_files).and_return(true)
    allow(File).to receive(:mtime).with(/.complete$/).and_return(Time.now - 25 * 60 * 60)
    expect_any_instance_of(BrowserStack::DeleteAppDownloads).to receive(:delete_files)

    BrowserStack::DeleteAppDownloads.new
  end

  context "when uuid folder found, .complete file found, session files not found" do
    before(:each) do
      allow(File).to receive(:exist?).with(/.complete$/).and_return(true)
      expect_any_instance_of(BrowserStack::DeleteAppDownloads).to receive(:delete_stale_logs_stability_files).and_return(true)
      allow(Dir).to receive(:glob).and_return(
        [common_apps_folder],
        ["#{common_apps_folder}/uuid1/"],
        []
      )
    end

    it "should delete files when last updated complete files is >= 1 hr" do
      allow(File).to receive(:mtime).with(/.complete$/).and_return(Time.now - 1 * 60 * 60)
      expect_any_instance_of(BrowserStack::DeleteAppDownloads).to receive(:delete_files)

      BrowserStack::DeleteAppDownloads.new
    end

    it "should delete files when size_above_threshold? returns true" do
      allow(File).to receive(:mtime).with(/.complete$/).and_return(Time.now - 30 * 60)
      allow_any_instance_of(BrowserStack::DeleteAppDownloads).to receive(:size_above_threshold?).and_return(true)
      expect_any_instance_of(BrowserStack::DeleteAppDownloads).to receive(:delete_files)

      BrowserStack::DeleteAppDownloads.new
    end
  end

  it "should delete files when uuid folder found, .complete file not found and .starting file older than 30 mins" do
    allow(File).to receive(:exist?).with(/.complete$/).and_return(false)
    allow(File).to receive(:exist?).with(/.starting$/).and_return(true)
    allow(Dir).to receive(:glob).and_return(
      [common_apps_folder],
      ["#{common_apps_folder}/uuid1/"]
    )
    allow(File).to receive(:mtime).with(/.starting$/).and_return(Time.now - 40 * 60)
    expect(FileUtils).to receive(:rm_f).and_return(true)
    expect(FileUtils).to receive(:rm_rf).and_return(true)
    expect_any_instance_of(BrowserStack::DeleteAppDownloads).to receive(:delete_stale_logs_stability_files).and_return(true)

    BrowserStack::DeleteAppDownloads.new
  end

  it "should continue iteration over apps even incase of exception for an app" do
    expect_any_instance_of(BrowserStack::DeleteAppDownloads).to receive(:delete_stale_logs_stability_files).and_return(true)
    allow(File).to receive(:exist?).with(/.complete$/).and_return(true)
    allow(Dir).to receive(:glob).and_return(
      [common_apps_folder, common_apps_folder2],
      ["#{common_apps_folder}/uuid1/"],
      [],
      ["#{common_apps_folder2}/uuid1/"],
      ["#{common_apps_folder}/sessionid_uuid1.session"]
    )
    expect(File).to receive(:mtime).with(/.complete$/).once.ordered.and_raise(RuntimeError, "File not found")
    expect(File).to receive(:mtime).with(/.complete$/).once.ordered.and_return(Time.now - 25 * 60 * 60)
    expect_any_instance_of(BrowserStack::DeleteAppDownloads).to receive(:delete_files)

    BrowserStack::DeleteAppDownloads.new
  end

  context "when no .complete, .starting files are found" do
    before(:each) do
      allow(File).to receive(:exist?).with(/.complete$/).and_return(false)
      allow(File).to receive(:exist?).with(/.starting$/).and_return(false)
      expect_any_instance_of(BrowserStack::DeleteAppDownloads).to receive(:delete_stale_logs_stability_files).and_return(true)
    end

    it "should delete file if no session files found" do
      allow(Dir).to receive(:glob).and_return(
        [common_apps_folder],
        ["#{common_apps_folder}/uuid1/"],
        []
      )
      allow(File).to receive(:mtime).and_return(Time.now - 1)
      expect_any_instance_of(BrowserStack::DeleteAppDownloads).to receive(:delete_files)

      BrowserStack::DeleteAppDownloads.new
    end

    it "should delete file if session files found but uuid dir older than 24 hours" do
      allow(Dir).to receive(:glob).and_return(
        [common_apps_folder],
        ["#{common_apps_folder}/uuid1/"],
        ["file1.session"]
      )
      allow(File).to receive(:mtime).and_return(Time.now - 25 * 60 * 60)
      expect_any_instance_of(BrowserStack::DeleteAppDownloads).to receive(:delete_files)

      BrowserStack::DeleteAppDownloads.new
    end
  end
end
