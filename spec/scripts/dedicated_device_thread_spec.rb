require 'json'

require_relative '../spec_helper'
require_relative '../../scripts/dedicated_device_thread'

describe BrowserStack::DedicatedDeviceThread do
  let(:device) { "1234" }
  let(:device_state) { DeviceState.new(device) }
  let(:config_json) { JSON.parse(fixture('config.json')) }
  let(:dedicated_video_rec_manager) { double('dedicated_video_rec_manager') }
  let(:static_conf_json) { JSON.parse(fixture('static_conf.json')) }
  let(:device_fork_executor) { double('device_fork_executor', { executor: nil }) }
  let(:fork_executor) { double('fork_executor', { pid: 404 }) }
  let(:provision_profile) { 'fake_provision_profile' }

  before do
    allow(DeviceState).to receive(:new).and_return(device_state)
    allow(DeviceManager).to receive(:is_first_cleanup?).and_return(false)
  end

  # TODO: this is assuming that we create an online phone. If we add specs, we
  # may need to test for offline phones and so on.
  let(:device_thread) do
    FileUtils.touch "/usr/local/.browserstack/config/ppuid_#{device}"
    BrowserStack::DedicatedDeviceThread.configure(
      "app",
      "*******",
      static_conf_json,
      "/usr/local/.browserstack/config",
      "/var/log/browserstack",
      "1.7.0",
      "/usr/local/.browserstack/appium_1.7.0_bstack",
      "/Users/<USER>/Library/Keychains/Browserstack.keychain-db",
      "the_g_____________pass",
      443,
      "hostname",
      "region",
      "subregion",
      400,
      "inventory.url.example.com",
      "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/DeviceSupport/",
      "/Users/<USER>/Library/Developer/Xcode/iOS DeviceSupport/",
      { "1.6.5" => "path_to_165",
        "1.7.0" => "path_to_170" },
      5,
      false,
      false,
      { "Proxy" => { "type" => "com.apple.proxy.http.global" } }
    )
    BrowserStack::DedicatedDeviceThread.new(
      config_json,
      device,
      false,
      8082,
      27_755,
      8402,
      :redis_client
    )
  end

  describe '#ppuid_independent_checks' do
    context 'dedicated cleanup' do
      before do
        cd = BrowserStack::CheckDevice
        allow_any_instance_of(cd).to receive(:check_device_supervised)
        allow_any_instance_of(cd).to receive(:check_battery)
        allow_any_instance_of(cd).to receive(:check_jailbroken)
        allow_any_instance_of(cd).to receive(:airplane_mode)
        allow_any_instance_of(cd).to receive(:check_device_date)
        allow_any_instance_of(cd).to receive(:check_developer_image_mounted)
        allow_any_instance_of(cd).to receive(:check_developer_symbols)
        allow_any_instance_of(cd).to receive(:check_media_backup)
        allow_any_instance_of(cd).to receive(:verify_device_firewall_rules)
        allow_any_instance_of(cd).to receive(:verify_iproxy_version_for_device)
        allow_any_instance_of(cd).to receive(:ensure_privoxy_running_plist)
        allow_any_instance_of(cd).to receive(:ensure_ios_webkit_debug_proxy_running_plist)
        allow_any_instance_of(cd).to receive(:devtools_proxy_server_running?)
        allow(dedicated_video_rec_manager).to receive(:write_session_video_file)
        allow(device_state).to receive(:dedicated_video_state_file_present?).and_return(true)
      end

      it 'should not call certain methods' do
        expect_any_instance_of(BrowserStack::CheckDevice).to_not receive(:check_all_contents_and_settings_erased)
        device_thread.ppuid_independent_checks
      end
    end
  end
end
