require 'json'

require_relative '../spec_helper'
require_relative '../../scripts/apple_tv_device_thread'

describe BrowserStack::AppleTVDeviceThread do
  let(:device) { "1234" }
  let(:device_state) { DeviceState.new(device) }
  let(:config_json) { JSON.parse(fixture('config.json')) }
  let(:static_conf_json) { JSON.parse(fixture('static_conf.json')) }
  let(:device_fork_executor) { double('device_fork_executor', { executor: nil }) }
  let(:fork_executor) { double('fork_executor', { pid: 404 }) }
  let(:provision_profile) { 'fake_provision_profile' }

  before do
    allow(DeviceState).to receive(:new).and_return(device_state)
    allow(DeviceManager).to receive(:is_first_cleanup?).and_return(false)
    FileUtils.touch "/usr/local/.browserstack/config/ppuid_#{device}"
    BrowserStack::AppleTVDeviceThread.configure(
      "app",
      "*******",
      static_conf_json,
      "/usr/local/.browserstack/config",
      "/var/log/browserstack",
      "1.7.0",
      "/usr/local/.browserstack/appium_1.7.0_bstack",
      "/Users/<USER>/Library/Keychains/Browserstack.keychain-db",
      "the_g_____________pass",
      443,
      "hostname",
      "region",
      "subregion",
      400,
      "inventory.url.example.com",
      "/Applications/Xcode.app/Contents/Developer/Platforms",
      "/Users/<USER>/Library/Developer/Xcode/iOS DeviceSupport/",
      { "1.6.5" => "path_to_165",
        "1.7.0" => "path_to_170" },
      5,
      false,
      false,
      { "Proxy" => { "type" => "com.apple.proxy.http.global" } }
    )
  end

  # TODO: this is assuming that we create an online phone. If we add specs, we
  # may need to test for offline phones and so on.
  let(:device_thread) do
    BrowserStack::AppleTVDeviceThread.new(
      config_json,
      device,
      false,
      8082,
      27_755,
      8402,
      :redis_client
    )
  end

  describe '#ppuid_independent_checks' do
    let(:cd) { BrowserStack::CheckDevice.new(device, :redis_client) }
    before(:each) do
      allow(device_thread).to receive(:dc).and_return(cd)
    end

    it 'calls correct methods to check with CheckDevice' do
      expect(cd).to receive(:check_all_contents_and_settings_erased)
      expect(cd).to receive(:check_platform_version_consistency)
      expect(cd).to receive(:check_device_supervised)
      expect(cd).to receive(:check_battery)
      expect(cd).to receive(:airplane_mode)
      expect(cd).to receive(:check_device_date)
      expect(cd).to receive(:check_developer_image_mounted)
      expect(cd).to receive(:verify_iproxy_version_for_device)
      expect(cd).to receive(:check_media_backup)
      expect(cd).to receive(:ensure_privoxy_running_plist)

      device_thread.ppuid_independent_checks
    end

    context 'for a new device connected' do
      let(:device_thread) { BrowserStack::AppleTVDeviceThread.new({}, device, false, 8082, 27_755, 8402, :redis_client) }
      let(:orientation_lock_file) { "/tmp/orientation_lock_opened_#{device}" }
      let(:cd) { BrowserStack::CheckDevice }

      before(:each) do
        FileUtils.rm_f(orientation_lock_file)
      end

      after(:each) do
        FileUtils.rm_f(orientation_lock_file)
      end

      it 'should create orientation lock file' do
        expect(cd).to receive(:check_all_contents_and_settings_erased)
        expect(cd).to receive(:check_platform_version_consistency)
        expect(cd).to receive(:check_device_supervised)
        expect(cd).to receive(:check_battery)
        expect(cd).to receive(:airplane_mode)
        expect(cd).to receive(:check_device_date)
        expect(cd).to receive(:check_developer_image_mounted)
        expect(cd).to receive(:verify_iproxy_version_for_device)
        expect(cd).to receive(:check_media_backup)
        expect(cd).to receive(:ensure_privoxy_running_plist)

        device_thread.ppuid_independent_checks
        expect(File.exist?(orientation_lock_file)).to be true
      end
    end
  end

  describe '#ppuid_dependent_checks' do
    let(:device_thread) { BrowserStack::AppleTVDeviceThread.new(config_json, device, false, 8082, 27_755, 8402, :redis_client) }
    let(:ppuid_file) { device_state.send(:ppuid_file) }
    let(:install_check) { double('mock_install_check') }
    let(:cd) { double('mock_check_device') }
    let(:config) do
      { "port" => 8080 }
    end

    before do
      allow(BrowserStack::CheckDevice).to receive(:new).and_return(cd)

      # Functions do not need checking during rotation lock check
      allow(cd).to receive(:ensure_appium_server_running)
      allow(cd).to receive(:ensure_webdriveragentrunner_version)
      allow(cd).to receive(:ensure_xcodebuild_running)
      allow(cd).to receive(:check_device_internet)
      allow(cd).to receive(:check_mdm_server)
      allow(cd).to receive(:check_mdm_settings)
      allow(cd).to receive(:check_internet_sharing)

      File.open(ppuid_file, 'a') do |f|
        f << "line 1\nline 2\n#{provision_profile}" # valid ppuid file needs 3 lines
      end

      mock_appium_server = instance_double('BrowserStack::AppiumServer', driver: nil, start_server_for_version: nil)
      allow(BrowserStack::AppiumServer).to receive(:new).and_return(mock_appium_server)
      allow(mock_appium_server).to receive(:running?).and_return(true)
      allow(Utils).to receive(:get_web_driver_agent_xctestrun_file).and_return("not nil")
    end

    after do
      File.truncate(ppuid_file, 0)
    end

    context 'install-phase is triggered' do
      it 'makes device offline if install-phase is already running' do
        expect(device_thread).to receive(:install_phase_running?).and_return(true)

        offline_hash = { 'offline_reason' => 'install-phase is still running',
                         'online' => false }
        expect(device_thread.ppuid_dependent_checks(config)).to eq(offline_hash)
      end

      it 'run installer if there is any installation is required' do
        expect(device_thread).to receive(:install_phase_running?).and_return(false)
        expect(InstallCheck).to receive(:new)
          .with(device, any_args, provision_profile, config_json, full_check: anything)
          .and_return(install_check)
        install_phase_requirements = ['some installation required', 'some other component'].to_set
        expect(install_check).to receive(:check_requirements).and_return(install_phase_requirements)

        offline_reason = "Triggered install-phase due to reason: #{install_phase_requirements}"
        expect(device_thread).to receive(:trigger_install_phase).with(install_phase_requirements.first, provision_profile).and_return(offline_reason)

        offline_hash = {
          'offline_reason' => offline_reason,
          'online' => false
        }
        expect(device_thread.ppuid_dependent_checks(config)).to eq(offline_hash)
      end

      context 'install-phase is not triggered' do
        it "do not run installer if there isn't any installation required" do
          expect(device_thread).to receive(:install_phase_running?).and_return(false)
          expect(InstallCheck).to receive(:new)
            .with(device, any_args, provision_profile, config_json, full_check: anything)
            .and_return(install_check)
          expect(install_check).to receive(:check_requirements).and_return(Set.new)

          expect(device_thread).not_to receive(:trigger_install_phase)

          expect(device_thread.ppuid_dependent_checks(config)).to eq({})
        end
      end
    end
  end
end
