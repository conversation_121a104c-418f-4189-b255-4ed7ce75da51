require 'json'
require_relative '../../scripts/screenshot_instrumentation_process'
require_relative '../spec_helper'
require_relative '../../config/constants'
require_relative "../../lib/utils/utils"

describe ScreenShotInstrumentation do
  before(:each) do
    expect(<PERSON>rowserStack).to receive(:init_logger).and_return(true)
  end

  it 'should successfully push feature usage screenshot and performance metrics' do
    screenshot_instrumentation = ScreenShotInstrumentation.new
    expect(File).to receive(:read).with("dummy").and_return("{\"session_id\": \"session_id\", \"genre\": \"app_automate\"}")
    expect(Utils).to receive(:get_screenshot_instrumentation_file).and_return("dummy_session")
    expect(File).to receive(:read).with("dummy_session").and_return(Utils.default_screenshot_instrumentation_data.to_json)
    expect_any_instance_of(UDPSocket).to receive(:send)
    expect(File).to receive(:mtime).with("dummy").and_return(Time.now)
    expect(Utils).to receive(:send_to_eds)
    expect(File).to receive(:exist?).with("dummy_session").and_return(true)
    expect(File).to receive(:delete).with("dummy_session")
    expect(File).to receive(:exist?).with("dummy_session.lock").and_return(true)
    expect(File).to receive(:delete).with("dummy_session.lock")

    screenshot_instrumentation.process("dummy")
  end

  it 'should successfully push performance metrics and not feature_metrics if file has passed 30 mins' do
    screenshot_instrumentation = ScreenShotInstrumentation.new
    expect(File).to receive(:read).with("dummy").and_return("{\"session_id\": \"session_id\", \"genre\": \"app_automate\"}")
    expect(Utils).to receive(:get_screenshot_instrumentation_file).and_return("dummy_session")
    expect(File).to receive(:read).with("dummy_session").and_return(Utils.default_screenshot_instrumentation_data.to_json)
    expect_any_instance_of(UDPSocket).to receive(:send)
    expect(File).to receive(:mtime).with("dummy").and_return(Time.now - 30 * 60)
    expect(Utils).not_to receive(:send_to_eds)
    expect(File).to receive(:exist?).with("dummy_session").and_return(true)
    expect(File).to receive(:delete).with("dummy_session")
    expect(File).to receive(:exist?).with("dummy_session.lock").and_return(true)
    expect(File).to receive(:delete).with("dummy_session.lock")

    screenshot_instrumentation.process("dummy")
  end
end
