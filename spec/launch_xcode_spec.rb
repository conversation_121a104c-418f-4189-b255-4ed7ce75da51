# frozen_string_literal: true

require_relative '../lib/launch_xcode'

describe LaunchXcode do
  before do
    allow(LaunchXcode).to receive(:launch_new_xcode)
    allow(LaunchXcode).to receive(:create_launch_error_lock_file)
  end

  describe '.xcode_running?' do
    it "returns true if the app is open" do
      fixture = "#{__dir__}/fixtures/ps_aux_xcode_opened.txt"
      allow(LaunchXcode).to receive(:ps_aux).and_return(File.read(fixture))
      expect(LaunchXcode.send(:xcode_running?)).to be true
    end

    it "returns false if only other xcode components are open" do
      fixture = "#{__dir__}/fixtures/ps_aux_xcode_not_opened.txt"
      allow(LaunchXcode).to receive(:ps_aux).and_return(File.read(fixture))
      expect(LaunchXcode.send(:xcode_running?)).to be false
    end
  end

  context 'When previous launch error and Xcode still not open' do
    before do
      allow(LaunchXcode).to receive(:previous_launch_error?).and_return(true)
      allow(LaunchXcode).to receive(:xcode_running?).and_return(false)
    end

    it 'refuses to launch xcode and raises and exception' do
      expect(LaunchXcode).not_to receive(:launch_new_xcode)
      expect { LaunchXcode.launch }.to raise_error(/Xcode failed to be launched previously/)
    end
  end

  context 'When no previous launch error' do
    before do
      allow(LaunchXcode).to receive(:previous_launch_error?).and_return(false)
    end

    context "When xcode is closed + it's running after launching it" do
      before do
        allow(LaunchXcode).to receive(:xcode_running?).and_return(false, true)
      end

      it 'tries to open xcode, no lock files created' do
        expect(LaunchXcode).to receive(:launch_new_xcode)
        expect(LaunchXcode).not_to receive(:create_launch_error_lock_file)
        LaunchXcode.launch
        LaunchXcode.launch
      end
    end

    context "When xcode is closed + it's not running after launching it" do
      before do
        allow(LaunchXcode).to receive(:xcode_running?).and_return(false, false)
      end

      it 'tries to open xcode, throws an exception, creates a lock file.' do
        expect(LaunchXcode).to receive(:launch_new_xcode)
        expect(LaunchXcode).to receive(:create_launch_error_lock_file)
        expect { LaunchXcode.launch }.to raise_error('Failed to launch Xcode')
      end
    end
  end
end
