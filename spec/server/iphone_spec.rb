require 'timecop'
require_relative '../spec_helper'
require_relative '../../server/iphone'

describe BrowserStack::IPhone do
  let(:device_config) do
    { 'device_version' => 11,
      'webdriver_port' => 8080,
      'selenium_port' => 8081,
      'debugger_port' => 8082,
      'device_name' => 'iPhone 11',
      'device_serial' => 'A3527427524' }
  end

  let(:device_config_ios13) do
    { 'device_version' => 13,
      'webdriver_port' => 8080,
      'selenium_port' => 8081,
      'debugger_port' => 8082,
      'device_name' => 'iPhone 11',
      'device_serial' => 'A3527427524' }
  end

  let(:device_config_ios14) do
    { 'device_version' => 14,
      'webdriver_port' => 8080,
      'selenium_port' => 8081,
      'debugger_port' => 8082,
      'device_name' => 'iPhone 11',
      'device_serial' => 'A3527427524' }
  end

  let(:device_config_ios16) do
    { 'device_version' => 16,
      'webdriver_port' => 8080,
      'selenium_port' => 8081,
      'debugger_port' => 8082,
      'device_name' => 'iPhone 11',
      'device_serial' => 'A3527427524' }
  end

  let(:uuid) { '000820-427503425' }
  let(:mock_appium_server) { double('appium_server') }
  let(:mock_abm) { double(AppleBusinessManagerHelper) }

  subject { BrowserStack::IPhone.new(device_config, uuid) }
  let(:iphone_ios13) { BrowserStack::IPhone.new(device_config_ios13, uuid) }

  let(:iphone_ios14) { BrowserStack::IPhone.new(device_config_ios14, uuid) }
  let(:iphone_ios16) { BrowserStack::IPhone.new(device_config_ios16, uuid) }
  let(:chromium_bundle_id) { 'browserstack.chromium.app' }
  let(:mock_chromium) { double(Chromium) }

  before do
    allow(NetworkHelper::NetworkSetup).to receive(:get_ip).with(no_args).and_return("127.0.0.1")
    allow(BrowserStack::AppiumServer).to receive(:new).and_return(mock_appium_server)
    allow(Chromium).to receive(:new).and_return(mock_chromium)
    allow(mock_chromium).to receive(:bundle_id).and_return(chromium_bundle_id)
    allow(mock_appium_server).to receive(:start_server_for_version)
    allow(IdeviceUtils).to receive(:use_devicectl).and_return false
    allow(BrowserStack::AppleBusinessManagerHelper).to receive(:new).and_return(mock_abm)
    allow(mock_abm).to receive(:device_eligible?).and_return(false)
    allow(DeviceManager).to receive(:is_first_cleanup?).and_return(false)
  end

  describe '#post_reboot' do
    let(:mock_cleanup_iphone) { double('cleanup_iphone') }

    it "should lock and unlock the device" do
      expect(BrowserStack::IPhone).to receive(:unlock_device).with(uuid, 20).and_return(true)
      expect(IosMdmServiceClient).to receive(:lock_device).with(uuid).and_return(true)
      expect(BrowserStack::IPhone).to receive(:unlock_device).with(uuid, 20).and_return(true)
      expect(mock_appium_server).to receive(:driver)

      subject.send :post_reboot
    end
  end

  describe '#verify_backup_preloaded_files' do
    let(:mock_cleanup_iphone) { double('cleanup_iphone') }
    let(:bs_media) { '/usr/local/.browserstack/media' }
    let(:photo_data_sqlite) { "/usr/local/.browserstack/photo_data/#{uuid}/Photos.sqlite" }

    before do
      allow(CleanupIphone).to receive(:new).and_return(mock_cleanup_iphone)
      allow(mock_cleanup_iphone).to receive(:copy_media)
      allow(mock_cleanup_iphone).to receive(:backup_preloaded_files)
      allow(mock_cleanup_iphone).to receive(:download_file_cleanup)
      allow(mock_cleanup_iphone).to receive(:log_ios_opened_apps)

      allow(File).to receive(:exist?).and_call_original
      allow(File).to receive(:exist?).with(bs_media).and_return(true)
      allow(File).to receive(:exist?).with(photo_data_sqlite).and_return(false)
    end

    context 'when device version is >= 13' do
      it 'returns immediately' do
        expect(iphone_ios13).not_to receive(:backup_preloaded_files)
        iphone_ios13.send :verify_backup_preloaded_files
      end
    end

    context 'when bs_media file doesn\'t exist' do
      before do
        allow(File).to receive(:exist?).with(bs_media).and_return(false)
      end

      it 'returns immediately' do
        expect(mock_cleanup_iphone).not_to receive(:backup_preloaded_files)
        subject.send :verify_backup_preloaded_files
      end
    end

    context 'when photo_data Photos.sqlite file exists' do
      before do
        allow(File).to receive(:exist?).with(photo_data_sqlite).and_return(true)
      end

      it 'returns immediately' do
        expect(mock_cleanup_iphone).not_to receive(:backup_preloaded_files)
        subject.send :verify_backup_preloaded_files
      end
    end

    context 'when device < 13 and bs_media & photo_data dirs exist' do
      it 'copies media, reboots, backs up preloaded files' do
        expect(mock_cleanup_iphone).to receive(:copy_media)
        expect(DeviceManager).to receive(:reboot_and_wait)
        expect(subject).to receive(:post_reboot)
        expect(mock_cleanup_iphone).to receive(:backup_preloaded_files)

        subject.send :verify_backup_preloaded_files
      end
    end
  end

  describe '#install_configuration_profiles' do
    let(:device_state) { double('device_state') }
    let(:check_device) { double('check_device') }
    let(:ip) { '*******' }
    let(:proxy_pac_url) { "http://#{ip}:45691/pacfile?device=#{uuid}" }
    let(:mock_cleanup_iphone) { double('cleanup_iphone') }
    let(:mock_configuration_profiles_manager) { double('configuration_profiles_manager') }
    let(:mock_configuration_profiles_enforcer) { double('configuration_profiles_enforcer') }

    before do
      allow_any_instance_of(NetworkHelper::NetworkSetup).to receive(:get_ip).with(no_args).and_return(ip)
      allow(BrowserStack::CheckDevice).to receive(:new)
        .with(uuid, REDIS_CLIENT).and_return(check_device)
      allow(CleanupIphone).to receive(:new).and_return(mock_cleanup_iphone)
      allow(mock_cleanup_iphone).to receive(:configuration_profiles_manager).and_return(mock_configuration_profiles_manager)
      allow(mock_cleanup_iphone).to receive(:configuration_profiles_enforcer).and_return(mock_configuration_profiles_enforcer)
    end

    context 'when device uses cfgutil managed profiles' do
      before(:each) do
        allow(mock_configuration_profiles_manager).to receive(:device_uses_cfgutil_managed_profiles?).and_return(true)
      end

      it 'should call enforce_configuration_profiles and install mdm and cfgutil profiles with force_install=true' do
        expect(mock_configuration_profiles_enforcer).to receive(:enforce_configuration_profiles).with(true)
        subject.send(:install_configuration_profiles)
      end
    end

    context 'when device does not use cfgutil managed profiles' do
      before(:each) do
        allow(mock_configuration_profiles_manager).to receive(:device_uses_cfgutil_managed_profiles?).and_return(false)
      end

      it 'should call check_mdm_settings and install mdm profiles with force_install=true' do
        expect(check_device).to receive(:check_mdm_settings)
          .with(
            proxy_pac_url,
            true,
            1,
            BrowserStack::Configuration.conf['mdm_profiles_required'],
            device_config['device_name'],
            device_config['device_version'],
            '',
            0
          )
        subject.send(:install_configuration_profiles)
      end
    end
  end

  describe '#cleanup' do
    let(:check_device) do
      double('check_device', check_platform_version_consistency: nil, check_internet_sharing: nil)
    end
    let(:ip) { '*******' }
    let(:proxy_pac_url) { "http://#{ip}:45691/pacfile?device=#{uuid}" }

    before do
      allow(BrowserStack::CheckDevice).to receive(:new)
        .with(uuid, REDIS_CLIENT).and_return(check_device)
      allow(subject).to receive(:check_lockdown)
      allow(subject).to receive(:check)
      allow(check_device).to receive(:check_device_supervised)
      # More mocking would be needed to allow cleanup to fully run.
      # We'll just any of the exceptions thrown by cleanup (mocking everything
      # is extremely hard and tedious).
    end

    context 'when the phone is not jailbroken' do
      before { allow(BrowserStack::JailbreakDetector).to receive(:check_jailbroken) }
      it "doesn't ban any user" do
        expect(BrowserStack::MobileTamperedAPI).not_to receive(:ban_last_user!)
        begin
          subject.cleanup('quick_cleanup')
        rescue
          nil
        end
      end
    end

    context 'when the phone is jailbroken' do
      before do
        allow(BrowserStack::JailbreakDetector)
          .to receive(:check_jailbroken)
          .and_return('my.jailbreak.app')
        allow(subject).to receive(:reset_socks5_forwarder)
        allow(BrowserStack::MobileTamperedAPI).to receive(:ban_last_user!)
      end

      xit "bans the last user" do
        expect(BrowserStack::MobileTamperedAPI).to receive(:ban_last_user!)
        begin
          subject.cleanup('quick_cleanup')
        rescue
          nil
        end
      end

      xit "raises an exception with the name of the jailbreaking app" do
        expect { subject.cleanup('quick_cleanup') }.to raise_error("manual fix required: Jailbroken with my.jailbreak.app")
      end
    end
  end

  describe '#full_cleanup' do
    let(:check_device) do
      double('check_device', check_platform_version_consistency: nil, check_internet_sharing: nil)
    end
    let(:mock_cleanup_iphone) { double('cleanup_iphone') }

    before do
      allow(BrowserStack::CheckDevice).to receive(:new)
        .with(uuid, REDIS_CLIENT).and_return(check_device)
      allow(subject).to receive(:check_lockdown).and_return(true)
      allow(subject).to receive(:check).and_return(true)
      allow(check_device).to receive(:check_device_supervised).and_return(true)
      expect(DeviceManager).to receive(:archive_and_truncate_appium_logs).and_return(true)
      allow(subject).to receive(:cleanup_live_testing_setup).and_return(true)
      allow(subject).to receive(:kill_xcode_build_and_iproxy).and_return(true)
      allow(CleanupIphone).to receive(:new).and_return(mock_cleanup_iphone)
      allow(mock_cleanup_iphone).to receive(:download_file_cleanup)
      allow(mock_cleanup_iphone).to receive(:log_ios_opened_apps)
    end

    context 'when the erase-restore flow fails' do
      before do
        allow(mock_cleanup_iphone).to receive(:save_completed_steps_state).and_return(true)
      end

      it "raises an exception when erase restore times out" do
        allow(mock_cleanup_iphone).to receive(:erase_and_restore).and_raise(Timeout::Error, "Erase Restore Timed out")
        expect { subject.full_cleanup }.to raise_error(Timeout::Error, "Erase Restore Timed out")
      end

      it "raises an exception when erase restore fails" do
        allow(mock_cleanup_iphone).to receive(:erase_and_restore).and_raise(CFGUtilBackupManager::BackupNotFoundError, "No backup found for 1234")
        expect { subject.full_cleanup }.to raise_error(CFGUtilBackupManager::BackupNotFoundError, "No backup found for 1234")
      end

      it "raises an exception when erase restore fails" do
        allow(mock_cleanup_iphone).to receive(:erase_and_restore).and_raise(CFGUtil::ExecutionError, "Non-zero exit code")
        expect { subject.full_cleanup }.to raise_error(CFGUtil::ExecutionError, "Non-zero exit code")
      end
    end
  end

  describe '#dedicated_cleanup' do
    let(:check_device) do
      double('check_device', check_platform_version_consistency: nil, check_internet_sharing: nil)
    end
    let(:mock_wda_checker) { double('WDAVersion') }
    let(:mock_cleanup_iphone) { double('cleanup_iphone') }

    before do
      allow(DeviceManager).to receive(:archive_and_truncate_appium_logs)
      allow(DeviceManager).to receive(:session_start_file)
      allow(BrowserStackAppHelper).to receive(:handle_cleanup_tasks)
      allow(BrowserStack::CheckDevice).to receive(:new)
        .with(uuid, REDIS_CLIENT).and_return(check_device)
      allow(subject).to receive(:check_lockdown)
      allow(subject).to receive(:check)
      allow(check_device).to receive(:check_device_supervised)
      allow(BrowserStack::JailbreakDetector).to receive(:check_jailbroken)
      allow(subject).to receive(:reset_socks5_forwarder)
      allow(WDAVersion).to receive(:new).with(uuid, anything, anything).and_return(mock_wda_checker)
      allow(mock_wda_checker).to receive(:outdated_version?).and_return(false)
      allow(mock_wda_checker).to receive(:install_wda_version)
      allow(subject.device_state).to receive(:resign_wda_file_present?).and_return(false)
      allow(subject).to receive(:install_required_apps_if_not_present)
      allow(CleanupIphone).to receive(:new).and_return(mock_cleanup_iphone)
      allow(mock_cleanup_iphone).to receive(:enable_safari_web_inspector)
      allow(mock_cleanup_iphone).to receive(:save_completed_steps_state)
      allow(mock_cleanup_iphone).to receive(:check_and_enforce_configuration_profiles)
      allow(mock_cleanup_iphone).to receive(:crash_logs_cleanup)
      allow(subject.device_state).to receive(:enable_safari_web_inspector_file_present?).and_return(false)

      # More mocking would be needed to allow cleanup to fully run.
      # We'll just any of the exceptions thrown by cleanup (mocking everything
      # is extremely hard and tedious).
    end

    it "calls only a subset of cleanup commands" do
      expect(subject).to receive(:check_replay_kit_still_running)
      expect(subject).to receive(:check_device_language)
      expect(subject).to receive(:check_wifi_status)
      expect(subject).to receive(:check_internet_sharing)
      expect(subject).to receive(:dismiss_system_popups)
      expect(subject).to receive(:dismiss_appstore_popup)
      expect(subject).to receive(:cleanup_live_testing_setup)
      expect(subject).to receive(:kill_xcode_build_and_iproxy)
      expect(subject).to receive(:install_required_apps_if_not_present)
      expect(subject).to receive(:cleanup_device)
      expect(subject).to receive(:restart_webkit_proxy)
      expect(subject).to receive(:phased_reboot_device)
      expect(subject).to receive(:check_if_cameras_are_blocked)
      expect(subject).to receive(:press_home)
      expect(subject).to receive(:lock_device_from_wda_with_fallback)
      expect(subject).to receive(:cleanup_app_testing_files)

      expect(subject).to_not receive(:app_cleanup)
      expect(subject).to_not receive(:check_safari_tabs)

      subject.dedicated_cleanup
    end
  end

  describe '#minified_cleanup' do
    let(:check_device) do
      double('check_device', check_platform_version_consistency: nil, check_internet_sharing: nil)
    end

    before do
      allow(BrowserStack::CheckDevice).to receive(:new)
        .with(uuid, REDIS_CLIENT).and_return(check_device)

      expect(DeviceManager).to receive(:archive_and_truncate_appium_logs)
      expect(DeviceManager).to receive(:session_start_file).and_return("abc")

      expect(FileUtils).to receive(:rm_f).twice
    end

    context 'when the device is in reserved flow' do
      it "should not raise exception" do
        expect(subject).to receive(:check_needs_full_reset)
        expect(subject).to receive(:check_lockdown)
        expect(subject).to receive(:minified_app_cleanup)
        expect(subject).to receive(:check_allow_settings_app_disabled)
        expect { subject.minified_cleanup('quick_cleanup', Time.now.to_i, false, true) }.to_not raise_error
      end

      it "should not call minified_app_cleanup if preserve_app_state_reserved_file_present" do
        deviceStateMock = double('DeviceStateMock')
        allow(deviceStateMock).to receive(:dedicated_cleanup_file_present?).and_return(false)
        expect(deviceStateMock).to receive(:preserve_app_state_reserved_file_present?).and_return true
        expect(deviceStateMock).to receive(:device_logger_pid_file_present?).and_return true
        expect(deviceStateMock).to receive(:session_bm_file_present?).and_return false
        expect(deviceStateMock).to receive(:device_logger_pid_file_to_array).and_return([1, 2])
        expect(deviceStateMock).to receive(:proxy_pac_url).and_return("*******")

        expect(DeviceState).to receive(:new).and_return deviceStateMock

        subject1 = BrowserStack::IPhone.new(device_config, uuid)
        expect(subject1).to receive(:check_needs_full_reset)
        expect(subject1).to receive(:check_lockdown)

        expect(subject1).to_not receive(:minified_app_cleanup)
        expect(subject1).to receive(:check_allow_settings_app_disabled)
        expect { subject1.minified_cleanup('quick_cleanup', Time.now.to_i, false, true) }.to_not raise_error
      end

      it "should call minified_app_cleanup if not preserve_app_state_reserved_file_present" do
        deviceStateMock = double('DeviceStateMock')
        allow(deviceStateMock).to receive(:dedicated_cleanup_file_present?).and_return(false)
        expect(deviceStateMock).to receive(:preserve_app_state_reserved_file_present?).and_return false
        expect(deviceStateMock).to receive(:device_logger_pid_file_present?).and_return true
        expect(deviceStateMock).to receive(:session_bm_file_present?).and_return false
        expect(deviceStateMock).to receive(:device_logger_pid_file_to_array).and_return([1, 2])
        expect(deviceStateMock).to receive(:proxy_pac_url).and_return("*******")

        expect(DeviceState).to receive(:new).and_return deviceStateMock

        subject1 = BrowserStack::IPhone.new(device_config, uuid)
        expect(subject1).to receive(:check_needs_full_reset)
        expect(subject1).to receive(:check_lockdown)

        expect(subject1).to receive(:minified_app_cleanup)
        expect(subject1).to receive(:check_allow_settings_app_disabled)
        expect { subject1.minified_cleanup('quick_cleanup', Time.now.to_i, false, true ) }.to_not raise_error
      end
    end
  end

  describe "initialize with nil config" do
    it 'does not raise an error' do
      iphone = BrowserStack::IPhone.new(nil, 1)
      expect(iphone.instance_variable_get(:@device_config)).to eq(nil)
    end
  end

  context "handle_contacts_cleanup" do
    let(:mock_cleanup_iphone) { double(CleanupIphone).as_null_object }
    before(:each) do
      allow(CleanupIphone).to receive(:new).and_return(mock_cleanup_iphone)
    end

    it 'clears contacts from device and reloads default contacts' do
      expect(IdeviceUtils).to receive(:delete_contacts).and_return(["", "0"])
      expect(IdeviceUtils).to receive(:load_contacts).and_return(["", "0"])
      expect { subject.contacts_app_cleanup }.not_to raise_error
    end

    context "raises exception" do
      it 'incase of deletion of contacts failed (2 repeats)' do
        expect(IdeviceUtils).to receive(:delete_contacts).and_return(["", "1"]).exactly(3)
        expect(IdeviceUtils).not_to receive(:load_contacts)
        expect { subject.contacts_app_cleanup }.to raise_error(ContactsCleanUpError)
      end

      it 'incase of loading of contacts failed (2 repeats)' do
        expect(IdeviceUtils).to receive(:delete_contacts).and_return(["", "0"]).exactly(1)
        expect(IdeviceUtils).to receive(:load_contacts).and_return(["", "1"]).exactly(3)
        expect { subject.contacts_app_cleanup }.to raise_error(ContactsCleanUpError)
      end

      it 'incase of loading of contacts timedout' do
        expect(IdeviceUtils).to receive(:delete_contacts).and_return(["", "0"])
        allow(IdeviceUtils).to receive(:load_contacts).and_raise(OSUtilsError)
        expect { subject.contacts_app_cleanup }.to raise_error(ContactsCleanUpError)
      end

      it 'incase of deletion of contacts timedout' do
        expect(IdeviceUtils).to receive(:delete_contacts).and_raise(OSUtilsError).exactly(3)
        expect { subject.contacts_app_cleanup }.to raise_error(ContactsCleanUpError)
      end
    end

    context 'when contacts are modified' do
      it "should reload contacts" do
        expect(subject).to receive(:need_contacts_cleanup?).and_return(true)
        expect(subject).to receive(:handle_contacts_cleanup)
        subject.contacts_app_cleanup
      end
    end

    context 'when contacts are not modified' do
      it "shouldn't reload contacts" do
        expect(subject).to receive(:need_contacts_cleanup?).and_return(false)
        expect(subject).not_to receive(:handle_contacts_cleanup)
        subject.contacts_app_cleanup
      end
    end
  end

  context "verify_contacts_count" do
    it 'verifies device version and expected contacts count' do
      expect(IdeviceUtils).to receive(:get_contacts_count_xml).and_return([40, "0"])
      expect { subject.verify_contacts_count }.not_to raise_error
    end

    it 'verifies device version and unexpected contacts count' do
      expect(IdeviceUtils).to receive(:get_contacts_count_xml).and_return([42, "0"])
      expect { subject.verify_contacts_count }.not_to raise_error
    end
  end

  describe '#automation_cleanup' do
    let(:mock_cleanup_iphone) { double('cleanup_iphone') }
    let(:mock_settings_driver) { double('settings_driver') }
    let(:mock_documents_driver) { double('documents_driver') }
    let(:mock_safari_driver) { double('safari_driver') }
    let(:mock_testflight_driver) { double('testflight_driver') }
    let(:installed_app) { double('mock_installed_app') }
    let(:test_suite_version) { '12.0' }
    let(:test_suite_bundle_id) { 'com.browserstack.BrowserStackUITests.xctrunner' }
    let(:ppuid) { "some-provisioning-profile-id" }
    let(:xcui_output) { "TEST SUITE SUCCEEDED" }
    let(:device_config) do
      {
        'device_version' => device_version,
        'webdriver_port' => 8080,
        'selenium_port' => 8081,
        'debugger_port' => 8082,
        'device_name' => 'iPhone 11',
        'device_serial' => 'A3527427524',
        'current_appium_version' => '1.7.0'
      }
    end
    let(:mock_device_state) do
      double('device_state',
             {
               check_global_proxy_file_older_than_days?: true,
               reinstall_browserstack_test_suite_file_present?: false,
               clean_provisioning_profile_file_present?: false,
               provisioning_profile_cleaned_file_older_than_days?: false,
               touch_provisioning_profile_cleaned_file: true,
               physical_sim_file_present?: false,
               esim_file_present?: false,
               sim_info_file_present?: false,
               dedicated_device_file_present?: false
             })
    end
    let(:mock_configuration_profiles_manager) { double('configuration_profiles_manager') }

    before do
      allow(CleanupIphone).to receive(:new).and_return(mock_cleanup_iphone)
      allow(subject).to receive(:need_assistive_touch_disabled?).and_return(true)
      allow(subject).to receive(:need_low_power_mode_disabled?).and_return(true)
      allow(subject).to receive(:need_enable_wifi?).and_return(true)
      allow(subject).to receive(:need_safari_reading_list_cleared?).and_return(true)
      allow(subject).to receive(:need_pwa_and_waiting_state_apps_cleanup?).and_return(true)
      allow(subject).to receive(:install_configuration_profiles)
      allow_any_instance_of(DeviceState).to receive(:sim_enabled_file_present?).and_return(true)
      allow_any_instance_of(DeviceState).to receive(:wifi_enabled_file_present?).and_return(true)
      allow_any_instance_of(DeviceState).to receive(:paint_timing_enabled_file_present?).and_return(false)
      allow_any_instance_of(DeviceState).to receive(:disabled_bluetooth_file_present?).and_return(false)
      allow_any_instance_of(DeviceState).to receive(:safari_settings_file_present?).and_return(false)
      allow(subject).to receive(:need_safari_cleanup?).and_return(true)
      allow(subject).to receive(:need_safari_cleanup?).and_return(true)
      allow(subject).to receive(:need_apple_wallet_cleanup?).and_return(true)
      allow(subject).to receive(:need_safari_url_bar_position_cleanup?).and_return(false)
      allow(subject).to receive(:wifi_enabled?).and_return(false)
      allow(subject).to receive(:remove_extra_keyboards?).and_return(true)
      allow(IdeviceUtils).to receive(:airplane_mode_on?).and_return(true)
      allow(DeviceState).to receive(:new).and_return(mock_device_state)
      allow(mock_cleanup_iphone).to receive(:kill_apps)
      allow(mock_cleanup_iphone).to receive(:download_file_cleanup)
      allow(mock_cleanup_iphone).to receive(:log_ios_opened_apps)
      allow(subject).to receive(:install_rigid_restrictions_profile).and_return(true)
      allow(mock_cleanup_iphone).to receive(:clean_voiceover)
      allow(mock_cleanup_iphone).to receive(:grant_access_photos_permission)
      allow(mock_cleanup_iphone).to receive(:disable_government_notifications)
      allow(mock_cleanup_iphone).to receive(:disable_airplane_mode_from_settings)
      allow_any_instance_of(DeviceState).to receive(:reinstall_browserstack_test_suite_file_present?).and_return(false)
      allow(BrowserStackAppHelper).to receive(:test_suite_version).and_return(test_suite_version)
      allow(InstalledApp).to receive(:new).and_return(installed_app)
      allow(BrowserStackAppHelper).to receive(:bstack_test_app_bundle_id).and_return(test_suite_bundle_id)
      allow(installed_app).to receive(:reinstall?).and_return(false)
      allow(BrowserStackAppHelper).to receive(:browserstack_test_suite_present?).and_return(true)
      allow(DeviceManager).to receive(:device_configuration_check).and_return(device_config)
      allow(mock_cleanup_iphone).to receive(:configuration_profiles_manager).and_return(mock_configuration_profiles_manager)
      allow(mock_configuration_profiles_manager).to receive(:device_uses_cfgutil_managed_profiles?).and_return(false)
    end

    context 'when ios 13' do
      let(:device_version) { 13 }

      it 'should clean things in order' do
        expect(mock_cleanup_iphone).to receive(:fix_enlarged_font_size).ordered
        expect(mock_cleanup_iphone).to receive(:install_launcher_and_bs_app).ordered
        expect(mock_cleanup_iphone).to receive(:install_rigid_restrictions_profile).ordered

        # Files App - XCUI cleanups
        expect(mock_cleanup_iphone).to receive(:delete_downloads).ordered

        # Settings - XCUI cleanups
        expect(mock_cleanup_iphone).to receive(:apple_id_signout).ordered
        expect(mock_cleanup_iphone).to receive(:enable_location_services).ordered
        expect(mock_cleanup_iphone).to receive(:safari_cleanup).ordered
        expect(mock_cleanup_iphone).to receive(:disable_government_notifications).ordered
        expect(mock_cleanup_iphone).to receive(:sign_out_sandbox_accounts).ordered
        expect(mock_cleanup_iphone).to receive(:disable_airplane_mode_from_settings).ordered
        expect(mock_cleanup_iphone).to receive(:disable_low_power_mode).ordered
        expect(mock_cleanup_iphone).to receive(:enable_wifi).ordered
        expect(mock_cleanup_iphone).to receive(:clean_pwa_and_waiting_state_apps).ordered
        expect(mock_cleanup_iphone).to receive(:reset_keyboard_settings).ordered
        expect(mock_cleanup_iphone).to receive(:siri_contacts_cleanup).ordered
        expect(mock_cleanup_iphone).to receive(:clean_imessages_app).ordered
        expect(mock_cleanup_iphone).to receive(:remove_extra_keyboards).ordered
        expect(subject).to receive(:set_time_to_utc).ordered
        expect(mock_cleanup_iphone).to receive(:disable_auto_lock).ordered
        expect(mock_cleanup_iphone).to receive(:enable_safari_web_inspector).ordered
        expect(mock_cleanup_iphone).to receive(:disable_bluetooth).ordered
        expect(mock_cleanup_iphone).to receive(:enable_redirect_extension).ordered
        expect(mock_cleanup_iphone).to receive(:disconnect_wifi).ordered
        expect(mock_cleanup_iphone).to receive(:apple_wallet_cleanup).ordered
        expect(mock_cleanup_iphone).to receive(:check_global_proxy_installed).ordered

        # Safari app - XCUI cleanups
        expect(mock_cleanup_iphone).to receive(:clean_bookmarks_favorites).ordered
        expect(mock_cleanup_iphone).to receive(:clean_safari_app).ordered

        expect(subject).to receive(:testflight_cleanup).ordered

        # Open driver to have WDA connection open
        expect(mock_appium_server).to receive(:driver).with(no_args).ordered
        expect(mock_cleanup_iphone).to receive(:timezone_reset_to_utc_needed?).and_return(false).ordered
        expect(mock_cleanup_iphone).to receive(:disable_dark_mode).ordered
        expect(mock_cleanup_iphone).to receive(:reset_accessibility_settings).ordered
        expect(mock_cleanup_iphone).to receive(:unmark_method).ordered
        expect(mock_cleanup_iphone).to receive(:set_default_font_size).and_return(xcui_output).ordered

        subject.automation_cleanup
      end
    end

    context 'when ios 14' do
      let(:device_version) { 14 }

      it 'should clean things in order' do
        expect(mock_cleanup_iphone).to receive(:fix_enlarged_font_size).ordered
        expect(mock_cleanup_iphone).to receive(:install_launcher_and_bs_app).ordered
        expect(mock_cleanup_iphone).to receive(:install_rigid_restrictions_profile).ordered

        # Files App - XCUI cleanups
        expect(mock_cleanup_iphone).to receive(:delete_downloads).ordered

        # Settings - XCUI cleanups
        expect(mock_cleanup_iphone).to receive(:reset_view_to_standard).ordered
        expect(mock_cleanup_iphone).to receive(:apple_id_signout).ordered
        # expect(mock_cleanup_iphone).to receive(:clear_contact_accounts).ordered
        expect(mock_cleanup_iphone).to receive(:enable_location_services).ordered
        expect(mock_cleanup_iphone).to receive(:safari_cleanup).ordered
        expect(mock_cleanup_iphone).to receive(:disable_government_notifications).ordered
        expect(mock_cleanup_iphone).to receive(:sign_out_sandbox_accounts).ordered
        expect(mock_cleanup_iphone).to receive(:disable_airplane_mode_from_settings).ordered
        expect(mock_cleanup_iphone).to receive(:disable_low_power_mode).ordered
        expect(mock_cleanup_iphone).to receive(:clean_pwa_and_waiting_state_apps).ordered
        expect(mock_cleanup_iphone).to receive(:reset_keyboard_settings).ordered
        expect(mock_cleanup_iphone).to receive(:siri_contacts_cleanup).ordered
        expect(mock_cleanup_iphone).to receive(:clean_imessages_app).ordered
        expect(mock_cleanup_iphone).to receive(:remove_extra_keyboards).ordered
        expect(subject).to receive(:set_time_to_utc).ordered
        expect(mock_cleanup_iphone).to receive(:disable_auto_lock).ordered
        expect(mock_cleanup_iphone).to receive(:enable_safari_web_inspector).ordered
        expect(mock_cleanup_iphone).to receive(:disable_bluetooth).ordered
        expect(mock_cleanup_iphone).to receive(:enable_redirect_extension).ordered
        expect(subject).to receive(:need_chromium_cleanup?).and_return(false).ordered
        expect(mock_cleanup_iphone).to receive(:clean_stored_password).ordered
        expect(mock_cleanup_iphone).to receive(:disconnect_wifi).ordered
        expect(mock_cleanup_iphone).to receive(:apple_wallet_cleanup).ordered
        expect(mock_cleanup_iphone).to receive(:check_global_proxy_installed).ordered

        # Safari app - XCUI cleanups
        expect(mock_cleanup_iphone).to receive(:clean_safari_app).ordered

        expect(subject).to receive(:testflight_cleanup).ordered

        # Open driver
        expect(mock_appium_server).to receive(:driver).with(no_args).and_return(mock_settings_driver).ordered
        expect(mock_cleanup_iphone).to receive(:timezone_reset_to_utc_needed?).and_return(false).ordered
        expect(mock_cleanup_iphone).to receive(:disable_dark_mode).ordered
        expect(mock_cleanup_iphone).to receive(:reset_accessibility_settings).ordered
        expect(mock_cleanup_iphone).to receive(:unmark_method).ordered
        expect(mock_cleanup_iphone).to receive(:set_default_font_size).and_return(xcui_output).ordered

        subject.automation_cleanup
      end

      describe "#xcui_automation" do
        context 'apple_id_signout' do
          before do
            expect(mock_cleanup_iphone).to receive(:fix_enlarged_font_size).ordered
            expect(mock_cleanup_iphone).to receive(:install_launcher_and_bs_app).ordered
            expect(mock_cleanup_iphone).to receive(:install_rigid_restrictions_profile).ordered
            expect(mock_cleanup_iphone).to receive(:delete_downloads)
            expect(mock_cleanup_iphone).to receive(:reset_view_to_standard)
            expect(mock_cleanup_iphone).to receive(:disable_government_notifications)
            expect(mock_cleanup_iphone).to receive(:sign_out_sandbox_accounts)
            expect(mock_cleanup_iphone).to receive(:disable_airplane_mode_from_settings)
            expect(mock_cleanup_iphone).to receive(:disable_low_power_mode)
            expect(mock_cleanup_iphone).to receive(:clean_pwa_and_waiting_state_apps)
            expect(mock_cleanup_iphone).to receive(:reset_keyboard_settings)
            expect(mock_cleanup_iphone).to receive(:siri_contacts_cleanup)
            expect(mock_cleanup_iphone).to receive(:clean_imessages_app)
            expect(mock_cleanup_iphone).to receive(:remove_extra_keyboards).ordered
            expect(mock_cleanup_iphone).to receive(:disconnect_wifi)
            expect(mock_cleanup_iphone).to receive(:apple_wallet_cleanup)
            expect(mock_cleanup_iphone).to receive(:check_global_proxy_installed)
            expect(subject).to receive(:set_time_to_utc)
            expect(mock_cleanup_iphone).to receive(:disable_auto_lock)
            expect(mock_cleanup_iphone).to receive(:enable_safari_web_inspector)
            expect(mock_cleanup_iphone).to receive(:disable_bluetooth)
            expect(mock_cleanup_iphone).to receive(:enable_location_services)
            expect(mock_cleanup_iphone).to receive(:kill_apps)

            # Safari app - XCUI cleanups
            expect(mock_cleanup_iphone).to receive(:clean_safari_app)
            expect(mock_cleanup_iphone).to receive(:safari_cleanup)
            expect(mock_cleanup_iphone).to receive(:enable_redirect_extension)
            expect(subject).to receive(:need_chromium_cleanup?).and_return(false).ordered
            expect(mock_cleanup_iphone).to receive(:clean_stored_password)
            expect(subject).to receive(:testflight_cleanup)

            # Open driver to have WDA connection open
            expect(mock_appium_server).to receive(:driver).with(no_args).ordered
            # expect(subject).to receive(:install_testflight).and_raise RuntimeError
            # expect(subject).to receive(:testflight_cleanup).and_raise RuntimeErro
            expect(mock_cleanup_iphone).to receive(:disable_dark_mode).ordered
            expect(mock_cleanup_iphone).to receive(:reset_accessibility_settings)
            expect(mock_cleanup_iphone).to receive(:unmark_method).ordered
            expect(mock_cleanup_iphone).to receive(:set_default_font_size).and_return(xcui_output).ordered
          end

          context 'when apple account is not found' do
            let( :not_found_apple_id_signout_output ) do
              '
              Apple ID not signed in, cross checking
              No sign in according to isAppleIDPresentV2
                  t =    13.86s Tear Down
              Closing any system popups
              '
            end
            before do
              expect(mock_cleanup_iphone).to receive(:apple_id_signout).once.and_return(not_found_apple_id_signout_output)
              allow(mock_cleanup_iphone).to receive(:set_default_font_size)
              allow(mock_cleanup_iphone).to receive(:kill_apps)
            end

            it 'should not call clear_contact_accounts' do
              expect(mock_cleanup_iphone).to_not receive(:clear_contact_accounts)
              expect(mock_cleanup_iphone).to receive(:timezone_reset_to_utc_needed?).and_return(false)
              subject.automation_cleanup
            end
          end
        end

        context 'disconnect_wifi' do
          before do
            expect(mock_cleanup_iphone).to receive(:fix_enlarged_font_size).ordered
            expect(mock_cleanup_iphone).to receive(:install_launcher_and_bs_app).ordered
            expect(mock_cleanup_iphone).to receive(:install_rigid_restrictions_profile).ordered
            expect(mock_cleanup_iphone).to receive(:delete_downloads)
            expect(mock_cleanup_iphone).to receive(:reset_view_to_standard)

            expect(mock_cleanup_iphone).to receive(:disable_government_notifications)
            expect(mock_cleanup_iphone).to receive(:sign_out_sandbox_accounts)
            expect(mock_cleanup_iphone).to receive(:disable_airplane_mode_from_settings)
            expect(mock_cleanup_iphone).to receive(:disable_low_power_mode)
            expect(mock_cleanup_iphone).to receive(:clean_pwa_and_waiting_state_apps)
            expect(mock_cleanup_iphone).to receive(:reset_keyboard_settings)
            expect(mock_cleanup_iphone).to receive(:siri_contacts_cleanup)
            expect(mock_cleanup_iphone).to receive(:clean_imessages_app)
            expect(mock_cleanup_iphone).to receive(:remove_extra_keyboards)
            expect(mock_cleanup_iphone).to receive(:apple_id_signout)
            expect(mock_cleanup_iphone).to receive(:safari_cleanup)
            expect(subject).to receive(:set_time_to_utc)
            expect(mock_cleanup_iphone).to receive(:disable_auto_lock)
            expect(mock_cleanup_iphone).to receive(:disable_bluetooth)
            expect(mock_cleanup_iphone).to receive(:enable_location_services)
            expect(mock_cleanup_iphone).to receive(:enable_safari_web_inspector)
            expect(mock_cleanup_iphone).to receive(:enable_redirect_extension)
            expect(subject).to receive(:need_chromium_cleanup?).and_return(false).ordered
            expect(mock_cleanup_iphone).to receive(:clean_stored_password)

            # Open driver to have WDA connection open
            # expect(subject).to receive(:install_testflight).and_raise RuntimeError
            # expect(subject).to receive(:testflight_cleanup).and_raise RuntimeError
          end

          context 'when auto join switch is not found' do
            let(:auto_join_not_present_logline) do
              '
              t =    27.25s         Snapshot accessibility hierarchy for app with pid 906
              t =    27.39s Find the StaticText
              t =    27.39s     Snapshot accessibility hierarchy for app with pid 906
              Assertion Failure: SettingsUITests.swift:92: XCTAssertTrue failed - Auto Join switch not present
              t =    27.46s Waiting 2.0s for "Sign Out" Cell to exist
              '
            end
            before do
              expect(mock_cleanup_iphone).to receive(:disconnect_wifi).once.and_raise(BrowserStackTestExecutionError.new('SettingsUITests', 'disconnect_wifi', auto_join_not_present_logline))

              expect(mock_appium_server).to receive(:driver).with(no_args).ordered
            end

            it 'should send zombie logs with kind aa-disconn-wifi-auto-join-missing' do
              expect(subject).to receive(:zombie).with("aa-disconn-wifi-auto-join-missing")
              expect(mock_cleanup_iphone).to receive(:apple_wallet_cleanup)
              expect(mock_cleanup_iphone).to receive(:check_global_proxy_installed)
              expect(mock_cleanup_iphone).to receive(:clean_safari_app)
              expect(subject).to receive(:testflight_cleanup)
              expect(mock_cleanup_iphone).to receive(:timezone_reset_to_utc_needed?).and_return(false)
              expect(mock_cleanup_iphone).to receive(:disable_dark_mode).ordered
              expect(mock_cleanup_iphone).to receive(:reset_accessibility_settings)
              expect(mock_cleanup_iphone).to receive(:unmark_method).ordered
              expect(mock_cleanup_iphone).to receive(:set_default_font_size).and_return(xcui_output).ordered
              expect { subject.automation_cleanup }.not_to raise_error
            end
          end

          context 'when auto join switch is found' do
            let(:auto_join_present_logline) do
              '
              t =    27.25s         Snapshot accessibility hierarchy for app with pid 906
              t =    27.39s Find the StaticText
              t =    27.39s     Snapshot accessibility hierarchy for app with pid 906
              t =    27.46s Waiting 2.0s for "Sign Out" Cell to exist
              '
            end
            before do
              expect(mock_cleanup_iphone).to receive(:disconnect_wifi).once.and_raise(BrowserStackTestExecutionError.new('SettingsUITests', 'disconnect_wifi', auto_join_present_logline))
            end

            it 'should raise exception' do
              expect { subject.automation_cleanup }.to raise_error(BrowserStackTestExecutionError)
            end
          end

          context "when wifi is connected to non BLT network" do
            it "should raise unknown wifi error" do
              expect(subject).to receive(:connected_to_browserstack_wifi?).and_return(false)
              expect(mock_cleanup_iphone).to receive(:disconnect_wifi).once.and_raise(BrowserStackTestExecutionError.new('SettingsUITests', 'disconnect_wifi', "output"))
              expect { subject.automation_cleanup }.to raise_error(WifiError, /wifi connected to unknown network/)
            end
          end

          context "when wifi is connected to BLT network" do
            it "should raise unknown wifi error" do
              expect(subject).to receive(:connected_to_browserstack_wifi?).and_return(true)
              expect(mock_cleanup_iphone).to receive(:disconnect_wifi).once.and_raise(BrowserStackTestExecutionError.new('SettingsUITests', 'disconnect_wifi', "output"))
              expect { subject.automation_cleanup }.not_to raise_error(WifiError, /wifi connected to unknown network/)
            end
          end
        end
      end
    end

    context 'when ios 10' do
      let(:device_version) { 10 }

      let(:mock_cleanup_iphone) { double('cleanup_iphone').as_null_object }

      before do
        allow(CleanupIphone).to receive(:new).and_return(mock_cleanup_iphone)
        expect(mock_cleanup_iphone).to receive(:install_launcher_and_bs_app).ordered
        expect(mock_cleanup_iphone).to receive(:install_rigid_restrictions_profile).ordered
      end

      it 'should not receive testflight cleanup' do
        expect(mock_cleanup_iphone).to_not receive(:testflight_cleanup)
        expect(mock_appium_server).to receive(:driver).with(app: 'settings').and_return(mock_settings_driver).ordered
        expect(mock_settings_driver).to receive(:driver_quit).ordered

        expect(mock_appium_server).to receive(:driver).with(no_args).and_return(mock_settings_driver).ordered

        subject.automation_cleanup
      end

      it 'should run clean safari app and test launch settings at the end' do
        expect(mock_cleanup_iphone).to receive(:clean_safari_app).ordered
        expect(mock_appium_server).to receive(:driver).with(app: 'settings').and_return(mock_settings_driver).ordered
        expect(mock_settings_driver).to receive(:driver_quit).ordered

        expect(mock_cleanup_iphone).to_not receive(:disconnect_wifi)
        expect(mock_appium_server).to receive(:driver).with(no_args).and_return(mock_settings_driver).ordered

        subject.automation_cleanup
      end
    end
  end

  describe '#dedicated_automation_cleanup' do
    let(:mock_appium_server) { double('appium_server') }
    let(:mock_cleanup_iphone) { double('cleanup_iphone') }
    let(:mock_settings_driver) { double('settings_driver') }
    let(:mock_documents_driver) { double('documents_driver') }
    let(:mock_safari_driver) { double('safari_driver') }
    let(:mock_testflight_driver) { double('testflight_driver') }
    let(:device_version) { 13 }
    let(:installed_app) { double('mock_installed_app') }
    let(:mock_custom_mdm_manager) { double('mock_custom_mdm_manager') }
    let(:test_suite_version) { '12.0' }
    let(:test_suite_bundle_id) { 'com.browserstack.BrowserStackUITests.xctrunner' }
    let(:ppuid) { "some-provisioning-profile-id" }
    let(:xcui_output) { "TEST SUITE SUCCEEDED" }
    let(:device_config) do
      {
        'device_version' => device_version,
        'webdriver_port' => 8080,
        'selenium_port' => 8081,
        'debugger_port' => 8082,
        'device_name' => 'iPhone 11',
        'device_serial' => 'A3527427524',
        'current_appium_version' => '1.7.0'
      }
    end
    let(:mock_device_state) do
      double('device_state',
             {
               check_global_proxy_file_older_than_days?: true,
               sim_enabled_file_present?: true,
               paint_timing_enabled_file_present?: true,
               disabled_bluetooth_file_present?: true,
               wifi_enabled_file_present?: true,
               reinstall_browserstack_test_suite_file_present?: false,
               physical_sim_file_present?: false,
               esim_file_present?: false
             })
    end
    let(:mock_configuration_profiles_manager) { double('configuration_profiles_manager') }

    before do
      allow(BrowserStack::AppiumServer).to receive(:new).and_return(mock_appium_server)
      allow(CleanupIphone).to receive(:new).and_return(mock_cleanup_iphone)
      allow(subject).to receive(:need_enable_wifi?).and_return(true)
      allow(subject).to receive(:wifi_enabled?).and_return(false)
      allow(mock_cleanup_iphone).to receive(:grant_access_photos_permission)
      allow(mock_cleanup_iphone).to receive(:clean_voiceover)
      allow(mock_cleanup_iphone).to receive(:disable_government_notifications)
      allow(mock_cleanup_iphone).to receive(:disable_airplane_mode_from_settings)
      allow(IdeviceUtils).to receive(:airplane_mode_on?).and_return(true)
      allow_any_instance_of(DeviceState).to receive(:wifi_enabled_file_present?).and_return(true)
      allow_any_instance_of(DeviceState).to receive(:safari_settings_file_present?).and_return(false)
      allow(DeviceState).to receive(:new).and_return(mock_device_state)
      allow(mock_cleanup_iphone).to receive(:kill_apps)
      allow_any_instance_of(DeviceState).to receive(:reinstall_browserstack_test_suite_file_present?).and_return(false)
      allow(BrowserStackAppHelper).to receive(:test_suite_version).and_return(test_suite_version)
      allow(InstalledApp).to receive(:new).and_return(installed_app)
      allow(BrowserStackAppHelper).to receive(:bstack_test_app_bundle_id).and_return(test_suite_bundle_id)
      allow(installed_app).to receive(:reinstall?).and_return(false)
      allow(BrowserStackAppHelper).to receive(:browserstack_test_suite_present?).and_return(true)
      allow(mock_cleanup_iphone).to receive(:configuration_profiles_manager).and_return(mock_configuration_profiles_manager)
      allow(mock_configuration_profiles_manager).to receive(:device_uses_cfgutil_managed_profiles?).and_return(false)
      allow(CustomMDMManager).to receive(:new).and_return(mock_custom_mdm_manager)
      allow(CustomMDMManager).to receive(:is_custom_mdm_device?).and_return(false)
    end

    it 'should clean things in order' do
      expect(mock_custom_mdm_manager).to receive(:manage_setup).ordered
      expect(mock_cleanup_iphone).to receive(:install_launcher_and_bs_app).ordered
      # MDM re-enrollment automation
      expect(mock_cleanup_iphone).to receive(:re_enroll_device_to_mdm).ordered

      # Settings - XCUI cleanups
      expect(mock_cleanup_iphone).to receive(:disable_government_notifications).ordered
      expect(mock_cleanup_iphone).to receive(:disable_airplane_mode_from_settings).ordered
      expect(mock_cleanup_iphone).to receive(:enable_wifi).ordered
      expect(subject).to receive(:set_time_to_utc).ordered
      expect(mock_cleanup_iphone).to receive(:disable_auto_lock).ordered
      expect(mock_cleanup_iphone).to receive(:disconnect_wifi).ordered
      expect(mock_cleanup_iphone).to receive(:check_global_proxy_installed).ordered

      expect(subject).to receive(:testflight_cleanup).ordered

      # Open driver to have WDA connection open
      expect(mock_appium_server).to receive(:driver).with(no_args).ordered
      expect(mock_cleanup_iphone).to receive(:timezone_reset_to_utc_needed?).and_return(false).ordered
      expect(mock_cleanup_iphone).to receive(:reset_accessibility_settings).ordered
      expect(mock_cleanup_iphone).to receive(:unmark_method).ordered
      expect(mock_cleanup_iphone).to receive(:set_default_font_size).and_return(xcui_output).ordered

      subject.dedicated_automation_cleanup
    end
  end

  describe "#testflight_cleanup" do
    let(:dir_name) { "/usr/local/.browserstack/config/installed_TestFlight" }
    let(:notifications_disabled_file) { "#{dir_name}/disabled_TestFlight_notifs_#{uuid}" }
    let(:testflight_installed_file) { "#{dir_name}/installed_TestFlight_#{uuid}" }
    let(:notifications_opened_file) { "/tmp/testflight_notification_opened_#{uuid}" }
    let(:mock_testflight) { double }

    before do
      allow(Automation::TestFlight).to receive(:new).and_return(mock_testflight)
      allow(iphone_ios14).to receive(:bounce_testflight).and_return(true)
      allow(iphone_ios16).to receive(:bounce_testflight).and_return(true)
      allow(Utils).to receive(:new_notifications_flow_enabled?).and_return(false)
      allow(BrowserStack::AppleBusinessManagerHelper).to receive(:new).and_return(mock_abm)
      allow(mock_abm).to receive(:device_eligible?).and_return(true)
      allow(mock_abm).to receive(:app_installed_with_retries?)
      allow(mock_abm).to receive(:install_app_via_mdm_using_vpp)
    end

    context "need_install_testflight? returns false" do
      before do
        allow(iphone_ios13).to receive(:need_install_testflight?).and_return(false)
        allow(Utils).to receive(:new_notifications_flow_enabled?).and_return(false)
      end

      it "does not install testflight" do
        allow(File).to receive(:exists?).with(notifications_disabled_file).and_return(true)
        allow(File).to receive(:exists?).with(notifications_opened_file).and_return(false)
        expect(iphone_ios13).to_not receive(:install_testflight)
        expect(FileUtils).to receive(:touch).with(testflight_installed_file)
        iphone_ios13.send :testflight_cleanup
      end

      context "notifications_disabled_file does not exist" do
        it "runs disable_notifications and touches file" do
          allow(File).to receive(:exists?).with(notifications_disabled_file).and_return(false)
          allow(File).to receive(:exists?).with(notifications_opened_file).and_return(false)
          expect(mock_testflight).to receive(:disable_notifications)
          expect(FileUtils).to receive(:touch).with(testflight_installed_file)
          iphone_ios13.send :testflight_cleanup
        end
      end

      context "notifications_disabled_file does exist" do
        it "does nothing extra" do
          allow(File).to receive(:exists?).with(notifications_disabled_file).and_return(true)
          allow(File).to receive(:exists?).with(notifications_opened_file).and_return(false)
          expect(Automation::TestFlight).to_not receive(:new)
          iphone_ios13.send :testflight_cleanup
        end
      end

      context "notifications_opened_file does not exist" do
        it "does nothing extra" do
          allow(File).to receive(:exists?).with(notifications_disabled_file).and_return(true)
          allow(File).to receive(:exists?).with(notifications_opened_file).and_return(false)
          expect(Automation::TestFlight).to_not receive(:new)
          iphone_ios13.send :testflight_cleanup
        end
      end

      context "notifications_opened_file does exist" do
        it "runs disable_notifications and removes file" do
          allow(File).to receive(:exists?).with(notifications_disabled_file).and_return(true)
          allow(File).to receive(:exists?).with(notifications_opened_file).and_return(true)
          expect(mock_testflight).to receive(:disable_notifications)
          iphone_ios13.send :testflight_cleanup
        end
      end
    end

    context "device_eligible? returns false" do
      context "check_app_with_bundle_id_exists returns true"
      before do
        allow(mock_abm).to receive(:device_eligible?).and_return(false)
        allow(IdeviceUtils).to receive(:check_app_with_bundle_id_exists).and_return(true)
      end

      it "uninstalls testflight" do
        expect(IdeviceUtils).to receive(:uninstall_app)
        expect { iphone_ios16.send :testflight_cleanup }.not_to raise_error
      end
    end

    context "when ios >= 16 and < 16.3" do
      it "should call bounce_testflight" do
        allow(File).to receive(:exists?).with(notifications_disabled_file).and_return(true)
        allow(File).to receive(:exists?).with(notifications_opened_file).and_return(false)
        expect(Automation::TestFlight).to_not receive(:new)
        allow(iphone_ios16).to receive(:device_version).and_return("16.2")
        iphone_ios16.instance_variable_set(:@dedicated_device, false)
        expect(iphone_ios16).to receive(:bounce_testflight)
        expect { iphone_ios16.send :testflight_cleanup }.not_to raise_error
      end
    end

    context "when ios >= 16.3" do
      before do
        allow(iphone_ios16).to receive(:device_version).and_return("16.3")
        iphone_ios16.instance_variable_set(:@dedicated_device, false)
      end

      it "should not call bounce_testflight" do
        allow(File).to receive(:exists?).with(notifications_disabled_file).and_return(true)
        allow(File).to receive(:exists?).with(notifications_opened_file).and_return(false)
        expect(Automation::TestFlight).to_not receive(:new)
        expect(iphone_ios16).not_to receive(:bounce_testflight)
        iphone_ios16.send(:testflight_cleanup)
      end
    end

    context "when ios is < 16" do
      before do
        allow(iphone_ios14).to receive(:device_version).and_return("14.0")
        iphone_ios14.instance_variable_set(:@dedicated_device, false)
      end

      it "should not call bounce_testflight" do
        allow(File).to receive(:exists?).with(notifications_disabled_file).and_return(true)
        allow(File).to receive(:exists?).with(notifications_opened_file).and_return(false)
        expect(Automation::TestFlight).to_not receive(:new)
        expect(iphone_ios14).not_to receive(:bounce_testflight)
        iphone_ios14.send(:testflight_cleanup)
      end
    end

    context "when device is dedicated" do
      before do
        allow(iphone_ios16).to receive(:device_version).and_return("16.1")
        iphone_ios16.instance_variable_set(:@dedicated_device, true)
        allow(iphone_ios16).to receive(:bounce_testflight)
      end

      it "does not call bounce_testflight" do
        allow(File).to receive(:exists?).with(notifications_disabled_file).and_return(true)
        allow(File).to receive(:exists?).with(notifications_opened_file).and_return(false)
        expect(Automation::TestFlight).to_not receive(:new)
        expect(iphone_ios16).not_to receive(:bounce_testflight)
        iphone_ios16.send(:testflight_cleanup)
      end
    end
  end

  describe '#launch_app' do
    let(:mock_wda_client) { double }

    it "should launch app without launch env variable if value of set_browserstack_env param is false" do
      expect(subject).to receive(:wda_client).and_return(mock_wda_client)
      expect(mock_wda_client).to receive(:launch_apps_with_locale).once.with(["com.test.test"], "en-UK", "en-UK")
      subject.send(:launch_app, "name", "com.test.test", "en-UK", "en-UK")
    end

    it "should launch app with launch env variable if value of set_browserstack_env param is true" do
      expect(subject).to receive(:wda_client).and_return(mock_wda_client)
      expect(mock_wda_client).to receive(:launch_apps_with_locale).once.with(["com.test.test"], "en-US", "en-US", "no", "yes")
      subject.send(:launch_app, "name", "com.test.test", nil, nil, 0, true)
    end
  end

  describe '#open_photos_app' do
    let(:mock_wda_client) { double }

    context 'os version is less than 13' do
      it 'should invoke Automation.bounce_app function and not call IPhone.launch_apps_with_display_name' do
        expect(Automation).to receive(:bounce_app).with(uuid, "com.apple.mobileslideshow")
        expect(subject).to_not receive(:wda_client)

        subject.send :open_photos_app
      end

      it 'should raise any exception in case of error' do
        expect(Automation).to receive(:bounce_app).with(uuid, "com.apple.mobileslideshow").and_raise("some_error")

        expect { subject.send :open_photos_app }.to raise_error('Bounce Photos app unsuccessful')
      end
    end

    context 'os version is equal to 13' do
      it 'should invoke wda.launch_app_with_bundle_id function' do
        expect(Automation).to_not receive(:bounce_app)
        expect(iphone_ios13).to receive(:wda_client).and_return(mock_wda_client)
        expect(mock_wda_client).to receive(:launch_app_with_bundle_id).once.with("com.apple.mobileslideshow")

        iphone_ios13.send :open_photos_app
      end

      it 'should should retry once if failed' do
        expect(Automation).to_not receive(:bounce_app)
        expect(iphone_ios13).to receive(:wda_client).exactly(1).times.and_raise("some_error")
        expect(iphone_ios13).to receive(:wda_client).and_return(mock_wda_client)
        expect(mock_wda_client).to receive(:launch_app_with_bundle_id).once.with("com.apple.mobileslideshow")

        iphone_ios13.send :open_photos_app
      end

      it 'should raise error if launch app function raises error' do
        expect(Automation).to_not receive(:bounce_app)
        expect(iphone_ios13).to receive(:wda_client).at_least(:once).and_raise("some_error")

        expect { iphone_ios13.send :open_photos_app }.to raise_error('Bounce Photos app unsuccessful')
      end
    end
  end

  describe '#app_cleanup' do
    context 'app is files app' do
      it 'does not try delete the files app' do
        allow(subject).to receive(:send_user_apps_to_cls)
        allow(subject).to receive(:send_user_apps_to_eds)
        allow(IdeviceUtils).to receive(:list_user_installed_apps).and_return([])
        subject.instance_variable_set(:@genre, 'live_testing')
        subject.instance_variable_set(:@session_params, { 'app_testing_bundle_id' => 'com.apple.DocumentsApp' })

        expect(IdeviceUtils).to_not receive(:uninstall_app)
        expect(subject).to_not receive(:handle_untrust_and_delete_client_enterprise_app)

        subject.send(:app_cleanup)
      end
    end
  end

  describe "#need_sandbox_account_cleanup?" do
    it "should clean if device logger is not reliable" do
      allow(subject).to receive(:device_logger_reliable?).and_return(false)
      expect(subject.send(:need_sandbox_account_cleanup?)).to eq(true)
    end

    it "should not skip if device needs force cleaning" do
      allow(subject).to receive(:device_logger_reliable?).and_return(true)
      expect(subject).to receive(:need_force_clean_sandbox_account?).and_return(true)
      expect(subject.send(:need_sandbox_account_cleanup?)).to eq(true)
    end

    it "should clean if device logger detected sign in" do
      allow(subject).to receive(:device_logger_reliable?).and_return(true)
      allow(subject).to receive(:need_force_clean_sandbox_account?).and_return(false)
      allow(subject.device_state).to receive(:device_logger_detected_sandbox_signed_in_file_present?).and_return(true)
      expect(subject.send(:need_sandbox_account_cleanup?)).to eq(true)
    end

    it "should not clean if all checks are satisfied" do
      allow(subject).to receive(:device_logger_reliable?).and_return(true)
      allow(subject).to receive(:need_force_clean_sandbox_account?).and_return(false)
      allow(subject.device_state).to receive(:device_logger_detected_sandbox_signed_in_file_present?).and_return(false)
      expect(subject.send(:need_sandbox_account_cleanup?)).to eq(false)
    end
  end

  describe "#need_safari_cleanup?" do
    context "clean safari" do
      ["automate", "live_testing"].each do |genre|
        it "returns true for #{genre} session" do
          subject.instance_variable_set(:@genre, genre)
          expect(subject.send(:need_safari_cleanup?)).to eq(true)
        end
      end

      ["app_live_testing", "app_automate"].each do |genre|
        it "returns true for #{genre} session if device logger is unreliable" do
          subject.instance_variable_set(:@genre, genre)
          expect(subject).to receive(:device_logger_reliable?).and_return(false)

          expect(subject.send(:need_safari_cleanup?)).to eq(true)
        end

        it "returns true for #{genre} session if safari launch was detected with reliable device logger" do
          subject.instance_variable_set(:@genre, genre)
          expect(subject).to receive(:device_logger_reliable?).and_return(true)
          expect(subject.device_state).to receive(:force_clean_safari_file_older_than_days?).with(kind_of(Numeric)).and_return(false)
          expect(subject.device_state).to receive(:device_logger_detected_safari_launched_file_present?).and_return(true)

          expect(subject.send(:need_safari_cleanup?)).to eq(true)
        end

        it "returns true for #{genre} session if safari launch was not detected but need to force clean" do
          subject.instance_variable_set(:@genre, genre)
          expect(subject).to receive(:device_logger_reliable?).and_return(true)
          expect(subject.device_state).to receive(:force_clean_safari_file_older_than_days?).with(kind_of(Numeric)).and_return(true)

          expect(subject.send(:need_safari_cleanup?)).to eq(true)
        end
      end
    end

    context "skip safari cleanup" do
      ["app_live_testing", "app_automate"].each do |genre|
        it "returns false for #{genre} session if safari launch was not detected with reliable device logger" do
          subject.instance_variable_set(:@genre, genre)
          expect(subject).to receive(:device_logger_reliable?).and_return(true)
          expect(subject.device_state).to receive(:device_logger_detected_safari_launched_file_present?).and_return(false)
          expect(subject.device_state).to receive(:force_clean_safari_file_older_than_days?).with(kind_of(Numeric)).and_return(false)
          allow(subject).to receive(:device_version).and_return(15.0)
          expect(subject.device_state).to receive(:watcher_unreliable_file_present?).and_return(false)
          mock_set = ['com.browserstack.app'].to_set
          expect(IosWatcher).to receive(:apps_opened_during_session_set).and_return(mock_set)
          expect(subject).to receive(:zombie).with(kind_of(String))

          expect(subject.send(:need_safari_cleanup?)).to eq(false)
        end
      end
    end
  end

  describe "#need_safari_bookmarks_cleanup?" do
    context "clean safari bookmarks" do
      ["automate", "live_testing"].each do |genre|
        it "returns true for #{genre} session" do
          subject.instance_variable_set(:@genre, genre)
          expect(subject.send(:need_safari_bookmarks_cleanup?)).to eq(true)
        end
      end

      ["app_live_testing", "app_automate"].each do |genre|
        it "returns true for #{genre} session if device logger is unreliable" do
          subject.instance_variable_set(:@genre, genre)
          expect(subject).to receive(:device_logger_reliable?).and_return(false)

          expect(subject.send(:need_safari_bookmarks_cleanup?)).to eq(true)
        end

        it "returns true for #{genre} session if safari added bookmark was detected with reliable device logger" do
          subject.instance_variable_set(:@genre, genre)
          expect(subject).to receive(:device_logger_reliable?).and_return(true)
          expect(subject.device_state).to receive(:force_clean_safari_bookmarks_file_older_than_days?).with(kind_of(Numeric)).and_return(false)
          expect(subject.device_state).to receive(:device_logger_detected_safari_bookmarks_launched_file_present?).and_return(true)

          expect(subject.send(:need_safari_bookmarks_cleanup?)).to eq(true)
        end

        it "returns true for #{genre} session if safari added bookmark was not detected but need to force clean" do
          subject.instance_variable_set(:@genre, genre)
          expect(subject).to receive(:device_logger_reliable?).and_return(true)
          expect(subject.device_state).to receive(:force_clean_safari_bookmarks_file_older_than_days?).with(kind_of(Numeric)).and_return(true)

          expect(subject.send(:need_safari_bookmarks_cleanup?)).to eq(true)
        end
      end
    end

    context "skip safari bookmark cleanup" do
      ["app_live_testing", "app_automate"].each do |genre|
        it "returns false for #{genre} session if safari added bookmark was not detected with reliable device logger" do
          subject.instance_variable_set(:@genre, genre)
          expect(subject).to receive(:device_logger_reliable?).and_return(true)
          expect(subject.device_state).to receive(:device_logger_detected_safari_bookmarks_launched_file_present?).and_return(false)
          expect(subject.device_state).to receive(:force_clean_safari_bookmarks_file_older_than_days?).with(kind_of(Numeric)).and_return(false)
          expect(subject).to receive(:zombie).with(kind_of(String))

          expect(subject.send(:need_safari_bookmarks_cleanup?)).to eq(false)
        end
      end
    end
  end

  describe "#need_safari_favorites_cleanup?" do
    context "clean safari favorites" do
      ["automate", "live_testing"].each do |genre|
        it "returns true for #{genre} session" do
          subject.instance_variable_set(:@genre, genre)
          expect(subject.send(:need_safari_favorites_cleanup?)).to eq(true)
        end
      end

      ["app_live_testing", "app_automate"].each do |genre|
        it "returns true for #{genre} session if device logger is unreliable" do
          subject.instance_variable_set(:@genre, genre)
          expect(subject).to receive(:device_logger_reliable?).and_return(false)

          expect(subject.send(:need_safari_favorites_cleanup?)).to eq(true)
        end

        it "returns true for #{genre} session if safari added favorites was detected with reliable device logger" do
          subject.instance_variable_set(:@genre, genre)
          expect(subject).to receive(:device_logger_reliable?).and_return(true)
          expect(subject.device_state).to receive(:force_clean_safari_favorites_file_older_than_days?).with(kind_of(Numeric)).and_return(false)
          expect(subject.device_state).to receive(:device_logger_detected_safari_favorites_launched_file_present?).and_return(true)

          expect(subject.send(:need_safari_favorites_cleanup?)).to eq(true)
        end

        it "returns true for #{genre} session if safari added favorites was not detected but need to force clean" do
          subject.instance_variable_set(:@genre, genre)
          expect(subject).to receive(:device_logger_reliable?).and_return(true)
          expect(subject.device_state).to receive(:force_clean_safari_favorites_file_older_than_days?).with(kind_of(Numeric)).and_return(true)

          expect(subject.send(:need_safari_favorites_cleanup?)).to eq(true)
        end
      end
    end

    context "skip safari favorites cleanup" do
      ["app_live_testing", "app_automate"].each do |genre|
        it "returns false for #{genre} session if safari added favorites was not detected with reliable device logger" do
          subject.instance_variable_set(:@genre, genre)
          expect(subject).to receive(:device_logger_reliable?).and_return(true)
          expect(subject.device_state).to receive(:device_logger_detected_safari_favorites_launched_file_present?).and_return(false)
          expect(subject.device_state).to receive(:force_clean_safari_favorites_file_older_than_days?).with(kind_of(Numeric)).and_return(false)
          expect(subject).to receive(:zombie).with(kind_of(String))

          expect(subject.send(:need_safari_favorites_cleanup?)).to eq(false)
        end
      end
    end
  end

  describe "#need_chromium_cleanup?" do
    it "should not skip if device needs force cleaning" do
      expect(subject).to receive(:device_version).and_return(15.0)
      expect(subject).to receive(:need_force_clean_chromium?).and_return(true)
      expect(subject.send(:need_chromium_cleanup?)).to eq(true)
    end

    it "should clean if device logger detected sign in" do
      expect(subject).to receive(:device_version).and_return(15.0)
      allow(subject).to receive(:need_force_clean_chromium?).and_return(false)
      allow(subject.device_state).to receive(:device_logger_detected_chromium_launched_file_present?).and_return(true)
      expect(subject.send(:need_chromium_cleanup?)).to eq(true)
    end

    it "should not clean if all checks are satisfied" do
      expect(subject).to receive(:device_version).and_return(13.0)
      allow(subject).to receive(:need_force_clean_chromium?).and_return(false)
      allow(subject.device_state).to receive(:device_logger_detected_chromium_launched_file_present?).and_return(false)
      expect(subject.send(:need_chromium_cleanup?)).to eq(false)
    end
  end

  describe "#need_safari_websocket_disable?" do
    context 'when device version is lower than 15' do
      before do
        allow(subject).to receive(:device_version).and_return(12.0)
      end

      it 'returns false' do
        expect(subject.send(:need_safari_websocket_disable?)).to eq(false)
      end
    end

    context 'when the device version is version is 15 or greater' do
      before do
        allow(subject).to receive(:device_version).and_return(15.2)
      end

      context 'when the safari_websocket_file is older than 7 days' do
        before do
          allow(subject.device_state).to receive(:safari_websocket_file_clean_on_weekend?).and_return(true)
        end

        it 'returns true' do
          expect(subject.send(:need_safari_websocket_disable?)).to eq(true)
        end
      end

      context 'when the safari_websocket_file is not old' do
        before do
          allow(subject.device_state).to receive(:safari_websocket_file_clean_on_weekend?).and_return(false)
        end

        it 'returns false' do
          expect(subject.send(:need_safari_websocket_disable?)).to eq(false)
        end
      end
    end
  end

  describe "#need_remove_extra_keyboards?" do
    it "should return false for older iOS devices" do
      expect(subject).to receive(:device_version).and_return(10.0)

      expect(subject.send(:need_remove_extra_keyboards?)).to eq(false)
    end

    it "should return true for iOS 14" do
      expect(subject).to receive(:device_version).and_return(14.0)

      expect(subject.send(:need_remove_extra_keyboards?)).to eq(true)
    end

    it "should return true for iOS 12" do
      expect(subject).to receive(:device_version).and_return(12.0)
      expect(subject.device_state).to receive(:remove_extra_keyboards_file_clean_on_weekend?).with(kind_of(Numeric)).and_return(true)

      expect(subject.send(:need_remove_extra_keyboards?)).to eq(true)
    end

    it "should return false for newer iOS devices if it was cleaned recently" do
      expect(subject).to receive(:device_version).and_return(12.0)
      expect(subject.device_state).to receive(:remove_extra_keyboards_file_clean_on_weekend?).with(kind_of(Numeric)).and_return(false)

      expect(subject.send(:need_remove_extra_keyboards?)).to eq(false)
    end

    it "should return true for iOS 13 for allowed region " do
      expect(subject).to receive(:device_version).and_return(13.0)
      subject.class.class_variable_set(:@@config, { "static_conf" => { "region" => "ap-south-1" } })
      expect(subject.device_state).to receive(:remove_extra_keyboards_file_clean_on_weekend?).with(kind_of(Numeric)).and_return(true)

      expect(subject.send(:need_remove_extra_keyboards?)).to eq(true)
    end

    it "should return false for iOS 13 for allowed region if cleaned already " do
      expect(subject).to receive(:device_version).and_return(13.0)
      subject.class.class_variable_set(:@@config, { "static_conf" => { "region" => "ap-south-1" } })
      expect(subject.device_state).to receive(:remove_extra_keyboards_file_clean_on_weekend?).with(kind_of(Numeric)).and_return(false)

      expect(subject.send(:need_remove_extra_keyboards?)).to eq(false)
    end
  end

  describe "#need_safari_url_bar_position_cleanup?" do
    it "should return false for older iOS devices" do
      expect(subject).to receive(:device_version).and_return(14.0)
      expect(subject.send(:need_safari_url_bar_position_cleanup?)).to eq(false)
    end

    it "should return true for newer iOS devices" do
      expect(subject).to receive(:device_version).and_return(15.0)
      expect(subject.device_state).to receive(:device_logger_detected_safari_url_bar_changed_file_present?).and_return(true)
      expect(subject.send(:need_safari_url_bar_position_cleanup?)).to eq(true)
    end

    it "should return false for newer iOS devices if url bar is not changed" do
      expect(subject).to receive(:device_version).and_return(15.0)
      expect(subject).to receive(:device_logger_reliable?).and_return(true)
      expect(subject.device_state).to receive(:device_logger_detected_safari_url_bar_changed_file_present?).and_return(false)
      expect(subject.send(:need_safari_url_bar_position_cleanup?)).to eq(false)
    end
  end

  describe "#need_siri_contacts_cleanup?" do
    it "should return false for older iOS devices" do
      expect(subject).to receive(:device_version).and_return(10.0)
      expect(subject.send(:need_siri_contacts_cleanup?)).to eq(false)
    end

    it "should return true for newer iOS devices" do
      expect(subject).to receive(:device_version).and_return(14.0)
      expect(subject.device_state).to receive(:siri_contacts_cleanup_file_older_than_days?).with(kind_of(Numeric)).and_return(true)
      expect(subject.send(:need_siri_contacts_cleanup?)).to eq(true)
    end
  end

  describe "#apple_wallet_cleanup" do
    let(:mock_cleanup_iphone) { double('cleanup_iphone') }
    let(:apple_wallet_output_no_pass) do
      '
      t =     4.46s             Wait for com.apple.Preferences to idle
      t =     6.40s Tap "App Store" StaticText
      t =     6.40s     Wait for com.apple.Preferences to idle
      t =     6.55s     Find the "App Store" StaticText
      t =     6.68s     Check for interrupting elements affecting "STORE" StaticText
      t =     6.71s     Synthesize event
      t =     6.90s         Scroll element to visible
      t =     6.92s         Find the "STORE" StaticText
      t =     7.04s     Wait for com.apple.Preferences to idle
      t =     7.64s Checking existence of `StaticText`
      t =     7.68s Waiting 5.0s for StaticText to exist
      t =     8.69s     Checking `Expect predicate `exists == 1` for object StaticText`
      t =     8.69s         Checking existence of `StaticText`
      t =     8.73s         Capturing element debug description
      t =     9.75s     Checking `Expect predicate `exists == 1` for object StaticText`
      t =     9.75s         Checking existence of `StaticText`
      t =     9.79s         Capturing element d
      '
    end

    let(:apple_wallet_output_with_pass ) do
      '
      t =     4.46s             Wait for com.apple.Preferences to idle
      t =     6.40s Tap "App Store" StaticText
      t =     6.40s     Wait for com.apple.Preferences to idle
      t =     6.55s     Find the "App Store" StaticText
      t =     6.68s     Check for interrupting elements affecting "STORE" StaticText
      t =     6.71s     Synthesize event
      t =     6.90s         Scroll element to visible
      t =     6.92s         Find the "STORE" StaticText
      t =     7.04s     Wait for com.apple.Preferences to idle
      t =     7.64s Checking existence of `StaticText`
      Removing a pass
      t =     7.68s Waiting 5.0s for StaticText to exist
      t =     8.69s     Checking `Expect predicate `exists == 1` for object StaticText`
      t =     8.69s         Checking existence of `StaticText`
      t =     8.73s         Capturing element debug description
      t =     9.75s     Checking `Expect predicate `exists == 1` for object StaticText`
      t =     9.75s         Checking existence of `StaticText`
      t =     9.79s         Capturing element d
      '
    end

    before do
      allow(CleanupIphone).to receive(:new).and_return(mock_cleanup_iphone)
    end

    context "device_version >= 13" do
      before do
        allow(subject).to receive(:device_version).and_return(14)
      end

      it "should push to zombie if any passes are removed" do
        expect(mock_cleanup_iphone).to receive(:apple_wallet_cleanup).and_return(apple_wallet_output_with_pass)
        expect(subject).to receive(:zombie).with("apple_wallet_passes_removed", "1")

        subject.apple_wallet_cleanup
      end

      it "should NOT push to zombie if there are no passes" do
        expect(mock_cleanup_iphone).to receive(:apple_wallet_cleanup).and_return(apple_wallet_output_no_pass)
        expect(subject).to_not receive(:zombie)

        subject.apple_wallet_cleanup
      end
    end
  end

  describe "#need_apple_wallet_cleanup?" do
    it "should return true for newer iOS devices" do
      expect(subject).to receive(:device_version).and_return(14.0)
      expect(subject).to receive(:device_type).and_return('iPhone')
      expect(subject.device_state).to receive(:device_logger_detected_apple_wallet_launched_file_present?).and_return(true)
      expect(subject.send(:need_apple_wallet_cleanup?)).to eq(true)
    end
  end

  describe "#apple_id_cleanup" do
    let(:mock_cleanup_iphone) { double('cleanup_iphone') }
    let(:found_apple_id_signout_output) do
      '
      t =    27.25s         Snapshot accessibility hierarchy for app with pid 906
      t =    27.39s Find the StaticText
      t =    27.39s     Snapshot accessibility hierarchy for app with pid 906
      [APPLE_ID_SIGNED_IN] Account: <EMAIL>
      Successfully fetched the Apple-ID, proceeding with signout.
      t =    27.46s Waiting 2.0s for "Sign Out" Cell to exist
      '
    end
    let(:not_found_apple_id_signout_output) do
      '
      Apple ID not signed in, cross checking
      No sign in according to isAppleIDPresentV2
          t =    13.86s Tear Down
      Closing any system popups
      '
    end
    let(:found_my_card_output) do
      '
      t =     4.94s Checking `Expect predicate `exists == 1` for object "Contacts" Button`
      t =     4.94s Checking existence of `"Contacts" Button`
      t =     5.06s Checking existence of `"Contacts" Button`
      t =     5.10s Tap "Contacts" Button
      t =     5.10s Wait for com.apple.mobilephone to idle
      t =     5.19s Find the "Contacts" Button
      t =     5.24s Check for interrupting elements affecting "Contacts" Button
      t =      nans     Synthesize event
      t =     5.63s Wait for com.apple.mobilephone to idle
      t =     5.74s Checking existence of `Other`
      tap My Card
      t =     5.83s Tap Other
      t =     5.83s Wait for com.apple.mobilephone to idle
      t =     5.92s Find the Other
      t =     5.99s Check for interrupting elements affecting "Usuario2 Apellido2, My Card" Other
      t =      nans     Synthesize event
      t =     6.38s Wait for com.apple.mobilephone to idle
      '
    end

    before do
      allow(CleanupIphone).to receive(:new).and_return(mock_cleanup_iphone)
      allow(subject.device_state).to receive(:signout_apple_id_via_wda_file_present?).and_return(false)
    end

    it "should push to zombie if any sandbox account is found and detected in device-logger" do
      expect(mock_cleanup_iphone).to receive(:apple_id_signout).and_return(found_apple_id_signout_output)
      expect(subject.device_state).to receive(:device_logger_detected_apple_id_signed_in_file_present?).and_return(true)
      expect(subject).to receive(:zombie).with("apple-id-signed-in", "<EMAIL>", { "data" => { device_logger_detected: true, xcuitest_detected: true, my_card_detected: nil, siri_accounts_detected: nil, wda_detected: nil } })

      subject.apple_id_cleanup
    end

    it "should push to zombie if any sandbox account is found and not detected in device-logger" do
      expect(mock_cleanup_iphone).to receive(:apple_id_signout).and_return(found_apple_id_signout_output)
      expect(subject.device_state).to receive(:device_logger_detected_apple_id_signed_in_file_present?).and_return(false)
      expect(subject).to receive(:zombie).with("apple-id-signed-in", "<EMAIL>", { "data" => { device_logger_detected: false, xcuitest_detected: true, my_card_detected: nil, siri_accounts_detected: nil, wda_detected: nil } })

      subject.apple_id_cleanup
    end

    it "should push to zombie if any my card is found in contact" do
      expect(mock_cleanup_iphone).to receive(:apple_id_signout).and_return(found_apple_id_signout_output)
      allow(subject).to receive(:device_version).and_return(14.0)
      allow(subject).to receive(:device_type).and_return("iPhone")
      expect(mock_cleanup_iphone).to receive(:clear_contact_accounts).and_return(found_my_card_output)
      expect(subject.device_state).to receive(:device_logger_detected_apple_id_signed_in_file_present?).and_return(false)
      expect(subject).to receive(:zombie).with("apple-id-signed-in", "<EMAIL>", { "data" => { device_logger_detected: false, xcuitest_detected: true, my_card_detected: true, siri_accounts_detected: false, wda_detected: nil } })

      subject.apple_id_cleanup
    end

    it "should not push to zombie if any sandbox account is found" do
      expect(mock_cleanup_iphone).to receive(:apple_id_signout).and_return(not_found_apple_id_signout_output)
      expect(subject).to_not receive(:zombie).with("apple-id-signed-in")

      subject.apple_id_cleanup
    end
  end

  describe "#sandbox_account_cleanup" do
    let(:mock_cleanup_iphone) { double('cleanup_iphone') }
    let(:signout_sandbox_output_no_account ) do
      '
      t =     4.46s             Wait for com.apple.Preferences to idle
      t =     6.40s Tap "App Store" StaticText
      t =     6.40s     Wait for com.apple.Preferences to idle
      t =     6.55s     Find the "App Store" StaticText
      t =     6.68s     Check for interrupting elements affecting "STORE" StaticText
      t =     6.71s     Synthesize event
      t =     6.90s         Scroll element to visible
      t =     6.92s         Find the "STORE" StaticText
      t =     7.04s     Wait for com.apple.Preferences to idle
      t =     7.64s Checking existence of `StaticText`
      No sandbox account found.
      t =     7.68s Waiting 5.0s for StaticText to exist
      t =     8.69s     Checking `Expect predicate `exists == 1` for object StaticText`
      t =     8.69s         Checking existence of `StaticText`
      t =     8.73s         Capturing element debug description
      t =     9.75s     Checking `Expect predicate `exists == 1` for object StaticText`
      t =     9.75s         Checking existence of `StaticText`
      t =     9.79s         Capturing element d
      '
    end

    let(:signout_sandbox_output_with_account ) do
      '
      t =     4.46s             Wait for com.apple.Preferences to idle
      t =     6.40s Tap "App Store" StaticText
      t =     6.40s     Wait for com.apple.Preferences to idle
      t =     6.55s     Find the "App Store" StaticText
      t =     6.68s     Check for interrupting elements affecting "STORE" StaticText
      t =     6.71s     Synthesize event
      t =     6.90s         Scroll element to visible
      t =     6.92s         Find the "STORE" StaticText
      t =     7.04s     Wait for com.apple.Preferences to idle
      t =     7.64s Checking existence of `StaticText`
      Tap on Apple ID:
      t =     7.68s Waiting 5.0s for StaticText to exist
      t =     8.69s     Checking `Expect predicate `exists == 1` for object StaticText`
      t =     8.69s         Checking existence of `StaticText`
      t =     8.73s         Capturing element debug description
      t =     9.75s     Checking `Expect predicate `exists == 1` for object StaticText`
      t =     9.75s         Checking existence of `StaticText`
      t =     9.79s         Capturing element d
      '
    end

    before do
      allow(CleanupIphone).to receive(:new).and_return(mock_cleanup_iphone)
    end

    it "should push to zombie if any sandbox account is found and not detected in device-logger" do
      expect(mock_cleanup_iphone).to receive(:sign_out_sandbox_accounts).and_return(signout_sandbox_output_with_account)
      expect(subject.device_state).to receive(:device_logger_detected_sandbox_signed_in_file_present?).and_return(false)
      expect(subject).to receive(:zombie).with("sandbox-apple-id-signed-in", "", { "data" => { device_logger_detected: false, xcuitest_detected: true, improved_logline_detected_by_devicelogger: false, popup_logline_detected_by_devicelogger: false } })

      subject.sandbox_account_cleanup
    end

    it "should push to zombie if any sandbox account is found and detected in device-logger" do
      expect(mock_cleanup_iphone).to receive(:sign_out_sandbox_accounts).and_return(signout_sandbox_output_with_account)
      expect(subject.device_state).to receive(:device_logger_detected_sandbox_signed_in_file_present?).and_return(true)
      expect(subject).to receive(:zombie).with("sandbox-apple-id-signed-in", "", { "data" => { device_logger_detected: true, xcuitest_detected: true, improved_logline_detected_by_devicelogger: false, popup_logline_detected_by_devicelogger: false } })

      subject.sandbox_account_cleanup
    end

    it "should not push to zombie if any sandbox account is found" do
      expect(mock_cleanup_iphone).to receive(:sign_out_sandbox_accounts).and_return(signout_sandbox_output_no_account)
      expect(subject).to_not receive(:zombie).with("sandbox-apple-id-signed-in")

      subject.sandbox_account_cleanup
    end
  end

  describe "#trust_client_enterprise_app" do
    let(:device_details) { { device: "uuid", appium_port: 8201 } }
    let(:random_dist_name) { "random_dist_name" }

    before do
      allow(subject).to receive(:get_device_details_for_app_trust).and_return(device_details)
    end

    it "perform trust when dist_name is non-empty" do
      enterprise_app_trust = double
      expect(EnterpriseAppTrust).to receive(:new)
        .with(uuid, device_config, device_details, random_dist_name)
        .and_return(enterprise_app_trust)
      expect(enterprise_app_trust).to receive(:perform_action).with('trust')
                                                              .at_least(:once).and_return(true)

      subject.trust_client_enterprise_app(random_dist_name)
    end

    it "perform trust when dist_name is not passed" do
      enterprise_app_trust = double
      expect(EnterpriseAppTrust).to receive(:new)
        .with(uuid, device_config, device_details, "").and_return(enterprise_app_trust)
      expect(enterprise_app_trust).to receive(:perform_action)
        .with('trust').at_least(:once).and_return(true)

      subject.trust_client_enterprise_app
    end

    it "should raise expection when app trust failed" do
      enterprise_app_trust = double
      expect(EnterpriseAppTrust).to receive(:new)
        .with(uuid, device_config, device_details, random_dist_name)
        .and_return(enterprise_app_trust)
      expect(enterprise_app_trust).to receive(:perform_action)
        .with('trust').at_least(:once).and_return(false)

      expect { subject.trust_client_enterprise_app(random_dist_name) }
        .to raise_error("EnterpriseApp trust failed for #{uuid}: All retries exhausted!")
    end

    it "should raise expection when device_details doesn't contain appium_port" do
      device_details.delete(:appium_port)
      enterprise_app_trust = double
      expect(EnterpriseAppTrust).to receive(:new)
        .with(uuid, device_config, device_details, random_dist_name)
        .and_return(enterprise_app_trust)
      expect(enterprise_app_trust).not_to receive(:perform_action)

      expect { subject.trust_client_enterprise_app(random_dist_name) }
        .to raise_error("Appium port to send trust commands not found")
    end
  end

  describe '#check_device_logger_reliability' do
    context 'When BM file is present' do
      before do
        allow(subject.device_state).to receive(:session_bm_file_present?).and_return(true)
        allow(subject.device_state).to receive(:remove_session_bm_file)
      end

      it 'should set @device_logger_reliable = true and remove BM file' do
        expect(subject).to receive(:zombie).with('bm-occurred')
        subject.send(:check_device_logger_reliability)
        expect(subject.instance_variable_get(:@device_logger_reliable)).to be_truthy
      end
    end
    context 'Device-logger pid file absent' do
      before do
        allow(File).to receive(:exist?).with(subject.device_state.send(:session_bm_file)).and_return(false)
        allow(File).to receive(:exist?).with(subject.device_state.send(:device_logger_pid_file))
                                       .and_return(false)
      end

      it 'should set @device_logger_reliable = false' do
        allow(subject).to receive(:zombie)
        allow(File).to receive(:exist?).with(subject.device_state.send(:manual_cleanup_file))

        subject.send(:check_device_logger_reliability)

        expect(subject.instance_variable_get(:@device_logger_reliability)).to be_falsey
      end

      it 'should push to zombie for non-manual cleanup' do
        allow(File).to receive(:exist?).with(subject.device_state.send(:manual_cleanup_file))
                                       .and_return(false)

        expect(subject).to receive(:zombie).with('device-logger-pid-file-absent')

        subject.send(:check_device_logger_reliability)
      end

      it 'should not push to zombie for manual cleanup' do
        allow(File).to receive(:exist?).with(subject.device_state.send(:manual_cleanup_file))
                                       .and_return(true)

        expect(subject).not_to receive(:zombie)

        subject.send(:check_device_logger_reliability)
      end
    end

    context 'Device-logger pid file is present' do
      let(:device_logger_pid) { '123' }
      before do
        allow(File).to receive(:exist?).with(subject.device_state.send(:session_bm_file)).and_return(false)
        allow(File).to receive(:exist?).with(subject.device_state.send(:device_logger_pid_file))
                                       .and_return(true)
        allow(subject).to receive(:zombie).with('cleanup-device-logger-reliability', '', anything)
      end

      it "should set @device_logger_reliable = true if device-logger process didn't crash" do
        device_logger_current_pids = "123\n456"
        allow(BrowserStack::OSUtils).to receive(:execute)
          .with("ps -ef | grep '[d]evice-logger' | awk '{print $2}'")
          .and_return(device_logger_current_pids)
        allow(Utils).to receive(:read_file_in_array)
          .with(subject.device_state.send(:device_logger_pid_file))
          .and_return([device_logger_pid])

        subject.send(:check_device_logger_reliability)

        expect(subject.instance_variable_get(:@device_logger_reliable)).to be_truthy
      end

      it "should set @device_logger_reliable = false if device-logger process crashed" do
        device_logger_current_pids = "456\n789"
        allow(BrowserStack::OSUtils).to receive(:execute)
          .with("ps -ef | grep '[d]evice-logger' | awk '{print $2}'")
          .and_return(device_logger_current_pids)
        allow(Utils).to receive(:read_file_in_array)
          .with(subject.device_state.send(:device_logger_pid_file))
          .and_return([device_logger_pid])

        subject.send(:check_device_logger_reliability)

        expect(subject.instance_variable_get(:@device_logger_reliable)).to be_falsey
      end
    end
  end

  describe '#device_logger_reliable?' do
    it 'should return true if @device_logger_reliable is true' do
      subject.instance_variable_set(:@device_logger_reliable, true)

      expect(subject.send(:device_logger_reliable?)).to be_truthy
    end

    it 'should return false if @device_logger_reliable is false' do
      subject.instance_variable_set(:@device_logger_reliable, false)

      expect(subject.send(:device_logger_reliable?)).to be_falsey
    end
  end

  describe '#wifi_enabled?' do
    let(:mock_wda_client) { double }

    before(:each) do
      expect(subject).to receive(:wda_client).and_return(mock_wda_client)
    end

    it 'should return false if wda client returns Disabled' do
      expect(mock_wda_client).to receive(:get_wifi_status).and_return({ "value" => "Disabled" })
      expect(subject.send(:wifi_enabled?)).to be_equal(false)
    end

    it 'should return true if wda client returns Enabled' do
      expect(mock_wda_client).to receive(:get_wifi_status).and_return({ "value" => "Enabled" })
      expect(subject.send(:wifi_enabled?)).to be_equal(true)
    end

    it 'should return false if wda client raises any Exception' do
      allow(mock_wda_client).to receive(:get_wifi_status).and_raise("Connection Refused")
      expect(subject.send(:wifi_enabled?)).to be_equal(false)
    end

    it 'should return false if wda client sends empty response' do
      expect(mock_wda_client).to receive(:get_wifi_status).and_return({ "value" => "" })
      expect(subject.send(:wifi_enabled?)).to be_equal(false)
    end
  end

  describe '#check_wifi_status' do
    it "should touch wifi_enabled file if wifi is enabled" do
      allow(subject).to receive(:wifi_enabled?).and_return(true)
      expect_any_instance_of(DeviceState).to receive(:touch_wifi_enabled_file).and_return(true)

      subject.send(:check_wifi_status)
    end

    it "should not touch wifi_enabled file if wifi is disabled" do
      allow(subject).to receive(:wifi_enabled?).and_return(false)
      expect_any_instance_of(DeviceState).to_not receive(:touch_wifi_enabled_file)

      subject.send(:check_wifi_status)
    end
  end

  describe '#set_time_to_utc' do
    let(:mock_cleanup_iphone) { double('cleanup_iphone') }
    let(:device_timestamp) { 100.0 }
    let(:current_timestamp) { 200.0 }
    let(:xcui_output) { "some random output\nTime interval since 1970: #{device_timestamp}\nsome more output" }

    before do
      allow(CleanupIphone).to receive(:new).and_return(mock_cleanup_iphone)
      Timecop.freeze(Time.at(current_timestamp))
    end

    after do
      Timecop.return
    end

    it "should run the xcui test & calculate the drift" do
      expect(mock_cleanup_iphone).to receive(:set_time_to_utc).and_return(xcui_output)
      expect(subject).to receive(:zombie).with('idevice-time-drift', '', { 'data' => (current_timestamp - device_timestamp) })
      subject.send(:set_time_to_utc)
    end
  end

  context "#connected_to_browserstack_wifi?" do
    let(:wifi_name) { "BLT iOS" }
    before(:each) do
      subject.class.class_variable_set(:@@config, { "static_conf" => { "ssid" => wifi_name } })
    end

    it "should return false when connected to non BLT network" do
      output = "Cell, 0x283279a00, {{0.0, 433.7}, {375.0, 44.0}}, label: 'Wi-Fi', value: BTIRedside"
      expect(subject.connected_to_browserstack_wifi?(output)).to eq(false)
    end

    it "should return true when connected to BLT network" do
      output = "Cell, 0x283279a00, {{0.0, 433.7}, {375.0, 44.0}}, label: 'Wi-Fi', value: #{wifi_name}"
      expect(subject.connected_to_browserstack_wifi?(output)).to eq(true)
    end

    it "should return true when wifi name is not in the output" do
      output = "some random output\nTime interval since 1970: 123123\nsome more output"
      expect(subject.connected_to_browserstack_wifi?(output)).to eq(true)
    end
  end

  describe "#need_contacts_cleanup?" do
    before(:each) do
      allow(subject).to receive(:verify_contacts_count)
    end

    context "when ios <= 13.2" do
      before do
        expect(subject).to receive(:device_version).and_return(12)
      end
      it "should return true" do
        expect(subject.send(:need_contacts_cleanup?)).to eq(true)
      end
    end

    context "when ios > 13.2" do
      before do
        expect(subject).to receive(:device_version).and_return(14)
      end

      context 'when contacts are modified or added' do
        before(:each) do
          allow(subject.device_state).to receive(:contacts_modified_file_present?).and_return(true)
        end

        it 'should return true' do
          expect(subject.send(:need_contacts_cleanup?)).to eq(true)
        end
      end

      context 'wehn contacts are not modified or added' do
        before(:each) do
          allow(subject.device_state).to receive(:contacts_modified_file_present?).and_return(false)
          allow(subject).to receive(:verify_contacts_count).and_return(true)
        end

        it 'should return false' do
          expect(subject.send(:need_contacts_cleanup?)).to eq(false)
        end
      end
    end
  end

  describe "get details of media" do
    it "should return s3 url, filename and filetype" do
      media = { "s3_url" => "something", "filename" => "test", "filetype" => "document" }
      expect(BrowserStack::IPhone.send(:get_file_details, media)).to eq(["something", "test", "document"])
    end
  end

  describe "check media files and push to device" do
    it "should push to browserstack app's folder if the media type is image" do
      expect(BrowserStack::IPhone).to receive(:push_media_file_to_device).with("test.jpg", ".jpg", "/some_path", "Photos", "some_device", "com.browserstack.app")

      BrowserStack::IPhone.send(:check_and_push_media_files, "image", "/some_path", "test.jpg", "some_device", "some_bundle", "session_id", "feature_usage")
    end

    it "should push to browserstack app's folder if the media type is video" do
      expect(BrowserStack::IPhone).to receive(:push_media_file_to_device).with("test.mp4", ".mp4", "/some_path", "Videos", "some_device", "com.browserstack.app")

      BrowserStack::IPhone.send(:check_and_push_media_files, "video", "/some_path", "test.mp4", "some_device", "some_bundle", "session_id", "feature_usage")
    end

    it "should push to user app's folder if the media type is document" do
      expect(BrowserStack::IPhone).to receive(:push_media_file_to_device).with("test", ".pdf", "/some_path", "Custom_Files", "some_device", "some_bundle")

      BrowserStack::IPhone.send(:check_and_push_media_files, "documents", "/some_path", "test.pdf", "some_device", "some_bundle", "session_id", "feature_usage")
    end
  end

  describe '#check_device_language' do
    it "should return if device language is english and add_new_language file created in less than 7 day old" do
      subject.instance_variable_set(:@device_config, device_config_ios14)
      subject.class.class_variable_set(:@@config, { "static_conf" => { "sub_region" => "us-east-1d" } })
      expect(IdeviceUtils).to receive(:device_language).and_return('en')
      expect(subject.device_state).to receive(:language_file_clean_on_weekend?).with(7).once.and_return(false)
      expect(subject).not_to receive(:get_additinonal_language)
      subject.check_device_language
    end

    it "should return if device language is english and device version is less than 13" do
      subject.instance_variable_set(:@device_config, device_config)
      expect(IdeviceUtils).to receive(:device_language).and_return('en')
      expect(subject.device_state).not_to receive(:language_file_present?)
      expect(subject.device_state).not_to receive(:language_file_older_than_days?)
      expect(subject).not_to receive(:get_additinonal_language)
      subject.check_device_language
    end

    it "should add language if device language is english and add_new_language file created in more than 7 day old" do
      languageUtilMock = double('LanguageUtilMock')
      subject.instance_variable_set(:@device_config, device_config_ios14)
      subject.class.class_variable_set(:@@config, { "static_conf" => { "sub_region" => "us-east-1d" } })
      subject.instance_variable_set(:@uuid, "1234")
      expect(IdeviceUtils).to receive(:device_language).once.and_return('en')
      expect(languageUtilMock).to receive(:find_language).once.and_return('en')
      expect(subject.device_state).to receive(:language_file_clean_on_weekend?).with(7).once.and_return(true)
      expect(LanguageUtil).to receive(:new).and_return(languageUtilMock)
      expect(languageUtilMock).to receive(:update_language).twice.and_return(true)
      # expect(IdeviceUtils).to receive(:device_language).and_return('en')
      expect(subject.device_state).to receive(:touch_language_file).and_return(nil)
      subject.check_device_language
    end

    it "should add language if device language is english and add_new_language file is not present" do
      languageUtilMock = double('LanguageUtilMock')
      subject.instance_variable_set(:@device_config, device_config_ios14)
      subject.class.class_variable_set(:@@config, { "static_conf" => { "sub_region" => "us-east-1d" } })
      subject.instance_variable_set(:@uuid, "1234")
      expect(IdeviceUtils).to receive(:device_language).once.and_return('en')
      expect(subject.device_state).to receive(:language_file_clean_on_weekend?).once.and_return(true)
      expect(languageUtilMock).to receive(:find_language).once.and_return('en')
      expect(LanguageUtil).to receive(:new).and_return(languageUtilMock)
      expect(languageUtilMock).to receive(:update_language).twice.and_return(true)
      expect(subject.device_state).to receive(:touch_language_file).and_return(nil)
      subject.check_device_language
    end

    it "should push to zombie when some error is raised while adding language" do
      languageUtilMock = double('LanguageUtilMock')
      subject.instance_variable_set(:@device_config, device_config_ios14)
      subject.instance_variable_set(:@uuid, "1234")
      subject.class.class_variable_set(:@@config, { "static_conf" => { "sub_region" => "us-east-1d" } })
      expect(IdeviceUtils).to receive(:device_language).once.and_return('en')
      expect(subject.device_state).to receive(:language_file_clean_on_weekend?).once.and_return(true)
      expect(LanguageUtil).to receive(:new).and_return(languageUtilMock)
      expect(languageUtilMock).to receive(:find_language).once.and_return('en')
      expect(languageUtilMock).to receive(:update_language).twice.and_raise(StandardError, "Some error occured")
      expect(subject.device_state).not_to receive(:touch_language_file)
      expect(subject).to receive(:zombie).with("device-language", "", { "data" => "en" }).and_return(nil)
      expect(subject).to receive(:zombie).with("alanguage-addition-failed", "StandardError - Some error occured").and_return(nil)
      expect(subject).to receive(:zombie).with("language-addition-failed", "StandardError - Some error occured").and_return(nil)
      subject.check_device_language
    rescue => e
      expect(e.message).to eq("Some exception occured while adding language to the device")
    end

    it "should raise error if device language is not english after adding new languages" do
      languageUtilMock = double('LanguageUtilMock')
      subject.instance_variable_set(:@device_config, device_config_ios14)
      subject.class.class_variable_set(:@@config, { "static_conf" => { "sub_region" => "us-east-1d" } })
      subject.instance_variable_set(:@uuid, "1234")
      expect(IdeviceUtils).to receive(:device_language).once.and_return('en')
      expect(subject.device_state).to receive(:language_file_clean_on_weekend?).once.and_return(true)
      expect(LanguageUtil).to receive(:new).and_return(languageUtilMock)
      expect(languageUtilMock).to receive(:find_language).once.and_return('hi')
      expect(languageUtilMock).to receive(:update_language).twice.and_return(true)
      expect(subject.device_state).not_to receive(:touch_language_file)
      expect { subject.check_device_language }.to raise_error(StandardError, OFFLINE_REASON_NEED_LANGUAGE_RESET)
    end
  end

  describe '#setup_voiceover_persistent_settings' do
    let(:uuid) { "00008110-000065AC02D1401E" }
    let(:session_id_value) { "10dd36c43f31469100a51d3e278e2e2e28121f1f" }
    let(:wda_client) { double("WdaClient") }
    let(:voiceover_data_to_push) { { event_name: "setVoiceOverPersistentSettings", product: "live", team: "live" } }

    before do
      allow(subject).to receive(:session_id).and_return(session_id_value)
      allow(subject).to receive(:wda_client).and_return(wda_client)
    end

    context "when device is a voiceover device" do
      it "logs success and sends success data to EDS" do
        allow(BrowserStack::VoiceoverHelper).to receive(:voiceover_device?).with(uuid).and_return(true)
        allow(wda_client).to receive(:set_voiceover_persistent_settings).and_return({ "value": { "result": "success" } })
        expect(BrowserStack.logger).to receive(:info).with("It is a voiceover device. Running WDA set_voiceover_persistent_settings")
        expect(wda_client).to receive(:set_voiceover_persistent_settings)

        success_event_json = { session_id: session_id_value, set_voiceover_persistent_settings: "success" }
        expect(Utils).to receive(:send_to_eds).with(voiceover_data_to_push.merge(event_json: success_event_json), "web_events", true)

        subject.setup_voiceover_persistent_settings
      end

      it "logs error and sends failure data to EDS on exception" do
        error_message = "Failed to set voiceover persistent settings"
        allow(BrowserStack::VoiceoverHelper).to receive(:voiceover_device?).with(uuid).and_return(true)
        allow(wda_client).to receive(:set_voiceover_persistent_settings).and_raise(StandardError.new(error_message))

        expect(BrowserStack.logger).to receive(:info).with("It is a voiceover device. Running WDA set_voiceover_persistent_settings")
        expect(BrowserStack.logger).to receive(:error).with(/StandardError #{error_message}/)

        failure_event_json = { session_id: session_id_value, set_voiceover_persistent_settings: "failed" }
        expect(Utils).to receive(:send_to_eds).with(voiceover_data_to_push.merge(event_json: failure_event_json), "web_events", true)

        subject.setup_voiceover_persistent_settings
      end
    end

    context "when device is not a voiceover device" do
      it "does not call WDA and logs not a voiceover device" do
        allow(BrowserStack::VoiceoverHelper).to receive(:voiceover_device?).with(uuid).and_return(false)
        expect(BrowserStack.logger).to receive(:info).with("Not a voiceover device. Not running WDA set_voiceover_persistent_settings")
        expect(wda_client).not_to receive(:set_voiceover_persistent_settings)

        subject.setup_voiceover_persistent_settings
      end
    end
  end
end
