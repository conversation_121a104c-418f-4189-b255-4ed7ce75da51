require '/usr/local/.browserstack/realmobile/server/mobile_cspt'

describe MCSPT do
  let(:device_id) { 'device_id' }
  let(:session_id) { 'session_id' }
  let(:bundle_id) { 'bundle_id' }
  let(:origin) { 'origin' }
  let(:analytics) { 'analytics' }
  let(:app_relaunch) { 'app_relaunch' }
  let(:user_id) { '100000' }
  let(:app_live_session_id) { 'app_live_session_id' }
  let(:app_automate_session_id) { 'app_automate_session_id' }
  let(:app_live) { 'app-live' }
  let(:app_automate) { 'app-automate' }
  let(:s3_url) { 's3_url' }
  let(:s3_id) { 's3_id' }
  let(:s3_key) { 's3_key' }
  let(:pusher_url) { 'pusher_url' }
  let(:pusher_channel) { 'pusher_channel' }
  let(:pusher_auth) { 'pusher_auth' }
  let(:message) { 'message' }

  subject { MCSPT }

  describe "#get_running_session" do
    it "should give the running CSPT session for the given app live session" do
      expect(subject).to receive(:get_session_file_path).with(app_live_session_id).and_return(app_live_session_id.to_s)
      expect(File).to receive(:exist?).with(app_live_session_id.to_s).and_return(true)
      expect(File).to receive(:read).with(app_live_session_id.to_s).and_return(app_live_session_id.to_s)
      subject.get_running_session(app_live_session_id)
    end
  end

  describe "#set_running_session" do
    it "should add the running CSPT session record for the given app live session id" do
      expect(subject).to receive(:get_session_file_path).with(app_live_session_id).and_return(app_live_session_id.to_s)
      expect(Dir).to receive(:exist?).and_return(true)
      expect(FileUtils).not_to receive(:mkdir_p)
      expect(File).to receive(:open)
      subject.set_running_session(app_live_session_id, session_id)
    end
  end

  describe "#delete_running_session" do
    it "should delete the running CSPT session record" do
      expect(subject).to receive(:get_session_file_path).with(app_live_session_id).and_return(app_live_session_id.to_s)
      expect(File).to receive(:exist?).with(app_live_session_id.to_s).and_return(true)
      expect(FileUtils).to receive(:rm_f).with(app_live_session_id.to_s)
      subject.delete_running_session(app_live_session_id)
    end
  end

  describe "#get_session_running_on_device" do
    it "should give session id and user id from device file" do
      expect(subject).to receive(:get_device_file_path).with(device_id).and_return(device_id.to_s)
      expect(File).to receive(:exist?).with(device_id.to_s).and_return(true)
      expect(File).to receive(:read).with(device_id.to_s).and_return("#{app_live_session_id},#{user_id}")
      subject.get_session_running_on_device(device_id)
    end
  end

  describe "#get_app_details" do
    it "should get app details" do
      expect(subject).to receive(:send_request).and_return([200, { "apps" => {} }])
      subject.get_app_details(device_id, bundle_id, origin)
    end
  end

  describe "#get_analytics" do
    it "should get analytics data" do
      expect(subject).to receive(:get_running_session).with(app_live_session_id).and_return(session_id)
      expect(subject).to receive(:get_app_details).with(device_id, bundle_id, analytics).and_return({})
      subject.get_analytics(app_live_session_id, device_id, bundle_id)
    end
  end

  describe "#copy_session_info" do
    it "should get session data" do
      expect(FileUtils).to receive(:copy_file).with("/tmp/_mobile/session/#{device_id}/session_info.json",
                                                    "#{MCSPT_DEVICE_FILES_DIR}/#{device_id}_session_info.json")
      subject.copy_session_info(device_id)
    end
  end

  describe "#get_session_info" do
    it "should give session info" do
      expect(File).to receive(:exist?).with("#{MCSPT_DEVICE_FILES_DIR}/#{device_id}_session_info.json").and_return(true)
      expect(File).to receive(:read).with(
        "#{MCSPT_DEVICE_FILES_DIR}/#{device_id}_session_info.json"
      ).and_return("{\"s3\":{\"stats\":{\"id\":\"#{s3_id}\",\"key\":\"#{s3_key}\",\"url\":\"#{s3_url}\"}}}")
      expect(JSON).to receive(:parse).with(
        "{\"s3\":{\"stats\":{\"id\":\"#{s3_id}\",\"key\":\"#{s3_key}\",\"url\":\"#{s3_url}\"}}}"
      ).and_return({ "s3" => { "stats" => { "id" => s3_id.to_s, "key" => s3_key.to_s, "url" => s3_url.to_s } } })
      subject.get_session_info(device_id)
    end
  end

  describe "#get_session_info" do
    it "should give empty json when session file is absent" do
      expect(File).to receive(:exist?).with(
        "#{MCSPT_DEVICE_FILES_DIR}/#{device_id}_session_info.json"
      ).and_return(false)
      subject.get_session_info(device_id)
    end
  end

  describe "#get_pusher_params" do
    it "should give pusher details" do
      expect(File).to receive(:exist?).with("#{BS_DIR}/state_files/#{device_id}_session").and_return(true)
      expect(File).to receive(:read).with("#{BS_DIR}/state_files/#{device_id}_session").and_return(
        "{\"pusher_url\":\"#{pusher_url}\",\"pusher_channel\":\"#{pusher_channel}\",\"pusher_auth\":\"#{pusher_auth}\"}"
      )
      expect(JSON).to receive(:parse).with(
        "{\"pusher_url\":\"#{pusher_url}\",\"pusher_channel\":\"#{pusher_channel}\",\"pusher_auth\":\"#{pusher_auth}\"}"
      ).and_return({ "pusher_url" => pusher_url, "pusher_channel" => pusher_channel, "pusher_auth" => pusher_auth })
      subject.get_pusher_params(device_id)
    end
  end

  describe "#get_pusher_params" do
    it "should give nil if session file not present" do
      expect(File).to receive(:exist?).with("#{BS_DIR}/state_files/#{device_id}_session").and_return(false)
      subject.get_pusher_params(device_id)
    end
  end

  describe "#notify_pusher" do
    it "should send event to pusher" do
      expect(subject).to receive(:get_pusher_params).with(device_id).and_return(
        { "pusher_url" => pusher_url, "pusher_channel" => pusher_channel, "pusher_auth" => pusher_auth }
      )
      expect(URI).to receive(:encode_www_form)
      expect(OSUtils).to receive(:execute)
      subject.notify_pusher(device_id, app_automate_session_id, message)
    end
  end

  describe "#upload_session_logs_to_s3" do
    it "should upload session report to s3" do
      expect(File).to receive(:open)
      expect(File).to receive(:size).with("/tmp/mcspt_session_report_#{app_automate_session_id}.json").and_return(2048)
      expect(subject).to receive(:get_session_info).with(device_id).and_return(
        { "s3_id": s3_id, "s3_key": s3_key, "s3_url": s3_url }
      )
      expect(Utils).to receive(:upload_file_to_s3).and_return([true, nil])
      expect(File).to receive(:delete).with("/tmp/mcspt_session_report_#{app_automate_session_id}.json")
      subject.upload_session_logs_to_s3(device_id, app_automate_session_id, {})
    end
  end

  describe "#start_session" do
    it "should start the CSPT session" do
      expect(subject).to receive(:send_request).and_return([200, { "sessionID" => session_id, "status" => "Success" }])
      expect(subject).to receive(:set_running_session).with(app_live_session_id, session_id)
      expect(Dir).to receive(:exist?).and_return(false)
      expect(FileUtils).to receive(:mkdir_p)
      expect(subject).to receive(:get_device_file_path)
      expect(File).to receive(:open)
      allow_any_instance_of(Object).to receive(:zombie_push)
      subject.start_session(app_live_session_id, user_id, device_id, bundle_id, app_relaunch)
    end
  end

  describe "#start_session" do
    it "should start the CSPT session for app automate" do
      expect(subject).to receive(:send_request).and_return([200, { "sessionID" => session_id, "status" => "Success" }])
      expect(subject).to receive(:set_running_session).with(app_automate_session_id, session_id)
      expect(Dir).to receive(:exist?).and_return(false)
      expect(FileUtils).to receive(:mkdir_p)
      expect(subject).to receive(:get_device_file_path)
      expect(File).to receive(:open)
      expect(subject).to receive(:copy_session_info).with(device_id)
      allow_any_instance_of(Object).to receive(:zombie_push)
      subject.start_session(app_automate_session_id, user_id, device_id, bundle_id, app_relaunch, app_automate: true)
    end
  end

  describe "#start_session" do
    it "should not set running session if start session API fails" do
      expect(subject).to receive(:get_device_file_path)
      expect(subject).to receive(:send_request).and_return([400, { "status" => "Failure" }])
      expect(subject).not_to receive(:set_running_session)
      expect(FileUtils).not_to receive(:mkdir_p)
      expect(FileUtils).not_to receive(:open)
      allow_any_instance_of(Object).to receive(:zombie_push)
      subject.start_session(app_live_session_id, user_id, device_id, bundle_id, app_relaunch)
    end
  end

  describe "#start_session" do
    it "should start CSPT session if older session timed out" do
      expect(subject).to receive(:get_device_file_path)
      expect(subject).to receive(:send_request).and_return([200, { "sessionID" => session_id, "status" => "Success" }])
      expect(subject).to receive(:set_running_session)
      expect(FileUtils).to receive(:mkdir_p)
      expect(File).to receive(:open)
      allow_any_instance_of(Object).to receive(:zombie_push)
      subject.start_session(app_live_session_id, user_id, device_id, bundle_id, app_relaunch)
    end
  end

  describe "#start_session" do
    it "should not set running session if start session API fails" do
      expect(subject).to receive(:get_device_file_path)
      expect(subject).to receive(:send_request).and_return([400, { "status" => "Failure" }])
      expect(subject).not_to receive(:set_running_session)
      expect(FileUtils).not_to receive(:mkdir_p)
      expect(FileUtils).not_to receive(:open)
      expect(subject).not_to receive(:copy_session_info).with(device_id)
      allow_any_instance_of(Object).to receive(:zombie_push)
      expect(Utils).to receive(:send_general_feature_usage_data_to_eds).with(
        app_automate_session_id, CSPT_FEATURE, false, GENRE_APP_AUTOMATE, "Session start failure: Failure"
      )
      subject.start_session(app_automate_session_id, user_id, device_id, bundle_id, app_relaunch, app_automate: true)
    end
  end

  describe "#start_session" do
    it "should start CSPT session if older session timed out" do
      expect(subject).to receive(:get_device_file_path)
      expect(subject).to receive(:send_request).and_return([200, { "sessionID" => session_id, "status" => "Success" }])
      expect(subject).to receive(:set_running_session)
      expect(FileUtils).to receive(:mkdir_p)
      expect(File).to receive(:open)
      expect(subject).to receive(:copy_session_info).with(device_id)
      allow_any_instance_of(Object).to receive(:zombie_push)
      subject.start_session(app_automate_session_id, user_id, device_id, bundle_id, app_relaunch, app_automate: true)
    end
  end

  describe "#stop_session" do
    it "should stop the CSPT session" do
      expect(subject).to receive(:get_running_session).with(app_live_session_id).and_return(session_id)
      expect(subject).to receive(:get_session_running_on_device).with(device_id).and_return(
        [app_live_session_id, user_id, app_live]
      )
      expect(subject).to receive(:delete_running_session).with(app_live_session_id)
      expect(subject).to receive(:send_request)
      expect(subject).to receive(:get_device_file_path)
      expect(File).to receive(:exist?).and_return(true)
      expect(FileUtils).to receive(:rm_f)
      expect(subject).to receive(:get_session_info_file_path)
      allow_any_instance_of(Object).to receive(:zombie_push)
      subject.stop_session(app_live_session_id, user_id, device_id, cancelled: false)
    end
  end

  describe "#stop_session" do
    it "should stop the CSPT session for app automate" do
      expect(subject).to receive(:get_running_session).with(app_automate_session_id).and_return(session_id)
      expect(subject).to receive(:get_session_running_on_device).with(device_id).and_return(
        [app_automate_session_id, user_id, app_automate]
      )
      expect(subject).to receive(:delete_running_session).with(app_automate_session_id)
      expect(subject).to receive(:get_session_report_file_path)
      expect(File).to receive(:exist?).and_return(false)
      expect(subject).to receive(:send_request).and_return([200, { "status" => "Session Stopped" }])
      expect(subject).to receive(:get_device_file_path)
      expect(File).to receive(:exist?).and_return(true)
      expect(FileUtils).to receive(:rm_f)
      expect(File).to receive(:exist?).and_return(true)
      expect(FileUtils).to receive(:rm_f)
      expect(subject).to receive(:upload_session_logs_to_s3).with(
        device_id, app_automate_session_id, { "status" => "Session Stopped" }
      ).and_return([true, nil])
      expect(File).to receive(:exist?).and_return(true)
      expect(FileUtils).to receive(:rm_f)
      expect(subject).to receive(:notify_pusher).with(
        device_id, app_automate_session_id, "SUCCESS"
      )
      expect(Utils).to receive(:send_general_feature_usage_data_to_eds).with(
        app_automate_session_id, CSPT_FEATURE, true, GENRE_APP_AUTOMATE, ""
      )
      allow_any_instance_of(Object).to receive(:zombie_push)
      subject.stop_session(app_automate_session_id, user_id, device_id, cancelled: false)
    end
  end

  describe "#stop_session" do
    it "should not upload session logs if session stop fails" do
      expect(subject).to receive(:get_running_session).with(app_automate_session_id).and_return(session_id)
      expect(subject).to receive(:get_session_running_on_device).with(device_id).and_return(
        [app_automate_session_id, user_id, app_automate]
      )
      expect(subject).to receive(:delete_running_session).with(app_automate_session_id)
      expect(subject).to receive(:get_session_report_file_path)
      expect(File).to receive(:exist?).and_return(false)
      expect(subject).to receive(:send_request).and_return([400, { "status" => "Failed" }])
      expect(subject).to receive(:get_device_file_path)
      expect(File).to receive(:exist?).and_return(true)
      expect(FileUtils).to receive(:rm_f)
      expect(File).to receive(:exist?).and_return(true)
      expect(FileUtils).to receive(:rm_f)
      expect(subject).not_to receive(:upload_session_logs_to_s3)
      expect(subject).not_to receive(:notify_pusher)
      expect(Utils).to receive(:send_general_feature_usage_data_to_eds).with(
        app_automate_session_id, CSPT_FEATURE, false, GENRE_APP_AUTOMATE, "Session stop failure: Failed"
      )
      expect(subject).to receive(:get_session_info_file_path)
      expect(File).to receive(:exist?).and_return(true)
      expect(FileUtils).to receive(:rm_f)
      allow_any_instance_of(Object).to receive(:zombie_push)
      subject.stop_session(app_automate_session_id, user_id, device_id, cancelled: false)
    end
  end

  describe "#stop_session" do
    it "should push event to zombie if report upload fails" do
      expect(subject).to receive(:get_running_session).with(app_automate_session_id).and_return(session_id)
      expect(subject).to receive(:get_session_running_on_device).with(device_id).and_return(
        [app_automate_session_id, user_id, app_automate]
      )
      expect(subject).to receive(:delete_running_session).with(app_automate_session_id)
      expect(subject).to receive(:get_session_report_file_path)
      expect(File).to receive(:exist?).and_return(false)
      expect(subject).to receive(:send_request).and_return([200, { "status" => "Session Stopped" }])
      expect(subject).to receive(:get_device_file_path)
      expect(File).to receive(:exist?).and_return(true)
      expect(FileUtils).to receive(:rm_f)
      expect(File).to receive(:exist?).and_return(true)
      expect(FileUtils).to receive(:rm_f)
      expect(subject).to receive(:upload_session_logs_to_s3).with(
        device_id, app_automate_session_id, { "status" => "Session Stopped" }
      ).and_return([false, "Error"])
      expect(File).to receive(:exist?).and_return(true)
      expect(FileUtils).to receive(:rm_f)
      expect(subject).to receive(:notify_pusher).with(
        device_id, app_automate_session_id, "FAILURE"
      )
      allow_any_instance_of(Object).to receive(:zombie_push)
      expect(Utils).to receive(:send_general_feature_usage_data_to_eds).with(
        app_automate_session_id, CSPT_FEATURE, false, GENRE_APP_AUTOMATE, "Report upload failure: Error"
      )
      allow_any_instance_of(Object).to receive(:zombie_push)
      subject.stop_session(app_automate_session_id, user_id, device_id, cancelled: false)
    end
  end

  describe "#stop_session" do
    it "should not stop the CSPT session when app automate session times out" do
      expect(subject).to receive(:get_running_session).with(app_automate_session_id).and_return(session_id)
      expect(subject).to receive(:get_session_running_on_device).with(device_id).and_return(
        [app_automate_session_id, user_id, app_automate]
      )
      expect(subject).to receive(:delete_running_session).with(app_automate_session_id)
      expect(subject).to receive(:get_session_report_file_path)
      expect(File).to receive(:exist?).and_return(true)
      expect(File).to receive(:read).and_return("{\"status\": \"Session Stopped\"}")
      expect(JSON).to receive(:parse).with("{\"status\": \"Session Stopped\"}").and_return(
        { "status" => "Session Stopped" }
      )
      expect(subject).not_to receive(:send_request)
      expect(subject).to receive(:get_device_file_path)
      expect(File).to receive(:exist?).and_return(true)
      expect(FileUtils).to receive(:rm_f)
      expect(File).to receive(:exist?).and_return(true)
      expect(FileUtils).to receive(:rm_f)
      expect(subject).to receive(:upload_session_logs_to_s3).with(
        device_id, app_automate_session_id, { "status" => "Session Stopped" }
      ).and_return([true, nil])
      expect(File).to receive(:exist?).and_return(true)
      expect(FileUtils).to receive(:rm_f)
      expect(subject).to receive(:notify_pusher).with(
        device_id, app_automate_session_id, "SUCCESS"
      )
      expect(Utils).to receive(:send_general_feature_usage_data_to_eds).with(
        app_automate_session_id, CSPT_FEATURE, true, GENRE_APP_AUTOMATE, ""
      )
      allow_any_instance_of(Object).to receive(:zombie_push)
      subject.stop_session(app_automate_session_id, user_id, device_id, cancelled: false)
    end
  end

  describe "#stop_session" do
    it "should not upload the session report to s3 when timed out CSPT app automate session has failed" do
      expect(subject).to receive(:get_running_session).with(app_automate_session_id).and_return(session_id)
      expect(subject).to receive(:get_session_running_on_device).with(device_id).and_return(
        [app_automate_session_id, user_id, app_automate]
      )
      expect(subject).to receive(:delete_running_session).with(app_automate_session_id)
      expect(subject).to receive(:get_session_report_file_path)
      expect(File).to receive(:exist?).and_return(true)
      expect(File).to receive(:read).and_return("{\"status\": \"Failed\"}")
      expect(JSON).to receive(:parse).with("{\"status\": \"Failed\"}").and_return({ "status" => "Failed" })
      expect(subject).not_to receive(:send_request)
      expect(subject).to receive(:get_device_file_path)
      expect(File).to receive(:exist?).and_return(true)
      expect(FileUtils).to receive(:rm_f)
      expect(File).to receive(:exist?).and_return(true)
      expect(FileUtils).to receive(:rm_f)
      expect(subject).not_to receive(:upload_session_logs_to_s3)
      expect(Utils).to receive(:send_general_feature_usage_data_to_eds).with(
        app_automate_session_id, CSPT_FEATURE, false, GENRE_APP_AUTOMATE, "Session stop failure: Failed"
      )
      expect(subject).to receive(:get_session_info_file_path)
      expect(File).to receive(:exist?).and_return(true)
      expect(FileUtils).to receive(:rm_f)
      allow_any_instance_of(Object).to receive(:zombie_push)
      subject.stop_session(app_automate_session_id, user_id, device_id, cancelled: false)
    end
  end

  describe "#stop_session" do
    it "should cancel the CSPT session" do
      expect(subject).to receive(:get_running_session).with(app_live_session_id).and_return(session_id)
      expect(subject).to receive(:get_session_running_on_device).with(device_id).and_return(
        [app_live_session_id, user_id, app_live]
      )
      expect(subject).to receive(:delete_running_session).with(app_live_session_id)
      expect(subject).to receive(:send_request)
      expect(subject).to receive(:get_device_file_path)
      expect(File).to receive(:exist?).and_return(true)
      expect(FileUtils).to receive(:rm_f)
      allow_any_instance_of(Object).to receive(:zombie_push)
      subject.stop_session(app_live_session_id, user_id, device_id, cancelled: true)
    end
  end

  describe "#stop_session" do
    it "should not call stop API if no CSPT session is running" do
      expect(subject).to receive(:get_running_session).with(app_live_session_id).and_return(nil)
      expect(subject).not_to receive(:get_session_running_on_device)
      expect(subject).not_to receive(:delete_running_session)
      expect(subject).not_to receive(:send_request)
      expect(subject).not_to receive(:get_device_file_path)
      expect(File).not_to receive(:exists?)
      expect(FileUtils).not_to receive(:rm_f)
      subject.stop_session(app_live_session_id, user_id, device_id)
    end
  end

  describe "#start_session_async" do
    it "should call Thread.bs_run to run start_session in async" do
      expect(subject).to receive(:get_running_session).with(app_live_session_id).and_return(nil)
      expect(Thread).to receive(:bs_run)
      subject.start_session_async(app_live_session_id, user_id, device_id, bundle_id, app_relaunch)
    end
  end

  describe "#start_session_async" do
    it "should not call Thread.bs_run if session already running" do
      expect(subject).to receive(:get_running_session).with(app_live_session_id).and_return(session_id)
      expect(subject).to receive(:get_device_file_path).and_return(device_id.to_s)
      expect(File).to receive(:exist?).and_return(true)
      expect(File).to receive(:ctime).with(device_id.to_s).and_return(Time.now - 10)
      expect(Thread).not_to receive(:bs_run)
      subject.start_session_async(app_live_session_id, user_id, device_id, bundle_id, app_relaunch)
    end
  end

  describe "#start_session_async" do
    it "should call Thread.bs_run if older session is timed out" do
      expect(subject).to receive(:get_running_session).with(app_live_session_id).and_return(session_id)
      expect(subject).to receive(:get_device_file_path).and_return(device_id.to_s)
      expect(File).to receive(:exist?).and_return(true)
      expect(File).to receive(:ctime).with(device_id.to_s).and_return(Time.now - 2000)
      expect(FileUtils).to receive(:rm_f).with(device_id.to_s)
      expect(Thread).to receive(:bs_run)
      subject.start_session_async(app_live_session_id, user_id, device_id, bundle_id, app_relaunch)
    end
  end

  describe "#start_session_async" do
    it "should not call Thread.bs_run if input is invalid" do
      allow_any_instance_of(Object).to receive(:zombie_push)
      expect(subject).not_to receive(:get_running_session)
      expect(subject).not_to receive(:get_device_file_path)
      expect(File).not_to receive(:exist?)
      expect(File).not_to receive(:ctime)
      expect(Thread).not_to receive(:bs_run)
      subject.start_session_async("", "", "", "", app_relaunch)
    end
  end

  describe "#stop_session_async" do
    it "should call Thread.bs_run to run stop_session in async" do
      expect(subject).to receive(:get_running_session).with(app_live_session_id).and_return(session_id)
      expect(Thread).to receive(:bs_run)
      subject.stop_session_async(app_live_session_id, user_id, device_id, cancelled: false)
    end
  end

  describe "#stop_session_async" do
    it "should not call Thread.bs_run if no session is running" do
      expect(subject).to receive(:get_running_session).with(app_live_session_id).and_return(nil)
      expect(Thread).not_to receive(:bs_run)
      subject.stop_session_async(app_live_session_id, user_id, device_id)
    end
  end

  describe "#stop_session_async" do
    it "should call Thread.bs_run to run stop_session in async when cancelled is true" do
      expect(subject).to receive(:get_running_session).with(app_live_session_id).and_return(session_id)
      expect(Thread).to receive(:bs_run)
      subject.stop_session_async(app_live_session_id, user_id, device_id, cancelled: true)
    end
  end

  describe "#stop_session_async" do
    it "should not call Thread.bs_run if input is invalid" do
      allow_any_instance_of(Object).to receive(:zombie_push)
      expect(subject).not_to receive(:get_running_session)
      expect(Thread).not_to receive(:bs_run)
      subject.stop_session_async("", "", "", cancelled: false)
    end
  end

  describe "#stop_session_async" do
    it "should not call Thread.bs_run if input is invalid" do
      allow_any_instance_of(Object).to receive(:zombie_push)
      expect(subject).not_to receive(:get_running_session)
      expect(Thread).not_to receive(:bs_run)
      subject.stop_session_async("", "", "", cancelled: true)
    end
  end

  describe "#stop_session_running_on_device_async" do
    it "should call stop_session_async" do
      expect(subject).to receive(:get_session_running_on_device).with(device_id).and_return([session_id, user_id])
      expect(subject).to receive(:stop_session_async).with(session_id, user_id, device_id, cancelled: true)
      subject.stop_session_running_on_device_async(device_id, cancelled: true)
    end
  end

  describe "#stop_session_running_on_device_async" do
    it "should call stop_session_async" do
      expect(subject).to receive(:get_session_running_on_device).with(device_id).and_return([session_id, user_id])
      expect(subject).to receive(:stop_session_async).with(session_id, user_id, device_id, cancelled: false)
      subject.stop_session_running_on_device_async(device_id, cancelled: false)
    end
  end

  describe "#stop_session_running_on_device_async" do
    it "should not call stop_session_async when no sesion is running" do
      expect(subject).to receive(:get_session_running_on_device).with(device_id).and_return(["", ""])
      expect(subject).not_to receive(:stop_session_async)
      subject.stop_session_running_on_device_async(device_id, cancelled: false)
    end
  end
end
