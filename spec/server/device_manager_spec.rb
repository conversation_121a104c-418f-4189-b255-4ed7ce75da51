require_relative '../spec_helper'
require_relative '../helpers/mock_ios_vid_capturer'
require_relative '../helpers/mock_testflight_automator'
require_relative '../../server/device_manager'
require_relative '../../lib/configuration'
require_relative '../../lib/utils/utils'
require_relative '../../lib/utils/app_analytics_util'
require_relative '../helpers/mock_airplane_mode_automator'
require_relative '../../lib/network_simulator'
require_relative '../../lib/helpers/wda_client'
require_relative '../../lib/allow_popups'
require_relative '../../lib/utils/time_recorder'
require '/usr/local/.browserstack/mobile-common/utils/app_patching_util'
require 'erb'
require 'securerandom'
require 'shellwords'

include AppPatchingUtil

describe DeviceManager do
  let(:device) { 'test_device' }
  let(:device_manager) { DeviceManager }
  let(:params) { { 'sample_param' => '12345' } }
  let(:mock_thread) { double('mock_thread') }

  before do
    allow(mock_thread).to receive(:[]=)
  end

  before :each do
    allow(CustomMDMManager).to receive(:is_custom_mdm_device?).and_return(false)
  end

  it 'should return valid snapshot details' do
    params = { timeout: 30 }
    allow(BrowserStack::Zombie).to receive(:configure).and_return(nil)
    device_manager.configure BrowserStack::Configuration.new.all
    allow(File).to receive(:read).and_return(JSON.dump({ "iproxyPort": 200 }))
    dummy_xml = "<dummy_node></dummy_node>"
    # BrowserStack::MethodInterceptor::WdaClientInterceptor is meta class created to wrap methods see method_interceptor.rb
    allow_any_instance_of(BrowserStack::MethodInterceptor::WdaClientInterceptor)
      .to receive(:snapshot_details)
      .and_return({ "value" => { "screenshot" => "s", "xmltree" => dummy_xml } })
    snaphot_details = device_manager.get_snapshot_details(device, params)
    expect(snaphot_details[:screenshot]).to eq("s")
    expect(snaphot_details[:xmltree]).to eq("<dummy_node xpath=\"/dummy_node\"/>")
    mock_appium_server = instance_double('BrowserStack::AppiumServer', driver: nil, start_server_for_version: nil)
    allow(BrowserStack::AppiumServer).to receive(:new).and_return(mock_appium_server)
    allow(mock_appium_server).to receive(:running?).and_return(true)
  end

  context ' restart_device ' do
    it ' should start thread and return if device is present ' do
      device = "1234"
      device_manager.configure BrowserStack::Configuration.new.all
      thread = double
      expect(device_manager).to receive(:device_configuration_check).and_return(nil)
      expect(Thread).to receive(:new).and_return(mock_thread)

      task = device_manager.restart_device(device)

      expect(task).to eq(mock_thread)
    end

    it ' should not start thread if it is already started ' do
      device = "1234"
      device_manager.configure BrowserStack::Configuration.new.all

      # clean set
      device_manager.devices_under_restart.clear
      expect(device_manager).to receive(:device_configuration_check).and_return(nil, nil, nil)
      expect(Thread).to receive(:new).and_return(mock_thread, mock_thread)

      # setup thread starts
      task = device_manager.restart_device(device)

      expect(task).to eq(mock_thread)
      # NOTE: that device will be never removed from set because thread is not actually running

      task2 = device_manager.restart_device(device)
      expect(task2).to eq(nil)

      # remove device manually
      device_manager.devices_under_restart.delete(device)
      # now should return thread
      task = device_manager.restart_device(device)
      expect(task).to eq(mock_thread)
    end

    it ' should not start thread if device is absent ' do
      device = double
      device_manager.configure BrowserStack::Configuration.new.all
      expect(device_manager).to receive(:device_configuration_check).and_raise(RuntimeError)
      expect { device_manager.restart_device(device) }.to raise_error(RuntimeError)
    end
  end

  context ' device_restart_job ' do
    it ' should call IosMdmClient and remove device from devices_under_restart ' do
      device = "1234"
      device_manager.configure BrowserStack::Configuration.new.all
      device_manager.devices_under_restart.add(device)

      expect(BrowserStack::IosMdmServiceClient).to receive(:restart_device).and_return("Success")

      device_manager.send(:restart_device_job, device)
      # removes device from set
      expect(device_manager.devices_under_restart.include?(device)).to eq(false)
    end
  end

  context ' reserved flow ' do
    it 'should call minified_cleanup_async and not raise error' do
      device = "1234"

      device_manager.configure BrowserStack::Configuration.new.all
      iPhoneMock = double('IPhoneMock')
      expect(iPhoneMock).to receive(:minified_cleanup).and_return true
      expect(iPhoneMock).to receive(:post_cleanup_success).and_return true

      expect(IPhone).to receive(:new).and_return iPhoneMock
      allow(DeviceManager).to receive(:stop_battery_metrics_publisher)

      expect(DeviceManager).to receive(:device_configuration_check).and_return({})

      expect(DeviceManager).to receive(:kill_all_forked_processes)
      expect(DeviceManager).to receive(:kill_app_download_process)
      expect(DeviceManager).to receive(:kill_app_install_process)

      expect(File).to receive(:open).exactly(7).times
      expect(File).to receive(:exists?).twice.and_return false
      expect { device_manager.minified_cleanup_async(device, 'quick_cleanup', "1234", Time.now.to_i) }.to_not raise_error
    end
  end

  context 'full cleanup' do
    let(:mockReport) { "dummy" }
    it 'should call full_cleanup and not raise error' do
      device = "1234"
      device_manager.configure BrowserStack::Configuration.new.all
      iPhoneMock = double('IPhoneMock')
      allow(OSUtils).to receive(:execute).and_return(true)
      allow(IPhone).to receive(:new).and_return iPhoneMock
      allow(DeviceManager).to receive(:device_configuration_check).and_return({ "device_name" => device })
      allow(DeviceManager).to receive(:stop_battery_metrics_publisher)
      allow(DeviceManager).to receive(:clear_apple_pay_data).and_return(true)
      allow(Secure::ApplePay).to receive(:dedicated_cloud_apple_pay_device?).and_return(false)
      expect_any_instance_of(DeviceState).to receive(:apple_pay_data_file_present?).and_return(true)
      allow_any_instance_of(DeviceState).to receive(:local_chrome_extension_file_present?).and_return(false)
      expect(File).to receive(:exists?).with(/xctest.pid/).and_return(false)
      expect(File).to receive(:exists?).with(/stop_params/).and_return(false)
      expect(File).to receive(:exist?).with(/video_params/).and_return(false)
      expect(MitmProxy).to receive(:stop_proxy).with(device, anything).and_return(true)
      allow(FileUtils).to receive(:rm_rf).and_return(true)
      allow(ImageInjector).to receive(:cleanup).and_return(true)
      allow(DeviceManager).to receive(:kill_all_forked_processes)
      expect(DeviceManager).to receive(:kill_app_download_process)
      expect(DeviceManager).to receive(:kill_app_install_process)
      expect(DeviceManager).to receive(:local_testing_chrome_extension_cleanup).and_return(true)
      expect(File).to receive(:exists?).with(/#{device}_session/).and_return(false)
      expect(File).to receive(:read).with(/cleanupdone_#{device}/).and_return(0)
      allow(DeviceManager).to receive(:check_and_load_plist).and_return(true)
      allow(DeviceManager).to receive(:kill_stray_video_recording_processes).and_return(true)
      expect(BrowserStack::OSUtils).to receive(:execute).with(/proxy_check.rb/).and_return(true)
      allow(PrivoxyManager).to receive(:reset_proxy).and_return(true)
      expect_any_instance_of(DeviceState).to receive(:fallback_to_v1_file_present?).and_return(true)
      expect_any_instance_of(DeviceState).to receive(:remove_fallback_to_v1_file)
      expect(iPhoneMock).to receive(:full_cleanup_mdm).with(no_args).and_return(true)
      allow(DeviceManager).to receive(:clear_xcuitest_derived_data).and_return(true)
      expect(BrowserStack::IPhone).to receive(:cleanup_telephony_files).with(device).and_return(true)
      allow(iPhoneMock).to receive(:post_cleanup_success).and_return(true)
      allow(AppleTVUtils).to receive(:apple_tv_device?).with(device).and_return(false)
      allow(iPhoneMock).to receive(:capture_rsd_values).and_return(true)
      allow(BrowserStack::TimeRecorder::Report).to receive(:new).and_return(mockReport)
      allow(mockReport).to receive(:record).and_yield

      expect { device_manager.cleanup_async(device, 'full_cleanup', "1234") }.to_not raise_error
    end
  end

  describe '.device_in_use?' do
    context 'when checking if a device is being used' do
      context 'when the device is in cleaning' do
        it 'returns true' do
          allow(DeviceManager).to receive(:device_in_cleanup?).and_return(true)
          expect(device_manager.device_in_use?(device, params)).to be(true)
        end
      end

      context 'when the device is being used by another user' do
        it 'returns true' do
          allow(DeviceManager).to receive(:device_in_other_users_session?).and_return(true)
          expect(device_manager.device_in_use?(device, params)).to be(true)
        end
      end

      context 'when the device is not cleaning or being used by another user' do
        it 'returns false' do
          allow(DeviceManager).to receive(:device_in_cleanup?).and_return(false)
          allow(DeviceManager).to receive(:device_in_other_users_session?).and_return(false)
          expect(device_manager.device_in_use?(device, params)).to be(false)
        end
      end
    end
  end

  describe ".blocked_devices_count" do
    let(:device_manager) { DeviceManager }

    let(:device_id1) do
      "randomdeviceid_1"
    end

    let(:device_id2) do
      "randomdeviceid_2"
    end

    let(:device_config) do
      JSON.parse(fixture("config.json"))
    end

    before(:each) do
      DeviceManager.configure BrowserStack::Configuration.new.all
      allow(DeviceManager).to receive(:all_devices)
        .and_return({
          device_id1 => device_config,
          device_id2 => device_config
        })
    end

    it "should return count of blocked devices" do
      expect(DeviceManager).to receive(:cleanup_done?).exactly(2).times.and_return(false)
      expect(device_manager.blocked_devices_count).to be(2)
    end

    it "should return count of blocked devices based on cleanup_done? logic" do
      expect(DeviceManager).to receive(:cleanup_done?).and_return(true, false)
      expect(device_manager.blocked_devices_count).to be(1)
    end
  end

  describe '.device_in_other_users_session?' do
    context 'when checking if the device is currently being used by another user' do
      context 'when the device is not in any session' do
        it 'returns false' do
          allow(DeviceManager).to receive(:device_in_session?).and_return(false)
          expect(device_manager.device_in_other_users_session?(device, params)).to be(false)
        end
      end

      context 'when the session file contents are empty' do
        it 'returns false' do
          allow(DeviceManager).to receive(:device_in_session?).and_return(true)
          allow(DeviceManager).to receive(:session_file_contents).and_return("")
          expect(device_manager.device_in_other_users_session?(device, params)).to be(false)
        end
      end

      context 'when the device is in a session and session file is not empty' do
        before do
          allow(DeviceManager).to receive(:device_in_session?).and_return(true)
          allow(DeviceManager).to receive(:session_file_contents).and_return({ "user_id": "6666666" })
        end

        let(:params) { { "user_id": "6666666" } } # NOTE: same user_id as session_file_production fixture

        context 'when the user id in the session file is different to the user id in params' do
          before do
            allow(DeviceManager).to receive(:session_file_contents)
              .and_return({ "user_id" => "1010101" })
            allow(File).to receive(:mtime).and_return(Time.now)
          end

          it 'pushes a message to zombie' do
            expect(BrowserStack::Zombie).to receive(:push_logs)
            device_manager.device_in_other_users_session?(device, params)
          end

          it 'returns true' do
            expect(device_manager.device_in_other_users_session?(device, params)).to be(true)
          end
        end

        context 'when the user id in the session file is the same as the user id in params' do
          it 'returns false' do
            allow(DeviceManager).to receive(:session_file_contents)
              .and_return({ "user_id" => "6666666" })
            allow(File).to receive(:mtime).and_return(Time.now)

            expect(device_manager.device_in_other_users_session?(device, params)).to be(false)
          end
        end
      end
    end
  end

  describe '.device_in_cleanup?' do
    context 'when checking if a device is in cleanup' do
      context 'when the cleanupdone file exists' do
        it 'returns true' do
          allow(File).to receive(:exist?).and_return(true)
          expect(device_manager.device_in_cleanup?(device)).to be(true)
        end
      end

      context 'when the cleanupdonefile does not exist' do
        it 'returns false' do
          allow(File).to receive(:exist?).and_return(false)
          expect(device_manager.device_in_cleanup?(device)).to be(false)
        end
      end
    end
  end

  describe '.device_in_session?' do
    context 'when checking if a device is in session' do
      context 'when the session file exists' do
        it 'returns true' do
          allow(File).to receive(:exist?).and_return(true)
          expect(device_manager.device_in_session?(device)).to be(true)
        end
      end

      context 'when the session file does not exist' do
        it 'returns false' do
          allow(File).to receive(:exist?).and_return(false)
          expect(device_manager.device_in_session?(device)).to be(false)
        end
      end
    end
  end

  describe '.check_and_set_cleanup_policy' do
    let(:device_state) { double('DeviceState') }
    it 'should set cleanup policy if :dedicated_cleanup param is set' do
      expect(device_state).to receive(:write_to_dedicated_cleanup_file)
      device_manager.check_and_set_cleanup_policy(device, { dedicated_cleanup: "true", dedicated_cleanup_config: "clean_browser" }, device_state)
    end
  end

  describe '.install_custom_certificate' do
    let(:device) { 'test_device' }
    let(:params) { { event_hash: {}, feature_usage: {}, 'automate_session_id' => 'test_session', 'app_automate_session_id' => 'app_test_session', 'product' => 'test_product' } }
    let(:custom_certificate_helper) { double('CustomCertificate') }

    before do
      allow(Utils).to receive(:mark_event_start)
      allow(Utils).to receive(:mark_event_end)
      allow(CustomCertificate).to receive(:new).and_return(custom_certificate_helper)
      allow(BrowserStack.logger).to receive(:info)
      allow(BrowserStack.logger).to receive(:error)
    end

    context 'when certificate is a CA certificate' do
      it 'should redirect to install_custom_ca_certificate for certificates_cer_ios' do
        params['customCertificateFile'] = { 'filename' => 'cert.cer', 'filetype' => 'certificates_cer_ios', 's3_url' => 'https://example.com/cert.cer' }.to_json

        expect(JSON).to receive(:parse).with(params['customCertificateFile']).and_call_original
        expect(device_manager).to receive(:install_custom_ca_certificate).with(device, params, anything)

        device_manager.install_custom_certificate(device, params)
      end

      it 'should redirect to install_custom_ca_certificate for certificates_cer_android' do
        params['customCertificateFile'] = { 'filename' => 'cert.cer', 'filetype' => 'certificates_cer_android', 's3_url' => 'https://example.com/cert.cer' }.to_json

        expect(JSON).to receive(:parse).with(params['customCertificateFile']).and_call_original
        expect(device_manager).to receive(:install_custom_ca_certificate).with(device, params, anything)

        device_manager.install_custom_certificate(device, params)
      end

      it 'should handle double-encoded JSON for CA certificates' do
        # Double-encoded JSON string (JSON string inside a JSON string)
        double_encoded = { 'filename' => 'cert.cer', 'filetype' => 'certificates_cer_android', 's3_url' => 'https://example.com/cert.cer' }.to_json.to_json
        params['customCertificateFile'] = double_encoded

        expect(JSON).to receive(:parse).with(params['customCertificateFile']).and_call_original
        expect(JSON).to receive(:parse).with(anything).and_call_original
        expect(device_manager).to receive(:install_custom_ca_certificate).with(device, params, anything)

        device_manager.install_custom_certificate(device, params)
      end
    end

    context 'when certificate is a PFX certificate' do
      it 'should call install_pfx_certificate' do
        params['customCertificateFile'] = { 'filename' => 'cert.pfx', 'filetype' => 'pfx', 's3_url' => 'https://example.com/cert.pfx' }.to_json

        expect(JSON).to receive(:parse).with(params['customCertificateFile']).exactly(2).times.and_call_original
        expect(custom_certificate_helper).to receive(:install_pfx_certificate)

        device_manager.install_custom_certificate(device, params)

        expect(params[:feature_usage]["install_pfx_certificate"]).to eq({ success: true })
      end

      it 'should handle double-encoded JSON for PFX certificates' do
        # Double-encoded JSON string (JSON string inside a JSON string)
        double_encoded = { 'filename' => 'cert.pfx', 'filetype' => 'pfx', 's3_url' => 'https://example.com/cert.pfx' }.to_json.to_json
        params['customCertificateFile'] = double_encoded

        expect(JSON).to receive(:parse).with(params['customCertificateFile']).exactly(2).times.and_call_original
        expect(JSON).to receive(:parse).with(anything).and_call_original
        expect(custom_certificate_helper).to receive(:install_pfx_certificate)

        device_manager.install_custom_certificate(device, params)

        expect(params[:feature_usage]["install_pfx_certificate"]).to eq({ success: true })
      end
    end

    context 'when an error occurs' do
      it 'should handle errors and update feature_usage accordingly' do
        params['customCertificateFile'] = { 'filename' => 'cert.pfx', 'filetype' => 'pfx', 's3_url' => 'https://example.com/cert.pfx' }.to_json

        expect(JSON).to receive(:parse).with(params['customCertificateFile']).exactly(2).times.and_call_original
        expect(custom_certificate_helper).to receive(:install_pfx_certificate).and_raise(StandardError.new("Certificate installation failed"))

        expect { device_manager.install_custom_certificate(device, params) }.to raise_error(FireCMDException)

        expect(params[:feature_usage]["install_pfx_certificate"][:success]).to eq(false)
        expect(params[:feature_usage]["install_pfx_certificate"][:exception]).to include("Certificate installation failed")
      end

      it 'should handle empty certificate details and raise FireCMDException with correct type' do
        params['customCertificateFile'] = {}.to_json

        expect(JSON).to receive(:parse).with(params['customCertificateFile']).and_call_original

        expect_error = proc do |error|
          expect(error).to be_a(FireCMDException)
          expect(error.message).to eq("Invalid custom certificate details")
          expect(error.kind).to eq("certificate_install_invalid_details")
        end

        expect { device_manager.install_custom_certificate(device, params) }.to raise_error(FireCMDException, &expect_error)

        expect(params[:feature_usage]["install_custom_certificate"][:success]).to eq(false)
        expect(params[:feature_usage]["install_custom_certificate"][:exception]).to eq("Invalid custom certificate details")
      end
    end
  end

  describe '.install_custom_ca_certificate' do
    let(:device) { 'test_device' }
    let(:params) { { event_hash: {}, feature_usage: {}, 'automate_session_id' => 'test_session_id', 'app_automate_session_id' => 'app_test_session_id', 'product' => 'test_product' } }
    let(:custom_ca_cert_file) do
      {
        's3_url' => 'https://example.com/cert.cer',
        'filename' => 'cert.cer',
        'filetype' => 'certificates_cer_android'
      }
    end
    let(:custom_certificate_helper) { double('CustomCertificate') }

    before do
      allow(Utils).to receive(:mark_event_start)
      allow(Utils).to receive(:mark_event_end)
      allow(CustomCertificate).to receive(:new).and_return(custom_certificate_helper)
      allow(BrowserStack.logger).to receive(:info)
      allow(BrowserStack.logger).to receive(:error)
    end

    context 'when installation is successful' do
      it 'should call install_all_custom_ca_certs with the correct parameters' do
        expect(CustomCertificate).to receive(:new).with(
          device,
          params["automate_session_id"] || params["app_automate_session_id"],
          anything,
          params["product"]
        ).and_return(custom_certificate_helper)

        expect(custom_certificate_helper).to receive(:install_all_custom_ca_certs)

        device_manager.install_custom_ca_certificate(device, params, custom_ca_cert_file)

        expect(params[:feature_usage]["install_custom_ca_certificates"]).to eq({ success: true })
      end

      it 'should properly set feature_usage for successful installation' do
        allow(custom_certificate_helper).to receive(:install_all_custom_ca_certs)

        device_manager.install_custom_ca_certificate(device, params, custom_ca_cert_file)

        expect(params[:feature_usage]["install_custom_ca_certificates"]).to eq({ success: true })
      end

      it 'should mark event start and end for timing instrumentation' do
        allow(custom_certificate_helper).to receive(:install_all_custom_ca_certs)
        expect(Utils).to receive(:mark_event_start).with('fire_cmd.install_custom_ca_certificates_time', params[:event_hash])
        expect(Utils).to receive(:mark_event_end).with('fire_cmd.install_custom_ca_certificates_time', params[:event_hash])

        device_manager.install_custom_ca_certificate(device, params, custom_ca_cert_file)
      end
    end

    context 'when handling different certificate formats' do
      it 'should handle certificate details with media_s3_url instead of s3_url' do
        custom_ca_cert_file_with_media_s3_url = {
          'media_s3_url' => 'https://example.com/cert.cer',
          'filename' => 'cert.cer',
          'filetype' => 'certificates_cer_android'
        }

        expect(CustomCertificate).to receive(:new) do |device_arg, session_id_arg, cert_array_arg, product_arg|
          expect(device_arg).to eq(device)
          expect(session_id_arg).to eq(params["automate_session_id"] || params["app_automate_session_id"])
          expect(cert_array_arg.first[:media_s3_url]).to eq('https://example.com/cert.cer')
          expect(product_arg).to eq(params["product"])
          custom_certificate_helper
        end

        expect(custom_certificate_helper).to receive(:install_all_custom_ca_certs)

        device_manager.install_custom_ca_certificate(device, params, custom_ca_cert_file_with_media_s3_url)

        expect(params[:feature_usage]["install_custom_ca_certificates"]).to eq({ success: true })
      end

      it 'should use default filetype when not provided' do
        custom_ca_cert_file_without_filetype = {
          's3_url' => 'https://example.com/cert.cer',
          'filename' => 'cert.cer'
        }

        expect(CustomCertificate).to receive(:new) do |_, _, cert_array_arg, _|
          expect(cert_array_arg.first[:filetype]).to eq('certificates_cer_android')
          custom_certificate_helper
        end

        expect(custom_certificate_helper).to receive(:install_all_custom_ca_certs)

        device_manager.install_custom_ca_certificate(device, params, custom_ca_cert_file_without_filetype)

        expect(params[:feature_usage]["install_custom_ca_certificates"]).to eq({ success: true })
      end
    end

    context 'when installation fails' do
      it 'should handle errors and update feature_usage accordingly' do
        expect(CustomCertificate).to receive(:new).and_return(custom_certificate_helper)
        expect(custom_certificate_helper).to receive(:install_all_custom_ca_certs).and_raise(StandardError.new("Certificate installation failed"))
        expect(BrowserStack.logger).to receive(:error).with(/Failed to install custom CA certificates with error: Certificate installation failed/)

        expect { device_manager.install_custom_ca_certificate(device, params, custom_ca_cert_file) }.to raise_error(FireCMDException)

        expect(params[:feature_usage]["install_custom_ca_certificates"][:success]).to eq(false)
        expect(params[:feature_usage]["install_custom_ca_certificates"][:exception]).to include("Certificate installation failed")
      end

      it 'should truncate long error messages in feature_usage' do
        long_error_message = "A" * 200
        expect(CustomCertificate).to receive(:new).and_return(custom_certificate_helper)
        expect(custom_certificate_helper).to receive(:install_all_custom_ca_certs).and_raise(StandardError.new(long_error_message))

        expect { device_manager.install_custom_ca_certificate(device, params, custom_ca_cert_file) }.to raise_error(FireCMDException)

        expect(params[:feature_usage]["install_custom_ca_certificates"][:exception].length).to eq(100)
      end

      it 'should still mark event end even when an error occurs' do
        expect(CustomCertificate).to receive(:new).and_return(custom_certificate_helper)
        expect(custom_certificate_helper).to receive(:install_all_custom_ca_certs).and_raise(StandardError.new("Certificate installation failed"))
        expect(Utils).to receive(:mark_event_start).with('fire_cmd.install_custom_ca_certificates_time', params[:event_hash])
        expect(Utils).to receive(:mark_event_end).with('fire_cmd.install_custom_ca_certificates_time', params[:event_hash])

        expect { device_manager.install_custom_ca_certificate(device, params, custom_ca_cert_file) }.to raise_error(FireCMDException)
      end

      it 'should raise FireCMDException with correct parameters when installation fails' do
        error_message = "Certificate installation failed"
        expect(CustomCertificate).to receive(:new).and_return(custom_certificate_helper)
        expect(custom_certificate_helper).to receive(:install_all_custom_ca_certs).and_raise(StandardError.new(error_message))

        expect_error = proc do |error|
          expect(error).to be_a(FireCMDException)
          expect(error.message).to eq(error_message)
          expect(error.kind).to eq("browserstack_ca_certificate_install_failure")
        end

        expect do
          device_manager.install_custom_ca_certificate(device, params, custom_ca_cert_file)
        end.to raise_error(FireCMDException, &expect_error)
      end
    end

    context 'when using different session ID parameters' do
      it 'should use automate_session_id when available' do
        params_with_automate_session_id = {
          event_hash: {},
          feature_usage: {},
          'automate_session_id' => 'test_automate_session',
          'product' => 'test_product'
        }

        expect(CustomCertificate).to receive(:new).with(
          device,
          'test_automate_session',
          anything,
          'test_product'
        ).and_return(custom_certificate_helper)

        expect(custom_certificate_helper).to receive(:install_all_custom_ca_certs)

        device_manager.install_custom_ca_certificate(device, params_with_automate_session_id, custom_ca_cert_file)
      end

      it 'should use app_automate_session_id when automate_session_id is not available' do
        params_with_app_automate_session_id = {
          event_hash: {},
          feature_usage: {},
          'app_automate_session_id' => 'test_app_automate_session',
          'product' => 'test_product'
        }

        expect(CustomCertificate).to receive(:new).with(
          device,
          'test_app_automate_session',
          anything,
          'test_product'
        ).and_return(custom_certificate_helper)

        expect(custom_certificate_helper).to receive(:install_all_custom_ca_certs)

        device_manager.install_custom_ca_certificate(device, params_with_app_automate_session_id, custom_ca_cert_file)
      end

      it 'should use nil when neither session ID is available' do
        params_without_session_ids = {
          event_hash: {},
          feature_usage: {},
          'product' => 'test_product'
        }

        expect(CustomCertificate).to receive(:new).with(
          device,
          nil,
          anything,
          'test_product'
        ).and_return(custom_certificate_helper)

        expect(custom_certificate_helper).to receive(:install_all_custom_ca_certs)

        device_manager.install_custom_ca_certificate(device, params_without_session_ids, custom_ca_cert_file)
      end
    end
  end
end

describe '.trust_client_enterprise_app' do
  let(:device) { "00008101-000A58880C0A002E" }
  let(:session_id) { "test_session_123" }
  let(:app_path) { "/tmp/app_path.ipa" }
  let(:device_config) { { "device_name" => "iPhone 11", "device_version" => "15.0" } }
  let(:idevice) { double('BrowserStack::IPhone') }
  let(:dist_name) { "Example Distribution Name" }

  before do
    allow(BrowserStack).to receive(:logger).and_return(double(info: nil, error: nil))
    allow(DeviceManager).to receive(:device_configuration_check).with(device).and_return(device_config)
    allow(FileUtils).to receive(:touch)
    allow(BrowserStack::IPhone).to receive(:new).with(device_config, device).and_return(idevice)
    allow(XcodeBuildUtils).to receive(:get_team_name_from_profile).with("#{app_path}/embedded.mobileprovision").and_return(dist_name)
  end

  context "when method parameters are nil" do
    it "raises FireCMDException if device is nil" do
      expect do
        DeviceManager.send(:trust_client_enterprise_app, nil, session_id, app_path)
      end.to raise_error(FireCMDException, "Device parameter cannot be nil")
    end

    it "raises FireCMDException if app_path is nil" do
      expect do
        DeviceManager.send(:trust_client_enterprise_app, device, session_id, nil)
      end.to raise_error(FireCMDException, "App path parameter cannot be nil")
    end
  end

  context "when trusting the enterprise app" do
    it "successfully trusts the client enterprise app" do
      expect(idevice).to receive(:trust_client_enterprise_app).with(dist_name).and_return(true)

      expect do
        DeviceManager.send(:trust_client_enterprise_app, device, session_id, app_path)
      end.not_to raise_error
    end

    it "creates a touch file for the device" do
      allow(idevice).to receive(:trust_client_enterprise_app).with(dist_name).and_return(true)

      expect(FileUtils).to receive(:touch).with("/tmp/client_enterprise_app_#{device}")

      DeviceManager.send(:trust_client_enterprise_app, device, session_id, app_path)
    end

    it "raises FireCMDException when app trust returns false" do
      expect(idevice).to receive(:trust_client_enterprise_app).with(dist_name).and_return(false)

      expect do
        DeviceManager.send(:trust_client_enterprise_app, device, session_id, app_path)
      end.to raise_error(FireCMDException, "app trust returned false after possible retries")
    end

    it "propagates exceptions during app trust process" do
      error_message = "Failed to trust enterprise app"
      expect(idevice).to receive(:trust_client_enterprise_app).with(dist_name).and_raise(StandardError.new(error_message))
      expect(BrowserStack::Zombie).to receive(:push_logs).with("enterprise-trust-failed", error_message, { "session_id" => session_id, "device" => device })

      expect do
        DeviceManager.send(:trust_client_enterprise_app, device, session_id, app_path)
      end.to raise_error(FireCMDException, error_message)
    end

    it "logs successful app trust completion" do
      allow(idevice).to receive(:trust_client_enterprise_app).with(dist_name).and_return(true)

      expect(BrowserStack.logger).to receive(:info).with(/starting client enterprise app trust/)
      expect(BrowserStack.logger).to receive(:info).with(/client enterprise app trust successful/)

      DeviceManager.send(:trust_client_enterprise_app, device, session_id, app_path)
    end

    it "logs exception details when app trust fails" do
      error_message = "Trust operation failed"
      expect(idevice).to receive(:trust_client_enterprise_app).with(dist_name).and_raise(StandardError.new(error_message))

      expect(BrowserStack.logger).to receive(:info).with(/client enterprise app trust failed/)
      expect(BrowserStack::Zombie).to receive(:push_logs).with("enterprise-trust-failed", error_message, { "session_id" => session_id, "device" => device })

      expect do
        DeviceManager.send(:trust_client_enterprise_app, device, session_id, app_path)
      end.to raise_error(FireCMDException)
    end
  end

  context "extracting team name" do
    it "extracts team name from mobileprovision file" do
      expect(XcodeBuildUtils).to receive(:get_team_name_from_profile).with("#{app_path}/embedded.mobileprovision").and_return(dist_name)
      allow(idevice).to receive(:trust_client_enterprise_app).with(dist_name).and_return(true)

      DeviceManager.send(:trust_client_enterprise_app, device, session_id, app_path)
    end

    it "uses extracted team name when calling trust_client_enterprise_app" do
      custom_dist_name = "Custom Distribution Team"
      expect(XcodeBuildUtils).to receive(:get_team_name_from_profile).with("#{app_path}/embedded.mobileprovision").and_return(custom_dist_name)
      expect(idevice).to receive(:trust_client_enterprise_app).with(custom_dist_name).and_return(true)

      DeviceManager.send(:trust_client_enterprise_app, device, session_id, app_path)
    end
  end
end

describe '.start' do
  let(:device_manager) { DeviceManager }
  let(:device) { 'test_device' }
  let(:hooter) { Hooter.new }
  let(:params) { { 'sample_param' => '123' } }

  it 'should raise an exception if the device is in use' do
    allow(DeviceManager).to receive(:device_in_use?).and_return(true)
    expect { device_manager.start(device, params) }.to raise_error(/Cannot start session/)
  end

  it 'should call get url when it is non-start request request' do
    allow(DeviceManager).to receive(:device_in_use?).and_return(false)
    allow(File).to receive(:exist?).and_return(true)
    allow_any_instance_of(DeviceState).to receive(:dedicated_device_file_present?).and_return(false)
    allow_any_instance_of(DeviceState).to receive(:dedicated_cleanup_file_present?).and_return(false)
    allow_any_instance_of(DeviceState).to receive(:custom_mdm_remove_setup_file_present?).and_return(false)
    allow(CustomMDMManager).to receive(:is_custom_mdm_device?).and_return(false)
    session = double("session", start: true)
    allow(BrowserStack::SessionFactory).to receive(:for).and_return(session)

    ["switcher", "local", "keyboard-layout", "ip_change"].each do |trigger_value|
      params = {
        'loader_trigger' => trigger_value,
        'genre' => 'live_testing',
        'url' => 'https://www.github.com',
        'session_start_events' => {}
      }
      expect(device_manager).to receive(:get_url).with(device, 12345).and_return('https://www.browserstack.com')
      expect(device_manager).to receive(:kill_video_recording_processes)
      device_manager.start(device, params)
      expect(params["url"]).to eq('https://www.browserstack.com')
    end
  end

  it 'should keep the same url if it is a start request' do
    params = {
      'loader_trigger' => 'start',
      'genre' => 'live_testing',
      'url' => 'https://www.github.com',
      'session_start_events' => {}
    }
    allow(DeviceManager).to receive(:device_in_use?).and_return(false)
    allow(File).to receive(:exist?).and_return(true)
    allow(CustomMDMManager).to receive(:is_custom_mdm_device?).and_return(false)
    allow_any_instance_of(DeviceState).to receive(:dedicated_device_file_present?).and_return(false)
    allow_any_instance_of(DeviceState).to receive(:dedicated_cleanup_file_present?).and_return(false)
    allow_any_instance_of(DeviceState).to receive(:custom_mdm_remove_setup_file_present?).and_return(false)
    session = double("session", start: true)
    allow(BrowserStack::SessionFactory).to receive(:for).and_return(session)

    expect(device_manager).not_to receive(:get_url).with(device, 12345)
    expect(device_manager).to receive(:kill_video_recording_processes)
    device_manager.start(device, params)
    expect(params["url"]).to eq('https://www.github.com')
  end

  it 'should call check and set cleanup policy' do
    params = {
      'loader_trigger' => 'start',
      'genre' => 'live_testing',
      'url' => 'https://www.github.com',
      'session_start_events' => {}
    }
    allow(DeviceManager).to receive(:device_in_use?).and_return(false)
    allow(File).to receive(:exist?).and_return(true)
    allow(CustomMDMManager).to receive(:is_custom_mdm_device?).and_return(false)
    allow_any_instance_of(DeviceState).to receive(:dedicated_device_file_present?).and_return(false)
    allow_any_instance_of(DeviceState).to receive(:dedicated_cleanup_file_present?).and_return(false)
    allow_any_instance_of(DeviceState).to receive(:custom_mdm_remove_setup_file_present?).and_return(false)
    session = double("session", start: true)
    expect(device_manager).to receive(:kill_video_recording_processes)
    allow(BrowserStack::SessionFactory).to receive(:for).and_return(session)
    expect(DeviceManager).to receive(:check_and_set_cleanup_policy)
    device_manager.start(device, params)
  end
end

describe 'restart' do
  let(:device_manager) { DeviceManager }
  let(:device) { 'test_device' }
  let(:wda_port) { 8400 }
  let(:device_config) { { 'webdriver_port' => wda_port } }
  let(:session) { double("session", start: true) }
  let(:actions) { 'action1,action2' }
  let(:params) { { 'device' => device, 'session_start_events' => {}, 'actions' => actions } }

  it 'should perform restart method in respective session factory' do
    expect(DeviceManager).to receive(:device_configuration_check).with(device).and_return(device_config)
    expect(BrowserStack::SessionFactory).to receive(:for).and_return(session)
    expect(session).to receive(:restart)
    device_manager.restart(device, params)
  end
end

describe 'connect_to_peer' do
  let(:device_manager) { DeviceManager }
  let(:device) { 'test_device' }
  let(:peer_server_url) { "https://preprod-chrome-repeater.bsstag.com:8079" }
  let(:terminal_ip) { "127.0.0.1" }
  let(:webrtc_session_id) { "dummy_vnc_key" }
  let(:enable_post_cleanup_peer_flow) { "true" }
  let(:params) { { key: "quarkbase", logging: "true", restart_vnc: "true", peer_server_url: peer_server_url, nodertc_port: "7778", device: device, device_name: "iPhone 8", terminal_ip: terminal_ip, enable_post_cleanup_peer_flow: enable_post_cleanup_peer_flow, vnc_key: webrtc_session_id, machine_key: "whatwhat", admin_key: "abcd", session_start_events: {} } }
  let(:config_file) { { 'cls_url' => "www.example.com", 'cls_port' => "4567"  } }
  let(:wda_params) { { peer_server_url: peer_server_url, webrtc_session_id: webrtc_session_id, terminal_ip: terminal_ip, device: device, cls_servers: [{ "type" => "CLS", "host" => config_file['cls_url'], "port" => config_file['cls_port'] }] }  }
  let(:wda_port) { 8400 }
  let(:device_config) { { 'webdriver_port' => wda_port } }

  it 'should raise error if device in cleaning or a session' do
    allow(DeviceManager).to receive(:device_in_use?).and_return(true)
    expect { device_manager.start(device, params) }.to raise_error(/Cannot start session/)
  end

  context "Post cleanup flow enabled" do
    let(:enable_post_cleanup_peer_flow) { "true" }
    let(:wda_client_dummy) { double("WDAclient") }

    before(:each) do
      allow(Configuration).to receive_message_chain(:new, :all).and_return(config_file)
      allow(DeviceManager).to receive(:device_configuration_check).with(device).and_return(device_config)
      allow(WdaClient).to receive(:new).with(wda_port).and_return(wda_client_dummy)
      allow(wda_client_dummy).to receive(:connect_to_peer).with(wda_params)
    end

    it 'should call connect_to_peer' do
      allow(wda_client_dummy).to receive(:running?).and_return(true)
      expect { device_manager.connect_to_peer(device, params) }.not_to raise_error
    end

    it 'should raise error if wda is not still running' do
      allow(wda_client_dummy).to receive(:running?).and_return(false)
      expect { device_manager.connect_to_peer(device, params) }.to raise_error(/WDA stopped running/)
    end
  end

  context "Post cleanup flow disabled" do
    let(:enable_post_cleanup_peer_flow) { "false" }

    it 'should not call connect_to_peer' do
      expect(BrowserStack::IPhone).not_to receive(:connect_to_peer)
      device_manager.connect_to_peer(device, params)
    end
  end
end

describe 'device_setup_completed?' do
  let(:device_manager) { DeviceManager }

  context 'when webkit plist is not present' do
    it 'returns false' do
      device = 'test'
      device_manager.configure BrowserStack::Configuration.new.all
      allow(device_manager).to receive(:device_configuration_check).with(device).and_return({ "debugger_port" => "123", "device_name" => device })
      allow(File).to receive(:exist?).with("/Library/LaunchDaemons/ios_webkit_debug_proxy_123.plist").and_return false
      result = device_manager.send(:device_setup_completed?, device)
      expect(result).to be false
    end
  end

  context 'when webkit plist is present' do
    it 'returns false' do
      device = 'test'
      device_manager.configure BrowserStack::Configuration.new.all
      allow(device_manager).to receive(:device_configuration_check).with(device).and_return({ "debugger_port" => "123", "device_name" => device })
      allow(File).to receive(:exist?).with("/Library/LaunchDaemons/ios_webkit_debug_proxy_123.plist").and_return true
      result = device_manager.send(:device_setup_completed?, device)
      expect(result).to be true
    end
  end
end

describe "#fire_cmd_start" do
  let(:mock_thread) { double('mock_thread') }

  before do
    mock_hooter_instance
    allow(mock_thread).to receive(:[]=)
  end

  let(:device_id) do
    "randomdeviceid"
  end

  let(:device_config) do
    JSON.parse(fixture("config.json"))
  end

  let(:state_files_dir) do
    "/usr/local/.browserstack/state_files"
  end

  let(:is_minimized_flow) { false }
  let(:params) { {} }

  before(:each) do
    DeviceManager.configure BrowserStack::Configuration.new.all

    allow(DeviceManager).to receive(:all_devices).and_return({ device_id => device_config })
    allow(DeviceManager).to receive(:session_file_contents).and_return(fixture("session_file_production"))
    allow(DeviceManager).to receive(:check_and_start_ios_watcher).and_return(nil)
    allow_any_instance_of(DeviceState).to receive(:dedicated_cleanup_file_present?).and_return(false)
    allow_any_instance_of(DeviceState).to receive(:custom_mdm_remove_setup_file_present?).and_return(false)
    allow(Secure::ApplePay).to receive(:dedicated_cloud_apple_pay_device?).and_return(false)
  end

  context 'async app installation' do
    let(:params) do
      {
        "async_app_download_install" => "true",
        "resignApp" => "true",
        "s3_app_url" => "https://example.com/app.ipa"
      }
    end

    before do
      allow(BrowserStack.logger).to receive(:info)
      allow(Thread).to receive(:bs_run).and_yield
      allow(DeviceManager).to receive(:install_main_app)
    end

    context 'when async installation is enabled' do
      before do
        allow(DeviceManager).to receive(:is_async_install_required?).and_return(true)
      end

      it 'returns true when all conditions are met' do
        async_params = {
          "async_app_download_install" => "true",
          "resignApp" => "true",
          "update_app_settings" => nil
        }

        expect(DeviceManager.is_async_install_required?(async_params, false)).to be true
      end

      it 'starts async installation when async conditions are met' do
        enable_async_main_app_install = true

        expect(Thread).to receive(:bs_run)
        expect(DeviceManager).to receive(:install_main_app).with(params, device_id)

        if enable_async_main_app_install
          Thread.bs_run do
            DeviceManager.install_main_app(params, device_id)
          end
        end
      end
    end

    context 'when async installation should be disabled' do
      it 'returns false when minimized flow is enabled' do
        min_flow_params = {
          "async_app_download_install" => "true",
          "resignApp" => "true"
        }

        expect(DeviceManager.is_async_install_required?(min_flow_params, true)).to be false
      end

      it 'returns false when resignApp is false' do
        resign_params = {
          "async_app_download_install" => "true",
          "resignApp" => "false"
        }

        expect(DeviceManager.is_async_install_required?(resign_params, false)).to be false
      end
    end

    context 'when handling app installation status' do
      before do
        allow(DeviceManager).to receive(:is_async_install_required?).and_return(true)
        allow(BrowserStack.logger).to receive(:info)
      end

      it 'updates installation status after successful installation' do
        status_tracker = double('StatusTracker')
        allow(DeviceManager).to receive(:status_tracker).and_return(status_tracker)
        expect(status_tracker).to receive(:update_status).with(device_id, 'app_installed')

        allow(DeviceManager).to receive(:install_main_app) do
          status_tracker.update_status(device_id, 'app_installed')
          true
        end

        Thread.bs_run do
          DeviceManager.install_main_app(params, device_id)
        end
      end

      it 'handles installation retry when initial attempt fails' do
        attempt_count = 0
        retry_count = 0

        allow(DeviceManager).to receive(:install_main_app) do
          attempt_count += 1
          if attempt_count == 1
            retry_count += 1
            BrowserStack.logger.info("Retrying app installation")
            true
          else
            true
          end
        end

        expect(BrowserStack.logger).to receive(:info).with("Retrying app installation")

        Thread.bs_run do
          DeviceManager.install_main_app(params, device_id)
        end

        expect(retry_count).to eq(1)
      end

      it 'validates app installation completion before proceeding' do
        allow(DeviceManager).to receive(:app_installed?).with(device_id).and_return(true)

        allow(DeviceManager).to receive(:install_main_app) do
          BrowserStack.logger.info("Installation verification completed")
          true
        end

        expect(BrowserStack.logger).to receive(:info).with("Installation verification completed")

        Thread.bs_run do
          DeviceManager.install_main_app(params, device_id)
        end
      end
    end
  end

  context "Automate" do
    # TODO: Move this to a fixture as more params are added here.
    let(:selenium_command_params) do
      {
        :genre => "automate",
        "automate_session_id" => SecureRandom.hex,
        # event_hash is injected into params in server.rb
        # This is used for instrumentation.
        :event_hash => {
          absoluate_start_time: (Time.now.to_f * 1000).to_i
        }
      }
    end

    let(:automate_funnel) { double :automate_funnel }

    it "should call all the required methods in the correct order while setting up a session which has webrtc streaming enabled" do
      selenium_command_params["webrtc_session_id"] = "session-1234"
      selenium_command_params[:video] = "true"
      expect(DeviceManager).to receive(:check_and_give_icloud_access)
      allow(DeviceManager).to receive(:check_and_give_settings_app_access)
      expect(Thread).to receive(:new).and_return(mock_thread).twice
      expect(AutomateFunnel).to receive(:new).and_return(automate_funnel)
      expect(automate_funnel).to receive(:mark_block_start).with('iOSFireCMD')
      expect(automate_funnel).to receive(:mark_breakup_start)
      expect(automate_funnel).to receive(:mark_block_start).with('iosInteractiveSession')
      expect(automate_funnel).to receive(:mark_block_end).with('iosInteractiveSession')
      expect(automate_funnel).to receive(:mark_block_start).with('DeviceSetup')
      expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
      expect(FileUtils).to receive(:touch).with("#{STATE_FILES_DIR}/session_start/#{device_id}").at_least(:once)
      expect(FileUtils).to receive(:touch).with("/var/log/browserstack/app_log_#{device_id}.log")
      expect(DeviceManager).to receive(:write_session_info).with(device_id, selenium_command_params).once.ordered
      expect(Thread).to receive(:new).and_return(mock_thread).twice.ordered
      expect(BrowserStack::IPhone).to_not receive(:unlock_device)
      expect(AppleTVUtils).to receive(:apple_tv_device?).and_return(false).exactly(4).times
      expect(BrowserStack::OSUtils).to receive(:execute).with(/\[x\]codebuild/).and_return("true").once.ordered
      expect(automate_funnel).to receive(:mark_block_end).with('DeviceSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('LocalTunnelSetup')
      expect(automate_funnel).to receive(:mark_block_end).with('LocalTunnelSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('VideoRecording')
      expect_any_instance_of(VideoRecManager).to receive(:start_rec)
      expect(automate_funnel).to receive(:mark_block_end).with('VideoRecording', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('GeoLocation')
      expect(automate_funnel).to receive(:mark_block_end).with('GeoLocation', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('NetworkSimulation')
      expect(automate_funnel).to receive(:mark_block_end).with('NetworkSimulation', 'success')
      expect(automate_funnel).to receive(:mark_breakup_end)
      expect(automate_funnel).to receive(:mark_block_end)
      expect(automate_funnel).to receive(:generate_data_json)

      ret = DeviceManager.fire_cmd_start(device_id, selenium_command_params)

      # TODO: have more robust assertions
      expect(ret).to_not have_key(:error)
      expect(ret).to have_key("platform_time_stats")
      expect(ret).to have_key(:port) # Port where Appium is running
      expect(ret).to have_key(:wda_port) # Port on which WDA is accessible on the machine (i.e the iProxy port)
    end

    it "should call all the required methods in the correct order while setting up a session which has webrtc streaming enabled for v2 video" do
      selenium_command_params["webrtc_session_id"] = "session-1234"
      selenium_command_params[:video] = "true"
      selenium_command_params["video_params_v2"] = { "v2" => "true" }.to_json
      expect(DeviceManager).to receive(:check_and_give_icloud_access)
      allow(DeviceManager).to receive(:check_and_give_settings_app_access)
      expect(Thread).to receive(:new).and_return(mock_thread).twice
      expect(AutomateFunnel).to receive(:new).and_return(automate_funnel)
      expect(automate_funnel).to receive(:mark_block_start).with('iOSFireCMD')
      expect(automate_funnel).to receive(:mark_breakup_start)
      expect(automate_funnel).to receive(:mark_block_start).with('iosInteractiveSession')
      expect(automate_funnel).to receive(:mark_block_end).with('iosInteractiveSession')
      expect(automate_funnel).to receive(:mark_block_start).with('DeviceSetup')
      expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
      expect(FileUtils).to receive(:touch).with("#{STATE_FILES_DIR}/session_start/#{device_id}").at_least(:once)
      expect(FileUtils).to receive(:touch).with("/var/log/browserstack/app_log_#{device_id}.log")
      expect(DeviceManager).to receive(:write_session_info).with(device_id, selenium_command_params).once.ordered
      expect(Thread).to receive(:new).and_return(mock_thread).twice.ordered
      expect(AppleTVUtils).to receive(:apple_tv_device?).and_return(false).exactly(3).times
      expect(BrowserStack::IPhone).to_not receive(:unlock_device)
      expect(BrowserStack::OSUtils).to receive(:execute).with(/\[x\]codebuild/).and_return("true").once.ordered
      expect(automate_funnel).to receive(:mark_block_end).with('DeviceSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('LocalTunnelSetup')
      expect(automate_funnel).to receive(:mark_block_end).with('LocalTunnelSetup', 'success')
      expect(DeviceManager).to receive(:start_v2_video_recording)
      expect(automate_funnel).to receive(:mark_block_start).with('GeoLocation')
      expect(automate_funnel).to receive(:mark_block_end).with('GeoLocation', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('NetworkSimulation')
      expect(automate_funnel).to receive(:mark_block_end).with('NetworkSimulation', 'success')
      expect(automate_funnel).to receive(:mark_breakup_end)
      expect(automate_funnel).to receive(:mark_block_end)
      expect(automate_funnel).to receive(:generate_data_json)

      ret = DeviceManager.fire_cmd_start(device_id, selenium_command_params)

      # TODO: have more robust assertions
      expect(ret).to_not have_key(:error)
      expect(ret).to have_key("platform_time_stats")
      expect(ret).to have_key(:port) # Port where Appium is running
      expect(ret).to have_key(:wda_port) # Port on which WDA is accessible on the machine (i.e the iProxy port)
    end

    it "should call all the required methods in the correct order while setting up a session which has webrtc streaming enabled for Apple TV" do
      selenium_command_params["webrtc_session_id"] = "session-1234"
      selenium_command_params[:video] = "true"
      selenium_command_params["video_params_v2"] = { "v2" => "true" }.to_json
      expect(DeviceManager).to receive(:check_and_give_icloud_access)
      expect(Thread).to receive(:new).and_return(mock_thread).twice
      expect(AutomateFunnel).to receive(:new).and_return(automate_funnel)
      expect(automate_funnel).to receive(:mark_block_start).with('iOSFireCMD')
      expect(automate_funnel).to receive(:mark_breakup_start)
      expect(automate_funnel).to receive(:mark_block_start).with('DeviceSetup')
      expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
      expect(FileUtils).to receive(:touch).with("#{STATE_FILES_DIR}/session_start/#{device_id}").at_least(:once)
      expect(FileUtils).to receive(:touch).with("/var/log/browserstack/app_log_#{device_id}.log")
      expect(DeviceManager).to receive(:write_session_info).with(device_id, selenium_command_params).once.ordered
      expect(Thread).to receive(:new).and_return(mock_thread).twice.ordered
      expect(AppleTVUtils).to receive(:apple_tv_device?).and_return(true).exactly(3).times
      expect(BrowserStack::IPhone).to_not receive(:unlock_device)
      expect(BrowserStack::OSUtils).to receive(:execute).with(/\[x\]codebuild/).and_return("true").once.ordered
      expect(automate_funnel).to receive(:mark_block_end).with('DeviceSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('LocalTunnelSetup')
      expect(automate_funnel).to receive(:mark_block_end).with('LocalTunnelSetup', 'success')
      expect(DeviceManager).to receive(:start_v2_video_recording)
      expect(automate_funnel).to receive(:mark_block_start).with('GeoLocation')
      expect(automate_funnel).to receive(:mark_block_end).with('GeoLocation', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('NetworkSimulation')
      expect(automate_funnel).to receive(:mark_block_end).with('NetworkSimulation', 'success')
      expect(automate_funnel).to receive(:mark_breakup_end)
      expect(automate_funnel).to receive(:mark_block_end)
      expect(automate_funnel).to receive(:generate_data_json)

      ret = DeviceManager.fire_cmd_start(device_id, selenium_command_params)

      # TODO: have more robust assertions
      expect(ret).to_not have_key(:error)
      expect(ret).to have_key("platform_time_stats")
      expect(ret).to have_key(:port) # Port where Appium is running
      expect(ret).to have_key(:wda_port) # Port on which WDA is accessible on the machine (i.e the iProxy port)
    end

    it "should call all the required methods in the correct order while setting up a session which has no special capabilities" do
      selenium_command_params[:video] = "true"
      expect(DeviceManager).to receive(:check_and_give_icloud_access)
      allow(DeviceManager).to receive(:check_and_give_settings_app_access)
      expect(Thread).to receive(:new).and_return(mock_thread)
      expect(AutomateFunnel).to receive(:new).and_return(automate_funnel)
      expect(automate_funnel).to receive(:mark_block_start).with('iOSFireCMD')
      expect(automate_funnel).to receive(:mark_breakup_start)
      expect(automate_funnel).to receive(:mark_block_start).with('DeviceSetup')
      expect(AppleTVUtils).to receive(:apple_tv_device?).and_return(false).exactly(3).times
      expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
      expect(FileUtils).to receive(:touch).with("#{STATE_FILES_DIR}/session_start/#{device_id}").at_least(:once)
      expect(FileUtils).to receive(:touch).with("/var/log/browserstack/app_log_#{device_id}.log")
      expect(DeviceManager).to receive(:write_session_info).with(device_id, selenium_command_params).once.ordered
      expect(Thread).to receive(:new).and_return(mock_thread).once.ordered
      expect(BrowserStack::IPhone).to receive(:unlock_device).once.ordered
      expect(Thread).to receive(:new).and_return(mock_thread).twice
      expect(BrowserStack::OSUtils).to receive(:execute).with(/\[x\]codebuild/).and_return("true").once.ordered
      expect(automate_funnel).to receive(:mark_block_end).with('DeviceSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('LocalTunnelSetup')
      expect(automate_funnel).to receive(:mark_block_end).with('LocalTunnelSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('VideoRecording')
      expect_any_instance_of(VideoRecManager).to receive(:start_rec)
      expect(automate_funnel).to receive(:mark_block_end).with('VideoRecording', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('GeoLocation')
      expect(automate_funnel).to receive(:mark_block_end).with('GeoLocation', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('NetworkSimulation')
      expect(automate_funnel).to receive(:mark_block_end).with('NetworkSimulation', 'success')
      expect(automate_funnel).to receive(:mark_breakup_end)
      expect(automate_funnel).to receive(:mark_block_end)
      expect(automate_funnel).to receive(:generate_data_json)

      ret = DeviceManager.fire_cmd_start(device_id, selenium_command_params)

      # TODO: have more robust assertions
      expect(ret).to_not have_key(:error)
      expect(ret).to have_key("platform_time_stats")
      expect(ret).to have_key(:port) # Port where Appium is running
      expect(ret).to have_key(:wda_port) # Port on which WDA is accessible on the machine (i.e the iProxy port)
    end

    it "should call all the required methods in the correct order while setting up a session which has no special capabilities for Apple TV" do
      selenium_command_params[:video] = "true"
      expect(DeviceManager).to receive(:check_and_give_icloud_access)
      expect(Thread).to receive(:new).and_return(mock_thread).twice
      expect(AutomateFunnel).to receive(:new).and_return(automate_funnel)
      expect(automate_funnel).to receive(:mark_block_start).with('iOSFireCMD')
      expect(automate_funnel).to receive(:mark_breakup_start)
      expect(automate_funnel).to receive(:mark_block_start).with('DeviceSetup')
      expect(AppleTVUtils).to receive(:apple_tv_device?).and_return(true).exactly(3).times
      expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
      expect(FileUtils).to receive(:touch).with("#{STATE_FILES_DIR}/session_start/#{device_id}").at_least(:once)
      expect(FileUtils).to receive(:touch).with("/var/log/browserstack/app_log_#{device_id}.log")
      expect(DeviceManager).to receive(:write_session_info).with(device_id, selenium_command_params).once.ordered
      expect(Thread).to receive(:new).and_return(mock_thread).once.ordered
      expect(BrowserStack::IPhone).not_to receive(:unlock_device)
      expect(Thread).to receive(:new).and_return(mock_thread).once.ordered
      expect(BrowserStack::OSUtils).to receive(:execute).with(/\[x\]codebuild/).and_return("true").once.ordered
      expect(automate_funnel).to receive(:mark_block_end).with('DeviceSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('LocalTunnelSetup')
      expect(automate_funnel).to receive(:mark_block_end).with('LocalTunnelSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('VideoRecording')
      expect_any_instance_of(VideoRecManager).to receive(:start_rec)
      expect(automate_funnel).to receive(:mark_block_end).with('VideoRecording', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('GeoLocation')
      expect(automate_funnel).to receive(:mark_block_end).with('GeoLocation', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('NetworkSimulation')
      expect(automate_funnel).to receive(:mark_block_end).with('NetworkSimulation', 'success')
      expect(automate_funnel).to receive(:mark_breakup_end)
      expect(automate_funnel).to receive(:mark_block_end)
      expect(automate_funnel).to receive(:generate_data_json)

      ret = DeviceManager.fire_cmd_start(device_id, selenium_command_params)

      # TODO: have more robust assertions
      expect(ret).to_not have_key(:error)
      expect(ret).to have_key("platform_time_stats")
      expect(ret).to have_key(:port) # Port where Appium is running
      expect(ret).to have_key(:wda_port) # Port on which WDA is accessible on the machine (i.e the iProxy port)
    end

    it "should call check_and_enable_paint_timing if required param present" do
      new_selenium_command_params = selenium_command_params.clone
      new_selenium_command_params[:paint_timing_enable] = "true"
      new_selenium_command_params[:session_id] = SecureRandom.hex
      new_selenium_command_params[:terminal_ip] = "***********"

      allow(DeviceManager).to receive(:check_and_give_settings_app_access)
      allow(DeviceManager).to receive(:device_configuration_check).and_return({ "device_version" => "14.0", "device_name" => "iPhone10,4" })
      expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
      expect(FileUtils).to receive(:touch).with("#{STATE_FILES_DIR}/session_start/#{device_id}").at_least(:once)
      expect(FileUtils).to receive(:touch).with("/var/log/browserstack/app_log_#{device_id}.log")
      expect(DeviceManager).to receive(:check_and_enable_paint_timing).with(device_id, new_selenium_command_params)
      allow(mock_thread).to receive(:bs_run)
      allow(Thread).to receive(:bs_run).and_return(mock_thread)

      ret = DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)

      expect(ret).to_not have_key(:error)
      expect(ret).to have_key("platform_time_stats")
      expect(ret).to have_key(:port)
      expect(ret).to have_key(:wda_port)
    end

    it "should call AllowPopups.do if required param present" do
      new_selenium_command_params = selenium_command_params.clone
      new_selenium_command_params["safariAllowPopups"] = "true"

      allow(DeviceManager).to receive(:check_and_give_settings_app_access)
      allow(DeviceManager).to receive(:device_configuration_check).and_return({ "device_version" => "14.0", "device_name" => "iPhone10,4" })
      expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
      expect(FileUtils).to receive(:touch).with("#{STATE_FILES_DIR}/session_start/#{device_id}").at_least(:once)
      expect(FileUtils).to receive(:touch).with("/var/log/browserstack/app_log_#{device_id}.log")
      expect(AllowPopups).to receive(:do).with(device_id, "14.0", "automate").and_return(true)
      allow(mock_thread).to receive(:bs_run)
      allow(Thread).to receive(:bs_run).and_return(mock_thread)

      ret = DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)

      expect(ret).to_not have_key(:error)
    end

    it "should return with error if AllowPopups.do returns false" do
      new_selenium_command_params = selenium_command_params.clone
      new_selenium_command_params["safariAllowPopups"] = "true"

      allow(DeviceManager).to receive(:check_and_give_settings_app_access)
      allow(DeviceManager).to receive(:device_configuration_check).and_return({ "device_version" => "14.0", "device_name" => "iPhone10,4" })
      expect(AllowPopups).to receive(:do).with(device_id, "14.0", "automate").and_return(false)
      allow(mock_thread).to receive(:bs_run)
      allow(Thread).to receive(:bs_run).and_return(mock_thread)

      ret = DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)

      expect(ret).to have_key(:error)
      expect(ret[:error]).to eq("Failed to AllowPopup")
    end
  end

  context "AppAutomate" do
    let(:app_download_url) do
      "http://some.download.url"
    end

    # TODO: Move this to a fixture as more params are added here.
    let(:selenium_command_params) do
      {
        :genre => "app_automate",
        "automate_session_id" => SecureRandom.hex,
        "s3_app_url" => app_download_url,
        # event_hash is injected into params in server.rb
        # This is used for instrumentation.
        :event_hash => {
          absoluate_start_time: (Time.now.to_f * 1000).to_i
        }
      }
    end

    before(:each) do
      allow(DeviceManager).to receive(:check_and_give_icloud_access)
      allow(DeviceManager).to receive(:check_and_give_settings_app_access)
      allow(CustomMDMManager).to receive(:is_custom_mdm_device?).and_return(false)
      allow_any_instance_of(DeviceState).to receive(:custom_mdm_remove_setup_file_present?).and_return(false)
    end

    let(:automate_funnel) { double :automate_funnel }
    let(:device_id) { "randomdeviceid" }
    let(:media_sync_params) { { feature_usage: {} } }

    it "should use ideviceinstaller when app_automate_custom_params has install_via_ideviceinstaller set to true" do
      new_selenium_command_params = selenium_command_params.clone
      new_selenium_command_params["app_automate_custom_params"] = { "install_via_ideviceinstaller" => true }.to_json

      allow(DeviceManager).to receive(:device_configuration_check).and_return({ "device_version" => "14.0", "device_name" => "iPhone10,4" })
      allow(FileUtils).to receive(:touch)
      allow(mock_thread).to receive(:bs_run)
      allow(Thread).to receive(:bs_run).and_return(mock_thread)

      expect(DeviceManager).to receive(:download_and_install_app).with(
        device_id,
        anything,
        anything,
        anything,
        anything,
        hash_including("install_via_ideviceinstaller" => true)
      ).and_return(app_download_url)

      DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)
    end

    it "should not use ideviceinstaller when app_automate_custom_params has install_via_ideviceinstaller set to false" do
      new_selenium_command_params = selenium_command_params.clone
      new_selenium_command_params["app_automate_custom_params"] = { "install_via_ideviceinstaller" => false }.to_json

      allow(DeviceManager).to receive(:device_configuration_check).and_return({ "device_version" => "14.0", "device_name" => "iPhone10,4" })
      allow(FileUtils).to receive(:touch)
      allow(mock_thread).to receive(:bs_run)
      allow(Thread).to receive(:bs_run).and_return(mock_thread)

      expect(DeviceManager).to receive(:download_and_install_app).with(
        device_id,
        anything,
        anything,
        anything,
        anything,
        hash_not_including("install_via_ideviceinstaller" => true)
      ).and_return(app_download_url)

      DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)
    end

    it "should call AllowPopups.do if required param present" do
      new_selenium_command_params = selenium_command_params.clone
      new_selenium_command_params["safariAllowPopups"] = "true"
      app_downloaded_path = "/some/random/path"

      allow(DeviceManager).to receive(:device_configuration_check).and_return({ "device_version" => "14.0", "device_name" => "iPhone10,4" })
      expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
      expect(FileUtils).to receive(:touch).with("#{STATE_FILES_DIR}/session_start/#{device_id}").at_least(:once)
      expect(FileUtils).to receive(:touch).with("/var/log/browserstack/app_log_#{device_id}.log")
      expect(AllowPopups).to receive(:do).with(device_id, "14.0", "app_automate").and_return(true)
      allow(mock_thread).to receive(:bs_run)
      allow(Thread).to receive(:bs_run).and_return(mock_thread)
      expect(DeviceManager).to receive(:download_and_install_app).and_return(app_download_url)

      ret = DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)
      expect(ret).to_not have_key(:error)
    end

    it 'should call check and set cleanup policy' do
      new_selenium_command_params = selenium_command_params.clone
      app_downloaded_path = "/some/random/path"

      allow(DeviceManager).to receive(:device_configuration_check).and_return({ "device_version" => "14.0", "device_name" => "iPhone10,4" })
      allow(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
      allow(FileUtils).to receive(:touch).with("#{STATE_FILES_DIR}/session_start/#{device_id}").at_least(:once)
      allow(FileUtils).to receive(:touch).with("/var/log/browserstack/app_log_#{device_id}.log")
      allow(mock_thread).to receive(:bs_run)
      allow(Thread).to receive(:bs_run).and_return(mock_thread)
      allow(DeviceManager).to receive(:download_and_install_app).and_return(app_download_url)
      expect(DeviceManager).to receive(:check_and_set_cleanup_policy)
      DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)
    end

    it "successful validate_custom_media_sync" do
      expect(BrowserStack::OSUtils).to receive(:execute).with(%r{/usr/local/bin/gtimeout}, true).and_return(["true", 0]).once.ordered
      expect(BrowserStack::OSUtils).to receive(:execute).with(/File write successfully/).and_return("1").once.ordered
      expect(BrowserStack::OSUtils).to receive(:execute).with(/Success: Media sync done/).and_return("1").once.ordered
      expect(BrowserStack::OSUtils).to receive(:execute).with(/Error: Some error occured during/).and_return(nil).once.ordered
      expect(BrowserStack::OSUtils).to receive(:execute).with(/rm -rf/, true).and_return(0).once.ordered

      DeviceManager.validate_custom_media_sync(device_id, 1, 0, media_sync_params)
      expect(media_sync_params).to eql({ feature_usage: {} })
    end

    it "failed media_sync file copy in validate_custom_media_sync" do
      expect(BrowserStack::OSUtils).to receive(:execute).with(%r{/usr/local/bin/gtimeout}, true).and_return(["true", 1]).exactly(8).times
      expect(BrowserStack::OSUtils).to receive(:execute).with(/rm -rf/, true).and_return(0).once.ordered

      expect { DeviceManager.validate_custom_media_sync(device_id, 1, 0, media_sync_params) }.to raise_exception
      expect(media_sync_params).to eql({ feature_usage: { "customMedia" => { success: false, exception: "custom_media sync failed" } } })
    end

    it "failed media_sync file write in validate_custom_media_sync" do
      expect(BrowserStack::OSUtils).to receive(:execute).with(%r{/usr/local/bin/gtimeout}, true).and_return(["true", 0]).exactly(8).times
      expect(BrowserStack::OSUtils).to receive(:execute).with(/File write successfully/).and_return("0").exactly(8).times
      expect(BrowserStack::OSUtils).to receive(:execute).with(/rm -rf/, true).and_return(0).once.ordered

      expect { DeviceManager.validate_custom_media_sync(device_id, 1, 0, media_sync_params) }.to raise_exception
      expect(media_sync_params).to eql({ feature_usage: { "customMedia" => { success: false, exception: "custom_media sync failed" } } })
    end

    it "failed to sync all custom media images in validate_custom_media_sync" do
      expect(BrowserStack::OSUtils).to receive(:execute).with(%r{/usr/local/bin/gtimeout}, true).and_return(["true", 0]).once.ordered
      expect(BrowserStack::OSUtils).to receive(:execute).with(/File write successfully/).and_return("1").once.ordered
      expect(BrowserStack::OSUtils).to receive(:execute).with(/Success: Media sync done/).and_return("0").once.ordered
      expect(BrowserStack::OSUtils).to receive(:execute).with(/Error: Some error occured during/).and_return(nil).once.ordered
      expect(BrowserStack::OSUtils).to receive(:execute).with(/rm -rf/, true).and_return(0).once.ordered

      expect { DeviceManager.validate_custom_media_sync(device_id, 1, 0, media_sync_params) }.to raise_exception
      expect(media_sync_params).to eql({ feature_usage: { "customMedia" => { success: false, exception: "custom_media sync failed" } } })
    end

    it "failed UIImageWriteToSavedPhotosAlbum in validate_custom_media_sync" do
      expect(BrowserStack::OSUtils).to receive(:execute).with(%r{/usr/local/bin/gtimeout}, true).and_return(["true", 0]).once.ordered
      expect(BrowserStack::OSUtils).to receive(:execute).with(/File write successfully/).and_return("1").once.ordered
      expect(BrowserStack::OSUtils).to receive(:execute).with(/Success: Media sync done/).and_return("0").once.ordered
      expect(BrowserStack::OSUtils).to receive(:execute).with(/Error: Some error occured during/).and_return("1").once.ordered
      expect(BrowserStack::OSUtils).to receive(:execute).with(/rm -rf/, true).and_return(0).once.ordered

      expect { DeviceManager.validate_custom_media_sync(device_id, 1, 0, media_sync_params) }.to raise_exception
      expect(media_sync_params).to eql({ feature_usage: { "customMedia" => { success: false, exception: "custom_media sync failed" } } })
    end

    it "should call all the required methods in the correct order while setting up a session which has no special capabilities" do
      selenium_command_params["webrtc_session_id"] = "session-1234"
      selenium_command_params[:video] = "true"
      expect(Thread).to receive(:new).and_return(mock_thread)
      expect(AutomateFunnel).to receive(:new).and_return(automate_funnel)
      expect(automate_funnel).to receive(:mark_block_start).with('iOSFireCMD')
      expect(automate_funnel).to receive(:mark_breakup_start)
      expect(automate_funnel).to receive(:mark_block_start).with('iosInteractiveSession')
      expect(automate_funnel).to receive(:mark_block_end).with('iosInteractiveSession')
      expect(automate_funnel).to receive(:mark_block_start).with('DeviceSetup')
      expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
      expect(FileUtils).to receive(:touch).with("#{STATE_FILES_DIR}/session_start/#{device_id}").at_least(:once)
      expect(FileUtils).to receive(:touch).with("/var/log/browserstack/app_log_#{device_id}.log")
      expect(DeviceManager).to receive(:write_session_info).with(device_id, selenium_command_params).exactly(2).times

      expect(Thread).to receive(:new).and_return(mock_thread).twice
      expect(BrowserStack::IPhone).to_not receive(:unlock_device)

      # Download App from S3
      app_downloaded_path = "/some/random/path"
      expect(DeviceManager).to receive(:download_and_install_app).and_return(app_download_url).ordered

      expect(AppleTVUtils).to receive(:apple_tv_device?).and_return(false).exactly(4).times
      expect(BrowserStack::OSUtils).to receive(:execute).with(/\[x\]codebuild/).and_return("true").once.ordered
      expect(automate_funnel).to receive(:mark_block_end).with('DeviceSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('LocalTunnelSetup')
      expect(automate_funnel).to receive(:mark_block_end).with('LocalTunnelSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('VideoRecording')
      expect_any_instance_of(VideoRecManager).to receive(:start_rec)
      expect(automate_funnel).to receive(:mark_block_end).with('VideoRecording', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('GeoLocation')
      expect(automate_funnel).to receive(:mark_block_end).with('GeoLocation', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('NetworkSimulation')
      expect(automate_funnel).to receive(:mark_block_end).with('NetworkSimulation', 'success')
      expect(automate_funnel).to receive(:mark_breakup_end)
      expect(automate_funnel).to receive(:mark_block_end)
      expect(automate_funnel).to receive(:generate_data_json)

      ret = DeviceManager.fire_cmd_start(device_id, selenium_command_params)

      # TODO: have more robust assertions
      expect(ret).to_not have_key(:error)
      expect(ret).to have_key("platform_time_stats")
      expect(ret).to have_key(:port) # Port where Appium is running
      expect(ret).to have_key(:wda_port) # Port on which WDA is accessible on the machine (i.e the iProxy port)
    end

    it "should call all the required methods in the correct order while setting up a session which has a special capability of other_apps" do
      new_selenium_command_params = selenium_command_params.clone
      new_selenium_command_params['other_apps'] = ["bs://abcdfegh"].to_s
      new_selenium_command_params["webrtc_session_id"] = "session-1234"
      new_selenium_command_params[:video] = "true"
      expect(Thread).to receive(:new).and_return(mock_thread)
      expect(AutomateFunnel).to receive(:new).and_return(automate_funnel)
      expect(automate_funnel).to receive(:mark_block_start).with('iOSFireCMD')
      expect(automate_funnel).to receive(:mark_breakup_start)
      expect(automate_funnel).to receive(:mark_block_start).with('iosInteractiveSession')
      expect(automate_funnel).to receive(:mark_block_end).with('iosInteractiveSession')
      expect(automate_funnel).to receive(:mark_block_start).with('DeviceSetup')
      expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
      expect(FileUtils).to receive(:touch).with("#{STATE_FILES_DIR}/session_start/#{device_id}").at_least(:once)
      expect(FileUtils).to receive(:touch).with("/var/log/browserstack/app_log_#{device_id}.log")
      expect(DeviceManager).to receive(:write_session_info).with(device_id, new_selenium_command_params).exactly(3).times
      expect(Thread).to receive(:new).and_return(mock_thread).twice
      expect(BrowserStack::IPhone).to_not receive(:unlock_device)

      # Download Other App from S3
      other_app_downloaded_url = "http://otherapp.download.url"
      expect(DeviceManager).to receive(:download_and_install_app).and_return(app_download_url).once.ordered
      expect(DeviceManager).to receive(:download_and_install_app).and_return(other_app_downloaded_url).once.ordered

      expect(AppleTVUtils).to receive(:apple_tv_device?).and_return(false).exactly(4).times
      expect(BrowserStack::OSUtils).to receive(:execute).with(/\[x\]codebuild/).and_return("true").once.ordered
      expect(automate_funnel).to receive(:mark_block_end).with('DeviceSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('LocalTunnelSetup')
      expect(automate_funnel).to receive(:mark_block_end).with('LocalTunnelSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('VideoRecording')
      expect_any_instance_of(VideoRecManager).to receive(:start_rec)
      expect(automate_funnel).to receive(:mark_block_end).with('VideoRecording', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('GeoLocation')
      expect(automate_funnel).to receive(:mark_block_end).with('GeoLocation', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('NetworkSimulation')
      expect(automate_funnel).to receive(:mark_block_end).with('NetworkSimulation', 'success')
      expect(automate_funnel).to receive(:mark_breakup_end)
      expect(automate_funnel).to receive(:mark_block_end)
      expect(automate_funnel).to receive(:generate_data_json)

      ret = DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)

      expect(ret).to_not have_key(:error)
      expect(ret).to have_key("platform_time_stats")
      expect(ret).to have_key(:port) # Port where Appium is running
      expect(ret).to have_key(:wda_port) # Port on which WDA is accessible on the machine (i.e the iProxy port)
      expect(ret["platform_time_stats"]).to have_key("downloading_and_installing_dependent_apps")
    end

    it "should call all the required methods in the correct order while setting up a session which has a special capability of mid_session_install_apps" do
      new_selenium_command_params = selenium_command_params.clone
      new_selenium_command_params['mid_session_install_apps'] = ["bs://abcdfegh"].to_s
      new_selenium_command_params["webrtc_session_id"] = "session-1234"
      new_selenium_command_params[:video] = "true"
      expect(Thread).to receive(:new).and_return(mock_thread)
      expect(AutomateFunnel).to receive(:new).and_return(automate_funnel)
      expect(automate_funnel).to receive(:mark_block_start).with('iOSFireCMD')
      expect(automate_funnel).to receive(:mark_breakup_start)
      expect(automate_funnel).to receive(:mark_block_start).with('iosInteractiveSession')
      expect(automate_funnel).to receive(:mark_block_end).with('iosInteractiveSession')
      expect(automate_funnel).to receive(:mark_block_start).with('DeviceSetup')
      expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
      expect(FileUtils).to receive(:touch).with("#{STATE_FILES_DIR}/session_start/#{device_id}").at_least(:once)
      expect(FileUtils).to receive(:touch).with("/var/log/browserstack/app_log_#{device_id}.log")
      expect(DeviceManager).to receive(:write_session_info).with(device_id, new_selenium_command_params).exactly(3).times
      expect(Thread).to receive(:new).and_return(mock_thread).twice
      expect(BrowserStack::IPhone).to_not receive(:unlock_device)

      # Download Other App from S3
      mid_session_install_app_downloaded_url = "http://midsessioninstallapp.download.url"
      expect(DeviceManager).to receive(:download_and_install_app).and_return(app_download_url).once.ordered
      expect(DeviceManager).to receive(:download_and_install_app).and_return(mid_session_install_app_downloaded_url).once.ordered

      expect(AppleTVUtils).to receive(:apple_tv_device?).and_return(false).exactly(4).times
      expect(BrowserStack::OSUtils).to receive(:execute).with(/\[x\]codebuild/).and_return("true").once.ordered
      expect(automate_funnel).to receive(:mark_block_end).with('DeviceSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('LocalTunnelSetup')
      expect(automate_funnel).to receive(:mark_block_end).with('LocalTunnelSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('VideoRecording')
      expect_any_instance_of(VideoRecManager).to receive(:start_rec)
      expect(automate_funnel).to receive(:mark_block_end).with('VideoRecording', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('GeoLocation')
      expect(automate_funnel).to receive(:mark_block_end).with('GeoLocation', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('NetworkSimulation')
      expect(automate_funnel).to receive(:mark_block_end).with('NetworkSimulation', 'success')
      expect(automate_funnel).to receive(:mark_breakup_end)
      expect(automate_funnel).to receive(:mark_block_end)
      expect(automate_funnel).to receive(:generate_data_json)

      ret = DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)

      expect(ret).to_not have_key(:error)
      expect(ret).to have_key("platform_time_stats")
      expect(ret).to have_key(:port) # Port where Appium is running
      expect(ret).to have_key(:wda_port) # Port on which WDA is accessible on the machine (i.e the iProxy port)
      expect(ret["platform_time_stats"]).to have_key("downloading_and_installing_dependent_apps")
    end

    it "should call all the required methods while setting up a session which has a special capability of mid_session_install_apps non reserved flow" do
      new_selenium_command_params = selenium_command_params.clone
      new_selenium_command_params['mid_session_install_apps'] = ["bs://abcdfegh"].to_s
      new_selenium_command_params["webrtc_session_id"] = "session-1234"
      new_selenium_command_params["reserveDevice"] = false
      new_selenium_command_params[:video] = "true"
      expect(DeviceManager).to receive(:check_dedicated_device_session)
      expect(Thread).to receive(:new).and_return(mock_thread)
      expect(AutomateFunnel).to receive(:new).and_return(automate_funnel)
      expect(automate_funnel).to receive(:mark_block_start).with('iOSFireCMD')
      expect(automate_funnel).to receive(:mark_breakup_start)
      expect(automate_funnel).to receive(:mark_block_start).with('iosInteractiveSession')
      expect(automate_funnel).to receive(:mark_block_end).with('iosInteractiveSession')
      expect(automate_funnel).to receive(:mark_block_start).with('DeviceSetup')
      expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once
      expect(FileUtils).to receive(:touch).with("#{STATE_FILES_DIR}/session_start/#{device_id}").at_least(:once)
      expect(FileUtils).to receive(:touch).with("/var/log/browserstack/app_log_#{device_id}.log")
      expect(DeviceManager).to receive(:write_session_info).with(device_id, new_selenium_command_params).exactly(3).times
      expect(Thread).to receive(:new).and_return(mock_thread).once

      deviceStateMock = double('DeviceStateMock')
      expect(deviceStateMock).to_not receive(:minimized_cleanup_reserved_file_present?)
      expect(deviceStateMock).to receive(:minimized_cleanup_unreserved_file_present?).and_return false
      expect(deviceStateMock).to_not receive(:preserve_app_state_reserved_file_present?)
      expect(deviceStateMock).to receive(:dedicated_cleanup_file_present?).and_return false
      expect(deviceStateMock).to receive(:custom_mdm_remove_setup_file_present?).and_return(false)
      allow(deviceStateMock).to receive(:keep_wifi_enabled_file_present?).and_return false

      expect(deviceStateMock).to_not receive(:touch_minimized_cleanup_reserved_file)

      expect(DeviceState).to receive(:new).exactly(4).times.and_return(deviceStateMock)
      # expect(BrowserStack::IPhone).to receive(:unlock_device).once

      # Download Other App from S3
      mid_session_install_app_downloaded_url = "http://midsessioninstallapp.download.url"
      expect(DeviceManager).to receive(:download_and_install_app).and_return(app_download_url).once
      expect(DeviceManager).to receive(:download_and_install_app).and_return(mid_session_install_app_downloaded_url).once

      expect(AppleTVUtils).to receive(:apple_tv_device?).and_return(false).exactly(4).times
      expect(BrowserStack::OSUtils).to receive(:execute).with(/\[x\]codebuild/).and_return("true").once
      expect(automate_funnel).to receive(:mark_block_end).with('DeviceSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('LocalTunnelSetup')
      expect(automate_funnel).to receive(:mark_block_end).with('LocalTunnelSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('VideoRecording')
      expect_any_instance_of(VideoRecManager).to receive(:start_rec)
      expect(automate_funnel).to receive(:mark_block_end).with('VideoRecording', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('GeoLocation')
      expect(automate_funnel).to receive(:mark_block_end).with('GeoLocation', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('NetworkSimulation')
      expect(automate_funnel).to receive(:mark_block_end).with('NetworkSimulation', 'success')
      expect(automate_funnel).to receive(:mark_breakup_end)
      expect(automate_funnel).to receive(:mark_block_end)
      expect(automate_funnel).to receive(:generate_data_json)
      expect(BrowserStack::DeviceLogger).to receive(:initialize).and_return("true").once
      expect(BrowserStack::DeviceLogger).to receive(:start).and_return("true").once

      ret = DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)

      expect(ret).to_not have_key(:error)
      expect(ret).to have_key("platform_time_stats")
      expect(ret).to have_key(:port) # Port where Appium is running
      expect(ret).to have_key(:wda_port) # Port on which WDA is accessible on the machine (i.e the iProxy port)
      expect(ret["platform_time_stats"]).to have_key("downloading_and_installing_dependent_apps")
    end

    it "should call all the required methods including timezone, custom_media, latitude, longitude  "\
    "while setting up a session which has a special capability of mid_session_install_apps for 1st session of reserved flow" do
      deviceStateMock = double('DeviceStateMock')
      expect(deviceStateMock).to receive(:minimized_cleanup_reserved_file_present?).and_return(false, false)
      expect(deviceStateMock).to receive(:minimized_cleanup_unreserved_file_present?).and_return false
      expect(deviceStateMock).to receive(:preserve_app_state_reserved_file_present?).and_return false
      expect(deviceStateMock).to receive(:dedicated_cleanup_file_present?).and_return false
      expect(deviceStateMock).to receive(:custom_mdm_remove_setup_file_present?).and_return(false)
      allow(deviceStateMock).to receive(:keep_wifi_enabled_file_present?).and_return false

      expect(deviceStateMock).to receive(:touch_minimized_cleanup_reserved_file).and_return true

      expect(DeviceState).to receive(:new).exactly(4).times.and_return(deviceStateMock)
      new_selenium_command_params = selenium_command_params.clone
      new_selenium_command_params['mid_session_install_apps'] = ["bs://abcdfegh"].to_s
      new_selenium_command_params["webrtc_session_id"] = "session-1234"
      new_selenium_command_params[:video] = "true"
      new_selenium_command_params["reserveDevice"] = true
      new_selenium_command_params["timezone"] = "fr"
      new_selenium_command_params["custom_media"] = "1"
      new_selenium_command_params["latitude"] = "1"
      new_selenium_command_params["longitude"] = "1"
      new_selenium_command_params[:networkSimulation] = true
      expect(DeviceManager).to receive(:check_dedicated_device_session)
      expect(AutomateFunnel).to receive(:new).and_return(automate_funnel)
      expect(automate_funnel).to receive(:mark_block_start).with('iOSFireCMD')
      expect(automate_funnel).to receive(:mark_breakup_start)
      expect(automate_funnel).to receive(:mark_block_start).with('iosInteractiveSession')
      expect(automate_funnel).to receive(:mark_block_end).with('iosInteractiveSession')
      expect(automate_funnel).to receive(:mark_block_start).with('DeviceSetup')
      expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once
      expect(FileUtils).to receive(:touch).with("#{STATE_FILES_DIR}/session_start/#{device_id}").at_least(:once)
      expect(FileUtils).to receive(:touch).with("/var/log/browserstack/app_log_#{device_id}.log")
      expect(DeviceManager).to receive(:write_session_info).with(device_id, new_selenium_command_params).exactly(3).times
      expect(Thread).to receive(:new).and_return(mock_thread).twice
      expect(BrowserStack::IPhone).to receive(:change_time_zone).once
      expect(BrowserStack::IPhone).to receive(:download_and_push_custom_media).once.and_return([1, 1, { "feature_usage_1" => { success: true, exception: "exception" } }])
      expect(DeviceManager).to receive(:set_gpslocation)
      expect(DeviceManager).to receive(:validate_custom_media_sync)
      expect(DeviceManager).to receive(:apply_network_simulation).and_return({})

      # Download Other App from S3
      mid_session_install_app_downloaded_url = "http://midsessioninstallapp.download.url"
      expect(DeviceManager).to receive(:download_and_install_app).and_return(app_download_url).once
      expect(DeviceManager).to receive(:download_and_install_app).and_return(mid_session_install_app_downloaded_url).once

      expect(AppleTVUtils).to receive(:apple_tv_device?).and_return(false).exactly(4).times
      expect(BrowserStack::OSUtils).to receive(:execute).with(/\[x\]codebuild/).and_return("true").once
      expect(automate_funnel).to receive(:mark_block_end).with('DeviceSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('LocalTunnelSetup')
      expect(automate_funnel).to receive(:mark_block_end).with('LocalTunnelSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('VideoRecording')
      expect_any_instance_of(VideoRecManager).to receive(:start_rec)
      expect(automate_funnel).to receive(:mark_block_end).with('VideoRecording', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('GeoLocation')
      expect(automate_funnel).to receive(:mark_block_end).with('GeoLocation', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('NetworkSimulation')
      expect(automate_funnel).to receive(:mark_block_end).with('NetworkSimulation', 'success')
      expect(automate_funnel).to receive(:mark_breakup_end)
      expect(automate_funnel).to receive(:mark_block_end)
      expect(automate_funnel).to receive(:generate_data_json)
      expect(BrowserStack::DeviceLogger).to receive(:initialize).and_return("true").once
      expect(BrowserStack::DeviceLogger).to receive(:start).and_return("true").once

      ret = DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)

      expect(ret).to_not have_key(:error)
      expect(ret).to have_key("platform_time_stats")
      expect(ret).to have_key(:port) # Port where Appium is running
      expect(ret).to have_key(:wda_port) # Port on which WDA is accessible on the machine (i.e the iProxy port)
      expect(ret["platform_time_stats"]).to have_key("downloading_and_installing_dependent_apps")
    end

    it "should not call methods relating timezone, custom_media, latitude, longitude  while setting up a session for 1+ sessions of reserved flow" do
      deviceStateMock = double('DeviceStateMock')
      expect(deviceStateMock).to receive(:minimized_cleanup_reserved_file_present?).and_return(true, true)
      expect(deviceStateMock).to receive(:minimized_cleanup_unreserved_file_present?).and_return false
      expect(deviceStateMock).to receive(:preserve_app_state_reserved_file_present?).and_return false
      expect(deviceStateMock).to receive(:dedicated_cleanup_file_present?).and_return false
      expect(deviceStateMock).to receive(:custom_mdm_remove_setup_file_present?).and_return(false)
      allow(deviceStateMock).to receive(:keep_wifi_enabled_file_present?).and_return false

      expect(deviceStateMock).to_not receive(:touch_minimized_cleanup_reserved_file)

      expect(DeviceState).to receive(:new).exactly(4).times.and_return(deviceStateMock)
      new_selenium_command_params = selenium_command_params.clone
      new_selenium_command_params['mid_session_install_apps'] = ["bs://abcdfegh"].to_s
      new_selenium_command_params["webrtc_session_id"] = "session-1234"
      new_selenium_command_params["reserveDevice"] = true
      new_selenium_command_params["timezone"] = "fr"
      new_selenium_command_params["custom_media"] = "1"
      new_selenium_command_params["latitude"] = "1"
      new_selenium_command_params["longitude"] = "1"
      new_selenium_command_params[:video] = "true"
      new_selenium_command_params[:networkSimulation] = true
      expect(DeviceManager).to receive(:check_dedicated_device_session)
      expect(AutomateFunnel).to receive(:new).and_return(automate_funnel)
      expect(automate_funnel).to receive(:mark_block_start).with('iOSFireCMD')
      expect(automate_funnel).to receive(:mark_breakup_start)
      expect(automate_funnel).to receive(:mark_block_start).with('iosInteractiveSession')
      expect(automate_funnel).to receive(:mark_block_end).with('iosInteractiveSession')
      expect(automate_funnel).to receive(:mark_block_start).with('DeviceSetup')
      expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once
      expect(FileUtils).to receive(:touch).with("#{STATE_FILES_DIR}/session_start/#{device_id}").at_least(:once)
      expect(FileUtils).to receive(:touch).with("/var/log/browserstack/app_log_#{device_id}.log")
      expect(DeviceManager).to receive(:write_session_info).with(device_id, new_selenium_command_params).exactly(3).times
      expect(Thread).to receive(:new).and_return(mock_thread).twice
      expect(BrowserStack::IPhone).to_not receive(:change_time_zone)
      expect(BrowserStack::IPhone).to_not receive(:download_and_push_custom_media)
      expect(DeviceManager).to_not receive(:set_gpslocation)
      expect(DeviceManager).to_not receive(:validate_custom_media_sync)
      expect(DeviceManager).to_not receive(:apply_network_simulation)

      # Download Other App from S3
      mid_session_install_app_downloaded_url = "http://midsessioninstallapp.download.url"
      expect(DeviceManager).to receive(:download_and_install_app).and_return(app_download_url).once
      expect(DeviceManager).to receive(:download_and_install_app).and_return(mid_session_install_app_downloaded_url).once

      expect(AppleTVUtils).to receive(:apple_tv_device?).and_return(false).exactly(4).times
      expect(BrowserStack::OSUtils).to receive(:execute).with(/\[x\]codebuild/).and_return("true").once
      expect(automate_funnel).to receive(:mark_block_end).with('DeviceSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('LocalTunnelSetup')
      expect(automate_funnel).to receive(:mark_block_end).with('LocalTunnelSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('VideoRecording')
      expect_any_instance_of(VideoRecManager).to receive(:start_rec)
      expect(automate_funnel).to receive(:mark_block_end).with('VideoRecording', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('GeoLocation')
      expect(automate_funnel).to receive(:mark_block_end).with('GeoLocation', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('NetworkSimulation')
      expect(automate_funnel).to receive(:mark_block_end).with('NetworkSimulation', 'success')
      expect(automate_funnel).to receive(:mark_breakup_end)
      expect(automate_funnel).to receive(:mark_block_end)
      expect(automate_funnel).to receive(:generate_data_json)
      expect(BrowserStack::DeviceLogger).to receive(:initialize).and_return("true").once
      expect(BrowserStack::DeviceLogger).to receive(:start).and_return("true").once

      ret = DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)

      expect(ret).to_not have_key(:error)
      expect(ret).to have_key("platform_time_stats")
      expect(ret).to have_key(:port) # Port where Appium is running
      expect(ret).to have_key(:wda_port) # Port on which WDA is accessible on the machine (i.e the iProxy port)
      expect(ret["platform_time_stats"]).to have_key("downloading_and_installing_dependent_apps")
    end

    it "should call all the required methods including timezone, custom_media, latitude, longitude  "\
    "while setting up a session and should always set preserve_app_state to false for 1st session of reserved flow"\
    " + Also handle for mid - session installs" do
      deviceStateMock = double('DeviceStateMock')
      expect(deviceStateMock).to receive(:minimized_cleanup_reserved_file_present?).and_return(false, false)
      expect(deviceStateMock).to receive(:minimized_cleanup_unreserved_file_present?).and_return false
      expect(deviceStateMock).to receive(:preserve_app_state_reserved_file_present?).and_return false
      expect(deviceStateMock).to receive(:dedicated_cleanup_file_present?).and_return false
      expect(deviceStateMock).to receive(:custom_mdm_remove_setup_file_present?).and_return(false)
      allow(deviceStateMock).to receive(:keep_wifi_enabled_file_present?).and_return false

      expect(deviceStateMock).to receive(:touch_minimized_cleanup_reserved_file).and_return true

      expect(DeviceState).to receive(:new).exactly(4).times.and_return(deviceStateMock)
      new_selenium_command_params = selenium_command_params.clone
      new_selenium_command_params['mid_session_install_apps'] = ["bs://abcdfegh"].to_s
      new_selenium_command_params["webrtc_session_id"] = "session-1234"
      new_selenium_command_params["reserveDevice"] = true
      new_selenium_command_params["timezone"] = "fr"
      new_selenium_command_params["custom_media"] = "1"
      new_selenium_command_params["latitude"] = "1"
      new_selenium_command_params["longitude"] = "1"
      new_selenium_command_params[:video] = "true"
      new_selenium_command_params[:networkSimulation] = true
      expect(DeviceManager).to receive(:check_dedicated_device_session)
      expect(AutomateFunnel).to receive(:new).and_return(automate_funnel)
      expect(automate_funnel).to receive(:mark_block_start).with('iOSFireCMD')
      expect(automate_funnel).to receive(:mark_breakup_start)
      expect(automate_funnel).to receive(:mark_block_start).with('iosInteractiveSession')
      expect(automate_funnel).to receive(:mark_block_end).with('iosInteractiveSession')
      expect(automate_funnel).to receive(:mark_block_start).with('DeviceSetup')
      expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once
      expect(FileUtils).to receive(:touch).with("#{STATE_FILES_DIR}/session_start/#{device_id}").at_least(:once)
      expect(FileUtils).to receive(:touch).with("/var/log/browserstack/app_log_#{device_id}.log")
      expect(DeviceManager).to receive(:write_session_info).with(device_id, new_selenium_command_params).exactly(3).times
      expect(Thread).to receive(:new).and_return(mock_thread).twice
      expect(BrowserStack::IPhone).to receive(:change_time_zone).once
      expect(BrowserStack::IPhone).to receive(:download_and_push_custom_media).once.and_return([1, 1, { "feature_usage_1" => { success: true, exception: "exception" } }])
      expect(DeviceManager).to receive(:set_gpslocation)
      expect(DeviceManager).to receive(:validate_custom_media_sync)
      expect(DeviceManager).to receive(:apply_network_simulation).and_return({})

      # Download Other App from S3
      mid_session_install_app_downloaded_url = "http://midsessioninstallapp.download.url"
      expect(DeviceManager).to receive(:download_and_install_app).with(anything, anything, true, anything, false, hash_including("preserve_app_state" => false ) ).and_return(app_download_url).once

      expect(DeviceManager).to receive(:download_and_install_app).with(anything, anything, true, anything, false, hash_excluding("preserve_app_state") ).once

      expect(AppleTVUtils).to receive(:apple_tv_device?).and_return(false).exactly(4).times
      expect(BrowserStack::OSUtils).to receive(:execute).with(/\[x\]codebuild/).and_return("true").once
      expect(automate_funnel).to receive(:mark_block_end).with('DeviceSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('LocalTunnelSetup')
      expect(automate_funnel).to receive(:mark_block_end).with('LocalTunnelSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('VideoRecording')
      expect_any_instance_of(VideoRecManager).to receive(:start_rec)
      expect(automate_funnel).to receive(:mark_block_end).with('VideoRecording', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('GeoLocation')
      expect(automate_funnel).to receive(:mark_block_end).with('GeoLocation', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('NetworkSimulation')
      expect(automate_funnel).to receive(:mark_block_end).with('NetworkSimulation', 'success')
      expect(automate_funnel).to receive(:mark_breakup_end)
      expect(automate_funnel).to receive(:mark_block_end)
      expect(automate_funnel).to receive(:generate_data_json)
      expect(BrowserStack::DeviceLogger).to receive(:initialize).and_return("true").once
      expect(BrowserStack::DeviceLogger).to receive(:start).and_return("true").once

      ret = DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)

      expect(ret).to_not have_key(:error)
      expect(ret).to have_key("platform_time_stats")
      expect(ret).to have_key(:port) # Port where Appium is running
      expect(ret).to have_key(:wda_port) # Port on which WDA is accessible on the machine (i.e the iProxy port)
      expect(ret["platform_time_stats"]).to have_key("downloading_and_installing_dependent_apps")
    end

    it "should not call methods relating timezone, custom_media, latitude, longitude  while setting up a session and should set preserve_app_state to true if preserve_app_state_reserved_file_present for 1+ sessions of reserved flow + Also handle for mid - session installs" do
      deviceStateMock = double('DeviceStateMock')
      expect(deviceStateMock).to receive(:minimized_cleanup_reserved_file_present?).and_return(true, true)
      expect(deviceStateMock).to receive(:minimized_cleanup_unreserved_file_present?).and_return false
      expect(deviceStateMock).to receive(:preserve_app_state_reserved_file_present?).and_return true
      expect(deviceStateMock).to receive(:dedicated_cleanup_file_present?).and_return false
      expect(deviceStateMock).to receive(:custom_mdm_remove_setup_file_present?).and_return(false)
      allow(deviceStateMock).to receive(:keep_wifi_enabled_file_present?).and_return false

      expect(deviceStateMock).to_not receive(:touch_minimized_cleanup_reserved_file)

      expect(DeviceState).to receive(:new).exactly(4).times.and_return(deviceStateMock)
      new_selenium_command_params = selenium_command_params.clone
      new_selenium_command_params['mid_session_install_apps'] = ["bs://abcdfegh"].to_s
      new_selenium_command_params["webrtc_session_id"] = "session-1234"
      new_selenium_command_params["reserveDevice"] = true
      new_selenium_command_params["timezone"] = "fr"
      new_selenium_command_params["custom_media"] = "1"
      new_selenium_command_params["latitude"] = "1"
      new_selenium_command_params["longitude"] = "1"
      new_selenium_command_params[:video] = "true"
      new_selenium_command_params[:networkSimulation] = true
      expect(DeviceManager).to receive(:check_dedicated_device_session)
      expect(AutomateFunnel).to receive(:new).and_return(automate_funnel)
      expect(automate_funnel).to receive(:mark_block_start).with('iOSFireCMD')
      expect(automate_funnel).to receive(:mark_breakup_start)
      expect(automate_funnel).to receive(:mark_block_start).with('iosInteractiveSession')
      expect(automate_funnel).to receive(:mark_block_end).with('iosInteractiveSession')
      expect(automate_funnel).to receive(:mark_block_start).with('DeviceSetup')
      expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once
      expect(FileUtils).to receive(:touch).with("#{STATE_FILES_DIR}/session_start/#{device_id}").at_least(:once)
      expect(FileUtils).to receive(:touch).with("/var/log/browserstack/app_log_#{device_id}.log")
      expect(DeviceManager).to receive(:write_session_info).with(device_id, new_selenium_command_params).exactly(3).times
      expect(Thread).to receive(:new).and_return(mock_thread).twice
      expect(BrowserStack::IPhone).to_not receive(:change_time_zone)
      expect(BrowserStack::IPhone).to_not receive(:download_and_push_custom_media)
      expect(DeviceManager).to_not receive(:set_gpslocation)
      expect(DeviceManager).to_not receive(:validate_custom_media_sync)
      expect(DeviceManager).to_not receive(:apply_network_simulation)
      expect(BrowserStack::DeviceLogger).to receive(:initialize).and_return("true").once
      expect(BrowserStack::DeviceLogger).to receive(:start).and_return("true").once

      # Download Other App from S3
      mid_session_install_app_downloaded_url = "http://midsessioninstallapp.download.url"
      expect(DeviceManager).to receive(:download_and_install_app).with(anything, anything, true, anything, false, hash_including("preserve_app_state" => true) ).and_return(app_download_url).once
      expect(DeviceManager).to receive(:download_and_install_app).with(anything, anything, true, anything, false, hash_including("preserve_app_state" => true) ).and_return(mid_session_install_app_downloaded_url).once

      expect(AppleTVUtils).to receive(:apple_tv_device?).and_return(false).exactly(4).times
      expect(BrowserStack::OSUtils).to receive(:execute).with(/\[x\]codebuild/).and_return("true").once
      expect(automate_funnel).to receive(:mark_block_end).with('DeviceSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('LocalTunnelSetup')
      expect(automate_funnel).to receive(:mark_block_end).with('LocalTunnelSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('VideoRecording')
      expect_any_instance_of(VideoRecManager).to receive(:start_rec)
      expect(automate_funnel).to receive(:mark_block_end).with('VideoRecording', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('GeoLocation')
      expect(automate_funnel).to receive(:mark_block_end).with('GeoLocation', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('NetworkSimulation')
      expect(automate_funnel).to receive(:mark_block_end).with('NetworkSimulation', 'success')
      expect(automate_funnel).to receive(:mark_breakup_end)
      expect(automate_funnel).to receive(:mark_block_end)
      expect(automate_funnel).to receive(:generate_data_json)

      ret = DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)

      expect(ret).to_not have_key(:error)
      expect(ret).to have_key("platform_time_stats")
      expect(ret).to have_key(:port) # Port where Appium is running
      expect(ret).to have_key(:wda_port) # Port on which WDA is accessible on the machine (i.e the iProxy port)
      expect(ret["platform_time_stats"]).to have_key("downloading_and_installing_dependent_apps")
    end

    it "should call all the required methods including timezone, custom_media, latitude, longitude  "\
    "while setting up a session and should always set preserve_app_state_for_reserved_flow to false for sessions in non reserved flow + Also handle for mid - session installs" do
      deviceStateMock = double('DeviceStateMock')
      expect(deviceStateMock).to_not receive(:minimized_cleanup_reserved_file_present?)
      expect(deviceStateMock).to receive(:minimized_cleanup_unreserved_file_present?).and_return false
      expect(deviceStateMock).to_not receive(:preserve_app_state_reserved_file_present?)
      expect(deviceStateMock).to receive(:dedicated_cleanup_file_present?).and_return false
      expect(deviceStateMock).to receive(:custom_mdm_remove_setup_file_present?).and_return(false)
      allow(deviceStateMock).to receive(:keep_wifi_enabled_file_present?).and_return false

      expect(deviceStateMock).to_not receive(:touch_minimized_cleanup_reserved_file)

      expect(DeviceState).to receive(:new).exactly(4).times.and_return(deviceStateMock)
      new_selenium_command_params = selenium_command_params.clone
      new_selenium_command_params['mid_session_install_apps'] = ["bs://abcdfegh"].to_s
      new_selenium_command_params["webrtc_session_id"] = "session-1234"
      new_selenium_command_params["timezone"] = "fr"
      new_selenium_command_params["custom_media"] = "1"
      new_selenium_command_params["latitude"] = "1"
      new_selenium_command_params["longitude"] = "1"
      new_selenium_command_params[:video] = "true"
      new_selenium_command_params[:networkSimulation] = true
      expect(DeviceManager).to receive(:check_dedicated_device_session)
      expect(AutomateFunnel).to receive(:new).and_return(automate_funnel)
      expect(automate_funnel).to receive(:mark_block_start).with('iOSFireCMD')
      expect(automate_funnel).to receive(:mark_breakup_start)
      expect(automate_funnel).to receive(:mark_block_start).with('iosInteractiveSession')
      expect(automate_funnel).to receive(:mark_block_end).with('iosInteractiveSession')
      expect(automate_funnel).to receive(:mark_block_start).with('DeviceSetup')
      expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once
      expect(FileUtils).to receive(:touch).with("#{STATE_FILES_DIR}/session_start/#{device_id}").at_least(:once)
      expect(FileUtils).to receive(:touch).with("/var/log/browserstack/app_log_#{device_id}.log")
      expect(DeviceManager).to receive(:write_session_info).with(device_id, new_selenium_command_params).exactly(3).times
      expect(Thread).to receive(:new).and_return(mock_thread).twice
      expect(BrowserStack::IPhone).to receive(:change_time_zone).once
      expect(BrowserStack::IPhone).to receive(:download_and_push_custom_media).once.and_return([1, 1, { "feature_usage_1" => { success: true, exception: "exception" } }])
      expect(DeviceManager).to receive(:set_gpslocation)
      expect(DeviceManager).to receive(:validate_custom_media_sync)
      expect(DeviceManager).to receive(:apply_network_simulation).and_return({})

      # Download Other App from S3
      mid_session_install_app_downloaded_url = "http://midsessioninstallapp.download.url"
      expect(DeviceManager).to receive(:download_and_install_app).with(anything, anything, true, anything, false, hash_including("preserve_app_state" => false) ).and_return(app_download_url).once

      expect(DeviceManager).to receive(:download_and_install_app).with(anything, anything, true, anything, false, hash_excluding("preserve_app_state") ).once

      expect(AppleTVUtils).to receive(:apple_tv_device?).and_return(false).exactly(4).times
      expect(BrowserStack::OSUtils).to receive(:execute).with(/\[x\]codebuild/).and_return("true").once
      expect(automate_funnel).to receive(:mark_block_end).with('DeviceSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('LocalTunnelSetup')
      expect(automate_funnel).to receive(:mark_block_end).with('LocalTunnelSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('VideoRecording')
      expect_any_instance_of(VideoRecManager).to receive(:start_rec)
      expect(automate_funnel).to receive(:mark_block_end).with('VideoRecording', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('GeoLocation')
      expect(automate_funnel).to receive(:mark_block_end).with('GeoLocation', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('NetworkSimulation')
      expect(automate_funnel).to receive(:mark_block_end).with('NetworkSimulation', 'success')
      expect(automate_funnel).to receive(:mark_breakup_end)
      expect(automate_funnel).to receive(:mark_block_end)
      expect(automate_funnel).to receive(:generate_data_json)
      expect(BrowserStack::DeviceLogger).to receive(:initialize).and_return("true").once
      expect(BrowserStack::DeviceLogger).to receive(:start).and_return("true").once

      ret = DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)

      expect(ret).to_not have_key(:error)
      expect(ret).to have_key("platform_time_stats")
      expect(ret).to have_key(:port) # Port where Appium is running
      expect(ret).to have_key(:wda_port) # Port on which WDA is accessible on the machine (i.e the iProxy port)
      expect(ret["platform_time_stats"]).to have_key("downloading_and_installing_dependent_apps")
    end

    it "should call all the required methods in the correct order while setting up a session which has update_app_settings cap - default appium" do
      update_app_settings = {
        "Environment" => "PSP",
        "OTP Auto-Fill" => "ON",
        "Performance Charting Mock Data" => "ON",
        "Server config" => "random",
        "Child" => {
          "Child Banner" => "OFF",
          "Explore Environment" => "DEV"
        }
      }.to_json
      new_selenium_command_params = selenium_command_params.clone
      new_selenium_command_params['update_app_settings'] = update_app_settings
      new_selenium_command_params[:video] = "true"
      new_selenium_command_params[:appium] = "1.7.0" #default appium

      expect(Thread).to receive(:bs_run).once.ordered
      expect(AutomateFunnel).to receive(:new).and_return(automate_funnel)
      expect(automate_funnel).to receive(:mark_block_start).with('iOSFireCMD')
      expect(automate_funnel).to receive(:mark_breakup_start)
      expect(automate_funnel).to receive(:mark_block_start).with('DeviceSetup')
      expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
      expect(FileUtils).to receive(:touch).with("#{STATE_FILES_DIR}/session_start/#{device_id}").at_least(:once)
      expect(FileUtils).to receive(:touch).with("/var/log/browserstack/app_log_#{device_id}.log")
      expect(DeviceManager).to receive(:write_session_info).with(device_id, new_selenium_command_params).exactly(2).times

      expect(Thread).to receive(:bs_run).twice
      expect(AppleTVUtils).to receive(:apple_tv_device?).and_return(false).exactly(3).times
      expect(BrowserStack::IPhone).to receive(:unlock_device).once.ordered

      # Download App from S3
      app_downloaded_path = "/some/random/path"
      expect(DeviceManager).to receive(:download_and_install_app).and_return(app_download_url).ordered
      expect(BrowserStack::AppSettingsUtil).to receive(:validate).once.ordered
      expect(BrowserStack::AppSettingsUtil).to receive(:update_settings).once.ordered

      expect(BrowserStack::OSUtils).to receive(:execute).with(/\[x\]codebuild/).and_return("true").once.ordered
      expect(automate_funnel).to receive(:mark_block_end).with('DeviceSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('LocalTunnelSetup')
      expect(automate_funnel).to receive(:mark_block_end).with('LocalTunnelSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('VideoRecording')
      expect_any_instance_of(VideoRecManager).to receive(:start_rec)
      expect(automate_funnel).to receive(:mark_block_end).with('VideoRecording', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('GeoLocation')
      expect(automate_funnel).to receive(:mark_block_end).with('GeoLocation', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('NetworkSimulation')
      expect(automate_funnel).to receive(:mark_block_end).with('NetworkSimulation', 'success')
      expect(automate_funnel).to receive(:mark_breakup_end)
      expect(automate_funnel).to receive(:mark_block_end)
      expect(automate_funnel).to receive(:generate_data_json)

      ret = DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)

      # TODO: have more robust assertions
      expect(ret).to_not have_key(:error)
      expect(ret).to have_key("platform_time_stats")
      expect(ret).to have_key(:port) # Port where Appium is running
      expect(ret).to have_key(:wda_port) # Port on which WDA is accessible on the machine (i.e the iProxy port)
    end

    it "should call all the required methods in the correct order while setting up a session which has update_app_settings cap - default appium for v2 video" do
      update_app_settings = {
        "Environment" => "PSP",
        "OTP Auto-Fill" => "ON",
        "Performance Charting Mock Data" => "ON",
        "Server config" => "random",
        "Child" => {
          "Child Banner" => "OFF",
          "Explore Environment" => "DEV"
        }
      }.to_json
      new_selenium_command_params = selenium_command_params.clone
      new_selenium_command_params['update_app_settings'] = update_app_settings
      new_selenium_command_params["video_params_v2"] = { "v2" => "true" }.to_json
      new_selenium_command_params[:video] = "true"
      new_selenium_command_params[:appium] = "1.7.0" #default appium

      expect(Thread).to receive(:bs_run).once.ordered
      expect(AutomateFunnel).to receive(:new).and_return(automate_funnel)
      expect(automate_funnel).to receive(:mark_block_start).with('iOSFireCMD')
      expect(automate_funnel).to receive(:mark_breakup_start)
      expect(automate_funnel).to receive(:mark_block_start).with('DeviceSetup')
      expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
      expect(FileUtils).to receive(:touch).with("#{STATE_FILES_DIR}/session_start/#{device_id}").at_least(:once)
      expect(FileUtils).to receive(:touch).with("/var/log/browserstack/app_log_#{device_id}.log")
      expect(DeviceManager).to receive(:write_session_info).with(device_id, new_selenium_command_params).exactly(2).times

      expect(Thread).to receive(:bs_run).twice
      expect(AppleTVUtils).to receive(:apple_tv_device?).and_return(false).twice
      expect(BrowserStack::IPhone).to receive(:unlock_device).once.ordered

      # Download App from S3
      app_downloaded_path = "/some/random/path"
      expect(DeviceManager).to receive(:download_and_install_app).and_return(app_download_url).ordered
      expect(BrowserStack::AppSettingsUtil).to receive(:validate).once.ordered
      expect(BrowserStack::AppSettingsUtil).to receive(:update_settings).once.ordered

      expect(BrowserStack::OSUtils).to receive(:execute).with(/\[x\]codebuild/).and_return("true").once.ordered
      expect(automate_funnel).to receive(:mark_block_end).with('DeviceSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('LocalTunnelSetup')
      expect(automate_funnel).to receive(:mark_block_end).with('LocalTunnelSetup', 'success')
      expect(DeviceManager).to receive(:start_v2_video_recording)
      expect(automate_funnel).to receive(:mark_block_start).with('GeoLocation')
      expect(automate_funnel).to receive(:mark_block_end).with('GeoLocation', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('NetworkSimulation')
      expect(automate_funnel).to receive(:mark_block_end).with('NetworkSimulation', 'success')
      expect(automate_funnel).to receive(:mark_breakup_end)
      expect(automate_funnel).to receive(:mark_block_end)
      expect(automate_funnel).to receive(:generate_data_json)

      ret = DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)

      # TODO: have more robust assertions
      expect(ret).to_not have_key(:error)
      expect(ret).to have_key("platform_time_stats")
      expect(ret).to have_key(:port) # Port where Appium is running
      expect(ret).to have_key(:wda_port) # Port on which WDA is accessible on the machine (i.e the iProxy port)
    end

    it "should call all the required methods in the correct order while setting up a session which has update_app_settings cap that only has permission settings - default appium" do
      update_app_settings = {
        "Permission Settings" => {
          "Location" => {
            "ALLOW LOCATION ACCESS" => "Always",
            "Precise Location" => "ON"
          }
        }
      }.to_json

      parsed_just_permission_settings_dsl = {
        "Location" => {
          "type" => "PSChildPaneSpecifier",
          "value" => {
            "ALLOW LOCATION ACCESS" => {
              "type" => "PSMultiValueSpecifier",
              "value" => "Always"
            },
            "Precise Location" => {
              "type" => "PSToggleSwitchSpecifier",
              "value" => "ON"
            }
          }
        }
      }

      new_selenium_command_params = selenium_command_params.clone
      new_selenium_command_params['update_app_settings'] = update_app_settings
      new_selenium_command_params[:video] = "true"
      new_selenium_command_params[:appium] = "1.7.0" #default appium

      expect(Thread).to receive(:bs_run).once.ordered
      expect(AutomateFunnel).to receive(:new).and_return(automate_funnel)
      expect(automate_funnel).to receive(:mark_block_start).with('iOSFireCMD')
      expect(automate_funnel).to receive(:mark_breakup_start)
      expect(automate_funnel).to receive(:mark_block_start).with('DeviceSetup')
      expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
      expect(FileUtils).to receive(:touch).with("#{STATE_FILES_DIR}/session_start/#{device_id}").at_least(:once)
      expect(FileUtils).to receive(:touch).with("/var/log/browserstack/app_log_#{device_id}.log")
      expect(DeviceManager).to receive(:write_session_info).with(device_id, new_selenium_command_params).exactly(2).times

      expect(Thread).to receive(:bs_run).twice
      expect(AppleTVUtils).to receive(:apple_tv_device?).and_return(false).exactly(3).times
      expect(BrowserStack::IPhone).to receive(:unlock_device).once.ordered

      # Download App from S3
      app_downloaded_path = "/some/random/path"
      expect(DeviceManager).to receive(:download_and_install_app).and_return(app_download_url).ordered
      expect(BrowserStack::AppSettingsUtil).to receive(:validate).and_return(parsed_just_permission_settings_dsl)
      expect(BrowserStack::AppSettingsUtil).to receive(:update_settings).once.ordered

      expect(BrowserStack::OSUtils).to receive(:execute).with(/\[x\]codebuild/).and_return("true").once.ordered
      expect(automate_funnel).to receive(:mark_block_end).with('DeviceSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('LocalTunnelSetup')
      expect(automate_funnel).to receive(:mark_block_end).with('LocalTunnelSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('VideoRecording')
      expect_any_instance_of(VideoRecManager).to receive(:start_rec)
      expect(automate_funnel).to receive(:mark_block_end).with('VideoRecording', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('GeoLocation')
      expect(automate_funnel).to receive(:mark_block_end).with('GeoLocation', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('NetworkSimulation')
      expect(automate_funnel).to receive(:mark_block_end).with('NetworkSimulation', 'success')
      expect(automate_funnel).to receive(:mark_breakup_end)
      expect(automate_funnel).to receive(:mark_block_end)
      expect(automate_funnel).to receive(:generate_data_json)

      ret = DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)

      # TODO: have more robust assertions
      expect(ret).to_not have_key(:error)
      expect(ret).to have_key("platform_time_stats")
      expect(ret).to have_key(:port) # Port where Appium is running
      expect(ret).to have_key(:wda_port) # Port on which WDA is accessible on the machine (i.e the iProxy port)
    end

    it "should call all the required methods in the correct order while setting up a session which has update_app_settings cap - non default appium" do
      update_app_settings = {
        "Environment" => "PSP",
        "OTP Auto-Fill" => "ON",
        "Performance Charting Mock Data" => "ON",
        "Server config" => "random",
        "Child" => {
          "Child Banner" => "OFF",
          "Explore Environment" => "DEV"
        }
      }.to_json
      new_selenium_command_params = selenium_command_params.clone
      new_selenium_command_params['update_app_settings'] = update_app_settings
      new_selenium_command_params[:video] = "true"
      new_selenium_command_params[:appium] = "1.8.0" #default appium

      expect(Thread).to receive(:bs_run).once.ordered
      expect(AutomateFunnel).to receive(:new).and_return(automate_funnel)
      expect(automate_funnel).to receive(:mark_block_start).with('iOSFireCMD')
      expect(automate_funnel).to receive(:mark_breakup_start)
      expect(automate_funnel).to receive(:mark_block_start).with('DeviceSetup')
      expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
      expect(FileUtils).to receive(:touch).with("#{STATE_FILES_DIR}/session_start/#{device_id}").at_least(:once)
      expect(FileUtils).to receive(:touch).with("/var/log/browserstack/app_log_#{device_id}.log")
      expect(DeviceManager).to receive(:write_session_info).with(device_id, new_selenium_command_params).exactly(2).times

      expect(Thread).to receive(:bs_run).once.ordered
      expect(BrowserStack::IPhone).to receive(:unlock_device).once.ordered

      # Download App from S3
      app_downloaded_path = "/some/random/path"
      expect(DeviceManager).to receive(:download_and_install_app).and_return(app_download_url).ordered
      expect(BrowserStack::AppSettingsUtil).to receive(:validate).once.ordered
      expect(BrowserStack::AppSettingsUtil).to receive(:update_settings).once.ordered
      expect(Thread).to receive(:bs_run).twice

      expect(AppleTVUtils).to receive(:apple_tv_device?).and_return(false).exactly(3).times
      expect(BrowserStack::OSUtils).to receive(:execute).with(/\[x\]codebuild/).and_return("true").once.ordered
      expect(automate_funnel).to receive(:mark_block_end).with('DeviceSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('LocalTunnelSetup')
      expect(automate_funnel).to receive(:mark_block_end).with('LocalTunnelSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('VideoRecording')
      expect_any_instance_of(VideoRecManager).to receive(:start_rec)
      expect(automate_funnel).to receive(:mark_block_end).with('VideoRecording', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('GeoLocation')
      expect(automate_funnel).to receive(:mark_block_end).with('GeoLocation', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('NetworkSimulation')
      expect(automate_funnel).to receive(:mark_block_end).with('NetworkSimulation', 'success')
      expect(automate_funnel).to receive(:mark_breakup_end)
      expect(automate_funnel).to receive(:mark_block_end)
      expect(automate_funnel).to receive(:generate_data_json)

      ret = DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)

      # TODO: have more robust assertions
      expect(ret).to_not have_key(:error)
      expect(ret).to have_key("platform_time_stats")
      expect(ret).to have_key(:port) # Port where Appium is running
      expect(ret).to have_key(:wda_port) # Port on which WDA is accessible on the machine (i.e the iProxy port)
    end

    it "should call all the required methods in the correct order while setting up a session which has update_app_settings cap - non default appium for v2 video" do
      update_app_settings = {
        "Environment" => "PSP",
        "OTP Auto-Fill" => "ON",
        "Performance Charting Mock Data" => "ON",
        "Server config" => "random",
        "Child" => {
          "Child Banner" => "OFF",
          "Explore Environment" => "DEV"
        }
      }.to_json
      new_selenium_command_params = selenium_command_params.clone
      new_selenium_command_params['update_app_settings'] = update_app_settings
      new_selenium_command_params["video_params_v2"] = { "v2" => "true" }.to_json
      new_selenium_command_params[:video] = "true"
      new_selenium_command_params[:appium] = "1.8.0" #default appium

      expect(Thread).to receive(:bs_run).once.ordered
      expect(AutomateFunnel).to receive(:new).and_return(automate_funnel)
      expect(automate_funnel).to receive(:mark_block_start).with('iOSFireCMD')
      expect(automate_funnel).to receive(:mark_breakup_start)
      expect(automate_funnel).to receive(:mark_block_start).with('DeviceSetup')
      expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
      expect(FileUtils).to receive(:touch).with("#{STATE_FILES_DIR}/session_start/#{device_id}").at_least(:once)
      expect(FileUtils).to receive(:touch).with("/var/log/browserstack/app_log_#{device_id}.log")
      expect(DeviceManager).to receive(:write_session_info).with(device_id, new_selenium_command_params).exactly(2).times

      expect(Thread).to receive(:bs_run).once.ordered
      expect(BrowserStack::IPhone).to receive(:unlock_device).once.ordered

      # Download App from S3
      app_downloaded_path = "/some/random/path"
      expect(DeviceManager).to receive(:download_and_install_app).and_return(app_download_url).ordered
      expect(BrowserStack::AppSettingsUtil).to receive(:validate).once.ordered
      expect(BrowserStack::AppSettingsUtil).to receive(:update_settings).once.ordered
      expect(Thread).to receive(:bs_run).twice

      expect(AppleTVUtils).to receive(:apple_tv_device?).and_return(false).twice
      expect(BrowserStack::OSUtils).to receive(:execute).with(/\[x\]codebuild/).and_return("true").once.ordered
      expect(automate_funnel).to receive(:mark_block_end).with('DeviceSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('LocalTunnelSetup')
      expect(automate_funnel).to receive(:mark_block_end).with('LocalTunnelSetup', 'success')
      expect(DeviceManager).to receive(:start_v2_video_recording)
      expect(automate_funnel).to receive(:mark_block_start).with('GeoLocation')
      expect(automate_funnel).to receive(:mark_block_end).with('GeoLocation', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('NetworkSimulation')
      expect(automate_funnel).to receive(:mark_block_end).with('NetworkSimulation', 'success')
      expect(automate_funnel).to receive(:mark_breakup_end)
      expect(automate_funnel).to receive(:mark_block_end)
      expect(automate_funnel).to receive(:generate_data_json)

      ret = DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)

      # TODO: have more robust assertions
      expect(ret).to_not have_key(:error)
      expect(ret).to have_key("platform_time_stats")
      expect(ret).to have_key(:port) # Port where Appium is running
      expect(ret).to have_key(:wda_port) # Port on which WDA is accessible on the machine (i.e the iProxy port)
    end

    it "should call all the required methods in the correct order while setting up a session which has update_app_settings cap - mdm flow" do
      update_app_settings = {
        "Environment" => "PSP",
        "OTP Auto-Fill" => "ON",
        "Performance Charting Mock Data" => "ON",
        "Server config" => "random",
        "Child" => {
          "Child Banner" => "OFF",
          "Explore Environment" => "DEV"
        }
      }.to_json
      new_selenium_command_params = selenium_command_params.clone
      new_selenium_command_params['update_app_settings'] = update_app_settings
      new_selenium_command_params["video_params_v2"] = { "v2" => "true" }.to_json
      new_selenium_command_params["mdm_enterprise_app_install"] = "true"
      new_selenium_command_params[:video] = "true"
      new_selenium_command_params[:appium] = "1.8.0" #default appium

      expect(Thread).to receive(:bs_run).once.ordered
      expect(AutomateFunnel).to receive(:new).and_return(automate_funnel)
      expect(automate_funnel).to receive(:mark_block_start).with('iOSFireCMD')
      expect(automate_funnel).to receive(:mark_breakup_start)
      expect(automate_funnel).to receive(:mark_block_start).with('DeviceSetup')
      expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
      expect(FileUtils).to receive(:touch).with("#{STATE_FILES_DIR}/session_start/#{device_id}").at_least(:once)
      expect(FileUtils).to receive(:touch).with("/var/log/browserstack/app_log_#{device_id}.log")
      expect(DeviceManager).to receive(:write_session_info).with(device_id, new_selenium_command_params).exactly(1).times

      expect(Thread).to receive(:bs_run).once.ordered
      expect(BrowserStack::IPhone).to receive(:unlock_device).once.ordered

      # Download App from S3
      app_downloaded_path = "/some/random/path"
      expect(DeviceManager).to receive(:perform_mdm_enterprise_app_install)
      expect(DeviceManager).to receive(:download_and_install_app).and_return(app_download_url).ordered
      expect(BrowserStack::AppSettingsUtil).to receive(:validate).once.ordered
      expect(BrowserStack::AppSettingsUtil).to receive(:update_settings).once.ordered
      expect(Thread).to receive(:bs_run).twice

      expect(AppleTVUtils).to receive(:apple_tv_device?).and_return(false).twice
      expect(BrowserStack::OSUtils).to receive(:execute).with(/\[x\]codebuild/).and_return("true").once.ordered
      expect(automate_funnel).to receive(:mark_block_end).with('DeviceSetup', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('LocalTunnelSetup')
      expect(automate_funnel).to receive(:mark_block_end).with('LocalTunnelSetup', 'success')
      expect(DeviceManager).to receive(:start_v2_video_recording)
      expect(automate_funnel).to receive(:mark_block_start).with('GeoLocation')
      expect(automate_funnel).to receive(:mark_block_end).with('GeoLocation', 'success')
      expect(automate_funnel).to receive(:mark_block_start).with('NetworkSimulation')
      expect(automate_funnel).to receive(:mark_block_end).with('NetworkSimulation', 'success')
      expect(automate_funnel).to receive(:mark_breakup_end)
      expect(automate_funnel).to receive(:mark_block_end)
      expect(automate_funnel).to receive(:generate_data_json)
      expect(DeviceManager).to receive(:validate_mdm_app_install)
      # expect(DeviceManager).to receive(:update_app_settings)

      ret = DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)
      # TODO: have more robust assertions
      expect(ret).to_not have_key(:error)
      expect(ret).to have_key("platform_time_stats")
      expect(ret).to have_key(:port) # Port where Appium is running
      expect(ret).to have_key(:wda_port) # Port on which WDA is accessible on the machine (i.e the iProxy port)
    end

    context "FireCMD Errors" do
      it "should raise AppDownloadFailedException" do
        new_selenium_command_params = selenium_command_params.clone
        expect(Thread).to receive(:new).and_return(mock_thread).once.ordered
        allow(DeviceManager).to receive(:get_folder_path_details_from_s3_url).and_return(["app_identifier", "cert_name"])
        expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
        expect(DeviceManager).to receive(:write_session_info).with(device_id, new_selenium_command_params).exactly(1).times
        expect(Thread).to receive(:new).and_return(mock_thread).once.ordered
        expect(FileUtils).to receive(:touch).with(/.starting/).once.ordered
        expect(BrowserStack::HttpUtils).to receive(:download).and_raise("Fatal error")

        ret = DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)

        expect(ret).to have_key(:error)
        expect(ret).to have_key(:type)
        expect(ret).to have_key(:kind)
        expect(ret[:error]).to eql("App App Download Failed")
        expect(ret[:kind]).to eql("app_download_install_failure")
        expect(ret[:type]).to eql("browserstack_error")
      end

      it "should raise AppDownloadFailedException twice if app_automate and caching proxy enabled" do
        new_selenium_command_params = selenium_command_params.clone
        new_selenium_command_params["app_automate_custom_params"] = "{\"subregion_app_caching_proxy_enabled\": true}"
        expect(Thread).to receive(:new).and_return(mock_thread).once.ordered
        allow(DeviceManager).to receive(:get_folder_path_details_from_s3_url).and_return(["app_identifier", "cert_name"])
        allow_any_instance_of(IosInfluxdbClient).to receive(:event).and_return(nil)
        expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
        expect(DeviceManager).to receive(:write_session_info).with(device_id, new_selenium_command_params).exactly(1).times
        expect(Thread).to receive(:new).and_return(mock_thread).once.ordered
        expect(FileUtils).to receive(:touch).with(/.starting/).once.ordered
        expect(BrowserStack::HttpUtils).to receive(:download).and_raise("Fatal error").twice

        ret = DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)

        expect(ret).to have_key(:error)
        expect(ret).to have_key(:type)
        expect(ret).to have_key(:kind)
        expect(ret[:error]).to eql("App App Download Failed")
        expect(ret[:kind]).to eql("app_download_install_failure")
        expect(ret[:type]).to eql("browserstack_error")
      end

      it "should raise AppInstallException" do
        new_selenium_command_params = selenium_command_params.clone
        expect(Thread).to receive(:new).and_return(mock_thread).once.ordered
        allow(DeviceManager).to receive(:get_folder_path_details_from_s3_url).and_return(["app_identifier", "cert_name"])
        expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
        expect(DeviceManager).to receive(:write_session_info).with(device_id, new_selenium_command_params).exactly(1).times
        expect(Thread).to receive(:new).and_return(mock_thread).once.ordered
        expect(FileUtils).to receive(:touch).with(/.starting/).once.ordered

        expect(BrowserStack::HttpUtils).to receive(:download).and_return(app_download_url).ordered
        expect(BrowserStack::OSUtils).to receive(:unarchive).and_raise("Fatal Error")
        ret = DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)

        expect(ret).to have_key(:error)
        expect(ret).to have_key(:type)
        expect(ret).to have_key(:kind)
        expect(ret[:error]).to eql("Fatal Error")
        expect(ret[:kind]).to eql("app_download_install_failure")
        expect(ret[:type]).to eql("browserstack_error")
      end

      it "should raise OthersAppsInstallException" do
        new_selenium_command_params = selenium_command_params.clone
        new_selenium_command_params['other_apps'] = ["bs://abcdfegh"].to_s
        expect(Thread).to receive(:new).and_return(mock_thread).once.ordered
        expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
        expect(DeviceManager).to receive(:write_session_info).with(device_id, new_selenium_command_params).exactly(2).times
        expect(Thread).to receive(:new).and_return(mock_thread).once.ordered

        expect(DeviceManager).to receive(:download_and_install_app).and_return(app_download_url).once.ordered
        expect(DeviceManager).to receive(:download_and_install_app).and_raise("Fatal Error")

        ret = DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)

        expect(ret).to have_key(:error)
        expect(ret).to have_key(:type)
        expect(ret).to have_key(:kind)
        expect(ret[:error]).to eql("Fatal Error")
        expect(ret[:kind]).to eql("other_apps_install_failure")
        expect(ret[:type]).to eql("browserstack_error")
      end

      it "should raise MidSessionAppsInstallException" do
        new_selenium_command_params = selenium_command_params.clone
        new_selenium_command_params['mid_session_install_apps'] = ["bs://abcdfegh"].to_s
        expect(Thread).to receive(:new).and_return(mock_thread).once.ordered
        expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
        expect(DeviceManager).to receive(:write_session_info).with(device_id, new_selenium_command_params).exactly(2).times
        expect(Thread).to receive(:new).and_return(mock_thread).once.ordered

        expect(DeviceManager).to receive(:download_and_install_app).and_return(app_download_url).once.ordered
        expect(DeviceManager).to receive(:download_and_install_app).and_raise("Fatal Error")

        ret = DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)

        expect(ret).to have_key(:error)
        expect(ret).to have_key(:type)
        expect(ret).to have_key(:kind)
        expect(ret[:error]).to eql("Fatal Error")
        expect(ret[:kind]).to eql("mid_session_apps_download_failure")
        expect(ret[:type]).to eql("browserstack_error")
      end

      it "should raise CustomMediaException" do
        new_selenium_command_params = selenium_command_params.clone
        new_selenium_command_params["custom_media"] = [{ 'filename': "media.filename", 'filetype': "media.filetype", 's3_url': "media_url" }.to_json].to_s
        new_selenium_command_params["s3_app_url"] = nil
        expect(Thread).to receive(:new).and_return(mock_thread).once.ordered
        expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
        expect(DeviceManager).to receive(:write_session_info).with(device_id, new_selenium_command_params).exactly(1).times
        expect(Thread).to receive(:new).and_return(mock_thread).once.ordered
        expect(IdeviceUtils).to receive(:device_version).and_return(Gem::Version.new(18.0))
        expect_any_instance_of(DeviceState).to receive(:touch_custom_media_cleanup_file)

        expect(BrowserStack::HttpUtils).to receive(:download).and_raise("Fatal Error")

        ret = DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)
        expect(ret).to have_key(:error)
        expect(ret).to have_key(:type)
        expect(ret).to have_key(:kind)
        expect(ret[:error]).to eql("Media Download Failed")
        expect(ret[:kind]).to eql("custom_media_download_update_failed")
        expect(ret[:type]).to eql("browserstack_error")
      end

      it "should raise FireCMDException if app settings validation fails" do
        update_app_settings = {
          "Environment" => "PSP",
          "OTP Auto-Fill" => "ON",
          "Performance Charting Mock Data" => "ON",
          "Server config" => "random",
          "Child" => {
            "Child Banner" => "OFF",
            "Explore Environment" => "DEV"
          }
        }.to_json
        new_selenium_command_params = selenium_command_params.clone
        new_selenium_command_params['update_app_settings'] = update_app_settings
        expect(Thread).to receive(:new).and_return(mock_thread).once.ordered
        expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
        expect(DeviceManager).to receive(:write_session_info).with(device_id, new_selenium_command_params).exactly(2).times
        expect(Thread).to receive(:new).and_return(mock_thread).once.ordered

        expect(DeviceManager).to receive(:download_and_install_app).and_return(app_download_url).once.ordered
        expect(BrowserStack::AppSettingsUtil).to receive(:validate).and_raise(AppSettingsError)

        expect { DeviceManager.fire_cmd_start(device_id, new_selenium_command_params) }.to raise_error(FireCMDException)
      end

      it "should raise FireCMDException if app settings automation fails" do
        update_app_settings = {
          "Environment" => "PSP",
          "OTP Auto-Fill" => "ON",
          "Performance Charting Mock Data" => "ON",
          "Server config" => "random",
          "Child" => {
            "Child Banner" => "OFF",
            "Explore Environment" => "DEV"
          }
        }.to_json
        new_selenium_command_params = selenium_command_params.clone
        new_selenium_command_params['update_app_settings'] = update_app_settings
        expect(Thread).to receive(:new).and_return(mock_thread).once.ordered
        expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
        expect(DeviceManager).to receive(:write_session_info).with(device_id, new_selenium_command_params).exactly(2).times
        expect(Thread).to receive(:new).and_return(mock_thread).once.ordered

        expect(DeviceManager).to receive(:download_and_install_app).and_return(app_download_url).once.ordered
        expect(BrowserStack::AppSettingsUtil).to receive(:validate)
        expect(BrowserStack::AppSettingsUtil).to receive(:update_settings).and_raise(AppSettingsError)
        expect { DeviceManager.fire_cmd_start(device_id, new_selenium_command_params) }.to raise_error(FireCMDException)
      end
    end

    context("TestFlight") do
      let(:app_download_url) do
        "https://testflight.apple.com/join/23kjsdf"
      end

      let(:automate_funnel) { double :automate_funnel }

      it "should launch TestFlight Automation to install the app" do
        selenium_command_params["webrtc_session_id"] = "session-1234"
        selenium_command_params[:video] = "true"
        expect(Thread).to receive(:new).and_return(mock_thread).once.ordered
        expect(AutomateFunnel).to receive(:new).and_return(automate_funnel)
        expect(automate_funnel).to receive(:mark_block_start).with('iOSFireCMD')
        expect(automate_funnel).to receive(:mark_breakup_start)
        expect(automate_funnel).to receive(:mark_block_start).with('iosInteractiveSession')
        expect(automate_funnel).to receive(:mark_block_end).with('iosInteractiveSession')
        expect(automate_funnel).to receive(:mark_block_start).with('DeviceSetup')
        expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
        expect(FileUtils).to receive(:touch).with("#{STATE_FILES_DIR}/session_start/#{device_id}").at_least(:once)
        expect(FileUtils).to receive(:touch).with("/var/log/browserstack/app_log_#{device_id}.log")
        expect(DeviceManager).to receive(:write_session_info).with(device_id, selenium_command_params).once.ordered
        expect(Thread).to receive(:new).and_return(mock_thread).twice

        # Device should be unlocked before running TestFlight automation. This is required for iOS 10 specifically.
        expect(BrowserStack::IPhone).to receive(:unlock_device).once.ordered

        # Download App from S3
        app_downloaded_path = "/some/random/path"
        expect(Automation::TestFlight).to receive(:new).with(kind_of(String), kind_of(String)).and_return(MockTestFlightAutomator.new).ordered
        expect_any_instance_of(MockTestFlightAutomator).to receive(:install_app_via_public_link).with(app_download_url)
        expect(FileUtils).to receive(:touch).with(/testflight_in_app_automate_opened/).once.ordered

        expect(AppleTVUtils).to receive(:apple_tv_device?).and_return(false).exactly(4).times
        expect(BrowserStack::OSUtils).to receive(:execute).with(/\[x\]codebuild/).and_return("true").once.ordered
        expect(automate_funnel).to receive(:mark_block_end).with('DeviceSetup', 'success')
        expect(automate_funnel).to receive(:mark_block_start).with('LocalTunnelSetup')
        expect(automate_funnel).to receive(:mark_block_end).with('LocalTunnelSetup', 'success')
        expect(automate_funnel).to receive(:mark_block_start).with('VideoRecording')
        expect_any_instance_of(VideoRecManager).to receive(:start_rec)
        expect(automate_funnel).to receive(:mark_block_end).with('VideoRecording', 'success')
        expect(automate_funnel).to receive(:mark_block_start).with('GeoLocation')
        expect(automate_funnel).to receive(:mark_block_end).with('GeoLocation', 'success')
        expect(automate_funnel).to receive(:mark_block_start).with('NetworkSimulation')
        expect(automate_funnel).to receive(:mark_block_end).with('NetworkSimulation', 'success')
        expect(automate_funnel).to receive(:mark_breakup_end)
        expect(automate_funnel).to receive(:mark_block_end)
        expect(automate_funnel).to receive(:generate_data_json)

        ret = DeviceManager.fire_cmd_start(device_id, selenium_command_params)

        # TODO: have more robust assertions
        expect(ret).to_not have_key(:error)
        expect(ret).to have_key("platform_time_stats")
        expect(ret).to have_key(:port) # Port where Appium is running
        expect(ret).to have_key(:wda_port) # Port on which WDA is accessible on the machine (i.e the iProxy port)
      end
    end

    context("Timezone") do
      let(:automate_funnel) { double :automate_funnel }

      it "should set timezone when param is not nil in caps" do
        expect(Thread).to receive(:new).and_return(mock_thread).once.ordered
        expect(AutomateFunnel).to receive(:new).and_return(automate_funnel)
        expect(automate_funnel).to receive(:mark_block_start).with('iOSFireCMD')
        expect(automate_funnel).to receive(:mark_breakup_start)
        expect(automate_funnel).to receive(:mark_block_start).with('DeviceSetup')

        new_selenium_command_params = selenium_command_params.clone
        new_selenium_command_params['timezone'] = "New York"
        new_selenium_command_params[:video] = "true"

        expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
        expect(FileUtils).to receive(:touch).with("#{STATE_FILES_DIR}/session_start/#{device_id}").at_least(:once)
        expect(FileUtils).to receive(:touch).with("/var/log/browserstack/app_log_#{device_id}.log")
        expect(DeviceManager).to receive(:write_session_info).with(device_id, new_selenium_command_params).exactly(2).times
        expect(Thread).to receive(:new).and_return(mock_thread).twice
        expect(BrowserStack::IPhone).to receive(:unlock_device).once.ordered
        expect(BrowserStack::IPhone).to receive(:change_time_zone)

        expect(DeviceManager).to receive(:download_and_install_app).and_return(app_download_url).ordered
        expect(AppleTVUtils).to receive(:apple_tv_device?).and_return(false).exactly(3).times
        expect(BrowserStack::OSUtils).to receive(:execute).with(/\[x\]codebuild/).and_return("true").once.ordered
        expect(automate_funnel).to receive(:mark_block_end).with('DeviceSetup', 'success')
        expect(automate_funnel).to receive(:mark_block_start).with('LocalTunnelSetup')
        expect(automate_funnel).to receive(:mark_block_end).with('LocalTunnelSetup', 'success')
        expect(automate_funnel).to receive(:mark_block_start).with('VideoRecording')
        expect_any_instance_of(VideoRecManager).to receive(:start_rec)
        expect(automate_funnel).to receive(:mark_block_end).with('VideoRecording', 'success')
        expect(automate_funnel).to receive(:mark_block_start).with('GeoLocation')
        expect(automate_funnel).to receive(:mark_block_end).with('GeoLocation', 'success')
        expect(automate_funnel).to receive(:mark_block_start).with('NetworkSimulation')
        expect(automate_funnel).to receive(:mark_block_end).with('NetworkSimulation', 'success')
        expect(automate_funnel).to receive(:mark_breakup_end)
        expect(automate_funnel).to receive(:mark_block_end)
        expect(automate_funnel).to receive(:generate_data_json)

        ret = DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)
        expect(ret).to_not have_key(:error)
        expect(ret).to have_key("platform_time_stats")
        expect(ret).to have_key(:port) # Port where Appium is running
        expect(ret).to have_key(:wda_port) # Port on which WDA is accessible on the machine (i.e the iProxy port)
      end

      it "should raise error when timezone automation failed with known exception" do
        expect(Thread).to receive(:new).and_return(mock_thread).once.ordered
        expect(AutomateFunnel).to receive(:new).and_return(automate_funnel)
        expect(automate_funnel).to receive(:mark_block_start).with('iOSFireCMD')
        expect(automate_funnel).to receive(:mark_breakup_start)
        expect(automate_funnel).to receive(:mark_block_start).with('DeviceSetup')

        new_selenium_command_params = selenium_command_params.clone
        new_selenium_command_params['timezone'] = "New York"

        expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
        expect(DeviceManager).to receive(:write_session_info).with(device_id, new_selenium_command_params).exactly(1).times
        expect(Thread).to receive(:new).and_return(mock_thread).once.ordered
        expect(BrowserStack::IPhone).to receive(:unlock_device).once.ordered
        expect(BrowserStack::IPhone).to receive(:change_time_zone).and_raise(MultipleTimezonesException)

        expect { DeviceManager.send(:fire_cmd_start, device_id, new_selenium_command_params) }.to raise_error(FireCMDException)
      end

      it "should raise error when timezone automation failed with unknown exception" do
        expect(Thread).to receive(:new).and_return(mock_thread).once.ordered
        expect(AutomateFunnel).to receive(:new).and_return(automate_funnel)
        expect(automate_funnel).to receive(:mark_block_start).with('iOSFireCMD')
        expect(automate_funnel).to receive(:mark_breakup_start)
        expect(automate_funnel).to receive(:mark_block_start).with('DeviceSetup')

        new_selenium_command_params = selenium_command_params.clone
        new_selenium_command_params['timezone'] = "New York"

        expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
        expect(DeviceManager).to receive(:write_session_info).with(device_id, new_selenium_command_params).exactly(1).times
        expect(Thread).to receive(:new).and_return(mock_thread).once.ordered
        expect(BrowserStack::IPhone).to receive(:unlock_device).once.ordered
        expect(BrowserStack::IPhone).to receive(:change_time_zone).and_raise(StandardError)

        expect { DeviceManager.send(:fire_cmd_start, device_id, new_selenium_command_params) }.to raise_error(FireCMDException)
      end
    end

    context("Update Ios Device Settings") do
      it "Appearance changes when params are passed true" do
        new_selenium_command_params = selenium_command_params.clone
        new_selenium_command_params[:feature_usage] = {}
        new_selenium_command_params[:feature_usage]["updateIosDeviceSettings"] = []
        new_selenium_command_params["updateIosDeviceSettings"] = "{\"darkMode\"=>\"true\"}"
        mockswitchmode = double('mockswitchmode')
        expect(Utils).to receive(:mark_event_start).with('fire_cmd.update_ios_device_settings_time', new_selenium_command_params[:event_hash])
        expect(BrowserStack::SwitchMode).to receive(:new).with(device_id, new_selenium_command_params['automate_session_id']).and_return(mockswitchmode)
        expect(mockswitchmode).to receive(:change_appearance_to).with("Dark").and_return("true")

        DeviceManager.update_device_settings(device_id, new_selenium_command_params)
      end

      it "Appearance changes when params are passed false" do
        new_selenium_command_params = selenium_command_params.clone
        new_selenium_command_params[:feature_usage] = {}
        new_selenium_command_params[:feature_usage]["updateIosDeviceSettings"] = []
        new_selenium_command_params["updateIosDeviceSettings"] = "{\"darkMode\"=>\"false\"}"
        mockswitchmode = double('mockswitchmode')
        expect(Utils).to receive(:mark_event_start).with('fire_cmd.update_ios_device_settings_time', new_selenium_command_params[:event_hash])
        expect(BrowserStack::SwitchMode).to_not receive(:new)
        expect(mockswitchmode).to_not receive(:change_appearance_to)

        DeviceManager.update_device_settings(device_id, new_selenium_command_params)
      end
    end

    context("AirPlane Mode") do
      let(:automate_funnel) { double :automate_funnel }

      it "should set airplane mode when param is true in caps" do
        expect(Thread).to receive(:new).and_return(mock_thread).once.ordered
        expect(AutomateFunnel).to receive(:new).and_return(automate_funnel)
        expect(automate_funnel).to receive(:mark_block_start).with('iOSFireCMD')
        expect(automate_funnel).to receive(:mark_breakup_start)
        expect(automate_funnel).to receive(:mark_block_start).with('iosInteractiveSession')
        expect(automate_funnel).to receive(:mark_block_end).with('iosInteractiveSession')
        expect(automate_funnel).to receive(:mark_block_start).with('DeviceSetup')
        new_selenium_command_params = selenium_command_params.clone
        new_selenium_command_params['network_airplane_mode'] = true
        new_selenium_command_params["webrtc_session_id"] = "session-1234"
        new_selenium_command_params[:video] = "true"
        expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
        expect(FileUtils).to receive(:touch).with("#{STATE_FILES_DIR}/session_start/#{device_id}").at_least(:once)
        expect(FileUtils).to receive(:touch).with("/var/log/browserstack/app_log_#{device_id}.log")
        expect(DeviceManager).to receive(:write_session_info).with(device_id, new_selenium_command_params).exactly(2).times
        expect(Thread).to receive(:new).and_return(mock_thread).twice
        expect(BrowserStack::IPhone).to receive(:unlock_device).once.ordered

        # Download App from S3
        app_downloaded_path = "/some/random/path"

        expect(Automation::AirplaneModeAutomation).to receive(:new).with(kind_of(String)).and_return(MockAiplaneModeAutomator.new).ordered
        expect_any_instance_of(MockAiplaneModeAutomator).to receive(:toggle_airplane_mode)
        expect(IdeviceUtils).to receive(:airplane_mode_on?).with(device_id).and_return(true).exactly(2).times

        expect(DeviceManager).to receive(:download_and_install_app).and_return(app_download_url).ordered
        expect(AppleTVUtils).to receive(:apple_tv_device?).and_return(false).exactly(4).times
        expect(BrowserStack::OSUtils).to receive(:execute).with(/\[x\]codebuild/).and_return("true").once.ordered
        expect(automate_funnel).to receive(:mark_block_end).with('DeviceSetup', 'success')
        expect(automate_funnel).to receive(:mark_block_start).with('LocalTunnelSetup')
        expect(automate_funnel).to receive(:mark_block_end).with('LocalTunnelSetup', 'success')
        expect(automate_funnel).to receive(:mark_block_start).with('VideoRecording')
        expect_any_instance_of(VideoRecManager).to receive(:start_rec)
        expect(automate_funnel).to receive(:mark_block_end).with('VideoRecording', 'success')
        expect(automate_funnel).to receive(:mark_block_start).with('GeoLocation')
        expect(automate_funnel).to receive(:mark_block_end).with('GeoLocation', 'success')
        expect(automate_funnel).to receive(:mark_block_start).with('NetworkSimulation')
        expect(automate_funnel).to receive(:mark_block_end).with('NetworkSimulation', 'success')
        expect(automate_funnel).to receive(:mark_breakup_end)
        expect(automate_funnel).to receive(:mark_block_end)
        expect(automate_funnel).to receive(:generate_data_json)

        ret = DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)
        expect(ret).to_not have_key(:error)
        expect(ret).to have_key("platform_time_stats")
        expect(ret).to have_key(:port) # Port where Appium is running
        expect(ret).to have_key(:wda_port) # Port on which WDA is accessible on the machine (i.e the iProxy port)
      end

      it "should raise start error if airplane is not enabled in 2 tries when param is true in caps" do
        new_selenium_command_params = selenium_command_params.clone
        new_selenium_command_params['network_airplane_mode'] = true
        expect(Thread).to receive(:new).and_return(mock_thread).once.ordered
        expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
        expect(DeviceManager).to receive(:write_session_info).with(device_id, new_selenium_command_params).exactly(1).times
        expect(Thread).to receive(:new).and_return(mock_thread).once.ordered
        expect(AppleTVUtils).to receive(:apple_tv_device?).and_return(false)
        expect(BrowserStack::IPhone).to receive(:unlock_device).once.ordered

        # Download App from S3
        app_downloaded_path = "/some/random/path"

        expect(Automation::AirplaneModeAutomation).to receive(:new).with(kind_of(String)).and_return(MockAiplaneModeAutomator.new).exactly(2).times
        expect_any_instance_of(MockAiplaneModeAutomator).to receive(:toggle_airplane_mode).exactly(2).times
        expect(IdeviceUtils).to receive(:airplane_mode_on?).with(device_id).and_return(false).exactly(3).times

        expect(DeviceManager).to_not receive(:download_and_install_app)
        expect(BrowserStack::OSUtils).to_not receive(:execute).with(/\[x\]codebuild/)

        ret = DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)
        expect(ret).to have_key(:error)
        expect(ret[:error]).to eql("ERROR: Airplane mode not enabled for device #{device_id}")
        expect(ret).to_not have_key("platform_time_stats")
        expect(ret).to have_key(:type)
        expect(ret).to have_key(:kind)
        expect(ret[:type]).to eql("browserstack_error")
        expect(ret[:kind]).to eql("airplane_mode_set_failed")
        expect(ret).to_not have_key(:port) # Port where Appium is running
        expect(ret).to_not have_key(:wda_port) # Port on which WDA is accessible on the machine (i.e the iProxy port)
      end

      it "should set airplane mode in 1 retry when param is true in caps" do
        expect(Thread).to receive(:new).and_return(mock_thread).once.ordered
        expect(AutomateFunnel).to receive(:new).and_return(automate_funnel)
        expect(automate_funnel).to receive(:mark_block_start).with('iOSFireCMD')
        expect(automate_funnel).to receive(:mark_breakup_start)
        expect(automate_funnel).to receive(:mark_block_start).with('iosInteractiveSession')
        expect(automate_funnel).to receive(:mark_block_end).with('iosInteractiveSession')
        expect(automate_funnel).to receive(:mark_block_start).with('DeviceSetup')
        new_selenium_command_params = selenium_command_params.clone
        new_selenium_command_params['network_airplane_mode'] = true
        new_selenium_command_params["webrtc_session_id"] = "session-1234"
        new_selenium_command_params[:video] = "true"
        expect(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
        expect(FileUtils).to receive(:touch).with("#{STATE_FILES_DIR}/session_start/#{device_id}").at_least(:once)
        expect(FileUtils).to receive(:touch).with("/var/log/browserstack/app_log_#{device_id}.log")
        expect(DeviceManager).to receive(:write_session_info).with(device_id, new_selenium_command_params).exactly(2).times
        expect(Thread).to receive(:new).and_return(mock_thread).twice
        expect(BrowserStack::IPhone).to receive(:unlock_device).once.ordered

        # Download App from S3
        app_downloaded_path = "/some/random/path"

        expect(Automation::AirplaneModeAutomation).to receive(:new).with(kind_of(String)).and_return(MockAiplaneModeAutomator.new).exactly(2).times
        expect_any_instance_of(MockAiplaneModeAutomator).to receive(:toggle_airplane_mode).exactly(2).times
        expect(IdeviceUtils).to receive(:airplane_mode_on?).with(device_id).and_return(false).once.ordered
        expect(IdeviceUtils).to receive(:airplane_mode_on?).with(device_id).and_return(true).exactly(2).times

        expect(DeviceManager).to receive(:download_and_install_app).and_return(app_download_url).ordered
        expect(AppleTVUtils).to receive(:apple_tv_device?).and_return(false).exactly(4).times
        expect(BrowserStack::OSUtils).to receive(:execute).with(/\[x\]codebuild/).and_return("true").once.ordered
        expect(automate_funnel).to receive(:mark_block_end).with('DeviceSetup', 'success')
        expect(automate_funnel).to receive(:mark_block_start).with('LocalTunnelSetup')
        expect(automate_funnel).to receive(:mark_block_end).with('LocalTunnelSetup', 'success')
        expect(automate_funnel).to receive(:mark_block_start).with('VideoRecording')
        expect_any_instance_of(VideoRecManager).to receive(:start_rec)
        expect(automate_funnel).to receive(:mark_block_end).with('VideoRecording', 'success')
        expect(automate_funnel).to receive(:mark_block_start).with('GeoLocation')
        expect(automate_funnel).to receive(:mark_block_end).with('GeoLocation', 'success')
        expect(automate_funnel).to receive(:mark_block_start).with('NetworkSimulation')
        expect(automate_funnel).to receive(:mark_block_end).with('NetworkSimulation', 'success')
        expect(automate_funnel).to receive(:mark_breakup_end)
        expect(automate_funnel).to receive(:mark_block_end)
        expect(automate_funnel).to receive(:generate_data_json)

        ret = DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)
        expect(ret).to_not have_key(:error)
        expect(ret).to have_key("platform_time_stats")
        expect(ret).to have_key(:port) # Port where Appium is running
        expect(ret).to have_key(:wda_port) # Port on which WDA is accessible on the machine (i.e the iProxy port)
      end
    end

    context "download_and_install_app" do
      let(:device_manager) { DeviceManager }
      let(:app_details) { { url: app_download_url, id: "com.browserstack.rspecapp", timeout: 50 } }
      let(:app_identifier) { "app_identififer" }
      let(:cert_name) { "production_34_14452020" }
      let(:download_folder) { "/tmp/apps/#{app_identifier}_#{cert_name}" }
      let(:extract_path) { "#{download_folder}/uuid" }
      let(:device) { "device_name" }

      context 'when preInstallApp is set' do
        it 'should skip app download and install' do
          new_selenium_command_params = selenium_command_params.clone
          new_selenium_command_params["app_automate_custom_params"] = "{\"preInstallApp\": true}"
          allow(FileUtils).to receive(:touch).with(/session_start_indicator/).once.ordered
          allow(FileUtils).to receive(:touch).with("#{STATE_FILES_DIR}/session_start/#{device_id}").at_least(:once)
          allow(FileUtils).to receive(:touch).with("/var/log/browserstack/app_log_#{device_id}.log")
          expect(DeviceManager).not_to receive(:download_and_install_app)
          ret = DeviceManager.fire_cmd_start(device_id, new_selenium_command_params)
          expect(ret).not_to have_key(:error)
        end
      end

      context "if app_identifier and cert is not nil" do
        before(:each) do
          selenium_command_params[:app_type] = "main"
          allow(DeviceManager).to receive(:get_backfill_obj).and_return(nil)
          allow(DeviceManager).to receive(:get_folder_path_details_from_s3_url).and_return([app_identifier, cert_name])
          allow(DeviceManager).to receive(:device_configuration_check).and_return(nil)
          device_manager.configure BrowserStack::Configuration.new.all
        end

        def allow_download
          expect(FileUtils).to receive(:mkdir_p).with(download_folder).and_return(true)
          allow(SecureRandom).to receive(:uuid).and_return("uuid")
          expect(FileUtils).to receive(:touch).with("#{download_folder}/uuid.starting").and_return(true)
          expect(BrowserStack::HttpUtils).to receive(:download).with(
            anything, anything, hash_excluding(:header, :dump_headers_to_file, :is_app_automate)
          ).and_return(true)
        end

        def allow_download_via_proxy
          expect(FileUtils).to receive(:mkdir_p).with(download_folder).and_return(true)
          allow(SecureRandom).to receive(:uuid).and_return("uuid")
          expect(FileUtils).to receive(:touch).with("#{download_folder}/uuid.starting").and_return(true)
          expect(BrowserStack::HttpUtils).to receive(:download).with(
            anything, anything, hash_including(header: anything, dump_headers_to_file: anything, is_app_automate: true)
          ).and_return(true)
        end

        def mobileprovisioning(regex = "Payload/*.app")
          allow(Dir).to receive(:glob).with("#{extract_path}/#{regex}").and_return("app_mobileprovision_path")
          expect(File).to receive(:read).with(/ppuid_#{device}/).and_return("ppuid1 ppuid2 ppuid3")
          allow(app_download_url).to receive(:match).with(/_ppuid1/).and_return(true)
        end

        def install_app
          expect(FileUtils).to receive(:touch).with(/.complete$/).and_return(true)
          expect(FileUtils).to receive(:touch).with(/.session$/).and_return(true)
          expect(DeviceManager).to receive(:install_app_and_verify).and_return(true)
        end

        def simple_download_flow
          allow_download
          expect(BrowserStack::OSUtils).to receive(:unarchive).and_return(0)
          mobileprovisioning
          allow(Utils).to receive(:is_md5_checksum_equal?).and_return(true)
          expect(FileUtils).to receive(:rm).with(/.ipa/).and_return(true)
          install_app
        end

        def simple_download_flow_via_proxy
          allow_download_via_proxy
          expect(File).to receive(:file?).and_return(true).twice
          expect(File).to receive(:read).with(/app_download_header_file/).and_return("X-Cache-Status: MISS")
          expect(File).to receive(:delete).and_return(true)
          expect(BrowserStack::OSUtils).to receive(:unarchive).and_return(0)
          mobileprovisioning
          allow(Utils).to receive(:is_md5_checksum_equal?).and_return(true)
          expect(FileUtils).to receive(:rm).with(/.ipa/).and_return(true)
          install_app
        end

        context "should download app if <identifier_certname> folder already present" do
          before(:each) do
            allow(File).to receive(:exist?).with(download_folder).and_return(true)
            allow_any_instance_of(Object).to receive(:sleep).and_return(0)
          end

          context "if no uuid folder found" do
            before(:each) do
              allow(Dir).to receive(:glob).with("#{download_folder}/*/").and_return([])
            end

            context "should wait/retry for max 10 secs if any .starting file found" do
              before(:each) do
                allow(Dir).to receive(:glob).with(/.starting$/).and_return(["uuid.starting"])
              end

              it "if .complete file found" do
                expect(Dir).to receive(:glob).with(/.complete$/).and_return(["#{download_folder}/uuid.complete"])
                expect(Utils).to receive(:set_event_value).with('app_download_time', selenium_command_params[:event_hash], 0)
                mobileprovisioning
                allow(Utils).to receive(:is_md5_checksum_equal?).and_return(true)
                expect(BrowserStack::AppSettingsUtil).to receive(:parse_settings_bundle).and_return(true)
                install_app

                DeviceManager.send(:download_and_install_app, device, app_details, true, selenium_command_params['automate_session_id'], false, selenium_command_params)
              end

              it "if .complete file not found" do
                expect(Dir).to receive(:glob).with(/.complete$/).exactly(7).times.ordered.and_return([])
                expect(BrowserStack::AppSettingsUtil).to receive(:parse_settings_bundle).and_return(true)
                simple_download_flow

                DeviceManager.send(:download_and_install_app, device, app_details, true, selenium_command_params['automate_session_id'], false, selenium_command_params)
              end
            end

            it "should download app via simple flow if .starting file not found" do
              allow(Dir).to receive(:glob).with(/.starting$/).and_return([])
              expect(BrowserStack::AppSettingsUtil).to receive(:parse_settings_bundle).and_return(true)
              simple_download_flow

              DeviceManager.send(:download_and_install_app, device, app_details, true, selenium_command_params['automate_session_id'], false, selenium_command_params)
            end
          end

          context "if uuid folder found it should wait/retry for max 3 secs for any .complete found" do
            before(:each) do
              allow(Dir).to receive(:glob).with("#{download_folder}/*/").and_return(["uuid"])
              allow(Dir).to receive(:glob).with(/.starting$/).and_return(true)
            end

            it "if any .complete file found" do
              expect(Dir).to receive(:glob).with(/.complete$/).and_return(["#{download_folder}/uuid.complete"])
              expect(Utils).to receive(:set_event_value).with('app_download_time', selenium_command_params[:event_hash], 0)
              mobileprovisioning
              allow(Utils).to receive(:is_md5_checksum_equal?).and_return(true)
              expect(BrowserStack::AppSettingsUtil).to receive(:parse_settings_bundle).and_return(true)
              install_app

              DeviceManager.send(:download_and_install_app, device, app_details, true, selenium_command_params['automate_session_id'], false, selenium_command_params)
            end

            it "if no .complete file found" do
              expect(Dir).to receive(:glob).with(/.complete$/).exactly(2).times.ordered.and_return([])
              expect(BrowserStack::AppSettingsUtil).to receive(:parse_settings_bundle).and_return(true)
              simple_download_flow

              DeviceManager.send(:download_and_install_app, device, app_details, true, selenium_command_params['automate_session_id'], false, selenium_command_params)
            end
          end
        end

        it "should download main app if no previous downloads found" do
          allow(File).to receive(:exist?).with(download_folder).and_return(false)
          expect(BrowserStack::AppSettingsUtil).to receive(:parse_settings_bundle).and_return(true)
          simple_download_flow

          DeviceManager.send(:download_and_install_app, device, app_details, true, selenium_command_params['automate_session_id'], false, selenium_command_params)
        end

        it "should download main app via caching proxy if no previous downloads found and flag set true for app automate" do
          allow(File).to receive(:exist?).with(download_folder).and_return(false)
          expect(BrowserStack::AppSettingsUtil).to receive(:parse_settings_bundle).and_return(true)
          params = selenium_command_params.dup
          params["subregion_app_caching_enabled"] = true
          simple_download_flow_via_proxy

          DeviceManager.send(:download_and_install_app, device, app_details, true, params['automate_session_id'], false, params)
        end

        it "should download main app via normal flow if no previous downloads found and flag set false for app automate" do
          allow(File).to receive(:exist?).with(download_folder).and_return(false)
          expect(BrowserStack::AppSettingsUtil).to receive(:parse_settings_bundle).and_return(true)
          simple_download_flow

          DeviceManager.send(:download_and_install_app, device, app_details, true, selenium_command_params['automate_session_id'], false, selenium_command_params)
        end

        context "For non-app-automate" do
          initial_value = true
          before(:each) do
            initial_value = DeviceManager.instance_variable_get(:@is_app_testing)
            allow(File).to receive(:exist?).with(download_folder).and_return(false)
            DeviceManager.instance_variable_set(:@is_app_testing, false)
            expect(BrowserStack::AppSettingsUtil).to receive(:parse_settings_bundle).and_return(true)
            simple_download_flow
          end

          after(:each) do
            DeviceManager.instance_variable_set(:@is_app_testing, initial_value)
          end

          it "should download main app via normal flow if no previous downloads found and flag set true for non-app automate" do
            params = selenium_command_params.dup
            params["subregion_app_caching_enabled"] = true
            DeviceManager.send(:download_and_install_app, device, app_details, true, params['automate_session_id'], false, params)
          end

          it "should download main app via normal flow if no previous downloads found and flag not set true for non-app automate" do
            DeviceManager.send(:download_and_install_app, device, app_details, true, selenium_command_params['automate_session_id'], false, selenium_command_params)
          end
        end

        it "should download test app if no previous downloads found" do
          selenium_command_params[:app_type] = "test"
          selenium_command_params['xctests'] = []
          allow_download
          expect(BrowserStack::OSUtils).to receive(:unarchive).and_return(0)
          mobileprovisioning("*.app")
          allow(Utils).to receive(:is_md5_checksum_equal?).and_return(true)
          allow(BrowserStack::OSUtils).to receive(:execute).with(/ls/).and_return("#{extract_path}/xcui_tests.log\nlog_file2")
          expect(File).to receive_message_chain(:basename, :gsub).and_return("true")
          allow(FileUtils).to receive(:cp).and_return(true)
          expect(BrowserStack::OSUtils).to receive(:execute).with(/cp/).exactly(2).times.and_return(true)
          expect(FileUtils).to receive(:rm).with(/.zip/).and_return(true)
          install_app

          DeviceManager.send(:download_and_install_app, device, app_details, false, selenium_command_params['automate_session_id'], "xcuitest", selenium_command_params)
        end

        it "should download test app if no previous downloads found for dynamic xcuitest case" do
          selenium_command_params[:app_type] = "test"
          selenium_command_params['test_params'] = "{\"only-testing\":[\"SomeTarget/SomeClass/SomeTest\", \"SomeOtherTarget/SomeClass/SomeTest\"],\"is_dynamic_xcuitest\":\"true\"}"
          selenium_command_params['xctests'] = []
          allow_download
          expect(BrowserStack::OSUtils).to receive(:unarchive).and_return(0)
          mobileprovisioning("*.app")
          expect(Utils).to receive(:is_md5_checksum_equal?).and_return(true)
          expect(BrowserStack::OSUtils).to receive(:execute).with(/ls/).twice.and_return("")
          expect(FileUtils).to receive(:touch).with("/tmp/#{device}_xctest_SomeTarget").once
          expect(FileUtils).to receive(:touch).with("/tmp/#{device}_xctest_SomeOtherTarget").once
          expect(FileUtils).to receive(:rm).with(/.zip/).and_return(true)
          install_app
          DeviceManager.send(:download_and_install_app, device, app_details, false, selenium_command_params['automate_session_id'], "xcuitest", selenium_command_params)
          expect(selenium_command_params['xctests']).to eq(["SomeTarget", "SomeOtherTarget"])
        end

        it "should download test app if no previous downloads found for dynamicTests param case" do
          module_name = "SomeTarget"
          selenium_command_params[:app_type] = "test"
          selenium_command_params['test_params'] = "{\"only-testing\":[\"#{module_name}/SomeClass/SomeTest\", \"#{module_name}/SomeClass/SomeOtherTest\"],\"is_dynamic_xcuitest\":\"true\"}"
          selenium_command_params['xctests'] = []
          allow_download
          expect(BrowserStack::OSUtils).to receive(:unarchive).and_return(0)
          mobileprovisioning("*.app")
          expect(Utils).to receive(:is_md5_checksum_equal?).and_return(true)
          expect(BrowserStack::OSUtils).to receive(:execute).with(/ls/).and_return("#{extract_path}/xctest_#{module_name}.log", "")
          expect(FileUtils).to receive(:touch).with("/tmp/#{device}_xctest_#{module_name}").once
          expect(FileUtils).to receive(:rm).with(/.zip/).and_return(true)
          install_app
          DeviceManager.send(:download_and_install_app, device, app_details, false, selenium_command_params['automate_session_id'], "xcuitest", selenium_command_params)
          expect(selenium_command_params['xctests']).to eq(["SomeTarget"])
        end

        it "should copy mobileprovision file only if md5checksums do not match" do
          allow_download
          expect(BrowserStack::OSUtils).to receive(:unarchive).and_return(0)
          mobileprovisioning
          allow(Utils).to receive(:is_md5_checksum_equal?).and_return(false)
          expect(FileUtils).to receive(:rm_f).with(/embedded\.mobileprovision$/).and_return(true)
          expect(FileUtils).to receive(:cp).with(/ppuid3.mobileprovision$/, /embedded\.mobileprovision$/).and_return(true)
          expect(FileUtils).to receive(:rm).with(/.ipa/).and_return(true)
          expect(BrowserStack::AppSettingsUtil).to receive(:parse_settings_bundle).and_return(true)
          install_app

          DeviceManager.send(:download_and_install_app, device, app_details, true, selenium_command_params['automate_session_id'], false, selenium_command_params)
        end

        it "should copy xctestrun file only for xctestrun_flow sessions" do
          selenium_command_params[:app_type] = "test"
          selenium_command_params["execution_flow"] = "xctestrun"
          allow_download
          expect(BrowserStack::OSUtils).to receive(:unarchive).and_return(0)
          mobileprovisioning("*.app")
          expect(Utils).to receive(:is_md5_checksum_equal?).and_return(true)
          expect(BrowserStack::OSUtils).to receive(:execute).with(/ls/).and_return("", "")
          expect(Dir).to receive(:glob).with("#{extract_path}/*.xctestrun").and_return(["app_xctestrun_path"])
          expect(FileUtils).to receive(:cp).with("app_xctestrun_path", "/tmp/#{device}_xctestrun.xctestrun").and_return(true)
          expect(FileUtils).to receive(:rm).with(/.zip/).and_return(true)
          install_app
          DeviceManager.send(:download_and_install_app, device, app_details, false, selenium_command_params['automate_session_id'], "xcuitest", selenium_command_params)
        end

        it "should result in FireCMDException if could not copy mobileprovision" do
          allow_download
          expect(BrowserStack::OSUtils).to receive(:unarchive).and_return(0)
          mobileprovisioning
          allow(File).to receive(:read).with(/\.mobileprovision$/).and_raise("Cannot read file")

          expect do
            DeviceManager.send(:download_and_install_app, device, app_details, true, selenium_command_params['automate_session_id'], false, selenium_command_params)
          end.to raise_error(FireCMDException)
        end

        it "should raise FireCMDException and clear cached app is app install fails" do
          allow_download
          expect(BrowserStack::OSUtils).to receive(:unarchive).and_return(0)
          mobileprovisioning
          allow(Utils).to receive(:is_md5_checksum_equal?).and_return(true)
          expect(BrowserStack::AppSettingsUtil).to receive(:parse_settings_bundle).and_return(true)
          expect(FileUtils).to receive(:rm).with(/.ipa/).and_return(true)
          expect(FileUtils).to receive(:touch).with(/.complete$/).and_return(true)
          expect(FileUtils).to receive(:touch).with(/.session$/).and_return(true)
          allow(DeviceManager).to receive(:install_app_and_verify).with(any_args).and_raise("ios-deploy failed: install-253,verify-0, install-error InstallFailed")
          expect(FileUtils).to receive(:rm_rf).with(download_folder)

          expect do
            DeviceManager.send(:download_and_install_app, device, app_details, true, selenium_command_params['automate_session_id'], false, selenium_command_params)
          end.to raise_error(FireCMDException)
        end

        context "For App-automate" do
          initial_value = true
          before(:each) do
            initial_value = DeviceManager.instance_variable_get(:@is_app_testing)
            DeviceManager.instance_variable_set(:@is_app_testing, true)
          end

          after(:each) do
            DeviceManager.instance_variable_set(:@is_app_testing, initial_value)
          end

          it "should clear cached app data and retry if install fails for subregion caching enabled" do
            allow(File).to receive(:exist?).with(download_folder).and_return(false)
            expect(BrowserStack::AppSettingsUtil).to receive(:parse_settings_bundle).and_return(true)
            params = selenium_command_params.dup
            params["subregion_app_caching_enabled"] = true
            allow_download_via_proxy
            expect(File).to receive(:file?).and_return(true).twice
            expect(File).to receive(:read).with(/app_download_header_file/).and_return("X-Cache-Status: MISS")
            expect(File).to receive(:delete).and_return(true)
            expect(BrowserStack::OSUtils).to receive(:unarchive).and_return(0)
            mobileprovisioning
            allow(Utils).to receive(:is_md5_checksum_equal?).and_return(true)
            expect(FileUtils).to receive(:rm).with(/.ipa/).and_return(true)
            expect(FileUtils).to receive(:touch).with(/.complete$/).and_return(true)
            expect(FileUtils).to receive(:touch).with(/.session$/).and_return(true)
            allow(DeviceManager).to receive(:install_app_and_verify).with(any_args).and_raise("App Unarchive Failed")
            expect(FileUtils).to receive(:rm_rf).with(download_folder)
            allow(DeviceManager).to receive(:download_and_install_app).and_call_original
            expect(DeviceManager).to receive(:download_and_install_app).with(
              device, app_details, true, params['automate_session_id'], false, hash_including(
                                                                                 "subregion_app_caching_enabled" => false,
                                                                                 "download_retry_skip_backfill" => true
                                                                               )
            ).and_return(true)

            DeviceManager.send(:download_and_install_app, device, app_details, true, params['automate_session_id'], false, params)
          end
        end
      end

      context "is_user_error?" do
        it "should return true if error message is user error" do
          expect(DeviceManager.send(:is_user_error?, "ios-deploy failed: install-253,verify-0, install-error InstallFailed")).to be true
        end

        it "should return false if error message is not user error" do
          expect(DeviceManager.send(:is_user_error?, "ios-deploy failed: install-253,verify-0, install-error - BrowserStack Error")).to be false
        end
      end

      it "should raise error if app_identifier or cert is nil" do
        allow(DeviceManager).to receive(:get_backfill_obj).and_return(nil)
        allow(DeviceManager).to receive(:get_folder_path_details_from_s3_url).and_return([nil, nil])
        allow(File).to receive(:exist?).with("/tmp/apps/_").and_return(false)

        expect do
          DeviceManager.send(:download_and_install_app, "device_name", app_details, true, selenium_command_params['automate_session_id'], false, selenium_command_params)
        end.to raise_error("app_identifier or cert_name is nil from s3 url")
      end
    end
  end
end

describe "is_async_install_required?" do
  let(:params_with_async) do
    {
      "async_app_download_install" => "true",
      "resignApp" => "true"
    }
  end

  let(:params_without_async) do
    {
      "async_app_download_install" => "false",
      "resignApp" => "true"
    }
  end

  let(:minimized_flow) { false }

  it "should return true when async is enabled and all conditions are met" do
    expect(DeviceManager.send(:is_async_install_required?, params_with_async, minimized_flow)).to be true
  end

  it "should return false when minimized flow is true" do
    expect(DeviceManager.send(:is_async_install_required?, params_with_async, true)).to be false
  end

  it "should return false when resignApp is false" do
    params = params_with_async.merge("resignApp" => "false")
    expect(DeviceManager.send(:is_async_install_required?, params, minimized_flow)).to be false
  end

  it "should return false when update_app_settings is present" do
    params = params_with_async.merge("update_app_settings" => true)
    expect(DeviceManager.send(:is_async_install_required?, params, minimized_flow)).to be false
  end

  it "should return false when async_app_download_install is false" do
    expect(DeviceManager.send(:is_async_install_required?, params_without_async, minimized_flow)).to be false
  end

  it "should handle case-insensitive true value" do
    params = params_with_async.merge("async_app_download_install" => "TRUE")
    expect(DeviceManager.send(:is_async_install_required?, params, minimized_flow)).to be true
  end
end

describe "get_folder_path_details_from_s3_url" do
  let(:url) { "https://d1f4l52323mgi5dp.cloudfront.net/12345678_production_34_14452020/12345678_production_34_14452020.ipa?Expires=1596535419&Signature=somerandomsignature&Key-Pair-Id=********************" }
  let(:instrumented_url) { "https://d1f4l52323mgi5dp.cloudfront.net/12345678_production_34_14452020/12345678_production_34_14452020_instrumented_1.ipa?Expires=1596535419&Signature=somerandomsignature&Key-Pair-Id=********************" }

  let(:another_url) { "https://browserstack-userapps-stag-use1.s3.amazonaws.com/data/5dbcd2a9bdc1c8f0a330fd86066c77ee1371ae3e_unsigned/5dbcd2a9bdc1c8f0a330fd86066c77ee1371ae3e_unsigned.ipa?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIA2XUQHUQMBSXWVBBM%2F20220106%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20220106T034257Z&X-Amz-Expires=10800&X-Amz-SignedHeaders=host&X-Amz-Signature=552ada730daf50fdd638d899d661f384714fbac71eaf5c1c108f4f4d2b0019fd" }
  let(:another_instrumented_url) { "https://browserstack-userapps-stag-use1.s3.amazonaws.com/data/12345678_production_34_14452020/12345678_production_34_14452020_instrumented_1.ipa?Expires=1596535419&Signature=somerandomsignature&Key-Pair-Id=********************" }

  it "should return app identifier, certname and instrumented_suffix" do
    app_identifier, cert_name, instrumented_suffix = DeviceManager.send(:get_folder_path_details_from_s3_url, url)

    expect(app_identifier).to eq("12345678")
    expect(cert_name).to eq("production_34_14452020")
    expect(instrumented_suffix).to eq("")
  end

  it "should return correct instrumented_suffix" do
    app_identifier, cert_name, instrumented_suffix = DeviceManager.send(:get_folder_path_details_from_s3_url, instrumented_url)

    expect(app_identifier).to eq("12345678")
    expect(cert_name).to eq("production_34_14452020")
    expect(instrumented_suffix).to eq("_instrumented_1")
  end

  it "should return app identifier, certname and instrumented_suffix for new config" do
    app_identifier, cert_name, instrumented_suffix = DeviceManager.send(:get_folder_path_details_from_s3_url, another_url)

    expect(app_identifier).to eq("5dbcd2a9bdc1c8f0a330fd86066c77ee1371ae3e")
    expect(cert_name).to eq("unsigned")
    expect(instrumented_suffix).to eq("")
  end

  it "should return correct instrumented_suffix for new config" do
    app_identifier, cert_name, instrumented_suffix = DeviceManager.send(:get_folder_path_details_from_s3_url, another_instrumented_url)

    expect(app_identifier).to eq("12345678")
    expect(cert_name).to eq("production_34_14452020")
    expect(instrumented_suffix).to eq("_instrumented_1")
  end
end

describe "set_timezone" do
  device = 'some_device_id'
  context "APP LIVE" do
    timezone_params = {
      "timezone" => "UTC",
      "app_live_session_id" => "1234",
      "product" => "app_live_testing"
    }
    let(:wda_port) { 8400 }
    let(:device_config) { { 'webdriver_port' => wda_port } }
    let(:mock_wda_client) { double('mock_wda_client') }
    it 'should set timezone when called' do
      allow(IdeviceUtils).to receive(:list_user_installed_apps).and_return(["com.test.test"])
      allow(WdaClient).to receive(:new).and_return(mock_wda_client)
      expect(BrowserStack::IPhone).to receive(:change_time_zone).once
      expect(Utils).to receive(:notify_pusher).with("TimezoneSetSuccess", timezone_params, device)
      expect(mock_wda_client).to receive(:launch_apps_with_locale).once.with(["com.test.test"], "en-US", "en-US")
      DeviceManager.set_timezone(device_config, device, timezone_params)
    end

    it 'should send error via pusher when timezone failed' do
      allow(BrowserStack::IPhone).to receive(:change_time_zone).and_raise(MultipleTimezonesException)
      expect(Utils).to receive(:notify_pusher).with("TimezonesException", timezone_params, device)
      DeviceManager.set_timezone(device_config, device, timezone_params)
    end
  end

  context "LIVE" do
    timezone_params = {
      "timezone" => "UTC",
      "live_session_id" => "1234",
      "product" => "live_testing"
    }
    let(:wda_port) { 8400 }
    let(:device_config) { { 'webdriver_port' => wda_port } }
    let(:mock_wda_client) { double('mock_wda_client') }
    it 'should set timezone when called' do
      allow(IdeviceUtils).to receive(:list_user_installed_apps).and_return(["com.test.test"])
      allow(WdaClient).to receive(:new).and_return(mock_wda_client)
      expect(BrowserStack::IPhone).to receive(:change_time_zone).once
      expect(Utils).to receive(:notify_pusher_for_live).with("TimezoneSetSuccess", timezone_params, device)
      expect(mock_wda_client).to receive(:launch_apps_with_locale).once.with(["com.test.test"], "en-US", "en-US")
      DeviceManager.set_timezone(device_config, device, timezone_params)
    end

    it 'should send error via pusher when timezone failed' do
      allow(BrowserStack::IPhone).to receive(:change_time_zone).and_raise(MultipleTimezonesException)
      expect(Utils).to receive(:notify_pusher_for_live).with("TimezonesException", timezone_params, device)
      DeviceManager.set_timezone(device_config, device, timezone_params)
    end
  end
end

describe "get_ios_states" do
  let(:device) { '000820-427503425' }
  let(:mock_date_time_helper) { double("DateTimeHelper") }
  let(:current_date) { { day: "5", month: "5", year: "2023" } }
  let(:initial_date) { { day: "5", month: "5", year: "2023" } }

  before(:each) do
    allow(IdeviceUtils).to receive(:idevice_name).and_return("iPhone")
    allow(DateTimeHelper).to receive(:new).and_return(mock_date_time_helper)
    allow(mock_date_time_helper).to receive(:current_date_on_device_hash).and_return(current_date)
    allow(mock_date_time_helper).to receive(:initial_date_on_device_hash).and_return(initial_date)
  end

  it 'should return dark_mode true if enabled' do
    expect_any_instance_of(DeviceState).to receive(:dark_mode_file_present?).and_return(true)
    resp = DeviceManager.get_ios_states(device)
    expect(resp[:dark_mode]).to eq(true)
  end

  it 'should return dark_mode false if disabled' do
    expect_any_instance_of(DeviceState).to receive(:dark_mode_file_present?).and_return(false)
    resp = DeviceManager.get_ios_states(device)
    expect(resp[:dark_mode]).to eq(false)
  end

  it 'should return xss_toggle true if enabled' do
    expect_any_instance_of(DeviceState).to receive(:prevent_cross_site_tracking_disabled_file_present?).and_return(false)
    resp = DeviceManager.get_ios_states(device)
    expect(resp[:xss_toggle]).to eq(true)
  end

  it 'should return xss_toggle false if disabled' do
    expect_any_instance_of(DeviceState).to receive(:prevent_cross_site_tracking_disabled_file_present?).and_return(true)
    resp = DeviceManager.get_ios_states(device)
    expect(resp[:xss_toggle]).to eq(false)
  end

  it 'should return low_power_mode true if enabled' do
    expect_any_instance_of(DeviceState).to receive(:low_power_mode_file_present?).and_return(true)
    resp = DeviceManager.get_ios_states(device)
    expect(resp[:low_power_mode]).to eq(true)
  end
end

describe "assistive_touch" do
  device = 'some_device_id'
  params = {
    "mode" => "enable",
    "app_live_session_id" => "1234",
    "product" => "app_live_testing"
  }
  let(:wda_port) { 8400 }
  let(:device_config) { { 'webdriver_port' => wda_port } }
  let(:mock_wda_client) { double('mock_wda_client') }
  let(:mock_assistive_touch_client) { double('mock_assistive_touch_client') }
  it 'should set enabled when called' do
    allow(IdeviceUtils).to receive(:list_user_installed_apps).and_return(["com.test.test"])
    allow(WdaClient).to receive(:new).and_return(mock_wda_client)
    allow(Secure::AssistiveTouchHelper).to receive(:new).and_return(mock_assistive_touch_client)
    expect(mock_assistive_touch_client).to receive(:switch).with("enable").and_return(true)
    expect(Utils).to receive(:notify_pusher).with("ToggleAssistiveTouchSetSuccess", params, device)
    expect(mock_wda_client).to receive(:launch_apps_with_locale).once.with(["com.test.test"], "en-US", "en-US")
    DeviceManager.toggle_assistive_touch(device_config, device, params)
  end

  it 'should send error via pusher when failed to enable' do
    allow(Secure::AssistiveTouchHelper).to receive(:new).and_return(mock_assistive_touch_client)
    expect(mock_assistive_touch_client).to receive(:switch).with("enable").and_return(false)
    expect(Utils).to receive(:notify_pusher).with("ToggleAssistiveTouchException", params, device)
    DeviceManager.toggle_assistive_touch(device_config, device, params)
  end
end

describe "change_device_name" do
  device = 'some_device_id'
  params = {
    "new_device_name" => "Test",
    "app_live_session_id" => "1234",
    "product" => "app_live_testing"
  }

  it "should change device name when called and no errors are raised" do
    expect(IdeviceUtils).to receive(:idevice_name).with("some_device_id", name: "Test")
    expect(Utils).to receive(:notify_pusher).with("ChangeDeviceNameSuccess", params, device)
    DeviceManager.change_device_name(device, params)
  end

  it "should raise error if any exception is raised" do
    expect(IdeviceUtils).to receive(:idevice_name).with("some_device_id", name: "Test").and_raise("Some error")
    expect(Utils).to receive(:notify_pusher).with("ChangeDeviceNameException", params, device)
    expect { DeviceManager.change_device_name(device, params) }.to raise_error("Some error")
  end
end

describe "change_mode" do
  device = 'some_device_id'
  let(:wda_port) { 8400 }
  let(:device_config) { { 'webdriver_port' => wda_port } }
  let(:mock_wda_client) { double('mock_wda_client') }
  let(:mock_changemode_client) { double('mock_changemode_client') }
  context "APP LIVE" do
    params = {
      "mode" => "Dark",
      "app_live_session_id" => "1234",
      "product" => "app_live_testing"
    }
    it 'should set DarkMode when called' do
      allow(IdeviceUtils).to receive(:list_user_installed_apps).and_return(["com.test.test"])
      allow(WdaClient).to receive(:new).and_return(mock_wda_client)
      allow(BrowserStack::SwitchMode).to receive(:new).and_return(mock_changemode_client)
      expect(mock_changemode_client).to receive(:change_appearance_to).with("Dark").and_return(true)
      expect(Utils).to receive(:notify_pusher).with("ChangeModeSetSuccess", params, device)
      expect(mock_wda_client).to receive(:launch_apps_with_locale).once.with(["com.test.test"], "en-US", "en-US")
      DeviceManager.change_mode(device_config, device, params)
    end

    it 'should send error via pusher when DarkMode failed' do
      allow(BrowserStack::SwitchMode).to receive(:new).and_return(mock_changemode_client)
      expect(mock_changemode_client).to receive(:change_appearance_to).with("Dark").and_return(false)
      expect(Utils).to receive(:notify_pusher).with("ChangeModeException", params, device)
      DeviceManager.change_mode(device_config, device, params)
    end
  end

  context "LIVE" do
    params = {
      "mode" => "Dark",
      "live_session_id" => "1234",
      "product" => "live_testing"
    }
    it 'should set DarkMode when called' do
      allow(IdeviceUtils).to receive(:list_user_installed_apps).and_return(["com.test.test"])
      allow(WdaClient).to receive(:new).and_return(mock_wda_client)
      allow(BrowserStack::SwitchMode).to receive(:new).and_return(mock_changemode_client)
      expect(mock_changemode_client).to receive(:change_appearance_to).with("Dark").and_return(true)
      expect(Utils).to receive(:notify_pusher_for_live).with("ChangeModeSetSuccess", params, device)
      expect(mock_wda_client).to receive(:launch_apps_with_locale).once.with(["com.test.test"], "en-US", "en-US")
      DeviceManager.change_mode(device_config, device, params)
    end

    it 'should send error via pusher when DarkMode failed' do
      allow(BrowserStack::SwitchMode).to receive(:new).and_return(mock_changemode_client)
      expect(mock_changemode_client).to receive(:change_appearance_to).with("Dark").and_return(false)
      expect(Utils).to receive(:notify_pusher_for_live).with("ChangeModeException", params, device)
      DeviceManager.change_mode(device_config, device, params)
    end
  end
end

describe "toggle_offline_mode" do
  device = 'some_device_id'
  ip = '127.0.0.1'
  let(:wda_port) { 8400 }
  let(:device_config) { { 'webdriver_port' => wda_port } }
  let(:mock_pfctl_helper) { double('mock_pfctl_helper') }
  let(:mock_device_state) { double(DeviceState) }

  before(:each) do
    allow(DeviceState).to receive(:new).and_return(mock_device_state)
    allow(mock_device_state).to receive(:dedicated_device_file_present?).and_return(false)
    allow(CustomMDMManager).to receive(:is_custom_mdm_device?).and_return(false)
    allow(DeviceManager).to receive(:session_file_contents)
  end

  context "APP_LIVE" do
    params = {
      "app_live_session_id" => "1234",
      "product" => "app_live_testing"
    }
    it 'should call pfctls enable_offline_mode when offline mode should be enabled' do
      params["mode"] = "Offline"
      allow(PFCTLHelper).to receive(:new).and_return(mock_pfctl_helper)
      expect(mock_pfctl_helper).to receive(:enable_offline_mode).and_return(true)
      expect(Utils).to receive(:notify_pusher).with("OfflineModeToggleSuccess", params, device)
      expect(BrowserStack::Zombie).to receive(:push_logs).exactly(:once).and_return(true)
      DeviceManager.toggle_offline_mode(ip, device_config, device, params)
    end

    it 'should call pfctls disable_offline_mode when offline mode should be disabled' do
      params["mode"] = "Online"
      allow(PFCTLHelper).to receive(:new).and_return(mock_pfctl_helper)
      expect(mock_pfctl_helper).to receive(:disable_offline_mode).and_return(true)
      expect(Utils).to receive(:notify_pusher).with("OfflineModeToggleSuccess", params, device)
      expect(BrowserStack::Zombie).to receive(:push_logs).exactly(:once).and_return(true)
      DeviceManager.toggle_offline_mode(ip, device_config, device, params)
    end

    it 'should notify error to pusher if offline mode toggling failed' do
      params["mode"] = "Offline"
      allow(PFCTLHelper).to receive(:new).and_return(mock_pfctl_helper)
      expect(mock_pfctl_helper).to receive(:enable_offline_mode).and_raise(OfflineModeFailureException, "Failed")
      expect(Utils).to receive(:notify_pusher).with("OfflineModeToggleException", params, device)
      expect(BrowserStack::Zombie).to receive(:push_logs).exactly(:once).and_return(true)
      expect { DeviceManager.toggle_offline_mode(ip, device_config, device, params) }.to raise_error(OfflineModeFailureException, "Failed")
    end
  end

  context "LIVE" do
    params = {
      "live_session_id" => "1234",
      "product" => "live_testing"
    }
    it 'should call pfctls enable_offline_mode when offline mode should be enabled' do
      params["mode"] = "Offline"
      allow(PFCTLHelper).to receive(:new).and_return(mock_pfctl_helper)
      expect(mock_pfctl_helper).to receive(:enable_offline_mode).and_return(true)
      expect(Utils).to receive(:notify_pusher_for_live).with("OfflineModeToggleSuccess", params, device)
      expect(BrowserStack::Zombie).to receive(:push_logs).exactly(:once).and_return(true)
      DeviceManager.toggle_offline_mode(ip, device_config, device, params)
    end

    it 'should call pfctls disable_offline_mode when offline mode should be disabled' do
      params["mode"] = "Online"
      allow(PFCTLHelper).to receive(:new).and_return(mock_pfctl_helper)
      expect(mock_pfctl_helper).to receive(:disable_offline_mode).and_return(true)
      expect(Utils).to receive(:notify_pusher_for_live).with("OfflineModeToggleSuccess", params, device)
      expect(BrowserStack::Zombie).to receive(:push_logs).exactly(:once).and_return(true)
      DeviceManager.toggle_offline_mode(ip, device_config, device, params)
    end

    it 'should notify error to pusher if offline mode toggling failed' do
      params["mode"] = "Offline"
      allow(PFCTLHelper).to receive(:new).and_return(mock_pfctl_helper)
      expect(mock_pfctl_helper).to receive(:enable_offline_mode).and_raise(OfflineModeFailureException, "Failed")
      expect(Utils).to receive(:notify_pusher_for_live).with("OfflineModeToggleException", params, device)
      expect(BrowserStack::Zombie).to receive(:push_logs).exactly(:once).and_return(true)
      expect { DeviceManager.toggle_offline_mode(ip, device_config, device, params) }.to raise_error(OfflineModeFailureException, "Failed")
    end
  end
end

describe "Accessibility Settings" do
  device = 'some_device_id'
  ip = '127.0.0.1'
  let(:wda_port) { 8400 }
  let(:state) do
    {
      motion: {
        reduce_motion: "disable"
      },
      voice_over: {
        speaking_rate: 50,
        captions_panel: "disable",
        navigation_style: "flat"
      },
      display_and_textsize: {
        increase_contrast: "disable",
        larger_accessibility_sizes: "disable",
        text_size: 4
      },
      pusher: "PusherMessage"
    }
  end
  let(:mock_wda_client) { double('mock_wda_client') }
  let(:device_config) { { 'webdriver_port' => wda_port } }
  let(:mock_accessibility_helper) { double('mock_accessibility_helper') }
  context "APP_LIVE" do
    params = {
      "app_live_session_id" => "1234",
      "product" => "app_live_testing"
    }

    it 'should notify error to pusher if accessibility settings update failed' do
      params["mode"] = {
        increase_contrast: "disable",
        larger_accessibility_sizes: "disable",
        text_size: -1
      }
      allow(Secure::AccessibilitySettingsHelper).to receive(:new).and_return(mock_accessibility_helper)
      expect(mock_accessibility_helper).to receive(:update_accessibility_settings).with(params["mode"]).and_return(state)
      allow(WdaClient).to receive(:new).and_return(mock_wda_client)
      expect(Utils).to receive(:notify_pusher).with(state[:pusher_message], params, device)
      allow(mock_wda_client).to receive(:foreground).and_return("com.google.chrome.ios")
      expect(BrowserStack::Zombie).to receive(:push_logs).exactly(:once).and_return(true)
      DeviceManager.update_accessibility_settings(device, params)
    end

    it 'should notify error to pusher if accessibility settings update failed' do
      params["mode"] = {
        increase_contrast: "disable",
        larger_accessibility_sizes: "disable",
        text_size: 4
      }
      allow(Secure::AccessibilitySettingsHelper).to receive(:new).and_return(mock_accessibility_helper)
      expect(mock_accessibility_helper).to receive(:update_accessibility_settings).with(params["mode"]).and_return(false)
      allow(WdaClient).to receive(:new).and_return(mock_wda_client)
      expect(Utils).to receive(:notify_pusher).with("AccessibilitySettingsException", params, device)
      allow(mock_wda_client).to receive(:foreground).and_return("com.google.chrome.ios")
      DeviceManager.update_accessibility_settings(device, params)
    end
  end

  context "LIVE" do
    params = {
      "live_session_id" => "1234",
      "product" => "live_testing"
    }

    it 'should notify error to pusher if accessibility settings update failed' do
      params["mode"] = {
        increase_contrast: "disable",
        larger_accessibility_sizes: "disable",
        text_size: -1
      }
      allow(Secure::AccessibilitySettingsHelper).to receive(:new).and_return(mock_accessibility_helper)
      expect(mock_accessibility_helper).to receive(:update_accessibility_settings).with(params["mode"]).and_return(state)
      allow(WdaClient).to receive(:new).and_return(mock_wda_client)
      expect(Utils).to receive(:notify_pusher_for_live).with(state[:pusher_message], params, device)
      allow(mock_wda_client).to receive(:foreground).and_return("com.google.chrome.ios")
      expect(BrowserStack::Zombie).to receive(:push_logs).exactly(:once).and_return(true)
      DeviceManager.update_accessibility_settings(device, params)
    end

    it 'should notify error to pusher if accessibility settings update failed' do
      params["mode"] = {
        increase_contrast: "disable",
        larger_accessibility_sizes: "disable",
        text_size: 4
      }
      allow(Secure::AccessibilitySettingsHelper).to receive(:new).and_return(mock_accessibility_helper)
      expect(mock_accessibility_helper).to receive(:update_accessibility_settings).with(params["mode"]).and_return(false)
      allow(WdaClient).to receive(:new).and_return(mock_wda_client)
      expect(Utils).to receive(:notify_pusher_for_live).with("AccessibilitySettingsException", params, device)
      allow(mock_wda_client).to receive(:foreground).and_return("com.google.chrome.ios")
      DeviceManager.update_accessibility_settings(device, params)
    end
  end
end

describe "switch_prevent_cross_site_tracking" do
  device = 'some_device_id'
  params = {
    "toggle" => "disable",
    "live_session_id" => "1234",
    "product" => "live_testing"
  }
  let(:wda_port) { 8400 }
  let(:device_config) { { 'webdriver_port' => wda_port } }
  let(:mock_wda_client) { double('mock_wda_client') }
  let(:mock_switchtoggle_client) { double('mock_switchtoggle_client') }

  before do
    allow(BrowserStack::PreventCrossSiteTracking).to receive(:new).and_return(mock_switchtoggle_client)
  end

  it 'should disable prevent_cross_site_tracking when called' do
    allow(WdaClient).to receive(:new).and_return(mock_wda_client)
    expect(mock_switchtoggle_client).to receive(:switch).with("disable").and_return(true)
    expect(Utils).to receive(:notify_pusher_for_live).with("PreventCrossSiteTrackingSuccess", params, device)
    DeviceManager.switch_prevent_cross_site_tracking(device_config, device, params)
  end

  it 'should send error via pusher when Switch toggle failed' do
    expect(mock_switchtoggle_client).to receive(:switch).with("disable").and_return(false)
    expect(Utils).to receive(:notify_pusher_for_live).with("PreventCrossSiteTrackingException", params, device)
    DeviceManager.switch_prevent_cross_site_tracking(device_config, device, params)
  end
end

describe "Backfill" do
  device = 'some_device_id'

  options = {
    certificate: "dummy",
    unsigned_app_url: "url",
    app_hashed_id: "hashed_id",
    is_framework: false,
    product: "app_automate",
    codesigner_host: "codesigner_host",
    rails_host: 'rails_host',
    skipSigningFrameworks: nil,
    patchApp: false,
    device_id: device,
    s3_config: "s3_config",
    user_id: "123",
    backfill_signed_app_url: "backfill_signed_app_url",
    browserstackInjectorVersion: nil
  }

  patchapp_options = {
    certificate: "dummy",
    unsigned_app_url: "url",
    app_hashed_id: "hashed_id",
    is_framework: false,
    product: "app_automate",
    codesigner_host: "codesigner_host",
    rails_host: 'rails_host',
    skipSigningFrameworks: nil,
    patchApp: true,
    device_id: device,
    s3_config: "s3_config",
    appFramework: "framework",
    user_id: "123",
    patched_app_download_url: "patched_app_download_url",
    browserstackInjectorVersion: 1
  }

  expected_patchapp_options = {
    certificate: "dummy",
    app_url: "url",
    is_framework: false,
    app_hashed_id: "hashed_id",
    product: "app_automate",
    from_platform: true,
    rails_host: 'rails_host',
    skipSigningFrameworks: nil,
    s3_config: "s3_config",
    user_id: "123",
    is_tvos: nil,
    browserstackInjectorVersion: 1
  }

  expected_options = {
    certificate: "dummy",
    app_url: "url",
    is_framework: false,
    app_hashed_id: "hashed_id",
    product: "app_automate",
    from_platform: true,
    rails_host: 'rails_host',
    skipSigningFrameworks: nil,
    s3_config: "s3_config",
    user_id: "123",
    is_tvos: nil,
    browserstackInjectorVersion: nil
  }

  tvos_expected_options = {
    certificate: "dummy",
    app_url: "url",
    is_framework: false,
    app_hashed_id: "hashed_id",
    product: "app_automate",
    from_platform: true,
    rails_host: 'rails_host',
    skipSigningFrameworks: nil,
    s3_config: "s3_config",
    user_id: "123",
    is_tvos: true,
    browserstackInjectorVersion: nil
  }

  tvos_backfill_object = {
    "app" => {
      "unsigned_app_url" => "app_unsigned_url",
      "app_hashed_id" => "app_hashed_id",
      "codesigner_host" => "codesigner_host",
      "certificate" => "certificate_name",
      "s3_config" => "s3_config",
      "is_tvos" => true
    }
  }

  backfill_object = {
    "app" => {
      "unsigned_app_url" => "app_unsigned_url",
      "app_hashed_id" => "app_hashed_id",
      "codesigner_host" => "codesigner_host",
      "certificate" => "certificate_name",
      "s3_config" => "s3_config"
    },
    "test_app" => {
      "unsigned_app_url" => "test_app_unsigned_url",
      "app_hashed_id" => "test_app_hashed_id",
      "codesigner_host" => "codesigner_host",
      "certificate" => "certificate_name",
      "s3_config" => "s3_config"
    },
    "other_apps" => [
          {
            "unsigned_app_url" => "other_apps_unsigned_url",
            "app_hashed_id" => "test_app_hashed_id",
            "codesigner_host" => "codesigner_host",
            "certificate" => "certificate_name",
            "s3_config" => "s3_config"
          },
          {
            "unsigned_app_url" => "other_apps_unsigned_url",
            "app_hashed_id" => "test_app_hashed_id",
            "codesigner_host" => "codesigner_host",
            "certificate" => "certificate_name",
            "s3_config" => "s3_config"
          }
      ],
    "mid_session_install_apps" => [
          {
            "unsigned_app_url" => "mid_session_apps_unsigned_url",
            "app_hashed_id" => "mid_session_app_hashed_id",
            "codesigner_host" => "codesigner_host",
            "certificate" => "certificate_name",
            "rails_host" => "rails_env_host",
            "s3_config" => "s3_config"
          },
          {
            "unsigned_app_url" => "mid_session_apps_unsigned_url",
            "app_hashed_id" => "mid_session_app_hashed_id",
            "codesigner_host" => "codesigner_host",
            "certificate" => "certificate_name",
            "rails_host" => "rails_env_host",
            "s3_config" => "s3_config"
          }
      ]
  }
  let(:device_manager) { DeviceManager }

  it 'should return url when called with correct backfill options' do
    expect(AppPatchingUtil).to receive(:update_app_patching_data_to_state_file)
    device_manager.configure BrowserStack::Configuration.new.all
    res_string = JSON.dump({
      "s3PresignedUrl" => "s3_url"
    })
    resp = double("response", code: 200, body: res_string)
    allow(AppAnalyticsUtil).to receive(:instrument_time)
    allow(AppAnalyticsUtil).to receive(:instrument_time)
    allow(BrowserStack::HttpUtils).to receive(:send_post).with("codesigner_host/backfill", { data: expected_options }, nil, true, { read_timeout: 180 }).and_return(resp)
    url = device_manager.send(:backfill_app, options)
    expect(url).to eq("backfill_signed_app_url")
  end

  it 'should return url when called with correct backfill options' do
    expect(AppPatchingUtil).to receive(:update_app_patching_data_to_state_file)
    allow(AppAnalyticsUtil).to receive(:instrument_time)
    device_manager.configure BrowserStack::Configuration.new.all
    res_string = JSON.dump({
      "s3PresignedUrl" => "s3_url"
    })
    options[:is_tvos] = true
    options[:backfill_signed_app_url] = nil
    resp = double("response", code: 200, body: res_string)
    allow(BrowserStack::HttpUtils).to receive(:send_post).with("codesigner_host/backfill", { data: tvos_expected_options }, nil, true, { read_timeout: 180 }).and_return(resp)
    url = device_manager.send(:backfill_app, options)
    expect(url).to eq("s3_url")
    options.delete(:is_tvos)
  end

  it 'should call patchApp when called with patchApp backfill options' do
    expect(AppPatchingUtil).to receive(:update_app_patching_data_to_state_file)
    allow(AppAnalyticsUtil).to receive(:instrument_time)
    device_manager.configure BrowserStack::Configuration.new.all
    res_string = JSON.dump({
      "s3PresignedUrl" => "s3_url"
    })
    resp = double("response", code: 200, body: res_string)
    expect(BrowserStack::HttpUtils).to receive(:send_post).with("codesigner_host/patchApp", { data: expected_patchapp_options }, nil, true, { read_timeout: 180 }).and_return(resp)
    url = device_manager.send(:backfill_app, patchapp_options)
    expect(url).to eq("patched_app_download_url")
  end

  it 'should call backfill when called with backfill backfill options [non including s3_object]' do
    options_clone = options.clone
    expected_options_clone = expected_options.clone

    expected_options_clone.delete("s3_config")
    options_clone.delete("s3_config")

    expect(AppPatchingUtil).to receive(:update_app_patching_data_to_state_file)
    allow(AppAnalyticsUtil).to receive(:instrument_time)
    device_manager.configure BrowserStack::Configuration.new.all
    res_string = JSON.dump({
      "s3PresignedUrl" => "s3_url"
    })
    resp = double("response", code: 200, body: res_string)
    allow(BrowserStack::HttpUtils).to receive(:send_post).with("codesigner_host/backfill", { data: expected_options_clone }, nil, true, { read_timeout: 180 }).and_return(resp)
    url = device_manager.send(:backfill_app, options_clone)
    expect(url).to eq("s3_url")
  end

  it 'should call patchApp when called with patchApp backfill options [non including s3_object]' do
    patchapp_options_clone = patchapp_options.clone
    expected_patchapp_options_clone = expected_patchapp_options.clone

    expected_patchapp_options_clone.delete("s3_config")
    patchapp_options_clone.delete("s3_config")

    expect(AppPatchingUtil).to receive(:update_app_patching_data_to_state_file)
    allow(AppAnalyticsUtil).to receive(:instrument_time)
    device_manager.configure BrowserStack::Configuration.new.all
    res_string = JSON.dump({
      "s3PresignedUrl" => "s3_url"
    })
    resp = double("response", code: 200, body: res_string)
    expect(BrowserStack::HttpUtils).to receive(:send_post).with("codesigner_host/patchApp", { data: expected_patchapp_options_clone }, nil, true, { read_timeout: 180 }).and_return(resp)
    url = device_manager.send(:backfill_app, patchapp_options_clone)
    expect(url).to eq("patched_app_download_url")
  end

  it 'should return nil if backfill fails' do
    expect(AppPatchingUtil).to receive(:update_app_patching_data_to_state_file)
    allow(AppAnalyticsUtil).to receive(:instrument_time)
    device_manager.configure BrowserStack::Configuration.new.all
    res_string = JSON.dump({
      "error" => "Failed for 3eeb0e9fd6fa0235dd1fb12d573a56dd43ab910b"
    })
    resp = double("response", code: 500, body: res_string)
    allow(BrowserStack::HttpUtils).to receive(:send_post).with("codesigner_host/backfill", { data: expected_options }, nil, true, { read_timeout: 180 }).and_return(resp)
    url = device_manager.send(:backfill_app, options)
    expect(url).to eq(nil)
  end

  it 'get_backfill_obj should return nil if no backfill in params' do
    params = { app_type: "test" }
    device_manager.configure BrowserStack::Configuration.new.all
    backfill_req = device_manager.send(:get_backfill_obj, params)
    expect(backfill_req).to eq(nil)
  end

  it 'get_backfill_obj should return nil if no app_type in params' do
    params = { "backfill" => "{}" }
    device_manager.configure BrowserStack::Configuration.new.all
    backfill_req = device_manager.send(:get_backfill_obj, params)
    expect(backfill_req).to eq(nil)
  end

  it 'get_backfill_obj should return backfill req for main app' do
    params = {
      "backfill" => backfill_object.to_json
    }
    params.merge!({ app_type: "main" })
    device_manager.configure BrowserStack::Configuration.new.all
    backfill_req = device_manager.send(:get_backfill_obj, params)
    expect(backfill_req["unsigned_app_url"]).to eq("app_unsigned_url")
  end

  it 'get_backfill_obj should return backfill req for main tvos app' do
    params = {
      "backfill" => tvos_backfill_object.to_json
    }
    params.merge!({ app_type: "main" })
    device_manager.configure BrowserStack::Configuration.new.all
    backfill_req = device_manager.send(:get_backfill_obj, params)
    expect(backfill_req["unsigned_app_url"]).to eq("app_unsigned_url")
    expect(backfill_req["is_tvos"]).to be_truthy
  end

  it 'get_backfill_obj should return backfill req for test app' do
    params = {
      "backfill" => backfill_object.to_json
    }
    params.merge!({ app_type: "test" })
    device_manager.configure BrowserStack::Configuration.new.all
    backfill_req = device_manager.send(:get_backfill_obj, params)
    expect(backfill_req["unsigned_app_url"]).to eq("test_app_unsigned_url")
  end

  it 'get_backfill_obj should return backfill req for other apps' do
    params = {
      "backfill" => backfill_object.to_json
    }
    params.merge!({ app_type: "dependent" })
    device_manager.configure BrowserStack::Configuration.new.all
    backfill_req = device_manager.send(:get_backfill_obj, params)
    expect(backfill_req[0]["unsigned_app_url"]).to eq("other_apps_unsigned_url")
  end

  it 'get_backfill_obj should return backfill req for midSession apps' do
    params = {
      "backfill" => backfill_object.to_json
    }
    params.merge!({ app_type: "dependent" })
    device_manager.configure BrowserStack::Configuration.new.all
    backfill_req = device_manager.send(:get_backfill_obj, params)
    expect(backfill_req[2]["unsigned_app_url"]).to eq("mid_session_apps_unsigned_url")
  end

  it 'get_backfill_obj should return nil for main app if main app backfill obj missing' do
    backfill_object_clone = backfill_object.clone
    backfill_object_clone.delete("app")
    params = {
      "backfill" => backfill_object_clone.to_json
    }
    params.merge!({ app_type: "main" })

    device_manager.configure BrowserStack::Configuration.new.all
    backfill_req = device_manager.send(:get_backfill_obj, params)
    expect(backfill_req).to eq(nil)
  end

  it 'get_backfill_obj should return nil for test app if test app backfill obj missing' do
    backfill_object_clone = backfill_object.clone
    backfill_object_clone.delete("test_app")
    params = {
      "backfill" => backfill_object_clone.to_json
    }
    params.merge!({ app_type: "test" })

    device_manager.configure BrowserStack::Configuration.new.all
    backfill_req = device_manager.send(:get_backfill_obj, params)
    expect(backfill_req).to eq(nil)
  end

  it 'get_backfill_obj should return only for mid_session_app if other_app backfill obj missing' do
    backfill_object_clone = backfill_object.clone
    backfill_object_clone.delete("other_apps")
    params = {
      "backfill" => backfill_object_clone.to_json
    }
    params.merge!({ app_type: "dependent" })

    device_manager.configure BrowserStack::Configuration.new.all
    backfill_req = device_manager.send(:get_backfill_obj, params)
    puts backfill_req
    expect(backfill_req[0]["unsigned_app_url"]).to eq("mid_session_apps_unsigned_url")
  end

  it 'get_backfill_obj should return nil if other_app and mid_session_app backfill obj missing' do
    backfill_object_clone = backfill_object.clone
    backfill_object_clone.delete("other_apps")
    backfill_object_clone.delete("mid_session_install_apps")
    params = {
      "backfill" => backfill_object_clone.to_json
    }
    params.merge!({ app_type: "dependent" })

    device_manager.configure BrowserStack::Configuration.new.all
    backfill_req = device_manager.send(:get_backfill_obj, params)
    expect(backfill_req).to eq(nil)
  end

  describe '.remove_device' do
    let(:udid) { 'abcd' }
    let(:state_files_dir) { '//usr/local/.browserstack/state_files' }
    let(:device_state) { DeviceState.new(udid) }

    before do
      expect(DeviceState).to receive(:new).and_return(device_state)
    end

    it "Removes the necessary files" do
      expect(device_manager).to receive(:load_config)
      device_manager.class_variable_set(:@@device_info, '{"devices": {"abcd": 1}}')
      device_manager.class_variable_set(:@@device_info_file, 'config.json')
      expect(Utils).to receive(:write_config_with_lock)
      expect(Dir).to receive(:glob).with("#{state_files_dir}/*")
      device_manager.remove_device(udid)
    end
  end

  describe "get url" do
    let(:http) { double('http') }
    it "should return empty url on error" do
      expect(Net::HTTP).to receive(:new).and_return(http)
      expect(http).to receive(:use_ssl=).with(true)
      expect(http).to receive(:read_timeout=).with(2)
      expect(http).to receive(:verify_mode=).with(OpenSSL::SSL::VERIFY_NONE)
      expect(http).to receive(:get) {
        raise Net::ProtocolError
      }
      expect(DeviceManager.get_url('_', '8080')).to eq('')
    end

    it "should return url if no errors are throw" do
      resp = double('resp')
      dummy_json = "[{\"url\":\"https://www.google.com\"}]"
      expect(http).to receive(:use_ssl=).with(true)
      expect(http).to receive(:read_timeout=).with(2)
      expect(http).to receive(:verify_mode=).with(OpenSSL::SSL::VERIFY_NONE)
      expect(Net::HTTP).to receive(:new).and_return(http)
      expect(resp).to receive(:body).and_return(dummy_json)
      expect(http).to receive(:get).and_return([resp, nil])

      expect(DeviceManager.get_url('_', '8080')).to eq('https://www.google.com')
    end
  end
end

describe "using_production_codesigner" do
  let(:device_manager) { DeviceManager }
  it "should return true if host is a production codesigner" do
    host = "http://codesigner-usw.browserstack.com"
    expect(device_manager.using_production_codesigner?(host)).to eq(true)
  end

  it "should return false if host is a staging codesigner" do
    host = "http://codesigner-usw.bsstag.com"
    expect(device_manager.using_production_codesigner?(host)).to eq(false)
  end
end

describe "patch_app" do
  options = {
    certificate: "dummy_cert",
    app_url: "dummy_url",
    app_hashed_id: "hashed_id",
    skipSigningFrameworks: false,
    product: "app_live",
    codesigner_host: "codesigner_host",
    rails_host: 'rails_host',
    device_id: 'some_device_id',
    appFramework: 'framework',
    user_id: "123",
    patched_app_download_url: "patched_app_download_url",
    browserstackInjectorVersion: 1
  }

  expected_options = {
    certificate: "dummy_cert",
    app_url: "dummy_url",
    app_hashed_id: "hashed_id",
    skipSigningFrameworks: false,
    product: "app_live",
    rails_host: 'rails_host',
    s3_config: nil,
    appFramework: 'framework',
    user_id: "123",
    browserstackInjectorVersion: 1,
    bonjour_service_type: nil,
    enable_bonjour: nil,
    network_usage_description: nil
  }

  let(:device_manager) { DeviceManager }
  it 'should send patchApp request to codesigner and return instrumented app S3 url' do
    codesigner_response = JSON.dump({
      "s3PresignedUrl" => "instrumented_app_s3_url"
    })
    resp = double("response", code: 200, body: codesigner_response)
    expect(BrowserStack::HttpUtils).to receive(:send_post).with("codesigner_host/patchApp", { data: expected_options }, nil, true, { read_timeout: 180 }).and_return(resp)
    expect(AppPatchingUtil).to receive(:update_app_patching_data_to_state_file).with(options[:device_id], any_args)
    instrumented_app_url = device_manager.patch_app(options)
    expect(instrumented_app_url).to eq("patched_app_download_url")
  end

  it 'should raise error if update_app_patching_data_to_state_file failed' do
    codesigner_response = JSON.dump({
      "s3PresignedUrl" => "instrumented_app_s3_url"
    })
    resp = double("response", code: 200, body: codesigner_response)
    expect(BrowserStack::HttpUtils).to receive(:send_post).with("codesigner_host/patchApp", { data: expected_options }, nil, true, { read_timeout: 180 }).and_return(resp)
    expect(AppPatchingUtil).to receive(:update_app_patching_data_to_state_file).with(options[:device_id], any_args).and_raise("some_error")
    expect { device_manager.patch_app(options) }.to raise_error("some_error")
  end
end

describe "check_and_push_failure_reason" do
  before do
    mock_hooter_instance
  end

  let(:device_manager) { DeviceManager }

  let(:device_id) do
    "randomdeviceid"
  end

  let(:device_config) do
    JSON.parse(fixture("config.json"))
  end

  before(:each) do
    DeviceManager.configure BrowserStack::Configuration.new.all
    allow(Utils).to receive(:send_product_stability_reason_to_eds)
    allow(DeviceManager).to receive(:all_devices)
      .and_return({
        device_id => device_config
      })
    @params =  {
      edsHost: 'edsHost',
      edsPort: 'edsPort',
      edsKey: 'edsKey',
      genre: 'app_automate',
      automate_session_id: "session_id",
      last_request: "last_request",
      last_raw_log: "last_raw_log",
      reason: "reason",
      session_limit: "7200"
    }
  end

  it 'should push valid data to eds and zombie in case of exception raised for sotimeout' do
    allow(File).to receive(:foreach).and_return([])
    expect(BrowserStack::Zombie).to receive(:push_logs).with("app-SOTIMEOUT-check-failed", "app_automate", anything)
    device_manager.send(:check_and_push_failure_reason, device_id, @params)
  end

  it 'should push valid data to eds and zombie for sotimeout' do
    appium_log_fixture = "#{__dir__}/../fixtures/appium_command_timeout.log"
    expect(device_manager).to receive(:session_appium_logs_device).with("randomdeviceid").and_return(appium_log_fixture)
    expected_params = { "data" => { "r" => "SOTIMEOUT-browserstack-command_timeout_threshold_reached",
                                    "s" => { last_raw_log: "last_raw_log",
                                             last_request: "last_request",
                                             reason: "reason",
                                             session_limit: "7200" },
                                    "v" => [],
                                    "f" => "false" },
                        "device" => "randomdeviceid",
                        "session_id" => nil,
                        "url" => "false",
                        "user_id" => nil }
    expect(BrowserStack::Zombie).to receive(:push_logs).with("app-SOTIMEOUT-browserstack", "app_automate", expected_params)
    expect(Utils).to receive(:send_to_eds).with(@params, EdsConstants::APP_AUTOMATE_TEST_SESSIONS, false, "secondary_diagnostic_reason",  "SOTIMEOUT-browserstack-command_timeout_threshold_reached", true)
    device_manager.send(:check_and_push_failure_reason, device_id, @params)
  end

  it 'should push valid data to eds and zombie for sotimeout for missing params' do
    appium_log_fixture = "#{__dir__}/../fixtures/appium_command_timeout.log"
    expect(device_manager).to receive(:session_appium_logs_device).with("randomdeviceid").and_return(appium_log_fixture)
    @params.delete(:last_raw_log)
    expected_params = { "data" => { "r" => "SOTIMEOUT-browserstack-command_timeout_threshold_reached",
                                    "s" => { last_raw_log: nil,
                                             last_request: "last_request",
                                             reason: "reason",
                                             session_limit: "7200" },
                                    "v" => [],
                                    "f" => "false" },
                        "device" => "randomdeviceid",
                        "session_id" => nil,
                        "url" => "false",
                        "user_id" => nil }
    expect(BrowserStack::Zombie).to receive(:push_logs).with("app-SOTIMEOUT-browserstack", "app_automate", expected_params)
    expect(Utils).to receive(:send_to_eds).with(@params, EdsConstants::APP_AUTOMATE_TEST_SESSIONS, false, "secondary_diagnostic_reason",  "SOTIMEOUT-browserstack-command_timeout_threshold_reached", true)
    device_manager.send(:check_and_push_failure_reason, device_id, @params)
  end

  it 'should push correct data to zombie in case of codesigning error' do
    original_config_root = DeviceManager.class_variable_get(:@@settings)['config_root']
    DeviceManager.class_variable_get(:@@settings)['config_root'] = "#{__dir__}/../fixtures"
    @params[:reason] = false
    appium_log_fixture = "#{__dir__}/../fixtures/appium_codesign_issue.log"
    expect(device_manager).to receive(:session_appium_logs_device).with("randomdeviceid").and_return(appium_log_fixture)
    expect(BrowserStack::Zombie).to receive(:push_logs).with("wda-not-signed-start-error", "app_automate", anything)
    expect(IdeviceUtils).to receive(:screenshot)
    device_manager.send(:check_and_push_failure_reason, device_id, @params)
    DeviceManager.class_variable_get(:@@settings)['config_root'] = original_config_root
  end
end

describe "log_appium_memory_usage" do
  let(:device_manager) { DeviceManager }
  let(:device_id) { "123456" }
  let(:process_details) { 'app             48359   0.1  0.6  4684136  1024   ??' }
  let(:params) do
    { automate_session_id: '123fakestreet' }
  end

  context 'when shell command is succesfull' do
    it 'logs and pushes to zombie the memory usage in KBs' do
      allow(BrowserStack::Zombie).to receive(:push_logs)
      allow(OSUtils).to receive(:execute).and_return([process_details, 0])

      expect(process_details).to receive(:split).and_return('1024')
      expect(BrowserStack::Zombie).to receive(:push_logs)
      device_manager.send(:log_appium_memory_usage, device_id, params)
    end
  end

  context 'when shell comman fails' do
    it 'logs an error message' do
      allow(OSUtils).to receive(:execute).and_return([process_details, 0])

      device_manager.send(:log_appium_memory_usage, device_id, params)
    end
  end

  context 'when an error occurs' do
    it 'it is rescued and the error is logged' do
      allow(OSUtils).to receive(:execute).and_raise(StandardError.new('Error Message'))

      expect { device_manager.send(:log_appium_memory_usage, device_id, params) }.to_not raise_error
    end
  end
end

describe "session_appium_logs" do
  let(:device_manager) { DeviceManager }

  let(:device_id) do
    "randomdeviceid"
  end

  let(:device_config) do
    JSON.parse(fixture("config.json"))
  end

  before(:each) do
    DeviceManager.configure BrowserStack::Configuration.new.all
    allow(DeviceManager).to receive(:all_devices)
      .and_return({
        device_id => device_config
      })
  end

  it 'should return appiumlogs path for a device' do
    path = device_manager.send(:session_appium_logs_device, device_id)
    expect(path).to eq("/var/log/browserstack/appium_8082.log")
  end
end

describe "feature usage" do
  let(:device_manager) { DeviceManager }

  it "init_feature_usage" do
    features_to_track = %w[airplaneMode customMedia gpsLocation geoLocation resignFalse customNetwork otherApps orientation]

    feature_usage = device_manager.send(:init_feature_usage, {})

    expect(feature_usage).not_to be_nil
    features_to_track.each do |feature|
      expect(feature_usage[feature][:success]).to eq("disabled")
    end
  end
end

describe "download and install other apps" do
  let(:device_manager) { DeviceManager }
  options = {
    certificate: "dummy",
    unsigned_app_url: "url",
    app_hashed_id: "hashed_id",
    device_id: "Samsung Galaxy S10",
    is_framework: "false",
    product: "app_automate",
    codesigner_host: "codesigner_host",
    secondary_codesigner_host: "secondary_codesigner_host",
    rails_host: 'rails_host',
    backfill: true,
    s3_config: "s3_config"
  }

  backfill_object = {
    "app" => {
      "unsigned_app_url" => "app_unsigned_url",
      "app_hashed_id" => "app_hashed_id",
      "codesigner_host" => "codesigner_host",
      "secondary_codesigner_host" => "secondary_codesigner_host",
      "certificate" => "certificate_name",
      "s3_config" => "s3_config"
    },
    "test_app" => {
      "unsigned_app_url" => "test_app_unsigned_url",
      "app_hashed_id" => "test_app_hashed_id",
      "codesigner_host" => "codesigner_host",
      "secondary_codesigner_host" => "secondary_codesigner_host",
      "certificate" => "certificate_name",
      "s3_config" => "s3_config"
    },
    "other_apps" => [
          {
            "unsigned_app_url" => "url",
            "app_hashed_id" => "hashed_id",
            "codesigner_host" => "codesigner_host",
            "secondary_codesigner_host" => "secondary_codesigner_host",
            "certificate" => "dummy",
            "bundle_id" => "com.example.browserstack",
            "rails_host" => "rails_host",
            "s3_config" => "s3_config"
          }
      ],
    "mid_session_install_apps" => [
        {
          "unsigned_app_url" => "mid_session_url",
          "app_hashed_id" => "hashed_id",
          "codesigner_host" => "codesigner_host",
          "secondary_codesigner_host" => "secondary_codesigner_host",
          "certificate" => "dummy",
          "bundle_id" => "com.example.browserstack",
          "rails_host" => "rails_host",
          "s3_config" => "s3_config"
        }
      ]
  }
  let(:other_apps) do
    [{
      "download_timeout" => "url_first",
      "hashed_id" => "hashed_id_first",
      "download_url" => "url",
      "bundle_id" => "com.example.browserstack"
    },
     {
       "download_timeout" => "url_second",
       "hashed_id" => "hashed_id_second",
       "download_url" => "url",
       "bundle_id" => "com.example.browserstack"
     }]
  end

  let(:mid_session_install_apps) do
    [{
      "download_timeout" => "url_first",
      "hashed_id" => "hashed_id_first",
      "download_url" => "url",
      "bundle_id" => "com.example.browserstack"
    }]
  end

  it 'should call backfill_app when called with correct backfill options for other_apps' do
    device_manager.configure BrowserStack::Configuration.new.all
    params = {
      "genre" => "app_automate",
      "other_apps" => backfill_object["other_apps"].to_json
    }
    expect(device_manager).to receive(:get_backfill_obj).and_return(backfill_object["other_apps"].to_a)
    expect(device_manager).to receive(:backfill_app).with(options).and_return("s3_url")
    expect(device_manager).to receive(:download_and_install_app).with(any_args).and_return(true)
    bundle_ids = device_manager.send(:download_and_install_dependent_apps, params['other_apps'], "Samsung Galaxy S10", params, "abcd1313")
    expect(bundle_ids).to eq(["com.example.browserstack"])
  end

  it 'should not call backfill_app when there is no backfill params for other_apps' do
    device_manager.configure BrowserStack::Configuration.new.all
    params = {
      "genre" => "app_automate",
      "other_apps" => backfill_object["other_apps"].to_json
    }
    expect(device_manager).to receive(:get_backfill_obj).and_return(nil)
    expect(device_manager).not_to receive(:backfill_app).with(options)
    expect(device_manager).to receive(:download_and_install_app).with(any_args).and_return(true)
    bundle_ids = device_manager.send(:download_and_install_dependent_apps, params['other_apps'], "Samsung Galaxy S10", params, "abcd1313")
    expect(bundle_ids).to eq(["com.example.browserstack"])
  end

  it 'should save duplicate_other_apps_details if duplicate bundle in otherApp' do
    device_manager.configure BrowserStack::Configuration.new.all
    params = {
      "genre" => "app_automate",
      "other_apps" => other_apps.to_json
    }
    expect(device_manager).to receive(:get_backfill_obj).and_return(nil)
    expect(device_manager).not_to receive(:backfill_app).with(options)
    expect(device_manager).to receive(:download_and_install_app).twice.with(any_args).and_return("path")
    bundle_ids = device_manager.send(:download_and_install_dependent_apps, params['other_apps'], "Samsung Galaxy S10", params, "abcd1313")
    expect(bundle_ids).to eq(["com.example.browserstack", "com.example.browserstack"])
    expect(params["duplicate_other_apps_details"]).to eq({ "hashed_id_second" => { app_path: "path", bundle_id: "com.example.browserstack" } })
  end

  it 'should call backfill_app when called with correct backfill options' do
    options[:unsigned_app_url] = "mid_session_url"
    device_manager.configure BrowserStack::Configuration.new.all
    params = {
      "genre" => "app_automate",
      "mid_session_install_apps" => backfill_object["mid_session_install_apps"].to_json
    }
    expect(device_manager).to receive(:get_backfill_obj).and_return(backfill_object["mid_session_install_apps"].to_a)
    expect(device_manager).to receive(:backfill_app).with(options).and_return("s3_url")
    expect(device_manager).to receive(:download_and_install_app).with(any_args).and_return(true)
    bundle_ids = device_manager.send(:download_and_install_dependent_apps, params['mid_session_install_apps'], "Samsung Galaxy S10", params, "abcd1313")
    expect(bundle_ids).to eq(["com.example.browserstack"])
  end

  it 'should not call backfill_app when there is no backfill params for mid_session_install_apps' do
    device_manager.configure BrowserStack::Configuration.new.all
    params = {
      "genre" => "app_automate",
      "mid_session_install_apps" => backfill_object["mid_session_install_apps"].to_json
    }
    expect(device_manager).to receive(:get_backfill_obj).and_return(nil)
    expect(device_manager).not_to receive(:backfill_app).with(options)
    expect(device_manager).to receive(:download_and_install_app).with(any_args).and_return(true)
    bundle_ids = device_manager.send(:download_and_install_dependent_apps, params['mid_session_install_apps'], "Samsung Galaxy S10", params, "abcd1313")
    expect(bundle_ids).to eq(["com.example.browserstack"])
  end

  it 'should save duplicate_other_apps_details if app is a mid_session cap' do
    device_manager.configure BrowserStack::Configuration.new.all
    params = {
      "genre" => "app_automate",
      "mid_session_install_apps" => mid_session_install_apps.to_json
    }
    expect(device_manager).to receive(:get_backfill_obj).and_return(nil)
    expect(device_manager).not_to receive(:backfill_app).with(options)
    expect(device_manager).to receive(:download_and_install_app).once.with(any_args).and_return("path")
    bundle_ids = device_manager.send(:download_and_install_dependent_apps, params['mid_session_install_apps'], "Samsung Galaxy S10", params, "abcd1313", "mid_session_install_apps")
    expect(bundle_ids).to eq(["com.example.browserstack"])
    expect(params["duplicate_other_apps_details"]).to eq({ "hashed_id_first" => { app_path: "path", bundle_id: "com.example.browserstack" } })
  end
end

describe "switch_appium" do
  let(:device_manager) { DeviceManager }
  let(:mock_appium_server) { double('mock_appium_server') }

  before do
    allow(BrowserStack::AppiumServer).to receive(:new).and_return(mock_appium_server)
  end

  it "should switch appium to given appium version" do
    params = {
      "genre" => "app_automate",
      "appium_version" => "1.16.0",
      :event_hash => {
        "absolute_start_time" => 1596602978263,
        "fire_cmd.write_session_info" => 0,
        "fire_cmd.unlock_device" => 1,
        "fire_cmd.allow_popups" => 0,
        "fire_cmd.enable_cross_site_tracking" => 0,
        "fire_cmd.set_orientation" => 2
      }
    }

    current_device = {
      "os" => "ios",
      "device_name" => "iPhone10,4",
      "online" => true,
      "web_driver_agent_xctestrun_file" => { "1.16.0" => "somewhere", "1.17.0" => "there" },
      "web_driver_agent_created_at" => { "1.16.0" => "2019-05-10 06:38:35 +0000", "1.17.0" => "2019-05-11 05:57:23 +0000" },
      "current_appium_version" => "1.16.0"
    }

    expect(Utils).to receive(:mark_event_start).with('fire_cmd.appium_create_time', params[:event_hash])
    expect(BrowserStack::WebDriverAgent).to receive(:stop).with("iPhone10,4")
    expect(BrowserStack::IPhone).to receive(:uninstall_wda).with("iPhone10,4")
    expect(mock_appium_server).to receive(:start_server_for_version).with("1.16.0", false, 30, "debug", nil, nil, nil)
    expect(Utils).to receive(:mark_event_end).with('fire_cmd.appium_create_time', params[:event_hash])
    expect(device_manager).to receive(:device_configuration_check).and_return('15.4')

    device_manager.send(:switch_appium, params, "iPhone10,4", current_device, "1.16.0")
  end
end

describe "app_strings" do
  let(:language) { "en" }
  let(:session_id) { "session_id" }
  let(:device_manager) { DeviceManager }
  let(:device) { "1234" }

  before(:each) do
    allow(DeviceManager).to receive(:session_file)
    allow(File).to receive(:exist?).and_return(true)
    allow(File).to receive(:read).and_return({ "downloaded_app_path": "/tmp/xyz.app" , 'automate_session_id' => session_id }.to_json)
  end

  it "should return app strings when language is passed as en and strings_file is empty" do
    expect(Dir).to receive(:exist?).with("/tmp/xyz.app/en.lproj").and_return(true)
    expect(Dir).to receive(:glob).with(["/tmp/xyz.app/en.lproj/*.stringsdict", "/tmp/xyz.app/en.lproj/*.strings"]).and_return(["/tmp/xyz.app/en.lproj/InfoPlist.strings"])
    expect(Utils).to receive(:parse_plist_and_convert_to_json).with("/tmp/xyz.app/en.lproj/InfoPlist.strings").and_return({ "app_strings" => "app_strings" })

    response = device_manager.send(:app_strings, device, session_id, "en", "")
    expect(response).to eq({ "app_strings" => "app_strings" })
  end

  it "should return app strings when language is passed as en and strings_file is not empty" do
    expect(Dir).to receive(:exist?).with("/tmp/xyz.app/en.lproj").and_return(true)
    expect(Utils).to receive(:parse_plist_and_convert_to_json).with("/tmp/xyz.app/en.lproj/InfoPlist.strings").and_return({ "app_strings" => "app_strings" })

    response = device_manager.send(:app_strings, device, session_id, "en", "InfoPlist.strings")
    expect(response).to eq({ "app_strings" => "app_strings" })
  end

  it "should return exception when app path is not present" do
    allow(File).to receive(:read).and_return({ "downloaded_app_path": "" , 'automate_session_id' => session_id }.to_json)
    allow(File).to receive(:exist?).and_return(false)

    expect { device_manager.send(:app_strings, device, session_id, "en", "InfoPlist.strings") }.to raise_error(RuntimeError)
  end

  it "should return AppStringsCommandError exception when lproj_path does not exist" do
    expect(Dir).to receive(:exist?).with("/tmp/xyz.app/en.lproj").and_return(false)

    expect { device_manager.send(:app_strings, device, session_id, "en", "InfoPlist.strings") }.to raise_error(AppStringsCommandError)
  end

  it "should return AppStringsCommandError exception when strings file sent does not exist" do
    expect(Dir).to receive(:exist?).with("/tmp/xyz.app/en.lproj").and_return(true)
    allow(File).to receive(:exist?).with("/tmp/xyz.app/en.lproj/InfoPlist.strings").and_return(false)

    expect { device_manager.send(:app_strings, device, session_id, "en", "InfoPlist.strings") }.to raise_error(AppStringsCommandError)
  end

  it "should return empty response when no strings file present" do
    expect(Dir).to receive(:exist?).with("/tmp/xyz.app/en.lproj").and_return(true)
    expect(Dir).to receive(:glob).with(["/tmp/xyz.app/en.lproj/*.stringsdict", "/tmp/xyz.app/en.lproj/*.strings"]).and_return([])

    expect(device_manager.send(:app_strings, device, session_id, "en", "")).to eq({})
  end

  it "should return AppStringsCommandError exception when strings file is not parseable" do
    expect(Dir).to receive(:exist?).with("/tmp/xyz.app/en.lproj").and_return(true)
    expect(Dir).to receive(:glob).with(["/tmp/xyz.app/en.lproj/*.stringsdict", "/tmp/xyz.app/en.lproj/*.strings"]).and_return(["/tmp/xyz.app/en.lproj/InfoPlist.strings"])
    expect(Utils).to receive(:parse_plist_and_convert_to_json).with("/tmp/xyz.app/en.lproj/InfoPlist.strings").and_raise(RuntimeError)

    expect { device_manager.send(:app_strings, device, session_id, "en", "") }.to raise_error(AppStringsCommandError)
  end
end

describe "install app" do
  let(:app_hashed_id) { "1234" }
  let(:app_path) { "path_to_other_app" }
  let(:session_id) { "session_id" }
  let(:device_manager) { DeviceManager }

  it "should install app when hashed_id passed is duplicate" do
    device = "1234"
    allow(DeviceManager).to receive(:session_file)
    allow(File).to receive(:exist?).and_return(true)
    allow(File).to receive(:read).and_return({ "duplicate_other_apps_details": { app_hashed_id => { "app_path" => app_path, "bundle_id" => "bundle_id" } }, 'automate_session_id' => session_id }.to_json)
    expect(DeviceManager).to receive('install_app_and_verify').with(device, {
      path: app_path, id: "bundle_id",
      retry_count: 1,
      session_id: session_id,
      app_type: "dependent"
    })
    expect(BrowserStack::AppSettingsUtil).to receive(:parse_settings_bundle)
    expect(Utils).to receive(:write_to_file_with_lock).and_return(true)

    expect(device_manager.send(:install_app, device, session_id, app_hashed_id)).to eq("")
  end

  it "should install app when hashed_id passed is in otherApps but not duplicate" do
    device = "1234"
    allow(DeviceManager).to receive(:session_file)
    allow(File).to receive(:exist?).and_return(true)
    allow(File).to receive(:read).and_return({ "unique_other_apps_details": { app_hashed_id => app_path } }.to_json)
    expect(BrowserStack::AppSettingsUtil).not_to receive(:parse_settings_bundle)
    expect(Utils).not_to receive(:write_to_file_with_lock)
    expect(device_manager.send(:install_app, device, session_id, app_hashed_id)).to eq("The app_url 'bs://#{app_hashed_id}' passed in the installApp command is already installed on the device")
  end

  it "should give error when hashed_id passed is not valid" do
    device = "1234"
    allow(DeviceManager).to receive(:session_file)
    allow(File).to receive(:exist?).and_return(true)
    allow(File).to receive(:read).and_return({}.to_json)

    expect { device_manager.send(:install_app, device, session_id, app_hashed_id) }.to raise_error(AppInstallCommandFailedException)
  end

  it "should give error when hashed_id passed is valid but install fails" do
    device = "1234"
    allow(DeviceManager).to receive(:session_file)
    allow(File).to receive(:exist?).and_return(true)
    allow(File).to receive(:read).and_return({ "duplicate_other_apps_details": { app_hashed_id => { "app_path" => app_path, "bundle_id" => "bundle_id" } }, 'automate_session_id' => session_id }.to_json)

    expect(DeviceManager).to receive('install_app_and_verify').with(device, {
      path: app_path, id: "bundle_id",
      retry_count: 1,
      session_id: session_id,
      app_type: "dependent"
    }).and_raise("ios-deploy failed: install-0,verify-253")

    expect { device_manager.send(:install_app, device, session_id, app_hashed_id) }.to raise_error(RuntimeError)
  end
end

describe "#install_app_and_verify" do
  let(:device_manager) { DeviceManager }
  let(:device) { '1234' }
  let(:options) { { path: "someApp.ipa", id: "some.bundle.id" } }

  it "should install app successfully using ios-deploy" do
    expect(BrowserStack::OSUtils).to receive(:safe_execute).with(anything, ["--id", "1234", "-b", "someApp.ipa"], anything, anything).and_return(["install_output", 0])
    expect(BrowserStack::OSUtils).to receive(:safe_execute).with(IOS_DEPLOY.to_s, ["--id", "1234", "-e", "-1", "some.bundle.id"], true).and_return(["verify_output", 0])

    device_manager.send(:install_app_and_verify, device, options.merge({ retry_count: 1 }))
  end

  it "should install app successfully using ideviceinstaller when install_via_ideviceinstaller is true" do
    expect(BrowserStack::OSUtils).to receive(:safe_execute).with("ideviceinstaller", ["-u", "1234", "-i", "someApp.ipa"], anything, anything).and_return(["install_output", 0])
    expect(BrowserStack::OSUtils).to receive(:safe_execute).with(IOS_DEPLOY.to_s, ["--id", "1234", "-e", "-1", "some.bundle.id"], true).and_return(["verify_output", 0])

    device_manager.send(:install_app_and_verify, device, options.merge({ retry_count: 1, install_via_ideviceinstaller: true }))
  end

  it "should retry install app if it fails in first retry" do
    expect(BrowserStack::OSUtils).to receive(:safe_execute).with(anything, ["--id", "1234", "-b", "someApp.ipa"], anything, anything).and_return(["install_output1", 253], ["install_output2", 0])
    expect(BrowserStack::OSUtils).to receive(:safe_execute).with(IOS_DEPLOY.to_s, ["--id", "1234", "-e", "-1", "some.bundle.id"], true).and_return(["verify_output", 0], ["verify_output2", 0])
    expect(BrowserStack::Zombie).to receive(:push_logs).and_return(true)

    device_manager.send(:install_app_and_verify, device, options.merge({ retry_count: 1 }))
  end

  it "should install app successfully if install failed, ios-deploy and ideviceinstaller verification passes" do
    expect(BrowserStack::OSUtils).to receive(:safe_execute).with(anything, ["--id", "1234", "-b", "someApp.ipa"], anything, anything).and_return(["install_output", 253])
    expect(BrowserStack::OSUtils).to receive(:safe_execute).with(IOS_DEPLOY.to_s, ["--id", "1234", "-e", "-1", "some.bundle.id"], true).and_return(["verify_output", 0])
    expect(IdeviceUtils).to receive(:app_installed?).and_return(true)

    device_manager.send(:install_app_and_verify, device, options.merge({ retry_count: 0 }))
  end

  it "should not install app successfully if install failed, ios-deploy verification passes but ideviceinstaller verification fails" do
    expect(BrowserStack::OSUtils).to receive(:safe_execute).with(anything, ["--id", "1234", "-b", "someApp.ipa"], anything, anything).and_return(["install_output", 253])
    expect(BrowserStack::OSUtils).to receive(:safe_execute).with(IOS_DEPLOY.to_s, ["--id", "1234", "-e", "-1", "some.bundle.id"], true).and_return(["verify_output", 0])
    expect(IdeviceUtils).to receive(:app_installed?).and_return(false)

    expect { device_manager.send(:install_app_and_verify, device, options.merge({ retry_count: 0 })) }.to raise_error(RuntimeError)
  end

  it "should not install app successfully if install failed, ios-deploy verification fails" do
    expect(BrowserStack::OSUtils).to receive(:safe_execute).with(anything, ["--id", "1234", "-b", "someApp.ipa"], anything, anything).and_return(["install_output", 253])
    expect(BrowserStack::OSUtils).to receive(:safe_execute).with(IOS_DEPLOY.to_s, ["--id", "1234", "-e", "-1", "some.bundle.id"], true).exactly(3).times.and_return(["verify_output", 1])
    expect(IdeviceUtils).to_not receive(:app_installed?)

    expect { device_manager.send(:install_app_and_verify, device, options.merge({ retry_count: 0 })) }.to raise_error(RuntimeError)
  end

  it "should not install app successfully with ideviceinstaller if install failed, ios-deploy verification fails" do
    expect(BrowserStack::OSUtils).to receive(:safe_execute).with("ideviceinstaller", ["-u", "1234", "-i", "someApp.ipa"], anything, anything).and_return(["install_output", 253])
    expect(BrowserStack::OSUtils).to receive(:safe_execute).with(IOS_DEPLOY.to_s, ["--id", "1234", "-e", "-1", "some.bundle.id"], true).exactly(3).times.and_return(["verify_output", 1])
    expect(IdeviceUtils).to_not receive(:app_installed?)

    expect { device_manager.send(:install_app_and_verify, device, options.merge({ retry_count: 0, install_via_ideviceinstaller: true })) }.to raise_error(RuntimeError)
  end
end

describe '.append_bundle_id_to_file' do
  let(:device) { 'test_device' }
  let(:bundle_id) { 'com.example.app' }
  let(:tmp_dir_path) { '/tmp' }
  let(:all_installed_apps) { "#{tmp_dir_path}/#{device}_installed_apps/ALL_INSTALLED_APPS" }
  let(:device_manager) { DeviceManager }

  before do
    stub_const('TMP_DIR_PATH', tmp_dir_path)
    allow(FileUtils).to receive(:mkdir_p)
    allow(File).to receive(:directory?).and_return(false)
    allow(File).to receive(:open).and_yield(double('file').as_null_object)
    allow(BrowserStack).to receive(:logger).and_return(double('logger').as_null_object)
  end

  it 'creates the directory if it does not exist' do
    expect(FileUtils).to receive(:mkdir_p).with(all_installed_apps)
    device_manager.send(:append_bundle_id_to_file, device, bundle_id)
  end

  it 'appends the bundle_id to the file' do
    file_double = double('file')
    expect(File).to receive(:open).with(all_installed_apps, 'a').and_yield(file_double)
    expect(file_double).to receive(:puts).with(bundle_id)
    device_manager.send(:append_bundle_id_to_file, device, bundle_id)
  end

  it 'logs a success message' do
    expect(BrowserStack.logger).to receive(:info).with("Successfully appended bundle_id: #{bundle_id} to file: #{all_installed_apps}}")
    device_manager.send(:append_bundle_id_to_file, device, bundle_id)
  end

  context 'when an error occurs' do
    before do
      allow(File).to receive(:open).and_raise(StandardError.new('Test error'))
    end

    it 'logs an error message' do
      expect(BrowserStack.logger).to receive(:error).with("Failed to append bundle_id: #{bundle_id} to file: #{all_installed_apps}. Error: Test error")
      device_manager.send(:append_bundle_id_to_file, device, bundle_id)
    end
  end
end

describe ".update_app_and_verify" do
  let(:device_manager) { DeviceManager }
  let(:device) { '1234' }
  let(:options) { { retry_count: 1 } }
  it "should call IdeviceUtils update_app method" do
    expect(IdeviceUtils).to receive(:update_app)
    allow(IdeviceUtils).to receive(:app_version)
    device_manager.send(:update_app_and_verify, device, options)
  end

  it "should call throw AppUpdateError on failure" do
    expect(IdeviceUtils).to receive(:update_app).twice
    allow(IdeviceUtils).to receive(:app_version).and_return('1.1.1')
    expect { device_manager.send(:update_app_and_verify, device, options) }.to raise_error(AppUpdateError)
  end
end

describe "apply_network_simulation" do
  let(:device_manager) { DeviceManager }
  let(:mock_network_simulator) { double('mock_network_simulator') }

  before do
    allow(NetworkSimulator).to receive(:new).and_return(mock_network_simulator)
    expect(device_manager).to receive(:get_network_simulator).and_return(NetworkSimulator.new("iPhone10", 1234))
  end

  it "should raise error if working interface not found while enabling no_network mode" do
    params = { network_wifi: "false" }
    expect(mock_network_simulator).to receive(:reset_network_simulation).twice.and_return(nil)
    expect(mock_network_simulator).to receive(:apply_network_mode).with("offline").and_raise("no working network interface found for the device iPhone10")
    expect(BrowserStack::Zombie).to receive(:push_logs)
    expect { device_manager.send(:apply_network_simulation, "iPhone10", params) }.to raise_exception
  end

  it "should not raise error if working interface is found while enabling no_network mode" do
    params = { network_wifi: "false" }
    result = nil
    expect(mock_network_simulator).to receive(:reset_network_simulation).once.and_return(nil)
    expect(mock_network_simulator).to receive(:apply_network_mode).with("offline").and_return(true)
    expect(BrowserStack::Zombie).not_to receive(:push_logs)

    expect { result = device_manager.send(:apply_network_simulation, "iPhone10", params) }.not_to raise_exception
    expect(result).to eq({})
  end

  it "should raise error if working interface not found while reseting" do
    params = { network_reset: "true" }
    expect(mock_network_simulator).to receive(:reset_network_simulation).once.ordered.and_raise("no working network interface found for the device iPhone10")
    expect(mock_network_simulator).to receive(:reset_network_simulation).once.ordered.and_return(nil)

    expect(BrowserStack::Zombie).to receive(:push_logs)
    expect { device_manager.send(:apply_network_simulation, "iPhone10", params) }.to raise_exception
  end

  it "should not raise error if working interface is found found while disabling no_network mode" do
    params = { network_reset: "true" }
    expect(mock_network_simulator).to receive(:reset_network_simulation).and_return(nil)

    expect(BrowserStack::Zombie).not_to receive(:push_logs)
    expect { device_manager.send(:apply_network_simulation, "iPhone10", params) }.not_to raise_exception
  end

  it "should not raise error while applying throttling rules" do
    params = { network_bw_dwld: 1000 }
    expect(mock_network_simulator).to receive(:reset_network_simulation).and_return(nil)
    expect(mock_network_simulator).to receive(:setup_throttling_rules).and_return(nil)

    expect(BrowserStack::Zombie).not_to receive(:push_logs)
    expect { device_manager.send(:apply_network_simulation, "iPhone10", params) }.not_to raise_exception
  end
end

describe ".parse_app_automate_custom_params" do
  let(:device_manager) { DeviceManager }
  it 'should parse custom params and return' do
    params = {
      "app_automate_custom_params" => { "skip_set_orientation" => "true" }.to_json
    }

    result = device_manager.send(:parse_app_automate_custom_params, params)
    expect(result).to eq({ "skip_set_orientation" => "true" })
  end

  it 'should parse custom params and return empty hash if parse failed' do
    params = {
      "app_automate_custom_params" => nil
    }

    result = device_manager.send(:parse_app_automate_custom_params, params)
    expect(result).to eq({})
  end
end

describe "crash report" do
  let(:session_id) { "session_id" }
  let(:device) { "1234" }
  let(:device_id) { "abcdxyz" }

  before(:each) do
    allow(DeviceManager).to receive(:session_file)
    allow(File).to receive(:exist?).and_return(true)
    allow(File).to receive(:read).and_return({ "downloaded_app_path": "/tmp/xyz.app" , 'automate_session_id' => session_id }.to_json)
  end

  it "should return num of crash reports when there are no errors" do
    expect(PlistBuddy).to receive(:get_value_of_key).with("/tmp/xyz.app/Info.plist", "CFBundleExecutable").and_return("app_name")
    expect(IdeviceUtils).to receive(:get_crash_report_from_device).with(device, ["app_name"], session_id, true).and_return(["/tmp/crash-reports_device_id/app_name-1.ips", "/tmp/crash-reports_device_id/app_name-2.ips"])

    expect(DeviceManager.crash_reports(device, session_id)).to eq [2, "app_name"]
  end

  it "should return num of crash reports when there are no errors with app having spaces" do
    allow(DeviceManager).to receive(:session_file)
    allow(File).to receive(:exist?).and_return(true)
    allow(File).to receive(:read).and_return({ "downloaded_app_path": "/tmp/Space App.app" , 'automate_session_id' => session_id }.to_json)

    expect(PlistBuddy).to receive(:get_value_of_key).with("#{Shellwords.escape('/tmp/Space App.app')}/Info.plist", "CFBundleExecutable").and_return("app_name")
    expect(IdeviceUtils).to receive(:get_crash_report_from_device).with(device, ["app_name"], session_id, true).and_return(["/tmp/crash-reports_device_id/app_name-1.ips", "/tmp/crash-reports_device_id/app_name-2.ips"])

    expect(DeviceManager.crash_reports(device, session_id)).to eq [2, "app_name"]
  end

  it "should return exception if get_crash_report_from_device throws an exception" do
    expect(PlistBuddy).to receive(:get_value_of_key).with("/tmp/xyz.app/Info.plist", "CFBundleExecutable").and_return("app_name")
    expect(IdeviceUtils).to receive(:get_crash_report_from_device).with(device, ["app_name"], session_id, true).and_raise("idevicecrashreport fetch failed")

    expect { DeviceManager.crash_reports(device, session_id) }.to raise_error("idevicecrashreport fetch failed")
  end
end

describe "crash report upload on stop" do
  let(:params) do
    {
      genre: "app_automate",
      automate_session_id: "session_id"

    }
  end
  let(:device) { "1234" }
  let(:crash_reports_dir) { "/tmp/crash-reports_#{device}" }
  let(:device_manager) { DeviceManager }

  before(:each) do
    device_manager.configure BrowserStack::Configuration.new.all
    allow_any_instance_of(DeviceManager).to receive(:check_wda_iproxy_status)
    allow(device_manager).to receive(:device_configuration_check).with(device).and_return(nil)
    allow(device_manager).to receive(:log_appium_memory_usage)
    allow_any_instance_of(DeviceManager).to receive(:check_and_push_failure_reason)
    allow(File).to receive(:write)
    allow(PortManager).to receive(:stop_forwarding_port)
  end

  it "success flow with no errors and num of reports > 0" do
    expect(DeviceManager).to receive(:crash_reports).with(device, params[:automate_session_id]).and_return([2, "app_name"])
    expect(BrowserStack::Zombie).not_to receive(:push_logs).with("idevicecrashreport-fetch-failed", "anything", { "device" => device, "session_id" => params[:automate_session_id] })
    expect(Utils).to receive(:create_upload_request_with_metadata).with("/tmp/crash_report_#{params[:automate_session_id]}.zip", params, "crash-report", "crash", { num_crash_reports: 2.to_s }, anything)
    expect(Utils).to receive(:send_to_eds).with(anything, EdsConstants::APP_AUTOMATE_TEST_SESSIONS, true)
    expect(FileUtils).to receive(:rm_rf).at_least(:once).with(crash_reports_dir)

    DeviceManager.stop(device, params)
  end

  it "flow with errors from crash_reports" do
    expect(DeviceManager).to receive(:crash_reports).with(device, params[:automate_session_id]).and_raise("idevicecrashreport fetch failed")
    expect(BrowserStack::Zombie).to receive(:push_logs).with("idevicecrashreport-fetch-failed", "idevicecrashreport fetch failed", { "device" => device, "session_id" => params[:automate_session_id] })
    expect(Utils).not_to receive(:create_upload_request_with_metadata).with("/tmp/crash_report_#{params[:automate_session_id]}.zip", params, "crash-report", "crash", { num_crash_reports: 2.to_s }, anything)
    expect(FileUtils).to receive(:rm_rf).at_least(:once).with(crash_reports_dir)
    expect(Utils).to receive(:send_to_eds).with({ feature_usage: { "appiumLogs" => { success: "disabled", exception: "" }, "crashLogs" => { success: "false", exception: "idevicecrashreport fetch failed" } }, hashed_id: params[:automate_session_id], timestamp: anything }, EdsConstants::APP_AUTOMATE_TEST_SESSIONS, true)

    DeviceManager.stop(device, params)
  end

  it "flow with errors from crash_reports when app is not found on machine" do
    expect(DeviceManager).to receive(:crash_reports).with(device, params[:automate_session_id]).and_raise("App not found at : /tmp/something.app, for crash reports.")
    expect(BrowserStack::Zombie).to receive(:push_logs).with("idevicecrashreport-app-not-found", anything, { "device" => device, "session_id" => params[:automate_session_id] })
    expect(BrowserStack::Zombie).not_to receive(:push_logs).with("idevicecrashreport-fetch-failed", anything, { "device" => device, "session_id" => params[:automate_session_id] })
    expect(FileUtils).to receive(:rm_rf).at_least(:once).with(crash_reports_dir)
    expect(Utils).not_to receive(:create_upload_request_with_metadata).with("/tmp/crash_report_#{params[:automate_session_id]}.zip", params, "crash-report", "crash", { num_crash_reports: 2.to_s }, anything)
    expect(Utils).to receive(:send_to_eds).with({ feature_usage: { "appiumLogs" => { success: "disabled", exception: "" }, "crashLogs" => { success: "false", exception: "App not found at : /tmp/something.app, for crash reports." } }, hashed_id: params[:automate_session_id], timestamp: anything }, EdsConstants::APP_AUTOMATE_TEST_SESSIONS, true)

    DeviceManager.stop(device, params)
  end

  it "sucess flow with crash_reports == 0" do
    expect(DeviceManager).to receive(:crash_reports).with(device, params[:automate_session_id]).and_return([0, "app_name"])
    expect(BrowserStack::Zombie).not_to receive(:push_logs).with("idevicecrashreport-fetch-failed", "app-automate", { "device" => device, "session_id" => params[:automate_session_id] })
    expect(Utils).not_to receive(:create_upload_request_with_metadata).with("/tmp/crash_report_#{params[:automate_session_id]}.zip", params, "crash-report", "crash", { num_crash_reports: 2.to_s }, anything)
    expect(FileUtils).to receive(:rm_rf).at_least(:once).with(crash_reports_dir)
    expect(Utils).to receive(:send_to_eds).with({ feature_usage: { "appiumLogs" => { success: "disabled", exception: "" }, "crashLogs" => { success: "true", exception: "", num_crash_reports: 0 } }, hashed_id: params[:automate_session_id], timestamp: anything }, EdsConstants::APP_AUTOMATE_TEST_SESSIONS, true)

    DeviceManager.stop(device, params)
  end
end

describe "check_wda_iproxy_status" do
  let(:device_manager) { DeviceManager }
  let(:device) { 'test_device' }
  let(:params) do
    {
      genre: "app_automate",
      'automate_session_id' => "session_id"
    }
  end
  let(:mock_wda_client) { double('mock_wda_client') }

  it "iproxy port not listening" do
    allow(device_manager).to receive(:device_configuration_check).and_return({ "webdriver_port" => "8084" })
    allow(WdaClient).to receive(:new).and_return(mock_wda_client)

    allow(mock_wda_client).to receive(:describe_running).and_return([true, "WDA running"])
    allow(BrowserStack::OSUtils).to receive(:is_port_listening?).and_return(false)
    wda_data = "session_type: #{params[:genre]} ; wda_running: true ; iproxy_running: false"
    expect(BrowserStack::Zombie).to receive(:push_logs).with("wda_iproxy_check_fail", wda_data,  { "session_id" => params['automate_session_id'], "device" => device }, nil, { "automate_session_id" => "session_id", :genre => "app_automate" })
    device_manager.send(:check_wda_iproxy_status, device, params)
  end

  it "wda not running" do
    allow(device_manager).to receive(:device_configuration_check).and_return({ "webdriver_port" => "8084" })
    allow(WdaClient).to receive(:new).and_return(mock_wda_client)

    allow(mock_wda_client).to receive(:describe_running).and_return([false, "WDA not running"])
    allow(BrowserStack::OSUtils).to receive(:is_port_listening?).and_return(false)
    wda_data = "session_type: #{params[:genre]} ; wda_running: false ; iproxy_running: false ; wda_status_description: WDA not running"

    expect(BrowserStack::Zombie).to receive(:push_logs).with("wda_iproxy_check_fail", wda_data,  { "session_id" => params['automate_session_id'], "device" => device }, nil, { "automate_session_id" => "session_id", :genre => "app_automate" })
    device_manager.send(:check_wda_iproxy_status, device, params)
  end

  it "iproxy and wda both running fine" do
    allow(device_manager).to receive(:device_configuration_check).and_return({ "webdriver_port" => "8084" })
    allow(WdaClient).to receive(:new).and_return(mock_wda_client)

    allow(mock_wda_client).to receive(:describe_running).and_return([true, "WDA running"])
    allow(BrowserStack::OSUtils).to receive(:is_port_listening?).and_return(true)

    expect(BrowserStack::Zombie).to_not receive(:push_logs)
    device_manager.send(:check_wda_iproxy_status, device, params)
  end

  context "EDS tracking" do
    before do
      allow(Time).to receive(:now).and_return(Time.at(1234567890))
    end

    it "sends data to EDS when iproxy port not listening" do
      allow(device_manager).to receive(:device_configuration_check).and_return({ "webdriver_port" => "8084" })
      allow(WdaClient).to receive(:new).and_return(mock_wda_client)
      allow(mock_wda_client).to receive(:describe_running).and_return([true, "WDA running"])
      allow(BrowserStack::OSUtils).to receive(:is_port_listening?).and_return(false)
      allow(BrowserStack::Zombie).to receive(:push_logs)

      wda_data = "session_type: #{params[:genre]} ; wda_running: true ; iproxy_running: false"
      expect(Utils).to receive(:send_to_eds).with(
        {
          secondary_diagnostic_reason: wda_data,
          hashed_id: "session_id",
          timestamp: 1234567890
        },
        'app_automate_test_sessions',
        true
      )

      device_manager.send(:check_wda_iproxy_status, device, params)
    end

    it "sends data to EDS when WDA not running" do
      allow(device_manager).to receive(:device_configuration_check).and_return({ "webdriver_port" => "8084" })
      allow(WdaClient).to receive(:new).and_return(mock_wda_client)
      allow(mock_wda_client).to receive(:describe_running).and_return([false, "WDA not running"])
      allow(BrowserStack::OSUtils).to receive(:is_port_listening?).and_return(false)
      allow(BrowserStack::Zombie).to receive(:push_logs)

      wda_data = "session_type: #{params[:genre]} ; wda_running: false ; iproxy_running: false ; wda_status_description: WDA not running"
      expect(Utils).to receive(:send_to_eds).with(
        {
          secondary_diagnostic_reason: wda_data,
          hashed_id: "session_id",
          timestamp: 1234567890
        },
        'app_automate_test_sessions',
        true
      )

      device_manager.send(:check_wda_iproxy_status, device, params)
    end

    it "doesn't send data to EDS when everything is running" do
      allow(device_manager).to receive(:device_configuration_check).and_return({ "webdriver_port" => "8084" })
      allow(WdaClient).to receive(:new).and_return(mock_wda_client)
      allow(mock_wda_client).to receive(:describe_running).and_return([true, "WDA running"])
      allow(BrowserStack::OSUtils).to receive(:is_port_listening?).and_return(true)

      expect(Utils).not_to receive(:send_to_eds)
      device_manager.send(:check_wda_iproxy_status, device, params)
    end
  end
end

describe "get_orientation" do
  let(:device_manager) { DeviceManager }
  let(:device) { 'test_device' }
  it "should not go  in rescue and return" do
    expect(device_manager).to receive(:device_configuration_check).and_return({ "webdriver_port" => "8084" })
    expect(WdaClient).to receive_message_chain(:new, :get_orientation).and_return(nil)
    device_manager.send(:get_orientation, device)
  end

  it "should give error and go to rescue" do
    expect(device_manager).to receive(:device_configuration_check).and_return({ "webdriver_port" => "8084" })
    expect_any_instance_of(WdaClient).to receive(:get_orientation).and_raise("some_random_error")
    expect_any_instance_of(WdaClient).to receive(:running?).and_return(nil)
    device_manager.send(:get_orientation, device)
  end
end

describe "kill_video_recording_processes" do
  let(:device_manager) { DeviceManager }
  let(:device) { 'test_device' }

  it "should kill all video related processes" do
    ["video_recording"].each do |process|
      expect(BrowserStack::OSUtils).to receive(:is_process_running?).with(process, device).and_return(true)
      expect(BrowserStack::OSUtils).to receive(:kill_process).with(process, device, "KILL")
    end
    device_manager.send(:kill_video_recording_processes, device)
  end

  it "should not kill video related processes not running" do
    ["video_recording"].each do |process|
      expect(BrowserStack::OSUtils).to receive(:is_process_running?).with(process, device).and_return(false)
      expect(BrowserStack::OSUtils).not_to receive(:kill_process).with(process, device, "KILL")
    end
    device_manager.send(:kill_video_recording_processes, device)
  end
end

describe "check_and_enable_paint_timing" do
  let(:device_manager) { DeviceManager }
  let(:state_files_dir) do
    "/usr/local/.browserstack/state_files"
  end
  let(:device_id) { "abcd" }
  let(:params) do
    {
      genre: "automate",
      session_id: SecureRandom.hex
    }
  end

  it 'should install test suite app if not present and run ui test' do
    allow(BrowserStackAppHelper).to receive(:browserstack_test_suite_present?).with(device_id).and_return(false)
    expect(BrowserStackAppHelper).to receive(:check_and_install_browserstack_test_suite).with(device_id)
    expect(BrowserStackAppHelper).to receive(:run_ui_test).with(device_id, :safari_experimental_feature_paint_timing_enable, session_id: params[:session_id])
    expect(FileUtils).to receive(:touch).with("#{state_files_dir}/paint_timing_enabled_#{device_id}")

    device_manager.send(:check_and_enable_paint_timing, device_id, params)
  end

  it 'should not install test suite app if present and run ui test' do
    allow(BrowserStackAppHelper).to receive(:browserstack_test_suite_present?).with(device_id).and_return(true)
    expect(BrowserStackAppHelper).to_not receive(:check_and_install_browserstack_test_suite).with(device_id)
    expect(BrowserStackAppHelper).to receive(:run_ui_test).with(device_id, :safari_experimental_feature_paint_timing_enable, session_id: params[:session_id])
    expect(FileUtils).to receive(:touch).with("#{state_files_dir}/paint_timing_enabled_#{device_id}")

    device_manager.send(:check_and_enable_paint_timing, device_id, params)
  end
end

describe "start_xctest_session" do
  let(:device_manager) { DeviceManager }
  let(:device) { "1224A23AF" }

  it "should return FIRECMD exception when device is in use" do
    expect(device_manager).to receive(:device_in_use?).and_return(true)
    expect { device_manager.start_xctest_session(device, {}) }.to raise_error(/in use by another user or cleanup/)
  end
end

describe "#is_syslog_hanged?" do
  let(:device_manager) { DeviceManager }
  let(:device) { "1224A23AF" }
  it 'should parse syslog correctly and push to zombie' do
    expect(BrowserStack::OSUtils).to receive(:execute).with("ps aux | grep #{device} | grep idevicesyslog | grep -v grep").and_return('sfdsf')
    expect(File).to receive(:file?).and_return(true)
    expect(BrowserStack::OSUtils).to receive(:execute).with("tail -1 /var/log/browserstack/syslog_1224A23AF.log | awk '{print $3 \" \" $4}'").and_return('[app_automate 1d21ea681b5be1f7f6cf279fe741ab28873309ac 2022-03-27 19:32:24:247] Mar 27 19:37:35 locationd[85270] <Notice>: {"msg":"client getting effective client name", "bundleId":"com.apple.Maps", "bundlePath":}')
    expect(BrowserStack::Zombie).to receive(:push_logs)
    device_manager.is_syslog_hanged?(device, { 'automation_session_id' => 'session_id', 'genre' => 'app_automate' })
  end
end

describe "#check_and_give_icloud_access" do
  let(:device_state) { double('DeviceState') }

  it "should forward traffic for certain urls" do
    socks5_forwarder_mock = instance_double('Privoxy::Socks5Forwarder')
    expect(device_state).to receive(:touch_icloud_access_file).and_return(true)
    expect(Privoxy::Socks5Forwarder).to receive(:new).and_return(socks5_forwarder_mock)
    expect(socks5_forwarder_mock).to receive(:forward_traffic)
    DeviceManager.check_and_give_icloud_access("abcd", { "icloud_access_enabled" => 'true' }, device_state)
  end
end

context 'apple_pay_setup in device manager' do
  let(:session_id) { "session_id" }
  let(:device) { "device_id" }

  it "should setup apple pay successfully" do
    expect(Utils).to receive(:mark_event_start).and_return(true)
    expect(Secure::ApplePay).to receive(:setup).and_return(true)
    expect(Utils).to receive(:mark_event_end).and_return(true)

    expect(DeviceManager.send(:apple_pay_setup, device, { feature_usage: {} })).to eq(nil)
  end
end

context 'fetch_bundle_id_from_session_file in device manager' do
  let(:session_id) { "session_id" }
  let(:device) { "device_id" }

  it "should return bundle id when fetch_bundle_id_from_session_file is called" do
    expect(DeviceManager).to receive(:session_file).at_least(:once)
    expect(File).to receive(:exist?).and_return(true)
    expect(File).to receive(:read).at_least(:once).and_return({ "app_testing_bundle_id": "bundle_id", 'automate_session_id' => session_id }.to_json)

    expect(DeviceManager.send(:fetch_bundle_id_from_session_file, device, session_id)).to eq("bundle_id")
  end

  it "should return empty bundle id when session_file is missing" do
    expect(DeviceManager).to receive(:session_file).at_least(:once)
    expect(File).to receive(:exist?).at_least(:once).and_return(false)

    expect(DeviceManager.send(:fetch_bundle_id_from_session_file, device, session_id)).to eq("")
  end

  it "should return empty bundle id when session_id does not match with sesion_file is called" do
    expect(DeviceManager).to receive(:session_file).at_least(:once)
    expect(File).to receive(:exist?).at_least(:once).and_return(true)
    expect(File).to receive(:read).and_return({ "app_testing_bundle_id": "bundle_id", 'automate_session_id' => "session1" }.to_json)

    expect(DeviceManager.send(:fetch_bundle_id_from_session_file, device, session_id)).to eq("")
  end
end

context "iOS 18 enterprise app" do
  let(:session_id) { 'test_session_id' }
  let(:app_details) { { url: 'http://example.com/app', bundle_id: 'com.example.app', display_name: 'Example App' } }
  let(:manifest_file_path) { '/tmp/test_session_id_manifest.xml' }
  let(:params) { { 'stats_aws_key' => 'key', 'stats_aws_secret' => 'secret', 'stats_aws_bucket' => 'bucket', 'automate_session_id' => session_id, 'stats_aws_region' => 'us-east-1', 'video_aws_region' => 'us-east-1', 'event_hash' => 'event_hash', 'genre' => 'genre' } }
  let(:device) { 'test_device' }
  let(:s3_url) { 'https://s3.amazonaws.com/bucket/test_session_id/test_session_id-manifest.xml' }

  describe '#create_manifest_file' do
    it 'creates a manifest file with the correct contents' do
      allow(File).to receive(:open).and_return(double(read: 'template content'))
      allow(ERB).to receive(:new).and_return(double(result: 'manifest content'))
      allow(Utils).to receive(:write_to_file)
      allow(BrowserStack.logger).to receive(:info)

      DeviceManager.create_manifest_file(session_id, app_details, manifest_file_path)

      expect(Utils).to have_received(:write_to_file).with(manifest_file_path, 'manifest content')
      expect(BrowserStack.logger).to have_received(:info).with("Writing Manifest contents: manifest content to #{manifest_file_path}")
    end
  end

  describe '#upload_manifest_file' do
    it 'uploads the manifest file to S3 and returns the URL' do
      allow(Utils).to receive(:mark_event_start)
      allow(Utils).to receive(:upload_file_to_s3).and_return([true, ''])
      allow(BrowserStack.logger).to receive(:info)
      allow(Utils).to receive(:mark_event_end)

      result = DeviceManager.upload_manifest_file(manifest_file_path, params)

      expect(result).to eq(s3_url)
      expect(Utils).to have_received(:mark_event_start).with('fire_cmd.upload_manifest_file', params[:event_hash])
      expect(Utils).to have_received(:upload_file_to_s3).with('key', 'secret', 'application/xml', manifest_file_path, 'public-read', s3_url, session_id, 'genre', nil, 300)
      expect(BrowserStack.logger).to have_received(:info).with("uploaded manifest to: #{s3_url}, ret: true, error: ")
      expect(Utils).to have_received(:mark_event_end).with('fire_cmd.upload_manifest_file', params[:event_hash])
    end

    it 'raises an error if the upload fails' do
      allow(Utils).to receive(:mark_event_start)
      allow(Utils).to receive(:upload_file_to_s3).and_return([false, 'error'])
      allow(BrowserStack::Zombie).to receive(:push_logs)
      allow(BrowserStack.logger).to receive(:info)
      allow(Utils).to receive(:mark_event_end)

      expect do
        DeviceManager.upload_manifest_file(manifest_file_path, params)
      end.to raise_error(RuntimeError, 'Error while uploading manifest file to S3: error')

      expect(BrowserStack::Zombie).to have_received(:push_logs).with('manifest-upload-failure', 'Failed to upload manifest file: error, ret: false', { 'session_id' => session_id })
    end
  end

  describe '#request_mdm_for_install_app' do
    it 'requests MDM to install the app' do
      allow(BrowserStack::IosMdmServiceClient).to receive(:configure)
      allow(BrowserStack::IosMdmServiceClient).to receive(:install_enterprise_application).and_return('success')
      allow(BrowserStack.logger).to receive(:info)

      DeviceManager.request_mdm_for_install_app(session_id, device, s3_url)

      expect(BrowserStack::IosMdmServiceClient).to have_received(:configure)
      expect(BrowserStack::IosMdmServiceClient).to have_received(:install_enterprise_application).with(device, s3_url)
    end
  end

  describe '#perform_mdm_enterprise_app_install' do
    it 'performs the MDM enterprise app install' do
      allow(Utils).to receive(:mark_event_start)
      allow(FileUtils).to receive(:touch)
      allow(DeviceManager).to receive(:create_manifest_file)
      allow(DeviceManager).to receive(:upload_manifest_file).and_return(s3_url)
      allow(DeviceManager).to receive(:request_mdm_for_install_app)
      allow(BrowserStack.logger).to receive(:info)
      allow(FileUtils).to receive(:rm_rf)
      allow(Utils).to receive(:mark_event_end)

      DeviceManager.perform_mdm_enterprise_app_install(session_id, device, app_details, params)

      expect(Utils).to have_received(:mark_event_start).with('fire_cmd.perform_mdm_enterprise_app_install', params[:event_hash])
      expect(DeviceManager).to have_received(:create_manifest_file).with(session_id, app_details, manifest_file_path)
      expect(DeviceManager).to have_received(:upload_manifest_file).with(manifest_file_path, params)
      expect(DeviceManager).to have_received(:request_mdm_for_install_app).with(session_id, device, s3_url)
      expect(FileUtils).to have_received(:rm_rf).with(manifest_file_path)
      expect(Utils).to have_received(:mark_event_end).with('fire_cmd.perform_mdm_enterprise_app_install', params[:event_hash])
    end

    it 'handles errors during the MDM enterprise app install' do
      allow(Utils).to receive(:mark_event_start)
      allow(FileUtils).to receive(:touch)
      allow(DeviceManager).to receive(:create_manifest_file).and_raise('error')
      allow(BrowserStack.logger).to receive(:info)
      allow(BrowserStack.logger).to receive(:error)
      allow(FileUtils).to receive(:rm_rf)
      allow(Utils).to receive(:mark_event_end)

      expect do
        DeviceManager.perform_mdm_enterprise_app_install(session_id, device, app_details, params)
      end.to raise_error(RuntimeError, 'MDM enterprise app install failed')

      expect(BrowserStack.logger).to have_received(:info).with('MDM enterprise app install failed')
      expect(BrowserStack.logger).to have_received(:error)
      expect(FileUtils).to have_received(:rm_rf).with(manifest_file_path)
      expect(Utils).to have_received(:mark_event_end).with('fire_cmd.perform_mdm_enterprise_app_install', params[:event_hash])
    end
  end

  describe "#install_main_app" do
    let(:device) { 'test_device_id' }
    let(:params) do
      {
        's3_app_url' => 'https://example.com/app.ipa',
        'app_testing_bundle_id' => 'com.example.app',
        'appDownloadTimeout' => 300,
        'automate_session_id' => 'session123'
      }
    end
    let(:downloaded_path) { '/tmp/downloaded_app.ipa' }

    before do
      allow(DeviceManager).to receive(:download_and_install_app).and_return(downloaded_path)
      allow(DeviceManager).to receive(:session_file).and_return('/tmp/session.json')
      allow(Utils).to receive(:write_to_file_with_lock)
      allow(DeviceManager).to receive(:write_session_info)
    end

    it 'updates params with downloaded app path and writes to session file' do
      expected_params = params.merge(
        'app_type' => 'main',
        'downloaded_app_path' => downloaded_path
      )

      expect(Utils).to receive(:write_to_file_with_lock)
        .with('/tmp/session.json', expected_params.to_json)

      DeviceManager.install_main_app(params, device)
    end

    it 'writes session info after installation' do
      expect(DeviceManager).to receive(:write_session_info)
        .with(device, hash_including('downloaded_app_path' => downloaded_path))

      DeviceManager.install_main_app(params, device)
    end

    it 'handles installation failures' do
      allow(DeviceManager).to receive(:download_and_install_app)
        .and_raise(StandardError.new('Download failed'))

      expect do
        DeviceManager.install_main_app(params, device)
      end.to raise_error(StandardError, 'Download failed')
    end

    it 'handles missing optional parameters gracefully' do
      params.delete('app_testing_bundle_id')
      params.delete('appDownloadTimeout')

      expect(DeviceManager).to receive(:download_and_install_app) do |_, app_details, *_rest|
        expect(app_details[:id]).to be_nil
        expect(app_details[:timeout]).to be_nil
        downloaded_path
      end

      DeviceManager.install_main_app(params, device)
    end
  end

  describe "#kill_app_install_process" do
    let(:device) { 'test_device_id' }
    let(:pid_file) { "/tmp/app_install_pid_test_device_id" }
    let(:logger) { double('logger') }

    before do
      allow(DeviceManager).to receive(:kill_app_install_process).and_call_original
      allow(BrowserStack).to receive(:logger).and_return(logger)
      allow(logger).to receive(:info).with(any_args)
      allow(Process).to receive(:kill)
      allow(File).to receive(:exist?).and_call_original
      allow(File).to receive(:read).and_call_original
      allow(File).to receive(:delete)
      allow(BSEnv).to receive(:debug?).and_return(true)
    end

    context "when pid file exists with valid pid" do
      it "kills the process and deletes the pid file" do
        File.write(pid_file, '12345')

        expect(Process).to receive(:kill).with(9, 12345)
        expect(File).to receive(:delete).with(pid_file)

        DeviceManager.kill_app_install_process(device)
      end
    end

    context "when pid file does not exist" do
      it "returns without performing any action" do
        allow(File).to receive(:exist?).with(pid_file).and_return(false)

        expect(Process).not_to receive(:kill)
        expect(File).not_to receive(:delete)

        DeviceManager.kill_app_install_process(device)
      end
    end
  end

  describe '#validate_mdm_app_install' do
    it 'validates the MDM app install' do
      allow(Utils).to receive(:mark_event_start)
      allow(IdeviceUtils).to receive(:app_installed?).and_return(true)
      allow(BrowserStack.logger).to receive(:info)
      allow(Utils).to receive(:mark_event_end)

      DeviceManager.validate_mdm_app_install(device, params)

      expect(Utils).to have_received(:mark_event_start).with('fire_cmd.validate_mdm_app_install', params[:event_hash])
      expect(IdeviceUtils).to have_received(:app_installed?).with(device, params['app_testing_bundle_id'])
      expect(BrowserStack.logger).to have_received(:info).with('App installed successfully')
      expect(BrowserStack.logger).to have_received(:info).with('validate_mdm_app_install done')
      expect(Utils).to have_received(:mark_event_end).with('fire_cmd.validate_mdm_app_install', params[:event_hash])
    end

    it 'retries if the app is not installed' do
      allow(Utils).to receive(:mark_event_start)
      allow(IdeviceUtils).to receive(:app_installed?).and_return(false, false, true)
      allow(BrowserStack.logger).to receive(:info)
      allow(Utils).to receive(:mark_event_end)

      DeviceManager.validate_mdm_app_install(device, params)

      expect(IdeviceUtils).to have_received(:app_installed?).exactly(3).times
      expect(BrowserStack.logger).to have_received(:info).with('App installed successfully')
      expect(BrowserStack.logger).to have_received(:info).with('validate_mdm_app_install done')
    end

    it 'raises an error if the app is not installed after retries' do
      allow(Utils).to receive(:mark_event_start)
      allow(IdeviceUtils).to receive(:app_installed?).and_return(false)
      allow(BrowserStack.logger).to receive(:info)
      allow(Utils).to receive(:mark_event_end)

      expect do
        DeviceManager.validate_mdm_app_install(device, params)
      end.to raise_error(RuntimeError, '[FIRECMD] App not found')

      expect(IdeviceUtils).to have_received(:app_installed?).exactly(10).times
    end
  end
end
