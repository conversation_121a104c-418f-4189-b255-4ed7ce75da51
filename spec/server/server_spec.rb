require_relative '../spec_helper'
require_relative '../../server/device_manager'
require_relative '../../lib/utils/utils'
require_relative '../../lib/utils/image_injector'
require_relative '../../lib/utils/file_injector'
require_relative '../../lib/utils/idevice_utils'
require '/usr/local/.browserstack/mobile-common/utils/app_patching_util'
require 'env_middleware'

include AppPatchingUtil

test_frameworks = ["xcuitest", "fluttertest"]

describe 'misc endpoints' do
  before do
    mock_hooter_instance
  end

  it 'should be running' do
    get '/'
    expect(last_response).to be_ok
  end

  it 'should reject requests with backticks in the params' do
    evil_code = CGI.escape("`sudo hack computer`")
    params = "device=123&bad_thing=#{evil_code}"
    get "/?#{params}"
    expect(last_response.status).to eql 400
    expect(last_response.body).to eql 'Illegal character(s) found in request params'
  end

  context 'DEVICE_ID_HEADER with SENSOR_MOCKER_HOST passed in request headers' do
    it 'should return 404 if the path is not whitelisted in the DEVICE_HEADER_ALLOWED_PATHS' do
      get "/stop", nil, { DEVICE_ID_HEADER => 'test_device', 'HTTP_HOST' => SENSOR_MOCKER_HOST }

      expect(last_response.status).to eql 404
    end
  end
end

describe 'GET /selenium_command' do
  let(:device) do
    "abcd"
  end

  before do
    mock_hooter_instance
    allow(EnvMiddleware).to receive(:process_start_request)
  end

  it 'should respond back as JSON the return value of DeviceManager#fire_cmd_start' do
    device_manager_return_value = { "some": "response" }
    expect(DeviceManager).to receive(:fire_cmd_start).and_return(device_manager_return_value)
    get "/selenium_command?device=#{device}"
    expect(last_response.status).to eql 200
    expect(last_response.body).to eql(device_manager_return_value.to_json)
  end

  it 'should give error when device is not part of the server, but return 200' do
    device = '_'
    error_message = "FATAL: Cannot find device #{device}."

    expect(DeviceManager)
      .to receive(:fire_cmd_start)
      .with(device, { 'device' => device, :event_hash => anything, 'session_start_events' => {} })
      .and_raise(RuntimeError, error_message)
    get "/selenium_command?device=#{device}"

    expect(last_response.status).to eql 200
    expect(last_response.body).to eql({
      error: "Got Error in selenium_command #{error_message}"
    }.to_json)
  end
end

test_frameworks.each do |test_framework|
  describe "GET /start_#{test_framework}_session" do
    let(:device) do
      "abcd"
    end

    before do
      mock_hooter_instance
      allow(EnvMiddleware).to receive(:process_start_request)
    end

    it 'should respond back {} as JSON on success' do
      device_manager_return_value = { "some" => "response" }
      expect(DeviceManager).to receive(:start_xctest_session).and_return(device_manager_return_value)
      get "/start_#{test_framework}_session?device=#{device}"
      expect(last_response.status).to eql 200
      expect(last_response.body).to eql({}.to_json)
    end

    it 'should respond back with error JSON on error' do
      device_manager_return_value = { "error": "response" }
      expect(DeviceManager).to receive(:start_xctest_session).and_return(device_manager_return_value)
      get "/start_#{test_framework}_session?device=#{device}"
      expect(last_response.status).to eql 500
      expect(last_response.body).to eql(device_manager_return_value.to_json)
    end

    it 'should respond back with error object when firecmd exception occurs' do
      start_session_response = "some error occured"
      expect(DeviceManager).to receive(:start_xctest_session).and_raise(FireCMDException.new(start_session_response))

      get "/start_#{test_framework}_session?device=#{device}"

      response_obj = JSON.parse(last_response.body)
      expect(last_response.status).to eql 500
      expect(response_obj["error"]).to eql start_session_response
      expect(response_obj["type"]).to eql BROWSERSTACK_ERROR_STRING
    end
  end

  describe "POST /start_#{test_framework}_session" do
    let(:device) do
      "abcd"
    end

    before do
      mock_hooter_instance
      allow(EnvMiddleware).to receive(:process_start_request)
    end

    it 'should respond back {} as JSON on success' do
      device_manager_return_value = { "some" => "response" }
      expect(DeviceManager).to receive(:start_xctest_session).and_return(device_manager_return_value)
      post "/start_#{test_framework}_session?device=#{device}" , {}.to_json, { 'CONTENT_TYPE' => 'application/json' }
      expect(last_response.status).to eql 200
      expect(last_response.body).to eql({}.to_json)
    end

    it 'should respond back with error JSON on error' do
      device_manager_return_value = { "error": "response" }
      expect(DeviceManager).to receive(:start_xctest_session).and_return(device_manager_return_value)
      post "/start_#{test_framework}_session?device=#{device}" , {}.to_json, { 'CONTENT_TYPE' => 'application/json' }
      expect(last_response.status).to eql 500
      expect(last_response.body).to eql(device_manager_return_value.to_json)
    end

    it 'should respond back with error object when firecmd exception occurs' do
      start_session_response = "some error occured"
      expect(DeviceManager).to receive(:start_xctest_session).and_raise(FireCMDException.new(start_session_response))

      post "/start_#{test_framework}_session?device=#{device}" , {}.to_json, { 'CONTENT_TYPE' => 'application/json' }

      response_obj = JSON.parse(last_response.body)
      expect(last_response.status).to eql 500
      expect(response_obj["error"]).to eql start_session_response
      expect(response_obj["type"]).to eql BROWSERSTACK_ERROR_STRING
    end
  end
end

describe 'GET /cleanup' do
  before do
    mock_hooter_instance
    allow(EnvMiddleware).to receive(:process_non_start_request)
    allow(BrowserStack::Zombie).to receive(:push_logs)
  end

  context 'when device has requested setup' do
    let(:device) { 'abcd' }
    let(:backupManagerMock) { double('CFGUtilBackupManager') }

    before do
      DeviceManager.request_setup(device)

      allow(DeviceManager)
        .to receive(:all_devices)
        .and_return({ device => {} })

      allow(DeviceManager)
        .to receive(:device_setup_completed?)
        .with(device)
        .and_return(true)

      allow(CFGUtilBackupManager)
        .to receive(:new)
        .and_return(backupManagerMock)
    end

    after do
      FileUtils.rm_f(DeviceManager.setup_requested_file(device))
    end

    it 'should return 200' do
      get "/cleanup?device=#{device}"
      expect(last_response.status).to eql 200
      expect(last_response.body).to eql "Device needs setup"
    end
  end

  context 'when device is not in config' do
    let(:backupManagerMock) { double('CFGUtilBackupManager') }

    it 'should return 200' do
      device = '_'
      allow(DeviceManager)
        .to receive(:all_devices)
        .and_return({})

      allow(CFGUtilBackupManager)
        .to receive(:new)
        .and_return(backupManagerMock)
      get "/cleanup?device=#{device}"
      expect(last_response.status).to eql 200
      expect(last_response.body).to eql "Device not found in config"
    end
  end

  context 'when device check has not finished setting up the device' do
    let(:backupManagerMock) { double('CFGUtilBackupManager') }

    it 'should return 200' do
      device = '_'
      allow(DeviceManager)
        .to receive(:device_setup_completed?)
        .with(device)
        .and_return(false)

      allow(CFGUtilBackupManager)
        .to receive(:new)
        .and_return(backupManagerMock)
      get "/cleanup?device=#{device}"
      expect(last_response.status).to eql 200
      expect(last_response.body).to eql "Device not found in config"
    end
  end

  context 'reserved flow - minified cleanup' do
    let(:device) { 'abcd' }
    let(:deviceStateMock) { double('DeviceStateMock') }
    let(:backupManagerMock) { double('CFGUtilBackupManager') }
    let(:mdmCleanupManagerMock) { double('MDMCleanupManager') }

    before do
      allow(DeviceManager)
        .to receive(:all_devices)
        .and_return({ device => {} })

      allow(DeviceManager)
        .to receive(:device_setup_completed?)
        .with(device)
        .and_return(true)

      allow(DeviceManager)
        .to receive(:setup_requested?)
        .with(device)
        .and_return(false)

      allow(DeviceManager)
        .to receive(:cleanup_done_file)
        .with(device)
        .and_return("abc")

      allow(CFGUtilBackupManager)
        .to receive(:new)
        .and_return(backupManagerMock)
    end

    after do
      FileUtils.rm_f(DeviceManager.setup_requested_file(device))
    end

    [GENRE_APP_AUTOMATE, GENRE_AUTOMATE].each do |genre|
      before(:each) do
        allow(deviceStateMock).to receive(:dedicated_cleanup_file_present?).and_return false
      end

      it "should return 500 error if minimized_cleanup_unreserved_file is present for #{genre}" do
        expect(deviceStateMock).to receive(:minimized_cleanup_unreserved_file_present?).and_return true
        expect(deviceStateMock).to receive(:remove_minimized_cleanup_unreserved_file).and_return true
        expect(deviceStateMock).to receive(:remove_minimized_cleanup_reserved_file).and_return true
        expect(deviceStateMock).to receive(:remove_preserve_app_state_reserved_file).and_return true
        expect(deviceStateMock).to receive(:mdm_full_cleanup_file_present?).at_least(:once).and_return false
        expect(deviceStateMock).to receive(:first_cleanup_file_present?).and_return false
        expect(FullCleanup::MDMCleanupManager).to receive(:new).and_return(mdmCleanupManagerMock)
        expect(mdmCleanupManagerMock).to receive(:run_full_cleanup?).and_return false

        expect(deviceStateMock).to receive(:touch_minimized_cleanup_reserved_file).and_return true
        expect(deviceStateMock).to receive(:app_a11y_app_details_file_present?).and_return false
        expect(deviceStateMock).to receive(:app_a11y_deduplication_hash_file_present?).and_return false

        expect(DeviceState).to receive(:new).and_return(deviceStateMock, deviceStateMock)

        expect(FileUtils).to receive(:touch).with("abc")

        get "/cleanup?device=#{device}&genre=#{genre}&reserveDevice=true"
        expect(last_response.status).to eql 500
      end

      it "should return 200 for happy flow #{genre}" do
        expect(deviceStateMock).to receive(:minimized_cleanup_reserved_file_present?).and_return true
        expect(deviceStateMock).to receive(:minimized_cleanup_unreserved_file_present?).and_return false

        expect(deviceStateMock).to_not receive(:remove_minimized_cleanup_unreserved_file)
        expect(deviceStateMock).to receive(:touch_minimized_cleanup_reserved_file).and_return true
        expect(deviceStateMock).to receive(:mdm_full_cleanup_file_present?).at_least(:once).and_return false
        expect(deviceStateMock).to receive(:first_cleanup_file_present?).and_return false
        expect(deviceStateMock).to receive(:app_a11y_app_details_file_present?).and_return false
        expect(deviceStateMock).to receive(:app_a11y_deduplication_hash_file_present?).and_return false
        expect(FullCleanup::MDMCleanupManager).to receive(:new).and_return(mdmCleanupManagerMock)
        expect(mdmCleanupManagerMock).to receive(:run_full_cleanup?).and_return false

        expect(DeviceState).to receive(:new).and_return(deviceStateMock, deviceStateMock)

        expect(FileUtils).to receive(:touch).with("abc")

        expect(OSUtils).to receive(:execute).with(/cleanup.rb/).and_return("", "")

        exit_status = 0
        expect(OSUtils).to receive(:execute).with(/cleanup.rb/, true).and_return([0, exit_status])

        get "/cleanup?device=#{device}&genre=#{genre}&reserveDevice=true"
        expect(last_response.status).to eql 200
      end

      it "should return 500 for failures in minified cleanup for #{genre}" do
        expect(deviceStateMock).to receive(:minimized_cleanup_reserved_file_present?).and_return false
        expect(deviceStateMock).to receive(:minimized_cleanup_unreserved_file_present?).and_return false

        expect(deviceStateMock).to receive(:remove_minimized_cleanup_reserved_file)
        expect(deviceStateMock).to receive(:remove_preserve_app_state_reserved_file)
        expect(deviceStateMock).to receive(:mdm_full_cleanup_file_present?).at_least(:once).and_return false
        expect(deviceStateMock).to receive(:first_cleanup_file_present?).and_return false
        expect(deviceStateMock).to receive(:touch_minimized_cleanup_reserved_file).and_return true
        expect(deviceStateMock).to receive(:app_a11y_app_details_file_present?).and_return false
        expect(deviceStateMock).to receive(:app_a11y_deduplication_hash_file_present?).and_return false
        expect(FullCleanup::MDMCleanupManager).to receive(:new).and_return(mdmCleanupManagerMock)
        expect(mdmCleanupManagerMock).to receive(:run_full_cleanup?).and_return false

        expect(DeviceState).to receive(:new).and_return(deviceStateMock, deviceStateMock)

        expect(FileUtils).to receive(:touch).with("abc")

        expect(OSUtils).to receive(:execute).with(/cleanup.rb/).and_return("", "")

        exit_status = 1
        expect(OSUtils).to receive(:execute).with(/cleanup.rb/, true).and_return([0, exit_status])

        get "/cleanup?device=#{device}&genre=#{genre}&reserveDevice=true"
        expect(last_response.status).to eql 500
      end
    end
  end
end

describe 'POST /set_cleanup_policy' do
  let(:device) do
    "abcd"
  end

  before do
    allow(EnvMiddleware).to receive(:process_start_request)
    mock_hooter_instance
  end

  it 'should respond back {} as JSON on success' do
    post "/set_cleanup_policy?device=#{device}" , { cleanup_type: "public" }.to_json, { 'CONTENT_TYPE' => 'application/json' }
    expect(last_response.status).to eql 200
    expect(last_response.body).to eql({}.to_json)

    post "/set_cleanup_policy?device=#{device}" , { cleanup_type: "dedicated", dedicated_cleanup_config: "clean_browser" }.to_json, { 'CONTENT_TYPE' => 'application/json' }
    expect(last_response.status).to eql 200
    expect(last_response.body).to eql({}.to_json)
  end

  it 'should respond back with error JSON on error' do
    post "/set_cleanup_policy?device=#{device}" , {}.to_json, { 'CONTENT_TYPE' => 'application/json' }
    expect(last_response.status).to eql 400
    expect(last_response.body).to eql({ error: "Invalid cleanup_type: " }.to_json)

    post "/set_cleanup_policy?device=#{device}" , { cleanup_type: "" }.to_json, { 'CONTENT_TYPE' => 'application/json' }
    expect(last_response.status).to eql 400
    expect(last_response.body).to eql({ error: "Invalid cleanup_type: " }.to_json)

    post "/set_cleanup_policy?device=#{device}" , { cleanup_type: "non_existent" }.to_json, { 'CONTENT_TYPE' => 'application/json' }
    expect(last_response.status).to eql 400
    expect(last_response.body).to eql({ error: "Invalid cleanup_type: non_existent" }.to_json)

    post "/set_cleanup_policy" , { cleanup_type: "public" }.to_json, { 'CONTENT_TYPE' => 'application/json' }
    expect(last_response.status).to eql 400
    expect(last_response.body).to eql("")
  end

  test_frameworks.each do |test_framework|
    it 'should respond back with error object when firecmd exception occurs' do
      start_session_response = "some error occured"
      expect(DeviceManager).to receive(:start_xctest_session).and_raise(FireCMDException.new(start_session_response))

      post "/start_#{test_framework}_session?device=#{device}" , {}.to_json, { 'CONTENT_TYPE' => 'application/json' }

      response_obj = JSON.parse(last_response.body)
      expect(last_response.status).to eql 500
      expect(response_obj["error"]).to eql start_session_response
      expect(response_obj["type"]).to eql BROWSERSTACK_ERROR_STRING
    end
  end
end

describe 'GET /start' do
  before do
    mock_hooter_instance
    allow(EnvMiddleware).to receive(:process_start_request)
  end

  it 'should give error when device is not given' do
    get '/start'
    expect(last_response.status).to eql 400
  end

  it 'should give error when device is not part of the server' do
    device = '_'
    error_message = "FATAL: Cannot find device #{device}."
    expect(DeviceManager)
      .to receive(:start)
      .with(device, { 'device' => device, 'session_start_events' => {} })
      .and_raise(RuntimeError, error_message)
    get "/start?device=#{device}"
    expect(last_response.status).to eql 500
    expect(last_response.body).to eql error_message
  end

  context 'when a cleanup_done file is present for the requested device' do
    DeviceManager.class_variable_set(:@@settings, { state_files_dir: __dir__ })
    let(:device) { 'test_device' }

    before do
      FileUtils.touch(DeviceManager.cleanup_done_file(device))
    end

    it 'should raise an exception' do
      get "/start?device=#{device}"
      expect(last_response.status).to eql 500
      expect(last_response.body).to match(/Cannot start session/)
    end

    after do
      FileUtils.rm_f(DeviceManager.cleanup_done_file(device))
    end
  end
end

describe "GET /restart" do
  let(:device) { 'test_device' }
  let(:actions) { 'action1,action2' }

  before do
    mock_hooter_instance
  end

  it 'should give error when actions is not given' do
    get '/restart'
    expect(last_response.status).to eql 400
  end

  it 'should call restart method in device manager' do
    expect(DeviceManager)
      .to receive(:restart)
      .with(device, { 'device' => device, 'session_start_events' => {}, 'actions' => actions })
    get "/restart?actions=#{actions}&device=#{device}"
    expect(last_response.status).to eql 200
  end
end

describe 'GET /geturl' do
  before do
    mock_hooter = mock_hooter_instance
    allow(mock_hooter).to receive(:send)
    allow(BrowserStack::Zombie).to receive(:push_logs)
  end

  it 'should return a blank string when trying to get url from invalid device' do
    get '/geturl?device=wrongdevice'
    expect(last_response).to be_ok
    expect(last_response.body).to eql ''
  end

  it 'should return a valid URL when received from device' do
    test_url = 'http://www.bstest.com'
    mock_returned_url_in_debugger_json(test_url)
    get '/geturl?device=test_device'
    expect(last_response).to be_ok
    expect(last_response.body).to eql test_url
  end

  it 'should return localhost when bs-local.com is received from device' do
    test_url = 'bs-local.com'
    mock_returned_url_in_debugger_json(test_url)
    get '/geturl?device=test_device'
    expect(last_response).to be_ok
    expect(last_response.body).to eql 'localhost'
  end

  it 'should return an empty string when a data url is received from device' do
    test_url = 'data: somedatastuff'
    mock_returned_url_in_debugger_json(test_url)
    get '/geturl?device=test_device'
    expect(last_response).to be_ok
    expect(last_response.body).to eql ''
  end
end

describe 'DELETE /recycle' do
  before do
    mock_hooter = mock_hooter_instance
    allow(mock_hooter).to receive(:send)
  end

  it "should return 200 if it's a VM" do
    allow(BrowserStack::OSUtils).to receive(:virtual_machine?).and_return(true)
    expect(BrowserStack::OSUtils).to receive(:shutdown_machine)

    delete '/recycle'

    expect(last_response).to be_ok
    expect(last_response.body).to be_empty
  end

  it "should return error if itsn't a VM" do
    allow(BrowserStack::OSUtils).to receive(:virtual_machine?).and_return(false)

    delete '/recycle'

    expect(BrowserStack::OSUtils).to_not receive(:shutdown_machine)
    expect(last_response).not_to be_ok
    expect(last_response.body).to include("Can't recycle")
  end
end

describe 'GET /hit_wda_endpoint' do
  let(:device) { 'my_device' }

  before do
    mock_hooter = mock_hooter_instance
    allow(mock_hooter).to receive(:send)
  end

  it 'should send get request to wda if device not in prod' do
    app.class_variable_set(:@@devices_conf, { 'devices' => { 'my_device' => { 'webdriver_port' => 123 } } })

    response_body = 'fake_body'
    response_double = double('fake_response', { body: response_body })
    expect_any_instance_of(WdaClient).to receive(:make_request).and_return(response_double)
    expect_any_instance_of(StaticConf::Config).to receive(:device_pointed_to_prod?).and_return(false)

    device = 'my_device'
    get "/hit_wda_endpoint?device=#{device}&endPoint=bs/list"

    expect(last_response.body).to eq(response_double.to_json)
  end

  it 'should have status = 422 if device not found' do
    app.class_variable_set(:@@devices_conf, { 'devices' => { 'my_device' => nil } })

    get "/hit_wda_endpoint?device=#{device}&endPoint=bs/list"

    expect(last_response.status).to eq(422)
  end

  it 'should have status = 400 if device pointing to prod' do
    app.class_variable_set(:@@devices_conf, { 'devices' => { 'my_device' => { 'webdriver_port' => 123 } } })
    expect_any_instance_of(StaticConf::Config).to receive(:device_pointed_to_prod?).and_return(true)

    get "/hit_wda_endpoint?device=#{device}&endPoint=bs/list"

    expect(last_response.status).to eq(400)
  end
end

describe 'POST /hit_wda_endpoint' do
  let(:device) { 'my_device' }

  before do
    mock_hooter = mock_hooter_instance
    allow(mock_hooter).to receive(:send)
  end

  it 'should send post request to wda if device not in prod' do
    app.class_variable_set(:@@devices_conf, { 'devices' => { 'my_device' => { 'webdriver_port' => 123 } } })

    response_body = 'fake_body'
    response_double = double('fake_response', { body: response_body })
    expect_any_instance_of(WdaClient).to receive(:make_request).and_return(response_double)
    expect_any_instance_of(StaticConf::Config).to receive(:device_pointed_to_prod?).and_return(false)

    post "/hit_wda_endpoint?device=#{device}&endPoint=bs/list"
    expect(last_response.body).to eq(response_double.to_json)
  end

  it 'should have status = 422 if device not found' do
    app.class_variable_set(:@@devices_conf, { 'devices' => { 'my_device' => nil } })

    post "/hit_wda_endpoint?device=#{device}&endPoint=bs/list"

    expect(last_response.status).to eq(422)
  end

  it 'should have status = 400 if device pointing to prod' do
    app.class_variable_set(:@@devices_conf, { 'devices' => { 'my_device' => { 'webdriver_port' => 123 } } })
    expect_any_instance_of(StaticConf::Config).to receive(:device_pointed_to_prod?).and_return(true)

    post "/hit_wda_endpoint?device=#{device}&endPoint=bs/list"

    expect(last_response.status).to eq(400)
  end
end

describe 'GET /update_device_log_level' do
  let(:device) { 'my_device' }

  before do
    mock_hooter = mock_hooter_instance
    allow(mock_hooter).to receive(:send)
  end

  it 'should return 400 bad request if log_type param is missing' do
    app.class_variable_set(:@@devices_conf, { 'devices' => { 'my_device' => { 'webdriver_port' => 123 } } })
    get "/update_device_log_level?device=#{device}"
    expect(last_response.status).to eq(400)
  end

  it 'should return 200 if log_type param is present' do
    app.class_variable_set(:@@devices_conf, { 'devices' => { 'my_device' => { 'webdriver_port' => 123 } } })
    get "/update_device_log_level?device=#{device}&log_level=2"
    expect(last_response.status).to eq(200)
  end
end

describe 'GET /restart_device' do
  before do
    mock_hooter = mock_hooter_instance
    allow(mock_hooter).to receive(:send)
  end

  it 'should give error when device is not given ' do
    get '/restart_device'
    expect(last_response.status).to eql 400
  end

  it 'should give error when device is not part of the server' do
    device = '_'
    error_message = "FATAL: Cannot find device #{device}."
    expect(DeviceManager)
      .to receive(:restart_device)
      .with(device)
      .and_raise(RuntimeError, error_message)
    get "/restart_device?device=#{device}"
    expect(last_response.status).to eql 500
    expect(last_response.body).to include(error_message)
  end

  context ' device is present ' do
    DeviceManager.class_variable_set(:@@settings, { state_files_dir: __dir__ })
    let(:device) { 'test_device' }

    it ' should return 200 ' do
      allow(DeviceManager).to receive(:restart_device).and_return(nil)
      get "/restart_device?device=#{device}"
      expect(last_response.status).to eql 200
      expect(last_response.body).to eql '{}'
    end
  end
end

describe 'GET /app_strings' do
  let(:session_id) { "session_id" }
  let(:device_id) { "test_device" }

  before do
    mock_hooter = mock_hooter_instance
    allow(mock_hooter).to receive(:send)
    allow(BrowserStack::Zombie).to receive(:push_logs)
  end

  it 'should give 200 if app_strings are fetched ' do
    expect(DeviceManager).to receive(:app_strings).with(device_id, "session_id", "en", "").and_return({ "app_strings" => "app_string" })

    get '/app_strings', { device: device_id, automate_session_id: "session_id", language: "en", string_file: "" }

    expect(last_response.status).to eql 200
  end

  it 'should give 500 with message if file for app_strings is not present' do
    expect(DeviceManager).to receive(:app_strings).with(device_id, "session_id", "en", "").and_raise(AppStringsCommandError.new("No /tmp/xyz.app/en.lproj found for App"))

    get '/app_strings', { device: device_id, automate_session_id: "session_id", language: "en", string_file: "" }

    expect(last_response.status).to eql 500
  end

  it 'should give 500 with message if browserstack error is present during app strings command' do
    expect(DeviceManager).to receive(:app_strings).with(device_id, "session_id", "en", "").and_raise(RuntimeError)

    get '/app_strings', { device: device_id, automate_session_id: "session_id", language: "en", string_file: "" }

    expect(last_response.status).to eql 500
  end
end

describe 'GET /get_screenshot' do
  let(:session_id) { "session_id" }
  let(:device_id) { "test_device" }

  before do
    mock_hooter = mock_hooter_instance
    allow(mock_hooter).to receive(:send)
    allow(BrowserStack::Zombie).to receive(:push_logs)
  end

  it 'should give 200 if screenshot captured ' do
    expect(DeviceManager).to receive(:get_screenshot_uncached).with(device_id, "potrait").and_return("screenshot-test-image")
    get '/get_screenshot', { device: device_id, orientation: 'potrait' }
    expect(last_response.status).to eql 200
  end

  it 'should give 500 if exception while screenshot capture ' do
    expect(DeviceManager).to receive(:get_screenshot_uncached).with(device_id, "potrait").and_raise("some_error")
    get '/get_screenshot', { device: device_id, orientation: 'potrait' }
    expect(last_response.status).to eql 500
  end
end

describe 'GET /snapshot' do
  let(:device_id) { "test_device" }

  before do
    mock_hooter = mock_hooter_instance
    allow(mock_hooter).to receive(:send)
    File.delete("/tmp/wda_status_#{device_id}.txt") if File.exist?("/tmp/wda_status_#{device_id}.txt")
  end

  it 'should give 200 with cheap_stream not passed and use idevicescreenshot for screenshot ' do
    expect_any_instance_of(WdaClient).to_not receive(:cached_running?)
    expect(DeviceManager).to receive(:get_screenshot_uncached).with(device_id, "potrait", false, nil).and_return("screenshot-test-image")
    get '/snapshot', { device: device_id, orientation: 'potrait' }
    expect(last_response.status).to eql 200
  end

  it 'should give 500 with cheap_stream not passed and not able to capture screenshot ' do
    expect_any_instance_of(WdaClient).to_not receive(:cached_running?)
    expect(DeviceManager).to receive(:get_screenshot_uncached).with(device_id, "potrait", false, nil).and_raise("some_error")
    get '/snapshot', { device: device_id, orientation: 'potrait' }
    expect(last_response.status).to eql 500
  end

  it 'should give 200 with cheap_stream as false and use idevicescreenshot for screenshot ' do
    expect_any_instance_of(WdaClient).to_not receive(:cached_running?)
    expect(DeviceManager).to receive(:get_screenshot_uncached).with(device_id, "potrait", false, "false").and_return("screenshot-test-image")
    get '/snapshot', { device: device_id, orientation: 'potrait', cheap_stream: 'false' }
    expect(last_response.status).to eql 200
  end

  it 'should give 200 for cheap_stream if wda running and snapshot captured ' do
    expect_any_instance_of(WdaClient).to receive(:cached_running?).with(no_args).and_return(true)
    expect(DeviceManager).to receive(:get_screenshot_uncached).with(device_id, "potrait", true, "true").and_return("screenshot-test-image")
    get '/snapshot', { device: device_id, orientation: 'potrait', cheap_stream: 'true' }
    expect(last_response.status).to eql 200
  end

  it 'should give 200 for cheap_stream if wda not running and snapshot captured ' do
    expect_any_instance_of(WdaClient).to receive(:cached_running?).with(no_args).and_return(false)
    expect(DeviceManager).to receive(:get_screenshot_uncached).with(device_id, "potrait", false, "true").and_return("screenshot-test-image")
    get '/snapshot', { device: device_id, orientation: 'potrait', cheap_stream: 'true' }
    expect(last_response.status).to eql 200
  end

  it 'should give 500 for cheap_stream if exception while snapshot capture ' do
    expect_any_instance_of(WdaClient).to receive(:cached_running?).with(no_args).and_return(true)
    expect(DeviceManager).to receive(:get_screenshot_uncached).with(device_id, "potrait", true, "true").and_raise("some_error")
    get '/snapshot', { device: device_id, orientation: 'potrait', cheap_stream: 'true' }
    expect(last_response.status).to eql 500
  end
end

describe 'GET /install_app' do
  let(:session_id) { "session_id" }
  let(:device_id) { "test_device" }

  before do
    mock_hooter = mock_hooter_instance
    allow(mock_hooter).to receive(:send)
    allow(BrowserStack::Zombie).to receive(:push_logs)
  end

  it 'should give 200 if app is installed correctly ' do
    expect(DeviceManager).to receive(:install_app).with(device_id, "session_id", "hashed_id").and_return("")

    get '/install_app', { device: device_id, automate_session_id: "session_id", app_hashed_id: "hashed_id" }

    expect(last_response.status).to eql 200
  end

  it 'should give 200 with message if app is already installed' do
    expect(DeviceManager).to receive(:install_app).with(device_id, "session_id", "hashed_id").and_return("The app_url 'bs://hashed_id' passed in the installApp command is already installed on the device")

    get '/install_app', { device: device_id, automate_session_id: "session_id", app_hashed_id: "hashed_id" }

    expect(last_response.status).to eql 200
  end

  it 'should give 500 with message if app_url passed is not valid' do
    expect(DeviceManager).to receive(:install_app).with(device_id, "session_id", "hashed_id").and_raise(AppInstallCommandFailedException.new("An unknown server-side error occurred while processing the command. Original error: The app_url value 'bs://hashed_id' is invalid."))

    get '/install_app', { device: device_id, automate_session_id: "session_id", app_hashed_id: "hashed_id" }

    expect(last_response.status).to eql 500
  end

  it 'should give 500 with message if browserstack error is present during app install' do
    expect(DeviceManager).to receive(:install_app).with(device_id, "session_id", "hashed_id").and_raise(RuntimeError)

    get '/install_app', { device: device_id, automate_session_id: "session_id", app_hashed_id: "hashed_id" }

    expect(last_response.status).to eql 500
  end
end

describe 'GET /inject_image' do
  let(:device) { 'my_device' }

  before do
    allow(mock_hooter_instance).to receive(:send)
    allow(BrowserStack::Zombie).to receive(:push_logs)
  end

  it 'should inject image and return 200' do
    expect(ImageInjector).to receive(:inject_media)
    expect(AppPatchingUtil).to receive(:update_app_patching_data_to_state_file).with(device, any_args)

    get "/inject_image?device=#{device}"
    expect(last_response.status).to eql 200
  end

  it 'should return 500 if error happens while injecting image' do
    expect(BrowserStack::Zombie).to receive(:push_logs).with("inject-image-failure", "some_error", anything)
    expect(ImageInjector).to receive(:inject_media).and_raise("some_error")
    expect(AppPatchingUtil).to receive(:update_app_patching_data_to_state_file).with(device, any_args)

    get "/inject_image?device=#{device}"
    expect(last_response.status).to eql 500
  end

  it 'should raise error if error happens while update_app_patching_data_to_state_file' do
    expect(ImageInjector).to receive(:inject_media)
    expect(AppPatchingUtil).to receive(:update_app_patching_data_to_state_file).with(device, any_args).and_raise("some_error")

    expect { get "/inject_image?device=#{device}" }.to raise_error("some_error")
  end
end

describe 'GET /media/push_to_device' do
  let(:device) { 'my_device' }

  before do
    allow(mock_hooter_instance).to receive(:send)
    allow(FileInjector).to receive(:inject_file)
    allow(Process).to receive(:detach)
  end

  it 'should inject file and return 200' do
    expect(FileInjector).to receive(:inject_file)

    get "/media/push_to_device?device=#{device}"
    expect(last_response.status).to eql 200
  end

  it 'should return 500 if error happens while injecting file' do
    expect(FileInjector).to receive(:inject_file).and_raise("some_error")

    get "/media/push_to_device?device=#{device}"
    expect(last_response.status).to eql 500
  end

  it 'should fork new process when use_pusher is sent as true' do
    expect(Process).to receive(:fork)

    get "/media/push_to_device?device=#{device}&use_pusher=true"
    expect(last_response.status).to eql 200
  end

  it 'should not fork new process when use_pusher is sent as false' do
    expect(Process).to_not receive(:fork)
    get "/media/push_to_device?device=#{device}&use_pusher=false"
    expect(last_response.status).to eql 200
  end
end

describe 'GET /upload_crash_logs' do
  let(:device) { 'my_device' }

  before do
    allow(mock_hooter_instance).to receive(:send)
  end

  it 'should upload crash logs and return 200' do
    expect(CrashLog).to receive(:generate_and_upload).and_return({ num_crash_reports: 2 })

    get "/upload_crash_logs?device=#{device}"
    expect(last_response.status).to eql 200
  end

  it 'should return 500 if error happens while uploading crash logs' do
    expect(CrashLog).to receive(:generate_and_upload).and_raise("some_error")

    get "/upload_crash_logs?device=#{device}"
    expect(last_response.status).to eql 500
  end
end

describe 'POST /execute_apple_pay' do
  let(:device) { 'my_device' }
  let(:params) { { product: "automate" } }
  let(:apple_pay_executor) { double('ApplePayExecutor') }

  before do
    allow(mock_hooter_instance).to receive(:send)
  end

  it 'should execute apple pay automation and return 200' do
    expect(ApplePayExecutor).to receive(:new).and_return(apple_pay_executor)
    expect(apple_pay_executor).to receive(:trigger_apple_pay).and_return(true)

    post "/execute_apple_pay?device=#{device}", params.to_json, { 'CONTENT_TYPE' => 'application/json', 'ACCEPT' => 'application/json' }
    expect(last_response.status).to eql 200
  end

  it 'should return 500 if error happens while executing apple pay automation' do
    expect(ApplePayExecutor).to receive(:new).and_return(apple_pay_executor)
    expect(apple_pay_executor).to receive(:trigger_apple_pay).and_return(false)

    post "/execute_apple_pay?device=#{device}", params.to_json, { 'CONTENT_TYPE' => 'application/json', 'ACCEPT' => 'application/json' }
    expect(last_response.status).to eql 500
  end
end

describe 'GET /device_logs' do
  let(:device) { 'test_device' }
  let(:session_id) { 'test_session_123' }
  let(:log_file_path) { "/var/log/browserstack/app_log_#{device}.log" }
  let(:timezone_offset) { -25200 } # -7 hours in seconds

  before do
    mock_hooter_instance
    allow(IdeviceUtils).to receive(:ideviceinfo).with(device, "TimeZoneOffsetFromUTC").and_return([timezone_offset.to_s])
    # Ensure the constant is available for tests
    stub_const('XCTEST_DEVICE_LOG_TIME_MATCH_REGEX', /^([A-Za-z]{3}\s+\d{1,2}\s\d{2}:\d{2}:\d{2})/)
  end

  context 'when required parameters are missing' do
    it 'should return 400 when session_id is missing' do
      get '/device_logs', { device: device }
      expect(last_response.status).to eql 400
      expect(JSON.parse(last_response.body)['error']).to eql 'Missing required parameter: session_id'
    end

    it 'should return 400 when session_id is empty' do
      get '/device_logs', { device: device, session_id: '' }
      expect(last_response.status).to eql 400
      expect(JSON.parse(last_response.body)['error']).to eql 'Missing required parameter: session_id'
    end
  end

  context 'when log file does not exist' do
    before do
      allow(File).to receive(:exist?).and_call_original
      allow(File).to receive(:exist?).with(log_file_path).and_return(false)
    end

    it 'should return 200 with empty logs and log the missing file' do
      expect(BrowserStack.logger).to receive(:info).with("Device log file not found: #{log_file_path}")

      get '/device_logs', { device: device, session_id: session_id }

      expect(last_response.status).to eql 200
      response_body = JSON.parse(last_response.body)
      expect(response_body['meta']['start_pos']).to eql 0
      expect(response_body['meta']['end_pos']).to eql 0
      expect(response_body['value']).to eql []
    end

    it 'should return 200 with empty logs when start_pos is provided' do
      start_pos = 100
      expect(BrowserStack.logger).to receive(:info).with("Device log file not found: #{log_file_path}")

      get '/device_logs', { device: device, session_id: session_id, start_pos: start_pos }

      expect(last_response.status).to eql 200
      response_body = JSON.parse(last_response.body)
      expect(response_body['meta']['start_pos']).to eql start_pos
      expect(response_body['meta']['end_pos']).to eql start_pos
      expect(response_body['value']).to eql []
    end
  end

  context 'when start position is beyond file size' do
    let(:file_size) { 1000 }
    let(:start_pos) { 1500 }

    before do
      allow(File).to receive(:exist?).and_call_original
      allow(File).to receive(:exist?).with(log_file_path).and_return(true)
      allow(File).to receive(:size).with(log_file_path).and_return(file_size)
    end

    it 'should return 200 with empty logs and log the position issue' do
      expect(BrowserStack.logger).to receive(:info).with("Start position #{start_pos} is beyond file size #{file_size}")

      get '/device_logs', { device: device, session_id: session_id, start_pos: start_pos }

      expect(last_response.status).to eql 200
      response_body = JSON.parse(last_response.body)
      expect(response_body['meta']['start_pos']).to eql start_pos
      expect(response_body['meta']['end_pos']).to eql start_pos
      expect(response_body['value']).to eql []
    end
  end

  context 'when reading logs successfully' do
    let(:file_size) { 2000 }
    let(:start_pos) { 0 }
    let(:mock_file) { StringIO.new(sample_log_content) }
    let(:sample_log_content) do
      "Dec 15 10:30:45 TestApp[123]: First log message\n" \
      "Dec 15 10:30:46 TestApp[123]: Second log message\n" \
      "Some log without timestamp\n" \
      "Dec 15 10:30:47 TestApp[123]: Third log message\n"
    end

    before do
      allow(File).to receive(:exist?).and_call_original
      allow(File).to receive(:exist?).with(log_file_path).and_return(true)
      allow(File).to receive(:size).with(log_file_path).and_return(file_size)
      allow(File).to receive(:open).with(log_file_path, 'r').and_yield(mock_file)
      allow(Time).to receive(:now).and_return(Time.new(2023, 12, 15, 10, 30, 45))
    end

    it 'should return 200 with parsed logs and correct metadata' do
      expect(BrowserStack.logger).to receive(:info).with(/actual_start_pos: \d+, actual_end_pos: \d+/)

      get '/device_logs', { device: device, session_id: session_id, start_pos: start_pos }

      expect(last_response.status).to eql 200
      response_body = JSON.parse(last_response.body)

      expect(response_body['meta']['start_pos']).to be >= 0
      expect(response_body['meta']['end_pos']).to be > response_body['meta']['start_pos']
      expect(response_body['value']).to be_an(Array)
      expect(response_body['value'].length).to eql 4

      # Check first log entry with timestamp
      first_log = response_body['value'][0]
      expect(first_log['level']).to eql 'ALL'
      expect(first_log['message']).to eql 'Dec 15 10:30:45 TestApp[123]: First log message'
      expect(first_log['timestamp']).to be_a(Integer)

      # Check log entry without timestamp (should use current time)
      third_log = response_body['value'][2]
      expect(third_log['level']).to eql 'ALL'
      expect(third_log['message']).to eql 'Some log without timestamp'
      expect(third_log['timestamp']).to be_a(Integer)
    end

    it 'should handle custom start_pos parameter' do
      custom_start_pos = 50
      mock_file.seek(custom_start_pos)

      get '/device_logs', { device: device, session_id: session_id, start_pos: custom_start_pos }

      expect(last_response.status).to eql 200
      response_body = JSON.parse(last_response.body)
      expect(response_body['meta']['start_pos']).to be >= custom_start_pos
    end

    it 'should handle negative start_pos by setting it to 0' do
      get '/device_logs', { device: device, session_id: session_id, start_pos: -10 }

      expect(last_response.status).to eql 200
      response_body = JSON.parse(last_response.body)
      expect(response_body['meta']['start_pos']).to be >= 0
    end
  end

  context 'when timezone parsing fails' do
    let(:file_size) { 1000 }
    let(:mock_file) { StringIO.new("Dec 15 10:30:45 TestApp[123]: Test log message\n") }

    before do
      allow(File).to receive(:exist?).and_call_original
      allow(File).to receive(:exist?).with(log_file_path).and_return(true)
      allow(File).to receive(:size).with(log_file_path).and_return(file_size)
      allow(File).to receive(:open).with(log_file_path, 'r').and_yield(mock_file)
      allow(IdeviceUtils).to receive(:ideviceinfo).with(device, "TimeZoneOffsetFromUTC").and_raise(StandardError.new("ideviceinfo failed"))
    end

    it 'should return 500 with error message when timezone retrieval fails' do
      get '/device_logs', { device: device, session_id: session_id }

      expect(last_response.status).to eql 500
      response_body = JSON.parse(last_response.body)
      expect(response_body['error']).to include('Error retrieving device logs')
    end
  end

  context 'when file reading fails' do
    let(:file_size) { 1000 }

    before do
      allow(File).to receive(:exist?).and_call_original
      allow(File).to receive(:exist?).with(log_file_path).and_return(true)
      allow(File).to receive(:size).with(log_file_path).and_return(file_size)
      allow(File).to receive(:open).with(log_file_path, 'r').and_raise(Errno::EACCES.new("Permission denied"))
    end

    it 'should return 500 with error message when file cannot be read' do
      get '/device_logs', { device: device, session_id: session_id }

      expect(last_response.status).to eql 500
      response_body = JSON.parse(last_response.body)
      expect(response_body['error']).to include('Error retrieving device logs')
    end
  end

  context 'when timestamp parsing fails' do
    let(:file_size) { 1000 }
    let(:mock_file) { StringIO.new("Dec 32 25:70:99 TestApp[123]: Invalid timestamp log\n") }

    before do
      allow(File).to receive(:exist?).and_call_original
      allow(File).to receive(:exist?).with(log_file_path).and_return(true)
      allow(File).to receive(:size).with(log_file_path).and_return(file_size)
      allow(File).to receive(:open).with(log_file_path, 'r').and_yield(mock_file)
      allow(Time).to receive(:now).and_return(Time.new(2023, 12, 15, 10, 30, 45))
    end

    it 'should fallback to current time when timestamp parsing fails' do
      get '/device_logs', { device: device, session_id: session_id }

      expect(last_response.status).to eql 200
      response_body = JSON.parse(last_response.body)

      log_entry = response_body['value'][0]
      expect(log_entry['timestamp']).to be_a(Integer)
      expect(log_entry['message']).to eql 'Dec 32 25:70:99 TestApp[123]: Invalid timestamp log'
    end
  end

  context 'when reading large number of lines' do
    let(:file_size) { 50000 }
    let(:large_log_content) { (1..15000).map { |i| "Dec 15 10:30:#{i % 60} TestApp[123]: Log message #{i}\n" }.join }
    let(:mock_file) { StringIO.new(large_log_content) }

    before do
      allow(File).to receive(:exist?).and_call_original
      allow(File).to receive(:exist?).with(log_file_path).and_return(true)
      allow(File).to receive(:size).with(log_file_path).and_return(file_size)
      allow(File).to receive(:open).with(log_file_path, 'r').and_yield(mock_file)
      allow(Time).to receive(:now).and_return(Time.new(2023, 12, 15, 10, 30, 45))
    end

    it 'should limit to 10000 lines maximum' do
      get '/device_logs', { device: device, session_id: session_id }

      expect(last_response.status).to eql 200
      response_body = JSON.parse(last_response.body)
      expect(response_body['value'].length).to eql 10000
    end
  end

  context 'when log file is empty' do
    let(:file_size) { 0 }
    let(:mock_file) { StringIO.new("") }

    before do
      allow(File).to receive(:exist?).and_call_original
      allow(File).to receive(:exist?).with(log_file_path).and_return(true)
      allow(File).to receive(:size).with(log_file_path).and_return(file_size)
      allow(File).to receive(:open).with(log_file_path, 'r').and_yield(mock_file)
    end

    it 'should return empty logs for empty file' do
      get '/device_logs', { device: device, session_id: session_id }

      expect(last_response.status).to eql 200
      response_body = JSON.parse(last_response.body)
      expect(response_body['value']).to eql []
      expect(response_body['meta']['start_pos']).to eql 0
      expect(response_body['meta']['end_pos']).to eql 0
    end
  end
end

describe 'POST /update_app_settings' do
  let(:device) { 'test_device' }
  let(:wda_port) { 8084 }
  let(:wda_client) { WdaClient.new(wda_port) }
  let(:device_config) { { 'webdriver_port' => wda_port } }

  let(:vaild_app_settings_dsl_mid_session) do
    {
      "Environment" => "PSP",
      "OTP Auto-Fill" => "ON",
      "Performance Charting Mock Data" => "ON",
      "Server config" => "random",
      "Child" => {
        "Child Banner" => "ON"
      }
    }
  end

  let(:vaild_app_settings_dsl_fire_cmd) do
    {
      "Environment" => "PSP",
      "OTP Auto-Fill" => "ON",
      "Performance Charting Mock Data" => "ON",
      "Server config" => "random",
      "Child" => {
        "Child Banner" => "OFF",
        "Explore Environment" => "DEV"
      }
    }
  end

  let(:parsed_app_settings_dsl_mid_session) do
    {
      "Environment" => { "value" => "PSP", "type" => "PSMultiValueSpecifier" },
      "OTP Auto-Fill" => { "value" => "ON", "type" => "PSToggleSwitchSpecifier" },
      "Performance Charting Mock Data" => { "value" => "ON", "type" => "PSToggleSwitchSpecifier" },
      "Server config" => { "value" => "random", "type" => "PSTextFieldSpecifier" },
      "Child" => {
        "value" => {
          "Child Banner" => { "value" => "ON", "type" => "PSToggleSwitchSpecifier" }
        },
        "type" => "PSChildPaneSpecifier"
      }
    }
  end

  let(:parsed_app_settings_dsl_firecmd) do
    {
      "Environment" => { "value" => "PSP", "type" => "PSMultiValueSpecifier" },
      "Performance Charting Mock Data" => { "value" => "ON", "type" => "PSToggleSwitchSpecifier" },
      "Server config" => { "value" => "random", "type" => "PSTextFieldSpecifier" },
      "Child" => {
        "value" => {
          "Explore Environment" => { "type" => "PSMultiValueSpecifier", "value" => "DEV" }
        },
        "type" => "PSChildPaneSpecifier"
      }
    }
  end

  let(:params) do
    {
      "app_testing_bundle_id" => 'some_bundle_id',
      "app_display_name" => 'some_app_name',
      "session_id" => 'some_session_id'
    }
  end

  let(:app_settings_fixtures) { "./spec/fixtures/app_settings" }
  let(:app_settings_bundle_root_plist) { "#{app_settings_fixtures}/Root.plist" }
  let(:app_settings_bundle_child_plist) { "#{app_settings_fixtures}/child.plist" }

  let(:parsed_app_setting_json) { "#{app_settings_fixtures}/AppSettings.json" }
  let(:parsed_app_setting_json_data) { JSON.parse(File.read(parsed_app_setting_json)) }
  let(:device_state) { DeviceState.new(device) }

  before do
    mock_hooter = mock_hooter_instance
    allow(mock_hooter).to receive(:send)
    app.class_variable_set(:@@devices_conf, { 'devices' => { 'test_device' => { 'webdriver_port' => 123 } } })
  end

  it 'should return error if update_app_settings already running' do
    expect(DeviceState).to receive(:new).and_return(device_state)
    expect(device_state).to receive(:update_app_settings_file_present?).and_return(true)
    expect(device_state).not_to receive(:remove_update_app_settings_file)
    params = {
      "device" => device
    }
    expected_response = { "status" => "error", "error" => { "kind" => "multiple_invocations", "meta_data" => {} } }
    post "/update_app_settings?device=#{device}", params.to_json, { 'CONTENT_TYPE' => 'application/json', 'ACCEPT' => 'application/json' }
    expect(last_response.status).to eql 200
    expect(last_response.body).to eql(expected_response.to_json)
  end

  it 'should return error if device location turned off and location permission is passed' do
    expect(DeviceState).to receive(:new).and_return(device_state)
    expect(device_state).to receive(:update_app_settings_file_present?).and_return(false)
    expect(device_state).to receive(:touch_update_app_settings_file)
    expect(device_state).to receive(:remove_update_app_settings_file)
    expect(device_state).to receive(:device_location_off_file_present?).and_return(true)
    params = {
      "device" => device,
      "session_id" => "session_id",
      "update_app_settings" => {
        "Permission Settings" => {
          "Location" => {
            "ALLOW LOCATION ACCESS" => "Always"
          }
        }
      }
    }
    session_info = {
      "session_id" => "session_id",
      "downloaded_app_path" => "downloaded_app_path",
      "app_testing_bundle_id" => "app_testing_bundle_id"
    }
    expect(DeviceManager).to receive(:session_file_contents).and_return(session_info)
    expected_response = { "status" => "error", "error" => { "kind" => "app_settings_device_location_off", "meta_data" => {} } }
    post "/update_app_settings?device=#{device}", params.to_json, { 'CONTENT_TYPE' => 'application/json', 'ACCEPT' => 'application/json' }
    expect(last_response.status).to eql 200
    expect(last_response.body).to eql(expected_response.to_json)
  end

  it 'should return error if request body not present' do
    expect(DeviceState).to receive(:new).and_return(device_state)
    expect(device_state).to receive(:update_app_settings_file_present?).and_return(false)
    expect(device_state).to receive(:touch_update_app_settings_file)
    expect(device_state).to receive(:remove_update_app_settings_file)
    expected_response = { "status" => "error", "error" => { "kind" => "app_settings_invalid_request", "meta_data" => {} } }
    post "/update_app_settings?device=#{device}", nil, { 'CONTENT_TYPE' => 'application/json', 'ACCEPT' => 'application/json' }
    expect(last_response.status).to eql 200
    expect(last_response.body).to eql(expected_response.to_json)
  end

  it 'should return error if request body is not a vaild json' do
    expect(DeviceState).to receive(:new).and_return(device_state)
    expect(device_state).to receive(:update_app_settings_file_present?).and_return(false)
    expect(device_state).to receive(:touch_update_app_settings_file)
    expect(device_state).to receive(:remove_update_app_settings_file)
    expected_response = { "status" => "error", "error" => { "kind" => "app_settings_invalid_request", "meta_data" => {} } }
    post "/update_app_settings?device=#{device}", "random string", { 'CONTENT_TYPE' => 'application/json', 'ACCEPT' => 'application/json' }
    expect(last_response.status).to eql 200
    expect(last_response.body).to eql(expected_response.to_json)
  end

  it 'should return error if session is not running' do
    expect(DeviceState).to receive(:new).and_return(device_state)
    expect(device_state).to receive(:update_app_settings_file_present?).and_return(false)
    expect(device_state).to receive(:touch_update_app_settings_file)
    expect(device_state).to receive(:remove_update_app_settings_file)

    expect(DeviceManager).to receive(:session_file_contents).and_return({})

    params = {
      "device" => device
    }
    expected_response = { "status" => "error", "error" => { "kind" => "app_settings_session_not_running", "meta_data" => {} } }
    post "/update_app_settings?device=#{device}", params.to_json, { 'CONTENT_TYPE' => 'application/json', 'ACCEPT' => 'application/json' }
    expect(last_response.status).to eql 200
    expect(last_response.body).to eql(expected_response.to_json)
  end

  it 'should flag if stop was called when automation was running' do
    params = {
      "device" => device,
      "update_app_settings" => vaild_app_settings_dsl_fire_cmd,
      "session_id" => "session_id",
      "app_testing_bundle_id" => "app_testing_bundle_id",
      "app_display_name" => "app_display_name"
    }
    session_info = {
      "session_id" => "session_id",
      "downloaded_app_path" => "downloaded_app_path",
      "app_testing_bundle_id" => "app_testing_bundle_id"
    }

    expect(DeviceState).to receive(:new).and_return(device_state)
    expect(device_state).to receive(:update_app_settings_file_present?).and_return(false)
    expect(device_state).to receive(:touch_update_app_settings_file)
    expect(device_state).to receive(:remove_update_app_settings_file)
    expect(DeviceManager).to receive(:stop_params_file).and_return("stop_param_path")
    expect(File).to receive(:exists?).with("stop_param_path").and_return(true)
    allow(File).to receive(:exists?).and_return(true)
    allow(BrowserStack::AppSettingsUtil).to receive(:get_settings_bundle_json_path).and_return(parsed_app_setting_json)

    expect(DeviceManager).to receive(:session_file_contents).and_return(session_info)
    expect(BrowserStack::AppSettingsUtil).to receive(:update_settings).and_return(true)
    expect(BrowserStack::AppSettingsUtil).to receive(:foreground_user_app).and_return(true)

    expected_response = { "status" => "success", "stop_called" => true }
    post "/update_app_settings?device=#{device}", params.to_json, { 'CONTENT_TYPE' => 'application/json', 'ACCEPT' => 'application/json' }
    expect(last_response.status).to eql 200
    expect(last_response.body).to eql(expected_response.to_json)
  end

  it 'should use upgraded app path incase app was updated' do
    session_info = {
      "session_id" => "session_id",
      "downloaded_app_path" => "downloaded_app_path",
      "app_testing_bundle_id" => "app_testing_bundle_id",
      "upgraded_app" => {
        "bundle_id" => "app_testing_bundle_id",
        "app_path" => "upgraded_app_path"
      }
    }

    params = {
      "device" => device,
      "update_app_settings" => vaild_app_settings_dsl_fire_cmd,
      "session_id" => "session_id"
    }

    expect(DeviceState).to receive(:new).and_return(device_state)
    expect(device_state).to receive(:update_app_settings_file_present?).and_return(false)
    expect(device_state).to receive(:touch_update_app_settings_file)
    expect(device_state).to receive(:remove_update_app_settings_file)
    expect(DeviceManager).to receive(:stop_params_file).and_return("stop_param_path")
    expect(File).to receive(:exists?).with("stop_param_path").and_return(false)
    allow(File).to receive(:exists?).and_return(true)
    allow(BrowserStack::AppSettingsUtil).to receive(:get_settings_bundle_json_path).and_return(parsed_app_setting_json)

    expect(DeviceManager).to receive(:session_file_contents).and_return(session_info)
    expect(BrowserStack::AppSettingsUtil).to receive(:validate).with(session_info["upgraded_app"]["app_path"], anything)
    expect(BrowserStack::AppSettingsUtil).to receive(:update_settings).and_return(true)
    expect(BrowserStack::AppSettingsUtil).to receive(:foreground_user_app).and_return(true)

    expected_response = { "status" => "success" }
    post "/update_app_settings?device=#{device}", params.to_json, { 'CONTENT_TYPE' => 'application/json', 'ACCEPT' => 'application/json' }
    expect(last_response.status).to eql 200
    expect(last_response.body).to eql(expected_response.to_json)
  end

  it 'should return error if DSL validation failed' do
    params = {
      "device" => device,
      "session_id" => "session_id"
    }
    session_info = {
      "session_id" => "session_id",
      "downloaded_app_path" => "downloaded_app_path",
      "app_testing_bundle_id" => "app_testing_bundle_id"
    }

    expect(DeviceState).to receive(:new).and_return(device_state)
    expect(device_state).to receive(:update_app_settings_file_present?).and_return(false)
    expect(device_state).to receive(:touch_update_app_settings_file)
    expect(device_state).to receive(:remove_update_app_settings_file)
    expect(DeviceManager).to receive(:stop_params_file).and_return("stop_param_path")
    expect(File).to receive(:exists?).with("stop_param_path").and_return(false)
    allow(File).to receive(:exists?).and_return(true)
    allow(BrowserStack::AppSettingsUtil).to receive(:get_settings_bundle_json_path).and_return(parsed_app_setting_json)

    expect(DeviceManager).to receive(:session_file_contents).and_return(session_info)
    expect(BrowserStack::AppSettingsUtil).not_to receive(:update_settings)
    expect(BrowserStack::AppSettingsUtil).not_to receive(:foreground_user_app)

    expected_response = { "status" => "error", "error" => { "kind" => "app_settings_invalid_request", "meta_data" => {} } }
    post "/update_app_settings?device=#{device}", params.to_json, { 'CONTENT_TYPE' => 'application/json', 'ACCEPT' => 'application/json' }
    expect(last_response.status).to eql 200
    expect(last_response.body).to eql(expected_response.to_json)
  end

  it 'should return error if automation failed' do
    params = {
      "device" => device,
      "update_app_settings" => vaild_app_settings_dsl_fire_cmd,
      "session_id" => "session_id",
      "app_testing_bundle_id" => "app_testing_bundle_id",
      "app_display_name" => "app_display_name"
    }
    session_info = {
      "session_id" => "session_id",
      "downloaded_app_path" => "downloaded_app_path",
      "app_testing_bundle_id" => "app_testing_bundle_id"
    }

    expect(DeviceState).to receive(:new).and_return(device_state)
    expect(device_state).to receive(:update_app_settings_file_present?).and_return(false)
    expect(device_state).to receive(:touch_update_app_settings_file)
    expect(device_state).to receive(:remove_update_app_settings_file)
    expect(DeviceManager).to receive(:stop_params_file).and_return("stop_param_path")
    expect(File).to receive(:exists?).with("stop_param_path").and_return(false)
    allow(File).to receive(:exists?).and_return(true)
    allow(BrowserStack::AppSettingsUtil).to receive(:get_settings_bundle_json_path).and_return(parsed_app_setting_json)

    expect(DeviceManager).to receive(:session_file_contents).and_return(session_info)
    expect(BrowserStack::AppSettingsUtil).to receive(:update_settings).and_raise(AppSettingsError)
    expect(BrowserStack::AppSettingsUtil).to receive(:foreground_user_app).and_return(true)

    expected_response = { "status" => "error", "error" => { "kind" => "app_settings_internal_error", "meta_data" => {} } }
    post "/update_app_settings?device=#{device}", params.to_json, { 'CONTENT_TYPE' => 'application/json', 'ACCEPT' => 'application/json' }
    expect(last_response.status).to eql 200
    expect(last_response.body).to eql(expected_response.to_json)
  end

  it 'should return error if foregrounding user app failed' do
    params = {
      "device" => device,
      "update_app_settings" => vaild_app_settings_dsl_fire_cmd,
      "session_id" => "session_id",
      "app_testing_bundle_id" => "app_testing_bundle_id",
      "app_display_name" => "app_display_name"
    }
    session_info = {
      "session_id" => "session_id",
      "downloaded_app_path" => "downloaded_app_path",
      "app_testing_bundle_id" => "app_testing_bundle_id"
    }

    expect(DeviceState).to receive(:new).and_return(device_state)
    expect(device_state).to receive(:update_app_settings_file_present?).and_return(false)
    expect(device_state).to receive(:touch_update_app_settings_file)
    expect(device_state).to receive(:remove_update_app_settings_file)
    expect(DeviceManager).to receive(:stop_params_file).and_return("stop_param_path")
    expect(File).to receive(:exists?).with("stop_param_path").and_return(false)
    allow(File).to receive(:exists?).and_return(true)
    allow(BrowserStack::AppSettingsUtil).to receive(:get_settings_bundle_json_path).and_return(parsed_app_setting_json)

    expect(DeviceManager).to receive(:session_file_contents).and_return(session_info)
    expect(BrowserStack::AppSettingsUtil).to receive(:update_settings).and_return(true)
    expect(BrowserStack::AppSettingsUtil).to receive(:foreground_user_app).and_raise(AppSettingsError)

    expected_response = { "status" => "error", "error" => { "kind" => "app_settings_internal_error", "meta_data" => {} } }
    post "/update_app_settings?device=#{device}", params.to_json, { 'CONTENT_TYPE' => 'application/json', 'ACCEPT' => 'application/json' }
    expect(last_response.status).to eql 200
    expect(last_response.body).to eql(expected_response.to_json)
  end

  it 'should succeed if request is valid and automation is a success' do
    params = {
      "device" => device,
      "update_app_settings" => vaild_app_settings_dsl_fire_cmd,
      "session_id" => "session_id",
      "app_testing_bundle_id" => "app_testing_bundle_id",
      "app_display_name" => "app_display_name"
    }
    session_info = {
      "session_id" => "session_id",
      "downloaded_app_path" => "downloaded_app_path",
      "app_testing_bundle_id" => "app_testing_bundle_id"
    }

    expect(DeviceState).to receive(:new).and_return(device_state)
    expect(device_state).to receive(:update_app_settings_file_present?).and_return(false)
    expect(device_state).to receive(:touch_update_app_settings_file)
    expect(device_state).to receive(:remove_update_app_settings_file)
    expect(DeviceManager).to receive(:stop_params_file).and_return("stop_param_path")
    expect(File).to receive(:exists?).with("stop_param_path").and_return(false)
    allow(File).to receive(:exists?).and_return(true)
    allow(BrowserStack::AppSettingsUtil).to receive(:get_settings_bundle_json_path).and_return(parsed_app_setting_json)

    expect(DeviceManager).to receive(:session_file_contents).and_return(session_info)
    expect(BrowserStack::AppSettingsUtil).to receive(:update_settings).and_return(true)
    expect(BrowserStack::AppSettingsUtil).to receive(:foreground_user_app).and_return(true)

    expected_response = { "status" => "success" }
    post "/update_app_settings?device=#{device}", params.to_json, { 'CONTENT_TYPE' => 'application/json', 'ACCEPT' => 'application/json' }
    expect(last_response.status).to eql 200
    expect(last_response.body).to eql(expected_response.to_json)
  end
end

describe 'GET /get_patch_type' do
  let(:device) { 'test_device' }
  let(:auth_key) { 'abcd' }

  before do
    allow(mock_hooter_instance).to receive(:send)
    allow(AuthenticationHelper).to receive(:auth_key_valid?).and_return(true)
    app.class_variable_set(:@@devices_conf, { 'devices' => { 'test_device' => { 'webdriver_port' => 123 } } })
  end

  it 'should return biometrics if the app patch type was biometrics and password_visibility_patch is false' do
    expect(Utils).to receive(:read_json_file).and_return({ APP_PATCH_TYPE => 'biometrics', 'password_visibility_patch' => false, BIOMETRIC_USER_OPTION_ACCESSIBLE => false })

    get "/get_patch_type", nil, { DEVICE_ID_HEADER => device, 'HTTP_HOST' => SENSOR_MOCKER_HOST }

    expect(last_response.status).to eql 200
    expect(last_response.body).to eql("[biometrics, , , ]")
  end

  it 'should return biometrics and password_visibility_patch if the app patch type was biometrics and password_visibility_patch is true' do
    expect(Utils).to receive(:read_json_file).and_return({ APP_PATCH_TYPE => 'biometrics', 'password_visibility_patch' => true, BIOMETRIC_USER_OPTION_ACCESSIBLE => true })

    get "/get_patch_type", nil, { DEVICE_ID_HEADER => device, 'HTTP_HOST' => SENSOR_MOCKER_HOST }

    expect(last_response.status).to eql 200
    expect(last_response.body).to eql("[biometrics, password_visibility_patch, , #{BIOMETRIC_USER_OPTION_ACCESSIBLE}]")
  end

  it 'should return all and password_visibility_patch if the app patch type is nil and password_visibility_patch is true' do
    expect(Utils).to receive(:read_json_file).and_return({ 'password_visibility_patch' => true, BIOMETRIC_USER_OPTION_ACCESSIBLE => true })

    get "/get_patch_type", nil, { DEVICE_ID_HEADER => device, 'HTTP_HOST' => SENSOR_MOCKER_HOST }

    expect(last_response.status).to eql 200
    expect(last_response.body).to eql("[biometrics, camera, passcode, password_visibility_patch, keychain, , #{BIOMETRIC_USER_OPTION_ACCESSIBLE}]")
  end

  it 'should return biometrics and keychain_biometrics if the app patch type was biometrics and keychain_biometrics is true' do
    expect(Utils).to receive(:read_json_file).and_return({ APP_PATCH_TYPE => 'biometrics', KEYCHAIN_BIOMETRICS => true, BIOMETRIC_USER_OPTION_ACCESSIBLE => true })

    get "/get_patch_type", nil, { DEVICE_ID_HEADER => device, 'HTTP_HOST' => SENSOR_MOCKER_HOST }

    expect(last_response.status).to eql 200
    expect(last_response.body).to eql("[biometrics, , #{KEYCHAIN_BIOMETRICS}, #{BIOMETRIC_USER_OPTION_ACCESSIBLE}]")
  end

  it 'should return all and keychain_biometrics if the app patch type is nil and keychain_biometrics is true' do
    expect(Utils).to receive(:read_json_file).and_return({ KEYCHAIN_BIOMETRICS => true, BIOMETRIC_USER_OPTION_ACCESSIBLE => true })

    get "/get_patch_type", nil, { DEVICE_ID_HEADER => device, 'HTTP_HOST' => SENSOR_MOCKER_HOST }

    expect(last_response.status).to eql 200
    expect(last_response.body).to eql("[biometrics, camera, passcode, , keychain, #{KEYCHAIN_BIOMETRICS}, #{BIOMETRIC_USER_OPTION_ACCESSIBLE}]")
  end

  it 'should return all if there was some exception raised while reading session file' do
    expect(Utils).to receive(:read_json_file).and_raise("some_error")

    get "/get_patch_type", nil, { DEVICE_ID_HEADER => device, 'HTTP_HOST' => SENSOR_MOCKER_HOST }

    expect(last_response.status).to eql 200
    expect(last_response.body).to eql("[biometrics, camera, passcode]")
  end

  it 'should return 200 and no data if the device id sent in header is invalid' do
    expect(Utils).to_not receive(:read_json_file)

    get "/get_patch_type", nil, { DEVICE_ID_HEADER => 'some_unknown_device', 'HTTP_HOST' => SENSOR_MOCKER_HOST }

    expect(last_response.status).to eql 200
    expect(last_response.body).to eql("")
  end
end

describe 'POST /biometric_user_option' do
  let(:device) { 'test_device' }
  before do
    mock_hooter_instance
    app.class_variable_set(:@@devices_conf, { 'devices' => { 'test_device' => { 'webdriver_port' => 123 } } })
  end

  it 'should respond back {} as JSON on success' do
    expect(Utils).to receive(:read_json_file).and_return({ "app_patch_type" => "biometric" })
    expect(Utils).to receive(:write_to_file).and_return({ "app_patch_type" => "biometric", BIOMETRIC_USER_OPTION => 'pass' })

    post "/biometric_user_option?device=#{device}" , {}.to_json, { 'CONTENT_TYPE' => 'application/json' }
    expect(last_response.status).to eql 200
    expect(last_response.body).to eql({}.to_json)
  end

  it 'should respond back {} as JSON on success' do
    expect(Utils).to receive(:read_json_file).and_return({ "app_patch_type" => "biometric" })
    expect(Utils).to receive(:write_to_file).and_return({ "app_patch_type" => "biometric", BIOMETRIC_USER_OPTION => 'fail' })

    post "/biometric_user_option?device=#{device}" , {}.to_json, { 'CONTENT_TYPE' => 'application/json' }
    expect(last_response.status).to eql 200
    expect(last_response.body).to eql({}.to_json)
  end

  it 'returns 500 and error message when an exception occurs' do
    allow(Utils).to receive(:read_json_file).and_raise(RuntimeError, 'Some error')
    post "/biometric_user_option?device=#{device}" , {}.to_json, { 'CONTENT_TYPE' => 'application/json' }
    expect(last_response.status).to eq(500)
    expect(last_response.body).to eq({ error: 'Error in fetching biometric_user_option Some error' }.to_json)
  end
end

describe 'GET /biometric_value' do
  let(:device) { 'test_device' }
  let(:auth_key) { 'abcd' }

  before do
    allow(mock_hooter_instance).to receive(:send)
    allow(AuthenticationHelper).to receive(:auth_key_valid?).and_return(true)
    app.class_variable_set(:@@devices_conf, { 'devices' => { 'test_device' => { 'webdriver_port' => 123 } } })
  end

  it 'should return BIOMETRIC_USER_OPTION pass if the option is pass' do
    expect(Utils).to receive(:read_json_file).and_return({ BIOMETRIC_USER_OPTION => 'pass' })

    get "/biometric_value", nil, { DEVICE_ID_HEADER => device, 'HTTP_HOST' => SENSOR_MOCKER_HOST }

    expect(last_response.status).to eql 200
    expect(last_response.body).to eql("pass")
  end

  it 'should return BIOMETRIC_USER_OPTION fails if the option is fail' do
    expect(Utils).to receive(:read_json_file).and_return({ BIOMETRIC_USER_OPTION => 'fail' })

    get "/biometric_value", nil, { DEVICE_ID_HEADER => device, 'HTTP_HOST' => SENSOR_MOCKER_HOST }

    expect(last_response.status).to eql 200
    expect(last_response.body).to eql("fail")
  end

  it 'should return BIOMETRIC_USER_OPTION cancel if the option is cancel' do
    expect(Utils).to receive(:read_json_file).and_return({ BIOMETRIC_USER_OPTION => 'cancel' })

    get "/biometric_value", nil, { DEVICE_ID_HEADER => device, 'HTTP_HOST' => SENSOR_MOCKER_HOST }

    expect(last_response.status).to eql 200
    expect(last_response.body).to eql("cancel")
  end

  it 'should return pass if there was some exception raised while reading session file' do
    expect(Utils).to receive(:read_json_file).and_raise("some_error")

    get "/biometric_value", nil, { DEVICE_ID_HEADER => device, 'HTTP_HOST' => SENSOR_MOCKER_HOST }

    expect(last_response.status).to eql 200
    expect(last_response.body).to eql("pass")
  end
end

describe 'POST /xcuitest/inject-media' do
  let(:device) { 'test_device' }
  before do
    mock_hooter_instance
    app.class_variable_set(:@@devices_conf, { 'devices' => { 'test_device' => { 'webdriver_port' => 123 } } })
    expect(DeviceManager).to receive(:session_file_contents).with(device).and_return(
      {
        "automate_session_id" => "some_session_id",
        "genre" => "app_automate",
        "camera_injection_media" => [
          {
            "media_id" => "someMedia",
            "filename" => "someMedia.png",
            "filetype" => "image",
            "s3_url" => "someS3Url"
          }.to_json.to_s
        ].to_json.to_s
      }
    )
  end

  it 'should inject image and return 200' do
    expect(ImageInjector).to receive(:inject_media)
    expect(AppPatchingUtil).to receive(:update_app_patching_data_to_state_file).with(device, any_args)

    post "/xcuitest/inject-media" , { "mediaID" => "someMedia.png" }.to_json, { DEVICE_ID_HEADER => 'test_device', 'HTTP_HOST' => SENSOR_MOCKER_HOST , 'CONTENT_TYPE' => 'application/json' }
    expect(last_response.status).to eql 200
  end

  it 'should return 400 if error happens while injecting image' do
    expect(AppPatchingUtil).to receive(:update_app_patching_data_to_state_file).with(device, any_args)

    post "/xcuitest/inject-media" , { "mediaID" => "random.png" }.to_json, { DEVICE_ID_HEADER => 'test_device', 'HTTP_HOST' => SENSOR_MOCKER_HOST , 'CONTENT_TYPE' => 'application/json' }
    expect(last_response.status).to eql 400
  end

  it 'should return 500 if error happens while injecting image' do
    expect(BrowserStack::Zombie).to receive(:push_logs).with("inject-image-failure", "some_error", anything)
    expect(ImageInjector).to receive(:inject_media).and_raise("some_error")
    expect(AppPatchingUtil).to receive(:update_app_patching_data_to_state_file).with(device, any_args)

    post "/xcuitest/inject-media" , { "mediaID" => "someMedia.png" }.to_json, { DEVICE_ID_HEADER => 'test_device', 'HTTP_HOST' => SENSOR_MOCKER_HOST , 'CONTENT_TYPE' => 'application/json' }
    expect(last_response.status).to eql 500
  end
end

describe 'GET /get_ios_settings' do
  before do
    mock_hooter_instance
  end

  context 'LIVE' do
    let(:accessibility_settings) do
      {
        motion: {
          reduce_motion_enabled: false
        },
        voice_over: {
          speaking_rate: 0,
          captions_panel_enabled: false,
          navigation_style: "flat"
        },
        display_and_textsize: {
          increase_contrast_enabled: false,
          larger_accessibility_sizes_enabled: false,
          text_size: 4
        }
      }
    end
    let(:device_id) { '000820-427503425' }
    let(:device_state) { DeviceState.new(device_id) }
    let(:mock_date_time_helper) { double("DateTimeHelper") }
    let(:date) { { day: "5", month: "5", year: "2023" } }

    context 'Accessibility Settings' do
      it 'should return accessibility settings included' do
        allow(IdeviceUtils).to receive(:idevice_name).and_return("iPhone")
        expect(DeviceManager).to receive(:get_accessibility_states).and_return(accessibility_settings)
        allow(DateTimeHelper).to receive(:new).and_return(mock_date_time_helper)
        allow(mock_date_time_helper).to receive(:current_date_on_device_hash).and_return(date)
        allow(mock_date_time_helper).to receive(:initial_date_on_device_hash).and_return(date)
        get '/get_ios_settings_state', { device: device_id, product: "live_testing", session_id: "1234" }
        expect(JSON.parse(last_response.body, symbolize_names: true)).to include(accessibility_settings)
      end
    end
  end
end

describe 'GET /set_geolocation' do
  let(:device) { "test_device" }
  let(:latitude) { "123" }
  let(:longitude) { "123" }
  let(:session_id) { "123" }
  let(:genre) { "live" }

  before do
    allow(mock_hooter_instance).to receive(:send)
    allow(BrowserStack::Zombie).to receive(:push_logs)
  end

  it 'should return 500 if latitude is nil' do
    get "/set_geolocation?device=#{device}&genre=#{genre}&session_id=#{session_id}&longitude=#{longitude}"
    expect(last_response.status).to eql 500
    expect(last_response.body).to eql 'param missing'
  end

  it 'should return 500 if longitude is nil' do
    get "/set_geolocation?device=#{device}&genre=#{genre}&session_id=#{session_id}&latitude=#{latitude}"
    expect(last_response.status).to eql 500
    expect(last_response.body).to eql 'param missing'
  end

  it 'should return 200 if latitude and longitude are present' do
    params = "device=#{device}&genre=#{genre}&session_id=#{session_id}&latitude=#{latitude}&longitude=#{longitude}"
    allow(Utils).to receive(:read_json_file).and_return("")
    expect(DeviceManager).to receive(:set_gpslocation).and_return({}.to_json)
    get "/set_geolocation?#{params}"
    expect(last_response.status).to eql 200
  end
end

describe 'GET /device_info' do
  let(:device) { 'my_device' }

  before do
    allow(mock_hooter_instance).to receive(:send)
  end

  it 'should get device info and return 200' do
    body_message = { 'current_device_time': '22:23', 'current_device_timezone': 'PST', 'current_device_name': 'iPhone' }
    expect(IdeviceUtils).to receive(:device_time).and_return("22:23")
    expect(IdeviceUtils).to receive(:timezone).and_return("PST")
    expect(IdeviceUtils).to receive(:idevice_name).and_return("iPhone")
    get "/device_info?device=#{device}"
    expect(last_response.status).to eql 200
    expect(last_response.body).to eql(body_message.to_json)
  end

  it 'should return 500 if error happens while getting device info' do
    expect(IdeviceUtils).to receive(:device_time).and_raise("some_error")
    get "/device_info?device=#{device}"
    expect(last_response.status).to eql 500
  end
end

describe 'GET /get_device_injected_file' do
  let(:device) { 'my_device' }

  before do
    allow(mock_hooter_instance).to receive(:send)
  end

  it 'should get file info and return 200' do
    expect(OSUtils).to receive(:execute).and_return 'Device_feature.csv'
    body_message = { 'file_uploaded': 'Device_feature.csv' }
    get "/get_device_injected_file?device=#{device}"
    expect(last_response.status).to eql 200
    expect(last_response.body).to eql(body_message.to_json)
  end
end

describe 'GET /trigger_interaction_sync_script' do
  let(:device) { 'my_device' }
  let(:is_sync_script_instance) { instance_double(InteractionSyncStabilityTester::InteractionSyncTestManager) }
  let(:is_sync_script_class) do
    class_double(InteractionSyncStabilityTester::InteractionSyncTestManager).as_stubbed_const
  end

  before do
    allow(mock_hooter_instance).to receive(:send)
  end

  it 'should trigger script and return 200' do
    allow(is_sync_script_class).to receive(:new).and_return(is_sync_script_instance)
    allow(is_sync_script_instance).to receive(:execute_script).and_return(true)
    body_message = { 'message': 'Script Trigger Successful' }
    get "/trigger_interaction_sync_script?device=#{device}"
    expect(last_response.status).to eql 200
    expect(last_response.body).to eql(body_message.to_json)
  end
end

describe 'POST /app_live/get_snapshot_details' do
  let(:app_live_session_id) { '12345' }
  let(:session_details_file) { "#{STATE_FILES_DIR}/al_session_#{app_live_session_id}" }
  let(:device) { 'device_1' }
  let(:session_details) { { 'device' => device }.to_json }
  let(:snapshot_details) { { 'snapshot' => 'details' } }

  before do
    allow(File).to receive(:exist?).and_call_original
    allow(File).to receive(:read).and_call_original
    allow(DeviceManager).to receive(:get_snapshot_details).and_return(snapshot_details)
    allow(BrowserStack::Zombie).to receive(:push_logs)
    allow(BrowserStack.logger).to receive(:error)
    stub_const('STATIC_CONF', { 'env' => 'prod', 'rails_endpoint' => "https://terminals.browserstack.com" })
  end

  context 'when the session file exists' do
    before do
      allow(File).to receive(:exist?).with(session_details_file).and_return(true)
      allow(File).to receive(:read).with(session_details_file).and_return(session_details)
    end

    it 'returns the snapshot details' do
      post '/app_live/get_snapshot_details', { app_live_session_id: app_live_session_id }.to_json

      expect(last_response.status).to eq(200)
      expect(JSON.parse(last_response.body)).to eq(snapshot_details)
      expect(BrowserStack::Zombie).to have_received(:push_logs).with("get-snapshot-details-success", "", { "session_id" => app_live_session_id })
    end
  end

  context 'when the session file does not exist' do
    before do
      allow(File).to receive(:exist?).with(session_details_file).and_return(false)
    end

    it 'returns a 404 error' do
      post '/app_live/get_snapshot_details', { app_live_session_id: app_live_session_id }.to_json

      expect(last_response.status).to eq(404)
      expect(JSON.parse(last_response.body)).to eq({ "error" => true, "message" => "No Terminal found" })
    end
  end

  context 'when an exception occurs' do
    let(:error_message) { 'Something went wrong' }

    before do
      allow(File).to receive(:exist?).with(session_details_file).and_return(true)
      allow(File).to receive(:read).with(session_details_file).and_raise(StandardError.new(error_message))
    end

    it 'returns a 500 error and logs the exception' do
      post '/app_live/get_snapshot_details', { app_live_session_id: app_live_session_id }.to_json

      expect(last_response.status).to eq(500)
      expect(JSON.parse(last_response.body)).to eq({ "error" => true, "message" => "Error getting snapshot details." })
      expect(BrowserStack::Zombie).to have_received(:push_logs).with("get-snapshot-details-error", "", { "session_id" => app_live_session_id })
      expect(BrowserStack.logger).to have_received(:error).with(%r{Exception in /get_snapshot_details : #{error_message}})
    end
  end
end
