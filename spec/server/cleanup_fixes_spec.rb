require_relative '../../server/cleanup_fixes'
require_relative '../../config/constants'
require 'fileutils'

class Mocker
  # include CleanupFixes

  def test_exception(exception_message)
    raise exception_message
  rescue => e
    CleanupFixes.handle_cleanup_exception(e, '00008110-00161D1422A2801E')
  end
end

describe CleanupFixes do
  include CleanupFixes

  def delete_all_cleanup_fixes_files
    Dir.glob("#{STATE_FILES_DIR}/cleanup_fixes/*").each do |file|
      File.delete(file)
    end
  end

  def test_exception(exception_message)
    raise exception_message
  rescue => e
    handle_cleanup_exception(e, 'somevar')
  end

  before do
    delete_all_cleanup_fixes_files
    allow(CleanupFixes).to receive(:rebuild_run_xcuittest).and_return(1)
  end

  after { delete_all_cleanup_fixes_files }

  it 'Should handle the exception' do
    handled = test_exception('An element could not be located on the page using the given search parameters.')
    expect(handled).to eq(true)

    mock = Mocker.new
    handled = mock.test_exception('An element could not be located on the page using the given search parameters.')
    expect(handled).to eq(true)
  end

  it 'Should not handle the exception' do
    handled = test_exception('Some random exception message')
    expect(handled).to eq(false)
  end

  it 'Should generate a state file path based on function name and args' do
    expected_path = "#{STATE_FILES_DIR}/cleanup_fixes/my_sexy_function_1_2"
    generated_path = CleanupFixes.generate_state_filename(:my_sexy_function, 1, 2)
    expect(expected_path).to eq(generated_path)

    expected_path = "#{STATE_FILES_DIR}/cleanup_fixes/my_sexy_function_00008110-001924243A22801E"
    generated_path = CleanupFixes.generate_state_filename(:my_sexy_function, '00008110-001924243A22801E')
    expect(expected_path).to eq(generated_path)

    expected_path = "#{STATE_FILES_DIR}/cleanup_fixes/my_sexy_function_00008110-001924243A22801E_SomeVar"
    generated_path = CleanupFixes.generate_state_filename(:my_sexy_function, ['00008110-001924243A22801E', 'SomeVar'])
    expect(expected_path).to eq(generated_path)
  end

  it 'Should retry from intial empty state with one retry' do
    state_file_path = "#{STATE_FILES_DIR}/cleanup_fixes/break_iphone"
    expect(File.exist?(state_file_path)).to eq(false)

    expect(CleanupFixes.state_handler(:break_iphone, 1)).to eq(true)

    expect(File.exist?(state_file_path)).to eq(true)

    expect(File.read(state_file_path)).to eq('1')

    expect(CleanupFixes.state_handler(:break_iphone, 1)).to eq(false)
  end

  it 'Should set count to zero if statefile empty' do
    state_file_path = "#{STATE_FILES_DIR}/cleanup_fixes/count_test"
    FileUtils.touch(state_file_path)
    expect(CleanupFixes.state_handler(:count_test, 1)).to eq(true)
    expect(File.read(state_file_path)).to eq('1')
  end

  it 'Should not run when retries exceeded from statefile' do
    state_file_path = "#{STATE_FILES_DIR}/cleanup_fixes/retries"
    File.write state_file_path, 5
    expect(CleanupFixes.state_handler(:retries, 5)).to eq(false)
  end

  it 'Should retry the expected number of retries' do
    retries = 5
    (1..retries).each { expect(CleanupFixes.state_handler(:repeat, 5)).to eq(true) }
    expect(CleanupFixes.state_handler(:repeat, 5)).to eq(false)
  end
end