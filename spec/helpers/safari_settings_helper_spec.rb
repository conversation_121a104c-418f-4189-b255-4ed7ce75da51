require 'timecop'
require_relative '../spec_helper'
require_relative '../../lib/helpers/safari_settings_helper'

describe SafariSettings do
  let(:device_config) { { 'webdriver_port' => 8080, "device_version" => '14.0' } }
  let(:uuid) { '000820-427503425' }
  let(:product) { 'app-live' }
  let(:session_id) { '1' }
  let(:ios_version) { '14.0' }
  let(:event_name) { 'web_events' }
  let(:start_time) { 100 }
  let(:end_time) { 100 }
  let(:subject) { SafariSettings.new(uuid, session_id, product) }
  let(:safari_settings_state) { { prevent_cross_site_tracking: "disable", block_popups: "disable" } }
  let(:safari_settings_state_enable) { { prevent_cross_site_tracking: "enable", block_popups: "enable" } }
  let(:data_reporter) { double('data_reporter') }

  before do
    allow(BrowserStack::Zombie).to receive(:configure)
    allow(BrowserStack::Zombie).to receive(:push_logs)
    allow(DataReportHelper).to receive(:settings_reporter).and_return(data_reporter)

    allow(File).to receive(:read).and_return("")
    allow(JSON).to receive(:parse).and_return({ "devices" => { uuid => device_config } })

    Timecop.freeze(Time.at(start_time))
  end

  after do
    Timecop.return
  end

  describe '#disable prevent xss and block popups' do
    it 'should run disable Block pop-ups and Prevent XSS automation and return correct output' do
      expect_any_instance_of(DeviceState).to receive(:touch_settings_automation_executing_file)
      expect_any_instance_of(DeviceState).to receive(:remove_settings_automation_executing_file)
      expect_any_instance_of(DeviceState).to receive(:remove_safari_settings_file)
      expect_any_instance_of(DeviceState).to receive(:write_to_safari_settings_file)
      expect_any_instance_of(WdaClient).to receive(:set_safari_settings).with(safari_settings_state).and_return({ 'value' => { 'error' => [false, false], 'message' => ["", ""] } })
      expect(data_reporter).to receive(:report_settings_data).and_return({})
      expect(subject.switch(safari_settings_state)).to eql({ prevent_cross_site_tracking: "pass", block_popups: "pass" })
    end

    it 'should raise log event to eds and zombie when test fails with unknown error' do
      expect_any_instance_of(DeviceState).to receive(:touch_settings_automation_executing_file)
      expect_any_instance_of(DeviceState).to receive(:remove_settings_automation_executing_file)
      expect_any_instance_of(WdaClient).to receive(:set_safari_settings).with(safari_settings_state).and_return({ 'value' => { 'error' => "unknown error", 'message' => "No payload in response" } })
      expect(data_reporter).to receive(:report_settings_data).and_return({})
      expect(subject.switch(safari_settings_state)).to eql({ prevent_cross_site_tracking: "fail", block_popups: "fail" })
    end

    it 'should raise log event to eds and zombie when test fails with already disabled' do
      expect_any_instance_of(DeviceState).to receive(:touch_settings_automation_executing_file)
      expect_any_instance_of(DeviceState).to receive(:remove_settings_automation_executing_file)
      expect_any_instance_of(DeviceState).to receive(:safari_settings_file_present?).and_return(true, true)
      expect_any_instance_of(DeviceState).to receive(:read_safari_settings_file).and_return("enable\nenable", "enable\nenable")
      expect_any_instance_of(WdaClient).to receive(:set_safari_settings).with(safari_settings_state).and_return({ 'value' => { 'error' => [false, true], 'message' => ["", "Failed to toggle switch as it is already disabled."] } })
      expect(data_reporter).to receive(:report_settings_data).and_return({}, {})
      expect(subject.switch(safari_settings_state)).to eql({ prevent_cross_site_tracking: "pass", block_popups: "fail" })
    end
  end

  describe '#enable prevent xss and block popups' do
    it 'should run disable Block pop-ups and Prevent XSS automation and return correct output' do
      expect_any_instance_of(DeviceState).to receive(:touch_settings_automation_executing_file)
      expect_any_instance_of(DeviceState).to receive(:remove_settings_automation_executing_file)
      expect_any_instance_of(DeviceState).to receive(:remove_safari_settings_file)
      expect_any_instance_of(DeviceState).to receive(:write_to_safari_settings_file)
      expect_any_instance_of(WdaClient).to receive(:set_safari_settings).with(safari_settings_state_enable).and_return({ 'value' => { 'error' => ["", ""], 'message' => ["", ""] } })
      expect(data_reporter).to receive(:report_settings_data).and_return({})
      expect(subject.switch(safari_settings_state_enable)).to eql({ prevent_cross_site_tracking: "pass", block_popups: "pass" })
    end

    it 'should raise log event to eds and zombie when test fails with unknown error' do
      expect_any_instance_of(DeviceState).to receive(:touch_settings_automation_executing_file)
      expect_any_instance_of(DeviceState).to receive(:remove_settings_automation_executing_file)
      expect_any_instance_of(WdaClient).to receive(:set_safari_settings).with(safari_settings_state_enable).and_return({ 'value' => { 'error' => "unknown error", 'message' => "No payload in response" } })
      expect(data_reporter).to receive(:report_settings_data).and_return({})
      expect(subject.switch(safari_settings_state_enable)).to eql({ prevent_cross_site_tracking: "fail", block_popups: "fail" })
    end

    it 'should raise log event to eds and zombie when test fails with already disabled' do
      expect_any_instance_of(DeviceState).to receive(:touch_settings_automation_executing_file)
      expect_any_instance_of(DeviceState).to receive(:remove_settings_automation_executing_file)
      expect_any_instance_of(DeviceState).to receive(:safari_settings_file_present?).and_return(true, true)
      expect_any_instance_of(DeviceState).to receive(:read_safari_settings_file).and_return("enable\nenable", "enable\nenable")
      expect_any_instance_of(WdaClient).to receive(:set_safari_settings).with(safari_settings_state_enable).and_return({ 'value' => { 'error' => [false, true], 'message' => ["", "Failed to toggle switch as it is already disabled."] } })
      expect(data_reporter).to receive(:report_settings_data).and_return({}, {})
      expect(subject.switch(safari_settings_state_enable)).to eql({ prevent_cross_site_tracking: "pass", block_popups: "fail" })
    end
  end
end
