require 'timecop'
require_relative '../spec_helper'
require_relative '../../lib/helpers/assistive_touch_helper'

describe Secure::AssistiveTouchHelper do
  let(:device_config) { { 'webdriver_port' => 8080, "device_version" => '14.0' } }
  let(:uuid) { '000820-427503425' }
  let(:file_path) { '/tmp/assistive_touch_opened_000820-427503425' }
  let(:product) { 'app-live' }
  let(:feature) { '' }
  let(:session_id) { '1' }
  let(:ios_version) { '14.0' }
  let(:event_name) { 'web_events' }
  let(:start_time) { 100 }
  let(:end_time) { 100 }
  let(:disable_failed) { 0 }
  let(:mock_ios_device) { double(IosDevice).as_null_object }
  let(:subject) { Secure::AssistiveTouchHelper.new(uuid, session_id, product) }

  before do
    allow(BrowserStack::Zombie).to receive(:configure)
    allow(BrowserStack::Zombie).to receive(:push_logs)

    allow(File).to receive(:read).and_return("")
    allow(File).to receive(:write).and_return("")
    allow(File).to receive(:exist).with(:file_path).and_return(false)
    allow(JSON).to receive(:parse).and_return({ "devices" => { uuid => device_config } })

    allow(IosDevice).to receive(:new).and_return(mock_ios_device)

    Timecop.freeze(Time.at(start_time))
  end

  after do
    Timecop.return
  end

  def handle_wda(toggle, toggle_error_reason = "", toggle_error_message = "", configure_error_reason = "no error", configure_error_message = "")
    reason = configure_error_reason
    message = "Failed to configure assistive-touch - #{configure_error_message}"
    if configure_error_reason == "unknown error" && toggle == "enable"
      expect_any_instance_of(WdaClient).to receive(:play_with_assistive_touch).with("configure").and_return({})
    else
      expect_any_instance_of(WdaClient).to receive(:play_with_assistive_touch).with("configure").and_return({ 'value' => { 'error' => configure_error_reason, 'message' => configure_error_message } }) unless toggle == "disable"
      if configure_error_reason == "no error"
        reason = toggle_error_reason
        message = toggle_error_message
        if toggle_error_reason == "unknown code error"
          expect(mock_ios_device).to receive(:"#{toggle}_assistive_touch").and_raise(toggle_error_message)
        else
          expect(mock_ios_device).to receive(:"#{toggle}_assistive_touch")
          message = toggle_error_message if toggle_error_reason == "no error"
        end
      end
    end
    [reason, message]
  end

  def handle_xcui(status)
    expect(BrowserStackAppHelper).to receive(:check_and_install_browserstack_test_suite).with(uuid)
    if status == "pass"
      expect(BrowserStackAppHelper).to receive(:run_ui_test).with(uuid, :disable_assistive_touch, session_id: session_id).and_return({})
      event_logger(status, "disable", "no error", "AssistiveTouch disabled successfully")
    else
      expect(BrowserStackAppHelper).to receive(:run_ui_test).with(uuid, :disable_assistive_touch, session_id: session_id).and_raise(BrowserStackTestExecutionError.new('test_class', 'test_func', 'random error message'))
    end
  end

  def parse_data(toggle, status, toggle_error_reason = "", toggle_error_message = "", configure_error_reason = "no error", configure_error_message = "")
    reason, message = handle_wda(toggle, toggle_error_reason, toggle_error_message, configure_error_reason, configure_error_message)
    event_logger(status, toggle, reason, message)
  end

  def event_logger(status, toggle, reason, message)
    expect(Utils).to receive(:send_to_eds).with({ event_name: "toggle-assistive-touch",
                                                  product: product,
                                                  os: "ios",
                                                  os_version: ios_version,
                                                  team: "device_features",
                                                  feature: "",
                                                  event_json: { session_id: session_id,
                                                                mode: toggle,
                                                                status: status,
                                                                error_reason: reason,
                                                                error_message: message,
                                                                time_taken: end_time - start_time } }, event_name, true)
    if status == "fail"
      expect(BrowserStack::Zombie).to receive(:push_logs).with("assistive-touch-failure",
                                                               "AutomationError",
                                                               { "device" => uuid,
                                                                 "product" => product,
                                                                 "session_id" => session_id,
                                                                 "os_version" => ios_version,
                                                                 "data" => {
                                                                   "error_message" => message,
                                                                   "mode" => toggle
                                                                 },
                                                                 "error" => reason })
    end
  end

  describe '#enable assistive_touch' do
    it 'should run automation to configure & enable assistive_touch and then return success response' do
      parse_data("enable", "pass", "no error", "AssistiveTouch successfully enabled", "no error", "assistive touch menu configured successfully")
      expect_any_instance_of(DeviceState).to receive(:touch_assistive_touch_opened_file)
      expect_any_instance_of(LockdownDeviceState).to receive(:get_assistive_touch_configuration_key)
      expect_any_instance_of(LockdownDeviceState).to receive(:set_assistive_touch_configuration_key)
      expect(subject.switch("enable")).to eql(true)
    end

    it 'should return failure response, log event to eds and zombie when assistive touch menu get customized successfully but test fails to enable assistive touch with unknown code error' do
      parse_data("enable", "fail", "unknown code error", "AssistiveTouch switch could not be located", "no error", "assistive touch menu configured successfully")
      expect_any_instance_of(LockdownDeviceState).to receive(:get_assistive_touch_configuration_key)
      expect_any_instance_of(LockdownDeviceState).to receive(:set_assistive_touch_configuration_key)
      expect(subject.switch("enable")).to eql(false)
    end

    it 'should return failure response, log event to eds and zombie when test fails to customize assistive touch with defined error' do
      parse_data("enable", "fail", "", "", "no such element", "Unable to properly set elements into AssistiveTouch Menu")
      expect_any_instance_of(LockdownDeviceState).to receive(:get_assistive_touch_configuration_key)
      expect(subject.switch("enable")).to eql(false)
    end

    it 'should return failure response, log event to eds and zombie when test fails to customize assistive touch with unknown error' do
      parse_data("enable", "fail", "", "", "unknown error", "No payload in response")
      expect_any_instance_of(LockdownDeviceState).to receive(:get_assistive_touch_configuration_key)
      expect(subject.switch("enable")).to eql(false)
    end
  end

  describe '#disable assistive_touch' do
    it 'should run automation to disable assistive_touch and then return success response' do
      parse_data("disable", "pass", "no error", "AssistiveTouch successfully disabled")
      expect_any_instance_of(DeviceState).to receive(:remove_assistive_touch_opened_file)
      expect(subject.switch("disable")).to eql(true)
    end

    it 'should log event to eds and zombie when test fails to disable assistive touch with unknown code error' do
      parse_data("disable", "fail", "unknown code error", "No payload in response")
      expect(subject.switch("disable")).to eql(false)
    end
  end
end
