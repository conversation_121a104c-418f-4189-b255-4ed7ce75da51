require_relative '../../config/constants'
require_relative '../../lib/helpers/authentication_helper'

RSpec.describe AuthenticationHelper do
  describe '.auth_key_valid?' do
    let(:valid_device_info) { { 'device_name' => 'iPhone14,5' } }
    let(:invalid_device_info) { nil }
    let(:device) { 'TestDevice' }
    let(:session_id) { 'test_session_id' }

    context 'when device name is not "iPhone14,5"' do
      it 'returns true' do
        allow(Utils).to receive(:read_json_file).and_return({ 'app_live_session_id' => session_id })
        allow(described_class).to receive(:read_data_from_auth_key_state_file).and_return(true)
        allow(described_class).to receive(:validate_auth_key).and_return(false)
        result = described_class.auth_key_valid?(valid_device_info, device)
        expect(result).to be_falsey
      end
    end

    context 'when device name is "iPhone14,5"' do
      it 'returns false' do
        result = described_class.auth_key_valid?(invalid_device_info, device)
        expect(result).to be_truthy
      end
    end

    context 'when session_id is nil' do
      it 'returns true' do
        allow(Utils).to receive(:read_json_file).and_return({})
        result = described_class.auth_key_valid?(valid_device_info, device)
        expect(result).to be_truthy
      end
    end

    context 'when session_id is not nil' do
      it 'calls validate_auth_key with the correct arguments' do
        allow(Utils).to receive(:read_json_file).and_return({ 'app_live_session_id' => session_id })
        allow(described_class).to receive(:read_data_from_auth_key_state_file).and_return(true)
        allow(described_class).to receive(:validate_auth_key).and_return(true)

        expect(described_class).to receive(:validate_auth_key).with(session_id, device, anything)

        described_class.auth_key_valid?(valid_device_info, device)
      end
    end

    context 'when an error occurs' do
      it 'logs the error and returns true' do
        allow(Utils).to receive(:read_json_file).and_raise('Some error')
        expect(BrowserStack.logger).to receive(:info).with(/Some error/)

        result = described_class.auth_key_valid?(valid_device_info, device)
        expect(result).to be_truthy
      end
    end
  end
end
