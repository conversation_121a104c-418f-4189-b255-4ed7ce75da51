require 'timecop'
require_relative '../spec_helper'
require_relative '../../lib/helpers/camera_injection_helper'

describe CameraInjectionHelper do
  let(:device_config) { { 'webdriver_port' => 8080, "device_version" => '14.0' } }
  let(:uuid) { '000820-427503425' }
  let(:product) { 'automate' }
  let(:session_id) { '1' }
  let(:ios_version) { '14.0' }
  let(:event_name) { 'web_events' }
  let(:camera_url) { 'some url' }
  let(:start_time) { 100 }
  let(:end_time) { 100 }
  let(:subject) { CameraInjectionHelper.new(uuid, session_id, product) }
  let(:data_reporter) { double('data_reporter') }

  before do
    allow(BrowserStack::Zombie).to receive(:configure)
    allow(BrowserStack::Zombie).to receive(:push_logs)
    allow(DataReportHelper).to receive(:new).and_return(data_reporter)
    allow(data_reporter).to receive(:report)
    allow(DeviceManager).to receive(:device_configuration_check).and_return(device_config)

    allow(JSON).to receive(:parse).and_return({ "devices" => { uuid => device_config } })

    Timecop.freeze(Time.at(start_time))
  end

  after do
    Timecop.return
  end

  describe '#update camera url' do
    it "Should raise exception" do
      expect(File).to receive(:open).and_raise(StandardError, "File can't be opened")
      CameraInjectionHelper.new(uuid, session_id, product).update_camera_url(camera_url)
    end

    it "Should update url successfully" do
      f = double
      expect(File).to receive(:open).and_return(f)
      expect(f).to receive(:read).and_return("some string")
      expect(f).to receive(:close)
      expect(File).to receive(:open).and_return(f)
      expect(f).to receive(:write)
      expect(f).to receive(:close)
      CameraInjectionHelper.new(uuid, session_id, product).update_camera_url(camera_url)
    end
  end
end
