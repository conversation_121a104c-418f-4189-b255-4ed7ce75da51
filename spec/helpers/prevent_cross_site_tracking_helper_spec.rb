require 'timecop'
require_relative '../spec_helper'
require_relative '../../lib/helpers/prevent_cross_site_tracking_helper'

describe BrowserStack::PreventCrossSiteTracking do
  let(:device_config) { { 'webdriver_port' => 8080, "device_version" => '14.0' } }
  let(:uuid) { '000820-427503425' }
  let(:product) { 'app-live' }
  let(:session_id) { '1' }
  let(:ios_version) { '14.0' }
  let(:event_name) { 'web_events' }
  let(:start_time) { 100 }
  let(:end_time) { 100 }
  let(:subject) { BrowserStack::PreventCrossSiteTracking.new(uuid, session_id, product) }

  before do
    allow(BrowserStack::Zombie).to receive(:configure)
    allow(BrowserStack::Zombie).to receive(:push_logs)

    allow(File).to receive(:read).and_return("")
    allow(JSON).to receive(:parse).and_return({ "devices" => { uuid => device_config } })

    Timecop.freeze(Time.at(start_time))
  end

  after do
    Timecop.return
  end

  def event_logger(toggle, status, error, message)
    if error == "unknown error"
      expect_any_instance_of(WdaClient).to receive(:switch_prevent_cross_site_tracking).with(toggle).and_return({})
    else
      expect_any_instance_of(WdaClient).to receive(:switch_prevent_cross_site_tracking).with(toggle).and_return({ 'value' => { 'error' => error, 'message' => message } })
    end
    expect(Utils).to receive(:send_to_eds).with( {
      event_name: "PreventCrossSiteTrackingToggle",
      product: product,
      os: "ios",
      os_version: ios_version,
      team: "device_features",
      event_json: {
        session_id: session_id,
        appearance_change_to: toggle,
        status: status,
        error_reason: error,
        error_message: message,
        time_taken: end_time - start_time
      }
    }, event_name, true )
    if status == "fail"
      expect(BrowserStack::Zombie).to receive(:push_logs).with("prevent-cross-site-tracking-toggle-failure",
                                                               "WdaAutomationError",
                                                               { "device" => uuid,
                                                                 "product" => product,
                                                                 "session_id" => session_id,
                                                                 "os_version" => ios_version,
                                                                 "data" => message,
                                                                 "url" => toggle,
                                                                 "error" => error })
    end

  end

  describe '#disable_prevent_cross_site_tracking' do
    it 'should run disable prevent cross-site tracking automation and return correct output' do
      event_logger("disable", "pass", nil, "Disabling Prevent Cross-Site Tracking success")
      expect_any_instance_of(DeviceState).to receive(:touch_prevent_cross_site_tracking_disabled_file)
      expect(subject.switch("disable")).to eql(true)
    end

    it 'should raise log event to eds and zombie when test fails with unknown error' do
      event_logger("disable", "fail", "unknown error", "No payload in response")
      expect(subject.switch("disable")).to eql(false)
    end

    it 'should raise log event to eds and zombie when test fails with already disabled' do
      event_logger("disable", "fail", "already disabled", "Failed to toggle switch as it is already disabled.")
      expect_any_instance_of(DeviceState).to receive(:touch_prevent_cross_site_tracking_disabled_file)
      expect(subject.switch("disable")).to eql(false)
    end

    it 'should raise log event to eds and zombie when test fails with no such element toggle button' do
      event_logger("disable", "fail", "no such element", "Failed to toggle switch due to toggle not found.")
      expect(subject.switch("disable")).to eql(false)
    end

    it 'should raise log event to eds and zombie when test fails with no such element Safari option' do
      event_logger("disable", "fail", "no such element", "Safari option could not be located.")
      expect(subject.switch("disable")).to eql(false)
    end
  end

  describe '#enable_prevent_cross_site_tracking' do
    it 'should run enable prevent cross-site tracking automation and return correct output' do
      event_logger("enable", "pass", nil, "Enabling Prevent Cross-Site Tracking success")
      expect_any_instance_of(DeviceState).to receive(:remove_prevent_cross_site_tracking_disabled_file)
      expect(subject.switch("enable")).to eql(true)
    end

    it 'should raise log event to eds and zombie when test fails with unknown error' do
      event_logger("enable", "fail", "unknown error", "No payload in response")
      expect(subject.switch("enable")).to eql(false)
    end

    it 'should raise log event to eds and zombie when test fails with already disabled' do
      event_logger("enable", "fail", "already enabled", "Failed to toggle switch as it is already disabled.")
      expect_any_instance_of(DeviceState).to receive(:remove_prevent_cross_site_tracking_disabled_file)
      expect(subject.switch("enable")).to eql(false)
    end

    it 'should raise log event to eds and zombie when test fails with no such element toggle button' do
      event_logger("enable", "fail", "no such element", "Failed to toggle switch due to toggle not found.")
      expect(subject.switch("enable")).to eql(false)
    end

    it 'should raise log event to eds and zombie when test fails with no such element Safari option' do
      event_logger("enable", "fail", "no such element", "Safari option could not be located.")
      expect(subject.switch("enable")).to eql(false)
    end
  end
end

