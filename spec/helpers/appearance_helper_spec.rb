require 'timecop'
require_relative '../spec_helper'
require_relative '../../lib/helpers/appearance_helper'

describe BrowserStack::SwitchMode do
  let(:device_config) { { 'webdriver_port' => 8080, "device_version" => '14.0' } }
  let(:uuid) { '000820-427503425' }
  let(:product) { 'app-live' }
  let(:session_id) { '1' }
  let(:ios_version) { '14.0' }
  let(:event_name) { 'web_events' }
  let(:start_time) { 100 }
  let(:end_time) { 100 }

  before do
    allow(BrowserStack::Zombie).to receive(:configure)
    allow(BrowserStack::Zombie).to receive(:push_logs)

    allow(File).to receive(:read).and_return("")
    allow(JSON).to receive(:parse).and_return({ "devices" => { uuid => device_config } })

    Timecop.freeze(Time.at(start_time))
  end

  after do
    Timecop.return
  end

  def event_logger(mode, status, error, message)
    if error == "unknown error"
      expect_any_instance_of(WdaClient).to receive(:switch_mode).with(mode).and_return({})
    else
      expect_any_instance_of(WdaClient).to receive(:switch_mode).with(mode).and_return({ 'value' => { 'error' => error,
                                                                                                      'message' => message } })
    end
    event_json = {
      session_id: session_id,
      appearance_change_to: mode,
      status: status,
      time_taken: end_time - start_time
    }

    # Add error fields only if error is not "no_error"
    unless error == "no error"
      event_json[:error_reason] = error
      event_json[:error_message] = message
    end

    expect(Utils).to receive(:send_to_eds).with( {
      event_name: "AppearanceChange",
      product: product,
      os: "ios",
      os_version: ios_version,
      team: "device_features",
      event_json: event_json
    }, event_name, true )
    if status == "fail"
      expect(BrowserStack::Zombie).to receive(:push_logs).with("appearance-change-failure",
                                                               "WdaAutomationError",
                                                               { "device" => uuid,
                                                                 "product" => product,
                                                                 "session_id" => session_id,
                                                                 "os_version" => ios_version,
                                                                 "data" => message,
                                                                 "url" => mode,
                                                                 "error" => error })
    end

  end

  describe '#change_appearance_to_dark' do
    it 'should run dark mode automation and return correct output' do
      event_logger("Dark", "pass", "no error", "Successfully switched appearance to dark mode.")
      expect_any_instance_of(DeviceState).to receive(:touch_dark_mode_file)
      subject = BrowserStack::SwitchMode.new(uuid, session_id, product)
      expect(subject.change_appearance_to("Dark")).to eql(true)
    end

    it 'should raise log event to eds and zombie when test fails with unknown error' do
      event_logger("Dark", "fail", "unknown error", "No payload in response")
      expect_any_instance_of(DeviceState).to receive(:touch_dark_mode_file)
      subject = BrowserStack::SwitchMode.new(uuid, session_id, product)
      expect(subject.change_appearance_to("Dark")).to eql(false)
    end

    it 'should raise log event to eds and zombie when test fails with unsupported iOS version' do
      event_logger("Dark", "fail", "unsupported iOS version", "early exit - appearance mode unsupported on this iOS version.")
      expect_any_instance_of(DeviceState).to receive(:touch_dark_mode_file)
      subject = BrowserStack::SwitchMode.new(uuid, session_id, product)
      expect(subject.change_appearance_to("Dark")).to eql(false)
    end

    it 'should raise log event to eds and zombie when test fails with no such element' do
      event_logger("Dark", "fail", "no such element", "Display&Brightness option could not be located.")
      expect_any_instance_of(DeviceState).to receive(:touch_dark_mode_file)
      subject = BrowserStack::SwitchMode.new(uuid, session_id, product)
      expect(subject.change_appearance_to("Dark")).to eql(false)
    end

    it 'should raise log event to eds and zombie when test fails with no such element dark mode button' do
      event_logger("Dark", "fail", "no such element", "Failed switch to Dark mode due to button not found.")
      expect_any_instance_of(DeviceState).to receive(:touch_dark_mode_file)
      subject = BrowserStack::SwitchMode.new(uuid, session_id, product)
      expect(subject.change_appearance_to("Dark")).to eql(false)
    end
  end

  describe "#change_appearance_to_light" do
    it 'should run light mode automation and return correct output' do
      event_logger("Light", "pass", "no error", "Successfully switched appearance to light mode.")
      expect_any_instance_of(DeviceState).to receive(:remove_dark_mode_file)
      subject = BrowserStack::SwitchMode.new(uuid, session_id, product)
      expect(subject.change_appearance_to("Light")).to eql(true)
    end

    it 'should raise log event to eds and zombie when test fails with unknown error' do
      event_logger("Light", "fail", "unknown error", "No payload in response")
      expect_any_instance_of(DeviceState).to_not receive(:remove_dark_mode_file)
      subject = BrowserStack::SwitchMode.new(uuid, session_id, product)
      expect(subject.change_appearance_to("Light")).to eql(false)
    end

    it 'should raise log event to eds and zombie when test fails with unsupported iOS version' do
      event_logger("Light", "fail", "unsupported iOS version", "early exit - appearance mode unsupported on this iOS version.")
      expect_any_instance_of(DeviceState).to_not receive(:remove_dark_mode_file)
      subject = BrowserStack::SwitchMode.new(uuid, session_id, product)
      expect(subject.change_appearance_to("Light")).to eql(false)
    end

    it 'should raise log event to eds and zombie when test fails with no such element' do
      event_logger("Light", "fail", "no such element", "Display&Brightness option could not be located.")
      expect_any_instance_of(DeviceState).to_not receive(:remove_dark_mode_file)
      subject = BrowserStack::SwitchMode.new(uuid, session_id, product)
      expect(subject.change_appearance_to("Light")).to eql(false)
    end

    it 'should raise log event to eds and zombie when test fails with no such element dark mode button' do
      event_logger("Light", "fail", "no such element", "Failed switch to light mode due to button not found.")
      expect_any_instance_of(DeviceState).to_not receive(:remove_dark_mode_file)
      subject = BrowserStack::SwitchMode.new(uuid, session_id, product)
      expect(subject.change_appearance_to("Light")).to eql(false)
    end
  end
end

