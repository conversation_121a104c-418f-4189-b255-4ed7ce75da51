require_relative '../spec_helper'
require_relative '../../lib/helpers/blueutil_helper'

describe BrowserStack::BlueutilHelper do
  let(:system_profiler_sample_output) { "{ \"SPBluetoothDataType\" : [  { \"local_device_title\" : {\"general_name\" : \"app Mac mini (108)\" } ]}" }
  let(:blueutil_binary_path) { "/usr/local/.browserstack/deps/blueutil/bin/blueutil" }
  describe '#enable_bluetooth' do
    it 'should enable mini bluetooth' do
      expect(BrowserStack::OSUtils).to receive(:execute).with("/usr/local/bin/gtimeout -s KILL 25 #{blueutil_binary_path} -p 1 -d 1")
      subject = BrowserStack::BlueutilHelper.new
      subject.enable_bluetooth
    end
  end

  describe '#disable_bluetooth' do
    it 'should disable mini bluetooth' do
      expect(BrowserStack::OSUtils).to receive(:execute).with("BLUEUTIL_ALLOW_ROOT=1 /usr/local/bin/gtimeout -s KILL 5 #{blueutil_binary_path} -p 0 -d 0")
      subject = BrowserStack::BlueutilHelper.new
      subject.disable_bluetooth
    end
  end

  describe '#device_address_disconnected?' do
    it 'should true if device is disconnected to mini via bluetooth' do
      expect(BrowserStack::OSUtils).to receive(:execute).with("#{blueutil_binary_path} --connected | grep \"sample-address\" | wc -l").and_return("0")
      subject = BrowserStack::BlueutilHelper.new
      expect(subject.device_address_disconnected?("sample-address")).to eql(true)
    end

    it 'should false if device is connected to mini via bluetooth' do
      expect(BrowserStack::OSUtils).to receive(:execute).with("#{blueutil_binary_path} --connected | grep \"sample-address\" | wc -l").and_return("1")
      subject = BrowserStack::BlueutilHelper.new
      expect(subject.device_address_disconnected?("sample-address")).to eql(false)
    end
  end
end

