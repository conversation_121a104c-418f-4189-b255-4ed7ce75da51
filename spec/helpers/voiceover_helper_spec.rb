require_relative '../spec_helper'
require_relative '../../lib/helpers/voiceover_helper'

describe BrowserStack::VoiceoverHelper do
  let(:system_profiler_sample_output) { "{\"SPBluetoothDataType\":[{\"local_device_title\":{\"general_name\":\"app Mac mini (108)\"}}]}" }
  let(:voiceover_config_file_path) { "/usr/local/.browserstack/realmobile/config/custom_devices/voiceover_devices.json" }
  let(:ventura_mini_ip) { "*************" }
  let(:bluetooth_server_ip) { "*************" }
  let(:dummy_bluetooth_device_address) { "1a-2f-4d-4g-8p" }
  let(:mock_response_ok) { instance_double('Faraday::Response', status: 200, body: '{"disconnected" : "false", "success" : "true", "mini_bluetooth_name": "mac_mini"}', success?: true) }
  let(:mock_http_response_error) { instance_double('Faraday::Response', status: 500, success?: true) }
  let(:session_id) { "qwerty" }
  let(:device_id) { "000087723-2349798" }
  let(:mini_bluetooth_name) { "app Mac mini (108)" }
  let(:file_handle) { double("file_handle") }

  before do
    allow(BrowserStack::OSUtils).to receive(:execute).with("system_profiler SPBluetoothDataType -json").and_return(system_profiler_sample_output)
    allow(BrowserStack::OSUtils).to receive(:execute).with("jq .bluetooth_server_mappings #{voiceover_config_file_path}").and_return('{ "*************": "*************" }')
    allow(BrowserStack::OSUtils).to receive(:execute).with("ls /usr/local/.browserstack/state_files | grep voiceover_used_ | wc -l").and_return('5')
    allow_any_instance_of(BrowserStack::VoiceoverHelper).to receive(:machine_ip).and_return(ventura_mini_ip)
    allow_any_instance_of(BrowserStack::BlueutilHelper).to receive(:machine_ip).and_return(ventura_mini_ip)
    allow(Utils).to receive(:send_to_eds)
  end

  describe '#voiceover_device?' do
    before do
      stub_const('VOICEOVER_DEVICES', { "devices" => ["dummy_id"] })
    end

    it 'should return false if device id is not present in VOICEOVER_DEVICES' do
      expect(BrowserStack::VoiceoverHelper.voiceover_device?("dummy_id")).to eql(true)
    end

    it 'should return true if device id is present in VOICEOVER_DEVICES' do
      expect(BrowserStack::VoiceoverHelper.voiceover_device?("dummy_idd")).to eql(false)
    end
  end

  describe "#pre_voiceover_setup" do
    device = 'some_device_id'
    let(:device_state) { DeviceState.new(device) }
    let(:mock_wda_client) { double('mock_wda_client') }
    let(:wda_port) { 8400 }
    let(:device_config) { { 'webdriver_port' => wda_port } }

    before do
      allow(DeviceState).to receive(:new).and_return(device_state)
      allow(WdaClient).to receive(:new).and_return(mock_wda_client)
      allow(Thread).to receive(:bs_run)
      DeviceManager.class_variable_set(:@@devices_conf, { "devices" => { device.to_s => device_config } } )
    end

    it 'should do nothing if both lseIOSBluetoothVoiceover and is_ios_screen_reader_public_cloud_device params are false' do
      params = {
        "lseIOSBluetoothVoiceover" => false,
        "is_ios_screen_reader_public_cloud_device" => false
      }
      expect(BrowserStack::VoiceoverHelper).to_not receive(:new)
      BrowserStack::VoiceoverHelper.pre_voiceover_setup(device, params)
    end

    it 'should apply MDM profile restriction, avoid giving settings access, and mark voiceover used if only is_ios_screen_reader_public_cloud_device is true' do
      params = {
        "lseIOSBluetoothVoiceover" => false,
        "is_ios_screen_reader_public_cloud_device" => true
      }
      expect_any_instance_of(BrowserStack::VoiceoverHelper).to receive(:mark_voiceover_used)
      expect(mock_wda_client).to_not receive(:settings_access)
      BrowserStack::VoiceoverHelper.pre_voiceover_setup(device, params)
    end

    it 'should apply MDM profile restriction, give settings access, and mark voiceover used if only lseIOSBluetoothVoiceover is true' do
      params = {
        "lseIOSBluetoothVoiceover" => true,
        "is_ios_screen_reader_public_cloud_device" => false
      }
      expect(DeviceManager).to receive(:device_configuration_check).with(device).and_return(device_config)
      expect_any_instance_of(BrowserStack::VoiceoverHelper).to receive(:mark_voiceover_used)
      expect(mock_wda_client).to receive(:settings_access).with("true", "false", "false", "true")
      BrowserStack::VoiceoverHelper.pre_voiceover_setup(device, params)
    end
  end

  describe '#mini_bluetooth_name' do
    it 'should return mini bluetooth name' do
      expect(BrowserStack::OSUtils).to receive(:execute).with("system_profiler SPBluetoothDataType -json").and_return(system_profiler_sample_output)
      subject = BrowserStack::VoiceoverHelper.new
      expect(subject.mini_bluetooth_name).to eql(mini_bluetooth_name)
    end

    it 'should raise exception if mini bluetooth name is not found from system_profiler command' do
      expect(BrowserStack::OSUtils).to receive(:execute).with("system_profiler SPBluetoothDataType -json").and_return("{}")
      subject = BrowserStack::VoiceoverHelper.new
      error_message = "Could not find mini's bluetooth name using system_profiler command"
      expect { subject.mini_bluetooth_name }.to raise_error(RuntimeError, error_message)
    end
  end

  describe '#start_interaction_server' do
    it "should nothing if interaction server process is already running" do
      expect(BrowserStack::OSUtils).to receive(:execute).with("lsof -i:9001 | grep -v PID | wc -l").and_return("1")
      subject = BrowserStack::VoiceoverHelper.new
      subject.start_interaction_server
    end

    it "should start interaction server if no process is running" do
      expect(BrowserStack::OSUtils).to receive(:execute).with("lsof -i:9001 | grep -v PID | wc -l").and_return("0")
      expect(Utils).to receive(:fork_process)
      subject = BrowserStack::VoiceoverHelper.new
      subject.start_interaction_server
    end
  end

  describe '#kill_interaction_server' do
    it 'should kill interaction-server' do
      expect(BrowserStack::OSUtils).to receive(:execute).with("lsof -i:9001 | grep -v PID | awk '{print $2}' | xargs kill")
      subject = BrowserStack::VoiceoverHelper.new
      subject.kill_interaction_server
    end
  end

  describe '#active_voiceover_sessions' do
    it 'return number of currently active iOS voiceover sessions' do
      expect(BrowserStack::OSUtils).to receive(:execute).with("ls /usr/local/.browserstack/state_files | grep voiceover_used_ | wc -l").and_return('5')
      subject = BrowserStack::VoiceoverHelper.new
      expect(subject.active_voiceover_sessions).to eql(5)
    end
  end

  describe '#delete_stale_voiceover_used_files' do
    it 'should do nothing if no used files are present' do
      expect(BrowserStack::OSUtils).to receive(:execute).with("ls /usr/local/.browserstack/state_files | grep voiceover_used_*").and_return('')
      subject = BrowserStack::VoiceoverHelper.new
      subject.delete_stale_voiceover_used_files
    end

    it 'should delete > 4 hour old used files' do
      voiceover_used_file = "/usr/local/.browserstack/state_files/voiceover_used_abc"
      expect(BrowserStack::OSUtils).to receive(:execute).with("ls /usr/local/.browserstack/state_files | grep voiceover_used_*").and_return('voiceover_used_abc')
      expect(File).to receive(:exist?).with(voiceover_used_file).and_return(true)
      last_created_time_241_mins_ago = Time.now - 241 * 60
      expect(File).to receive(:ctime).with(voiceover_used_file).and_return(last_created_time_241_mins_ago)
      expect(FileUtils).to receive(:rm_f).with(voiceover_used_file)
      subject = BrowserStack::VoiceoverHelper.new
      subject.delete_stale_voiceover_used_files
    end

    it 'should not delete < 4 hour old used files' do
      voiceover_used_file = "/usr/local/.browserstack/state_files/voiceover_used_abc"
      expect(BrowserStack::OSUtils).to receive(:execute).with("ls /usr/local/.browserstack/state_files | grep voiceover_used_*").and_return('voiceover_used_abc')
      expect(File).to receive(:exist?).with(voiceover_used_file).and_return(true)
      last_created_time_239_mins_ago = Time.now - 239 * 60
      expect(File).to receive(:ctime).with(voiceover_used_file).and_return(last_created_time_239_mins_ago)
      expect(FileUtils).to_not receive(:rm_f).with(voiceover_used_file)
      subject = BrowserStack::VoiceoverHelper.new
      subject.delete_stale_voiceover_used_files
    end
  end

  describe '#machine_ip' do
    it 'should return machine IP from config' do
      allow(BrowserStack::Configuration).to receive_message_chain(:new, :all).and_return({
        "ip_file" => "/path/to/ip/file",
        "platform_category" => "ios_njb_17"
      })
      allow_any_instance_of(BrowserStack::VoiceoverHelper).to receive(:machine_ip).and_call_original
      allow(File).to receive(:read).with("/path/to/ip/file").and_return("*******")
      subject = BrowserStack::VoiceoverHelper.new
      expect(subject.machine_ip).to eql("*******")
    end
  end

  describe '#bluetooth_server_mappings' do
    it 'should return key value pairs of bluetooth server' do
      allow(BrowserStack::OSUtils).to receive(:execute).with("jq .bluetooth_server_mappings #{voiceover_config_file_path}").and_return('{"*******" : "*******"}')
      subject = BrowserStack::VoiceoverHelper.new
      expect(subject.bluetooth_server_mappings).to eql({ "*******" => "*******" })
    end
  end

  describe '#device_address_disconnected?' do
    it 'should return true if device address is disconnected' do
      allow_any_instance_of(BrowserStack::VoiceoverHelper).to receive(:machine_ip).and_return(ventura_mini_ip)
      mock_response_disconnected_true = instance_double('Faraday::Response', status: 200, body: '{"disconnected" : "true"}', success?: true)
      allow(BrowserStack::HttpUtils).to receive(:make_get_request).with("http://#{bluetooth_server_ip}:45671/voiceover_bluetooth_connection_status?address=#{dummy_bluetooth_device_address}", 15).and_return(mock_response_disconnected_true)
      subject = BrowserStack::VoiceoverHelper.new
      expect(subject.device_address_disconnected?(dummy_bluetooth_device_address)).to eql(true)
    end

    it 'should return false if device address is connected' do
      allow_any_instance_of(BrowserStack::VoiceoverHelper).to receive(:machine_ip).and_return(ventura_mini_ip)
      mock_response_disconnected_false = instance_double('Faraday::Response', status: 200, body: '{"disconnected" : "false"}', success?: true)
      expect(BrowserStack::HttpUtils).to receive(:make_get_request).with("http://#{bluetooth_server_ip}:45671/voiceover_bluetooth_connection_status?address=#{dummy_bluetooth_device_address}", 15).and_return(mock_response_disconnected_false)
      subject = BrowserStack::VoiceoverHelper.new
      expect(subject.device_address_disconnected?(dummy_bluetooth_device_address)).to eql(false)
    end
  end

  describe '#setup' do
    it 'should do the setup and return mini bluetooth name if API call is successful' do
      expect(BrowserStack::HttpUtils).to receive(:make_get_request).with("http://#{bluetooth_server_ip}:45671/voiceover_init?session_id=#{session_id}&device_id=#{device_id}", 15).and_return(mock_response_ok)
      subject = BrowserStack::VoiceoverHelper.new
      expect(subject.setup(device_id, session_id)).to eql("mac_mini")
    end

    it 'raise error if API call fails' do
      expect(BrowserStack::HttpUtils).to receive(:make_get_request).with("http://#{bluetooth_server_ip}:45671/voiceover_init?session_id=#{session_id}&device_id=#{device_id}", 15).and_return(mock_http_response_error)
      subject = BrowserStack::VoiceoverHelper.new
      expect { subject.setup(device_id, session_id) }.to raise_error(RuntimeError, "API call to endpoint : http://*************:45671/voiceover_init?session_id=qwerty&device_id=000087723-2349798 failed")
    end

    it 'raise error if JSON parse fails' do
      mock_response_success_invalid_json = instance_double('Faraday::Response', status: 200, body: 'yo', success?: true)
      expect(BrowserStack::HttpUtils).to receive(:make_get_request).with("http://#{bluetooth_server_ip}:45671/voiceover_init?session_id=#{session_id}&device_id=#{device_id}", 15).and_return(mock_response_success_invalid_json)
      subject = BrowserStack::VoiceoverHelper.new
      expect { subject.setup(device_id, session_id) }.to raise_error(RuntimeError)
    end
  end

  describe '#write_session_params_to_voiceover_used_file' do
    it 'should write the given params to the voiceover used file' do
      params = {}
      allow(BrowserStack::Configuration).to receive_message_chain(:new, :all).and_return({
        "ip_file" => "/path/to/ip/file",
        "platform_category" => "ios_njb_17"
      })
      expect(File).to receive(:open).with("#{STATE_FILES_DIR}/voiceover_used_#{device_id}", "w").and_yield(file_handle)
      expect(file_handle).to receive(:write).with(params.to_json).and_return(true)
      subject = BrowserStack::VoiceoverHelper.new
      subject.write_session_params_to_voiceover_used_file(device_id, params)
    end
  end

  describe '#mark_voiceover_used' do
    it 'should touch voiceover used file in bluetooth client and send an API request to bluetooth server' do
      expect_any_instance_of(DeviceState).to receive(:touch_voiceover_used_file)
      expect(BrowserStack::HttpUtils).to receive(:make_get_request).with("http://#{bluetooth_server_ip}:45671/voiceover_mark_device_used?device_id=#{device_id}", 15).and_return(mock_response_ok)
      subject = BrowserStack::VoiceoverHelper.new
      expect(subject.mark_voiceover_used(device_id, {})).to eql(true)
    end

    it 'raise error if API call fails' do
      expect_any_instance_of(DeviceState).to receive(:touch_voiceover_used_file)
      expect(BrowserStack::HttpUtils).to receive(:make_get_request).with("http://#{bluetooth_server_ip}:45671/voiceover_mark_device_used?device_id=#{device_id}", 15).and_return(mock_http_response_error)
      subject = BrowserStack::VoiceoverHelper.new
      expect { subject.mark_voiceover_used(device_id, {}) }.to raise_error(RuntimeError)
    end

    it 'raise error if JSON parse fails' do
      mock_response_success_invalid_json = instance_double('Faraday::Response', status: 200, body: 'yo', success?: true)
      expect(BrowserStack::HttpUtils).to receive(:make_get_request).with("http://#{bluetooth_server_ip}:45671/voiceover_mark_device_used?device_id=#{device_id}", 15).and_return(mock_response_success_invalid_json)
      subject = BrowserStack::VoiceoverHelper.new
      expect { subject.mark_voiceover_used(device_id, {}) }.to raise_error(RuntimeError)
    end
  end

  describe '#trigger_cleanup_on_bluetooth_server' do
    it 'should trigger API request to bluetooth server for cleanup' do
      expect(BrowserStack::HttpUtils).to receive(:make_get_request).with("http://#{bluetooth_server_ip}:45671/voiceover_cleanup?device_id=#{device_id}", 15).and_return(mock_response_ok)
      subject = BrowserStack::VoiceoverHelper.new
      expect(subject.trigger_cleanup_on_bluetooth_server(device_id)).to eql(true)
    end

    it 'raise error if API call fails' do
      expect(BrowserStack::HttpUtils).to receive(:make_get_request).with("http://#{bluetooth_server_ip}:45671/voiceover_cleanup?device_id=#{device_id}", 15).and_return(mock_http_response_error)
      subject = BrowserStack::VoiceoverHelper.new
      expect { subject.trigger_cleanup_on_bluetooth_server(device_id) }.to raise_error(RuntimeError)
    end

    it 'raise error if JSON parse fails' do
      mock_response_success_invalid_json = instance_double('Faraday::Response', status: 200, body: 'yo', success?: true)
      expect(BrowserStack::HttpUtils).to receive(:make_get_request).with("http://#{bluetooth_server_ip}:45671/voiceover_cleanup?device_id=#{device_id}", 15).and_return(mock_response_success_invalid_json)
      subject = BrowserStack::VoiceoverHelper.new
      expect { subject.trigger_cleanup_on_bluetooth_server(device_id) }.to raise_error(RuntimeError)
    end
  end

  describe '#cleanup' do
    before do
      expect_any_instance_of(BrowserStack::VoiceoverHelper).to receive(:delete_stale_voiceover_used_files)
      expect(File).to receive(:exist?).with("#{STATE_FILES_DIR}/voiceover_used_#{device_id}").and_return(true)
      expect(FileUtils).to receive(:rm_f).with("#{STATE_FILES_DIR}/voiceover_used_#{device_id}")
    end

    it 'should not do cleanup if there are active voiceover sessions' do
      expect_any_instance_of(BrowserStack::VoiceoverHelper).to receive(:active_voiceover_sessions).and_return(5)
      expect_any_instance_of(BrowserStack::VoiceoverHelper).to_not receive(:kill_interaction_server)
      subject = BrowserStack::VoiceoverHelper.new
      subject.cleanup(device_id)
    end

    it 'should do cleanup if no sessions are active at the moment' do
      expect_any_instance_of(BrowserStack::VoiceoverHelper).to receive(:active_voiceover_sessions).and_return(0)
      expect_any_instance_of(BrowserStack::VoiceoverHelper).to receive(:kill_interaction_server)
      expect_any_instance_of(BrowserStack::BlueutilHelper).to receive(:disable_bluetooth)
      subject = BrowserStack::VoiceoverHelper.new
      subject.cleanup(device_id)
    end
  end

  describe '#start_bluetooth_and_interaction_server' do
    it 'should start bluetooth server, interaction server and return mini bluetooth name' do
      expect_any_instance_of(BrowserStack::BlueutilHelper).to receive(:enable_bluetooth)
      expect_any_instance_of(BrowserStack::VoiceoverHelper).to receive(:start_interaction_server)
      expect(BrowserStack::OSUtils).to receive(:execute).with("system_profiler SPBluetoothDataType -json").and_return(system_profiler_sample_output)
      subject = BrowserStack::VoiceoverHelper.new
      expect(subject.start_bluetooth_and_interaction_server).to eql(mini_bluetooth_name)
    end
  end
end
