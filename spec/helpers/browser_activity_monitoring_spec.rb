require_relative '../spec_helper'
require_relative '../../lib/helpers/browser_activity_monitoring'

RSpec.describe BrowserActivityMonitoring do
  let(:session_id) { "session123" }
  let(:device_id) { "device123" }
  let(:debugger_port) { 9222 }
  let(:genre) { "test_genre" }
  let(:bam) { described_class.new(session_id, device_id, debugger_port, genre) }
  let(:start_file_path) { described_class.start_file(device_id) }

  describe '#initialize' do
    it 'initializes with correct parameters' do
      expect(bam.instance_variable_get(:@session_id)).to eq(session_id)
      expect(bam.instance_variable_get(:@device_id)).to eq(device_id)
      expect(bam.instance_variable_get(:@debugger_port)).to eq(debugger_port)
      expect(bam.instance_variable_get(:@genre)).to eq(genre)
      expect(bam.instance_variable_get(:@history)).to eq({})
    end
  end

  describe '#start' do
    before do
      allow(Thread).to receive(:bs_run).and_yield
      allow(FileUtils).to receive(:touch)
      allow(File).to receive(:exist?).and_return(true, false)
      allow(File).to receive(:delete)
    end

    it 'creates the start file and sends history to EDS' do
      expect(FileUtils).to receive(:touch).with(start_file_path)
      expect(bam).to receive(:send_history_to_eds).once
      bam.start
    end
  end

  describe '.start_file' do
    it 'returns the correct start file path' do
      start_file = "#{STATE_FILES_DIR}/start_monitoring_#{device_id}"
      expect(described_class.start_file(device_id)).to eq(start_file)
    end
  end

  describe '.running?' do
    before do
      allow(File).to receive(:exist?).and_return(true)
    end

    it 'returns true if start file exists' do
      expect(described_class.running?(device_id)).to be true
    end

    it 'returns false if start file does not exist' do
      allow(File).to receive(:exist?).and_return(false)
      expect(described_class.running?(device_id)).to be false
    end
  end
end
