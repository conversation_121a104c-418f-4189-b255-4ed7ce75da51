# Mocked Logger, with message caching.
class MockLogger
  LOG_LEVELS = %i[debug info warn error fatal unknown].freeze

  attr_accessor :params

  def initialize
    @logs_cache = Hash.new([])
    @params = {}
  end

  LOG_LEVELS.each do |log_level|
    define_method("cached_#{log_level}_logs") do
      @logs_cache[log_level]
    end
  end

  def method_missing(m, *args)
    raise "#{self} not configured for log level: #{m}" unless LOG_LEVELS.include?(m)

    @logs_cache[m] += [args[0]]
  end

  def respond_to_missing?(m, *args)
    LOG_LEVELS.include?(m)
  end
end

if $PROGRAM_NAME == __FILE__
  require 'test/unit'
  include Test::Unit::Assertions

  logger = MockLogger.new
  logger.info("log message")
  assert_equal(logger.cached_info_logs, ["log message"])
  assert_equal(logger.cached_error_logs, [])
end
