require_relative '../spec_helper'
require_relative '../../lib/recover_device'

describe BrowserStack::RecoverDevice do
  let(:device_id) { "device_id" }
  let(:device_os_version) { 16.0 }
  let(:ecid) { "ecid" }
  let(:recover_device) { BrowserStack::RecoverDevice.new(device_id, ecid) }
  let(:device_address) { "20" }
  let(:address_command) { "address_command" }
  let(:reset_command) { "reset_command" }
  let(:lockdown_code) { "-100" }
  let(:idevice_info) { double("idevice_info") }

  describe "#attempt" do
    context "device is conencted" do
      it "should return without doing anything" do
        expect(recover_device).to receive(:connected?).and_return(true)
        expect(recover_device).not_to receive(:device_on_usb?)
        recover_device.attempt
      end
    end

    context "device is not on usb" do
      it "should raise exception without resetting the usb" do
        allow(RecoveryMode).to receive(:check_and_exit).and_return('')
        expect(recover_device).to receive(:connected?).and_return(false)
        expect(recover_device).to receive(:device_address_from_ioreg).and_return("")
        expect { recover_device.attempt }.to raise_error(BrowserStack::RecoverDevice::DeviceNotOnUSB, "manual fix required: device is not on usb")
      end
    end

    context "device is on usb but not on ideviceinfo" do
      it "should not raise an error if device is recovered" do
        expect(recover_device).to receive(:connected?).once.ordered.and_return(true)
        expect { recover_device.attempt }.not_to raise_error
      end

      it "should raise lockdown error if device is in lockdown" do
        allow(RecoveryMode).to receive(:check_and_exit).and_return('')
        expect(recover_device).to receive(:connected?).twice.and_return(false)
        expect(recover_device).to receive(:pre_recover_checks).twice
        expect(recover_device).to receive(:recover)
        expect(IdeviceUtils).to receive(:ideviceinfo).with(device_id, "ProductType").and_return(idevice_info)
        expect(idevice_info).to receive(:first).and_return("idevice_info")
        expect(recover_device).to receive(:lockdown_code).and_return(lockdown_code)
        expect { recover_device.attempt }.to raise_error(BrowserStack::RecoverDevice::DeviceInLockdown, "Lockdown issue # -100")
      end

      it "should raise device on usb but not on ideviceinfo if device is still on usb after recovery but did not come online on ideviceinfo" do
        allow(RecoveryMode).to receive(:check_and_exit).and_return('')
        expect(recover_device).to receive(:connected?).twice.and_return(false)
        expect(recover_device).to receive(:pre_recover_checks).twice
        expect(recover_device).to receive(:recover)
        expect(IdeviceUtils).to receive(:ideviceinfo).with(device_id, "ProductType").and_return(idevice_info)
        expect(idevice_info).to receive(:first).and_return("idevice_info")
        expect(recover_device).to receive(:device_on_usb?).and_return(true)
        expect { recover_device.attempt }.to raise_error(BrowserStack::RecoverDevice::DeviceNotOnIdeviceInfo)
      end

      it "should raise device not on usb if device is not on usb after reset" do
        allow(RecoveryMode).to receive(:check_and_exit).and_return('')
        expect(recover_device).to receive(:connected?).twice.and_return(false)
        expect(recover_device).to receive(:pre_recover_checks).twice
        expect(recover_device).to receive(:recover)
        expect(IdeviceUtils).to receive(:ideviceinfo).with(device_id, "ProductType").and_return(idevice_info)
        expect(idevice_info).to receive(:first).and_return("idevice_info")
        expect(recover_device).to receive(:device_on_usb?).and_return(false)
        expect { recover_device.attempt }.to raise_error(BrowserStack::RecoverDevice::DeviceNotOnUSB)
      end
    end
  end

  # Private Methods

  describe ".lockdown_code_present?" do
    it "true when we have lockdown code" do
      expect(recover_device.send(:lockdown_code_present?, lockdown_code)).to eql(true)
    end

    it "false when we don't have lockdown code" do
      expect(recover_device.send(:lockdown_code_present?, false)).to eql(false)
    end
  end

  describe ".lockdown_reason" do
    context "not in lockdown" do
      it "should indicate that device is not in lockdown" do
        expect(recover_device.send(:lockdown_reason, false)).to eql("I am not in lockdown")
      end
    end

    context "device in lockdown" do
      before(:each) do
        allow(recover_device).to receive(:lockdown_code_present?).and_return(true)
      end

      it "should indicate trust issue when we have trust issue" do
        ['-18', '-19', '-5'].each do |code|
          expect(recover_device.send(:lockdown_reason, code)).to eql("I have trust issue # #{code}")
        end
      end

      it "should return a lockdown message if not a trust issue" do
        expect(recover_device.send(:lockdown_reason, -100)).to eql("Lockdown issue # -100")
      end
    end
  end

  describe ".connected?" do
    let(:lockdown_code) { "code" }

    before(:each) do
      allow(IdeviceUtils).to receive(:ideviceinfo).with(device_id, "ProductType").and_return(idevice_info)
      allow(IdeviceUtils).to receive(:has_passcode?).with(device_id).and_return(false)
      allow(IdeviceUtils).to receive(:use_devicectl).with(device_id).and_return(false)
      allow(idevice_info).to receive(:first).and_return("idevice_info")
    end

    context "device is connected to idevice info" do
      it "should return true" do
        # mocking exit status
        fork { exit 0 }
        Process.wait
        expect(recover_device).to receive(:device_on_idevice_info?).with("idevice_info").and_return(true)
        expect(recover_device.send(:connected?)).to eq(true)
      end
    end

    context "device is not connected" do
      it "should return false when device is not on idevice info" do
        # mocking exit status
        fork { exit 0 }
        Process.wait
        expect(recover_device).to receive(:device_on_idevice_info?).and_return(false)
        expect(recover_device.send(:connected?)).to eq(false)
      end

      it "should return false when ideviceinfo command throws non-zero exit code" do
        # mocking exit status
        fork { exit 1 }
        Process.wait
        expect(recover_device.send(:connected?)).to eq(false)
      end
    end
  end

  describe ".reset_usb_with_address" do
    context "device is not on usb" do
      it "should not reset if adress is empty" do
        expect(recover_device).not_to receive(:usb_reset_command)
        expect(BrowserStack::OSUtils).not_to receive(:execute)
        recover_device.send(:reset_usb_with_address, "")
      end
    end

    context "device is on the usb" do
      before(:each) do
        expect(recover_device).to receive(:usb_reset_command).and_return(reset_command)
      end

      it "should execute usb reset command" do
        expect(BrowserStack::OSUtils).to receive(:execute).with(reset_command)
        recover_device.send(:reset_usb_with_address, device_address)
      end

      it "should fail silently" do
        expect(BrowserStack::OSUtils).to receive(:execute).with(reset_command).and_raise(StandardError)
        expect { recover_device.send(:reset_usb_with_address, device_address) }.not_to raise_error
      end
    end
  end
end
