require_relative '../spec_helper'
require_relative '../../lib/custom_exceptions'

describe BrowserStackTestExecutionError do
  describe '#build_message' do
    context 'when output arg is not passed' do
      let(:error) { BrowserStackTestExecutionError.new('test_class', 'test_func') }
      it 'defaults it\'s test_output value to empty string' do
        expect(error.test_output).to eq('')
      end
    end

    context 'when output does not contain BUILD INTERRUPTED' do
      let(:error) { BrowserStackTestExecutionError.new('test_class', 'test_func', 'some test run error') }
      it 'returns message containing \'failed\'' do
        expect(error.message.include?('failed')).to eq(true)
      end
    end

    context 'when output does contain BUILD INTERRUPTED' do
      let(:error) { BrowserStackTestExecutionError.new('test_class', 'test_func', 'test BUILD INTERRUPTED error') }
      it 'returns message containing \'timed out\'' do
        expect(error.message.include?('timed out')).to eq(true)
      end
    end

    context 'when there is an underlying error in output' do
      let(:error) { BrowserStackTestExecutionError.new('test_class', 'test_func', 'test error Underlying Error: error_message.') }
      it 'returns this underlying error in the message' do
        expect(error.message.include?('error_message')).to eq(true)
      end
    end
  end
end