require_relative '../spec_helper'
require_relative '../../lib/upgraded_device_check_state'
require './spec/support/file_helper'

RSpec.configure do |c|
  c.include FileHelpers
end

describe BrowserStack::UpgradedDeviceCheckState do
  let(:device) { "device" }
  let(:device_state) { DeviceState.new(device) }
  let(:file_utils) { double(FileUtils.new) }
  let(:upgraded_device_check_state) { BrowserStack::UpgradedDeviceCheckState.new(device) }
  let(:test_flight_automation_double) { double(Automation::TestFlight) }

  before do
    allow(DeviceState).to receive(:new).with(device).and_return(device_state)
    allow(Automation::TestFlight).to receive(:new).and_return(test_flight_automation_double)
  end

  describe "#remove_location_files" do
    it 'should remove location related files' do
      expect(device_state).to receive(:remove_location_services_enabled_file).and_return true
      expect(device_state).to receive(:remove_appstore_location_services_popup_file).and_return true
      expect(device_state).to receive(:remove_location_service_for_app_session_file).and_return true
      expect(device_state).to receive(:remove_current_locale_app_live_file).and_return true
      upgraded_device_check_state.remove_location_files
    end
  end

  describe "#remove_camera_files" do
    it 'should remove camera files' do
      expect(device_state).to receive(:remove_back_camera_file).and_return true
      expect(device_state).to receive(:remove_front_camera_file).and_return true
      expect(device_state).to receive(:remove_last_camera_check_file).and_return true
      upgraded_device_check_state.remove_camera_files
    end
  end

  describe "#remove_orientation_files" do
    it 'should remove orientation files' do
      expect(device_state).to receive(:remove_force_check_orientation_lock_file).and_return true
      upgraded_device_check_state.remove_orientation_files
    end
  end

  describe "#remove_safari_state_files" do
    it 'should remove safari state files' do
      expect(device_state).to receive(:remove_enable_safari_web_inspector_file).and_return true
      expect(device_state).to receive(:remove_force_clean_safari_file).and_return true
      expect(device_state).to receive(:remove_force_clean_safari_bookmarks_file).and_return true
      expect(device_state).to receive(:remove_force_clean_safari_favorites_file).and_return true
      upgraded_device_check_state.remove_safari_state_files
    end
  end

  describe "#remove_auto_lock_files" do
    it 'should remove auto lock state file' do
      expect(device_state).to receive(:remove_disable_auto_lock_file).and_return true
      upgraded_device_check_state.remove_auto_lock_files
    end
  end

  describe "#remove_government_notifications_file" do
    it 'should remove government notification state file' do
      expect(device_state).to receive(:remove_government_notifications_file).and_return true
      upgraded_device_check_state.remove_government_notifications_file
    end
  end

  describe "#remove_update_failed" do
    it 'should remove update failed state file' do
      expect(device_state).to receive(:remove_update_failed_file).and_return true
      upgraded_device_check_state.remove_update_failed_file
    end
  end

  describe "#remove_wda_files" do
    let(:wda_files)  do
      [
      "#{TMP_DIR_PATH}/#{device}_abc_wda.zip",
      "#{TMP_DIR_PATH}/#{device}_abc_wda.zip.md5"
    ]
    end
    it 'should remove wda files' do
      expect(Dir).to receive(:glob).with("#{TMP_DIR_PATH}/#{device}_*_wda.zip*").and_return(wda_files)
      expect(wda_files).to receive(:each) { |&block|
        expect(FileUtils).to receive(:rm_f)
        block.call
      }
      upgraded_device_check_state.remove_wda_files
    end
  end

  describe "#remove_apps_files" do
    let(:apps_files)  do
      [
      "#{TMP_DIR_PATH}/#{device}_installed_apps/123",
      "#{TMP_DIR_PATH}/#{device}_installed_apps/456"
    ]
    end
    it 'should remove apps files' do
      expect(Dir).to receive(:glob).with("#{TMP_DIR_PATH}/#{device}_installed_apps/*").and_return(apps_files)
      expect(apps_files).to receive(:each) { |&block|
        expect(FileUtils).to receive(:rm_f)
        block.call
      }
      expect(device_state).to receive(:remove_browserstack_app_built_version_file).and_return true
      upgraded_device_check_state.remove_apps_files
    end
  end

  describe "#remove_device_logger_files" do
    it 'should remove files created by device logger' do
      expect(device_state).to receive(:remove_device_logger_pid_file).and_return true
      expect(device_state).to receive(:remove_device_logger_session_end_pid_file).and_return true
      upgraded_device_check_state.remove_device_logger_files
    end
  end

  describe "#discard_old_states" do
    it 'should uninstall testflight and invoke file removal methods' do
      expect(test_flight_automation_double).to receive(:uninstall)
      expect(upgraded_device_check_state).to receive(:remove_location_files)
      expect(upgraded_device_check_state).to receive(:remove_camera_files)
      expect(upgraded_device_check_state).to receive(:remove_orientation_files)
      expect(upgraded_device_check_state).to receive(:remove_safari_state_files)
      expect(upgraded_device_check_state).to receive(:remove_auto_lock_files)
      expect(upgraded_device_check_state).to receive(:remove_government_notifications_file)
      expect(upgraded_device_check_state).to receive(:remove_wda_files)
      expect(upgraded_device_check_state).to receive(:remove_apps_files)
      expect(upgraded_device_check_state).to receive(:remove_device_logger_files)
      expect(upgraded_device_check_state).to receive(:remove_update_failed_file)
      upgraded_device_check_state.discard_old_states
    end
  end

  describe "#remove_testflight_files" do
    it 'should remove testflight files' do
      %i[
        remove_disabled_testflight_notifs_file
        remove_installed_testflight_file
        remove_installed_com_apple_testflight_installed_file
        remove_com_apple_testflight_file
      ].each { |m| expect(device_state).to receive(m) }
      upgraded_device_check_state.remove_testflight_files
    end
  end

  describe "#remove_siri_files" do
    it 'should remove siri files' do
      %i[
        remove_disable_siri_contact_suggestions_file
        remove_siri_search_cleaned_file
      ].each { |m| expect(device_state).to receive(m) }
      upgraded_device_check_state.remove_siri_files
    end
  end

  describe "#remove_bluetooth_network_files" do
    it 'should remove bluetooth and apple network files' do
      %i[
        remove_disabled_bluetooth_file
        remove_check_global_proxy_file
        remove_nat_setup_complete_file
      ].each { |m| expect(device_state).to receive(m) }
      upgraded_device_check_state.remove_bluetooth_network_files
    end
  end

  describe "#remove_apple_id_files" do
    it 'should remove bluetooth and apple network files' do
      %i[
        remove_force_clean_apple_id_file
        remove_pwa_enabled_file
      ].each { |m| expect(device_state).to receive(m) }
      upgraded_device_check_state.remove_apple_id_files
    end
  end

  describe "#remove_session_reboot_photos_files" do
    it 'should remove session files' do
      %i[
        remove_photos_permission_file
        remove_reboot_file
        remove_webdriveragent_file
        remove_session_info_file
        remove_session_start_file
        remove_session_lock_file
        remove_session_file
      ].each { |m| expect(device_state).to receive(m) }
      upgraded_device_check_state.remove_session_reboot_photos_files
    end
  end
end
