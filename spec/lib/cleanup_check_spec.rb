require_relative '../spec_helper'
require_relative '../../lib/configuration'
require_relative '../../lib/helpers/automation'
require_relative '../../lib/utils/idevice_utils'
require_relative '../../lib/cleanup_check'
require 'fileutils'

describe CleanupCheck do
  let(:uuid) { 'abcd' }
  let(:device_version) { '13.2.0' }
  let(:device_config) do
    { "current_appium_version" => "1.14.0",
      "device_type" => "iPhone",
      "device_name" => "iPhone 8",
      "device_version" => device_version,
      "selenium_port" => 8080,
      "webdriver_port" => 8401,
      "debugger_port" => 12_345 }
  end
  let(:state_files_dir) { "/usr/local/.browserstack/state_files" }

  before(:all) do
    IdeviceUtils.configure(BrowserStack::Configuration.conf)
  end

  before(:each) do
    allow(IdeviceUtils).to receive(:app_installed?).with(uuid, "TestFlight").and_return(true)
  end

  subject do
    CleanupCheck.new(
      device_config,
      uuid
    )
  end

  describe '#need_photos_permission?' do
    it 'returns true if photos permission file is present' do
      allow(subject.device_state).to receive(:photos_permission_file_present?).and_return(true)
      expect(subject.need_photos_permission?).to be true
    end

    it 'returns false if photos permission file is not present' do
      allow(subject.device_state).to receive(:photos_permission_file_present?).and_return(false)
      expect(subject.need_photos_permission?).to be false
    end
  end

  describe '#need_voiceover_cleanup?' do
    it 'returns true if voiceover used file is present' do
      allow(subject.device_state).to receive(:voiceover_used_file_present?).and_return(true)
      expect(subject.need_voiceover_cleanup?).to be true
    end

    it 'returns false if voiceover used file is not present' do
      allow(subject.device_state).to receive(:voiceover_used_file_present?).and_return(false)
      expect(subject.need_voiceover_cleanup?).to be false
    end
  end

  describe '#need_disable_government_notifications?' do
    it 'returns true if government notifications file is older than specified days' do
      allow(subject.device_state).to receive(:government_notifications_file_clean_on_weekend?).with(CLEANUP_STEPS_FREQ[:disable_government_notifications]).and_return(true)
      expect(subject.need_disable_government_notifications?).to be true
    end

    it 'returns false if government notifications file is not older than specified days' do
      allow(subject.device_state).to receive(:government_notifications_file_clean_on_weekend?).with(CLEANUP_STEPS_FREQ[:disable_government_notifications]).and_return(false)
      expect(subject.need_disable_government_notifications?).to be false
    end
  end

  describe '#need_disable_airplane_mode_from_settings?' do
    it 'returns true if airplane mode is on' do
      allow(IdeviceUtils).to receive(:airplane_mode_on?).with(uuid).and_return(true)
      expect(subject.need_disable_airplane_mode_from_settings?).to be true
    end

    it 'returns false if airplane mode is off' do
      allow(IdeviceUtils).to receive(:airplane_mode_on?).with(uuid).and_return(false)
      expect(subject.need_disable_airplane_mode_from_settings?).to be false
    end
  end
end
