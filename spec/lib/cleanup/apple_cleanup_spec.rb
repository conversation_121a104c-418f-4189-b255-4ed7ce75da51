# frozen_string_literal: true

require_relative '../../../lib/cleanup/apple_cleanup'

describe Cleanup::AppleCleanup do
  let(:device_id) { "1234567890" }
  let(:device_state) { double("DeviceState") }

  subject do
    Class.new(Cleanup::AppleCleanup)
         .new(udid: device_id)
  end

  before(:each) do
    allow(DeviceState).to receive(:new).and_return(device_state)
  end

  context '.new' do
    it 'should initialize the necessary params' do
      expect(subject.udid).to eq(device_id)
      expect(subject.device_state).to eq(device_state)
    end
  end

  context '#run' do
    it 'should raise error when subclass doesnt implement #run' do
      expect do
        subject.send(:run)
      end.to raise_error(Cleanup::AppleCleanup::CleanupError)
    end
  end

  context '#task_runner' do
    it 'should raise error when subclass doesnt implement #task_runner' do
      expect do
        subject.send(:task_runner)
      end.to raise_error(Cleanup::AppleCleanup::CleanupError)
    end
  end

  context '#on_success' do
    it 'should clean all corresponding state files' do
      expect(device_state).to receive(:remove_full_cleanup_file)
      expect(device_state).to receive(:remove_xctest_session_timedout_file)
      expect(device_state).to receive(:remove_manual_cleanup_file)
      expect(device_state).to receive(:remove_device_logger_pid_file)
      expect(device_state).to receive(:remove_device_logger_session_end_pid_file)
      expect(device_state).to receive(:remove_wifi_enabled_file)
      expect(device_state).to receive(:remove_xcuitest_result_bundle_zip_file)
      expect(device_state).to receive(:remove_cleanupdone_file)
      expect(device_state).to receive(:remove_session_start_indicator_file)

      subject.send(:on_success)
    end
  end

  context '#perform' do
    it 'should call run' do
      expect(subject).to receive(:run)
      expect(subject).to receive(:on_success)
      subject.perform
    end
  end
end

