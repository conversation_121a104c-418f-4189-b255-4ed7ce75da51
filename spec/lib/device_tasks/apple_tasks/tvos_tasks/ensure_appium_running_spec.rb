# frozen_string_literal: true

require_relative '../../../../../lib/device_tasks/task_runner'

describe DeviceTasks::TvosTaskRunner do
  let(:udid) { 'dev123' }
  let(:device_config) do
    {
      'current_appium_version' => 'current_appium_version',
      'device_name' => 'device_name',
      'device_version' => 'device_version',

      'selenium_port' => 'selenium_port',
      'webdriver_port' => 'webdriver_port',
      'debugger_port' => 'debugger_port'
    }
  end
  let(:device_state) { DeviceState.new(udid) }
  let(:appium_server_double) do
    instance_double(
      AppiumServer,
      start_server_for_version: true,
      driver: true
    )
  end

  subject { DeviceTasks::TvosTaskRunner.new(udid: udid) }

  before(:each) do
    allow(DeviceState).to receive(:new).with(udid).and_return(device_state)
    allow(DeviceConf).to receive(:[]).with(udid).and_return(device_config)
    cd = BrowserStack::CheckDevice
    allow_any_instance_of(cd).to receive(:check_developer_image_mounted)
    allow(BrowserStack::AppiumServer).to receive(:new).and_return(appium_server_double)
    allow(IdeviceUtils).to receive(:device_version).with(udid).and_return(Gem::Version.new('16'))
    allow(subject).to receive(:apple_tv_device?).and_return(true)
    allow(subject).to receive(:kill_xcode_build_and_iproxy)
  end

  context '#ensure_appium_running' do
    it 'should restart wda and raise error if wda is not running' do
      opts = { check_running: false }
      expect(BrowserStack::AppiumServer).to receive(:new).with(udid, device_config, opts)
                                                         .and_return(appium_server_double)
      force_restart = true
      expect(appium_server_double).to receive(:start_server_for_version)
        .with(anything, force_restart)
        .and_raise(WDALaunchError)
      allow(device_state).to receive(:wda_uninstalled_file_present?).and_return(true)
      expect(AppleTVUtils).to receive(:restart_wda).with(udid)

      expect do
        subject.ensure_appium_running(force_restart: force_restart)
      end.to raise_error(WDALaunchError)
    end

    it 'should return successfully if wda is running and appium is started' do
      opts = { check_running: false }
      expect(BrowserStack::AppiumServer).to receive(:new).with(udid, device_config, opts)
                                                         .and_return(appium_server_double)
      force_restart = true
      expect(appium_server_double).to receive(:start_server_for_version)
        .with(anything, force_restart)
      subject.ensure_appium_running(force_restart: force_restart)
      allow(device_state).to receive(:wda_uninstalled_file_present?)
      allow(device_state).to receive(:remove_wda_uninstalled_file)
    end
  end
end

