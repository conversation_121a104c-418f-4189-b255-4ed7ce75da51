# frozen_string_literal: true

require 'json'

require_relative '../../../../../lib/device_tasks/apple_tasks/tvos_tasks/delete_user_apps'

describe DeviceTasks::TvosTaskRunner do
  let(:udid) { 'dev123' }
  let(:device_config) { {} }
  let(:device_state_double) { double('DeviceState') }

  subject { DeviceTasks::TvosTaskRunner.new(udid: udid) }

  before(:each) do
    allow(DeviceState).to receive(:new).with(udid).and_return(device_state_double)
  end

  context '#delete_user_apps' do
    it 'should return if no genre passed delete apps' do
      expect(device_state_double).to receive(:read_session_file).and_return("{}")
      expect(IdeviceUtils).to_not receive(:uninstall_app)
      subject.delete_user_apps
    end

    it 'should not uninstall if installed apps are only known apps' do
      session_file_contents = {
        genre: "app_automate",
        app_testing_bundle_id: "com.apple.AppStore"
      }.to_json

      expect(device_state_double).to receive(:read_session_file).and_return(session_file_contents)
      expect(IdeviceUtils).to_not receive(:uninstall_app)
      expect(IdeviceUtils).to receive(:list_apps)
      expect(IdeviceUtils).to receive(:list_user_installed_apps).and_return([])
      expect(subject).to receive(:send_user_apps_to_cls)
      subject.delete_user_apps
    end

    it 'should uninstall user installed apps' do
      session_file_contents = {
        genre: "app_automate",
        app_testing_bundle_id: "com.domain.package"
      }.to_json

      expect(device_state_double).to receive(:read_session_file).and_return(session_file_contents)
      expect(IdeviceUtils).to receive(:uninstall_app).with("dev123", "com.domain.package")
      expect(IdeviceUtils).to receive(:list_apps)
      expect(IdeviceUtils).to receive(:list_user_installed_apps).and_return([])
      expect(subject).to receive(:send_user_apps_to_cls)
      subject.delete_user_apps
    end
  end
end

