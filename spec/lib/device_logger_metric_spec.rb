require_relative '../../lib/device_logger_metric'
require_relative '../../config/constants'

describe DeviceLoggerMetric do
  let(:device_id) { 'device_id' }
  let(:session_id) { 'session_id' }

  before do
    allow(subject).to receive(:log)
    Dir.mkdir(SESSION_START_DIR) unless Dir.exist?(SESSION_START_DIR)
  end

  before(:each) do
    FileUtils.touch("#{SESSION_START_DIR}/#{device_id}")  # Session start
  end

  after(:each) do
    FileUtils.rm("#{SESSION_START_DIR}/#{device_id}") if File.exist?("#{SESSION_START_DIR}/#{device_id}")
  end

  after do
    FileUtils.rm_rf(SESSION_START_DIR)
  end

  subject { DeviceLoggerMetric.new(device_id, session_id) }

  describe "#current_session_count" do
    it "should return number of concurrent sessions running" do
      FileUtils.touch("#{SESSION_START_DIR}/temp_device")
      expect(subject.send(:current_session_count)).to equal(2)
      FileUtils.rm("#{SESSION_START_DIR}/temp_device")
    end

    it "should return zero for none sessions running" do
      FileUtils.rm("#{SESSION_START_DIR}/#{device_id}")
      expect(subject.send(:current_session_count)).to equal(0)
    end
  end

  describe "#dl_macro_stats" do
    let(:exitstatus) { double('exitstatus') }

    it "should get current resource usage stats for dl running" do
      allow(exitstatus).to receive(:pid).and_return(11111)
      allow(exitstatus).to receive(:exitstatus).and_return(0)
      expect(Open3).to receive(:capture2e).with(
        "ps -ef | grep [d]evice-logger | awk '{print $2}' | xargs ps -o %cpu,%mem -p $1 | tail -n +2"
      ).and_return(
        [" 0.1  0.2\n 1.8  0.8\n", exitstatus]
      )
      current_stats, status = subject.send(:dl_macro_stats)
      expect(current_stats).to eq({ "cpu" => 2, "ram" => 1, "no_of_session" => 1,  "total_dl_process" => 2 })
    end

    it "should get current resource usage stats for dl running with zero sessions" do
      allow(exitstatus).to receive(:pid).and_return(11111)
      allow(exitstatus).to receive(:exitstatus).and_return(0)
      expect(Open3).to receive(:capture2e).with(
        "ps -ef | grep [d]evice-logger | awk '{print $2}' | xargs ps -o %cpu,%mem -p $1 | tail -n +2"
      ).and_return(
        [" 0.1  0.2\n 1.8  0.8\n", exitstatus]
      )
      FileUtils.rm("#{SESSION_START_DIR}/#{device_id}")
      current_stats, status = subject.send(:dl_macro_stats)
      expect(current_stats).to eq({ "cpu" => 2, "ram" => 1, "no_of_session" => 0, "total_dl_process" => 2 })
    end

    it "should get current resource usage stats for dl not running" do
      allow(exitstatus).to receive(:pid).and_return(11111)
      allow(exitstatus).to receive(:exitstatus).and_return(0)
      expect(Open3).to receive(:capture2e).with(
        "ps -ef | grep [d]evice-logger | awk '{print $2}' | xargs ps -o %cpu,%mem -p $1 | tail -n +2"
      ).and_return(
        ["", exitstatus]
      )
      current_stats, status = subject.send(:dl_macro_stats)
      expect(current_stats).to eq({ "cpu" => 0, "ram" => 0, "no_of_session" => 1, "total_dl_process" => 0 })
    end

    it "should get default resource usage template for status error" do
      allow(exitstatus).to receive(:pid).and_return(11111)
      allow(exitstatus).to receive(:exitstatus).and_return(1)
      expect(Open3).to receive(:capture2e).with(
        "ps -ef | grep [d]evice-logger | awk '{print $2}' | xargs ps -o %cpu,%mem -p $1 | tail -n +2"
      ).and_return(
        ["", exitstatus]
      )
      current_stats, status = subject.send(:dl_macro_stats)
      expect(current_stats).to eq({ "cpu" => 0, "ram" => 0, "no_of_session" => 0, "total_dl_process" => 0 })
    end
  end

  describe "#should_stop_monitor?" do
    it "should return true when monitoring is not running" do
      expect(subject.send(:should_stop_monitor?)).to equal(true)
    end

    it "should return false when monitor state file is present" do
      FileUtils.touch("#{STATE_FILES_DIR}/device_logger_metric_#{device_id}")
      expect(subject.send(:should_stop_monitor?)).to equal(false)
      FileUtils.rm("#{STATE_FILES_DIR}/device_logger_metric_#{device_id}")  # restore
    end
  end

  describe "#start_monitoring" do
    it "should not start if monitoring state file is already present" do
      FileUtils.touch("#{STATE_FILES_DIR}/device_logger_metric_#{device_id}")
      expect(subject).not_to receive(:dl_macro_stats)
      subject.send(:start_monitoring)
      FileUtils.rm("#{STATE_FILES_DIR}/device_logger_metric_#{device_id}") # restore
    end
  end
end
