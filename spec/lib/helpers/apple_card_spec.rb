require_relative '../../../lib/helpers/apple_card'
require_relative '../../../lib/configuration'
require_relative '../../../lib/utils/http_utils'

describe Secure::AppleCard do
  let(:device_id) { '123123' }
  let(:provider) { 'mastercard' }
  let(:number) { '0000000000000000' }
  let(:date) { '11 - November' }
  let(:year) { '2024' }
  let(:cvv) { '111' }

  before do
    @apple_card = Secure::AppleCard.new(provider, number, date, year, cvv)
  end

  it "should intialize correctly" do
    expect(@apple_card.provider).to eq(provider)
    expect(@apple_card.number).to eq(number)
    expect(@apple_card.date).to eq(date)
    expect(@apple_card.year).to eq(year)
    expect(@apple_card.cvv).to eq(cvv)
    expect(@apple_card.stability_is_reported).to eq(false)
  end

  it "should mark success" do
    @apple_card.mark_success

    expect(@apple_card.status).to eq(true)
  end

  it "should mark failure" do
    @apple_card.mark_fail

    expect(@apple_card.status).to eq(false)
  end
end
