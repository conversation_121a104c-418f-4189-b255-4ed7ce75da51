require 'timecop'
require_relative '../../spec_helper'
require_relative '../../../lib/helpers/device_sim_helper'
require_relative '../../../lib/utils/idevice_utils'
require_relative '../../../config/constants'

describe DeviceSIMHelper do
  let(:device) { '11111111-1111111111111111' }
  let(:phone_number) { '9876543210' }
  let(:cf_number) { '9999999999' }
  let(:full_device_config) do
    {
      "sim_details" => [
        {
          "carrier" => "CCCCCCCCCCCCCCCCCCCC",
          "iccid" => "iccid-000000000",
          "imsi" => "imsi-00000000",
          "sim_type" => "physical",
          "sim_slot" => 1,
          "imei" => "imei-0000000000000",
          "phone_number" => phone_number.to_s
        }
      ],
      "ip" => "**************"
    }
  end
  let(:empty_device_config) do
    {
      "sim_details": []
    }
  end
  let(:full_sim_config_json) { "{\"#{device}\": { \"phone_number\": \"#{phone_number}\" }}" }
  let(:empty_sim_config_json) { "{}" }
  let(:success_data) { { 'result' => "success", 'time_taken' => 2 } }
  let(:failure_data) { { 'result' => "failed", 'time_taken' => 2, "error" => "some_xcui_reason", "error_message" => "some xcui message" } }
  let(:wda_success_data) { { "result" => "success", "time_taken" => 0, "error" => "no error" } }
  let(:wda_failure_data) { { "result" => "failed", "time_taken" => 2, "error" => { "reason" => "some reason" } } }
  let(:mdm_success_data) { { "result" => "success", "time_taken" => 0 } }
  let(:mdm_failure_data) { { "result" => "failed", "time_taken" => 0, "error" => "some_mdm_error" } }

  let(:instance) { described_class.new(device) }
  let(:configuration) { instance_double(Configuration) }
  let(:sms_helper) { instance_double(SMSHelper) }
  let(:device_state) { double(DeviceState) }
  let(:data_report_helper) { instance_double(DataReportHelper) }
  let(:wda_client) { instance_double(WdaClient) }

  before do
    # Stub constructor methods
    allow(WdaClient).to receive(:new).and_return(wda_client)
    allow(Configuration).to receive(:new).and_return(configuration)
    allow(configuration).to receive(:all).and_return({})
    allow(SMSHelper).to receive(:new).and_return(sms_helper)
    allow(DataReportHelper).to receive(:new).and_return(data_report_helper)
    allow(DeviceState).to receive(:new).and_return(device_state)
    allow(device_state).to receive(:sim_info_file_present?).and_return(true)
    allow(device_state).to receive(:dedicated_device_file_present?).and_return(true)
    allow(device_state).to receive(:physical_sim_file_present?).and_return(true)

    allow(File).to receive(:read).with(DeviceSIMHelper::PUBLIC_SIM_DEVICES_FILE).and_return(empty_sim_config_json)
    allow(File).to receive(:read).with(DeviceSIMHelper::PRIVATE_SIM_DEVICES_FILE).and_return(full_sim_config_json)
    allow(described_class).to receive(:device_config).with(device).and_return(full_device_config)
    allow(described_class).to receive(:sim_config).with(device).and_return({ "phone_number" => phone_number })
    allow(instance).to receive(:log)
    allow(data_report_helper).to receive(:report)
  end

  describe '.sim_info' do
    before do
      allow(described_class).to receive(:refresh_state_files).with(device)
    end

    context 'when sim_config is not present' do
      before do
        allow(File).to receive(:read).with(DeviceSIMHelper::PUBLIC_SIM_DEVICES_FILE).and_return(empty_sim_config_json)
        allow(described_class).to receive(:sim_config_present?).with(device).and_return(false)
        allow(device_state).to receive(:physical_sim_file_present?).and_return(false)
      end

      it 'refreshes state files and returns an empty array' do
        puts "full_device_config: #{full_device_config}"
        expect(described_class).to receive(:refresh_state_files).with(device)
        expect(described_class.sim_info(device)).to eq([])
      end
    end

    context 'when sim_config is present and force_check is false' do
      it 'returns the sim_details from device_config' do
        allow(device_state).to receive(:sim_validation_check_file_present?).and_return(true)
        allow(device_state).to receive(:sim_config_file_present?).and_return(true)
        allow(device_state).to receive(:read_sim_config_file).and_return(true)
        allow(device_state).to receive(:sim_config_present?).and_return(true)
        allow(described_class).to receive(:sim?).and_return(true)

        described_class.sim_info(device)
      end
    end

    context 'when force_check is true and check_force_read_config_required? returns false' do
      before do
        allow(described_class).to receive(:check_force_read_config_required?).and_return(false)
        allow(described_class).to receive(:sim?).with(device).and_return(true)
      end

      it 'returns the sim_details from device_config' do
        expect(described_class.sim_info(device, force_check: true)).to eq(full_device_config["sim_details"])
      end
    end

    context 'when force_check is true and check_force_read_config_required? returns true' do
      before do
        allow(described_class).to receive(:check_force_read_config_required?).and_return(true)
        allow(described_class).to receive(:retrieve_sim_details_from_device).with(device, phone_number).and_return(full_device_config["sim_details"])
      end

      it 'refreshes state files and retrieves sim details from the device' do
        expect(described_class).to receive(:refresh_state_files).with(device)
        expect(described_class).to receive(:retrieve_sim_details_from_device).with(device, phone_number)
        expect(described_class.sim_info(device, force_check: true)).to eq(full_device_config["sim_details"])
      end
    end

    context 'when an error occurs' do
      before do
        allow(described_class).to receive(:device_config).with(device).and_raise(StandardError.new('Test error'))
        allow(BrowserStack.logger).to receive(:error)
      end

      it 'logs the error and retrieves sim details with an empty phone number' do
        expect(BrowserStack.logger).to receive(:error).with(/sim_info has ran into error/)
        expect(described_class).to receive(:retrieve_sim_details_from_device).with(device, "").and_return(full_device_config["sim_details"])
        allow(described_class).to receive(:sim?).and_return(true)
        expect(described_class.sim_info(device, force_check: true)).to eq(full_device_config["sim_details"])
      end

      it 'returns an empty array if retrieving sim details raises an error' do
        allow(described_class).to receive(:sim?).and_return(true)
        allow(described_class).to receive(:retrieve_sim_details_from_device).with(device, "").and_raise(StandardError)
        allow(device_state).to receive(:sim_config_file_present?).and_return(true)
        allow(device_state).to receive(:read_sim_config_file).and_return([])
        expect(described_class.sim_info(device)).to eq([])
      end
    end
  end

  describe '.check_force_read_config_required?' do
    let(:sim_details) { DeviceSIMHelper.device_config(device)["sim_details"] }
    let(:sim_config) { DeviceSIMHelper.sim_config(device) }
    keys_to_check = %w[carrier phone_number sim_slot sim_type imsi iccid imei]

    context 'when sim_details_json is nil' do
      it 'returns true' do
        expect(described_class.check_force_read_config_required?(nil, sim_config)).to be true
      end
    end

    context 'when sim_details_json is empty' do
      it 'returns true' do
        expect(described_class.check_force_read_config_required?([], sim_config)).to be true
      end
    end

    context 'when sim_detail_json with sim_slot 1 is not found' do
      let(:sim_details_json) { [{ "sim_slot" => 2, "phone_number" => "0987654321" }] }

      it 'returns true' do
        expect(described_class.check_force_read_config_required?(sim_details_json, sim_config)).to be true
      end
    end

    context 'when any key in keys_to_check is missing or empty in sim_detail_json' do
      keys_to_check.each do |key|
        context "when #{key} is missing in sim_detail_json" do
          let(:sim_details_json) { [{ "sim_slot" => 1, key => nil }] }

          it "returns true if #{key} is missing" do
            expect(described_class.check_force_read_config_required?(sim_details_json, sim_config)).to be true
          end
        end

        context "when #{key} is empty in sim_detail_json" do
          let(:sim_details_json) { [{ "sim_slot" => 1, key => "" }] }

          it "returns true if #{key} is empty" do
            expect(described_class.check_force_read_config_required?(sim_details_json, sim_config)).to be true
          end
        end
      end
    end

    context 'when phone_number in sim_detail_json does not match device_sim_config phone_number' do
      let(:sim_details_json) { [{ "sim_slot" => 1, "phone_number" => "different num" }] }

      it 'returns true' do
        expect(described_class.check_force_read_config_required?(sim_details_json, sim_config)).to be true
      end
    end

    context 'when all checks pass' do
      it 'returns false' do
        expect(described_class.check_force_read_config_required?(sim_details, sim_config)).to be false
      end
    end
  end

  describe '#cleanup' do
    before do
      allow(instance).to receive(:esim?).and_return(false)
      allow(instance).to receive(:enable_call_forwarding).and_return(true)
      allow(instance).to receive(:change_sim_state).and_return(true)
      allow(sms_helper).to receive(:sms_cleanup).and_return(true)
      allow(instance).to receive(:clear_sms_notifications).and_return(true)
      allow(instance).to receive(:send_sim_stats)
      allow(device_state).to receive(:ios_sms_app_opened_file_present?).and_return(false)
      allow(device_state).to receive(:remove_ios_sms_app_opened_file).and_return(true)
    end

    context 'when sim is enabled' do
      before do
        allow(described_class).to receive(:sim_enabled?).with(device).and_return(true)
      end

      it 'returns true when cleanup is successful' do
        expect(instance.cleanup).to be true
      end

      it 'returns false when cleanup fails' do
        allow(instance).to receive(:clear_sms_notifications).and_return(false)

        expect(instance.cleanup).to be false
      end

      it 'sends sim stats on success' do
        instance.cleanup
        expect(instance).to have_received(:send_sim_stats).with('cleanup', true, anything, true, false)
      end

      it 'sends sim stats on failure' do
        allow(instance).to receive(:clear_sms_notifications).and_return(false) # Cause failure

        expect(instance).to receive(:send_sim_stats).with('cleanup', false, anything, true, false)
        expect(instance.cleanup).to be false
      end

      it 'log sim sms' do
        allow(device_state).to receive(:ios_sms_app_opened_file_present?).and_return(true)
        allow(File).to receive(:exists?).and_return(false)
        allow(FileUtils).to receive(:mkdir_p).and_return(true)
        allow(BrowserStack::OSUtils).to receive(:execute).and_return(true)
        allow(IosBackupSqliteHelper).to receive(:new).and_return(true)
        allow_any_instance_of(IosBackupSqliteHelper).to receive(:retrieve_messages).and_return({})
        allow(BrowserStack::Zombie).to receive(:push_logs).and_return(true)
        allow(instance).to receive(:clear_sms_notifications).and_return(false)
        expect(instance.cleanup).to be false
      end
    end

    context 'when sim is not enabled' do
      before do
        allow(described_class).to receive(:sim_enabled?).with(device).and_return(false)
      end

      it 'returns true without attempting cleanup' do
        expect(instance.cleanup).to be true
        expect(instance).not_to have_received(:enable_call_forwarding)
        expect(instance).not_to have_received(:change_sim_state)
      end

      it 'sends sim stats with sim session as false' do
        instance.cleanup
        expect(instance).to have_received(:send_sim_stats).with('cleanup', true, anything, false, false)
      end
    end

    context 'when an exception is raised' do
      before do
        allow(described_class).to receive(:sim_enabled?).with(device).and_return(true)
        allow(instance).to receive(:clear_sms_notifications).and_raise(StandardError.new("Something went wrong"))
      end

      it 'logs the error and returns false' do
        expect(instance.cleanup).to be false
        expect(instance).to have_received(:log).with(/Failed in SIM cleanup flow, error: Something went wrong/)
      end

      it 'sends sim stats on failure' do
        instance.cleanup
        expect(instance).to have_received(:send_sim_stats).with('cleanup', false, anything, true, false)
      end
    end
  end

  describe '.refresh_state_files' do
    before do
      allow(described_class).to receive(:sim_config_present?).with(device).and_return(false)
      allow(device_state).to receive(:touch_physical_sim_file)
      allow(device_state).to receive(:touch_esim_file)
      allow(device_state).to receive(:remove_physical_sim_file)
      allow(device_state).to receive(:remove_esim_file)
      allow(device_state).to receive(:physical_sim_file_present?).and_return(false)
      allow(device_state).to receive(:esim_file_present?).and_return(false)
      allow(device_state).to receive(:remove_sim_enabled_file)
    end

    shared_examples 'no action' do
      it 'should not touch/remove any file' do
        expect(device_state).not_to receive(:remove_physical_sim_file)
        expect(device_state).not_to receive(:remove_esim_file)
        expect(device_state).not_to receive(:remove_sim_enabled_file)
        expect(device_state).not_to receive(:remove_sim_config_file)
        expect(device_state).not_to receive(:touch_physical_sim_file)
        expect(device_state).not_to receive(:touch_esim_file)
        described_class.refresh_state_files(device)
      end
    end

    context 'when sim config is not present and state files are present' do
      before do
        allow(device_state).to receive(:physical_sim_file_present?).and_return(true)
      end

      it 'removes physical SIM, eSIM, and sim enabled state files' do
        expect(device_state).to receive(:remove_physical_sim_file)
        expect(device_state).to receive(:remove_esim_file)
        expect(device_state).to receive(:remove_sim_enabled_file)
        expect(device_state).to receive(:remove_sim_validation_check_file)
        expect(device_state).to receive(:remove_sim_config_file)
        expect(device_state).not_to receive(:touch_physical_sim_file)
        expect(device_state).not_to receive(:touch_esim_file)
        described_class.refresh_state_files(device)
      end
    end

    context 'when no state files are present and no sim config is present' do
      it 'should not touch/remove any file' do
        allow(device_state).to receive(:remove_sim_config_file).and_return(true)
        expect(device_state).to receive(:remove_sim_validation_check_file)
        expect(device_state).not_to receive(:touch_physical_sim_file)
        expect(device_state).not_to receive(:touch_esim_file)
        described_class.refresh_state_files(device)
      end
    end

    context 'when sim config is present and physical SIM or eSIM is already identified' do
      before do
        allow(described_class).to receive(:sim_config_present?).with(device).and_return(true)
        allow(device_state).to receive(:esim_file_present?).and_return(true)
      end

      it_should_behave_like 'no action'
    end

    context 'when no sim is inserted' do
      before do
        allow(described_class).to receive(:sim_config_present?).with(device).and_return(true)
        allow(described_class).to receive(:sim_inserted?).with(device).and_return(false)
      end

      it_should_behave_like 'no action'
    end

    context 'when esim is enabled' do
      before do
        allow(described_class).to receive(:sim_config_present?).with(device).and_return(true)
        allow(described_class).to receive(:sim_inserted?).with(device).and_return(true)
        allow(described_class).to receive(:esim_enabled?).with(device).and_return(true)
      end

      it 'touches the eSIM file' do
        expect(device_state).not_to receive(:remove_physical_sim_file)
        expect(device_state).not_to receive(:remove_esim_file)
        expect(device_state).not_to receive(:remove_sim_enabled_file)
        expect(device_state).not_to receive(:touch_physical_sim_file)
        expect(device_state).to receive(:touch_esim_file)
        described_class.refresh_state_files(device)
      end
    end

    context 'when esim is not enabled and physical SIM is present' do
      before do
        allow(described_class).to receive(:sim_config_present?).with(device).and_return(true)
        allow(described_class).to receive(:sim_inserted?).with(device).and_return(true)
        allow(described_class).to receive(:esim_enabled?).with(device).and_return(false)
      end

      it 'touches the physical SIM file' do
        expect(device_state).not_to receive(:remove_physical_sim_file)
        expect(device_state).not_to receive(:remove_esim_file)
        expect(device_state).not_to receive(:remove_sim_enabled_file)
        expect(device_state).to receive(:touch_physical_sim_file)
        expect(device_state).not_to receive(:touch_esim_file)
        described_class.refresh_state_files(device)
      end
    end
  end
  describe "#enable_call_forwarding" do
    before do
      allow(device_state).to receive(:call_forwarding_file_present?).and_return(true)
    end

    it "should return true" do
      expect(instance).to receive(:run_test).with(:enable_call_forwarding, env_params: { 'call_forwarding_number': instance.call_forwarding_number }).and_return(success_data)
      expect(data_report_helper).to receive(:report).with(success_data)
      expect(device_state).to receive(:remove_call_forwarding_file)
      expect(instance.enable_call_forwarding).to eq(true)
    end

    it "should return false" do
      expect(instance).to receive(:run_test).with(:enable_call_forwarding, env_params: { 'call_forwarding_number': instance.call_forwarding_number }).and_return(failure_data)
      expect(data_report_helper).to receive(:report).with(failure_data)
      expect(device_state).not_to receive(:remove_call_forwarding_file)
      expect(instance.enable_call_forwarding).to eq(false)
    end
  end

  describe '#session setup' do
    context "when device state file doesn't exist" do
      before do
        expect(device_state).to receive(:sim_enabled_file_present?).and_return(false)
      end

      it 'should enable SIM, call sms cleaner and return true' do
        expect(instance).to receive(:enable_sim).and_return(true)
        expect(instance.session_setup).to eq(true)
      end

      it 'should return false when session setup fails' do
        expect(instance).to receive(:enable_sim).and_return(false)
        expect(instance.session_setup).to eq(false)
      end
    end

    it "should return true when device state exists" do
      expect(device_state).to receive(:sim_enabled_file_present?).and_return(true)
      expect(instance.session_setup).to eq(true)
    end
  end

  describe "#change_sim_state_through_wda" do
    let(:state) { "state" }
    let(:prefs_output) { { some_key: "some_value" } }

    before do
      allow(device_state).to receive(:esim_file_present?).and_return(false)
      allow(CleanupIphone).to receive(:get_prefs_for_version).and_return(prefs_output)
      allow(DeviceCtl::Device).to receive(:launch_app_with_prefs).and_return("Launched application")
    end

    it "should call wda client and parse success response" do
      wda_response = {
        "value" => {
          "error" => "no error"
        }
      }
      expect(DeviceCtl::Device).to receive(:launch_app_with_prefs).with(anything, "com.apple.Preferences", prefs_output).and_return("Launched application")
      expect(wda_client).to receive(:change_sim_state).with(state, "optimised_flow", "").and_return(wda_response)
      expect { instance.change_sim_state_through_wda(state, false) }.not_to raise_error
    end

    it "should return error reason as WDA Stuck when when no response from WDA" do
      wda_response = {}
      expect(wda_client).to receive(:change_sim_state).with(state, "optimised_flow", "").and_return(wda_response)
      expect { instance.change_sim_state_through_wda(state, false) }.to raise_error(WdaAutomationError, "No payload in response")
    end

    it "should call wda client with esim method for esim" do
      wda_response = {
        "value" => {
          "result" => "success"
        }
      }
      expect(wda_client).to receive(:change_esim_state).with(state, "optimised_flow").and_return(wda_response)
      expect { instance.change_sim_state_through_wda(state, true) }.not_to raise_error
    end
  end

  describe "#change_sim_state_through_xcui" do
    before do
      expect(BrowserStackAppHelper).to receive(:check_and_install_browserstack_test_suite)
      allow(instance).to receive(:esim?).and_return(false)
    end

    it "should call enable_sim test for enable state" do
      expect(instance).to receive(:run_test).with(:enable_sim, instance_of(Hash)).and_return({ "result" => "success" })
      instance.change_sim_state_through_xcui("enable", false)
    end

    it "should call disable_sim test for disable state" do
      expect(instance).to receive(:run_test).with(:disable_sim, instance_of(Hash)).and_return({ "result" => "success" })
      instance.change_sim_state_through_xcui("disable", false)
    end

    it "should call disable_esim for esim" do
      expect(instance).to receive(:run_test).with(:disable_esim, instance_of(Hash)).and_return({ "result" => "success" })
      instance.change_sim_state_through_xcui("disable", true)
    end

    it "should raise exception in case of failure" do
      expect(instance).to receive(:run_test).with(:disable_esim, instance_of(Hash)).and_return({ "result" => "failed" })
      expect { instance.change_sim_state_through_xcui("disable", true) }.to raise_error(GenericException)
    end
  end

  context "#change_sim_state" do
    let(:start_time) { 100 }

    before do
      expect(instance).to receive(:carrier)
      Timecop.freeze(Time.at(start_time))
    end

    after do
      Timecop.return
    end

    it "should report and return true when wda passed" do
      expect(instance).to receive(:change_sim_state_through_wda).and_return(wda_success_data)
      expect(device_state).to receive(:touch_sim_enabled_file)
      expect(data_report_helper).to receive(:report).with({
        "automation_via" => "wda",
        "error" => {},
        "result" => "success",
        "state" => "enable",
        "time_taken" => 0,
        "wda_actual_flow" => nil,
        "wda_expected_flow" => nil
      })
      result = instance.change_sim_state("enable", false)
      expect(result).to eq([true, ""])
    end

    it "should report and return false when wda failed with singal issue" do
      expect(instance).to receive(:change_sim_state_through_wda).and_raise(WdaAutomationError.new("automation error"), "Didn't get expected signal check")
      result = instance.change_sim_state("enable", false)
      expect(result).to eq([false, "Didn't get expected signal check"])
    end

    it "should report and return true when wda failed but inb passed" do
      expect(instance).to receive(:change_sim_state_through_wda).and_raise(WdaAutomationError.new("unknown error"), "No payload in response")
      expect(instance).to receive(:change_sim_state_through_xcui).and_return(success_data)
      expect(device_state).to receive(:touch_sim_enabled_file)
      expect(data_report_helper).to receive(:report).with({
        "automation_via" => "xcui",
        "error" => { "wda" => { "message" => "No payload in response", "reason" => "unknown error" } },
        "result" => "success",
        "state" => "enable",
        "time_taken" => 0,
        "wda_actual_flow" => "",
        "wda_expected_flow" => ""
      })
      result = instance.change_sim_state("enable", false)
      expect(result).to eq([true, ""])
    end

    it "should report and return false when wda and inb failed" do
      expect(instance).to receive(:change_sim_state_through_wda).and_raise(WdaAutomationError.new("unknown error"), "No payload in response")
      expect(instance).to receive(:change_sim_state_through_xcui).and_raise(GenericException.new("Failed to change SIM state through XCUI", {
        "response" => failure_data
      }))
      expect(data_report_helper).to receive(:report).with({
        "automation_via" => "",
        "error" => { "inb" => { "message" => "some xcui message", "reason" => "some_xcui_reason" }, "wda" => { "message" => "No payload in response", "reason" => "unknown error" } },
        "result" => "failed",
        "state" => "enable",
        "time_taken" => 0,
        "wda_actual_flow" => "",
        "wda_expected_flow" => ""
      })
      result = instance.change_sim_state("enable", false)
      expect(result).to eq([false, "some xcui message"])
    end
  end

  describe "#enable_sim" do
    it "should touch relevant files and return true if sim is enabled successfully" do
      expect(instance).to receive(:change_sim_state).with("enable", false).and_return([true, ""])
      expect(instance).to receive(:enable_sim_modification).twice.and_return(true)
      expect(instance).to receive(:needs_call_forwarding?).and_return(true)
      expect(device_state).to receive(:touch_call_forwarding_file)
      expect(device_state).to receive(:esim_file_present?).and_return(false)
      expect(instance.enable_sim).to eq(true)
    end

    it "should not touch call forwarding file if not needed" do
      expect(instance).to receive(:change_sim_state).with("enable", false).and_return([true, ""])
      expect(instance).to receive(:enable_sim_modification).twice.and_return(true)
      expect(instance).to receive(:needs_call_forwarding?).and_return(false)
      expect(device_state).not_to receive(:touch_call_forwarding_file)
      expect(device_state).to receive(:esim_file_present?).and_return(false)
      expect(instance.enable_sim).to eq(true)
    end

    it "should return false, if enabling sim modification fails" do
      expect(instance).to receive(:enable_sim_modification).and_return(false)
      expect(instance.enable_sim).to eq(false)
    end

    it "should return false, if change sim state fails" do
      expect(instance).to receive(:change_sim_state).with("enable", false).and_return([false, "some error message"])
      expect(device_state).to receive(:esim_file_present?).twice.and_return(false)
      expect(instance.enable_sim).to eq(false)
    end
  end

  describe "#clear_sms_notifications" do
    it "should report and return true when mdm passed" do
      expect(Utils).to receive(:toggle_notifications_mdm).and_return(mdm_success_data)
      expect(data_report_helper).to receive(:report).with({
        **mdm_success_data,
        "result_code" => 0,
        "error" => {}
      }, hash_including(force_zombie: false))
      result = instance.clear_sms_notifications
      expect(result).to eq(true)
    end

    it "should report and return true when mdm failed but xcui passed" do
      expect(Utils).to receive(:toggle_notifications_mdm).and_return(mdm_failure_data)
      expect(instance).to receive(:toggle_notifications_xcui).and_return(success_data)
      expect(data_report_helper).to receive(:report).with({
        "result" => "success",
        "time_taken" => 2,
        "result_code" => 1,
        "error" => {
          "mdm" => mdm_failure_data["error"]
        }
      }, hash_including(force_zombie: true))
      result = instance.clear_sms_notifications
      expect(result).to eq(true)
    end

    it "should report and return false when mdm and xcui failed" do
      expect(Utils).to receive(:toggle_notifications_mdm).and_return(mdm_failure_data)
      expect(instance).to receive(:toggle_notifications_xcui).and_return(failure_data)
      expect(data_report_helper).to receive(:report).with({
        "result" => "failed",
        "time_taken" => 2,
        "result_code" => 2,
        "error" => {
          "mdm" => mdm_failure_data["error"],
          "xcui" => { 'reason' => failure_data["error"], 'message' => failure_data["error_message"] }
        }
      }, hash_including(force_zombie: true))
      result = instance.clear_sms_notifications
      expect(result).to eq(false)
    end
  end
end
