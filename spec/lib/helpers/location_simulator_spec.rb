require_relative '../../spec_helper'
require_relative '../../../lib/helpers/location_simulator'

describe LocationSimulator do
  let(:device) { "some_device" }
  let(:latitude) { 2.5 }
  let(:longitude) { 2.5 }
  let(:mock_config) { double(BrowserStack::Configuration).as_null_object }
  let(:mock_device_config) { { "device_version" => "16.0" } }
  let(:mock_device_config_ios17) { { "device_version" => "17.1" } }

  before(:each) do
    allow(BrowserStack::Configuration).to receive(:new).and_return(mock_config)
    allow(File).to receive(:write)
    allow(File).to receive(:delete)

    @location_simulator = LocationSimulator.new(device)
  end

  describe '#simulate' do
    context 'ios version >= 17' do
      before(:each) do
        allow(BrowserStack::DeviceConf).to receive(:[]).with(device).and_return(mock_device_config_ios17)
      end

      context 'when simulation succeeds' do
        before(:each) do
          allow(@location_simulator).to receive(:simulate_greater_than_equal_to17)
        end

        it 'should not raise error' do
          @location_simulator.simulate(latitude, longitude)
        end
      end

      context 'when simulation fails' do
        before(:each) do
          allow(@location_simulator).to receive(:simulate_greater_than_equal_to17).and_raise("Something went wrong")
        end

        it 'should raise error' do
          expect { @location_simulator.simulate(latitude, longitude) }.to raise_error("Something went wrong")
        end
      end
    end

    context 'ios version < 17' do
      before(:each) do
        allow(BrowserStack::DeviceConf).to receive(:[]).and_return(mock_device_config)
      end

      context 'when simulation succeeds' do
        before(:each) do
          allow(@location_simulator).to receive(:simulate_less_than17)
        end

        it 'should not raise error' do
          @location_simulator.simulate(latitude, longitude)
        end
      end

      context 'when simulation fails' do
        before(:each) do
          allow(@location_simulator).to receive(:simulate_less_than17).and_raise("Something went wrong")
        end

        it 'should raise error' do
          expect { @location_simulator.simulate(latitude, longitude) }.to raise_error("Something went wrong")
        end
      end
    end
  end

  describe '#reset' do
    before(:each) do
      allow(@location_simulator).to receive(:simulate)
    end

    context 'ios version >= 17' do
      before(:each) do
        allow(BrowserStack::DeviceConf).to receive(:[]).with(device).and_return(mock_device_config_ios17)
      end

      context 'when reset succeeds' do
        before(:each) do
          allow(@location_simulator).to receive(:kill_existing_location_simulation_greater_than_equal_to17)
        end

        it 'should not raise error' do
          @location_simulator.reset
        end
      end

      context 'when simulation fails' do
        before(:each) do
          allow(@location_simulator).to receive(:kill_existing_location_simulation_greater_than_equal_to17).and_raise("Something went wrong")
        end

        it 'should raise error' do
          expect { @location_simulator.reset }.to raise_error("Something went wrong")
        end
      end
    end

    context 'ios version < 17' do
      before(:each) do
        allow(BrowserStack::DeviceConf).to receive(:[]).and_return(mock_device_config)
      end

      context 'when simulation succeeds' do
        it 'should not raise error' do
          @location_simulator.reset
        end
      end

      context 'when simulation fails' do
        before(:each) do
          allow(@location_simulator).to receive(:simulate).and_raise("Something went wrong")
        end

        it 'should raise error' do
          expect { @location_simulator.reset }.to raise_error("Something went wrong")
        end
      end
    end
  end

  describe '#simulate_less_than17' do
    context 'when simulation succeeds' do
      before(:each) do
        allow(Utils).to receive(:fork_process)
      end

      it 'should not raise error' do
        @location_simulator.send(:simulate_less_than17, latitude, longitude)
      end
    end

    context 'when simulation fails' do
      before(:each) do
        allow(Utils).to receive(:fork_process).and_raise("Something went wrong")
      end

      it 'should raise error' do
        expect { @location_simulator.send(:simulate_less_than17, latitude, longitude) }.to raise_error("Something went wrong")
      end
    end
  end

  describe '#simulate_greater_than_equal_to17' do
    before(:each) do
      allow(@location_simulator).to receive(:kill_existing_location_simulation_greater_than_equal_to17)
    end

    context 'when simulation succeeds' do
      before(:each) do
        allow(PyMobileDevice::Developer).to receive(:simulate_location)
      end

      it 'should not raise error' do
        @location_simulator.send(:simulate_greater_than_equal_to17, latitude, longitude)
      end
    end

    context 'when simulation fails' do
      before(:each) do
        allow(PyMobileDevice::Developer).to receive(:simulate_location).and_raise("Something went wrong")
      end

      it 'should raise error' do
        expect { @location_simulator.send(:simulate_greater_than_equal_to17, latitude, longitude) }.to raise_error("Something went wrong")
      end
    end
  end

  describe '#kill_existing_location_simulation_greater_than_equal_to17' do
    before(:each) do
      allow(Utils).to receive(:process_running?).and_return(true)
      allow(File).to receive(:exists?).and_return(true)
      allow(File).to receive(:read).and_return({ pid: 1 }.to_json)
      allow(Process).to receive(:kill)
    end

    context 'when location simulator file does not exist' do
      before(:each) do
        allow(File).to receive(:exists?).and_return(false)
      end

      it 'should not raise error' do
        expect(Process).to_not receive(:kill)
        @location_simulator.send(:kill_existing_location_simulation_greater_than_equal_to17)
      end
    end

    context 'when location simulator pid is not running' do
      before(:each) do
        allow(Utils).to receive(:process_running?).and_return(false)
      end

      it 'should not raise error' do
        expect(Process).to_not receive(:kill)
        @location_simulator.send(:kill_existing_location_simulation_greater_than_equal_to17)
      end
    end

    context 'when killing existing location simulation succeeds' do
      it 'should not raise error' do
        expect(Process).to receive(:kill)
        @location_simulator.send(:kill_existing_location_simulation_greater_than_equal_to17)
      end
    end

    context 'when simulation fails' do
      before(:each) do
        allow(Process).to receive(:kill).and_raise("Something went wrong")
      end

      it 'should raise error' do
        expect(Process).to receive(:kill)
        expect { @location_simulator.send(:kill_existing_location_simulation_greater_than_equal_to17) }.to raise_error("Something went wrong")
      end
    end
  end
end
