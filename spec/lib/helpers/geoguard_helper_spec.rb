require_relative '../../../lib/helpers/geoguard_helper'
require 'spec_helper'

describe GeoguardHelper do
  let(:device) { 'test_device' }
  let(:session) { 'test_session' }
  let(:logger) { double('logger').as_null_object }
  let(:mock_data_reporter) { double(DataReportHelper).as_null_object }
  let(:mock_server_config) { double(BrowserStack::Configuration).as_null_object }
  let(:mock_device_state) { double(DeviceState).as_null_object }
  let(:mock_wda_client) { double(WdaClient).as_null_object }

  let(:subject) { GeoguardHelper.new(device, session, logger) }

  before(:each) do
    allow(DataReportHelper).to receive(:new).and_return(mock_data_reporter)
    allow(BrowserStack::Configuration).to receive(:new).and_return(mock_server_config)
    allow(DeviceState).to receive(:new).and_return(mock_device_state)
    allow(WdaClient).to receive(:new).and_return(mock_wda_client)
    allow(subject).to receive(:device_config).and_return({
      test_device: {
        webdriver_port: 1234
      }
    })
  end

  describe '#setup' do
    context 'when GeoGuard is not already installed' do
      before(:each) do
        allow(IdeviceUtils).to receive(:check_app_with_bundle_id_exists).and_return(false)
      end

      it 'should install geoguard app and enable location services' do
        expect(BrowserStackAppHelper).to receive(:check_and_install_browserstack_test_suite).and_return(true)
        expect(subject).to receive(:install_geoguard_app).and_return(true)
        expect(subject).to receive(:allow_geoguard_location_permissions).and_return(true)
        expect(subject).to receive(:allow_geoguard_notifications).and_return(true)
        expect(mock_wda_client).not_to receive(:launch_app_with_bundle_id)
        expect(mock_device_state).to receive(:touch_geoguard_cleanup_file).and_return(true)
        expect(mock_data_reporter).to receive(:report).with(hash_including({
          "action" => "setup",
          "result" => "success",
          "install_via" => "mdm",
          "app_already_installed" => false,
          "error" => nil
        }))

        expect(subject.setup).to eql(true)
      end

      it 'should raise error in case of any failure' do
        expect(BrowserStackAppHelper).to receive(:check_and_install_browserstack_test_suite).and_return(true)
        expect(subject).to receive(:install_geoguard_app).and_raise("Something went wrong")
        expect(subject).not_to receive(:allow_geoguard_location_permissions)
        expect(subject).to receive(:allow_geoguard_notifications).and_return(true)
        expect(mock_wda_client).not_to receive(:launch_app_with_bundle_id)
        expect(mock_device_state).not_to receive(:touch_geoguard_cleanup_file)
        expect(mock_device_state).to receive(:touch_geoguard_failed_file).and_return(true)
        expect(mock_data_reporter).to receive(:report).with(hash_including({
          "action" => "setup",
          "result" => "failed",
          "install_via" => "mdm",
          "app_already_installed" => false,
          "error" => "Something went wrong"
        }))

        expect { subject.setup }.to raise_error("Something went wrong")
      end
    end

    context 'when Geoguard is already installed' do
      before(:each) do
        allow(IdeviceUtils).to receive(:check_app_with_bundle_id_exists).and_return(true)
      end

      it 'should remove notifications profile to allow notifications' do
        expect(BrowserStackAppHelper).not_to receive(:check_and_install_browserstack_test_suite)
        expect(subject).not_to receive(:install_geoguard_app)
        expect(subject).not_to receive(:allow_geoguard_location_permissions)
        expect(subject).to receive(:allow_geoguard_notifications).and_return(true)
        expect(mock_wda_client).to receive(:launch_app_with_bundle_id).and_return(true)
        expect(mock_device_state).to receive(:touch_geoguard_cleanup_file).and_return(true)
        expect(mock_data_reporter).to receive(:report).with(hash_including({
          "action" => "setup",
          "result" => "success",
          "install_via" => "mdm",
          "app_already_installed" => true,
          "error" => nil
        }))

        expect(subject.setup).to eql(true)
      end

      it 'should raise error in case of any failure' do
        expect(BrowserStackAppHelper).not_to receive(:check_and_install_browserstack_test_suite)
        expect(subject).not_to receive(:install_geoguard_app)
        expect(subject).not_to receive(:allow_geoguard_location_permissions)
        expect(subject).to receive(:allow_geoguard_notifications).and_raise("Something went wrong")
        expect(mock_wda_client).not_to receive(:launch_app_with_bundle_id)
        expect(mock_device_state).not_to receive(:touch_geoguard_cleanup_file)
        expect(mock_device_state).to receive(:touch_geoguard_failed_file).and_return(true)
        expect(mock_data_reporter).to receive(:report).with(hash_including({
          "action" => "setup",
          "result" => "failed",
          "install_via" => "mdm",
          "app_already_installed" => true,
          "error" => "Something went wrong"
        }))

        expect { subject.setup }.to raise_error("Something went wrong")
      end
    end
  end

  describe '#cleanup' do
    context 'when Geoguard is not already installed' do
      before(:each) do
        allow(IdeviceUtils).to receive(:check_app_with_bundle_id_exists).and_return(false)
      end

      it 'should return early' do
        expect(IdeviceUtils).not_to receive(:uninstall_app)
        expect(Utils).not_to receive(:change_notifications_profile_mdm)
        expect(mock_wda_client).not_to receive(:kill_apps)
        expect(mock_data_reporter).not_to receive(:report)
        expect(subject.cleanup).to eql(nil)
        expect(mock_data_reporter).not_to receive(:report)
      end
    end

    context 'when Geoguard is already installed' do
      before(:each) do
        allow(IdeviceUtils).to receive(:check_app_with_bundle_id_exists).and_return(true)
      end

      context 'when Geoguard setup has completed' do
        before(:each) do
          allow(mock_device_state).to receive(:geoguard_failed_file_present?).and_return(false)
          allow(mock_device_state).to receive(:geoguard_cleanup_file_present?).and_return(true)
        end

        it 'should add notifications profile and kill Geoguard app' do
          expect(IdeviceUtils).not_to receive(:uninstall_app)
          expect(subject).to receive(:disallow_geoguard_notifications).and_return(true)
          expect(mock_wda_client).to receive(:kill_apps).and_return(true)
          expect(mock_data_reporter).to receive(:report).with(hash_including({
            "action" => "cleanup",
            "result" => "success",
            "error" => nil
          }))
          expect(subject.cleanup).to eql(true)
        end
      end

      context 'when Geoguard setup has failed' do
        before(:each) do
          allow(mock_device_state).to receive(:geoguard_failed_file_present?).and_return(true)
          allow(mock_device_state).to receive(:geoguard_cleanup_file_present?).and_return(false)
        end

        it 'should uninstall Geoguard app' do
          expect(IdeviceUtils).to receive(:uninstall_app).and_return(true)
          expect(subject).not_to receive(:disallow_geoguard_notifications)
          expect(mock_wda_client).not_to receive(:kill_apps)
          expect(mock_data_reporter).to receive(:report).with(hash_including({
            "action" => "cleanup",
            "result" => "success",
            "error" => nil
          }))
          expect(subject.cleanup).to eql(true)
        end
      end
    end
  end

  describe '#disallow_geoguard_notifications' do
    context 'when notifications is changed successfully' do
      before(:each) do
        allow(Utils).to receive(:change_notifications_profile_mdm).and_return({
          "result" => "success"
        })
      end

      it 'should return true' do
        expect(subject.send(:disallow_geoguard_notifications)).to eql(true)
      end
    end

    context 'when failed to change notifications' do
      before(:each) do
        allow(Utils).to receive(:change_notifications_profile_mdm).and_return({
          result: "failed"
        })
      end

      it 'should raise error' do
        expect { subject.send(:disallow_geoguard_notifications) }.to raise_error("Failed to disallow Geoguard Notification")
      end
    end
  end
end
