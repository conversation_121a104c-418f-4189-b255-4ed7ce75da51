require_relative '../../spec_helper'
require_relative '../../../lib/helpers/mobile_config_helper'

describe BrowserStack::MobileConfigHelper do
  let(:device_config) { { 'device_version' => 14, 'webdriver_port' => 8080, 'selenium_port' => 8081, 'debugger_port' => 8082, 'device_name' => 'iPhone 11', 'device_serial' => 'A3527427524' } }
  let(:uuid) { '000820-427503425' }
  let(:eds_event_type) { "web_events" }
  let(:eds_event_name_install) { "InstallMobileConfig" }
  let(:eds_event_name_remove)  { "RemoveMobileConfig" }

  subject { BrowserStack::MobileConfigHelper.new(uuid, device_config) }

  describe '#install_mobile_config_profile' do
    let(:session_id) { '1' }
    let(:product) { 'app-live' }
    let(:path_to_config_file) { '/tmp/temp.mobileconfig' }
    let(:base64_encoded) { 'L3RtcC90ZW1wLm1vYmlsZWNvbmZpZw==' }
    let(:random_hash_suffix) { 'abcd' }
    let(:payload_id_const) { 'PayloadIdentifier' }
    let(:prefix_constant) { 'com.browserstack.ssl.' }
    let(:payload_id) { 'com.browserstack.ssl.abcd' }
    let(:payload_ids) { ['com.browserstack.ssl.abcd'] }
    let(:state_file_contents) { { 'com.browserstack.ssl.abcd' => 0 } }
    let(:err_message) { 'err' }
    let(:os_version) { 14 }
    let(:start_time) { 100 }
    let(:end_time) { 100 }

    before(:each) do
      subject.class.class_variable_set(:@@config, { "mobile_config_ssl_prefix" => prefix_constant, "mobile_config_ID_name" => payload_id_const })
      Timecop.freeze(Time.at(start_time))
      expect(JSON).to receive(:parse).and_return(state_file_contents)
      expect(Digest::MD5).to receive(:hexdigest).and_return(base64_encoded)
      expect(File).to receive(:read).and_return(path_to_config_file)
      expect(PlistBuddy).to receive(:set_key_in_plist).and_return(true)
      expect(Base64).to receive(:encode64).and_return(base64_encoded)
    end

    after do
      Timecop.return
    end

    it "should raise SSLCert Error in case of any error" do
      expect_any_instance_of(DeviceState).to receive(:mobile_config_installed_file_present?).and_return(true)
      expect_any_instance_of(DeviceState).to receive(:read_mobile_config_installed_file).and_return(payload_ids)
      expect(subject).to receive(:session_id).and_return(session_id).exactly(2).times
      expect(subject).to receive(:device_version).and_return(os_version).exactly(2).times
      expect(IosMdmServiceClient).to receive(:make_request).with({ "request_type" => "InstallProfile", "udid" => uuid, "payload" => base64_encoded }, REDIS_CLIENT).and_raise(err_message)
      expect(BrowserStack::Zombie).to receive(:push_logs).with("ssl-install-failure", err_message, { "device" => uuid, "product" => product, "session_id" => session_id , "data" => { "time_taken" => end_time - start_time, "os_version" => os_version } })
      expect(Utils).to receive(:send_to_eds).with({ "event_name" => eds_event_name_install, "team" => "device_features", "product" => "device_features", "event_json" => { "device" => uuid, "session_id" => session_id, "os_version" => os_version, "time_taken" => end_time - start_time, "success" => "false", "error" => err_message } }, eds_event_type, true)
      expect { subject.install_mobile_config_profile(path_to_config_file, product) }.to raise_error(SSLCertificateNotInstalledException)
    end

    it "should touch the device state file in case it does not exist" do
      expect_any_instance_of(DeviceState).to receive(:mobile_config_installed_file_present?).and_return(false)
      expect_any_instance_of(DeviceState).to receive(:touch_mobile_config_installed_file)
      expect_any_instance_of(DeviceState).to receive(:read_mobile_config_installed_file).and_return(payload_ids)
      expect_any_instance_of(DeviceState).to receive(:write_to_mobile_config_installed_file)
      expect(subject).to receive(:session_id).and_return(session_id)
      expect(subject).to receive(:device_version).and_return(os_version)
      expect(IosMdmServiceClient).to receive(:make_request).with({ "request_type" => "InstallProfile", "udid" => uuid, "payload" => base64_encoded }, REDIS_CLIENT).and_return(true)
      expect(Utils).to receive(:send_to_eds).with({ "event_name" => eds_event_name_install, "team" => "device_features", "product" => "device_features", "event_json" => { "device" => uuid, "session_id" => session_id, "os_version" => os_version, "time_taken" => end_time - start_time, "success" => "true" } }, eds_event_type, true)
      expect { subject.install_mobile_config_profile(path_to_config_file, product) }.not_to raise_error
    end

    it "should not touch the device state file in case it does already exist" do
      expect_any_instance_of(DeviceState).to receive(:mobile_config_installed_file_present?).and_return(true)
      expect_any_instance_of(DeviceState).not_to receive(:touch_mobile_config_installed_file)
      expect_any_instance_of(DeviceState).to receive(:read_mobile_config_installed_file).and_return(payload_ids)
      expect_any_instance_of(DeviceState).to receive(:write_to_mobile_config_installed_file)
      expect(subject).to receive(:session_id).and_return(session_id)
      expect(subject).to receive(:device_version).and_return(os_version)
      expect(IosMdmServiceClient).to receive(:make_request).with({ "request_type" => "InstallProfile", "udid" => uuid, "payload" => base64_encoded }, REDIS_CLIENT).and_return(true)
      expect(Utils).to receive(:send_to_eds).with({ "event_name" => eds_event_name_install, "team" => "device_features", "product" => "device_features", "event_json" => { "device" => uuid, "session_id" => session_id, "os_version" => os_version, "time_taken" => end_time - start_time, "success" => "true" } }, eds_event_type, true )
      expect { subject.install_mobile_config_profile(path_to_config_file, product) }.not_to raise_error
    end
  end

  describe '#check_and_remove_mobile_config_payload' do
    let(:session_id) { '1' }
    let(:payload_id) { 'com.browserstack.ssl.abcd' }
    let(:state_file_contents_zero) { { 'com.browserstack.ssl.abcd' => 0 } }
    let(:state_file_contents_two) { { 'com.browserstack.ssl.abcd' => 2 } }
    let(:state_file_contents_max) { { 'com.browserstack.ssl.abcd' => 5 } }
    let(:os_version) { 14 }
    let(:start_time) { 100 }
    let(:end_time) { 100 }

    def send_remove_request_to_mdm_and_data_to_eds_pass
      expect(IosMdmServiceClient).to receive(:make_request).with( { "request_type" => "RemoveProfile", "udid" => uuid, "Identifier" => payload_id }, REDIS_CLIENT).and_return(true)
      expect(subject).to receive(:device_version).and_return(os_version)
      expect(subject).to receive(:session_id).and_return(session_id)
      expect(Utils).to receive(:send_to_eds).with({ "event_name" => eds_event_name_remove, "team" => "device_features", "product" => "device_features", "event_json" => { "device" => uuid, "session_id" => session_id, "os_version" => os_version, "time_taken" => end_time - start_time, "success" => "true", "identifier" => payload_id } }, eds_event_type, true)
    end

    def send_remove_request_to_mdm_and_data_to_eds_fail
      expect(IosMdmServiceClient).to receive(:make_request).with( { "request_type" => "RemoveProfile", "udid" => uuid, "Identifier" => payload_id }, REDIS_CLIENT).and_raise("err")
      expect(subject).to receive(:device_version).and_return(os_version).exactly(2).times
      expect(subject).to receive(:session_id).and_return(session_id).exactly(2).times
      expect(Utils).to receive(:send_to_eds).with({ "event_name" => eds_event_name_remove, "team" => "device_features", "product" => "device_features", "event_json" => { "device" => uuid, "session_id" => session_id, "os_version" => os_version, "time_taken" => end_time - start_time, "success" => "false", "identifier" => payload_id } }, eds_event_type, true)
    end

    before(:each) do
      subject.class.class_variable_set(:@@config, { "max_retries_ssl_cert" => 5 } )
      Timecop.freeze(Time.at(start_time))
    end

    after do
      Timecop.return
    end

    it "should call removal mdm command when zero attempts for removal and delete that identifier for success" do
      send_remove_request_to_mdm_and_data_to_eds_pass
      expect(subject.check_and_remove_mobile_config_payload(payload_id, state_file_contents_zero)).to be_empty
    end

    it "should call removal mdm command when two attempts for removal and delete that identifier for success" do
      send_remove_request_to_mdm_and_data_to_eds_pass
      expect(subject.check_and_remove_mobile_config_payload(payload_id, state_file_contents_two)).to be_empty
    end

    it "should call removal mdm command when five attempts and send zombie logs and remove the identifier" do
      send_remove_request_to_mdm_and_data_to_eds_fail
      expect(BrowserStack::Zombie).to receive(:push_logs).with("ssl-remove-failure", "Retries Exceeded", { "device" => uuid, "session_id" => session_id, "data" => { "certificate_id" => payload_id, "time_taken" => end_time - start_time, "os_version" => os_version } })
      expect(subject.check_and_remove_mobile_config_payload(payload_id, state_file_contents_max)).to be_empty
    end
  end

  describe "#remove_mobile_configs" do
    let(:session_id) { '1' }
    let(:payload_id_const) { 'PayloadIdentifier' }
    let(:prefix_constant) { 'com.browserstack.ssl.' }
    let(:payload_id) { 'com.browserstack.ssl.abcd' }
    let(:state_file_contents) { { 'com.browserstack.ssl.abcd' => '0' } }
    let(:state_file_contents_array) { ['com.browserstack.ssl.abcd'] }
    let(:state_file_contents_partial) { { 'com.browserstack.ssl.abcd' => '0', 'com.browserstack.ssl.abcde' => '5' } }
    let(:state_file_contents_partial_one_iteration) { { 'com.browserstack.ssl.abcd' => '1', 'com.browserstack.ssl.abcde' => '5' } }
    let(:state_file_contents_partial_final) { { 'com.browserstack.ssl.abcd' => '1' } }
    let(:state_file_contents_array_partial) { ['com.browserstack.ssl.abcd', 'com.browserstack.ssl.abcde'] }
    let(:err_message) { 'err' }

    it "should push logs to zombie in case of complete removal and deletes state file" do
      expect_any_instance_of(DeviceState).to receive(:read_mobile_config_installed_file).and_return(state_file_contents.to_json)
      expect(subject).to receive(:check_and_remove_mobile_config_payload).with(state_file_contents_array[0], state_file_contents).and_return({})
      expect_any_instance_of(DeviceState).to receive(:remove_mobile_config_installed_file)
      expect { subject.remove_mobile_configs }.not_to raise_error
    end

    it "should push logs to zombie in case of partial removal and writes back data into state file" do
      expect_any_instance_of(DeviceState).to receive(:read_mobile_config_installed_file).and_return(state_file_contents_partial.to_json)
      expect(subject).to receive(:check_and_remove_mobile_config_payload).with(state_file_contents_array_partial[0], state_file_contents_partial).and_return(state_file_contents_partial_one_iteration)
      expect(subject).to receive(:check_and_remove_mobile_config_payload).with(state_file_contents_array_partial[1], state_file_contents_partial_one_iteration).and_return(state_file_contents_partial_final)
      expect_any_instance_of(DeviceState).to receive(:write_to_mobile_config_installed_file).with(state_file_contents_partial_final.to_json)
      expect { subject.remove_mobile_configs }.not_to raise_error
    end
  end
end
