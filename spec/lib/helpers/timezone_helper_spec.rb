require_relative '../../spec_helper'
require_relative '../../../lib/helpers/timezone_helper'
require 'timecop'

describe BrowserStack::TimezoneHelper do
  let(:uuid) { '000820-427503425' }
  let(:timezone) { 'timezone' }
  let(:timezone_mdm) { 'timezone' }
  let(:session_id) { '1' }
  let(:product) { 'app-live' }
  let(:device_config) { { "device_version" => "13", 'webdriver_port' => '8400' } }
  let(:device_config_lower_os) { { "device_version" => "11", 'webdriver_port' => '8400' } }
  let(:device_config_os_mdm) { { "device_version" => "15", 'webdriver_port' => '8400' } }
  subject { BrowserStack::TimezoneHelper.new(uuid, session_id, product, device_config) }

  let(:iPhone_lower_os) { BrowserStack::TimezoneHelper.new(uuid, session_id, product, device_config_lower_os) }

  let(:iPhone_os_mdm) { BrowserStack::TimezoneHelper.new(uuid, session_id, product, device_config_os_mdm) }

  describe "#get_error_type" do
    describe "#error_type_exists" do
      let(:retry_error) { "Descendants matching type SearchField" }
      let(:bootstrap_error) { "Early unexpected exit, operation never finished bootstrapping" }

      it "should return RetryError for retry error message" do
        expect(TimezoneHelper.get_error_type(retry_error)).to eq("RetryError")
      end

      it "should return XCUITest Bootstrap error for bootstrap error message" do
        expect(TimezoneHelper.get_error_type(bootstrap_error)).to eq("XCUITestBootstrapError")
      end
    end

    describe "#error_type_does_not_exist" do
      let(:err_output) { 'abc' }

      it "should return OtherError in case the type doest not exist" do
        expect(TimezoneHelper.get_error_type(err_output)).to eq("OtherError")
      end
    end
  end

  describe "#change_time_zone" do
    def fail_wda_automation_with_message(test_output, zombie_reason, error_msg, eds_reason, mdm_response_message)
      expect_any_instance_of(WdaClient).to receive(:change_time_zone).and_return({ 'value' => { 'message' => test_output } })
      expect(BrowserStack::Zombie).to receive(:push_logs).and_return(true)

      expect(Utils).to receive(:send_to_eds).with( {
        event_name: "UpdateTimezone",
        product: product,
        os: "ios",
        team: "device_features",
        event_json: {
          session_id: "1",
          time_taken: 0,
          timezone_updated: "failed",
          "wda-response": test_output,
          "wda-total-time": 0,
          error_reason: eds_reason
        }
      }, event_name, true )
    end
    let(:event_name) { 'web_events' }
    let(:start_time) { 100 }
    let(:end_time) { 100 }

    before do
      Timecop.freeze(Time.at(start_time))
    end

    after do
      Timecop.return
    end

    it "should run MDM command and return success message" do
      expect(BrowserStack::IosMdmServiceClient).to receive(:set_timezone).and_return(true)
      expect(BrowserStack::Zombie).to receive(:push_logs).and_return(true)
      expect(Utils).to receive(:send_to_eds).with( {
        event_name: "UpdateTimezone",
        product: product,
        os: "ios",
        team: "device_features",
        event_json: {
          session_id: "1",
          timezone_updated: "success",
          time_taken: 0,
          "mdm-response": "Timezone change success.",
          "mdm-total-time": 0
        }
      }, event_name, true )

      expect { iPhone_os_mdm.change_time_zone(timezone, timezone_mdm) }.not_to raise_error
    end

    it "should run WDA and return success message when MDM command fails" do
      expect(BrowserStack::IosMdmServiceClient).to receive(:set_timezone).and_return(false)
      expect_any_instance_of(WdaClient).to receive(:change_time_zone).and_return({ 'value' => { 'message' => 'Timezone change success.' } })
      expect(BrowserStack::Zombie).to receive(:push_logs).and_return(true)
      expect(Utils).to receive(:send_to_eds).with( {
        event_name: "UpdateTimezone",
        product: product,
        os: "ios",
        team: "device_features",
        event_json: {
          session_id: "1",
          fallback: "true",
          fallback_reason: "Timezone change failed.",
          timezone_updated: "success",
          time_taken: 0,
          "mdm-response": "Timezone change failed.",
          "mdm-total-time": 0, "wda-response": "Timezone change success.",
          "wda-total-time": 0
        }
      }, event_name, true )

      expect { iPhone_os_mdm.change_time_zone(timezone, timezone_mdm) }.not_to raise_error
    end

    it "should run the wda test and return for correct input when " do
      expect(BrowserStack::IosMdmServiceClient).to receive(:set_timezone).and_return(false)
      expect_any_instance_of(WdaClient).to receive(:change_time_zone).and_return({ 'value' => { 'message' => 'Timezone change success.' } })
      expect(BrowserStack::Zombie).to receive(:push_logs).and_return(true)
      expect(Utils).to receive(:send_to_eds).with( {
        event_name: "UpdateTimezone",
        product: product,
        os: "ios",
        team: "device_features",
        event_json: {
          session_id: "1",
          fallback: "true",
          fallback_reason: "Timezone change failed.",
          timezone_updated: "success",
          time_taken: 0,
          "mdm-response": "Timezone change failed.",
          "mdm-total-time": 0,
          "wda-response": "Timezone change success.",
          "wda-total-time": 0
        }
      }, event_name, true )

      expect { iPhone_os_mdm.change_time_zone(timezone, timezone_mdm) }.not_to raise_error
    end

    it "should return error when test fails with no search results" do
      fail_wda_automation_with_message('No search results', "No Timezone", 'NoTimezonesException', "no_search_results", "mdm_response_message")
      expect { subject.change_time_zone(timezone, timezone_mdm) }.to raise_error(NoTimezonesException)
    end

    it "should return error when test fails with multiple timezones found" do
      fail_wda_automation_with_message('Multiple search results', "Multiple Timezones", 'MultipleTimezonesException', "multiple_search_results", "mdm_response_message")
      expect { subject.change_time_zone(timezone, timezone_mdm) }.to raise_error(MultipleTimezonesException)
    end

    it "should raise exception on less than iOS 12 devices" do
      expect { iPhone_lower_os.change_time_zone(timezone, timezone_mdm) }.to raise_error(TimezoneNotSupportedException)
    end
  end
end
