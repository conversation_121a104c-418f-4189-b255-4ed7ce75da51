require_relative "../../spec_helper"
require_relative "../../../lib/helpers/pfctl_helper"

describe PFCTLHelper do
  let(:device_config) { { "webdriver_port" => 8080, "device_version" => "14.0" } }
  let(:uuid) { "000820-427503425" }
  let(:product) { "app-live" }
  let(:session_id) { "1" }
  let(:ios_version) { "14.0" }
  let(:device_ip) { "127.0.0.1" }
  let(:terminal_ip) { "***********" }
  let(:file_handle) { double("file_handle") }
  let(:rules_template) do
    "\nblock return in on bridge100 proto tcp from %{device_ip} to any no state\n"\
                        "block return out on bridge100 proto tcp from any to %{device_ip} no state\n"\
                        "pass in on bridge100 from %{device_ip} to <app_live_whitelist> no state\n"\
                        "pass out on bridge100 from <app_live_whitelist> to %{device_ip} no state\n"\
                        "pass in on bridge100 proto tcp from %{device_ip} to %{terminal_ip} port 48080 <> 48179 no state\n"\
                        "pass out on bridge100 proto tcp from %{terminal_ip} port 48080 <> 48179 to %{device_ip} no state\n"
  end

  before do
    @pfctl_object = PFCTLHelper.new(terminal_ip, uuid, "1234", "app_automate")
  end

  context "#setup" do
    it "should create the folder and create device conf file if directory did not exist" do
      allow(Dir).to receive(:exists?).and_return(false)
      expect(Dir).to receive(:mkdir).with(PF_CONFIGURATION_DIR).and_return(true)
      allow(File).to receive(:exists?).and_return(false)
      expect(FileUtils).to receive(:touch).with("#{PF_CONFIGURATION_DIR}/device_conf.json").and_return(true)

      @pfctl_object.setup
    end

    it "should just create device conf file if it doesnt exist" do
      allow(Dir).to receive(:exists?).and_return(true)
      allow(File).to receive(:exists?).and_return(false)
      expect(Dir).not_to receive(:mkdir).with(PF_CONFIGURATION_DIR)
      expect(FileUtils).to receive(:touch).with("#{PF_CONFIGURATION_DIR}/device_conf.json").and_return(true)

      @pfctl_object.setup
    end

    it "should not touch device conf file if it exists" do
      allow(Dir).to receive(:exists?).and_return(true)
      allow(File).to receive(:exists?).and_return(true)
      expect(Dir).not_to receive(:mkdir).with(PF_CONFIGURATION_DIR)
      expect(FileUtils).not_to receive(:touch).with("#{PF_CONFIGURATION_DIR}/device_conf.json")

      @pfctl_object.setup
    end
  end

  context "#fetch_ip_address" do
    it "should return ip addres of device if wda client sent a correct response" do
      allow(DeviceManager).to receive(:device_configuration_check).with(uuid).and_return({ "webdriver_port" => "8080" })
      allow_any_instance_of(WdaClient).to receive(:device_ip).and_return({ "value" => device_ip })

      expect(@pfctl_object.fetch_ip_address).to eq(device_ip)
    end
  end

  context "#add_offline_mode_rules" do
    it "should append offline mode rules to devices pf conf file" do
      allow(Utils).to receive(:with_lock).with(anything) do |&block|
        allow(File).to receive(:open).and_return(file_handle)
        block.call
      end
      allow(File).to receive(:read).with("#{PF_CONFIGURATION_DIR}/device_conf.json").and_return("")
      expect(Utils).to receive(:write_to_file).with(anything, { uuid => format(rules_template, device_ip: device_ip, terminal_ip: terminal_ip) }.to_json)

      @pfctl_object.add_offline_mode_rules(device_ip)
    end

    it "should raise error if it is unable to open a file" do
      allow(Utils).to receive(:with_lock).with(anything) do |&block|
        allow(File).to receive(:open).and_return(file_handle)
        block.call
      end

      allow(File).to receive(:read).with("#{PF_CONFIGURATION_DIR}/device_conf.json").and_return("")
      expect(Utils).to receive(:write_to_file).with(anything, { uuid => format(rules_template, device_ip: device_ip, terminal_ip: terminal_ip) }.to_json).and_raise("Unable to open the file")

      expect { @pfctl_object.add_offline_mode_rules(device_ip) }.to raise_error(StandardError, "Unable to open the file")
    end

    it "should raise error if it is unable to acquire lock" do
      expect(Utils).to receive(:with_lock).and_raise(LockfileTimeoutError)

      expect { @pfctl_object.add_offline_mode_rules(device_ip) }.to raise_error(LockfileTimeoutError)
    end
  end

  context "#remove_offline_mode_rules" do
    it "should remove offline mode rules from devices pf conf file" do
      allow(Utils).to receive(:with_lock).with(anything) do |&block|
        allow(File).to receive(:open).and_return(file_handle)
        block.call
      end

      allow(File).to receive(:read).with("#{PF_CONFIGURATION_DIR}/device_conf.json").and_return({ uuid => format(rules_template, device_ip: device_ip, terminal_ip: terminal_ip) }.to_json)
      expect(Utils).to receive(:write_to_file).with(anything, {}.to_json)

      @pfctl_object.remove_offline_mode_rules
    end

    it "should raise error if it is unable to open a file" do
      allow(Utils).to receive(:with_lock).with(anything) do |&block|
        allow(File).to receive(:open).and_return(file_handle)
        block.call
      end

      allow(File).to receive(:read).with("#{PF_CONFIGURATION_DIR}/device_conf.json").and_return({ uuid => format(rules_template, device_ip: device_ip, terminal_ip: terminal_ip) }.to_json)
      expect(Utils).to receive(:write_to_file).with(anything, {}.to_json).and_raise("Unable to open the file")

      expect { @pfctl_object.remove_offline_mode_rules }.to raise_error(StandardError, "Unable to open the file")
    end

    it "should raise error if it is unable to acquire lock" do
      expect(Utils).to receive(:with_lock).and_raise(LockfileTimeoutError)

      expect { @pfctl_object.remove_offline_mode_rules }.to raise_error(LockfileTimeoutError)
    end
  end

  context "#enable_offline_mode" do
    it "should not reload pf config if state file already exists" do
      allow_any_instance_of(DeviceState).to receive(:offline_mode_file_present?).and_return(true)
      expect(BrowserStack::OSUtils).not_to receive(:execute2)

      @pfctl_object.enable_offline_mode
    end

    it "should reload pf config for the device interfaces" do
      allow_any_instance_of(DeviceState).to receive(:offline_mode_file_present?).and_return(false)
      allow(@pfctl_object).to receive(:setup).and_return(true)
      allow(@pfctl_object).to receive(:fetch_ip_address).and_return(device_ip)
      expect(@pfctl_object).to receive(:add_offline_mode_rules).with(device_ip).and_return(true)
      expect(@pfctl_object).to receive(:reload_pf_conf).and_return(true)
      expect(@pfctl_object).to receive(:kill_states).and_return(true)
      expect_any_instance_of(DeviceState).to receive(:touch_offline_mode_file).and_return(true)

      @pfctl_object.enable_offline_mode
    end

    it "should raise error if device ip is nil" do
      allow_any_instance_of(DeviceState).to receive(:offline_mode_file_present?).and_return(false)
      allow(@pfctl_object).to receive(:setup).and_return(true)
      allow(@pfctl_object).to receive(:fetch_ip_address).and_return("")
      expect(@pfctl_object).not_to receive(:add_offline_mode_rules)
      expect(@pfctl_object).not_to receive(:reload_pf_conf)
      expect_any_instance_of(DeviceState).not_to receive(:touch_offline_mode_file)

      expect { @pfctl_object.enable_offline_mode }.to raise_error(OfflineModeFailureException)
    end
  end

  context "#disable_offline_mode" do
    it "should not reload pf config if state file is absent" do
      allow_any_instance_of(DeviceState).to receive(:offline_mode_file_present?).and_return(false)
      expect(@pfctl_object).not_to receive(:reload_pf_conf)

      @pfctl_object.disable_offline_mode
    end

    it "should restore pf config after removing offline mode rules" do
      allow_any_instance_of(DeviceState).to receive(:offline_mode_file_present?).and_return(true)
      expect(@pfctl_object).to receive(:remove_offline_mode_rules).and_return(true)
      expect(@pfctl_object).to receive(:reload_pf_conf).and_return(true)
      expect_any_instance_of(DeviceState).to receive(:remove_offline_mode_file).and_return(true)

      @pfctl_object.disable_offline_mode
    end

    it "should raise error if there is an error in acquiring lock during remove_offline_mode_rules" do
      allow_any_instance_of(DeviceState).to receive(:offline_mode_file_present?).and_return(true)
      expect(@pfctl_object).to receive(:remove_offline_mode_rules).and_raise(LockfileTimeoutError)

      expect { @pfctl_object.disable_offline_mode }.to raise_error(OfflineModeFailureException)
    end
  end

  context "#reload_pf_conf" do
    it "should reload original pf config file if device_conf is empty" do
      allow(Utils).to receive(:with_lock).with(anything) do |&block|
        allow(File).to receive(:open).and_return(file_handle)
        block.call
      end
      allow(File).to receive(:exists?).and_return(false)
      allow(File).to receive(:read).and_return("")
      allow(FileUtils).to receive(:cp).with(PF_ORIGINAL_CONF, "#{PF_CONFIGURATION_DIR}/custom_pf.conf").and_return(true)
      allow(File).to receive(:open).with("#{PF_CONFIGURATION_DIR}/custom_pf.conf", "a").and_return(file_handle)
      allow(file_handle).to receive(:write).and_return(true)
      allow(file_handle).to receive(:close).and_return(true)
      expect(BrowserStack::OSUtils).to receive(:execute2).with(/#{PF_ORIGINAL_CONF}/)

      @pfctl_object.reload_pf_conf
    end

    it "should reload original pf config file and raise error if validation fails" do
      allow(Utils).to receive(:with_lock).with(anything) do |&block|
        allow(File).to receive(:open).and_return(file_handle)
        block.call
      end
      allow(File).to receive(:exists?).and_return(false)
      allow(File).to receive(:read).and_return({ uuid => format(rules_template, device_ip: device_ip, terminal_ip: terminal_ip) }.to_json)
      allow(FileUtils).to receive(:cp).with(PF_ORIGINAL_CONF, "#{PF_CONFIGURATION_DIR}/custom_pf.conf").and_return(true)
      allow(File).to receive(:open).with("#{PF_CONFIGURATION_DIR}/custom_pf.conf", "a").and_return(file_handle)
      allow(file_handle).to receive(:write).and_return(true)
      allow(file_handle).to receive(:close).and_return(true)
      expect(@pfctl_object).to receive(:validate_custom_conf).and_raise(OfflineModeFailureException, "abc")
      expect(@pfctl_object).to receive(:remove_offline_mode_rules).and_return(true)
      expect(BrowserStack::OSUtils).to receive(:execute2).with(/sudo pfctl -q -f #{PF_ORIGINAL_CONF}/)

      expect { @pfctl_object.reload_pf_conf }.to raise_error(OfflineModeFailureException)
    end

    it "should load custom conf file if device_conf is not empty and it is a valid config" do
      allow(Utils).to receive(:with_lock).with(anything) do |&block|
        allow(File).to receive(:open).and_return(file_handle)
        block.call
      end
      allow(File).to receive(:exists?).and_return(false)
      allow(File).to receive(:read).and_return({ uuid => format(rules_template, device_ip: device_ip, terminal_ip: terminal_ip) }.to_json)
      allow(FileUtils).to receive(:cp).with(PF_ORIGINAL_CONF, "#{PF_CONFIGURATION_DIR}/custom_pf.conf").and_return(true)
      allow(File).to receive(:open).with("#{PF_CONFIGURATION_DIR}/custom_pf.conf", "a").and_return(file_handle)
      allow(file_handle).to receive(:write).and_return(true)
      allow(file_handle).to receive(:close).and_return(true)
      expect(@pfctl_object).to receive(:validate_custom_conf).and_return(true)
      expect(BrowserStack::OSUtils).to receive(:execute2).with(%r{#{PF_CONFIGURATION_DIR}/custom_pf.conf})

      @pfctl_object.reload_pf_conf
    end

    it "should raise error if it is unable to acquire lock" do
      expect(Utils).to receive(:with_lock).and_raise(LockfileTimeoutError)

      expect { @pfctl_object.reload_pf_conf }.to raise_error(LockfileTimeoutError)
    end
  end
end
