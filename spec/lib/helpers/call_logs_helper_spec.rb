require 'timecop'
require_relative '../../spec_helper'
require_relative '../../../lib/helpers/call_logs_helper'

describe CallLogsHelper do
  let(:device_config) { { 'webdriver_port' => 8080, "device_version" => '14.0' } }
  let(:uuid) { '000820-427503425' }
  let(:product) { 'app-live' }
  let(:session_id) { '1' }
  let(:ios_version) { '14.0' }
  let(:event_name) { 'web_events' }
  let(:start_time) { 100 }
  let(:end_time) { 100 }
  let(:success_data) do
    {
      result: "success",
      time_taken: 2
    }
  end
  let(:failure_data) do
    {
      result: "failed",
      time_taken: 2,
      error_reason: "error_reason",
      error_message: "error_message"
    }
  end
  let(:push_data) do
    {
      event_name: "ios-contacts-call-logs",
      product: product,
      os: "ios",
      os_version: ios_version,
      team: "device_features"
    }
  end

  let(:success_push_data) do
    {
      **push_data,
      event_json: {
        session_id: session_id,
        **success_data
      }
    }
  end

  let(:failure_push_data) do
    {
      **push_data,
      event_json: {
        session_id: session_id,
        **failure_data
      }
    }
  end

  let(:zombie_push_data) do
    {
      "result" => "failed",
      "device" => uuid,
      "product" => product,
      "session_id" => session_id,
      "os_version" => ios_version,
      "error_reason" => "error_reason",
      "error_message" => "error_message"
    }
  end

  before do
    expect(BrowserStack::Zombie).to receive(:configure)
    expect(File).to receive(:read).at_least(:once).and_return("")
    expect(JSON).to receive(:parse).at_least(:once).and_return("devices" => { uuid => device_config })

    Timecop.freeze(Time.at(start_time))
    @testObject = CallLogsHelper.new(uuid, session_id, product)
  end

  after do
    Timecop.return
    @testObject
  end

  context '#clear_call_logs' do
    before do
      start_time = Time.now
      expect(Time).to receive(:now).and_return(start_time)
    end
    it 'when call logs cleared successfully' do
      expect(BrowserStackAppHelper).to receive(:run_ui_test).with(uuid, :call_logs_cleanup, session_id: session_id)
      expect(@testObject).to receive(:prepare_data).with("success", start_time.to_i, "", "").and_return(success_data)
      expect(@testObject).to receive(:event_logger).with(success_data)
      @testObject.clear_call_logs
    end

    it "when clearing call log xcui test fails" do
      test_class = 'test_class'
      test_func = 'test_func'
      expect(BrowserStackAppHelper).to receive(:run_ui_test).with(uuid, :call_logs_cleanup, session_id: session_id).and_raise(BrowserStackTestExecutionError.new(test_class, test_func, 'test_output'))
      expect(@testObject).to receive(:prepare_data).with("failed", start_time.to_i, "BrowserStackTestExecutionError", "#{test_func} @ #{test_class} failed").and_return(failure_data)
      expect(@testObject).to receive(:event_logger).with(failure_data)
      @testObject.clear_call_logs
    end

    it "when some other error occurs" do
      expect(BrowserStackAppHelper).to receive(:run_ui_test).with(uuid, :call_logs_cleanup, session_id: session_id).and_raise(StandardError.new("some unknown error"))
      expect(@testObject).to receive(:prepare_data).with("failed", start_time.to_i, "UnknownError", "some unknown error").and_return(failure_data)
      expect(@testObject).to receive(:event_logger).with(failure_data)
      @testObject.clear_call_logs
    end
  end

  context "#prepare_data" do
    before do
      start_time = Time.now
      expect(Time).to receive(:now).and_return(start_time + 2)
    end

    it "should return success data" do
      expect( @testObject.send(:prepare_data, "success", start_time.to_i) ).to eq(success_data)
    end

    it "should return failed data" do
      expect( @testObject.send(:prepare_data, "failed", start_time.to_i, "error_reason", "error_message") ).to eq(failure_data)
    end
  end

  context "#event_logger" do
    it "should push success data to eds" do
      expect(Utils).to receive(:send_to_eds).with(success_push_data, "web_events", true)
      @testObject.event_logger(success_data)
    end

    it "should push failure data to eds and zombie" do
      expect(Utils).to receive(:send_to_eds).with(failure_push_data, "web_events", true)
      expect(BrowserStack::Zombie).to receive(:push_logs).with("ios-contacts-call-logs", "error_reason", zombie_push_data)
      @testObject.event_logger(failure_data)
    end
  end
end
