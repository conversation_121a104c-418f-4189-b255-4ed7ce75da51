require_relative '../../../lib/helpers/favorite_contact_helper'

describe FavoriteContactHelper do
  let(:uuid) { '000820-427503425' }
  let(:session_id) { '1' }
  let(:success_data) { { 'result' => "success", 'time_taken' => 2 } }
  let(:failure_data) { { 'result' => "failed", 'time_taken' => 2, "error" => "some_xcui_reason", "error_message" => "some xcui message" } }

  before do
    @test_object = FavoriteContactHelper.new(uuid, session_id)
  end

  context "#cleanup" do
    it "should return true" do
      expect(@test_object).to receive(:run_test).with(:favorite_contact_cleanup).and_return(success_data)
      expect_any_instance_of(DataReportHelper).to receive(:report).with(success_data)
      result = @test_object.cleanup
      expect(result).to eq(true)
    end

    it "should return false" do
      expect(@test_object).to receive(:run_test).with(:favorite_contact_cleanup).and_return(failure_data)
      expect_any_instance_of(DataReportHelper).to receive(:report).with(failure_data)
      result = @test_object.cleanup
      expect(result).to eq(false)
    end
  end
end
