require_relative '../../spec_helper'
require_relative '../../../lib/helpers/custom_contacts'
require_relative '../../../lib/utils/osutils'

describe CustomContacts do
  let(:file) { "BEGIN:VCARD\r\nBDAY;VALUE=DATE:1963-09-21\r\nVERSION:3.0\r\nN:Stenerson;Derik\r\nFN:<PERSON><PERSON>\r\nORG:Microsoft Corporation\r\nADR;TYPE=WORK,POSTAL,PARCEL:;;One Microsoft Way;Redmond;WA;98052-6399;USA\r\nTEL;TYPE=WORK,MSG:******-936-5522\r\nTEL;TYPE=WORK,FAX:******-936-7329\r\nEMAIL;TYPE=INTERNET:<EMAIL>\r\nEND:VCARD\r\n" }
  let(:device) { "device" }

  describe "get_email_details" do
    it "email" do
      allow(File).to receive(:read).and_return(file)
      vcard1 = Vpim::Vcard.decode(file)
      ret = CustomContacts.get_email_details(vcard1[0])
      expect(ret["contact"]).to eql(["51"])
      expect(ret["type"]).to eql("home")
      expect(ret["value"]).to eql("<EMAIL>")
      expect(ret["com.apple.syncservices.RecordEntityName"]).to eq("com.apple.contacts.Email Address")
    end
  end

  describe "get_contact_details" do
    it "contact" do
      allow(File).to receive(:read).and_return(file)
      vcard1 = Vpim::Vcard.decode(file)
      ret = CustomContacts.get_contact_details(vcard1[0])
      expect(ret["first name"]).to eql("Derik")
      expect(ret["last name"]).to eql("Stenerson")
      expect(ret["company name"]).to eql("Microsoft Corporation")
      expect(ret["middle name"]).to eql("")
      expect(ret["com.apple.syncservices.RecordEntityName"]).to eql("com.apple.contacts.Contact")
    end
  end

  describe "get_phone_details" do
    it "phone" do
      allow(File).to receive(:read).and_return(file)
      vcard1 = Vpim::Vcard.decode(file)
      ret = CustomContacts.get_phone_details(vcard1[0])
      expect(ret["contact"]).to eql(["51"])
      expect(ret["value"]).to eql("******-936-5522")
      expect(ret["type"]).to eql("work")
      expect(ret["com.apple.syncservices.RecordEntityName"]).to eql("com.apple.contacts.Phone Number")
    end
  end

  describe "get_address_details" do
    it "address" do
      allow(File).to receive(:read).and_return(file)
      vcard1 = Vpim::Vcard.decode(file)
      ret = CustomContacts.get_address_details(vcard1[0])
      expect(ret["contact"]).to eql(["51"])
      expect(ret["label"]).to eql("7")
      expect(ret["type"]).to eql("work")
      expect(ret["country"]).to eql("USA")
      expect(ret["country code"]).to eql("IN")
      expect(ret["postal code"]).to eql("98052-6399")
      expect(ret["state"]).to eql("Redmond")
      expect(ret["city"]).to eql("WA")
      expect(ret["street"]).to eql("One Microsoft Way")
      expect(ret["com.apple.syncservices.RecordEntityName"]).to eql("com.apple.contacts.Street Address")
    end
  end

  describe "vcf_to_json_conversion" do
    it "vcf_to_json_conversion" do
      allow(File).to receive(:read).with("file_path").and_return(file)
      allow(Utils).to receive(:write_to_file)
      res = CustomContacts.vcf_to_json_conversion("file_path", device)
      expect(res[0]).to eq("/tmp/custom_media_device/contacts_list_file.json")
      expect(res[1]).to eq("/tmp/custom_media_device/contacts_metadata_file.json")
    end
  end

  describe "load_custom_contacts" do
    it "load_custom_contacts" do
      allow(CustomContacts).to receive(:vcf_to_json_conversion).with("file_path", device).and_return(["contact_list_path", "contact_metadata_path"])
      expect(FileUtils).to receive(:touch)
      allow(CustomContacts).to receive(:convert_json_to_plist)
      expect(IdeviceUtils).to receive(:load_contacts).and_return(["", "0"])
      res = CustomContacts.load_custom_contacts(device, "file_path")
    end
  end
end
