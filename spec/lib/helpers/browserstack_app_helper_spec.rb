require_relative '../../spec_helper'
require_relative '../../../lib/helpers/browserstack_app_helper'

describe BrowserStackAppHelper do
  let(:uuid) { "random_uuid" }
  let(:test_suite_bundle_id) { 'com.browserstack.BrowserStackUITests.xctrunner' }
  let(:test_suite_version) { '12.0' }
  let(:browserstack_app_version) { '8.0' }
  let(:ppuid) { "some-provisioning-profile-id" }
  let(:device_state) { double('mock_device_state') }
  let(:installed_app) { double('mock_installed_app') }
  let(:test_class_name) { 'KeyChainBiometricTests' }
  let(:test_function_name) { 'testBiometric' }
  let(:chromium_mock_obj) { double('chromium_mock') }
  let(:chromium_bundle_id) { 'mock_bundle' }
  let(:mock_aws_client) { double("mock_aws_client") }
  let(:device_config) { double("device_config") }
  let(:screenshot_dir) { "/tmp/#{uuid}" }
  let(:session_id) { "abcd" }
  let(:file_handle) { double("file_handle") }
  let(:device_info) do
    {
      'ip' => '***********',
      'sub_region' => 'US-West',
      'device_name' => 'iPhone 13',
      'device_version' => 'iOS 15.1'
    }
  end
  let(:deploy_env) { 'not-canary' }
  let(:test_to_execute) { :apple_id_signout }
  let(:environment_variables) { {} }

  before do
    allow(BrowserStackAppHelper).to receive(:bstack_test_app_bundle_id).and_return(test_suite_bundle_id)
    allow(BrowserStackAppHelper).to receive(:ppuid).and_return(ppuid)
    allow(BrowserStackAppHelper).to receive(:device_state).and_return(device_state)
    allow(BrowserStackAppHelper).to receive(:device_version).and_return(16.0)
    # allow(BrowserStackAppHelper).to receive(:device_config).and_return(device_config)
    allow(BrowserStackAppHelper).to receive(:device_config).with(uuid).and_return(device_info)
    allow(Chromium).to receive(:new).and_return(chromium_mock_obj)
    allow(chromium_mock_obj).to receive(:bundle_id).and_return(chromium_bundle_id)

    environment_variables['MACHINE'] = device_info['ip']
    environment_variables['REGION'] = device_info['sub_region']
    environment_variables['DEVICE_NAME'] = device_info['device_name']
    environment_variables['OS_VERSION'] = device_info['device_version']
    environment_variables['DEPLOY_ENV'] = deploy_env
    environment_variables['DEVICE'] = uuid
    environment_variables['SESSION_ID'] = session_id
    environment_variables['TEST_NAME'] = test_to_execute
  end

  context "handle_cleanup_tasks" do
    it "should call all the required methods" do
      allow(BrowserStack::Zombie).to receive(:push_logs)

      expect(BrowserStackAppHelper).to receive(:check_and_build_and_install_browserstack_app).with(uuid)
      expect(BrowserStackAppHelper).to receive(:check_and_install_browserstack_apps).with(uuid)

      BrowserStackAppHelper.handle_cleanup_tasks(uuid)
    end
  end

  context "browserstack_app_built?" do
    let(:bstack_app_path) { "bstack_app_path" }
    let(:bstack_test_suite_path) { "bstack_test_suite_path" }
    let(:bstack_info_plist) { "bstack_info_plist" }
    let(:test_suite_info_plist) { "test_suite_info_plist" }
    let(:rebuild_path) { REBUILD_BROWSERSTACK_APP_PATH_PREFIX + uuid }

    before(:each) do
      allow(BrowserStackAppHelper).to receive(:browserstack_app_path).and_return(bstack_app_path)
      allow(BrowserStackAppHelper).to receive(:browserstack_app_info_plist).and_return(bstack_info_plist)
      allow(BrowserStackAppHelper).to receive(:browserstack_test_suite_path).and_return(bstack_test_suite_path)
      allow(BrowserStackAppHelper).to receive(:test_suite_info_plist).and_return(test_suite_info_plist)

      allow(File).to receive(:exist?).with(bstack_app_path).and_return(true)
      allow(File).to receive(:exist?).with(bstack_info_plist).and_return(true)
      allow(File).to receive(:exist?).with(bstack_test_suite_path).and_return(true)
      allow(File).to receive(:exist?).with(test_suite_info_plist).and_return(true)
      allow(File).to receive(:exist?).with(rebuild_path).and_return(false)
    end

    it "returns false when browserstack_app_path does not exist" do
      allow(File).to receive(:exist?).with(bstack_app_path).and_return(false)
      expect(BrowserStackAppHelper.browserstack_app_built?(uuid)).to eq(false)
    end

    it "returns false when browserstack app info.plist does not exist" do
      allow(File).to receive(:exist?).with(bstack_info_plist).and_return(false)
      expect(BrowserStackAppHelper.browserstack_app_built?(uuid)).to eq(false)
    end

    it "returns false when browserstack_test_suite_path does not exist" do
      allow(File).to receive(:exist?).with(bstack_test_suite_path).and_return(false)
      expect(BrowserStackAppHelper.browserstack_app_built?(uuid)).to eq(false)
    end

    it "returns false when browserstack app info.plist does not exist" do
      allow(File).to receive(:exist?).with(test_suite_info_plist).and_return(false)
      expect(BrowserStackAppHelper.browserstack_app_built?(uuid)).to eq(false)
    end

    it "returns false when rebuild_browserstack_path does exist" do
      allow(File).to receive(:exist?).with(rebuild_path).and_return(true)
      expect(BrowserStackAppHelper.browserstack_app_built?(uuid)).to eq(false)
    end

    it "otherwise returns true" do
      expect(BrowserStackAppHelper.browserstack_app_built?(uuid)).to eq(true)
    end
  end

  context "#already_built?" do
    let(:rebuild_path) { REBUILD_BROWSERSTACK_APP_PATH_PREFIX + uuid }

    before do
      allow(BrowserStackAppHelper).to receive(:browserstack_app_built_for_prov_id_path).and_return("browserstack_app_built_for_prov_id_path")
    end

    it "returns true when app was built recently" do
      expect(File).to receive(:exist?).with("browserstack_app_built_for_prov_id_path").and_return(true)
      expect(File).to receive(:exist?).with(rebuild_path).and_return(true)
      expect(File).to receive(:mtime).with("browserstack_app_built_for_prov_id_path").and_return(129)
      expect(File).to receive(:mtime).with(rebuild_path).and_return(0)

      expect(BrowserStackAppHelper.already_built?(uuid)).to eq(true)
    end

    it "returns false when app was built earlier" do
      expect(File).to receive(:exist?).with("browserstack_app_built_for_prov_id_path").and_return(true)
      expect(File).to receive(:exist?).with(rebuild_path).and_return(true)
      expect(File).to receive(:mtime).with("browserstack_app_built_for_prov_id_path").and_return(9)
      expect(File).to receive(:mtime).with(rebuild_path).and_return(129)

      expect(BrowserStackAppHelper.already_built?(uuid)).to eq(false)
    end

    it "returns false when app was not built" do
      expect(File).to receive(:exist?).with("browserstack_app_built_for_prov_id_path").and_return(false)

      expect(BrowserStackAppHelper.already_built?(uuid)).to eq(false)
    end

    it "returns false when rebuild file doesn't exit " do
      expect(File).to receive(:exist?).with("browserstack_app_built_for_prov_id_path").and_return(true)
      expect(File).to receive(:exist?).with(rebuild_path).and_return(false)

      expect(BrowserStackAppHelper.already_built?(uuid)).to eq(false)
    end
  end

  context "#build_browserstack_app" do
    let(:rebuild_path) { REBUILD_BROWSERSTACK_APP_PATH_PREFIX + uuid }

    it "should not rebuild if already built" do
      expect(BrowserStackAppHelper).to receive(:already_built?).and_return(true)
      expect(FileUtils).to receive(:rm_f).with(rebuild_path)
      expect(BrowserStack::WebDriverAgent).to_not receive(:build_browserstack_app)

      BrowserStackAppHelper.build_browserstack_app(uuid)
    end

    it "should rebuild if not already built" do
      expect(BrowserStackAppHelper).to receive(:already_built?).and_return(false)
      expect(BrowserStack::WebDriverAgent).to receive(:build_browserstack_app).ordered
      expect(FileUtils).to receive(:rm_f).with(rebuild_path).ordered

      BrowserStackAppHelper.build_browserstack_app(uuid)
    end
  end

  context "check_and_build_and_install_browserstack_app" do
    let(:rebuild_path) { REBUILD_BROWSERSTACK_APP_PATH_PREFIX + uuid }

    it 'should rebuild if the app if already installed' do
      allow(BrowserStackAppHelper).to receive(:browserstack_app_built?).and_return(false)
      allow(BrowserStackAppHelper).to receive(:browserstack_app_install_required?).and_return(false)

      expect(BrowserStackAppHelper).to receive(:build_browserstack_app)
      expect(IdeviceUtils).to receive(:uninstall_app).with(uuid, BROWSERSTACK_APP_BUNDLE_ID)
      expect(IdeviceUtils).to receive(:uninstall_app).with(uuid, test_suite_bundle_id)
      expect(BrowserStackAppHelper).to receive(:install_browserstack_app).with(uuid)
      expect(BrowserStackAppHelper).to receive(:install_browserstack_test_suite).with(uuid)

      BrowserStackAppHelper.check_and_build_and_install_browserstack_app(uuid)
    end

    it "should not rebuild the app if already built and installed" do
      allow(BrowserStackAppHelper).to receive(:browserstack_app_built?).and_return(true)
      allow(BrowserStackAppHelper).to receive(:browserstack_app_install_required?).and_return(false)

      expect(BrowserStackAppHelper).not_to receive(:build_browserstack_app)
      expect(BrowserStackAppHelper).not_to receive(:install_browserstack_app)
      BrowserStackAppHelper.check_and_build_and_install_browserstack_app(uuid)
    end

    it "should raise exception if browserstack build fails" do
      allow(BrowserStackAppHelper).to receive(:browserstack_app_built?).and_return(false)
      allow(BrowserStackAppHelper).to receive(:browserstack_app_install_required?).and_return(true)

      allow(BrowserStackAppHelper).to receive(:build_browserstack_app).and_raise(BrowserStackAppBuildError)
      expect { BrowserStackAppHelper.check_and_build_and_install_browserstack_app(uuid) }.to raise_error(BrowserStackAppBuildError)
    end
  end

  context "#run_ui_test" do
    before(:each) do
      allow(File).to receive(:write).and_return(true)
      BrowserStackAppHelper.instance_variable_set(:@xctest_xmlfile, "/tmp/bstack_test_suite_xctestrun_#{uuid}.xml")
    end

    context "when no env variables are provided" do
      let(:auto_join_not_present_logline) { "    t =    10.90s Assertion Failure: SettingsUITests.swift:92: XCTAssertTrue failed - Auto Join switch not present" }

      before(:each) do
        allow(BrowserStackAppHelper).to receive(:generate_bstack_xctestrun_file).with(uuid, environment_variables: environment_variables).and_return(true)
      end

      it "should throw an error if unknown test provided" do
        expect { BrowserStackAppHelper.run_ui_test(uuid, :some_test, session_id: session_id) }.to raise_error
      end

      it "should not throw any error in case of success" do
        puts "in spec: #{environment_variables}"
        expect(BrowserStack::OSUtils).to receive(:execute).with("xcodebuild -xctestrun /tmp/bstack_test_suite_xctestrun_#{uuid}.xml -destination \"id=#{uuid}\" test-without-building -only-testing:\"BrowserStackUITests/SettingsUITests/testAppleIDSignOut\" 2>&1 | tee -a /var/log/browserstack/browserstack_xcuitest_random_uuid_apple_id_signout.log", true, timeout: 60).and_return(["it was TEST EXECUTE SUCCEEDED worked fine", "pid 25448 exit 0"])
        expect { BrowserStackAppHelper.run_ui_test(uuid, :apple_id_signout, session_id: session_id) }.to_not raise_error
      end

      it "should raise error in case of test execution error" do
        expect(BrowserStack::OSUtils).to receive(:execute).with("xcodebuild -xctestrun /tmp/bstack_test_suite_xctestrun_#{uuid}.xml -destination \"id=#{uuid}\" test-without-building -only-testing:\"BrowserStackUITests/SettingsUITests/testAppleIDSignOut\" 2>&1 | tee -a /var/log/browserstack/browserstack_xcuitest_random_uuid_apple_id_signout.log", true, timeout: 60).and_return(["it went wrong", "pid 25448 exit 0"])
        expect { BrowserStackAppHelper.run_ui_test(uuid, :apple_id_signout, session_id: session_id) }.to raise_error(BrowserStackTestExecutionError)
      end

      it "should raise error in case of command execution error" do
        expect(BrowserStack::OSUtils).to receive(:execute).with("xcodebuild -xctestrun /tmp/bstack_test_suite_xctestrun_#{uuid}.xml -destination \"id=#{uuid}\" test-without-building -only-testing:\"BrowserStackUITests/SettingsUITests/testAppleIDSignOut\" 2>&1 | tee -a /var/log/browserstack/browserstack_xcuitest_random_uuid_apple_id_signout.log", true, timeout: 60).and_raise(OSUtilsError)
        expect { BrowserStackAppHelper.run_ui_test(uuid, :apple_id_signout, session_id: session_id) }.to raise_error(OSUtilsError)
      end
    end

    context "when env variables are provided" do
      let(:env_hash) { { "variable_name" => "value1" } }

      before(:each) do
        expect(BrowserStackAppHelper).to receive(:generate_bstack_xctestrun_file).with(uuid, environment_variables: env_hash).and_return(true)
      end

      it "should not throw any error in case of success" do
        expect(BrowserStack::OSUtils).to receive(:execute).with("xcodebuild -xctestrun /tmp/bstack_test_suite_xctestrun_#{uuid}.xml -destination \"id=#{uuid}\" test-without-building -only-testing:\"BrowserStackUITests/SettingsUITests/testAppleIDSignOut\" 2>&1 | tee -a /var/log/browserstack/browserstack_xcuitest_random_uuid_apple_id_signout.log", true, timeout: 60).and_return(["it was TEST EXECUTE SUCCEEDED worked fine", "pid 25448 exit 0"])
        expect { BrowserStackAppHelper.run_ui_test(uuid, :apple_id_signout, session_id: "session_id", environment_variables: env_hash) }.to_not raise_error
      end
    end
  end

  context "#run_cleanup_tests" do
    before(:each) do
      allow(BrowserStackAppHelper).to receive(:generate_bstack_xctestrun_file).with(uuid, { environment_variables: { chromium_package: chromium_bundle_id } }).and_return(true)
      allow(File).to receive(:write).and_return(true)
      allow(BrowserStack::OSUtils).to receive(:execute).and_return(["SUCCEEDED", 0])
      BrowserStackAppHelper.instance_variable_set(:@xctest_xmlfile, "/tmp/bstack_test_suite_xctestrun_#{uuid}.xml")
    end

    it "should return appropriate kind on values" do
      expect(File).to receive(:open).with("#{screenshot_dir}/#{session_id}.mp4", "rb").and_yield(file_handle)
      allow(Aws::S3::Client).to receive(:new).and_return(mock_aws_client)
      expect(mock_aws_client).to receive(:put_object)
      run_cleanup_test = BrowserStackAppHelper.run_cleanup_tests(uuid, "validation", "all", "abcd")
      expect(JSON.parse(run_cleanup_test)["passed"]).to be_an(Array)
      expect(JSON.parse(run_cleanup_test)["passed_count"]).to be_an(Integer)
      expect(JSON.parse(run_cleanup_test)["failed"]).to be_an(Hash)
      expect(JSON.parse(run_cleanup_test)["failed_count"]).to be_an(Integer)
    end

    it "should not throw any error" do
      allow(BrowserStack::OSUtils).to receive(:execute).and_return(["error_output", 1])
      expect(File).to receive(:open).with("#{screenshot_dir}/#{session_id}.mp4", "rb").and_yield(file_handle)
      allow(Aws::S3::Client).to receive(:new).and_return(mock_aws_client)
      expect(mock_aws_client).to receive(:put_object)
      run_cleanup_test = BrowserStackAppHelper.run_cleanup_tests(uuid, "validation", "all", "abcd")
      expect { run_cleanup_test }.to_not raise_error
      expect(JSON.parse(run_cleanup_test)["passed"]).to match_array([])
      expect(JSON.parse(run_cleanup_test)["failed_count"]).to be > 0
    end
  end

  context "run_xcui_test" do
    before(:each) do
      allow(BrowserStackAppHelper).to receive(:generate_bstack_xctestrun_file).with(uuid, { environment_variables: {} }).and_return(true)
      allow(BrowserStack::OSUtils).to receive(:execute).and_return(["Test output", 0])
      BrowserStackAppHelper.instance_variable_set(:@xctest_xmlfile, "/tmp/bstack_test_suite_xctestrun_#{uuid}.xml")
      allow(BrowserStackAppHelper).to receive(:check_and_install_browserstack_test_suite)
    end

    it 'should return a JSON object with result and status' do
      expected_output = { result: "Test output", status: 0 }.to_json
      result = BrowserStackAppHelper.run_xcui_test(uuid, 60, test_class_name, test_function_name)
      expect(JSON.parse(result)["status"]).to be_an(Integer)
      expect(result).to eq(expected_output)
    end

    it 'should handle a non-default timeout value' do
      expected_output = { result: "Test output", status: 0 }.to_json
      result = BrowserStackAppHelper.run_xcui_test(uuid, 120, test_class_name, test_function_name)
      expect(JSON.parse(result)["status"]).to be_an(Integer)
      expect(result).to eq(expected_output)
    end
  end

  context "launching app" do
    it "should not raise exception if the automation was successful" do
      allow(BrowserStackAppHelper).to receive(:run_ui_test).with(uuid, :launch_browserstack_app)

      expect(BrowserStack::Zombie).to_not receive(:push_logs)
      expect(Utils).to receive(:send_general_feature_usage_data_to_eds).with("some_session_id", FEATURE_CUSTOM_MEDIA, true, GENRE_APP_AUTOMATE, "")
      expect { BrowserStackAppHelper.launch_browserstack_app(uuid, "some_session_id") }.to_not raise_error
    end

    it "should raise exception if the automation was unsuccessful" do
      allow(BrowserStackAppHelper).to receive(:run_ui_test).with(uuid, :launch_browserstack_app).and_raise(StandardError)

      expect(Utils).to receive(:send_general_feature_usage_data_to_eds).with("", FEATURE_CUSTOM_MEDIA, false, GENRE_APP_AUTOMATE, "App launch instrumentation failed")
      expect(BrowserStack::Zombie).to receive(:push_logs)
      expect { BrowserStackAppHelper.launch_browserstack_app(uuid) }.to raise_error(LaunchBrowserStackError)
    end
  end

  context '.check_and_install_browserstack_test_suite' do
    let(:reinstall_file) { "reinstall_browserstack_test_suite_#{uuid}" }

    it 'should install the test suite if required' do
      expect(BrowserStackAppHelper).to receive(:test_suite_install_required?).with(uuid).and_return(true)
      expect(BrowserStackAppHelper).to receive(:install_browserstack_test_suite).with(uuid)
      expect(device_state).to receive(:remove_reinstall_browserstack_test_suite_file)

      BrowserStackAppHelper.check_and_install_browserstack_test_suite(uuid)
    end
  end

  context '.test_suite_install_required?' do
    let(:reinstall_file) { "reinstall_browserstack_test_suite_#{uuid}" }

    before do
      allow(device_state).to receive(:reinstall_browserstack_test_suite_file_present?).and_return(false)
      allow(BrowserStackAppHelper).to receive(:test_suite_version).and_return(test_suite_version)
      allow(InstalledApp).to receive(:new).and_return(installed_app)
      allow(installed_app).to receive(:reinstall?).and_return(false)
      allow(BrowserStackAppHelper).to receive(:browserstack_test_suite_present?).and_return(true)
    end

    it 'should return true if reinstall file found' do
      expect(device_state).to receive(:reinstall_browserstack_test_suite_file_present?).and_return(true)
      expect(BrowserStackAppHelper.test_suite_install_required?(uuid)).to be(true)
    end

    it 'should return true if recommended by InstalledApp object' do
      expect(installed_app).to receive(:reinstall?).with(latest_version: test_suite_version).and_return(true)
      expect(BrowserStackAppHelper.test_suite_install_required?(uuid)).to be(true)
    end

    it 'should return true if test suite is not present on device' do
      expect(BrowserStackAppHelper)
        .to receive(:browserstack_test_suite_present?).with(uuid).and_return(false)
      expect(BrowserStackAppHelper.test_suite_install_required?(uuid)).to be(true)
    end

    it 'should otherwise return false' do
      expect(BrowserStackAppHelper.test_suite_install_required?(uuid)).to be(false)
    end
  end

  context '.check_and_install_browserstack_app' do
    it 'should install the test suite if required' do
      expect(BrowserStackAppHelper)
        .to receive(:browserstack_app_install_required?).with(uuid).and_return(true)

      expect(BrowserStackAppHelper).to receive(:install_browserstack_app).with(uuid)
      expect(device_state).to receive(:remove_reinstall_browserstack_app_file)

      BrowserStackAppHelper.check_and_install_browserstack_app(uuid)
    end
  end

  context '.browserstack_app_install_required?' do
    before do
      allow(device_state).to receive(:reinstall_browserstack_app_file_present?).and_return(false)
      allow(BrowserStackAppHelper)
        .to receive(:browserstack_app_version).with(uuid).and_return(browserstack_app_version)
      allow(InstalledApp).to receive(:new).and_return(installed_app)
      allow(installed_app).to receive(:reinstall?).and_return(false)
      allow(BrowserStackAppHelper).to receive(:browserstack_app_present?).and_return(true)
    end

    it 'should return true if reinstall file found' do
      expect(device_state).to receive(:reinstall_browserstack_app_file_present?).and_return(true)
      expect(BrowserStackAppHelper.browserstack_app_install_required?(uuid)).to be(true)
    end

    it 'should return true if recommended by InstalledApp object' do
      expect(installed_app)
        .to receive(:reinstall?).with(latest_version: browserstack_app_version).and_return(true)
      expect(BrowserStackAppHelper.browserstack_app_install_required?(uuid)).to be(true)
    end

    it 'should return true if test suite is not present on device' do
      expect(BrowserStackAppHelper)
        .to receive(:browserstack_app_present?).with(uuid).and_return(false)
      expect(BrowserStackAppHelper.browserstack_app_install_required?(uuid)).to be(true)
    end

    it 'should otherwise return false' do
      expect(BrowserStackAppHelper.browserstack_app_install_required?(uuid)).to be(false)
    end
  end

  context "install_browserstack_test_suite" do
    let(:test_suite_path) { "some_browserstack_test_suite_path" }

    before do
      allow(BrowserStackAppHelper).to receive(:browserstack_test_suite_path).and_return(test_suite_path)
      allow(File).to receive(:exist?).and_return(true)
      allow(IdeviceUtils).to receive(:install_app)
      allow(BrowserStackAppHelper).to receive(:ppuid).and_return(ppuid)
      allow(BrowserStackAppHelper).to receive(:browserstack_test_suite_present?).and_return(true)
      allow(BrowserStackAppHelper).to receive(:test_suite_version).and_return(test_suite_version)
      allow(InstalledApp).to receive(:new).and_return(installed_app)
      allow(installed_app).to receive(:update_config)
    end

    it "should raise error if browserstack test suite path is not present" do
      allow(File).to receive(:exist?).with(test_suite_path).and_return(false)
      expect { BrowserStackAppHelper.install_browserstack_test_suite(uuid) }
        .to raise_error('BrowserStack TestSuite file not found')
    end

    it "should install browserstack test suite" do
      expect(IdeviceUtils).to receive(:install_app).with(uuid, test_suite_path)
      BrowserStackAppHelper.install_browserstack_test_suite(uuid)
    end

    it "should raise error if test suite not found on device after install" do
      allow(BrowserStackAppHelper).to receive(:browserstack_test_suite_present?).with(uuid).and_return(false)
      expect { BrowserStackAppHelper.install_browserstack_test_suite(uuid) }
        .to raise_error("BrowserStack TestSuite install failed.")
    end

    it 'should update installed app config after install' do
      expect(InstalledApp).to receive(:new).with(uuid, test_suite_bundle_id).and_return(installed_app)
      expect(installed_app).to receive(:update_config).with(test_suite_version, ppuid)
      BrowserStackAppHelper.install_browserstack_test_suite(uuid)
    end
  end

  context "install_browserstack_app" do
    let(:browserstack_app_path) { "some_browserstack_app_path" }
    let(:app_version) { '10.0' }

    before do
      allow(BrowserStackAppHelper).to receive(:browserstack_app_path).with(uuid).and_return(browserstack_app_path)
      allow(File).to receive(:exist?).with(browserstack_app_path).and_return(true)
      allow(IdeviceUtils).to receive(:install_app).with(uuid, browserstack_app_path, { avoid_devicectl: false })
      allow(BrowserStackAppHelper).to receive(:browserstack_app_present?).with(uuid).and_return(true)
      allow(BrowserStackAppHelper)
        .to receive(:browserstack_app_version).with(uuid).and_return(app_version)
      allow(InstalledApp).to receive(:new).and_return(installed_app)
      allow(installed_app).to receive(:update_config)
      allow(device_state).to receive(:touch_photos_permission_file)
    end

    it "should raise error if app does not exist at path" do
      expect(File).to receive(:exist?).with(browserstack_app_path).and_return(false)
      expect { BrowserStackAppHelper.install_browserstack_app(uuid, { avoid_devicectl: false }) }
        .to raise_error("BrowserStack App installation failed, directory not found.")
    end

    it 'should install app' do
      expect(IdeviceUtils).to receive(:install_app).with(uuid, browserstack_app_path, { avoid_devicectl: false })
      BrowserStackAppHelper.install_browserstack_app(uuid)
    end

    it "should raise error if app not present on device after install" do
      expect(BrowserStackAppHelper).to receive(:browserstack_app_present?).and_return(false)
      expect { BrowserStackAppHelper.install_browserstack_app(uuid) }
        .to raise_error('BrowserStackApp install failed.')
    end

    it 'should update installed app config after install' do
      expect(InstalledApp).to receive(:new).with(uuid, 'com.browserstack.app').and_return(installed_app)
      expect(installed_app).to receive(:update_config).with(app_version, ppuid)
      BrowserStackAppHelper.install_browserstack_app(uuid)
    end

    it 'should touch photos permission file after install' do
      expect(device_state).to receive(:touch_photos_permission_file)
      BrowserStackAppHelper.install_browserstack_app(uuid)
    end
  end

  context "refresh_gallery" do
    it "should launch the app for refreshing gallery" do
      expect(BrowserStack.logger).to receive(:info).with("Refreshing gallery #{uuid}")
      expect(BrowserStackAppHelper).to receive(:check_and_install_browserstack_apps)
      expect(BrowserStackAppHelper).to receive(:launch_browserstack_app)
      expect(BrowserStack.logger).to receive(:info).with("Custom Media Sync worked successfully for device #{uuid} and session_id : !")
      expect { BrowserStackAppHelper.refresh_gallery(uuid) }.to_not raise_error
    end

    it "should raise error in case of some issue while launching app and should raise error" do
      expect(BrowserStack.logger).to receive(:info).with("Refreshing gallery #{uuid}")
      expect(BrowserStackAppHelper).to receive(:check_and_install_browserstack_apps)
      expect(BrowserStackAppHelper).to receive(:launch_browserstack_app).and_raise(LaunchBrowserStackError)
      expect(BrowserStack.logger).to receive(:error)
      expect { BrowserStackAppHelper.refresh_gallery(uuid) }.to raise_error(LaunchBrowserStackError)
    end
  end

  context "generate_xctestrun_file" do
    before do
      BrowserStackAppHelper.instance_variable_set(:@bstack_test_suite_bundle_id, nil)
      allow(BrowserStackAppHelper).to receive(:bstack_test_app_bundle_id).and_call_original
    end

    it "should generate xctestrun file for BrowserStack test suite when XCode version is 11" do
      expect(BrowserStack::OSUtils).to receive(:check_xcode).and_return("Xcode 11.5\nBuild version 11E608c")
      expect(Utils).to receive(:write_to_file).with("/tmp/bstack_test_suite_xctestrun_#{uuid}.xml", /com.browserstack.BrowserStackUITests.xctrunner/)
      BrowserStackAppHelper.generate_bstack_xctestrun_file(uuid)
    end

    it "should generate xctestrun file for BrowserStack test suite when XCode version is not 11" do
      expect(BrowserStack::OSUtils).to receive(:check_xcode).and_return("Xcode 10.0\nBuild version 10A255")
      expect(Utils).to receive(:write_to_file).with("/tmp/bstack_test_suite_xctestrun_#{uuid}.xml", /com.apple.test.BrowserStackUITests-Runner/)
      BrowserStackAppHelper.generate_bstack_xctestrun_file(uuid)
    end
  end

  context "bstack_test_app_bundle_id" do
    before(:each) do
      BrowserStackAppHelper.instance_variable_set(:@bstack_test_suite_bundle_id, nil)
      allow(BrowserStackAppHelper).to receive(:bstack_test_app_bundle_id).and_call_original
    end

    it "should return com.apple.test.BrowserStackUITests-Runner if XCode version is not 11" do
      expect(BrowserStack::OSUtils).to receive(:check_xcode).and_return("Xcode 10.0\nBuild version 10A255")
      expect(BrowserStackAppHelper.bstack_test_app_bundle_id).to eq("com.apple.test.BrowserStackUITests-Runner")
      expect(BrowserStackAppHelper.instance_variable_get(:@bstack_test_suite_bundle_id)).to eq("com.apple.test.BrowserStackUITests-Runner")
    end

    it "should return com.browserstack.BrowserStackUITests.xctrunner if XCode version is 11" do
      expect(BrowserStack::OSUtils).to receive(:check_xcode).and_return("Xcode 11.5\nBuild version 11E608c")
      expect(BrowserStackAppHelper.bstack_test_app_bundle_id).to eq("com.browserstack.BrowserStackUITests.xctrunner")
      expect(BrowserStackAppHelper.instance_variable_get(:@bstack_test_suite_bundle_id)).to eq("com.browserstack.BrowserStackUITests.xctrunner")
    end
  end
end
