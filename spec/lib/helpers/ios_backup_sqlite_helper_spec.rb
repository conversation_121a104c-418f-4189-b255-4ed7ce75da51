require_relative '../../../lib/helpers/ios_backup_sqlite_helper'
require_relative '../../spec_helper'

describe IosBackupSqliteHelper do
  let(:uuid) { '000820-427503425' }
  let(:sequel) { double("Sequel") }
  let(:subject) { IosBackupSqliteHelper.new(uuid) }
  let(:chat_message_join) { double('chat_message_join') }
  let(:chat) { double('chat') }
  let(:message) { double('message') }

  describe '#retrieve_messages' do
    it "file does not exists" do
      expect(File).to receive(:exist?).and_return(false)
      expect(subject.retrieve_messages).to eql("error")
    end

    it "db is null" do
      expect(File).to receive(:exist?).and_return(true)
      expect(Sequel).to receive(:connect).and_return(nil)
      expect(subject.retrieve_messages).to eql("error")
    end

    it "success run" do
      result = {}
      expect(File).to receive(:exist?).and_return(true)
      expect(Sequel).to receive(:connect).and_return(sequel)
      expect(sequel).to receive(:[]).with(:chat_message_join).and_return(chat_message_join)
      expect(sequel).to receive(:[]).with(:chat).and_return(chat)
      expect(sequel).to receive(:[]).with(:message).and_return(message)
      expect(message).to receive(:join).with(:chat_message_join, message_id: :ROWID).and_return(message)
      expect(message).to receive(:join).with(:chat, ROWID: :chat_id).and_return(message)
      expect(message).to receive(:select).with(:chat_identifier, :text, :destination_caller_id).and_return(message)
      expect(message).to receive(:all).and_return(result)
      expect(sequel).to receive(:disconnect).and_return(true)
      expect(subject.retrieve_messages).to eql({})
    end
  end
end
