require 'timecop'
require_relative '../../spec_helper'
require_relative '../../../lib/helpers/custom_certificate_helper'

describe CustomCertificate do
  let(:device_id) { '000820-427503425' }
  let(:session_id) { '1' }
  let(:certificate_details) { { "s3_url" => "https://example.com/cert.pfx", "filename" => "cert.pfx", "filetype" => "pfx", "password" => "password123" }.to_json }
  let(:mock_device_state) { double("DeviceState").as_null_object }
  let(:mock_configuration_profiles_manager) { double("ConfigurationProfilesManager").as_null_object }
  let(:mock_data_reporter) { double("DataReportHelper").as_null_object }

  before do
    allow(DeviceState).to receive(:new).and_return(mock_device_state)
    allow(ConfigurationProfilesManager).to receive(:new).and_return(mock_configuration_profiles_manager)
    allow(DataReportHelper).to receive(:new).and_return(mock_data_reporter)
    allow(BrowserStack::IosMdmServiceClient).to receive(:configure)
    allow(BrowserStack::HttpUtils).to receive(:download)
    allow(FileUtils).to receive(:mkdir_p)
    allow(File).to receive(:delete)

    @custom_certificate = CustomCertificate.new(device_id, session_id, certificate_details)
  end

  describe '.get_file_details' do
    it 'parses media JSON and returns file details' do
      media = certificate_details
      result = CustomCertificate.get_file_details(media)
      expect(result).to eq(["https://example.com/cert.pfx", "cert.pfx", "pfx", "password123"])
    end
  end

  describe '.custom_certificate_folder' do
    it 'returns the custom certificate folder path' do
      expect(@custom_certificate.custom_certificate_folder(device_id)).to eq("/tmp/custom_certificates_#{device_id}")
    end
  end

  describe '#install_pfx_certificate' do
    context 'when installation is successful' do
      it 'installs the certificate and reports success' do
        expect(BrowserStack.logger).to receive(:info).with("[CustomCertificate] Installing custom certificate on device : #{device_id} and session ID : #{session_id}")
        expect(BrowserStack::HttpUtils).to receive(:download).with("https://example.com/cert.pfx", anything, { retry_count: 3, timeout: 20 }, "custom_media")
        expect(mock_configuration_profiles_manager).to receive(:install_profile).with(:pfx_certificate, hash_including(certificate_path: anything, password: "password123"), install_via: :mdm)
        expect(mock_device_state).to receive(:touch_custom_certificate_installed_file)
        expect(mock_data_reporter).to receive(:report).with(hash_including("action" => "install", "status" => "success" ))
        expect(BrowserStack.logger).to receive(:info).with("[CustomCertificate] Successfully installed custom certificate on device : #{device_id} for session ID: #{session_id}")

        @custom_certificate.install_pfx_certificate
      end
    end

    context 'when installation fails' do
      before do
        allow(BrowserStack::HttpUtils).to receive(:download).and_raise(StandardError.new("Download error"))
      end

      it 'logs the error and reports failure' do
        expect(mock_data_reporter).to receive(:report).with(hash_including("action" => "install", "status" => "fail", "error" => "Download error"))

        expect { @custom_certificate.install_pfx_certificate }.to raise_error(StandardError)
      end
    end
  end

  describe '#remove_pfx_certificate' do
    context 'when removal is successful' do
      it 'removes the certificate and reports success' do
        expect(BrowserStack.logger).to receive(:info).with("[CustomCertificate] Removing custom certificate on device : #{device_id} and session ID : #{session_id}")
        expect(mock_configuration_profiles_manager).to receive(:remove_profile).with(:pfx_certificate, anything, remove_via: :mdm)
        expect(mock_device_state).to receive(:remove_custom_certificate_installed_file)
        expect(mock_data_reporter).to receive(:report).with(hash_including( "action" => "remove", "status" => "success" ))
        expect(BrowserStack.logger).to receive(:info).with("[CustomCertificate] Successfully removed custom certificate on device : #{device_id} for session ID: #{session_id}")

        @custom_certificate.remove_pfx_certificate
      end
    end

    context 'when removal fails' do
      before do
        allow(mock_configuration_profiles_manager).to receive(:remove_profile).and_raise(StandardError.new("Removal error"))
      end

      it 'logs the error and reports failure' do
        expect(mock_data_reporter).to receive(:report).with(hash_including("action" => "remove", "status" => "fail", "error" => "Removal error"))

        expect { @custom_certificate.remove_pfx_certificate }.to raise_error(StandardError)
      end
    end
  end

  describe '#install_all_custom_ca_certs' do
    let(:certificate_details) do
      [
        { filetype: "cer", filename: "cert1", media_s3_url: "https://example.com/cert1.cer" },
        { filetype: "mobileconfig", media_s3_url: "https://example.com/cert2.mobileconfig" }
      ]
    end
    let(:custom_mobileconfig) { { "PayloadContent" => [] } }
    let(:custom_mobileconfig_file_path) { "#{STATE_FILES_DIR}/app_live_custom_certificate_#{device_id}.mobileconfig" }

    before do
      allow(@custom_certificate).to receive(:load_mobileconfig).and_return(custom_mobileconfig)
      allow(File).to receive(:write)
      allow(BrowserStack::IosMdmServiceClient).to receive(:install_profile)
      allow(FileUtils).to receive(:rm)
      allow(FileUtils).to receive(:mv)
    end

    context 'when installation is successful' do
      it 'processes and installs all custom certificates' do
        allow(File).to receive(:read).with(custom_mobileconfig_file_path).and_return("")
        expect(@custom_certificate).to receive(:process_ca_certificates).with(custom_mobileconfig)
        expect(File).to receive(:write).with(custom_mobileconfig_file_path, anything)
        expect(BrowserStack::IosMdmServiceClient).to receive(:install_profile)

        @custom_certificate.install_all_custom_ca_certs
      end
    end

    context 'when an error occurs' do
      before do
        allow(@custom_certificate).to receive(:process_ca_certificates).and_raise(StandardError.new("Processing error"))
        allow(File).to receive(:exists?).with(custom_mobileconfig_file_path).and_return(true)
      end

      it 'restores the original mobileconfig file and raises an error' do
        allow(FileUtils).to receive(:mv)
        expect(FileUtils).to receive(:mv)
        expect { @custom_certificate.install_all_custom_ca_certs }.to raise_error(StandardError, "Processing error")
      end
    end
  end

  describe '#remove_all_custom_ca_certs' do
    let(:custom_mobileconfig_file_path) { "#{STATE_FILES_DIR}/app_live_custom_certificate_#{device_id}.mobileconfig" }

    before do
      allow(FileUtils).to receive(:rm)
      allow(BrowserStack::IosMdmServiceClient).to receive(:remove_profile)
    end

    it 'removes the custom certificate profile' do
      expect(BrowserStack::IosMdmServiceClient).to receive(:remove_profile).with(device_id, "custom_certificate", "com.browserstack.cert.rootca.profile")
      expect(FileUtils).to receive(:rm).with(custom_mobileconfig_file_path)

      @custom_certificate.remove_all_custom_ca_certs
    end
  end

  describe '#load_mobileconfig' do
    let(:custom_mobileconfig_file_path) { "#{STATE_FILES_DIR}/app_live_custom_certificate_#{device_id}.mobileconfig" }
    let(:tmp_mobileconfig_file_path) { "/tmp/app_live_custom_certificate_#{device_id}.mobileconfig" }

    before do
      allow(File).to receive(:read).and_return('<plist></plist>')
      allow(Plist).to receive(:parse_xml).and_return("Parsed XML")
    end

    context 'when the file already exists' do
      it 'loads the existing mobileconfig file' do
        allow(File).to receive(:exists?).with(custom_mobileconfig_file_path).and_return(true)
        expect(FileUtils).to receive(:cp).with(custom_mobileconfig_file_path, tmp_mobileconfig_file_path)
        expect(Plist).to receive(:parse_xml).with('<plist></plist>', { marshal: false })

        result = @custom_certificate.send(:load_mobileconfig, true, custom_mobileconfig_file_path, tmp_mobileconfig_file_path)
        expect(result).to eq("Parsed XML")
      end
    end

    context 'when the file does not exist' do
      it 'loads an empty mobileconfig template' do
        allow(File).to receive(:exists?).with(custom_mobileconfig_file_path).and_return(false)
        expect(Plist).to receive(:parse_xml).with(File.read("#{TEMPLATES_DIR}/mobileconfig_injection_empty_plist.plist"), { marshal: false })

        result = @custom_certificate.send(:load_mobileconfig, false, custom_mobileconfig_file_path, tmp_mobileconfig_file_path)
        expect(result).to eq("Parsed XML")
      end
    end
  end

  describe '#process_ca_certificates' do
    let(:certificate_details) do
      [
        { filetype: "cer", filename: "cert1", media_s3_url: "https://example.com/cert1.cer" },
        { filetype: "mobileconfig", media_s3_url: "https://example.com/cert2.mobileconfig" }
      ]
    end
    let(:custom_mobileconfig) { { "PayloadContent" => [] } }

    before do
      allow(@custom_certificate).to receive(:process_cer_certificate)
      allow(@custom_certificate).to receive(:process_mobileconfig_certificate)
    end

    it 'processes certificates based on their type' do
      expect(@custom_certificate).to receive(:process_cer_certificate).with(certificate_details.first, custom_mobileconfig)
      expect(@custom_certificate).to receive(:process_mobileconfig_certificate).with(certificate_details.last, custom_mobileconfig)

      @custom_certificate.send(:process_ca_certificates, custom_mobileconfig)
    end
  end
end
