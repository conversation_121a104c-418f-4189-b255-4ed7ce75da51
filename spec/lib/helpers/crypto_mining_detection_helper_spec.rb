require_relative '../../spec_helper'
require_relative '../../../lib/helpers/crypto_mining_detection_helper'

require 'fileutils'
require 'timecop'

describe CryptoMiningDetectionHelper do
  let(:coinblocker_file) { CryptoMiningDetectionHelper::COINBLOCKERLIST_FILE }
  let(:coinblocker_url) { CryptoMiningDetectionHelper::COINBLOCKERLIST_URL }

  let(:crypto_list) do
    <<~HEREDOC
      gulano.coinhive.com
      gulden.theminingpools.com
      gulf.moneroocean.stream
    HEREDOC
  end

  let(:crypto_set) { Set.new(crypto_list.split("\n")) }

  subject { CryptoMiningDetectionHelper.new }

  describe '#instrument_crypto_mining' do
    it 'returns early if no crypto domains found' do
      allow(subject).to receive(:coin_blocker_list).and_return(crypto_set)

      domain_names_set = Set['www.google.com']
      domain_timestamps = { 'www.google.com' => Set['2022-12-06 16:12:10'] }

      allow(subject).to receive(:parse_dns_logs).and_return([domain_names_set, domain_timestamps])
      expect(File).to receive(:truncate)

      expect(BrowserStack::Zombie).not_to receive(:configure)

      subject.instrument_crypto_mining
    end

    it 'pushes to zombie if crypto domain found' do
      allow(subject).to receive(:coin_blocker_list).and_return(crypto_set)

      domain_names_set = Set['gulf.moneroocean.stream']
      domain_timestamps = { 'gulf.moneroocean.stream' => Set['2022-12-06 16:12:10'] }

      allow(subject).to receive(:parse_dns_logs).and_return([domain_names_set, domain_timestamps])

      expect(File).to receive(:truncate)
      expect(BrowserStack::Zombie).to receive(:configure)

      data = {
        'data': {
          'domain' => 'gulf.moneroocean.stream',
          'access_times' => ['2022-12-06 16:12:10']
        }
      }
      zombie_args = ['crypto-mining-detected', '', data]
      expect(BrowserStack::Zombie).to receive(:push_logs).with(*zombie_args)

      subject.instrument_crypto_mining
    end
  end

  describe '#parse_dns_logs' do
    before do
      stub_const(
        'CryptoMiningDetectionHelper::DNS_LOGS',
        "#{__dir__}/../../fixtures/dns_requests.log"
      )

      now = Time.local(2022, 12, 6, 0, 0, 0)
      Timecop.freeze(now)
    end

    after { Timecop.return }

    it 'reads dns log file and parses log lines' do
      expected_domains = Set[
        'mzstatic.com.edgekey.net',
        'e673.dsce9.akamaiedge.net',
        'www.google.com',
        'time.g.aaplimg.com',
        'app-measurement.com'
      ]

      expected_timestamps = {
        'app-measurement.com' => Set['2022-12-06 16:12:12'],
        'e673.dsce9.akamaiedge.net' => Set['2022-12-06 16:12:08'],
        'mzstatic.com.edgekey.net' => Set['2022-12-06 16:12:08'],
        'time.g.aaplimg.com' => Set['2022-12-06 16:12:11'],
        'www.google.com' => Set['2022-12-06 16:12:10', '2022-12-06 16:12:12']
      }

      expect(subject.parse_dns_logs).to eq([expected_domains, expected_timestamps])
    end

    it 'returns empty set/hash if log lines failed to parse' do
      allow(subject).to receive(:parse_log_line).and_return(nil)
      expect(subject.parse_dns_logs).to eq([Set[], {}])
    end
  end

  describe '#parse_log_line' do
    before do
      now = Time.local(2022, 12, 6, 0, 0, 0)
      Timecop.freeze(now)
    end

    after { Timecop.return }

    context 'valid log line' do
      it 'correctly parses dns client request' do
        line = '18:40:43.422800 IP ***************.63370 > ***************.53: '\
               '54429+ A? www.google.com. (32)'

        expected_output = ['2022-12-06 18:40:43', ['www.google.com']]
        expect(subject.parse_log_line(line)).to eq(expected_output)
      end

      it 'correctly parses dns server response' do
        line = '18:42:12.729558 IP ***************.53 > ***************.50505: '\
               '12983 3/1/0 CNAME weather-data.apple.com.akadns.net., '\
               'CNAME a2047.dscapi9.akamai.net. (233)'

        expected_output = [
          '2022-12-06 18:42:12',
          ['weather-data.apple.com.akadns.net', 'a2047.dscapi9.akamai.net']
        ]

        expect(subject.parse_log_line(line)).to eq(expected_output)
      end

      it 'ignores IP addresses in response' do
        line = '16:13:04.774619 IP ***************.53 > ***************.50078: '\
               '58370 4/0/0 CNAME ipcdn-lb.apple.com.akadns.net., '\
               'CNAME ipcdn.g.aaplimg.com., A *************, A ************* (138)'

        expected_output = [
          '2022-12-06 16:13:04',
          ['ipcdn-lb.apple.com.akadns.net', 'ipcdn.g.aaplimg.com']
        ]

        expect(subject.parse_log_line(line)).to eq(expected_output)
      end
    end

    context 'invalid log line' do
      it { expect(subject.parse_log_line(nil)).to eq(nil) }
      it { expect(subject.parse_log_line('')).to eq(nil) }

      it do
        line = 'tcpdump: ioctl(SIOCIFCREATE): Operation not permitted'
        expect(subject.parse_log_line(line)).to eq(nil)
      end

      it do
        line = 'listening on pktap, link-type PKTAP (Apple DLT_PKTAP), capture size 262144 bytes'
        expect(subject.parse_log_line(line)).to eq(nil)
      end
    end
  end

  describe '#convert_to_timestamp' do
    context 'when valid time string' do
      before do
        now = Time.local(2022, 12, 6, 0, 0, 0)
        Timecop.freeze(now)
      end

      after { Timecop.return }

      it { expect(subject.convert_to_timestamp('16:13')).to eq('2022-12-06 16:13:00') }
      it { expect(subject.convert_to_timestamp('16:13:27')).to eq('2022-12-06 16:13:27') }
      it { expect(subject.convert_to_timestamp('16:13:27.448250')).to eq('2022-12-06 16:13:27') }
    end

    context 'when invalid time string' do
      it { expect(subject.convert_to_timestamp(nil)).to be(nil) }
      it { expect(subject.convert_to_timestamp('')).to be(nil) }
      it { expect(subject.convert_to_timestamp('asdf')).to be(nil) }
      it { expect(subject.convert_to_timestamp('123:32')).to be(nil) }
    end
  end

  describe '#valid_ip_address?' do
    context 'when ip is valid' do
      it { expect(subject.valid_ip_address?('*************')).to be(true) }
      it { expect(subject.valid_ip_address?('***************')).to be(true) }
      it { expect(subject.valid_ip_address?('*************')).to be(true) }
    end

    context 'when ip is not valid' do
      it { expect(subject.valid_ip_address?(nil)).to be(false) }
      it { expect(subject.valid_ip_address?('')).to be(false) }
      it { expect(subject.valid_ip_address?('asdf')).to be(false) }
      it { expect(subject.valid_ip_address?('123.345.789.123')).to be(false) }
      it { expect(subject.valid_ip_address?('www.google.com')).to be(false) }
    end
  end

  describe '#coin_blocker_list' do
    before { File.write(coinblocker_file, crypto_list) }

    it 'downloads list and reads file when download required' do
      allow(subject).to receive(:coin_blocker_list_download_required?).and_return(true)
      expect(subject).to receive(:download_coin_blocker_list)
      expect(subject.coin_blocker_list).to eq(crypto_set)
    end

    it 'reads file without downloading if download not required' do
      allow(subject).to receive(:coin_blocker_list_download_required?).and_return(false)
      expect(subject).not_to receive(:download_coin_blocker_list)
      expect(subject.coin_blocker_list).to eq(crypto_set)
    end
  end

  describe '#coin_blocker_list_download_required?' do
    it 'returns true if coinblocker file does not exist' do
      FileUtils.rm_f(coinblocker_file)
      expect(subject.coin_blocker_list_download_required?).to be(true)
    end

    it 'returns true if coinblocker is older than 7 days' do
      FileUtils.touch(coinblocker_file)
      Timecop.freeze(Date.today + 8)
      expect(subject.coin_blocker_list_download_required?).to be(true)
      Timecop.return
    end

    it 'returns true if coinblocker is younger than 7 days' do
      FileUtils.touch(coinblocker_file)
      expect(subject.coin_blocker_list_download_required?).to be(false)
    end
  end

  describe '#download_coin_blocker_list' do
    after { FileUtils.rm_f(coinblocker_file) }

    it 'downloads coin blocker list and writes to file' do
      stub_request(:get, coinblocker_url).to_return(status: 200, body: crypto_list)
      subject.download_coin_blocker_list
      expect(File.read(coinblocker_file)).to eq(crypto_list)
    end
  end
end
