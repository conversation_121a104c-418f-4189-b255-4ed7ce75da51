require_relative '../../spec_helper'
require_relative '../../../lib/helpers/apple_wallet'
require_relative '../../../server/device_manager'

MOCK_RAILS_SERVER = 'https://mock_rails_server'

class MockSuccessResponse
  def code
    "200"
  end
end

class MockStaticConf
  def device_rails_endpoint(device_id)
    MOCK_RAILS_SERVER
  end
end

class MockConfiguration
  def all
    {
      'static_conf' => MockStaticConf.new
    }
  end
end

describe Secure::AppleWallet do
  let(:device_config) { { "device_version" => '15.4', "region" => 'us-east-1', 'webdriver_port' => 8080 } }
  let(:uuid) { '000820-427503425' }
  let(:product) { 'app-live' }
  let(:session_id) { '1' }
  let(:ios_version) { '15.4' }
  let(:event_name) { 'web_events' }
  let(:sandbox_cards) { { "0": [["****************", "12 - December", "2024", "111"]], "1": [["****************", "12 - December", "2024", "111"]], "2": [["****************", "12 - December", "2024", "111"]], "3": [["****************", "12 - December", "2024", "111"]], "4": [["****************", "12 - December", "2024", "111"]] } }
  let(:automation_success_data) { { "value" => { "status" => "pass", "message" => "successfully added card to wallet", "debugDescription" => "" } } }
  let(:automation_failure_data) { { "value" => { "status" => "fail", "message" => "Failed to tap on '/Done/' StaticText", "debugDescription" => "Attributes: Application, pid: 9770, label: 'Wallet'\nElement subtree:\n →Application, 0x283538620, pid: 9770, label: 'Wallet'\n    Window (Main), 0x2835380e0, ..." } } }

  let(:provider) { 'mastercard' }
  let(:number) { '0000000000000000' }
  let(:date) { '11 - November' }
  let(:year) { '2024' }
  let(:cvv) { '111' }

  before do
    allow(BrowserStack::Configuration).to receive(:new).and_return(MockConfiguration.new)
    allow(DeviceManager).to receive(:device_configuration_check).and_return(device_config)
    stub_const("APPLE_PAY_PAYMENT_NETWORKS", ["0", "1", "2", "3", "4"])
    stub_const('SANDBOX_CARDS', sandbox_cards)
    allow(BrowserStack::Zombie).to receive(:configure)
    @obj = Secure::AppleWallet.new(uuid, session_id, product)
  end

  describe "add_sandbox_card" do
    it "should successfully add card to wallet in one go" do
      expect_any_instance_of(WdaClient).to receive(:add_card).and_return(automation_success_data)
      result = @obj.add_sandbox_card
      expect(result).to eq(true)
    end
    it "should successfully fail to add card to wallet" do
      expect_any_instance_of(WdaClient).to receive(:add_card).exactly(3).times.and_return(automation_failure_data)
      result = @obj.add_sandbox_card
      expect(result).to eq(false)
    end
    it "should successfully fail to add card in first few attempts but pass eventually" do
      expect_any_instance_of(WdaClient).to receive(:add_card).exactly(2).times.and_return(automation_failure_data)
      expect_any_instance_of(WdaClient).to receive(:add_card).and_return(automation_success_data)
      result = @obj.add_sandbox_card
      expect(result).to eq(true)
    end
    it "should raise curl exception" do
      expect_any_instance_of(WdaClient).to receive(:add_card).exactly(3).times do
        raise WdaClientError, "curl-error"
      end
      expect_any_instance_of(WdaClient).to receive(:running?).exactly(3).times.and_return(false)
      result = @obj.add_sandbox_card
      expect(result).to eq(false)
    end
    it "should raise parse exception" do
      expect_any_instance_of(WdaClient).to receive(:add_card).exactly(3).times do
        raise WdaClientError, "parse-error"
      end
      expect_any_instance_of(WdaClient).to receive(:running?).exactly(3).times.and_return(false)
      result = @obj.add_sandbox_card
      expect(result).to eq(false)
    end
    it "should raise standard exception" do
      expect_any_instance_of(WdaClient).to receive(:add_card).exactly(3).times do
        raise(StandardError)
      end
      result = @obj.add_sandbox_card
      expect(result).to eq(false)
    end

    it "should report success to rails" do
      rails_endpoint = "#{MOCK_RAILS_SERVER}/admin/update_card_stability"

      req_body = {
        "cards" => [
          {
            number: number,
            provider: provider,
            status: true
          }
        ]
      }

      expect(BrowserStack::HttpUtils).to receive(:send_post).with(
        rails_endpoint, req_body
      ).and_return(MockSuccessResponse.new)

      expect_any_instance_of(WdaClient).to receive(:add_card).and_return(automation_success_data)

      @obj.add_sandbox_card(
        cards = [
          {
            'provider' => provider,
            'number' => number,
            'date' => date,
            'year' => year,
            'cvv' => cvv
          },
          {
            'provider' => provider,
            'number' => number,
            'date' => date,
            'year' => year,
            'cvv' => cvv
          },
          {
            'provider' => provider,
            'number' => number,
            'date' => date,
            'year' => year,
            'cvv' => cvv
          }
        ]
      )
    end

    it "should report all success and failure to rails" do
      rails_endpoint = "#{MOCK_RAILS_SERVER}/admin/update_card_stability"

      req_body = {
        "cards" => [
          {
            number: number,
            provider: provider,
            status: false
          },
          {
            number: number,
            provider: provider,
            status: false
          },
          {
            number: number,
            provider: provider,
            status: true
          }
        ]
      }

      expect_any_instance_of(WdaClient).to receive(:add_card).exactly(2).times.and_return(automation_failure_data)
      expect_any_instance_of(WdaClient).to receive(:add_card).and_return(automation_success_data)

      expect(BrowserStack::HttpUtils).to receive(:send_post).with(
        rails_endpoint, req_body
      ).and_return(MockSuccessResponse.new)

      @obj.add_sandbox_card(
        cards = [
          {
            'provider' => provider,
            'number' => number,
            'date' => date,
            'year' => year,
            'cvv' => cvv
          },
          {
            'provider' => provider,
            'number' => number,
            'date' => date,
            'year' => year,
            'cvv' => cvv
          },
          {
            'provider' => provider,
            'number' => number,
            'date' => date,
            'year' => year,
            'cvv' => cvv
          }
        ]
      )
    end
  end
end

