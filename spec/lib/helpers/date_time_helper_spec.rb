require 'timecop'
require_relative '../../spec_helper'
require_relative '../../../lib/helpers/device_sim_helper'
require_relative '../../../lib/utils/idevice_utils'

describe DateTimeHelper do
  let(:udid) { '000820-427503425' }
  let(:session_id) { '1' }
  let(:product) { 'app-live' }
  let(:mock_device_state) { double("DeviceState").as_null_object }
  let(:mock_wda_client) { instance_double("WdaClient") }
  let(:wda_success_response) { { "value" => { "error" => "no error" } } }
  let(:wda_failure_response) { { "value" => { "error" => "some error" } } }
  let(:xcui_success_data) { { 'result' => "success", 'time_taken' => 2 } }
  let(:xcui_failure_data) { { 'result' => "failed", 'time_taken' => 2, "error" => "some_xcui_reason", "error_message" => "some xcui message" } }

  before do
    allow(DeviceManager).to receive(:device_configuration_check).and_return({ "device_version" => '14.0' })
    expect(DeviceState).to receive(:new).and_return(mock_device_state)
    expect(WdaClient).to receive(:new).and_return(mock_wda_client)

    @testObject = DateTimeHelper.new(udid, { session_id: session_id, product: product })
  end

  context "#automatic_via_mdm" do
    let(:mock_configuration_profiles_manager) { double("ConfigurationProfilesManager") }

    before do
      allow(ConfigurationProfilesManager).to receive(:new).and_return(mock_configuration_profiles_manager)
      allow(mock_device_state).to receive(:passcode_file_present?).and_return(false)
      allow(CustomMDMManager).to receive(:is_custom_mdm_device?).and_return(false)
    end
    it "should not raise any exception" do
      expect(mock_configuration_profiles_manager).to receive(:install_profile).exactly(2).times
      expect(BrowserStack::IosMdmServiceClient).to receive(:lock_device)
      expect(mock_wda_client).to receive(:unlock_device)
      expect(mock_device_state).to receive(:touch_force_automatic_date_time_file)
      expect(mock_device_state).to receive(:remove_force_automatic_date_time_file)
      expect { @testObject.automatic_via_mdm }.to_not raise_error
    end

    it "should raise passocde_enabled exception if passcode is set" do
      expect(mock_device_state).to receive(:passcode_file_present?).and_return(true)
      expect { @testObject.automatic_via_mdm }.to raise_error("passcode_enabled")
    end

    it "should raise MDMProfileException if MDM fails" do
      expect(mock_configuration_profiles_manager).to receive(:install_profile).and_raise(MdmProfileException, "Some reason")
      expect(mock_device_state).to receive(:touch_force_automatic_date_time_file)
      expect(mock_device_state).to receive(:remove_force_automatic_date_time_file)
      expect { @testObject.automatic_via_mdm }.to raise_error
    end
  end

  context "#automatic" do
    before do
      allow(mock_device_state).to receive(:touch_settings_automation_executing_file)
      allow(mock_device_state).to receive(:remove_settings_automation_executing_file)
    end

    context "success cases: should return true" do
      it "when requested for true and automatic is true" do
        expect(mock_device_state).to receive(:date_time_automatic_file_present?).and_return(true)
        expect(@testObject.automatic(true)).to eq(true)
      end

      it "when requested for false and automatic is false" do
        expect(mock_device_state).to receive(:date_time_automatic_file_present?).and_return(false)
        expect(@testObject.automatic(false)).to eq(true)
      end

      it "when requested for true automatic is false and successfully set via MDM" do
        expect(@testObject).to receive(:automatic_via_mdm)
        expect(mock_device_state).to receive(:date_time_automatic_file_present?).and_return(false)
        expect(mock_device_state).to receive(:touch_date_time_automatic_file).and_return(true)
        expect_any_instance_of(DataReportHelper).to receive(:report_settings_data)
        expect(@testObject.automatic(true)).to eq(true)
      end

      it "when requested for true automatic is false and successfully set via WDA but MDM failed" do
        expect(@testObject).to receive(:automatic_via_mdm).and_raise(MdmProfileException, "some error")
        expect(mock_wda_client).to receive(:set_date_time_automatic).with(true).and_return(wda_success_response)
        expect(mock_device_state).to receive(:date_time_automatic_file_present?).and_return(false)
        expect(mock_device_state).to receive(:touch_date_time_automatic_file)
        expect_any_instance_of(DataReportHelper).to receive(:report_settings_data)
        expect(@testObject.automatic(true)).to eq(true)
      end

      it "when requested for false automatic is true and successfully set via WDA" do
        expect(mock_device_state).to receive(:date_time_automatic_file_present?).and_return(true)
        expect(mock_wda_client).to receive(:set_date_time_automatic).with(false).and_return(wda_success_response)
        expect(mock_device_state).to receive(:remove_date_time_automatic_file)
        expect_any_instance_of(DataReportHelper).to receive(:report_settings_data)
        expect(@testObject.automatic(false)).to eq(true)
      end
    end

    context "failure cases: should return false" do
      it "when requested for false and WDA failed" do
        expect(mock_device_state).to receive(:date_time_automatic_file_present?).and_return(true)
        expect(mock_wda_client).to receive(:set_date_time_automatic).with(false).and_return(wda_failure_response)
        expect_any_instance_of(DataReportHelper).to receive(:report_settings_data)
        expect(mock_device_state).to receive(:touch_date_time_automatic_file)
        expect(@testObject.automatic(false)).to eq(false)
      end

      it "when requested for true and both MDM and WDA failed" do
        expect(@testObject).to receive(:automatic_via_mdm).and_raise(MdmProfileException, "some error")
        expect(mock_device_state).to receive(:date_time_automatic_file_present?).and_return(false)
        expect(mock_wda_client).to receive(:set_date_time_automatic).with(true).and_return(wda_failure_response)
        expect_any_instance_of(DataReportHelper).to receive(:report_settings_data)
        expect(mock_device_state).to receive(:remove_date_time_automatic_file)
        expect(@testObject.automatic(true)).to eq(false)
      end
    end
  end

  context "#time" do
    before do
      allow(mock_device_state).to receive(:touch_settings_automation_executing_file)
      allow(mock_device_state).to receive(:remove_settings_automation_executing_file)
    end

    let(:custom_time) { "12:35" }
    it "should return true when time set successfully" do
      expect(mock_wda_client).to receive(:set_device_time).with(custom_time).and_return(wda_success_response)
      expect(mock_device_state).to receive(:touch_custom_time_file)
      expect(mock_device_state).to receive(:remove_date_time_automatic_file)
      expect_any_instance_of(DataReportHelper).to receive(:report_settings_data)
      expect(@testObject.time(custom_time)).to eq(true)
    end

    it "should return false when time setting failed" do
      expect(mock_wda_client).to receive(:set_device_time).with(custom_time).and_return(wda_failure_response)
      expect_any_instance_of(DataReportHelper).to receive(:report_settings_data)
      expect(@testObject.time(custom_time)).to eq(false)
    end
  end

  describe '#change_date' do
    context 'when a valid new date is provided' do
      let(:new_date) { '2023-09-10' }

      before do
        allow(@testObject).to receive(:validate_date).and_return(true)
        allow(@testObject).to receive(:call_wda_method_with_args).and_return({ 'result' => 'success' })
        allow(IdeviceUtils).to receive(:idevicepair).and_return('SUCCESS')
        allow(mock_wda_client).to receive(:foreground).and_return("com.sample.app")
        allow(mock_wda_client).to receive(:set_foreground).and_return(true)
        allow(mock_device_state).to receive(:remove_date_time_automatic_file).and_return(true)
        allow(mock_device_state).to receive(:touch_custom_date_file).and_return(true)
        allow(mock_device_state).to receive(:read_custom_date_file).and_return(true)
        allow(mock_device_state).to receive(:write_custom_date_file).and_return(true)
        allow(DataReportHelper).to receive(:report_settings_data).and_return(true)
      end

      it 'should change the date successfully' do
        expect(@testObject.change_date(new_date)).to eq([true, nil])
      end

      it 'should send a success message to pusher' do
        expect(@testObject).to receive(:push_to_pusher).with('SetExactDateSuccess')
        @testObject.change_date(new_date, send_to_pusher: true)
      end
    end

    context 'when an invalid new date is provided' do
      let(:new_date) { 'invalid_date' }

      before do
        allow(@testObject).to receive(:validate_date).and_return(false)
        allow(DataReportHelper).to receive(:report_settings_data).and_return(true)
        allow(mock_device_state).to receive(:remove_date_time_automatic_file).and_return(true)
        allow(mock_wda_client).to receive(:foreground).and_return("com.sample.app")
        allow(mock_wda_client).to receive(:set_foreground).and_return(true)
      end

      it 'should not change the date and return false' do
        expect(@testObject.change_date(new_date)).to eq([false, "CD_0001"])
      end

      it 'should send a failure message to pusher' do
        expect(@testObject).to receive(:validate_date).and_return(false)
        expect(@testObject).to receive(:push_to_pusher).with('SetExactDateFailure')
        @testObject.change_date(new_date, send_to_pusher: true)
      end
    end
  end

  context "#cleanup" do
    it "should not raise any error when success" do
      expect_any_instance_of(XCUITestHelper).to receive(:run_test).with(:set_time_to_utc).and_return(xcui_success_data)
      expect(mock_device_state).to receive(:remove_date_time_automatic_file)
      expect(mock_device_state).to receive(:remove_custom_time_file)
      expect(mock_device_state).to receive(:remove_custom_date_file)
      expect { @testObject.cleanup }.to_not raise_error
    end

    it "should raise exception when failed" do
      expect_any_instance_of(XCUITestHelper).to receive(:run_test).with(:set_time_to_utc).and_return(xcui_failure_data)
      expect_any_instance_of(DataReportHelper).to receive(:report)
      expect { @testObject.cleanup }.to raise_error(DateTimeException)
    end
  end
end
