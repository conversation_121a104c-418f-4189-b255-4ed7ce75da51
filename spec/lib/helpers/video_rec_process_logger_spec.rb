require_relative '../../spec_helper'
require_relative '../../../lib/helpers/video_rec_process_logger'

describe VideoRecProcessLogger do
  let(:device) { 'test_device' }
  let(:device_state) { double('device_state') }
  let(:configuration) {  double('configuration') }
  let(:server_config) { { 'idevicesyslog_bin_path' => '/usr/bin/idevicesyslog' } }
  before do
    allow(DeviceState).to receive(:new).and_return(device_state)
    allow(Configuration).to receive(:new).and_return(configuration)
    allow(configuration).to receive(:all).and_return(server_config)
  end

  describe "#start" do
    it "should start new custom idevice syslog logger and store pid" do
      expect(IdeviceUtils).to receive(:idevicesyslog_bin_path).and_return("/usr/bin/idevicesyslog")
      expect(Process).to receive(:spawn).with("/usr/bin/idevicesyslog -u abcd  -p Launcher -p live_streaming -p WebDriverAgentRunner-Runner >> /var/log/browserstack/video_rec_process_abcd.log", { pgroup: true }).and_return(9999)
      expect(Process).to receive(:detach).with(9999)
      expect(device_state).to receive(:write_to_video_logger_pid_file).with(9999)
      video_rec_process_logger = VideoRecProcessLogger.new("abcd", "1234")
      video_rec_process_logger.start
    end
  end

  describe "#stop" do
    it "should kill custom idevice sysloger" do
      expect(device_state).to receive(:video_logger_pid_file_present?).and_return(true)
      expect(device_state).to receive(:read_video_logger_pid_file).and_return("9999")
      expect(device_state).to receive(:remove_video_logger_pid_file).and_return("9999")
      expect(Process).to receive(:getpgid).with(9999).and_return(1111)
      expect(OSUtils).to receive(:kill_pid).with(-1111)
      video_rec_process_logger = VideoRecProcessLogger.new("abcd", "1234")
      video_rec_process_logger.stop
    end
  end

  describe "#check_and_start" do
    it "should kill stale idevice sysloger and start new process" do
      expect(device_state).to receive(:video_logger_pid_file_present?).and_return(true)
      expect(device_state).to receive(:read_video_logger_pid_file).and_return("9999")
      expect(device_state).to receive(:remove_video_logger_pid_file).and_return("9999")
      expect(Process).to receive(:getpgid).with(9999).and_return(1111)
      expect(OSUtils).to receive(:kill_pid).with(-1111)
      video_rec_process_logger = VideoRecProcessLogger.new("abcd", "1234")
      expect(video_rec_process_logger).to receive(:start)
      video_rec_process_logger.check_and_start
    end
  end
end
