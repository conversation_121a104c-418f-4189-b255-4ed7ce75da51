require_relative '../../spec_helper'
require_relative '../../../lib/helpers/sandbox_account'
require_relative '../../../server/device_manager'

describe Secure::SandboxAccount do
  let(:device_config) { { "device_version" => '15.4', "region" => 'us-east-1', 'webdriver_port' => 8080 } }
  let(:device_id) { '123' }

  before do
    allow(DeviceManager).to receive(:device_configuration_check).and_return(device_config)
    allow(BrowserStack::Zombie).to receive(:configure)
    @obj = Secure::SandboxAccount.new(device_id)
  end

  describe '#add_sandbox_account' do
    context 'when everything goes well' do
      it 'successfully adds a sandbox account' do
        expect_any_instance_of(Secure::SandboxAccount).to receive(:setup_environment)
        expect_any_instance_of(Secure::SandboxAccount).to receive(:find_sandbox_account).and_return('sandbox_account_name')

        wda_client_double = instance_double('WdaClient')
        allow(WdaClient).to receive(:new).and_return(wda_client_double)
        expect(wda_client_double).to receive(:sign_in).with('sandbox_account_name').and_return('value' => { 'status' => 'pass' })

        expect_any_instance_of(Secure::SandboxAccount).to receive(:handle_response)

        expect_any_instance_of(Secure::SandboxAccount).to receive(:reset_environment)

        @obj.add_sandbox_account
      end
    end

    context 'when WdaAutomationError or WdaClientError is raised' do
      it 'handles the error and returns false' do
        expect_any_instance_of(Secure::SandboxAccount).to receive(:setup_environment)
        expect_any_instance_of(Secure::SandboxAccount).to receive(:find_sandbox_account).and_return('sandbox_account_name')
        allow(WdaClient).to receive(:new).and_raise(WdaAutomationError.new('Test error'))

        expect_any_instance_of(Secure::SandboxAccount).to receive(:handle_error)

        expect_any_instance_of(Secure::SandboxAccount).to receive(:reset_environment)

        expect(@obj.add_sandbox_account).to be_falsey
      end
    end
  end
end
