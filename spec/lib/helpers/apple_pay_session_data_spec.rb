# frozen_string_literal: true

require_relative '../../spec_helper'
require_relative '../../../lib/helpers/apple_pay_session_data'
require_relative '../../../server/device_manager'

describe Secure::ApplePaySessionData do
  let(:device_config) { { "device_version" => '15.4', "region" => 'us-east-1', 'webdriver_port' => 8080 } }
  let(:product) { 'app-live' }
  let(:session_id) { '1' }
  let(:udid) { '000820-427503425' }
  let(:ios_version) { '15.4' }
  let(:event_name) { 'web_events' }
  let(:mock_wda_client) { double(WdaClient) }
  let(:mock_device_state) { double(DeviceState) }
  let(:mock_xcui_test_helper) { double(XCUITestHelper) }
  let(:mock_data_report_helper) { double(DataReportHelper) }
  let(:mock_passcode_helper) { double(Secure::Passcode).as_null_object }
  let(:mock_apple_wallet) { double(Secure::AppleWallet).as_null_object }

  before do
    @obj = Secure::ApplePaySessionData.new(product, session_id, udid)
    allow(DataReportHelper).to receive(:new).and_return(mock_data_report_helper)
    allow(WdaClient).to receive(:new).and_return(mock_wda_client)
    allow(DeviceState).to receive(:new).and_return(mock_device_state)
    allow(Secure::Passcode).to receive(:new).and_return(mock_passcode_helper)
    allow(Secure::AppleWallet).to receive(:new).and_return(mock_apple_wallet)
  end

  describe '#run_and_report_cleanup' do
    context 'with argument :appstore' do
      it 'calls #cleanup with argument :appstore' do
        allow(@obj).to receive(:cleanup).and_return({ "status" => "pass" })
        expect(mock_data_report_helper).to receive(:report).and_return(true)
        @obj.run_and_report_cleanup("appstore")
      end
    end

    context 'with argument :settings' do
      it 'calls #cleanup with argument :settings' do
        allow(@obj).to receive(:cleanup).and_return({ "status" => "pass" })
        expect(mock_data_report_helper).to receive(:report).and_return(true)
        @obj.run_and_report_cleanup("settings")
      end
    end
  end

  describe '#cleanup' do
    context 'with argument :appstore' do
      it 'calls cleanup_using_wda_client which fails then calls XCTest fallback ' do
        allow(DeviceManager).to receive(:device_configuration_check).and_return(device_config)
        allow(DataReportHelper).to receive(:generate_wda_xcui_fallback_reporting_data).and_return({
          "status" => "pass",
          "wda_cleanup_status" => false,
          "wda_response_data" => {},
          "ios_njb_cleanup_status" => true,
          "ios_njb_response_data" => {
            "result" => "success",
            "time_taken" => "15",
            "error" => nil,
            "error_message" => nil
          }
        })
        expect_any_instance_of(Secure::ApplePaySessionData).to receive(:cleanup_using_wda_client).and_return([false, "curl-error"])
        expect_any_instance_of(Secure::ApplePaySessionData).to receive(:cleanup_using_ios_njb_client).and_return([true, {
          "result" => "success",
          "time_taken" => "15",
          "error" => nil,
          "error_message" => nil
        }])

        @obj.cleanup("appstore")
      end

      it 'calls cleanup_using_wda_client, receive success response ' do
        allow(DeviceManager).to receive(:device_configuration_check).and_return(device_config)
        allow(DataReportHelper).to receive(:generate_wda_xcui_fallback_reporting_data).and_return({
          "status" => "pass",
          "wda_cleanup_status" => true,
          "wda_response_data" => { "value" => { "status" => "pass", "message" => "successfully signed out of appstore", "debugDescription" => "" } },
          "ios_njb_cleanup_status" => "",
          "ios_njb_response_data" => ""
        })
        expect(@obj).to receive(:cleanup_using_wda_client).and_return({ "value" => { "status" => "pass", "message" => "successfully signed out of appstore", "debugDescription" => "" } })

        @obj.cleanup("appstore")
      end
    end

    context 'with argument :settings' do
      it 'calls cleanup_using_wda_client which fails, then calls XCTest fallback ' do
        allow(DeviceManager).to receive(:device_configuration_check).and_return(device_config)
        expect(mock_device_state).to receive(:apple_pay_data_file_present?).and_return(true)
        expect(@obj).to receive(:cleanup_using_wda_client).and_return([false, "curl-error"])
        expect(@obj).to receive(:cleanup_using_ios_njb_client).and_return([true, {
          "result" => "success",
          "time_taken" => "15",
          "error" => nil,
          "error_message" => nil
        }])
        @obj.cleanup("settings")
      end

      it 'calls cleanup_using_wda_client, receive success response ' do
        allow(DeviceManager).to receive(:device_configuration_check).and_return(device_config)
        expect(mock_device_state).to receive(:apple_pay_data_file_present?).and_return(true)
        expect(@obj).to receive(:cleanup_using_wda_client).and_return({ "value" => { "status" => "pass", "message" => "successfully signed out of appstore", "debugDescription" => "" } })
        @obj.cleanup("settings")
      end

      it 'calls cleanup_using_wda_client, receive success response ' do
        allow(DeviceManager).to receive(:device_configuration_check).and_return(device_config)
        expect(mock_device_state).to receive(:apple_pay_data_file_present?).and_return(true)
        expect(@obj).to receive(:cleanup_using_wda_client).and_return({ "value" => { "status" => "pass", "message" => "successfully signed out of appstore", "debugDescription" => "" } })
        @obj.cleanup("settings")
      end
    end
  end

  describe '#cleanup_using_wda_client' do
    context 'with argument :settings' do
      it 'triggers wda cleanup endpoint for apple pay settings, receive success response ' do
        expect(mock_device_state).to receive(:apple_pay_data_file_present?).exactly(1).times.and_return(true)
        expect(mock_wda_client).to receive(:clear_transaction_defaults).and_return(true)
        expect(@obj).to receive(:response_handler).and_return(true)
        @obj.cleanup_using_wda_client("settings", mock_device_state, mock_wda_client)
      end
    end

    context 'with argument :appstore' do
      it 'triggers wda cleanup endpoint for appstore, receive success response ' do
        expect(mock_wda_client).to receive(:reset_logins).and_return(true)
        expect(@obj).to receive(:response_handler).and_return(true)
        @obj.cleanup_using_wda_client("appstore", mock_device_state, mock_wda_client)
      end
    end
  end

  describe '#response_handler' do
    let(:success_response)  { { "value" => { "status" => "pass", "message" => "successfully signed out of appstore", "debugDescription" => "" } } }
    let(:failure_response)  { { "value" => { "status" => "fail", "message" => "failed to sign out of appstore", "debugDescription" => "some long text" } } }

    context 'with argument :settings' do
      it 'should generate success response' do
        expect(@obj.response_handler("settings", mock_device_state, success_response)).to eq([true, success_response["value"]])
      end
    end

    context 'with argument :appstore' do
      it 'should generate success response' do
        expect(@obj.response_handler("appstore", mock_device_state, success_response)).to eq([true, success_response["value"]])
      end
    end
  end

  describe '#cleanup_using_ios_njb_client' do
    before do
      allow(mock_device_state).to receive(:custom_mdm_file_present?)
      allow(mock_device_state).to receive(:dedicated_device_file_present?)
    end

    context 'with argument :settings' do
      it 'should generate success response' do
        allow(BrowserStackAppHelper).to receive(:build_and_install_browserstack_app).and_return(true)
        allow(XCUITestHelper).to receive(:new).and_return(mock_xcui_test_helper)
        expect(mock_wda_client).to receive(:running?).exactly(1).times.and_return(true)
        expect(mock_wda_client).to receive(:settings_access).exactly(2).times.and_return({
          'value' => { 'global_settings_access_enabled' => 'true' }
        })
        expect_any_instance_of(Secure::ApplePaySessionData).to receive(:run_xcui_test).and_return([true, {
          "result" => "success",
          "time_taken" => "5",
          "error" => nil,
          "error_message" => nil
        }])
        expect(@obj.cleanup_using_ios_njb_client("settings", mock_device_state, mock_wda_client)).to eq([true, {
          "result" => "success",
          "time_taken" => "5",
          "error" => nil,
          "error_message" => nil
        }])
      end

      it 'should generate failure response' do
        allow(XCUITestHelper).to receive(:new).and_return(mock_xcui_test_helper)
        expect(mock_wda_client).to receive(:running?).exactly(1 ).times.and_return(true)
        expect(mock_wda_client).to receive(:settings_access).exactly(2).times.and_return({
          'value' => { 'global_settings_access_enabled' => 'true' }
        })
        expect_any_instance_of(Secure::ApplePaySessionData).to receive(:run_xcui_test).and_return([false, {
          "result" => "failed",
          "time_taken" => "5",
          "error" => "some error",
          "error_message" => "some message"
        }])
        expect(@obj.cleanup_using_ios_njb_client("settings", mock_device_state, mock_wda_client)).to eq([false, {
          "result" => "failed",
          "time_taken" => "5",
          "error" => "some error",
          "error_message" => "some message"
        }])
      end
    end

    context 'with argument :appstore' do
      it 'should generate success response' do
        allow(XCUITestHelper).to receive(:new).and_return(mock_xcui_test_helper)
        expect(mock_wda_client).to receive(:running?).exactly(1).times.and_return(true)
        expect(mock_wda_client).to receive(:settings_access).exactly(2).times.and_return({
          'value' => { 'global_settings_access_enabled' => 'true' }
        })
        expect_any_instance_of(Secure::ApplePaySessionData).to receive(:run_xcui_test).and_return([true, {
          "result" => "success",
          "time_taken" => "5",
          "error" => nil,
          "error_message" => nil
        }])
        expect(@obj.cleanup_using_ios_njb_client("appstore", mock_device_state, mock_wda_client)).to eq([true, {
          "result" => "success",
          "time_taken" => "5",
          "error" => nil,
          "error_message" => nil
        }])
      end

      it 'should generate failure response' do
        allow(XCUITestHelper).to receive(:new).and_return(mock_xcui_test_helper)
        expect(mock_wda_client).to receive(:running?).exactly(1).times.and_return(true)
        expect(mock_wda_client).to receive(:settings_access).exactly(2).times.and_return({
          'value' => { 'global_settings_access_enabled' => 'true' }
        })
        expect_any_instance_of(Secure::ApplePaySessionData).to receive(:run_xcui_test).and_return([false, {
          "result" => "failed",
          "time_taken" => "5",
          "error" => "some error",
          "error_message" => "some message"
        }])
        expect(@obj.cleanup_using_ios_njb_client("appstore", mock_device_state, mock_wda_client)).to eq([false, {
          "result" => "failed",
          "time_taken" => "5",
          "error" => "some error",
          "error_message" => "some message"
        }])
      end
    end
  end
end
