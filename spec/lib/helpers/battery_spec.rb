require_relative '../../spec_helper'
require_relative '../../../lib/helpers/battery'
require_relative '../../../server/device_manager'

describe Battery do
  let(:device_config) { { "device_version" => '15.4', "region" => 'us-east-1', 'webdriver_port' => 8080 } }
  let(:uuid) { '000820-427503425' }
  let(:product) { 'app-live' }
  let(:session_id) { '1' }
  let(:ios_version) { '15.4' }
  let(:event_name) { 'web_events' }
  let(:automation_success_data) { { "value" => { "status" => "pass", "message" => "successfully added card to wallet", "debugDescription" => "" } } }
  let(:automation_failure_data) { { "value" => { "status" => "fail", "message" => "Failed to tap on '/Done/' StaticText", "debugDescription" => "Attributes: Application, pid: 9770, label: 'Wallet'\nElement subtree:\n →Application, 0x283538620, pid: 9770, label: 'Wallet'\n    Window (Main), 0x2835380e0, ..." } } }

  before do
    allow(DeviceManager).to receive(:device_configuration_check).and_return(device_config)
    allow(BrowserStack::Zombie).to receive(:configure)
    @testObject = Battery.new(uuid, session_id, product)
  end

  describe "toggle low power mode" do
    it "should successfully toggle low power mode to On/Off" do
      expect_any_instance_of(WdaClient).to receive(:low_power_mode).and_return(automation_success_data)
      result = @testObject.change_low_power_mode
      expect(result).to eq(true)
    end
    it "should fail to toggle low power mode to On/Off" do
      expect_any_instance_of(WdaClient).to receive(:low_power_mode).and_return(automation_failure_data)
      result = @testObject.change_low_power_mode
      expect(result).to eq(false)
    end
    it "should raise curl exception" do
      wda_client = double
      expect(WdaClient).to receive(:new).and_return(wda_client)
      expect(wda_client).to receive(:low_power_mode) do
        raise WdaClientError, "curl-error"
      end
      expect(wda_client).to receive(:running?).and_return(false)
      result = @testObject.change_low_power_mode
      expect(result).to eq(false)
    end
    it "should raise parse exception" do
      wda_client = double
      expect(WdaClient).to receive(:new).and_return(wda_client)
      expect(wda_client).to receive(:low_power_mode) do
        raise WdaClientError, "parse-error"
      end
      expect(wda_client).to receive(:running?).and_return(false)
      result = @testObject.change_low_power_mode
      expect(result).to eq(false)
    end
    it "should raise standard exception" do
      wda_client = double
      expect(WdaClient).to receive(:new).and_return(wda_client)
      expect(wda_client).to receive(:low_power_mode) do
        raise(StandardError)
      end
      result = @testObject.change_low_power_mode
      expect(result).to eq(false)
    end
  end
end
