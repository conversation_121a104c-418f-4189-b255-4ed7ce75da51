require_relative '../../spec_helper'
require_relative '../../../lib/helpers/accessibility_settings_helper'

describe Secure::AccessibilitySettingsHelper do
  let(:uuid) { '000820-427503425' }
  let(:product) { 'live' }
  let(:session_id) { '1' }
  let(:mock_ios_device) { double(BrowserStack::IosDevice) }
  let(:mock_accessibility_settings) { double(AccessibilitySettingsUtil) }
  let(:subject) { Secure::AccessibilitySettingsHelper.new(uuid, session_id, product) }
  let(:enable_reduce_motion) { "{ \"reduce_motion\": \"enable\" }" }
  let(:disable_reduce_motion) { "{ \"reduce_motion\": \"disable\" }" }
  let(:enable_increase_contrast) { "{ \"increase_contrast\": \"enable\" }" }
  let(:disable_increase_contrast) { "{ \"increase_contrast\": \"disable\" }" }
  let(:increase_speaking_rate) { "{ \"speaking_rate\": 100 }" }
  let(:default_speaking_rate) { "{ \"speaking_rate\": 50 }" }
  let(:decrease_speaking_rate) { "{ \"speaking_rate\": 0 }" }
  let(:voice_over_settings) do
    {
      speaking_rate: 0,
      navigation_style: "grouped",
      captions_panel: "enable"
    }
  end
  let(:display_and_textsize_settings) do
    {
      increase_contrast: "disable",
      larger_accessibility_sizes: "disable",
      text_size: 4
    }
  end
  let(:device_config) { { "device_version" => '15.0', "region" => 'us-east-1', 'webdriver_port' => 8080 } }
  let(:device_state) { DeviceState.new(uuid) }

  before do
    allow(DeviceManager).to receive(:device_configuration_check).and_return(device_config)
    allow(BrowserStack::IosDevice).to receive(:new).and_return(mock_ios_device)
    allow(DeviceState).to receive(:new).with(uuid).and_return(device_state)
  end

  before :each do
    expect(mock_ios_device).to receive(:accessibility_settings).and_return(mock_accessibility_settings)
  end

  after :each do
    device_state.remove_accessibility_settings_file
  end

  describe 'Accessibility Settings' do
    context 'Motion Settings' do
      it 'should enable reduce_motion' do
        expect(mock_accessibility_settings).to receive(:enable).with('ReduceMotionEnabled').and_return(true)
        expect(subject.send(:toggle_accessibility_setting, 'ReduceMotionEnabled', 'enable')).to eql(true)
      end

      it 'should disable reduce_motion' do
        expect(mock_accessibility_settings).to receive(:disable).with('ReduceMotionEnabled').and_return(true)
        expect(subject.send(:toggle_accessibility_setting, 'ReduceMotionEnabled', 'disable')).to eql(true)
      end
    end

    context 'Display and TextSize Settings' do
      it 'should enable increase_contrast' do
        expect(mock_accessibility_settings).to receive(:enable).with('DarkenSystemColors').and_return(true)
        expect(subject.send(:toggle_accessibility_setting, 'DarkenSystemColors', 'enable')).to eql(true)
      end

      it 'should disable increase_contrast' do
        expect(mock_accessibility_settings).to receive(:disable).with('DarkenSystemColors').and_return(true)
        expect(subject.send(:toggle_accessibility_setting, 'DarkenSystemColors', 'disable')).to eql(true)
      end
    end

    context 'VoiceOver Settings' do
      it 'should change speaking_rate to 1' do
        expect(mock_accessibility_settings).to receive(:update).with('VoiceOverTouchSpeakingRate', 1).and_return(true)
        expect(subject.send(:update_accessibility_setting, 'VoiceOverTouchSpeakingRate', 1)).to eql(true)
      end

      it 'should change speaking_rate to 0' do
        expect(mock_accessibility_settings).to receive(:update).with('VoiceOverTouchSpeakingRate', 0).and_return(true)
        expect(subject.send(:update_accessibility_setting, 'VoiceOverTouchSpeakingRate', 0)).to eql(true)
      end

      it 'should change speaking_rate to -1' do
        expect(mock_accessibility_settings).to receive(:update).with('VoiceOverTouchSpeakingRate', -1).and_return(true)
        expect(subject.send(:update_accessibility_setting, 'VoiceOverTouchSpeakingRate', -1)).to eql(true)
      end
    end

    context 'Update Accessibility Settings' do
      context 'Motion Settings' do
        it 'should enable reduce_motion' do
          expect(mock_accessibility_settings).to receive(:enable).with('ReduceMotionEnabled').and_return(true)
          expect(subject.update_accessibility_settings(enable_reduce_motion)[:motion][:reduce_motion_enabled]).to eql(true)
        end

        it 'should disable reduce_motion' do
          expect(mock_accessibility_settings).to receive(:disable).with('ReduceMotionEnabled').and_return(true)
          expect(subject.update_accessibility_settings(disable_reduce_motion)[:motion][:reduce_motion_enabled]).to eql(false)
        end
      end

      context 'Display and TextSize Settings' do
        it 'should enable increase_contrast' do
          expect(mock_accessibility_settings).to receive(:enable).with('DarkenSystemColors').and_return(true)
          expect(subject.update_accessibility_settings(enable_increase_contrast)[:display_and_textsize][:increase_contrast_enabled]).to eql(true)
        end

        it 'should disable increase_contrast' do
          expect(mock_accessibility_settings).to receive(:disable).with('DarkenSystemColors').and_return(true)
          expect(subject.update_accessibility_settings(disable_increase_contrast)[:display_and_textsize][:increase_contrast_enabled]).to eql(false)
        end
      end

      context 'VoiceOver Settings' do
        it 'should change speaking_rate to 1' do
          expect(mock_accessibility_settings).to receive(:update).with('VoiceOverTouchSpeakingRate', 1.0).and_return(true)
          expect(subject.update_accessibility_settings(increase_speaking_rate)[:voice_over][:speaking_rate]).to eql(100)
        end

        it 'should change speaking_rate to 0.5' do
          expect(mock_accessibility_settings).to receive(:update).with('VoiceOverTouchSpeakingRate', 0.5).and_return(true)
          expect(subject.update_accessibility_settings(default_speaking_rate)[:voice_over][:speaking_rate]).to eql(50)
        end

        it 'should change speaking_rate to 0' do
          expect(mock_accessibility_settings).to receive(:update).with('VoiceOverTouchSpeakingRate', 0.0).and_return(true)
          expect(subject.update_accessibility_settings(decrease_speaking_rate)[:voice_over][:speaking_rate]).to eql(0)
        end
      end

      context 'Multiple Settings - idevice' do
        before :each do
          expect(mock_ios_device).to receive(:accessibility_settings).and_return(mock_accessibility_settings)
        end

        context 'Reduce Motion and Increase Conntrast' do
          it 'should enable reduce_motion and increase_contrast' do
            expect(mock_accessibility_settings).to receive(:enable).with('ReduceMotionEnabled').and_return(true)
            expect(mock_accessibility_settings).to receive(:enable).with('DarkenSystemColors').and_return(true)
            expect(subject.update_accessibility_settings(enable_reduce_motion)[:motion][:reduce_motion_enabled]).to eql(true)
            expect(subject.update_accessibility_settings(enable_increase_contrast)[:display_and_textsize][:increase_contrast_enabled]).to eql(true)
          end

          it 'should enable reduce_motion and disable increase_contrast' do
            expect(mock_accessibility_settings).to receive(:enable).with('ReduceMotionEnabled').and_return(true)
            expect(mock_accessibility_settings).to receive(:disable).with('DarkenSystemColors').and_return(true)
            expect(subject.update_accessibility_settings(enable_reduce_motion)[:motion][:reduce_motion_enabled]).to eql(true)
            expect(subject.update_accessibility_settings(disable_increase_contrast)[:display_and_textsize][:increase_contrast_enabled]).to eql(false)
          end
        end

        context 'Reduce Motion and Voice Over Speaking Rate' do
          it 'should enable reduce_motion and speaking_rate to 1' do
            expect(mock_accessibility_settings).to receive(:enable).with('ReduceMotionEnabled').and_return(true)
            expect(mock_accessibility_settings).to receive(:update).with('VoiceOverTouchSpeakingRate', 1).and_return(true)
            expect(subject.update_accessibility_settings(increase_speaking_rate)[:voice_over][:speaking_rate]).to eql(100)
            expect(subject.update_accessibility_settings(enable_reduce_motion)[:motion][:reduce_motion_enabled]).to eql(true)
          end

          it 'should disable reduce_motion and speaking_rate to 0.5' do
            expect(mock_accessibility_settings).to receive(:disable).with('ReduceMotionEnabled').and_return(true)
            expect(mock_accessibility_settings).to receive(:update).with('VoiceOverTouchSpeakingRate', 0.5).and_return(true)
            expect(subject.update_accessibility_settings(default_speaking_rate)[:voice_over][:speaking_rate]).to eql(50)
            expect(subject.update_accessibility_settings(disable_reduce_motion)[:motion][:reduce_motion_enabled]).to eql(false)
          end
        end

        context 'Speaking Rate and Increase Conntrast' do
          it 'should enable increase_contrast and speaking_rate to 1' do
            expect(mock_accessibility_settings).to receive(:enable).with('DarkenSystemColors').and_return(true)
            expect(mock_accessibility_settings).to receive(:update).with('VoiceOverTouchSpeakingRate', 1).and_return(true)
            expect(subject.update_accessibility_settings(increase_speaking_rate)[:voice_over][:speaking_rate]).to eql(100)
            expect(subject.update_accessibility_settings(enable_increase_contrast)[:display_and_textsize][:increase_contrast_enabled]).to eql(true)
          end

          it 'should disable increase_contrast and speaking_rate to 0.5' do
            expect(mock_accessibility_settings).to receive(:disable).with('DarkenSystemColors').and_return(true)
            expect(mock_accessibility_settings).to receive(:update).with('VoiceOverTouchSpeakingRate', 0.5).and_return(true)
            expect(subject.update_accessibility_settings(default_speaking_rate)[:voice_over][:speaking_rate]).to eql(50)
            expect(subject.update_accessibility_settings(disable_increase_contrast)[:display_and_textsize][:increase_contrast_enabled]).to eql(false)
          end
        end
      end

      context 'Multiple Settings - WDA and idevice' do
        let(:wda_voice_over_settings) do
          {
            navigation_style: "grouped",
            captions_panel: "enable"
          }
        end
        let(:wda_display_textsize_settings) do
          {
            larger_accessibility_sizes: "disable",
            text_size: 4
          }
        end
        context 'Voice Over' do
          it 'should update all Voice Over Settings' do
            expect(mock_accessibility_settings).to receive(:update).with('VoiceOverTouchSpeakingRate', 0).and_return(true)
            expect_any_instance_of(WdaClient).to receive(:set_voice_over_settings).with(wda_voice_over_settings).and_return({ "value" => { "success" => true, "captions_panel" => { "success" => true }, "navigation_style" => { "success" => true } } })
            state = subject.update_accessibility_settings(voice_over_settings.to_json)
            puts "[STATE] #{state}"
            expect(state[:voice_over][:speaking_rate]).to eql(0)
            expect(state[:voice_over][:captions_panel_enabled]).to eql(true)
            expect(state[:voice_over][:navigation_style]).to eql("grouped")
          end

          it 'should update speaking_rate and captions_panel' do
            expect(mock_accessibility_settings).to receive(:update).with('VoiceOverTouchSpeakingRate', 1).and_return(true)
            expect_any_instance_of(WdaClient).to receive(:set_voice_over_settings).with(wda_voice_over_settings).and_return({ "value" => { "success" => true, "captions_panel" => { "success" => true }, "navigation_style" => { "success" => true } } })
            voice_over_settings[:speaking_rate] = 100
            voice_over_settings[:captions_panel] = "disable"
            wda_voice_over_settings[:captions_panel] = "disable"
            state = subject.update_accessibility_settings(voice_over_settings.to_json)
            expect(state[:voice_over][:speaking_rate]).to eql(100)
            expect(state[:voice_over][:captions_panel_enabled]).to eql(false)
            expect(state[:voice_over][:navigation_style]).to eql("grouped")
          end

          it 'should update captions_panel and navigation_style' do
            expect(mock_accessibility_settings).to receive(:update).with('VoiceOverTouchSpeakingRate', 0).and_return(true)
            expect_any_instance_of(WdaClient).to receive(:set_voice_over_settings).with(wda_voice_over_settings).and_return({ "value" => { "success" => true, "captions_panel" => { "success" => true }, "navigation_style" => { "success" => true } } })
            voice_over_settings[:speaking_rate] = "0"
            voice_over_settings[:navigation_style] = "grouped"
            state = subject.update_accessibility_settings(voice_over_settings.to_json)
            expect(state[:voice_over][:speaking_rate]).to eql(0)
            expect(state[:voice_over][:captions_panel_enabled]).to eql(true)
            expect(state[:voice_over][:navigation_style]).to eql("grouped")
          end
        end

        context 'Display and Text Size' do
          it 'should update all Display Settings' do
            expect(mock_accessibility_settings).to receive(:enable).with('DarkenSystemColors').and_return(true)
            expect_any_instance_of(WdaClient).to receive(:set_display_settings).with(wda_display_textsize_settings).and_return({ "value" => { "success" => true, "message" => "" } })
            display_and_textsize_settings[:increase_contrast] = "enable"
            display_and_textsize_settings[:larger_accessibility_sizes] = "enable"
            display_and_textsize_settings[:text_size] = 8
            wda_display_textsize_settings[:larger_accessibility_sizes] = "enable"
            wda_display_textsize_settings[:text_size] = 8
            state = subject.update_accessibility_settings(display_and_textsize_settings.to_json)
            expect(state[:display_and_textsize][:increase_contrast_enabled]).to eql(true)
            expect(state[:display_and_textsize][:larger_accessibility_sizes_enabled]).to eql(true)
            expect(state[:display_and_textsize][:text_size]).to eql(8)
          end

          it 'should update increase_contrast and text_size' do
            expect(mock_accessibility_settings).to receive(:enable).with('DarkenSystemColors').and_return(true)
            expect_any_instance_of(WdaClient).to receive(:set_display_settings).with(wda_display_textsize_settings).and_return({ "value" => { "success" => true, "message" => "" } })
            display_and_textsize_settings[:increase_contrast] = "enable"
            display_and_textsize_settings[:text_size] = 5
            wda_display_textsize_settings[:text_size] = 5
            state = subject.update_accessibility_settings(display_and_textsize_settings.to_json)
            expect(state[:display_and_textsize][:increase_contrast_enabled]).to eql(true)
            expect(state[:display_and_textsize][:larger_accessibility_sizes_enabled]).to eql(false)
            expect(state[:display_and_textsize][:text_size]).to eql(5)
          end

          it 'should update text_size and enable larger_accessibility_sizes' do
            expect(mock_accessibility_settings).to receive(:disable).with('DarkenSystemColors').and_return(true)
            expect_any_instance_of(WdaClient).to receive(:set_display_settings).with(wda_display_textsize_settings).and_return({ "value" => { "success" => true, "message" => "" } })
            display_and_textsize_settings[:larger_accessibility_sizes] = "enable"
            display_and_textsize_settings[:text_size] = 8
            wda_display_textsize_settings[:larger_accessibility_sizes] = "enable"
            wda_display_textsize_settings[:text_size] = 8
            state = subject.update_accessibility_settings(display_and_textsize_settings.to_json)
            expect(state[:display_and_textsize][:increase_contrast_enabled]).to eql(false)
            expect(state[:display_and_textsize][:larger_accessibility_sizes_enabled]).to eql(true)
            expect(state[:display_and_textsize][:text_size]).to eql(8)
          end
        end
      end
    end
  end
end
