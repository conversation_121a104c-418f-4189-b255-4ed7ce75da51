require_relative '../../spec_helper'
require_relative '../../../lib/helpers/automate_gestures/apple_pay_executor'

require 'tempfile'
require 'timecop'

file = Tempfile.new('screenshot')

describe ApplePayExecutor do
  let(:device_config) { { "device_version" => '15.4', "region" => 'us-east-1', 'webdriver_port' => 8080 } }
  let(:uuid) { '000820-427503425' }
  let(:product) { 'app-live' }
  let(:session_id) { '1' }
  let(:ios_version) { '15.4' }
  let(:event_name) { 'web_events' }
  let(:mock_wda_client) { double(WdaClient) }
  let(:data_report_helper) { double(DataReportHelper) }
  let(:mock_ios_device) { double(IosDevice).as_null_object }
  let(:mock_idevice_utils) { double(IdeviceUtils) }
  let(:mock_image_data) { "\x89PNG\r\n\x1A\n\x00\x00\x00\rIHDR\x00\x00\x03<\x00\x00\a\x00\x10\x02\x00\x00\x00\xCA\xF3\r?\x00\x00\x01uiCCPkCGColorSp..." }
  let(:coordinates) { %w[293 702] }
  let(:start_time) { 100 }
  let(:end_time) { 100 }
  let(:device_state) { DeviceState.new('000820-427503425') }

  before do
    allow(DeviceState).to receive(:new).and_return(device_state)
    allow(device_state).to receive(:apple_pay_configuration_file_present?).and_return(true)
    allow(DeviceManager).to receive(:device_configuration_check).and_return(device_config)
    allow(BrowserStack::IosDevice).to receive(:new).and_return(mock_ios_device)
    allow(WdaClient).to receive(:new).and_return(mock_wda_client)
    allow(BrowserStack::Zombie).to receive(:configure)
    @apple_pay_executor = ApplePayExecutor.new(uuid, session_id, product)
    Timecop.freeze(Time.at(start_time))
  end

  after do
    Timecop.return
  end

  describe "trigger_apple_pay" do
    it "should locate and tap on assistive touch menu and then apple pay button, then confirm button" do
      expect_any_instance_of(ApplePayExecutor).to receive(:screenshot_file_path).exactly(4).times.and_return(file.path)
      expect(mock_ios_device).to receive(:take_screenshot).exactly(3).times.and_return(mock_image_data)
      expect_any_instance_of(ApplePayExecutor).to receive(:coordinates).exactly(3).times.and_return(coordinates)
      expect(mock_wda_client).to receive(:tap).exactly(3).times.and_return(true)
      expect_any_instance_of(DataReportHelper).to receive(:report).with({ "retries" => 0,
                                                                          "status" => "pass",
                                                                          "time_taken" => end_time - start_time }).and_return(true)
      expect(@apple_pay_executor.trigger_apple_pay).to eq(true)
    end

    it "should raise curl exception in performing tap through WebDriverAgentRunner" do
      expect_any_instance_of(ApplePayExecutor).to receive(:screenshot_file_path).exactly(2).times.and_return(file.path)
      expect(mock_ios_device).to receive(:take_screenshot).exactly(1).times.and_return(mock_image_data)
      expect_any_instance_of(ApplePayExecutor).to receive(:coordinates).exactly(1).times.and_return(coordinates)
      expect(mock_wda_client).to receive(:tap) do
        raise WdaClientError, "curl-error"
      end
      expect(mock_wda_client).to receive(:running?).and_return(false)
      expect_any_instance_of(DataReportHelper).to receive(:report).with({ "error" => "curl-error - curl-error",
                                                                          "retries" => 0,
                                                                          "status" => "fail",
                                                                          "time_taken" => end_time - start_time,
                                                                          "wda-client-status" => "non running" }).and_return(true)
      expect(@apple_pay_executor.trigger_apple_pay).to eq(false)
    end

    it "should raise parse exception in performing tap through WebDriverAgentRunner" do
      expect_any_instance_of(ApplePayExecutor).to receive(:screenshot_file_path).exactly(2).times.and_return(file.path)
      expect_any_instance_of(ApplePayExecutor).to receive(:coordinates).exactly(1).times.and_return(coordinates)
      expect(mock_ios_device).to receive(:take_screenshot).exactly(1).times.and_return(mock_image_data)
      expect(mock_wda_client).to receive(:tap) do
        raise WdaClientError, "parse-error"
      end
      expect(mock_wda_client).to receive(:running?).and_return(false)
      expect_any_instance_of(DataReportHelper).to receive(:report).with({ "error" => "parse-error - parse-error",
                                                                          "retries" => 0,
                                                                          "status" => "fail",
                                                                          "time_taken" => end_time - start_time,
                                                                          "wda-client-status" => "non running" }).and_return(true)
      expect(@apple_pay_executor.trigger_apple_pay).to eq(false)
    end

    it "should raise ScriptExecutionError while evaluating coordinates using opencv script" do
      expect_any_instance_of(ApplePayExecutor).to receive(:screenshot_file_path).exactly(2).times.and_return(file.path)
      expect(mock_ios_device).to receive(:take_screenshot).exactly(1).times.and_return(mock_image_data)
      expect(@apple_pay_executor).to receive(:coordinates) do
        raise(ScriptExecutionError)
      end
      expect_any_instance_of(DataReportHelper).to receive(:report).and_return(true)
      expect(@apple_pay_executor.trigger_apple_pay).to eq(false)
    end

    it "should raise UnsupportedElementError while evaluating coordinates using opencv script" do
      expect_any_instance_of(ApplePayExecutor).to receive(:screenshot_file_path).exactly(2).times.and_return(file.path)
      expect(mock_ios_device).to receive(:take_screenshot).exactly(1).times.and_return(mock_image_data)
      expect(@apple_pay_executor).to receive(:coordinates) do
        raise(ScriptExecutionError)
      end
      expect_any_instance_of(DataReportHelper).to receive(:report).and_return(true)
      expect(@apple_pay_executor.trigger_apple_pay).to eq(false)
    end

    it "should raise some exception and fail to trigger shake" do
      expect_any_instance_of(ApplePayExecutor).to receive(:screenshot_file_path).exactly(2).times.and_return(file.path)
      expect(mock_ios_device).to receive(:take_screenshot).exactly(1).times.and_return(mock_image_data)
      expect(@apple_pay_executor).to receive(:coordinates) do
        raise(StandardError)
      end
      expect_any_instance_of(DataReportHelper).to receive(:report).and_return(true)
      expect(@apple_pay_executor.trigger_apple_pay).to eq(false)
    end
  end
end
