require_relative '../../../lib/helpers/crash_log'
require_relative '../../../server/device_manager'
require_relative '../../../config/constants'
require_relative '../../../lib/utils/osutils'
require_relative '../../../lib/utils/utils'
require_relative '../../../lib/utils/idevice_utils'

describe CrashLog do
  let(:session_id) { "session_id" }
  let(:device) { "xxxx" }
  let(:crash_zip_path) { "/tmp/crash_report_#{session_id}.zip" }
  let(:type) { "app_live_crash" }
  let(:params) do
    {
      session_id: "session_id",
      "s3_config":
      {
        s3_bucket: 'bucket',
        s3_region: 'region'
      }
    }
  end

  let(:metadata) do
    {
      num_crash_reports: "2"
    }
  end

  before(:each) do
    allow(DeviceManager).to receive(:session_file)
    allow(File).to receive(:exist?).and_return(true)
  end

  describe '#generate_and_upload' do
    it "should return num of crash reports when there are no errors and upload to s3 when number of crash reports is not 0" do
      expect(CrashLog).to receive(:generate).and_return(["file1.ips", "file2.ips"])
      expect(CrashLog).to receive(:upload).and_return(true)
      expect(CrashLog).to receive(:cleanup).and_return(true)

      expect(CrashLog.generate_and_upload(device, params, anything)).to eq({ num_crash_reports: 2 })
    end
  end

  describe '#generate' do
    before(:each) do
      allow(IdeviceUtils).to receive(:configure)
      allow(IdeviceUtils).to receive(:list_user_installed_apps_details)
      allow(IdeviceUtils).to receive(:get_crash_report_from_device)
      allow(IdeviceUtils).to receive(:create_zip_of_crash_report)
    end

    context 'when crash reports are generated succesfully' do
      it 'should not throw exception' do
        allow(IdeviceUtils).to receive(:list_user_installed_apps_details).and_return([
          {
            bundle_id: "bundle",
            version: "version",
            display_name: "name",
            bundle_executable: "executable"
          }
        ])
        allow(IdeviceUtils).to receive(:get_crash_report_from_device).and_return(["some_report"])
        allow(IdeviceUtils).to receive(:create_zip_of_crash_report)
        expect(CrashLog.generate(device, anything, crash_zip_path, session_id)).to eq(["some_report"])
      end
    end

    context 'when crash reports are not generated successfully' do
      it 'should throw exception' do
        allow(IdeviceUtils).to receive(:list_user_installed_apps_details).and_raise("Some error")
        expect { CrashLog.generate(device, anything, crash_zip_path, session_id) }.to raise_error("Some error")
      end
    end
  end

  describe '#upload' do
    context 'when crash logs are uploaded succesfully' do
      it 'should not throw exception' do
        allow(Utils).to receive(:upload_file_to_s3).and_return([true, ''])
        allow(JSON).to receive(:parse).and_return({
          s3_bucket: 'bucket',
          s3_region: 'region'
        })
        expect(CrashLog.send(:upload, params, "some_path", anything)).to eq(true)
      end
    end

    context 'when crash logs are not uploaded successfully' do
      it 'should throw exception' do
        allow(Utils).to receive(:upload_file_to_s3).and_raise("Some error")
        allow(JSON).to receive(:parse).and_return({
          s3_bucket: 'bucket',
          s3_region: 'region'
        })
        expect { CrashLog.upload(params, "some_path", anything) }.to raise_error("Some error")
      end
    end
  end

  describe '#cleanup' do
    context 'when crash logs are cleaned up succesfully' do
      it 'should not throw exception and return true' do
        allow(FileUtils).to receive(:rm_rf)
        expect(CrashLog.cleanup(device, "some_path")).to eq(true)
      end
    end

    context 'when crash logs are not cleaned up successfully' do
      it 'should throw exception' do
        allow(FileUtils).to receive(:rm_rf).and_raise("Some error")
        expect { CrashLog.cleanup(device, "some_path") }.to raise_error("Some error")
      end
    end
  end
end
