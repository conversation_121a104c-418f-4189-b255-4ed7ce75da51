require_relative '../../spec_helper'
require_relative '../../../lib/helpers/app_accessibility'
require_relative '../../../server/device_manager'

describe AppAccessibility do
  let(:device_config) { { "device_version" => '15.4', "region" => 'us-east-1', 'webdriver_port' => 8080 } }
  let(:uuid) { '000820-427503425' }
  let(:product) { 'app-accessibility' }
  let(:session_id) { '1' }
  let(:ios_version) { '15.4' }
  let(:event_name) { 'web_events' }
  let(:automation_success_data) do
    { "value" => { "status" => "pass", "data" => '{
      "uid" : "01000000-0000-0000-4100-000000000000",
       }', "screenshot" => '\/9j\/4AAQSkZJRgABAQAASABIAAD\/4QBYRXhpZgAATU0AKgAAAAgAAgESAAMAAAAB\r\nAAEAAIdpAAQAAAABAAAAJgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAEkqAD\r\nAAQAAAABAAAJ5AAAAAD\/7QA4UGhvdG9zaG9wIDMuMAA4QklNBAQAAAAAAAA4QklN\r\nBCUAAAAA', "deviceInfo" => {
         "scaleFactor" => "3.000000"
       }, "error" => "" } }
  end
  let(:automation_failure_data) do
    { "value" => { "status" => "fail", "data" => "some error", "screenshot" => '\/9j\/4AAQSkZJRgABAQAASABIAAD\/4QBYRXhpZgAATU0AKgAAAAgAAgESAAMAAAAB\r\nAAEAAIdpAAQAAAABAAAAJgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAEkqAD\r\nAAQAAAABAAAJ5AAAAAD\/7QA4UGhvdG9zaG9wIDMuMAA4QklNBAQAAAAAAAA4QklN\r\nBCUAAAAA', "deviceInfo" => {
      "scaleFactor" => "3.000000"
    }, "error" => "" } }
  end

  let(:session_info) do
    { downloaded_app_path: "app_path", app_testing_bundle_id: "app_bundle_id" }
  end

  before do
    allow(DeviceManager).to receive(:device_configuration_check).and_return(device_config)
    allow(DeviceManager).to receive(:session_file_contents).and_return(session_info)
    allow(BrowserStack::Zombie).to receive(:configure)
    @testObject = AppAccessibility.new(uuid, session_id, product)
  end

  describe "fetch_accessibility_info" do
    it "should successfully fetch accessibility info" do
      expect_any_instance_of(WdaClient).to receive(:post_accessibility_info).and_return(automation_success_data)
      result = @testObject.fetch_accessibility_info
      expect(result).to eq({ "os" => "ios", "session_id" => "1", "status" => "pass", "data" => "{\n      \"uid\" : \"01000000-0000-0000-4100-000000000000\",\n       }", "screenshot" => "\\/9j\\/4AAQSkZJRgABAQAASABIAAD\\/4QBYRXhpZgAATU0AKgAAAAgAAgESAAMAAAAB\\r\\nAAEAAIdpAAQAAAABAAAAJgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAEkqAD\\r\\nAAQAAAABAAAJ5AAAAAD\\/7QA4UGhvdG9zaG9wIDMuMAA4QklNBAQAAAAAAAA4QklN\\r\\nBCUAAAAA", "deviceInfo" => { "scaleFactor" => "3.000000" },
                             "error" => "", "user_interface_style" => nil, "supported_orientations" => nil, "bundle_id" => nil, "app_name" => nil, "app_version" => nil, "accessibility_audit_data" => [], "focus_order_caption_data" => []  })
    end
    it "should fail to fetch accessibility info" do
      expect_any_instance_of(WdaClient).to receive(:post_accessibility_info).and_return(automation_failure_data)
      result = @testObject.fetch_accessibility_info
      expect(result).to eq({})
    end
    it "should raise curl exception" do
      wda_client = double
      expect(WdaClient).to receive(:new).and_return(wda_client)
      expect(wda_client).to receive(:post_accessibility_info) do
        raise WdaClientError, "curl-error"
      end
      expect(wda_client).to receive(:running?).and_return(false)
      result = @testObject.fetch_accessibility_info
      expect(result).to eq({})
    end
    it "should raise parse exception" do
      wda_client = double
      expect(WdaClient).to receive(:new).and_return(wda_client)
      expect(wda_client).to receive(:post_accessibility_info) do
        raise WdaClientError, "parse-error"
      end
      expect(wda_client).to receive(:running?).and_return(false)
      result = @testObject.fetch_accessibility_info
      expect(result).to eq({})
    end
    it "should raise standard exception" do
      wda_client = double
      expect(WdaClient).to receive(:new).and_return(wda_client)
      expect(wda_client).to receive(:post_accessibility_info) do
        raise(StandardError)
      end
      result = @testObject.fetch_accessibility_info
      expect(result).to eq({})
    end
  end
end
