require_relative "../../../lib/helpers/download_file_helper"
require_relative "../../../lib/utils/idevice_file_utils"
require_relative "../../../server/device_manager"

describe DownloadFileHelper do
  let(:session_id) { "session_id" }
  let(:device) { "000820-427503425" }
  let(:product) { "app-live" }
  let(:device_config) { { "webdriver_port" => 8080, "device_version" => "14.0" } }
  let(:download_file_zip_path) { "/tmp/download-files_#{device}.zip" }
  let(:download_file_folder_path) { "/tmp/download-files_#{device}" }
  let(:list_of_preloaded_media_path) { "/tmp/preloaded-media-list_#{device}" }
  let(:type) { "download-file" }
  let(:success_data) { { download_file_size: 10, message: "DOWNLOAD_FILES_SUCCESS", result: "success", time_taken: 0 } }
  let(:failure_data_max_size_limit_error) { { download_file_size: 100, message: "DOWNLOAD_LIMIT_EXCEEDED", result: "failed", time_taken: 0 } }
  let(:failure_data_files_not_available) { { download_file_size: 0, message: "DOWNLOAD_FILES_NOT_AVAILABLE", result: "failed", time_taken: 0 } }
  let(:failure_data_failed) { { download_file_size: 0, message: "DOWNLOAD_FILES_FAILED", result: "failed", time_taken: 0 } }

  let(:params) do
    {
      session_id: "session_id",
      max_files_size: 50,
      s3_config: {
        s3_bucket: "bucket",
        s3_region: "region"
      }
    }
  end

  before(:each) do
    allow(BrowserStack::Zombie).to receive(:configure)
    allow(BrowserStack::Zombie).to receive(:push_logs)
    allow(DeviceManager).to receive(:session_file)
    allow(File).to receive(:exist?).and_return(true)
    allow(FileUtils).to receive(:rm_rf)
    allow(DeviceManager).to receive(:device_configuration_check).and_return(device_config)
    @testObject = DownloadFileHelper.new(device, session_id, product)
  end

  describe "#generate" do
    before(:each) do
      allow(IdeviceUtils).to receive(:configure)
      allow(IdeviceFileUtils).to receive(:pull_folder)
      allow(IdeviceFileUtils).to receive(:pull_all_photos)
      allow(Utils).to receive(:create_zip)
      allow(File).to receive(:read).and_return("DCIM/IMG_000.jpg")
      allow(OSUtils).to receive(:execute)
    end

    it "should not throw exception when download file zip is generated succesfully" do
      expect(BrowserStack::OSUtils).to receive(:execute).with("unzip -l #{download_file_zip_path} | tail -1").and_return("10 /tmp/apps")
      expect(@testObject.send(:generate, download_file_zip_path, params)).to eq({ message: "DOWNLOAD_FILES_SUCCESS", file_size: 10, status: "success" })
    end

    it "should show corresponding error message when download file zip is generated succesfully but exceeds size limit" do
      expect(BrowserStack::OSUtils).to receive(:execute).with("unzip -l #{download_file_zip_path} | tail -1").and_return("10000000000 /tmp/apps")
      expect(@testObject.send(:generate, download_file_zip_path, params)).to eq({ message: "DOWNLOAD_LIMIT_EXCEEDED",
                                                                                  file_size: 10000000000, status: "failed" })
    end

    it "should show corresponding error message when download file zip is generated succesfully but no files are present" do
      expect(BrowserStack::OSUtils).to receive(:execute).with("unzip -l #{download_file_zip_path} | tail -1").and_return("0 /tmp/apps")
      expect(@testObject.send(:generate, download_file_zip_path, params)).to eq({ message: "DOWNLOAD_FILES_NOT_AVAILABLE", file_size: 0, status: "failed" })
    end

    it "should show corresponding error message when download file zip is not generated succesfully" do
      allow(Utils).to receive(:create_zip).and_raise("Some error")
      expect(@testObject.send(:generate, download_file_zip_path, params)).to eq({ message: "DOWNLOAD_FILES_FAILED", file_size: 0, status: "failed" })
    end
  end

  describe "#upload" do
    context "when downloaded file zip is uploaded succesfully" do
      it "should not throw exception" do
        allow(Utils).to receive(:upload_file_to_s3).and_return([true, ""])
        allow(Utils).to receive(:get_presigned_url).and_return("some_presigned_s3_url")
        allow(JSON).to receive(:parse).and_return({
          s3_bucket: "bucket",
          s3_region: "region"
        })
        expect(@testObject.send(:upload, params, download_file_zip_path)).to eq("some_presigned_s3_url")
      end
    end

    context "when downloaded file zip is not uploaded successfully" do
      it "should throw exception" do
        allow(Utils).to receive(:upload_file_to_s3).and_raise("Some error")
        allow(Utils).to receive(:get_presigned_url).and_return("some_presigned_s3_url")
        allow(JSON).to receive(:parse).and_return({
          s3_bucket: "bucket",
          s3_region: "region"
        })
        expect { @testObject.send(:upload, params, download_file_zip_path) }.to raise_error("Some error")
      end
    end
  end
end
