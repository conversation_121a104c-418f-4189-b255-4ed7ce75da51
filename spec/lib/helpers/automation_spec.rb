require_relative '../../spec_helper'
require_relative '../../../lib/helpers/automation'

describe Automation do
  describe "#get_driver_for_app(device_id, app, caps={})" do
    let(:device_id) { "device123456" }
    let(:device_config) do
      { "current_appium_version" => "1.14.0",
        "device_name" => "iPhone 8",
        "device_version" => "13.2.0",
        "selenium_port" => 8080,
        "webdriver_port" => 8401,
        "debugger_port" => 12345 }
    end
    let(:mock_appium_server) { instance_double('BrowserStack::AppiumServer', driver: nil) }
    before do
      allow(DeviceManager).to receive(:device_configuration_check).and_return(device_config)
      allow(BrowserStack::AppiumServer).to receive(:new).with(device_id).and_return(mock_appium_server)
      allow(mock_appium_server).to receive(:running?).and_return(true)
    end

    it 'creates an instance of AppiumServer' do
      allow(DeviceManager).to receive(:device_configuration_check).with(device_id).and_return(device_config)
      expect(BrowserStack::AppiumServer).to receive(:new).with(device_id)
      Automation.get_driver_for_app(device_id, 'settings')
    end

    it 'calls .driver on the created instance of AppiumServer with the right params' do
      allow(DeviceManager).to receive(:device_configuration_check).with(device_id).and_return(device_config)
      expect(mock_appium_server).to receive(:driver).with({ app: 'settings' })
      Automation.get_driver_for_app(device_id, 'settings')
    end
  end

  describe '#allow_wda_local_network' do
    let(:driver) { double('AppiumServer') }
    let(:wda_client) { double('WdaClient') }
    let(:device_id) { double('device') }
    let(:element) { double('Element') }

    before do
      allow(Automation).to receive(:open_settings_app).and_return(driver)
      allow_any_instance_of(Object).to receive(:sleep)
    end

    context 'local network access for wda not enabled' do
      let(:allow_button) { double('Element') }

      context 'WebDriverAgentRunner-Runner is on home screen' do
        it 'clicks element on home screen to open app' do
          expect(driver).to receive(:background_app)
          expect(driver).to receive(:find_element).with(:accessibility_id, "WebDriverAgentRunner-Runner")

          expect(wda_client).to receive(:trigger_local_network_access_popup)
          expect(driver).to receive(:find_element).with(:name, "OK").and_return(allow_button)
          expect(allow_button).to receive(:click)

          expect(driver).to receive(:reset)
          expect(driver).to receive(:driver_quit)
          Automation.allow_wda_local_network(device_id, wda_client)
        end
      end

      context 'WebDriverAgentRunner-Runner is not on home screen' do
        it 'calls idevice_utils to launch app with bundle id' do
          expect(driver).to receive(:background_app)

          expect(driver).to receive(:find_element).with(:accessibility_id, "WebDriverAgentRunner-Runner").and_raise(Selenium::WebDriver::Error::NoSuchElementError)
          expect(IdeviceUtils).to receive(:launch_app_with_bundle_id)
          expect(wda_client).to receive(:trigger_local_network_access_popup)
          expect(driver).to receive(:find_element).with(:name, "OK").and_return(allow_button)
          expect(allow_button).to receive(:click)

          expect(driver).to receive(:reset)
          expect(driver).to receive(:driver_quit)
          Automation.allow_wda_local_network(device_id, wda_client)
        end
      end
    end
  end
end