require_relative '../../spec_helper'
require_relative '../../../lib/helpers/dedicated_video_rec_manager'
require_relative '../../../lib/models/device_state'

describe DedicatedVideoRecManager do
  let(:device) { 'device_123' }
  let(:session_id) { 'session_123' }
  let(:video_files) { ['file1.MP4', 'file2.MP4', 'file3.MP4'] }
  let(:device_state) { DeviceState.new(device) }

  before do
    allow(DeviceState).to receive(:new).with(device).and_return(device_state)
    allow(BrowserStack::Zombie).to receive(:push_logs)
  end

  describe '#get_new_video_files' do
    before do
      allow(device_state).to receive(:dedicated_video_state_file_to_array).and_return(['file1.MP4', 'file2.MP4'])
      allow(device_state).to receive(:write_array_to_dedicated_video_state_file)
    end

    context 'when new video files are available' do
      it 'returns the new video files and logs the appropriate information' do
        result = DedicatedVideoRecManager.new(device).get_new_video_files(video_files, session_id)

        expect(result).to eq(['file3.MP4'])
      end
    end

    context 'when no new video files are available' do
      it 'raises a VideoFileFetchException' do
        allow(device_state).to receive(:dedicated_video_state_file_to_array).and_return(['file1.MP4', 'file2.MP4', 'file3.MP4'])

        expect do
          DedicatedVideoRecManager.new(device).get_new_video_files(video_files, session_id)
        end.to raise_error(VideoFileFetchException, "[Dedicated] Error in get_new_video_files")
      end
    end

    context 'when an error occurs' do
      it 'logs the error and raises a VideoFileFetchException' do
        allow(device_state).to receive(:dedicated_video_state_file_to_array).and_raise(StandardError, 'Error occurred')
        expect do
          DedicatedVideoRecManager.new(device).get_new_video_files(video_files, session_id)
        end.to raise_error(VideoFileFetchException, "[Dedicated] Error in get_new_video_files")
      end
    end
  end

  describe '#write_session_video_file' do
    before do
      allow(device_state).to receive(:dedicated_video_state_file_present?).and_return(false)
      allow(device_state).to receive(:write_array_to_dedicated_video_state_file)
    end

    context 'when video files are fetched successfully' do
      it 'writes the video files to the state file and logs the appropriate information' do
        allow(BrowserStack::OSUtils).to receive(:execute)
        allow(Dir).to receive(:glob).and_return(['file1.MP4', 'file2.MP4', 'file3.MP4'])

        DedicatedVideoRecManager.new(device).write_session_video_file('component', session_id)

        expect(BrowserStack.logger).to have_received(:info).with(/Success : Video files fetched for #{@device}/)
      end
    end

    context 'when the video state file is present' do
      it 'does not attempt to write the video files' do
        allow(device_state).to receive(:dedicated_video_state_file_present?).and_return(true)
        DedicatedVideoRecManager.new(device).write_session_video_file('component', session_id)
        # No other actions should be taken
      end
    end

    context 'when an OSUtilsError occurs' do
      it 'retries and eventually raises the error if retry count exceeds' do
        allow(BrowserStack::OSUtils).to receive(:execute).and_raise(OSUtilsError, 'Timeout error')
        allow(BrowserStack.logger).to receive(:error)

        expect do
          DedicatedVideoRecManager.new(device).write_session_video_file('component', session_id)
        end.to raise_error(OSUtilsError, 'Timeout error')

        expect(BrowserStack.logger).to have_received(:error).with(/Failed : Timeout/).twice
      end
    end

    context 'when another error occurs' do
      it 'logs the error and raises it' do
        allow(BrowserStack::OSUtils).to receive(:execute).and_raise(StandardError, 'Other error')
        allow(BrowserStack.logger).to receive(:error)

        expect do
          DedicatedVideoRecManager.new(device).write_session_video_file('component', session_id)
        end.to raise_error(StandardError, 'Other error')

        expect(BrowserStack.logger).to have_received(:error).with(/Failed : Error, Other error/)
      end
    end
  end
end
