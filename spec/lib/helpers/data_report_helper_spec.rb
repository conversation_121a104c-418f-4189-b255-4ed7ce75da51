require_relative '../../spec_helper'
require_relative '../../../lib/helpers/data_report_helper'

describe DataReportHelper do
  let(:event_name) { 'some_event' }
  let(:session_id) { 'session_id' }
  let(:ios_version) { 14 }
  let(:error) { 'some_error' }
  let(:product) { 'app-live' }
  let(:sample_success_data) do
    {
      "result" => "success",
      "time_taken" => 2
    }
  end

  let(:sample_failed_data) do
    {
      "result" => "failed",
      "time_taken" => 2,
      "error" => error,
      "error_message" => "some message"
    }
  end

  before do
    expect(BrowserStack::Zombie).to receive(:configure)
    @testObject = DataReportHelper.new(event_name, session_id: session_id, ios_version: ios_version)
  end

  context "#report" do
    it "should send data to success data to eds with default params" do
      expect(Utils).to receive(:send_to_eds).with(anything, "web_events", true)
      @testObject.report(sample_success_data)
    end

    it "should send data to zombie in case of failure" do
      expect(Utils).to receive(:send_to_eds).with(anything, "web_events", true)
      expect(BrowserStack::Zombie).to receive(:push_logs).with("#{event_name}-failure", "some_error", { "data" => { "error" => "some_error", "error_message" => "some message", "result" => "failed", "time_taken" => 2 }, "device" => nil, "os_version" => 14, "product" => nil, "session_id" => "session_id", "team" => "device-features", "user_id" => nil })
      @testObject.report(sample_failed_data)
    end

    it "should send data only to eds when eds_only is true" do
      expect(Utils).to receive(:send_to_eds)
      expect(BrowserStack::Zombie).not_to receive(:push_logs)
      @testObject.report(sample_failed_data, eds_only: true)
    end

    it "should send data only to zombie when zombie_only is true" do
      expect(Utils).not_to receive(:send_to_eds)
      expect(BrowserStack::Zombie).to receive(:push_logs)
      @testObject.report(sample_failed_data, zombie_only: true)
    end

    it "should always send data to zombie when force_zombie is true except for success cases" do
      expect(Utils).to receive(:send_to_eds)
      expect(BrowserStack::Zombie).to receive(:push_logs)
      @testObject.report(sample_success_data, force_zombie: true, zombies_data: sample_failed_data)
    end
  end
end

