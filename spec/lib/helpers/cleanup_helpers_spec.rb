require 'fileutils'

require_relative '../../../lib/configuration'
require_relative '../../../lib/helpers/cleanup_helpers'
require_relative '../../spec_helper'

describe CleanupHelpers do
  let(:udid) { '00008020-000A48A43463002E' }

  before do
    DeviceManager.configure(BrowserStack::Configuration.new.all)
    allow(DeviceManager).to receive(:request_setup)
    allow(DeviceManager).to receive(:request_cleanup)
    allow(CleanupHelpers).to receive(:exit!)
  end

  describe '.abort_and_request_setup!' do
    it 'requests a setup' do
      expect(DeviceManager).to receive(:request_setup).with(udid)
      CleanupHelpers.abort_and_request_setup!(udid)
    end

    it 'requests a cleanup' do
      expect(DeviceManager).to receive(:request_cleanup).with(udid)
      CleanupHelpers.abort_and_request_setup!(udid)
    end

    it 'updates cleanup failure reason to unknown' do
      expect(DeviceManager).to receive(:write_cleanup_failure_reason).with(udid, '', 'requesting device check, reason: unknown')
      CleanupHelpers.abort_and_request_setup!(udid)
    end

    it 'updates cleanup failure reason to the passed reason' do
      expect(DeviceManager).to receive(:write_cleanup_failure_reason).with(udid, '', 'requesting device check, reason: mdm')
      CleanupHelpers.abort_and_request_setup!(udid, 'mdm')
    end

    it 'exits the program' do
      expect(CleanupHelpers).to receive(:exit!)
      CleanupHelpers.abort_and_request_setup!(udid)
    end
  end

  describe '.cleanup_possible?' do
    let(:provisioning_manager) { double('mock_provisioning_manager') }
    let(:device_state) { double('mock_device_state') }

    before do
      allow(CleanupHelpers).to receive(:default_xctestrun_present?).and_return(true)
      allow(ProvisioningManager).to receive(:new).and_return(provisioning_manager)
      allow(provisioning_manager).to receive(:problem_detected?).and_return(false)
      allow(DeviceState).to receive(:new).and_return(device_state)
      allow(device_state).to receive(:update_provisioning_profile_file_present?).and_return(false)
      allow(device_state).to receive(:touch_needs_provisioning_file)
    end

    it 'is false when issue with provisioning profile detected' do
      allow(provisioning_manager).to receive(:problem_detected?).and_return(true)
      expect(CleanupHelpers.cleanup_possible?(udid))
        .to eq([false, "provisioning profile issue detected, waiting for device check"])
    end

    it 'touches need provisioning file when issue with provisioning profile detected' do
      allow(provisioning_manager).to receive(:problem_detected?).and_return(true)
      expect(device_state).to receive(:touch_needs_provisioning_file)
      CleanupHelpers.cleanup_possible?(udid)
    end

    context 'when provisioning profile update required' do
      let(:ppuid_file) { double('mock_ppuid_file') }
      let(:prod60) { 'production_60_02072022' }
      let(:prod21) { 'production_21_17112022' }

      before do
        allow(provisioning_manager).to receive(:problem_detected?).and_return(true)
        allow(PpuidFile).to receive(:new).and_return(ppuid_file)
        allow(device_state).to receive(:update_provisioning_profile_file_present?).and_return(true)
      end

      it 'is true when branch_name is production_60_02072022' do
        allow(ppuid_file).to receive(:branch_name).and_return(prod60)
        expect(CleanupHelpers.cleanup_possible?(udid)).to be_truthy
      end

      it 'is false when branch_name is not equal to production_60_02072022' do
        allow(ppuid_file).to receive(:branch_name).and_return(prod21)
        expect(CleanupHelpers.cleanup_possible?(udid)).to eq([false, "provisioning profile issue detected, waiting for device check"])
      end
    end

    it 'is otherwise true' do
      expect(CleanupHelpers.cleanup_possible?(udid)).to be_truthy
    end
  end
end
