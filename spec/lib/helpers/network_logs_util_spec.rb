require 'rspec'
require 'json'
require 'fileutils'
require_relative '../../spec_helper'
require_relative '../../../lib/utils/utils'
require_relative '../../../lib/helpers/network_logs_util'

describe NetworkLogsUtil do
  let(:device) { 'test_device' }
  let(:params) { { 'app_live_session_id' => '12345', mitm_ws_readiness_timeout: 30, mitm_ws_readiness_interval: 1 } }
  let(:network_logs_util) { NetworkLogsUtil.new(device, params) }

  before do
    allow(File).to receive(:read).and_return('{}')
    allow(FileUtils).to receive(:touch)
    allow(File).to receive(:exist?).and_return(true)
    allow(File).to receive(:open).and_yield(double(read: '{}', write: nil, rewind: nil, truncate: nil, flock: nil))
    allow(BrowserStack).to receive_message_chain(:logger, :info)
    allow(BrowserStack).to receive_message_chain(:logger, :error)
  end

  describe '#initialize' do
    it 'creates a new instance without errors' do
      expect { network_logs_util }.not_to raise_error
    end
  end

  describe '#safe_execute' do
    it 'calls the method and logs execution time' do
      expect(network_logs_util).to receive(:test_method)
      network_logs_util.safe_execute(:test_method)
      expect(network_logs_util.instance_variable_get(:@network_logs_event_hash)).to have_key('test_method_time_taken')
    end

    it 'rescues exceptions and logs an error' do
      allow(network_logs_util).to receive(:test_method).and_raise(StandardError, 'test error')
      expect(BrowserStack.logger).to receive(:error).with(/test error/)
      network_logs_util.safe_execute(:test_method)
    end
  end
end
