require_relative '../../../lib/helpers/apple_pay_prefill_detail_helper'
require_relative '../../spec_helper'

describe ApplePayPrefillDetailHelper do
  let(:device_id) { "test_device" }
  let(:session_id) { "test_session" }
  let(:product) { "app_automate" }
  let(:mock_assistive_touch_util) { double(AssistiveTouchUtil).as_null_object }
  let(:mock_device_config) { double(BrowserStack::Configuration).as_null_object }
  let(:mock_wda) { double(WdaClient).as_null_object }
  let(:mock_data_report_helper) { double(DataReportHelper).as_null_object }
  let(:subject) { ApplePayPrefillDetailHelper.new(device_id, { product: product, session_id: session_id }) }

  before(:each) do
    allow(AssistiveTouchUtil).to receive(:new).and_return(mock_assistive_touch_util)
    allow(DeviceManager).to receive(:device_configuration_check).and_return(mock_device_config)
    allow(WdaClient).to receive(:new).and_return(mock_wda)
    allow(DataReportHelper).to receive(:new).and_return(mock_data_report_helper)
  end

  describe 'execute' do
    context 'when wda return success response' do
      before(:each) do
        allow(mock_wda).to receive(:prefill_apple_pay_detail).and_return({ 'value' => { 'status' => 'pass' } })
      end

      it 'should return true with no error code' do
        res, err = subject.execute({})
        expect(res).to eq(true)
        expect(err).to eq(nil)
      end
    end

    context 'when wda return failure response' do
      context 'when wda returns failure response with failed to tap on Done button' do
        before(:each) do
          allow(mock_wda).to receive(:prefill_apple_pay_detail).and_return({ 'value' => { 'status' => 'fail', 'message' => "Failed to tap on element \"Done\" Button, because it is not enabled" } })
        end

        it 'should return false with error code' do
          res, err = subject.execute({})
          expect(res).to eq(false)
          expect(err).to eq("APPD_0001")
        end
      end

      context 'when wda returns any failure response' do
        before(:each) do
          allow(mock_wda).to receive(:prefill_apple_pay_detail).and_return({ 'value' => { 'status' => 'fail', 'message' => 'Failed to tap on element \"Back\" Button' } })
        end

        it 'should return false with no error code' do
          res, err = subject.execute({})
          expect(res).to eq(false)
          expect(err).to eq(nil)
        end
      end
    end
  end
end
