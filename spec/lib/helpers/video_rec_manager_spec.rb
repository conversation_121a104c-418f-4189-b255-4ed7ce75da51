require_relative '../../spec_helper'
require_relative '../../../lib/helpers/video_rec_manager'

describe VideoRecManager do
  let(:params) do
    {
      "genre" => 'app_automate',
      "orientation" => "portrait",
      "automate_session_id" => "abcd"
    }
  end

  let(:device_config) do
    { 'device_version' => "16.0" }
  end

  let(:success_wda_response) do
    {
      "value" => {
        "status" => "Success"
      }
    }
  end

  let(:failed_wda_response) do
    {
      "value" => {
        "status" => "Failed"
      }
    }
  end

  let(:slow_wda_response) do
    {
      "value" => {
        "status" => "Success",
        "reason" => "iOS video rec: springboard launch took time, try reboot after session"
      }
    }
  end

  let(:v2_params) do
    {
      "v2" => "true"
    }
  end

  let(:v2_experiment_params) do
    {
      "v2" => "true",
      "stop_experiment" => "true"
    }
  end

  let(:v2_performance_params) do
    {
      "v2" => "true",
      "v2_performance" => "true"
    }
  end

  let(:device) { 'test_device' }

  describe "#start_rec" do
    context "#v2 video recording" do
      it "should start v2 recording with success" do
        new_params = params.clone
        new_params["video_params_v2"] = v2_params.to_json
        video_rec_manager = VideoRecManager.new(device, new_params, 8400)

        expect(BrowserStack::Zombie).to receive(:push_logs)
        expect(video_rec_manager).to receive(:trigger_video_recording).with("start").and_return([success_wda_response, false])
        expect(video_rec_manager).not_to receive(:push_error_to_eds)

        expect { video_rec_manager.start_rec }.not_to raise_error
      end

      it "should push reason to eds that v2 recording did not start due to automation failure" do
        new_params = params.clone
        new_params["video_params_v2"] = v2_params.to_json
        video_rec_manager = VideoRecManager.new(device, new_params, 8400)

        expect(BrowserStack::Zombie).to receive(:push_logs)
        expect(video_rec_manager).to receive(:trigger_video_recording).with("start").and_return([failed_wda_response, false])
        expect(video_rec_manager).to receive(:push_error_to_eds)

        expect { video_rec_manager.start_rec }.not_to raise_error
      end

      it "should push reason to eds that v2 recording did not start and falls back to v1 due to standard error" do
        new_params = params.clone
        new_params["video_params_v2"] = v2_params.to_json
        video_rec_manager = VideoRecManager.new(device, new_params, 8400)

        expect(BrowserStack::Zombie).to receive(:push_logs)
        expect(video_rec_manager).to receive(:trigger_video_recording).with("start").and_return([{}, true])
        expect_any_instance_of(DeviceState).to receive(:touch_needs_phased_reboot_file)
        expect(video_rec_manager).to receive(:start_v1_recording)
        expect(video_rec_manager).to receive(:push_error_to_eds)

        expect { video_rec_manager.start_rec }.not_to raise_error
      end

      it "should push reason to eds that v2 recording started and touch needs reboot file on specific wda response" do
        new_params = params.clone
        new_params["video_params_v2"] = v2_params.to_json
        video_rec_manager = VideoRecManager.new(device, new_params, 8400)

        expect(BrowserStack::Zombie).to receive(:push_logs)
        expect(video_rec_manager).to receive(:trigger_video_recording).with("start").and_return([slow_wda_response, false])
        expect(video_rec_manager).not_to receive(:push_error_to_eds)
        expect_any_instance_of(DeviceState).to receive(:touch_needs_phased_reboot_file)

        expect { video_rec_manager.start_rec }.not_to raise_error
      end
    end

    context "#v1 video recording" do
      it "should start v1 recording with success" do
        video_rec_manager = VideoRecManager.new(device, params, 8400)

        expect(BrowserStack::Zombie).to receive(:push_logs)
        expect(video_rec_manager).not_to receive(:trigger_video_recording)
        expect(video_rec_manager).not_to receive(:push_error_to_eds)
        expect(Utils).to receive(:fork_process)

        expect { video_rec_manager.start_rec }.not_to raise_error
      end
    end
  end

  describe "#stop_rec" do
    context "#v2 video recording" do
      context "standard flow" do
        before(:each) do
          expect_any_instance_of(VideoRecProcessLogger).to receive(:check_and_start)
          expect_any_instance_of(VideoRecProcessLogger).to receive(:stop)
        end
        it "should stop v2 recording with success" do
          new_params = params.clone
          new_params["video_params_v2"] = v2_params.to_json
          video_rec_manager = VideoRecManager.new(device, new_params, 8400)

          expect_any_instance_of(DeviceState).to receive(:fallback_to_v1_file_present?).and_return(false)
          expect(video_rec_manager).to receive(:trigger_video_recording).with("stop").and_return([success_wda_response, false])
          expect(video_rec_manager).to receive(:fetch_v2_video_file).and_return("video_file")
          expect(video_rec_manager).to receive(:create_upload_request_for_video).with("video_file", new_params)
          expect(video_rec_manager).not_to receive(:push_error_to_eds)

          expect { video_rec_manager.stop_rec }.not_to raise_error
        end

        it "should try to fetch and upload video file if video file is present even if wda automation fails" do
          new_params = params.clone
          new_params["video_params_v2"] = v2_params.to_json
          video_rec_manager = VideoRecManager.new(device, new_params, 8400)

          expect_any_instance_of(DeviceState).to receive(:fallback_to_v1_file_present?).and_return(false)
          expect(video_rec_manager).to receive(:trigger_video_recording).with("stop").and_return([failed_wda_response, true])
          expect(video_rec_manager).to receive(:fetch_v2_video_file).and_return("video_file")
          expect(video_rec_manager).to receive(:create_upload_request_for_video).with("video_file", new_params)

          expect { video_rec_manager.stop_rec }.not_to raise_error
        end

        it "should not push data to eds and create upload request when file fetch after wda automation fails and xcuitest is successful and video file is fetched" do
          new_params = params.clone
          new_params["video_params_v2"] = v2_params.to_json
          video_rec_manager = VideoRecManager.new(device, new_params, 8400)

          expect_any_instance_of(DeviceState).to receive(:fallback_to_v1_file_present?).and_return(false)
          expect(video_rec_manager).to receive(:trigger_video_recording).with("stop").and_return([failed_wda_response, true])
          expect(video_rec_manager).to receive(:fetch_v2_video_file).and_return(nil)
          expect(BrowserStackAppHelper).to receive(:check_and_install_browserstack_apps)
          expect(BrowserStackAppHelper).to receive(:run_ui_test).and_return("random")
          expect(video_rec_manager).to receive(:fetch_v2_video_file).and_return("video_file")
          expect(video_rec_manager).to receive(:create_upload_request_for_video).with("video_file", new_params)
          expect(video_rec_manager).not_to receive(:push_error_to_eds)
          expect { video_rec_manager.stop_rec }.not_to raise_error
        end

        it "should not push error to eds but upload file if wda as well as xcuitest automation fails but we successfully fetch file after xcuitest" do
          new_params = params.clone
          new_params["video_params_v2"] = v2_params.to_json
          video_rec_manager = VideoRecManager.new(device, new_params, 8400)

          expect_any_instance_of(DeviceState).to receive(:fallback_to_v1_file_present?).and_return(false)
          expect(video_rec_manager).to receive(:trigger_video_recording).with("stop").and_return([failed_wda_response, true])
          expect(video_rec_manager).to receive(:fetch_v2_video_file).and_return(nil)
          expect(BrowserStackAppHelper).to receive(:check_and_install_browserstack_apps)
          expect(BrowserStackAppHelper).to receive(:run_ui_test).and_raise(BrowserStackTestExecutionError.new('test_class', 'test_func'))
          expect(video_rec_manager).to receive(:fetch_v2_video_file).and_return("video_file")
          expect(video_rec_manager).to receive(:create_upload_request_for_video)
          expect(video_rec_manager).not_to receive(:push_error_to_eds)

          expect { video_rec_manager.stop_rec }.not_to raise_error
        end

        it "should push error to eds if wda as well as xcuitest automation fails and we were not able to successfully fetch file after xcuitest" do
          new_params = params.clone
          new_params["video_params_v2"] = v2_params.to_json
          device_state = double
          expect(DeviceState).to receive(:new).and_return(device_state).twice
          video_rec_manager = VideoRecManager.new(device, new_params, 8400)

          expect(device_state).to receive(:fallback_to_v1_file_present?).and_return(false)
          expect(video_rec_manager).to receive(:trigger_video_recording).with("stop").and_return([failed_wda_response, true])
          expect(video_rec_manager).to receive(:fetch_v2_video_file).and_return(nil)
          expect(BrowserStackAppHelper).to receive(:check_and_install_browserstack_apps)
          expect(BrowserStackAppHelper).to receive(:run_ui_test).and_raise(BrowserStackTestExecutionError.new('test_class', 'test_func'))
          expect(video_rec_manager).to receive(:fetch_v2_video_file).and_return(nil)
          expect(video_rec_manager).not_to receive(:create_upload_request_for_video)
          expect(video_rec_manager).to receive(:push_error_to_eds)
          expect(device_state).to receive(:touch_launcher_photos_permission_file)
          expect(device_state).to receive(:touch_bs_launcher_install_file)

          expect { video_rec_manager.stop_rec }.not_to raise_error
        end

        it "should push reason to eds that v2 recording (wda+xcuitest) stopped but could not find the file" do
          new_params = params.clone
          new_params["video_params_v2"] = v2_params.to_json
          device_state = double
          expect(DeviceState).to receive(:new).and_return(device_state).twice
          video_rec_manager = VideoRecManager.new(device, new_params, 8400)

          expect(device_state).to receive(:fallback_to_v1_file_present?).and_return(false)
          expect(video_rec_manager).to receive(:trigger_video_recording).with("stop").and_return([failed_wda_response, true])
          expect(video_rec_manager).to receive(:fetch_v2_video_file).and_return(nil)
          expect(BrowserStackAppHelper).to receive(:check_and_install_browserstack_apps)
          expect(BrowserStackAppHelper).to receive(:run_ui_test).and_return("random")
          expect(video_rec_manager).to receive(:fetch_v2_video_file).and_return(nil)
          expect(video_rec_manager).not_to receive(:create_upload_request_for_video)
          expect(video_rec_manager).to receive(:push_error_to_eds)
          expect(device_state).to receive(:touch_launcher_photos_permission_file)
          expect(device_state).to receive(:touch_bs_launcher_install_file)

          expect { video_rec_manager.stop_rec }.not_to raise_error
        end

        it "should push reason to eds if wda automation is successful but fetching file failed" do
          new_params = params.clone
          new_params["video_params_v2"] = v2_params.to_json
          device_state = double
          expect(DeviceState).to receive(:new).and_return(device_state).twice
          video_rec_manager = VideoRecManager.new(device, new_params, 8400)

          expect(device_state).to receive(:fallback_to_v1_file_present?).and_return(false)
          expect(video_rec_manager).to receive(:trigger_video_recording).with("stop").and_return([success_wda_response, false])
          expect(video_rec_manager).to receive(:fetch_v2_video_file).and_return(nil)
          expect(video_rec_manager).to receive(:push_error_to_eds)
          expect(device_state).to receive(:touch_launcher_photos_permission_file)
          expect(video_rec_manager).to receive(:queue_device_for_reboot)

          expect { video_rec_manager.stop_rec }.not_to raise_error
        end

        it "should push reason to eds that v2 recording did not stop due to standard error" do
          new_params = params.clone
          new_params["video_params_v2"] = v2_params.to_json
          video_rec_manager = VideoRecManager.new(device, new_params, 8400)

          expect_any_instance_of(DeviceState).to receive(:fallback_to_v1_file_present?).and_return(false)
          expect(video_rec_manager).to receive(:trigger_video_recording).with("stop").and_return(["WDA timed out", true])
          expect(video_rec_manager).to receive(:fetch_v2_video_file).and_return(nil)
          expect(BrowserStackAppHelper).to receive(:check_and_install_browserstack_apps)
          expect(BrowserStackAppHelper).to receive(:run_ui_test).and_raise(BrowserStackTestExecutionError.new('test_class', 'test_func'))
          expect(video_rec_manager).to receive(:fetch_v2_video_file).and_return(nil)
          expect(video_rec_manager).not_to receive(:create_upload_request_for_video)
          expect(video_rec_manager).to receive(:push_error_to_eds)

          expect { video_rec_manager.stop_rec }.not_to raise_error
        end
      end
    end

    context "#v1 video recording" do
      it "should stop v1 recording with success" do
        video_rec_manager = VideoRecManager.new(device, params, 8400)

        ios_vid_capturer = double
        expect_any_instance_of(DeviceState).to receive(:fallback_to_v1_file_present?).and_return(true)
        expect(IosVidCapturer).to receive(:new).and_return(ios_vid_capturer)
        expect(ios_vid_capturer).to receive(:stop)
        expect(ios_vid_capturer).to receive(:upload)

        expect { video_rec_manager.stop_rec }.not_to raise_error
      end

      it "should fallback to v1 if file is present" do
        new_params = params.clone
        new_params["video_params_v2"] = v2_params.to_json
        video_rec_manager = VideoRecManager.new(device, new_params, 8400)
        ios_vid_capturer = double

        expect_any_instance_of(DeviceState).to receive(:fallback_to_v1_file_present?).and_return(true)
        expect(IosVidCapturer).to receive(:new).and_return(ios_vid_capturer)
        expect(ios_vid_capturer).to receive(:stop)
        expect(ios_vid_capturer).to receive(:upload)

        expect { video_rec_manager.stop_rec }.not_to raise_error
      end
    end
  end

  describe "#trigger_video_recording" do
    context "#v2 video recording" do
      it "should raise error if wda is not running" do
        new_params = params.clone
        new_params["video_params_v2"] = v2_params.to_json
        video_rec_manager = VideoRecManager.new(device, new_params, 8400)
        allow(Utils).to receive(:is_wda_kill_flag_enabled?).and_return(true)
        result = video_rec_manager.trigger_video_recording("start")
        expect(result).to eq(["WDA not running", false])
      end

      it "should successfully start automation and close iproxy properly" do
        new_params = params.clone
        new_params["video_params_v2"] = v2_params.to_json
        video_rec_manager = VideoRecManager.new(device, new_params, 8400)
        wda_client = double

        expect(PortManager).to receive(:stop_forwarding_port).with(device, 8400 + 1700, 'video')
        expect(PortManager).to receive(:forward_port).with(device, 8400 + 1700, 8400, 'video')
        expect(WdaClient).to receive(:new).with(8400 + 1700, device).and_return(wda_client)
        expect(wda_client).to receive(:start_replay_kit_recording).and_return(success_wda_response)
        expect(BrowserStack::Zombie).to receive(:push_logs)
        expect(PortManager).to receive(:stop_forwarding_port).with(device, 8400 + 1700, 'video')

        result = video_rec_manager.trigger_video_recording("start")
        expect(result).to eq([success_wda_response, false])
      end

      it "should successfully start automation with performance flag enabled and close iproxy properly" do
        new_params = params.clone
        new_params["video_params_v2"] = v2_performance_params.to_json
        video_rec_manager = VideoRecManager.new(device, new_params, 8400)
        wda_client = double

        expect(PortManager).to receive(:stop_forwarding_port).with(device, 8400 + 1700, 'video')
        expect(PortManager).to receive(:forward_port).with(device, 8400 + 1700, 8400, 'video')
        expect(WdaClient).to receive(:new).with(8400 + 1700, device).and_return(wda_client)
        expect(wda_client).to receive(:start_replay_kit_recording).with(true).and_return(success_wda_response)
        expect(BrowserStack::Zombie).to receive(:push_logs)
        expect(PortManager).to receive(:stop_forwarding_port).with(device, 8400 + 1700, 'video')

        result = video_rec_manager.trigger_video_recording("start")
        expect(result).to eq([success_wda_response, false])
      end

      it "should successfully stop automation and close iproxy properly" do
        new_params = params.clone
        new_params["video_params_v2"] = v2_params.to_json
        video_rec_manager = VideoRecManager.new(device, new_params, 8400)
        wda_client = double

        expect(PortManager).to receive(:stop_forwarding_port).with(device, 8400 + 1700, 'video')
        expect(PortManager).to receive(:forward_port).with(device, 8400 + 1700, 8400, 'video')
        expect(WdaClient).to receive(:new).with(8400 + 1700, device).and_return(wda_client)
        expect(wda_client).to receive(:stop_replay_kit_recording).and_return(success_wda_response)
        expect(BrowserStack::Zombie).to receive(:push_logs)
        expect(PortManager).to receive(:stop_forwarding_port).with(device, 8400 + 1700, 'video')

        result = video_rec_manager.trigger_video_recording("stop")
        expect(result).to eq([success_wda_response, false])
      end

      it "should successfully stop automation with performance flag enabled and close iproxy properly" do
        new_params = params.clone
        new_params["video_params_v2"] = v2_performance_params.to_json
        video_rec_manager = VideoRecManager.new(device, new_params, 8400)
        wda_client = double

        expect(PortManager).to receive(:stop_forwarding_port).with(device, 8400 + 1700, 'video')
        expect(PortManager).to receive(:forward_port).with(device, 8400 + 1700, 8400, 'video')
        expect(WdaClient).to receive(:new).with(8400 + 1700, device).and_return(wda_client)
        expect(wda_client).to receive(:stop_replay_kit_recording).with(true, false).and_return(success_wda_response)
        expect(BrowserStack::Zombie).to receive(:push_logs)
        expect(PortManager).to receive(:stop_forwarding_port).with(device, 8400 + 1700, 'video')

        result = video_rec_manager.trigger_video_recording("stop")
        expect(result).to eq([success_wda_response, false])
      end

      it "should close iproxy properly and push error as false and failed wda response on automation failure" do
        new_params = params.clone
        new_params["video_params_v2"] = v2_params.to_json
        video_rec_manager = VideoRecManager.new(device, new_params, 8400)
        wda_client = double

        expect(PortManager).to receive(:stop_forwarding_port).with(device, 8400 + 1700, 'video')
        expect(PortManager).to receive(:forward_port).with(device, 8400 + 1700, 8400, 'video')
        expect(WdaClient).to receive(:new).with(8400 + 1700, device).and_return(wda_client)
        expect(wda_client).to receive(:start_replay_kit_recording).and_return(failed_wda_response)
        expect(BrowserStack::Zombie).to receive(:push_logs)
        expect(PortManager).to receive(:stop_forwarding_port).with(device, 8400 + 1700, 'video')

        result = video_rec_manager.trigger_video_recording("start")
        expect(result).to eq([failed_wda_response, false])
      end

      it "should close iproxy properly and push error as true and when wda request times out" do
        new_params = params.clone
        new_params["video_params_v2"] = v2_params.to_json
        video_rec_manager = VideoRecManager.new(device, new_params, 8400)
        wda_client = double

        expect(PortManager).to receive(:stop_forwarding_port).with(device, 8400 + 1700, 'video')
        expect(PortManager).to receive(:forward_port).with(device, 8400 + 1700, 8400, 'video')
        expect(WdaClient).to receive(:new).with(8400 + 1700, device).and_return(wda_client)
        expect(wda_client).to receive(:start_replay_kit_recording).and_raise(TimeoutError)
        expect(BrowserStack::Zombie).to receive(:push_logs)
        expect(PortManager).to receive(:stop_forwarding_port).with(device, 8400 + 1700, 'video')

        result = video_rec_manager.trigger_video_recording("start")
        expect(result).to eq(["WDA command timed out", true])
      end

      it "should close iproxy properly and push error as true and when port forwarding throws standard error" do
        new_params = params.clone
        new_params["video_params_v2"] = v2_params.to_json
        video_rec_manager = VideoRecManager.new(device, new_params, 8400)

        expect(PortManager).to receive(:stop_forwarding_port).with(device, 8400 + 1700, 'video')
        expect(PortManager).to receive(:forward_port).and_raise("Port forwarding not possible")
        expect(BrowserStack::Zombie).to receive(:push_logs)
        expect(PortManager).to receive(:stop_forwarding_port).with(device, 8400 + 1700, 'video')

        result = video_rec_manager.trigger_video_recording("start")
        expect(result).to eq(["Port forwarding not possible", true])
      end
    end
  end

  describe '#fetch_v2_video_file' do
    let(:session_id) { 'sample_session_id' }
    let(:config) { { 'video_recording_workspace' => '/tmp/workspace/' } }
    let(:device_video_files) { ['/path/to/video1.MP4', '/path/to/video2.MP4'] }
    let(:workspace) { '/tmp/workspace/' }
    let(:uuid) { 'random-uuid' }
    let(:video_manager) { VideoRecManager.new(device, params) }

    before do
      # Initialize necessary instance variables
      video_manager.instance_variable_set(:@session_id, session_id)
      #.instance_variable_set(:@conf, config)

      allow(SecureRandom).to receive(:uuid).and_return(uuid)
      allow(File).to receive(:exists?).and_return(true)
      allow(FileUtils).to receive(:cp)
      allow(FileUtils).to receive(:rm)
      allow(BrowserStack::OSUtils).to receive(:execute).and_return('')
    end

    it 'should return video file path when successfully fetched' do
      output_file = "#{workspace}some_uuid.MP4"
      allow(video_manager).to receive(:correct_video_file_fetched?).and_return([output_file, false])
      result = video_manager.fetch_v2_video_file
      expect(result).to eq(output_file)
    end

    it 'should return nil when an error occurs during device mounting' do
      allow(BrowserStack::OSUtils).to receive(:execute).with(/ifuse/, anything).and_raise(OSUtilsError.new('Mount error'))
      result = video_manager.fetch_v2_video_file
      expect(result).to be_nil
    end

    it 'should return nil when an error occurs in VideoFileFetchException' do
      allow(video_manager).to receive(:correct_video_file_fetched?).and_raise(VideoFileFetchException.new('Video fetch error'))
      result = video_manager.fetch_v2_video_file
      expect(result).to be_nil
    end

    it 'should retry mounting once if OSUtilsError is raised the first time' do
      # First call raises error, second call succeeds
      call_count = 0
      allow(BrowserStack::OSUtils).to receive(:execute) do |cmd, _opts|
        if cmd.include?('ifuse') && call_count == 0
          call_count += 1
          raise OSUtilsError, 'Mount error'
        else
          ''
        end
      end

      video_manager.fetch_v2_video_file

      # Verify the unmount command was called for retry
      expect(BrowserStack::OSUtils).to have_received(:execute).with("sudo umount -f /Users/<USER>/#{device}", timeout: 10).at_least(:once)
    end

    it 'should retry mounting once if VideoFileFetchException is raised the first time' do
      call_count = 0
      allow(video_manager).to receive(:correct_video_file_fetched?) do
        if call_count == 0
          call_count += 1
          raise VideoFileFetchException, 'Video fetch error'
        else
          [nil, false]
        end
      end

      video_manager.fetch_v2_video_file

      # Verify the unmount command was called for retry
      expect(BrowserStack::OSUtils).to have_received(:execute).with("sudo umount -f /Users/<USER>/#{device}", timeout: 10).at_least(:once)
    end

    it 'should not retry more than twice for any error' do
      call_count = 0
      allow(BrowserStack::OSUtils).to receive(:execute) do |cmd, _opts|
        if cmd.include?('ifuse')
          call_count += 1
          raise OSUtilsError, 'Mount error'
        else
          ''
        end
      end

      video_manager.fetch_v2_video_file

      expect(call_count).to be <= 2
    end

    it 'should create device workspace directory if it does not exist' do
      video_manager.fetch_v2_video_file
      expect(BrowserStack::OSUtils).to have_received(:execute).with("sudo su -l #{USER} -c 'mkdir -p /Users/<USER>/#{device}'", timeout: 10).twice
    end

    it 'should unmount device if retry is triggered' do
      allow(BrowserStack::OSUtils).to receive(:execute).with(/ifuse/, anything).and_raise(OSUtilsError.new('Error'))
      allow(BrowserStack::OSUtils).to receive(:execute).with("sudo umount -f /Users/<USER>/#{device}", timeout: 10)
      video_manager.fetch_v2_video_file
      expect(BrowserStack::OSUtils).to have_received(:execute).with("sudo umount -f /Users/<USER>/#{device}", timeout: 10).at_least(:once)
    end
  end

  describe '#correct_video_file_fetched?' do
    let(:session_id) { 'sample_session_id' }
    let(:config) { { 'video_recording_workspace' => '/tmp/workspace/' } }
    let(:device_video_files) { ['/path/to/video1.MP4', '/path/to/video2.MP4'] }
    let(:workspace) { '/tmp/workspace/' }
    let(:uuid) { 'random-uuid' }
    let(:video_manager) { VideoRecManager.new(device, params) }

    before do
      # Initialize necessary instance variables
      video_manager.instance_variable_set(:@session_id, session_id)
      video_manager.instance_variable_set(:@conf, config)

      allow(SecureRandom).to receive(:uuid).and_return(uuid)
      allow(File).to receive(:exists?).and_return(true)
      allow(FileUtils).to receive(:cp)
      allow(FileUtils).to receive(:rm)
      allow(BrowserStack::OSUtils).to receive(:execute).and_return('')
    end

    it 'should return the correct video file path when exactly one file has 2 copyright lines' do
      allow(BrowserStack::OSUtils).to receive(:execute).with(/cat.*metadata/, anything).and_return('2', '0')
      expected_output_file = "#{workspace}#{uuid}.MP4"
      result, = video_manager.correct_video_file_fetched?(device_video_files, workspace)
      expect(result).to eq(expected_output_file)
    end

    it 'should raise VideoFileFetchException when multiple files have 2 copyright lines' do
      allow(BrowserStack::OSUtils).to receive(:execute).with(/cat.*metadata/, anything).and_return('2')
      expect do
        video_manager.correct_video_file_fetched?(device_video_files, workspace)
      end.to raise_error(VideoFileFetchException, "Incorrect Video File")
    end

    it 'should return exception when no files have 2 copyright lines' do
      allow(BrowserStack::OSUtils).to receive(:execute).with(/cat.*metadata/, anything).and_return('0')
      expect do
        video_manager.correct_video_file_fetched?(device_video_files, workspace)
      end.to raise_error(VideoFileFetchException, "Incorrect Video File")
    end

    it 'should handle and skip non-existent video files' do
      allow(File).to receive(:exists?).with(device_video_files[0]).and_return(false)
      allow(File).to receive(:exists?).with(device_video_files[1]).and_return(true)
      allow(BrowserStack::OSUtils).to receive(:execute).with(/cat.*metadata/, anything).and_return('2')
      result, = video_manager.correct_video_file_fetched?(device_video_files, workspace)
      expect(result).to eq("#{workspace}#{uuid}.MP4")
    end

    it 'should clean up temporary files if an exception occurs during processing' do
      temp_file = "#{workspace}#{uuid}.MP4"
      allow(FileUtils).to receive(:cp).and_raise(StandardError.new('Copy error'))
      allow(File).to receive(:exists?).with(temp_file).and_return(true)
      begin
        video_manager.correct_video_file_fetched?(device_video_files, workspace)
      rescue
        nil
      end
      expect(FileUtils).to have_received(:rm).with(temp_file).at_least(:once)
    end

    it 'should handle errors when copying the video file' do
      allow(FileUtils).to receive(:cp).and_raise(StandardError.new('Copy error'))
      expect do
        video_manager.correct_video_file_fetched?(device_video_files, workspace)
      end.to raise_error(VideoFileFetchException, "Incorrect Video File")
    end

    it 'should handle errors when running ffmpeg command' do
      allow(BrowserStack::OSUtils).to receive(:execute).with(/#{FFMPEG}/, anything).and_raise(StandardError.new('FFMPEG error'))
      expect do
        video_manager.correct_video_file_fetched?(device_video_files, workspace)
      end.to raise_error(VideoFileFetchException, "Incorrect Video File")
    end

    it 'should handle errors when reading metadata file' do
      allow(BrowserStack::OSUtils).to receive(:execute).with(/cat.*metadata/, anything).and_raise(StandardError.new('Read error'))
      expect do
        video_manager.correct_video_file_fetched?(device_video_files, workspace)
      end.to raise_error(VideoFileFetchException, "Incorrect Video File")
    end

    it 'should clean up the output file if no valid files are found' do
      output_file = "#{workspace}#{uuid}.MP4"
      allow(BrowserStack::OSUtils).to receive(:execute).with(/cat.*metadata/, anything).and_return('0')
      expect do
        video_manager.correct_video_file_fetched?(device_video_files, workspace)
      end.to raise_error(VideoFileFetchException, "Incorrect Video File")
      expect(FileUtils).to have_received(:rm).at_least(:once)
    end

    it 'should handle empty device_video_files array' do
      expect { video_manager.correct_video_file_fetched?([], workspace) }.to raise_error(VideoFileFetchException, "No video files found")
    end
  end

  describe 'fetch_v2_video_file edge cases' do
    let(:session_id) { 'sample_session_id' }
    let(:config) { { 'video_recording_workspace' => '/tmp/workspace/' } }
    let(:video_manager) { VideoRecManager.new(device, params) }

    before do
      stub_const('CONFIG_JSON_FILE', '/path/to/config.json')
      stub_const('USER', 'app')
      stub_const('IFUSE', '/path/to/ifuse')
      stub_const('FFMPEG', '/path/to/ffmpeg')
      stub_const('STATE_FILES_DIR', '/path/to/state_files')

      video_manager.instance_variable_set(:@session_id, session_id)
      video_manager.instance_variable_set(:@conf, config)

      allow(File).to receive(:read).with(CONFIG_JSON_FILE).and_return({ "devices" => { device => device_config } }.to_json)
      allow(BrowserStack::OSUtils).to receive(:execute).and_return('')
      allow(FileUtils).to receive(:rm_rf)
      allow(FileUtils).to receive(:mkdir_p)
      allow(FileUtils).to receive(:rm)
      allow(FileUtils).to receive(:touch)
      allow(BrowserStack::Zombie).to receive(:push_logs)
    end

    it 'should handle the case when the device directory is not accessible' do
      allow(BrowserStack::OSUtils).to receive(:execute).with(/mkdir/, anything).and_raise(OSUtilsError.new('Permission denied'))
      result = video_manager.fetch_v2_video_file
      expect(result).to be_nil
    end

    it 'should handle the situation when FFMPEG executable is not available' do
      allow(video_manager).to receive(:correct_video_file_fetched?) do |_args|
        allow(BrowserStack::OSUtils).to receive(:execute).with(/#{FFMPEG}/, anything).and_raise(OSUtilsError.new('Command not found'))
        [nil, false]
      end
      result = video_manager.fetch_v2_video_file
      expect(result).to be_nil
    end

    it 'should handle the situation when there is insufficient disk space for copying' do
      allow(video_manager).to receive(:correct_video_file_fetched?) do |_args|
        allow(FileUtils).to receive(:cp).and_raise(StandardError.new('No space left on device'))
        [nil, false]
      end
      result = video_manager.fetch_v2_video_file
      expect(result).to be_nil
    end

    it 'should handle the case when directory permissions prevent file operations' do
      allow(FileUtils).to receive(:mkdir_p).and_raise(Errno::EACCES.new('Permission denied'))
      result = video_manager.fetch_v2_video_file
      expect(result).to be_nil
    end

    it 'should handle device disconnection during file operations' do
      allow(Dir).to receive(:glob).with("/Users/<USER>/#{device}/DCIM/*/*").and_raise(Errno::ENOENT.new('No such file or directory'))
      result = video_manager.fetch_v2_video_file
      expect(result).to be_nil
    end
  end

  describe 'fetch_v2_video_file mocking verification' do
    let(:session_id) { 'sample_session_id' }
    let(:config) { { 'video_recording_workspace' => '/tmp/workspace/' } }
    let(:video_manager) { VideoRecManager.new(device, params) }

    before do
      stub_const('CONFIG_JSON_FILE', '/path/to/config.json')
      stub_const('USER', 'app')
      stub_const('IFUSE', '/path/to/ifuse')
      stub_const('FFMPEG', '/path/to/ffmpeg')
      stub_const('STATE_FILES_DIR', '/path/to/state_files')

      video_manager.instance_variable_set(:@session_id, session_id)
      video_manager.instance_variable_set(:@conf, config)
      video_manager.instance_variable_set(:@prefix_genre, 'app_automate_')

      allow(File).to receive(:read).with(CONFIG_JSON_FILE).and_return({ "devices" => { device => device_config } }.to_json)
      allow(BrowserStack::OSUtils).to receive(:execute).and_return('')
      allow(FileUtils).to receive(:rm_rf)
      allow(FileUtils).to receive(:mkdir_p)
      allow(FileUtils).to receive(:rm)
      allow(FileUtils).to receive(:touch)
      allow(Dir).to receive(:glob).and_return([])
      allow(BrowserStack::Zombie).to receive(:push_logs)
      allow(video_manager).to receive(:correct_video_file_fetched?).and_return([nil, false])
    end

    it 'should correctly mock OSUtils.execute calls with expected inputs and outputs' do
      expected_commands = [
        "sudo su -l #{USER} -c 'mkdir -p /Users/<USER>/#{device}'",
        "#{IFUSE} -o allow_other,umask=0000,default_permissions -u #{device} /Users/<USER>/#{device}",
        "sudo umount -f /Users/<USER>/#{device}"
      ]

      video_manager.fetch_v2_video_file

      expected_commands.each do |cmd|
        expect(BrowserStack::OSUtils).to have_received(:execute).with(cmd, timeout: 10)
      end
    end

    it 'should verify FileUtils operations are called with correct parameters' do
      workspace = "/tmp/workspace/#{device}/#{session_id}/"
      video_manager.fetch_v2_video_file
      expect(FileUtils).to have_received(:rm_rf).with(workspace)
      expect(FileUtils).to have_received(:mkdir_p).with(workspace)
      expect(FileUtils).to have_received(:touch).with("#{STATE_FILES_DIR}/#{device}_reinstall_preloaded_media")
    end

    it 'should mock File operations to test different file existence scenarios' do
      workspace = "/tmp/workspace/#{device}/#{session_id}/"
      metadata_path = "#{workspace}metadata"
      allow(File).to receive(:exists?).with(metadata_path).and_return(true)
      video_manager.fetch_v2_video_file
      expect(FileUtils).to have_received(:rm).with(metadata_path)
    end

    it 'should verify Zombie.push_logs is called with correct parameters' do
      video_manager.fetch_v2_video_file
      expect(BrowserStack::Zombie).to have_received(:push_logs).with(
        "app_automate_ios-video-v2-rec-fetch-time",
        anything,
        hash_including("session_id" => session_id)
      )
    end
  end

  describe "#create_upload_request_for_video" do
    context "v2 video recording" do
      it "should create video json upload request" do
        new_params = params.clone
        new_params["video_params_v2"] = v2_params.to_json
        video_rec_manager = VideoRecManager.new(device, new_params, 8400)

        expect(File).to receive(:dirname)
        expect(Dir).to receive(:exist?).and_return(true)
        expect(Utils).to receive(:write_to_file)

        expect { video_rec_manager.create_upload_request_for_video("abcd", params) }.not_to raise_error
      end
    end
  end

  describe "#validate_video_duration" do
    let(:video_file) { "/tmp/test_video.mp4" }
    let(:start_time_file) { "/tmp/video_rec_start_time_test_device.txt" }
    let(:video_manager) { VideoRecManager.new(device, params) }

    before do
      stub_const('FFPROBE', '/path/to/ffprobe')
      video_manager.instance_variable_set(:@start_time_file, start_time_file)
      video_manager.instance_variable_set(:@session_id, "test_session_id")
      video_manager.instance_variable_set(:@prefix_genre, "app_automate_")
      allow(File).to receive(:exist?).and_return(true)
      allow(BrowserStack.logger).to receive(:info)
      allow(BrowserStack.logger).to receive(:error)
      allow(BrowserStack.logger).to receive(:warn)
      allow(BrowserStack::Zombie).to receive(:push_logs)
      allow(FileUtils).to receive(:rm)
      allow(FileUtils).to receive(:delete)
      allow(File).to receive(:delete)
    end

    context "when video file does not exist" do
      it "should return false if video file is nil" do
        expect(video_manager.validate_video_duration(nil)).to be false
      end

      it "should return false if video file does not exist" do
        allow(File).to receive(:exist?).with(video_file).and_return(false)
        expect(video_manager.validate_video_duration(video_file)).to be false
      end
    end

    context "when video duration is within acceptable range" do
      before do
        rec_start_time = Time.now.to_f - 100
        allow(File).to receive(:read).with(start_time_file).and_return(rec_start_time.to_s)
        video_manager.instance_variable_set(:@rec_stop_time, Time.now.to_f)
        allow(BrowserStack::OSUtils).to receive(:execute).with(/#{FFPROBE}/, anything).and_return("95.5")
      end

      it "should return true when video duration is within tolerance" do
        expect(video_manager.validate_video_duration(video_file)).to be true
      end

      it "should log validation results" do
        video_manager.validate_video_duration(video_file)
        expect(BrowserStack.logger).to have_received(:info).with(/\[validate_video_duration\] Recording duration:/)
        expect(BrowserStack.logger).to have_received(:info).with(/\[validate_video_duration\] Valid range:.*Is valid: true/)
      end

      it "should push logs to monitoring with success status" do
        video_manager.validate_video_duration(video_file)
        expect(BrowserStack::Zombie).to have_received(:push_logs).with(
          "app_automate_ios-video-duration-validation",
          "Video duration valid",
          hash_including("session_id" => "test_session_id")
        )
      end

      it "should delete the start time file after validation" do
        video_manager.validate_video_duration(video_file)
        expect(File).to have_received(:delete).with(start_time_file)
      end
    end

    context "when video duration is outside acceptable range" do
      before do
        rec_start_time = Time.now.to_f - 100
        allow(File).to receive(:read).with(start_time_file).and_return(rec_start_time.to_s)
        video_manager.instance_variable_set(:@rec_stop_time, Time.now.to_f)
        allow(BrowserStack::OSUtils).to receive(:execute).with(/#{FFPROBE}/, anything).and_return("50.0")
      end

      it "should return false when video duration is below tolerance" do
        expect(video_manager.validate_video_duration(video_file)).to be false
      end

      it "should push logs to monitoring with failure status" do
        video_manager.validate_video_duration(video_file)
        expect(BrowserStack::Zombie).to have_received(:push_logs).with(
          "app_automate_ios-video-duration-validation",
          "Video duration invalid",
          hash_including("session_id" => "test_session_id")
        )
      end
    end

    context "when ffprobe execution fails" do
      before do
        rec_start_time = Time.now.to_f - 100
        allow(File).to receive(:read).with(start_time_file).and_return(rec_start_time.to_s)
        video_manager.instance_variable_set(:@rec_stop_time, Time.now.to_f)
        allow(BrowserStack::OSUtils).to receive(:execute).with(/#{FFPROBE}/, anything).and_raise(StandardError.new("ffprobe failed"))
      end

      it "should return false when ffprobe execution fails" do
        expect(video_manager.validate_video_duration(video_file)).to be false
      end

      it "should log the error" do
        video_manager.validate_video_duration(video_file)
        expect(BrowserStack.logger).to have_received(:error).with(/\[validate_video_duration\] Error: ffprobe failed/)
      end
    end

    context "when video duration is at the boundary of acceptable range" do
      before do
        rec_start_time = Time.now.to_f - 100
        allow(File).to receive(:read).with(start_time_file).and_return(rec_start_time.to_s)
        video_manager.instance_variable_set(:@rec_stop_time, Time.now.to_f)
      end

      it "should return true when video duration is at upper boundary" do
        allow(BrowserStack::OSUtils).to receive(:execute).with(/#{FFPROBE}/, anything).and_return("120.0")
        expect(video_manager.validate_video_duration(video_file)).to be true
      end
    end
  end
end
