require 'timecop'
require_relative '../../spec_helper'
require_relative '../../../lib/helpers/passcode_helper'

describe Secure::Passcode do
  let(:device_config) { { 'webdriver_port' => 8080, "device_name" => 'iPhone', "device_version" => '14.0' } }
  let(:uuid) { '000820-427503425' }
  let(:product) { 'app-live' }
  let(:feature) { '' }
  let(:session_id) { '1' }
  let(:ios_version) { '14.0' }
  let(:event_name) { 'web_events' }
  let(:start_time) { 100 }
  let(:end_time) { 100 }
  let(:passcode) { '123456' }
  let(:plist) { 'L3RtcC90ZW1wLm1vYmlsZWNvbmZpZw==' }

  before do
    allow(BrowserStack::Zombie).to receive(:configure)
    allow(BrowserStack::Zombie).to receive(:push_logs)

    allow(File).to receive(:read).and_return("")
    allow(JSON).to receive(:parse).and_return({ "devices" => { uuid => device_config } })

    Timecop.freeze(Time.at(start_time))
  end

  after do
    Timecop.return
  end

  def event_logger(mode, kind, status, error, message)
    if error == "unknown error"
      expect_any_instance_of(WdaClient).to receive(:set_passcode).with(passcode).and_return({})
    else
      expect_any_instance_of(WdaClient).to receive(:set_passcode).with(passcode).and_return({ 'value' => { 'error' => error,
                                                                                                           'message' => message } })
    end
    expect(Utils).to receive(:send_to_eds).with( {
      event_name: "passcode-state-change",
      product: product,
      os: "ios",
      os_version: ios_version,
      team: "device_features",
      feature: '',
      event_json: {
        session_id: session_id,
        passcode_state_change_to: mode,
        status: status,
        error_reason: error,
        error_message: message,
        time_taken: end_time - start_time
      }
    }, event_name, true )
    if status == "fail"
      expect(BrowserStack::Zombie).to receive(:push_logs).with("passcode-state-change",
                                                               kind,
                                                               { "device" => uuid,
                                                                 "product" => product,
                                                                 "session_id" => session_id,
                                                                 "os_version" => ios_version,
                                                                 "data" => message,
                                                                 "url" => mode,
                                                                 "error" => error })
    end

  end

  describe "passcode_settings" do
    it 'should successfully apply mdm restrictions' do
      expect_any_instance_of(ConfigurationProfilesManager).to receive(:install_profile).and_return(true)
      expect(Utils).to receive(:send_to_eds).with( {
        event_name: "passcode-state-change",
        product: product,
        os: "ios",
        os_version: ios_version,
        team: "device_features",
        feature: '',
        event_json: {
          session_id: session_id,
          passcode_state_change_to: "On",
          status: "pass",
          time_taken: end_time - start_time
        }
      }, event_name, true )
      expect_any_instance_of(DeviceState).to receive(:touch_force_install_mdm_profiles_file)
      subject = Secure::Passcode.new(uuid, session_id, product)
      subject.passcode_settings(REDIS_CLIENT)
    end

    it 'should fail to update restrictions profile' do
      expect_any_instance_of(ConfigurationProfilesManager).to receive(:install_profile).and_raise(StandardError.new('some error'))
      expect(BrowserStack::Zombie).to receive(:push_logs).with("passcode-state-change",
                                                               "mdm-passcode-settings",
                                                               { "device" => uuid,
                                                                 "product" => product,
                                                                 "session_id" => session_id,
                                                                 "os_version" => ios_version,
                                                                 "data" => "some error",
                                                                 "url" => "On",
                                                                 "error" => "mdm-command-execution-error" })
      expect_any_instance_of(DeviceState).to receive(:touch_force_install_mdm_profiles_file)
      subject = Secure::Passcode.new(uuid, session_id, product)
      subject.passcode_settings(REDIS_CLIENT)
    end

    it 'should fail to apply restrictions profile' do
      expect_any_instance_of(ConfigurationProfilesManager).to receive(:install_profile).and_raise(StandardError.new('some error'))
      expect(BrowserStack::Zombie).to receive(:push_logs).with("passcode-state-change",
                                                               "mdm-passcode-settings",
                                                               { "device" => uuid,
                                                                 "product" => product,
                                                                 "session_id" => session_id,
                                                                 "os_version" => ios_version,
                                                                 "data" => "some error",
                                                                 "url" => "On",
                                                                 "error" => "mdm-command-execution-error" })
      expect_any_instance_of(DeviceState).to receive(:touch_force_install_mdm_profiles_file)
      subject = Secure::Passcode.new(uuid, session_id, product)
      subject.passcode_settings(REDIS_CLIENT)
    end
  end

  describe "set passcode" do
    # The below return error and messages are received from WDA client
    it 'should raise log event to eds and zombie when test fails with unknown error' do
      event_logger("On", "wda-automation", "pass", "no error", "Successfully set passcode on the device.")
      expect_any_instance_of(DeviceState).to receive(:touch_passcode_file)
      subject = Secure::Passcode.new(uuid, session_id, product)
      expect(subject.set_passcode).to eql(true)
    end

    it 'should raise log event to eds and zombie when test fails with invalid passcode' do
      event_logger("On", "wda-automation", "fail", "invalid passcode", "passcode must be exactly of 6 characters and numeric.")
      expect_any_instance_of(DeviceState).to receive(:touch_passcode_file)
      subject = Secure::Passcode.new(uuid, session_id, product)
      expect(subject.set_passcode).to eql(false)
    end

    it 'should raise log event to eds and zombie when test fails with no such element' do
      event_logger("On", "wda-automation", "fail", "no such element", "Turn Passcode On option could not be located.")
      expect_any_instance_of(DeviceState).to receive(:touch_passcode_file)
      subject = Secure::Passcode.new(uuid, session_id, product)
      expect(subject.set_passcode).to eql(false)
    end

    it 'should raise log event to eds and zombie when test fails with no such element timeout on validating passcode being set or not' do
      event_logger("On", "wda-automation", "fail", "timeout - app killed prematurely", "passcode might have been set to #{passcode}")
      expect_any_instance_of(DeviceState).to receive(:touch_passcode_file)
      subject = Secure::Passcode.new(uuid, session_id, product)
      expect(subject.set_passcode).to eql(false)
    end
  end

  describe "clear passcode" do
    it "should clear the passcode using mdm command" do
      expect(BrowserStack::IosMdmServiceClient).to receive(:clear_passcode).with(uuid, REDIS_CLIENT).and_return(true)
      expect_any_instance_of(DeviceState).to receive(:remove_passcode_file)
      subject = Secure::Passcode.new(uuid, session_id, product)
      subject.clear_passcode(REDIS_CLIENT)
    end
  end
end


