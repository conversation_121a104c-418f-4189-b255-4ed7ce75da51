require 'timecop'
require_relative '../../spec_helper'
require_relative '../../helpers/mock_logger'
require_relative '../../../lib/helpers/sms_helper'

describe SMSHelper do
  let(:device_config) { { 'webdriver_port' => 8080, "device_version" => '14.0' } }
  let(:uuid) { '000820-427503425' }
  let(:product) { 'app-live' }
  let(:session_id) { '1' }
  let(:ios_version) { '14.0' }
  let(:event_name) { 'web_events' }
  let(:start_time) { 100 }
  let(:end_time) { 100 }

  before do
    logger = MockLogger.new
    allow(BrowserStack).to receive(:logger).and_return(MockLogger.new)

    expect(BrowserStack::Zombie).to receive(:configure)
    expect(File).to receive(:read).at_least(:once).and_return("")
    expect(JSON).to receive(:parse).at_least(:once).and_return("devices" => { uuid => device_config })

    Timecop.freeze(Time.at(start_time))
    @testObject = SMSHelper.new(uuid, session_id, product)
  end

  after do
    Timecop.return
    @testObject
  end

  def prepare_data(error_reason, error_message)
    data = if error_reason == ""
             { status: "success",
               time_taken: end_time - start_time }
           else
             { status: "failure",
               error_reason: error_reason,
               error_message: error_message,
               time_taken: end_time - start_time }
           end
    data_to_push = { event_name: "sms-cleanup",
                     product: product,
                     os: "ios",
                     os_version: ios_version,
                     team: "device_features" }
    event_json = { session_id: session_id }
    event_json.merge!(data)
    data_to_push.merge!({ event_json: event_json })
    data_to_push
  end

  def prepare_test_output(error_substring)
    <<~HEREDOC
      2022-02-21 18:47:03.780 BrowserStackUITests-Runner[1155:180896] Running tests... \
      Test Suite 'Selected tests' started at 2022-02-21 18:47:03.845 \
      Test Suite 'BrowserStackUITests.xctest' started at 2022-02-21 18:47:03.845 \
      Test Suite 'MessagesUITests' started at 2022-02-21 18:47:03.846
      Test Case '-[BrowserStackUITests.MessagesUITests testMessagesAppCleanup]' started.
      t = 0.00s Start Test at 2022-02-21 18:47:03.846
      t = 0.04s Set Up
      t = 0.04s Set device orientation to Portrait
      t = 0.60s Open com.apple.MobileSMS
      t = 0.67s Launch com.apple.MobileSMS
      t = 0.77s Wait for com.apple.MobileSMS to idle
      t = 1.87s Terminate com.apple.MobileSMS:1158
      t = 3.00s Open com.apple.MobileSMS
      t = 3.07s Launch com.apple.MobileSMS
      t = 3.16s Wait for com.apple.MobileSMS to idle
      t = 4.29s Assertion Failure: MessagesUITests.swift:36: XCTAssertFalse failed - #{error_substring}
      t = 4.35s Tear Down
      t = 4.35s Snapshot accessibility hierarchy for app with pid 1160
      Failing tests:
      MessagesUITests.testMessagesAppCleanup()
      ** TEST EXECUTE FAILED **
    HEREDOC
  end

  def expectations_from_sms_cleanup(error_type, error_reason, error_message)
    expect(BrowserStackAppHelper).to receive(:check_and_install_browserstack_test_suite).with(uuid)
    if error_type != ""
      expect(BrowserStack::Zombie).to receive(:push_logs).with("sms-cleanup-failure",
                                                               error_type,
                                                               { "device" => uuid,
                                                                 "product" => product,
                                                                 "session_id" => session_id,
                                                                 "os_version" => ios_version,
                                                                 "error_reason" => error_reason,
                                                                 "error_message" => error_message })
    end

    data_to_push = prepare_data(error_reason, error_message)
    expect(Utils).to receive(:send_to_eds).with(data_to_push, event_name, true)
  end

  context '# test sms_cleanup method' do
    it 'when run_ui_test does not raises error' do
      allow(BrowserStackAppHelper).to receive(:run_ui_test).with(uuid, :sms_cleanup, session_id: session_id).and_return(["Delete button tapped"])

      expect_any_instance_of(DeviceState).to receive(:touch_clean_sms_file)
      expectations_from_sms_cleanup("", "", "")
      expect(@testObject.sms_cleanup).to eq(true)
    end

    it 'when run_ui_test raises BrowserStackTestExecutionError containing pre-defined "ElementNotFound, Unable to find Edit Button - May be App UI got changed" message as substring' do
      error_type = "BrowserStackTestExecutionError"
      error_reason = "ElementNotFound"
      error_message = "Unable to find Edit Button - May be App UI got changed"
      allow(BrowserStackAppHelper).to receive(:run_ui_test)
        .with(uuid, :sms_cleanup, session_id: session_id)
        .and_raise(BrowserStackTestExecutionError.new('test_class', 'test_func', prepare_test_output("#{error_reason}, #{error_message}")))

      expectations_from_sms_cleanup(error_type, error_reason, error_message)
      @testObject.sms_cleanup
    end

    it 'when run_ui_test raises BrowserStackTestExecutionError containing pre-defined "ElementNotFound, Unable to find messagesTable or collectionView - may be App UI got changed" message' do
      error_type = "BrowserStackTestExecutionError"
      error_reason = "ElementNotFound"
      error_message = "Unable to find messagesTable or collectionView - may be App UI got changed"
      allow(BrowserStackAppHelper).to receive(:run_ui_test)
        .with(uuid, :sms_cleanup, session_id: session_id)
        .and_raise(BrowserStackTestExecutionError.new('test_class', 'test_func', prepare_test_output("#{error_reason}, #{error_message}")))

      expectations_from_sms_cleanup(error_type, error_reason, error_message)
      @testObject.sms_cleanup
    end

    it 'when run_ui_test raises BrowserStackTestExecutionError containing pre-defined "ElementNotFound, Unable to find Select Messages Button - may be App UI got changed" message' do
      error_type = "BrowserStackTestExecutionError"
      error_reason = "ElementNotFound"
      error_message = "Unable to find Select Messages Button - may be App UI got changed"
      allow(BrowserStackAppHelper).to receive(:run_ui_test)
        .with(uuid, :sms_cleanup, session_id: session_id)
        .and_raise(BrowserStackTestExecutionError.new('test_class', 'test_func', prepare_test_output("#{error_reason}, #{error_message}")))

      expectations_from_sms_cleanup(error_type, error_reason, error_message)
      @testObject.sms_cleanup
    end

    it 'when run_ui_test raises BrowserStackTestExecutionError containing pre-defined "ElementNotFound, Unable to find Delete Button - may be App UI got changed" message' do
      error_type = "BrowserStackTestExecutionError"
      error_reason = "ElementNotFound"
      error_message = "Unable to find Delete Button - may be App UI got changed"
      allow(BrowserStackAppHelper).to receive(:run_ui_test)
        .with(uuid, :sms_cleanup, session_id: session_id)
        .and_raise(BrowserStackTestExecutionError.new('test_class', 'test_func', prepare_test_output("#{error_reason}, #{error_message}")))

      expectations_from_sms_cleanup(error_type, error_reason, error_message)
      @testObject.sms_cleanup
    end

    it 'when run_ui_test raises BrowserStackTestExecutionError containing pre-defined "ElementNotFound, Unable to find confirmation Button for deletion - may be App UI got changed" message' do
      error_type = "BrowserStackTestExecutionError"
      error_reason = "ElementNotFound"
      error_message = "Unable to find confirmation Button for deletion - may be App UI got changed"
      allow(BrowserStackAppHelper).to receive(:run_ui_test)
        .with(uuid, :sms_cleanup, session_id: session_id)
        .and_raise(BrowserStackTestExecutionError.new('test_class', 'test_func', prepare_test_output("#{error_reason}, #{error_message}")))

      expectations_from_sms_cleanup(error_type, error_reason, error_message)
      @testObject.sms_cleanup
    end

    it 'when run_ui_test raises BrowserStackTestExecutionError containing non-pre-defined "ERROR ERROR ERROR" message' do
      error_type = "BrowserStackTestExecutionError"
      error_reason = "UnknownError"
      error_message = "ERROR ERROR ERROR"
      allow(BrowserStackAppHelper).to receive(:run_ui_test)
        .with(uuid, :sms_cleanup, session_id: session_id)
        .and_raise(BrowserStackTestExecutionError.new('test_class', 'test_func', "ERROR ERROR ERROR"))

      expectations_from_sms_cleanup(error_type, error_reason, error_message)
      @testObject.sms_cleanup
    end

    it 'when run_ui_test raises StandardError with "ERROR ERROR ERROR" message' do
      error_type = "StandardError"
      error_reason = "UnknownError"
      error_message = "ERROR ERROR ERROR"
      allow(BrowserStackAppHelper).to receive(:run_ui_test).with(uuid, :sms_cleanup, session_id: session_id).and_raise(StandardError.new('ERROR ERROR ERROR'))

      expectations_from_sms_cleanup(error_type, error_reason, error_message)
      @testObject.sms_cleanup
    end
  end
end
