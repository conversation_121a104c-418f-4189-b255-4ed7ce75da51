require 'spec_helper'
require_relative '../../../lib/helpers/apple_business_manager_helper'

RSpec.describe BrowserStack::AppleBusinessManagerHelper do
  let(:device_config) do
    {
      "device_version" => 17.0,
      "device_serial" => "abcd",
      "device_name" => "iPhone14,7",
      "mobileprovision_branch" => "production_109_25112024"
    }
  end

  before do
    allow(DeviceState).to receive(:new).and_return(nil) # Stub DeviceState.new
    allow(BrowserStack::Zombie).to receive(:configure) # Stub Zombie.configure
    allow(IosMdmServiceClient).to receive(:configure) # Stub IosMdmServiceClient.configure
    allow_any_instance_of(AppleBusinessManagerHelper).to receive(:device_config).and_return(device_config) # Stub IosMdmServiceClient.configure
  end

  describe '#initialize' do
    it 'initializes the helper object with correct attributes' do
      helper = BrowserStack::AppleBusinessManagerHelper.new("device_id", "app_name")

      expect(helper.instance_variable_get(:@uuid)).to eq("device_id")
      expect(helper.instance_variable_get(:@app_name)).to eq("app_name")
      expect(helper.instance_variable_get(:@device_id)).to eq("device_id")
      expect(helper.instance_variable_get(:@device_config)).to eq(device_config)
    end
  end

  describe '#device_config' do
    context 'when config file exists and device is present' do
      it 'returns the device configuration' do
        config = { "devices" => { "device_id" => device_config } }
        allow_any_instance_of(BrowserStack::Configuration).to receive_message_chain(:new, :all).and_return({ 'config_json_file' => 'config_file_path' })
        allow(File).to receive(:read).with('config_file_path').and_return(config.to_json)

        helper = BrowserStack::AppleBusinessManagerHelper.new("device_id", "app_name")

        expect(helper.device_config).to eq(config["devices"]["device_id"])
      end
    end
  end

  describe '#device_eligible?' do
    context 'when app is already installed, VPP token is present, region is "ap-south-1", iOS version is >= 17, and device name is "iPhone14,7"' do
      it 'returns true' do
        stub_const('VPP_TOKEN', 'mocked_vpp_token')
        allow_any_instance_of(BrowserStack::AppleBusinessManagerHelper).to receive(:app_installed_with_retries?).with(1).and_return(false)
        allow(BrowserStack::AppleBusinessManagerHelper).to receive(:logger).and_return(double('logger', info: nil)) # Stub logger

        helper = BrowserStack::AppleBusinessManagerHelper.new("device_id", "app_name")

        expect(helper.device_eligible?("ap-south-1")).to eq(true)
      end
    end

    context 'when app is not installed' do
      it 'returns false' do
        allow_any_instance_of(BrowserStack::AppleBusinessManagerHelper).to receive(:app_installed_with_retries?).with(1).and_return(true)
        allow(BrowserStack::AppleBusinessManagerHelper).to receive(:logger).and_return(double('logger', info: nil)) # Stub logger

        helper = BrowserStack::AppleBusinessManagerHelper.new("device_id", "app_name")

        expect(helper.device_eligible?("ap-south-1")).to eq(false)
      end
    end
  end

  describe '#send_mdm_install_app_request' do
    let(:device_id) { "device_id" }
    let(:app_name) { "testflight" }
    let(:helper) { BrowserStack::AppleBusinessManagerHelper.new(device_id, app_name) }

    context "when the installation request is successful" do
      before do
        allow(BrowserStack::AppleBusinessManagerHelper).to receive(:logger).and_return(double('logger', info: nil)) # Stub logger
        allow(IosMdmServiceClient).to receive(:send).and_return("installation_successful_response")
        allow(APP_DETAILS).to receive(:[]).and_return({ itunes_store_id: "123" })
        allow(REDIS_CLIENT).to receive(:anything) # Stub REDIS_CLIENT
      end

      it "returns the installation response" do
        expect(helper.send_mdm_install_app_request).to eq("installation_successful_response")
      end
    end

    context "when an error occurs during installation" do
      before do
        allow(BrowserStack::AppleBusinessManagerHelper).to receive(:logger).and_return(double('logger', info: nil)) # Stub logger
        allow(IosMdmServiceClient).to receive(:send).and_raise("Installation failed")
      end

      it "logs the error and returns the error message" do
        expect(helper.send_mdm_install_app_request).to eq("Installation failed")
      end
    end
  end

  describe '#assign_license_to_device' do
    let(:device_id) { "device_id" }
    let(:app_name) { "app_name" }
    let(:helper) { BrowserStack::AppleBusinessManagerHelper.new(device_id, app_name) }

    context "when license assignment is successful" do
      before do
        allow(BrowserStack::AppleBusinessManagerHelper).to receive(:logger).and_return(double('logger', info: nil)) # Stub logger
        allow(VPPUtils).to receive(:associate_license).and_return({ "eventId" => "event_id" })
        allow(VPPUtils).to receive(:assignment_status).with("event_id").and_return({ "eventStatus" => "COMPLETE" })
        allow(helper).to receive(:update_vpp_enrolled_device_list_file).and_return(true)
        allow(APP_DETAILS).to receive(:[]).and_return({ itunes_store_id: "123" })
      end

      it "returns true" do
        expect(helper.assign_license_to_device).to eq(true)
      end
    end

    context "when license assignment is pending" do
      before do
        allow(BrowserStack::AppleBusinessManagerHelper).to receive(:logger).and_return(double('logger', info: nil)) # Stub logger
        allow(VPPUtils).to receive(:associate_license).and_return({ "eventId" => "event_id" })
        allow(VPPUtils).to receive(:assignment_status).with("event_id").and_return({ "eventStatus" => "PENDING" })
        allow(helper).to receive(:update_vpp_enrolled_device_list_file).and_return(true)
        allow(APP_DETAILS).to receive(:[]).and_return({ itunes_store_id: "123" })
      end

      it "returns true" do
        expect(helper.assign_license_to_device).to eq(true)
      end
    end

    context "when license assignment fails" do
      before do
        allow(BrowserStack::AppleBusinessManagerHelper).to receive(:logger).and_return(double('logger', info: nil)) # Stub logger
        allow(VPPUtils).to receive(:associate_license).and_return({ "eventId" => "event_id" })
        allow(VPPUtils).to receive(:assignment_status).with("event_id").and_return({ "eventStatus" => "FAILED" })
      end

      it "raises an AppleBusinessManagerError" do
        expect(helper.assign_license_to_device).to eq(false)
      end
    end

    context "when an error occurs during license assignment" do
      before do
        allow(BrowserStack::AppleBusinessManagerHelper).to receive(:logger).and_return(double('logger', info: nil)) # Stub logger
        allow(VPPUtils).to receive(:associate_license).and_raise("License assignment failed")
      end

      it "returns false" do
        expect(helper.assign_license_to_device).to eq(false)
      end
    end
  end
end
