require 'timecop'
require_relative '../../spec_helper'
require_relative '../../../lib/helpers/xcui_test_helper'
require_relative '../../../lib/helpers/browserstack_app_helper'

describe XCUITestHelper do
  let(:uuid) { '000820-427503425' }
  let(:session_id) { '1' }
  let(:start_time) { 100 }
  let(:test) { "some_test" }
  let(:timeout) { 60 }
  let(:env_params) { {} }
  let(:test_execution_error) { "BrowserStackTestExecutionError" }
  let(:test_execution_error_msg) { "Test failed" }
  let(:success_data) { { "result" => "success", "time_taken" => 0, "error" => nil, "error_message" => nil } }
  let(:failure_data) { { "result" => "failed", "time_taken" => 0, "error" => test_execution_error, "error_message" => test_execution_error_msg } }

  before do
    Timecop.freeze(Time.at(start_time))
    @testObject = XCUITestHelper.new(uuid, session_id)
  end

  after do
    Timecop.return
  end

  context "#run_test" do
    it "should return success data" do
      expect(BrowserStackAppHelper).to receive(:run_ui_test).with(uuid, test, timeout, session_id: session_id, environment_variables: env_params)
      result = @testObject.run_test(test, timeout: timeout, env_params: env_params)
      expect(result).to eq(success_data)
    end

    it "should return failed data when test execution fails" do
      expect(BrowserStackAppHelper).to receive(:run_ui_test).with(uuid, test, timeout, session_id: session_id, environment_variables: env_params).and_raise(BrowserStackTestExecutionError.new("some_class", "some_function", test_execution_error_msg))
      result = @testObject.run_test(test, timeout: timeout, env_params: env_params)
      expect(result).to eq(failure_data)
    end
  end

  context "#parse_test_output_for_error" do
    it "should return error message as it is if doesn't find matches" do
      error = BrowserStackTestExecutionError.new("class", "func", test_execution_error_msg)
      expect(@testObject.parse_test_output_for_error(error)).to eq(test_execution_error_msg)
    end

    it "should parse properly for XCTAssertFalse" do
      error_message = "t = 4.29s Assertion Failure: MessagesUITests.swift:36: XCTAssertFalse failed - Some element not found"
      error = BrowserStackTestExecutionError.new("class", "func", error_message)
      expect(@testObject.parse_test_output_for_error(error)).to eq("Some element not found")
    end

    it "should parse properly for XCTAssertTrue" do
      error_message = "t = 4.29s Assertion Failure: MessagesUITests.swift:36: XCTAssertTrue failed - Some element not found"
      error = BrowserStackTestExecutionError.new("class", "func", error_message)
      expect(@testObject.parse_test_output_for_error(error)).to eq("Some element not found")
    end
  end
end

