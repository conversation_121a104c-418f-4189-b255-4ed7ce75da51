require_relative '../../spec_helper'
require_relative '../../../lib/helpers/automate_gestures/shake'

require 'tempfile'
require 'timecop'

file = Tempfile.new('screenshot')

describe Shake do
  let(:device_config) { { "device_version" => '15.4', "region" => 'us-east-1', 'webdriver_port' => 8080 } }
  let(:uuid) { '000820-427503425' }
  let(:product) { 'app-live' }
  let(:session_id) { '1' }
  let(:ios_version) { '15.4' }
  let(:event_name) { 'web_events' }
  let(:mock_wda_client) { double(WdaClient) }
  let(:data_report_helper) { double(DataReportHelper) }
  let(:mock_ios_device) { double(IosDevice).as_null_object }
  let(:mock_idevice_utils) { double(IdeviceUtils) }
  let(:start_time) { 100 }
  let(:end_time) { 100 }

  before do
    allow(DeviceManager).to receive(:device_configuration_check).and_return(device_config)
    allow(BrowserStack::IosDevice).to receive(:new).and_return(mock_ios_device)
    allow(WdaClient).to receive(:new).and_return(mock_wda_client)
    allow(BrowserStack::Zombie).to receive(:configure)
    @shake_obj = Shake.new(uuid, session_id, product)
    Timecop.freeze(Time.at(start_time))
  end

  after do
    Timecop.return
  end

  describe "shake_device" do
    it "should locate and tap on assistive touch menu and then shake button" do
      expect(@shake_obj).to receive(:assistive_touch_shake_optimized).and_return(true)
      expect(mock_ios_device).to receive(:disable_assistive_touch).and_return(true)
      expect_any_instance_of(DataReportHelper).to receive(:report).with(hash_including({ "status" => "pass" })).and_return(true)
      expect(@shake_obj.shake_device).to eq(true)
    end

    it "should return false if some exception is raised" do
      expect(@shake_obj).to receive(:assistive_touch_shake_optimized).and_raise('Some error')
      expect(mock_ios_device).to receive(:disable_assistive_touch).and_return(true)
      expect_any_instance_of(DataReportHelper).to receive(:report).with(hash_including({ "status" => "fail" })).and_return(true)
      expect(@shake_obj.shake_device).to eq(false)
    end
  end

  describe "shake_device" do
    it "should return true if successfully shake'd the device" do
      expect(@shake_obj).to receive(:assistive_touch_shake_optimized).and_return(true)
      expect(mock_ios_device).to receive(:disable_assistive_touch).and_return(true)
      expect_any_instance_of(DataReportHelper).to receive(:report).with({ "retries" => 0,
                                                                          "status" => "pass",
                                                                          "time_taken" => end_time - start_time }).and_return(true)
      expect(@shake_obj.shake_device).to eq(true)
    end

    it "should return false if some exception is raised" do
      expect(@shake_obj).to receive(:assistive_touch_shake_optimized).and_raise('Some error')
      expect(mock_ios_device).to receive(:disable_assistive_touch).and_return(true)
      expect_any_instance_of(DataReportHelper).to receive(:report).and_return(true)
      expect(@shake_obj.shake_device).to eq(false)
    end
  end

  describe "assistive_touch_shake_optimized" do
    it "should return true if wda is able to click on assistive touch icon" do
      expect(mock_ios_device).to receive(:enable_assistive_touch).and_return(true)
      expect(mock_wda_client).to receive(:tap_assistive_touch_element).and_return({ "value" => { "status" => "pass" } })
      expect(@shake_obj.assistive_touch_shake_optimized).to eq(true)
    end

    it "should raise exception if wda is unable to click on assistive touch icon" do
      expect(mock_ios_device).to receive(:enable_assistive_touch).and_return(true)
      expect(mock_wda_client).to receive(:tap_assistive_touch_element).and_return({ "value" => { "status" => "fail",
                                                                                                 "message" => "Some error" } })
      expect { @shake_obj.assistive_touch_shake_optimized }.to raise_error("Some error")
    end
  end
end
