require_relative '../../../lib/helpers/host_stats'

describe BrowserStack::HostStats do
  subject { BrowserStack::HostStats.new  }
  let(:mock_zombie) { double }
  let(:stats) { { "data" => {} } }
  let(:stat_val) { 1 }
  let(:swap_result) { "1 1 1" }
  let(:error) { StandardError.new("you_shall_not_pass") }

  before do
    allow(BrowserStack::Zombie).to receive(:new).and_return(mock_zombie)
    subject.instance_variable_set(:@page_size, 1)
    allow(subject).to receive(:instrument_puma_server)
  end

  describe "#perform" do
    it "should push to zombie with kind ios_host_stats when no error" do
      expect(BrowserStack::Zombie).to receive(:push_logs).with('ios_host_stats', '', anything)

      subject.perform
    end

    it "should push to zombie with kind ios_host_stats_failed when error" do
      expect(subject).to receive(:host_stats).and_raise(error)
      expect(BrowserStack::Zombie).to receive(:push_logs).with('ios_host_stats_failed', error.message, anything)

      subject.perform
    end
  end

  describe "#host_stats" do
    it "should collect swap , memory, cpu and port_stats" do
      expect(subject).to receive(:swap_memory_stats).and_return(stats)
      expect(subject).to receive(:memory_stats).and_return(stats)
      expect(subject).to receive(:cpu_stats).and_return(stats)
      expect(subject).to receive(:port_stats).and_return(stats)
      expect(subject).to receive(:os_version).and_return(stats)

      subject.host_stats
    end
  end

  describe "#memory_stats" do
    it "should get memory stats" do
      expect(subject).to receive(:pages_free).once.and_return(stat_val)
      expect(subject).to receive(:pages_active).once.and_return(stat_val)
      expect(subject).to receive(:pages_inactive).once.and_return(stat_val)
      expect(subject).to receive(:translation_faults).once.and_return(stat_val)

      subject.memory_stats
    end
  end

  describe "#swap_memory_stats" do
    it "should get swap memory stats" do
      expect(subject).to receive(:swap_stats).once.and_return(swap_result)
      subject.swap_memory_stats
    end
  end

  describe "#disk_stats" do
    it "should get disk stats" do
      expect(subject).to receive(:disk_space_used).once.and_return(stat_val)
      expect(subject).to receive(:disk_space_available).once.and_return(stat_val)

      subject.disk_stats
    end
  end

  describe "#cpu_stats" do
    it "should get cpu stats" do
      expect(subject).to receive(:cpu_usage).once.and_return stat_val
      expect(subject).to receive(:process_count).once.and_return stat_val
      expect(subject).to receive(:average_load).once.and_return stat_val
      subject.cpu_stats
    end
  end

  describe "#port_stats" do
    it "should get number of ports for all required ports" do
      ["45671", "45680"].each do |port|
        expect(subject).to receive(:port_count).with(port).ordered.and_return stat_val
      end
      subject.port_stats
    end
  end
end
