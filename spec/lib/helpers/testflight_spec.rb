require_relative '../../spec_helper'
require_relative '../../../lib/helpers/testflight'
require_relative '../../../lib/custom_exceptions'

describe Automation::TestFlight do
  describe ".handle_popup_if_any" do
    let(:subject) { Automation::TestFlight.new("device1415") }
    let(:mock_driver) { double }

    before do
      allow_any_instance_of(Object).to receive(:sleep)
      mock_appium_server = instance_double('BrowserStack::AppiumServer', driver: nil, start_server_for_version: nil)
      allow(BrowserStack::AppiumServer).to receive(:new).and_return(mock_appium_server)
      allow(mock_appium_server).to receive(:running?).and_return(true)
    end

    context "alert not found" do
      before do
        allow(mock_driver).to receive(:find_element).with(:class, 'XCUIElementTypeAlert')
                                                    .and_raise(Selenium::WebDriver::Error::NoSuchElementError)
      end

      it "returns early if no alert found" do
        expect(subject).to receive(:log).with("No alert found, all good !")
        subject.handle_popup_if_any(mock_driver)
      end
    end

    context "alert found" do
      let(:alert) { double }

      before do
        allow(mock_driver).to receive(:find_element).with(:class, 'XCUIElementTypeAlert').and_return(alert)
      end

      it "Clicks don't allow notifications if notifications alert" do
        allow(alert).to receive(:text).and_return("Would like to send you notifications")

        mock_element = double
        allow(mock_driver).to receive(:find_element).with(:name, "Don’t Allow").and_return(mock_element)
        expect(mock_element).to receive(:click).at_least(:once)

        subject.handle_popup_if_any(mock_driver)
      end

      it "raises exception if apple id verification alert" do
        allow(alert).to receive(:text).and_return("Apple ID Verification")

        expect { subject.handle_popup_if_any(mock_driver) }.to raise_error(AppleIDVerificationException)
      end

      it "Clicks OK if any other alert" do
        allow(alert).to receive(:text).and_return("unknown notification")

        mock_element = double
        allow(mock_driver).to receive(:find_element).with(:name, "OK").and_return(mock_element)
        expect(mock_element).to receive(:click).at_least(:once)

        subject.handle_popup_if_any(mock_driver)
      end

      it "handles multiple alerts in a row" do
        allow(alert).to receive(:text).and_return("Would like to send you notifications",
                                                  "unknown notification",
                                                  "Apple ID Verification")

        mock_element = double("mock_element")
        allow(mock_driver).to receive(:find_element).with(:name, any_args).and_return(mock_element)

        expect(mock_element).to receive(:click).twice
        begin
          subject.handle_popup_if_any(mock_driver)
        rescue
          AppleIDVerificationException
        end
      end
    end
  end
end
