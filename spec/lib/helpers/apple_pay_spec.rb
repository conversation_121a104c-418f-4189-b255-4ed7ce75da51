require_relative '../../spec_helper'
require_relative '../../../lib/helpers/apple_pay'
require_relative '../../../lib/models/device_state'

describe Secure::ApplePay do
  let(:uuid) { '000820-427503425' }
  let(:product) { 'app-live' }
  let(:session_id) { '1' }
  let(:params) { { 'app_live_session_id' => "123" } }
  let(:success_data) { { "attempt count" => 1, "result" => "success", "time_taken" => 0 } }
  let(:failure_data) { { "result" => "failed", "status" => "failed", "time_taken" => 0 } }
  let(:device_state) { double('mock_device_state').as_null_object }
  let(:mock_wda_client) { double(WdaClient) }

  before do
    allow(DeviceState).to receive(:new).and_return(device_state)
    expect(DeviceManager).to receive(:device_configuration_check).at_least(1).and_return({ "device_version" => "14.0" })
    @test_object = Secure::ApplePay.new(uuid, session_id, product)
  end

  context "#session setup" do
    it "should successfully run setup apple pay on device" do
      expect(Secure::ApplePay).to receive(:apple_pay_configured?).and_return(true)
      expect(device_state).to receive(:passcode_file_present?).and_return(false)
      expect(device_state).to receive(:touch_settings_automation_executing_file)
      expect(Secure::ApplePay).to receive(:custom_pusher).with("APPLE_PAY_INITIATED", params, uuid)
      expect_any_instance_of(Secure::ApplePay).to receive(:prepare_device).and_return(true)
      expect(device_state).to receive(:remove_settings_automation_executing_file)
      expect(Secure::ApplePay).to receive(:custom_pusher).with("ENABLING_APPLE_PAY_SUCCESSFUL", params, uuid)
      Secure::ApplePay.setup(uuid, params)
    end

    it 'should raise exception if Apple Pay is not configured on the device' do
      expect(Secure::ApplePay).to receive(:apple_pay_configured?).and_return(false)
      expect(device_state).to receive(:remove_settings_automation_executing_file)
      expect { Secure::ApplePay.setup(uuid, params) }.to raise_error("Apple Pay is not configured on this device")
    end

    it "should not run setup apple pay on device" do
      expect(Secure::ApplePay).to receive(:apple_pay_configured?).and_return(true)
      expect(device_state).to receive(:passcode_file_present?).and_return(false)
      expect(device_state).to receive(:touch_settings_automation_executing_file)
      expect(Secure::ApplePay).to receive(:custom_pusher).with("APPLE_PAY_INITIATED", params, uuid)
      expect_any_instance_of(Secure::ApplePay).to receive(:prepare_device).and_return(false)
      expect(Secure::ApplePay).to receive(:custom_pusher).with("ENABLING_APPLE_PAY_UNSUCCESSFUL", params, uuid)
      expect(device_state).to receive(:remove_settings_automation_executing_file)
      expect { Secure::ApplePay.setup(uuid, params) }.to raise_error(BrowserStack::SessionException)
    end
  end

  context '#session prepare' do
    it "should successfully run automation steps to setup apple pay on device" do
      expect_any_instance_of(Secure::Passcode).to receive(:passcode_settings).and_return(true)
      expect(Secure::ApplePay).to receive(:custom_pusher).with("START_ENABLING_DEVICE_PASSCODE", params, uuid)
      expect_any_instance_of(Secure::Passcode).to receive(:set_passcode).and_return(true)
      expect(Secure::ApplePay).to receive(:custom_pusher).with("START_ADDING_CARD_TO_WALLET", params, uuid)
      wallet = double
      expect(Secure::AppleWallet).to receive(:new).and_return(wallet)
      expect(wallet).to receive(:add_sandbox_card).and_return(true)
      expect(Secure::ApplePay).to receive(:custom_pusher).with("START_ENABLING_ASSISTIVE_TOUCH", params, uuid)
      assistive_touch = double
      expect(Secure::AssistiveTouchHelper).to receive(:new).and_return(assistive_touch)
      expect(assistive_touch).to receive(:switch).and_return(true)
      expect(device_state).to receive(:touch_apple_pay_data_file).and_return(true)
      expect(Secure::ApplePay).to receive(:custom_pusher).with("START_ENABLING_APPLE_PAY", params, uuid)
      result = @test_object.prepare_device(REDIS_CLIENT, params)
      expect(result).to eq(true)
    end

    it "should fail run automation passcode step to setup apple pay on device" do
      expect_any_instance_of(Secure::Passcode).to receive(:passcode_settings).and_return(true)
      expect(Secure::ApplePay).to receive(:custom_pusher).with("START_ENABLING_DEVICE_PASSCODE", params, uuid)
      expect_any_instance_of(Secure::Passcode).to receive(:set_passcode).and_return(false)
      expect(Secure::ApplePay).to receive(:custom_pusher).with("ENABLING_DEVICE_PASSCODE_UNSUCCESSFUL", params, uuid)
      result = @test_object.prepare_device(REDIS_CLIENT, params)
      expect(result).to eq(false)
    end

    it "should fail to run wallet addition steps to setup apple pay on device" do
      expect_any_instance_of(Secure::Passcode).to receive(:passcode_settings).and_return(true)
      expect(Secure::ApplePay).to receive(:custom_pusher).with("START_ENABLING_DEVICE_PASSCODE", params, uuid)
      expect_any_instance_of(Secure::Passcode).to receive(:set_passcode).and_return(true)
      expect(Secure::ApplePay).to receive(:custom_pusher).with("START_ADDING_CARD_TO_WALLET", params, uuid)
      wallet = double
      expect(Secure::AppleWallet).to receive(:new).and_return(wallet)
      expect(wallet).to receive(:add_sandbox_card).and_return(false)
      expect(Secure::ApplePay).to receive(:custom_pusher).with("ADDING_CARD_TO_WALLET_UNSUCCESSFUL", params, uuid)
      result = @test_object.prepare_device(REDIS_CLIENT, params)
      expect(result).to eq(false)
    end

    it "should fail to run assistive touch steps to setup apple pay on device" do
      expect_any_instance_of(Secure::Passcode).to receive(:passcode_settings).and_return(true)
      expect(Secure::ApplePay).to receive(:custom_pusher).with("START_ENABLING_DEVICE_PASSCODE", params, uuid)
      expect_any_instance_of(Secure::Passcode).to receive(:set_passcode).and_return(true)
      expect(Secure::ApplePay).to receive(:custom_pusher).with("START_ADDING_CARD_TO_WALLET", params, uuid)
      wallet = double
      expect(Secure::AppleWallet).to receive(:new).and_return(wallet)
      expect(wallet).to receive(:add_sandbox_card).and_return(true)
      expect(Secure::ApplePay).to receive(:custom_pusher).with("START_ENABLING_ASSISTIVE_TOUCH", params, uuid)
      assistive_touch = double
      expect(Secure::AssistiveTouchHelper).to receive(:new).and_return(assistive_touch)
      expect(assistive_touch).to receive(:switch).and_return(false)
      expect(Secure::ApplePay).to receive(:custom_pusher).with("ENABLING_ASSISTIVE_TOUCH_UNSUCCESSFUL", params, uuid)
      result = @test_object.prepare_device(REDIS_CLIENT, params)
      expect(result).to eq(false)
    end
  end

  context '#evaluate_apple_pay' do
    before(:each) do
    end

    context 'when apple pay already configured and sandbox account logged in' do
      before(:each) do
        allow_any_instance_of(Secure::ApplePay).to receive(:configured?).and_return(true)
        allow_any_instance_of(Secure::ApplePay).to receive(:icloud_account_logged_in?).and_return(true)
        expect(device_state).not_to receive(:remove_apple_pay_configuration_file)
      end

      it 'should return' do
        expect_any_instance_of(Secure::ApplePay).not_to receive(:add_sandbox_account)
        expect_any_instance_of(Secure::ApplePay).not_to receive(:session_ready?)
        expect_any_instance_of(Secure::ApplePay).not_to receive(:final_steps?)

        result = @test_object.evaluate_apple_pay
      end
    end

    context 'when apple pay already configured and sandbox account not logged in' do
      before(:each) do
        allow_any_instance_of(Secure::ApplePay).to receive(:configured?).and_return(true)
        allow_any_instance_of(Secure::ApplePay).to receive(:icloud_account_logged_in?).and_return(false)
        expect(device_state).not_to receive(:remove_apple_pay_configuration_file)
      end

      it 'should run add_sandbox_account' do
        expect_any_instance_of(Secure::ApplePay).to receive(:add_sandbox_account).and_return(true)
        expect_any_instance_of(Secure::ApplePay).not_to receive(:session_ready?)
        expect_any_instance_of(Secure::ApplePay).not_to receive(:final_steps?)

        result = @test_object.evaluate_apple_pay
      end
    end

    context 'when Apple Pay is not configured and Sandbox account not logged in' do
      before(:each) do
        allow_any_instance_of(Secure::ApplePay).to receive(:configured?).and_return(false)
        allow_any_instance_of(Secure::ApplePay).to receive(:icloud_account_logged_in?).and_return(false)
        expect(device_state).to receive(:remove_apple_pay_configuration_file).and_return(true)
      end

      it "should pass when both sandbox account and DC configuration are present" do
        expect_any_instance_of(Secure::ApplePay).to receive(:add_sandbox_account).and_return(true)
        expect(Secure::AssistiveTouchHelper).to receive(:dc_configured?).and_return(true)
        expect_any_instance_of(Secure::ApplePay).to receive(:session_ready?).and_return(true)
        expect_any_instance_of(Secure::ApplePay).to receive(:final_steps).and_return(true)
        expect_any_instance_of(Secure::ApplePay).to receive(:log_and_report).and_return(true)

        result = @test_object.evaluate_apple_pay

        expect(result).to be(true)
      end

      it "should fail when sandbox account is present but DC configuration is missing" do
        expect_any_instance_of(Secure::ApplePay).to receive(:add_sandbox_account).and_return(true)
        expect(Secure::AssistiveTouchHelper).to receive(:dc_configured?).and_return(false)
        expect_any_instance_of(Secure::ApplePay).to receive(:log_and_report).and_return(false)

        result = @test_object.evaluate_apple_pay

        expect(result).to be(false)
      end

      it "should pass when sandbox account is missing but DC configuration is present" do
        expect_any_instance_of(Secure::ApplePay).to receive(:add_sandbox_account).and_return(false)
        expect(Secure::AssistiveTouchHelper).to receive(:dc_configured?).and_return(true)
        expect_any_instance_of(Secure::ApplePay).to receive(:log_and_report).and_return(false)

        result = @test_object.evaluate_apple_pay

        expect(result).to be(false)
      end

      it "should pass when sandbox account is present but DC configuration is missing" do
        expect_any_instance_of(Secure::ApplePay).to receive(:add_sandbox_account).and_return(true)
        expect(Secure::AssistiveTouchHelper).to receive(:dc_configured?).and_return(false)
        expect_any_instance_of(Secure::ApplePay).to receive(:log_and_report).and_return(false)

        result = @test_object.evaluate_apple_pay

        expect(result).to be(false)
      end
    end
  end

  context '#configured?' do
    it "should pass when configuration check is successful" do
      allow_any_instance_of(Secure::ApplePay).to receive(:perform_configuration_check).and_return(true)
      allow_any_instance_of(DataReportHelper).to receive(:report)

      result = @test_object.configured?

      expect(result).to be(true)
    end

    it "should handle WdaClientError and return false" do
      allow_any_instance_of(Secure::ApplePay).to receive(:perform_configuration_check).and_raise(WdaClientError)
      allow_any_instance_of(DataReportHelper).to receive(:report)

      result = @test_object.configured?

      expect(result).to be(false)
    end

    it "should handle other exceptions and return false" do
      error_message = "Custom error message"
      allow_any_instance_of(Secure::ApplePay).to receive(:perform_configuration_check).and_raise(error_message)
      allow_any_instance_of(DataReportHelper).to receive(:report)

      result = @test_object.configured?

      expect(result).to be(false)
    end
  end

  context '#set_as_configured' do
    it "should pass when configuration set is successful" do
      allow_any_instance_of(Secure::ApplePay).to receive(:perform_configuration_set).and_return(true)
      allow_any_instance_of(DataReportHelper).to receive(:report)
      expect(device_state).to receive(:touch_apple_pay_configuration_file)

      result = @test_object.set_as_configured

      expect(result).to be(true)
    end

    it "should handle WdaClientError and return false" do
      allow_any_instance_of(Secure::ApplePay).to receive(:perform_configuration_set).and_raise(WdaClientError)
      allow_any_instance_of(DataReportHelper).to receive(:report)
      expect(device_state).to receive(:remove_apple_pay_configuration_file)

      result = @test_object.set_as_configured

      expect(result).to be(false)
    end

    it "should handle other exceptions and return false" do
      error_message = "Custom error message"
      allow_any_instance_of(Secure::ApplePay).to receive(:perform_configuration_set).and_raise(error_message)
      allow_any_instance_of(DataReportHelper).to receive(:report)
      expect(device_state).to receive(:remove_apple_pay_configuration_file)

      result = @test_object.set_as_configured

      expect(result).to be(false)
    end
  end

  context '#session_ready?' do
    it "should pass when prepare_device returns true" do
      allow_any_instance_of(Secure::ApplePay).to receive(:prepare_device).and_return(true)
      allow_any_instance_of(DataReportHelper).to receive(:report)

      result = @test_object.session_ready?

      expect(result).to be(true)
    end

    it "should pass when prepare_device returns false" do
      allow_any_instance_of(Secure::ApplePay).to receive(:prepare_device).and_return(false)
      allow_any_instance_of(DataReportHelper).to receive(:report)

      result = @test_object.session_ready?

      expect(result).to be(false)
    end
  end
end
