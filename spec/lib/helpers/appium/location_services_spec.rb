require_relative '../../../../lib/helpers/appium/location_services'
require 'selenium-webdriver'

describe 'Automation' do
  let(:driver) { double('AppiumServer') }
  let(:device_id) { '000000-000000000000' }
  let(:find_element_success) { double }

  before do
    allow(find_element_success).to receive(:click).and_return(true)
  end

  context 'location_permissions_popup' do
    it 'should return when no location popup is detected' do
      expect(driver).to receive(:find_element).with(:xpath, "//*[contains(@label,'to use your location?')]").and_raise(Selenium::WebDriver::Error::NoSuchElementError)
      result = Automation.location_permissions_popup(driver)
      expect(result).to be(nil)
    end

    it 'should click each popup based on LOCATION_POOPUP_OPTIONS' do
      Automation::LOCATION_POOPUP_OPTIONS.each do |opt, val|
        expect(driver).to receive(:find_element).with(:xpath, "//*[contains(@label,'to use your location?')]").and_return('')
        expect(driver).to receive(:find_element).with(name: val).and_return(find_element_success)
        result = Automation.location_permissions_popup(driver, permission: opt)
        expect(result).to be(true)
      end
    end
  end
end