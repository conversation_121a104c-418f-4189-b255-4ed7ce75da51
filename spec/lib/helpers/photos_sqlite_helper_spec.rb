require_relative '../../../lib/helpers/photos_sqlite_helper'

describe PhotosSqliteHelper do
  let(:mock_sqlite_path) { '/tmp/Photos-temp_device.sqlite' }
  let(:device_id) { 'temp_device' }
  let(:db) { double('SequelDb') }
  let(:mock_query) { double('Query') }
  let(:mock_ifuse) { double('IFuse') }
  let(:mock_ifuse_mount) { '/tmp/mock_ifuse_mount' }

  before(:each) do
    allow(File).to receive(:file?).with(mock_sqlite_path).and_return true
    allow(Sequel).to receive(:connect).with("sqlite://#{mock_sqlite_path}").and_return db
    allow(db).to receive(:disconnect).and_return true
    allow(mock_query).to receive(:count).and_return 9
    allow(Ifuse).to receive(:new).with(device_id).and_return(mock_ifuse)
    allow(mock_ifuse).to receive(:mount_point).and_return(mock_ifuse_mount)
  end

  describe '.all_media_count' do
    it "counts all rows from ZGENERICASSET table on iOS 13" do
      sqlite_helper = PhotosSqliteHelper.new(device_id, 13.0)
      expect(db).not_to receive(:[]).with(:ZASSET)
      expect(db).to receive(:[]).with(:ZGENERICASSET).and_return mock_query
      expect(sqlite_helper.all_media_count).to eql 9
    end

    it "counts all rows from ZASSET table on iOS 14+" do
      (14..16).each do |os|
        sqlite_helper = PhotosSqliteHelper.new(device_id, os)
        expect(db).not_to receive(:[]).with(:ZGENERICASSET)
        expect(db).to receive(:[]).with(:ZASSET).and_return mock_query
        expect(mock_query).to receive(:count).and_return 9
        expect(sqlite_helper.all_media_count).to eql 9
      end
    end
  end

  describe '.hidden_media_count' do
    it "counts all rows from ZGENERICASSET on iOS 13 where ZHIDDEN is 1" do
      sqlite_helper = PhotosSqliteHelper.new(device_id, 13.0)
      expect(db).not_to receive(:[]).with(:ZASSET)
      expect(db).to receive(:[]).with(:ZGENERICASSET).and_return mock_query
      expect(mock_query).to receive(:where).with(ZHIDDEN: 1).and_return mock_query
      expect(sqlite_helper.hidden_media_count).to eql 9
    end

    it "counts all rows from ZASSET on iOS 14+ where ZHIDDEN is 1" do
      (14..16).each do |os|
        sqlite_helper = PhotosSqliteHelper.new(device_id, os)
        expect(db).not_to receive(:[]).with(:ZGENERICASSET)
        expect(db).to receive(:[]).with(:ZASSET).and_return mock_query
        expect(mock_query).to receive(:where).with(ZHIDDEN: 1).and_return mock_query
        expect(sqlite_helper.hidden_media_count).to eql 9
      end
    end
  end

  describe '.marked_favorite_count' do
    it "counts all rows from ZGENERICASSET on iOS 13 where ZFAVORITE is 1" do
      sqlite_helper = PhotosSqliteHelper.new(device_id, 13.0)
      expect(db).not_to receive(:[]).with(:ZASSET)
      expect(db).to receive(:[]).with(:ZGENERICASSET).and_return mock_query
      expect(mock_query).to receive(:where).with(ZFAVORITE: 1).and_return mock_query
      expect(sqlite_helper.marked_favorite_count).to eql 9
    end

    it "counts all rows from ZASSET on iOS 14+ where ZFAVORITE is 1" do
      (14..16).each do |os|
        sqlite_helper = PhotosSqliteHelper.new(device_id, os)
        expect(db).not_to receive(:[]).with(:ZGENERICASSET)
        expect(db).to receive(:[]).with(:ZASSET).and_return mock_query
        expect(mock_query).to receive(:where).with(ZFAVORITE: 1).and_return mock_query
        expect(sqlite_helper.marked_favorite_count).to eql 9
      end
    end
  end

  describe '.trashed_media_count' do
    it "counts all rows from ZGENERICASSET on iOS 13 where ZTRASHEDSTATE is 1" do
      sqlite_helper = PhotosSqliteHelper.new(device_id, 13.0)
      expect(db).not_to receive(:[]).with(:ZASSET)
      expect(db).to receive(:[]).with(:ZGENERICASSET).and_return mock_query
      expect(mock_query).to receive(:where).with(ZTRASHEDSTATE: 1).and_return mock_query
      expect(sqlite_helper.trashed_media_count).to eql 9
    end

    it "counts all rows from ZASSET on iOS 14+ where ZTRASHEDSTATE is 1" do
      (14..16).each do |os|
        sqlite_helper = PhotosSqliteHelper.new(device_id, os)
        expect(db).not_to receive(:[]).with(:ZGENERICASSET)
        expect(db).to receive(:[]).with(:ZASSET).and_return mock_query
        expect(mock_query).to receive(:where).with(ZTRASHEDSTATE: 1).and_return mock_query
        expect(sqlite_helper.trashed_media_count).to eql 9
      end
    end
  end

  describe '.video_count' do
    it "counts all rows from ZGENERICASSET on iOS 13 where ZDURATION is not 0" do
      sqlite_helper = PhotosSqliteHelper.new(device_id, 13.0)
      expect(db).not_to receive(:[]).with(:ZASSET)
      expect(db).to receive(:[]).with(:ZGENERICASSET).and_return mock_query
      expect(mock_query).to receive(:exclude).with(ZDURATION: 0).and_return mock_query
      expect(sqlite_helper.video_count).to eql 9
    end

    it "counts all rows from ZASSET on iOS 14+ where ZDURATION is not 0" do
      (14..16).each do |os|
        sqlite_helper = PhotosSqliteHelper.new(device_id, os)
        expect(db).not_to receive(:[]).with(:ZGENERICASSET)
        expect(db).to receive(:[]).with(:ZASSET).and_return mock_query
        expect(mock_query).to receive(:exclude).with(ZDURATION: 0).and_return mock_query
        expect(sqlite_helper.video_count).to eql 9
      end
    end
  end

  describe '.photos_count' do
    it "counts all rows from ZGENERICASSET on iOS 13 where ZDURATION is 0" do
      sqlite_helper = PhotosSqliteHelper.new(device_id, 13.0)
      expect(db).not_to receive(:[]).with(:ZASSET)
      expect(db).to receive(:[]).with(:ZGENERICASSET).and_return mock_query
      expect(mock_query).to receive(:where).with(ZDURATION: 0).and_return mock_query
      expect(sqlite_helper.photos_count).to eql 9
    end

    it "counts all rows from ZASSET on iOS 14+ where ZDURATION is 0" do
      (14..16).each do |os|
        sqlite_helper = PhotosSqliteHelper.new(device_id, os)
        expect(db).not_to receive(:[]).with(:ZGENERICASSET)
        expect(db).to receive(:[]).with(:ZASSET).and_return mock_query
        expect(mock_query).to receive(:where).with(ZDURATION: 0).and_return mock_query
        expect(sqlite_helper.photos_count).to eql 9
      end
    end
  end

  describe '.albums_count' do
    it "counts all rows from ZGENERICALBUM on iOS 13 and 14 where ZKIND is 2" do
      (13..14).each do |os|
        sqlite_helper = PhotosSqliteHelper.new(device_id, os)
        expect(db).to receive(:[]).with(:ZGENERICALBUM).and_return mock_query
        expect(mock_query).to receive(:exclude).with(ZTITLE: nil).and_return mock_query
        expect(mock_query).to receive(:where).with(ZKIND: 2, ZTRASHEDSTATE: 0).and_return mock_query
        expect(mock_query).not_to receive(:where).with(ZIMPORTEDBYBUNDLEIDENTIFIER: "com.apple.mobileslideshow", ZTRASHEDSTATE: 0)
        expect(sqlite_helper.albums_count).to eql 9
      end
    end

    it "counts all rows from ZGENERICALBUM on iOS 15+ with ZIMPORTEDBYBUNDLEIDENTIFIER" do
      (15..16).each do |os|
        sqlite_helper = PhotosSqliteHelper.new(device_id, os)
        expect(db).to receive(:[]).with(:ZGENERICALBUM).and_return mock_query
        expect(mock_query).to receive(:exclude).with(ZTITLE: nil).and_return mock_query
        expect(mock_query).not_to receive(:where).with(ZKIND: 2, ZTRASHEDSTATE: 0)
        expect(mock_query).to receive(:where).with(ZIMPORTEDBYBUNDLEIDENTIFIER: "com.apple.mobileslideshow", ZTRASHEDSTATE: 0).and_return mock_query
        expect(sqlite_helper.albums_count).to eql 9
      end
    end
  end

  describe '.folder_count' do
    it "counts all rows from ZGENERICALBUM on iOS 13 and 14 where ZKIND is 4000" do
      (13..14).each do |os|
        sqlite_helper = PhotosSqliteHelper.new(device_id, os)
        expect(db).to receive(:[]).with(:ZGENERICALBUM).and_return mock_query
        expect(mock_query).to receive(:exclude).with(ZTITLE: nil).and_return mock_query
        expect(mock_query).to receive(:where).with(ZKIND: 4000, ZTRASHEDSTATE: 0).and_return mock_query
        expect(mock_query).not_to receive(:where).with(ZIMPORTEDBYBUNDLEIDENTIFIER: "com.apple.mobileslideshow", ZTRASHEDSTATE: 0)
        expect(sqlite_helper.folder_count).to eql 9
      end
    end

    it "counts all rows - 3 from ZGENERICALBUM on iOS 15+ with ZIMPORTEDBYBUNDLEIDENTIFIER" do
      (15..16).each do |os|
        sqlite_helper = PhotosSqliteHelper.new(device_id, os)
        expect(db).to receive(:[]).with(:ZGENERICALBUM).and_return mock_query
        expect(mock_query).to receive(:exclude).with(ZTITLE: nil).and_return mock_query
        expect(mock_query).not_to receive(:where).with(ZKIND: 2, ZTRASHEDSTATE: 0)
        expect(mock_query).to receive(:where).with(ZIMPORTEDBYBUNDLEIDENTIFIER: nil, ZTRASHEDSTATE: 0).and_return mock_query
        expect(sqlite_helper.folder_count).to eql 6
      end
    end
  end

  describe '.ios_media_state_consistent?' do
    subject { PhotosSqliteHelper.new(device_id, 15.0) }
    before(:each) do
      allow(subject).to receive(:pull_photos_sqlite_from_device).and_return nil
      allow(subject).to receive(:delete_tmp_sqlite_files).and_return nil
      allow(subject).to receive(:all_media_count).and_return 9
      allow(subject).to receive(:hidden_media_count).and_return 0
      allow(subject).to receive(:trashed_media_count).and_return 0
      allow(subject).to receive(:video_count).and_return 3
      allow(subject).to receive(:photos_count).and_return 6
      allow(subject).to receive(:albums_count).and_return 0
      allow(subject).to receive(:folder_count).and_return 0
      allow(subject).to receive(:marked_favorite_count).and_return 0
    end

    it "returns false if all_media_count is not 9" do
      expect(subject).to receive(:all_media_count).and_return 10
      expect(subject.ios_media_state_consistent?).to eql false
    end

    it "returns false if hidden_media_count is not 0" do
      expect(subject).to receive(:hidden_media_count).and_return 1
      expect(subject.ios_media_state_consistent?).to eql false
    end

    it "returns false if trashed_media_count is not 0" do
      expect(subject).to receive(:trashed_media_count).and_return 1
      expect(subject.ios_media_state_consistent?).to eql false
    end

    it "returns false if video_count is not 3" do
      expect(subject).to receive(:video_count).and_return 4
      expect(subject.ios_media_state_consistent?).to eql false
    end

    it "returns false if photos_count is not 6" do
      expect(subject).to receive(:photos_count).and_return 7
      expect(subject.ios_media_state_consistent?).to eql false
    end

    it "returns false if albums_count is not 0" do
      expect(subject).to receive(:albums_count).and_return 1
      expect(subject.ios_media_state_consistent?).to eql false
    end

    it "returns true if all parameters are correct" do
      expect(subject).to receive(:all_media_count).and_return 9
      expect(subject).to receive(:hidden_media_count).and_return 0
      expect(subject).to receive(:trashed_media_count).and_return 0
      expect(subject).to receive(:video_count).and_return 3
      expect(subject).to receive(:photos_count).and_return 6
      expect(subject).to receive(:albums_count).and_return 0
      expect(subject.ios_media_state_consistent?).to eql true
    end

    it "does not consider folder_count and marked_favorite_count for consistent media" do
      expect(subject).to receive(:folder_count).and_return 1
      expect(subject).to receive(:marked_favorite_count).and_return 2
      expect(subject.ios_media_state_consistent?).to eql true
    end

    it "does not runs the checks for iOS version < 13" do
      (1..12).each do |os|
        sqlite_helper = PhotosSqliteHelper.new(device_id, os)
        expect(sqlite_helper).not_to receive(:pull_photos_sqlite_from_device)
        expect(sqlite_helper).not_to receive(:all_media_count)
        expect(sqlite_helper).not_to receive(:hidden_media_count)
        expect(sqlite_helper).not_to receive(:trashed_media_count)
        expect(sqlite_helper).not_to receive(:video_count)
        expect(sqlite_helper).not_to receive(:photos_count)
        expect(sqlite_helper).not_to receive(:albums_count)

        expect(sqlite_helper.ios_media_state_consistent?).to eql true
      end
    end

    it "does not runs the checks for iOS version 13.4 to 13.7" do
      [13.4, 13.5, 13.6, 13.7].each do |os|
        sqlite_helper = PhotosSqliteHelper.new(device_id, os)
        expect(sqlite_helper).not_to receive(:pull_photos_sqlite_from_device)
        expect(sqlite_helper).not_to receive(:all_media_count)
        expect(sqlite_helper).not_to receive(:hidden_media_count)
        expect(sqlite_helper).not_to receive(:trashed_media_count)
        expect(sqlite_helper).not_to receive(:video_count)
        expect(sqlite_helper).not_to receive(:photos_count)
        expect(sqlite_helper).not_to receive(:albums_count)

        expect(sqlite_helper.ios_media_state_consistent?).to eql true
      end
    end
  end

  describe '.delete_tmp_sqlite_files' do
    it "deletes temporary sqlite files from tmp folder" do
      sqlite_helper = PhotosSqliteHelper.new(device_id, 15.0)
      expect(FileUtils).to receive(:rm_f).with("/tmp/Photos-#{device_id}.sqlite").and_return nil
      expect(FileUtils).to receive(:rm_f).with("/tmp/Photos-#{device_id}.sqlite-shm").and_return nil
      expect(FileUtils).to receive(:rm_f).with("/tmp/Photos-#{device_id}.sqlite-wal").and_return nil
      sqlite_helper.send(:delete_tmp_sqlite_files)
    end
  end

  describe '.copy_sqlite_files_to_tmp' do
    it "copy sqlite files to tmp folder" do
      sqlite_helper = PhotosSqliteHelper.new(device_id, 15.0)
      expect(FileUtils).to receive(:cp).with("#{mock_ifuse_mount}/PhotoData/Photos.sqlite", "/tmp/Photos-#{device_id}.sqlite").and_return nil
      expect(FileUtils).to receive(:cp).with("#{mock_ifuse_mount}/PhotoData/Photos.sqlite-shm", "/tmp/Photos-#{device_id}.sqlite-shm").and_return nil
      expect(FileUtils).to receive(:cp).with("#{mock_ifuse_mount}/PhotoData/Photos.sqlite-wal", "/tmp/Photos-#{device_id}.sqlite-wal").and_return nil
      sqlite_helper.send(:copy_sqlite_files_to_tmp)
    end
  end

  describe '.pull_photos_sqlite_from_device' do
    it "doesn't mounts ifuse if it is already mounted" do
      sqlite_helper = PhotosSqliteHelper.new(device_id, 15.0)
      expect(sqlite_helper).to receive(:delete_tmp_sqlite_files).and_return true
      expect(File).to receive(:exists?).with(mock_ifuse_mount).and_return true
      expect(sqlite_helper).to receive(:copy_sqlite_files_to_tmp).and_return true
      expect(mock_ifuse).not_to receive(:run)
      sqlite_helper.send(:pull_photos_sqlite_from_device)
    end

    it "mounts ifuse if it is not already mounted" do
      sqlite_helper = PhotosSqliteHelper.new(device_id, 15.0)
      expect(sqlite_helper).to receive(:delete_tmp_sqlite_files).and_return true
      expect(File).to receive(:exists?).with(mock_ifuse_mount).and_return false
      expect(mock_ifuse).to receive(:run).and_return true
      sqlite_helper.send(:pull_photos_sqlite_from_device)
    end
  end

  describe '.tmp_photos_sqlite_file_path' do
    it "returns tmp path where sqlite will be copied" do
      sqlite_helper = PhotosSqliteHelper.new(device_id, 15.0)
      expect(sqlite_helper.send(:tmp_photos_sqlite_file_path)).to eql "/tmp/Photos-#{device_id}.sqlite"
    end
  end

  describe '.asset_table' do
    it "returns correct table for ios < 14" do
      (12..13).each do |os|
        sqlite_helper = PhotosSqliteHelper.new(device_id, os)
        expect(sqlite_helper.send(:asset_table)).to eql :ZGENERICASSET
      end
    end

    it "returns correct table for ios 14+" do
      (14..16).each do |os|
        sqlite_helper = PhotosSqliteHelper.new(device_id, os)
        expect(sqlite_helper.send(:asset_table)).to eql :ZASSET
      end
    end
  end
end
