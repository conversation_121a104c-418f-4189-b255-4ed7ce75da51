require_relative '../../spec_helper'
require_relative '../../../lib/helpers/socat_helper'

RSpec.describe SocatHelper do
  describe '.get_device_socat_port' do
    context 'when device exists in configuration' do
      let(:device_id) { 'device1' }

      it 'returns the correct socat port' do
        allow(BrowserStack::Configuration).to receive_message_chain(:new, :all) do
          { 'config_json_file' => 'path/to/config.json', 'socat_listen_port_offset' => 100 }
        end
        allow(File).to receive(:read).with('path/to/config.json').and_return('{"devices":{"device1":{"selenium_port":4444}}}')
        expect(SocatHelper.get_device_socat_port(device_id)).to eq(4544)
      end
    end

    context 'when device does not exist in configuration' do
      let(:device_id) { 'nonexistent_device' }

      it 'returns 0' do
        allow(BrowserStack::Configuration).to receive_message_chain(:new, :all) { { 'config_json_file' => 'path/to/config.json' } }
        allow(File).to receive(:read).with('path/to/config.json').and_raise(Errno::ENOENT)
        expect(SocatHelper.get_device_socat_port(device_id)).to eq(0)
      end
    end
  end

  describe '.kill' do
    context 'when socat processes exist on specified port' do
      let(:socat_port) { 12345 }

      it 'kills the socat processes' do
        expect(BrowserStack::OSUtils).to receive(:execute).with("ps aux | grep -E 'socat.*TCP-LISTEN:' | grep -v grep | awk '{print $2}'", true, { timeout: 10 }).and_return(["1234", 0])
        expect(BrowserStack::OSUtils).to receive(:execute).with("ps aux | grep -E 'socat.*TCP-LISTEN:12345' | grep -v grep | awk '{print $2}' | xargs -n 1 -I {} timeout 5 kill -9 {}", true)
        SocatHelper.kill(socat_port)
      end
    end

    context 'when no socat processes exist on specified port' do
      let(:socat_port) { 12345 }

      it 'outputs appropriate message' do
        expect(BrowserStack::OSUtils).to receive(:execute).with("ps aux | grep -E 'socat.*TCP-LISTEN:' | grep -v grep | awk '{print $2}'", true, { timeout: 10 }).and_return(["", 0])
        SocatHelper.kill(socat_port)
      end
    end
  end
end
