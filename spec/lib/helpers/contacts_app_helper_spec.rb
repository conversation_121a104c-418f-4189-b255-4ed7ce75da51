require_relative '../../../lib/helpers/contacts_app_helper'
require_relative '../../spec_helper'

describe ContactsAppHelper do
  let(:device) { "some_device" }
  let(:session) { "some_session" }
  let(:product) { "some_product" }
  let(:subject) { ContactsAppHelper.new(device, session, product) }
  let(:mock_device_state) { double(Devi<PERSON>State).as_null_object }
  let(:mock_icon_layout_util) { double(IconLayoutUtil).as_null_object }

  before(:each) do
    allow(IconLayoutUtil).to receive(:new).and_return(mock_icon_layout_util)
  end

  describe '#add_icon_to_home_screen' do
    before(:each) do
      allow(mock_icon_layout_util).to receive(:icon_state=)
    end

    context 'when icon was added to home screen successfully' do
      it 'should return true' do
        expect(subject.add_icon_to_home_screen).to eq(true)
      end
    end

    context 'when icon was not added to home screen' do
      before(:each) do
        allow(mock_icon_layout_util).to receive(:icon_state=).and_raise("Some error")
      end

      it 'should return false' do
        expect(subject.add_icon_to_home_screen).to eq(false)
      end
    end
  end
end
