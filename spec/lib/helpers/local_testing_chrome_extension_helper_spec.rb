require_relative '../../spec_helper'
require_relative '../../../lib/helpers/local_testing_chrome_extension_helper'

RSpec.describe LocalTestingChromeExtension do
  let(:device_id) { 'device1' }
  let(:session_id) { 'session123' }
  let(:helper) { described_class.new(device_id, session_id) }

  describe '#send_data_to_eds' do
    it 'sends data to EDS' do
      data = { key: 'value' }
      expect(Utils).to receive(:send_to_eds).with(
        hash_including(event_name: 'test_event', product: 'live', os: anything, team: 'live'),
        'web_events',
        true
      )
      helper.send_data_to_eds('test_event', data)
    end
  end

  describe '#request_stats' do
    context 'when state file exists' do
      it 'returns parsed JSON data' do
        allow(File).to receive(:read).and_return('{"P1": 1, "P25": 25, "P50": 50, "P75": 75, "P99": 99, "total_requests": 100, "total_failed_requests": 5}')
        expect(helper.request_stats).to eq({
          "P1" => 1,
          "P25" => 25,
          "P50" => 50,
          "P75" => 75,
          "P99" => 99,
          "total_requests" => 100,
          "total_failed_requests" => 5
        })
      end
    end

    context 'when state file does not exist' do
      it 'returns an empty hash' do
        allow(File).to receive(:read).and_raise(Errno::ENOENT)
        expect(helper.request_stats).to eq({})
      end
    end
  end

  describe '#cleanup' do
    it 'performs cleanup tasks' do
      allow(File).to receive(:read).and_return('{"P1": 1, "P25": 25, "P50": 50, "P75": 75, "P99": 99, "total_requests": 100, "total_failed_requests": 5}')
      allow(helper).to receive(:local_testing_chrome_extension_session?).and_return(true)
      allow(BrowserStack::OSUtils).to receive(:execute)

      expect(SocatHelper).to receive(:cleanup)
      expect(helper).to receive(:send_data_to_eds)
      expect(helper).to receive(:remove_state_file)
      helper.cleanup
    end
  end

  describe '#get_state_file_path' do
    it 'returns state file path' do
      expect(described_class.get_state_file_path(device_id)).to eq("#{STATE_FILES_DIR}/local_chrome_extension_#{device_id}")
    end
  end

  describe '#touch_state_file' do
    it 'touches state file' do
      expect(helper.touch_state_file).to be(true)
    end
  end

  describe '#remove_state_file' do
    it 'removes state file' do
      expect(helper.remove_state_file).to be(true)
    end
  end
end
