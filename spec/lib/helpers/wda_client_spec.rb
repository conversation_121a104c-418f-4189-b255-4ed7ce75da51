require_relative '../../spec_helper'
require_relative '../../../lib/helpers/wda_client'

describe WdaClient do
  let(:wda_port) { 8084 }
  let(:wda_client) { WdaClient.new(wda_port) }
  let(:status_cache_file) { "/tmp/wda_status_#{Thread.current[:device_id]}.txt" }

  before do
    allow(wda_client).to receive(:make_request).and_return({ 'status' => 0, 'value' => { 'state' => 'success' } })
    allow($stdout).to receive(:puts) # This removes console output from rspecs logs
    allow(wda_client.instance_variable_get(:@influxdb_client)).to receive(:event)
    allow(BrowserStack::Zombie).to receive(:push_logs)
  end

  before(:each) do
    File.delete(status_cache_file) if File.exist?(status_cache_file)
  end

  describe '.new' do
  end

  describe '.clear_clipboard' do
    it 'calls make_request function to clear_clipboard endpoint' do
      expect(wda_client).to receive(:make_request)
        .with('POST', '/bs/clear_clipboard', {})
      wda_client.clear_clipboard
    end
  end

  describe '.status' do
    it 'calls status function with the right params' do
      expect(wda_client).to receive(:make_request).with('GET', '/status', {}, 5)
      expect(wda_client.status).to eq({ 'status' => 0, 'value' => { 'state' => 'success' } })
    end
  end

  describe '.running?' do
    it 'returns true if wda gives response "success"' do
      expect(wda_client.running?).to be true
    end

    it 'returns false if wda does not return "success"' do
      allow(wda_client).to receive(:make_request).and_return({ 'value' => { 'state' => 'failure' } })
      expect(wda_client.running?).to be false
    end

    it 'returns false if wda does not return Hash in expected format' do
      allow(wda_client).to receive(:make_request).and_return({})
      expect(wda_client.running?).to be false
    end

    it 'returns false if wda raises exception' do
      allow(wda_client).to receive(:make_request).and_raise(Timeout::Error)
      expect(wda_client.running?).to be false
    end
  end

  describe '.cached_running?' do
    it 'returns true from cached data' do
      File.open(status_cache_file, "w") { |f| f.write "#{Time.now.to_i},true" }
      expect_any_instance_of(WdaClient).to_not receive(:running?)
      expect(wda_client.cached_running?).to be true
      expect(File.exists?(status_cache_file)).to be true
    end

    it 'returns false from cached data' do
      File.open(status_cache_file, "w") { |f| f.write "#{Time.now.to_i},false" }
      expect_any_instance_of(WdaClient).to_not receive(:running?)
      expect(wda_client.cached_running?).to be false
      expect(File.exists?(status_cache_file)).to be true
    end

    it 'returns current status when cached data is not available' do
      expect_any_instance_of(WdaClient).to receive(:running?).with(no_args).and_return(true)
      expect(wda_client.cached_running?).to be true
    end

    it 'returns current status false when cached data is not available' do
      expect_any_instance_of(WdaClient).to receive(:running?).with(no_args).and_return(false)
      expect(wda_client.cached_running?).to be false
      expect(File.exists?(status_cache_file)).to be true
    end

    it 'returns current status if cached data has expired' do
      File.open(status_cache_file, "w") { |f| f.write "#{Time.now.to_i - 20},false" }
      expect_any_instance_of(WdaClient).to receive(:running?).with(no_args).and_return(true)
      expect(wda_client.cached_running?).to be true
      expect(File.exists?(status_cache_file)).to be true
    end

    it 'returns current status as false if cached data has expired' do
      File.open(status_cache_file, "w") { |f| f.write "#{Time.now.to_i - 20},true" }
      expect_any_instance_of(WdaClient).to receive(:running?).with(no_args).and_return(false)
      expect(wda_client.cached_running?).to be false
      expect(File.exists?(status_cache_file)).to be true
    end

    it 'returns current status if cache file has invalid data' do
      File.open(status_cache_file, "w") { |f| f.write "invalid_data_here" }
      expect_any_instance_of(WdaClient).to receive(:running?).with(no_args).and_return(true)
      expect(wda_client.cached_running?).to be true
      expect(File.exists?(status_cache_file)).to be true
    end
  end

  describe '.describe_running' do
    it 'returns true if wda gives response "success"' do
      is_wda_running, wda_status_description = wda_client.describe_running
      expect(is_wda_running).to eq(true)
      expect(wda_status_description).to eq("wda success state")
    end

    it 'returns false if wda does not return "success"' do
      wda_response = { 'value' => { 'state' => 'failure' } }
      allow(wda_client).to receive(:make_request).and_return(wda_response)
      expected_response = "wda failed state"
      is_wda_running, wda_status_description = wda_client.describe_running
      expect(is_wda_running).to eq(false)
      expect(wda_status_description).to eq(expected_response)
    end

    it 'returns false if wda does not return Hash in expected format' do
      wda_response = {}
      allow(wda_client).to receive(:make_request).and_return(wda_response)
      expected_response = "WDA not running. WDA status response: #{wda_response}"
      is_wda_running, wda_status_description = wda_client.describe_running
      expect(is_wda_running).to eq(false)
      expect(wda_status_description).to eq(expected_response)
    end

    it 'returns false if wda raises exception' do
      allow(wda_client).to receive(:make_request).and_raise(Timeout::Error)
      is_wda_running, wda_status_description = wda_client.describe_running
      expect(is_wda_running).to eq(false)
      expect(wda_status_description).to include("WDA not running. Error getting response:")
    end
  end

  describe '.tap(x, y)' do
    it 'calls make_request function with the right params' do
      my_x = 42
      my_y = 1764
      expect(wda_client).to receive(:make_request)
        .with('POST', '/bs/tap', { x: my_x, y: my_y })
      wda_client.tap(my_x, my_y)
    end

    it 'raises an exception if response status isnt 0' do
      my_x = 42
      my_y = 1764
      allow(wda_client).to receive(:make_request).and_return({ 'status' => 42 })
      expect { wda_client.tap(my_x, my_y) }.to raise_error("Failed to Tap at #{my_x} #{my_y}")
    end
  end

  describe '.lock_device' do
    it 'calls /lock endpoint for iOS < 12' do
      expect(wda_client).to receive(:make_request).with('GET', '/lock')
      wda_client.lock_device(10)
    end

    it 'calls /wda/lock endpoint for iOS >= 12' do
      expect(wda_client).to receive(:make_request).with('POST', '/wda/lock')
      wda_client.lock_device(12)
    end
  end

  describe '.clean_keychain' do
    it 'calls make_request function with the right params' do
      expect(wda_client).to receive(:make_request)
        .with('GET', '/bs/clean_keychain')
      wda_client.clean_keychain
    end
  end

  describe '.unlock_device' do
    it 'calls make_request function with the right params' do
      expect(wda_client).to receive(:make_request)
        .with('GET', '/bs/unlock_device')
      wda_client.unlock_device
    end
  end

  describe '.launch_app_with_app_display_name(app_display_name)' do
    it 'calls make_request function with the right params' do
      my_app_display_name = "my App"
      expect(wda_client).to receive(:make_request)
        .with('POST', '/bs/launchapp', { displayName: my_app_display_name, strictLaunch: true })
      wda_client.launch_app_with_app_display_name(my_app_display_name)
    end
  end

  describe '.set_rtc_data_and_start(rtc_data)' do
    let(:rtc_data) { "data" }

    it 'calls make_request function with the right params' do
      expect(wda_client).to receive(:make_request)
        .with('POST', '/bs/set_rtcdata_and_start', { rtc_data: rtc_data })
      wda_client.set_rtc_data_and_start(rtc_data)
    end
  end

  describe '.connect_to_peer' do
    let(:params) { "data" }

    it 'calls make_request function with the right params' do
      expect(wda_client).to receive(:make_request)
        .with('POST', '/bs/connect_to_peer', params)
      wda_client.connect_to_peer(params)
    end
  end

  describe '.get_metrics' do
    it 'calls make_request function to get metrics' do
      expect(wda_client).to receive(:make_request)
        .with('GET', '/bs/wda/metrics').and_return({ 'value' => { 'memoryUsageMB' => 100, 'cpuUsagePercentage' => 50 } })
      metrics = wda_client.get_metrics
      expect(metrics[:process_name]).to eq(WDA)
      expect(metrics[:memory_usage]).to eq(100)
      expect(metrics[:cpu_usage]).to eq(50)
      expect(metrics[:time_consumed]).to be_a(Float)
    end

    it 'logs an error for invalid response' do
      allow(wda_client).to receive(:make_request).and_return(nil)
      expect(BrowserStack.logger).to receive(:error).with("Invalid WDA response from endpoint: /bs/wda/metrics")
      wda_client.get_metrics
    end
  end

  # Private functions

  describe '.make_request(verb, endpoint, params={})' do
    let(:endpoint) { "/foobar" }
    let(:url) { "http://127.0.0.1:#{wda_port}#{endpoint}" }
    let(:response_body) { "{\n  \"foo\" : \"bar\"\n}" }
    let(:parsed_response_body) { { "foo" => "bar" } }

    before do
      allow(wda_client).to receive(:make_request).and_call_original
    end

    it 'returns response body as Hash when the verb is GET' do
      stub_request(:get, url).to_return(status: 200, body: response_body)
      expect(wda_client.send(:make_request, 'GET', endpoint)).to eq(parsed_response_body)
    end

    it 'returns response body as Hash when the verb is POST' do
      stub_request(:post, url).to_return(status: 200, body: response_body)
      expect(wda_client.send(:make_request, 'POST', endpoint)).to eq(parsed_response_body)
    end

    it 'raises error if the request times out' do
      stub_request(:get, url).to_timeout
      expect { wda_client.send(:make_request, 'POST', endpoint) }.to raise_error(/failed/)
    end

    it 'raises error if the request fails' do
      stub_request(:post, url).to_return(status: 400, body: response_body)
      expect { wda_client.send(:make_request, 'POST', endpoint) }.to raise_error(/failed/)
    end

    context 'GET requests' do
      it 'returns {} if response is not json' do
        stub_request(:post, url).to_return(status: 200, body: "")
        expect(wda_client.send(:make_request, 'GET', endpoint)).to eq({})
      end
    end

    context 'POST requests' do
      it 'raises an exception if the request fails' do
        stub_request(:post, url).to_return(status: 408, body: "")
        expect { wda_client.send(:make_request, 'POST', endpoint) }.to raise_error(/failed/)
      end

      context 'response body is not json' do
        before do
          stub_request(:post, url).to_return(status: 200, body: "")
        end

        it 'raises a json parse exception' do
          expect { wda_client.send(:make_request, 'POST', endpoint) }.to raise_error(/json/)
        end

        it 'exception contains endpoint' do
          expect { wda_client.send(:make_request, 'POST', endpoint) }.to raise_error(/#{endpoint}/)
        end
      end
    end
  end
end
