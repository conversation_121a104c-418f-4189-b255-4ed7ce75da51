require_relative '../spec_helper'
require_relative '../../lib/utils/utils'
require_relative '../../server/device_manager'

describe VideoUtility do
  before(:each) do
    DeviceManager.configure BrowserStack::Configuration.new.all
    @device_config = {
      "device_id" => {
        "current_appium_version" => "1.14.0",
        "device_name" => "iPhone 8",
        "device_version" => "13.2.0",
        "selenium_port" => 8080,
        "webdriver_port" => 8401,
        "debugger_port" => 12345
      }
    }
    @video_utility_instance = VideoUtility.new
    @server_config = BrowserStack::Configuration.new.all
    @video_utility_instance.instance_variable_set(:@device_id, "device_id")
    @video_utility_instance.instance_variable_set(:@session_id, "session_id")
    @video_utility_instance.instance_variable_set(:@device_workspace, "#{@server_config['video_recording_workspace']}device_id/")
    @video_utility_instance.instance_variable_set(:@video_parts_prefix, "__video__")
    @video_utility_instance.instance_variable_set(:@server_config, @server_config)
    @video_utility_instance.instance_variable_set(:@workspace, "#{@server_config['video_recording_workspace']}device_id/session_id/")
    @video_utility_instance.instance_variable_set(:@video_merge_completed_file, "#{@server_config['video_recording_workspace']}device_id/session_id/video_merge_completed.txt")
  end

  describe '.log' do
    it 'should log specified format' do
      allow(DeviceManager).to receive(:all_devices).and_return(@device_config)
      expect($stdout).to receive(:puts).with("#{Time.now} device_id session_id : test_message")
      @video_utility_instance.log("test_message")
    end
  end

  describe '.clean_workspace' do
    it 'should clean workspace' do
      allow(DeviceManager).to receive(:all_devices).and_return(@device_config)
      expect(FileUtils).to receive(:rm_rf).with(@video_utility_instance.instance_variable_get(:@workspace))
      @video_utility_instance.clean_workspace
    end

    it 'should not cleacn workspace if recording is skipped' do
      @video_utility_instance.instance_variable_set(:@skip_recording, true)
      expect(FileUtils).not_to receive(:rm_rf).with(@video_utility_instance.instance_variable_get(:@workspace))
      @video_utility_instance.clean_workspace
    end
  end

  describe '.clean_stale_session_folders_from_workspace' do
  end

  describe '.delete_all_matching' do
    it 'should delete all files in the dir matching the pattern' do
      expect(Dir).to receive(:glob).and_return(["test_file"])
      expect(File).to receive(:delete).with('test_file')
      @video_utility_instance.delete_all_matching("/tmp/device_id/session_id/__video__*.mp4")
    end
  end

  describe '.create_concat_file' do
    it 'should create a file with all the video snippets' do
      expect(File).to receive(:exist?).with('/tmp/device_id/session_id/video_part_names.txt').and_return(true)
      expect(File).to receive(:delete).with('/tmp/device_id/session_id/video_part_names.txt')
      FileUtils.mkdir_p "/tmp/device_id/session_id/"
      FileUtils.touch("/tmp/device_id/session_id/__video__0.mp4")
      FileUtils.touch('/tmp/device_id/session_id/__video__1.mp4')
      outfile = @video_utility_instance.create_concat_file
      FileUtils.rm_rf("/tmp/device_id/session_id/__video*.mp4")
      expect(File.read(outfile).lines).to eq(["file /tmp/device_id/session_id/__video__0.mp4\n", "file /tmp/device_id/session_id/__video__1.mp4"])
      FileUtils.rm_rf(outfile)
    end

    it 'should raise exception if failed to create concat file' do
      FileUtils.rm_rf("/tmp/device_id/")
      expect(File).to receive(:exist?).with('/tmp/device_id/session_id/video_part_names.txt').and_return(true)
      expect { @video_utility_instance.create_concat_file }.to raise_error(VideoRecordError)
    end
  end

  describe '.create_upload_request' do
    it 'should create an upload request for uploader' do
      file_name = "/tmp/device_id/session_idvideo_rec_#{Time.now.to_i}.mp4"
      upload_req_file = "/usr/local/.browserstack/files_to_be_processed/files_to_upload/VIDEO/video_upload_1234.json"
      json_data = {
        upload_type: "video",
        file_name: file_name,
        video_params: { "video_1" => "test", "video_2" => "test" },
        genre: "app_automate"
      }
      expect(File).to receive(:dirname).with(upload_req_file).and_return("/usr/local/.browserstack/files_to_be_processed/files_to_upload/VIDEO/")
      expect(Dir).to receive(:exist?).and_return(false)
      expect(FileUtils).to receive(:mkdir_p).with("/usr/local/.browserstack/files_to_be_processed/files_to_upload/VIDEO/")
      expect(Utils).to receive(:write_to_file).with(upload_req_file, json_data.to_json)
      @video_utility_instance.create_upload_request(upload_req_file, json_data)
    end

    it 'should not create an upload request if recording was skipped' do
      file_name = "/tmp/device_id/session_idvideo_rec_#{Time.now.to_i}.mp4"
      upload_req_file = "/usr/local/.browserstack/files_to_be_processed/files_to_upload/VIDEO/video_upload_1234.json"
      json_data = {
        upload_type: "video",
        file_name: file_name,
        video_params: { "video_1" => "test", "video_2" => "test" },
        genre: "app_automate"
      }
      @video_utility_instance.instance_variable_set(:@skip_recording, true)
      expect(Utils).not_to receive(:write_to_file).with(upload_req_file, json_data.to_json)
      @video_utility_instance.create_upload_request(upload_req_file, json_data)
    end
  end

  describe '.create_upload_request_for_video' do
    it 'should create an upload request for uploader' do
      file_name = "/tmp/device_id/session_id/video_rec_#{Time.now.to_i}.mp4"
      upload_req_file = "/usr/local/.browserstack/files_to_be_processed/files_to_upload/VIDEO/video_upload_1234.json"
      video_params = {
        "video_1" => "test",
        "video_2" => "test",
        "random" => "random"
      }
      json_data = {
        upload_type: "video",
        file_name: file_name,
        video_params: { "video_1" => "test", "video_2" => "test" },
        genre: "app_automate"
      }
      @video_utility_instance.instance_variable_set(:@genre, "app_automate")
      expect(SecureRandom).to receive(:uuid).and_return("1234")
      expect(@video_utility_instance).to receive(:create_upload_request).with(upload_req_file, json_data)
      @video_utility_instance.create_upload_request_for_video(file_name, video_params )
    end
  end

  describe '.stop_file' do
    it 'should return stop file path' do
      result = @video_utility_instance.stop_file
      expect(result).to eq("/tmp/device_id/session_id/stop_video_recording")
    end
  end

  describe '.idevicevideoproxy_stop' do
    it 'should kill all idevicevideoproxy processes' do
      expect(OSUtils).to receive(:kill_all_processes).with("idevicevideoproxy", "device_id")
      @video_utility_instance.idevicevideoproxy_stop
    end
  end
end
