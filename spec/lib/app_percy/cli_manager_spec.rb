require_relative '../../spec_helper'
require_relative '../../../lib/app_percy/cli_manager'

describe AppPercy::CLIManager do
  let(:device_id) { '123ABC' }
  let(:device) do
    {
      'device_name' => 'iPhone XS',
      'port' => rand(1..10000)
    }
  end
  let(:params) do
    {
      'app_percy' => {
        'env' => {
          'PERCY_TOKEN' => 'token',
          'PERCY_BRANCH' => 'branch'
        }
      },
      'automate_session_id' => '123'
    }
  end
  let(:logging_dir) { '/var/log/browserstack' }
  let(:session_id) { '123' }
  let(:cli_port) { "5#{device['port']}" }
  let(:logging_dir) { "/var/log/browserstack" }
  let(:cli_manager) { AppPercy::CLIManager.new(device_id) }

  before(:each) do
    allow(DeviceManager).to receive(:device_configuration_check).with(device_id).and_return(device)
  end

  describe '#start_percy_cli' do
    it "should call start command with correct env variables" do
      cli_start_command =
        "PERCY_TOKEN='token' PERCY_BRANCH='branch' " \
        "percy app exec:start --port #{cli_port} " \
        " > #{logging_dir}/percy_cli.123_#{cli_port}.log 2>&1"

      expect(cli_manager).to receive(:system).with(cli_start_command)
      thread = cli_manager.start_percy_cli(params)
      thread.join
    end
  end

  describe '#stop_percy_cli' do
    it 'should kill cli process and return true' do
      expect(BrowserStack::OSUtils).to receive(:is_process_running?).with("percy\\\ app\\\ exec:start", "'port #{cli_port}'").and_return(true)
      expect(cli_manager).to receive(:system).with(
        "percy app exec:stop --port #{cli_port}"
      ).and_return(true)
      expect(BrowserStack::OSUtils).to receive(:is_process_running?).exactly(2).with("percy\\\ app\\\ exec:start", "'port #{cli_port}'").and_return(false)
      expect(cli_manager.stop_percy_cli).to eql(true)
    end

    it 'should return false when the cli process is not killed' do
      expect(BrowserStack::OSUtils).to receive(:is_process_running?).with("percy\\\ app\\\ exec:start", "'port #{cli_port}'").and_return(true)
      expect(cli_manager).to receive(:system).with(
        "percy app exec:stop --port #{cli_port}"
      ).and_return(true)
      expect(BrowserStack::OSUtils).to receive(:is_process_running?).exactly(5).times.with("percy\\\ app\\\ exec:start", "'port #{cli_port}'").and_return(true)
      expect(cli_manager.stop_percy_cli).to eql(false)
    end

    it 'should return true when the cli process is killed via kill process' do
      expect(BrowserStack::OSUtils).to receive(:is_process_running?).with("percy\\\ app\\\ exec:start", "'port #{cli_port}'").and_return(true)
      expect(cli_manager).to receive(:system).with(
        "percy app exec:stop --port #{cli_port}"
      ).and_return(true)
      expect(BrowserStack::OSUtils).to receive(:is_process_running?).exactly(5).times.with("percy\\\ app\\\ exec:start", "'port #{cli_port}'").and_return(true, true, true, true, false)
      expect(BrowserStack::OSUtils).to receive(:kill_process).with("percy\\\ app\\\ exec:start", "'port #{cli_port}'").and_return(true)
      expect(cli_manager.stop_percy_cli).to eql(true)
    end

    it 'should return false when the cli process is not killed via kill process' do
      expect(BrowserStack::OSUtils).to receive(:is_process_running?).with("percy\\\ app\\\ exec:start", "'port #{cli_port}'").and_return(true)
      expect(cli_manager).to receive(:system).with(
        "percy app exec:stop --port #{cli_port}"
      ).and_return(true)
      expect(BrowserStack::OSUtils).to receive(:is_process_running?).exactly(5).times.with("percy\\\ app\\\ exec:start", "'port #{cli_port}'").and_return(true)
      expect(BrowserStack::OSUtils).to receive(:kill_process).with("percy\\\ app\\\ exec:start", "'port #{cli_port}'").and_return(false)
      expect(cli_manager.stop_percy_cli).to eql(false)
    end
  end

  describe '#cli_running?' do
    it 'should kill cli process and return true' do
      expect(BrowserStack::OSUtils).to receive(:is_process_running?).with("percy\\\ app\\\ exec:start", "'port #{cli_port}'").and_return(true)
      expect(cli_manager.cli_running?).to eql(true)
    end

    it 'should return false when the cli process is not killed' do
      expect(BrowserStack::OSUtils).to receive(:is_process_running?).with("percy\\\ app\\\ exec:start", "'port #{cli_port}'").and_return(false)
      expect(cli_manager.cli_running?).to eql(false)
    end
  end

  describe '#cli_check' do
    it "logs once if cli is running on first check" do
      expect(BrowserStack::OSUtils).to receive(:is_process_running?).with("percy\\\ app\\\ exec:start", "'port #{cli_port}'").and_return(true)
      expect(BrowserStack.logger).to receive(:info).exactly(1).times
      cli_manager.cli_check
    end

    it "logs twice if cli is running on second check" do
      expect(BrowserStack::OSUtils).to receive(:is_process_running?).with("percy\\\ app\\\ exec:start", "'port #{cli_port}'").and_return(false, true)
      expect(BrowserStack.logger).to receive(:info).exactly(2).times
      cli_manager.cli_check
    end

    it "raises timeout error if cli is timing out" do
      allow(BrowserStack::OSUtils).to receive(:is_process_running?).with("percy\\\ app\\\ exec:start", "'port #{cli_port}'").and_return(false)
      expect(BrowserStack.logger).to receive(:info).with(/App Percy Timed out waiting CLI to start/)
      cli_manager.cli_check
    end
  end

  describe '#cli_port' do
    it 'should return the cli port' do
      expect(AppPercy::CLIManager.cli_port(device['port'])).to eql(cli_port)
    end
  end

  describe '#cli_log_file_path' do
    it 'should return the cli_log_file_path' do
      expect(cli_manager.cli_log_file_path(session_id)).to eql("#{logging_dir}/percy_cli.#{session_id}_#{cli_port}.log")
    end
  end

  describe '#force_stop' do
    it 'should kill percy cli' do
      expect(BrowserStack::OSUtils).to receive(:kill_process).with("percy\\ app\\ exec:start", "'port 5#{device['port']}'").and_return(nil)
      AppPercy::CLIManager.force_stop(device['port'])
    end
  end
end
