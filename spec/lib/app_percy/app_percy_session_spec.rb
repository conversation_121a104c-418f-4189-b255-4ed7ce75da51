require 'app_percy_utils'
require_relative '../../spec_helper'
require_relative '../../../lib/app_percy/app_percy_session'

describe AppPercy::Session do
  let(:device_id) { '123ABC' }
  let(:session_id) { 'ABC' }
  let(:params) do
    {
      'app_percy' => {
        'env' => {
          'PERCY_TOKEN' => 'token',
          'PERCY_PARALLEL_TOTAL' => 2,
          'PERCY_PARALLEL_NONCE' => 'nonce'
        }
      }
    }
  end
  let(:device) do
    {
      'device_name' => 'iPhone XS',
      'port' => rand(1..10000)
    }
  end
  let(:app_percy_session) { AppPercy::Session.new(device_id) }

  before(:each) do
    allow(DeviceManager).to receive(:device_configuration_check).with(device_id).and_return(device)
    expect(app_percy_session.cli_manager).not_to be(nil)
  end

  describe '#start_percy_cli' do
    it 'should receive start percy ClI' do
      allow(AppPercy::Util).to receive(:push_to_zombie).and_return(nil)
      expect(app_percy_session.cli_manager).to receive(:start_percy_cli).with(params)
      expect(app_percy_session.cli_manager).to receive(:cli_check).and_return(true)
      app_percy_session.start_percy_cli(params)
    end
  end

  describe '#stop_percy_cli' do
    it('should stop CLI') do
      allow(AppPercy::Util).to receive(:push_to_zombie).and_return(nil)
      expect(app_percy_session.cli_manager).to receive(:stop_percy_cli).and_return(true)
      expect(app_percy_session.stop_percy_cli(session_id)).to eql(true)
    end

    it('should return false') do
      allow(AppPercy::Util).to receive(:push_to_zombie).and_return(nil)
      expect(app_percy_session.cli_manager).to receive(:stop_percy_cli).and_return(false)
      expect(app_percy_session.stop_percy_cli(session_id)).to eql(false)
    end
  end

  describe '#org_limit_reached?' do
    context('when CLI started successfully') do
      it('should return false') do
        expect(File).to receive(:read).and_return("Percy CLI started")
        expect(app_percy_session.org_limit_reached?(session_id)).to eql(false)
      end
    end

    context('when CLI did not start due to limit reached') do
      it('should return billing URL') do
        expect(File).to receive(:read).and_return(
          "Error: This organization has exceeded the limits of the Percy BrowserStack plan." \
          "Administrators can upgrade here: https://percy.io/organizations/percy/billing"
        )
        expect(app_percy_session.org_limit_reached?(session_id)).to eql("https://percy.io/organizations/percy/billing")
      end
    end
  end
end
