require 'fileutils'

require_relative '../../../lib/provisioning/ppuid_file'

describe PpuidFile do
  before do
    stub_const('CONFIG_ROOT', '/tmp/')
  end

  let(:udid) { 'abcd' }
  let(:ppuid_contents) { ['myBranch', 'myTeamID', 'myPPUID'] }
  let(:ppuid_file_path) { File.join(CONFIG_ROOT, "ppuid_#{udid}") }

  before { File.write(ppuid_file_path, ppuid_contents.join("\n")) }
  after  { FileUtils.rm_rf(ppuid_file_path) }

  subject { PpuidFile.new(udid) }

  describe '#valid?' do
    it 'returns true if ppuid file exists and has three lines' do
      expect(subject.valid?).to be(true)
    end

    it 'returns false if error raised while accessing ppuid file contents' do
      FileUtils.rm_rf(ppuid_file_path)
      expect(subject.valid?).to be(false)
    end
  end

  describe '#contents' do
    it 'reads contents of ppuid file to array' do
      expect(subject.contents).to eq(ppuid_contents)
    end

    it 'raises error if ppuid file does not exist' do
      FileUtils.rm_rf(ppuid_file_path)
      expect { subject.contents }.to raise_error(/File not found/)
    end

    it 'raises error if ppuid file exists but has no lines' do
      File.write(ppuid_file_path, '')
      expect { subject.contents }.to raise_error(/Invalid ppuid file/)
    end
  end

  describe '#branch_name' do
    it { expect(subject.branch_name).to eq('myBranch') }
  end

  describe '#team_id' do
    it { expect(subject.team_id).to eq('myTeamID') }
  end

  describe '#ppuid' do
    it { expect(subject.ppuid).to eq('myPPUID') }
  end

  describe '#write' do
    let(:path) { "/tmp/ppuid_#{udid}" }
    let(:new_values) { ['newBranch', 'newTeamID', 'newPPUID'] }

    it 'writes ppuid file, updates @contents' do
      FileUtils.rm_rf(ppuid_file_path)

      subject.write(*new_values)

      expect(File.readlines(ppuid_file_path, chomp: true)).to eq(new_values)
      expect(subject.contents).to eq(new_values)
    end

    it 'updates existing ppuid if present' do
      expect(File.exist?(ppuid_file_path)).to eq(true)
      expect(subject.contents).to eq(['myBranch', 'myTeamID', 'myPPUID'])

      subject.write(*new_values)

      expect(File.readlines(ppuid_file_path, chomp: true)).to eq(new_values)
      expect(subject.contents).to eq(new_values)
    end
  end
end
