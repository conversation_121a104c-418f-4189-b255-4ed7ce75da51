require_relative '../../../lib/provisioning/fastlane_match'

describe FastlaneMatch do
  let(:branch) { 'production_x_ddmmyyyy' }
  let(:match_runner) { double('mock_match_runner') }
  let(:device_id) { 'abcdefg' }

  subject { FastlaneMatch.new(device_id) }

  before do
    allow(Match::Runner).to receive(:new).and_return(match_runner)
    allow(match_runner).to receive(:run)
    allow(IdeviceUtils).to receive(:os).with(device_id).and_return('ios')
  end

  describe '#run' do
    let(:env_vars) { { 'uuid' => '1234', 'team-id' => 'myTeamID' } }

    it 'runs match and then returns fastlane env variables' do
      expect(subject).to receive(:match).with(branch)
      expect(subject).to receive(:fetch_fastlane_match_environment_variables).and_return(env_vars)

      expect(subject.run(branch)).to eq(['1234', 'myTeamID'])
    end
  end

  describe '#match' do
    let(:platform) { 'ios' }
    let(:params) do
      {
        app_identifier: '*',
        force: false,
        force_for_new_devices: false,
        git_branch: branch,
        git_url: '**************:browserstack/realmobile-certificates.git',
        keychain_name: '/Users/<USER>/Library/Keychains/Browserstack.keychain-db',
        keychain_password: 'passw0rd',
        platform: platform,
        readonly: true,
        shallow_clone: false,
        skip_confirmation: false,
        skip_docs: false,
        storage_mode: 'git',
        team_id: nil,
        team_name: nil,
        type: "development",
        username: nil,
        verbose: false,
        workspace: nil,
        clone_branch_directly: true
      }
    end

    it 'runs match with correct params' do
      expect(match_runner).to receive(:run).with(params)
      subject.send(:match, branch)
    end

    it 'sets up required environmental variables before match' do
      subject.send(:match, branch)
      # Password is different according to the env
      expect(ENV['MATCH_PASSWORD'].empty?).to be(false)
      expect(ENV['GIT_SSH_COMMAND']).to match(%r{ssh -i /Users/<USER>/.})
    end

    context 'is for an appletv device' do
      let(:platform) { 'tvos' }
      before(:each) do
        allow(IdeviceUtils).to receive(:os).with(device_id).and_return('tvos')
      end

      it 'runs match with platform as tvos' do
        expect(match_runner).to receive(:run).with(params)
        subject.send(:match, branch)
      end
    end
  end

  describe '#fetch_fastlane_match_environment_variables' do
    it 'fetches env variables' do
      ENV['sigh_*_development'] = 'myUDID'
      ENV['sigh_*_development_team-id'] = 'myTeamID'
      expect(subject.send(:fetch_fastlane_match_environment_variables))
        .to eq({ 'uuid' => 'myUDID', 'team-id' => 'myTeamID' })
    end
  end
end