require 'fileutils'

require_relative '../../../lib/provisioning/provisioning_manager'
require_relative '../../spec_helper'

describe ProvisioningManager do
  let(:udid) { 'abcd' }
  let(:device_name) { 'iPhone XS' }

  let(:device_state) { double('mock_device_state') }

  let(:ppuid) { 'myPPUID' }
  let(:team_id) { 'myTeamID' }
  let(:ppuid_file) { double('mock_ppuid_file') }

  let(:profile) { double('mock_profile') }
  let(:certificate) { double('mock_certificate') }
  let(:cert_expiry) { Time.parse('2100-01-01') }
  let(:cert_sha1) { 'ABCDEFG123456789' }

  let(:branch_name) { 'production_1_01012020' }
  let(:profile_config) do
    {
      branch_name => {
        'ppuid' => ppuid,
        'team_id' => team_id,
        'certificate_expiry' => cert_expiry.to_s,
        'config_update_time' => '2021-04-28 22:21'
      }
    }
  end

  before do
    allow(DeviceState).to receive(:new).with(udid).and_return(device_state)

    allow(PpuidFile).to receive(:new).and_return(ppuid_file)
    allow(ppuid_file).to receive(:valid?).and_return(true)
    allow(ppuid_file).to receive(:ppuid).and_return(ppuid)
    allow(ppuid_file).to receive(:branch_name).and_return(branch_name)

    allow(ProvisioningProfile).to receive(:new).and_return(profile)
    allow(profile).to receive(:ppuid).and_return(ppuid)
    allow(profile).to receive(:team_id).and_return(team_id)
    allow(profile).to receive(:developer_certificate).and_return(certificate)
    allow(profile).to receive(:developer_certificate_sha1).and_return(cert_sha1)
    allow(certificate).to receive(:not_after).and_return(cert_expiry)
  end

  subject { ProvisioningManager.new(udid) }

  describe '#update' do
    before do
      allow(subject).to receive(:fetch_branch_name_or_request_provisioning).and_return(branch_name)
      allow(subject).to receive(:current_branch_names).and_return([branch_name])
      allow(device_state).to receive(:rotate_certificate_file_present?).and_return(false)
      allow(device_state).to receive(:update_provisioning_profile_file_present?).and_return(false)
      allow(subject).to receive(:download_provisioning_profile).with(branch_name)
      allow(device_state).to receive(:touch_rebuild_browserstack_app_file)
      allow(device_state).to receive(:remove_rotate_certificate_file)
      allow(device_state).to receive(:remove_update_provisioning_profile_file)
    end

    it 'raises error if device needs to be registered' do
      expect(subject).to receive(:fetch_branch_name_or_request_provisioning)
        .with(device_name).and_raise(ProvisioningServerError)

      expect { subject.update(device_name) }.to raise_error(ProvisioningServerError)
    end

    it 'raises error if branch from server is not in provisioning_profile_branches.yml' do
      expect(subject).to receive(:current_branch_names).and_return([])
      expect { subject.update(device_name) }.to raise_error(ProvisioningError)
    end

    context 'branch name is not in provisioning_profile_config.json' do
      before do
        allow(ProvisioningManager).to receive(:read_profile_config).and_return({})
      end

      it 'downloads provisioning profile' do
        expect(subject).not_to receive(:valid_provisioning_profile?)
        expect(subject).to receive(:download_provisioning_profile).with(branch_name)
        expect(device_state).to receive(:touch_rebuild_browserstack_app_file)
        expect(device_state).to receive(:remove_rotate_certificate_file)
        expect(device_state).to receive(:remove_update_provisioning_profile_file)
        subject.update(device_name)
      end
    end

    context 'branch name is in provisioning_profile_config.json' do
      before do
        allow(ProvisioningManager).to receive(:read_profile_config).and_return(profile_config)
      end

      it 'downloads provisioning profile anyway if rotate certificate file is present' do
        expect(device_state).to receive(:rotate_certificate_file_present?).and_return(true)
        expect(subject).not_to receive(:valid_provisioning_profile?)
        expect(subject).to receive(:download_provisioning_profile).with(branch_name)
        expect(device_state).to receive(:touch_rebuild_browserstack_app_file)
        expect(device_state).to receive(:remove_rotate_certificate_file)
        expect(device_state).to receive(:remove_update_provisioning_profile_file)
        subject.update(device_name)
      end

      it 'confims profile from config is valid, writes ppuid file and returns' do
        path = '/path/to/myPPUID.mobileprovision'
        expect(MobileprovisionFile).to receive(:path).with(ppuid: ppuid).and_return(path)
        expect(ProvisioningProfile).to receive(:new).with(path).and_return(profile)
        expect(subject).to receive(:valid_provisioning_profile?)
          .with(profile: profile).and_return(true)
        expect(ppuid_file).to receive(:write).with(branch_name, team_id, ppuid)
        expect(device_state).to receive(:remove_update_provisioning_profile_file)
        subject.update(device_name)
      end

      it 'downloads new profile if profile from config is not valid' do
        path = '/path/to/myPPUID.mobileprovision'
        expect(MobileprovisionFile).to receive(:path).with(ppuid: ppuid).and_return(path)
        expect(ProvisioningProfile).to receive(:new).with(path)
        expect(subject).to receive(:valid_provisioning_profile?).and_return(false)
        expect(subject).to receive(:download_provisioning_profile).with(branch_name)
        expect(device_state).to receive(:touch_rebuild_browserstack_app_file)
        expect(device_state).to receive(:remove_rotate_certificate_file)
        expect(device_state).to receive(:remove_update_provisioning_profile_file)
        subject.update(device_name)
      end
    end

    context 'update provisioning profile file is present' do
      before do
        allow(device_state).to receive(:update_provisioning_profile_file_present?).and_return(true)
      end

      it 'removes update provisioning profile file and returns early' do
        expect(device_state).to receive(:remove_update_provisioning_profile_file)
        expect(ProvisioningManager).not_to receive(:read_profile_config)
        subject.update(device_name)
      end

      context 'branch name is production_61_15022023' do
        let(:branch_name) { 'production_61_15022023' }

        it 'proceeds with downloading the provisioning profile' do
          expect(subject).to receive(:download_provisioning_profile).with(branch_name)
          subject.update(device_name)
        end
      end
    end
  end

  describe '#problem_detected?' do
    before do
      allow(device_state).to receive(:rotate_certificate_file_present?).and_return(false)
      allow(device_state).to receive(:update_provisioning_profile_file_present?).and_return(false)
      allow(ppuid_file).to receive(:valid?).and_return(true)
      allow(subject).to receive(:current_branch_names).and_return([branch_name])
      allow(subject).to receive(:valid_provisioning_profile?).and_return(true)
    end

    it 'returns true if rotate certificate file is present' do
      allow(device_state).to receive(:rotate_certificate_file_present?).and_return(true)
      expect(subject.problem_detected?).to eq(true)
    end

    it 'returns true if update provisioning profile file is present' do
      allow(device_state).to receive(:update_provisioning_profile_file_present?).and_return(true)
      expect(subject.problem_detected?).to eq(true)
    end

    it 'returns true if ppuid file invalid' do
      allow(ppuid_file).to receive(:valid?).and_return(false)
      expect(subject.problem_detected?).to eq(true)
    end

    it 'returns true if branch name not in current_branch_names' do
      allow(subject).to receive(:current_branch_names).and_return([])
      expect(subject.problem_detected?).to eq(true)
    end

    it 'returns true if provisioning profile not valid' do
      allow(subject).to receive(:valid_provisioning_profile?).and_return(false)
      expect(subject.problem_detected?).to eq(true)
    end

    it 'otherwise returns false' do
      expect(subject.problem_detected?).to eq(false)
    end
  end

  describe '#current_branch_names' do
    let(:config_path) { File.expand_path('../../../config', __dir__) }

    before do
      # so test works no matter where the repo is cloned.
      stub_const('CONFIG_PATH', config_path)
    end

    it 'returns a list of branch names' do
      expect(File.exist?("#{config_path}/provisioning_profile_branches.yml")).to be(true)

      branch_names = subject.current_branch_names
      expect(branch_names).to be_an_instance_of(Array)
      expect(branch_names.length).to be > 0
    end
  end

  describe '#valid_provisioning_profile?' do
    it 'return true if valid_provisioning_profile raises no error' do
      expect(subject).to receive(:validate_provisioning_profile).with(profile)
      expect(subject.valid_provisioning_profile?(profile: profile)).to be(true)
    end

    it 'return false if valid_provisioning_profile raises a ProvisioningError' do
      expect(subject).to receive(:validate_provisioning_profile)
        .with(profile).and_raise(ProvisioningError)
      expect(subject.valid_provisioning_profile?(profile: profile)).to be(false)
    end

    it 'return false if valid_provisioning_profile raises a ProvisioningProfileError' do
      expect(subject).to receive(:validate_provisioning_profile)
        .with(profile).and_raise(ProvisioningProfileError)
      expect(subject.valid_provisioning_profile?(profile: profile)).to be(false)
    end

    it 'uses ppuid file profile if no profile passed' do
      expect(subject).to receive(:validate_provisioning_profile).with(profile)
      expect(subject.valid_provisioning_profile?).to be(true)
    end
  end

  describe '#validate_provisioning_profile' do
    let(:identity) { 'Joe Blogs' }

    before do
      allow(profile).to receive(:developer_certificate_expired?).and_return(false)
      allow(profile).to receive(:device_included?).with(udid).and_return(true)
      allow(profile).to receive(:developer_certificate_identity).and_return(identity)
      allow(BrowserStack::OSUtils).to receive(:codesigning_identity_exists?)
        .with(identity).and_return(true)
    end

    it 'raises error if profile\'s developer certificate has expired' do
      allow(profile).to receive(:developer_certificate_expired?).and_return(true)
      expect { subject.validate_provisioning_profile(profile) }.to raise_error(ProvisioningError)
    end

    it 'raises error if udid is not in profile' do
      allow(profile).to receive(:device_included?).with(udid).and_return(false)
      expect { subject.validate_provisioning_profile(profile) }.to raise_error(ProvisioningError)
    end

    it 'raises error if profile\'s cert\'s identity is not in the keychain' do
      allow(BrowserStack::OSUtils).to receive(:codesigning_identity_exists?)
        .with(identity).and_return(false)
      expect { subject.validate_provisioning_profile(profile) }.to raise_error(ProvisioningError)
    end

    it 'otherwise returns without error' do
      expect { subject.validate_provisioning_profile(profile) }.not_to raise_error
    end
  end

  describe '#fetch_branch_name_or_request_provisioning' do
    let(:client) { double('mock_provisioning_server_client') }

    before do
      allow(ProvisioningServerClient).to receive(:new).and_return(client)
    end

    it 'returns branch name if device is provisioned' do
      response_body = { 'already_provisioned' => true, 'account_name' => branch_name }.to_json
      expect(client).to receive(:request_device_info).with(udid).and_return(response_body)
      expect(client).not_to receive(:request_add_device)

      expect(subject.fetch_branch_name_or_request_provisioning(device_name)).to eq(branch_name)
    end

    it 'requests add_device if request_device_info fails' do
      expect(client).to receive(:request_device_info).with(udid).and_raise(ProvisioningServerError)
      expect(client).to receive(:request_add_device).and_raise(ProvisioningServerError)

      expect { subject.fetch_branch_name_or_request_provisioning(device_name) }
        .to raise_error(ProvisioningServerError)
    end

    it 'requests add_device if device is not yet provisioned' do
      response_body = { 'already_provisioned' => false, 'account_name' => branch_name }.to_json
      expect(client).to receive(:request_device_info).with(udid).and_return(response_body)
      expect(client).to receive(:request_add_device).and_raise(ProvisioningServerError)

      expect { subject.fetch_branch_name_or_request_provisioning(device_name) }
        .to raise_error(ProvisioningServerError)
    end
  end

  describe '#download_provisioning_profile' do
    let(:fastlane_match) { double('mock_fastlane_match') }
    let(:new_profile) { double('mock_new_profile') }
    let(:new_ppuid) { 'newPPUID' }
    let(:new_team_id) { 'newTeamID' }

    before do
      allow(FastlaneMatch).to receive(:new).and_return(fastlane_match)
      allow(new_profile).to receive(:ppuid).and_return(new_ppuid)
      allow(new_profile).to receive(:team_id).and_return(new_team_id)
      allow(device_state).to receive(:update_provisioning_profile_file_present?).and_return(false)
    end

    it 'runs fastlane match, validates profile and updates config/ppuid file' do
      output = [new_ppuid, new_team_id]
      expect(fastlane_match).to receive(:run).with(branch_name).and_return(output)

      new_path = '/path/to/newPPUID.mobileprovision'
      expect(MobileprovisionFile).to receive(:path).with(ppuid: 'newPPUID').and_return(new_path)
      expect(ProvisioningProfile).to receive(:new).with(new_path).and_return(new_profile)
      expect(subject).to receive(:validate_provisioning_profile).with(new_profile)

      expect(ProvisioningManager).to receive(:update_profile_config).with(branch_name, new_profile)
      expect(ppuid_file).to receive(:write).with(branch_name, new_team_id, new_ppuid)

      subject.download_provisioning_profile(branch_name)
    end

    it 'raises ProvisioningError error if fastlane fails' do
      output = [new_ppuid, new_team_id]
      expect(fastlane_match).to receive(:run).with(branch_name).and_raise(RuntimeError)

      expect { subject.download_provisioning_profile(branch_name) }.to raise_error(ProvisioningError)
    end
  end

  describe '#read_profile_config' do
    it 'returns parsed json config' do
      File.write(PROVISIONING_PROFILE_CONFIG, profile_config.to_json)
      expect(ProvisioningManager.read_profile_config).to eq(profile_config)
      FileUtils.rm_rf(PROVISIONING_PROFILE_CONFIG)
    end

    it 'returns {} if file does not exist' do
      expect(ProvisioningManager.read_profile_config).to eq({})
    end

    it 'returns {} if file does not contain valid json' do
      FileUtils.touch(PROVISIONING_PROFILE_CONFIG)
      expect(ProvisioningManager.read_profile_config).to eq({})
    end
  end

  describe '#update_profile_config' do
    let(:now) { Time.parse('2022-04-29 08:41') }

    before do
      allow(Time).to receive(:now).and_return(now)
      File.write(PROVISIONING_PROFILE_CONFIG, profile_config.to_json)
    end

    after { FileUtils.rm_rf(PROVISIONING_PROFILE_CONFIG) }

    it 'writes new profile to config' do
      expected_new_config = {
        branch_name => {
          'ppuid' => ppuid,
          'team_id' => team_id,
          'certificate_expiry' => cert_expiry.to_s,
          'config_update_time' => '2021-04-28 22:21'
        },
        'production_2_01012022' => {
          'ppuid' => ppuid,
          'team_id' => team_id,
          'certificate_expiry' => cert_expiry.to_s,
          'config_update_time' => now.utc.to_s
        }
      }

      ProvisioningManager.update_profile_config('production_2_01012022', profile)

      profile_config = JSON.parse(File.read(PROVISIONING_PROFILE_CONFIG))
      expect(profile_config).to eq(expected_new_config)
    end

    it 'updates existing profile in config' do
      expected_new_config = {
        branch_name => {
          'ppuid' => 'newPPUID',
          'team_id' => team_id,
          'certificate_expiry' => cert_expiry.to_s,
          'config_update_time' => now.utc.to_s
        }
      }

      allow(profile).to receive(:ppuid).and_return('newPPUID')
      ProvisioningManager.update_profile_config(branch_name, profile)

      profile_config = JSON.parse(File.read(PROVISIONING_PROFILE_CONFIG))
      expect(profile_config).to eq(expected_new_config)
    end
  end

  describe '#ppuid_file_profile' do
    it 'initialises provisioning profile obj from ppuid in ppuid file' do
      path = '/path/to/myPPUID.mobileprovision'
      expect(MobileprovisionFile).to receive(:path).with(ppuid: ppuid).and_return(path)
      expect(ProvisioningProfile).to receive(:new).with(path).and_return(profile)
      subject.ppuid_file_profile
    end
  end

  describe '#error' do
    it 'raises a ProvisioningError' do
      expect { subject.error('boom!') }.to raise_error(ProvisioningError)
    end
  end
end
