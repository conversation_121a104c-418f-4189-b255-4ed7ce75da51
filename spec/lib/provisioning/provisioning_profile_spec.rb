require 'fileutils'

require_relative '../../../lib/provisioning/provisioning_profile'
require_relative '../../spec_helper'

describe ProvisioningProfile do
  let(:mobileprovision_plist) do
    fixture('7f0be347-a7bf-49a7-978b-7ac6c3dc469c.mobileprovision.plist')
  end

  let(:mobileprovision_file) { '/tmp/mobileprovision_file' }

  before do
    FileUtils.touch('/tmp/mobileprovision_file')
    allow(BrowserStack::OSUtils).to receive(:execute)
      .with(/security/, true).and_return([mobileprovision_plist, 0])
  end

  after  { FileUtils.rm_rf('/tmp/mobileprovision_file') }

  subject { ProvisioningProfile.new(mobileprovision_file) }

  describe '#data' do
    it 'calls #parse_mobileprovision' do
      expect(subject).to receive(:parse_mobileprovision)
      subject.data
    end
  end

  describe '#parse_mobileprovision' do
    it 'raises error if mobileprovision file not found' do
      subject = ProvisioningProfile.new('')
      expect { subject.parse_mobileprovision }.to raise_error(ProvisioningProfileError)
    end

    it 'executes security command to read mobileprovision file' do
      subject.parse_mobileprovision
    end

    it 'raises error if security command is not successful' do
      expect(BrowserStack::OSUtils).to receive(:execute)
        .with(/security/, true).and_return(['', 1])
      expect { subject.parse_mobileprovision }.to raise_error(ProvisioningProfileError)
    end

    it 'returns hash from output plist' do
      expect(subject.parse_mobileprovision).to be_a(Hash)
    end
  end

  describe '#ppuid' do
    it { expect(subject.ppuid).to eq('7f0be347-a7bf-49a7-978b-7ac6c3dc469c') }
  end

  describe '#entitlements' do
    it do
      entitlements = {
        "application-identifier" => "6P6ZL7YXV5.*",
        "com.apple.developer.team-identifier" => "6P6ZL7YXV5",
        "get-task-allow" => true,
        "keychain-access-groups" => ["6P6ZL7YXV5.*"]
      }
      expect(subject.entitlements).to eq(entitlements)
    end
  end

  describe '#team_name' do
    it { expect(subject.team_name).to eq('Siddharth Lamba') }
  end

  describe '#team_id' do
    it { expect(subject.team_id).to eq('6P6ZL7YXV5') }
  end

  describe '#developer_certificate' do
    it 'returns certificate as OpenSSL object' do
      expect(subject.developer_certificate).to be_a(OpenSSL::X509::Certificate)
    end
  end

  describe '#developer_certificate_expired?' do
    it 'returns true if certificate has expired' do
      expect(subject.developer_certificate_expired?).to be(true)
    end

    # Cert expired on 2019-10-15
    it 'returns false if certificate has not yet expired' do
      allow(Time).to receive(:now).and_return(Time.parse("2019-01-01"))
      expect(subject.developer_certificate_expired?).to be(false)
    end
  end

  describe '#developer_certificate_sha1' do
    it do
      expect(subject.developer_certificate_sha1)
        .to eq('D506754731F58F5994933045FB0039F9C5222C47')
    end
  end

  describe '#device_included?' do
    it 'returns true for devices that are in the profile' do
      expect(subject.device_included?('00008020-001D48393A30003A')).to be(true)
    end

    it 'returns false for devices that are not in the profile' do
      expect(subject.device_included?('abcd')).to be(false)
    end
  end

  describe '#error' do
    it 'raises a ProvisioningProfileError' do
      expect { subject.error('boom!') }.to raise_error(ProvisioningProfileError)
    end
  end
end
