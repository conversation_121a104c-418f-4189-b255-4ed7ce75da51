require_relative '../../../lib/provisioning/mobileprovision_file'

describe MobileprovisionFile do
  let(:udid) { 'abcd' }
  let(:ppuid) { 'myPPUID' }
  let(:mobileprovision_file) { File.join(PROVISIONING_PROFILE_DIR, "#{ppuid}.mobileprovision") }

  describe '.path' do
    it 'raises an error if passed nothing' do
      expect { MobileprovisionFile.path }.to raise_error(/either ppuid or device udid/)
    end

    it 'returns correct path when passed ppuid' do
      expect(MobileprovisionFile.path(ppuid: ppuid)).to eq(mobileprovision_file)
    end

    it 'uses only ppuid if passed both ppuid and udid' do
      expect(PpuidFile).not_to receive(:new)
      expect(MobileprovisionFile.path(ppuid: ppuid, udid: udid)).to eq(mobileprovision_file)
    end

    context 'when reading ppuid from file' do
      let(:ppuid_contents) { ['myBranch', 'myTeamID', ppuid] }
      let(:ppuid_file_path) { File.join(CONFIG_ROOT, "ppuid_#{udid}") }

      it 'returns correct path when passed udid' do
        File.write(ppuid_file_path, ppuid_contents.join("\n"))
        expect(MobileprovisionFile.path(udid: udid)).to eq(mobileprovision_file)
        FileUtils.rm_rf(ppuid_file_path)
      end

      it 'raises an error if device does not have a ppuid file' do
        FileUtils.rm_rf(ppuid_file_path)
        expect { MobileprovisionFile.path(udid: udid) }.to raise_error(/File not found/)
      end
    end
  end
end
