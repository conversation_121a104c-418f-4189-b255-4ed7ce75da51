require_relative '../../../lib/provisioning/provisioning_server_client'
require_relative '../../spec_helper'

describe ProvisioningServerClient do
  let(:udid) { 'abcd' }
  let(:device_name) { 'iPhone XS' }
  let(:server) { 'https://provisioning-server.browserstack.com' }
  let(:auth_config) { { user: "admin", password: "password" }.to_json }

  subject { ProvisioningServerClient.new }

  before(:each) do
    allow(File).to receive(:read).and_return(auth_config)
  end

  describe '#request_device_info' do
    it 'returns device info if response code is 200' do
      body = {
        device_id: udid,
        device_type: 'iphone',
        model: device_name,
        already_provisioned: true
      }.to_json

      stub_request(:get, "#{server}/devices/#{udid}").to_return(body: body, status: 200)
      expect(subject.request_device_info(udid)).to eq(body)
    end

    it 'raises "Device not found" if status code = 404' do
      stub_request(:get, "#{server}/devices/#{udid}").to_return(body: nil, status: 404)
      expect { subject.request_device_info(udid) }
        .to raise_error(ProvisioningServerError, 'Device not found')
    end

    it 'raises generic error if status code is anything else' do
      stub_request(:get, "#{server}/devices/#{udid}").to_return(body: nil, status: 500)
      expect { subject.request_device_info(udid) }.to raise_error(ProvisioningServerError)
    end
  end

  describe '#request_add_device' do
    let(:data) do
      {
        'device_id' => udid,
        'env' => 'production',
        'device_type' => 'iphone'
      }.to_json
    end

    it 'returns device info if response code is 202' do
      # Sucessful requests to this endpoint return a json with "msg" as key, while
      # unsuccessful requests return a json with "message" as key.
      # TODO: Fix this inconsistency in the provisioning server.
      body = { msg: 'Request received' }.to_json
      stub_request(:post, "#{server}/add_device")
        .with(body: data).to_return(body: body, status: 202)

      expect { subject.request_add_device(udid, device_name) }
        .to raise_error(ProvisioningServerError, 'Awaiting provisioning')
    end

    it 'raises "Can\'t provision" if status code = 400' do
      body = { message: 'Device already registered' }.to_json
      stub_request(:post, "#{server}/add_device")
        .with(body: data).to_return(body: body, status: 400)

      expect { subject.request_add_device(udid, device_name) }
        .to raise_error(ProvisioningServerError, "Can't provision: #{body}")
    end

    it 'raises "Can\'t provision" if status code = 500' do
      body = { message: 'Not enough space in any of the accounts' }.to_json
      stub_request(:post, "#{server}/add_device")
        .with(body: data).to_return(body: body, status: 500)

      expect { subject.request_add_device(udid, device_name) }
        .to raise_error(ProvisioningServerError, "Can't provision: #{body}")
    end

    it 'raises generic error if status code is anything else' do
      stub_request(:post, "#{server}/add_device")
        .with(body: data).to_return(body: nil, status: 404)

      expect { subject.request_add_device(udid, device_name) }
        .to raise_error(ProvisioningServerError)
    end

    context 'device is an Apple TV'  do
      let(:device_name) { 'AppleTV14,1' }
      let(:data) do
        {
          'device_id' => udid,
          'env' => 'production',
          'device_type' => 'appletv'
        }.to_json
      end

      it 'sends POST request with device_type "appletv"' do
        body = { msg: 'Request received' }.to_json
        stub_request(:post, "#{server}/add_device")
          .with(body: data).to_return(body: body, status: 202)

        expect { subject.request_add_device(udid, device_name) }
          .to raise_error(ProvisioningServerError, 'Awaiting provisioning')
      end
    end
  end

  describe '#error' do
    it 'raises a ProvisioningServerError' do
      expect { subject.error('boom!') }.to raise_error(ProvisioningServerError)
    end
  end
end