require_relative '../../lib/version'

describe BrowserStack::Version do
  describe '.initialize' do
    it 'takes a semver string as an argument' do
      ['9', '9.0', '9.0.4'].each do |version|
        expect { BrowserStack::Version.new version }.not_to raise_exception
      end
    end

    it 'raises ArgumentException for malformed versions' do
      ['9-0-4', '9a', '9.0hello', '9.0.4prealpha-cooldroid',
       '.', '....', '.9.3.3', '9...3'].each do |version|
        expect { BrowserStack::Version.new version }.to raise_exception(ArgumentError)
      end
    end
  end

  describe '#canonical_without_patch' do
    it "formats the version like \#{major}.\#{minor}" do
      expect(BrowserStack::Version.new('9.1.0').canonical_without_patch).to be_eql('9.1')
      expect(BrowserStack::Version.new('9.1.4').canonical_without_patch).to be_eql('9.1')
      expect(BrowserStack::Version.new('9').canonical_without_patch).to be_eql('9.0')
      expect(BrowserStack::Version.new('9.1').canonical_without_patch).to be_eql('9.1')
      expect(BrowserStack::Version.new('94.10.742').canonical_without_patch).to be_eql('94.10')
    end
  end
end
