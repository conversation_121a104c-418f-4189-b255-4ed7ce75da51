require_relative '../spec_helper'
require_relative '../../lib/network_simulator'
require_relative '../../lib/utils/idevice_utils'

describe NetworkSimulator do
  let(:device_id) { "123456" }
  let(:driver_port) { "10001" }
  before do
    @simulator = NetworkSimulator.new(device_id, driver_port)
  end
  def expect_not_nil_working_interfaces
    expect(Utils).to receive(:get_working_interfaces).and_return(["en22"])
  end

  def expect_nil_working_interfaces
    expect(Utils).to receive(:get_working_interfaces).and_return([])
  end

  def expect_nil_list_of_bridge_interfaces
    expect(IdeviceUtils).to receive(:is_network_interface_in_bridge?).with("en22").and_return(false)
  end

  def expect_not_nil_list_of_bridge_interfaces
    expect(IdeviceUtils).to receive(:is_network_interface_in_bridge?).with("en22").and_return(true)
  end
  context "enable no_network mode" do
    it "should not raise error and not call delete_network_interface_from_bridge if bridge_interfaces is nil" do
      expect_not_nil_working_interfaces
      expect_nil_list_of_bridge_interfaces
      expect(IdeviceUtils).not_to receive(:delete_network_interface_from_bridge)
      expect { @simulator.enable_no_network_mode }.to_not raise_error
    end

    it "should not raise error and call delete_network_interface_from_bridge if bridge_interfaces is not nil" do
      expect_not_nil_working_interfaces
      expect_not_nil_list_of_bridge_interfaces
      expect(IdeviceUtils).to receive(:delete_network_interface_from_bridge)
      expect { @simulator.enable_no_network_mode }.to_not raise_error
    end
  end

  context "disable no_network mode" do
    it "should not throw error and add interface to bridge interface's member list if it's not a member of bridge interface and in nat plist" do
      expect_not_nil_working_interfaces
      expect(IdeviceUtils).to receive(:is_network_interface_in_bridge?).and_return(false)
      expect(IdeviceUtils).to receive(:is_network_interface_in_nat_plist?).and_return(true)
      expect(IdeviceUtils).to receive(:add_network_interface_to_bridge).and_return(true)
      expect { @simulator.disable_no_network_mode }.to_not raise_error
    end
  end

  context "apply_offline_mode" do
    it "should apply offline mode by taking interface down" do
      expect(@simulator).to receive(:fetch_working_interfaces_for_device).and_return(["en11"])
      expect(IdeviceUtils).to receive(:update_interface_state).with("en11", "down").and_return(["", 0])

      expect { @simulator.apply_offline_mode }.to_not raise_error
    end

    it "should raise error if offline mode is not applied" do
      expect(@simulator).to receive(:fetch_working_interfaces_for_device).and_return(["en11"])
      expect(IdeviceUtils).to receive(:update_interface_state).with("en11", "down").and_return(["", 1])

      expect { @simulator.apply_offline_mode }.to raise_error("Interface could not be turned down")
    end

    it "should not raise error if one of the interface is not found/any error" do
      expect(@simulator).to receive(:fetch_working_interfaces_for_device).and_return(["en11", "en22"])
      expect(IdeviceUtils).to receive(:update_interface_state).with("en11", "down").and_return(["", 1])
      expect(IdeviceUtils).to receive(:update_interface_state).with("en22", "down").and_return(["", 0])

      expect { @simulator.apply_offline_mode }.to_not raise_error
    end
  end

  context "apply_online_mode" do
    it "should apply online mode by taking interface up" do
      expect(@simulator).to receive(:fetch_working_interfaces_for_device).and_return(["en11"])
      expect(IdeviceUtils).to receive(:update_interface_state).with("en11", "up").and_return(["", 0])

      expect { @simulator.apply_online_mode }.to_not raise_error
    end

    it "should raise error if offline mode is not applied" do
      expect(@simulator).to receive(:fetch_working_interfaces_for_device).and_return(["en11"])
      expect(IdeviceUtils).to receive(:update_interface_state).with("en11", "up").and_return(["", 1])

      expect { @simulator.apply_online_mode }.to raise_error("Interface could not be turned up")
    end

    it "should not raise error if one of the interface is not found/any error" do
      expect(@simulator).to receive(:fetch_working_interfaces_for_device).and_return(["en11", "en22"])
      expect(IdeviceUtils).to receive(:update_interface_state).with("en11", "up").and_return(["", 1])
      expect(IdeviceUtils).to receive(:update_interface_state).with("en22", "up").and_return(["", 0])

      expect { @simulator.apply_online_mode }.to_not raise_error
    end
  end
end
