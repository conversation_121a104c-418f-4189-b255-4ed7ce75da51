require_relative '../../../lib/file_uploaders/video_uploader'
require_relative '../../spec_helper'
require_relative '../../../config/constants'
require_relative '../../../lib/ios_vid_capturer'
require_relative '../../helpers/mock_ios_vid_capturer'

describe 'VideoUploader' do
  before(:each) do
    allow(BrowserStack).to receive(:init_logger)
  end

  it '[Old Flow, No Async video process] : should successfully upload a video' do
    json_data = {
      file_name: "some_random_name.mp4",
      video_params: {
        session_id: "session_id",
        video_file: 'video_file',
        video_aws_keys: 'video_aws_keys',
        video_aws_secret: 'video_aws_secret',
        video_aws_bucket: 'video_aws_bucket',
        video_aws_region: 'us-east-1'
      },
      genre: "app_automate",
      dest: "some_random_s3_path",
      upload_type: 'video'
    }

    videoUploader = VideoUploader.new
    allow(File).to receive(:read).and_return(json_data.to_json)
    allow(File).to receive(:exist?).and_return(false, true)
    expect(Utils).to receive(:upload_file_to_s3).and_return([true, nil])
    expect(File).to receive(:delete)
    videoUploader.process("/some/random/file/path")
  end

  context 'invalid upload_type' do
    it '[Old Flow, No Async video process] : should not upload the file and send data to zombie if upload_type is empty' do
      json_data = {
        file_name: "some_random_name.mp4",
        video_params: {
          session_id: "session_id",
          video_file: 'video_file',
          video_aws_keys: 'video_aws_keys',
          video_aws_secret: 'video_aws_secret',
          video_aws_bucket: 'video_aws_bucket',
          video_aws_region: 'us-east-1'
        },
        genre: "app_automate",
        dest: "some_random_s3_path"
      }

      videoUploader = VideoUploader.new
      allow(File).to receive(:read).and_return(json_data.to_json)
      allow(File).to receive(:exist?).and_return(false)
      expect(Utils).to_not receive(:upload_file_to_s3)
      expect(BrowserStack::Zombie).to receive(:push_logs).with("invalid-file-upload-request", "some_random_name.mp4", { "upload_type": nil, "url": "video" })
      expect(File).to receive(:delete)
      videoUploader.process("/some/random/file/path")
    end

    it '[Old Flow, No Async video process] : should not upload the file and send data to zombie if upload_type is invalid' do
      json_data = {
        file_name: "some_random_name.mp4",
        video_params: {
          session_id: "session_id",
          video_file: 'video_file',
          video_aws_keys: 'video_aws_keys',
          video_aws_secret: 'video_aws_secret',
          video_aws_bucket: 'video_aws_bucket',
          video_aws_region: 'us-east-1'
        },
        genre: "app_automate",
        dest: "some_random_s3_path",
        upload_type: 'invalid_upload_type'
      }

      videoUploader = VideoUploader.new
      allow(File).to receive(:read).and_return(json_data.to_json)
      allow(File).to receive(:exist?).and_return(false)
      expect(Utils).to_not receive(:upload_file_to_s3)
      expect(BrowserStack::Zombie).to receive(:push_logs).with("invalid-file-upload-request", "some_random_name.mp4", { "upload_type": 'invalid_upload_type', "url": "video" })
      expect(File).to receive(:delete)
      videoUploader.process("/some/random/file/path")
    end
  end

  context 'failure scenarios' do
    it '[Old Flow, No Async video process] : should raise exception if request file not found' do
      videoUploader = VideoUploader.new
      expect { videoUploader.process("/some/random/file/path") }.to raise_error(Errno::ENOENT)
    end

    it '[Old Flow, No Async video process] : should raise exception if file to be uploaded not found' do
      json_data = {
        file_name: "some_random_name.mp4",
        video_params: {
          session_id: "session_id",
          video_file: 'video_file',
          video_aws_keys: 'video_aws_keys',
          video_aws_secret: 'video_aws_secret',
          video_aws_bucket: 'video_aws_bucket',
          video_aws_region: 'us-east-1'
        },
        genre: "app_automate",
        dest: "some_random_s3_path",
        upload_type: 'video'
      }

      videoUploader = VideoUploader.new
      allow(File).to receive(:read).and_return(json_data.to_json)
      allow(File).to receive(:exist?).and_return(false, false)
      expect(Utils).to_not receive(:upload_file_to_s3)
      expect(File).to_not receive(:delete)
      expect { videoUploader.process("/some/random/file/path") }.to raise_error("Cannot find some_random_name.mp4 on disc, Cannot upload.")
    end

    it '[Old Flow, No Async video process] : should raise exception if s3 upload failed' do
      json_data = {
        file_name: "some_random_name.mp4",
        video_params: {
          session_id: "session_id",
          video_file: 'video_file',
          video_aws_keys: 'video_aws_keys',
          video_aws_secret: 'video_aws_secret',
          video_aws_bucket: 'video_aws_bucket',
          video_aws_region: 'us-east-1'
        },
        genre: "app_automate",
        dest: "some_random_s3_path",
        upload_type: 'video'
      }

      videoUploader = VideoUploader.new
      allow(File).to receive(:read).and_return(json_data.to_json)
      allow(File).to receive(:exist?).and_return(false, true)
      expect(Utils).to receive(:upload_file_to_s3).and_return([false, "some_error"])
      expect(File).to_not receive(:delete)
      expect { videoUploader.process("/some/random/file/path") }.to raise_error("Error while uploading to S3: some_error")
    end
  end

  it '[New Flow, Async video processing] : Unsuccessful processing of last video part' do
    json_data = {
      file_name: "some_random_name.mp4",
      video_params: {
        video_session_id: "session_id",
        video_file: 'video_file',
        video_aws_keys: 'video_aws_keys',
        video_aws_secret: 'video_aws_secret',
        video_aws_bucket: 'video_aws_bucket',
        video_aws_region: 'us-east-1'
      },
      genre: "app_automate",
      dest: "some_random_s3_path",
      upload_type: 'video',
      async_process_file: 'async_process_file'
    }

    meta_data = {
      session_id: "session_id",
      video_part_name: "__video__0",
      screenshot_counter: "50",
      video_parts_counter: "0",
      video_out_file: "video_out_file",
      workspace: "workspace",
      video_parts_prefix: "__video__",
      fps: "5",
      part_size: "50"
    }

    videoUploader = VideoUploader.new
    mock_object = MockIosVidCapturer.new
    allow(File).to receive(:read).and_return(json_data.to_json, meta_data.to_json)
    allow(File).to receive(:exist?).and_return(true, false, true, false, true)
    expect(IosVidCapturer).to receive(:new).and_return(mock_object)
    expect(Utils).to_not receive(:upload_file_to_s3)
    expect(File).to receive(:delete).twice
    expect(BrowserStack::Zombie).to receive(:push_logs).with("app-ios-video-rec-failure", "mock error", { "session_id" => "session_id" })

    videoUploader.process("/some/random/file/path")
  end

  it '[New Flow, Async video processing] : Successful processing of last video part, Unsuccessful processing of create_video' do
    json_data = {
      file_name: "some_random_name.mp4",
      video_params: {
        video_session_id: "session_id",
        video_file: 'video_file',
        video_aws_keys: 'video_aws_keys',
        video_aws_secret: 'video_aws_secret',
        video_aws_bucket: 'video_aws_bucket',
        video_aws_region: 'us-east-1'
      },
      genre: "app_automate",
      dest: "some_random_s3_path",
      upload_type: 'video',
      async_process_file: 'async_process_file'
    }

    meta_data = {
      session_id: "session_id",
      video_part_name: "__video__0",
      screenshot_counter: 50,
      video_parts_counter: -1,
      video_out_file: "video_out_file",
      workspace: "workspace",
      video_parts_prefix: "__video__",
      fps: 5,
      part_size: 50
    }

    videoUploader = VideoUploader.new
    mock_object = MockIosVidCapturer.new(true)
    allow(File).to receive(:read).and_return(json_data.to_json, meta_data.to_json)
    allow(File).to receive(:exist?).and_return(true, false, true, false, true)
    allow(Dir).to receive(:glob).and_return([])
    allow(File).to receive(:size).and_return(10)
    expect(IosVidCapturer).to receive(:new).and_return(mock_object)

    expect(Utils).to_not receive(:upload_file_to_s3)
    expect(File).to receive(:delete).twice
    expect(BrowserStack::Zombie).to receive(:push_logs).with("app-ios-video-rec-failure", "Video not rendered mock error", { "session_id" => "session_id" })
    videoUploader.process("/some/random/file/path")
  end

  it '[New Flow, Async video processing] : Successful processing of last video part, successful processing of video_upload' do
    json_data = {
      file_name: "some_random_name.mp4",
      video_params: {
        session_id: "session_id",
        video_file: 'video_file',
        video_aws_keys: 'video_aws_keys',
        video_aws_secret: 'video_aws_secret',
        video_aws_bucket: 'video_aws_bucket',
        video_aws_region: 'us-east-1'
      },
      genre: "app_automate",
      dest: "some_random_s3_path",
      upload_type: 'video',
      async_process_file: 'async_process_file'
    }

    meta_data = {
      session_id: "session_id",
      video_part_name: "__video__0",
      screenshot_counter: 50,
      video_parts_counter: -1,
      video_out_file: "video_out_file",
      workspace: "workspace",
      video_parts_prefix: "__video__",
      fps: 5,
      part_size: 50
    }

    videoUploader = VideoUploader.new
    mock_object = MockIosVidCapturer.new(true, true)

    allow(File).to receive(:read).and_return(json_data.to_json, meta_data.to_json)
    allow(File).to receive(:exist?).and_return(true, false, true, false, true)
    allow(Dir).to receive(:glob).and_return([])
    allow(File).to receive(:size).and_return(10)

    expect(IosVidCapturer).to receive(:new).and_return(mock_object)
    expect(Utils).to receive(:upload_file_to_s3).and_return([true, nil])
    expect(File).to receive(:delete).thrice
    videoUploader.process("/some/random/file/path")
  end
end