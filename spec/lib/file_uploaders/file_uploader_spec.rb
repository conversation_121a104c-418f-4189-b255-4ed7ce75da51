require_relative '../../../lib/file_uploaders/file_uploader'
require_relative '../../spec_helper'
require_relative '../../../config/constants'

describe FileUploader do
  let(:test_id) { "40cf96d3673099565f821a28924f5fa8dc268ba912345678" }
  let(:instru) { "instru" }
  let(:network) { "network" }
  let(:fileUploader) { FileUploader.new("other_files_uploader") }
  let(:logs_stability_file) { "./spec/fixtures/sessionid_xctest_logs_stability" }
  let(:logs_stability_file_data) { JSON.parse(File.read(logs_stability_file)) }
  let(:time_components) { { queue: 10, upload: 20 } }

  before(:each) do
    allow(BrowserStack::Zombie).to receive(:configure).and_return(true)
    allow(BrowserStack::Configuration).to receive_message_chain(:new, :all).and_return({
      "logging_root" => "/path/to/logging/root",
      "ip_file" => "/path/to/ip/file",
      "static_conf" => {
        "zombie_url" => "http://pager.bsstag.com"
      }
    })
    allow(BrowserStack).to receive(:init_logger)
  end

  it "creates a FileUplaoder Object" do
    expect(fileUploader).to be_a FileUploader
  end

  context "update_logs_stability_metrics" do
    it "should return if framework type is not xcuitest" do
      expect(File).not_to receive(:exists?)

      fileUploader.update_logs_stability_metrics(test_id, "success", 10, time_components, "instru", "appium")
    end

    it "should return if log type is not in ['instru', 'network', 'device']" do
      expect(File).not_to receive(:exists?)

      fileUploader.update_logs_stability_metrics(test_id, "success", 10, time_components, "other", "xcuitest")
    end

    it "should update count for uploaded in instrumentation logs" do
      expect(File).to receive(:exists?).and_return(false)
      expect(Utils).to receive(:send_to_eds).and_return(true)

      fileUploader.update_logs_stability_metrics(test_id, "success", 10, time_components, instru, "xcuitest")
    end

    it "should rescue and print the exception raised" do
      error = "Could not open error"
      expect(File).to receive(:exists?).and_return(true)
      expect(File).to receive(:open).and_raise(error)
      expect(BrowserStack).to receive_message_chain(:logger, :info).with(/error in update_logs_stability_metrics message/).and_return(true)

      fileUploader.update_logs_stability_metrics(test_id, "success", 10, time_components, instru, "xcuitest")
    end

    context "should update stability metrics with required status" do
      before(:each) do
        expect(Utils).to receive(:get_logs_stability_file_path).with(test_id[0...40]).and_return(logs_stability_file)

        expect(File).to receive(:exists?).and_return(true)
        expect_any_instance_of(File).to receive(:flock).and_return(true)
        expect_any_instance_of(File).to receive(:rewind).and_return(true)
        expect_any_instance_of(File).to receive(:flush).and_return(true)
        expect_any_instance_of(File).to receive(:truncate).and_return(true)

        expect(Utils).to receive(:push_stability_metrics).and_return(true)
      end

      it "success in file for instru logs" do
        logs_stability_file_data["instrumentationLogs"]["uploaded"] = logs_stability_file_data["instrumentationLogs"]["uploaded"] + 1
        expect_any_instance_of(File).to receive(:write).with(logs_stability_file_data.to_json).and_return(true)

        fileUploader.update_logs_stability_metrics(test_id, "success", 10, time_components, instru, "xcuitest")
      end

      it "failure in file for instru logs because of 0 file size" do
        logs_stability_file_data["instrumentationLogs"]["failed"] = logs_stability_file_data["instrumentationLogs"]["failed"] + 1
        expect_any_instance_of(File).to receive(:write).with(logs_stability_file_data.to_json).and_return(true)

        fileUploader.update_logs_stability_metrics(test_id, "success", 0, time_components, instru, "xcuitest")
      end

      it "failure in file for network logs because of 140 file size" do
        logs_stability_file_data["networkLogs"]["failed"] = logs_stability_file_data["networkLogs"]["failed"] + 1
        expect_any_instance_of(File).to receive(:write).with(logs_stability_file_data.to_json).and_return(true)

        fileUploader.update_logs_stability_metrics(test_id, "success", 140, time_components, network, "xcuitest")
      end

      it "failure in file for instru logs because of upload failure" do
        logs_stability_file_data["instrumentationLogs"]["failed"] = logs_stability_file_data["instrumentationLogs"]["failed"] + 1
        expect_any_instance_of(File).to receive(:write).with(logs_stability_file_data.to_json).and_return(true)

        fileUploader.update_logs_stability_metrics(test_id, "failed", 10, time_components, instru, "xcuitest")
      end
    end
  end
end
