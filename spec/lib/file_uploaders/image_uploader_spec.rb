require_relative '../../../lib/file_uploaders/image_uploader'
require_relative '../../spec_helper'
require_relative '../../../config/constants'

describe 'ImageUploader' do
  before(:each) do
    allow(BrowserStack).to receive(:init_logger)
  end

  context 'successful uploads' do
    it 'should successfully upload a screenshot' do
      json_data = {
        file_name: "some_random_name.jpg",
        dest: "some_random_s3_path",
        upload_type: 'screenshot',
        key_id: 'some_aws_key',
        secret_key: 'some_aws_secret',
        session_id: "session_id",
        start_time: Time.now
      }

      imageUploader = ImageUploader.new
      allow(File).to receive(:read).and_return(json_data.to_json)
      expect(Utils).to receive(:get_screenshot_instrumentation_file).and_return("dummy")
      expect(File).to receive(:readable?).and_return(true)
      expect(Utils).to receive(:upload_file_to_s3).and_return([true, nil])
      expect(File).to receive(:exist?).with("dummy").and_return(true)
      expect(Utils).to receive(:update_screenshot_instrumentation_with_lock).with("dummy", "upload", "success", anything, 2, "skipped")
      expect(File).to receive(:delete)
      imageUploader.process("/some/random/file/path")
    end

    it 'should successfully upload a screenshot and not delete it for keep screenshot' do
      json_data = {
        file_name: "some_random_name.jpg",
        dest: "some_random_s3_path",
        upload_type: 'screenshot',
        key_id: 'some_aws_key',
        secret_key: 'some_aws_secret',
        session_id: "session_id",
        start_time: Time.now,
        keep_screenshot: "true"
      }

      imageUploader = ImageUploader.new
      allow(File).to receive(:read).and_return(json_data.to_json)
      expect(Utils).to receive(:get_screenshot_instrumentation_file).and_return("dummy")
      expect(File).to receive(:readable?).and_return(true)
      expect(Utils).to receive(:upload_file_to_s3).and_return([true, nil])
      expect(File).to receive(:exist?).with("dummy").and_return(true)
      expect(Utils).to receive(:update_screenshot_instrumentation_with_lock).with("dummy", "upload", "success", anything, 2, "skipped")
      expect(File).to_not receive(:delete)
      imageUploader.process("/some/random/file/path")
    end

    it 'should successfully upload a screenshot and check for black screenshot if key is true' do
      json_data = {
        file_name: "some_random_name.jpg",
        dest: "some_random_s3_path",
        upload_type: 'screenshot',
        key_id: 'some_aws_key',
        secret_key: 'some_aws_secret',
        session_id: "session_id",
        start_time: Time.now,
        keep_screenshot: "true",
        check_black_screenshot: true
      }

      imageUploader = ImageUploader.new
      allow(File).to receive(:read).and_return(json_data.to_json)
      expect(Utils).to receive(:get_screenshot_instrumentation_file).and_return("dummy")
      expect(File).to receive(:readable?).and_return(true)
      expect(Utils).to receive(:upload_file_to_s3).and_return([true, nil])
      expect(File).to receive(:exist?).with("dummy").and_return(true)
      expect(ScreenshotUtil).to receive(:is_black_screenshot?).with("some_random_name.jpg").and_return(true)
      expect(Utils).to receive(:update_screenshot_instrumentation_with_lock).with("dummy", "upload", "success", anything, 2, "true")
      expect(File).to_not receive(:delete)
      imageUploader.process("/some/random/file/path")
    end

    it 'should successfully upload a screenshot and not check for black screenshot if key is false' do
      json_data = {
        file_name: "some_random_name.jpg",
        dest: "some_random_s3_path",
        upload_type: 'screenshot',
        key_id: 'some_aws_key',
        secret_key: 'some_aws_secret',
        session_id: "session_id",
        start_time: Time.now,
        keep_screenshot: "true",
        check_black_screenshot: false
      }

      imageUploader = ImageUploader.new
      allow(File).to receive(:read).and_return(json_data.to_json)
      expect(Utils).to receive(:get_screenshot_instrumentation_file).and_return("dummy")
      expect(File).to receive(:readable?).and_return(true)
      expect(Utils).to receive(:upload_file_to_s3).and_return([true, nil])
      expect(File).to receive(:exist?).with("dummy").and_return(true)
      expect(ScreenshotUtil).not_to receive(:is_black_screenshot?)
      expect(Utils).to receive(:update_screenshot_instrumentation_with_lock).with("dummy", "upload", "success", anything, 2, "skipped")
      expect(File).to_not receive(:delete)
      imageUploader.process("/some/random/file/path")
    end

    it 'should successfully upload a screenshot and move to timeout stage' do
      json_data = {
        file_name: "some_random_name.jpg",
        dest: "some_random_s3_path",
        upload_type: 'screenshot',
        key_id: 'some_aws_key',
        secret_key: 'some_aws_secret',
        session_id: "session_id",
        start_time: "2021-04-19 14:50:37.41201 +0530"
      }
      imageUploader = ImageUploader.new
      allow(File).to receive(:read).and_return(json_data.to_json)
      expect(Utils).to receive(:get_screenshot_instrumentation_file).and_return("dummy")
      expect(File).to receive(:readable?).and_return(true)
      expect(Utils).to receive(:upload_file_to_s3).and_return([true, nil])
      expect(File).to receive(:exist?).with("dummy").and_return(true)
      expect(Utils).to receive(:update_screenshot_instrumentation_with_lock).with("dummy", "upload", "timeout", anything, 2, "skipped")
      expect(File).to receive(:delete)
      imageUploader.process("/some/random/file/path")
    end

    it 'should successfully upload an xcuitest screenshot' do
      json_data = {
        file_name: "some_random_name.jpg",
        s3_params: {
          s3_url: "random_s3_url",
          session_id: "some_session_id",
          aws_key: 'aws_key',
          aws_bucket: 'aws_bucket'
        },
        format: 'format',
        upload_type: 'xcuitest_screenshot'
      }
      imageUploader = ImageUploader.new
      expect(File).to receive(:exist?).and_return(true)
      allow(File).to receive(:read).and_return(json_data.to_json)
      expect(Utils).to receive(:upload_file_to_s3).and_return([true, nil])
      expect(File).to receive(:delete)
      imageUploader.process("/some/random/file/path")
    end
  end

  context 'failure scenarios' do
    context 'invalid upload_type' do
      it 'should not upload the file and push to zombie if upload_type is missing' do
        json_data = {
          file_name: "some_random_name.jpg",
          dest: "some_random_s3_path",
          key_id: 'some_aws_key',
          secret_key: 'some_aws_secret'
        }
        imageUploader = ImageUploader.new
        allow(File).to receive(:read).and_return(json_data.to_json)
        expect(BrowserStack::Zombie).to receive(:push_logs).with("invalid-file-upload-request", "some_random_name.jpg", { "upload_type": nil, "url": "image" })
        expect(File).to receive(:delete)
        imageUploader.process("/some/random/file/path")
      end

      it 'should not upload the file and push to zombie if upload_type is invalid' do
        json_data = {
          file_name: "some_random_name.jpg",
          dest: "some_random_s3_path",
          upload_type: "some_random_type",
          key_id: 'some_aws_key',
          secret_key: 'some_aws_secret'
        }
        imageUploader = ImageUploader.new
        allow(File).to receive(:read).and_return(json_data.to_json)
        expect(BrowserStack::Zombie).to receive(:push_logs).with("invalid-file-upload-request", "some_random_name.jpg", { "upload_type": "some_random_type", "url": "image" })
        expect(File).to receive(:delete)
        imageUploader.process("/some/random/file/path")
      end
    end

    it 'should throw exception if request file not found' do
      imageUploader = ImageUploader.new
      expect { imageUploader.process("/some/random/file/path") }.to raise_error(Errno::ENOENT)
    end

    context 'failures for screenshot upload' do
      it 'should raise exception if file to be uploaded is not readable for' do
        json_data = {
          file_name: "some_random_name.jpg",
          dest: "some_random_s3_path",
          upload_type: 'screenshot',
          key_id: 'some_aws_key',
          secret_key: 'some_aws_secret'
        }
        imageUploader = ImageUploader.new
        allow(File).to receive(:read).and_return(json_data.to_json)
        expect(Utils).to receive(:get_screenshot_instrumentation_file).and_return("dummmy")
        expect(File).to receive(:readable?).and_return(false)
        expect(BrowserStack::OSUtils).to_not receive(:execute)
        expect(File).to_not receive(:delete)
        expect { imageUploader.process("/some/random/file/path") }.to raise_error("Image at some_random_name.jpg is not readable/non-existent !")
      end

      it 'should raise exception if screenshot is in progress' do
        json_data = {
          file_name: "some_random_name.jpg",
          dest: "some_random_s3_path",
          upload_type: 'screenshot',
          key_id: 'some_aws_key',
          secret_key: 'some_aws_secret',
          session_id: "session_id",
          start_time: "2021-04-19 14:50:37.41201 +0530",
          keep_screenshot: "true",
          screenshot_lock_file: "sample_jpeg"
        }
        imageUploader = ImageUploader.new
        allow(File).to receive(:read).and_return(json_data.to_json)
        expect(Utils).to receive(:get_screenshot_instrumentation_file).and_return("dummmy")
        expect(File).to receive(:exist?).and_return(true)
        expect { imageUploader.process("/some/random/file/path") }.to raise_error("Screenshot currently in progress!")
      end

      it 'should raise exception if file upload to s3 fails' do
        json_data = {
          file_name: "some_random_name.jpg",
          dest: "some_random_s3_path",
          upload_type: 'screenshot',
          key_id: 'some_aws_key',
          secret_key: 'some_aws_secret'
        }
        imageUploader = ImageUploader.new
        allow(File).to receive(:read).and_return(json_data.to_json)
        expect(Utils).to receive(:get_screenshot_instrumentation_file).and_return("dummmy")
        expect(File).to receive(:readable?).and_return(true)
        expect(Utils).to receive(:upload_file_to_s3).and_return([false, "some_error"])
        expect(File).to_not receive(:delete)
        expect { imageUploader.process("/some/random/file/path") }.to raise_error("Error while uploading to S3: some_error")
      end
    end

    context 'failures for xcuitest screenshot upload' do
      it 'should raise exception if file to be uploaded does not exist' do
        json_data = {
          file_name: "some_random_name.jpg",
          s3_params: {
            s3_url: "random_s3_url",
            session_id: "some_session_id",
            aws_key: 'aws_key',
            aws_bucket: 'aws_bucket'
          },
          format: 'format',
          upload_type: 'xcuitest_screenshot'
        }
        imageUploader = ImageUploader.new
        allow(File).to receive(:read).and_return(json_data.to_json)
        expect(File).to receive(:exist?).and_return(false)
        expect(Utils).to_not receive(:upload_file_to_s3)
        expect(File).to_not receive(:delete)
        expect { imageUploader.process("/some/random/file/path") }.to raise_error("Cannot find some_random_name.jpg on disk, Cannot upload.")
      end

      it 'should raise exception if upload to s3 failed' do
        json_data = {
          file_name: "some_random_name.jpg",
          s3_params: {
            s3_url: "random_s3_url",
            session_id: "some_session_id",
            aws_key: 'aws_key',
            aws_bucket: 'aws_bucket'
          },
          format: 'format',
          upload_type: 'xcuitest_screenshot'
        }
        imageUploader = ImageUploader.new
        allow(File).to receive(:read).and_return(json_data.to_json)
        expect(File).to receive(:exist?).and_return(true)
        expect(Utils).to receive(:upload_file_to_s3).and_return([false, "some_error"])
        expect(File).to_not receive(:delete)
        expect { imageUploader.process("/some/random/file/path") }.to raise_error("Error while uploading to S3: some_error")
      end
    end
  end
end
