require_relative '../../../lib/file_uploaders/network_files_uploader'
require_relative '../../spec_helper'
require_relative '../../../config/constants'
require_relative '../../../lib/mitm_proxy'
require_relative '../../helpers/mock_mitm_proxy'

describe 'NetworkFilesUploader' do
  before(:each) do
    allow(BrowserStack).to receive(:init_logger)
  end

  it 'should successfully upload har file on retry' do
    json_data = {
      file_name: "some_random_name",
      upload_type: "har-file",
      session_id: 'session_id',
      s3_params: {
        genre: 'app_automate',
        networklogs_aws_key: 'networklogs_aws_key',
        networklogs_aws_secret: 'networklogs_aws_secret',
        networklogs_aws_bucket: 'networklogs_aws_bucket',
        networklogs_aws_region: 'us-east-1'
      }
    }

    networkFilesUploader = NetworkFilesUploader.new
    allow(File).to receive(:read).and_return(json_data.to_json)
    allow(File).to receive(:file?).and_return(true)
    allow(File).to receive(:size).and_return(50)
    expect(File).to receive(:exist?).and_return(true)
    expect(MitmProxy).to_not receive(:new)
    expect(Utils).to receive(:upload_file_to_s3).and_return([true, nil])
    expect(File).to receive(:delete)
    networkFilesUploader.process("/some/random/file/path")
  end

  it 'convert_flow_to_har failure ' do
    json_data = {
      file_name: "some_random_name",
      upload_type: "har-file",
      session_id: 'session_id',
      s3_params: {
        genre: 'app_automate',
        networklogs_aws_key: 'networklogs_aws_key',
        networklogs_aws_secret: 'networklogs_aws_secret',
        networklogs_aws_bucket: 'networklogs_aws_bucket',
        networklogs_aws_region: 'us-east-1'
      }
    }

    networkFilesUploader = NetworkFilesUploader.new
    mock_object = MockMitmProxy.new
    allow(File).to receive(:read).and_return(json_data.to_json)
    allow(File).to receive(:file?).and_return(false)
    allow(File).to receive(:size).and_return(-1)
    expect(File).to_not receive(:exist?)
    expect(MitmProxy).to receive(:new).and_return(mock_object)
    expect(Utils).to_not receive(:upload_file_to_s3)
    expect(File).to receive(:delete)
    networkFilesUploader.process("/some/random/file/path")
  end

  it 'convert_flow_to_har success + upload success ' do
    json_data = {
      file_name: "some_random_name",
      upload_type: "har-file",
      session_id: 'session_id',
      s3_params: {
        genre: 'app_automate',
        networklogs_aws_key: 'networklogs_aws_key',
        networklogs_aws_secret: 'networklogs_aws_secret',
        networklogs_aws_bucket: 'networklogs_aws_bucket',
        networklogs_aws_region: 'us-east-1'
      }
    }

    networkFilesUploader = NetworkFilesUploader.new
    mock_object = MockMitmProxy.new(true)
    allow(File).to receive(:read).and_return(json_data.to_json)
    allow(File).to receive(:file?).and_return(false)
    allow(File).to receive(:size).and_return(-1)
    expect(File).to receive(:exist?).and_return(true)
    expect(MitmProxy).to receive(:new).and_return(mock_object)
    expect(Utils).to receive(:upload_file_to_s3).and_return([true, nil])
    expect(File).to receive(:delete).twice
    networkFilesUploader.process("/some/random/file/path")
  end
end