require_relative '../../../lib/file_uploaders/other_files_uploader'
require_relative '../../spec_helper'
require_relative '../../../config/constants'

describe 'OtherFilesUploader' do
  before(:each) do
    allow(BrowserStack).to receive(:init_logger)
  end

  context 'successfull uploads' do
    it 'should successfully upload xcuitest raw logs' do
      json_data = {
        file_name: "some_random_name",
        upload_type: "xcuitest_raw_logs",
        s3_params: {
          session_id: 'session_id',
          aws_key: 'aws_key',
          aws_secret: 'aws_secret',
          aws_bucket: 'aws_bucket',
          aws_region: 'us-east-1',
          s3_url: 'some_s3_url'
        }
      }

      otherFilesUploader = OtherFilesUploader.new
      allow(File).to receive(:read).and_return(json_data.to_json)
      expect(File).to receive(:exist?).and_return(true)
      expect(Utils).to receive(:upload_file_to_s3).and_return([true, nil])
      expect(File).to receive(:delete)
      otherFilesUploader.process("/some/random/file/path")
    end

    it 'should successfully upload har file' do
      json_data = {
        file_name: "some_random_name",
        upload_type: "har-file",
        session_id: 'session_id',
        s3_params: {
          genre: 'app_automate',
          networklogs_aws_key: 'networklogs_aws_key',
          networklogs_aws_secret: 'networklogs_aws_secret',
          networklogs_aws_bucket: 'networklogs_aws_bucket',
          networklogs_aws_region: 'us-east-1'
        }
      }

      otherFilesUploader = OtherFilesUploader.new
      allow(File).to receive(:read).and_return(json_data.to_json)
      expect(File).to receive(:exist?).and_return(true)
      expect(Utils).to receive(:upload_file_to_s3).and_return([true, nil])
      expect(File).to receive(:delete)
      otherFilesUploader.process("/some/random/file/path")
    end

    it 'should successfully upload video map' do
      json_data = {
        file_name: "some_random_name",
        upload_type: "video_map_upload",
        session_id: 'session_id',
        video_params: {
          video_session_id: 'video_session_id',
          video_aws_keys: 'video_aws_keys',
          video_aws_secret: 'video_aws_secret',
          video_aws_bucket: 'video_aws_bucket',
          video_aws_region: 'us-east-1'
        }
      }

      otherFilesUploader = OtherFilesUploader.new
      allow(File).to receive(:read).and_return(json_data.to_json)
      expect(File).to receive(:exist?).and_return(true)
      expect(Utils).to receive(:upload_file_to_s3).and_return([true, nil])
      expect(File).to receive(:delete)
      otherFilesUploader.process("/some/random/file/path")
    end

    it 'should successfully upload logs' do
      json_data = {
        type: "device",
        file_name: "some_random_name",
        upload_type: "logs_upload",
        session_id: 'session_id',
        genre: 'app_automate',
        s3_params: {
          session_id: 'session_id',
          aws_key: 'aws_key',
          aws_secret: 'aws_secret',
          aws_bucket: 'aws_bucket',
          aws_region: 'us-east-1',
          s3_url: 'some_s3_url'
        }
      }

      otherFilesUploader = OtherFilesUploader.new
      allow(File).to receive(:read).and_return(json_data.to_json)
      expect(File).to receive(:exist?).and_return(true)
      expect(Utils).to receive(:upload_file_to_s3).and_return([true, nil])
      expect(File).to receive(:delete)
      otherFilesUploader.process("/some/random/file/path")
    end

    it 'should successfully upload zip file' do
      json_data = {
        type: "device",
        file_name: "some_random_name",
        upload_type: "crash-report",
        session_id: 'session_id',
        genre: 'app_automate',
        s3_params: {
          session_id: 'session_id',
          aws_key: 'aws_key',
          aws_secret: 'aws_secret',
          aws_bucket: 'aws_bucket',
          aws_region: 'us-east-1',
          s3_url: 'some_s3_url',
          metadata: {
            num_crash_reports: "2"
          }
        }
      }

      otherFilesUploader = OtherFilesUploader.new
      allow(File).to receive(:read).and_return(json_data.to_json)
      expect(File).to receive(:exist?).and_return(true)
      expect(Utils).to receive(:upload_file_to_s3).and_return([true, nil])
      expect(File).to receive(:delete)
      otherFilesUploader.process("/some/random/file/path")
    end

    it 'should successfully upload jpeg file' do
      json_data = {
        type: "device",
        file_name: "some_random_name",
        upload_type: "automation_screenshot_upload",
        session_id: 'session_id',
        genre: 'app_automate',
        s3_params: {
          session_id: 'session_id',
          aws_key: 'aws_key',
          aws_secret: 'aws_secret',
          aws_bucket: 'aws_bucket',
          aws_region: 'us-east-1',
          s3_url: 'some_s3_url'
        }
      }

      otherFilesUploader = OtherFilesUploader.new
      allow(File).to receive(:read).and_return(json_data.to_json)
      expect(File).to receive(:exist?).and_return(true)
      expect(Utils).to receive(:upload_file_to_s3).and_return([true, nil])
      expect(File).to receive(:delete)
      otherFilesUploader.process("/some/random/file/path")
    end
  end

  context 'invalid upload_type' do
    it 'should not upload the file and send data to zombie if upload_type is empty' do
      json_data = {
        file_name: "some_random_name.mp4",
        video_params: {
          session_id: "session_id",
          video_file: 'video_file',
          video_aws_keys: 'video_aws_keys',
          video_aws_secret: 'video_aws_secret',
          video_aws_bucket: 'video_aws_bucket',
          video_aws_region: 'us-east-1'
        },
        genre: "app_automate",
        dest: "some_random_s3_path"
      }

      otherFilesUploader = OtherFilesUploader.new
      allow(File).to receive(:read).and_return(json_data.to_json)
      expect(Utils).to_not receive(:upload_file_to_s3)
      expect(BrowserStack::Zombie).to receive(:push_logs).with("invalid-file-upload-request", "some_random_name.mp4", { "upload_type": nil, "url": "others" })
      expect(File).to receive(:delete)
      otherFilesUploader.process("/some/random/file/path")
    end

    it 'should not upload the file and send data to zombie if upload_type is invalid' do
      json_data = {
        file_name: "some_random_name.mp4",
        upload_type: 'random_upload_type',
        video_params: {
          session_id: "session_id",
          video_file: 'video_file',
          video_aws_keys: 'video_aws_keys',
          video_aws_secret: 'video_aws_secret',
          video_aws_bucket: 'video_aws_bucket',
          video_aws_region: 'us-east-1'
        },
        genre: "app_automate",
        dest: "some_random_s3_path"
      }

      otherFilesUploader = OtherFilesUploader.new
      allow(File).to receive(:read).and_return(json_data.to_json)
      expect(Utils).to_not receive(:upload_file_to_s3)
      expect(BrowserStack::Zombie).to receive(:push_logs).with("invalid-file-upload-request", "some_random_name.mp4", { "upload_type": 'random_upload_type', "url": "others" })
      expect(File).to receive(:delete)
      otherFilesUploader.process("/some/random/file/path")
    end

    it 'should not upload the file and send data to zombie if upload file is not present' do
      json_data = {
        type: "device",
        file_name: "some_random_name",
        upload_type: "logs_upload",
        session_id: 'session_id',
        genre: 'app_automate',
        s3_params: {
          session_id: 'session_id',
          aws_key: 'aws_key',
          aws_secret: 'aws_secret',
          aws_bucket: 'aws_bucket',
          aws_region: 'us-east-1',
          s3_url: 'some_s3_url'
        }
      }

      otherFilesUploader = OtherFilesUploader.new
      allow(File).to receive(:read).and_return(json_data.to_json)
      expect(File).to receive(:exist?).and_return(false)
      expect(Utils).to_not receive(:upload_file_to_s3)
      expect(File).to_not receive(:delete)
      expect { otherFilesUploader.process("/some/random/file/path") }.to raise_error("Cannot find some_random_name on disc, Cannot upload.")
    end
  end

  it 'should raise exception if request file not found' do
    otherFilesUploader = OtherFilesUploader.new
    expect { otherFilesUploader.process("/some/random/file/path") }.to raise_error(Errno::ENOENT)
  end
end
