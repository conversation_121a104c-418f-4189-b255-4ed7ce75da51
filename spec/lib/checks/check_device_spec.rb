require_relative '../../spec_helper'
require_relative '../../../lib/checks/check_device'
require_relative '../../../lib/checks/check_plist'
require_relative '../../../lib/custom_exceptions'
require_relative '../../../lib/utils/http_utils'
require_relative '../../../lib/utils/iproxy'
require_relative '../../../lib/models/device_state'

require 'yaml'

describe BrowserStack::CheckDevice do
  let(:device) { 'mockdevice123' }
  let(:mock_logger) { double("logger").as_null_object }
  let(:mock_redis) { double('redis') }
  let(:check_device) { BrowserStack::CheckDevice.new(device, mock_redis) }
  let(:device_state) { DeviceState.new(device) }
  let(:payload_identifier_in_config) do
    constants_yml = "#{__dir__}/../../../config/constants.yml"
    YAML.load_file(constants_yml)['pac_profile_payload_id']
  end

  before do
    allow(DeviceManager).to receive(:configure)
    allow(BrowserStack).to receive(:logger).and_return(mock_logger)
  end

  it 'creates a CheckDevice object' do
    expect(check_device).to be_a BrowserStack::CheckDevice
  end

  context 'when checking if device has internet working' do
    let(:port) { 8400 }
    let(:ios_version) { '10' }
    let(:wda_url) { "http://localhost:#{port}/bs/internet_check" }
    let(:timeout) { 6 }
    let(:mock_response) { double }

    before do
      # BrowserStack::MethodInterceptor::WdaClientInterceptor is meta class created to wrap methods see method_interceptor.rb
      allow_any_instance_of(BrowserStack::MethodInterceptor::WdaClientInterceptor).to receive(:internet_check)
      allow_any_instance_of(BrowserStack::MethodInterceptor::WdaClientInterceptor).to receive(:unlock_device)
      allow_any_instance_of(BrowserStack::MethodInterceptor::WdaClientInterceptor).to receive(:lock_device)
    end

    it 'returns nil when status in response object is 0' do
      expect_any_instance_of(BrowserStack::MethodInterceptor::WdaClientInterceptor)
        .to receive(:internet_check)
        .and_return({ "status" => 0 })
      expect(check_device.check_device_internet(port, ios_version)).to be_nil
    end

    context 'when it fails' do
      before do
        allow_any_instance_of(BrowserStack::MethodInterceptor::WdaClientInterceptor)
          .to receive(:internet_check)
          .and_return({ "status" => 13 })
      end

      it 'raises an exception when status is non zero' do
        expect { check_device.check_device_internet(port, ios_version) }
          .to raise_error(MobileCheckException, /internet down/)
      end

      it 'should call make_get_request the same ammount of attempts' do
        expect_any_instance_of(BrowserStack::MethodInterceptor::WdaClientInterceptor).to receive(:internet_check).twice

        expect { check_device.check_device_internet(port, ios_version) }
          .to raise_error(MobileCheckException, /internet down/)
      end

      it 'should call wda_client unlock and lock once if fails the first attempt' do
        expect_any_instance_of(BrowserStack::MethodInterceptor::WdaClientInterceptor).to receive(:unlock_device).once
        expect_any_instance_of(BrowserStack::MethodInterceptor::WdaClientInterceptor).to receive(:lock_device).once

        expect { check_device.check_device_internet(port, ios_version) }
          .to raise_error(MobileCheckException, /internet down/)
      end
    end
  end

  context 'when checking for an internet connection using proxy options' do
    let(:proxy_options) { { host: 'localhost', port: 12345 } }

    it 'returns nil when a 200 status code is received' do
      expect(BrowserStack::HttpUtils)
        .to receive(:test_url_code)
        .with('http://www.google.com', nil, proxy_options)
        .and_return(200)
      expect(check_device.check_internet_connection(proxy_options)).to be_nil
    end

    it 'raises an error when a non-200 status is received' do
      expect(BrowserStack::HttpUtils)
        .to receive(:test_url_code)
        .with('http://www.google.com', nil, proxy_options)
        .and_return(400)
      expect { check_device.check_internet_connection(proxy_options) }
        .to raise_error(RuntimeError, 'Could not connect to internet')
    end
  end

  context 'when checking that privoxy is running using the config.privoxy.org test' do
    let(:proxy_options) { { host: 'localhost', port: 12345 } }
    let(:test_template) { 'test template' }

    it 'returns nil if the given template is received and matched' do
      mock_response = double
      expect(mock_response).to receive(:code).and_return(200)
      expect(mock_response).to receive(:body).and_return(test_template)
      expect(BrowserStack::HttpUtils)
        .to receive(:get_response)
        .with(URI.parse(PRIVOXY_TEST_URL), nil, proxy_options)
        .and_return(mock_response)

      expect(check_device.check_privoxy_running(proxy_options, test_template)).to be_nil
    end

    it 'raises an error if the given template is not matched' do
      mock_response = double
      expect(mock_response).to receive(:code).and_return(200)
      expect(mock_response).to receive(:body).and_return('something else')
      expect(BrowserStack::HttpUtils)
        .to receive(:get_response)
        .with(URI.parse(PRIVOXY_TEST_URL), nil, proxy_options)
        .and_return(mock_response)

      expect { check_device.check_privoxy_running(proxy_options, test_template) }
        .to raise_error(
          RuntimeError, 'Did not receive default template from privoxy config url'
        )
    end

    it 'raises an error if the response code is not 200' do
      mock_response = double
      expect(mock_response).to receive(:code).and_return(503)
      expect(BrowserStack::HttpUtils)
        .to receive(:get_response)
        .with(URI.parse(PRIVOXY_TEST_URL), nil, proxy_options)
        .and_return(mock_response)

      expect { check_device.check_privoxy_running(proxy_options, test_template) }
        .to raise_error(
          RuntimeError, 'Did not receive default template from privoxy config url'
        )
    end
  end

  context 'when ensuring privoxy is running and configured correctly' do
    it 'check the privoxy config and the internet connection' do
      mock_cp = double
      config = { 'privoxy_port' => 1234 }
      server_config = { 'privoxy_conf_dir' => '/tmp/' }
      allow(BrowserStack::CheckPlist).to receive(:new).and_return(mock_cp)
      expect(mock_cp).to receive(:update)
      expect(PrivoxyManager).to receive(:configure)
      expect(PrivoxyManager).to receive(:ensure_pac_file)
      expect(check_device).to receive(:check_privoxy_running)
      expect(check_device).to receive(:check_internet_connection)
      check_device.ensure_privoxy_running_plist('test_username', server_config, config, '/tmp')
    end
  end

  describe '#check_device_supervised' do
    context 'when the device is not supervised' do
      it 'does not raise an exception' do
        expect(IdeviceUtils)
          .to receive(:device_supervised?)
          .and_return(true)

        expect { check_device.check_device_supervised }
          .not_to raise_error
      end
    end

    context 'when the device is not enrolled in mdm' do
      it 'raises an exception that it needs to be re-enrolled' do
        expect(IdeviceUtils)
          .to receive(:device_supervised?)
          .and_return(false)

        expect { check_device.check_device_supervised }
          .to raise_error(OFFLINE_REASON_DEVICE_NOT_SUPERVISED)
      end
    end
  end

  describe '#ensure_xcodebuild_running' do
    let(:retry_file) { "/tmp/xcodebuild_retry_count_#{device}" }
    let(:mock_appium_server) { double }

    before(:each) do
      allow(Utils).to receive(:folder_md5).and_return("1234567890")
      allow(AppiumServer).to receive(:new).and_return(mock_appium_server)
      allow(mock_appium_server).to receive(:driver)
    end

    context 'when offline reason does not start with "wda not running"' do
      let(:offline_reason) { "some reason" }

      it 'raise an exception if wda is outdated' do
        allow(check_device).to receive(:wda_outdated?).and_return(true)
        expect { check_device.ensure_xcodebuild_running(offline_reason, "idevice", device, "port") }
          .to raise_error(MobileCheckException, "wda not running - outdated wda")
      end

      it 'raise an exception if xcodebuild or wda client not running' do
        allow(check_device).to receive(:wda_outdated?).and_return(false)
        allow(check_device).to receive(:xcodebuild_or_wda_client_not_running?).and_return(true)
        expect { check_device.ensure_xcodebuild_running(offline_reason, "idevice", device, "port") }
          .to raise_error(MobileCheckException, "wda not running - xcodebuild or wda server not running")
      end

      it 'remove the xcodebuild retry file if there are no exceptions' do
        allow(check_device).to receive(:wda_client_running?).and_return(false)
        allow(check_device).to receive(:xcodebuild_or_wda_client_not_running?).and_return(false)
        allow(check_device).to receive(:wda_outdated?).and_return(false)

        expect(FileUtils).to receive(:rm_f).with(device_state.send(:xcodebuild_retry_count_file))

        check_device.ensure_xcodebuild_running(offline_reason, "idevice", device, "port")
      end

      context 'when both xcodebuild and wda client are running' do
        before(:each) do
          allow(BrowserStack::OSUtils).to receive(:wda_xcodebuild_running?).and_return(true)
          allow(check_device).to receive(:wda_client_running?).and_return(true)
          allow(check_device).to receive(:wda_outdated?).and_return(false)
        end

        it 'does not raise exception' do
          expect do
            check_device.ensure_xcodebuild_running(offline_reason, "idevice", device, "port")
          end.not_to raise_error
        end

        it 'removes retry file' do
          File.write(retry_file, "5")
          check_device.ensure_xcodebuild_running(offline_reason, "idevice", device, "port")
          expect(File).not_to exist(retry_file)
        end
      end
    end

    context 'when offline reason starts with "wda not running"' do
      let(:offline_reason) { "wda not running" }

      context 'when both wda client and xcodebuild are running' do
        before(:each) do
          allow(check_device).to receive(:xcodebuild_or_wda_client_not_running?).and_return(false)
          allow(check_device).to receive(:wda_outdated?).and_return(false)
        end

        it 'does not raise any exception' do
          expect do
            check_device.ensure_xcodebuild_running(offline_reason, "idevice", device, "port")
          end.not_to raise_error
        end

        it 'removes retry file' do
          File.write(retry_file, "5")
          check_device.ensure_xcodebuild_running(offline_reason, "idevice", device, "port")
          expect(File).not_to exist(retry_file)
        end
      end

      context 'when wda client or xcodebuild are not running' do
        before(:each) do
          allow(check_device).to receive(:xcodebuild_or_wda_client_not_running?).and_return(true)
        end

        context 'when xcodebuild is running' do
          before(:each) do
            allow(BrowserStack::OSUtils).to receive(:wda_xcodebuild_running?).and_return(true)
            allow(check_device).to receive(:kill_wda_xcodebuild_if_old).and_return("xcodebuild killed")
          end

          it 'calls #kill_xcode_build_if_old' do
            expect(check_device).to receive(:kill_wda_xcodebuild_if_old)
            begin
              check_device.ensure_xcodebuild_running(offline_reason, "idevice", device, "port")
            rescue
              nil
            end
          end

          it 'raises an exception' do
            expect do
              check_device.ensure_xcodebuild_running(offline_reason, "idevice", device, "port")
            end.to raise_error(MobileCheckException, "wda not running - xcodebuild killed")
          end
        end

        context 'when xcodebuild is not running' do
          before(:each) do
            allow(BrowserStack::OSUtils).to receive(:wda_xcodebuild_running?).and_return(false)
            allow(check_device).to receive(:fix_xcodebuild)
          end

          context 'when retry count is below 5' do
            it 'adds 1 to the number in the retry count file' do
              File.write(retry_file, "2")
              check_device.ensure_xcodebuild_running(offline_reason, "idevice", device, "port")
              expect(File.read(retry_file).to_i).to eq(3)
            end

            it 'calls #fix_xcodebuild' do
              expect(check_device).to receive(:fix_xcodebuild)
              check_device.ensure_xcodebuild_running(offline_reason, "idevice", device, "port")
            end
          end

          context 'when retry count is above 5' do
            it 'raises an exception' do
              File.write(retry_file, "7")
              expect do
                check_device.ensure_xcodebuild_running(offline_reason, "idevice", device, "port")
              end.to raise_error(MobileCheckException, "wda not running - max retries reached")
            end
          end
        end
      end
    end
  end

  describe '#extract_package_name_from_wda_path' do
    context 'when getting the package name from the wda_path' do
      let(:wda_path1) { "/usr/local/.browserstack/some_package_1.2.3/dir/file" }
      let(:wda_path2) { nil }
      let(:wda_path3) { "/usr/local/" }

      it 'returns the 4th directory down in the path' do
        expect(check_device.extract_package_name_from_wda_path( wda_path1 )).to eq("some_package_1.2.3")
      end

      it 'returns an empty string if wda_path cannot be split' do
        expect(check_device.extract_package_name_from_wda_path( wda_path2 )).to eq("")
      end

      it 'returns an empty string if the path is less than 4 levels deep' do
        expect(check_device.extract_package_name_from_wda_path( wda_path3 )).to eq("")
      end
    end
  end

  describe '#check_filename_with_hash_exists' do
    let(:filename_with_hash) { "/usr/local/.browserstack/appium_1.11/hashes_1b232r_424r2bn_23o31zs" }
    before(:each) { allow(File).to receive(:exists?).and_call_original }

    context 'when the file exists' do
      it 'returns true' do
        allow(File).to receive(:exists?).with(:filename_with_hash).and_return(true)
        expect(check_device.check_filename_with_hash_exists(:filename_with_hash)).to eq(true)
      end
    end

    context 'when the file does not exists' do
      it 'raises a MobileCheckException' do
        allow(File).to receive(:exists?).with(:filename_with_hash).and_return(false)
        expect { check_device.check_filename_with_hash_exists(:filename_with_hash) }.to raise_error(
          MobileCheckException, /filename with calculated hash doesn't exist/
        )
      end
    end
  end

  describe '#fix_xcodebuild' do
    context 'exception caused by AppiumServer#driver' do
      before(:each) do
        @mock_idevice = double
        @mock_idevice.stub(:unlock_device)
        @mock_idevice.stub(:lock_device)
        @mock_idevice.stub(:device_version)
        mock_appium_server = instance_double('BrowserStack::AppiumServer', driver: nil)
        allow(BrowserStack::AppiumServer).to receive(:new).and_return(mock_appium_server)
        allow(mock_appium_server).to receive(:driver).and_raise("appium error")
        allow(BrowserStack::IPhone).to receive(:uninstall_wda)
      end

      it 'raises unlock device error when #unlock_device returns error string' do
        @mock_idevice.stub(:unlock_device) { "unlock device failed" }

        expect do
          check_device.fix_xcodebuild(device, @mock_idevice, 8080)
        end.to raise_error(MobileCheckException, "wda not running - unlock device failed")
      end

      it 'raises lock device error when #lock_device returns error string' do
        @mock_idevice.stub(:lock_device) { "lock device failed" }

        expect do
          check_device.fix_xcodebuild(device, @mock_idevice, 8080)
        end.to raise_error(MobileCheckException, "wda not running - lock device failed")
      end

      it 'raises uninstall wda error when #uninstall_wda returns error string' do
        allow(BrowserStack::IPhone).to receive(:uninstall_wda).and_return("failed to uninstall wda")

        expect do
          check_device.fix_xcodebuild(device, @mock_idevice, 8080)
        end.to raise_error(MobileCheckException, "wda not running - failed to uninstall wda")
      end

      it 'raises "start driver failed" error' do
        expect do
          check_device.fix_xcodebuild(device, @mock_idevice, 8080)
        end.to raise_error(MobileCheckException, "wda not running - appium error")
      end
    end
  end

  describe '#kill_xcodebuild_if_old' do
    context 'when xcodebuild process is older than 10 minutes' do
      before(:each) do
        allow(BrowserStack::OSUtils).to receive(:wda_xcodebuild_running_time).and_return(42)
      end

      it 'kills the xcodebuild process' do
        expect(BrowserStack::OSUtils).to receive(:kill_wda_xcodebuild)
        check_device.kill_wda_xcodebuild_if_old(device)
      end

      it 'returns "xcodebuild killed"' do
        expect(check_device.kill_wda_xcodebuild_if_old(device)).to match(/xcodebuild killed/)
      end
    end

    context 'when xcodebuild process is younger than 10 minutes' do
      before(:each) do
        allow(BrowserStack::OSUtils).to receive(:wda_xcodebuild_running_time).and_return(3)
      end

      it 'does not kill the xcodebuild process' do
        expect(BrowserStack::OSUtils).not_to receive(:kill_wda_xcodebuild)
        check_device.kill_wda_xcodebuild_if_old(device)
      end

      it 'returns "xcodebuild running"' do
        expect(check_device.kill_wda_xcodebuild_if_old(device)).to match(/xcodebuild running/)
      end
    end
  end

  describe '#testflight_installed?' do
    context 'returns false' do
      it 'return false when testflight is not installed' do
        expect(IdeviceUtils).to receive(:check_app_with_bundle_id_exists).with(device, "com.apple.TestFlight").and_return(false)
        expect(check_device.testflight_installed?).to eq(false)
      end

      it 'return false when there is an exception' do
        expect(IdeviceUtils).to receive(:check_app_with_bundle_id_exists).with(device, "com.apple.TestFlight").and_raise(RuntimeError)
        expect(check_device.testflight_installed?).to eq(false)
      end
    end

    context 'returns true' do
      it 'return true when testflight is installed' do
        expect(IdeviceUtils).to receive(:check_app_with_bundle_id_exists).with(device, "com.apple.TestFlight").and_return(true)
        expect(check_device.testflight_installed?).to eq(true)
      end
    end
  end

  describe '#xcode_build_or_wda_client_not_running?' do
    context 'when xcodebuild is not running' do
      it 'returns true' do
        allow(BrowserStack::OSUtils).to receive(:wda_xcodebuild_running?).and_return(false)
        expect(check_device.xcodebuild_or_wda_client_not_running?("device", "port")).to eq(true)
      end
    end

    context 'when wda client is not running' do
      it 'returns true' do
        allow(check_device).to receive(:wda_client_running?).and_return(false)
        expect(check_device.xcodebuild_or_wda_client_not_running?("device", "port")).to eq(true)
      end
    end

    context 'when both xcodebuild and wda client are running' do
      it 'returns false' do
        allow(check_device).to receive(:wda_client_running?).and_return(true)
        allow(BrowserStack::OSUtils).to receive(:wda_xcodebuild_running?).and_return(true)
        expect(check_device.xcodebuild_or_wda_client_not_running?("device", "port")).to eq(false)
      end
    end
  end

  describe '#parse_installed_profiles' do
    before(:each) do
      # Don't fill in any info to skip the UUID and type checks. We are only concerned about the payload identifier.
      @profiles_required = { 'Proxy' => {} }
    end
    context 'when the profile has a different payload identifier' do
      before do
        @profiles_installed = { 'CommandUUID' => 'd5d17635-5227-4a1a-badc-dbc946dd0a65',
                                'ProfileList' =>
                               [{ 'HasRemovalPasscode' => false,
                                  'IsEncrypted' => false,
                                  'IsManaged' => true,
                                  'PayloadContent' =>
                                 [{ 'PayloadDescription' => 'Global HTTP Proxy',
                                    'PayloadDisplayName' => 'Global HTTP Proxy',
                                   # Different to constants.yml
                                    'PayloadIdentifier' => 'com.apple.proxy.http.global.NOT_THE_SAME_UUID',
                                    'PayloadType' => 'com.apple.proxy.http.global',
                                    'PayloadVersion' => 1 }],
                                  'PayloadDisplayName' => 'Proxy',
                                  'PayloadIdentifier' => 'mdm.fde0564ca18ca2d7fca40a2ffaab1021f504de08',
                                  'PayloadOrganization' => 'BrowserStack',
                                  'PayloadRemovalDisallowed' => false,
                                  'PayloadUUID' => 'fde0564ca18ca2d7fca40a2ffaab1021f504de08',
                                  'PayloadVersion' => 1 }],
                                'Status' => 'Acknowledged',
                                'UDID' => 'fde0564ca18ca2d7fca40a2ffaab1021f504de08' }
        @mdm_ca_certs_present, @profiles_to_install = check_device.parse_installed_profiles(@profiles_required, @profiles_installed)
      end
      it "Returns it in the list of profiles_to_install" do
        expect(@profiles_to_install).to be_eql(["Proxy"])
      end
    end

    context 'when the profiles match' do
      before do
        @profiles_installed = { 'CommandUUID' => 'd5d17635-5227-4a1a-badc-dbc946dd0a65',
                                'ProfileList' =>
                               [{ 'HasRemovalPasscode' => false,
                                  'IsEncrypted' => false,
                                  'IsManaged' => true,
                                  'PayloadContent' =>
                                 [{ 'PayloadDescription' => 'Global HTTP Proxy',
                                    'PayloadDisplayName' => 'Global HTTP Proxy',
                                   # Same one as in constants.yml
                                    'PayloadIdentifier' => payload_identifier_in_config,
                                    'PayloadType' => 'com.apple.proxy.http.global',
                                    'PayloadVersion' => 1 }],
                                  'PayloadDisplayName' => 'Proxy',
                                  'PayloadIdentifier' => 'mdm.fde0564ca18ca2d7fca40a2ffaab1021f504de08',
                                  'PayloadOrganization' => 'BrowserStack',
                                  'PayloadRemovalDisallowed' => false,
                                  'PayloadUUID' => 'fde0564ca18ca2d7fca40a2ffaab1021f504de08',
                                  'PayloadVersion' => 1 }],
                                'Status' => 'Acknowledged',
                                'UDID' => 'fde0564ca18ca2d7fca40a2ffaab1021f504de08' }
        @mdm_ca_certs_present, @profiles_to_install = check_device.parse_installed_profiles(@profiles_required, @profiles_installed)
      end
      it "Doesn't return it in the list of profiles_to_install" do
        expect(@profiles_to_install).to be_eql([])
      end
    end
  end

  describe "#verify_iproxy_version_for_device" do
    it 'should raise exception if invalid version of iproxy is present on machine' do
      expect(Iproxy).to receive(:version).and_return("2.0.2")

      expect { check_device.verify_iproxy_version_for_device("12.0") }
        .to raise_error(MobileCheckException, "invalid iproxy version")
    end

    it 'should not exception if valid version of iproxy is present on machine' do
      expect(Iproxy).to receive(:version).and_return("usage: iproxy LOCAL_TCP_PORT DEVICE_TCP_PORT [UDID]")
      expect { check_device.verify_iproxy_version_for_device("12.0") }
        .not_to raise_error
    end
  end

  describe "#check_internet_sharing" do
    it 'should raise exception if working interfaces are empty and os < 16' do
      expect(Utils).to receive(:get_working_interfaces).exactly(6).times.and_return([])
      allow(IdeviceUtils).to receive(:device_version).and_return Gem::Version.new('15.0')
      allow(BrowserStack::OSUtils).to receive(:bridge100_exists?).and_return(true)
      expect { check_device.check_internet_sharing("8400") }
        .to raise_error(MobileCheckException, "device's network interface not found")
    end

    it 'should not raise exception if working interfaces are empty and os >= 17' do
      expect(Utils).to receive(:get_working_interfaces).exactly(6).times.and_return([])
      allow(IdeviceUtils).to receive(:device_version).and_return Gem::Version.new('17.0')
      allow(BrowserStack::OSUtils).to receive(:bridge100_exists?).and_return(true)
      expect { check_device.check_internet_sharing("8400") }.not_to raise_error
    end

    it 'should raise exception if bridged interfaces are empty' do
      allow(BrowserStack::OSUtils).to receive(:bridge100_exists?).and_return(true)
      expect(Utils).to receive(:get_working_interfaces).and_return(["en11"])
      expect(IdeviceUtils).to receive(:is_interface_active?).and_return(true)
      expect(IdeviceUtils).to receive(:is_network_interface_in_bridge?).and_return(true, true)
      expect(IdeviceUtils).to receive(:is_network_interface_in_nat_plist?).and_return(false, false)

      expect { check_device.check_internet_sharing("8400") }
        .to raise_error(MobileCheckException, "device's network interface not in bridge, is internet sharing enabled for this device?")
    end

    it 'should try to turn interface up if already down' do
      allow(BrowserStack::OSUtils).to receive(:bridge100_exists?).and_return(true)
      expect(Utils).to receive(:get_working_interfaces).and_return(["en11"])
      expect(IdeviceUtils).to receive(:is_interface_active?).and_return(false)
      expect(IdeviceUtils).to receive(:update_interface_state).with("en11", "up").and_return(true)
      expect(IdeviceUtils).to receive(:is_network_interface_in_bridge?).and_return(true, true)
      expect(IdeviceUtils).to receive(:is_network_interface_in_nat_plist?).and_return(false, false)

      expect { check_device.check_internet_sharing("8400") }
        .to raise_error(MobileCheckException, "device's network interface not in bridge, is internet sharing enabled for this device?")
    end

    it 'should raise exception if any of the device interface is not up' do
      allow(BrowserStack::OSUtils).to receive(:bridge100_exists?).and_return(true)
      expect(Utils).to receive(:get_working_interfaces).and_return(["en11"])
      expect(IdeviceUtils).to receive(:is_interface_active?).and_return(true, false)
      expect(IdeviceUtils).to receive(:is_network_interface_in_bridge?).and_return(true, true)
      expect(IdeviceUtils).to receive(:is_network_interface_in_nat_plist?).and_return(true, true)

      expect { check_device.check_internet_sharing("8400") }
        .to raise_error(MobileCheckException, "device's network interface is not active")
    end

    it 'should raise exception if device is not using internet sharing' do
      allow(BrowserStack::OSUtils).to receive(:bridge100_exists?).and_return(true)
      expect(Utils).to receive(:get_working_interfaces).and_return(["en11", "en22"])
      expect(IdeviceUtils).to receive(:is_interface_active?).and_return(true, true, true, true)
      expect(IdeviceUtils).to receive(:is_network_interface_in_bridge?).and_return(true, true, true, true)
      expect(check_device).to receive(:get_device_ip).and_return(["{\"value\"=>\"***************\", \"sessionId\"=>\"70648F19-34AC-4408-AA9C-3DEB285EBC89\"}", "***************"])
      expect(IdeviceUtils).to receive(:is_network_interface_in_nat_plist?).and_return(true, true, true, true)

      expect { check_device.check_internet_sharing("8400") }
        .to raise_error(MobileCheckException, "device is not using internet sharing")
    end

    it 'should not raise exception if device is using internet sharing' do
      allow(BrowserStack::OSUtils).to receive(:bridge100_exists?).and_return(true)
      expect(Utils).to receive(:get_working_interfaces).and_return(["en11", "en22"])
      expect(IdeviceUtils).to receive(:is_interface_active?).and_return(true, true, true, true)
      expect(IdeviceUtils).to receive(:is_network_interface_in_bridge?).and_return(true, true, true, true)
      expect(check_device).to receive(:get_device_ip).and_return(["{\"value\"=>\"***********\", \"sessionId\"=>\"70648F19-34AC-4408-AA9C-3DEB285EBC89\"}", "***********"])
      expect(IdeviceUtils).to receive(:is_network_interface_in_nat_plist?).and_return(true, true, true, true)

      expect { check_device.check_internet_sharing("8400") }
        .not_to raise_error
    end

    it 'should not raise exception if WDA endpoint returns error' do
      allow(BrowserStack::OSUtils).to receive(:bridge100_exists?).and_return(true)
      expect(Utils).to receive(:get_working_interfaces).and_return(["en11", "en22"])
      expect(IdeviceUtils).to receive(:is_interface_active?).and_return(true, true, true, true)
      expect(IdeviceUtils).to receive(:is_network_interface_in_bridge?).and_return(true, true, true, true)
      expect(check_device).to receive(:get_device_ip).and_return(["{\"value\"=>\"error\", \"sessionId\"=>\"70648F19-34AC-4408-AA9C-3DEB285EBC89\"}", "error"])
      expect(IdeviceUtils).to receive(:is_network_interface_in_nat_plist?).and_return(true, true, true, true)

      expect { check_device.check_internet_sharing("8400") }
        .not_to raise_error
    end

    it 'should not raise exception if WDA endpoint does not respond' do
      allow(BrowserStack::OSUtils).to receive(:bridge100_exists?).and_return(true)
      expect(Utils).to receive(:get_working_interfaces).and_return(["en11", "en22"])
      expect(IdeviceUtils).to receive(:is_interface_active?).and_return(true, true, true, true)
      expect(IdeviceUtils).to receive(:is_network_interface_in_bridge?).and_return(true, true, true, true)
      expect(check_device).to receive(:get_device_ip).and_return(["{}", ""])
      expect(IdeviceUtils).to receive(:is_network_interface_in_nat_plist?).and_return(true, true, true, true)

      expect { check_device.check_internet_sharing("8400") }
        .not_to raise_error
    end

    it 'should not raise exception even if WdaClient raises an exception' do
      allow(BrowserStack::OSUtils).to receive(:bridge100_exists?).and_return(true)
      expect(Utils).to receive(:get_working_interfaces).and_return(["en11", "en22"])
      expect(IdeviceUtils).to receive(:is_interface_active?).and_return(true, true, true, true)
      expect(IdeviceUtils).to receive(:is_network_interface_in_bridge?).and_return(true, true, true, true)
      expect(WdaClient).to receive(:new).and_raise("Cannot parse WDA response")
      expect(IdeviceUtils).to receive(:is_network_interface_in_nat_plist?).and_return(true, true, true, true)

      expect { check_device.check_internet_sharing("8400") }
        .not_to raise_error
    end
  end

  describe "#devtools_proxy_server_running?" do
    let(:devtools_proxy_server_port) { 443 }
    let(:devtools_proxy_expected_url) { "https://localhost:#{devtools_proxy_server_port}" }
    let(:devtools_proxy_expected_body) { "There's no sun in the shadow of the wizard" }
    let(:devtools_not_running_error) { "Devtools Proxy Server not running" }

    context 'when the server is running and serving properly' do
      before do
        stub_request(:get, devtools_proxy_expected_url)
          .to_return(body: devtools_proxy_expected_body, status: 200)
      end
      it 'should not raise any errors' do
        expect do
          check_device
            .devtools_proxy_server_running?(devtools_proxy_server_port)
        end
          .not_to raise_error
      end
    end
  end

  describe "#ensure_appium_server_running" do
    let(:appium_server) { double }
    let(:default_appium_version) { '2.0.0' }
    let(:device_hash) { {} }
    before(:each) do
      allow(BrowserStack::DeviceConf).to receive(:[]).with(device).and_return(device_hash)
      allow(BrowserStack::AppiumServer).to receive(:new).with(device, device_hash).and_return(appium_server)
      allow(BrowserStack::Configuration).to receive(:[]).with('default_appium_version').and_return(default_appium_version)
    end

    it 'calls to start appium server for default version' do
      expect(appium_server).to receive(:start_server_for_version).with(default_appium_version)

      check_device.ensure_appium_server_running
    end

    context 'raises WDALaunchError' do
      it 'stops wda, reboots the device and raises error' do
        expect(appium_server).to receive(:start_server_for_version)
          .with(default_appium_version).and_raise(WDALaunchError)
        expect(AppleTVUtils).to receive(:stop_wda_launch_agent).with(device)
        expect(IosMdmServiceClient).to receive(:restart_device).with(device).and_return("restarted")

        expect { check_device.ensure_appium_server_running }.to raise_error(WDALaunchError)
      end

      context 'Reboot attempt fails' do
        it 'raises new error with reboot failure info' do
          expect(appium_server).to receive(:start_server_for_version)
            .with(default_appium_version).and_raise(WDALaunchError)
          expect(AppleTVUtils).to receive(:stop_wda_launch_agent).with(device)
          expect(IosMdmServiceClient).to receive(:restart_device).with(device).and_return(nil)

          expect { check_device.ensure_appium_server_running }.to raise_error(RuntimeError)
        end
      end
    end

    context 'raises AppiumServerError' do
      it 'does not reboot device nor stop wda' do
        expect(appium_server).to receive(:start_server_for_version)
          .with(default_appium_version).and_raise(AppiumServerError)

        expect { check_device.ensure_appium_server_running }.to raise_error(AppiumServerError)
      end
    end
  end
end
