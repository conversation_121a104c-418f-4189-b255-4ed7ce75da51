require_relative '../../../lib/checks/machine'
require_relative '../../spec_helper'
require_relative '../../../lib/checks/check_plist'
require_relative '../../../lib/utils/http_utils'
require_relative '../../../config/constants'

describe BrowserStack::MachineCheck do
  let(:mobile_root) { 'mobile_root' }
  let(:logging_root) { 'logging_root' }
  let(:appium_root) { 'appium_root' }
  let(:user) { 'user' }
  let(:server_config) do
    { "static_conf" => { "rails_endpoint" => "www.browserstack.com",
                         "admin_terminals_user" => "user",
                         "admin_terminals_pass" => "password" },
      "enterprise_dummy_min_version" => "1.0",
      "opencv4nodejs_path" => { "install_path" => "random/path/to/opencv4nodejs/",
                                "link_path" => "randompath/to/link/of/opencv4nodejs/" },
      "mobile_root" => "/usr/local/.browserstack/realmobile" }
  end
  subject do
    BrowserStack::MachineCheck.new(mobile_root,
                                   logging_root,
                                   appium_root,
                                   user,
                                   server_config)
  end
  describe '#find_pid' do
    it "should find the pid of a prcocess" do
      expect(BrowserStack::OSUtils).to receive(:execute).with("pgrep \"Apple Configurator\"").and_return(200)
      expect(subject.find_pid("Apple Configurator")).to be_eql(200)
    end
  end

  describe '#kill_pid_if_more_than_threshold' do
    it "should find and kill Apple configurator" do
      expect(BrowserStack::OSUtils).to receive(:execute).with("ps -p 200 -o lstart| tail -n1").and_return("Thu Apr  6 12:35:10 2023")
      expect(DateTime).to receive(:now).and_return(DateTime.parse("Thu Apr  6 15:35:10 2023"))
      expect(BrowserStack::OSUtils).to receive(:kill_pid).with(200).and_return("1")
      subject.kill_pid_if_more_than_threshold(200 , 60 , "Apple Configurator")
    end

    it "should not kill Apple Configurator if time is less than 1hr" do
      expect(BrowserStack::OSUtils).to receive(:execute).with("ps -p 200 -o lstart| tail -n1").and_return("Thu Apr  6 12:35:10 2023")
      expect(DateTime).to receive(:now).and_return(DateTime.parse("Thu Apr  6 12:37:10 2023"))
      expect(BrowserStack::OSUtils).to_not receive(:kill_pid)
      subject.kill_pid_if_more_than_threshold(200 , 60 , "Apple Configurator")
    end
  end

  describe '#enterprise_dummy_out_of_date?' do
    it 'Returns false when versions match' do
      allow(BrowserStack::OSUtils)
        .to receive(:get_attr_from_plist_file)
        .and_return("1.0")

      expect(subject.enterprise_dummy_out_of_date?).to be_eql(false)
    end

    it 'Returns false when version in plist is greater' do
      allow(BrowserStack::OSUtils)
        .to receive(:get_attr_from_plist_file)
        .and_return("2.0.0")

      expect(subject.enterprise_dummy_out_of_date?).to be_eql(false)
    end

    it 'Returns true when version in plist is less' do
      allow(BrowserStack::OSUtils)
        .to receive(:get_attr_from_plist_file)
        .and_return("0.9.1")

      expect(subject.enterprise_dummy_out_of_date?).to be_eql(true)
    end
  end

  describe '#internet_sharing_setup_on_all_devices?' do
    it 'returns false if bridge 100 not present' do
      expect(File).to receive(:read).and_return('{"devices":{"device_id":{"device_version":"1.2.3"}}}')
      expect(BrowserStack::OSUtils).to receive(:bridge100_exists?).and_return(false)
      expect(subject.internet_sharing_setup_on_all_devices?).to be(false)
    end
  end

  describe '#ensure_private_key_amount' do
    context 'when key amount is less than 200' do
      it 'should just log the key amount' do
        expect(BrowserStack::OSUtils).to receive(:execute).with(KEY_AMOUNT_CMD).and_return("  50 \n ")
        expect(BrowserStack.logger).to receive(:info).exactly(1).times
        subject.ensure_private_key_amount
      end
    end

    context 'when key amount is greater than 200' do
      it 'should push to zombie and execute binary' do
        expect(BrowserStack::OSUtils).to receive(:execute).with(KEY_AMOUNT_CMD).and_return("  300 \n ", "  30 \n  ")
        expect(BrowserStack::OSUtils).to receive(:execute).with(KEY_CLEANER_CMD).exactly(1).times
        expect(BrowserStack::Zombie).to receive(:push_logs)
        expect(BrowserStack.logger).to receive(:info).exactly(2).times
        subject.ensure_private_key_amount
      end
    end
  end

  describe '#ensure_machine_logged_in' do
    it 'should return if machine logged in' do
      expect(BrowserStack::OSUtils).to receive(:execute).with("who").and_return("console")
      subject.ensure_machine_logged_in
    end
    context 'when account not logged in' do
      it 'should return if all devices are not offline from rails' do
        expect(BrowserStack::OSUtils).to receive(:execute).with("who").and_return("Output indicating account not logged in")
        expect(BrowserStack::Zombie).to receive(:push_logs)
        expect(subject).to receive(:get_machine_ip).and_return("IP")
        expect(RailsRequest).to receive(:all_devices_offline?).and_return(false)
        subject.ensure_machine_logged_in
      end
      context 'when all devices are offline from rails' do
        it 'should log to BQ and reboot if not rebooted already today' do
          expect(BrowserStack::OSUtils).to receive(:execute).with("who").and_return("Output indicating account not logged in")
          expect(subject).to receive(:get_machine_ip).and_return("IP")
          expect(RailsRequest).to receive(:all_devices_offline?).and_return(true)
          expect(BrowserStack::Zombie).to receive(:push_logs).twice
          expect(BrowserStack::OSUtils).to receive(:execute).with("sudo reboot")
          subject.ensure_machine_logged_in
        end
        it 'should log to BQ only if rebooted already today' do
          expect(BrowserStack::OSUtils).to receive(:execute).with("who").and_return("Output indicating account not logged in")
          expect(BrowserStack::Zombie).to receive(:push_logs)
          subject.ensure_machine_logged_in
        end
      end
    end
  end

  describe '#ensure_apple_intermediate_cert_present' do
    let(:downloaded_cert_md5sum) { '08a45128fa238443623421dd2c9887ab' }
    let(:intermediate_cert_shas) { %w[06EC06599F4ED0027CC58956B4D3AC1255114F35 FF6797793A3CD798DC5B2ABEF56F73EDC9F83A64] }
    let(:apple_intermediate_file_path) { "/tmp/AppleWWDRCAG3.cer" }

    context 'new intermediate cert present' do
      it 'should not call HttpUtils.download' do
        expect(BrowserStack::HttpUtils).not_to receive(:download)
        expect(BrowserStack::OSUtils).to receive(:certificates_sha1).twice.and_return(intermediate_cert_shas)

        subject.ensure_apple_intermediate_cert_present
      end
    end

    context 'new intermediate cert absent' do
      before(:each) do
        allow(BrowserStack::Zombie).to receive(:push_logs)
      end

      it 'should call download & install the certificate' do
        allow(Utils).to receive(:get_md5_checksum_for_file).and_return(downloaded_cert_md5sum)
        allow(BrowserStack::OSUtils).to receive(:certificates_sha1).twice.and_return([])
        allow(FileUtils).to receive(:rm_rf)

        expect(BrowserStack::HttpUtils).to receive(:download)
        expect(BrowserStack::OSUtils).to receive(:add_trusted_cert).with(apple_intermediate_file_path, BrowserStack::Configuration['system_keychain_path'])
        expect(BrowserStack::OSUtils).to receive(:add_trusted_cert).with(apple_intermediate_file_path, BrowserStack::Configuration['browserstack_keychain_path'])

        subject.ensure_apple_intermediate_cert_present
      end

      it "should raise an exception if md5sum doesn't match" do
        allow(Utils).to receive(:get_md5_checksum_for_file).and_return('incorrect_md5sum')
        allow(BrowserStack::OSUtils).to receive(:certificates_sha1).twice.and_return([])
        allow(BrowserStack::HttpUtils).to receive(:download)

        expect(BrowserStack::OSUtils).not_to receive(:add_trusted_cert)

        expect { subject.ensure_apple_intermediate_cert_present }.to raise_error(StandardError, "md5sum of apple intermediate doesn't match")
      end

      it 'should raise an exception if OSUtils.download raises an exception' do
        allow(BrowserStack::OSUtils).to receive(:certificates_sha1).twice.and_return([])
        allow(BrowserStack::HttpUtils).to receive(:download).and_raise(HTTPException)

        expect { subject.ensure_apple_intermediate_cert_present }.to raise_error(HTTPException)
      end
    end
  end

  describe "ensure_nomad_is_installed_and_running" do
    context "when nomad is not installed" do
      it "returns not installed error" do
        allow(File).to receive(:file?).and_return(false)
        expect { subject.ensure_nomad_running }.to raise_error(MachineCheckException, "Nomad not installed")
      end
    end

    context "when nomad is installed" do
      it "raises alert if nomad is not running" do
        allow(File).to receive(:file?).and_return(true)
        allow(subject).to receive(:`).and_return("")
        expect { subject.ensure_nomad_running }.to raise_error(MachineCheckException, "Nomad not running")
      end

      it "does nothing if nomad is running" do
        allow(File).to receive(:file?).and_return(true)
        allow(subject).to receive(:`).and_return("nomad.d process")
        expect { subject.ensure_nomad_running }.not_to raise_error
      end
    end
  end

  describe 'download_bs_media' do
    context 'when the ios version is greater than 10/11' do
      it 'should call ios 13 methoed for higher platform versions' do
        allow(Dir).to receive(:exist?).and_return(true)
        allow(File).to receive(:exist?).and_return(false)
        expect(subject).to receive(:download_bs_media_ios13)
        expect(subject.download_bs_media('ios_njb_13'))
      end
    end

    context 'when the ios version is 10/11' do
      it "should do nothing if it's already downloaded" do
        allow(File).to receive(:exists?).and_return(true)
        expect(subject.download_bs_media('ios_njb_11')).to be_eql(true)
      end

      it 'should download if it doesnt exist' do
        allow(File).to receive(:exists?).and_return(false)
        allow(BrowserStack::OSUtils).to receive(:execute).and_return(true)
        expect(AwsS3Wrapper).to receive(:download!)
        expect(subject.download_bs_media('ios_njb_11'))
      end
    end
  end

  describe "download_bs_media_ios13" do
    it "should not download the file if the redownload file is absent and the media dir is present" do
      allow(Dir).to receive(:exist?).and_return(true)
      allow(File).to receive(:exist?).and_return(false)

      expect(FileUtils).not_to receive(:rm_rf)
      expect(AwsS3Wrapper).not_to receive(:download!)

      expect { subject.download_bs_media_ios13 }.not_to raise_error
    end

    it "should download the file if the redownload file is absent and the media dir is absent" do
      allow(Dir).to receive(:exist?).and_return(false)
      allow(File).to receive(:exist?).and_return(false)

      expect(FileUtils).to receive(:rm_rf).exactly(2).times
      expect(AwsS3Wrapper).to receive(:download!).with("bs-mobile-stag", "ios/bs_media_ios13.zip", "/tmp/bs_media_ios13.zip", "bs")
      expect(BrowserStack::OSUtils).to receive(:execute)

      expect { subject.download_bs_media_ios13 }.not_to raise_error
    end

    it "should download the file if the redownload file is present and the media dir is present" do
      allow(Dir).to receive(:exist?).and_return(true)
      allow(File).to receive(:exist?).and_return(true)

      expect(FileUtils).to receive(:rm_rf).exactly(2).times
      expect(AwsS3Wrapper).to receive(:download!).with("bs-mobile-stag", "ios/bs_media_ios13.zip", "/tmp/bs_media_ios13.zip", "bs")
      expect(BrowserStack::OSUtils).to receive(:execute)

      expect { subject.download_bs_media_ios13 }.not_to raise_error
    end

    it "should download the file if the redownload file is present and the media dir is absent" do
      allow(Dir).to receive(:exist?).and_return(false)
      allow(File).to receive(:exist?).and_return(true)

      expect(FileUtils).to receive(:rm_rf).exactly(2).times
      expect(AwsS3Wrapper).to receive(:download!).with("bs-mobile-stag", "ios/bs_media_ios13.zip", "/tmp/bs_media_ios13.zip", "bs")
      expect(BrowserStack::OSUtils).to receive(:execute)

      expect { subject.download_bs_media_ios13 }.not_to raise_error
    end

    it "should raise alert if it fails to download the media files" do
      allow(Dir).to receive(:exist?).and_return(false)
      allow(File).to receive(:exist?).and_return(true)

      expect(FileUtils).to receive(:rm_rf).exactly(2).times
      allow(AwsS3Wrapper).to receive(:download!).with("bs-mobile-stag", "ios/bs_media_ios13.zip", "/tmp/bs_media_ios13.zip", "bs").and_raise("some error")
      expect(BrowserStack::OSUtils).not_to receive(:execute)

      expect { subject.download_bs_media_ios13 }.to raise_error("some error")
    end
  end

  describe 'idevicevideoproxy_is_present?' do
    context "when the binary is already installed" do
      it "should return true" do
        expect(BrowserStack::OSUtils).to receive(:which).with('idevicevideoproxy').and_return('/usr/local/bin/idevicevideoproxy')
        allow(File).to receive(:exists?).with('/usr/local/bin/idevicevideoproxy').and_return(true)
        expect(subject.idevicevideoproxy_is_present?).to be_truthy
      end
    end

    context "when the binary is not installed yet" do
      it 'should clone iproxy to idevicevideoproxy and return true when installed' do
        expect(BrowserStack::OSUtils).to receive(:which).with('idevicevideoproxy').and_return('/usr/local/bin/idevicevideoproxy')
        allow(File).to receive(:exists?).with('/usr/local/bin/idevicevideoproxy').and_return(false)
        expect(BrowserStack::OSUtils).to receive(:which).with('iproxy').and_return('/usr/local/bin/iproxy')
        allow(FileUtils).to receive(:copy_file).with('/usr/local/bin/iproxy', '/usr/local/bin/idevicevideoproxy')
        allow(BrowserStack::OSUtils).to receive(:execute).with("/usr/local/bin/idevicevideoproxy -h", true).and_return(["result", "0"])

        expect(subject.idevicevideoproxy_is_present?).to be_truthy
      end

      it 'should clone iproxy to idevicevideoproxy and return false if install fails' do
        expect(BrowserStack::OSUtils).to receive(:which).with('idevicevideoproxy').and_return('/usr/local/bin/idevicevideoproxy')
        allow(File).to receive(:exists?).with('/usr/local/bin/idevicevideoproxy').and_return(false)
        expect(BrowserStack::OSUtils).to receive(:which).with('iproxy').and_return('/usr/local/bin/iproxy')
        allow(FileUtils).to receive(:copy_file).with('/usr/local/bin/iproxy', '/usr/local/bin/idevicevideoproxy')
        allow(BrowserStack::OSUtils).to receive(:execute).with("/usr/local/bin/idevicevideoproxy -h", true).and_return(["result", "0"])
        expect(subject.idevicevideoproxy_is_present?).to be_truthy
      end
    end
  end

  describe '#ensure_supervision_identities_downloaded' do
    let(:dir) { "/usr/local/.browserstack/config/supervision_identities" }
    let(:dir_content) { [nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil] }

    it 'should not download if dir and files exist' do
      allow(Dir).to receive(:exists?).and_return(true)
      allow(Dir).to receive(:glob).with("#{dir}/**/*").and_return(dir_content)
      expect(AwsS3Wrapper).not_to receive(:download!)

      subject.ensure_supervision_identities_downloaded
    end

    it 'should download if dir or file dont exist' do
      allow(Dir).to receive(:exists?).and_return(false)
      expect(AwsS3Wrapper).to receive(:download!)
      expect(BrowserStack::OSUtils).to receive(:execute)
      expect(FileUtils).to receive(:chown_R)

      subject.ensure_supervision_identities_downloaded
    end
  end

  describe '#remove_debug_screenshots' do
    let(:dir) { '/Users/<USER>/debug_screenshots' }
    let(:new_file) { "#{dir}/new_file" }
    let(:new_dir) { "#{dir}/new_dir/" }
    let(:old_file) { "#{dir}/old_file" }
    let(:old_dir) { "#{dir}/old_dir" }

    let(:days) { 10 }

    let(:very_old) { Time.now - 60 * 60 * 24 * (days + 1) }

    before do
      FileUtils.mkdir_p(dir)
      allow(File).to receive(:mtime).and_return(Time.now) # Default response
      allow(File).to receive(:mtime).with(old_file).and_return(very_old)
      allow(File).to receive(:mtime).with(old_dir).and_return(very_old)
    end

    after do
      FileUtils.rm_rf(dir)
    end

    it 'creates debug screenshots dir if not exists' do
      FileUtils.rm_rf(dir)
      subject.remove_debug_screenshots
      expect(File).to exist(dir)
    end

    it 'does not remove new files' do
      FileUtils.touch(new_file)
      subject.remove_debug_screenshots
      expect(File).to exist(new_file)
    end

    it 'does not remove new directories' do
      FileUtils.mkdir(new_dir)
      subject.remove_debug_screenshots
      expect(File).to exist(new_dir)
    end

    it 'removes old files' do
      FileUtils.touch(old_file)
      subject.remove_debug_screenshots
      expect(File).not_to exist(old_file)
    end

    it 'removes old directories' do
      FileUtils.mkdir(old_dir)
      subject.remove_debug_screenshots
      expect(File).not_to exist(old_dir)
    end
  end

  describe '#instrument_usb_removals' do
    it 'pushes to zombie if usb removals >= 10' do
      allow(BrowserStack::OSUtils).to receive(:execute).and_return("15")
      expect(BrowserStack::Zombie).to receive(:push_logs)
      subject.instrument_usb_removals
    end
  end

  describe '#ensure_no_public_dns_configured' do
    context 'when no public DNS servers are found' do
      it 'logs the success and continues' do
        allow(BrowserStack::OSUtils).to receive(:execute)
          .with("scutil --dns | grep 'nameserver\\[[0-9]*\\]' | awk '{print $3}'")
          .and_return("192.168.1.1\n10.0.0.1")

        expect(BrowserStack::Zombie).not_to receive(:push_logs)

        subject.ensure_no_public_dns_configured
      end
    end

    context 'when public DNS servers are found' do
      it 'logs the error, sends alert via Zombie and raises an exception' do
        allow(BrowserStack::OSUtils).to receive(:execute)
          .with("scutil --dns | grep 'nameserver\\[[0-9]*\\]' | awk '{print $3}'")
          .and_return("8.8.8.8\n1.1.1.1")

        expect(BrowserStack::Zombie).to receive(:push_logs)
          .with("public-dns-found", nil, { "dns_servers" => ["8.8.8.8", "1.1.1.1"] })

        expect { subject.ensure_no_public_dns_configured }.to raise_error(MachineCheckException, "Public DNS servers found in configuration")
      end
    end
  end
end
