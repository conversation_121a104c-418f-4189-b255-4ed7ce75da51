require_relative '../../../lib/checks/check_plist'

require_relative '../../spec_helper'

RSpec.describe BrowserStack::CheckPlist do
  let(:fixtures_dir) { "#{Dir.getwd}/spec/fixtures/check_plist" }
  let(:launch_daemons_dir) { BrowserStack::Configuration['plist_dir_system'] }
  let(:launch_agents_dir) { BrowserStack::Configuration['plist_dir_user'] }

  before do
    FileUtils.mkdir_p(launch_daemons_dir)
    FileUtils.mkdir_p(launch_agents_dir)
    FileUtils.mkdir_p("#{NIX_SERVICE_FILES}/Library/LaunchAgents")
    FileUtils.mkdir_p("#{NIX_SERVICE_FILES}/Library/LaunchDaemons")

    BrowserStack::CheckPlist.configure(
      "#{Dir.getwd}/templates",
      launch_daemons_dir,
      launch_agents_dir
    )

    allow(Utils).to receive(:write_as_root) do |filename, data|
      File.write(filename, data)
    end
  end

  describe '.update_nix_plist' do
    let(:service_name) { 'com.browserstack.my_service' }
    let(:service_type) { ServiceType.SystemService }

    let(:destination) { "/Library/LaunchDaemons/#{service_name}.plist" }
    let(:source) { File.join(NIX_SERVICE_FILES, destination) }

    let(:plist_file) { "#{fixtures_dir}/expected_launch_agent.plist" }

    after do
      FileUtils.rm_rf(source)
      FileUtils.rm_rf(destination)
    end

    context 'no plist file in .browserstack/nix/services' do
      it 'raises error' do
        expect { BrowserStack::CheckPlist.update_nix_plist(name: service_name, type: service_type) }
          .to raise_error(/File not found/)
      end
    end

    context 'plist file present at .browserstack/nix/services but not at /Library/Launch.../' do
      before { FileUtils.cp(plist_file, source) }

      it 'unloads service, copies plist and reloads service' do
        expect(described_class).not_to receive(:unload_service)
        expect(BrowserStack::OSUtils).to receive(:execute).with("sudo cp #{source} #{destination}")
        expect(BrowserStack::OSUtils).to receive(:is_plist_loaded?).and_return(false)
        expect(described_class).to receive(:load_service).with(service_name, service_type)

        described_class.update_nix_plist(name: service_name, type: service_type)
      end
    end

    context 'plist file in .browserstack/nix/services is different to file at /Library/Launch...' do
      before do
        FileUtils.cp(plist_file, source)
        File.write(destination, '<alternative plist>')
      end

      it 'unloads service, copies plist and reloads service' do
        expect(described_class).to receive(:unload_service).with(service_name, service_type)
        expect(BrowserStack::OSUtils).to receive(:execute).with("sudo cp #{source} #{destination}")
        expect(BrowserStack::OSUtils).to receive(:is_plist_loaded?).and_return(false)
        expect(described_class).to receive(:load_service).with(service_name, service_type)

        described_class.update_nix_plist(name: service_name, type: service_type)
      end
    end

    context 'plist file the same in both locations, but service not running' do
      before do
        FileUtils.cp(plist_file, source)
        FileUtils.cp(plist_file, destination)
      end

      it 'loads the plist' do
        expect(described_class).not_to receive(:unload_service)
        expect(BrowserStack::OSUtils).not_to receive(:execute)
        expect(BrowserStack::OSUtils).to receive(:is_plist_loaded?).and_return(false)
        expect(described_class).to receive(:load_service).with(service_name, service_type)

        described_class.update_nix_plist(name: service_name, type: service_type)
      end
    end

    context 'plist file the same in both locations and service is running' do
      before do
        FileUtils.cp(plist_file, source)
        FileUtils.cp(plist_file, destination)
      end

      it 'does nothing' do
        expect(described_class).not_to receive(:unload_service)
        expect(BrowserStack::OSUtils).not_to receive(:execute)
        expect(BrowserStack::OSUtils).to receive(:is_plist_loaded?).and_return(true)
        expect(described_class).not_to receive(:load_service)

        described_class.update_nix_plist(name: service_name, type: service_type)
      end
    end
  end

  describe '#update' do
    let(:check_plist) do
      BrowserStack::CheckPlist.new(
        'example_program',
        ServiceType.UserService,
        ['run_command', 'arg1', 'arg2'],
        '/var/log/browserstack/example_program.log',
        '/var/log/browserstack/example_program.log',
        'app',
        '/custom/path',
        { 'WorkingDirectory' => '/my/working/dir' },
        false,
        { 'Minute' => 60 },
        true,
        true,
        'generic_plist.erb',
        '/custom/prepend/path',
        false
      )
    end
    let(:expected_plist_data) { File.read("#{fixtures_dir}/expected_launch_agent.plist") }

    before { FileUtils.rm_rf("#{launch_agents_dir}/example_program.plist") }

    context 'is a new LaunchDaemon/LaunchAgent' do
      it 'writes new plist file to disk and loads service' do
        expect(BrowserStack::CheckPlist).to receive(:load_service).with('example_program', ServiceType.UserService, 'app', 2)
        expect(BrowserStack::OSUtils).to receive(:is_plist_loaded?).with('example_program', ServiceType.UserService).and_return(true)

        check_plist.update

        expect(File.read("#{launch_agents_dir}/example_program.plist")).to eq(expected_plist_data)
      end
    end

    context 'plist file for LaunchDaemon/LaunchAgent already exists' do
      before(:each) do
        File.write("#{launch_agents_dir}/example_program.plist", expected_plist_data)
      end

      context 'new data matches data for existing plist' do
        context 'service is already loaded' do
          it 'does not try load the service or re write the plist data' do
            expect(BrowserStack::CheckPlist).to_not receive(:load_service).with('example_program', ServiceType.UserService, 'app', 2)
            expect(BrowserStack::OSUtils).to receive(:is_plist_loaded?).with('example_program', ServiceType.UserService).and_return(true, true)

            check_plist.update
          end
        end

        context 'service is not already loaded' do
          it 'calls to load the plist without attempting rewrite of plist data' do
            expect(BrowserStack::CheckPlist).to receive(:load_service).with('example_program', ServiceType.UserService, 'app', 2)
            expect(BrowserStack::OSUtils).to receive(:is_plist_loaded?).with('example_program', ServiceType.UserService).and_return(false, true)

            check_plist.update
          end
        end
      end

      context 'new data does not match data for existing plist' do
        it 'unloads existing service, writes new plist file to disk and loads service' do
          expect(BrowserStack::OSUtils).to receive(:file_compare).with(anything, "#{launch_agents_dir}/example_program.plist").and_return(false)
          expect(BrowserStack::CheckPlist).to receive(:unload_service).with('example_program', ServiceType.UserService, 'app')
          expect(BrowserStack::CheckPlist).to receive(:load_service).with('example_program', ServiceType.UserService, 'app', 2)
          expect(BrowserStack::OSUtils).to receive(:is_plist_loaded?).with('example_program', ServiceType.UserService).and_return(true)

          check_plist.update
        end
      end

      context 'force update is called' do
        it 'writes new plist file to disk and loads service' do
          expect(BrowserStack::CheckPlist).to receive(:unload_service).with('example_program', ServiceType.UserService, 'app')
          expect(BrowserStack::CheckPlist).to receive(:load_service).with('example_program', ServiceType.UserService, 'app', 2)
          expect(BrowserStack::OSUtils).to receive(:is_plist_loaded?).with('example_program', ServiceType.UserService).and_return(true)

          check_plist.update(true)
        end
      end
    end
  end
end
