require_relative '../spec_helper'
require_relative '../../lib/ios_influxdb_client'

describe BrowserStack::IosInfluxdbClient do
  let(:device_config) { fixture('full_config.json') }
  let(:device_id) { '00008030-000A65590183802E' }
  let(:subject) { BrowserStack::IosInfluxdbClient.new(MockLogger.new) }

  before do
    allow(File).to receive(:read).and_call_original
    allow(File).to receive(:read).with(CONFIG_JSON_FILE).and_return(device_config)
    allow(subject).to receive(:track_event)
  end

  describe '#event' do
    it 'tracks event with given parameters' do
      expect(subject).to receive(:track_event).with('unlock-device-failed', 'server', 'fire-cmd-start', device_id, 'true')

      subject.event(
        device_id,
        'unlock-device-failed',
        component: 'server',
        subcomponent: 'fire-cmd-start',
        is_error: true
      )
    end

    it 'handles keyword arguments correctly' do
      expect(subject).to receive(:track_event).with('unlock-device-failed', anything, anything, device_id, 'true')
      subject.event(
        device_id,
        'unlock-device-failed',
        is_error: true
      )
    end

    it 'defaults component to preset class variable if not given' do
      expect(subject).to receive(:track_event).with(anything, 'server', anything, anything, anything)

      BrowserStack::IosInfluxdbClient.class_variable_set(:@@component, 'server')
      subject.event(device_id, 'unlock-device-failed')
    end

    it 'defaults sub-component to the method name when nil' do
      expect(subject).to receive(:track_event).with(anything, anything, 'fire-cmd-start', anything, anything)

      def fire_cmd_start
        unlock_device
      end

      def unlock_device
        subject.event(device_id, 'unlock-device-failed')
      end

      fire_cmd_start
    end
  end

  describe '#device_name' do
    it 'returns correct device name from config' do
      expect(subject.device_name(device_id)).to eq('iPhone12,8')
    end

    it 'returns "not-applicable" if device name is "NA"' do
      expect(subject.device_name('NA')).to eq('not-applicable')
    end

    it 'returns \'unknown\' when device name is empty' do
      expect(subject.device_name("00008027-001069940181802E")).to eq('unknown')
    end

    it 'raises error if device id is not in config' do
      expect { subject.device_name('1234') }.to raise_error(RuntimeError)
    end
  end
end
