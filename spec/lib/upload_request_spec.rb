require_relative '../spec_helper'
require_relative '../../lib/upload_request'

describe UploadRequest do
  describe "start" do
    it "should create a json with all the params passed and make a create request" do
      upload_request = UploadRequest.new("someDirectory")
      expected_json = {
        file_name: "image_file_name",
        dest: "s3_file_name",
        upload_type: 'screenshot',
        key_id: "key",
        secret_key: "secret",
        session_id: "session_id",
        start_time: "start_time",
        screenshot_lock_file: "lockfile",
        keep_screenshot: true,
        check_black_screenshot: true
      }
      expect(SecureRandom).to receive(:uuid).and_return("randomeFileName")
      expect(upload_request).to receive(:create_request).with("someDirectory/randomeFileName.json", expected_json).once.and_return(nil)
      upload_request.start("image_file_name", "s3_file_name", "lockfile", "session_id", "start_time", "key", "secret", true, true)
    end
  end

  describe "create_request" do
    it "should create a request file when it doesn't exist" do
      upload_request = UploadRequest.new("someDirectory")
      expect(File).to receive(:dirname).with("someFile").and_return("someFile")
      expect(Dir).to receive(:exist?).and_return(true)
      expect(File).to receive(:exist?).with("someFile").and_return(false)
      expect(Utils).to receive(:write_to_file).with("someFile", { some_key: "value" }.to_json)
      upload_request.create_request("someFile", { some_key: "value" })
    end

    it "should not create a request file when it exist" do
      upload_request = UploadRequest.new("someDirectory")
      expect(File).to receive(:dirname).with("someFile").and_return("someFile")
      expect(Dir).to receive(:exist?).and_return(true)
      expect(File).to receive(:exist?).with("someFile").and_return(true)
      expect(Utils).to_not receive(:write_to_file)
      upload_request.create_request("someFile", { some_key: "value" })
    end
  end
end
