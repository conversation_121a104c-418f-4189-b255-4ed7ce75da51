require_relative '../spec_helper'
require_relative '../../lib/configuration'
require_relative '../../lib/cleanup_iphone'
require_relative '../../lib/helpers/automation'
require_relative '../../lib/helpers/testflight'
require_relative '../../lib/helpers/browserstack_app_helper'
require_relative '../../lib/utils/idevice_utils'
require_relative '../../lib/models/ios_device'
require 'fileutils'

describe CleanupIphone do
  let(:appium_port) { 8080 }
  let(:uuid) { 'abcd' }
  let(:device_version) { '13.2.0' }
  let(:mock_testflight) { double('testflight') }
  let(:device_config) do
    { "current_appium_version" => "1.14.0",
      "device_name" => "iPhone 8",
      "device_version" => device_version,
      "selenium_port" => 8080,
      "webdriver_port" => 8401,
      "debugger_port" => 12_345 }
  end
  let(:state_files_dir) { "/usr/local/.browserstack/state_files" }
  let(:mock_configuration_profiles_manager) { double(ConfigurationProfilesManager).as_null_object }
  let(:mock_configuration_profiles_enforcer) { double(ConfigurationProfilesEnforcer).as_null_object }
  let(:mock_ios_device) { double(IosDevice).as_null_object }

  before(:all) do
    IdeviceUtils.configure(BrowserStack::Configuration.conf)
  end

  before(:each) do
    allow(IdeviceUtils).to receive(:app_installed?).with(uuid, "TestFlight").and_return(true)
    allow(BrowserStack::Zombie).to receive(:push_logs)
    allow(ConfigurationProfilesManager).to receive(:new).and_return(mock_configuration_profiles_manager)
    allow(ConfigurationProfilesEnforcer).to receive(:new).and_return(mock_configuration_profiles_enforcer)
    allow(IosDevice).to receive(:new).and_return(mock_ios_device)
  end

  subject do
    CleanupIphone.new(
      'abcd',
      8080,
      device_version,
      'iPhone 11',
      8082,
      0,
      "session_id"
    )
  end

  describe '#check_global_proxy_installed' do
    let(:ip) { '*******' }
    let(:xcui_test_output) { double('xcui_test_output') }

    before do
      allow_any_instance_of(NetworkHelper::NetworkSetup).to receive(:get_ip).and_return(ip)
    end

    it 'should not check xcui output if IP is nil' do
      allow_any_instance_of(NetworkHelper::NetworkSetup).to receive(:get_ip).and_return(nil)
      allow(BrowserStackAppHelper).to receive(:run_ui_test).and_return(xcui_test_output)

      subject.check_global_proxy_installed

      expect(xcui_test_output).not_to receive(:include?)
    end

    it 'should touch check_global_proxy_file if IP is correct' do
      allow(BrowserStackAppHelper).to receive(:run_ui_test).and_return("some xcui output #{ip}")

      check_global_proxy_file = "#{STATE_FILES_DIR}/check_global_proxy_#{uuid}"
      expect(FileUtils).to receive(:touch).with(check_global_proxy_file).and_call_original

      subject.check_global_proxy_installed
    end

    it 'should not touch check_global_proxy_file & raise an exception if IP is incorrect' do
      allow(BrowserStackAppHelper).to receive(:run_ui_test).and_return("some xcui output with wrong IP")

      expect(FileUtils).not_to receive(:touch).with("#{STATE_FILES_DIR}/check_global_proxy_#{uuid}")

      expect { subject.check_global_proxy_installed }
        .to raise_error(MdmProfileException, 'Wrong global proxy installed in device')
    end
  end

  describe '#clear_clipboard' do
    let(:mock_wda_client) { double(WdaClient).as_null_object }
    before(:each) do
      allow(WdaClient).to receive(:new).and_return(mock_wda_client)
    end

    it 'clears clipboard' do
      expect(mock_wda_client).to receive(:set_foreground)
      expect(mock_wda_client).to receive(:clear_clipboard)
      subject.clear_clipboard
    end
  end

  describe '#clean_bookmarks_favorites' do
    it 'cleans using idevicebookmark' do
      expect(IdeviceUtils).to receive(:delete_bookmarks)
      subject.clean_bookmarks_favorites
    end
  end

  describe '#clean_safari_fav_and_bookmarks' do
    it 'cleans using UI automation, BrowserStackAppHelper runs an XCUI test' do
      expect(BrowserStackAppHelper).to receive(:run_ui_test).with('abcd', :clean_safari_fav_and_bookmarks, session_id: "session_id")
      subject.clean_safari_fav_and_bookmarks
    end
  end

  describe '#clean_safari_url_bar_position' do
    subject do
      CleanupIphone.new(
        'abcd',
        8080,
        15.0,
        'iPhone 13',
        8082,
        0,
        "session_id"
      )
    end

    it 'should clean safari url bar position if required file present' do
      expect(BrowserStackAppHelper).to receive(:run_ui_test).with('abcd', :clean_safari_url_bar_position, session_id: "session_id")
      expect(FileUtils).to receive(:rm_f).with("#{state_files_dir}/safari_url_bar_changed_abcd")
      subject.clean_safari_url_bar_position
    end
  end

  describe '#disable_government_notifications' do
    let(:mock_cleanup_check) { instance_double(CleanupCheck) }
    let(:mock_device_state) { double(DeviceState).as_null_object }

    before do
      allow(subject).to receive(:device_state).and_return(mock_device_state)
      allow(subject).to receive(:cleanup_check).and_return(mock_cleanup_check)
    end

    context 'when government notifications need to be disabled' do
      before do
        allow(mock_cleanup_check).to receive(:need_disable_government_notifications?).and_return(true)
        allow(BrowserStackAppHelper).to receive(:run_ui_test).with('abcd', :disable_government_notifications, session_id: "session_id")
      end

      it 'runs the UI test to disable government notifications' do
        expect(BrowserStackAppHelper).to receive(:run_ui_test).with('abcd', :disable_government_notifications, session_id: "session_id")
        subject.disable_government_notifications
      end

      it 'touches the government notifications file' do
        expect(subject.device_state).to receive(:touch_government_notifications_file)
        subject.disable_government_notifications
      end
    end

    context 'when government notifications do not need to be disabled' do
      before do
        allow(mock_cleanup_check).to receive(:need_disable_government_notifications?).and_return(false)
      end

      it 'does not run the UI test to disable government notifications' do
        expect(BrowserStackAppHelper).not_to receive(:run_ui_test)
        subject.disable_government_notifications
      end

      it 'does not touch the government notifications file' do
        expect(subject.device_state).not_to receive(:touch_government_notifications_file)
        subject.disable_government_notifications
      end
    end
  end

  describe '#grant_access_photos_permission' do
    let(:mock_device_state) { double(DeviceState).as_null_object }
    let(:mock_browserstack_app_helper) { class_double(BrowserStackAppHelper).as_stubbed_const }
    let(:mock_cleanup_check) { instance_double(CleanupCheck) }

    before do
      allow(subject).to receive(:device_state).and_return(mock_device_state)
      allow(subject).to receive(:cleanup_check).and_return(mock_cleanup_check)
    end

    context 'when photos permission is needed' do
      before do
        allow(mock_cleanup_check).to receive(:need_photos_permission?).and_return(true)
      end

      it 'runs the UI test to allow photos popup' do
        expect(mock_browserstack_app_helper).to receive(:run_ui_test).with('abcd', :allow_photos_popup, 120, session_id: "session_id")
        expect(mock_device_state).to receive(:remove_photos_permission_file)

        subject.grant_access_photos_permission
      end

      it 'retries and installs BrowserStack apps if an error occurs' do
        allow(mock_browserstack_app_helper).to receive(:run_ui_test).and_raise(StandardError)
        expect(mock_browserstack_app_helper).to receive(:check_and_install_browserstack_apps).with('abcd')

        expect { subject.grant_access_photos_permission }.to raise_error(StandardError)
      end
    end

    context 'when photos permission is not needed' do
      before do
        allow(mock_cleanup_check).to receive(:need_photos_permission?).and_return(false)
      end

      it 'does not run the UI test' do
        expect(mock_browserstack_app_helper).not_to receive(:run_ui_test)
        expect(mock_device_state).not_to receive(:remove_photos_permission_file)

        subject.grant_access_photos_permission
      end
    end
  end

  describe '#disable_airplane_mode_from_settings' do
    let(:mock_cleanup_check) { instance_double(CleanupCheck) }
    context 'when airplane mode needs to be disabled' do
      before do
        allow(subject).to receive(:cleanup_check).and_return(mock_cleanup_check)
        allow(mock_cleanup_check).to receive(:need_disable_airplane_mode_from_settings?).and_return(true)
      end

      it 'runs the UI test to disable airplane mode' do
        expect(BrowserStackAppHelper).to receive(:run_ui_test).with('abcd', :disable_airplane_mode, session_id: "session_id")
        subject.disable_airplane_mode_from_settings
      end
    end

    context 'when airplane mode does not need to be disabled' do
      before do
        allow(subject).to receive(:cleanup_check).and_return(mock_cleanup_check)
        allow(mock_cleanup_check).to receive(:need_disable_airplane_mode_from_settings?).and_return(false)
      end

      it 'does not run the UI test to disable airplane mode' do
        expect(BrowserStackAppHelper).not_to receive(:run_ui_test)
        subject.disable_airplane_mode_from_settings
      end
    end
  end

  describe '#clean_voiceover' do
    let(:mock_cleanup_check) { instance_double(CleanupCheck) }
    let(:mock_voiceover_helper) { instance_double(BrowserStack::VoiceoverHelper) }

    before do
      allow(subject).to receive(:cleanup_check).and_return(mock_cleanup_check)
      allow(BrowserStackAppHelper).to receive(:run_ui_test)
      allow(BrowserStack::VoiceoverHelper).to receive(:new).and_return(mock_voiceover_helper)
      allow(mock_voiceover_helper).to receive(:trigger_cleanup_on_bluetooth_server)
      allow(subject.device_state).to receive(:remove_voiceover_used_file)
    end

    context 'when voiceover cleanup is needed' do
      before do
        allow(mock_cleanup_check).to receive(:need_voiceover_cleanup?).and_return(true)
      end

      it 'runs the UI test to disable voiceover' do
        expect(BrowserStackAppHelper).to receive(:run_ui_test).with('abcd', :disable_voiceover)
        subject.clean_voiceover
      end

      it 'triggers cleanup on bluetooth server' do
        expect(mock_voiceover_helper).to receive(:trigger_cleanup_on_bluetooth_server).with('abcd')
        subject.clean_voiceover
      end

      it 'removes the voiceover used file' do
        expect(subject.device_state).to receive(:remove_voiceover_used_file)
        subject.clean_voiceover
      end

      it 'logs an error if the UI test fails' do
        allow(BrowserStackAppHelper).to receive(:run_ui_test).and_raise(StandardError, 'UI test failed')
        subject.clean_voiceover
      end
    end

    context 'when voiceover cleanup is not needed' do
      before do
        allow(mock_cleanup_check).to receive(:need_voiceover_cleanup?).and_return(false)
      end

      it 'does not run the UI test to disable voiceover' do
        expect(BrowserStackAppHelper).not_to receive(:run_ui_test)
        subject.clean_voiceover
      end

      it 'does not trigger cleanup on bluetooth server' do
        expect(mock_voiceover_helper).not_to receive(:trigger_cleanup_on_bluetooth_server)
        subject.clean_voiceover
      end

      it 'does not remove the voiceover used file' do
        expect(subject.device_state).not_to receive(:remove_voiceover_used_file)
        subject.clean_voiceover
      end
    end
  end

  describe '#safari_remote_automation' do
    subject do
      CleanupIphone.new(
        'abcd',
        8080,
        15.0,
        'iPhone 13',
        8082,
        0,
        "session_id"
      )
    end

    it 'should enable safari remote automation if required file not present' do
      expect(BrowserStackAppHelper).to receive(:run_ui_test).with('abcd', :safari_remote_automation, session_id: "session_id")
      expect(subject.device_state).to receive(:touch_safari_remote_automation_file)
      subject.safari_remote_automation
    end
  end

  describe '#siri_contacts_cleanup' do
    it 'cleans using UI automation, BrowserStackAppHelper runs an XCUI test' do
      expect(BrowserStackAppHelper).to receive(:run_ui_test).with('abcd', :siri_contacts_cleanup, session_id: "session_id")
      expect(subject.device_state).to receive(:touch_siri_contacts_cleanup_file)

      subject.siri_contacts_cleanup
    end
  end

  describe '#sim_signal_strength_xcui_run_test' do
    let(:device_id) { 'abcd' }
    let(:session_id) { 'session_id' }
    let(:device_state) { double('DeviceState') }
    let(:browserstack_app_helper) { class_double('BrowserStackAppHelper').as_stubbed_const }
    let(:browserstack_logger) { class_double('BrowserStack::Logger').as_stubbed_const }
    let(:browserstack_zombie) { class_double('BrowserStack::Zombie').as_stubbed_const }
    let(:browserstack_test_execution_error) { class_double('BrowserStackTestExecutionError').as_stubbed_const }
    let(:device_config) { { 'sim_details' => [{ 'carrier' => 'Carrier1', 'sim_slot' => 1 }] } }

    subject do
      CleanupIphone.new(
        device_id,
        8080,
        '13.2.0',
        'iPhone 11',
        8082,
        0,
        session_id
      )
    end

    before do
      allow(subject).to receive(:device_state).and_return(device_state)
      allow(subject).to receive(:device_config).and_return(device_config)
      allow(device_state).to receive(:touch_sim_signal_strength_file)
      allow(device_state).to receive(:sim_signal_strength_file_present?).and_return(false)
      allow(device_state).to receive(:sim_signal_strength_file_older_than_minutes?).and_return(true)
      allow(browserstack_logger).to receive(:info)
    end

    it 'runs the test and returns the xcui_output if the test is successful' do
      allow(browserstack_app_helper).to receive(:check_and_install_browserstack_test_suite).with(device_id)
      allow(browserstack_app_helper).to receive(:run_ui_test).with(device_id, :fetch_sim_signal_strength).and_return('[SIM_STRENGTH] SIM Signal Strengths: ["3 of 4 bars, signal strength"]')

      result = subject.sim_signal_strength_xcui_run_test

      expect(result).to eq({ 'status' => true, 'xcui_output' => '[SIM_STRENGTH] SIM Signal Strengths: ["3 of 4 bars, signal strength"]' })
    end

    it 'returns false status if the xcui_output is empty' do
      allow(browserstack_app_helper).to receive(:check_and_install_browserstack_test_suite).with(device_id)
      allow(browserstack_app_helper).to receive(:run_ui_test).with(device_id, :fetch_sim_signal_strength).and_return(nil)

      result = subject.sim_signal_strength_xcui_run_test

      expect(result).to eq({ 'status' => false, 'xcui_output' => nil })
    end

    it 'returns false status if an error occurs' do
      allow(browserstack_app_helper).to receive(:check_and_install_browserstack_test_suite).with(device_id)
      allow(browserstack_app_helper).to receive(:run_ui_test).with(device_id, :fetch_sim_signal_strength).and_raise(StandardError.new('Test error'))
    end
  end

  describe '#get_bars_from_xcui_output' do
    let(:sim_details) { [{ "carrier" => "Carrier1", "sim_slot" => 1 }, { "carrier" => "Carrier2", "sim_slot" => 2 }] }
    let(:xcui_output) { '[SIM_STRENGTH] SIM Signal Strengths: ["3 of 4 bars, signal strength", "2 of 4 bars, signal strength"]' }

    it 'returns updated sim details with signal strength' do
      result = subject.get_bars_from_xcui_output(xcui_output, sim_details)
      expect(result).to eq([
        { "carrier" => "Carrier1", "signal_strength" => "3", "sim_slot" => 1 },
        { "carrier" => "Carrier2", "signal_strength" => "2", "sim_slot" => 2 }
      ])
    end

    it 'returns original sim details if no matches are found' do
      result = subject.get_bars_from_xcui_output('', sim_details)
      expect(result).to eq([{ "carrier" => "Carrier1", "signal_strength" => "Unknown", "sim_slot" => 1 }])
    end

    it 'handles negative signal strength values as "Unknown"' do
      xcui_output_with_negative = '[SIM_STRENGTH] SIM Signal Strengths: ["-1 of 4 bars, signal strength"]'
      result = subject.get_bars_from_xcui_output(xcui_output_with_negative, sim_details)
      expect(result).to eq([
        { "carrier" => "Carrier1", "signal_strength" => "Unknown", "sim_slot" => 1 },
        { "carrier" => "Carrier2", "sim_slot" => 2 }
      ])
    end

    it 'ignores extra signal strength values beyond available sim slots' do
      xcui_output_extra = '[SIM_STRENGTH] SIM Signal Strengths: ["3 of 4 bars, signal strength", "2 of 4 bars, signal strength", "1 of 4 bars, signal strength"]'
      result = subject.get_bars_from_xcui_output(xcui_output_extra, sim_details)
      expect(result).to eq([
        { "carrier" => "Carrier1", "signal_strength" => "3", "sim_slot" => 1 },
        { "carrier" => "Carrier2", "signal_strength" => "2", "sim_slot" => 2 }
      ])
    end

    it 'logs error and returns original sim details if an exception occurs' do
      allow(subject).to receive(:get_bars_from_xcui_output).and_raise(StandardError.new("Test error"))
      result = begin
        subject.get_bars_from_xcui_output(xcui_output, sim_details)
      rescue
        sim_details
      end
      expect(result).to eq(sim_details)
    end
  end

  describe '#check_sim_signal_strength' do
    let(:device_state) { double('DeviceState', dedicated_device_file_present?: false) }
    let(:device_id) { 'test-device-id' }
    let(:sim_details) { { "carrier" => "TestCarrier", "sim_slot" => 1 } }
    let(:device_config) { { 'sim_details' => sim_details } }
    let(:test_response) { { 'status' => true, 'xcui_output' => [] } }

    before do
      allow(subject).to receive(:device_id).and_return(device_id)
      allow(subject).to receive(:device_config).and_return(device_config)
      allow(subject).to receive(:sim_signal_strength_xcui_run_test).and_return(test_response)
      allow(subject).to receive(:get_bars_from_xcui_output).and_return([{ "carrier" => "TestCarrier", "signal_strength" => 3, "sim_slot" => 1 }])
      allow(subject).to receive(:device_state).and_return(device_state)
    end

    it "should return if sim_details is nil" do
      allow(subject).to receive(:device_config).and_return({ 'sim_details' => nil })
      subject.check_sim_signal_strength
    end

    it "should return if sim_details is empty" do
      allow(subject).to receive(:device_config).and_return({ 'sim_details' => {} })
      subject.check_sim_signal_strength
    end

    it "should handle exceptions gracefully without raising errors" do
      allow(subject).to receive(:sim_signal_strength_xcui_run_test).and_raise(StandardError.new('Test error'))
      expect { subject.check_sim_signal_strength }.not_to raise_error
    end
  end

  describe '#reset_keyboard_settings' do
    it 'cleans using UI automation, BrowserStackAppHelper runs an XCUI test' do
      expect(BrowserStackAppHelper).to receive(:run_ui_test).with('abcd', :reset_keyboard_settings, session_id: "session_id").and_return ""

      subject.reset_keyboard_settings
    end
  end

  describe '#disable_paint_timing' do
    subject do
      CleanupIphone.new(
        'abcd',
        8080,
        13.3,
        'iPhone 11',
        8082,
        0,
        "session_id"
      )
    end

    it 'should disable experimental feature paint timing if required file present' do
      expect(BrowserStackAppHelper).to receive(:run_ui_test).with('abcd', :safari_experimental_feature_paint_timing_disable, session_id: "session_id")
      expect(FileUtils).to receive(:rm_f).with("#{state_files_dir}/paint_timing_enabled_abcd")
      subject.disable_paint_timing
    end
  end

  describe '#disable_safari_websocket' do
    it 'should run ui test to disable experimental websocket setting' do
      expect(BrowserStackAppHelper).to receive(:run_ui_test).with('abcd', :safari_experimental_feature_websocket_disable, session_id: "session_id")
      subject.disable_safari_websocket
    end
  end

  describe '#disconnect_wifi' do
    let(:wifi_name) { "abcd" }
    subject do
      CleanupIphone.new(
        'abcd',
        8080,
        13.3,
        'iPhone 11',
        8082,
        0,
        "session_id"
      )
    end

    it 'should disconnect wifi if required file present' do
      allow(subject.instance_variable_get(:@config)).to receive(:[])
        .with('static_conf')
        .and_return({ 'ssid' => wifi_name })
      expect(BrowserStackAppHelper).to receive(:run_ui_test).with('abcd', :disconnect_wifi, session_id: "session_id", environment_variables: { wifi_name: wifi_name })
      subject.disconnect_wifi
    end
  end

  context 'ifuse cleanup' do
    let(:mock_ifuse) { double('mock_ifuse') }
    let(:mount_point) { '/tmp/test_mount_point' }
    let(:mock_photos_sqlite_helper) { double('mock_photos_sqlite_helper') }

    before do
      allow(mock_ifuse).to receive(:run)
      allow(mock_ifuse).to receive(:mount_point).and_return(mount_point)
      allow(Ifuse).to receive(:new).and_return(mock_ifuse)
      allow(PhotosSqliteHelper).to receive(:new).and_return(mock_photos_sqlite_helper)
      allow(mock_photos_sqlite_helper).to receive(:ios_media_state_consistent?).and_return(true)
    end

    describe '.copy_media' do
      it 'runs ifuse command' do
        expect(mock_ifuse).to receive(:run)
        subject.copy_media
      end
    end

    describe '.backup_preloaded_files' do
      it 'runs ifuse command' do
        expect(mock_ifuse).to receive(:run)
        subject.backup_preloaded_files
      end
    end

    describe '.install_preloaded_files' do
      let(:media_directory) { '/tmp/test_bs_media_multiversion' }
      let(:canonical_device_version) do
        BrowserStack::Version.new(device_version).canonical_without_patch
      end

      before do
        allow(subject.instance_variable_get(:@config)).to receive(:[])
          .with('bs_media_multiversion')
          .and_return(media_directory)

        allow(mock_ifuse).to receive(:run).and_yield
        allow(subject).to receive(:force_install_preloaded_media?).and_return false
        allow(subject).to receive(:optimised_preload_media_deletion) { |array| array }
      end

      it 'raises error unless media directory exists' do
        FileUtils.rm_rf(media_directory)
        allow(mock_ios_device).to receive(:device_eligible_for_optimised_flow?).and_return(false)
        expect { subject.install_preloaded_files }.to raise_error(/Can't find media/)
      end

      context 'media directory exists' do
        before do
          FileUtils.mkdir(mount_point)

          FileUtils.touch("#{mount_point}/downloads.28.sqlitedb")
          FileUtils.touch("#{mount_point}/downloads.28.sqlitedb-shm")
          FileUtils.mkdir("#{mount_point}/Downloads/")

          FileUtils.touch("#{mount_point}/foobar")
          FileUtils.mkdir("#{mount_point}/FooBar/")
          FileUtils.touch("#{mount_point}/FooBar/foobar")

          FileUtils.mkdir(media_directory)

          FileUtils.touch("#{media_directory}/IMG1")
          FileUtils.touch("#{media_directory}/IMG2")

          FileUtils.touch("#{media_directory}/Photos_#{canonical_device_version}.sqlite")
          FileUtils.touch("#{media_directory}/Photos_#{canonical_device_version}.sqlite-shm")
        end

        after do
          FileUtils.rm_rf(mount_point)
          FileUtils.rm_rf(media_directory)
        end

        it 'runs commands in ifuse block' do
          allow(subject).to receive(:preloaded_files_consistent?).with(mount_point).and_return(false, true)
          allow(mock_ios_device).to receive(:device_eligible_for_optimised_flow?).and_return(false)
          subject.install_preloaded_files

          expect(File).not_to exist("#{mount_point}/foobar")
          expect(File).not_to exist("#{mount_point}/FooBar/foobar")

          expect(File).to exist("#{mount_point}/DCIM/100APPLE")
          expect(File).to exist("#{mount_point}/PhotoData")

          expect(File).to exist("#{mount_point}/DCIM/100APPLE/IMG1")
          expect(File).to exist("#{mount_point}/DCIM/100APPLE/IMG2")

          expect(File).to exist("#{mount_point}/PhotoData/Photos.sqlite")
          expect(File).to exist("#{mount_point}/PhotoData/Photos.sqlite-shm")
        end

        it 'raises error if sqlite dbs are missing' do
          FileUtils.rm("#{media_directory}/Photos_#{canonical_device_version}.sqlite")
          FileUtils.rm("#{media_directory}/Photos_#{canonical_device_version}.sqlite-shm")
          allow(mock_ios_device).to receive(:device_eligible_for_optimised_flow?).and_return(false)
          expect { subject.install_preloaded_files }.to raise_error(/No Photos.sqlite databases/)
        end
      end
    end

    describe '.install_preloaded_files_legacy' do
      it 'runs ifuse command' do
        expect(mock_ifuse).to receive(:run)
        subject.install_preloaded_files_legacy
      end
    end

    describe '.cleanup_custom_media' do
      let(:mock_app) { double('mock_app') }
      let(:custom_media_mount_point) { "/tmp/custom_media_mount_#{uuid}" }

      before do
        allow(BrowserStack::App).to receive(:new).and_return(mock_app)
      end

      after do
        FileUtils.rm_rf(custom_media_mount_point)
      end

      it 'mounts app root dir, removes synced media, unmounts app root dir' do
        allow_any_instance_of(DeviceState).to receive(
          :custom_media_cleanup_file_present?
        ).and_return(true)
        expect(mock_app).to receive(:mount_root_directory_of_app)
          .with(uuid, custom_media_mount_point, BROWSERSTACK_APP_BUNDLE_ID)
        expect(BrowserStack::OSUtils).to receive(:execute).with(
          /rm -rf/, true
        ).and_return(["any_string", 0])
        expect(mock_app).to receive(:unmount_root_directory_of_app)
          .with(custom_media_mount_point)

        subject.send :cleanup_custom_media
      end

      # rubocop:disable Style/RegexpLiteral
      it 'falls back to ios-deploy if ifuse based removal fails' do
        allow_any_instance_of(DeviceState).to receive(
          :custom_media_cleanup_file_present?
        ).and_return(true)
        expect(mock_app).to receive(:mount_root_directory_of_app)
          .with(uuid, custom_media_mount_point, BROWSERSTACK_APP_BUNDLE_ID)
        expect(BrowserStack::OSUtils).to receive(:execute).with(/rm -rf/, true).and_return(["any_string", 1])
        expect(mock_app).to receive(:unmount_root_directory_of_app)
          .with(custom_media_mount_point)

        expect(BrowserStack::OSUtils).to receive(:execute).twice.with(%r{ios-deploy --id})

        subject.instance_variable_set(:@device_version, 18.0)

        subject.send :cleanup_custom_media
      end
      # rubocop:enable Style/RegexpLiteral
    end
  end

  describe '#reset_contacts_app' do
    context "when mobile address book app access is enabled" do
      let(:params) { { "enable_mobile_addressbook_app_access" => true } }
      let(:mock_contacts_app_helper) { double(ContactsAppHelper).as_null_object }
      let(:mock_wda_client) { double(WdaClient).as_null_object }

      before(:each) do
        expect(subject).to receive(:contacts_app_helper).and_return(mock_contacts_app_helper)
        allow(WdaClient).to receive(:new).and_return(mock_wda_client)
      end

      it "should add contacts app icon to home screen" do
        expect(mock_contacts_app_helper).to receive(:add_icon_to_home_screen)
        subject.reset_contacts_app
      end

      context 'when contacts app is opened' do
        before(:each) do
          allow_any_instance_of(DeviceState).to receive(:device_logger_detected_contacts_app_opened_file_present?).and_return(true)
        end

        it 'should send data to eds' do
          expect(Utils).to receive(:send_to_eds)
          subject.reset_contacts_app
        end

        it 'should kill contacts app' do
          expect(mock_wda_client).to receive(:kill_apps)
          subject.reset_contacts_app
        end

        it 'should remove usage file' do
          expect_any_instance_of(DeviceState).to receive(:remove_device_logger_detected_contacts_app_opened_file)
          subject.reset_contacts_app
        end
      end
    end
  end

  context 'completed steps' do
    let(:clean_steps_completed_file) { '/tmp/cleanup_completed_steps_abcd' }
    let(:device_hash) { {} }
    before(:each) do
      allow(BrowserStack::DeviceConf).to receive(:[]).and_return(device_hash)
      allow(BrowserStack::Configuration).to receive(:conf).and_return('state_files_dir' => '/tmp')
    end

    describe '#get_cleanup_state' do
      it 'returns empty hash if no state file present' do
        expect(subject.instance_variable_get(:@completed_steps).empty?).to be true
      end

      it 'reads state file and parses json if state file present' do
        File.write(clean_steps_completed_file, { safari_cleanup: true, sign_out_sandbox_accounts: true }.to_json)
        expect(subject.instance_variable_get(:@completed_steps)[:safari_cleanup]).to be true
        expect(subject.instance_variable_get(:@completed_steps)[:sign_out_sandbox_accounts]).to be true
      end

      it 'reads state file and returns empty hash if state file empty' do
        File.write(clean_steps_completed_file, '')
        expect(subject.instance_variable_get(:@completed_steps).empty?).to be true
      end
    end

    describe '#save_completed_steps_state' do
      let(:completed_steps) { { safari_cleanup: true, sign_out_sandbox_accounts: true } }
      before(:each) do
        subject.instance_variable_set(:@completed_steps, completed_steps)
        FileUtils.rm_r(clean_steps_completed_file)
      end

      it 'writes completed_steps hash to json state file' do
        expect(File.exist?(clean_steps_completed_file)).to be false
        subject.save_completed_steps_state
        expect(File.exist?(clean_steps_completed_file)).to be true
        saved_state = JSON.parse(File.read(clean_steps_completed_file), symbolize_names: true)
        expect(saved_state[:safari_cleanup]).to be true
        expect(saved_state[:sign_out_sandbox_accounts]).to be true
      end
    end

    describe '#clear_completed_steps_state' do
      before(:each) do
        File.write(clean_steps_completed_file, { safari_cleanup: true, sign_out_sandbox_accounts: true }.to_json)
      end

      it 'removes completed_steps state file' do
        expect(File.exist?(clean_steps_completed_file)).to be true
        subject.clear_completed_steps_state
        expect(File.exist?(clean_steps_completed_file)).to be false
        expect(subject.instance_variable_get(:@completed_steps).empty?).to be true
      end
    end

    describe '#set_time_to_utc' do
      it "should run ui test if it is needed to set time" do
        expect(BrowserStackAppHelper).to receive(:run_ui_test).with(uuid, :set_time_to_utc, 120, session_id: "session_id")

        subject.send(:set_time_to_utc)
      end
    end

    describe '#timezone_reset_to_utc_needed' do
      it "should return true if timezone of device is not GMT" do
        expect(IdeviceUtils).to receive(:ideviceinfo).with(uuid, "TimeZone").and_return(['Asia/Kolkata'])
        expect(subject.timezone_reset_to_utc_needed?).to be true
      end

      it "should return false if timezone of device is GMT" do
        expect(IdeviceUtils).to receive(:ideviceinfo).with(uuid, "TimeZone").and_return(['GMT'])
        expect(subject.timezone_reset_to_utc_needed?).to be false
      end
    end

    describe '#reset_time_zone_to_utc' do
      let(:event_name) { 'web_events' }
      let(:timezone_helper) { double('timezone_helper') }

      it "should run the wda test and send success to zombie if test runs with wda" do
        expect(BrowserStack::TimezoneHelper).to receive(:new).and_return(timezone_helper)
        expect(timezone_helper).to receive(:change_time_zone).and_return(true)
        expect(BrowserStack::Zombie).to receive(:push_logs).with("timezone-change-success", "", { "device" => uuid, "session_id" => 'session_id', "data" => { "cleanup" => "success" } })

        subject.send(:reset_time_zone_to_utc)
      end

      it "should run the xcui test and send fallback info to zombie if test fails with wda" do
        expect(BrowserStack::TimezoneHelper).to receive(:new).and_return(timezone_helper)
        expect(timezone_helper).to receive(:change_time_zone).and_return(false)
        expect(BrowserStackAppHelper).to receive(:run_ui_test).and_return('success')
        expect(BrowserStack::Zombie).to receive(:push_logs).with("timezone-change-success", "XCUI Fallback", { "device" => uuid, "session_id" => 'session_id', "data" => { "cleanup" => "success", "fallback" => "true" } })

        subject.send(:reset_time_zone_to_utc)
      end

      it "should send failure data to zombie in case both xcui and wda fails" do
        expect(BrowserStack::TimezoneHelper).to receive(:new).and_return(timezone_helper)
        expect(timezone_helper).to receive(:change_time_zone).and_return(false)
        expect(BrowserStackAppHelper).to receive(:run_ui_test).and_raise(BrowserStackTestExecutionError.new('test_class', 'test_func', 'test_output'))

        expect(BrowserStack::Zombie).to receive(:push_logs).with("timezone-change-failure", "test_output", { "device" => uuid, "session_id" => 'session_id', "data" => { "cleanup" => "failure" } })

        subject.send(:reset_time_zone_to_utc)
      end
    end

    # TODO: We need better tests for the actual implementation of these individual steps, when we do this we can improve the tests below
    context 'calling cleanup steps' do
      let(:completed_steps) { Hash.new(true) }
      before(:each) { subject.instance_variable_set(:@completed_steps, completed_steps) }

      it 'performs no operation when method is marked as already completed' do
        expect { subject.delete_downloads }.not_to raise_error
        expect { subject.safari_cleanup }.not_to raise_error
        expect { subject.disable_assistive_touch }.not_to raise_error
        expect { subject.disable_government_notifications }.not_to raise_error
        expect { subject.apple_id_signout }.not_to raise_error
        expect { subject.sign_out_sandbox_accounts }.not_to raise_error
        expect { subject.disable_airplane_mode_from_settings }.not_to raise_error
        expect { subject.disable_low_power_mode }.not_to raise_error
        expect { subject.clean_pwa_and_waiting_state_apps }.not_to raise_error
        expect { subject.clear_reading_list }.not_to raise_error
        expect { subject.siri_contacts_cleanup }.not_to raise_error
        expect { subject.disconnect_wifi }.not_to raise_error
      end
    end
  end

  describe 'force_re_enroll_to_mdm' do
    it 'should not raise any error if MDM UI automation is successful' do
      allow(IosMdmServiceClient).to receive(:run_mdm_ui_automation).and_return(true)
      expect { subject.force_re_enroll_to_mdm("Full Cleanup") }.not_to raise_error
    end

    it 'should raise error and send data to zombie if MDM UI automation is not successful due to BrowserStackTestExecutionError' do
      allow(IosMdmServiceClient).to receive(:run_mdm_ui_automation).and_raise(BrowserStackTestExecutionError.new('test_class', 'test_func', 'test_output'))
      expect(BrowserStack::Zombie).to receive(:push_logs).with('mdm-re-enrollment', /MDM re-enrollment automation failed/, anything)
      expect { subject.force_re_enroll_to_mdm("Full Cleanup") }.to raise_error(/MDM re-enrollment failed/)
    end
  end

  describe 'install_proxy_profile' do
    it 'should not raise any error if proxy profile installation is successful' do
      allow(mock_configuration_profiles_manager).to receive(:install_profile).with(:proxy, install_via: :automatic).and_return(true)
      expect(subject.device_state).to receive(:touch_check_global_proxy_file).and_return(true)
      expect { subject.install_proxy_profile("Full Cleanup") }.not_to raise_error
    end

    it 'should raise any error if proxy profile installation is not successful' do
      allow(mock_configuration_profiles_manager).to receive(:install_profile).with(:proxy, install_via: :automatic).and_raise("MDM server not reachable")
      expect { subject.install_proxy_profile("Full Cleanup") }.to raise_error("Proxy Profile Installation Failed")
    end
  end

  describe 'erase_and_restore' do
    let(:backup_manager) { double('backup_manager') }

    before(:each) do
      allow(CFGUtilBackupManager).to receive(:new).and_return(backup_manager)
    end

    it 'should not raise any error if erase and restore flow is successful' do
      expect(backup_manager).to receive(:backup_ready?).and_return(true)
      allow(OSUtils).to receive(:execute).and_return("udid")
      expect(backup_manager).to receive(:erase_device).and_return(true)
      expect(backup_manager).to receive(:restore_backup).and_return(true)
      allow(subject).to receive(:sleep).and_return(true)
      expect { subject.erase_and_restore }.not_to raise_error
    end

    it 'should raise error if backup is not found for the device' do
      expect(backup_manager).to receive(:backup_ready?).and_return(false)
      allow(OSUtils).to receive(:execute).and_return("udid")
      allow(subject).to receive(:sleep).and_return(true)
      expect { subject.erase_and_restore }.to raise_error(CFGUtilBackupManager::BackupNotFoundError, "No backup found for #{uuid}")
    end

    it 'should throw timeout error if the flow is timedout due to restore' do
      expect(backup_manager).to receive(:backup_ready?).and_return(true)
      allow(OSUtils).to receive(:execute).and_return("udid")
      allow(backup_manager).to receive(:erase_device).and_return(true)
      expect(backup_manager).to receive(:restore_backup).and_raise(Timeout::Error, "Restore Timed Out")
      allow(subject).to receive(:sleep).and_return(true)
      expect { subject.erase_and_restore }.to raise_error("Full Cleanup: Erase and Restore Timed out")
    end
  end

  describe 'force_re_enroll_to_mdm' do
    it 'should not raise any error if MDM UI automation is successful' do
      allow(IosMdmServiceClient).to receive(:run_mdm_ui_automation).and_return(true)
      expect { subject.force_re_enroll_to_mdm("Full Cleanup") }.not_to raise_error
    end

    it 'should raise error and send data to zombie if MDM UI automation is not successful due to BrowserStackTestExecutionError' do
      allow(IosMdmServiceClient).to receive(:run_mdm_ui_automation).and_raise(BrowserStackTestExecutionError.new('test_class', 'test_func', 'test_output'))
      expect(BrowserStack::Zombie).to receive(:push_logs).with('mdm-re-enrollment', /MDM re-enrollment automation failed/, anything)
      expect { subject.force_re_enroll_to_mdm("Full Cleanup") }.to raise_error(/MDM re-enrollment failed/)
    end
  end

  describe 'install_proxy_profile' do
    it 'should not raise any error if proxy profile installation is successful' do
      allow(mock_configuration_profiles_manager).to receive(:install_profile).with(:proxy, install_via: :automatic).and_return(true)
      expect(subject.device_state).to receive(:touch_check_global_proxy_file).and_return(true)
      expect { subject.install_proxy_profile("Full Cleanup") }.not_to raise_error
    end

    it 'should raise any error if proxy profile installation is not successful' do
      allow(mock_configuration_profiles_manager).to receive(:install_profile).with(:proxy, install_via: :automatic).and_raise("MDM server not reachable")
      expect { subject.install_proxy_profile("Full Cleanup") }.to raise_error("Proxy Profile Installation Failed")
    end
  end

  describe 'erase_and_restore' do
    let(:backup_manager) { double('backup_manager') }

    before(:each) do
      allow(CFGUtilBackupManager).to receive(:new).and_return(backup_manager)
    end

    it 'should not raise any error if erase and restore flow is successful' do
      expect(backup_manager).to receive(:backup_ready?).and_return(true)
      allow(OSUtils).to receive(:execute).and_return("udid")
      expect(backup_manager).to receive(:erase_device).and_return(true)
      expect(backup_manager).to receive(:restore_backup).and_return(true)
      allow(subject).to receive(:sleep).and_return(true)
      expect { subject.erase_and_restore }.not_to raise_error
    end

    it 'should raise error if backup is not found for the device' do
      expect(backup_manager).to receive(:backup_ready?).and_return(false)
      allow(OSUtils).to receive(:execute).and_return("udid")
      allow(subject).to receive(:sleep).and_return(true)
      expect { subject.erase_and_restore }.to raise_error(CFGUtilBackupManager::BackupNotFoundError, "No backup found for #{uuid}")
    end

    it 'should throw timeout error if the flow is timedout due to restore' do
      expect(backup_manager).to receive(:backup_ready?).and_return(true)
      allow(OSUtils).to receive(:execute).and_return("udid")
      allow(backup_manager).to receive(:erase_device).and_return(true)
      expect(backup_manager).to receive(:restore_backup).and_raise(Timeout::Error, "Restore Timed Out")
      allow(subject).to receive(:sleep).and_return(true)
      expect { subject.erase_and_restore }.to raise_error("Full Cleanup: Erase and Restore Timed out")
    end
  end

  describe '#check_and_enforce_configuration_profiles' do
    context 'when device uses cfgutil managed profiles' do
      before(:each) do
        allow(mock_configuration_profiles_manager).to receive(:device_uses_cfgutil_managed_profiles?).and_return(true)
      end

      context 'when force_install_mdm_profiles_file is present' do
        before(:each) do
          allow(subject.device_state).to receive(:force_install_mdm_profiles_file_present?).and_return(true)
          allow(subject.device_state).to receive(:configuration_profile_periodic_check_file_older_than_days?).and_return(false)
        end

        it 'should call enforce_configuration_profiles with force_install: true and periodic_check_due: false' do
          expect(mock_configuration_profiles_enforcer).to receive(:enforce_configuration_profiles).with(true, false).and_return(true)
          expect(subject.check_and_enforce_configuration_profiles).to eql(true)
        end

        it 'should raise error when error is raised' do
          expect(mock_configuration_profiles_enforcer).to receive(:enforce_configuration_profiles).with(true, false).and_raise("Something went wrong")
          expect { subject.check_and_enforce_configuration_profiles }.to raise_error("Something went wrong")
        end
      end

      context 'when configuration_profile_periodic_check_file is older than configuration_profile_periodic_check' do
        before(:each) do
          allow(subject.device_state).to receive(:force_install_mdm_profiles_file_present?).and_return(false)
          allow(subject.device_state).to receive(:configuration_profile_periodic_check_file_older_than_days?).and_return(true)
        end

        it 'should call enforce_configuration_profiles with force_install: false and periodic_check_due: true' do
          expect(mock_configuration_profiles_enforcer).to receive(:enforce_configuration_profiles).with(false, true).and_return(true)
          expect(subject.check_and_enforce_configuration_profiles).to eql(true)
        end

        it 'should raise error when error is raised' do
          expect(mock_configuration_profiles_enforcer).to receive(:enforce_configuration_profiles).with(false, true).and_raise("Something went wrong")
          expect { subject.check_and_enforce_configuration_profiles }.to raise_error("Something went wrong")
        end
      end

      context 'when both force_install_mdm_profiles_file is present and configuration_profile_periodic_check_file is older than configuration_profile_periodic_check' do
        before(:each) do
          allow(subject.device_state).to receive(:force_install_mdm_profiles_file_present?).and_return(true)
          allow(subject.device_state).to receive(:configuration_profile_periodic_check_file_older_than_days?).and_return(true)
        end

        it 'should call enforce_configuration_profiles with force_install: true and periodic_check_due: true' do
          expect(mock_configuration_profiles_enforcer).to receive(:enforce_configuration_profiles).with(true, true).and_return(true)
          expect(subject.check_and_enforce_configuration_profiles).to eql(true)
        end

        it 'should raise error when error is raised' do
          expect(mock_configuration_profiles_enforcer).to receive(:enforce_configuration_profiles).with(true, true).and_raise("Something went wrong")
          expect { subject.check_and_enforce_configuration_profiles }.to raise_error("Something went wrong")
        end
      end

      context 'when neither force_install_mdm_profiles_file is present and neither configuration_profile_periodic_check_file is older than configuration_profile_periodic_check' do
        before(:each) do
          allow(subject.device_state).to receive(:force_install_mdm_profiles_file_present?).and_return(false)
          allow(subject.device_state).to receive(:configuration_profile_periodic_check_file_older_than_days?).and_return(false)
        end

        it 'should call enforce_configuration_profiles with force_install: false and periodic_check_due: false' do
          expect(mock_configuration_profiles_enforcer).to receive(:enforce_configuration_profiles).with(false, false).and_return(true)
          expect(subject.check_and_enforce_configuration_profiles).to eql(true)
        end
      end
    end

    context 'when device does not use cfgutil managed profiles' do
      before(:each) do
        allow(mock_configuration_profiles_manager).to receive(:device_uses_cfgutil_managed_profiles?).and_return(false)
      end

      it 'should not call enforce_configuration_profiles' do
        expect(mock_configuration_profiles_enforcer).not_to receive(:enforce_configuration_profiles)
        expect(subject.check_and_enforce_configuration_profiles).to eql(nil)
      end
    end
  end

  describe '#enroll_device_to_mdm_via_cfgutil' do
    context 'when force_enroll is false' do
      context 'when MDM is already installed' do
        before(:each) do
          allow(mock_configuration_profiles_manager).to receive(:profile_installed_via_cfgutil?).and_return(true)
        end

        it 'should return early' do
          expect(mock_configuration_profiles_manager).not_to receive(:remove_profile)
          expect(mock_configuration_profiles_manager).not_to receive(:install_profile)
          expect(subject.enroll_device_to_mdm_via_cfgutil).to eql(true)
        end
      end

      context 'when MDM is not already installed' do
        before(:each) do
          allow(mock_configuration_profiles_manager).to receive(:profile_installed_via_cfgutil?).and_return(false)
        end

        it 'should install MDM profile' do
          expect(mock_configuration_profiles_manager).not_to receive(:remove_profile)
          expect(mock_configuration_profiles_manager).to receive(:install_profile).with(:MDM, install_via: :cfgutil)
          expect(subject.enroll_device_to_mdm_via_cfgutil).to eql(true)
        end
      end
    end

    context 'when force_enroll is true' do
      context 'when MDM is already installed' do
        before(:each) do
          allow(mock_configuration_profiles_manager).to receive(:profile_installed_via_cfgutil?).and_return(true)
        end

        it 'should install MDM profile' do
          expect(mock_configuration_profiles_manager).to receive(:remove_profile).with(:MDM, nil, remove_via: :cfgutil)
          expect(mock_configuration_profiles_manager).to receive(:install_profile).with(:MDM, install_via: :cfgutil)
          expect(subject.enroll_device_to_mdm_via_cfgutil(true)).to eql(true)
        end
      end

      context 'when MDM is not already installed' do
        before(:each) do
          allow(mock_configuration_profiles_manager).to receive(:profile_installed_via_cfgutil?).and_return(false)
        end

        it 'should remove and install MDM profile' do
          expect(mock_configuration_profiles_manager).not_to receive(:remove_profile)
          expect(mock_configuration_profiles_manager).to receive(:install_profile).with(:MDM, install_via: :cfgutil)
          expect(subject.enroll_device_to_mdm_via_cfgutil(true)).to eql(true)
        end
      end
    end
  end

  describe '#message_notification_cleanup' do
    it 'should not trigger cleanup if file not present' do
      expect(subject.device_state).to receive(:enable_message_notification_failure_file_present?).and_return(false)
      expect(Utils).to_not receive(:enable_notifications).with(subject.device_id)

      expect(subject.message_notification_cleanup).to eq(true)
    end

    it 'should trigger cleanup and run enable_notifications' do
      expect(subject.device_state).to receive(:enable_message_notification_failure_file_present?).and_return(true)
      expect(subject.device_state).to receive(:remove_enable_message_notification_failure_file)
      expect(Utils).to receive(:enable_notifications).with(subject.device_id)

      expect(subject.message_notification_cleanup).to eq(true)
    end

    it 'should trigger cleanup and run enable_notifications with error' do
      expect(subject.device_state).to receive(:enable_message_notification_failure_file_present?).and_return(true)
      expect(Utils).to receive(:enable_notifications).and_raise(StandardError)

      expect { subject.message_notification_cleanup }.to raise_error(StandardError)
    end
  end

  describe '#get_prefs_for_version' do
    let(:device_version) { '17.1' }
    let(:device_name) { 'iPhone12,1' }

    before(:each) do
      allow(SETTINGS_PREFS).to receive(:[]).and_return({
        rules: [
          {
            device_name: /^(iPhone|iPad).*/,
            os_version: /^(17\.\d+(\.\d+)?|18\.[0-1](\.\d+)?)$/,
            rule: "include",
            pref: "some_preference"
          },
          {
            device_name: /^(iPhone|iPad).*/,
            os_version: /^18\.[2-3](\.\d+)?$/,
            rule: "include",
            pref: "some_other_preference"
          }
        ],
        min_os_version: 17.0
      })
    end

    context 'when device is iPhone and version matches some_preference' do
      it 'returns the correct preference' do
        prefs = described_class.get_prefs_for_version(device_version, device_name, SETTINGS_PREFS[:random])
        expect(prefs).to eq('some_preference')
      end
    end

    context 'when device version matches some_other_preference' do
      let(:device_version) { '18.3' }
      it 'returns the correct preference' do
        prefs = described_class.get_prefs_for_version(device_version, device_name, SETTINGS_PREFS[:random])
        expect(prefs).to eq('some_other_preference')
      end
    end

    context 'when device version does not match any rule' do
      let(:device_version) { '19.0' }
      it 'returns nil' do
        prefs = described_class.get_prefs_for_version(device_version, device_name, SETTINGS_PREFS[:random])
        expect(prefs).to be_nil
      end
    end

    context 'when device is iPad and version matches some_preference' do
      let(:device_name) { 'iPad7,4' }
      it 'returns the correct preference' do
        prefs = described_class.get_prefs_for_version(device_version, device_name, SETTINGS_PREFS[:random])
        expect(prefs).to eq('some_preference')
      end
    end

    context 'when device is unsupported' do
      let(:device_name) { 'AndroidPhone' }
      it 'returns nil' do
        prefs = described_class.get_prefs_for_version(device_version, device_name, SETTINGS_PREFS[:random])
        expect(prefs).to be_nil
      end
    end
  end
  describe '#set_env_vars_for_navigation' do
    let(:device_id) { '12345' }
    let(:device_version) { '17.1' }
    let(:device_name) { 'iPhone12,1' }
    let(:pref_for_automation) { :random }

    before(:each) do
      allow(SETTINGS_PREFS).to receive(:[]).and_return({
        random: { some_key: 'some_value' },
        rules: [
          {
            device_name: /^(iPhone|iPad).*/,
            os_version: /^(17\.[0-9]+(\.[0-9]+)?|18\.[0-1](\.[0-9]+)?)$/,
            rule: 'include',
            pref: 'some_preference'
          }
        ],
        min_os_version: 17.0
      })

      allow(self).to receive(:get_prefs_for_version).and_return('some_preference')
    end

    context 'when preferences are available' do
      it 'returns optimised_flow in env_vars' do
        allow(DeviceCtl::Device).to receive(:launch_app_with_prefs).and_return("Launched application")
        result = subject.set_env_vars_for_navigation(device_id, device_version, device_name, pref_for_automation)
        expect(result).to eq({ flow: 'optimised_flow' })
      end
    end

    context 'when app fails to launch' do
      it 'raises an error if the app launch fails' do
        allow(DeviceCtl::Device).to receive(:launch_app_with_prefs).and_return("Some other response")
        expect do
          subject.set_env_vars_for_navigation(device_id, device_version, device_name, pref_for_automation)
        end.to raise_error("Failed to launch Settings app")
      end
    end
  end

  describe '#enable_location_services' do
    let(:env_vars) { {} }
    let(:device_id) { 'device_1' }
    let(:device_version) { 16.0 }  # Now using float for the version
    let(:device_name) { "iPhone12,1" }
    let(:mock_device_state) { instance_double('DeviceState') }
    let(:mock_chrome) { instance_double('Chrome') }
    before(:each) do
      # Mocks for device state and IdeviceUtils
      allow(subject.device_state).to receive(:mdm_full_cleanup_file_present?).and_return(false)
      allow(subject.device_state).to receive(:first_cleanup_file_present?).and_return(false)
      allow(subject.device_state).to receive(:chrome_cleanup_required_file_present?).and_return(false)
      allow(IdeviceUtils).to receive(:check_app_with_bundle_id_exists).and_return(true)
      allow(IdeviceUtils).to receive(:ideviceinfo).and_return([device_version.to_s])  # Returning string because `ideviceinfo` returns a string list
      allow(mock_chrome).to receive(:update_app_version_using_ios_version)
      allow(mock_chrome).to receive(:setup)
      allow(BrowserStackAppHelper).to receive(:run_ui_test)
      allow(subject.device_state).to receive(:touch_location_services_enabled_file)
      allow(subject.device_state).to receive(:remove_device_location_off_file)
      allow(SETTINGS_PREFS).to receive(:[]).and_return({
        min_os_version: 14.0
      })
    end
    context 'when device version >= min_os_version' do
      context 'and get_prefs_for_version returns prefs' do
        before(:each) do
          allow(described_class).to receive(:get_prefs_for_version).and_return('some_preferences')
        end
        it 'should add "optimised_flow" to the env_vars' do
          expect(DeviceCtl::Device).to receive(:launch_app_with_prefs).and_return("Launched application")
          subject.enable_location_services
        end
      end
      context 'and get_prefs_for_version returns nil' do
        before(:each) do
          allow(subject).to receive(:get_prefs_for_version).and_return(nil)
        end
        it 'should not add "optimised_flow" to the env_vars' do
          subject.enable_location_services
          # Check that "flow" is not added to env_vars
          expect(env_vars).not_to have_key('flow')
        end
      end
    end
    context 'when device version < min_os_version' do
      before(:each) do
        allow(SETTINGS_PREFS).to receive(:[]).and_return({ min_os_version: 16.0 })
      end
      it 'should not add "optimised_flow" to the env_vars' do
        subject.enable_location_services
        expect(env_vars).not_to have_key('flow')
      end
    end
  end
end
