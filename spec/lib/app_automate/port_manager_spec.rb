require_relative '../../spec_helper'
require_relative '../../../lib/app_automate/port_manager'
require_relative '../../../lib/utils/osutils'

describe BrowserStack::PortManager do
  let(:device_id) { "123456" }
  let(:driver_port) { "10001" }
  let(:app_port) { "12345" }

  it 'calls Iproxy to forward port' do
    expect(Iproxy).to receive(:start).with(device_id, driver_port, app_port)
    expect(Iproxy).to receive(:running?).and_return(true)
    BrowserStack::PortManager.forward_port(device_id, driver_port, app_port)
  end

  it "raises FireCMDException after 3 retries " do
    expect(Iproxy).to receive(:start).exactly(3).times
    expect(Iproxy).to receive(:running?).exactly(3).times.and_return(false)
    expect do
      BrowserStack::PortManager.forward_port(device_id, driver_port, app_port)
    end.to raise_error
  end

  it "kills iproxy if iproxy is running" do
    pid = 1234
    expect(BrowserStack::OSUtils).to receive(:grep_process_pid).and_return(pid)
    expect(BrowserStack::OSUtils).to receive(:kill_pid).with(pid)
    BrowserStack::PortManager.stop_forwarding_port(device_id, driver_port)
  end
end
