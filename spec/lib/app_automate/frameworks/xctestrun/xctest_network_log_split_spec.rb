require_relative "../../../../spec_helper"
require_relative "../../../../../lib/utils/utils"
require_relative "../../../../../server/spawn_xctest"

describe BrowserStack::XCTestNetworkLogSplit do
  let(:xctestrun_failed_instru_file) { "./spec/fixtures/xctestrun_instrumentation_failure.txt" }
  let(:nw_logs_flow_file) { "/path/to/flow_file" }
  let(:nw_logs_har_file) { "/path/to/har_file" }
  let(:device_log_file) { "./spec/fixtures/xctestrun_devicelog.txt" }
  let(:session_id) { "abcd" }
  let(:device) { "device-name" }
  let(:mitmproxy) { double("mitmproxy_instance") }
  let(:har_file) { double("harfile") }
  let(:retry_config) { {} }
  let(:time_components) do
    {
      xcodebuild: { start: Time.now - 100, end: Time.now },
      video: { start_duration: 0, end_duration: 90, device_start_time: Time.now - 95 }
    }
  end

  let(:test_details) do
    {
      "dummy_test_1" => {
        "name" => "dummy_test_1",
        "duration" => 1.555,
        "instru_log_boundary" => { "end" => 6762, "start" => 0 },
        "device_log_boundary" => { "end" => 0, "start" => 123 },
        "start_time" => "2023-08-19 13:13:10 +0000",
        "video": "0,25",
        "status" => "failed"
      },
      "dummy_test_2" => {
        "name" => "dummy_test_2",
        "duration" => 1.000,
        "instru_log_boundary" => { "end" => 6762, "start" => 0 },
        "device_log_boundary" => { "end" => 124, "start" => 238 },
        "start_time" => "2023-08-19 13:13:35 +0000",
        "video": "25,80",
        "status" => "failed"
      },
      "dummy_test_3" => {
        "name" => "dummy_test_3",
        "duration" => 1.000,
        "instru_log_boundary" => { "end" => 6762, "start" => 0 },
        "device_log_boundary" => { "end" => 239, "start" => 450 },
        "start_time" => "2023-08-19 13:14:30 +0000",
        "video": "80,100",
        "status" => "failed"
      }
    }
  end

  let(:instru_log_double) { instance_double(XCTestInstruLogParse) }
  let(:device_log_double) { instance_double(XCTestDeviceLogSplit) }
  let(:video_log_double) { instance_double(XCTestVideoLogSplit) }

  before do
    allow(XCTestInstruLogParse).to receive(:new).and_return(instru_log_double)
    allow(instru_log_double).to receive(:process)
    allow(XCTestDeviceLogSplit).to receive(:new).and_return(device_log_double)
    allow(device_log_double).to receive(:process)
    allow(XCTestVideoLogSplit).to receive(:new).and_return(video_log_double)
    allow(video_log_double).to receive(:process)

    allow(mitmproxy).to receive(:dumpfile).and_return(nw_logs_flow_file)
    allow(mitmproxy).to receive(:harfile).and_return(nw_logs_har_file)
  end

  context "#initialize" do
    it "should raise empty file error when network log file is missing" do
      allow(File).to receive(:exist?).and_return(false)
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_failed_instru_file, device, mitmproxy, true, false, false, session_id, time_components, 7200, retry_config)
      xctest_log_split_manager.test_details = { "key" => "value" }
      xctest_log_split_manager.test_details_valid = true
      xctest_log_split_manager.process_logs
      puts xctest_log_split_manager.log_split_failures
      expect(xctest_log_split_manager.log_split_failures).to have_key(XCTEST_NETWORK_LOG_SPLIT_ERROR)
    end
  end

  context "#process_log" do
    it "should not process network log when network log is not enabled for session" do
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_failed_instru_file, device, mitmproxy, false, true, true, session_id, time_components, 7200, retry_config)
      xctest_log_split_manager.test_details = test_details
      xctest_log_split_manager.test_details_valid = true

      expect(XCTestDeviceLogSplit).to receive(:new)
      expect(XCTestVideoLogSplit).to receive(:new)
      expect(XCTestNetworkLogSplit).to_not receive(:new)

      xctest_log_split_manager.process_logs
    end

    it "should split video log when test details have no issues" do
      mod_test_details = test_details
      mod_test_details["dummy_test_1"]["network_log_boundary"] = { "start" => 0, "end" => 7792 }
      mod_test_details["dummy_test_2"]["network_log_boundary"] = { "start" => 7793, "end" => 12920 }
      mod_test_details["dummy_test_3"]["network_log_boundary"] = { "start" => 12921, "end" => 13541 }

      # Validate log File Subbing
      expect(File).to receive(:exist?).with(nw_logs_flow_file).ordered.and_return(true)
      expect(File).to receive(:zero?).with(nw_logs_flow_file).and_return(false)
      # XCTestNetworkLogSplit#process_log Stubbing
      expect(MitmProxy).to receive(:stop_proxy).and_return(true)
      expect(Utils).to receive(:write_to_file).and_return(true)
      expect(mitmproxy).to receive(:convert_flow_to_har).with(nw_logs_flow_file, %r{/tmp/test_details_}).and_return(true)
      expect(File).to receive(:read).and_return(mod_test_details.to_json)
      expect_any_instance_of(XCTestNetworkLogSplit).to_not receive(:fallback_nw_logs)
      # XCTestLogSplitManager#process_xctest_network_log Stubbing
      expect(FileUtils).to receive(:rm_f).with(nw_logs_flow_file).ordered.and_return(true)
      expect(File).to receive(:exist?).with(%r{/tmp/test_details_}).and_return(true)
      expect(FileUtils).to receive(:rm_f).with(%r{/tmp/test_details_}).ordered.and_return(true)

      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_failed_instru_file, device, mitmproxy, true, true, true, session_id, time_components, 7200, retry_config)
      xctest_log_split_manager.test_details = test_details
      xctest_log_split_manager.test_details_valid = true
      xctest_log_split_manager.process_logs
    end

    it "should fallback to consolidated nwlogs when no tests contain start_time" do
      test_details.each do |_test_name, test_obj|
        test_obj["start_time"] = nil
      end

      har_size = 7792
      mod_test_details = test_details
      mod_test_details["dummy_test_1"]["network_log_boundary"] = { "start" => 0, "end" => har_size }
      mod_test_details["dummy_test_2"]["network_log_boundary"] = { "start" => 0, "end" => har_size }
      mod_test_details["dummy_test_3"]["network_log_boundary"] = { "start" => 0, "end" => har_size }

      # Validate log File Subbing
      expect(File).to receive(:exist?).with(nw_logs_flow_file).ordered.and_return(true)
      expect(File).to receive(:zero?).with(nw_logs_flow_file).and_return(false)
      # XCTestNetworkLogSplit#process_log Stubbing
      expect(MitmProxy).to receive(:stop_proxy).and_return(true)
      expect(BrowserStack::Zombie).to receive(:push_logs).and_return(true)
      expect(File).to receive(:exist?).with(nw_logs_har_file).and_return(true)
      expect(File).to receive(:truncate).with(nw_logs_har_file, 0).and_return(true)
      expect(mitmproxy).to receive(:convert_flow_to_har).and_return(true)
      # XCTestLogSplitManager#process_xctest_network_log Stubbing
      expect(FileUtils).to receive(:rm_f).with(nw_logs_flow_file).ordered.and_return(true)
      expect(File).to receive(:exist?).with(%r{/tmp/test_details_}).and_return(true)
      expect(FileUtils).to receive(:rm_f).with(%r{/tmp/test_details_}).ordered.and_return(true)

      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_failed_instru_file, device, mitmproxy, true, true, true, session_id, time_components, 7200, retry_config)
      xctest_log_split_manager.test_details = test_details
      xctest_log_split_manager.test_details_valid = true
      xctest_log_split_manager.process_logs
    end

    it "should fallback to consolidated nwlogs when dumpfile is empty" do
      test_details.each do |_test_name, test_obj|
        test_obj["start_time"] = nil
      end

      har_size = 7792
      mod_test_details = test_details
      mod_test_details["dummy_test_1"]["network_log_boundary"] = { "start" => 0, "end" => har_size }
      mod_test_details["dummy_test_2"]["network_log_boundary"] = { "start" => 0, "end" => har_size }
      mod_test_details["dummy_test_3"]["network_log_boundary"] = { "start" => 0, "end" => har_size }

      # Validate log File Subbing
      expect(File).to receive(:exist?).with(nw_logs_flow_file).ordered.and_return(true)
      expect(File).to receive(:zero?).with(nw_logs_flow_file).and_return(true)
      # XCTestNetworkLogSplit#process_log Stubbing
      expect(MitmProxy).to receive(:stop_proxy).and_return(true)
      expect(BrowserStack::Zombie).to receive(:push_logs).and_return(true)
      expect(File).to receive(:exist?).with(nw_logs_har_file).and_return(true)
      expect(File).to receive(:truncate).with(nw_logs_har_file, 0).and_return(true)
      expect(mitmproxy).to receive(:convert_flow_to_har).and_return(true)
      # XCTestLogSplitManager#process_xctest_network_log Stubbing
      expect(FileUtils).to receive(:rm_f).with(nw_logs_flow_file).ordered.and_return(true)
      expect(File).to receive(:exist?).with(%r{/tmp/test_details_}).and_return(true)
      expect(FileUtils).to receive(:rm_f).with(%r{/tmp/test_details_}).ordered.and_return(true)

      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_failed_instru_file, device, mitmproxy, true, true, true, session_id, time_components, 7200, retry_config)
      xctest_log_split_manager.test_details = test_details
      xctest_log_split_manager.test_details_valid = true
      xctest_log_split_manager.process_logs
    end

    it "should not fallback to consolidated nwlogs twice when dumpfile is empty" do
      test_details.each do |_test_name, test_obj|
        test_obj["start_time"] = nil
      end

      har_size = 7792
      mod_test_details = test_details
      mod_test_details["dummy_test_1"]["network_log_boundary"] = { "start" => 0, "end" => har_size }
      mod_test_details["dummy_test_2"]["network_log_boundary"] = { "start" => 0, "end" => har_size }
      mod_test_details["dummy_test_3"]["network_log_boundary"] = { "start" => 0, "end" => har_size }

      # Validate log File Subbing
      expect(File).to receive(:exist?).with(nw_logs_flow_file).ordered.and_return(true)
      expect(File).to receive(:zero?).with(nw_logs_flow_file).and_return(true)
      # XCTestNetworkLogSplit#process_log Stubbing
      expect(MitmProxy).to receive(:stop_proxy).and_return(true)
      expect(BrowserStack::Zombie).to receive(:push_logs).and_return(true)
      expect(File).to receive(:exist?).with(nw_logs_har_file).and_return(true)
      expect(File).to receive(:truncate).with(nw_logs_har_file, 0).and_return(true)
      allow_any_instance_of(BrowserStack::XCTestNetworkLogSplit).to receive(:network_log_boundary_valid?).and_return(false)
      expect(mitmproxy).to receive(:convert_flow_to_har).exactly(:once).and_return(true)
      # XCTestLogSplitManager#process_xctest_network_log Stubbing
      expect(FileUtils).to receive(:rm_f).with(nw_logs_flow_file).ordered.and_return(true)
      expect(File).to receive(:exist?).with(%r{/tmp/test_details_}).and_return(true)
      expect(FileUtils).to receive(:rm_f).with(%r{/tmp/test_details_}).ordered.and_return(true)

      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_failed_instru_file, device, mitmproxy, true, true, true, session_id, time_components, 7200, retry_config)
      xctest_log_split_manager.test_details = test_details
      xctest_log_split_manager.test_details_valid = true
      xctest_log_split_manager.process_logs
    end

    it "should fallback to consolidated nwlogs when atleast 1 invalid bounds are present" do
      mod_test_details = test_details
      mod_test_details["dummy_test_1"]["network_log_boundary"] = { "start" => 0, "end" => 240 }
      mod_test_details["dummy_test_2"]["start_time"] = ""
      mod_test_details["dummy_test_3"]["network_log_boundary"] = { "start" => 300, "end" => 400 }

      # Validate log File Subbing
      expect(File).to receive(:exist?).with(nw_logs_flow_file).ordered.and_return(true)
      expect(File).to receive(:zero?).with(nw_logs_flow_file).and_return(false)
      # XCTestNetworkLogSplit#process_log Stubbing
      expect(MitmProxy).to receive(:stop_proxy).and_return(true)
      expect(Utils).to receive(:write_to_file).and_return(true)
      expect(mitmproxy).to receive(:convert_flow_to_har).with(nw_logs_flow_file, %r{/tmp/test_details_}).and_return(true)
      expect(File).to receive(:read).and_return(mod_test_details.to_json)
      # XCTestNetworkLogSplit#fallback_nw_logs Stubbing
      expect(BrowserStack::Zombie).to receive(:push_logs).and_return(true)
      expect(File).to receive(:exist?).with(nw_logs_har_file).and_return(true)
      expect(File).to receive(:truncate).with(nw_logs_har_file, 0).and_return(true)
      expect(mitmproxy).to receive(:convert_flow_to_har).and_return(true)
      # XCTestLogSplitManager#process_xctest_network_log Stubbing
      expect(FileUtils).to receive(:rm_f).with(nw_logs_flow_file).ordered.and_return(true)
      expect(File).to receive(:exist?).with(%r{/tmp/test_details_}).and_return(true)
      expect(FileUtils).to receive(:rm_f).with(%r{/tmp/test_details_}).ordered.and_return(true)

      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_failed_instru_file, device, mitmproxy, true, true, true, session_id, time_components, 7200, retry_config)
      xctest_log_split_manager.test_details = test_details
      xctest_log_split_manager.test_details_valid = true
      xctest_log_split_manager.process_logs
    end

    it "should fallback to consolidated nwlogs when temp test_details file is not present" do
      mod_test_details = test_details
      mod_test_details["dummy_test_1"]["network_log_boundary"] = { "start" => 0, "end" => 240 }
      mod_test_details["dummy_test_2"]["start_time"] = ""
      mod_test_details["dummy_test_3"]["network_log_boundary"] = { "start" => 300, "end" => 400 }

      # Validate log File Subbing
      expect(File).to receive(:exist?).with(nw_logs_flow_file).ordered.and_return(true)
      expect(File).to receive(:zero?).with(nw_logs_flow_file).and_return(false)
      # XCTestNetworkLogSplit#process_log Stubbing
      expect(MitmProxy).to receive(:stop_proxy).and_return(true)
      expect(Utils).to receive(:write_to_file).and_return(true)
      expect(mitmproxy).to receive(:convert_flow_to_har).with(nw_logs_flow_file, %r{/tmp/test_details_}).and_return(true)
      expect(File).to receive(:read).and_raise(StandardError, "File not found")
      # XCTestNetworkLogSplit#fallback_nw_logs Stubbing
      expect(BrowserStack::Zombie).to receive(:push_logs).and_return(true)
      expect(File).to receive(:exist?).with(nw_logs_har_file).and_return(true)
      expect(File).to receive(:truncate).with(nw_logs_har_file, 0).and_return(true)
      expect(mitmproxy).to receive(:convert_flow_to_har).and_return(true)
      # XCTestLogSplitManager#process_xctest_network_log Stubbing
      expect(FileUtils).to receive(:rm_f).with(nw_logs_flow_file).ordered.and_return(true)
      expect(File).to receive(:exist?).with(%r{/tmp/test_details_}).and_return(true)
      expect(FileUtils).to receive(:rm_f).with(%r{/tmp/test_details_}).ordered.and_return(true)

      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_failed_instru_file, device, mitmproxy, true, true, true, session_id, time_components, 7200, retry_config)
      xctest_log_split_manager.test_details = test_details
      xctest_log_split_manager.test_details_valid = true
      xctest_log_split_manager.process_logs
    end
  end
end
