require_relative '../../../../spec_helper'
require_relative '../../../../../lib/utils/utils'
require_relative '../../../../../lib/app_automate/frameworks/xctestrun/xctestrun_session'
require_relative '../../../../../server/spawn_xctest'
require 'zip'

describe BrowserStack::XCTestrunSession do
  let(:xctestrun_session) { BrowserStack::XCTestrunSession.new({}, {}) }
  let(:xctestrun_path) { "xctestrun_file_path" }
  let(:xctestrun_failed_instru_file) { "./spec/fixtures/xctestrun_instrumentation_failure.txt" }
  let(:xctestrun_success_instru_file) { "./spec/fixtures/xctestrun_instrumentation_success.txt" }
  let(:xctestrun_failed_instru_file_NA_count) { "./spec/fixtures/xctestrun_instrumentation_failure_NA_count.txt" }
  let(:xctestrun_success_multiple_config_file) { "./spec/fixtures/xctestrun_instrumentation_success_multi_config.txt" }

  context "#run_xctestsuite" do
    let(:summary_manager) { double("summary_manager") }

    it "should invoke xctestrun flow execution steps in order" do
      xctestrun_session.instance_variable_set("@summary_manager", summary_manager)
      allow(xctestrun_session).to receive(:get_video_duration).and_return(0, 10)
      allow(IdeviceUtils).to receive(:device_time).and_return(Time.at(0).to_s)
      expect(xctestrun_session).to receive(:initialize_logs_stability_metrics).once.ordered.and_return(true)
      expect(summary_manager).to receive(:send_setup_build_pusher_event).once.ordered.and_return(true)
      expect(xctestrun_session).to receive(:video_enabled?).once.times.ordered.and_return(true)
      expect(VideoRecManager).to receive_message_chain(:new, :start_rec).and_return(true)
      expect(xctestrun_session).to receive(:invoke_xcodebuild_command).once.ordered.and_return(0)

      xctestrun_session.send(:run_xctestsuite, xctestrun_path)

      time_components = xctestrun_session.instance_variable_get("@time_components")
      expect(time_components[:video]).to eql({ start_duration: 0, end_duration: 10, device_start_time: Time.at(0) })
    end
  end

  context "#start" do
    let(:summary_manager) { double("summary_manager") }

    it "should trigger xctestrun flow" do
      expect_any_instance_of(BrowserStack::Session).to receive(:start).and_return(true)
      expect(BrowserStack::XCTestrunSummary).to receive(:new).once.ordered.and_return(summary_manager)
      expect(xctestrun_session).to receive(:save_session_params_v2).once.ordered.and_return(true)
      expect(xctestrun_session).to receive(:setup_params).once.ordered.and_return(true)
      expect(summary_manager).to receive(:setup_summary_v2).and_return("summary_file_path")
      expect(BrowserStack::XCTestrunManager).to receive(:generate_xctestrun_for_testing).and_return(xctestrun_path)
      expect(xctestrun_session).to receive(:run_xctestsuite).once.ordered.and_return(true)
      expect(xctestrun_session).to receive(:post_test_execution_actions).once.ordered
      expect(xctestrun_session).to receive(:stop).once.ordered.and_return(true)

      xctestrun_session.start
    end

    it "should trigger update_xctestrun_with_enviromentvariable if enableCameraImageInjection is true" do
      xctestrun_session.instance_variable_set("@params", { "enableCameraImageInjection" => true })
      expect_any_instance_of(BrowserStack::Session).to receive(:start).and_return(true)
      expect(BrowserStack::XCTestrunSummary).to receive(:new).once.ordered.and_return(summary_manager)
      expect(xctestrun_session).to receive(:save_session_params_v2).once.ordered.and_return(true)
      expect(xctestrun_session).to receive(:setup_params).once.ordered.and_return(true)
      expect(summary_manager).to receive(:setup_summary_v2).and_return("summary_file_path")
      expect(BrowserStack::XCTestrunManager).to receive(:generate_xctestrun_for_testing).and_return(xctestrun_path)
      expect(BrowserStack::XCTestrunManager).to receive(:update_xctestrun_with_enviromentvariable)
      expect(xctestrun_session).to receive(:run_xctestsuite).once.ordered.and_return(true)
      expect(xctestrun_session).to receive(:post_test_execution_actions).once.ordered
      expect(xctestrun_session).to receive(:stop).once.ordered.and_return(true)

      xctestrun_session.start
    end

    it "should rescue incase of any exceptions" do
      expect_any_instance_of(BrowserStack::Session).to receive(:start).and_return(true)
      expect(BrowserStack::XCTestrunSummary).to receive(:new).once.ordered.and_return(summary_manager)
      expect(xctestrun_session).to receive(:save_session_params_v2).and_raise(StandardError)
      expect(BrowserStack::Zombie).to receive(:push_logs).once.ordered.and_return(true)
      expect(xctestrun_session).to receive(:stop).once.ordered.and_return(true)

      xctestrun_session.start
    end

    it "should generate xctestrun v1 if singleRunnerInvocation is passed" do
      xctestrun_session.instance_variable_set("@params", { "singleRunnerInvocation" => true })
      xctestrun_session.instance_variable_set(:@testsuite_files, ["/tmp/93203920_xctest_product_module_name", "/tmp/93203920_xctest_other_product_module_name"])
      expect_any_instance_of(BrowserStack::Session).to receive(:start).and_return(true)
      expect(BrowserStack::XCTestrunSummary).to receive(:new).once.ordered.and_return(summary_manager)
      expect(xctestrun_session).to receive(:save_session_params_v2).once.ordered.and_return(true)
      expect(xctestrun_session).to receive(:setup_params).once.ordered.and_return(true)
      expect(summary_manager).to receive(:setup_summary_v2).and_return("summary_file_path")
      expect(xctestrun_session).to receive(:filter_testlist).and_return(@testsuite_files)
      expect(xctestrun_session).to receive(:generate_xctestrun_xml).and_return(xctestrun_path)
      expect(xctestrun_session).to receive(:run_xctestsuite).once.ordered.and_return(true)
      expect(xctestrun_session).to receive(:stop).once.ordered.and_return(true)
      xctestrun_session.start
    end
  end

  context "#post_test_execution_actions" do
    let(:summary_manager) { double("summary_manager") }

    it "should invoke post test execution actions in order" do
      xctestrun_session.instance_variable_set("@summary_manager", summary_manager)
      expect(xctestrun_session).to receive(:process_instru_logs).once.ordered.and_return(["", 0])
      expect(summary_manager).to receive(:update_test_summary_v2).once.ordered.and_return({})
      expect(xctestrun_session).to receive(:upload_logfiles).once.ordered.and_return(true)
      expect(summary_manager).to receive(:update_callback_file).once.ordered.and_return(true)
      expect(summary_manager).to receive(:update_session_stats).once.ordered.and_return(true)

      status = xctestrun_session.send(:post_test_execution_actions, 0)
    end

    it "should invoke log splitting" do
      xctestrun_session.instance_variable_set("@summary_manager", summary_manager)
      allow(xctestrun_session).to receive(:should_split_log?).and_return(true)
      expect(xctestrun_session).to receive(:fetch_crash_logs)
      expect(xctestrun_session).to receive(:split_log).and_return({})
      expect(xctestrun_session).to receive(:upload_logfiles).once.ordered.and_return(true)
      expect(summary_manager).to receive(:update_callback_file).once.ordered.and_return(true)
      expect(summary_manager).to receive(:update_session_stats).once.ordered.and_return(true)

      status = xctestrun_session.send(:post_test_execution_actions, 0)
    end

    it "should handle exception raised in log splitting" do
      xctestrun_session.instance_variable_set("@summary_manager", summary_manager)
      allow(xctestrun_session).to receive(:should_split_log?).and_return(true)
      allow(xctestrun_session).to receive(:devicelogs_enabled?).and_return(true)

      # Setup the exception case
      expect(xctestrun_session).to receive(:parse_test_execution_logs)
      expect(xctestrun_session).to receive(:fetch_crash_logs)
      expect(xctestrun_session).to receive(:split_log).and_raise(StandardError, "Log splitting error")
      expect(BrowserStack.logger).to receive(:error)

      # In the exception case, fetch_and_upload_crash_logs SHOULD be called
      expect(xctestrun_session).to receive(:fetch_and_upload_crash_logs).with("").and_return(0)
      expect(xctestrun_session).to receive(:xctest_summary_without_log_split)

      # Rest of the expectations
      expect(xctestrun_session).to receive(:upload_logfiles).once.ordered.and_return(true)
      expect(summary_manager).to receive(:update_callback_file).once.ordered.and_return(true)
      expect(summary_manager).to receive(:update_session_stats).once.ordered.and_return(true)

      # Call the method under test
      xctestrun_session.send(:post_test_execution_actions, 0)
    end

    it "should send log split failures detection to error reasons" do
      xctestrun_session.instance_variable_set("@summary_manager", summary_manager)
      allow(xctestrun_session).to receive(:should_split_log?).and_return(true)
      allow(xctestrun_session).to receive(:devicelogs_enabled?).and_return(true)
      allow(xctestrun_session).to receive(:video_enabled?).and_return(true)
      xctest_log_split_manager_double = double('XCTestLogSplitManager')
      expect(BrowserStack::XCTestLogSplitManager).to receive(:new).and_return(xctest_log_split_manager_double)
      expect(xctest_log_split_manager_double).to receive(:process_logs)
      expect(summary_manager).to receive(:generate_summary_log_split).and_return(["", { classes: { "hi" => "by" } }])
      expect(xctest_log_split_manager_double).to receive(:test_details).exactly(2).times
      expect(xctest_log_split_manager_double).to receive(:split_time).and_return({})
      expect(xctest_log_split_manager_double).to receive(:log_split_errors).exactly(2).times
      expect(xctest_log_split_manager_double).to receive(:log_split_failures).and_return({ "hi" => "bye" })
      expect(BrowserStack::Zombie).to receive(:push_logs).with("xctest-logsplit-info", "", { "data" => "{\"split_issues\":null}", "session_id" => nil })
      expect(Utils).to receive(:send_general_feature_usage_data_to_eds).exactly(5).times
      expect(xctestrun_session).to receive(:send_error_reason_in_file)
      expect(xctestrun_session).to receive(:fetch_crash_logs)
      expect(xctestrun_session).to receive(:upload_logfiles).once.ordered.and_return(true)
      expect(summary_manager).to receive(:update_callback_file).once.ordered.and_return(true)
      expect(summary_manager).to receive(:update_session_stats).once.ordered.and_return(true)
      expect(xctestrun_session).to receive(:parse_instru_logs)

      status = xctestrun_session.send(:post_test_execution_actions, 0)
    end
  end

  context "#process_instru_logs" do
    it "mark as error" do
      status, failures = xctestrun_session.send(:process_instru_logs, 70)

      expect(status).to eql("error")
      expect(failures).to eql(0)
    end

    context "mark as timedout" do
      it "based on exit code" do
        expect(xctestrun_session).to receive(:is_test_timedout?).and_return(true)
        status, failures = xctestrun_session.send(:process_instru_logs, 137)

        expect(status).to eql("timedout")
        expect(failures).to eql(0)
      end

      it "based on time durations" do
        expect(xctestrun_session).to receive(:is_test_timedout?).and_return(false)
        xctestrun_session.instance_variable_set("@total_session_time", 7200)
        xctestrun_session.instance_variable_set("@session_start_time", Time.now - 7500)
        status, failures = xctestrun_session.send(:process_instru_logs, 0)

        expect(status).to eql("timedout")
        expect(failures).to eql(0)
      end
    end

    context "Mark as passed/Failed" do
      before(:each) do
        xctestrun_session.instance_variable_set(:@test_names, Set.new)
        xctestrun_session.instance_variable_set(:@test_states, {})
        expect(xctestrun_session).to receive(:is_test_timedout?).and_return(false)
        expect(xctestrun_session).to receive(:is_test_errored?).and_return(false)
        expect(xctestrun_session).to receive(:test_expired?).and_return(false)
        allow(BrowserStack::Zombie).to receive(:push_logs).and_return(true)
      end

      it "marks as passed if no failing tests observed" do
        xctestrun_session.instance_variable_set("@instrumentation_file", xctestrun_success_instru_file)

        status, failures = xctestrun_session.send(:process_instru_logs, 0)

        expect(status).to eql("passed")
        expect(failures).to eql(0)
      end

      it "marks as failed when failing tests observed" do
        xctestrun_session.instance_variable_set("@instrumentation_file", xctestrun_failed_instru_file)

        status, failures = xctestrun_session.send(:process_instru_logs, 0)

        expect(status).to eql("failed")
        expect(failures).to eql(4)
      end

      it "marks as failed with NA failures when failing tests observed but not able to detect count of failed test" do
        xctestrun_session.instance_variable_set("@instrumentation_file", xctestrun_failed_instru_file_NA_count)

        status, failures = xctestrun_session.send(:process_instru_logs, 0)

        expect(status).to eql("failed")
        expect(failures).to eql("NA")
      end

      it "default mark as failed" do
        expect(File).to receive(:foreach).and_return([])

        status, failures = xctestrun_session.send(:process_instru_logs, 0)

        expect(status).to eql("failed")
        expect(failures).to eql("NA")
      end
    end
  end

  context "#invoke_xcodebuild_command" do
    before(:each) do
      expect(xctestrun_session).to receive(:start_timeout_manager).at_least(:once).and_return(true)
      expect(xctestrun_session).to receive(:stop_timeout_manager).at_least(:once).and_return(true)
      expect(xctestrun_session).to receive(:get_test_expire_time).at_least(:once).and_return(7100)
      allow_any_instance_of(Object).to receive(:sleep).and_return(0)
    end

    it "Executes xcodebuild command" do
      xctestrun_session.instance_variable_set("@params", {})
      expect(BrowserStack::OSUtils).to receive(:execute).once.ordered.and_return(["", 0])

      status = xctestrun_session.send(:invoke_xcodebuild_command, xctestrun_path)

      expect(status).to eql(0)
    end

    it "should execute xcodebuild command with enableResultBundle true" do
      xctestrun_session.instance_variable_set(:@params, { 'enableResultBundle' => "true" })
      expect(BrowserStack::OSUtils).to receive(:execute).and_return(["", 0])
      expect(xctestrun_session).to receive(:should_retry_test?).and_return(false)

      status = xctestrun_session.send(:invoke_xcodebuild_command, xctestrun_path)

      expect(status).to eq(0)
    end

    it "should execute xcodebuild command by removing xcresult bundle incase of bs retries" do
      xctestrun_session.instance_variable_set(:@test_meta_info, { "retries" => 0, "retry_time" => 0 })
      xctestrun_session.instance_variable_set(:@params, { 'enableResultBundle' => "true" })
      expect(BrowserStack::OSUtils).to receive(:execute).and_return(["", 0], ["", 0])
      expect(xctestrun_session).to receive(:should_retry_test?).and_return(true, false)
      expect(FileUtils).to receive(:rm_rf).twice.and_return(true)

      status = xctestrun_session.send(:invoke_xcodebuild_command, xctestrun_path)

      expect(status).to eq(0)
    end

    it "should retry xcodebuild command when pending tests > 0" do
      xctestrun_session.instance_variable_set(:@test_meta_info, { "retries" => 0, "retry_time" => 0 })
      expect(BrowserStack::OSUtils).to receive(:execute).exactly(3).times.and_return(["", 0], ["", 0], ["", 0])
      allow(xctestrun_session).to receive(:analyse_tests_execution).and_return(true)
      expect(xctestrun_session).to receive(:should_retry_test?).and_return(false, false, false)
      expect(xctestrun_session).to receive(:should_retry_sri_execution?).and_return(true, true, false)

      status = xctestrun_session.send(:invoke_xcodebuild_command, xctestrun_path)

      expect(status).to eq(0)
    end

    it "should retry xcodebuild command" do
      xctestrun_session.instance_variable_set(:@test_meta_info, { "retries" => 0, "retry_time" => 0 })
      expect(BrowserStack::OSUtils).to receive(:execute).exactly(2).times.and_return(["", 70], ["", 0])
      expect(xctestrun_session).to receive(:should_retry_test?).and_return(true, false)

      status = xctestrun_session.send(:invoke_xcodebuild_command, xctestrun_path)

      expect(status).to eq(0)
    end
  end

  context "#configuration_filters" do
    it "Returns configuration params if passed in params" do
      xctestrun_session.instance_variable_set("@params", {
        "test_params" => "{\"only-test-configuration\" : [\"German\"]}"
      })

      status = xctestrun_session.send(:configuration_filters)

      expect(status).to match(" -only-test-configuration")
    end

    it "Returns all configuration flags as passed in params" do
      xctestrun_session.instance_variable_set("@params", {
        "test_params" => "{\"skip-test-configuration\" : [\"German\"]}"
      })

      status = xctestrun_session.send(:configuration_filters)

      expect(status).to match(" -skip-test-configuration")
    end

    it "Returns no configuration flags if not passed in params" do
      xctestrun_session.instance_variable_set("@params", {})

      status = xctestrun_session.send(:configuration_filters)

      expect(status).to eql("")
    end
  end

  context "#get_log_splitting_error_reason" do
    it "should return appropriate error type if not a user error" do
      xctestrun_session.instance_variable_set("@test_meta_info", {})
      xctestrun_session.instance_variable_set("@any_test_case_found", true)
      error_reason = xctestrun_session.send(:get_log_splitting_error_reason, "InstruParseEmpty")

      expect(error_reason).to eql("instru-no-test-detected-xcode")
    end

    it "should return appripriate error type when user error: instruLogs contains app issues" do
      xctestrun_session.instance_variable_set("@test_meta_info", { "test_crash" => "74" })
      error_reason = xctestrun_session.send(:get_log_splitting_error_reason, "InstruParseEmpty")

      expect(error_reason).to eql("instru-no-test-detected-user")
    end

    it "should return appripriate error type when user error: instruLogs contains does not contain Test Case" do
      xctestrun_session.instance_variable_set("@test_meta_info", {})
      xctestrun_session.instance_variable_set("@any_test_case_found", false)
      error_reason = xctestrun_session.send(:get_log_splitting_error_reason, "InstruParseEmpty")

      expect(error_reason).to eql("instru-no-test-detected-user")
    end
  end

  context "#setup_test_list" do
    it "should set empty test identifiers if not sri flow" do
      xctestrun_session.instance_variable_set("@params", { "singleRunnerInvocation" => false })

      xctestrun_session.send(:setup_test_list)

      only_testing = xctestrun_session.instance_variable_get("@only_testing_list")
      skip_testing_list = xctestrun_session.instance_variable_get("@skip_testing_list")
      expect(only_testing.size).to eq(0)
      expect(skip_testing_list.size).to eq(0)
    end

    it "should set empty test identifiers if no test_params passed" do
      xctestrun_session.instance_variable_set("@params", { "singleRunnerInvocation" => true })

      xctestrun_session.send(:setup_test_list)

      only_testing = xctestrun_session.instance_variable_get("@only_testing_list")
      skip_testing_list = xctestrun_session.instance_variable_get("@skip_testing_list")
      expect(only_testing.size).to eq(0)
      expect(skip_testing_list.size).to eq(0)
    end

    it "should set empty test identifiers incase of any exception" do
      xctestrun_session.instance_variable_set("@params", { "singleRunnerInvocation" => true, "test_params" => {}.to_json })
      expect(JSON).to receive(:parse).and_raise(StandardError.new("Something went Wrong"))
      expect(BrowserStack::Zombie).to receive(:push_logs).and_return(true)

      xctestrun_session.send(:setup_test_list)

      only_testing = xctestrun_session.instance_variable_get("@only_testing_list")
      skip_testing_list = xctestrun_session.instance_variable_get("@skip_testing_list")
      expect(only_testing.size).to eq(0)
      expect(skip_testing_list.size).to eq(0)
    end

    it "should set empty test identifiers if no test identifier flag is sent by user" do
      xctestrun_session.instance_variable_set("@params", { "singleRunnerInvocation" => true, "test_params" => { "is_dynamic_xcuitest" => "false" }.to_json })

      xctestrun_session.send(:setup_test_list)

      only_testing = xctestrun_session.instance_variable_get("@only_testing_list")
      skip_testing_list = xctestrun_session.instance_variable_get("@skip_testing_list")
      expect(only_testing.size).to eq(0)
      expect(skip_testing_list.size).to eq(0)
    end

    it "should appropriately set test identifiers if user passes only-testing" do
      xctestrun_session.instance_variable_set("@params", { "singleRunnerInvocation" => true, "test_params" => { "is_dynamic_xcuitest" => "true" }.to_json })
      xctestrun_session.instance_variable_set("@testsuite_files", ["testsuite_files"])
      expect(xctestrun_session).to receive(:get_testlist).and_return(["test1", "test2"])
      xctestrun_session.send(:setup_test_list)

      only_testing = xctestrun_session.instance_variable_get("@only_testing_list")
      skip_testing_list = xctestrun_session.instance_variable_get("@skip_testing_list")
      expect(only_testing.size).to eq(2)
      expect(skip_testing_list.size).to eq(0)
    end
  end

  context "#eval_test_identifiers_string" do
    it "prepares only-testing test-indentifier string" do
      xctestrun_session.instance_variable_set("@only_testing_list", ["test1", "test2"])
      test_identifer_string = xctestrun_session.send(:eval_test_identifiers_string)
      expect(test_identifer_string).to eq(" -only-testing:\"test1\"  -only-testing:\"test2\" ")
    end

    it "prepares skip-testing test-indentifier string" do
      xctestrun_session.instance_variable_set("@skip_testing_list", ["test1", "test2"])
      test_identifer_string = xctestrun_session.send(:eval_test_identifiers_string)
      expect(test_identifer_string).to eq(" -skip-testing:\"test1\"  -skip-testing:\"test2\" ")
    end

    it "prepares only-testing and skip-testing test-indentifier string" do
      xctestrun_session.instance_variable_set("@skip_testing_list", ["test1"])
      xctestrun_session.instance_variable_set("@only_testing_list", ["test3"])
      test_identifer_string = xctestrun_session.send(:eval_test_identifiers_string)
      expect(test_identifer_string).to eq(" -only-testing:\"test3\"  -skip-testing:\"test1\" ")
    end
  end

  context "#should_retry_sri_execution?" do
    it "should return true when pending tests > 0" do
      expect(xctestrun_session).to receive(:xcuitest_sri_flow?).and_return(true)
      expect(xctestrun_session).to receive(:bstack_retry_on_sri_enabled?).and_return(true)
      expect(xctestrun_session).to receive(:analyse_tests_execution).and_return(true)

      ret = xctestrun_session.send(:should_retry_sri_execution?, 0)
      expect(ret).to be_falsey
    end

    it "should not return true when pending tests = false" do
      expect(xctestrun_session).to receive(:xcuitest_sri_flow?).and_return(false)
      expect(xctestrun_session).not_to receive(:bstack_retry_on_sri_enabled?)

      ret = xctestrun_session.send(:should_retry_sri_execution?, 0)
      expect(ret).to be_falsey
    end

    it "should not return true when max retry count is exceeded" do
      ret = xctestrun_session.send(:should_retry_sri_execution?, 5)
      expect(ret).to be_falsey
    end
  end

  context "#analyse_tests_execution" do
    let(:complete_test_list) do
      Set[
      "BStackMediaAppUITests/BStackMediaAppUITests/testGPSLocationOtherApp",
      "BStackMediaAppUITests/BStackMediaAppUITests/testMediaText",
      "BStackMediaAppUITests/BStackMediaAppUITests/testGeoLocationText",
      "BStackMediaAppUITests/BStackMediaAppUITests/testGPSLocationText"
    ]
    end

    before(:each) do
      xctestrun_session.instance_variable_set("@only_testing_list", Set.new)
      xctestrun_session.instance_variable_set("@skip_testing_list", Set.new)
      xctestrun_session.instance_variable_set("@test_meta_info", {})
    end

    it "should update skip-testing list when some tests weren't executed" do
      xctestrun_session.instance_variable_set("@instru_log_file_size", 0)
      xctestrun_session.instance_variable_set("@instrumentation_file", "./spec/fixtures/xctest_instrumentation_log/sri_partial_execution.log")
      xctestrun_session.instance_variable_set("@session_with_test_filters", false)

      xctestrun_session.send(:analyse_tests_execution, 0)

      expect(xctestrun_session.instance_variable_get("@pending_tests")).to eq(true)
      expect(xctestrun_session.instance_variable_get("@skip_testing_list")).to eq(Set["BStackMediaAppUITests/BStackMediaAppUITests/testGPSLocationOtherApp", "BStackMediaAppUITests/BStackMediaAppUITests/testMediaText"])
      expect(xctestrun_session.instance_variable_get("@only_testing_list")).to eq(Set.new)
    end

    it "should not set pending_tests when all tests weren executed" do
      xctestrun_session.instance_variable_set("@instru_log_file_size", 0)
      xctestrun_session.instance_variable_set("@instrumentation_file", "./spec/fixtures/xctest_instrumentation_log/sri_complete_execution.log")
      xctestrun_session.instance_variable_set("@session_with_test_filters", false)

      xctestrun_session.send(:analyse_tests_execution, 0)

      expect(xctestrun_session.instance_variable_get("@pending_tests")).to be_falsey
      expect(xctestrun_session.instance_variable_get("@skip_testing_list")).to eq(complete_test_list)
      expect(xctestrun_session.instance_variable_get("@only_testing_list")).to eq(Set.new)
    end

    it "should update only-testing lists when some tests weren't executed" do
      xctestrun_session.instance_variable_set("@instru_log_file_size", 0)
      xctestrun_session.instance_variable_set("@instrumentation_file", "./spec/fixtures/xctest_instrumentation_log/sri_partial_execution.log")
      xctestrun_session.instance_variable_set("@session_with_test_filters", true)
      xctestrun_session.instance_variable_set("@only_testing_list", complete_test_list)

      xctestrun_session.send(:analyse_tests_execution, 0)

      expect(xctestrun_session.instance_variable_get("@pending_tests")).to eq(true)
      expect(xctestrun_session.instance_variable_get("@skip_testing_list")).to eq(Set.new)
      expect(xctestrun_session.instance_variable_get("@only_testing_list")).to eq(Set["BStackMediaAppUITests/BStackMediaAppUITests/testGeoLocationText", "BStackMediaAppUITests/BStackMediaAppUITests/testGPSLocationText"])
    end

    it "should not set pending_tests variable when all tests were executed" do
      xctestrun_session.instance_variable_set("@instru_log_file_size", 0)
      xctestrun_session.instance_variable_set("@instrumentation_file", "./spec/fixtures/xctest_instrumentation_log/sri_complete_execution.log")
      xctestrun_session.instance_variable_set("@session_with_test_filters", true)
      xctestrun_session.instance_variable_set("@only_testing_list", complete_test_list)

      xctestrun_session.send(:analyse_tests_execution, 0)

      expect(xctestrun_session.instance_variable_get("@pending_tests")).to be_falsey
      expect(xctestrun_session.instance_variable_get("@skip_testing_list")).to eq(Set.new)
      expect(xctestrun_session.instance_variable_get("@only_testing_list")).to eq(Set.new)
    end

    it "should update only-testing and skip-testing lists when some tests were started but unfinished due to xcode issue" do
      xctestrun_session.instance_variable_set("@instru_log_file_size", File.size("./spec/fixtures/xctest_instrumentation_log/sri_complete_execution.log"))
      xctestrun_session.instance_variable_set("@instrumentation_file", "./spec/fixtures/xctest_instrumentation_log/sri_halt_execution.log")
      xctestrun_session.instance_variable_set("@session_with_test_filters", true)
      xctestrun_session.instance_variable_set("@only_testing_list", Set["BStackMediaAppUITests/BStackMediaAppUITests/testGeoLocationText", "BStackMediaAppUITests/BStackMediaAppUITests/testGPSLocationText"])

      xctestrun_session.send(:analyse_tests_execution, 1)

      expect(xctestrun_session.instance_variable_get("@pending_tests")).to eq(true)
      expect(xctestrun_session.instance_variable_get("@skip_testing_list")).to eq(Set.new)
      expect(xctestrun_session.instance_variable_get("@only_testing_list")).to eq(Set["BStackMediaAppUITests/BStackMediaAppUITests/testGPSLocationText"])
    end
  end

  context "#zip_and_upload_crash_logs" do
    let(:device) { "iPhone14,2" }
    let(:params) do
      {
        "automate_session_id" => "test_session_id",
        "devicelogs_aws_bucket" => "test-bucket",
        "devicelogs_aws_region" => "test-region",
        "devicelogs_aws_key" => "test-key",
        "devicelogs_aws_secret" => "test-secret"
      }
    end
    let(:server_config) { { "host" => "localhost", "port" => 8080 } }
    let(:device_config) { { "device" => device } }

    before do
      xctestrun_session.instance_variable_set(:@params, params)
      xctestrun_session.instance_variable_set(:@device, device)
      xctestrun_session.instance_variable_set(:@server_config, server_config)
      xctestrun_session.instance_variable_set(:@session_id, "test_session_id")

      allow(xctestrun_session).to receive(:get_md5).and_return("md5hash")
      allow(xctestrun_session).to receive(:get_s3_params).and_return({})
      allow(Utils).to receive(:create_upload_request)
      allow(BrowserStack).to receive(:logger).and_return(double(info: nil, error: nil))
      allow(FileUtils).to receive(:mkdir_p)
      allow(File).to receive(:exist?).and_return(true)
      allow(File).to receive(:delete)
    end

    context "when summary_file_data is nil" do
      it "returns early" do
        expect(Utils).not_to receive(:create_upload_request)
        xctestrun_session.send(:zip_and_upload_crash_logs, nil)
      end
    end

    context "when summary_file_data has no classes" do
      it "returns early" do
        test_summary = {}
        expect(Utils).not_to receive(:create_upload_request)
        xctestrun_session.send(:zip_and_upload_crash_logs, test_summary)
      end
    end

    context "when test_summary has classes but no tests" do
      it "does not process any crash logs" do
        test_summary = {
          classes: {
            "TestClass" => {}
          }
        }
        expect(Utils).not_to receive(:create_upload_request)
        xctestrun_session.send(:zip_and_upload_crash_logs, test_summary)
      end
    end

    context "when test_summary has classes with tests but no crash files" do
      it "does not process any crash logs" do
        test_summary = {
          classes: {
            "TestClass" => {
              tests: {
                "testMethod" => {}
              }
            }
          }
        }
        expect(Utils).not_to receive(:create_upload_request)
        xctestrun_session.send(:zip_and_upload_crash_logs, test_summary)
      end
    end

    context "when test_summary has tests with empty crash files" do
      it "does not process any crash logs" do
        test_summary = {
          classes: {
            "TestClass" => {
              tests: {
                "testMethod" => { crash_files: [] }
              }
            }
          }
        }
        expect(Utils).not_to receive(:create_upload_request)
        xctestrun_session.send(:zip_and_upload_crash_logs, test_summary)
      end
    end
  end
end
