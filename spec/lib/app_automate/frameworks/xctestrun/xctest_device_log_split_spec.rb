require_relative "../../../../spec_helper"
require_relative "../../../../../lib/utils/utils"
require_relative "../../../../../server/spawn_xctest"

describe BrowserStack::XCTestDeviceLogSplit do
  let(:xctestrun_failed_instru_file) { "./spec/fixtures/xctestrun_instrumentation_failure.txt" }
  let(:device_log_file) { "./spec/fixtures/xctestrun_devicelog.txt" }
  let(:session_id) { "abcd" }
  let(:device) { "device-name" }
  let(:retry_config) { {} }
  let(:time_components)  do
    {
      xcodebuild: { start: Time.now - 100, end: Time.now },
      video: { start_duration: 0, end_duration: 90, device_start_time: Time.now - 95 }
    }
  end

  let(:instru_log_double) { instance_double(XCTestInstruLogParse) }
  let(:video_log_double) { instance_double(XCTestVideoLogSplit) }
  before do
    allow(BrowserStack::<PERSON><PERSON>Logger).to receive(:app_device_logs_file).and_return(device_log_file)
    allow(XCTestInstruLogParse).to receive(:new).and_return(instru_log_double)
    allow(instru_log_double).to receive(:process)
    allow(XCTestVideoLogSplit).to receive(:new).and_return(video_log_double)
    allow(video_log_double).to receive(:process)
  end

  context "#initialize" do
    it "should raise empty file error when device log file is emtpy" do
      allow(File).to receive(:exist?).and_return(true)
      allow(File).to receive(:zero?).and_return(true)
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_failed_instru_file, device, nil, false, true, true, session_id, time_components, 7200, retry_config)
      xctest_log_split_manager.test_details = { "key" => "value" }
      xctest_log_split_manager.test_details_valid = true
      xctest_log_split_manager.process_logs
      expect(xctest_log_split_manager.log_split_failures).to have_key(XCTEST_DEVICE_LOG_SPLIT_ERROR)
    end

    it "should raise empty file error when device log file is missing" do
      allow(File).to receive(:exist?).and_return(false)
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_failed_instru_file, device, nil, false, true, true, session_id, time_components, 7200, retry_config)
      xctest_log_split_manager.test_details = { "key" => "value" }
      xctest_log_split_manager.test_details_valid = true
      xctest_log_split_manager.process_logs
      expect(xctest_log_split_manager.log_split_failures).to have_key(XCTEST_DEVICE_LOG_SPLIT_ERROR)
    end
  end

  context "#process_log" do
    before do
      allow_any_instance_of(XCTestDeviceLogSplit).to receive(:sanitized_time) do |_instance, time|
        parsed_date_time = DateTime.parse(time.to_s)
        new_date_time = DateTime.new(2023, parsed_date_time.month, parsed_date_time.day, parsed_date_time.hour, parsed_date_time.minute, parsed_date_time.second)
        DateTime.parse(new_date_time.to_time.to_s).to_time
      end
    end

    it "should not process device log when device log is not enabled for session" do
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_failed_instru_file, device, nil, false, true, false, session_id, time_components, 7200, retry_config)
      xctest_log_split_manager.test_details = {
        "dummy_test_1" => {
          "name" => "dummy_test_1",
          "duration" => 1.555,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:11.555 +0000").to_time,
          "status" => "failed"
        },
        "dummy_test_2" => {
          "name" => "dummy_test_2",
          "duration" => 1.000,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:14 +0000").to_time,
          "status" => "failed"
        },
        "dummy_test_3" => {
          "name" => "dummy_test_3",
          "duration" => 1.000,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:16 +0000").to_time,
          "status" => "failed"
        }
      }
      xctest_log_split_manager.test_details_valid = true
      expect(XCTestDeviceLogSplit).to_not receive(:new)
      expect(XCTestVideoLogSplit).to receive(:new)
      xctest_log_split_manager.process_logs
    end

    it "should parse device log when none of test execution time is overlapping" do
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_failed_instru_file, device, nil, false, true, true, session_id, time_components, 7200, retry_config)
      xctest_log_split_manager.test_details = {
        "dummy_test_1" => {
          "name" => "dummy_test_1",
          "duration" => 1.555,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:11.555 +0000").to_time,
          "status" => "failed"
        },
        "dummy_test_2" => {
          "name" => "dummy_test_2",
          "duration" => 1.000,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:14 +0000").to_time,
          "status" => "failed"
        },
        "dummy_test_3" => {
          "name" => "dummy_test_3",
          "duration" => 1.000,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:16 +0000").to_time,
          "status" => "failed"
        }
      }
      xctest_log_split_manager.test_details_valid = true
      xctest_log_split_manager.process_logs
      expected_device_log_boundaries = [
        { "start" => 0, "end" => 374 },
        { "start" => 374, "end" => 564 },
        { "start" => 564, "end" => 1008 }
      ]
      expected_device_log_boundaries.each_with_index do |bounds, index|
        expect(xctest_log_split_manager.test_details["dummy_test_#{index + 1}"]["device_log_boundary"]).to eql(bounds)
      end
    end

    it "should parse device log when test execution time is overlapping" do
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_failed_instru_file, device, nil, false, true, true, session_id, time_components, 7200, retry_config)
      xctest_log_split_manager.test_details = {
        "dummy_test_1" => {
          "name" => "dummy_test_1",
          "duration" => 2.000,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:11 +0000").to_time,
          "status" => "failed"
        },
        "dummy_test_2" => {
          "name" => "dummy_test_2",
          "duration" => 1.000,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:13 +0000").to_time,
          "status" => "failed"
        },
        "dummy_test_3" => {
          "name" => "dummy_test_3",
          "duration" => 4.000,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:14 +0000").to_time,
          "status" => "failed"
        }
      }
      xctest_log_split_manager.test_details_valid = true
      xctest_log_split_manager.process_logs
      expected_device_log_boundaries = [
        { "start" => 0, "end" => 374 },
        { "start" => 288, "end" => 475 },
        { "start" => 374, "end" => 1008 }
      ]
      expected_device_log_boundaries.each_with_index do |bounds, index|
        expect(xctest_log_split_manager.test_details["dummy_test_#{index + 1}"]["device_log_boundary"]).to eql(bounds)
      end
    end

    it "should parse device log when device log is missing for few test" do
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_failed_instru_file, device, nil, false, true, true, session_id, time_components, 7200, retry_config)
      xctest_log_split_manager.test_details = {
        "dummy_test_1" => {
          "name" => "dummy_test_1",
          "duration" => 2.000,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:11 +0000").to_time,
          "status" => "failed"
        },
        "dummy_test_2" => {
          "name" => "dummy_test_2",
          "duration" => 1.000,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:17 +0000").to_time,
          "status" => "failed"
        },
        "dummy_test_3" => {
          "name" => "dummy_test_3",
          "duration" => 1.000,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:19 +0000").to_time,
          "status" => "failed"
        }
      }
      xctest_log_split_manager.test_details_valid = true
      xctest_log_split_manager.process_logs
      expected_device_log_boundaries = [
        { "start" => 0, "end" => 374 },
        { "start" => 751, "end" => 837 },
        { "start" => 751, "end" => 1008 }
      ]
      expected_device_log_boundaries.each_with_index do |bounds, index|
        expect(xctest_log_split_manager.test_details["dummy_test_#{index + 1}"]["device_log_boundary"]).to eql(bounds)
      end
    end

    it "should parse device log when last few test doesn't have device log" do
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_failed_instru_file, device, nil, false, true, true, session_id, time_components, 7200, retry_config)
      xctest_log_split_manager.test_details = {
        "dummy_test_1" => {
          "name" => "dummy_test_1",
          "duration" => 4.000,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:11 +0000").to_time,
          "status" => "failed"
        },
        "dummy_test_2" => {
          "name" => "dummy_test_2",
          "duration" => 1.000,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:20 +0000").to_time,
          "status" => "failed"
        },
        "dummy_test_3" => {
          "name" => "dummy_test_3",
          "duration" => 1.000,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:21 +0000").to_time,
          "status" => "failed"
        }
      }
      xctest_log_split_manager.test_details_valid = true
      xctest_log_split_manager.process_logs
      expected_device_log_boundaries = [
        { "start" => 0, "end" => 564 },
        { "start" => 837, "end" => 1008 },
        { "start" => nil, "end" => nil }
      ]
      expected_device_log_boundaries.each_with_index do |bounds, index|
        expect(xctest_log_split_manager.test_details["dummy_test_#{index + 1}"]["device_log_boundary"]).to eql(bounds)
      end
    end

    it "should parse device log when device log generated more than last test execution time" do
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_failed_instru_file, device, nil, false, true, true, session_id, time_components, 7200, retry_config)
      xctest_log_split_manager.test_details = {
        "dummy_test_1" => {
          "name" => "dummy_test_1",
          "duration" => 2.000,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:11 +0000").to_time,
          "status" => "failed"
        },
        "dummy_test_2" => {
          "name" => "dummy_test_2",
          "duration" => 1.000,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:17 +0000").to_time,
          "status" => "failed"
        },
        "dummy_test_3" => {
          "name" => "dummy_test_3",
          "duration" => 1.000,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:19 +0000").to_time,
          "status" => "failed"
        }
      }
      xctest_log_split_manager.test_details_valid = true
      xctest_log_split_manager.process_logs
      expected_device_log_boundaries = [
        { "start" => 0, "end" => 374 },
        { "start" => 751, "end" => 837 },
        { "start" => 751, "end" => 1008 }
      ]
      expected_device_log_boundaries.each_with_index do |bounds, index|
        expect(xctest_log_split_manager.test_details["dummy_test_#{index + 1}"]["device_log_boundary"]).to eql(bounds)
      end
    end

    it "should parse device log when multiple test executed in one second" do
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_failed_instru_file, device, nil, false, true, true, session_id, time_components, 7200, retry_config)
      xctest_log_split_manager.test_details = {
        "dummy_test_1" => {
          "name" => "dummy_test_1",
          "duration" => 0.001,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:11 +0000").to_time,
          "status" => "failed"
        },
        "dummy_test_2" => {
          "name" => "dummy_test_2",
          "duration" => 0.001,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:11 +0000").to_time,
          "status" => "failed"
        },
        "dummy_test_3" => {
          "name" => "dummy_test_3",
          "duration" => 0.001,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:11 +0000").to_time,
          "status" => "failed"
        }
      }
      xctest_log_split_manager.test_details_valid = true
      xctest_log_split_manager.process_logs
      expected_device_log_boundaries = [
        { "start" => 0, "end" => 101 },
        { "start" => 0, "end" => 101 },
        { "start" => 0, "end" => 1008 }
      ]
      expected_device_log_boundaries.each_with_index do |bounds, index|
        expect(xctest_log_split_manager.test_details["dummy_test_#{index + 1}"]["device_log_boundary"]).to eql(bounds)
      end
    end

    it "should not split device log when test start time or duration not prsent is not present" do
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_failed_instru_file, device, nil, false, true, true, session_id, time_components, 7200, retry_config)
      xctest_log_split_manager.test_details = {
        "dummy_test_1" => {
          "name" => "dummy_test_1",
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:11 +0000").to_time,
          "status" => "failed"
        },
        "dummy_test_2" => {
          "name" => "dummy_test_2",
          "duration" => 0.001,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "status" => "failed"
        },
        "dummy_test_3" => {
          "name" => "dummy_test_3",
          "duration" => 0.001,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:11 +0000").to_time,
          "status" => "failed"
        }
      }
      xctest_log_split_manager.test_details_valid = true
      xctest_log_split_manager.process_logs
      expected_device_log_boundaries = [
        { "start" => nil, "end" => nil },
        { "start" => nil, "end" => nil },
        { "start" => 0, "end" => 1008 }
      ]
      expected_device_log_boundaries.each_with_index do |bounds, index|
        expect(xctest_log_split_manager.test_details["dummy_test_#{index + 1}"]["device_log_boundary"]).to eql(bounds)
      end
    end
  end
end
