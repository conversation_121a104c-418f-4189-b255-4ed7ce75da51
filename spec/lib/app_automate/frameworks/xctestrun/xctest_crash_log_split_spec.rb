require_relative "../../../../spec_helper"
require_relative "../../../../../lib/utils/utils"
require_relative "../../../../../server/spawn_xctest"

describe BrowserStack::XCTestCrashLogSplit do
  let(:xctestrun_failed_instru_file) { "./spec/fixtures/xctestrun_instrumentation_failure.txt" }
  let(:device_log_file) { "./spec/fixtures/xctestrun_devicelog.txt" }
  let(:crash_log_file) { "./spec/fixtures" }
  let(:crash_log_path) { "/tmp/crash_logs_path" }
  let(:session_id) { "abcd" }
  let(:device) { "device-name" }
  let(:time_components) do
    {
      xcodebuild: { start: Time.now - 100, end: Time.now },
      video: { start_duration: 0, end_duration: 90, device_start_time: Time.now - 95 }
    }
  end
  let(:retry_config) { {} }

  let(:test_details) do
    {
      "dummy_test_1" => {
        "name" => "dummy_test_1",
        "duration" => 1.555,
        "start_time" => Time.parse("2023-08-19 13:13:10 +0000"),
        "end_time" => Time.parse("2023-08-19 13:13:15 +0000"),
        "status" => "failed"
      },
      "dummy_test_2" => {
        "name" => "dummy_test_2",
        "duration" => 1.000,
        "start_time" => Time.parse("2023-08-19 13:13:35 +0000"),
        "end_time" => Time.parse("2023-08-19 13:13:40 +0000"),
        "status" => "failed"
      },
      "dummy_test_3" => {
        "name" => "dummy_test_3",
        "duration" => 1.000,
        "start_time" => Time.parse("2023-08-19 13:14:30 +0000"),
        "end_time" => Time.parse("2023-08-19 13:14:35 +0000"),
        "status" => "failed"
      }
    }
  end

  let(:instru_log_double) { instance_double(XCTestInstruLogParse) }
  let(:device_log_double) { instance_double(XCTestDeviceLogSplit) }
  let(:video_log_double) { instance_double(XCTestVideoLogSplit) }

  let(:log_split_manager) do
    manager = double("log_split_manager")
    allow(manager).to receive(:crash_log_path).and_return(crash_log_path)
    allow(manager).to receive(:session_id).and_return(session_id)
    allow(manager).to receive(:device).and_return(device)
    allow(manager).to receive(:test_details).and_return(test_details)
    allow(manager).to receive(:add_log_split_error)
    allow(manager).to receive(:log)
    manager
  end

  before do
    allow(XCTestInstruLogParse).to receive(:new).and_return(instru_log_double)
    allow(instru_log_double).to receive(:process)
    allow(XCTestDeviceLogSplit).to receive(:new).and_return(device_log_double)
    allow(device_log_double).to receive(:process)
    allow(XCTestVideoLogSplit).to receive(:new).and_return(video_log_double)
    allow(video_log_double).to receive(:process)
  end

  context "#initialize" do
    it "should initialize crash log path correctly" do
      allow(Dir).to receive(:[]).and_return(["crash1.ips", "crash2.ips"])

      crash_log_split = BrowserStack::XCTestCrashLogSplit.new(log_split_manager)
      expect(crash_log_split.instance_variable_get(:@crash_log_path)).to eq(crash_log_path)
      expect(crash_log_split.instance_variable_get(:@crash_logs)).to eq(["crash1.ips", "crash2.ips"])
    end

    it "should handle empty crash logs directory" do
      allow(Dir).to receive(:[]).and_return([])

      crash_log_split = BrowserStack::XCTestCrashLogSplit.new(log_split_manager)
      expect(crash_log_split.instance_variable_get(:@crash_logs)).to eq([])
    end
  end

  context "#validate_log_file" do
    it "should return early if crash log path is nil" do
      allow(log_split_manager).to receive(:crash_log_path).and_return(nil)
      crash_log_split = BrowserStack::XCTestCrashLogSplit.new(log_split_manager)

      expect(crash_log_split.validate_log_file).to be_nil
    end

    it "should return early if crash log directory doesn't exist" do
      allow(Dir).to receive(:exist?).with(crash_log_path).and_return(false)
      crash_log_split = BrowserStack::XCTestCrashLogSplit.new(log_split_manager)

      expect(crash_log_split.validate_log_file).to be_nil
    end

    it "should return early if crash log directory is empty" do
      allow(Dir).to receive(:exist?).with(crash_log_path).and_return(true)
      allow(Dir).to receive(:empty?).with(crash_log_path).and_return(true)
      crash_log_split = BrowserStack::XCTestCrashLogSplit.new(log_split_manager)

      expect(crash_log_split.validate_log_file).to be_nil
    end
  end

  context "#crash log parsing and mapping" do
    it "should parse crash log" do
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_failed_instru_file, device, nil, false, true, true, session_id, time_components, 7200, retry_config)
      allow(xctest_log_split_manager).to receive(:crash_log_path).and_return(crash_log_file)
      allow(xctest_log_split_manager).to receive(:log)
      xctest_log_split_manager.test_details = {
        "dummy_test_1" => {
          "name" => "dummy_test_1",
          "duration" => 1.555,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:11.555 +0000").to_time,
          "status" => "failed"
        },
        "dummy_test_2" => {
          "name" => "dummy_test_2",
          "duration" => 1.000,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:14 +0000").to_time,
          "status" => "failed"
        },
        "dummy_test_3" => {
          "name" => "dummy_test_3",
          "duration" => 1.000,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:16 +0000").to_time,
          "status" => "failed"
        }
      }
      xctest_log_split_manager.test_details_valid = true
      xctest_log_split_manager.process_logs
      expect(xctest_log_split_manager.test_details["dummy_test_2"]["crash_files"]).to eql(["./spec/fixtures/xctestrun_crash.ips"])
    end

    it "should parse crash log with crash log in between two test" do
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_failed_instru_file, device, nil, false, true, true, session_id, time_components, 7200, retry_config)
      allow(xctest_log_split_manager).to receive(:crash_log_path).and_return(crash_log_file)
      allow(xctest_log_split_manager).to receive(:log)

      crash_file = "./spec/fixtures/xctestrun_crash.ips"
      crash_time_str = "2023-12-16 00:02:12.555 +0000"
      allow(File).to receive(:foreach).with(crash_file).and_yield("Some content").and_yield("Date/Time: #{crash_time_str}").and_yield("More content")
      xctest_log_split_manager.test_details = {
        "dummy_test_1" => {
          "name" => "dummy_test_1",
          "duration" => 1.555,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:11.555 +0000").to_time,
          "status" => "failed"
        },
        "dummy_test_2" => {
          "name" => "dummy_test_2",
          "duration" => 1.000,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:14 +0000").to_time,
          "status" => "failed"
        },
        "dummy_test_3" => {
          "name" => "dummy_test_3",
          "duration" => 1.000,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:16 +0000").to_time,
          "status" => "failed"
        }
      }
      xctest_log_split_manager.test_details_valid = true
      xctest_log_split_manager.process_logs
      expect(xctest_log_split_manager.test_details["dummy_test_1"]["crash_files"]).to eql([crash_file])
    end

    it "should parse crash log with crash log before even first test" do
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_failed_instru_file, device, nil, false, true, true, session_id, time_components, 7200, retry_config)
      allow(xctest_log_split_manager).to receive(:crash_log_path).and_return(crash_log_file)
      allow(xctest_log_split_manager).to receive(:log)

      crash_file = "./spec/fixtures/xctestrun_crash.ips"
      crash_time_str = "2023-12-16 00:02:10.555 +0000"
      allow(File).to receive(:foreach).with(crash_file).and_yield("Some content").and_yield("Date/Time: #{crash_time_str}").and_yield("More content")
      xctest_log_split_manager.test_details = {
        "dummy_test_1" => {
          "name" => "dummy_test_1",
          "duration" => 1.555,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:11.555 +0000").to_time,
          "status" => "failed"
        },
        "dummy_test_2" => {
          "name" => "dummy_test_2",
          "duration" => 1.000,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:14 +0000").to_time,
          "status" => "failed"
        },
        "dummy_test_3" => {
          "name" => "dummy_test_3",
          "duration" => 1.000,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:16 +0000").to_time,
          "status" => "failed"
        }
      }
      xctest_log_split_manager.test_details_valid = true
      xctest_log_split_manager.process_logs
      expect(xctest_log_split_manager.test_details["dummy_test_1"]["crash_files"]).to eql([crash_file])
    end
  end

  context "#parse_crash_time_from_crash_file" do
    it "should parse time correctly from crash file" do
      crash_file = "/tmp/crash.ips"
      crash_time_str = "2023-08-19 13:13:30.000000000 +0000"
      expected_time = Time.parse(crash_time_str)

      allow(File).to receive(:foreach).with(crash_file).and_yield("Some content").and_yield("Date/Time: #{crash_time_str}").and_yield("More content")

      crash_log_split = BrowserStack::XCTestCrashLogSplit.new(log_split_manager)
      result = crash_log_split.send(:parse_crash_time_from_crash_file, crash_file)

      expect(result).to eq(expected_time)
    end

    it "should parse time from captureTime field" do
      crash_file = "/tmp/crash.ips"
      crash_time_str = "2023-08-19T13:13:30Z"
      expected_time = Time.parse(crash_time_str)

      allow(File).to receive(:foreach).with(crash_file)
                                      .and_yield('Some content')
                                      .and_yield('  "captureTime" : "2023-08-19T13:13:30Z",')
                                      .and_yield('More content')

      crash_log_split = BrowserStack::XCTestCrashLogSplit.new(log_split_manager)
      result = crash_log_split.send(:parse_crash_time_from_crash_file, crash_file)

      expect(result).to eq(expected_time)
    end

    it "should return nil for invalid time format" do
      crash_file = "/tmp/crash.ips"
      invalid_time_str = "Invalid Time Format"

      allow(File).to receive(:foreach).with(crash_file).and_yield("Some content").and_yield("Date/Time: #{invalid_time_str}").and_yield("More content")

      crash_log_split = BrowserStack::XCTestCrashLogSplit.new(log_split_manager)
      result = crash_log_split.send(:parse_crash_time_from_crash_file, crash_file)

      expect(result).to be_nil
    end

    it "should return nil if no Date/Time or captureTime line exists" do
      crash_file = "./spec/fixtures/xctestrun_crash.ips"
      allow(File).to receive(:foreach).with(crash_file).and_yield("Some content").and_yield("No date time here").and_yield("More content")
      expect(log_split_manager).to receive(:update_log_split_failures_count).with("crash_log_split_failures")

      crash_log_split = BrowserStack::XCTestCrashLogSplit.new(log_split_manager)
      result = crash_log_split.send(:parse_crash_time_from_crash_file, crash_file)

      expect(result).to be_nil
    end
  end

  context "#crash_logs_with_timestamps" do
    it "should return sorted crash logs with timestamps" do
      crash_file1 = "/tmp/crash1.ips"
      crash_file2 = "/tmp/crash2.ips"
      time1 = Time.parse("2023-08-19 13:13:30 +0000")
      time2 = Time.parse("2023-08-19 13:13:40 +0000")

      allow(Dir).to receive(:[]).and_return([crash_file1, crash_file2])
      crash_log_split = BrowserStack::XCTestCrashLogSplit.new(log_split_manager)

      allow(crash_log_split).to receive(:parse_crash_time_from_crash_file).with(crash_file1).and_return(time1)
      allow(crash_log_split).to receive(:parse_crash_time_from_crash_file).with(crash_file2).and_return(time2)

      result = crash_log_split.send(:crash_logs_with_timestamps)

      expect(result).to eq([
        { path: crash_file1, crash_time: time1 },
        { path: crash_file2, crash_time: time2 }
      ])
    end

    it "should filter out crash logs with nil timestamps" do
      crash_file1 = "/tmp/crash1.ips"
      crash_file2 = "/tmp/crash2.ips"
      time1 = Time.parse("2023-08-19 13:13:30 +0000")

      allow(Dir).to receive(:[]).and_return([crash_file1, crash_file2])
      crash_log_split = BrowserStack::XCTestCrashLogSplit.new(log_split_manager)

      allow(crash_log_split).to receive(:parse_crash_time_from_crash_file).with(crash_file1).and_return(time1)
      allow(crash_log_split).to receive(:parse_crash_time_from_crash_file).with(crash_file2).and_return(nil)

      result = crash_log_split.send(:crash_logs_with_timestamps)

      expect(result).to eq([{ path: crash_file1, crash_time: time1 }])
    end
  end

  context "#map_crashes_to_tests" do
    it "should properly map crash logs to tests based on timestamps" do
      crash_file1 = "/tmp/crash1.ips"
      crash_file2 = "/tmp/crash2.ips"
      crash_time1 = Time.parse("2023-08-19 13:13:12 +0000") # During dummy_test_1
      crash_time2 = Time.parse("2023-08-19 13:14:32 +0000") # During dummy_test_3

      allow(Dir).to receive(:[]).and_return([crash_file1, crash_file2])
      crash_log_split = BrowserStack::XCTestCrashLogSplit.new(log_split_manager)

      allow(crash_log_split).to receive(:crash_logs_with_timestamps).and_return([
        { path: crash_file1, crash_time: crash_time1 },
        { path: crash_file2, crash_time: crash_time2 }
      ])

      crash_log_split.send(:map_crashes_to_tests)

      # Test that crash files got added to the test_details
      expect(test_details["dummy_test_1"]["crash_files"]).to include(crash_file1)
      expect(test_details["dummy_test_3"]["crash_files"]).to include(crash_file2)
      expect(test_details["dummy_test_2"]["crash_files"]).to be_nil
    end

    it "should handle crash logs that occur between tests" do
      crash_file = "/tmp/crash.ips"
      crash_time = Time.parse("2023-08-19 13:13:20 +0000") # After dummy_test_1, before dummy_test_2

      allow(Dir).to receive(:[]).and_return([crash_file])
      crash_log_split = BrowserStack::XCTestCrashLogSplit.new(log_split_manager)

      allow(crash_log_split).to receive(:crash_logs_with_timestamps).and_return([
        { path: crash_file, crash_time: crash_time }
      ])

      crash_log_split.send(:map_crashes_to_tests)

      # Test that crash logs between tests are attributed to the previous test
      expect(test_details["dummy_test_1"]["crash_files"]).to include(crash_file)
      expect(test_details["dummy_test_2"]["crash_files"]).to be_nil
    end

    it "should assign crash logs to the first test if they occur before all tests" do
      crash_file = "/tmp/crash.ips"
      crash_time = Time.parse("2023-08-19 13:12:00 +0000") # Before all tests

      allow(Dir).to receive(:[]).and_return([crash_file])
      crash_log_split = BrowserStack::XCTestCrashLogSplit.new(log_split_manager)

      allow(crash_log_split).to receive(:crash_logs_with_timestamps).and_return([
        { path: crash_file, crash_time: crash_time }
      ])

      crash_log_split.send(:map_crashes_to_tests)

      # Test that crash logs before all tests are attributed to the first test
      expect(test_details["dummy_test_1"]["crash_files"]).to include(crash_file)
    end
  end

  context "#process_log" do
    it "should process crash logs successfully" do
      allow(Dir).to receive(:[]).and_return(["/tmp/crash1.ips", "/tmp/crash2.ips"])
      crash_log_split = BrowserStack::XCTestCrashLogSplit.new(log_split_manager)

      expect(crash_log_split).to receive(:map_crashes_to_tests)

      crash_log_split.process_log
    end

    it "should handle empty crash logs directory" do
      allow(Dir).to receive(:[]).and_return([])
      crash_log_split = BrowserStack::XCTestCrashLogSplit.new(log_split_manager)

      expect(crash_log_split).not_to receive(:map_crashes_to_tests)

      crash_log_split.process_log
    end
  end
end
