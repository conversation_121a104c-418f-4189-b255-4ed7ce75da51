require_relative "../../../../spec_helper"
require_relative "../../../../../lib/utils/utils"
require_relative "../../../../../server/spawn_xctest"

describe BrowserStack::XCTestVideoLogSplit do
  let(:xctestrun_failed_instru_file) { "./spec/fixtures/xctestrun_instrumentation_failure.txt" }
  let(:device_log_file) { "./spec/fixtures/xctestrun_devicelog.txt" }
  let(:session_id) { "abcd" }
  let(:device) { "device-name" }
  let(:retry_config) { {} }

  let(:instru_log_double) { instance_double(XCTestInstruLogParse) }
  let(:device_log_double) { instance_double(XCTestDeviceLogSplit) }

  before do
    allow(XCTestInstruLogParse).to receive(:new).and_return(instru_log_double)
    allow(instru_log_double).to receive(:process)
    allow(XCTestDeviceLogSplit).to receive(:new).and_return(device_log_double)
    allow(device_log_double).to receive(:process)
  end

  context "#process_log" do
    it "should not process video log when video log is not enabled for session" do
      time_components = {
        xcodebuild: { start: Time.now - 100, end: Time.now },
        video: { start_duration: 0, end_duration: 90, device_start_time: Time.now - 95 }
      }

      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_failed_instru_file, device, nil, false, false, true, session_id, time_components, 7200, retry_config)
      xctest_log_split_manager.test_details = {
        "dummy_test_1" => {
          "name" => "dummy_test_1",
          "duration" => 1.555,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:11.555 +0000").to_time,
          "status" => "failed"
        },
        "dummy_test_2" => {
          "name" => "dummy_test_2",
          "duration" => 1.000,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:14 +0000").to_time,
          "status" => "failed"
        },
        "dummy_test_3" => {
          "name" => "dummy_test_3",
          "duration" => 1.000,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => DateTime.parse("2023-12-16 00:02:16 +0000").to_time,
          "status" => "failed"
        }
      }
      xctest_log_split_manager.test_details_valid = true
      expect(XCTestDeviceLogSplit).to receive(:new)
      expect(XCTestVideoLogSplit).to_not receive(:new)
      xctest_log_split_manager.process_logs
    end

    it "should split video log when test details have no issues" do
      time_components = {
        xcodebuild: { start: Time.now - 100, end: Time.now },
        video: { start_duration: 0, end_duration: 90, device_start_time: Time.now - 95 }
      }
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_failed_instru_file, device, nil, false, true, true, session_id, time_components, 7200, retry_config)
      xctest_log_split_manager.test_details = {
        "dummy_test_1" => {
          "name" => "dummy_test_1",
          "duration" => 1.555,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => Time.now - 90,
          "status" => "failed"
        },
        "dummy_test_2" => {
          "name" => "dummy_test_2",
          "duration" => 1.000,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => Time.now - 80,
          "status" => "failed"
        },
        "dummy_test_3" => {
          "name" => "dummy_test_3",
          "duration" => 1.000,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => Time.now - 70,
          "status" => "failed"
        }
      }
      xctest_log_split_manager.test_details_valid = true
      xctest_log_split_manager.process_logs
      expected_video_log_boundaries = [
        { "start" => 0, "end" => 15 },
        { "start" => 15, "end" => 25 },
        { "start" => 25, "end" => 90 }
      ]
      expected_video_log_boundaries.each_with_index do |bounds, index|
        expect(xctest_log_split_manager.test_details["dummy_test_#{index + 1}"]["video_log_boundary"]).to eql(bounds)
      end
    end

    it "should not split video log when test details have missing start time" do
      time_components = {
        xcodebuild: { start: Time.now - 100, end: Time.now },
        video: { start_duration: 0, end_duration: 90, device_start_time: Time.now - 95 }
      }
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_failed_instru_file, device, nil, false, true, true, session_id, time_components, 7200, retry_config)
      xctest_log_split_manager.test_details = {
        "dummy_test_1" => {
          "name" => "dummy_test_1",
          "duration" => 1.555,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => nil,
          "status" => "failed"
        },
        "dummy_test_2" => {
          "name" => "dummy_test_2",
          "duration" => 1.000,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => nil,
          "status" => "failed"
        },
        "dummy_test_3" => {
          "name" => "dummy_test_3",
          "duration" => 1.000,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => nil,
          "status" => "failed"
        }
      }
      xctest_log_split_manager.test_details_valid = true
      xctest_log_split_manager.process_logs
      expected_video_log_boundaries = [
        { "start" => nil, "end" => nil },
        { "start" => nil, "end" => nil },
        { "start" => nil, "end" => nil }
      ]
      expected_video_log_boundaries.each_with_index do |bounds, index|
        expect(xctest_log_split_manager.test_details["dummy_test_#{index + 1}"]["video_log_boundary"]).to eql(bounds)
      end
    end

    it "should partial split video log when some test details have missing start time" do
      time_components = {
        xcodebuild: { start: Time.now - 100, end: Time.now },
        video: { start_duration: 0, end_duration: 90, device_start_time: Time.now - 95 }
      }
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_failed_instru_file, device, nil, false, true, true, session_id, time_components, 7200, retry_config)
      xctest_log_split_manager.test_details = {
        "dummy_test_1" => {
          "name" => "dummy_test_1",
          "duration" => 1.555,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => nil,
          "status" => "failed"
        },
        "dummy_test_2" => {
          "name" => "dummy_test_2",
          "duration" => 1.000,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => Time.now - 80,
          "status" => "failed"
        },
        "dummy_test_3" => {
          "name" => "dummy_test_3",
          "duration" => 1.000,
          "instru_log_boundary" => { "end" => 6762, "start" => 0 },
          "start_time" => nil,
          "status" => "failed"
        }
      }
      xctest_log_split_manager.test_details_valid = true
      xctest_log_split_manager.process_logs
      expected_video_log_boundaries = [
        { "start" => nil, "end" => nil },
        { "start" => nil, "end" => nil },
        { "start" => nil, "end" => nil }
      ]
      expected_video_log_boundaries.each_with_index do |bounds, index|
        expect(xctest_log_split_manager.test_details["dummy_test_#{index + 1}"]["video_log_boundary"]).to eql(bounds)
      end
    end
  end
end
