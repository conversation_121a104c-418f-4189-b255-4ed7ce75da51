require_relative '../../../../spec_helper'
require_relative '../../../../../lib/utils/utils'
require_relative '../../../../../server/spawn_xctest'

describe BrowserStack::XCTestLogSplitManager do
  let(:xctestrun_failed_instru_file) { "./spec/fixtures/xctestrun_instrumentation_failure.txt" }
  let(:xctestrun_success_instru_file) { "./spec/fixtures/xctestrun_instrumentation_success.txt" }
  let(:xctestrun_failed_instru_file_NA_count) { "./spec/fixtures/xctestrun_instrumentation_failure_NA_count.txt" }
  let(:xctestrun_success_multiple_config_file) { "./spec/fixtures/xctestrun_instrumentation_success_multi_config.txt" }
  let(:xctestrun_all_status_test_iteration) { "./spec/fixtures/xctestrun_instrumentation_xcretry_test_iteration_all_status.txt" }
  let(:xctestrun_mix_status_missing_final_status) { "./spec/fixtures/xctestrun_instrumentation_xcretry_missing_final_status.txt" }
  let(:xctestrun_success_test_iteration) { "./spec/fixtures/xctestrun_instrumentation_xcretry_test_iteration_success.txt" }
  let(:xctestrun_failure_test_iteration) { "./spec/fixtures/xctestrun_instrumentation_xcretry_test_iteration_failure.txt" }
  let(:xctestrun_no_status_tests) { "./spec/fixtures/xctestrun_instrumentation_no_status_tests.txt" }
  let(:xctestrun_no_status_no_start_tests) { "./spec/fixtures/xctestrun_instrumentation_no_status_no_start.txt" }
  let(:xctestrun_no_status_single_test) { "./spec/fixtures/xctestrun_instrumentation_no_status_single_test.txt" }
  let(:xctestrun_testsuite_all_unit_tests) { "./spec/fixtures/xctestrun_instrumentation_all_ut.txt" }
  let(:device_log_file) { "./spec/fixtures/xctestrun_instrumentation_no_status_single_test.txt" }
  let(:session_id) { "abcd" }
  let(:device) { "device-name" }
  let(:retry_config) { {} }
  let(:time_components)  do
    {
      xcodebuild: { start: Time.now - 100, end: Time.now },
      video: { start_duration: 0, end_duration: 90, device_start_time: Time.now - 95 }
    }
  end

  let(:device_log_double) { instance_double(XCTestDeviceLogSplit) }
  let(:video_log_double) { instance_double(XCTestVideoLogSplit) }
  before do
    allow(XCTestDeviceLogSplit).to receive(:new).and_return(device_log_double)
    allow(device_log_double).to receive(:process)
    allow(XCTestVideoLogSplit).to receive(:new).and_return(video_log_double)
    allow(video_log_double).to receive(:process)
  end

  context "#initialize" do
    it "should raise empty file error when instrumentation file is emtpy" do
      allow(File).to receive(:exist?).and_return(true)
      allow(File).to receive(:zero?).and_return(true)
      begin
        BrowserStack::XCTestLogSplitManager.new(xctestrun_failed_instru_file, device, nil, false, true, true, session_id, time_components, 7200, retry_config).process_logs
      rescue FileEmpty => e
        expect(e.message).to eql("Log file: #{xctestrun_failed_instru_file} is empty")
      end
    end

    it "should raise empty file error when instrumentation file is missing" do
      allow(File).to receive(:exist?).and_return(false)
      begin
        BrowserStack::XCTestLogSplitManager.new(xctestrun_failed_instru_file, device, nil, true, true, true, session_id, time_components, 7200, retry_config).process_logs
      rescue FileNotFound => e
        expect(e.message).to eql("Log file: #{xctestrun_failed_instru_file} not found")
      end
    end
  end

  context "#process_logs" do
    it "should parse test details of failed unit and UI tests" do
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_failed_instru_file, device, nil, false, true, true, session_id, time_components, 7200, retry_config)

      expected_test_details = { "BudgetKeeperTests.AccountsViewModelUnitTest testAccountsListEmpty" =>
                                { "name" => "BudgetKeeperTests.AccountsViewModelUnitTest testAccountsListEmpty",
                                  "duration" => 0.006,
                                  "instru_log_boundary" => { "end" => 838, "start" => 0 },
                                  "start_time" => nil,
                                  "is_unit_test" => true,
                                  "status" => "passed" },
                                "BudgetKeeperTests.AccountsViewModelUnitTest testAddNewAccount" =>
                                { "name" => "BudgetKeeperTests.AccountsViewModelUnitTest testAddNewAccount",
                                  "duration" => 0.004,
                                  "instru_log_boundary" => { "end" => 1025, "start" => 838 },
                                  "start_time" => nil,
                                  "is_unit_test" => true,
                                  "status" => "passed" },
                                "BudgetKeeperTests.AccountsViewModelUnitTest testDeleteAccount" =>
                                { "name" => "BudgetKeeperTests.AccountsViewModelUnitTest testDeleteAccount",
                                  "duration" => 0.0,
                                  "instru_log_boundary" => { "end" => 1212, "start" => 1025 },
                                  "start_time" => nil,
                                  "is_unit_test" => true,
                                  "status" => "passed" },
                                "BudgetKeeperTests.AccountsViewModelUnitTest testMultipleAccounts" =>
                                { "name" => "BudgetKeeperTests.AccountsViewModelUnitTest testMultipleAccounts",
                                  "duration" => 0.0,
                                  "instru_log_boundary" => { "end" => 1405, "start" => 1212 },
                                  "start_time" => nil,
                                  "is_unit_test" => true,
                                  "status" => "passed" },
                                "BudgetKeeperTests.AccountsViewModelUnitTest testUpdateBalance" =>
                                { "name" => "BudgetKeeperTests.AccountsViewModelUnitTest testUpdateBalance",
                                  "duration" => 0.0,
                                  "instru_log_boundary" => { "end" => 2482, "start" => 1405 },
                                  "start_time" => "2022-12-15 19:58:33 +0000",
                                  "is_unit_test" => true,
                                  "status" => "passed" },
                                "BudgetKeeperUITests.AccountsViewUITest testAddAccount" =>
                                { "name" => "BudgetKeeperUITests.AccountsViewUITest testAddAccount",
                                  "duration" => 5.722,
                                  "instru_log_boundary" => { "end" => 4764, "start" => 2483 },
                                  "start_time" => "2022-12-15 19:58:33 +0000",
                                  "status" => "failed" },
                                "BudgetKeeperUITests.AccountsViewUITest testDeleteAccount" =>
                                { "name" => "BudgetKeeperUITests.AccountsViewUITest testDeleteAccount",
                                  "duration" => 9.169,
                                  "instru_log_boundary" => { "end" => 7653, "start" => 4764 },
                                  "start_time" => "2022-12-15 19:58:39 +0000",
                                  "status" => "failed" },
                                "BudgetKeeperUITests.AccountsViewUITest testMultipleAccounts" =>
                                { "name" => "BudgetKeeperUITests.AccountsViewUITest testMultipleAccounts",
                                  "duration" => 16.824,
                                  "instru_log_boundary" => { "end" => 13902, "start" => 7653 },
                                  "start_time" => "2022-12-15 19:58:48 +0000",
                                  "status" => "failed" },
                                "BudgetKeeperUITests.AccountsViewUITest testUpdateBalance" =>
                                { "name" => "BudgetKeeperUITests.AccountsViewUITest testUpdateBalance",
                                  "duration" => 10.194,
                                  "instru_log_boundary" => { "end" => 18794, "start" => 13902 },
                                  "start_time" => "2022-12-15 19:59:05 +0000",
                                  "status" => "failed" } }

      xctest_log_split_manager.process_logs
      expect(JSON.parse(xctest_log_split_manager.test_details.to_json)).to eql(expected_test_details)
      expect(xctest_log_split_manager.final_test_status).to eql("FAILED")
    end

    it "should parse test details of testsuite with all having unittests" do
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_testsuite_all_unit_tests, device, nil, false, true, true, session_id, time_components, 7200, retry_config)

      expected_test_details = { "UnitTests.ApiCookieTests test_APICookie_clearSessionCookies" =>
                                { "name" => "UnitTests.ApiCookieTests test_APICookie_clearSessionCookies",
                                  "duration" => 0.002,
                                  "start_time" => nil,
                                  "status" => "passed",
                                  "is_unit_test" => true,
                                  "instru_log_boundary" => { "start" => 0, "end" => 15952  } },
                                "UnitTests.ApiCookieTests test_APICookie_ReadCookieValue" =>
                                  { "name" => "UnitTests.ApiCookieTests test_APICookie_ReadCookieValue",
                                    "duration" => 0.001,
                                    "start_time" => nil,
                                    "status" => "passed",
                                    "is_unit_test" => true,
                                    "instru_log_boundary" => { "start" => 15952, "end" => 16127 } },
                                "UnitTests.ApiCookieTests testCreateSessionCookie" =>
                                  { "name" => "UnitTests.ApiCookieTests testCreateSessionCookie",
                                    "duration" => 0.002,
                                    "start_time" => nil,
                                    "status" => "passed",
                                    "is_unit_test" => true,
                                    "instru_log_boundary" => { "start" => 16127, "end" => 16288 } },
                                "UnitTests.ApiCookieTests testDeleteWKWebViewCookie" =>
                                  { "name" => "UnitTests.ApiCookieTests testDeleteWKWebViewCookie",
                                    "duration" => 0.003,
                                    "start_time" => nil,
                                    "status" => "passed",
                                    "is_unit_test" => true,
                                    "instru_log_boundary" => { "start" => 16288, "end" => 16453 } },
                                "UnitTests.ApiCookieTests testGetAllCookies" =>
                                  { "name" => "UnitTests.ApiCookieTests testGetAllCookies",
                                    "duration" => 0.0,
                                    "start_time" => nil,
                                    "status" => "passed",
                                    "is_unit_test" => true,
                                    "instru_log_boundary" => { "start" => 16453, "end" => 16602 } },
                                "UnitTests.ApiCookieTests testGetCookie" =>
                                  { "name" => "UnitTests.ApiCookieTests testGetCookie",
                                    "duration" => 0.001,
                                    "start_time" => nil,
                                    "status" => "passed",
                                    "is_unit_test" => true,
                                    "instru_log_boundary" => { "start" => 16602, "end" => 16743 } },
                                "UnitTests.ApiCookieTests testSetCookie" =>
                                  { "name" => "UnitTests.ApiCookieTests testSetCookie",
                                    "duration" => 0.005,
                                    "start_time" => nil,
                                    "status" => "passed",
                                    "is_unit_test" => true,
                                    "instru_log_boundary" => { "start" => 16743, "end" => 17022 } } }
      begin
        xctest_log_split_manager.process_logs
      rescue => e
        expect(xctest_log_split_manager.test_details_valid).to eql(false)
      end
      expect(JSON.parse(xctest_log_split_manager.test_details.to_json)).to eql(expected_test_details)
    end

    it "should parse test details of success unit tests" do
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_success_instru_file, device, nil, false, true, true, session_id, time_components, 7200, retry_config)

      expected_test_details = { "BudgetKeeperTests.AccountsViewModelUnitTest testAccountsListEmpty" =>
                                { "name" => "BudgetKeeperTests.AccountsViewModelUnitTest testAccountsListEmpty",
                                  "duration" => 0.001,
                                  "instru_log_boundary" => { "end" => 2012, "start" => 0 },
                                  "start_time" => nil,
                                  "is_unit_test" => true,
                                  "status" => "passed" } }
      begin
        xctest_log_split_manager.process_logs
      rescue => e
        expect(xctest_log_split_manager.test_details_valid).to eql(false)
      end
      expect(JSON.parse(xctest_log_split_manager.test_details.to_json)).to eql(expected_test_details)
      expect(xctest_log_split_manager.final_test_status).to eql("SUCCEEDED")
    end

    it "should parse test details of failed tests which dont have final failed list" do
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_failed_instru_file_NA_count, device, nil, false, true, true, session_id, time_components, 7200, retry_config)

      expected_test_details = { "digitalwalletUITests.MyAccountUITests testMyAccountDeletionScreen" =>
                                { "name" => "digitalwalletUITests.MyAccountUITests testMyAccountDeletionScreen",
                                  "duration" => 6.037,
                                  "instru_log_boundary" => { "end" => 6762, "start" => 0 },
                                  "start_time" => "2022-12-16 00:02:11 +0000",
                                  "status" => "failed" } }

      xctest_log_split_manager.process_logs
      expect(JSON.parse(xctest_log_split_manager.test_details.to_json)).to eql(expected_test_details)
      expect(xctest_log_split_manager.final_test_status).to eql("FAILED")
    end

    it "should parse test details of success tests with multiple config" do
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_success_multiple_config_file, device, nil, false, true, true, session_id, time_components, 7200, retry_config)

      expected_test_details = { "BullsEyeUITests.BullsEyeUITests testGameStyleSwitch002" =>
                                  { "name" => "BullsEyeUITests.BullsEyeUITests testGameStyleSwitch002",
                                    "duration" => 7.898,
                                    "instru_log_boundary" => { "end" => 5044, "start" => 0 },
                                    "start_time" => "2023-06-15 10:04:10 +0000",
                                    "status" => "failed" },
                                "BullsEyeUITests.BullsEyeUITests2 testGameStyleSwitch" =>
                                  { "name" => "BullsEyeUITests.BullsEyeUITests2 testGameStyleSwitch",
                                    "duration" => 2.451,
                                    "instru_log_boundary" => { "end" => 7318, "start" => 5045 },
                                    "start_time" => "2023-06-15 10:04:18 +0000",
                                    "status" => "passed" } }

      xctest_log_split_manager.process_logs
      expect(JSON.parse(xctest_log_split_manager.test_details.to_json)).to eql(expected_test_details)
      expect(xctest_log_split_manager.final_test_status).to eql("SUCCEEDED")
    end

    it "should parse test details of retried test with multiple status and test iteration config" do
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_all_status_test_iteration, device, nil, false, true, true, session_id, time_components, 7200, retry_config)

      expected_test_details = { "BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch002" =>
                                { "name" => "BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch002",
                                  "duration" => 16.615,
                                  "instru_log_boundary" => { "end" => 12007, "start" => 0 },
                                  "start_time" => "2023-07-06 16:55:45 +0000",
                                  "status" => "failed" } }

      xctest_log_split_manager.process_logs
      expect(JSON.parse(xctest_log_split_manager.test_details.to_json)).to eql(expected_test_details)
      expect(xctest_log_split_manager.final_test_status).to eql("FAILED")
    end

    it "should mark status as passed for retried test with mix status that doens't have final test status" do
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_mix_status_missing_final_status, device, nil, false, true, true, session_id, time_components, 7200, retry_config)

      expected_test_details = { "BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch002" =>
                                { "name" => "BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch002",
                                  "duration" => 16.615,
                                  "instru_log_boundary" => { "end" => 11981, "start" => 0 },
                                  "start_time" => "2023-07-06 16:55:45 +0000",
                                  "status" => "failed" } }

      xctest_log_split_manager.process_logs
      expect(JSON.parse(xctest_log_split_manager.test_details.to_json)).to eql(expected_test_details)
      expect(xctest_log_split_manager.final_test_status).to eql nil
    end

    it "should parse test details of retried success tests and test iteration config" do
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_success_test_iteration, device, nil, false, true, true, session_id, time_components, 7200, retry_config)

      expected_test_details = { "BullsEyeUITests.AlwaysPassingTest testGameStyleSwitch" =>
                                { "name" => "BullsEyeUITests.AlwaysPassingTest testGameStyleSwitch",
                                  "duration" => 16.23,
                                  "instru_log_boundary" => { "end" => 9252, "start" => 0 },
                                  "start_time" => "2023-07-06 16:54:14 +0000",
                                  "status" => "passed" } }

      xctest_log_split_manager.process_logs
      expect(JSON.parse(xctest_log_split_manager.test_details.to_json)).to eql(expected_test_details)
      expect(xctest_log_split_manager.final_test_status).to eql("SUCCEEDED")
    end

    it "should parse test details of retried failure tests and test iteration config" do
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_failure_test_iteration, device, nil, false, true, true, session_id, time_components, 7200, retry_config)

      expected_test_details = { "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch" =>
                                { "name" => "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch",
                                  "duration" => 12.753,
                                  "instru_log_boundary" => { "end" => 7519, "start" => 0 },
                                  "start_time" => "2023-07-06 16:54:43 +0000",
                                  "status" => "failed" } }

      xctest_log_split_manager.process_logs
      expect(JSON.parse(xctest_log_split_manager.test_details.to_json)).to eql(expected_test_details)
      expect(xctest_log_split_manager.final_test_status).to eql("FAILED")
    end

    it "should handle test with no status and duration" do
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_no_status_tests, device, nil, false, true, true, session_id, time_components, 7200, retry_config)

      expected_test_details = { "BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch002" =>
                                  { "name" => "BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch002",
                                    "duration" => 3.24,
                                    "instru_log_boundary" => { "end" => 3410, "start" => 0 },
                                    "start_time" => "2023-07-09 15:20:48 +0000",
                                    "status" => nil },
                                "BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch004" =>
                                  { "name" => "BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch004",
                                    "duration" => 6.655,
                                    "instru_log_boundary" => { "end" => 7038, "start" => 3410 },
                                    "start_time" => "2023-07-09 15:20:51 +0000",
                                    "status" => "failed" },
                                "BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch029" =>
                                  { "name" => "BullsEyeUITests.RandomlyFailingTest testGameStyleSwitch029",
                                    "duration" => nil,
                                    "instru_log_boundary" => { "end" => 11139, "start" => 7038 },
                                    "start_time" => "2023-07-09 15:23:00 +0000",
                                    "status" => nil } }

      xctest_log_split_manager.process_logs
      expect(JSON.parse(xctest_log_split_manager.test_details.to_json)).to eql(expected_test_details)
      expect(xctest_log_split_manager.final_test_status).to eql("FAILED")
    end

    it "should handle consecutive test with no end of first test and no start of next test" do
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_no_status_no_start_tests, device, nil, false, true, true, session_id, time_components, 7200, retry_config)

      expected_test_details = { "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch" => { "duration" => 1.696, "instru_log_boundary" => { "end" => 2530, "start" => 0 }, "name" => "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch", "start_time" => "2023-07-06 16:54:43 +0000", "status" => "failed" },
                                "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch01" => { "duration" => 1.66, "instru_log_boundary" => { "end" => 3463, "start" => 2530 }, "name" => "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch01", "start_time" => "2023-07-06 16:54:45 +0000", "status" => "failed" },
                                "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch02" => { "duration" => nil, "instru_log_boundary" => { "end" => 5042, "start" => 3463 }, "name" => "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch02", "start_time" => "2023-07-06 16:54:48 +0000", "status" => nil },
                                "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch03" => { "duration" => 2.755, "instru_log_boundary" => { "end" => 5137, "start" => 3463 }, "name" => "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch03", "start_time" => "2023-07-06 16:54:47 +0000", "status" => "failed" },
                                "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch04" => { "duration" => 300.0, "instru_log_boundary" => { "end" => 5976, "start" => 5137 }, "name" => "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch04", "start_time" => "2023-07-06 16:54:50 +0000", "status" => nil },
                                "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch05" => { "duration" => 1.66, "instru_log_boundary" => { "end" => 6909, "start" => 5976 }, "name" => "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch05", "start_time" => "2023-07-06 16:59:50 +0000", "status" => "failed" },
                                "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch06" => { "duration" => 1.66, "instru_log_boundary" => { "end" => 7786, "start" => 6909 }, "name" => "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch06", "start_time" => "2023-07-06 16:59:52 +0000", "status" => "failed" },
                                "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch07" => { "duration" => 1.66, "instru_log_boundary" => { "end" => 9739, "start" => 7786 }, "name" => "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch07", "start_time" => "2023-07-06 16:59:53 +0000", "status" => "failed" } }
      expected_errors = {
        "test_start_missing" => [
                            "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch",
                            "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch03",
                            "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch07"
                          ],
        "prev_test_start_time_missing" => [
                            "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch01",
                            "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch04"
                          ],
        "prev_test_start_end_mismatch" => [
                            "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch03"
                          ],
        "prev_test_status_missing" => [
                            "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch02",
                            "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch04"
                          ],
        "prev_test_duration_missing" => [
                            "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch05"
                          ],
        "test_start_time_missing" => [
                            "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch06",
                            "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch07"
                          ],
        "test_validation_failed" => [
                            "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch02",
                            "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch04"
                          ],
        "test_instru_log_start_bound_missing" => [
                            "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch",
                            "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch03",
                            "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch07"
                          ]
      }

      xctest_log_split_manager.process_logs
      expect(JSON.parse(xctest_log_split_manager.log_split_errors.to_json)).to eql(expected_errors)
      expect(JSON.parse(xctest_log_split_manager.test_details.to_json)).to eql(expected_test_details)
      expect(xctest_log_split_manager.final_test_status).to eql("FAILED")
    end

    it "should parse test details of single test with no status" do
      xctest_log_split_manager = BrowserStack::XCTestLogSplitManager.new(xctestrun_no_status_single_test, device, nil, false, true, true, session_id, time_components, 7200, retry_config)
      expected_test_details = { "SkrillPayments_UITests.LoyaltyRedeemTests testRedeemSweepstake" => { "name" => "SkrillPayments_UITests.LoyaltyRedeemTests testRedeemSweepstake", "duration" => nil, "start_time" => "2023-07-31 01:45:31 +0000", "status" => nil, "instru_log_boundary" => { "start" => 0, "end" => 3200 } } }
      expected_log_split_errors = { "prev_test_status_missing" => ["SkrillPayments_UITests.LoyaltyRedeemTests testRedeemSweepstake"], "test_validation_failed" => ["SkrillPayments_UITests.LoyaltyRedeemTests testRedeemSweepstake"] }
      expected_log_split_failures = { "instru_log_parse_failures" => { "duration" => 1, "status" => 1 } }

      begin
        xctest_log_split_manager.process_logs
      rescue TestsValidationFailed => e
        expect(xctest_log_split_manager.test_details_valid).to eql(false)
      end

      expect(JSON.parse(xctest_log_split_manager.test_details.to_json)).to eql(expected_test_details)
      expect(JSON.parse(xctest_log_split_manager.log_split_errors.to_json)).to eql(expected_log_split_errors)
      expect(JSON.parse(xctest_log_split_manager.log_split_failures.to_json)).to eql(expected_log_split_failures)
      expect(xctest_log_split_manager.final_test_status).to eql("FAILED")
    end
  end
end
