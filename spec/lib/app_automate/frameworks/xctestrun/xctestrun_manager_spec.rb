require_relative '../../../../spec_helper'
require_relative '../../../../../lib/utils/utils'
require_relative '../../../../../lib/utils/osutils'
require_relative '../../../../../lib/app_automate/frameworks/xctestrun/xctestrun_manager'

describe BrowserStack::XCTestrunManager do
  let(:xctestrun_manager) { BrowserStack::XCTestrunManager }

  before(:each) do
    allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).and_call_original
  end

  context 'for iOS <= 16' do
    it 'generates xctestrun for testing' do
      stub_const("BrowserStack::XCTestrunManager::MAX_CONFIGS", 1)
      stub_const("BrowserStack::XCTestrunManager::MAX_TARGETS", 1)
      stub_const("BrowserStack::XCTestrunManager::MAX_CODE_COVERAGE_BUNDLES", 1)
      allow(BrowserStack::OSUtils).to receive(:execute)
      allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).and_return('', 'val1', 'val2', '2', 'val3', '', '', 'val4', '') # First call is for format version
      allow(xctestrun_manager).to receive(:replace_test_bundle_path)
      allow(xctestrun_manager).to receive(:add_ui_target_bundle_id)

      expect(BrowserStack::PlistBuddy).to receive(:get_value_of_key).exactly(9).times
      expect(BrowserStack::PlistBuddy).to receive(:delete_key_in_plist).exactly(4).times
      expect(BrowserStack::PlistBuddy).to receive(:add_key_value_in_plist)
      _, retry_strategy = xctestrun_manager.generate_xctestrun_for_testing('randomDevice', 'randombundleid', 16, 'randomapppath', 'randomtestapppath')
      expect(retry_strategy).to eq('2')
    end

    it 'loops on all config available' do
      stub_const("BrowserStack::XCTestrunManager::MAX_TARGETS", 1)
      stub_const("BrowserStack::XCTestrunManager::MAX_CODE_COVERAGE_BUNDLES", 1)
      allow(BrowserStack::OSUtils).to receive(:execute)
      allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).and_return('', 'val1', 'val2', '3', '', '', '') # First call is for format version
      allow(xctestrun_manager).to receive(:replace_test_bundle_path)
      allow(xctestrun_manager).to receive(:add_ui_target_bundle_id)

      expect(BrowserStack::PlistBuddy).to receive(:get_value_of_key).exactly(7).times
      expect(BrowserStack::PlistBuddy).to receive(:delete_key_in_plist).exactly(4).times
      expect(BrowserStack::PlistBuddy).to receive(:add_key_value_in_plist)
      _, retry_strategy = xctestrun_manager.generate_xctestrun_for_testing('randomDevice', 'randombundleid', 16, 'randomapppath', 'randomtestapppath')
      expect(retry_strategy).to eq('3')
    end

    it 'loops on all targets available' do
      stub_const("BrowserStack::XCTestrunManager::MAX_CONFIGS", 1)
      stub_const("BrowserStack::XCTestrunManager::MAX_CODE_COVERAGE_BUNDLES", 1)
      allow(BrowserStack::OSUtils).to receive(:execute)
      allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).and_return('', 'val1', 'val2', '1', '', '', '') # First call is for format version
      allow(xctestrun_manager).to receive(:replace_test_bundle_path)
      allow(xctestrun_manager).to receive(:add_ui_target_bundle_id)

      expect(BrowserStack::PlistBuddy).to receive(:get_value_of_key).exactly(7).times
      expect(BrowserStack::PlistBuddy).to receive(:delete_key_in_plist).exactly(4).times
      expect(BrowserStack::PlistBuddy).to receive(:add_key_value_in_plist)
      _, retry_strategy = xctestrun_manager.generate_xctestrun_for_testing('randomDevice', 'randombundleid', 16, 'randomapppath', 'randomtestapppath')
      expect(retry_strategy).to eq('1')
    end

    it 'loops on all config and targets available' do
      allow(BrowserStack::OSUtils).to receive(:execute)
      allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).and_return('', 'val1', 'val2', '2', '', '', 'val3', 'val4', '', '', 'val5', '/abc/randomapppath.app', '', '') # First call is for format version
      allow(xctestrun_manager).to receive(:replace_test_bundle_path)
      allow(xctestrun_manager).to receive(:add_ui_target_bundle_id)

      expect(BrowserStack::PlistBuddy).to receive(:get_value_of_key).exactly(14).times
      expect(BrowserStack::PlistBuddy).to receive(:delete_key_in_plist).exactly(5).times
      expect(BrowserStack::PlistBuddy).to receive(:add_key_value_in_plist)
      expect(BrowserStack::PlistBuddy).to receive(:set_key_in_plist)
      _, retry_strategy = xctestrun_manager.generate_xctestrun_for_testing('randomDevice', 'randombundleid', 16, '/tmp/randomapppath.app', 'randomtestapppath')
      expect(retry_strategy).to eq('2')
    end

    it 'replaces the test bundle path' do
      allow(BrowserStack::OSUtils).to receive(:execute)
      allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).and_return('val1')

      expect(BrowserStack::PlistBuddy).to receive(:get_value_of_key)
      expect(BrowserStack::PlistBuddy).to receive(:add_key_value_in_plist)
      expect(BrowserStack::PlistBuddy).to receive(:delete_key_in_plist)
      xctestrun_manager.replace_test_bundle_path('randomFilePath', 'randomKey')
    end

    it 'adds ui target bundle id' do
      allow(BrowserStack::OSUtils).to receive(:execute)
      allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).and_return('val1')

      expect(BrowserStack::PlistBuddy).to receive(:add_key_value_in_plist)
      xctestrun_manager.add_ui_target_bundle_id('randomFilePath', 'randomKey', 'randombundleid')
    end
  end

  context 'for iOS >= 17' do
    context "and non unit tests target" do
      before(:each) do
        allow(described_class).to receive(:unit_tests_target?).and_return(false)
      end

      after(:each) do
        allow(described_class).to receive(:unit_tests_target?).and_call_original
      end

      it 'generates xctestrun for testing' do
        stub_const("BrowserStack::XCTestrunManager::MAX_CONFIGS", 1)
        stub_const("BrowserStack::XCTestrunManager::MAX_TARGETS", 1)
        stub_const("BrowserStack::XCTestrunManager::MAX_CODE_COVERAGE_BUNDLES", 1)
        allow(BrowserStack::OSUtils).to receive(:execute)
        allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).and_return('', 'val1', 'val2', '', 'val1', 'val2', '2', 'val1', 'val2', 'val3', 'val1', '/abc/randomapppath.app') # First call is for format version

        expect(BrowserStack::PlistBuddy).to receive(:get_value_of_key).exactly(12).times
        expect(BrowserStack::PlistBuddy).to receive(:delete_key_in_plist).exactly(6).times
        expect(BrowserStack::PlistBuddy).to receive(:add_key_value_in_plist).exactly(3).times
        expect(BrowserStack::PlistBuddy).to receive(:set_key_in_plist)
        _, retry_strategy = xctestrun_manager.generate_xctestrun_for_testing('randomDevice', 'randombundleid', 17, 'randomapppath', '/tmp/randomtestapppath.app')
        expect(retry_strategy).to eq('2')
      end

      it 'loops on all config available' do
        stub_const("BrowserStack::XCTestrunManager::MAX_TARGETS", 1)
        stub_const("BrowserStack::XCTestrunManager::MAX_CODE_COVERAGE_BUNDLES", 1)
        allow(BrowserStack::OSUtils).to receive(:execute)
        allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).and_return('', 'val1', 'val2', '', '', 'val1', 'val2', '3', '', '') # First call is for format version

        expect(BrowserStack::PlistBuddy).to receive(:get_value_of_key).exactly(11).times
        expect(BrowserStack::PlistBuddy).to receive(:delete_key_in_plist).exactly(4).times
        expect(BrowserStack::PlistBuddy).to receive(:add_key_value_in_plist).exactly(3).times
        _, retry_strategy = xctestrun_manager.generate_xctestrun_for_testing('randomDevice', 'randombundleid', 17, 'randomapppath', 'randomtestapppath')
        expect(retry_strategy).to eq('3')
      end

      it 'loops on all targets available' do
        stub_const("BrowserStack::XCTestrunManager::MAX_CONFIGS", 1)
        stub_const("BrowserStack::XCTestrunManager::MAX_CODE_COVERAGE_BUNDLES", 1)
        allow(BrowserStack::OSUtils).to receive(:execute)
        allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).and_return('', 'val1', 'val2', '', 'val1', 'val2', '1', '', '') # First call is for format version

        expect(BrowserStack::PlistBuddy).to receive(:get_value_of_key).exactly(10).times
        expect(BrowserStack::PlistBuddy).to receive(:delete_key_in_plist).exactly(4).times
        expect(BrowserStack::PlistBuddy).to receive(:add_key_value_in_plist).exactly(3).times
        _, retry_strategy = xctestrun_manager.generate_xctestrun_for_testing('randomDevice', 'randombundleid', 17, 'randomapppath', 'randomtestapppath')
        expect(retry_strategy).to eq('1')
      end

      it 'loops on all config and targets available' do
        allow(BrowserStack::OSUtils).to receive(:execute)
        allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).and_return('', 'val1', 'val2', '', '', 'val1', 'val2', '2', '', '', 'val1', 'val2', 'val3', '', 'val1', '/abc/randomapppath.app', '', '') # First call is for format version

        expect(BrowserStack::PlistBuddy).to receive(:get_value_of_key).exactly(18).times
        expect(BrowserStack::PlistBuddy).to receive(:delete_key_in_plist).exactly(6).times
        expect(BrowserStack::PlistBuddy).to receive(:add_key_value_in_plist).exactly(3).times
        expect(BrowserStack::PlistBuddy).to receive(:set_key_in_plist)
        _, retry_strategy = xctestrun_manager.generate_xctestrun_for_testing('randomDevice', 'randombundleid', 17, '/tmp/randomapppath.app', 'randomtestapppath')
        expect(retry_strategy).to eq('2')
      end

      it 'generates xctestrun for testing when useDestinationArtifacts is present' do
        stub_const("BrowserStack::XCTestrunManager::MAX_CONFIGS", 1)
        stub_const("BrowserStack::XCTestrunManager::MAX_TARGETS", 1)
        stub_const("BrowserStack::XCTestrunManager::MAX_CODE_COVERAGE_BUNDLES", 1)
        allow(BrowserStack::OSUtils).to receive(:execute)
        allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).and_return('', 'val1', 'val2', 'true', 'val3', 'val1', 'val2', '2', 'val1', 'val2', '', 'val1', '') # First call is for format version

        expect(BrowserStack::PlistBuddy).to receive(:get_value_of_key).exactly(13).times
        expect(BrowserStack::PlistBuddy).to receive(:delete_key_in_plist).exactly(8).times
        expect(BrowserStack::PlistBuddy).to receive(:add_key_value_in_plist).exactly(4).times
        _, retry_strategy = xctestrun_manager.generate_xctestrun_for_testing('randomDevice', 'randombundleid', 17, 'randomapppath', 'randomtestapppath')
        expect(retry_strategy).to eq('2')
      end

      it 'loops on all config and targets available when useDestinationArtifacts is present' do
        allow(BrowserStack::OSUtils).to receive(:execute)
        allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).and_return('', 'val1', 'val2', 'true', 'usedestvalue', '', '', 'val1', 'val2', '2', '', '', 'val1', 'val2', 'val3', '', 'val1', '/abc/randomtestapppath.app', '', '') # First call is for format version

        expect(BrowserStack::PlistBuddy).to receive(:get_value_of_key).exactly(20).times
        expect(BrowserStack::PlistBuddy).to receive(:delete_key_in_plist).exactly(9).times
        expect(BrowserStack::PlistBuddy).to receive(:add_key_value_in_plist).exactly(4).times
        expect(BrowserStack::PlistBuddy).to receive(:set_key_in_plist)
        _, retry_strategy = xctestrun_manager.generate_xctestrun_for_testing('randomDevice', 'randombundleid', 17, 'randomapppath', '/tmp/randomtestapppath.app')
        expect(retry_strategy).to eq('2')
      end
    end
  end

  # V1 XCTestrun tests
  context 'for V1 XCTestrun' do
    it 'should call handle_v1_xctestrun if format version is 1' do
      allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).and_return('1')
      expect(xctestrun_manager).to receive(:handle_v1_xctestrun)
      xctestrun_manager.generate_xctestrun_for_testing('randomDevice', 'randombundleid', 17, 'randomapppath', '/tmp/randomtestapppath.app')
    end

    it 'should correctly handle v1 xctestrun' do
      file_path = 'some_file_path'
      app_path = 'some_app_path'
      test_runner_path = 'some_test_runner_path'
      test_target_key = "TestTarget1"
      # Mock the plutil and jq command to return an array of keys
      allow_any_instance_of(Object).to receive(:`).with(/plutil -convert json -o - "some_file_path".*jq/).and_return("TestTarget1\n")
      test_bundle_path = "some_test_bundle_path"
      allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).with(file_path, "#{test_target_key}:IsAppHostedTestBundle").and_return("true")
      allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).with(file_path, "#{test_target_key}:TestBundlePath").and_return(test_bundle_path)
      allow(File).to receive(:join).and_return("joined_path")
      allow(File).to receive(:dirname).and_return("dirname")
      allow(File).to receive(:basename).and_return("basename")

      expect(BrowserStack::PlistBuddy).to receive(:set_key_in_plist).with(file_path, "#{test_target_key}:TestBundlePath", "joined_path")
      expect(BrowserStack::PlistBuddy).to receive(:delete_key_in_plist).with(file_path, "#{test_target_key}:DependentProductPaths")
      expect(BrowserStack::PlistBuddy).to receive(:delete_key_in_plist).with(file_path, "#{test_target_key}:TestHostPath")
      expect(xctestrun_manager).to receive(:add_test_host_path).with(file_path, test_target_key, test_runner_path, app_path)
      expect(xctestrun_manager).to receive(:add_preffered_screen_capture_format).with(file_path, test_target_key, "screenshots")

      xctestrun_manager.handle_v1_xctestrun(file_path, app_path, test_runner_path)
    end

    it 'should skip non-test targets in v1 xctestrun' do
      file_path = 'some_file_path'
      app_path = 'some_app_path'
      test_runner_path = 'some_test_runner_path'
      test_target_key = "NonTestTarget"
      # Mock the plutil and jq command to return an array of keys, but here the target won't have `IsAppHostedTestBundle` set to true
      allow_any_instance_of(Object).to receive(:`).with(/plutil -convert json -o - "some_file_path".*jq/).and_return("NonTestTarget\n")
      allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).with(file_path, "#{test_target_key}:IsAppHostedTestBundle").and_return("")

      expect(BrowserStack::PlistBuddy).not_to receive(:set_key_in_plist)
      expect(BrowserStack::PlistBuddy).not_to receive(:delete_key_in_plist)
      expect(xctestrun_manager).not_to receive(:add_test_host_path)
      expect(xctestrun_manager).not_to receive(:add_preffered_screen_capture_format)

      xctestrun_manager.handle_v1_xctestrun(file_path, app_path, test_runner_path)
    end

    it 'should handle multiple test targets in v1 xctestrun' do
      file_path = 'some_file_path'
      app_path = 'some_app_path'
      test_runner_path = 'some_test_runner_path'
      test_target_key1 = "TestTarget1"
      test_target_key2 = "TestTarget2"
      root_keys_output = "<key>#{test_target_key1}</key><key>#{test_target_key2}</key><key>__xctestrun_metadata__</key>"
      test_bundle_path = "some_test_bundle_path"

      # Mock the plutil and jq command to return an array of keys
      allow_any_instance_of(Object).to receive(:`).with(/plutil -convert json -o - "some_file_path".*jq/).and_return("TestTarget1\nTestTarget2\n")

      allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).with(file_path, "#{test_target_key1}:IsAppHostedTestBundle").and_return("true")
      allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).with(file_path, "#{test_target_key2}:IsAppHostedTestBundle").and_return("true")
      allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).with(file_path, "#{test_target_key1}:TestBundlePath").and_return(test_bundle_path)
      allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).with(file_path, "#{test_target_key2}:TestBundlePath").and_return(test_bundle_path)
      allow(File).to receive(:join).and_return("joined_path")
      allow(File).to receive(:dirname).and_return("dirname")
      allow(File).to receive(:basename).and_return("basename")

      # Expect calls only for the first target, as handle_v1_xctestrun breaks after processing the first target
      expect(BrowserStack::PlistBuddy).to receive(:set_key_in_plist).with(file_path, "#{test_target_key1}:TestBundlePath", "joined_path")
      expect(BrowserStack::PlistBuddy).to receive(:delete_key_in_plist).with(file_path, "#{test_target_key1}:DependentProductPaths")
      expect(BrowserStack::PlistBuddy).to receive(:delete_key_in_plist).with(file_path, "#{test_target_key1}:TestHostPath")
      expect(xctestrun_manager).to receive(:add_test_host_path).with(file_path, test_target_key1, test_runner_path, app_path)
      expect(xctestrun_manager).to receive(:add_preffered_screen_capture_format).with(file_path, test_target_key1, "screenshots")

      xctestrun_manager.handle_v1_xctestrun(file_path, app_path, test_runner_path)
    end
  end

  describe '.unit_tests_target?' do
    let(:file_path) { 'path/to/file.plist' }
    let(:test_target_key) { 'TestTargetKey' }
    let(:key_path) { "#{test_target_key}:IsAppHostedTestBundle" }

    context 'when PlistBuddy returns "true"' do
      it 'returns true' do
        allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).with(file_path, key_path).and_return(" true \n")
        result = described_class.unit_tests_target?(file_path, test_target_key)
        expect(result).to be true
      end
    end

    context 'when PlistBuddy returns "false"' do
      it 'returns false' do
        allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).with(file_path, key_path).and_return(" false \n")
        result = described_class.unit_tests_target?(file_path, test_target_key)
        expect(result).to be false
      end
    end

    context 'when PlistBuddy returns nil' do
      it 'returns false' do
        allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).with(file_path, key_path).and_return("")
        result = described_class.unit_tests_target?(file_path, test_target_key)
        expect(result).to be false
      end
    end
  end

  describe 'update_xctestrun_with_enviromentvariable' do
    it 'updating xctestrun with format V2 for testing' do
      stub_const("BrowserStack::XCTestrunManager::MAX_CONFIGS", 1)
      stub_const("BrowserStack::XCTestrunManager::MAX_TARGETS", 1)
      stub_const("BrowserStack::XCTestrunManager::MAX_CODE_COVERAGE_BUNDLES", 1)
      allow(BrowserStack::OSUtils).to receive(:execute)
      allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).and_return('2', "[]") # First call is for format version

      expect(BrowserStack::PlistBuddy).to receive(:get_value_of_key).exactly(3).times
      expect(BrowserStack::PlistBuddy).to receive(:add_key_value_in_plist).twice
      xctestrun_manager.update_xctestrun_with_enviromentvariable('randomDevice')
    end

    it 'pdating xctestrun with format V1 for testing' do
      stub_const("BrowserStack::XCTestrunManager::MAX_CONFIGS", 1)
      stub_const("BrowserStack::XCTestrunManager::MAX_TARGETS", 1)
      stub_const("BrowserStack::XCTestrunManager::MAX_CODE_COVERAGE_BUNDLES", 1)
      allow(BrowserStack::OSUtils).to receive(:execute)
      allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).and_return('1', "[]") # First call is for format version
      allow_any_instance_of(Kernel).to receive(:`).and_return('randomTest')

      expect(BrowserStack::PlistBuddy).to receive(:add_key_value_in_plist).twice
      xctestrun_manager.update_xctestrun_with_enviromentvariable('randomDevice')
    end
  end
end
