require_relative '../../../../spec_helper'
require_relative '../../../../../lib/utils/utils'
require_relative '../../../../../lib/app_automate/frameworks/xctestrun/xctestrun_summary'

describe BrowserStack::XCTestrunSummary do
  let!(:summary_manager) { BrowserStack::XCTestrunSummary.new({}, {}) }
  let(:class_name) { BrowserStack::XCTestrunSummary::DEFAULT_CLASS_NAME }
  let(:test_name) { BrowserStack::XCTestrunSummary::DEFAULT_TEST_NAME }
  let(:test_status) { BrowserStack::XCTestrunSummary::DEFAULT_TEST_STATUS }
  let(:build_id) { "12345" }
  let(:test_id) { "test_id" }
  let(:session_id) { "54321" }
  let(:device_name) { "iPhone 13 mini" }
  let(:failed_tests_statuses) do
    {
      "total" => 1,
      'passed' => 0,
      'failed' => 1,
      'skipped' => 0,
      'timedout' => 0,
      'error' => 0,
      'running' => 0,
      'queued' => 0
    }
  end
  let(:passed_tests_statuses) do
    {
      "total" => 1,
      'passed' => 1,
      'failed' => 0,
      'skipped' => 0,
      'timedout' => 0,
      'error' => 0,
      'running' => 0,
      'queued' => 0
    }
  end
  let(:running_tests_statuses) do
    {
      "total" => 1,
      'passed' => 0,
      'failed' => 0,
      'skipped' => 0,
      'timedout' => 0,
      'error' => 0,
      'running' => 1,
      'queued' => 0
    }
  end
  let(:test_states) do
    {
      "total" => 0,
      'passed' => 0,
      'failed' => 0,
      'skipped' => 0,
      'timedout' => 0,
      'error' => 0,
      'running' => 0,
      'queued' => 0
    }
  end
  let!(:basic_structure) do
    {
      build_id: build_id,
      session_id: session_id,
      test_summary: test_states,
      classes: {
        class_name.to_s => {
          name: class_name,
          tests_summary: test_states,
          tests: {
            test_name.to_s => {
              name: test_name,
              start_time: '',
              status: test_status,
              test_id: test_id,
              duration: '',
              video: '',
              class: class_name
            }
          }
        }
      }
    }
  end

  let(:build_message) do
    {
      build_id: build_id,
      device: device_name,
      session_id: session_id,
      testlist: []
    }
  end

  context "update_test_summary_v2" do
    let(:summary_file_path) { "summary_file_path" }
    let(:time_components) do
      {
        xcodebuild: { start: Time.at(0), end: Time.at(10) },
        video: { start_duration: 0, end_duration: 11 }
      }
    end

    before(:each) do
      summary_manager.instance_variable_set("@summary_file_v2", summary_file_path)
    end

    context "when summary data contains test object" do
      before(:each) do
        basic_structure[:test_summary] = running_tests_statuses
        basic_structure[:classes][class_name][:tests_summary] = running_tests_statuses
        expect(File).to receive(:read).with(summary_file_path).and_return(basic_structure.to_json)
      end

      it "should update summary file v2 with test output" do
        expect(summary_manager).to receive(:video_enabled?).and_return(true)

        expected_summary = JSON.parse(basic_structure.to_json)
        expected_summary['classes'][class_name]['tests'][test_name].merge!({
          status: "failed",
          start_time: Time.at(0),
          name: "3 Failures",
          duration: 10.0,
          crash_logs_count: 0,
          video: "0,11"
        })
        expected_summary['duration'] = 10.0
        expected_summary['test_summary'] = failed_tests_statuses
        expected_summary['classes'][class_name]['tests_summary'] = failed_tests_statuses
        expect(Utils).to receive(:write_to_file).with(summary_file_path, expected_summary.to_json).and_return(true)

        result = summary_manager.update_test_summary_v2(time_components, "failed", 3, 0)

        expect(result).to eql(failed_tests_statuses)
      end

      it "should update summary file v2 with video disabled" do
        expect(summary_manager).to receive(:video_enabled?).and_return(false)

        expected_summary = JSON.parse(basic_structure.to_json)
        expected_summary['classes'][class_name]['tests'][test_name].merge!({
          status: "passed",
          start_time: Time.at(0),
          name: "0 Failures",
          duration: 10.0,
          crash_logs_count: 0
        })
        expected_summary['duration'] = 10.0
        expected_summary['test_summary'] = passed_tests_statuses
        expected_summary['classes'][class_name]['tests_summary'] = passed_tests_statuses
        expect(Utils).to receive(:write_to_file).with(summary_file_path, expected_summary.to_json).and_return(true)

        result = summary_manager.update_test_summary_v2(time_components, "passed", 0, 0)

        expect(result).to eql(passed_tests_statuses)
      end
    end

    it "should not update test object for malformed summary data" do
      expect(File).to receive(:read).with(summary_file_path).and_return({}.to_json)
      expect(Utils).to receive(:write_to_file).with(summary_file_path, { duration: 10.0 }.to_json).and_return(true)

      result = summary_manager.update_test_summary_v2(time_components, "passed", 0, 0)

      expect(result).to eql(nil)
    end
  end

  context "setup_summary_v2" do
    it "should write to summary file with basic structure" do
      basic_structure[:classes][class_name][:tests_summary]["total"] = 1
      basic_structure[:classes][class_name][:tests_summary]["running"] = 1
      basic_structure[:test_summary]["total"] = 1
      basic_structure[:test_summary]["running"] = 1
      expect(Utils).to receive(:write_to_file).with(anything, basic_structure.to_json)

      summary_manager.setup_summary_v2(build_id, session_id, test_id)
    end
  end

  context "send_setup_build_pusher_event" do
    it "should notify pusher with setup_build event" do
      expect(summary_manager).to receive(:notify_pusher).with("setup_build", build_message.to_json).and_return(true)

      summary_manager.send_setup_build_pusher_event(build_id, session_id, device_name)
    end
  end

  context "update_session_stats" do
    it "should push session stats to zombie" do
      test_states["failed"] = 1
      session_stats = {
        sessionid: session_id,
        test_failed: 1,
        test_success: 0,
        test_ignored: 0,
        test_timedout: 0
      }
      expect(BrowserStack::Zombie).to receive(:push_logs).with("app_automation_session_stats", "", session_stats).and_return(true)

      summary_manager.update_session_stats(session_id, test_states)
    end

    it "should not process if test summary is not present" do
      expect(BrowserStack::Zombie).to_not receive(:push_logs)

      summary_manager.update_session_stats(session_id, nil)
    end
  end

  context "update_callback_file" do
    let(:callback_file) { "callback_file_path" }

    it "should update callback file with test status" do
      test_summary = test_states
      test_summary.delete("total")
      expected_test_summary = { "test_status" => test_summary }
      expect(Utils).to receive(:read_json_file).with(callback_file).and_return({})
      expect(Utils).to receive(:write_to_file).with(callback_file, expected_test_summary.to_json).and_return(true)

      summary_manager.update_callback_file(callback_file, test_states)
    end

    it "should not read json file if test summary is not present" do
      expect(Utils).to_not receive(:read_json_file)

      summary_manager.update_callback_file(callback_file, nil)
    end
  end

  context "test_duration" do
    it "should return time difference" do
      result = summary_manager.send(:test_duration, Time.at(10), Time.at(0))

      expect(result).to eql(10.0)
    end

    it "should return 0 if any time component is nil" do
      result = summary_manager.send(:test_duration, Time.at(0), nil)

      expect(result).to eql(0)
    end
  end

  context "log split summary" do
    let(:time_components) do
      {
        xcodebuild: { start: Time.at(0), end: Time.at(10) },
        video: { start_duration: 0, end_duration: 11 }
      }
    end

    it "should generate summary using logsplit data" do
      log_split_test_details = { "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch" => { "duration" => 1.7, "instru_log_boundary" => { "end" => 2530, "start" => 2437 }, "device_log_boundary" => { "start" => 751, "end" => 837 }, "video_log_boundary" => { "start" => 0, "end" => 11 }, "name" => "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch", "start_time" => "2023-07-06 16:54:43 +0000", "status" => "failed" },
                                 "BullsEyeUITests.AlwaysPassingTest testGameStyleSwitch01" => { "duration" => 1.66, "instru_log_boundary" => { "end" => 3463, "start" => 2530 }, "device_log_boundary" => { "start" => 751, "end" => 837 }, "video_log_boundary" => { "start" => 0, "end" => 11 }, "name" => "BullsEyeUITests.AlwaysPassingTest testGameStyleSwitch01", "start_time" => "2023-07-06 16:54:45 +0000", "status" => "failed" },
                                 "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch02" => { "duration" => nil, "instru_log_boundary" => { "end" => nil, "start" => nil }, "device_log_boundary" => { "start" => 751, "end" => 837 }, "video_log_boundary" => { "start" => 0, "end" => 11 }, "name" => "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch02", "start_time" => "2023-07-06 16:54:48 +0000", "status" => nil },
                                 "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch03" => { "duration" => 2.76, "instru_log_boundary" => { "end" => 5137, "start" => 5042 }, "device_log_boundary" => { "start" => 751, "end" => 837 }, "video_log_boundary" => { "start" => 0, "end" => 11 }, "name" => "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch03", "start_time" => "2023-07-06 16:54:47 +0000", "status" => "failed" },
                                 "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch04" => { "duration" => 300.0, "instru_log_boundary" => { "end" => nil, "start" => nil }, "device_log_boundary" => { "start" => 751, "end" => 837 }, "video_log_boundary" => { "start" => 0, "end" => 11 }, "name" => "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch04", "start_time" => "2023-07-06 16:54:50 +0000", "status" => nil },
                                 "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch05" => { "duration" => 1.66, "instru_log_boundary" => { "end" => 6909, "start" => 5976 }, "device_log_boundary" => { "start" => 751, "end" => 837 }, "video_log_boundary" => { "start" => 0, "end" => 11 }, "name" => "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch05", "start_time" => "2023-07-06 16:59:50 +0000", "status" => "failed" },
                                 "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch06" => { "duration" => 1.66, "instru_log_boundary" => { "end" => 7786, "start" => 6909 }, "device_log_boundary" => { "start" => 751, "end" => 837 }, "video_log_boundary" => { "start" => 0, "end" => 11 }, "name" => "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch06", "start_time" => "2023-07-06 16:59:52 +0000", "status" => "failed" },
                                 "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch07" => { "duration" => 1.66, "instru_log_boundary" => { "end" => 8566, "start" => 8472 }, "device_log_boundary" => { "start" => 751, "end" => 837 }, "video_log_boundary" => { "start" => 0, "end" => 11 }, "name" => "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch07", "start_time" => "2023-07-06 16:59:53 +0000", "status" => "failed" },
                                 "BullsEyeUITests.AlwaysPassingTest testGameStyleSwitch07" => { "duration" => 1.66, "instru_log_boundary" => { "end" => 8566, "start" => 8472 }, "device_log_boundary" => { "start" => 751, "end" => 837 }, "video_log_boundary" => { "start" => 0, "end" => 11 }, "name" => "BullsEyeUITests.AlwaysPassingTest testGameStyleSwitch07", "start_time" => "2023-07-06 16:59:53 +0000", "status" => "failed" } }
      expected_summary_file_data = { "build_id" => "12345",
                                     "session_id" => "54321",
                                     "test_summary" =>
         { "total" => 9,
           "passed" => 0,
           "failed" => 9,
           "skipped" => 0,
           "timedout" => 0,
           "error" => 0,
           "running" => 0,
           "queued" => 0 },
                                     "classes" =>
         { "AlwaysFailingTest" =>
           { "name" => "AlwaysFailingTest",
             "tests_summary" =>
             { "total" => 7,
               "passed" => 0,
               "failed" => 7,
               "skipped" => 0,
               "timedout" => 0,
               "error" => 0,
               "running" => 0,
               "queued" => 0 },
             "tests" =>
             { "testGameStyleSwitch" =>
               { "name" => "testGameStyleSwitch",
                 "start_time" => "2023-07-06 16:54:43 +0000",
                 "status" => "failed",
                 "test_id" => "54321f1fc3140",
                 "duration" => 1.7,
                 "video" => "0,11",
                 "instrumentation_log" => { "end" => 2530, "start" => 2437 },
                 "device_log" => { "start" => 751, "end" => 837 },
                 "network_log" => nil,
                 "class" => "AlwaysFailingTest" },
               "testGameStyleSwitch02" =>
               { "name" => "testGameStyleSwitch02",
                 "start_time" => "2023-07-06 16:54:48 +0000",
                 "status" => "failed",
                 "test_id" => "54321f9a63806",
                 "duration" => nil,
                 "video" => "0,11",
                 "instrumentation_log" => { "end" => nil, "start" => nil },
                 "device_log" => { "start" => 751, "end" => 837 },
                 "network_log" => nil,
                 "class" => "AlwaysFailingTest" },
               "testGameStyleSwitch03" =>
               { "name" => "testGameStyleSwitch03",
                 "start_time" => "2023-07-06 16:54:47 +0000",
                 "status" => "failed",
                 "test_id" => "5432190f310be",
                 "duration" => 2.76,
                 "video" => "0,11",
                 "instrumentation_log" => { "end" => 5137, "start" => 5042 },
                 "device_log" => { "start" => 751, "end" => 837 },
                 "network_log" => nil,
                 "class" => "AlwaysFailingTest" },
               "testGameStyleSwitch04" =>
               { "name" => "testGameStyleSwitch04",
                 "start_time" => "2023-07-06 16:54:50 +0000",
                 "status" => "failed",
                 "test_id" => "5432194cc7b71",
                 "duration" => 300.0,
                 "video" => "0,11",
                 "instrumentation_log" => { "end" => nil, "start" => nil },
                 "device_log" => { "start" => 751, "end" => 837 },
                 "network_log" => nil,
                 "class" => "AlwaysFailingTest" },
               "testGameStyleSwitch05" =>
               { "name" => "testGameStyleSwitch05",
                 "start_time" => "2023-07-06 16:59:50 +0000",
                 "status" => "failed",
                 "test_id" => "543217b0321c6",
                 "duration" => 1.66,
                 "video" => "0,11",
                 "instrumentation_log" => { "end" => 6909, "start" => 5976 },
                 "device_log" => { "start" => 751, "end" => 837 },
                 "network_log" => nil,
                 "class" => "AlwaysFailingTest" },
               "testGameStyleSwitch06" =>
               { "name" => "testGameStyleSwitch06",
                 "start_time" => "2023-07-06 16:59:52 +0000",
                 "status" => "failed",
                 "test_id" => "543213f65fa4f",
                 "duration" => 1.66,
                 "video" => "0,11",
                 "instrumentation_log" => { "end" => 7786, "start" => 6909 },
                 "device_log" => { "start" => 751, "end" => 837 },
                 "network_log" => nil,
                 "class" => "AlwaysFailingTest" },
               "testGameStyleSwitch07" =>
               { "name" => "testGameStyleSwitch07",
                 "start_time" => "2023-07-06 16:59:53 +0000",
                 "status" => "failed",
                 "test_id" => "543217c737799",
                 "duration" => 1.66,
                 "video" => "0,11",
                 "instrumentation_log" => { "end" => 8566, "start" => 8472 },
                 "device_log" => { "start" => 751, "end" => 837 },
                 "network_log" => nil,
                 "class" => "AlwaysFailingTest" } } },
           "AlwaysPassingTest" =>
           { "name" => "AlwaysPassingTest",
             "tests_summary" =>
             { "total" => 2,
               "passed" => 0,
               "failed" => 2,
               "skipped" => 0,
               "timedout" => 0,
               "error" => 0,
               "running" => 0,
               "queued" => 0 },
             "tests" =>
             { "testGameStyleSwitch01" =>
               { "name" => "testGameStyleSwitch01",
                 "start_time" => "2023-07-06 16:54:45 +0000",
                 "status" => "failed",
                 "test_id" => "543212892c321",
                 "duration" => 1.66,
                 "video" => "0,11",
                 "instrumentation_log" => { "end" => 3463, "start" => 2530 },
                 "device_log" => { "start" => 751, "end" => 837 },
                 "network_log" => nil,
                 "class" => "AlwaysPassingTest" },
               "testGameStyleSwitch07" =>
               { "name" => "testGameStyleSwitch07",
                 "start_time" => "2023-07-06 16:59:53 +0000",
                 "status" => "failed",
                 "test_id" => "5432164499799",
                 "duration" => 1.66,
                 "video" => "0,11",
                 "instrumentation_log" => { "end" => 8566, "start" => 8472 },
                 "device_log" => { "start" => 751, "end" => 837 },
                 "network_log" => nil,
                 "class" => "AlwaysPassingTest" } } } },
                                     "duration" => 10.0 }
      test_state, summary_file_data = summary_manager.generate_summary_log_split(log_split_test_details, build_id, session_id, time_components, Time.now - 7000, 7200)
      expect(JSON.parse(summary_file_data.to_json)).to eql(expected_summary_file_data)
      # puts (JSON.parse(summary_file_data.to_json)) == (expected_summary_file_data)
    end

    it "should mark last test as TIMEDOUT if no status of last test present and session timedout" do
      expected_summary_file_data = {
        "build_id" => "12345",
        "session_id" => "54321",
        "test_summary" =>
         { "total" => 2,
           "passed" => 0,
           "failed" => 1,
           "skipped" => 0,
           "timedout" => 1,
           "error" => 0,
           "running" => 0,
           "queued" => 0 },
        "classes" =>
         { "AlwaysFailingTest" =>
           { "name" => "AlwaysFailingTest",
             "tests_summary" =>
             { "total" => 2,
               "passed" => 0,
               "failed" => 1,
               "skipped" => 0,
               "timedout" => 1,
               "error" => 0,
               "running" => 0,
               "queued" => 0 },
             "tests" =>
             { "testGameStyleSwitch" =>
               { "name" => "testGameStyleSwitch",
                 "start_time" => "2023-07-06 16:54:43 +0000",
                 "status" => "failed",
                 "test_id" => "54321f1fc3140",
                 "duration" => 1.7,
                 "video" => nil,
                 "instrumentation_log" => { "end" => 2530, "start" => 2437 },
                 "device_log" => nil,
                 "network_log" => nil,
                 "class" => "AlwaysFailingTest" },
               "testGameStyleSwitch01" =>
               { "name" => "testGameStyleSwitch01",
                 "start_time" => "2023-07-06 16:54:45 +0000",
                 "status" => "timedout",
                 "test_id" => "543216192003f",
                 "duration" => 1.66,
                 "video" => nil,
                 "instrumentation_log" => { "end" => 3463, "start" => 2530 },
                 "device_log" => nil,
                 "network_log" => nil,
                 "class" => "AlwaysFailingTest" } } } },
        "duration" => 10.0
      }
      log_split_test_details = { "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch" => { "duration" => 1.7, "instru_log_boundary" => { "end" => 2530, "start" => 2437 }, "name" => "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch", "start_time" => "2023-07-06 16:54:43 +0000", "status" => "failed" },
                                 "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch01" => { "duration" => 1.66, "instru_log_boundary" => { "end" => 3463, "start" => 2530 }, "name" => "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch01", "start_time" => "2023-07-06 16:54:45 +0000", "status" => nil } }
      expect(summary_manager).to receive(:session_timedout?).and_return(true)
      test_state, summary_file_data = summary_manager.generate_summary_log_split(log_split_test_details, build_id, session_id, time_components, Time.now - 7000, 7200)
      expect(JSON.parse(summary_file_data.to_json)).to eql(expected_summary_file_data)
    end

    it "should not add test to summary file if failed to extract testname or classname" do
      log_split_test_details = { "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch" => { "duration" => 1.7, "instru_log_boundary" => { "end" => 2530, "start" => 2437 }, "device_log_boundary" => { "start" => 751, "end" => 837 }, "name" => "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch", "start_time" => "2023-07-06 16:54:43 +0000", "status" => "failed" },
                                 "gibberish123_gibberish" => { "duration" => 1.66, "instru_log_boundary" => { "end" => 3463, "start" => 2530 }, "device_log_boundary" => { "start" => 751, "end" => 837 }, "name" => "BullsEyeUITests.AlwaysPassingTest testGameStyleSwitch01", "start_time" => "2023-07-06 16:54:45 +0000", "status" => "failed" },
                                 "AlwaysFailingTestCopy testGameStyleSwitchCopy" => { "duration" => 1.66, "instru_log_boundary" => { "end" => 3463, "start" => 2530 }, "device_log_boundary" => { "start" => 751, "end" => 837 }, "name" => "AlwaysFailingTestCopy testGameStyleSwitchCopy", "start_time" => "2023-07-06 16:54:45 +0000", "status" => "failed" },
                                 "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch02" => { "duration" => nil, "instru_log_boundary" => { "end" => nil, "start" => nil }, "device_log_boundary" => { "start" => 751, "end" => 837 }, "name" => "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch02", "start_time" => "2023-07-06 16:54:48 +0000", "status" => nil } }
      expected_summary_file_data = {
        "build_id" => "12345",
        "session_id" => "54321",
        "test_summary" =>
         { "total" => 3,
           "passed" => 0,
           "failed" => 3,
           "skipped" => 0,
           "timedout" => 0,
           "error" => 0,
           "running" => 0,
           "queued" => 0 },
        "classes" =>
         { "AlwaysFailingTest" =>
           { "name" => "AlwaysFailingTest",
             "tests_summary" =>
             { "total" => 2,
               "passed" => 0,
               "failed" => 2,
               "skipped" => 0,
               "timedout" => 0,
               "error" => 0,
               "running" => 0,
               "queued" => 0 },
             "tests" =>
             { "testGameStyleSwitch" =>
               { "name" => "testGameStyleSwitch",
                 "start_time" => "2023-07-06 16:54:43 +0000",
                 "status" => "failed",
                 "test_id" => "54321f1fc3140",
                 "duration" => 1.7,
                 "video" => nil,
                 "instrumentation_log" => { "end" => 2530, "start" => 2437 },
                 "device_log" => { "start" => 751, "end" => 837 },
                 "network_log" => nil,
                 "class" => "AlwaysFailingTest" },
               "testGameStyleSwitch02" =>
               { "name" => "testGameStyleSwitch02",
                 "start_time" => "2023-07-06 16:54:48 +0000",
                 "status" => "failed",
                 "test_id" => "54321f9a63806",
                 "duration" => nil,
                 "video" => nil,
                 "instrumentation_log" => { "end" => nil, "start" => nil },
                 "device_log" => { "start" => 751, "end" => 837 },
                 "network_log" => nil,
                 "class" => "AlwaysFailingTest" } } } ,
           "AlwaysFailingTestCopy" => {
             "name" => "AlwaysFailingTestCopy",
             "tests_summary" => {
               "total" => 1,
               "passed" => 0,
               "failed" => 1,
               "skipped" => 0,
               "timedout" => 0,
               "error" => 0,
               "running" => 0,
               "queued" => 0
             },
             "tests" =>
                    { "testGameStyleSwitchCopy" =>
                      { "name" => "testGameStyleSwitchCopy",
                        "start_time" => "2023-07-06 16:54:45 +0000",
                        "status" => "failed",
                        "test_id" => "54321e0111f60",
                        "duration" => 1.66,
                        "video" => nil,
                        "instrumentation_log" => { "end" => 3463, "start" => 2530 },
                        "device_log" => { "start" => 751, "end" => 837 },
                        "network_log" => nil,
                        "class" => "AlwaysFailingTestCopy" } }
           } },
        "duration" => 10.0
      }
      test_state, summary_file_data = summary_manager.generate_summary_log_split(log_split_test_details, build_id, session_id, time_components, Time.now - 7000, 7200)
      expect(JSON.parse(summary_file_data.to_json)).to eql(expected_summary_file_data)
    end

    it "should mark last test as TIMEDOUT if no status of last test present and session expired" do
      expected_summary_file_data = {
        "build_id" => "12345",
        "session_id" => "54321",
        "test_summary" =>
         { "total" => 2,
           "passed" => 0,
           "failed" => 1,
           "skipped" => 0,
           "timedout" => 1,
           "error" => 0,
           "running" => 0,
           "queued" => 0 },
        "classes" =>
         { "AlwaysFailingTest" =>
           { "name" => "AlwaysFailingTest",
             "tests_summary" =>
             { "total" => 2,
               "passed" => 0,
               "failed" => 1,
               "skipped" => 0,
               "timedout" => 1,
               "error" => 0,
               "running" => 0,
               "queued" => 0 },
             "tests" =>
             { "testGameStyleSwitch" =>
               { "name" => "testGameStyleSwitch",
                 "start_time" => "2023-07-06 16:54:43 +0000",
                 "status" => "failed",
                 "test_id" => "54321f1fc3140",
                 "duration" => 1.7,
                 "video" => nil,
                 "instrumentation_log" => { "end" => 2530, "start" => 2437 },
                 "device_log" => nil,
                 "network_log" => nil,
                 "class" => "AlwaysFailingTest" },
               "testGameStyleSwitch01" =>
               { "name" => "testGameStyleSwitch01",
                 "start_time" => "2023-07-06 16:54:45 +0000",
                 "status" => "timedout",
                 "test_id" => "543216192003f",
                 "duration" => 1.66,
                 "video" => nil,
                 "instrumentation_log" => { "end" => 3463, "start" => 2530 },
                 "device_log" => nil,
                 "network_log" => nil,
                 "class" => "AlwaysFailingTest" } } } },
        "duration" => 10.0
      }
      log_split_test_details = { "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch" => { "duration" => 1.7, "instru_log_boundary" => { "end" => 2530, "start" => 2437 }, "name" => "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch", "start_time" => "2023-07-06 16:54:43 +0000", "status" => "failed" },
                                 "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch01" => { "duration" => 1.66, "instru_log_boundary" => { "end" => 3463, "start" => 2530 }, "name" => "BullsEyeUITests.AlwaysFailingTest testGameStyleSwitch01", "start_time" => "2023-07-06 16:54:45 +0000", "status" => nil } }
      test_state, summary_file_data = summary_manager.generate_summary_log_split(log_split_test_details, build_id, session_id, time_components, Time.now - 7500, 7200)
      expect(JSON.parse(summary_file_data.to_json)).to eql(expected_summary_file_data)
    end
  end
end
