require_relative '../../../lib/device_update/update_manager'
require_relative '../../../lib/models/model_state'

describe UpdateManager do
  let(:device_uuid) { "udid" }
  let(:device_model) { "model" }
  let(:url) { "https://updates.xyz.com/2022FallFCS/" }
  let(:dc_url) { "http://dc-file-server.service.prod.mobile.browserstack.com:3131" }

  before(:each) do
    allow(BrowserStack).to receive(:init_logger)
  end

  describe 'retrieve_update_file' do
    it 'should fetch update file from Download Endpoint' do
      allow(BrowserStack::HttpUtils).to receive(:download).and_return(true)
      update_manager = UpdateManager.new(device_uuid, device_model)
      allow(update_manager).to receive(:local_path).and_return("/tmp/update.ipsw")
      expect(BrowserStack::HttpUtils).to receive(:download).with("#{dc_url}/#{device_model}.ipsw", "/tmp/update.ipsw", { timeout: 600 })
      update_manager.retrieve_update_file
    end

    it 'should fallback to downloading from apple if IPSW not available on DE' do
      allow(BrowserStack::HttpUtils).to receive(:download)
        .with("#{dc_url}/model.ipsw", "/tmp/update.ipsw", { timeout: 600 })
        .and_raise(HTTPException)
      update_manager = UpdateManager.new(device_uuid, device_model)
      allow(update_manager).to receive(:local_path).and_return("/tmp/update.ipsw")
      allow(update_manager).to receive(:find_ipsw_url).and_return(url)
      allow(update_manager).to receive(:resolve_ip).and_return("random_ip")
      expect(BrowserStack::HttpUtils).to receive(:download)
        .with(url, "/tmp/update.ipsw", { timeout: 600, resolved_address: "updates.xyz.com:443:random_ip" })
      update_manager.retrieve_update_file
    end
  end

  describe 'update_device' do
    it 'should update device[CFGUtil]' do
      cfgutil_response = { "Command" => "update", "Type" => "CommandOutput", "Devices" => ["any-ecid"] }
      allow_any_instance_of(CFGUtil).to receive(:update_device).and_return(cfgutil_response)
      update_manager = UpdateManager.new(device_uuid, device_model)
      expect(update_manager.update_device).to eq(cfgutil_response)
    end

    it 'should raise an error if cfgutil fails' do
      allow_any_instance_of(CFGUtil).to receive(:update_device).and_raise("Device not found")
      update_manager = UpdateManager.new(device_uuid, device_model)
      expect { update_manager.update_device }.to raise_error(UpdateManager::UpdateDeviceError, "Device not found")
    end
  end

  describe 'pre_update_checks' do
    describe 'machine is incompatible' do
      it 'should raise an error if cfgutil not installed' do
        allow_any_instance_of(CFGUtil).to receive(:installed?).and_return(false)
        update_manager = UpdateManager.new(device_uuid, device_model)
        expect { update_manager.pre_update_checks }.to raise_error(UpdateManager::IncompatibleMachine)
      end

      it 'should raise an error if device is off usb' do
        allow_any_instance_of(CFGUtil).to receive(:installed?).and_return(true)
        allow(BrowserStack::OSUtils).to receive(:execute).and_return("random")
        update_manager = UpdateManager.new(device_uuid, device_model)
        expect { update_manager.pre_update_checks }.to raise_error(UpdateManager::IncompatibleMachine)
      end
    end

    describe 'machine is compatible' do
      before(:each) do
        allow_any_instance_of(CFGUtil).to receive(:installed?).and_return(true)
        allow(BrowserStack::OSUtils).to receive(:execute).and_return("udid")
      end
    end
  end

  describe 'start_update' do
    let(:mock_model_state) { double('ModelState') }

    before(:each) do
      allow(ModelState).to receive(:new).and_return(mock_model_state)
      allow(mock_model_state).to receive(:update_manager_log_file)
      allow(mock_model_state).to receive(:touch_undergoing_update_file)
      allow(mock_model_state).to receive(:remove_undergoing_update_file)
      allow(BrowserStack::OSUtils).to receive(:execute).and_return(true)
    end

    it 'should rescue if machine is incompatible' do
      update_manager = UpdateManager.new(device_uuid, device_model)

      allow(update_manager).to receive(:pre_update_checks).and_raise(UpdateManager::IncompatibleMachine)
      expect(mock_model_state).to receive(:touch_incompatible_machine_for_update_file)
      update_manager.start_update
    end

    it 'should rescue if download fails' do
      update_manager = UpdateManager.new(device_uuid, device_model)

      allow(update_manager).to receive(:pre_update_checks).and_raise(UpdateManager::IncompatibleMachine)
      expect(mock_model_state).to receive(:touch_incompatible_machine_for_update_file)
      update_manager.start_update
    end

    it 'should start update if ipsw is already present' do
      update_manager = UpdateManager.new(device_uuid, device_model)

      allow(update_manager).to receive(:pre_update_checks).and_return(true)
      allow(mock_model_state).to receive(:downloading_update_file_present?).and_return(false)
      allow(File).to receive(:exist?).and_return(true)
      allow(BrowserStack::OSUtils).to receive(:execute).and_return(true)
      expect(update_manager).to receive(:update_device).and_return(true)
      update_manager.start_update
    end

    it 'should download ipsw if downloading file or ipsw not present' do
      update_manager = UpdateManager.new(device_uuid, device_model)

      allow(update_manager).to receive(:pre_update_checks).and_return(true)
      allow(mock_model_state).to receive(:downloading_update_file_present?).and_return(false)
      allow(File).to receive(:exist?).and_return(false)
      expect(update_manager).to receive(:retrieve_update_file).and_return(true)
      expect(update_manager).to receive(:update_device).and_return(true)
      update_manager.start_update
    end
  end
end
