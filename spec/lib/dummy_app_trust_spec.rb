require_relative '../../lib/dummy_app_trust'

describe EnterpriseAppTrust do
  let(:udid) { '1234' }
  let(:device_details) { { appium_port: 8083, device_type: "iphone" } }
  let(:dist_name) { "dummy_dist" }
  let(:bundle_id) { "dummy_bundle_id" }
  let(:subject) { EnterpriseAppTrust.new(udid, {}, device_details, dist_name, bundle_id) }

  describe '.verified_over_internet?' do
    it 'returns false if not verified' do
      mock_driver = double("driver")
      mock_appium_element = double("driver")

      allow(subject).to receive(:driver).and_return(mock_driver)
      allow(mock_appium_element).to receive(:value).and_return("An app from developer “iPhone Distribution: #{dist_name}” is not verified on this #{device_details[:device_type]} and will not run until it is verified using your network connection.")
      allow(mock_driver).to receive(:find_eles_by_predicate_include).and_return([mock_appium_element])

      expect(mock_driver).to receive(:get_source)
      expect(subject.send(:verified_over_internet?)).to be false
    end

    it 'returns true if verified' do
      mock_driver = double("driver")

      allow(subject).to receive(:driver).and_return(mock_driver)
      allow(mock_driver).to receive(:find_eles_by_predicate_include).and_return([])

      expect(mock_driver).to_not receive(:get_source)
      expect(subject.send(:verified_over_internet?)).to be true
    end
  end
end
