require_relative '../../spec_helper'
require_relative '../../../lib/apps/app'
require_relative '../../../lib/apps/chrome'
require_relative '../../../lib/apps/launcher'
require_relative '../../../lib/utils/osutils'

describe BrowserStack::App do
  describe '#update_app_version_using_ios_version' do
    chrome_ios_version_map = {
      10 => "64.0.3282.112",
      11 => "75.0.3770.70",
      12 => "86.0.4240.93",
      13 => "92.0.4515.90",
      14 => "92.0.4515.90",
      15 => "92.0.4515.90",
      'latest' => "92.0.4515.90"
    }
    launcher_ios_version_map = {
      10 => "1",
      11 => "1",
      12 => "1",
      13 => "15",
      14 => "15",
      15 => "15",
      'latest' => "15"
    }
    apps_versions_map = {
      BrowserStack::Chrome.new => chrome_ios_version_map,
      BrowserStack::Launcher.new => launcher_ios_version_map
    }

    apps_versions_map.each do |app, app_ios_version_map|
      context "#{app} versions" do
        app_ios_version_map.each do |ios_version, app_version|
          it "should return correct #{app} version for iOS #{ios_version}" do
            app.update_app_version_using_ios_version(ios_version)
            expect(app.instance_variable_get(:@version)).to eq(app_version)
          end
        end

        it "should return latest version if iOS version is not in map" do
          app.update_app_version_using_ios_version(9999999)
          expect(app.instance_variable_get(:@version)).to eq(app_ios_version_map['latest'])
        end
      end
    end
  end

  describe '#get_nix_store_source' do
    nix_store_source = "/nix/store/1fzvnr2ascwscam5jz412jzb6czzbb58-ios_chrome_app-86"
    source = "/usr/local/.browserstack/config/apps/cache/com.google.chrome.ios-86.0.4240.93.app"
    cmd = "ls -al #{source} | awk '{print $NF}'"
    trail_str = "\n"
    app_folder_dir_content = "all_frames_document_end_web_bundle.js\nall_frames_web_bundle.js\nar.lproj\nbg.lproj\nca.lproj\nchrome_100_percent.pak"

    context "when nix store is used" do
      before do
        allow(BrowserStack::OSUtils).to receive(:execute).with(cmd).and_return nix_store_source + trail_str
      end
      it "returns nix store source" do
        expect(BrowserStack::App.new('com.google.chrome.ios', 'chrome').get_nix_store_source(source)).to eq(nix_store_source)
      end
    end

    context "when nix store is not used" do
      before do
        allow(BrowserStack::OSUtils).to receive(:execute).with(cmd).and_return app_folder_dir_content + trail_str
      end
      it "returns garbage string" do
        expect(BrowserStack::App.new('com.google.chrome.ios', 'chrome').get_nix_store_source(source)).to eq(app_folder_dir_content)
      end
    end
  end

  describe '#remove_nix_meta_files' do
    nix_store_source = "/nix/store/1fzvnr2ascwscam5jz412jzb6czzbb58-ios_chrome_app-86"
    source = "/usr/local/.browserstack/config/apps/cache/com.google.chrome.ios-86.0.4240.93.app"
    cmd = "ls -al #{source} | awk '{print $NF}'"
    trail_str = "\n"
    app_folder_dir_content = "all_frames_document_end_web_bundle.js\nall_frames_web_bundle.js\nar.lproj\nbg.lproj\nca.lproj\nchrome_100_percent.pak"

    context "when nix store is used" do
      before do
        allow_any_instance_of(BrowserStack::App).to receive(:get_nix_store_source).and_return nix_store_source
        allow(BrowserStack::OSUtils).to receive(:execute).with("find #{nix_store_source} -name '._*' -delete")
      end

      it "finds and deletes files beginning with ._ in the nix store directory" do
        expect(BrowserStack::OSUtils).to receive(:execute).with("find #{nix_store_source} -name '._*' -delete")
        BrowserStack::App.new('com.google.chrome.ios', 'chrome').remove_nix_meta_files(source)
      end
    end

    context "when nix store is not used" do
      before do
        allow_any_instance_of(BrowserStack::App).to receive(:get_nix_store_source).and_return app_folder_dir_content
        allow(BrowserStack::OSUtils).to receive(:execute).with("find #{app_folder_dir_content} -name '._*' -delete")
      end

      it "finds and deletes files beginning with ._ in the directory" do
        expect(BrowserStack::OSUtils).to receive(:execute).with("find #{app_folder_dir_content} -name '._*' -delete")
        BrowserStack::App.new('com.google.chrome.ios', 'chrome').remove_nix_meta_files(source)
      end
    end
  end
end
