require 'fileutils'

require_relative '../../../lib/apps/installed_app'
require_relative '../../spec_helper'

describe InstalledApp do
  let(:udid) { 'myphone' }
  let(:bundle_id) { 'com.google.chrome.ios' }
  let(:version) { '92.0.4515.90' }
  let(:ppuid) { 'my_provisionin_profile_unique_id' }

  let(:config_path) { File.join(CONFIG_ROOT, "installed_apps_#{udid}.json") }
  let(:ppuid_file_path) { File.join(CONFIG_ROOT, "ppuid_#{udid}") }
  let(:branch_name) { 'newBranch' }

  let(:now) { Time.parse('2022-07-27 15:01') }

  let(:installed_apps_config) do
    {
      bundle_id => {
        'version' => version,
        'ppuid' => ppuid,
        'branch_name' => branch_name,
        'install_date' => now.utc.to_s
      }
    }
  end

  subject { InstalledApp.new(udid, bundle_id) }

  before do
    allow(Time).to receive(:now).and_return(now)
  end

  describe '#reinstall?' do
    let(:ppuid_file) { double('mock_ppuid_file') }
    let(:ppuid) { 'my_provisioning_profile_unique_id' }

    before do
      allow(PpuidFile).to receive(:new).and_return(ppuid_file)
      allow(ppuid_file).to receive(:ppuid).and_return(ppuid)
      File.write(config_path, installed_apps_config.to_json)
    end

    after { FileUtils.rm_rf(config_path) }

    it 'returns true if config empty' do
      FileUtils.rm_rf(config_path)
      expect(subject.reinstall?(latest_version: version)).to be true
    end

    it 'returns true if bundle id not in config' do
      installed_app = InstalledApp.new(udid, 'com.browserstack.Launcher')
      expect(installed_app.reinstall?(latest_version: '13')).to be true
    end

    it 'returns true if ppuid of app is not the same as device\'s current ppuid' do
      expect(PpuidFile).to receive(:new).with(udid).and_return(ppuid_file)
      expect(ppuid_file).to receive(:ppuid).and_return('another_ppuid')
      expect(subject.reinstall?(latest_version: version)).to be true
    end

    it 'returns true if version of app is not the same as the latest app version' do
      expect(subject.reinstall?(latest_version: '103.0.5060.63')).to be true
    end

    it 'otherwise returns false' do
      expect(subject.reinstall?(latest_version: version)).to be false
    end
  end

  describe '#read_config' do
    it 'returns parsed json config' do
      File.write(config_path, installed_apps_config.to_json)
      expect(subject.read_config).to eq(installed_apps_config)
      FileUtils.rm_rf(config_path)
    end

    it 'returns {} if file does not exist' do
      expect(subject.read_config).to eq({})
    end

    it 'returns {} if file does not contain valid json' do
      FileUtils.touch(config_path)
      expect(subject.read_config).to eq({})
    end
  end

  describe '#update_config' do
    let(:new_values) { ['newBranch', 'newTeamID', 'newPPUID'] }

    before do
      allow(File).to receive(:exist?).and_return(true)
      allow(File).to receive(:readlines).and_return(new_values)
      File.write(config_path, installed_apps_config.to_json)
    end

    after  { FileUtils.rm_rf(config_path) }

    it 'writes new profile to config' do
      expected_new_config = installed_apps_config.dup
      expected_new_config['com.browserstack.Launcher'] = {
        'version' => '13',
        'ppuid' => ppuid,
        'branch_name' => branch_name,
        'install_date' => now.utc.to_s
      }

      installed_app = InstalledApp.new(udid, 'com.browserstack.Launcher')
      installed_app.update_config('13', ppuid)

      config = JSON.parse(File.read(config_path))
      expect(config).to eq(expected_new_config)
    end

    it 'updates existing profile in config' do
      new_now = Time.parse('2022-08-27 16:29')
      allow(Time).to receive(:now).and_return(new_now)

      expected_new_config = {
        bundle_id => {
          'version' => '103.0.5060.63',
          'ppuid' => ppuid,
          'branch_name' => branch_name,
          'install_date' => new_now.utc.to_s
        }
      }

      subject.update_config('103.0.5060.63', ppuid)

      config = JSON.parse(File.read(config_path))
      expect(config).to eq(expected_new_config)
    end

    it 'throws error if invalid version string passed' do
      version = "File Doesn't Exist, Will Create"
      expect { subject.update_config(version, ppuid) }.to raise_error(ArgumentError)
    end
  end
end
