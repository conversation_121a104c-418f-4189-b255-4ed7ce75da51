require_relative '../../../lib/percy/renderer_key_check'
require_relative '../../../server/device_manager'
require_relative '../../../lib/utils/screenshot_util'

describe Percy::RendererKeyCheck do
  let(:device_id) { "123" }
  let(:device) { double("device") }
  let(:renderer_key_check) { Percy::RendererKeyCheck.new(device_id) }

  before(:each) do
    allow(DeviceManager).to receive(:device_configuration_check).with(device_id).and_return(device)
  end

  describe "#initialize" do
    context "with correct device_id" do
      it "initializes instance variables" do
        expect(renderer_key_check.device_id).to eq(device_id)
      end
    end
  end

  describe "#validate_renderer_key" do
    before(:each) do
      allow(DeviceManager).to receive(:session_file).with(device_id).and_return('/tmp/session_file')
      allow(File).to receive(:read).with('/tmp/session_file').and_return("{\"env\":{\"PLATFORM\":\"iphone\",\"ASYNC_RENDERER\":\"true\"},\"renderer_key\":\"1234#abcd\"}")
    end

    context "Won't throw error if key is matched" do
      it "returns nil and doesn't throw error" do
        op = renderer_key_check.validate_renderer_key(renderer_key: '1234#abcd')
        expect(op).to equal(nil)
      end
    end

    context "will throw RendererKeyError on key mismatch" do
      it "throws Incorrect Renderer key error" do
        expect { renderer_key_check.validate_renderer_key(renderer_key: 'mismatch') }.to raise_error(RendererKeyError, "Incorrect renderer key")
      end
    end
  end

  describe "#validate_renderer_key_exists" do
    before(:each) do
      allow(DeviceManager).to receive(:session_file).with(device_id).and_return('/tmp/session_file')
      allow(File).to receive(:read).with('/tmp/session_file').and_return("{\"env\":{\"PLATFORM\":\"iphone\",\"ASYNC_RENDERER\":\"true\"}}")
    end

    context "Won't throw error if key is not present" do
      it "returns nil and doesn't throw error" do
        op = renderer_key_check.validate_renderer_key_exists
        expect(op).to equal(nil)
      end
    end
  end
end
