require_relative '../../../lib/percy/tiles_manager'
require_relative '../../../server/device_manager'
require_relative '../../../lib/utils/screenshot_util'

describe Percy::<PERSON>ilesManager do
  let(:device_id) { "123" }
  let(:device) { double("device") }
  let(:tiles_manager) { Percy::TilesManager.new(device_id) }

  let(:tile1_path) { "some/path" }
  let(:tile1_build_id) { 123 }
  let(:tile1_url) { "some_url" }
  let(:tile1_error) { nil }
  let(:tile1_seq_no) { 0 }
  let(:tile1) { Percy::Tile.new(tile1_path, tile1_build_id, tile1_url, tile1_error, tile1_seq_no) }

  let(:tile2_path) { "some/path2" }
  let(:tile2_build_id) { 123 }
  let(:tile2_url) { "some_url2" }
  let(:tile2_error) { nil }
  let(:tile2_seq_no) { 1 }
  let(:tile2) { Percy::Tile.new(tile2_path, tile2_build_id, tile2_url, tile2_error, tile2_seq_no) }

  let(:error_path) { "some/path2" }
  let(:error_build_id) { 123 }
  let(:error_url) { nil }
  let(:error_error) { "Could not upload" }
  let(:error_seq_no) { 0 }
  let(:error_tile) { Percy::Tile.new(error_path, error_build_id, error_url, error_error, error_seq_no) }

  let(:path) { "some_path" }
  let(:file_handle) { double("file_handle") }
  let(:bucket) { "some bucket" }
  let(:strip) { { "header_height" => 123, "footer_height" => 100 } }

  before(:each) do
    allow(DeviceManager).to receive(:device_configuration_check).with(device_id).and_return(device)
  end

  describe "#initialize" do
    context "with correct device_id" do
      it "initializes instance variables" do
        expect(tiles_manager.device_id).to eq(device_id)
      end
    end
  end

  describe "#capture_tile" do
    before(:each) do
      allow(File).to receive(:read).with("#{STATE_FILES_DIR}/#{device_id}_session").and_return("{\"env\":{\"PLATFORM\":\"iphone\"},\"renderer_key\":\"1234#abcd\"}")
    end
    let(:appium_session_id) { "some session string" }
    let(:build_id) { 123 }
    let(:seq_no) { 0 }
    let(:image_content) { double("image content") }

    context "with seq_no = 0" do
      it "captures tile and starts background processing" do
        tile = Percy::Tile.new
        expect(tiles_manager).to receive(:init_state)
        expect(ScreenshotsUtil).to receive(:capture_screenshot_via_appium).with(
          device_id, appium_session_id
        ).and_return(image_content)
        expect(Percy::Tile).to receive(:new).and_return(tile)
        expect(tiles_manager).to receive(:save_tile).with(tile)
        expect(tiles_manager).to receive(:capture_tile_bg).with(
          tile, image_content, strip, bucket
        )

        thread = tiles_manager.capture_tile(appium_session_id, build_id, seq_no, strip, bucket, renderer_key: "cleanup")
        thread.join
      end
    end

    context "with seq_no != 0" do
      let(:seq_no) { 1 }

      it "captures tile and starts background processing" do
        tile = Percy::Tile.new
        expect(tiles_manager).not_to receive(:init_state)
        expect(ScreenshotsUtil).to receive(:capture_screenshot_via_appium).with(
          device_id, appium_session_id
        ).and_return(image_content)
        expect(Percy::Tile).to receive(:new).and_return(tile)
        expect(tiles_manager).to receive(:save_tile).with(tile)
        expect(tiles_manager).to receive(:capture_tile_bg).with(
          tile, image_content, strip, bucket
        )

        thread = tiles_manager.capture_tile(appium_session_id, build_id, seq_no, strip, bucket, renderer_key: "cleanup")
        thread.join
      end
    end
  end

  describe "#capture_tile_bg" do
    let(:appium_session_id) { "some session string" }
    let(:build_id) { 123 }
    let(:seq_no) { 0 }
    let(:image_content) { double("image content") }

    before(:each) do
      expect(tiles_manager).to receive(:image_file_path).with(
        tile1.build_id, tile1.seq_no
      ).and_return(path)
      expect(tiles_manager).to receive(:save_image).with(path, image_content)
      expect(tiles_manager).to receive(:strip_image).with(path, strip)
      expect(tiles_manager).to receive(:save_tile).with(tile1)
    end

    it "saves, strips and uploads tile" do
      expect(tiles_manager).to receive(:upload_tile).with(bucket, tile1)

      tiles_manager.capture_tile_bg(tile1, image_content, strip, bucket)

      expect(tile1.path).to eq(path)
    end

    it "populates error if any step fails" do
      error = StandardError.new("Upload failed")
      expect(tiles_manager).to receive(:upload_tile).with(bucket, tile1).and_raise(error)

      tiles_manager.capture_tile_bg(tile1, image_content, strip, bucket)

      expect(tile1.error).to eq(error.message)
    end
  end

  describe "#finalize" do
    let(:expected_tiles) { 2 }
    let(:tiles) { [tile1, tile2] }

    before(:each) do
      allow(File).to receive(:read).with("#{STATE_FILES_DIR}/#{device_id}_session").and_return("{\"env\":{\"PLATFORM\":\"iphone\"},\"renderer_key\":\"1234#abcd\"}")
      allow(tiles_manager).to receive(:tiles).and_return(tiles)
    end

    it "throws error if files are not uploaded before timeout" do
      allow(tiles_manager).to receive(:upload_done?).and_return(false)

      expect { tiles_manager.finalize(expected_tiles, renderer_key: "cleanup") }.to raise_error(StandardError)
    end

    it "returns tiles if upload is done" do
      allow(tiles_manager).to receive(:upload_done?).and_return(true)
      expect(tiles_manager).to receive(:cleanup).and_return(true)

      result = tiles_manager.finalize(expected_tiles, renderer_key: "cleanup")

      expect(result).to eq(tiles)
    end
  end

  describe "#cleanup" do
    it "deletes the tmp folder" do
      expect(tiles_manager).to receive(:delete_tmp_images_folder)

      tiles_manager.cleanup
    end
  end

  describe "#tiles" do
    it "reads the tile meta and returns the list of tiles" do
      expected_tiles = [tile1, tile2]
      expect(tiles_manager).to receive(:tmp_images_dir).and_return(path)
      expect(Dir).to receive(:glob).with("#{path}/*_meta").and_return(["a_meta", "b_meta"])
      expect(tiles_manager).to receive(:load_tile).and_return(tile1, tile2)

      tiles = tiles_manager.tiles
      expect(tiles).to eq(expected_tiles)
    end
  end

  describe "private: " do
    describe "#init_state" do
      it "cleans the state" do
        expect(tiles_manager).to receive(:delete_tmp_images_folder)
        expect(tiles_manager).to receive(:create_tmp_images_folder)
        tiles_manager.send(:init_state)
      end
    end

    describe "#save_image" do
      it "writes file to the path" do
        data = "data"

        file_content = double("file_content")
        expect(File).to receive(:open).with(path, "wb+").and_yield(file_handle)
        expect(Base64).to receive(:decode64).with(data).and_return(file_content)
        expect(file_handle).to receive(:puts).with(file_content)

        tiles_manager.send(:save_image, path, data)
      end
    end

    describe "#save_tile" do
      it "writes tile to machine to save it" do
        tile_path = path
        expect(file_handle).to receive(:flock).with(2)
        expect(file_handle).to receive(:rewind)
        expect(file_handle).to receive(:write).with(tile1.to_h.to_json)
        expect(file_handle).to receive(:truncate)
        expect(file_handle).to receive(:flush)
        expect(file_handle).to receive(:pos)
        expect(tiles_manager).to receive(:tile_meta_file_path).with(
          tile1_build_id, tile1_seq_no
        ).and_return(tile_path)
        expect(File).to receive(:open).with(tile_path, File::RDWR | File::CREAT, 0o644).and_yield(file_handle)

        tiles_manager.send(:save_tile, tile1)
      end
    end

    describe "#load_tile" do
      it "loads the path into tile struct" do
        meta_path = path

        file_content = tile1.to_h.to_json
        expect(File).to receive(:open).with(meta_path, "r").and_return(file_content)
        expect(JSON).to receive(:parse).with(file_content).and_call_original

        tile = tiles_manager.send(:load_tile, meta_path)

        expect(tile).to eq(tile1)
      end
    end

    describe "#strip_image" do
      context "with nil strip" do
        let(:strip) { { "header_height" => nil, "footer_height" => nil } }

        it "returns without stripping" do
          expect(tiles_manager).not_to receive(:system)

          tiles_manager.send(:strip_image, path, strip)
        end
      end

      context "with strip and imagemagik success" do
        it "returns after stripping" do
          stub_const('ENV', { 'IMAGEMAGICK_CONVERT' => 'path_to_imagemagick_convert' })
          expect(tiles_manager).to receive(:system).with(
            "path_to_imagemagick_convert #{path} -strip -define png:include-chunk=none -gravity North -chop 0x#{strip['header_height']} -gravity South -chop 0x#{strip['footer_height']} #{path}"
          ).and_return(true)

          tiles_manager.send(:strip_image, path, strip)
        end

        context "with right and left offset" do
          let(:strip) { { "header_height" => 10, "footer_height" => 20, "left_offset" => 30, "right_offset" => 40 } }

          it "returns after stripping right and left offset" do
            stub_const('ENV', { 'IMAGEMAGICK_CONVERT' => 'path_to_imagemagick_convert' })
            expect(tiles_manager).to receive(:system).with(
              "path_to_imagemagick_convert #{path} -strip -define png:include-chunk=none -gravity North -chop"\
              " 0x#{strip['header_height']} -gravity South -chop 0x#{strip['footer_height']} -gravity East -chop"\
              " #{strip['right_offset']}x0 -gravity West -chop #{strip['left_offset']}x0 #{path}"
            ).and_return(true)

            tiles_manager.send(:strip_image, path, strip)
          end
        end
      end

      context "with strip and imagemagik failure" do
        it "throws exception" do
          stub_const('ENV', { 'IMAGEMAGICK_CONVERT' => 'path_to_imagemagick_convert' })
          expect(tiles_manager).to receive(:system).with(
            "path_to_imagemagick_convert #{path} -strip -define png:include-chunk=none -gravity North -chop 0x#{strip['header_height']} -gravity South -chop 0x#{strip['footer_height']} #{path}"
          ).and_return(false)

          expect { tiles_manager.send(:strip_image, path, strip) }.to raise_error(StandardError)
        end
      end
    end

    describe "#image_file_path" do
      it "returns path of image file" do
        build_id = 123
        seq_no = 0

        expect(tiles_manager).to receive(:tmp_images_dir).and_return(path)
        result = tiles_manager.send(:image_file_path, build_id, seq_no)

        expect(result).to eq("#{path}/#{build_id}_#{seq_no}")
      end
    end

    describe "#tile_meta_file_path" do
      it "returns path of image file" do
        build_id = 123
        seq_no = 0

        expect(tiles_manager).to receive(:image_file_path).with(build_id, seq_no).and_return(path)
        result = tiles_manager.send(:tile_meta_file_path, build_id, seq_no)

        expect(result).to eq("#{path}_meta")
      end
    end

    describe "#tmp_images_dir" do
      it "returns path of tmp image directory" do
        result = tiles_manager.send(:tmp_images_dir)

        expect(result).to eq("/tmp/#{tiles_manager.device_id}/tiles")
      end
    end

    describe "#create_tmp_images_folder" do
      it "creates tmp image directory" do
        expect(tiles_manager).to receive(:tmp_images_dir).and_return(path)
        expect(tiles_manager).to receive(:system).with(/mkdir -p #{path}/)

        tiles_manager.send(:create_tmp_images_folder)
      end
    end

    describe "#delete_tmp_images_folder" do
      it "creates tmp image directory" do
        expect(tiles_manager).to receive(:tmp_images_dir).and_return(path)
        expect(tiles_manager).to receive(:system).with(/rm -rf #{path}/)

        tiles_manager.send(:delete_tmp_images_folder)
      end
    end

    describe "#upload_file_name" do
      it "generates file name used in bucket" do
        file_content = "some content"
        expect(File).to receive(:read).with(tile1.path).and_return(file_content)

        result = tiles_manager.send(:upload_file_name, tile1)

        expect(result).to eq("#{Digest::SHA256.hexdigest(file_content)}-#{tile1.build_id}")
      end
    end

    describe "#upload_tile" do
      context "upload success" do
        it "tile.url is populated" do
          public_url = "some new public url"

          upload_name = "some sha - build_id"

          expect(tiles_manager).to receive(:upload_file_name).with(tile1).and_return(upload_name)
          expect(GCSManager).to receive(:upload).with(
            bucket, upload_name, tile1.path, device_id, cert_delete_access: false
          ).and_return(public_url)

          tiles_manager.send(:upload_tile, bucket, tile1)

          expect(tile1.url).to eq(public_url)
        end
      end

      context "upload failure" do
        it "tile.error is populated" do
          error = StandardError.new("Some error message")

          upload_name = "some sha - build_id"

          expect(tiles_manager).to receive(:upload_file_name).with(tile1).and_return(upload_name)
          expect(GCSManager).to receive(:upload).with(
            bucket, upload_name, tile1.path, device_id, cert_delete_access: false
          ).and_raise(error)

          tiles_manager.send(:upload_tile, bucket, tile1)

          expect(tile1.error).to eq(error.message)
        end
      end
    end

    describe "#upload_done?" do
      let(:expected_tiles) { 2 }

      it "throws exception if expected_tiles does not match actual" do
        tiles = [tile1]
        expect(tiles_manager).to receive(:tiles).and_return(tiles)

        expect { tiles_manager.send(:upload_done?, expected_tiles) }.to raise_error(StandardError)
      end

      it "with upload complete successfully" do
        tiles = [tile1, tile2]
        expected_tiles = 2
        expect(tiles_manager).to receive(:tiles).and_return(tiles)

        result = tiles_manager.send(:upload_done?, expected_tiles)

        expect(result).to eq(true)
      end

      it "with upload failed" do
        error_tile.seq_no = 1
        tiles = [tile1, error_tile]
        expected_tiles = 2
        expect(tiles_manager).to receive(:tiles).and_return(tiles)

        result = tiles_manager.send(:upload_done?, expected_tiles)

        expect(result).to eq(true)
      end

      it "with upload not completed" do
        tile2.url = nil
        tile2.error = nil

        tiles = [tile1, tile2]
        expected_tiles = 2
        expect(tiles_manager).to receive(:tiles).and_return(tiles)

        result = tiles_manager.send(:upload_done?, expected_tiles)

        expect(result).to eq(false)
      end
    end
  end
end
