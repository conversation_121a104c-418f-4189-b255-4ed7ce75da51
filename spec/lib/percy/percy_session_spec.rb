require_relative '../../spec_helper'
require_relative '../../../lib/percy/percy_session'

describe Percy::PercySess<PERSON> do
  let(:device_id) { '123ABC' }
  let(:device) do
    {
      'current_appium_version' => '1.14.0',
      'device_name' => 'iPhone XS',
      'device_version' => '13.2.0',
      'selenium_port' => 8004,
      'webdriver_port' => 8084,
      'debugger_port' => 24458,
      'port' => 1234
    }
  end
  let(:docker_image) { 'random123_renderer_amd64' }
  let(:env_vars) do
    {
      'PLATFORM' => 'ios_blue',
      'ENABLE_DND_MODE' => true
    }
  end
  let(:poa_setup_instance) { instance_double(PercyOnAutomate::SetupAutomateSession) }
  let(:poa_setup_class) do
    class_double(PercyOnAutomate::SetupAutomateSession).as_stubbed_const
  end
  let(:poa_screenshot_manager_instance) { instance_double(PercyOnAutomate::PoaScreenshotManager) }
  let(:poa_screenshot_manager_class) do
    class_double(PercyOnAutomate::PoaScreenshotManager).as_stubbed_const
  end
  let(:poa_dom_metadata_handler_class_instance) { instance_double(PercyOnAutomate::DomMetaDataHandler) }
  let(:poa_dom_metadata_handler_class) do
    class_double(PercyOnAutomate::DomMetaDataHandler).as_stubbed_const
  end
  let(:env_vars_fetch_certs) do
    {
      'PLATFORM' => 'ios_blue',
      'DISABLE_DEVICE_ANIMATIONS' => true,
      'FETCH_CERTS' => true,
      'ENABLE_DND_MODE' => true
    }
  end
  let(:dummy_cert) do
    {
      "cert" => "abc"
    }
  end

  before(:each) do
    allow(DeviceManager).to receive(:device_configuration_check).with(device_id).and_return(device)
    allow(DeviceManager).to receive(:session_file).with(device_id).and_return('/tmp/session_file')
    allow(File).to receive(:read).with('/tmp/session_file').and_return("{\"jp_executable\": \"random\",\"env\":{\"PLATFORM\":\"iphone\"}}")
    allow(BrowserStack::OSUtils).to receive(:is_arm?).and_return(false)
    @percy_session = Percy::PercySession.new(device_id)
  end

  describe '#start_server' do
    before(:each) do
      allow(File).to receive(:read).with('/tmp/session_file').and_return("")
      @percy_session = Percy::PercySession.new(device_id)
      expect(BrowserStack.logger).to receive(:info).with('Starting server')
      expect(BrowserStack::DeviceLogger).to receive(:clean_workspace).with(device_id)
      expect(BrowserStack::DeviceLogger).to receive(:start).with(device_id)
      expect(BrowserStack.logger).to receive(:info).with('Started device logger for percy')
      expect(PrivoxyManager).to receive(:setup_privoxy)
      expect(@percy_session.browserup_manager).to receive(:setup_browserup).and_return(true)
      expect(DeviceManager).to receive(:switch_appium).with({}, device_id, device, '1.22.0', 'error')
      expect(IdeviceUtils).to receive(:uninstall_redirect_app).and_return("thread")
      expect(@percy_session.jp_manager.jp_executable_path).to be(nil)
      allow(BrowserStackAppHelper).to receive(:check_and_install_browserstack_test_suite).and_return(true)
    end

    it 'should start server' do
      expect(@percy_session).to receive(:save_session).with(env_vars)
      expect(BrowserStack::Session).to receive(:extension_uninstalled?).with({ genre: "percy" }, "thread").and_return(true)
      expect(BrowserStackAppHelper).to receive(:run_ui_test).with(device_id, :enable_focus_mode)
      @percy_session.start_server(env_vars)
    end

    it 'should start server with fetch certs' do
      allow(File).to receive(:open).with("/tmp/percy/#{device_id}/certs/gcloud.json", 'w').and_call_original
      expect(@percy_session).to receive(:save_session).with(env_vars_fetch_certs)
      expect(@percy_session).to receive(:fetch_certs_from_torch).with(env_vars_fetch_certs).and_return(dummy_cert)
      expect(Percy::PercySession).to receive(:gcp_tmp_file_path).with(device_id).and_call_original
      expect(BrowserStack::Session).to receive(:extension_uninstalled?).with({ genre: "percy" }, "thread").and_return(true)
      expect(BrowserStackAppHelper).to receive(:run_ui_test).with(device_id, :enable_focus_mode)
      @percy_session.start_server(env_vars_fetch_certs)
    end

    it 'should start server even if enable_focus_mode fails' do
      expect(BrowserStack::Session).to receive(:extension_uninstalled?).with({ genre: "percy" }, "thread").and_return(true)
      expect(BrowserStackAppHelper).to receive(:run_ui_test).with(device_id, :enable_focus_mode)
                                                            .and_raise(BrowserStackTestExecutionError.new('HomeScreenUITests', 'enable_focus_mode', "Failed"))
      expect { @percy_session.start_server(env_vars) }.to_not raise_error
    end

    it "should handle error check_and_install_browserstack_test_suite" do
      expect(BrowserStack::Session).to receive(:extension_uninstalled?).with({ genre: "percy" }, "thread").and_return(true)
      expect(BrowserStackAppHelper).to receive(:check_and_install_browserstack_test_suite).and_raise(StandardError)

      expect { @percy_session.start_server(env_vars) }.to_not raise_error
    end

    it 'should fail to start if redirect app uninstall fails' do
      expect(BrowserStack::Session).to receive(:extension_uninstalled?).with({ genre: "percy" }, "thread").and_return(nil)
      expect { @percy_session.start_server(env_vars) }.to raise_error(StandardError)
    end

    context 'ENABLE_DND_MODE disabled' do
      let(:env_vars) do
        {
          'PLATFORM' => 'ios_blue'
        }
      end

      it 'should start server and not run enable_focus_mode' do
        expect(BrowserStack::Session).to receive(:extension_uninstalled?).with({ genre: "percy" }, "thread").and_return(true)
        expect(BrowserStackAppHelper).to_not receive(:run_ui_test)
        @percy_session.start_server(env_vars)
      end
    end

    context 'DISABLE_SMS_NOTIFICATION' do
      let(:env_vars) do
        {
          'PLATFORM' => 'ios_blue'
        }
      end

      before(:each) do
        allow(IdeviceUtils).to receive(:check_app_with_bundle_id_exists).and_return(false)
      end

      it 'should start server and not run change_notifications_profile_mdm' do
        expect(BrowserStack::Session).to receive(:extension_uninstalled?).with({ genre: "percy" }, "thread").and_return(true)
        expect(Utils).to_not receive(:change_notifications_profile_mdm)
        @percy_session.start_server(env_vars)
      end

      it 'should start server and run change_notifications_profile_mdm' do
        env_vars['DISABLE_SMS_NOTIFICATION'] = true
        expect(BrowserStack::Session).to receive(:extension_uninstalled?).with({ genre: "percy" }, "thread").and_return(true)
        expect(Utils).to receive(:change_notifications_profile_mdm).with(['com.apple.MobileSMS'], device_id, true).and_return({ "result" => "success" })
        @percy_session.start_server(env_vars)
      end

      it 'should start server and run change_notifications_profile_mdm and handle failure' do
        env_vars['DISABLE_SMS_NOTIFICATION'] = true
        expect(BrowserStack::Session).to receive(:extension_uninstalled?).with({ genre: "percy" }, "thread").and_return(true)
        expect(Utils).to receive(:change_notifications_profile_mdm).with(['com.apple.MobileSMS'], device_id, true).and_return({ "result" => "failed" })
        @percy_session.start_server(env_vars)
      end

      it 'should start server and run change_notifications_profile_mdm when geoguard present' do
        env_vars['DISABLE_SMS_NOTIFICATION'] = true
        expect(IdeviceUtils).to receive(:check_app_with_bundle_id_exists).and_return(true)
        expect(BrowserStack::Session).to receive(:extension_uninstalled?).with({ genre: "percy" }, "thread").and_return(true)
        expect(Utils).to receive(:change_notifications_profile_mdm).with(['com.apple.MobileSMS', GEOGUARD_BUNDLE_ID], device_id, true).and_return({ "result" => "success" })
        @percy_session.start_server(env_vars)
      end
    end
  end

  describe '#stop_server' do
    let(:device_state) { DeviceState.new(device_id) }

    before(:each) do
      allow(DeviceManager).to receive(:session_file).with(device_id).and_return('/tmp/session_file')
      allow(File).to receive(:read).with('/tmp/session_file').and_return("{\"env\":{\"PLATFORM\":\"iphone\",\"ENABLE_DND_MODE\": true}}")
    end

    it 'should stop the server' do
      expect(BrowserStack.logger).to receive(:info).with('Stopping server')
      expect(BrowserStack::DeviceLogger).to receive(:destroy).with(device_id)
      expect(PrivoxyManager).to receive(:reset_proxy).with(device_id, device)
      expect(@percy_session.browserup_manager).to receive(:clean_browserup)
      expect(BrowserStackAppHelper).to receive(:run_ui_test).with(device_id, :disable_focus_mode)
      expect(Utils).to_not receive(:enable_notifications)
      @percy_session.stop_server
    end

    it 'should stop the server and not call disable_focus_mode' do
      expect(BrowserStack.logger).to receive(:info).with('Stopping server')
      allow(File).to receive(:read).with('/tmp/session_file').and_return("{\"env\":{\"PLATFORM\":\"iphone\"}}")
      expect(BrowserStack::DeviceLogger).to receive(:destroy).with(device_id)
      expect(PrivoxyManager).to receive(:reset_proxy).with(device_id, device)
      expect(@percy_session.browserup_manager).to receive(:clean_browserup)
      expect(BrowserStackAppHelper).to_not receive(:run_ui_test)
      @percy_session.stop_server
    end

    it 'should  stop the server even if disable_focus_mode fails' do
      expect(BrowserStack.logger).to receive(:info).with('Stopping server')
      expect(BrowserStack::DeviceLogger).to receive(:destroy).with(device_id)
      expect(PrivoxyManager).to receive(:reset_proxy).with(device_id, device)
      expect(@percy_session.browserup_manager).to receive(:clean_browserup)
      expect(BrowserStackAppHelper).to receive(:run_ui_test).with(device_id, :disable_focus_mode)
                                                            .and_raise(BrowserStackTestExecutionError.new('HomeScreenUITests', 'disable_focus_mode', "Failed"))
      expect { @percy_session.stop_server }.to_not raise_error
    end

    it 'should run enable_notifications' do
      expect(BrowserStack.logger).to receive(:info).with('Stopping server')
      allow(File).to receive(:read).with('/tmp/session_file').and_return("{\"env\":{\"PLATFORM\":\"iphone\",\"DISABLE_SMS_NOTIFICATION\": true}}")
      expect(BrowserStack::DeviceLogger).to receive(:destroy).with(device_id)
      expect(PrivoxyManager).to receive(:reset_proxy).with(device_id, device)
      expect(@percy_session.browserup_manager).to receive(:clean_browserup)
      expect(Utils).to receive(:enable_notifications).with(device_id)

      @percy_session.stop_server
    end

    it 'should handle error from enable_notifications' do
      expect(BrowserStack.logger).to receive(:info).with('Stopping server')
      allow(File).to receive(:read).with('/tmp/session_file').and_return("{\"env\":{\"PLATFORM\":\"iphone\",\"DISABLE_SMS_NOTIFICATION\": true}}")
      expect(BrowserStack::DeviceLogger).to receive(:destroy).with(device_id)
      expect(PrivoxyManager).to receive(:reset_proxy).with(device_id, device)
      expect(@percy_session.browserup_manager).to receive(:clean_browserup)
      expect(Utils).to receive(:enable_notifications).with(device_id).and_raise("Failed")
      expect(DeviceState).to receive(:new).and_return(device_state).twice
      expect(device_state).to receive(:touch_enable_message_notification_failure_file)

      @percy_session.stop_server
    end
  end

  describe '#start_jackproxy' do
    before(:each) do
      allow(File).to receive(:read).with('/tmp/session_file').and_return("{\"env\":{\"PLATFORM\":\"iphone\"}}")
      @percy_session = Percy::PercySession.new(device_id, docker_image)
      expect(@percy_session.jp_manager).not_to be(nil)
    end
    it 'should start jackproxy if only proxy map is given' do
      expect(@percy_session.jp_manager).to receive(:setup_jackproxy).and_return(true)
      expect(@percy_session).to receive(:stop_jackproxy)
      expect(@percy_session).not_to receive(:get_proxy_map)
      expect(@percy_session).not_to receive(:randomize_proxy_map)
      expect(@percy_session).to receive(:save_proxy_map)
      expect(@percy_session.jp_manager).to receive(:start_jackproxy)
      @percy_session.start_jackproxy(['p1', 'p2'], "random_proxymap")
    end

    it 'should start jackproxy if proxy map url is given' do
      expect(@percy_session.jp_manager).to receive(:setup_jackproxy).and_return(true)
      expect(@percy_session).to receive(:stop_jackproxy)
      expect(@percy_session).to receive(:get_proxy_map)
      expect(@percy_session).to receive(:randomize_proxy_map)
      expect(@percy_session).to receive(:save_proxy_map)
      expect(@percy_session.jp_manager).to receive(:start_jackproxy)
      @percy_session.start_jackproxy(['p1', 'p2'], "random_proxymap", {}, "https://xyz.com", "abc")
    end

    it 'should throw error' do
      expect(@percy_session.jp_manager).to receive(:setup_jackproxy).and_return(false)
      expect { @percy_session.start_jackproxy(['p1', 'p2'], "random_proxymap") }.to raise_error(RuntimeError)
    end
  end

  describe '#stop_jackproxy' do
    before(:each) do
      allow(DeviceManager).to receive(:device_configuration_check).with(device_id).and_return(device)
      allow(DeviceManager).to receive(:session_file).with(device_id).and_return('/tmp/session_file')
      allow(File).to receive(:read).with('/tmp/session_file').and_return("{\"env\":{\"PLATFORM\":\"iphone\"},\"renderer_key\":\"1234#abcd\",\"jp_executable\":\"random\"}")
    end

    it('should stop jackproxy') do
      expect(@percy_session.jp_manager).to receive(:stop_jackproxy?).and_return(true)
      expect(@percy_session).to receive(:delete_proxy_map)
      expect(@percy_session.stop_jackproxy(renderer_key: "cleanup")).to eql(true)
    end

    it('should return false') do
      expect(@percy_session.jp_manager).to receive(:stop_jackproxy?).and_return(false)
      expect(@percy_session.stop_jackproxy(renderer_key: "cleanup")).to eql(false)
    end
  end

  describe '#health_check?' do
    let(:browserup_port) { 'some_port' }

    it 'should return true if health check passes' do
      expect(@percy_session.browserup_manager).to receive(:browserup_running?).and_return(true)
      expect(@percy_session.browserup_manager).to receive(:browserup_proxy_port).and_return(browserup_port)
      expect(PrivoxyManager).to receive(:percy_config_set?).with(device, browserup_port).and_return(true)
      expect(@percy_session.health_check?).to eql(true)
    end

    it 'should return false if health check fails' do
      expect(@percy_session.browserup_manager).to receive(:browserup_running?).and_return(false)
      expect(PrivoxyManager).not_to receive(:percy_config_set?)
      expect(@percy_session.health_check?).to eql(false)
    end
  end

  describe '#cleanup' do
    it 'should perform cleanup' do
      expect(@percy_session.browserup_manager).to receive(:clean_browserup)
      expect(BrowserStack::DeviceLogger).to receive(:destroy).with(device_id)
      expect(@percy_session).to receive(:stop_jackproxy)
      expect(@percy_session).to receive(:delete_extra_files)
      expect(@percy_session).to receive(:clean_jp_executables)
      @percy_session.cleanup
    end
  end

  describe '#running?' do
    it 'should return false on no file existing' do
      expect(MobileSessionInfo).to receive(:file_path).with('ABC123').and_return('/usr/tmp/ABC123/session.json')
      expect(File).to receive(:file?).with('/usr/tmp/ABC123/session.json').and_return(false)
      res = Percy::PercySession.running?('ABC123')
      expect(res).to eq({ "status" => false }.to_json)
    end

    it 'should return false on file mtime > 180s' do
      expect(MobileSessionInfo).to receive(:file_path).with('ABC123').and_return('/usr/tmp/ABC123/session.json')
      expect(File).to receive(:file?).with('/usr/tmp/ABC123/session.json').and_return(true)
      expect(File).to receive(:mtime).with('/usr/tmp/ABC123/session.json').and_return(Time.now - 900)
      res = Percy::PercySession.running?('ABC123')
      expect(res).to eq({ "status" => false }.to_json)
    end

    it 'should return true on file mtime < 180s' do
      expect(MobileSessionInfo).to receive(:file_path).with('ABC123').and_return('/usr/tmp/ABC123/session.json')
      expect(File).to receive(:file?).with('/usr/tmp/ABC123/session.json').and_return(true)
      expect(File).to receive(:mtime).with('/usr/tmp/ABC123/session.json').and_return(Time.now)
      res = Percy::PercySession.running?('ABC123')
      expect(res).to eq({ "status" => true }.to_json)
    end
  end

  describe '#keep_alive' do
    it 'should throw error if file does not exist' do
      expect(MobileSessionInfo).to receive(:file_path).with('ABC123').and_return('/usr/tmp/ABC123/session.json')
      expect(File).to receive(:file?).with('/usr/tmp/ABC123/session.json').and_return(false)
      res = Percy::PercySession.keep_alive('ABC123')
      expect(res).to be(false)
    end

    it 'should update mtime if file exists' do
      expect(MobileSessionInfo).to receive(:file_path).with('ABC123').and_return('/usr/tmp/ABC123/session.json')
      expect(File).to receive(:file?).with('/usr/tmp/ABC123/session.json').and_return(true)
      expect(FileUtils).to receive(:touch).with('/usr/tmp/ABC123/session.json').and_return(['/usr/tmp/ABC123/session.json'])
      res = Percy::PercySession.keep_alive('ABC123')
      expect(res).to eq(['/usr/tmp/ABC123/session.json'])
    end
  end

  describe '#handle_proxy_map' do
    before(:each) do
      @percy_session = Percy::PercySession.new(device_id, docker_image)
    end
    it 'should download and randomize proxy_map if received correct proxy_map_url' do
      temp_json = "{\"render.percy.local\":\"xyz\"}"
      expected_json = "{\"render.percy.acb\":\"xyz\"}"
      allow(Net::HTTP).to receive(:get).and_return(temp_json)
      expect(@percy_session).to receive(:save_proxy_map).with(expected_json)
      @percy_session.handle_proxy_map(nil, "https://abc.com", "acb")
    end

    it 'should throw error if not able to download proxy map' do
      allow(Net::HTTP).to receive(:get).and_raise(SocketError)
      expect { @percy_session.handle_proxy_map(nil, "https://abc.com", "xyz") }.to raise_error(SocketError)
    end
  end

  describe 'percy_setup_automate_session_handler' do
    before do
      allow(Percy::PercyUtil).to receive(:logit).with('poa_setup_automate_session_api').and_yield(
        {
          product: 'percy',
          success: false,
          message: ''
        }
      )
      @percy_session = Percy::PercySession.new(device_id, docker_image)
    end

    it 'should call setup with POA' do
      allow(poa_setup_class).to receive(:new).and_return(poa_setup_instance)
      allow(poa_setup_instance).to receive(:setup)
      expect(poa_setup_instance).to receive(:setup).exactly(1).times.with(
        { 'param' => '123', 'device_id' => device_id }
      )
      expect(@percy_session.percy_setup_automate_session_handler({ 'param' => '123' })).to eq(
        "Completed setting up Percy On Automate Session"
      )
    end

    it 'return 500 and error when setting up POA' do
      allow(poa_setup_class).to receive(:new).and_return(poa_setup_instance)
      allow(poa_setup_instance).to receive(:setup).and_raise('Error setting up POA')
      expect(poa_setup_instance).to receive(:setup).exactly(1).times.with(
        { 'param' => '123', 'device_id' => device_id }
      )
      expect(@percy_session.percy_setup_automate_session_handler({ 'param' => '123' })).to eq([500, "Error setting up POA"])
    end
  end

  describe 'percy_dom_metadata_finalize_handler' do
    before do
      allow(Percy::PercyUtil).to receive(:logit).with('percy_dom_metadata_finalize_handler').and_yield(
        {
          product: 'percy',
          success: false,
          message: ''
        }
      )
      @percy_session = Percy::PercySession.new(device_id, docker_image)
    end

    it 'should return true' do
      allow(poa_dom_metadata_handler_class).to receive(:new).and_return(poa_dom_metadata_handler_class_instance)
      allow(poa_dom_metadata_handler_class_instance).to receive(:dom_metadata_finalize).and_return({ 'url' => 'testURL' })
      expect(@percy_session.percy_dom_metadata_finalize_handler({ 'param' => '123' })).to eq('testURL')
    end

    it 'return 500 and error when finalize DOM:' do
      allow(poa_dom_metadata_handler_class).to receive(:new).and_return(poa_dom_metadata_handler_class_instance)
      allow(poa_dom_metadata_handler_class_instance).to receive(:dom_metadata_finalize).and_raise('Error finalizing DOM')
      expect(@percy_session.percy_dom_metadata_finalize_handler({ 'param' => '123' })).to eq([500, "Error finalizing DOM"])
    end
  end

  describe 'percy_dom_metadata_upload_handler' do
    before do
      allow(Percy::PercyUtil).to receive(:logit).with('percy_dom_metadata_upload_handler').and_yield(
        {
          product: 'percy',
          success: false,
          message: ''
        }
      )
      @percy_session = Percy::PercySession.new(device_id, docker_image)
    end

    it 'should return true if feature_flags is not passed' do
      allow(poa_screenshot_manager_class).to receive(:new).and_return(poa_screenshot_manager_instance)
      allow(poa_screenshot_manager_instance).to receive(:download_and_execute_metadata_script)
        .with('zipped').and_return(true)
      expect(@percy_session.percy_dom_metadata_upload_handler({ 'param' => '123', 'feature_flags' => 'zipped' }))
        .to eq(true)
    end

    it 'should return true' do
      allow(poa_screenshot_manager_class).to receive(:new).and_return(poa_screenshot_manager_instance)
      allow(poa_screenshot_manager_instance).to receive(:download_and_execute_metadata_script)
        .with(nil).and_return(true)
      expect(@percy_session.percy_dom_metadata_upload_handler({ 'param' => '123' })).to eq(true)
    end

    it 'return 500 and error when download and execute DOM metadata:' do
      allow(poa_screenshot_manager_class).to receive(:new).and_return(poa_screenshot_manager_instance)
      allow(poa_screenshot_manager_instance).to receive(:download_and_execute_metadata_script).and_raise('Error')
      expect(@percy_session.percy_dom_metadata_upload_handler({ 'param' => '123' })).to eq([500, "Error"])
    end
  end

  describe 'percy_screenshot_handler' do
    let(:devices_json) do
      { '123' => {
        'os' => 'ios'
      } }
    end
    before do
      allow(Percy::PercyUtil).to receive(:logit).with('poa_screenshot_api').and_yield(
        {
          product: 'percy',
          success: false,
          message: ''
        }
      )
      @percy_session = Percy::PercySession.new(device_id, docker_image)
    end

    it 'should call setup with POA' do
      allow(poa_screenshot_manager_class).to receive(:new).and_return(poa_screenshot_manager_instance)
      allow(poa_screenshot_manager_instance).to receive(:capture_screenshot).and_return('image_hash')
      expect(poa_screenshot_manager_instance).to receive(:capture_screenshot).exactly(1).times.with(no_args)
      expect(@percy_session.percy_screenshot_handler({ 'param' => '123' }, devices_json: devices_json)).to eq('image_hash')
    end

    it 'return 500 and error when capturing screenshots:' do
      allow(poa_screenshot_manager_class).to receive(:new).and_return(poa_screenshot_manager_instance)
      allow(poa_screenshot_manager_instance).to receive(:capture_screenshot).and_raise('Error capturing screenshot')
      expect(poa_screenshot_manager_instance).to receive(:capture_screenshot).exactly(1).times.with(no_args)
      expect(@percy_session.percy_screenshot_handler({ 'param' => '123' }, devices_json: devices_json)).to eq([500, "Error capturing screenshot"])
    end
  end
end
