require_relative '../../spec_helper'
require_relative '../../../lib/percy/jackproxy_manager'

describe Percy::JackproxyManager do
  let(:jp_executable) { "1aadff997f97a83ded9df65c1d96d3a613c00ee1-darwin-amd64" }
  let(:jp_deps) { "/usr/local/.browserstack/deps/percy/jackproxy" }
  let(:jp_port) { '28081' }
  let(:jp_https_port) { '38081' }

  before(:all) do
    @jp_manager = Percy::JackproxyManager.new({ "port" => "8081" }, "1aadff997f97a83ded9df65c1d96d3a613c00ee1-darwin-amd64")
  end

  describe '#setup_jackproxy' do
    it 'should exit with true if jackproxy binary is present' do
      expect(File).to receive(:file?).with("#{jp_deps}/binaries/#{jp_executable}").and_return(true)
      expect(@jp_manager.setup_jackproxy).to eql(true)
    end

    it 'should download jackproxy binary if file is not present' do
      expect(File).to receive(:file?).with("#{jp_deps}/binaries/#{jp_executable}").and_return(false)
      expect(@jp_manager).to receive(:system).with(/create-dirs/).and_return(true)
      expect(@jp_manager.setup_jackproxy).to eql(true)
    end
  end

  describe '#stop_jackproxy' do
    it 'should kill jackproxy process and return true' do
      expect(BrowserStack::OSUtils).to receive(:kill_process).with("jackproxy", "'port #{jp_port}'")
      expect(BrowserStack::OSUtils).to receive(:is_process_running?).with("jackproxy", "'port #{jp_port}'").and_return(false)
      expect(@jp_manager.stop_jackproxy?).to eql(true)
    end

    it 'should return false when the jacproxy process is not killed' do
      expect(BrowserStack::OSUtils).to receive(:kill_process).with("jackproxy", "'port #{jp_port}'")
      expect(BrowserStack::OSUtils).to receive(:is_process_running?).with("jackproxy", "'port #{jp_port}'").and_return(true)
      expect(@jp_manager.stop_jackproxy?).to eql(false)
    end
  end

  describe '#jp_executable_path' do
    it 'should return the jackproxy executable path' do
      expect(@jp_manager.jp_executable_path).to eql("#{jp_deps}/binaries/#{jp_executable}")
    end
  end

  describe '#jackproxy_log_file_path' do
    it 'should return the jackproxy log path' do
      expect(@jp_manager.jackproxy_log_file_path).to include("jackproxy.#{jp_port}.log")
    end
  end

  describe '#proxy_map_file_path' do
    it 'should return the proxy map file path' do
      stub_const("Percy::JackproxyManager::JACKPROXY_FOLDER", '/sample/folder')
      expect(@jp_manager).to receive(:proxy_map_file_name).and_return('random_name')
      expect(@jp_manager.proxy_map_file_path).to eql("/sample/folder/proxymapdata/random_name")
    end
  end
end
