require_relative '../../spec_helper'
require_relative '../../../lib/percy/util'

describe <PERSON>::<PERSON><PERSON><PERSON> do
  let(:data_success) do
    {
      success: true,
      comparison_id: 123456,
      message: '',
      browser_version: 'chrome_96'
    }
  end
  let(:data_failure) do
    {
      success: false,
      comparison_id: 123456,
      message: '',
      browser_version: 'chrome_96'
    }
  end
  let(:event_name) { 'dummy_event' }
  let(:file_name) { 'some_file_name' }
  let(:file_content) { 'some_content' }

  describe '#send_instrumentation' do
    it 'should send instrumentation to cls' do
      expect(<PERSON>::<PERSON>).to receive(:send_to_cls)
      Percy::<PERSON><PERSON><PERSON>.send_instrumentation(event_name, data_success)
    end

    it 'should send instrumentation to pager' do
      data_success.delete(:comparison_id)
      expect(Percy::<PERSON>).to receive(:send_to_pager)
      Percy::<PERSON><PERSON><PERSON>.send_instrumentation(event_name, data_success)
    end
  end

  describe '#send_to_cls' do
    it 'should send events to cls' do
      allow(<PERSON>::<PERSON><PERSON><PERSON>).to receive(:push_to_cls)
      expect(BrowserStack.logger).to receive(:info).with("Data sent to cls")
      Percy::Percy<PERSON><PERSON>.send_to_cls(event_name, data_success)
    end

    it 'should handle if error occurs' do
      allow(<PERSON>::<PERSON>Util).to receive(:push_to_cls).and_raise(StandardError)
      expect(BrowserStack.logger).to receive(:info).with(/failed to send to cls:/)
      <PERSON>::<PERSON>Util.send_to_cls(event_name, data_success)
    end

    it 'should not send to pager in case of success=false' do
      allow(<PERSON>::<PERSON>Util).to receive(:push_to_cls).and_raise(StandardError)
      expect(BrowserStack.logger).to receive(:info).with(/failed to send to cls:/)
      expect(Percy::PercyUtil).to receive(:send_to_pager)
      Percy::PercyUtil.send_to_cls(event_name, data_failure)
    end
  end

  describe '#send_to_pager' do
    it 'should send events to pager' do
      expect(BrowserStack::Zombie).to receive(:push_logs)
      expect(BrowserStack.logger).to receive(:info).with("Data sent to pager")
      Percy::PercyUtil.send_to_pager(event_name, data_success)
    end

    it 'should handle if error occurs' do
      expect(BrowserStack::Zombie).to receive(:push_logs).and_raise(StandardError)
      expect(BrowserStack.logger).to receive(:info).with(/failed to send to pager:/)
      Percy::PercyUtil.send_to_pager(event_name, data_success)
    end
  end

  describe '#file_with_lock' do
    it 'writes data to a file with lock' do
      file_double = double('file', flock: true, rewind: true, write: true, flush: true, truncate: true, pos: true)
      expect(File).to receive(:open).with(file_name, File::RDWR | File::CREAT, 0o644).and_yield(file_double)
      expect(file_double).to receive(:write).with(file_content)
      Percy::PercyUtil.write_to_file_with_lock(file_name, file_content)
    end

    it 'read data to a file with lock' do
      expect(File).to receive(:open).with(file_name, 'r').and_yield(double('file', flock: true, read: file_content))
      result = Percy::PercyUtil.read_file_with_lock(file_name)
      expect(result).to eq(file_content)
    end

    it 'logs timeout error when lock cannot be acquired' do
      allow(File).to receive(:open).and_yield(double('file', flock: proc { raise(Timeout::Error) }))
      allow(Timeout).to receive(:timeout).and_raise(Timeout::Error)
      expect(BrowserStack.logger).to receive(:info).with("Timeout Error while acquiring lock on file: some_file_name, Timeout::Error")
      Percy::PercyUtil.read_file_with_lock(file_name)
    end
  end
end
