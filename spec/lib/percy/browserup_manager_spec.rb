require_relative '../../spec_helper'
require_relative '../../../lib/percy/browserup_manager'

describe Percy::BrowserupManager do
  let(:bup_proxy_port) { '58081' }
  let(:bup_jar) { 'browserup.jar' }

  before(:all) do
    @bup_manager = Percy::BrowserupManager.new({ "port" => "8081" }, "123")
  end

  describe '#setup_browserup' do
    it 'should return true if browserup is running' do
      expect(@bup_manager).to receive(:browserup_running?).and_return(true)
      expect(@bup_manager.setup_browserup).to eql(true)
    end

    context 'with browserup not running' do
      before(:each) do
        expect(@bup_manager).to receive(:browserup_running?).and_return(false)
        expect(@bup_manager).to receive(:start_browserup).and_return(true)
      end

      it 'and return true when pid file found after start' do
        expect(File).to receive(:exist?).with(/browserup_proxy_#{bup_proxy_port}_pid/).and_return(true)
        expect(@bup_manager.setup_browserup).to eql(true)
      end

      it 'and raise_error when pid file not found after start' do
        allow(@bup_manager).to receive(:sleep).at_most(10).times
        expect(File).to receive(:exist?).with(/browserup_proxy_#{bup_proxy_port}_pid/).and_return(false).exactly(10).times
        expect { @bup_manager.setup_browserup }.to raise_error(RuntimeError)
      end
    end
  end

  describe '#browserup_proxy_port' do
    it 'should return the browserup proxy port' do
      expect(@bup_manager.browserup_proxy_port).to eql(bup_proxy_port)
    end
  end

  describe '#browserup_log_file_path' do
    it 'should return the browserup log path' do
      expect(@bup_manager.browserup_log_file_path).to include("browserup_123.log")
    end
  end

  describe '#clean_browserup' do
    it 'should kill and clean browerup' do
      allow(@bup_manager).to receive(:sleep)
      expect(@bup_manager).to receive(:system).with(/grep -i browserup.*xargs kill -9/).and_return(true)
      expect(File).to receive(:exist?).with(/browserup_proxy_#{bup_proxy_port}_pid/).and_return(true)
      expect(File).to receive(:delete).with(/browserup_proxy_#{bup_proxy_port}_pid/).and_return(true)
      expect(BrowserStack.logger).to receive(:info).with('PID file wasn\'t deleted on killing browserup')
      expect(@bup_manager.clean_browserup).to eql(nil)
    end

    it 'should kill browserup and pid file is missing' do
      allow(@bup_manager).to receive(:sleep)
      expect(@bup_manager).to receive(:system).with(/grep -i browserup.*xargs kill -9/).and_return(true)
      expect(File).to receive(:exist?).with(/browserup_proxy_#{bup_proxy_port}_pid/).and_return(false)
      expect(@bup_manager.clean_browserup).to eql(nil)
    end
  end
end
