require_relative '../spec_helper'
require_relative '../../lib/privoxy_manager'

describe PrivoxyManager do
  describe '.write_privoxy_conf' do
    before do
      @settings = {
        'templates_dir' => '/usr/local/.browserstack/realmobile/templates',
        'pacfile_dir' => '/usr/local/.browserstack/config',
        'privoxy_socket_timeout' => 150,
        'privoxy_server_timeout' => 450,
        'privoxy_listen_port_offset' => 40_000,
        'privoxy_conf_dir' => '/usr/local/.browserstack/privoxy',
        'privoxy_logs_dir' => '/var/log/browserstack/privoxy',
        'privoxy_templates_dir' => '/usr/local/.browserstack/privoxy/templates',
        'plist_dir_user' => '/Library/LaunchAgents/',
        'privoxy_templates_content' => 'BROWSERSTACK DEFAULT TEMPLATE FILE',
        'proxy_whitelisted_hosts' => ['mdm-eu.browserstack.com',
                                      'gateway.push.apple.com',
                                      '*.apple.com',
                                      '*.browserstack.com',
                                      '*.bsstag.com',
                                      '*.icloud.com']
      }

      PrivoxyManager.configure(@settings)
      @device = '1234'
      @current_device_config = { 'port' => 8086, 'selenium_port' => 8086 }
      @privoxy_options = { repeater_host_port: 'repeater.com', hosts: ['/'] }
      @privoxy_options_hosts_only = { repeater_host_port: 'repeater.com', hosts: ['mydomain.com:80'] }
    end

    it 'writes the expected host exceptions in the action file' do
      PrivoxyManager.send(:write_privoxy_conf, @device, @current_device_config, @privoxy_options)
      output = File.read(PrivoxyManager.send(:privoxy_conf_path, @current_device_config))
      @settings['proxy_whitelisted_hosts'].each do |exception|
        expect(output).to include("forward #{exception} .")
      end
    end

    it 'writes the expected hosts in the action file for default all hosts forwarding' do
      PrivoxyManager.send(:write_privoxy_conf, @device, @current_device_config, @privoxy_options)
      output = File.read(PrivoxyManager.send(:privoxy_conf_path, @current_device_config))
      expect(output).to include("forward-socks5 / repeater.com .")
    end

    it 'writes the expected hosts in the action file for only specified hosts' do
      PrivoxyManager.send(:write_privoxy_conf, @device, @current_device_config, @privoxy_options_hosts_only)
      output = File.read(PrivoxyManager.send(:privoxy_conf_path, @current_device_config))
      expect(output).not_to include("forward-socks5 / repeater.com .")
      expect(output).to include("forward-socks5 mydomain.com:80 repeater.com .")
    end
  end

  describe '.percy_cli_port' do
    let(:percy_cli_port) { 58086 }
    let(:current_device_config) do
      { 'selenium_port' => 8086 }
    end

    it 'returns correct cli port' do
      expect(PrivoxyManager.percy_cli_port(current_device_config)).to eql(percy_cli_port)
    end
  end

  describe '.percy_config_set?' do
    let(:device_config) { { some_config: :value } }
    let(:privoxy_path) { '/some/path' }
    let(:percy_forwarding_port) { '58080' }

    it 'returns true if percy forwarding rule is set' do
      expect(PrivoxyManager).to receive(:privoxy_conf_path).with(device_config).and_return(privoxy_path)
      expect(File).to receive(:readlines).with(privoxy_path).and_return(["some other config", "forward / :#{percy_forwarding_port}"])
      expect(PrivoxyManager.percy_config_set?(device_config, percy_forwarding_port)).to eql(true)
    end

    it 'returns false if percy forwarding rule is not set' do
      expect(PrivoxyManager).to receive(:privoxy_conf_path).with(device_config).and_return(privoxy_path)
      expect(File).to receive(:readlines).with(privoxy_path).and_return(["some other config"])
      expect(PrivoxyManager.percy_config_set?(device_config, percy_forwarding_port)).to eql(false)
    end
  end
end
