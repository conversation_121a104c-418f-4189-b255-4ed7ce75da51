require_relative '../../lib/utils/idevice_utils'
require_relative '../../lib/ifuse'

describe Ifuse do
  let(:udid) { '1234' }
  let(:mount_point) { "/Users/<USER>/#{udid}" }
  let(:subject) { Ifuse.new(udid) }

  # Creating sample status objects, which OSUtils.execute can return
  let(:success_status) do
    system("ls /tmp > /dev/null 2>&1")
    $CHILD_STATUS
  end

  let(:failure_status) do
    system("fail > /dev/null 2>&1")
    $CHILD_STATUS
  end

  before do
    allow(BrowserStack::OSUtils).to receive(:execute)
  end

  describe '.run' do
    it 'mounts, executes command if passed, unmounts' do
      cmd = 'foobar'
      expect(subject).to receive(:mount_filesystem)
      expect(BrowserStack::OSUtils).to receive(:execute).with(cmd)
      expect(subject).to receive(:unmount_filesystem)

      subject.run(cmd)
    end

    it 'mounts, executes block if passed, unmounts' do
      expect(subject).to receive(:mount_filesystem)
      expect(subject).to receive(:unmount_filesystem)

      expect { |b| subject.run(&b) }.to yield_with_no_args
    end
  end

  describe '.mount_filesystem' do
    it 'mounts the file system, then runs sanity check' do
      expect(BrowserStack::OSUtils).to receive(:execute).with(/#{USER}/)
      expect(BrowserStack::OSUtils).to receive(:execute).with(/#{IFUSE}/)
      expect(subject).to receive(:mount_point).twice
      expect(subject).to receive(:sanity_check_filesystem)

      subject.send :mount_filesystem
    end
  end

  describe '.unmount_filesystem' do
    before do
      allow(File).to receive(:exist?).with(mount_point).and_return(true)
    end

    context 'when there is no unmounting error' do
      it 'executes unmount once and removes mount point' do
        expect(BrowserStack::OSUtils).to receive(:execute)
          .with(/umount/, true)
          .and_return([anything, success_status])
          .once

        expect(FileUtils).to receive(:remove_dir)

        subject.send :unmount_filesystem
      end
    end

    context 'when there is an unmounting error' do
      before do
        allow(subject).to receive(:sleep)
      end

      it 'tries executing unmount 5 times, sends zombie push and raises error' do
        expect(BrowserStack::OSUtils).to receive(:execute)
          .with(/umount/, true)
          .and_return([anything, failure_status])
          .exactly(5).times

        expect { subject.send :unmount_filesystem }.to raise_error(/umount command failed/)
      end
    end
  end
end
