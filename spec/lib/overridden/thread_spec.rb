require_relative '../../../lib/overridden/thread'
require_relative '../../../config/constants'

describe Thread do
  describe '.bs_run' do
    let(:fake_logger_params) { { some_key: 'fake_value' } }

    before do
      Thread.current[:logger_params] = fake_logger_params
    end

    it 'should copy logger params' do
      t = Thread.bs_run {}

      expect(t[:logger_params]).to eq(fake_logger_params)
    end

    after do
      Thread.current[:logger_params] = nil
    end
  end

  # Check lib/overridden/thread for details
  it 'should not add new Thread.new use Thread.bs_run instead' do
    constants = YAML.load_file("#{__dir__}/../../../config/constants.yml")
    repo_root = constants['mobile_root']
    command = "grep -wR \"Thread\\.new\" #{repo_root} --include=\"*.rb\" --include=\"*.ru\" | wc -l"
    puts "Running: #{command}"
    expect(`#{command}`.to_i).to eq(4)
  end
end
