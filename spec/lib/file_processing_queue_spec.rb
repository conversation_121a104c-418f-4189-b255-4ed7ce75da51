require_relative '../spec_helper'
require_relative '../../lib/file_processing_queue'

class FileMockConfig
  def self.all
    {
      "files_to_upload_dir" => "/tmp/files_to_upload",
      "images_to_upload_dir" => "/tmp/IMAGES"
    }
  end
end

describe BrowserStack::FileProcessingQueue do
  let(:file_processing_queue) do
    BrowserStack::FileProcessingQueue.new("TEST_UPLOADER", "files_to_upload_dir")
  end

  before(:each) do
    allow(BrowserStack::Configuration).to receive(:new).and_return FileMockConfig
    allow(BrowserStack::Zombie).to receive(:push_logs)
    FileUtils.mkdir_p("/tmp/files_to_upload")
  end

  after(:each) do
    FileUtils.rm_rf("/tmp/files_to_upload")
    FileUtils.rm_rf("/tmp/IMAGES")
  end

  it "should create channel directory if doesn't exist" do
    channel = 'images_to_upload_dir'
    directory = FileMockConfig.all[channel]
    expect(File.directory?(directory)).to be false
    BrowserStack::FileProcessingQueue.new("test_dir", channel, 0)
    expect(File.directory?(directory)).to be true
  end

  it "should create a FileProcessingQueue object" do
    expect(file_processing_queue).to be_a BrowserStack::FileProcessingQueue
  end

  it "should yield the oldest file in directory for processing" do
    FileUtils.touch("/tmp/files_to_upload/first.json")
    sleep 0.001
    FileUtils.touch("/tmp/files_to_upload/second.json")
    allow(File).to receive(:delete).with("/tmp/files_to_upload/first.json_lock")

    files_yielded = []
    file_processing_queue.process_files do |request_file|
      files_yielded << request_file
    end
    expect(files_yielded[0]).to be_eql("/tmp/files_to_upload/first.json_lock")
  end

  it "should return nil if no file is present in directory" do
    files_yielded = []
    file_processing_queue.process_files do |request_file|
      files_yielded << request_file
    end
    expect(files_yielded).to be_eql([])
  end

  it "should return nil if name is screenshot_instrumentation and file age is less than 20 minutes" do
    file_processing_queue = BrowserStack::FileProcessingQueue.new("screenshot_instrumentation", "files_to_upload_dir", 2)
    FileUtils.touch("/tmp/files_to_upload/first.json")
    allow(File).to receive(:mtime).and_return(Time.now - 19 * 60)
    files_yielded = []
    file_processing_queue.process_files do |request_file|
      files_yielded << request_file
    end
    expect(files_yielded).to be_eql([])
  end

  it "should return file if name is screenshot_instrumentation and file age is more than 20 minutes" do
    file_processing_queue = BrowserStack::FileProcessingQueue.new("screenshot_instrumentation", "files_to_upload_dir", 2)
    FileUtils.touch("/tmp/files_to_upload/first.json")
    allow(File).to receive(:mtime).and_return(Time.now - 21 * 60)
    files_yielded = []
    files_yielded = []
    file_processing_queue.process_files do |request_file|
      files_yielded << request_file
    end
    expect(files_yielded[0]).to be_eql("/tmp/files_to_upload/first.json_lock")
  end

  it "should call update_screenshot_instrumentation_with_lock if process is upload and exception is raised" do
    FileUtils.touch("/tmp/files_to_upload/first.json")
    file_processing_queue = BrowserStack::FileProcessingQueue.new("image_uploader", "files_to_upload_dir", 0)
    expect(File).to receive(:delete).with("/tmp/files_to_upload/first.json_lock").and_raise(StandardError)
    expect(File).to receive(:read).with("/tmp/files_to_upload/first.json_lock").and_return("{\"session_id\": \"session_id\"}")
    expect(Utils).to receive(:get_screenshot_instrumentation_file).and_return("dummy")
    expect(File).to receive(:exist?).and_return(true)
    expect(Utils).to receive(:update_screenshot_instrumentation_with_lock).with("dummy", "upload", "failed", anything)
    expect(File).to receive(:delete).with("/tmp/files_to_upload/first.json_lock")

    files_yielded = []
    file_processing_queue.process_files do |request_file|
      files_yielded << request_file
    end
    expect(files_yielded[0]).to be_eql("/tmp/files_to_upload/first.json_lock")
  end

  it "should call update_screenshot_instrumentation_with_lock if process is convert and exception is raised" do
    FileUtils.touch("/tmp/files_to_upload/first.json")
    file_processing_queue = BrowserStack::FileProcessingQueue.new("file_converter", "files_to_upload_dir", 0)
    expect(File).to receive(:delete).with("/tmp/files_to_upload/first.json_lock").and_raise(StandardError)
    expect(File).to receive(:read).with("/tmp/files_to_upload/first.json_lock").and_return("{\"session_id\": \"session_id\"}")
    expect(Utils).to receive(:get_screenshot_instrumentation_file).and_return("dummy")
    expect(File).to receive(:exist?).and_return(true)
    expect(Utils).to receive(:update_screenshot_instrumentation_with_lock).with("dummy", "convert", "failed", anything)
    expect(File).to receive(:delete).with("/tmp/files_to_upload/first.json_lock")

    files_yielded = []
    file_processing_queue.process_files do |request_file|
      files_yielded << request_file
    end
    expect(files_yielded[0]).to be_eql("/tmp/files_to_upload/first.json_lock")
  end

  it "should call update_screenshot_instrumentation_with_lock if process is screenshot_instrumentation and exception is raised" do
    file_processing_queue = BrowserStack::FileProcessingQueue.new("screenshot_instrumentation", "files_to_upload_dir", 0)
    FileUtils.touch("/tmp/files_to_upload/first.json")
    allow(File).to receive(:mtime).and_return(Time.now - 21 * 60)
    expect(File).to receive(:delete).with("/tmp/files_to_upload/first.json_lock").and_raise(StandardError)
    expect(File).to receive(:read).with("/tmp/files_to_upload/first.json_lock").and_return("{\"session_id\": \"session_id\"}")
    expect(Utils).to receive(:get_screenshot_instrumentation_file).and_return("dummy")
    expect(File).to receive(:exist?).with("dummy").and_return(true)
    expect(File).to receive(:delete).with("dummy")
    expect(File).to receive(:exist?).with("dummy.lock").and_return(true)
    expect(File).to receive(:delete).with("dummy.lock")
    expect(File).to receive(:delete).with("/tmp/files_to_upload/first.json_lock")

    files_yielded = []
    file_processing_queue.process_files do |request_file|
      files_yielded << request_file
    end
    expect(files_yielded[0]).to be_eql("/tmp/files_to_upload/first.json_lock")
  end
end
