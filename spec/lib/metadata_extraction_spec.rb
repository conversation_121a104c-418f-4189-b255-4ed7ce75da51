require_relative "../../lib/helpers/metadata_extraction"

RSpec.describe MetadataExtraction do
  let(:device_id) { "device123" }
  let(:app_path) { "/fake/app/path" }
  let(:session_id) { "session-001" }
  let(:opts) do
    {
      "session_id" => session_id,
      "app_check_endpoint" => "https://api.example.com/app_check",
      "app_bs_auth" => "fake_auth",
      "app_filename" => "FakeApp.ipa",
      "os" => "ios",
      "user_id" => "user123"
    }
  end
  let(:device_config) do
    {
      "device_name" => "iPhone 14",
      "device_version" => "17.0"
    }
  end

  subject { described_class.new(device_id, app_path, opts) }

  before do
    allow(DeviceManager).to receive(:device_configuration_check).with(device_id).and_return(device_config)
    allow(subject).to receive(:log)
  end

  describe "#validate_app_path" do
    it "returns false if path is nil" do
      subject.instance_variable_set(:@app_path, nil)
      expect(subject.send(:validate_app_path)).to be false
    end

    it "returns false if path is empty" do
      subject.instance_variable_set(:@app_path, "")
      expect(subject.send(:validate_app_path)).to be false
    end

    it "returns false if path is not a directory" do
      allow(File).to receive(:directory?).and_return(false)
      expect(subject.send(:validate_app_path)).to be false
    end

    it "returns true if valid directory" do
      allow(File).to receive(:directory?).with(app_path).and_return(true)
      expect(subject.send(:validate_app_path)).to be true
    end
  end

  describe "#extract_validate_and_get_app_params" do
    before do
      allow(subject).to receive(:validate_app_path).and_return(true)
      allow(subject).to receive(:extract_app_metadata)
      allow(subject).to receive(:validate_extracted_metadata).and_return(true)
      allow(subject).to receive(:verify_app).and_return([true, {}])
    end

    it "returns [true, {}] when all validations pass" do
      result = subject.extract_validate_and_get_app_params
      expect(result).to eq([true, {}])
    end

    it "returns false if app path validation fails" do
      allow(subject).to receive(:validate_app_path).and_return(false)
      expect(subject.extract_validate_and_get_app_params).to eq([false, {}])
    end

    it "logs and returns false on exception" do
      allow(subject).to receive(:extract_app_metadata).and_raise(StandardError, "Oops")
      expect(subject.extract_validate_and_get_app_params).to eq(false)
    end
  end

  describe "#validate_extracted_metadata" do
    it "returns false if any metadata is invalid" do
      subject.instance_variable_set(:@display_name, "")
      subject.instance_variable_set(:@bundle_id, "com.example")
      subject.instance_variable_set(:@minimum_os_version, 14.0)
      subject.instance_variable_set(:@device_family, "iphone")

      expect(subject.send(:validate_extracted_metadata)).to be false
    end

    it "returns true if all metadata is valid" do
      subject.instance_variable_set(:@display_name, "MyApp")
      subject.instance_variable_set(:@bundle_id, "com.example")
      subject.instance_variable_set(:@minimum_os_version, 14.0)
      subject.instance_variable_set(:@device_family, "iphone")

      expect(subject.send(:validate_extracted_metadata)).to be true
    end
  end

  describe "#verify_app" do
    before do
      subject.instance_variable_set(:@display_name, "TestApp")
      subject.instance_variable_set(:@bundle_id, "com.test.app")
      subject.instance_variable_set(:@device_family, "iphone")
      subject.instance_variable_set(:@minimum_os_version, 15.0)
    end

    context "when device family mismatches" do
      before do
        device_config["device_name"] = "iPad"
        subject.instance_variable_set(:@device, device_config)
      end

      it "returns [false, app_related_params]" do
        expect(subject.send(:verify_app)).to match([false, hash_including("app_display_name" => "TestApp")])
      end
    end

    context "when OS version is too low" do
      before do
        device_config["device_version"] = "13.0"
        subject.instance_variable_set(:@device, device_config)
      end

      it "returns [false, app_related_params]" do
        expect(subject.send(:verify_app)).to match([false, hash_including("app_display_name" => "TestApp")])
      end
    end

    context "when API responds with success" do
      before do
        allow(HttpUtils).to receive(:send_post).and_return(double(code: 200, body: { app_params: { "some_key" => "value" } }.to_json))
      end

      it "returns true and merged params" do
        result = subject.send(:verify_app)
        expect(result.first).to eq(true)
        expect(result.last).to include("some_key" => "value")
      end
    end

    context "when API responds with error" do
      before do
        allow(HttpUtils).to receive(:send_post).and_return(double(code: 500, body: ""))
      end

      it "returns false" do
        expect(subject.send(:verify_app)).to match([false, hash_including("app_display_name" => "TestApp")])
      end
    end
  end

  describe "#extract_display_name" do
    it "tries multiple fallbacks to extract display name" do
      allow(PlistBuddy).to receive(:get_value_of_key).and_return("TestApp")
      allow(subject).to receive(:plist_file).and_return("Info.plist")
      allow(subject).to receive(:en_proj_plist).and_return(nil)

      expect(subject.send(:extract_display_name)).to eq("TestApp")
    end
  end
end
