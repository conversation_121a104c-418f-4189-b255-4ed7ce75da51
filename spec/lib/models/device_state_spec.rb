require 'timecop'
require_relative '../../../lib/models/device_state'
require_relative '../../../lib/utils/utils'

describe DeviceState do
  device_id = 'fake_udid'
  device_state = DeviceState.new(device_id)
  state_files = device_state.private_methods(false).select do |method|
    method.to_s.end_with?('_file')
  end
  let(:sample_array) { %w[The quick brown fox] }

  state_files.each do |state_file|
    state_file_path = device_state.send(state_file)

    before do
      # Handle any parent directories that need to be created also.
      Utils.touch_file_and_create_parents(state_file_path)
    end

    describe "##{state_file}_present?" do
      let(:state_file_present_method) { "#{state_file}_present?" }

      it "should return true if #{state_file} is present" do
        expect(device_state.send(state_file_present_method)).to be_truthy
      end

      it "should return false if #{state_file} is absent" do
        FileUtils.rm_f(state_file_path)

        expect(device_state.send(state_file_present_method)).to be_falsey
      end
    end

    describe "##{state_file}_to_array" do
      let(:state_file_to_array_method) { "#{state_file}_to_array" }

      it "should read #{state_file} in array" do
        File.write(state_file_path, sample_array.join("\n"))

        expect(device_state.send(state_file_to_array_method)).to eq(sample_array)
      end
    end

    describe "#remove_#{state_file}" do
      let(:state_file_remove_method) { "remove_#{state_file}" }

      it "should delete #{state_file}" do
        device_state.send(state_file_remove_method)

        expect(File.exist?(state_file_path)).to be_falsey
      end
    end

    describe "#touch_#{state_file}" do
      let(:state_file_touch_method) { "touch_#{state_file}" }

      before do
        FileUtils.rm_f(state_file_path)
      end

      it "should create #{state_file}" do
        device_state.send(state_file_touch_method)

        expect(File.exist?(state_file_path)).to be_truthy
      end
    end

    describe "#write_to_#{state_file}" do
      let(:state_file_write_to_method) { "write_to_#{state_file}" }

      before do
        FileUtils.rm_f(state_file_path)
      end

      it "should write to #{state_file}" do
        data = sample_array.join("\n")
        device_state.send(state_file_write_to_method, data)

        expect(File.read(state_file_path)).to eq(data)
      end
    end

    describe "##{state_file}_older_than_days?" do
      days = rand(1..365)
      let(:state_file_older_than_method) { "#{state_file}_older_than_days?" }

      it "should return true if #{state_file} does not exist" do
        FileUtils.rm_f(state_file_path)

        expect(device_state.send(state_file_older_than_method, days)).to be_truthy
      end

      context "#{state_file} is not older than #{days} days" do
        it "should return false" do
          FileUtils.touch(state_file_path)
          expect(device_state.send(state_file_older_than_method, days)).to be_falsey
        end
      end

      context "#{state_file} is older than #{days} days" do
        before do
          FileUtils.touch(state_file_path)
          Timecop.travel(Time.now + (days + 1) * 60 * 60 * 24)
        end

        after do
          Timecop.return
        end

        it "should return true" do
          expect(device_state.send(state_file_older_than_method, days)).to be_truthy
        end
      end
    end

    describe "##{state_file}_older_than_minutes?" do
      minutes = rand(1..365)
      let(:state_file_older_than_method) { "#{state_file}_older_than_minutes?" }

      it "should return true if #{state_file} does not exist" do
        FileUtils.rm_f(state_file_path)

        expect(device_state.send(state_file_older_than_method, minutes)).to be_truthy
      end

      context "#{state_file} is not older than #{minutes} minutes" do
        it "should return false" do
          FileUtils.touch(state_file_path)
          expect(device_state.send(state_file_older_than_method, minutes)).to be_falsey
        end
      end

      context "#{state_file} is older than #{minutes} minutes" do
        before do
          FileUtils.touch(state_file_path)
          Timecop.travel(Time.now + (minutes + 1) * 60)
        end

        after do
          Timecop.return
        end

        it "should return true" do
          expect(device_state.send(state_file_older_than_method, minutes)).to be_truthy
        end
      end
    end

    context "File stat" do
      before do
        FileUtils.touch(state_file_path)
      end

      describe "##{state_file}_created_at" do
        let(:state_file_created_at) { "#{state_file}_created_at" }

        it "should call birthtime for that file" do
          expect(File).to receive(:birthtime).with(state_file_path)
          device_state.send(state_file_created_at)
        end
      end

      describe "##{state_file}_updated_at" do
        let(:state_file_updated_at) { "#{state_file}_updated_at" }

        it "should call mtime for that file" do
          expect(File).to receive(:mtime).with(state_file_path)
          device_state.send(state_file_updated_at)
        end
      end
    end
  end
end
