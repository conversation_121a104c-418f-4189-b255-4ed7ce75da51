require_relative '../spec_helper'
require_relative '../../lib/stale_file_cleaner'

describe BrowserStack::StaleFileCleaner do
  let(:stale_file_cleaner) do
    BrowserStack::StaleFileCleaner.new("TEST_CLEANER", "/tmp/testStaleFile", "*.json", 10)
  end

  before(:each) do
    FileUtils.mkdir_p("/tmp/testStaleFile")
    FileUtils.touch("/tmp/testStaleFile/abc.json")
    FileUtils.touch("/tmp/testStaleFile/xyz.json")
    FileUtils.touch("/tmp/testStaleFile/pqr.json_lock")
  end

  after(:each) do
    FileUtils.rm_rf("/tmp/testStaleFile")
  end

  it "should create a StaleFileCleaner object" do
    expect(stale_file_cleaner).to be_a BrowserStack::StaleFileCleaner
  end

  it "should delate all the files matching the pattern and having age less than threshold" do
    allow(File).to receive(:mtime).and_return(Time.now - 20)
    files_expected_to_be_yielded = ["/tmp/testStaleFile/abc.json", "/tmp/testStaleFile/xyz.json"]

    files_yielded = []
    stale_file_cleaner.process_files do |request_file|
      files_yielded << request_file
    end
    expect(files_yielded).to match_array(files_expected_to_be_yielded)
    expect(Dir["/tmp/testStaleFile/*"]).to match_array(["/tmp/testStaleFile/pqr.json_lock"])
  end

  it "should not delete file if no file present in directory has age lest than more than threshold" do
    allow(File).to receive(:mtime).and_return(Time.now)
    files_yielded = []
    stale_file_cleaner.process_files do |request_file|
      files_yielded << request_file
    end
    expect(files_yielded).to eql([])
    expect(Dir["/tmp/testStaleFile/*"]).to match_array(["/tmp/testStaleFile/abc.json", "/tmp/testStaleFile/xyz.json", "/tmp/testStaleFile/pqr.json_lock"])
  end
end
