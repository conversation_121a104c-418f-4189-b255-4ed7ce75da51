require_relative '../../../../lib/device_setup/backup_manager/managers/cfgutil_backup_manager'
require_relative '../../../../lib/device_setup/backup_manager/managers/manager_abs'
require_relative '../../../../lib/utils/osutils'
require 'fileutils'

describe ******************** do
  let(:device_uuid) { "udid" }
  let(:device_config_ios15) do
    {
      "device_version" => "15.0",
      "device_serial" => "SERIAL",
      "device_name" => "iPhone11,3",
      "sub_region" => "us-west-1e",
      "ip" => "127.0.0.1"
    }
  end
  let(:mobile_sync_dir) { '/Users/<USER>/Library/Application\ Support/MobileSync/Backup' }
  before(:each) do
    allow_any_instance_of(********************).to receive(:device_config).and_return(device_config_ios15)
  end

  describe 'backup_device' do
    it 'should backup device[CFGUtil]' do
      cfgutil_response = { "Command" => "backup", "Type" => "CommandOutput", "Devices" => ["0x1875502644801E"] }
      allow_any_instance_of(CFGUtil).to receive(:backup).and_return(cfgutil_response)
      bm = ********************.new(device_uuid)
      expect(bm.backup_device).to eq(cfgutil_response)
    end
  end

  describe 'machine_provisioned?' do
    it 'should return true if symlink is configured' do
      allow(Dir).to receive(:[]).with("#{mobile_sync_dir}/*").and_return(true)
      bm = ********************.new(device_uuid)
      expect(bm.machine_provisioned?).to eq(true)
    end

    it 'should return false if symlink is not configured' do
      allow(Dir).to receive(:[]).with("#{mobile_sync_dir}/*").and_raise("Operation not permitted")
      bm = ********************.new(device_uuid)
      expect(bm.machine_provisioned?).to eq(false)
    end
  end

  describe 'retrieve_backup' do
    before(:each) do
      allow_any_instance_of(********************).to receive(:clean).and_return(true)
      allow_any_instance_of(********************).to receive(:download).and_return(true)
      allow_any_instance_of(********************).to receive(:extract).and_return(true)
      allow_any_instance_of(********************).to receive(:patch_backup).and_return(true)
    end
    it 'should clean the backup folder and populate backup if force clean is set to true' do
      bm = ********************.new(device_uuid)
      expect(bm).to receive(:clean)
      expect(bm).to receive(:download)
      expect(bm).to receive(:extract)
      expect(bm).to receive(:patch_backup)
      bm.retrieve_backup(force_clean: true)
    end

    it 'should only populate backup if force clean is not set to true' do
      bm = ********************.new(device_uuid)
      expect(bm).not_to receive(:clean)
      expect(bm).to receive(:download)
      expect(bm).to receive(:extract)
      expect(bm).to receive(:patch_backup)
      bm.retrieve_backup(force_clean: false)
    end
  end

  describe 'clean' do
    it 'should remove Backup Folder and downloaded zip' do
      allow(BrowserStack::OSUtils).to receive(:execute).and_return(true)
      bm = ********************.new(device_uuid)
      expect(BrowserStack::OSUtils).to receive(:execute).with(/udid_backup/)
      expect(BrowserStack::OSUtils).to receive(:execute).with(%r{/Users/<USER>/Library/Application\\ Support/MobileSync/Backup/udid})
      bm.clean
    end
  end

  describe 'download' do
    it 'should download the zip from URL' do
      allow(BrowserStack::HttpUtils).to receive(:download).and_return(true)
      bm = ********************.new(device_uuid)
      expect(BrowserStack::HttpUtils).to receive(:download).with(/iPhone11,3_15.0_us-west-1e_backup/, anything)
      bm.download
    end

    it 'should raise an error if it is not able to download backup' do
      allow(BrowserStack::HttpUtils).to receive(:download).and_raise("Permission Denied")
      bm = ********************.new(device_uuid)
      expect(BrowserStack::HttpUtils).to receive(:download).with(/iPhone11,3_15.0_us-west-1e_backup/, anything)
      expect { bm.download }.to raise_error(BackupManager::BackupRetrievalError, "Permission Denied")
    end
  end

  describe 'extract' do
    it 'should extract the files to backup folder' do
      allow(BrowserStack::OSUtils).to receive(:execute).and_return([true, 0])
      bm = ********************.new(device_uuid)
      expect(BrowserStack::OSUtils).to receive(:execute).with(/unzip/, anything)
      bm.extract
    end

    it 'should raise an error if it is not able to extract backup' do
      allow(BrowserStack::OSUtils).to receive(:execute).and_raise("Permission Denied")
      bm = ********************.new(device_uuid)
      expect(BrowserStack::OSUtils).to receive(:execute).with(/unzip/, anything)
      expect { bm.extract }.to raise_error(BackupManager::BackupRetrievalError, /Permission Denied/)
    end

    it 'should raise an error if it is not able to extract backup (non zero status code)' do
      allow(BrowserStack::OSUtils).to receive(:execute).and_return([true, 1])
      bm = ********************.new(device_uuid)
      expect(BrowserStack::OSUtils).to receive(:execute).with(/unzip/, anything)
      expect { bm.extract }.to raise_error(BackupManager::BackupRetrievalError, /unzip command failed/)
    end
  end

  describe 'patch_backup' do
    it 'should patch the extracted backup folder' do
      allow(BrowserStack::PlistBuddy).to receive(:set_key_in_plist).and_return(true)
      bm = ********************.new(device_uuid)
      expect(BrowserStack::PlistBuddy).to receive(:set_key_in_plist).with(/Info.plist/, /Target Identifier/, device_uuid)
      bm.patch_backup
    end

    it 'should raise an error if it is not able to patch the backup' do
      allow(BrowserStack::PlistBuddy).to receive(:set_key_in_plist).and_raise("Permission Denied")
      bm = ********************.new(device_uuid)
      expect(BrowserStack::PlistBuddy).to receive(:set_key_in_plist).with(/Info.plist/, /Target Identifier/, device_uuid)
      expect { bm.patch_backup }.to raise_error(BackupManager::BackupSpecificationError, "Permission Denied")
    end
  end

  describe 'validate_backup' do
    it 'should not raise any errors if validations are passed' do
      allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).with(/Info.plist/, /Target Identifier/).and_return(device_uuid)
      allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).with(/Manifest.plist/, /Lockdown:UniqueDeviceID/).and_return(device_uuid)
      allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).with(/Manifest.plist/, /Lockdown:SerialNumber/).and_return(device_config_ios15["device_serial"])
      bm = ********************.new(device_uuid)
      bm.validate_backup
    end

    it 'should raise corresponding error if validations fail' do
      allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).with(/Info.plist/, anything).and_return("")
      bm = ********************.new(device_uuid)
      expect { bm.validate_backup }.to raise_error(BackupManager::BackupSpecificationError, /Unable to set device id in Info.plist/)
    end

    it 'should raise corresponding error if validations fail' do
      allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).with(/Info.plist/, /Target Identifier/).and_return(device_uuid)
      allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).with(/Manifest.plist/, /Lockdown:UniqueDeviceID/).and_return("")
      bm = ********************.new(device_uuid)
      expect { bm.validate_backup }.to raise_error(BackupManager::BackupSpecificationError, /Unable to set device id in Manifest.plist/)
    end

    it 'should raise corresponding error if validations fail' do
      allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).with(/Info.plist/, /Target Identifier/).and_return(device_uuid)
      allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).with(/Manifest.plist/, /Lockdown:UniqueDeviceID/).and_return(device_uuid)
      allow(BrowserStack::PlistBuddy).to receive(:get_value_of_key).with(/Manifest.plist/, /Lockdown:SerialNumber/).and_return("")
      bm = ********************.new(device_uuid)
      expect { bm.validate_backup }.to raise_error(BackupManager::BackupSpecificationError, /Unable to set serial in Manifest.plist/)
    end
  end

  describe 'backup_ready?' do
    it 'should return false if backup doesnt exist for the device' do
      bm = ********************.new(device_uuid)
      allow_any_instance_of(CFGUtil).to receive(:backup_exists?).and_return(false)
      expect(bm.backup_ready?).to eq false
    end

    it 'should return false if backup validation fails for the device' do
      bm = ********************.new(device_uuid)
      allow_any_instance_of(CFGUtil).to receive(:backup_exists?).and_return(true)
      allow(bm).to receive(:validate_backup).and_raise(BackupManager::BackupSpecificationError)
      expect(bm.backup_ready?).to eq false
    end

    it 'should return true if backup validation is successful for the device' do
      bm = ********************.new(device_uuid)
      allow_any_instance_of(CFGUtil).to receive(:backup_exists?).and_return(true)
      allow(bm).to receive(:validate_backup).and_return(true)
      expect(bm.backup_ready?).to eq true
    end
  end

  describe 'restore_backup' do
    it 'should raise an error if backup_ready? is false' do
      bm = ********************.new(device_uuid)
      allow(bm).to receive(:backup_ready?).and_return(false)
      expect { bm.restore_backup }.to raise_error(BackupManager::BackupRestoreError, /No backup found for udid/)
    end

    it 'should raise an error if cfgutil restore_backup fails' do
      bm = ********************.new(device_uuid)
      allow(bm).to receive(:backup_ready?).and_return(true)
      expect_any_instance_of(CFGUtil).to receive(:restore_backup).with({ source: device_uuid }).and_raise("Restore backup failed")
      expect { bm.restore_backup }.to raise_error(BackupManager::BackupRestoreError, /Restore backup failed/)
    end

    it 'should call cfgutil restore_backup if backup_ready? is true' do
      bm = ********************.new(device_uuid)
      allow(bm).to receive(:backup_ready?).and_return(true)
      expect_any_instance_of(CFGUtil).to receive(:restore_backup).with({ source: device_uuid }).and_return(true)
      bm.restore_backup
    end
  end
end
