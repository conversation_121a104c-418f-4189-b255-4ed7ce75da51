require_relative '../../spec_helper'
require_relative '../../../lib/device_setup/install_check'
require_relative '../../../lib/models/device_state'

describe BrowserStack::InstallCheck do
  let(:device_config) { JSON.parse(fixture('config.json')) }

  let(:device_uuid) { "fake_device_uuid" }
  let(:device_version) { "11.4" }
  let(:device_state) { DeviceState.new(device_uuid) }
  let(:installations_required_file) { device_state.send(:installations_required_file) }
  let(:ppuid) { "fake_provision_profile" }

  let(:chrome_bundle_id) { BrowserStack::Chrome::BUNDLE_ID }
  let(:launcher_bundle_id) { BrowserStack::Launcher::BUNDLE_ID }
  let(:mock_chromium) { double(Chromium) }
  let(:mock_abm) { double(AppleBusinessManagerHelper) }
  let(:chromium_bundle_id) { 'browserstack.chromium.app' }

  let(:app_bundle_id) { "com.google.chrome.ios" }
  let(:app_name) { "Chrome" }
  let(:app_version) { "80.0.3987.95" }
  let(:os) { 'ios' }

  let(:app) do
    double('sample_app', { bundle_id: app_bundle_id, name: app_name, version: app_version })
  end

  let(:install_check) do
    BrowserStack::InstallCheck.new(device_uuid, device_version, ppuid, device_config)
  end

  before do
    allow(IdeviceUtils).to receive(:list_apps).and_raise("timeout")
    install_check.instance_variable_set(:@installations_required, Set.new)
    allow(IdeviceUtils).to receive(:os).with(device_uuid).and_return(os)
    allow(Chromium).to receive(:new).and_return(mock_chromium)
    allow(BrowserStack::AppleBusinessManagerHelper).to receive(:new).and_return(mock_abm)
    allow(mock_abm).to receive(:device_eligible?).and_return(false)
  end

  describe '#initialize' do
    before { FileUtils.touch(installations_required_file) }
    after { FileUtils.rm(installations_required_file) }

    it 'should read from installation_required_file if present' do
      expect(Utils).to receive(:read_file_in_array)
        .with(installations_required_file)
        .and_return([])
      BrowserStack::InstallCheck.new(device_uuid, device_version, ppuid, device_config)
    end

    it 'should initialize @user_installed_apps with required apps if list_apps raises an exception' do
      expect(install_check.instance_variable_get(:@user_installed_apps)).to eq(nil)
    end
  end

  describe '#check_requirements' do
    context 'when full_check is false' do
      before do
        install_check.instance_variable_set(:@full_check, false)
      end

      it 'returns without checking any apps' do
        expect(install_check).not_to receive(:check_missing_browserstack_app)
        expect(install_check).not_to receive(:check_missing_wdas)
        expect(install_check).not_to receive(:check_unsigned_wdas)

        install_check.check_requirements
      end
    end

    context 'when full_check is true' do
      before do
        install_check.instance_variable_set(:@full_check, true)
      end

      it 'should run all the checks' do
        expect(install_check).to receive(:check_missing_browserstack_app)

        expect(install_check).to receive(:check_missing_wdas)
        expect(install_check).to receive(:check_unsigned_wdas)

        expect(install_check).to receive(:app_installation_required?)
          .with(instance_of(BrowserStack::Chrome))
          .and_return(true)

        expect(install_check).to receive(:app_installation_required?)
          .with(instance_of(BrowserStack::Launcher))
          .and_return(true)
        expect(mock_chromium).to receive(:install_required?).and_return(true)
        expect(mock_chromium).to receive(:bundle_id).and_return(chromium_bundle_id)
        install_check.check_requirements
      end

      context 'device is an apple tv device' do
        let(:os) { 'tvos' }

        it 'should run only checks for apple tv' do
          expect(install_check).to receive(:check_missing_wdas)
          expect(install_check).to receive(:check_unsigned_wdas)

          expect(install_check).not_to receive(:check_missing_browserstack_app)
          expect(install_check).not_to receive(:check_app).with(Chrome.new)
          expect(install_check).not_to receive(:check_app).with(Launcher.new)
          expect(install_check).not_to receive(:check_app).with(Redirect.new)

          install_check.check_requirements
        end
      end
    end
  end

  describe '#check_app' do
    context 'when app installation required' do
      before do
        allow(install_check).to receive(:app_installation_required?).and_return(true)
      end

      it 'adds app bundle id to installations required' do
        install_check.instance_variable_set(:@installations_required, Set.new)
        install_check.send(:check_app, app)
        expect(install_check.instance_variable_get(:@installations_required))
          .to eq([app_bundle_id].to_set)
      end

      it 'deletes app bundle id from device config' do
        expect(install_check.instance_variable_get(:@device_config)['app_info'][app_bundle_id])
          .not_to be_nil
        install_check.send(:check_app, app)
        expect(install_check.instance_variable_get(:@device_config)['app_info'][app_bundle_id])
          .to be_nil
      end
    end

    context 'when app installation not required' do
      before do
        allow(install_check).to receive(:app_installation_required?).and_return(false)
      end

      it 'calls update_config_if_nil' do
        expect(install_check).to receive(:update_config_if_nil).with(app)
        install_check.send(:check_app, app)
      end
    end
  end

  describe '#app_installation_required?' do
    let(:installed_app) { double('mock_installed_app') }
    let(:app_version) { '13' }

    before do
      allow(app).to receive(:update_app_version_using_ios_version).with(device_version.to_i)
      allow(app).to receive(:app_present_for_provision_profile?).and_return(true)
      allow(app).to receive(:version).and_return(app_version)
      allow(InstalledApp).to receive(:new).and_return(installed_app)
      allow(installed_app).to receive(:reinstall?).and_return(false)
      allow(app).to receive(:present_on_device?).and_return(true)

      install_check.instance_variable_set(:@full_check, true)
      install_check.instance_variable_set(:@user_installed_apps, [{ bundle_id: "something", version: "100" }])
    end

    it 'returns true if state_file contains the app bundle id' do
      install_check.instance_variable_set(:@installations_required, [app_bundle_id].to_set)
      expect(install_check.send(:app_installation_required?, app)).to be_truthy
    end

    it 'returns true if app is not present for the provision profile' do
      expect(app).to receive(:app_present_for_provision_profile?).with(ppuid).and_return(false)
      expect(install_check.send(:app_installation_required?, app)).to be_truthy
    end

    it 'returns true if installed app is not up to date with latest app version / device ppuid' do
      expect(installed_app).to receive(:reinstall?)
        .with(latest_version: app_version).and_return(true)

      expect(install_check.send(:app_installation_required?, app)).to be_truthy
    end

    it 'should return false if there are no requirements' do
      expect(app).to receive(:app_present_for_provision_profile?).with(ppuid).and_return(true)
      expect(app).to receive(:present_on_device?).and_return(true)
      expect(install_check.send(:app_installation_required?, app)).to be_falsey
    end
  end

  describe '#check_missing_wdas' do
    before do
      install_check.instance_variable_set(:@installations_required, Set.new)
    end

    it 'adds wda_install and wda_signing to installations_required if wda install required' do
      allow(install_check).to receive(:wdas_not_downloaded).and_return(["APPIUM_VERSION_1", "APPIUM_VERSION_2"])

      install_check.send(:check_missing_wdas)

      expect(install_check.instance_variable_get(:@installations_required))
        .to eq(['wda_install_APPIUM_VERSION_1', 'wda_install_APPIUM_VERSION_2'].to_set)
    end

    it 'does not do anything if all appium versions are downloaded' do
      allow(install_check).to receive(:wdas_not_downloaded).and_return([])

      install_check.send(:check_missing_wdas)

      expect(install_check.instance_variable_get(:@installations_required))
        .to eq([].to_set)
    end
  end

  describe "#check_unsigned_wda" do
    it 'adds wda_signing to installations_required if wda signing required' do
      allow(install_check).to receive(:wdas_not_signed).and_return(["APPIUM_VERSION_1", "APPIUM_VERSION_2"])

      install_check.send(:check_unsigned_wdas)

      expect(install_check.instance_variable_get(:@installations_required))
        .to eq(['wda_signing_APPIUM_VERSION_1', 'wda_signing_APPIUM_VERSION_2'].to_set)
    end

    it 'does not add anything if signing is not required' do
      allow(install_check).to receive(:wdas_not_signed).and_return([])

      install_check.send(:check_unsigned_wdas)

      expect(install_check.instance_variable_get(:@installations_required))
        .to eq([].to_set)
    end
  end

  describe '#wdas_not_downloaded' do
    before do
      install_check.instance_variable_set(:@web_driver_agents, { "APPIUM_VERSION_1": "something", "APPIUM_VERSION_2": "something" })
      allow(WDAVersion).to receive(:device_wda_folder_path).and_return("wda_path")
    end

    it 'should return the appium version for which wda files are missing' do
      expect(WDAVersion).to receive(:outdated?).with(device_uuid, 'APPIUM_VERSION_1', device_version).and_return(false)
      expect(WDAVersion).to receive(:outdated?).with(device_uuid, 'APPIUM_VERSION_2', device_version).and_return(true)

      expect(install_check.send(:wdas_not_downloaded)).to eq(["APPIUM_VERSION_2"])
    end

    it 'should return the appium version for which wda files are missing (all)' do
      expect(WDAVersion).to receive(:outdated?).with(device_uuid, 'APPIUM_VERSION_1', device_version).and_return(true)
      expect(WDAVersion).to receive(:outdated?).with(device_uuid, 'APPIUM_VERSION_2', device_version).and_return(true)

      expect(install_check.send(:wdas_not_downloaded)).to eq(["APPIUM_VERSION_1", "APPIUM_VERSION_2"])
    end

    it 'should return empty if no files are missing' do
      expect(WDAVersion).to receive(:outdated?).with(device_uuid, 'APPIUM_VERSION_1', device_version).and_return(false)
      expect(WDAVersion).to receive(:outdated?).with(device_uuid, 'APPIUM_VERSION_2', device_version).and_return(false)

      expect(install_check.send(:wdas_not_downloaded)).to eq([])
    end
  end

  describe '#wdas_not_signed' do
    let(:time1) { Time.now }
    let(:time2) { Time.now + 10 }

    before do
      install_check.instance_variable_set(:@web_driver_agents, { "APPIUM_VERSION_1": "something", "APPIUM_VERSION_2": "something" })
    end

    it 'should return all appium versions which require signing' do
      expect(install_check).to receive(:unsigned_wda?).with('APPIUM_VERSION_1').and_return(true)
      expect(install_check).to receive(:unsigned_wda?).with('APPIUM_VERSION_2').and_return(true)
      expect(install_check.send(:wdas_not_signed)).to eq(["APPIUM_VERSION_1", "APPIUM_VERSION_2"])
    end

    it 'should return the appium version which requires signing' do
      expect(install_check).to receive(:unsigned_wda?).with('APPIUM_VERSION_1').and_return(true)
      expect(install_check).to receive(:unsigned_wda?).with('APPIUM_VERSION_2').and_return(false)
      expect(install_check.send(:wdas_not_signed)).to eq(["APPIUM_VERSION_1"])
    end

    it 'should return empty if all wdas are up to date' do
      expect(install_check).to receive(:unsigned_wda?).with('APPIUM_VERSION_1').and_return(false)
      expect(install_check).to receive(:unsigned_wda?).with('APPIUM_VERSION_2').and_return(false)
      expect(install_check.send(:wdas_not_signed)).to eq([])
    end
  end

  describe '#unsigned_wda?' do
    let(:ppuid) { 'abcdefg' }
    let(:ppuid_file) { double('mock_ppuid_file') }
    let(:appium_version) { 'APPIUM_VERSION_1' }

    before do
      allow(PpuidFile).to receive(:new).and_return(ppuid_file)
      allow(ppuid_file).to receive(:valid?).and_return(true)
      allow(ppuid_file).to receive(:ppuid).and_return(ppuid)
      allow(WDAVersion).to receive(:device_wda_folder_path).and_return("wda_path")
    end

    it 'returns true if resign wda file is present' do
      device_state.touch_resign_wda_file
      expect(install_check.send(:unsigned_wda?, appium_version)).to be(true)
      device_state.remove_resign_wda_file
    end

    it 'returns true if ppuid in ppuid file does not match ppuid in wda db' do
      expect(install_check.send(:unsigned_wda?, appium_version)).to be(true)
    end

    it 'returns false otherwise' do
      allow(File).to receive(:exists?).and_return(true)
      allow(File).to receive(:readlines).and_return(["", "", ppuid])
      expect(install_check.send(:unsigned_wda?, appium_version)).to be(false)
    end
  end

  describe '#update_config_if_nil' do
    let(:installed_at) { "2020-08-27 22:52:13 +0000" }
    let(:device_config) do
      {
        'app_info' => {
          app_bundle_id => {
            "version" => app_version,
            "installed_at" => installed_at
          }
        }
      }
    end

    it 'should add app bundle id to config if its nil & reinstall_app = false' do
      install_check.instance_variable_set(:@device_config, {})
      expect(Time).to receive(:now).and_return(installed_at)

      install_check.send(:update_config_if_nil, app)

      expect(install_check.instance_variable_get(:@device_config)).to eq(device_config)
    end
  end

  describe '#update_installations_required' do
    it 'should write the array to the state file' do
      expect(Utils).to receive(:write_to_file).with(installations_required_file, "")
      install_check.send(:update_installations_required)
    end
  end
end
