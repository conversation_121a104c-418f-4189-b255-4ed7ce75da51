require 'rspec'
require_relative '../../../../lib/device_setup/developer_mode/check_and_enable'

describe 'DeveloperMode' do
  let(:udid) { 'udid' }

  context 'device_connected?' do
    it 'is connected' do
      expect(DeveloperMode).to receive(:`).with('idevice_id').and_return("#{udid} (USB)")
      expect(DeveloperMode.device_connected?(udid)).to be(true)
    end

    it 'is not connected' do
      expect(DeveloperMode).to receive(:`).with('idevice_id').and_return('')
      expect(DeveloperMode.device_connected?(udid)).to be(false)
    end
  end

  context 'check_and_enable' do
    before(:each) do
      @dbl = instance_double(DeveloperModeUtil)
      expect(DeveloperModeUtil).to receive(:new).with(udid).and_return(@dbl)
    end

    it 'Developer mode is already enabled' do
      expect(@dbl).to receive(:enabled?).and_return(true)
      DeveloperMode.check_and_enable(udid)
    end

    it 'enables developer mode' do
      expect(@dbl).to receive(:enabled?).and_return(false)
      expect(@dbl).to receive(:toggle)
      expect(DeveloperMode).to receive(:device_connected?).with(udid).and_return(true)
      expect(@dbl).to receive(:enable)
      expect(DeveloperMode).to receive(:sleep).with(5).exactly(2).times
      expect(DeveloperMode).to receive(:sleep).with(7).exactly(1).times
      expect(DeveloperMode).to receive(:sleep).with(3).exactly(1).times
      expect(@dbl).to receive(:enabled?).and_return(true)
      DeveloperMode.check_and_enable(udid)
    end

    it 'could not enabled and raises an error' do
      expect(@dbl).to receive(:enabled?).and_return(false)
      expect(@dbl).to receive(:toggle)
      expect(DeveloperMode).to receive(:device_connected?).with(udid).and_return(false).exactly(7).times
      expect(DeveloperMode).to receive(:sleep).with(5).exactly(8).times
      expect(DeveloperMode).to receive(:sleep).with(3).exactly(1).times
      expect(@dbl).to receive(:enabled?).and_return(false)
      expect { DeveloperMode.check_and_enable(udid) }.to raise_error(DeveloperModeError, 'Could not enable developer mode')
    end
  end
end
