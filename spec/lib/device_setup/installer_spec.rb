require_relative '../../spec_helper'
require_relative '../../../lib/device_setup/installer'
require_relative '../../../lib/models/device_state'

describe BrowserStack::Installer do
  let(:device_config) { {} }
  let(:device_uuid) { "fake_device_uuid" }
  let(:device_version) { "13" }
  let(:device_state) { DeviceState.new(device_uuid) }
  let(:installations_required_file) { device_state.send(:installations_required_file) }
  let(:install_phase_failed_file) { device_state.send(:install_phase_failed_file) }
  let(:provision_profile) { "fake_provision_profile" }
  let(:install_check) { double('install_check_double') }

  let(:chrome_bundle_id) { BrowserStack::Chrome::BUNDLE_ID }
  let(:launcher_bundle_id) { BrowserStack::Launcher::BUNDLE_ID }
  let(:chromium_bundle_id) { 'browserstack.chromium.app' }
  let(:mock_chromium) { double(Chromium) }
  let(:mock_abm) { double(AppleBusinessManagerHelper) }

  let(:app_bundle_id) { "com.google.chrome.ios" }
  let(:app) { double('sample_app', { bundle_id: app_bundle_id }) }

  let(:installer) do
    BrowserStack::Installer.new(
      device_uuid,
      device_version,
      provision_profile,
      device_config
    )
  end

  before(:each) do
    allow(BrowserStack).to receive(:init_logger)
    allow(BrowserStack::InstallCheck).to receive(:new).and_return(install_check)
    allow(Chromium).to receive(:new).and_return(mock_chromium)
    allow(mock_chromium).to receive(:bundle_id).and_return(chromium_bundle_id)
    allow(Utils).to receive(:write_array_to_file!)
    installer.instance_variable_set(:@appium_versions, ["APPIUM_VERSION_1", "APPIUM_VERSION_2"])
    allow(BrowserStack::AppleBusinessManagerHelper).to receive(:new).and_return(mock_abm)
    allow(mock_abm).to receive(:device_eligible?).and_return(false)
  end

  describe '#trigger_install' do
    before do
      allow(install_check).to receive(:check_requirements).and_return(Set.new)
    end

    it 'should do nothing if there is nothing to be installed' do
      expect(installer).not_to receive(:install_app)

      installer.trigger_install
    end

    it "should install chrome if it's in installations_required" do
      allow(install_check).to receive(:check_requirements)
        .and_return([BrowserStack::Chrome::BUNDLE_ID].to_set)

      expect(installer).to receive(:install_app).with(instance_of(BrowserStack::Chrome))
      expect(installer).not_to receive(:install_app).with(instance_of(BrowserStack::Launcher))

      installer.trigger_install
    end

    it "should install launcher if it's in installations_required" do
      allow(install_check).to receive(:check_requirements)
        .and_return([launcher_bundle_id].to_set)

      expect(installer).not_to receive(:install_app).with(instance_of(BrowserStack::Chrome))
      expect(installer).to receive(:install_app).with(instance_of(BrowserStack::Launcher))

      installer.trigger_install
    end

    it 'should call check_wdas to install/download/sign wda for wda_install' do
      allow(install_check).to receive(:check_requirements).and_return(["wda_install_APPIUM_VERSION"].to_set)
      expect(installer).to receive(:check_wdas)
      installer.trigger_install
    end

    it 'should call check_wdas to install/download/sign wda for wda_signing' do
      allow(install_check).to receive(:check_requirements).and_return(["wda_signing_APPIUM_VERSION"].to_set)
      expect(installer).to receive(:check_wdas)
      installer.trigger_install
    end

    it 'should call check_wdas to install/download/sign wda for both wda_signing and wda_install' do
      allow(install_check).to receive(:check_requirements).and_return(["wda_signing_APPIUM_VERSION", "wda_install_APPIUM_VERSION"].to_set)
      expect(installer).to receive(:check_wdas)
      installer.trigger_install
    end

    it 'should call grant_access_photos_permission post check_and_build_and_install_browserstack_app' do
      allow(install_check).to receive(:check_requirements).and_return([BrowserStackAppHelper::INSTALL_PHASE_IDENTIFIER].to_set)
      allow(installer.instance_variable_get(:@device_state)).to receive(:photos_permission_file_present?).and_return(true)
      expect(BrowserStackAppHelper).to receive(:check_and_build_and_install_browserstack_app)
      expect(installer.instance_variable_get(:@cleanup_iphone)).to receive(:grant_access_photos_permission)
      installer.trigger_install
    end

    it "should remove state file if @installations_required is empty after installations" do
      allow(install_check).to receive(:check_requirements)
        .and_return([chrome_bundle_id, launcher_bundle_id].to_set)
      allow(installer).to receive(:install_app).with(instance_of(BrowserStack::Chrome)) do
        installer.instance_variable_get(:@installations_required).delete(chrome_bundle_id)
      end
      allow(installer).to receive(:install_app).with(instance_of(BrowserStack::Launcher)) do
        installer.instance_variable_get(:@installations_required).delete(launcher_bundle_id)
      end
      allow(FileUtils).to receive(:rm_f).with(install_phase_failed_file)

      expect(FileUtils).to receive(:rm_f).with(installations_required_file)

      installer.trigger_install
    end
  end

  describe '#install_app' do
    before(:each) do
      installer.instance_variable_set(:@installations_required, [app_bundle_id].to_set)
      allow(app).to receive(:update_app_version_using_ios_version).with(device_version.to_i)
      allow(app).to receive(:uninstall_from_device).with(device_uuid)
      allow(FileUtils).to receive(:touch)
        .with(BrowserStack::Chrome.dismiss_chrome_popup_file(CONFIG_ROOT, device_uuid))
      allow(app).to receive(:setup)

      installer.instance_variable_set(:@installations_required, [app_bundle_id].to_set)
    end

    it 'should install the app' do
      expect(app).to receive(:setup).with(device_uuid, provision_profile, true)
      installer.send(:install_app, app)
    end

    it 'should remove bundle id from @installations_required' do
      installer.send(:install_app, app)
      expect(installer.instance_variable_get(:@installations_required)).to be_empty
    end
  end

  describe "#check_wdas" do
    it "should call download_wda_for_appiums" do
      installer.instance_variable_set(:@installations_required, ['wda_install_APPIUM_VERSION_1'].to_set)
      expect(installer).to receive(:download_wda_for_appiums).with(["APPIUM_VERSION_1"])
      expect(installer).to_not receive(:sign_wda_for_appiums).with(["APPIUM_VERSION_1"])

      installer.send(:check_wdas)
      expect(installer.instance_variable_get(:@installations_required)).to eq([].to_set)
    end

    it "should call sign_wdas_for_appiums" do
      installer.instance_variable_set(:@installations_required, ['wda_signing_APPIUM_VERSION_1'].to_set)
      expect(installer).to receive(:sign_wda_for_appiums).with(["APPIUM_VERSION_1"])
      expect(installer).to_not receive(:download_wda_for_appiums).with(["APPIUM_VERSION_1"])

      installer.send(:check_wdas)
      expect(installer.instance_variable_get(:@installations_required)).to eq([].to_set)
    end

    it "should call sign_wdas_for_appiums and download_wda_for_appiums if required" do
      installer.instance_variable_set(:@installations_required, ['wda_signing_APPIUM_VERSION_1', 'wda_install_APPIUM_VERSION_2'].to_set)
      expect(installer).to receive(:sign_wda_for_appiums).with(["APPIUM_VERSION_1"])
      expect(installer).to receive(:download_wda_for_appiums).with(["APPIUM_VERSION_2"])

      installer.send(:check_wdas)
      expect(installer.instance_variable_get(:@installations_required)).to eq([].to_set)
    end

    it "should not call sign_wdas_for_appiums and download_wda_for_appiums if not required" do
      installer.instance_variable_set(:@installations_required, [].to_set)
      expect(installer).to_not receive(:sign_wda_for_appiums)
      expect(installer).to_not receive(:download_wda_for_appiums)

      installer.send(:check_wdas)
      expect(installer.instance_variable_get(:@installations_required)).to eq([].to_set)
    end
  end

  describe "#sign_wda_for_appiums" do
    it 'should call sign_wda' do
      expect(installer).to receive(:sign_wda).once
      installer.send(:sign_wda_for_appiums, ["APPIUM_VERSION_1"])
    end

    it 'should call sign_wda twice if required' do
      expect(installer).to receive(:sign_wda).twice
      installer.send(:sign_wda_for_appiums, ["APPIUM_VERSION_1", "APPIUM_VERSION_2"])
    end
  end

  describe '#sign_wda' do
    it "should delegate to WDAVersion#sign_wda" do
      w = double
      expect(WDAVersion).to receive(:new).with(device_uuid, "APPIUM_VERSION", device_version).and_return(w)
      expect(w).to receive(:sign_wda)

      installer.send(:sign_wda, "APPIUM_VERSION")
    end
  end
end
