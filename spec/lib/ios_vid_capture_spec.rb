require_relative '../spec_helper'
require_relative '../../lib/ios_vid_capturer'
require_relative '../../server/device_manager'

describe IosVidCapturer do
  before(:all) do
    @server_config = BrowserStack::Configuration.new.all
    DeviceManager.configure @server_config
    # IdeviceUtils.configure(server_config)
    @device_config = {
      "device_id" => {
        "current_appium_version" => "1.14.0",
        "device_name" => "iPhone 8",
        "device_version" => "13.2.0",
        "selenium_port" => 8080,
        "webdriver_port" => 8401,
        "debugger_port" => 12345
      }
    }
  end
  context '#capture_idevicescreenshot' do
    let(:frame_path) { '/temp/i1.jpeg' }

    it 'should capture screenshot in desired format and save the image at desired path' do
      allow(DeviceManager).to receive(:all_devices).and_return(@device_config)
      vid = IosVidCapturer.new('device_id', 'session', @server_config, 'portrait')
      expect(IdeviceUtils).to receive(:screenshot)
      vid.capture_idevicescreenshot(frame_path)
    end
  end

  context '#upload' do
    let(:video_file) { double("video_file") }
    let(:merged_file) { double("merged_file") }
    subject { IosVidCapturer.new('device_id', 'session', @server_config, 'portrait') }

    before(:each) do
      allow_any_instance_of(Object).to receive(:sleep).and_return(0)
      allow(DeviceManager).to receive(:all_devices).and_return(@device_config)
      allow(File).to receive(:read).and_return(video_file)
      subject.instance_variable_set(:@skip_recording, false)
      subject.instance_variable_set(:@video_merge_completed_file, merged_file)
    end

    it 'should wait for renderer and create upload request successfully' do
      expect(File).to receive(:exist?).with(video_file).and_return(true)
      expect(File).to receive(:exist?).with(merged_file).and_return(true)
      expect(subject).to receive(:process_video_in_async?).at_least(:once).and_return(false)
      expect(subject).to receive(:create_upload_request).exactly(2).times.and_return(true)

      subject.upload({})
    end

    it 'should cleanup workspace when Timeout Error Occurred' do
      allow(File).to receive(:exist?).with(video_file).and_return(true)
      allow(File).to receive(:exist?).with(merged_file).and_return(false)
      expect(subject).to receive(:process_video_in_async?).at_least(:once).and_return(false)
      expect(subject).to_not receive(:create_upload_request)
      expect(FileUtils).to receive(:rm_rf).and_return(true)

      expect { subject.upload({}) }.to raise_error(Timeout::Error)
    end
  end
end
