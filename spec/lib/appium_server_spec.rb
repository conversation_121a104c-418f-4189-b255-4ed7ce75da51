require_relative '../spec_helper'
require_relative '../../lib/appium_server'

describe BrowserStack::AppiumServer do
  let(:mock_http_response_ok) { instance_double('Faraday::Response', status: 200, success?: true) }
  let(:mock_http_response_error) { instance_double('Faraday::Response', status: 500, success?: true) }
  let(:mock_check_plist) { instance_double('BrowserStack::CheckPlist', update: nil) }
  let(:mock_driver) { instance_double('Appium::Driver', start_driver: nil) }
  let(:device_id) { "1234567890" }
  let(:device_config) do
    { 'current_appium_version' => '1.14.0',
      'device_name' => 'iPhone XS',
      'device_version' => '13.2.0',
      'selenium_port' => 8004,
      'webdriver_port' => 8084,
      'debugger_port' => 24458 }
  end
  let(:server_url) { "http://127.0.0.1:#{device_config['selenium_port']}/wd/hub" }
  let(:my_default_caps) do
    {
      platformName: 'iOS',
      url: server_url,
      deviceName: device_config['device_name'],
      automationName: 'XCUITest',
      orientation: 'PORTRAIT',
      showXcodeLog: false,
      wdaStartupRetries: 2
    }
  end
  let(:my_appium_server) { BrowserStack::AppiumServer.new(device_id, device_config) }

  before(:each) do
    allow(IdeviceUtils).to receive(:os).with(device_id).and_return('ios')
    allow_any_instance_of(BrowserStack::AppiumServer).to receive(:sleep)
    allow(BrowserStack::HttpUtils).to receive(:make_get_request).and_return(mock_http_response_ok)
    allow(BrowserStack::Zombie).to receive(:push_logs) # Mock this as part as metric pushing
    allow(OSUtils).to receive(:macos_version).and_return('10.15') # Mock this as part as metric pushing
    allow_any_instance_of(PpuidFile).to receive(:contents).and_return(['team', 'date', 'ppuid']) # Mock this as part as metric pushing
    allow(BrowserStack::DeviceConf).to receive(:[]).with(device_id).and_return(device_config)
    allow(AppleTVUtils).to receive(:verify_wda_running).with(device_id).and_return(nil)
    allow(Iproxy).to receive(:version).and_return('0.9.0')
    allow(Iproxy).to receive(:running?).and_return(true)
    allow(Iproxy).to receive(:start)
  end

  # Verifies the arguments passed to .new
  describe 'self.verify_server_args(device_id, device_config)' do
    it 'raises an exception if device_id is nil' do
      device_id = nil
      device_config = nil
      expect { BrowserStack::AppiumServer.verify_server_args(device_id, device_config) }.to raise_error(ArgumentError, "device_id must be a non-empty string")
    end
    it 'raises an exception if device_id is empty' do
      device_id = ''
      device_config = nil
      expect { BrowserStack::AppiumServer.verify_server_args(device_id, device_config) }.to raise_error(ArgumentError, "device_id must be a non-empty string")
    end
    it 'raises an exception if device_config is nil' do
      device_id = "1234567890"
      device_config = nil
      expect { BrowserStack::AppiumServer.verify_server_args(device_id, device_config) }.to raise_error(ArgumentError, "device_config must be a non-empty hash")
    end
    it 'raises an exception if device_config is empty' do
      device_id = "1234567890"
      device_config = {}
      expect { BrowserStack::AppiumServer.verify_server_args(device_id, device_config) }.to raise_error(ArgumentError, "device_config must be a non-empty hash")
    end
    it 'raises an exception if device_config is missing some required keys' do
      device_id = "1234567890"
      device_config = { "current_appium_version" => '1.14.0',
                        "device_name" => 'iPhone 8 Plus',
                        "device_version" => '',
                        "webdriver_port" => 8480,
                        "debugger_port" => 123456 }
      expect { BrowserStack::AppiumServer.verify_server_args(device_id, device_config) }.to raise_error(ArgumentError, "device_config is missing the keys [\"device_version\", \"selenium_port\"]")
    end
    it 'deosnt raise an exception if everything is ok' do
      device_id = "1234567890"
      device_config = { "current_appium_version" => '1.14.0',
                        "device_name" => 'iPhone 8 Plus',
                        "device_version" => '13.2',
                        "selenium_port" => 8081,
                        "webdriver_port" => 8480,
                        "debugger_port" => 123456 }
      expect { BrowserStack::AppiumServer.verify_server_args(device_id, device_config) }.to_not raise_error
    end
  end

  describe '.running?(max_attempts)' do
    it 'returns true after few calls if Appium server is running' do
      allow(BrowserStack::HttpUtils).to receive(:make_get_request).and_return(mock_http_response_error, mock_http_response_error, mock_http_response_ok)
      expect(my_appium_server.running?(5)).to be(true)
    end

    it 'raises an AppiumServerError if server is not running after max_attempts' do
      allow(BrowserStack::HttpUtils).to receive(:make_get_request).and_return(mock_http_response_error, mock_http_response_error, mock_http_response_error)
      expect { my_appium_server.running?(3) }.to raise_error(AppiumServerError)
    end

    context 'apple tv device' do
      before do
        allow(IdeviceUtils).to receive(:os).with(device_id).and_return('tvos')
        allow(AppleTVUtils).to receive(:verify_wda_running).with(device_id).and_return(nil)
        allow(IdeviceUtils).to receive(:list_apps).and_return([])
        allow(IdeviceUtils).to receive(:device_version).with(device_id).and_return(Gem::Version.new('16'))
        allow(IdeviceUtils).to receive(:install_app).and_return(nil)
      end

      it 'returns true if appium server running and wda is running' do
        allow(BrowserStack::HttpUtils).to receive(:make_get_request).and_return(mock_http_response_error, mock_http_response_error, mock_http_response_ok)
        expect(AppleTVUtils).to receive(:verify_wda_running).with(device_id)
        expect(my_appium_server.running?(5)).to be(true)
      end

      it 'raises an error if appium server running but wda not running' do
        allow(BrowserStack::HttpUtils).to receive(:make_get_request).and_return(mock_http_response_error, mock_http_response_error, mock_http_response_ok)
        expect(AppleTVUtils).to receive(:verify_wda_running).with(device_id).and_raise(WDALaunchError)
        expect { my_appium_server.running? }.to raise_error(AppiumServerError)
      end
    end
  end

  describe '.driver(caps = {})' do
    before do
      allow(Appium::Driver).to receive(:new).and_return(mock_driver)
    end

    it 'returns a driver' do
      expect(my_appium_server.driver).to be(mock_driver)
    end

    it 'calls .start_driver with default options + app set to settings if no caps passed' do
      options = {
        caps: my_default_caps.merge({ app: 'settings' }),
        appium_lib: {
          port: device_config['selenium_port']
        }
      }
      expect(Appium::Driver).to receive(:new).with(options, false)
      my_appium_server.driver
    end

    it 'calls .start_driver with merged options if caps passed' do
      my_caps = { app: 'com.apple.DocumentsApp' }
      options = {
        caps: my_default_caps.merge(my_caps),
        appium_lib: {
          port: device_config['selenium_port']
        }
      }
      expect(Appium::Driver).to receive(:new).with(options, false)
      my_appium_server.driver(my_caps)
    end

    it 'raises a AppiumServerError if .start_driver fails with EOFError' do
      allow(mock_driver).to receive(:start_driver).and_raise(EOFError)
      expect { my_appium_server.driver }.to raise_error(AppiumServerError)
    end

    it 'raises a AppiumServerError if .start_driver times out' do
      allow(mock_driver).to receive(:start_driver).and_raise(Selenium::WebDriver::Error::UnknownError)
      expect { my_appium_server.driver }.to raise_error(AppiumServerError)
    end

    it 'raises a StandardError if .start_driver failes for something else' do
      allow(mock_driver).to receive(:start_driver).and_raise("Failed for some reason")
      expect { my_appium_server.driver }.to raise_error(StandardError)
    end
  end

  describe '.restart' do
    before do
      allow(BrowserStack::OSUtils).to receive(:kill_process)
      allow(BrowserStack::CheckPlist).to receive(:unload_service)
      allow(BrowserStack::CheckPlist).to receive(:load_service)
    end

    it 'tries to kill appium process' do
      allow(my_appium_server).to receive(:running?).and_return(true)
      expect(BrowserStack::OSUtils).to receive(:kill_process)
      my_appium_server.restart
    end

    it 'does not raise AppiumServerError if server is running' do
      allow(my_appium_server).to receive(:running?).and_return(true)
      expect { my_appium_server.restart }.not_to raise_error
    end

    it 'raises AppiumServerError if server is not running after 3 retries' do
      allow(my_appium_server).to receive(:running?).and_raise(AppiumServerError, "Failed for some reason")
      expect { my_appium_server.restart }.to raise_error(AppiumServerError)
    end
  end

  describe '.start_server_for_version(appium_version)' do
    it 'calls update_plist and running?' do
      my_appium_version = '1.14.0'
      expect(my_appium_server).to receive(:update_plist).with(my_appium_version, 'debug', false, nil, nil, false)
      expect(my_appium_server).to receive(:running?)
      my_appium_server.start_server_for_version(my_appium_version)
    end

    it 'raises an AppiumServerError if it failed to update the plist' do
      my_appium_version = '1.14.0'
      allow(my_appium_server).to receive(:update_plist).and_raise(AppiumServerError)
      expect { my_appium_server.start_server_for_version(my_appium_version) }.to raise_error(AppiumServerError)
    end

    it 'raises an AppiumServerError if server fails to start' do
      my_appium_version = '1.14.0'
      allow(my_appium_server).to receive(:update_plist).with(my_appium_version, 'debug', false, nil, nil, false)
      allow(my_appium_server).to receive(:running?).and_raise(AppiumServerError)
      expect { my_appium_server.start_server_for_version(my_appium_version) }.to raise_error(AppiumServerError)
    end

    context 'apple tv device' do
      before do
        allow(IdeviceUtils).to receive(:os).with(device_id).and_return('tvos')
        my_appium_server.instance_variable_set(:@device_version, '16')
        allow(AppleTVUtils).to receive(:verify_wda_running?).with(device_id).at_least(:once)
      end

      it 'makes sure the launch agent for wda is loaded and verifies wda is running' do
        my_appium_version = '2.0.0'
        expect(my_appium_server).to receive(:update_plist).with(my_appium_version, 'debug', false, "xcuitest", "4.12.2", false)
        expect(my_appium_server).to receive(:ensure_iproxy_running)
        expect(my_appium_server).to receive(:running?)
        expect(AppleTVUtils).to receive(:update_wda_launch_agent).with(device_id, my_appium_version)
        my_appium_server.start_server_for_version(my_appium_version)
      end
    end
  end

  describe '.update_plist(appium_version, force_update)' do
    before do
      allow(BrowserStack::WebDriverAgent).to receive(:xctestrun_file_path).and_return("path/to/xctestrun/file")
      allow(BrowserStack::CheckPlist).to receive(:new).and_return(mock_check_plist)
    end

    it 'does not raise an error when plist update is successful' do
      my_appium_version = '1.14.0'
      node_path = '/nix/store/path-to-node-package/bin/node'
      allow(BrowserStack::OSUtils).to receive(:macos_version).and_return('10')
      allow(File).to receive(:exist?).and_return(true)
      expect(my_appium_server).to receive(:get_node_path).and_return(node_path)
      allow(mock_check_plist).to receive(:update)
      expect { my_appium_server.update_plist(my_appium_version, 'debug', false) }.not_to raise_error
    end

    it 'raises an AppiumServerError if update fails' do
      my_appium_version = '1.14.0'
      allow(mock_check_plist).to receive(:update).and_raise("Update failed")
      expect { my_appium_server.update_plist(my_appium_version, 'debug', false) }.to raise_error(AppiumServerError)
    end

    it "raises an AppiumServerError if node path doesn't exist" do
      my_appium_version = '1.14.0'
      node_path = '/nix/store/path-to-node-package/bin/node'
      allow(BrowserStack::OSUtils).to receive(:macos_version).and_return('10')
      expect(my_appium_server).to receive(:get_node_path).and_return(node_path)
      expect(File).to receive(:exist?).with(node_path).and_return(false)
      allow(mock_check_plist).to receive(:update)
      expect { my_appium_server.update_plist(my_appium_version, 'debug', false) }.to raise_error(AppiumServerError)
    end

    it "raises an AppiumServerError if appium path doesn't exist" do
      my_appium_version = '1.8.0'
      node_path = '/nix/store/path-to-node-package/bin/node'
      allow(BrowserStack::OSUtils).to receive(:macos_version).and_return('10')
      expect(my_appium_server).to receive(:get_node_path).and_return(node_path)
      expect(File).to receive(:exist?).with(node_path).and_return(true)
      expect(File).to receive(:exist?).with(%r{build/lib/main.js}).and_return(false)
      allow(mock_check_plist).to receive(:update)
      expect { my_appium_server.update_plist(my_appium_version, 'debug', false) }.to raise_error(AppiumServerError)
    end

    describe "choose appropriate node version for appium based on machine" do
      combinations = [
        ['1.6.5' , '10.11.0', '10'],
        ['1.7.0' , '10.11.0', '10'],
        ['1.7.1' , '10.11.0', '10'],
        ['1.7.2' , '10.11.0', '10'],
        ['1.8.0' , '10.11.0', '10'],
        ['1.9.1' , '10.11.0', '10'],
        ['1.10.1', '10.11.0', '10'],
        ['1.11.1', '10.11.0', '10'],
        ['1.12.1', '10.11.0', '10'],
        ['1.13.0', '10.11.0', '10'],
        ['1.14.0', '10.11.0', '10'],
        ['1.15.0', '10.16.3', '10'],
        ['1.16.0', '10.16.3', '10'],
        ['1.17.0', '10.16.3', '10'],
        ['1.18.0', '10.16.3', '10'],
        ['1.19.1', '10.16.3', '10'],
        ['1.20.2', '10.16.3', '10'],
        ['1.21.0', '14.16.0', '10'],
        ['1.6.5' , '14.16.0', '11'],
        ['1.7.0' , '14.16.0', '11'],
        ['1.7.1' , '14.16.0', '11'],
        ['1.7.2' , '14.16.0', '11'],
        ['1.8.0' , '14.16.0', '11'],
        ['1.9.1' , '14.16.0', '11'],
        ['1.10.1', '14.16.0', '11'],
        ['1.11.1', '14.16.0', '11'],
        ['1.12.1', '14.16.0', '11'],
        ['1.13.0', '14.16.0', '11'],
        ['1.14.0', '14.16.0', '11'],
        ['1.15.0', '14.16.0', '11'],
        ['1.16.0', '14.16.0', '11'],
        ['1.17.0', '14.16.0', '11'],
        ['1.18.0', '14.16.0', '11'],
        ['1.19.1', '14.16.0', '11'],
        ['1.20.2', '14.16.0', '11'],
        ['1.21.0', '14.16.0', '11']
      ]

      combinations.each do |combination|
        appium_version, node_version, mac_version = combination
        it "starts appium server with node v#{node_version} for appium versions #{appium_version} on bigsur: #{mac_version} machine" do
          node_path = "/nix/store/path-to-node-package/bin/node"
          allow(BrowserStack::OSUtils).to receive(:macos_version).and_return(mac_version)
          allow(BrowserStack::OSUtils).to receive(:which).with('node').and_return(node_path)
          expect(my_appium_server).to receive(:get_node_path).and_return(node_path)
          expect(File).to receive(:exist?).with(node_path).and_return(true)
          allow(File).to receive(:exist?).and_return(true)
          allow(mock_check_plist).to receive(:update)
          expect { my_appium_server.update_plist(appium_version, 'debug', false) }.not_to raise_error(AppiumServerError)
        end
      end
    end
  end

  describe '.ensure_iproxy_running' do
    before do
      allow(Utils).to receive(:fork_process)
    end

    context 'when iproxy is not running' do
      it 'tries to run iproxy and raises exception if still not running after attempt to run' do
        allow(Iproxy).to receive(:running?).and_return(false, false).exactly(5).times

        expect(Iproxy).to receive(:start).with(device_id, device_config['webdriver_port'], "8100").exactly(4).times
        expect { my_appium_server.ensure_iproxy_running }.to raise_error(AppiumServerError, "Couldn't start iproxy")
      end

      it 'tries to run iproxy and does not raise exception if running afterwards' do
        allow(Iproxy).to receive(:running?).and_return(false, true)

        expect(Iproxy).to receive(:start).with(device_id, device_config['webdriver_port'], "8100")
        expect { my_appium_server.ensure_iproxy_running }.not_to raise_error
      end
    end

    context 'when iproxy is running' do
      it 'does not raise any exception' do
        allow(Iproxy).to receive(:running?).and_return(true)
        expect { my_appium_server.ensure_iproxy_running }.not_to raise_error
      end
    end
  end
end
