require_relative '../../lib/device_params'

describe DeviceParams do
  let(:fixtures_folder) { "#{__dir__}/../fixtures" }
  before do
    allow(BrowserStack::Configuration).to receive(:base_dir).and_return(fixtures_folder)
    DeviceParams.class_variable_set(:@@device_config, nil)
    DeviceParams.configure
  end

  after do
    DeviceParams.class_variable_set(:@@device_config, nil)
  end

  describe '.get_device_width_and_height' do
    it 'returns width and height of streaming params' do
      expect(DeviceParams.get_device_width_and_height('iPhone8,4')).to eql [380, 672]
    end
  end

  describe '.control_center_coordinates' do
    it 'returns coordinates of nearest greatest ios version' do
      expect(DeviceParams.control_center_coordinates('iPad6,11', 15.0, 'show')['os_version']).to eql '15.0'
      expect(DeviceParams.control_center_coordinates('iPad6,11', 16.0, 'show')['os_version']).to eql '15.0'
      expect(DeviceParams.control_center_coordinates('iPhone8,4', '16.1.2', 'dismiss')['os_version']).to eql '15.1'
      expect(DeviceParams.control_center_coordinates('iPhone8,4', 15.1, 'dismiss')['os_version']).to eql '15.1'
    end

    it 'returns coordinates of * if ios version not > for any matching os' do
      expect(DeviceParams.control_center_coordinates('iPad6,11', 14.9, 'show')['os_version']).to eql '*'
      expect(DeviceParams.control_center_coordinates('iPad6,11', 12.0, 'dismiss')['os_version']).to eql '*'
      expect(DeviceParams.control_center_coordinates('iPhone8,4', 15.0, 'dismiss')['os_version']).to eql '*'
    end

    it 'raises DeviceConfigException if device is not in config' do
      expect { DeviceParams.control_center_coordinates('NonExistentiDevice', 14.9, 'show') }.to raise_error DeviceConfigException
      expect { DeviceParams.control_center_coordinates('NonExistentiDevice', 14.9, 'dismiss') }.to raise_error DeviceConfigException
    end
  end

  describe '.control_centre_show_coordinates' do
    it 'calls control_center_coordinates with "show"' do
      model = 'iPad6,11'
      version = 15.1
      expect(DeviceParams).to receive(:control_center_coordinates).with(model, version, 'show')
      DeviceParams.control_centre_show_coordinates(model, version)
    end
  end

  describe '.control_centre_dismiss_coordinates' do
    it 'calls control_center_coordinates with "dismiss"' do
      model = 'iPad6,11'
      version = 15.1
      expect(DeviceParams).to receive(:control_center_coordinates).with(model, version, 'dismiss')
      DeviceParams.control_centre_dismiss_coordinates(model, version)
    end
  end
end
