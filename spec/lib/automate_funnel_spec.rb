require_relative '../spec_helper'
require_relative '../../lib/automate_funnel'

describe AutomateFunnel do
  describe "#mark_block_start" do
    let(:funnel_events) { [] }
    let(:funnel_blocks) { {} }

    it "should add event and block" do
      subject.instance_variable_set(:@funnel_events, funnel_events)
      subject.instance_variable_set(:@funnel_blocks, funnel_blocks)
      subject.mark_block_start('abc')
      expect(funnel_events.length).to eq(1)
      expect(funnel_blocks.length).to eq(1)
      expect(funnel_blocks['abc'].length).to eq(2)
    end
  end

  describe "#mark_block_end" do
    let(:funnel_blocks) { { "abc" => { startTime: 1610641879 } } }

    it "should not add failure reason to block" do
      subject.instance_variable_set(:@funnel_blocks, funnel_blocks)
      subject.mark_block_end('abc', 'success')
      expect(funnel_blocks['abc'].length).to eq(3)
    end

    it "should add failure reason to block" do
      subject.instance_variable_set(:@funnel_blocks, funnel_blocks)
      subject.mark_block_end('abc', 'failure', 'region-down')
      expect(funnel_blocks['abc'].length).to eq(4)
    end
  end

  describe "#mark_breakup_start" do
    let(:funnel_events) { [] }

    it "should push breakup_start" do
      subject.instance_variable_set(:@funnel_events, funnel_events)
      subject.mark_breakup_start
      expect(funnel_events.length).to eq(1)
    end
  end

  describe "#mark_breakup_end" do
    let(:funnel_events) { [] }

    it "should push breakup_end" do
      subject.instance_variable_set(:@funnel_events, funnel_events)
      subject.mark_breakup_end
      expect(funnel_events.length).to eq(1)
    end
  end

  describe "#generate_data_json" do
    let(:funnel_events) { [] }
    let(:funnel_blocks) { {} }

    it "should generate data json for success" do
      subject.instance_variable_set(:@funnel_events, funnel_events)
      subject.instance_variable_set(:@funnel_blocks, funnel_blocks)
      subject.mark_block_start('authenticate')
      subject.mark_breakup_start
      subject.mark_breakup_end
      subject.mark_block_end('authenticate', 'success')
      data_json = subject.generate_data_json
      expect(funnel_events.length).to eq(3)
      expect(data_json[:blockName]).to eq('authenticate')
      expect(data_json[:result]).to eq('success')
      expect(data_json[:component]).to eq('ios')
      expect(data_json[:startTime]).not_to be_nil
      expect(data_json[:totalTime]).not_to be_nil
      expect(data_json[:failureReason]).to be_nil
    end

    it "should generate data json for failure" do
      subject.instance_variable_set(:@funnel_events, funnel_events)
      subject.instance_variable_set(:@funnel_blocks, funnel_blocks)
      subject.mark_block_start('authenticate')
      subject.mark_breakup_start
      subject.mark_breakup_end
      subject.mark_block_end('authenticate', 'failure', 'Invalid username or password')
      data_json = subject.generate_data_json
      expect(funnel_events.length).to eq(3)
      expect(data_json[:blockName]).to eq('authenticate')
      expect(data_json[:result]).to eq('failure')
      expect(data_json[:component]).to eq('ios')
      expect(data_json[:startTime]).not_to be_nil
      expect(data_json[:totalTime]).not_to be_nil
      expect(data_json[:failureReason]).to eq('Invalid username or password')
    end
  end
end
