require_relative '../../spec_helper'
require_relative '../../../lib/wrappers/cfg_util'

describe CFGUtil do
  let(:uuid) { "1234567890" }
  let(:cfgutil) { CFGUtil.new(udid: uuid) }

  describe "#ecid" do
    it "returns ecid of device if it is plugged in" do
      response = { "device" => { "UDID" => :uuid } }
      allow(subject).to receive(:list).and_return(response)
      expect(subject).to receive(:ecid).and_return(:uuid)

      subject.ecid
    end

    it "returns nil of device if it is plugged in" do
      response = { "device" => {} }
      allow(subject).to receive(:list).and_return(response)
      expect(subject).to receive(:ecid).and_return(nil)

      subject.ecid
    end
  end

  describe "#installed?" do
    it "returns false if cfgutil is not installed" do
      allow(subject).to receive(:execute).with('version').and_raise(CFGUtil::CommandNotFoundError)

      expect(subject.installed?).to eql(false)
    end

    it "returns true if cfgutil is installed" do
      allow(subject).to receive(:execute).with('version').and_return("cfgutil 2.15 (807)")

      expect(subject.installed?).to eql(true)
    end

    it 'should retry if error is raised' do
      allow(subject).to receive(:execute).and_raise("Something went wrong").exactly(3)

      expect { subject.installed? }.to raise_error("Something went wrong")
    end
  end

  describe "#get_icon_layout" do
    it "returns icon layout when device is plugged in" do
      layout = [[
        "com.apple.mobilesafari",
        "com.apple.DocumentsApp"
      ]]

      allow(subject).to receive(:get_icon_layout).and_return(layout)
      expect(subject.get_icon_layout).to be_an_instance_of(Array)
    end

    it "raises error if device is not connected" do
      allow(subject).to receive(:ecid).and_return(nil)
      expect { subject.get_icon_layout }.to raise_error(CFGUtil::DeviceNotFoundError)
    end
  end

  describe "#backup" do
    it "raises error if device is not connected" do
      allow(subject).to receive(:ecid).and_return(nil)
      expect { subject.backup }.to raise_error(CFGUtil::DeviceNotFoundError)
    end

    it "performs backup if device is connected" do
      test_ecid = "1234567890"
      allow(subject).to receive(:ecid).and_return(test_ecid)
      allow(subject).to receive(:backup).and_return("Operation \"backup\" succeeded on 1 devices.")
      expect(subject).to receive(:backup).and_return("Operation \"backup\" succeeded on 1 devices.")

      message = subject.backup
    end
  end

  describe "#restore_backup" do
    it "raises error if device is not connected" do
      allow(cfgutil).to receive(:ecid).and_return(nil)
      allow(cfgutil).to receive(:list_backups).and_return("Source: 1234567890 Encrypted: No Date: Today at 12:39 pm Name: iPhone")
      expect { cfgutil.restore_backup }.to raise_error(CFGUtil::DeviceNotFoundError)
    end

    it "performs restore backup if device is connected" do
      test_ecid = "1234567890"
      allow(cfgutil).to receive(:ecid).and_return(test_ecid)
      allow(cfgutil).to receive(:list_backups).and_return("Source: 1234567890 Encrypted: No Date: Today at 12:39 pm Name: iPhone")
      allow(cfgutil).to receive(:restore_backup).and_return("Operation \"restore-backup\" succeeded on 1 devices.")
      expect(cfgutil).to receive(:restore_backup).and_return("Operation \"restore-backup\" succeeded on 1 devices.")

      message = cfgutil.restore_backup
    end
  end

  describe "#erase" do
    it "raises error if device is not connected" do
      allow(subject).to receive(:ecid).and_return(nil)
      expect { subject.erase }.to raise_error(CFGUtil::DeviceNotFoundError)
    end

    it "performs erase if device is connected" do
      test_ecid = "1234567890"
      allow(subject).to receive(:ecid).and_return(test_ecid)
      allow(subject).to receive(:erase).and_return("Operation \"erase\" succeeded on 1 devices.")
      expect(subject).to receive(:erase).and_return("Operation \"erase\" succeeded on 1 devices.")

      message = subject.erase
    end
  end
end
