require_relative '../spec_helper'
require_relative '../../lib/configuration'

describe BrowserStack::Configuration do
  let(:fixtures_folder) { "#{__dir__}/../fixtures" }

  before(:each) do
    allow(File).to receive(:exists?).and_call_original
    allow(BrowserStack::Configuration).to receive(:base_dir).and_return(fixtures_folder)
    BrowserStack::Configuration.instance_variable_set(:@conf, nil)
  end

  after(:each) do
    #unstub
    BrowserStack::Configuration.instance_variable_set(:@conf, nil)
    allow(BrowserStack::Configuration).to receive(:base_dir).and_call_original
  end

  it 'gets configuration from config.yml' do
    allow(File).to receive(:exists?).with("#{fixtures_folder}/config/machine_custom_config.yml").and_return(false)
    config = BrowserStack::Configuration.new.all
    expect(config["mdm_server_url"]).to eq('mdm-config.browserstack.com')
  end

  it 'overwrites config with custom config if present' do
    config = BrowserStack::Configuration.new.all
    expect(config["mdm_server_url"]).to eq('mdm-custom-config.browserstack.com')
  end

  it 'gets configuration from config.yml as class method' do
    expect(BrowserStack::Configuration["mdm_server_url"]).to eq('mdm-custom-config.browserstack.com')
  end
end
