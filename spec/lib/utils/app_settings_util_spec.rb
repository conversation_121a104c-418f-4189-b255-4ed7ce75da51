require_relative '../../spec_helper'
require_relative '../../../lib/utils/app_settings_util'
require_relative '../../../lib/utils/osutils'
require_relative '../../../config/constants'
require_relative '../../../server/device_manager'
require_relative '../../../lib/utils/utils'

describe BrowserStack::AppSettingsUtil do
  let(:device) { 'test_device' }
  let(:wda_port) { 8084 }
  let(:wda_client) { WdaClient.new(wda_port) }
  let(:device_config) { { 'webdriver_port' => wda_port } }

  let(:vaild_app_settings_dsl_mid_session) do
    {
      "Environment" => "PSP",
      "OTP Auto-Fill" => "ON",
      "Performance Charting Mock Data" => "ON",
      "Server config" => "random",
      "Child" => {
        "Child Banner" => "ON"
      }
    }
  end

  let(:vaild_app_settings_dsl_fire_cmd) do
    {
      "Environment" => "PSP",
      "OTP Auto-Fill" => "ON",
      "Performance Charting Mock Data" => "ON",
      "Server config" => "random",
      "Child" => {
        "Child Banner" => "OFF",
        "Explore Environment" => "DEV"
      }
    }
  end

  let(:valid_permission_settings_dsl_template) do
    {
      "Environment" => "PSP",
      "OTP Auto-Fill" => "ON",
      "Performance Charting Mock Data" => "ON",
      "Server config" => "random",
      "Permission Settings" => {
      }
    }
  end

  let(:just_permission_settings_dsl_template) do
    {
      "Permission Settings" => {
      }
    }
  end

  let(:invalid_permission_settings_dsl) do
    {
      "Environment" => "PSP",
      "OTP Auto-Fill" => "ON",
      "Performance Charting Mock Data" => "ON",
      "Server config" => "random",
      "Permission Settings" => {
      }
    }
  end

  let(:non_existent_permission_settings_dsl) do
    {
      "Permission Settings" => {
        "Mobile Data" => "ON"
      }
    }
  end

  let(:individual_settings) do
    {
      "location_settings_ios_14_valid" => {
        "Location" => {
          "ALLOW LOCATION ACCESS" => "Always",
          "Precise Location" => "ON"
        }
      },
      "location_settings_ios_14_invalid" => {
        "Location" => {
          "ALLOW LOCATION ACCESS" => "Never",
          "Precise Location" => "ON"
        }
      },
      "language_settings_ios_14_valid" => {
        "Language" => { "OTHER LANGUAGES" => "Hindi" }
      },
      "language_settings_ios_14_invalid" => {
        "Language" => { "INVALID" => "Hindi" }
      },
      "location_settings_ios_14_invalid_location_access" => {
        "Location" => "Always"
      },
      "location_settings_ios_14_valid_precise_location" => {
        "Location" => {
          "Precise Location" => "ON"
        }
      },
      "location_settings_ios_13_valid" => {
        "Location" => {
          "ALLOW LOCATION ACCESS" => "Always"
        }
      },
      "location_settings_ios_13_invalid" => {
        "Location" => {
          "ALLOW LOCATION ACCESS" => "Always",
          "Precise Location" => "ON"
        }
      },
      "location_settings_ios_12_valid" => {
        "Location" => {
          "ALLOW LOCATION ACCESS" => "Always"
        }
      },
      "contacts_settings" => {
        "Contacts" => "OFF"
      },
      "camera_settings" => {
        "Camera" => "OFF"
      },
      "photos_settings_ios_14_valid" => {
        "Photos" => "Selected Photos"
      },
      "photos_settings_ios_14_invalid" => {
        "Photos" => "Read and Write"
      },
      "photos_settings_ios_13_valid" => {
        "Photos" => "Read and Write"
      },
      "photos_settings_ios_13_invalid" => {
        "Photos" => "Selected Photos"
      },
      "photos_settings_ios_17_valid" => {
        "Photos" => "Full Access"
      },
      "photos_settings_ios_17_invalid" => {
        "Photos" => "Read and Write"
      }
    }
  end

  let(:parsed_app_settings_dsl_mid_session) do
    {
      "Environment" => { "value" => "PSP", "type" => "PSMultiValueSpecifier" },
      "OTP Auto-Fill" => { "value" => "ON", "type" => "PSToggleSwitchSpecifier" },
      "Performance Charting Mock Data" => { "value" => "ON", "type" => "PSToggleSwitchSpecifier" },
      "Server config" => { "value" => "random", "type" => "PSTextFieldSpecifier" },
      "Child" => {
        "value" => {
          "Child Banner" => { "value" => "ON", "type" => "PSToggleSwitchSpecifier" }
        },
        "type" => "PSChildPaneSpecifier"
      }
    }
  end

  let(:parsed_app_settings_dsl_firecmd) do
    {
      "Environment" => { "value" => "PSP", "type" => "PSMultiValueSpecifier" },
      "Performance Charting Mock Data" => { "value" => "ON", "type" => "PSToggleSwitchSpecifier" },
      "Server config" => { "value" => "random", "type" => "PSTextFieldSpecifier" },
      "Child" => {
        "value" => {
          "Explore Environment" => { "type" => "PSMultiValueSpecifier", "value" => "DEV" }
        },
        "type" => "PSChildPaneSpecifier"
      }
    }
  end

  let(:parsed_permission_settings_dsl_firecmd) do
    {
      "Environment" => { "value" => "PSP", "type" => "PSMultiValueSpecifier" },
      "Performance Charting Mock Data" => { "value" => "ON", "type" => "PSToggleSwitchSpecifier" },
      "Server config" => { "value" => "random", "type" => "PSTextFieldSpecifier" }
    }
  end

  let(:parsed_permission_settings_dsl_mid_session) do
    {
      "Environment" => { "value" => "PSP", "type" => "PSMultiValueSpecifier" },
      "Performance Charting Mock Data" => { "value" => "ON", "type" => "PSToggleSwitchSpecifier" },
      "Server config" => { "value" => "random", "type" => "PSTextFieldSpecifier" },
      "OTP Auto-Fill" => { "value" => "ON", "type" => "PSToggleSwitchSpecifier" }
    }
  end

  let(:parsed_settings) do
    {
      "location_settings_ios_14" => {
        "Location" => {
          "type" => "PSChildPaneSpecifier",
          "value" => {
            "ALLOW LOCATION ACCESS" => {
              "type" => "PSMultiValueSpecifier",
              "value" => "Always"
            },
            "Precise Location" => {
              "type" => "PSToggleSwitchSpecifier",
              "value" => "ON"
            }
          }
        }
      },
      "language_settings_ios_14" => {
        "Language" => {
          "type" => "PSChildPaneSpecifier",
          "value" => {
            "OTHER LANGUAGES" => {
              "type" => "PSMultiValueSpecifier", "value" => "Hindi"
            }
          }
        }
      },
      "location_settings_ios_14_only_precise" => {
        "Location" => {
          "type" => "PSChildPaneSpecifier",
          "value" => {
            "Precise Location" => {
              "type" => "PSToggleSwitchSpecifier",
              "value" => "ON"
            }
          }
        }
      },
      "location_settings_ios_13" => {
        "Location" => {
          "type" => "PSChildPaneSpecifier",
          "value" => {
            "ALLOW LOCATION ACCESS" => {
              "type" => "PSMultiValueSpecifier",
              "value" => "Always"
            }
          }
        }
      },
      "location_settings_ios_12" => {},
      "contacts_settings" => {
        "Contacts" => { "value" => "OFF", "type" => "PSToggleSwitchSpecifier" }
      },
      "camera_settings" => {
        "Camera" => { "value" => "OFF", "type" => "PSToggleSwitchSpecifier" }
      },
      "photos_settings_ios_14" => {
        "Photos" => {
          "type" => "PSMultiValueSpecifier",
          "value" => "Selected Photos"
        }
      },
      "photos_settings_ios_13" => {
        "Photos" => {
          "type" => "PSMultiValueSpecifier",
          "value" => "Read and Write"
        }
      },
      "photos_settings_ios_17" => {
        "Photos" => {
          "type" => "PSMultiValueSpecifier",
          "value" => "Full Access"
        },
        "special_post_action" => "postPhotosAppPermission"
      }
    }
  end

  let(:params) do
    {
      "app_testing_bundle_id" => 'some_bundle_id',
      "app_display_name" => 'some_app_name',
      "session_id" => 'some_session_id'
    }
  end

  let(:app_settings_fixtures) { "./spec/fixtures/app_settings" }
  let(:app_settings_bundle_root_plist) { "#{app_settings_fixtures}/Root.plist" }
  let(:app_settings_bundle_child_plist) { "#{app_settings_fixtures}/child.plist" }
  let(:app_settings_bundle_invalid_array_plist) { "#{app_settings_fixtures}/invalid_array.plist" }
  let(:fn_lproj) { "#{app_settings_fixtures}/fn.lproj" }
  let(:en_lproj) { "#{app_settings_fixtures}/en.lproj" }

  let(:parsed_app_setting_json) { "#{app_settings_fixtures}/AppSettings.json" }
  let(:parsed_app_setting_json_data) { JSON.parse(File.read(parsed_app_setting_json)) }

  context "validate" do
    it 'should return valid response in case of vaild params during firecmd' do
      params["update_app_settings"] = vaild_app_settings_dsl_fire_cmd.to_json
      params["is_firecmd"] = true
      allow(File).to receive(:exists?).and_return(true)
      allow(BrowserStack::AppSettingsUtil).to receive(:get_settings_bundle_json_path).and_return(parsed_app_setting_json)
      expect(BrowserStack::AppSettingsUtil.send(:validate, "app_path", params)).to eq(parsed_app_settings_dsl_firecmd)
    end

    it 'should return vaild response in case of vaild params during mid session' do
      params["update_app_settings"] = vaild_app_settings_dsl_mid_session.to_json
      allow(File).to receive(:exists?).and_return(true)
      allow(BrowserStack::AppSettingsUtil).to receive(:get_settings_bundle_json_path).and_return(parsed_app_setting_json)
      expect(BrowserStack::AppSettingsUtil.send(:validate, "app_path", params)).to eq(parsed_app_settings_dsl_mid_session)
    end

    describe "Permission Settings" do
      combinations = [
        ["17.0", "photos_settings_ios_17_valid", true, "photos_settings_ios_17", ""],
        ["17.0", "photos_settings_ios_17_invalid", false, "", "Invalid value for key"],
        ["14.0", "location_settings_ios_14_valid", true, "location_settings_ios_14", ""],
        ["14.0", "language_settings_ios_14_valid", true, "language_settings_ios_14", ""],
        ["14.0", "language_settings_ios_14_invalid", false, "", "Key not found"],
        ["14.0", "location_settings_ios_14_invalid", false, "", "Precise Location cannot be set when Location Access is set to 'Never'"],
        ["14.0", "location_settings_ios_14_invalid_location_access", false, "", "Not a valid Location Permission Setting"],
        ["14.0", "location_settings_ios_14_valid_precise_location", true, "location_settings_ios_14_only_precise", ""],
        ["14.0", "contacts_settings", true, "contacts_settings", ""],
        ["14.0", "camera_settings", true, "camera_settings", ""],
        ["14.0", "photos_settings_ios_14_valid", true, "photos_settings_ios_14", ""],
        ["14.0", "photos_settings_ios_14_invalid", false, "", "Invalid value for key"],
        ["13.0", "location_settings_ios_13_valid", true, "location_settings_ios_13", ""],
        ["13.0", "location_settings_ios_13_invalid", false, "", "Key not found"],
        ["13.0", "contacts_settings", true, "contacts_settings", ""],
        ["13.0", "camera_settings", true, "camera_settings", ""],
        ["13.0", "photos_settings_ios_13_valid", true, "photos_settings_ios_13", ""],
        ["13.0", "photos_settings_ios_13_invalid", false, "", "Invalid value for key"],
        ["12.2", "location_settings_ios_12_valid", true, "location_settings_ios_12", ""]
      ]

      combinations.each do |combination|
        os_version, input, is_valid, output, error = combination

        it 'should return valid output it input is valid during midsession' do
          params["os_version"] = os_version
          valid_permission_settings_dsl_template["Permission Settings"] = individual_settings[input]
          params["update_app_settings"] = valid_permission_settings_dsl_template.to_json
          params["is_firecmd"] = false
          allow(File).to receive(:exists?).and_return(true)
          allow(BrowserStack::AppSettingsUtil).to receive(:get_settings_bundle_json_path).and_return(parsed_app_setting_json)
          if is_valid
            expect(BrowserStack::AppSettingsUtil.send(:validate, "app_path", params)).to eq(parsed_permission_settings_dsl_mid_session.merge(parsed_settings[output]))
          else
            expect { BrowserStack::AppSettingsUtil.send(:validate, "app_path", params) }.to raise_error(error)
          end
        end

        it 'should return valid output if only permission settings are passed during midsession' do
          params["os_version"] = os_version
          just_permission_settings_dsl_template["Permission Settings"] = individual_settings[input]
          params["update_app_settings"] = just_permission_settings_dsl_template.to_json
          params["is_firecmd"] = false
          allow(File).to receive(:exists?).and_return(true)
          allow(BrowserStack::AppSettingsUtil).to receive(:get_settings_bundle_json_path).and_return(parsed_app_setting_json)
          if is_valid
            expect(BrowserStack::AppSettingsUtil.send(:validate, "app_path", params)).to eq(parsed_settings[output])
          else
            expect { BrowserStack::AppSettingsUtil.send(:validate, "app_path", params) }.to raise_error(error)
          end
        end

        it 'should ignore permission settings during firecmd' do
          params["os_version"] = os_version
          valid_permission_settings_dsl_template["Permission Settings"] = individual_settings[input]
          params["update_app_settings"] = valid_permission_settings_dsl_template.to_json
          params["is_firecmd"] = true
          allow(File).to receive(:exists?).and_return(true)
          allow(BrowserStack::AppSettingsUtil).to receive(:get_settings_bundle_json_path).and_return(parsed_app_setting_json)
          expect(BrowserStack::AppSettingsUtil.send(:validate, "app_path", params)).to eq(parsed_permission_settings_dsl_firecmd)
        end

        it 'should ignore if just permission settings are passed during firecmd' do
          params["os_version"] = os_version
          just_permission_settings_dsl_template["Permission Settings"] = individual_settings[input]
          params["update_app_settings"] = just_permission_settings_dsl_template.to_json
          params["is_firecmd"] = true
          allow(File).to receive(:exists?).and_return(true)
          allow(BrowserStack::AppSettingsUtil).to receive(:get_settings_bundle_json_path).and_return(parsed_app_setting_json)
          expect(BrowserStack::AppSettingsUtil.send(:validate, "app_path", params)).to eq({})
        end
      end
    end

    context "missing params" do
      %w[update_app_settings app_display_name app_testing_bundle_id session_id].each do |invalid_param|
        it "should raise exception in case of missing param - #{invalid_param}" do
          params[invalid_param.to_s] = nil
          expect { BrowserStack::AppSettingsUtil.send(:validate, "app_path", params) }.to raise_error("Invalid params")
        end
      end

      it "should raise exception in case app not present" do
        expect { BrowserStack::AppSettingsUtil.send(:validate, "app_path", params) }.to raise_error("Invalid params")
      end
    end

    it 'should raise exception in case of invaild params - invalid dsl json' do
      params["update_app_settings"] = "{\"a\":\"dakfjagi\""
      params["is_firecmd"] = true
      allow(File).to receive(:exists?).and_return(true)
      allow(BrowserStack::AppSettingsUtil).to receive(:get_settings_bundle_json_path).and_return(parsed_app_setting_json)
      expect { BrowserStack::AppSettingsUtil.send(:validate, "app_path", params) }.to raise_error("Invaild DSL structure")
    end

    it 'should raise exception in case of Settings bundle file not present' do
      params["update_app_settings"] = vaild_app_settings_dsl_fire_cmd.to_json
      params["is_firecmd"] = true
      allow(File).to receive(:exists?).and_return(true, false)
      allow(BrowserStack::AppSettingsUtil).to receive(:get_settings_bundle_json_path).and_return("app_settings_bundle_json_path")
      allow(BrowserStack::AppSettingsUtil).to receive(:get_settings_bundle_json_path).and_return(parsed_app_setting_json)
      expect { BrowserStack::AppSettingsUtil.send(:validate, "app_path", params) }.to raise_error("App Settings JSON absent")
    end

    it 'should raise exception in case of DSL key is invalid' do
      vaild_app_settings_dsl_fire_cmd["random key"] = "afaf"
      params["update_app_settings"] = vaild_app_settings_dsl_fire_cmd.to_json
      params["is_firecmd"] = true
      allow(File).to receive(:exists?).and_return(true)
      allow(BrowserStack::AppSettingsUtil).to receive(:get_settings_bundle_json_path).and_return(parsed_app_setting_json)
      expect { BrowserStack::AppSettingsUtil.send(:validate, "app_path", params) }.to raise_error("Key not found")
    end

    it 'should raise exception in case of DSL value is invalid for a key' do
      vaild_app_settings_dsl_fire_cmd["OTP Auto-Fill"] = "afaf"
      params["update_app_settings"] = vaild_app_settings_dsl_fire_cmd.to_json
      params["is_firecmd"] = true
      allow(File).to receive(:exists?).and_return(true)
      allow(BrowserStack::AppSettingsUtil).to receive(:get_settings_bundle_json_path).and_return(parsed_app_setting_json)
      expect { BrowserStack::AppSettingsUtil.send(:validate, "app_path", params) }.to raise_error("Invalid value for key")
    end
  end

  context "update_settings" do
    it 'should update settings for given params' do
      params["update_app_settings"] = vaild_app_settings_dsl_fire_cmd.to_json
      expect(Utils).to receive(:enable_settings_app).and_return(true)
      allow(DeviceManager).to receive(:device_configuration_check).with(device).and_return(device_config)
      allow(wda_client.instance_variable_get(:@influxdb_client)).to receive(:event)
      allow(WdaClient).to receive(:new).and_return(wda_client)
      response = { "value" => { "status" => "success" } }
      expect(wda_client).to receive(:make_request).with("POST", "/bs/updateSettings", anything).and_return(response)
      expect(Utils).to receive(:disable_settings_app).and_return(true)
      expect { BrowserStack::AppSettingsUtil.send(:update_settings, device, parsed_app_settings_dsl_firecmd, params) }.not_to raise_error
    end

    it 'should raise exception in case of settings app enable failed' do
      params["update_app_settings"] = vaild_app_settings_dsl_fire_cmd.to_json
      expect(Utils).to receive(:enable_settings_app).and_raise(AppSettingsError, "Failed to Enable settings app")
      expect(DeviceManager).not_to receive(:device_configuration_check)
      allow(wda_client.instance_variable_get(:@influxdb_client)).to receive(:event)
      expect(WdaClient).not_to receive(:new)
      expect(wda_client).not_to receive(:make_request).with("POST", "/bs/updateSettings", anything)
      expect(Utils).to receive(:disable_settings_app).and_return(true)
      expect { BrowserStack::AppSettingsUtil.send(:update_settings, device, parsed_app_settings_dsl_firecmd, params) }.to raise_error("Failed to Enable settings app")
    end

    it 'should raise exception if automation failed - wda sent error response for firecmd' do
      params["update_app_settings"] = vaild_app_settings_dsl_fire_cmd.to_json
      params["is_firecmd"] = true
      params["session_id"] = "session_id"
      expect(Utils).to receive(:enable_settings_app).and_return(true)
      allow(DeviceManager).to receive(:device_configuration_check).with(device).and_return(device_config)
      allow(wda_client.instance_variable_get(:@influxdb_client)).to receive(:event)
      allow(WdaClient).to receive(:new).and_return(wda_client)
      response = { "value" => { "status" => "error", "error" => { "message" => "some error", "screenshot": "screenshot-data" } } }
      expect(wda_client).to receive(:make_request).with("POST", "/bs/updateSettings", anything).and_return(response)
      expect(File).to receive(:open).with("/tmp/update_settings_screenshot_session_id.jpeg", "wb")
      expect(Utils).to receive(:disable_settings_app).and_return(true)
      expect { BrowserStack::AppSettingsUtil.send(:update_settings, device, parsed_app_settings_dsl_firecmd, params) }.to raise_error("some error")
    end

    it 'should raise exception if automation failed - wda sent error response for non firecmd' do
      params["update_app_settings"] = vaild_app_settings_dsl_fire_cmd.to_json
      params["session_id"] = "session_id"
      expect(Utils).to receive(:enable_settings_app).and_return(true)
      allow(DeviceManager).to receive(:device_configuration_check).with(device).and_return(device_config)
      allow(wda_client.instance_variable_get(:@influxdb_client)).to receive(:event)
      allow(WdaClient).to receive(:new).and_return(wda_client)
      response = { "value" => { "status" => "error", "error" => { "message" => "some error", "screenshot": "screenshot-data" } } }
      expect(wda_client).to receive(:make_request).with("POST", "/bs/updateSettings", anything).and_return(response)
      expect(File).not_to receive(:open).with("/tmp/update_settings_screenshot_session_id.jpeg", "wb")
      expect(Utils).to receive(:disable_settings_app).and_return(true)
      expect { BrowserStack::AppSettingsUtil.send(:update_settings, device, parsed_app_settings_dsl_firecmd, params) }.to raise_error("some error")
    end

    it 'should raise exception if automation failed - wda request error' do
      params["update_app_settings"] = vaild_app_settings_dsl_fire_cmd.to_json
      expect(Utils).to receive(:enable_settings_app).and_return(true)
      allow(DeviceManager).to receive(:device_configuration_check).with(device).and_return(device_config)
      allow(wda_client.instance_variable_get(:@influxdb_client)).to receive(:event)
      allow(WdaClient).to receive(:new).and_return(wda_client)
      expect(wda_client).to receive(:make_request).with("POST", "/bs/updateSettings", anything).and_raise(StandardError, "request failed")
      expect(Utils).to receive(:disable_settings_app).and_return(true)
      expect { BrowserStack::AppSettingsUtil.send(:update_settings, device, parsed_app_settings_dsl_firecmd, params) }.to raise_error("request failed")
    end
  end

  context "foreground_user_app" do
    it 'should foreground user app for given params' do
      allow(DeviceManager).to receive(:device_configuration_check).with(device).and_return(device_config)
      allow(wda_client.instance_variable_get(:@influxdb_client)).to receive(:event)
      allow(WdaClient).to receive(:new).and_return(wda_client)
      response = { "value" => { "status" => "success" } }
      expect(wda_client).to receive(:make_request).with("POST", "/bs/forgroundApp", anything).and_return(response)
      expect { BrowserStack::AppSettingsUtil.send(:foreground_user_app, device, params) }.not_to raise_error
    end

    it 'should raise exception if automation failed - wda sent error response' do
      allow(DeviceManager).to receive(:device_configuration_check).with(device).and_return(device_config)
      allow(wda_client.instance_variable_get(:@influxdb_client)).to receive(:event)
      allow(WdaClient).to receive(:new).and_return(wda_client)
      response = { "value" => { "status" => "error", "error" => { "message" => "some error" } } }
      expect(wda_client).to receive(:make_request).with("POST", "/bs/forgroundApp", anything).and_return(response)
      expect { BrowserStack::AppSettingsUtil.send(:foreground_user_app, device, params) }.to raise_error("some error")
    end

    it 'should raise exception if automation failed - wda request error' do
      allow(DeviceManager).to receive(:device_configuration_check).with(device).and_return(device_config)
      allow(wda_client.instance_variable_get(:@influxdb_client)).to receive(:event)
      allow(WdaClient).to receive(:new).and_return(wda_client)
      expect(wda_client).to receive(:make_request).with("POST", "/bs/forgroundApp", anything).and_raise(StandardError, "request failed")
      expect { BrowserStack::AppSettingsUtil.send(:foreground_user_app, device, params) }.to raise_error("request failed")
    end
  end

  context "parse_settings_bundle" do
    it 'should parse plists present in apps settings.bundle' do
      app_path = app_settings_fixtures
      expect(Dir).to receive(:glob).with("#{app_path}/Settings.bundle/*.plist").and_return([app_settings_bundle_root_plist, app_settings_bundle_child_plist])
      expect(BrowserStack::AppSettingsUtil).to receive(:get_settings_bundle_json_path).with(app_path).and_return(app_settings_fixtures)
      expect(File).to receive(:write).and_return(true)
      allow(BrowserStack::OSUtils).to receive(:safe_execute).and_return({ "test" => "test" }.to_json)
      expect(BrowserStack::AppSettingsUtil.parse_settings_bundle(app_path, params)).to eq({ "Root" => { "test" => "test" }, "child" => { "test" => "test" } })
      expect(BrowserStack::AppSettingsUtil).to_not receive(:remove_lproj_dir)
    end

    it 'should parse plists present in apps settings.bundle and skip invalid plists' do
      app_path = app_settings_fixtures
      expect(Dir).to receive(:glob).with("#{app_path}/Settings.bundle/*.plist").and_return([app_settings_bundle_root_plist, app_settings_bundle_child_plist, app_settings_bundle_invalid_array_plist])
      expect(BrowserStack::AppSettingsUtil).to receive(:get_settings_bundle_json_path).with(app_path).and_return(app_settings_fixtures)
      expect(File).to receive(:write).and_return(true)
      allow(BrowserStack::OSUtils).to receive(:safe_execute).and_return({ "test" => "test" }.to_json, { "test" => "test" }.to_json, ["test", "test"].to_json)
      expect(BrowserStack::AppSettingsUtil.parse_settings_bundle(app_path, params)).to eq({ "Root" => { "test" => "test" }, "child" => { "test" => "test" } })
    end

    it 'should push to zombie in case parse failed' do
      app_path = app_settings_fixtures
      params["device_id"] = device
      expect(Dir).to receive(:glob).with("#{app_path}/Settings.bundle/*.plist").and_return([app_settings_bundle_root_plist, app_settings_bundle_child_plist])
      expect(BrowserStack::Zombie).to receive(:push_logs).with("settings-bundle-parse-failed", "", { "device" => "test_device", "session_id" => "some_session_id" })
      expect { BrowserStack::AppSettingsUtil.parse_settings_bundle(app_path, params) }.not_to raise_error
    end
    it 'should call remove_lproj_dir if params: remove_ios_app_settings_localization is set' do
      app_path = app_settings_fixtures
      params["remove_ios_app_settings_localization"] = true
      expect(FileUtils).to receive(:rm_rf).with(en_lproj).and_return(en_lproj)
      expect(FileUtils).to receive(:rm_rf).with(fn_lproj).and_return(fn_lproj)
      expect(Dir).to receive(:glob).with("#{app_path}/Settings.bundle/*.plist").and_return([app_settings_bundle_root_plist, app_settings_bundle_child_plist])
      expect(Dir).to receive(:glob).with("#{app_path}/Settings.bundle/*.lproj").and_return([en_lproj, fn_lproj])
      allow(BrowserStack::OSUtils).to receive(:safe_execute).and_return({ "test" => "test" }.to_json, { "test" => "test" }.to_json, ["test", "test"].to_json)
      expect(BrowserStack::AppSettingsUtil).to receive(:remove_lproj_dir).and_call_original
      BrowserStack::AppSettingsUtil.parse_settings_bundle(app_path, params)
    end
  end

  context "get_settings_bundle_json_path" do
    it 'should return settings bundle json path' do
      app_path = app_settings_fixtures
      expect(BrowserStack::AppSettingsUtil.get_settings_bundle_json_path(app_path)).to eq("/usr/local/.browserstack/realmobile/spec/AppSettings.json")
    end
  end

  context "get_response" do
    it 'should return json formatted as required by hub - incase of error' do
      response = { error: { "kind" => "automation_error", :meta_data => { "key" => "some key" } }, status: "error" }
      expect(BrowserStack::AppSettingsUtil.get_response("error", "automation_error", { "key" => "some key" } )).to eq(response)
    end

    it 'should return json formatted as required by hub - incase of success' do
      response = { status: "success" }
      expect(BrowserStack::AppSettingsUtil.get_response("success", "", { "key" => "some key" } )).to eq(response)
    end
  end

  context "validate_settings_tuple" do
    it 'should not raise exception for valid DSL key, value' do
    end

    it 'should raise exception for invalid DSL key, value' do
    end
  end

  context "create_enterprise_wda_client" do
    let(:device_id) { "sample_device_id" }
    let(:forwarding_port) { wda_port + 1700 }
    let(:params) { { "automate_session_id" => "test_session_123" } }

    before do
      allow(BrowserStack).to receive_message_chain(:logger, :info)
      allow(BrowserStack).to receive_message_chain(:logger, :error)
      allow(BrowserStack::Zombie).to receive(:push_logs)
    end

    it 'should create wda client with proper port forwarding' do
      expect(PortManager).to receive(:stop_forwarding_port).with(device_id, forwarding_port, 'update_settings')
      expect(PortManager).to receive(:forward_port).with(device_id, forwarding_port, wda_port, 'update_settings')

      expect(WdaClient).to receive(:new).with(forwarding_port, device_id).and_return(wda_client)

      result_client, result_port = BrowserStack::AppSettingsUtil.send(:create_enterprise_wda_client, device_id, wda_port, params)

      expect(result_client).to eq(wda_client)
      expect(result_port).to eq(forwarding_port)
    end

    it 'should stop existing port forwarding before creating a new one' do
      expect(PortManager).to receive(:stop_forwarding_port).with(device_id, forwarding_port, 'update_settings').ordered
      expect(PortManager).to receive(:forward_port).with(device_id, forwarding_port, wda_port, 'update_settings').ordered
      allow(WdaClient).to receive(:new).with(forwarding_port, device_id).and_return(wda_client)

      BrowserStack::AppSettingsUtil.send(:create_enterprise_wda_client, device_id, wda_port, params)
    end
  end
end
