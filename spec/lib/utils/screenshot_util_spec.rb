require_relative '../../spec_helper'
require_relative '../../../server/device_manager'
require_relative '../../../lib/utils/screenshot_util'

describe ScreenshotsUtil do
  let(:device) { 'test_device' }
  let(:wda_port) { 8084 }
  let(:wda_client) { WdaClient.new(wda_port) }
  let(:device_config) { { 'webdriver_port' => wda_port } }

  context "#capture_screenshot_via_wda" do
    # let(:device_manager) { DeviceManager }
    let(:wda_client_dummy) { double("WDAclient") }
    let(:response) { { 'value' => "" } }
    it 'should capture screenshot using wda' do
      expect(WdaClient).to receive(:new).with(wda_port).and_return(wda_client_dummy)
      expect(wda_client_dummy).to receive(:get_scaled_screenshot).and_return(response)
      expect(File).to receive(:open)
      ScreenshotsUtil.capture_screenshot_via_wda('/temp/i1.jpeg', device, wda_port)
    end
  end
end
