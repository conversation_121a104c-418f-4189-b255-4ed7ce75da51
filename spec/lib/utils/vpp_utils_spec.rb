require 'spec_helper'
require_relative '../../../lib/utils/vpp_utils'
require_relative '../../../lib/custom_exceptions'

RSpec.describe BrowserStack::VPPUtils do
  let(:adam_id) { '408709785' }
  let(:app_name) { 'TestApp' }
  let(:serial_number) { 'ABC123' }
  let(:event_id) { '954910a8-3d9c-4fde-948d-253e5aef431a' }
  let(:vpp_token) { 'sample_token' }
  let(:headers) { { 'Authorization' => "Bearer #{vpp_token}" } }
  let(:response_double) { double('response') }
  let(:body) { '{"assets": [{"adamId": "408709785"}]}' }

  before do
    stub_const('BrowserStack::VPP_TOKEN', vpp_token)
  end

  describe '.asset_information' do
    it 'returns asset information for the given adam_id' do
      allow(BrowserStack::HttpUtils).to receive(:make_get_request_with_headers).and_return(response_double)
      allow(response_double).to receive(:status).and_return(200)
      allow(response_double).to receive(:body).and_return(body)
      expect(described_class.asset_information(adam_id)).to eq(JSON.parse(body)['assets'][0])
    end

    it 'raises error if non-200 status code returned' do
      allow(BrowserStack::HttpUtils).to receive(:make_get_request_with_headers).and_return(response_double)
      allow(response_double).to receive(:status).and_return(500)
      expect { described_class.asset_information(adam_id) }.to raise_error
    end
  end

  describe '.token_expiry' do
    let(:response_body) { '{"tokenExpirationDate": "2025-01-16T06:45:18+0000"}' }
    let(:response) { instance_double(Faraday::Response, status: 200, body: response_body) }

    context 'when the request is successful' do
      before do
        allow(BrowserStack::HttpUtils).to receive(:make_get_request_with_headers).and_return(response)
      end

      it 'returns the token expiration date' do
        expect(BrowserStack::VPPUtils.token_expiry).to eq('2025-01-16T06:45:18+0000')
      end
    end

    context 'when the request fails' do
      before do
        allow(BrowserStack::HttpUtils).to receive(:make_get_request_with_headers).and_raise(Faraday::ClientError, 'Error')
      end

      it 'raises a VPPUtilsError' do
        expect { BrowserStack::VPPUtils.token_expiry }.to raise_error
      end
    end
  end

  describe '.fetch_assignments' do
    let(:response_body) { '{"assignments": [{"adamId": "123456", "serialNumber": "ABC123"}]}' }
    let(:response) { instance_double(Faraday::Response, status: 200, body: response_body) }

    context 'when the request is successful' do
      before do
        allow(BrowserStack::HttpUtils).to receive(:make_get_request_with_headers).and_return(response)
      end

      it 'returns parsed JSON response' do
        expect(BrowserStack::VPPUtils.fetch_assignments).to eq(JSON.parse(response_body))
      end
    end

    context 'when the request fails' do
      before do
        allow(BrowserStack::HttpUtils).to receive(:make_get_request_with_headers).and_raise(Faraday::ClientError, 'Error')
      end

      it 'raises a VPPUtilsError' do
        expect { BrowserStack::VPPUtils.fetch_assignments }.to raise_error
      end
    end
  end

  describe '.assignment_status' do
    let(:event_id) { '123456' }
    let(:response_body) { '{"eventStatus": "COMPLETE", "eventType": "ASSOCIATE"}' }
    let(:response) { instance_double(Faraday::Response, status: 200, body: response_body) }

    context 'when the request is successful' do
      before do
        allow(BrowserStack::HttpUtils).to receive(:make_get_request_with_headers).and_return(response)
      end

      it 'returns parsed JSON response' do
        expect(BrowserStack::VPPUtils.assignment_status(event_id)).to eq(JSON.parse(response_body))
      end
    end

    context 'when the request fails' do
      before do
        allow(BrowserStack::HttpUtils).to receive(:make_get_request_with_headers).and_raise(Faraday::ClientError, 'Error')
      end

      it 'raises a VPPUtilsError' do
        expect { BrowserStack::VPPUtils.assignment_status(event_id) }.to raise_error
      end
    end
  end

  describe '.associate_license' do
    let(:app_name) { 'testflight' }
    let(:serial_number) { '123456' }
    let(:response_body) { '{"eventId": "123456", "tokenExpirationDate": "2030-11-08T22:33:22+0000"}' }
    let(:response) { instance_double(Faraday::Response, status: 200, body: response_body) }

    context 'when the request is successful' do
      before do
        allow(BrowserStack::HttpUtils).to receive(:make_post_request_with_headers).and_return(response)
        allow(BrowserStack::VPPUtils).to receive(:handle_error)
      end

      it 'returns parsed JSON response' do
        expect(BrowserStack::VPPUtils.associate_license(app_name, serial_number)).to eq(JSON.parse(response_body))
      end
    end

    context 'when the request fails' do
      before do
        allow(BrowserStack::HttpUtils).to receive(:make_post_request_with_headers).and_raise(Faraday::ClientError, 'Error')
        allow(BrowserStack::VPPUtils).to receive(:handle_error)
      end

      it 'raises a VPPUtilsError' do
        expect { BrowserStack::VPPUtils.associate_license(app_name, serial_number) }.to raise_error
      end
    end
  end
end

