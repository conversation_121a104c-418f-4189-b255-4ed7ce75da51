require_relative "../../../lib/utils/download_file"
require_relative "../../../lib/helpers/download_file_helper"
require_relative "../../../lib/utils/idevice_file_utils"
require_relative "../../../server/device_manager"

describe DownloadFile do
  let(:session_id) { "session_id" }
  let(:device) { "000820-427503425" }
  let(:product) { "app-live" }
  let(:device_config) { { "webdriver_port" => 8080, "device_version" => "14.0" } }
  let(:download_file_zip_path) { "/tmp/download-files_#{device}.zip" }
  let(:download_file_folder_path) { "/tmp/download-files_#{device}" }
  let(:list_of_preloaded_media_path) { "/tmp/preloaded-media-list_#{device}" }
  let(:type) { "download-file" }
  let(:success_data) { { download_file_size: 10, message: "DOWNLOAD_FILES_SUCCESS", result: "success", time_taken: 0 } }
  let(:failure_data_max_size_limit_error) { { download_file_size: 100, message: "DOWNLOAD_LIMIT_EXCEEDED", result: "failed", time_taken: 0 } }
  let(:failure_data_files_not_available) { { download_file_size: 0, message: "DOWNLOAD_FILES_NOT_AVAILABLE", result: "failed", time_taken: 0 } }
  let(:failure_data_failed) { { download_file_size: 0, message: "DOWNLOAD_FILES_FAILED", result: "failed", time_taken: 0 } }

  let(:params) do
    {
      session_id: "session_id",
      max_files_size: 50,
      s3_config: {
        s3_bucket: "bucket",
        s3_region: "region"
      }
    }
  end

  before(:each) do
    allow(BrowserStack::Zombie).to receive(:configure)
    allow(BrowserStack::Zombie).to receive(:push_logs)
    allow(DeviceManager).to receive(:session_file)
    allow(File).to receive(:exist?).and_return(true)
    allow(FileUtils).to receive(:rm_rf)
    allow(DeviceManager).to receive(:device_configuration_check).and_return(device_config)
    @testObject = DownloadFile.new(device, session_id, product)
  end

  describe "#setup" do
    it "should create the list of preloaded media at platform" do
      expect(FileUtils).to receive(:mkdir_p)
      expect(FileUtils).to receive(:touch)
      expect(IdeviceFileUtils).to receive(:add_file)
      expect(IdeviceFileUtils).to receive(:get_list_of_preloaded_media)
      expect(File).to receive(:open).with(list_of_preloaded_media_path, "w")
      @testObject.setup
    end
  end

  describe "#generate_and_upload" do
    it "should return status as success, and upload file when there are no errors" do
      expect_any_instance_of(DownloadFileHelper).to receive(:generate).and_return({ message: "DOWNLOAD_FILES_SUCCESS", file_size: 10, status: "success" })
      expect_any_instance_of(DownloadFileHelper).to receive(:upload).and_return("some_presigned_url")
      expect_any_instance_of(DataReportHelper).to receive(:report).with(success_data)
      expect(@testObject.generate_and_upload(params, anything)).to eq({ message: "DOWNLOAD_FILES_SUCCESS", url: "some_presigned_url" })
    end
    it "should return status as 'No Files are available', and not upload file when no files are available" do
      expect_any_instance_of(DownloadFileHelper).to receive(:generate).and_return({ message: "DOWNLOAD_FILES_NOT_AVAILABLE", file_size: 0, status: "failed" })
      expect_any_instance_of(DownloadFileHelper).not_to receive(:upload)
      expect_any_instance_of(DataReportHelper).to receive(:report).with(failure_data_files_not_available)
      expect(@testObject.generate_and_upload(params, anything)).to eq({ message: "DOWNLOAD_FILES_NOT_AVAILABLE", url: nil })
    end

    it "should return status as 'download file limit exceeded', and not upload file when download file limit exceeded" do
      expect_any_instance_of(DownloadFileHelper).to receive(:generate).and_return({ message: "DOWNLOAD_LIMIT_EXCEEDED", file_size: 100, status: "failed" })
      expect_any_instance_of(DownloadFileHelper).not_to receive(:upload)
      expect_any_instance_of(DataReportHelper).to receive(:report).with(failure_data_max_size_limit_error)
      expect(@testObject.generate_and_upload(params, anything)).to eq({ message: "DOWNLOAD_LIMIT_EXCEEDED", url: nil })
    end

    it "should return status as 'failed', and not upload file when some error happened" do
      expect_any_instance_of(DownloadFileHelper).to receive(:generate).and_return({ message: "DOWNLOAD_FILES_FAILED", file_size: 0, status: "failed" })
      expect_any_instance_of(DownloadFileHelper).not_to receive(:upload)
      expect_any_instance_of(DataReportHelper).to receive(:report).with(failure_data_failed)
      expect(@testObject.generate_and_upload(params, anything)).to eq({ message: "DOWNLOAD_FILES_FAILED", url: nil })
    end
  end

  describe "#cleanup" do
    before(:each) do
      allow(IdeviceFileUtils).to receive(:truncate_folder)
    end

    context "when downloaded file zip is cleaned up succesfully" do
      it "should not throw exception and return true" do
        allow(FileUtils).to receive(:rm_rf)
        expect(@testObject.cleanup).to eq(true)
      end
    end

    context "when downloaded file zip is not cleaned up successfully" do
      it "should throw exception" do
        allow(FileUtils).to receive(:rm_rf).and_raise("Some error")
        expect { @testObject.cleanup }.to raise_error("Some error")
      end
    end
  end
end
