require_relative '../../../lib/utils/wda_version'

describe WDAVersion do
  let(:device) { "ABCD" }
  let(:appium_version) { "APPIUM_VERSION_1" }
  let(:ios_version) { 13.2 }
  let(:macos_version) { '10.14.1' }
  let(:force_download) { false }
  let(:wda_version) { 4 }
  let(:ppuid_file) { PpuidFile.new(device) }

  let(:wda_folder) do
    "/abcd"
  end

  before do
    # Mock request to download endpoint
    allow(Faraday).to receive(:get).and_return(Faraday::Response.new(status: 200, body: wda_version.to_s))
    allow(WDAVersion).to receive(:wda_folder_path).and_return(wda_folder)
    allow(BrowserStack::OSUtils).to receive(:macos_version).and_return(macos_version)
    allow(PpuidFile).to receive(:new).and_return(ppuid_file)
    allow(ppuid_file).to receive(:valid?).and_return(true)
    allow(ppuid_file).to receive(:content).and_return(['myBranch', 'myTeamID', 'myPPUID'])
    allow(ppuid_file).to receive(:ppuid).and_return('myPPUID')
  end

  let(:subject) do
    WDAVersion.new(device, appium_version, ios_version)
  end

  describe "#outdated_version?" do
    # TODO
  end

  describe "#sign_wda" do
    it "should run resign.sh and update wda db" do
      expect(subject).to receive(:run_resign_sh)
      expect(subject).to receive(:xctestrun_fixup)

      subject.sign_wda
    end
  end

  describe "#xctestrun_fixup" do
    it "should create a null file for appium" do
      expect(BrowserStack::XcodeUtils).to receive(:get_platform_version).and_return("10.1")
      expect(subject).to receive(:xctestrun_file_template).with("10.1").and_return("sample-iphoneos10.1.xctestrun")
      expect(File).to receive(:readable?).with("sample-iphoneos10.1.xctestrun").and_return(true).twice
      expect(subject).to receive(:xctestrun_file_template).with("null").and_return("sample-iphoneosnull.xctestrun").once
      expect(FileUtils).to receive(:cp).with("sample-iphoneos10.1.xctestrun", "sample-iphoneosnull.xctestrun").once

      subject.send(:xctestrun_fixup)
    end

    it "should create a 12.0 file for 12.1" do
      expect(BrowserStack::XcodeUtils).to receive(:get_platform_version).and_return("12.1")
      expect(subject).to receive(:xctestrun_file_template).with("12.1").and_return("sample-iphoneos12.1.xctestrun")
      expect(File).to receive(:readable?).with("sample-iphoneos12.1.xctestrun").and_return(true).twice

      expect(subject).to receive(:xctestrun_file_template).with("null").and_return("sample-iphoneosnull.xctestrun").ordered
      expect(FileUtils).to receive(:cp).with("sample-iphoneos12.1.xctestrun", "sample-iphoneosnull.xctestrun").once

      expect(subject).to receive(:xctestrun_file_template).with("12.0").and_return("sample-iphoneos12.0.xctestrun")
      expect(FileUtils).to receive(:cp).with("sample-iphoneos12.1.xctestrun", "sample-iphoneos12.0.xctestrun").once

      subject.send(:xctestrun_fixup)
    end

    it "should not create 12.0 file for 13" do
      expect(BrowserStack::XcodeUtils).to receive(:get_platform_version).and_return("13.0")
      expect(subject).to receive(:xctestrun_file_template).with("13.0").and_return("sample-iphoneos13.0.xctestrun")
      expect(File).to receive(:readable?).with("sample-iphoneos13.0.xctestrun").and_return(true).twice
      expect(subject).to receive(:xctestrun_file_template).with("null").and_return("sample-iphoneosnull.xctestrun").once
      expect(FileUtils).to receive(:cp).once

      subject.send(:xctestrun_fixup)
    end

    context "missing xctestrun files" do
      it "should raise an error if xctestrun is missing for os 13 " do
        expect(BrowserStack::XcodeUtils).to receive(:get_platform_version).and_return("13.0")
        expect(subject).to receive(:xctestrun_file_template).with("13.0").and_return("sample-iphoneos13.0.xctestrun")
        expect(File).to receive(:readable?).with("sample-iphoneos13.0.xctestrun").and_return(false).twice

        expect { subject.send(:xctestrun_fixup) }.to raise_error(XCTestRunFileMissing)
      end
    end
  end

  describe "#run_resign_sh" do
    let(:provisioning_profile) { double('mock_provisioning_profile') }

    before(:each) do
      allow(subject).to receive(:apple_tv_device?).and_return(false)
    end

    #
    # TODO add more specs
    #
    it "should run resign.sh script" do
      expect(MobileprovisionFile).to receive(:path).and_return('path/to/ppuid.mobileprovision')
      expect(ProvisioningProfile).to receive(:new).and_return(provisioning_profile)
      expect(provisioning_profile).to receive(:developer_certificate_identity)
      expect(provisioning_profile).to receive(:developer_certificate_sha1).exactly(2).times
      expect(provisioning_profile).to receive(:entitlements).and_return({ "keychain-access-groups" => ["TEAMID.*"] })
      expect(provisioning_profile).to receive(:team_id).and_return("TEAMID")
      expect(provisioning_profile).to receive(:entitlements_plist)
      expect(BrowserStack::OSUtils).to receive(:execute).with(anything, true).and_return(["DONE", 0])
      expect(BrowserStack::OSUtils).to receive(:execute).with(anything, true).and_return(["DONE", 0])

      subject.send(:run_resign_sh)
    end

    it "should raise error if resign.sh script fails" do
      expect(MobileprovisionFile).to receive(:path).and_return('path/to/ppuid.mobileprovision')
      expect(ProvisioningProfile).to receive(:new).and_return(provisioning_profile)
      expect(provisioning_profile).to receive(:developer_certificate_identity)
      expect(provisioning_profile).to receive(:developer_certificate_sha1).exactly(2).times
      expect(provisioning_profile).to receive(:entitlements).and_return({ "keychain-access-groups" => ["TEAMID.*"] })
      expect(provisioning_profile).to receive(:team_id).and_return("TEAMID")
      expect(provisioning_profile).to receive(:entitlements_plist)
      expect(BrowserStack::OSUtils).to receive(:execute).with(anything, true).and_return(["DONE", 1])

      expect do
        subject.send(:run_resign_sh)
      end.to raise_error("resign_wda.sh failed check logs")
    end
  end

  describe "#install_wda_version" do
    context "outdated_version = true" do
      before do
        allow(subject).to receive(:outdated_version?).and_return(true)
      end

      it "should download, extract and resign if ppuid file exists" do
        expect(ppuid_file).to receive(:valid?).and_return(true)
        expect(subject).to receive(:copy_source_files)
        expect(subject).to receive(:sign_wda)

        subject.install_wda_version
      end

      it "should not download, extract and resign if ppuid file does not exist" do
        expect(ppuid_file).to receive(:valid?).and_return(false)
        expect(subject).to_not receive(:copy_source_files)
        expect(subject).to_not receive(:sign_wda)

        subject.install_wda_version
      end
    end

    context "force_download" do
      let(:force_download) { true }

      it "should download, extract and resign if ppuid file exists" do
        expect(ppuid_file).to receive(:valid?).and_return(true)
        expect(subject).to receive(:copy_source_files)
        expect(subject).to receive(:sign_wda)

        subject.install_wda_version
      end
    end

    context "outdated_version = false" do
      before do
        allow(subject).to receive(:outdated_version?).and_return(false)
      end

      it "should not do anything if version is not outdated" do
        expect(subject).to_not receive(:copy_source_files)
        expect(subject).to_not receive(:sign_wda)

        subject.install_wda_version
      end
    end
  end

  describe '#ensure_correct_ios_version' do
    context 'when ios version is less than 13.4' do
      it 'returns the major ios version only' do
        expect(WDAVersion.send(:ensure_correct_ios_version, '12.1')).to eq(12)
      end
    end

    context 'when ios version is greater or equal to 13.4' do
      it 'returns \'13.4\' when ios version is 13.4' do
        expect(WDAVersion.send(:ensure_correct_ios_version, '13.4')).to eq('13.4')
      end

      it 'returns \'13.4\' when ios version is 13.4.1' do
        expect(WDAVersion.send(:ensure_correct_ios_version, '13.4.1')).to eq('13.4')
      end

      it 'returns \'13.4\' when ios version is 14' do
        expect(WDAVersion.send(:ensure_correct_ios_version, '14')).to eq('13.4')
      end
    end

    context 'when ios version is 12 to 13.3 but host is catalina' do
      before do
        allow(BrowserStack::OSUtils).to receive(:macos_version).and_return('10.15.5')
      end

      it 'returns \'13.4\' when ios version is 12' do
        expect(WDAVersion.send(:ensure_correct_ios_version, '12')).to eq('13.4')
      end

      it 'returns \'13.4\' when ios version is 13.3' do
        expect(WDAVersion.send(:ensure_correct_ios_version, '13.3')).to eq('13.4')
      end
    end
  end
end
