require_relative '../../spec_helper'
require_relative '../../../lib/utils/xcode_build_utils'
require 'fileutils'

describe BrowserStack::XcodeBuildUtils do
  context "#get_team_name_from_profile" do
    before(:each) do
      tmp_file = double
      allow(Tempfile).to receive(:new).and_return(tmp_file)
      allow(tmp_file).to receive(:path).and_return("path")
      allow(tmp_file).to receive(:unlink).and_return(true)
      allow(File).to receive(:write).and_return(true)
    end

    it "returns teamname from provision profile" do
      allow(BrowserStack::OSUtils).to receive(:execute).and_return("some_profile", ["team Name ", 0])

      expect(BrowserStack::XcodeBuildUtils.get_team_name_from_profile("mobileprovision_path")).to eq("team Name")
    end

    it "returns empty teamname if any command status code is non-zero" do
      allow(BrowserStack::OSUtils).to receive(:execute).and_return("some_profile", ["No such file present", 1])

      expect(BrowserStack::XcodeBuildUtils.get_team_name_from_profile("mobileprovision_path")).to eq("")
    end
  end
end
