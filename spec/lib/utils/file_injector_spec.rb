require_relative '../../spec_helper'
require_relative '../../../lib/utils/file_injector'
require_relative '../../../lib/utils/osutils'
require_relative '../../../config/constants'
require_relative '../../../server/device_manager'
require_relative '../../../lib/utils/utils'

describe FileInjector do
  let(:device) { 'test_device' }
  let(:params) do
    {
      session_id: 'some_session_id',
      file_url: 'some_file_url',
      media_hashed_id: 'some_hashed_id',
      format: '.pdf',
      bundle_id: 'some_bundle_id',
      file_name: 'test.pdf'
    }
  end

  before(:each) do
    allow(Utils).to receive(:send_to_eds)
    allow(Utils).to receive(:notify_pusher)
    allow(BrowserStack::Zombie).to receive(:push_logs)
    allow(OSUtils).to receive(:execute)
    allow(IdeviceUtils).to receive(:os).and_return(15.0)
  end

  context "inject_file" do
    it 'should not download the file from s3 and dont push it in app if the product is not app live or live' do
      params[:product] = 'automate'

      expect(FileInjector).to_not receive(:get_injection_file_dir)
      expect(FileUtils).to_not receive(:mkdir_p)
      expect(File).to_not receive(:exist?)
      expect(BrowserStack::HttpUtils).to_not receive(:download)
      expect(FileInjector).to_not receive(:insert_injection_file_in_app)

      expect { FileInjector.send(:inject_file, device, params) }.not_to raise_error
    end

    it 'should download the file from s3 and push it in chrome folder if product is app live or live' do
      params[:product] = 'app_live'

      expect(FileInjector).to receive(:get_injection_file_dir).and_return("/tmp/injection_media/test_device")
      expect(FileUtils).to receive(:mkdir_p).with("/tmp/injection_media/test_device")
      expect(BrowserStack::HttpUtils).to receive(:download).with('some_file_url', '/tmp/injection_media/test_device/some_hashed_id.pdf', { retry_count: 3, timeout: 60 }, "file_injection")
      expect(FileInjector).to receive(:insert_injection_file_in_app).with('test_device', 'com.google.chrome.ios', '/tmp/injection_media/test_device/some_hashed_id.pdf', '.pdf', 'some_session_id', params, 'test')

      expect { FileInjector.send(:inject_file, device, params) }.not_to raise_error
    end

    it 'should download the file from s3 and push it in chrome apps foldder if product is live' do
      params[:product] = 'live'

      expect(FileInjector).to receive(:get_injection_file_dir).and_return("/tmp/injection_media/test_device")
      expect(FileUtils).to receive(:mkdir_p).with("/tmp/injection_media/test_device")
      expect(BrowserStack::HttpUtils).to receive(:download).with('some_file_url', '/tmp/injection_media/test_device/some_hashed_id.pdf', { retry_count: 3, timeout: 60 }, "file_injection")
      expect(FileInjector).to receive(:insert_injection_file_in_app).with('test_device', 'com.google.chrome.ios', '/tmp/injection_media/test_device/some_hashed_id.pdf', '.pdf', 'some_session_id', params, 'test')

      expect { FileInjector.send(:inject_file, device, params) }.not_to raise_error
    end

    it 'should raise error if error is raised while pushing file in app' do
      params[:product] = 'app_live'

      expect(FileInjector).to receive(:get_injection_file_dir).and_return("/tmp/injection_media/test_device")
      expect(FileUtils).to receive(:mkdir_p).with("/tmp/injection_media/test_device")
      expect(BrowserStack::HttpUtils).to receive(:download).with('some_file_url', '/tmp/injection_media/test_device/some_hashed_id.pdf', { retry_count: 3, timeout: 60 }, "file_injection")
      expect(FileInjector).to receive(:insert_injection_file_in_app).with('test_device', 'com.google.chrome.ios', '/tmp/injection_media/test_device/some_hashed_id.pdf', '.pdf', 'some_session_id', params, 'test').and_raise("some_error")

      expect { FileInjector.send(:inject_file, device, params) }.to raise_error("some_error")
    end

    it 'should raise error if error is raised while downloading the file' do
      params[:product] = 'app_live'

      expect(FileInjector).to receive(:get_injection_file_dir).and_return("/tmp/injection_media/test_device")
      expect(FileUtils).to receive(:mkdir_p).with("/tmp/injection_media/test_device")
      expect(BrowserStack::HttpUtils).to receive(:download).with('some_file_url', '/tmp/injection_media/test_device/some_hashed_id.pdf', { retry_count: 3, timeout: 60 }, "file_injection").and_raise("some_error")
      expect(FileInjector).to_not receive(:insert_injection_file_in_app)

      expect { FileInjector.send(:inject_file, device, params) }.to raise_error("some_error")
    end
  end

  context 'insert_injection_file_in_app' do
    let(:bundle_id) { 'some_bundle_id' }
    let(:file_name) { 'some_file_name' }
    let(:file_path) { 'some_file_path.pdf' }
    let(:format) { '.pdf' }

    it 'should return true if media is pushed to device is successfully' do
      expect(IdeviceFileUtils).to receive(:add_file).and_return(["some_output", 0])
      expect(FileInjector.send(:insert_injection_file_in_app, device, bundle_id, file_path, format, 'some_session_id', params, file_name)).to eql(true)
    end

    it 'should raise error if media is not pushed to device' do
      expect(IdeviceFileUtils).to receive(:add_file).exactly(3).and_return(['some_output', 1])
      expect { FileInjector.send(:insert_injection_file_in_app, device, bundle_id, file_path, format, 'some_session_id', params, file_name) }.to raise_error(RuntimeError, "Failed to push file to device #{device}")
    end

    it 'should create unzipped directory, unzip file if file format is .zip and push to device' do
      zip_format = ".zip"
      expect(FileUtils).to receive(:mkdir_p).with(Shellwords.escape("/tmp/injection_media/test_device/#{file_name}"))
      expect(OSUtils).to receive(:execute).with("unzip \"#{file_path}\" -d \"/tmp/injection_media/test_device/#{file_name}\"", true, timeout: 30).and_return(["some_output", 0])
      expect(IdeviceFileUtils).to receive(:add_file).and_return(['some_output', 0])
      expect(FileInjector.send(:insert_injection_file_in_app, device, bundle_id, file_path, zip_format, 'some_session_id', params, file_name)).to eql(true)
    end

    it 'should raise error if unzip file fails and if file format is .zip' do
      zip_format = ".zip"
      expect(FileUtils).to receive(:mkdir_p).exactly(3).with(Shellwords.escape("/tmp/injection_media/test_device/#{file_name}"))
      expect(OSUtils).to receive(:execute).with("unzip \"#{file_path}\" -d \"/tmp/injection_media/test_device/#{file_name}\"", true, timeout: 30).exactly(3).and_return(["some_output", 1])
      expect { FileInjector.send(:insert_injection_file_in_app, device, bundle_id, file_path, zip_format, 'some_session_id', params, file_name) }.to raise_error("Failed to push file to device #{device}, Reason: Failed to unzip file at path: #{file_path} to directory: /tmp/injection_media/test_device/#{file_name}, " \
                                                                                                                                                                 "result: some_output, status: 1")
    end
  end

  context "get_injection_file_dir" do
    it "should return the injection file dir" do
      expect(FileInjector.send(:get_injection_file_dir, device)).to eql("/tmp/injection_media/#{device}")
    end
  end

  context 'cleanup' do
    it 'should delete the file injection dir' do
      expect(FileInjector).to receive(:get_injection_file_dir)
      expect(FileUtils).to receive(:rm_rf)

      expect { FileInjector.send(:cleanup, device) }.not_to raise_error
    end
  end
end

