require_relative '../../spec_helper'
require_relative '../../../lib/utils/idevice_utils'
require_relative '../../../lib/utils/osutils'

require 'shellwords'
require 'timecop'

describe IdeviceUtils do
  let(:udid) { 'myphone' }

  # Creating sample status objects, which OSUtils.execute can return
  let(:success_status) do
    system("ls /tmp > /dev/null 2>&1")
    $CHILD_STATUS
  end

  let(:failure_status) do
    system("fail > /dev/null 2>&1")
    $CHILD_STATUS
  end

  before(:all) do
    templates_dir = '/usr/local/.browserstack/realmobile/templates'
    server_config = {
      'contacts_data_path' => "#{templates_dir}/contacts_data.plist",
      'contacts_metadata_path' => "#{templates_dir}/contacts_metadata.plist",
      'known_apps' => ['com.browserstack.app'].to_set
    }

    IdeviceUtils.configure(server_config)
  end

  describe ".idevices" do
    context "when getting the currently connected devices" do
      before(:each) do
        IdeviceUtils.instance_variable_set(:@idevice_id, "idevice_id")
      end

      it "executes the command 'idevice_id -l' to get the currently connected devices" do
        allow(BrowserStack::OSUtils).to receive(:execute).and_return("execute has been called\n")

        expect(BrowserStack::OSUtils).to receive(:execute).with("idevice_id -l")
        IdeviceUtils.idevices
      end

      it "converts the string of devices returned by 'idevice_id -l' to an array of devices" do
        allow(BrowserStack::OSUtils)
          .to receive(:execute)
          .with("idevice_id -l")
          .and_return("device_id_1\ndevice_id_2\ndevice_id_3\n")

        expect(IdeviceUtils.idevices).to be_eql(["device_id_1", "device_id_2", "device_id_3"])
      end

      it "retries 5 more times if the initial 'idevice_id -l' command returned empty" do
        allow(BrowserStack::OSUtils)
          .to receive(:execute)
          .with("idevice_id -l")
          .and_return("")

        expect(BrowserStack::OSUtils).to receive(:execute).exactly(6).times

        IdeviceUtils.idevices
      end
    end
  end

  describe '.idevice_name' do
    let(:name) { "iPhone" }

    before(:all) do
      IdeviceUtils.instance_variable_set(:@idevicename, "idevicename")
    end

    it 'should call idevicename without any param with name = nil' do
      expect(BrowserStack::OSUtils).to receive(:execute).with("idevicename -u #{udid}")
                                                        .and_return(name)
      IdeviceUtils.idevice_name(udid)
    end

    it 'should call idevicename with the given name' do
      expect(BrowserStack::OSUtils).to receive(:execute).with("idevicename -u #{udid} '#{name}'")
                                                        .and_return(name)
      IdeviceUtils.idevice_name(udid, name: name)
    end
  end

  describe '.list_apps' do
    context 'when the kind is unknown' do
      it 'raises ArgumentError' do
        expect { IdeviceUtils.list_apps(udid, kind: :foo) }
          .to raise_error(ArgumentError, 'Unknown kind: foo')
      end
    end

    context do
      before do
        allow(Plist).to receive(:parse_xml).and_return([])
      end

      it 'lists user apps when no second arg is given' do
        expect(IdeviceUtils)
          .to receive(:run_with_timeout)
          .with("#{IDEVICEINSTALLER} -u #{udid} -o xml -l -o list_user 2>&1", 5)
          .and_return(['the_plist', success_status])

        IdeviceUtils.list_apps(udid)
      end

      it 'lists all apps when second arg is all' do
        expect(IdeviceUtils)
          .to receive(:run_with_timeout)
          .with("#{IDEVICEINSTALLER} -u #{udid} -o xml -l -o list_all 2>&1", 5)
          .and_return(['the_plist', success_status])

        IdeviceUtils.list_apps(udid, kind: :all)
      end

      it 'tries multiple attempts when specified' do
        expect(IdeviceUtils).to receive(:run_with_timeout)
          .exactly(3).times
          .and_return(['the_plist', failure_status])

        expect { IdeviceUtils.list_apps(udid, attempts: 3) }.to raise_error(/Non zero exit code/)
      end
    end

    it 'returns bundle_id, version and display name for all of them' do
      # end to end test
      fixture_plist = "#{__dir__}/../../fixtures/sample_ideviceinstaller_apps.plist"

      allow(IdeviceUtils)
        .to receive(:run_with_timeout)
        .and_return([File.read(fixture_plist), success_status])

      apps = IdeviceUtils.list_apps(udid)

      expect(apps).to be_eql(
        [
          { bundle_id: 'com.apple.foo', version: '1.0', display_name: 'Fooer', version_string: '1.0', bundle_executable: 'Fooer' },
          { bundle_id: 'com.apple.AppStore', version: '1', display_name: 'App Store', version_string: '3.0', bundle_executable: 'AppStore' }
        ]
      )
    end
  end

  describe '.app_version' do
    it "should return version of the app" do
      allow(IdeviceUtils).to receive(:list_apps).and_return([{ bundle_id: 'com.test', version: '1.0.1' }])
      expect(IdeviceUtils.app_version('abcd', 'com.test'))
    end
  end

  describe '.list_user_installed_apps_details' do
    before do
      user_apps = [
        { bundle_id: "com.apple.foo", version: "1.0", display_name: "Fooer" },
        { bundle_id: "com.browserstack.app", version: "1", display_name: "BrowserStack" }
      ]
      allow(IdeviceUtils).to receive(:list_apps).and_return(user_apps)
    end

    it 'returns user apps which are not known apps' do
      unknown_user_app = [
        { bundle_id: "com.apple.foo", version: "1.0", display_name: "Fooer" }
      ]
      expect(IdeviceUtils.list_user_installed_apps_details(udid)).to eq(unknown_user_app)
    end
  end

  describe '.app_installed?' do
    it 'is true when app is installed' do
      apps = [
        { bundle_id: "com.apple.foo", version: "1.0", display_name: "Fooer" },
        { bundle_id: "com.browserstack.app", version: "1", display_name: "BrowserStack" }
      ]
      allow(IdeviceUtils).to receive(:list_apps).and_return(apps)

      expect(IdeviceUtils.app_installed?(udid, 'com.apple.foo')).to be true
      expect(IdeviceUtils.app_installed?(udid, 'BrowserStack')).to be true
    end

    it 'is false when app is not installed' do
      apps = [
        { bundle_id: "com.apple.foo", version: "1.0", display_name: "Fooer" },
        { bundle_id: "com.browserstack.app", version: "1", display_name: "BrowserStack" }
      ]
      allow(IdeviceUtils).to receive(:list_apps).and_return(apps)

      expect(IdeviceUtils.app_installed?(udid, 'com.unknown.app')).to be false
    end
  end

  describe '.app_installed?' do
    it 'is true when app is installed' do
      apps = [
        { bundle_id: "com.apple.foo", version: "1.0", display_name: "Fooer" },
        { bundle_id: "com.browserstack.app", version: "1", display_name: "BrowserStack" }
      ]
      allow(IdeviceUtils).to receive(:list_apps).and_return(apps)

      expect(IdeviceUtils.app_installed?(udid, 'com.apple.foo')).to be true
      expect(IdeviceUtils.app_installed?(udid, 'BrowserStack')).to be true
    end

    it 'is false when app is not installed' do
      apps = [
        { bundle_id: "com.apple.foo", version: "1.0", display_name: "Fooer" },
        { bundle_id: "com.browserstack.app", version: "1", display_name: "BrowserStack" }
      ]
      allow(IdeviceUtils).to receive(:list_apps).and_return(apps)

      expect(IdeviceUtils.app_installed?(udid, 'com.unknown.app')).to be false
    end
  end

  context "idevicecontacts" do
    describe '#delete_contacts' do
      it 'should delete contacts' do
        expect(BrowserStack::OSUtils).to receive(:execute)
          .with("/usr/local/.browserstack/realmobile/deps/bin/idevicecontacts -u myphone -d", true, { timeout: 20 })

        expect { IdeviceUtils.delete_contacts("myphone") }.not_to raise_error
      end

      it 'should raise exception if delete contacts timedout' do
        expect(BrowserStack::OSUtils).to receive(:execute)
          .with("/usr/local/.browserstack/realmobile/deps/bin/idevicecontacts -u myphone -d", true, { timeout: 20 })
          .and_raise(OSUtilsError)

        expect { IdeviceUtils.delete_contacts("myphone") }.to raise_error(IdeviceError)
      end
    end

    describe '#load_contacts' do
      let(:file_paths_args) do
        '/usr/local/.browserstack/realmobile/templates/contacts_data.plist,'\
        '/usr/local/.browserstack/realmobile/templates/contacts_metadata.plist'
      end

      it 'should load contacts' do
        expect(BrowserStack::OSUtils).to receive(:execute)
          .with("/usr/local/.browserstack/realmobile/deps/bin/idevicecontacts -u myphone -a '#{file_paths_args}'", true, { timeout: 20 })

        expect { IdeviceUtils.load_contacts("myphone") }.not_to raise_error
      end

      it 'should raise exception if load contacts timedout' do
        expect(BrowserStack::OSUtils).to receive(:execute)
          .with("/usr/local/.browserstack/realmobile/deps/bin/idevicecontacts -u myphone -a '#{file_paths_args}'", true, { timeout: 20 })
          .and_raise(OSUtilsError)

        expect { IdeviceUtils.load_contacts("myphone") }.to raise_error(IdeviceError)
      end
    end

    describe '#get_contacts_count' do
      let(:device) { 'myphone' }

      let(:cmd) do
        "/usr/local/.browserstack/realmobile/deps/bin/idevicecontacts -u myphone -l | plutil  -convert json -r -o - -- - | jq length"
      end

      it 'should get contacts count' do
        expect(BrowserStack::OSUtils).to receive(:execute)
          .with(cmd, true, { timeout: 20 }).and_return(['40', '0'])

        expect do
          output, status = IdeviceUtils.get_contacts_count(device)
          expect(output).to eq([40])
          expect(status).to eq("0")
        end.not_to raise_error
      end

      it 'should raise exception if get contacts count timedout' do
        expect(BrowserStack::OSUtils).to receive(:execute)
          .with(cmd, true, { timeout: 20 }).and_raise(OSUtilsError)
        expect { IdeviceUtils.get_contacts_count(device) }.to raise_error(IdeviceError)
      end
    end

    describe '#get_contacts_count_xml' do
      let(:device) { 'myphone' }

      let(:cmd) do
        "/usr/local/.browserstack/realmobile/deps/bin/idevicecontacts -u myphone -l "
      end

      it 'should get contacts count' do
        expect(BrowserStack::OSUtils).to receive(:execute)
          .with(cmd, true, { timeout: 20 }).and_return(['<plist><dict><key>123</key></dict></plist>', '0'])

        expect do
          output, status = IdeviceUtils.get_contacts_count_xml(device)
          expect(output).to eql(1)
          expect(status).to eq("0")
        end.not_to raise_error
      end

      it 'should raise exception if get contacts count timed out' do
        expect(BrowserStack::OSUtils).to receive(:execute)
          .with(cmd, true, { timeout: 20 }).and_raise(OSUtilsError)
        expect { IdeviceUtils.get_contacts_count_xml(device) }.to raise_error(IdeviceError)
      end
    end

    describe '.delete_bookmarks' do
      context 'when deleting safari bookmarks on a device' do
        it 'executes the idevicebookmark -d command with a timeout' do
          allow(BrowserStack::OSUtils)
            .to receive(:execute)
            .and_return(['done', success_status])

          expect(BrowserStack::OSUtils)
            .to receive(:execute)
            .with(
              "/usr/local/.browserstack/realmobile/deps/bin/idevicebookmark -u myphone -d",
              true,
              timeout: IdeviceUtils::COMMAND_TIMEOUT
            )

          IdeviceUtils.delete_bookmarks('myphone')
        end
      end

      context 'when the return status of idevicebookmark is not 0' do
        it 'raises an exception' do
          allow(BrowserStack::OSUtils)
            .to receive(:execute)
            .and_return(['done', failure_status])

          expect { IdeviceUtils.delete_bookmarks('myphone') }
            .to raise_error(IdeviceBookmarkError, /non-zero return/)
        end
      end

      context 'when the execute command times out' do
        it 'raises an exception' do
          allow(BrowserStack::OSUtils)
            .to receive(:execute)
            .and_raise(OSUtilsError)

          expect { IdeviceUtils.delete_bookmarks('myphone') }
            .to raise_error(IdeviceBookmarkError, /command timed out/)
        end
      end
    end

    describe 'get_crash_report_from_device' do
      let(:device_id) { 'device_id' }
      let(:session_id) { 'session_id' }
      let(:app_name) { 'app_name' }

      before(:each) do
        FileUtils.mkdir_p("/tmp/crash-reports_#{device_id}")
        FileUtils.touch("/tmp/crash-reports_#{device_id}/#{app_name}-1.ips")
        FileUtils.touch("/tmp/crash-reports_#{device_id}/#{app_name}-2.ips")
        FileUtils.touch("/tmp/crash-reports_#{device_id}/Space App-1.ips")
        FileUtils.touch("/tmp/crash-reports_#{device_id}/Space App-2.ips")
      end

      after(:each) do
        FileUtils.rm_rf("/tmp/crash-reports_#{device_id}")
      end

      context 'success' do
        it 'fires idevicecrashreport and returns 2 crash files' do
          expect(FileUtils).to receive(:mkdir_p).with("/tmp/crash-reports_#{device_id}")
          expect(BrowserStack::OSUtils).to receive(:execute).with("/usr/local/.browserstack/realmobile/deps/bin/idevicecrashreport -u #{device_id} /tmp/crash-reports_#{device_id} -f #{app_name}", timeout: 45)
          expect(BrowserStack::OSUtils).to receive(:execute).with("zip -r -j /tmp/crash_report_#{session_id}.zip /tmp/crash-reports_#{device_id}/#{app_name}-1.ips /tmp/crash-reports_#{device_id}/#{app_name}-2.ips" , timeout: 10)
          expect(File).to receive(:exist?).with("/tmp/crash_report_#{session_id}.zip").and_return(true)
          expect(BrowserStack::Zombie).to receive(:push_logs)

          crash_reports = IdeviceUtils.get_crash_report_from_device(device_id, [app_name], session_id)

          expect(crash_reports).to eq(["/tmp/crash-reports_device_id/app_name-1.ips", "/tmp/crash-reports_device_id/app_name-2.ips"])
        end

        it 'fires idevicecrashreport and returns 2 crash files with app having spaces' do
          expect(FileUtils).to receive(:mkdir_p).with("/tmp/crash-reports_#{device_id}")
          expect(BrowserStack::OSUtils).to receive(:execute).with("/usr/local/.browserstack/realmobile/deps/bin/idevicecrashreport -u #{device_id} /tmp/crash-reports_#{device_id} -f #{Shellwords.escape('Space App')}", timeout: 45)
          expect(BrowserStack::OSUtils).to receive(:execute).with("zip -r -j /tmp/crash_report_#{session_id}.zip #{Shellwords.escape("/tmp/crash-reports_#{device_id}/Space App-1.ips")} #{Shellwords.escape("/tmp/crash-reports_#{device_id}/Space App-2.ips")}" , timeout: 10)
          expect(File).to receive(:exist?).with("/tmp/crash_report_#{session_id}.zip").and_return(true)
          expect(BrowserStack::Zombie).to receive(:push_logs)

          num_crash_reports = IdeviceUtils.get_crash_report_from_device(device_id, ["Space App"], session_id)

          expect(num_crash_reports).to eq(["/tmp/crash-reports_device_id/Space\\ App-1.ips", "/tmp/crash-reports_device_id/Space\\ App-2.ips"])
        end

        it 'fires idevicecrashreport and returns no crash files' do
          FileUtils.rm_rf("/tmp/crash-reports_#{device_id}")

          expect(FileUtils).to receive(:mkdir_p).with("/tmp/crash-reports_#{device_id}")
          expect(BrowserStack::OSUtils).to receive(:execute).with("/usr/local/.browserstack/realmobile/deps/bin/idevicecrashreport -u #{device_id} /tmp/crash-reports_#{device_id} -f #{app_name}", timeout: 45)
          expect(BrowserStack::OSUtils).not_to receive(:execute).with("zip -r -j /tmp/crash_report_#{session_id}.zip /tmp/crash-reports_#{device_id}/#{app_name}-1.ips /tmp/crash-reports_#{device_id}/#{app_name}-2.ips" , timeout: 10)
          expect(BrowserStack::Zombie).to receive(:push_logs)

          num_crash_reports = IdeviceUtils.get_crash_report_from_device(device_id, [app_name], session_id)

          expect(num_crash_reports).to eq([])
        end
      end

      context 'failure' do
        it 'raises exception if command gets timed out' do
          expect(FileUtils).to receive(:mkdir_p).with("/tmp/crash-reports_#{device_id}")
          expect(BrowserStack::OSUtils).to receive(:execute).exactly(1).times.with("/usr/local/.browserstack/realmobile/deps/bin/idevicecrashreport -u #{device_id} /tmp/crash-reports_#{device_id} -f #{app_name}", timeout: 45).and_raise(OSUtilsError)
          expect(BrowserStack.logger).to receive(:error).exactly(1).times
          expect(BrowserStack::Zombie).to receive(:push_logs)

          expect { IdeviceUtils.get_crash_report_from_device(device_id, [app_name], session_id) }.to raise_error("idevicecrashreport fetch failed")
        end

        it 'raises exception if there is a standard error' do
          expect(FileUtils).to receive(:mkdir_p).with("/tmp/crash-reports_#{device_id}")
          expect(BrowserStack::OSUtils).to receive(:execute).exactly(1).times.with("/usr/local/.browserstack/realmobile/deps/bin/idevicecrashreport -u #{device_id} /tmp/crash-reports_#{device_id} -f #{app_name}", timeout: 45).and_raise(StandardError)
          expect(BrowserStack.logger).to receive(:error).exactly(1).times
          expect(BrowserStack::Zombie).to receive(:push_logs)

          expect { IdeviceUtils.get_crash_report_from_device(device_id, [app_name], session_id) }.to raise_error("idevicecrashreport fetch failed")
        end

        it 'raises exception zip is not created' do
          expect(FileUtils).to receive(:mkdir_p).with("/tmp/crash-reports_#{device_id}")
          expect(BrowserStack::OSUtils).to receive(:execute).with("/usr/local/.browserstack/realmobile/deps/bin/idevicecrashreport -u #{device_id} /tmp/crash-reports_#{device_id} -f #{Shellwords.escape('Space App')}", timeout: 45)
          expect(BrowserStack::OSUtils).to receive(:execute).with("zip -r -j /tmp/crash_report_#{session_id}.zip #{Shellwords.escape("/tmp/crash-reports_#{device_id}/Space App-1.ips")} #{Shellwords.escape("/tmp/crash-reports_#{device_id}/Space App-2.ips")}" , timeout: 10)
          expect(File).to receive(:exist?).with("/tmp/crash_report_#{session_id}.zip").and_return(false)
          expect(BrowserStack.logger).to receive(:error).exactly(1).times
          expect(BrowserStack::Zombie).to receive(:push_logs)

          expect { IdeviceUtils.get_crash_report_from_device(device_id, ["Space App"], session_id) }.to raise_error("idevicecrashreport fetch failed")
        end
      end
    end

    describe 'clear_clash_reports_from_device' do
      let(:device_id) { 'device_id' }
      context 'success' do
        it 'fires idevicecrashreport without raising exception' do
          expect(FileUtils).to receive(:mkdir_p).with("/tmp/crash-reports_#{device_id}")
          expect(BrowserStack::OSUtils).to receive(:execute).with("/usr/local/.browserstack/realmobile/deps/bin/idevicecrashreport -u #{device_id} /tmp/crash-reports_#{device_id} -s .log", timeout: 45).once
          expect(BrowserStack::OSUtils).to receive(:execute).with("grep -C 10 '\"name\" : \"replayd\"' /tmp/crash-reports_#{device_id}/JetsamEvent* | grep '\"reason\"'", timeout: 5).once
          expect(FileUtils).to receive(:rm_rf).at_least(:once).with("/tmp/crash-reports_#{device_id}")
          expect(BrowserStack::Zombie).to receive(:push_logs)

          IdeviceUtils.clear_crash_reports_from_device(device_id)
        end
      end

      context 'failure' do
        it 'raises exception if command gets timed out' do
          expect(FileUtils).to receive(:mkdir_p).with("/tmp/crash-reports_#{device_id}")
          expect(BrowserStack::OSUtils).to receive(:execute).exactly(1).times.with("/usr/local/.browserstack/realmobile/deps/bin/idevicecrashreport -u #{device_id} /tmp/crash-reports_#{device_id} -s .log", timeout: 45).and_raise(OSUtilsError)
          expect(FileUtils).to receive(:rm_rf).at_least(:once).with("/tmp/crash-reports_#{device_id}")
          expect(BrowserStack.logger).to receive(:error).exactly(1).times
          expect(BrowserStack::Zombie).to receive(:push_logs).exactly(1).times

          IdeviceUtils.clear_crash_reports_from_device(device_id)
        end
      end
    end
  end

  describe '#launch_app_with_bundle_id' do
    let(:device_id) { 'device_id' }
    let(:bundle_id) { 'fake.bundle.id' }
    let(:thread) { double('Thread') }

    before do
      allow_any_instance_of(Object).to receive(:sleep)
    end

    it 'calls a separate thread and then kills the thread' do
      expect(Thread).to receive(:bs_run).and_return(thread)
      expect(Thread).to receive(:kill).with(thread)
      IdeviceUtils.launch_app_with_bundle_id(device_id, bundle_id)
    end
  end

  describe '#timezone' do
    let(:uuid) { '1' }

    it 'returns the device timezone' do
      expect(IdeviceUtils).to receive(:ideviceinfo).with(uuid, "TimeZone").and_return(['Asia/Kolkata'])
      expect(IdeviceUtils.timezone(uuid)).to eq('Asia/Kolkata')
    end
  end

  describe '#time' do
    let(:current_timestamp) { 0 }
    let(:uuid) { '1' }

    before do
      Timecop.freeze(Time.at(current_timestamp))
    end

    after do
      Timecop.return
    end

    it 'returns the device time' do
      expect(IdeviceUtils).to receive(:ideviceinfo).with(uuid, "TimeZoneOffsetFromUTC").and_return([10800])
      expect(IdeviceUtils.time(uuid)).to be_between(Time.at(current_timestamp) + 10790, Time.at(current_timestamp) + 10810)
    end
  end
end
