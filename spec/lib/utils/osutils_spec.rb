require_relative '../../spec_helper'
require_relative '../../../lib/utils/osutils'

describe BrowserStack::OSUtils do
  context ".device_ios_version" do
    let(:uuid) { '1234' }

    it 'Gives the correct major version as a string for the expected ideviceinfo output' do
      ideviceinfo = fixture('ideviceinfo.txt')
      allow(BrowserStack::OSUtils).to receive(:`).with("ideviceinfo -u #{uuid}").and_return(ideviceinfo)
      expect(BrowserStack::OSUtils.device_ios_version('1234')).to be_eql('10')
    end

    it 'Gives 13.4 as the version for 13.4 devices' do
      ideviceinfo = fixture('ideviceinfo_13-4.txt')
      allow(BrowserStack::OSUtils).to receive(:`).with("ideviceinfo -u #{uuid}").and_return(ideviceinfo)
      expect(BrowserStack::OSUtils.device_ios_version('1234')).to be_eql("13.4")
    end

    it 'Returns nil for unexpected input' do
      allow(BrowserStack::OSUtils).to receive(:`).with("ideviceinfo -u #{uuid}").and_return('Command not found')
      expect(BrowserStack::OSUtils.device_ios_version('1234')).to be_eql(nil)
    end
  end

  describe '.kill_all_processes' do
    let(:process_name) { '/usr/local/bin/fakebinary' }
    let(:device) { 'abc123' }
    let(:command) { "ps -ef | grep #{process_name} | grep #{device} | grep -v grep | awk '{print $2}' | xargs kill" }

    context 'when force is true' do
      it 'runs command with -9 at the end' do
        command << " -9"
        expect(BrowserStack::OSUtils).to receive(:execute).with(command)
        BrowserStack::OSUtils.kill_all_processes(process_name, device, true)
      end
    end

    context 'when force is false' do
      it 'runs command without -9 on the end' do
        expect(BrowserStack::OSUtils).to receive(:execute).with(command)
        BrowserStack::OSUtils.kill_all_processes(process_name, device)
      end
    end
  end

  describe '.kill_process_on_port' do
    context 'when passing incorrect port as argument' do
      it 'should return nil' do
        expect(BrowserStack::OSUtils.kill_process_on_port("abc")).to eql(nil)
        expect(BrowserStack::OSUtils.kill_process_on_port("a12")).to eql(nil)
      end
    end
    context 'check return status' do
      it 'should return expected output' do
        allow(BrowserStack::OSUtils)
          .to receive(:execute)
          .with("while sudo kill -9 $(sudo lsof -t -i:8993 -sTCP:LISTEN) 2>/dev/null; do sleep 1; done")
          .and_return("")
        expect(BrowserStack::OSUtils.kill_process_on_port(8993).to_s).to eql("")
        expect(BrowserStack::OSUtils.kill_process_on_port(8993).to_s).to eql("")
      end
    end
  end

  describe '.list_port_process' do
    it 'should list lsof output for a port' do
      allow(BrowserStack::OSUtils).to receive(:execute).with("sudo lsof -i:8993").and_return("")
      expect(BrowserStack::OSUtils.list_port_process(8993)).to eql([])
    end
  end

  describe '.kill_duplicate_stale_processes' do
    context 'when checking if any given process is running more than once under launchd as parent' do
      it 'should kill the process' do
        allow(BrowserStack::OSUtils)
          .to receive(:execute)
          .with("ps -ef | grep 'process_name' | grep 'process_identifier' | grep -v grep | awk '{print $2,$3}' ")
          .and_return("123 2\n 124 1")
        expect(BrowserStack::OSUtils).to receive(:kill_pid).with(124)
        expect(BrowserStack::OSUtils.kill_duplicate_stale_processes("process_name", "process_identifier")).to eql([124])
      end

      it 'should do nothing if no duplicate process' do
        allow(BrowserStack::OSUtils)
          .to receive(:execute)
          .with("ps -ef | grep 'process_name' | grep 'process_identifier' | grep -v grep | awk '{print $2,$3}' ")
          .and_return("123 2\n 124 3")
        expect(BrowserStack::OSUtils).not_to receive(:kill_pid).with(:any)
        expect(BrowserStack::OSUtils.kill_duplicate_stale_processes("process_name", "process_identifier")).to eql([])
      end
    end
  end

  describe '.execute' do
    context 'timeout given' do
      let(:gtimeout) { BrowserStack::OSUtils::TIMEOUT_CMD }

      it 'wraps execution in a system gtimeout' do
        expected_timeout = 60
        expect(BrowserStack::OSUtils).to receive(:`).with("#{gtimeout} -k5 60 echo success") { `echo success` }
        result = BrowserStack::OSUtils.execute("echo success", timeout: expected_timeout)
        expect(result).to eq("success\n")
      end

      it 'raises OSUtilsError if command times out' do
        expected_timeout = 60
        expect(BrowserStack::OSUtils).to receive(:`).with("#{gtimeout} -k5 60 echo success") { `exit 124` }
        expect { BrowserStack::OSUtils.execute("echo success", timeout: expected_timeout) }.to raise_error(OSUtilsError)
      end
    end

    context 'no timeout given' do
      it 'executes command without a timeout' do
        expect(BrowserStack::OSUtils).to receive(:`).with("echo success").and_call_original
        result = BrowserStack::OSUtils.execute("echo success")
        expect(result).to eq("success\n")
      end
    end
  end

  describe '.safe_execute' do
    let(:tmp_file_name) { '/tmp/test_log' }
    context 'timeout given' do
      let(:gtimeout) { BrowserStack::OSUtils::TIMEOUT_CMD }

      it 'wraps execution in a system gtimeout' do
        expected_timeout = 60
        allow(BrowserStack::OSUtils).to receive(:get_tmp_random_file_name).and_return(tmp_file_name)
        allow(File).to receive(:read).with(tmp_file_name).and_return("success")
        allow(File).to receive(:exist?).with(tmp_file_name).and_return(false)
        allow(FileUtils).to receive(:rm).with(tmp_file_name).and_return(tmp_file_name)
        expect(BrowserStack::OSUtils).to receive(:system).with(gtimeout, "-k5", "60", "echo", "success", err: [tmp_file_name, "a"], out: [tmp_file_name, "a"]).and_call_original
        result = BrowserStack::OSUtils.safe_execute("echo", ["success"], timeout: expected_timeout)
        expect(result).to eq("success")
      end

      it 'raises OSUtilsError if command times out' do
        expected_timeout = 60
        allow(BrowserStack::OSUtils).to receive(:get_tmp_random_file_name).and_return(tmp_file_name)
        allow(File).to receive(:read).with(tmp_file_name).and_return(`exit 124`)
        allow(File).to receive(:exist?).with(tmp_file_name).and_return(false)
        allow(FileUtils).to receive(:rm).with(tmp_file_name).and_return(tmp_file_name)
        expect(BrowserStack::OSUtils).to receive(:system).with(gtimeout, "-k5", "60", "echo", "success", err: [tmp_file_name, "a"], out: [tmp_file_name, "a"]) { `exit 124` }
        expect { BrowserStack::OSUtils.safe_execute("echo", ["success"], timeout: expected_timeout) }.to raise_error(OSUtilsError)
      end
    end

    context 'no timeout given' do
      it 'executes command without a timeout' do
        allow(BrowserStack::OSUtils).to receive(:get_tmp_random_file_name).and_return(tmp_file_name)
        expect(BrowserStack::OSUtils).to receive(:system).with("echo", "success", err: [tmp_file_name, "a"], out: [tmp_file_name, "a"]).and_call_original
        result = BrowserStack::OSUtils.safe_execute("echo", ["success"])
        expect(result).to eq("success\n")
      end
    end
  end

  describe '.bridge100_exists?' do
    it 'should return true if ifconfig command successful' do
      expect(BrowserStack::OSUtils).to receive(:execute)
        .with('ifconfig bridge100', true).and_return(['', 0])
      expect(BrowserStack::OSUtils.bridge100_exists?).to be(true)
    end

    it 'should return false if ifconfig command unsuccessful' do
      expect(BrowserStack::OSUtils).to receive(:execute)
        .with('ifconfig bridge100', true).and_return(['', 1])
      expect(BrowserStack::OSUtils.bridge100_exists?).to be(false)
    end

    it 'should return false if OSUtilsError' do
      expect(BrowserStack::OSUtils).to receive(:execute)
        .with('ifconfig bridge100', true).and_raise(OSUtilsError)
      expect(BrowserStack::OSUtils.bridge100_exists?).to be(false)
    end
  end

  describe '.get_video_duration' do
    it 'should get duration of the video' do
      allow(BrowserStack::OSUtils)
        .to receive(:execute)
        .with("ffprobe -i /random/path/video.mp4 -show_format -v quiet | sed -n 's/duration=//p'")
        .and_return("195.250\n")
      expect(BrowserStack::OSUtils.get_video_duration("/random/path/video.mp4")).to eql("195.250")
    end
  end

  describe 'grep_process_details' do
    it 'should grep with all given options' do
      process = "iproxy"
      driver_port = "18281"
      app_port = "12345"
      grep_cmd = "ps aux | grep #{process} | grep #{driver_port} | grep #{app_port} | grep -v grep"

      expect(BrowserStack::OSUtils).to receive(:execute).with(grep_cmd)

      BrowserStack::OSUtils.grep_process_details(process, driver_port, app_port)
    end

    it 'should raise error if no params are passed' do
      expect do
        BrowserStack::OSUtils.grep_process_details
      end.to raise_error
    end
  end

  describe "grep_process_pid" do
    it "calls grep_process_details with given param and returns pid" do
      process = "iproxy"
      driver_port = "18281"
      app_port = "12345"

      expect(BrowserStack::OSUtils).to receive(:grep_process_details).with(process, driver_port, app_port).and_return("user 123 0.0 iproxy")

      result = BrowserStack::OSUtils.grep_process_pid(process, driver_port, app_port)

      expect(result).to eq(123)
    end

    it "returns nil if process is not running" do
      process = "iproxy"
      driver_port = "18281"
      app_port = "12345"

      expect(BrowserStack::OSUtils).to receive(:grep_process_details).with(process, driver_port, app_port).and_return("")

      result = BrowserStack::OSUtils.grep_process_pid(process, driver_port, app_port)

      expect(result).to eq(nil)
    end
  end

  describe ".m1_machine?" do
    it 'should return true if architecture is arm64' do
      allow(BrowserStack::OSUtils)
        .to receive(:execute)
        .with("uname -m")
        .and_return("arm64\n")
      expect(BrowserStack::OSUtils.m1_machine?).to eq(true)
    end

    it 'should return false if architecture is not arm64' do
      allow(BrowserStack::OSUtils)
        .to receive(:execute)
        .with("uname -m")
        .and_return("x86_64\n")
      expect(BrowserStack::OSUtils.m1_machine?).to eq(false)
    end
  end

  describe ".macos_version" do
    it 'should return an string of the Mac OS version' do
      allow(BrowserStack::OSUtils)
        .to receive(:execute)
        .with("/usr/bin/sw_vers | grep ProductVersion")
        .and_return("ProductVersion:	11.5.2")
      expect(BrowserStack::OSUtils.macos_version).to eq('11.5.2')
    end
  end

  describe '.get_cert_identities' do
    let(:identities_output) do
      %[

Policy: X.509 Basic
  Matching identities
  1) 566A83690796FDA76AB6854B33730D2171F65676 "iPhone Developer: Ankur Goel (L2SYV57X8M)" (CSSMERR_TP_CERT_EXPIRED)
  2) 74D206F62042E508487E76ED853EF547CDA025F9 "iPhone Developer: Vishal Chauhan (44K4QDFNBN)" (CSSMERR_TP_CERT_EXPIRED)
  3) D506754731F58F5994933045FB0039F9C5222C47 "iPhone Developer: Siddharth Lamba (9ZS959N5CN)" (CSSMERR_TP_CERT_EXPIRED)
  4) DB7C60F93D030A703E499C196788A3F46DB3D103 "iPhone Developer: Heena Bawa (JR982XV2B8)"
  5) EFF864A6997FDFCD977235CC5931A0982994553E "iPhone Developer: Yohan Pereira (QWH2VBNBKQ)"
  6) 57D936AB95F4FD4D5C2F470875EDB6B78D48AACF "iPhone Developer: Kalpesh Doshi (P8ZG4TLUQC)"
  7) D04A21381DE38C1AD8B4721E84285AAC17F647B2 "iPhone Developer: Eric Fetterolf (FU9PXW6J5Z)"
  8) 56BC55ADA8FBA48C25EE86F2A44833EE884ADB9C "iPhone Developer: David Burns (ZUVCRM75LP)"
  9) C15E52EB60895B5A3271034C3A9F469578581CC7 "iPhone Developer: Kunal Sheth (NLA8F3NPJ8)"
 10) 0B7375DDF3EFF6188D2BD8FC65DA30BBD6433E6E "iPhone Developer: Dhimil Gosalia (9J53R54FR7)"
    10 identities found

  Valid identities only
  1) DB7C60F93D030A703E499C196788A3F46DB3D103 "iPhone Developer: Heena Bawa (JR982XV2B8)"
  2) EFF864A6997FDFCD977235CC5931A0982994553E "iPhone Developer: Yohan Pereira (QWH2VBNBKQ)"
  3) 57D936AB95F4FD4D5C2F470875EDB6B78D48AACF "iPhone Developer: Kalpesh Doshi (P8ZG4TLUQC)"
    3 valid identities found
      ]
    end

    it 'returns array of tuples containing cert hash and cert id' do
      expect(BrowserStack::OSUtils).to receive(:retrieve_certificates).with('app', valid_only: false).and_return(identities_output)
      expected_result = [
        ['566A83690796FDA76AB6854B33730D2171F65676', 'iPhone Developer: Ankur Goel (L2SYV57X8M)', true],
        ['74D206F62042E508487E76ED853EF547CDA025F9', 'iPhone Developer: Vishal Chauhan (44K4QDFNBN)', true],
        ['D506754731F58F5994933045FB0039F9C5222C47', 'iPhone Developer: Siddharth Lamba (9ZS959N5CN)', true],
        ['DB7C60F93D030A703E499C196788A3F46DB3D103', 'iPhone Developer: Heena Bawa (JR982XV2B8)', false],
        ['EFF864A6997FDFCD977235CC5931A0982994553E', 'iPhone Developer: Yohan Pereira (QWH2VBNBKQ)', false],
        ['57D936AB95F4FD4D5C2F470875EDB6B78D48AACF', 'iPhone Developer: Kalpesh Doshi (P8ZG4TLUQC)', false],
        ['D04A21381DE38C1AD8B4721E84285AAC17F647B2', 'iPhone Developer: Eric Fetterolf (FU9PXW6J5Z)', false],
        ['56BC55ADA8FBA48C25EE86F2A44833EE884ADB9C', 'iPhone Developer: David Burns (ZUVCRM75LP)', false],
        ['C15E52EB60895B5A3271034C3A9F469578581CC7', 'iPhone Developer: Kunal Sheth (NLA8F3NPJ8)', false],
        ['0B7375DDF3EFF6188D2BD8FC65DA30BBD6433E6E', 'iPhone Developer: Dhimil Gosalia (9J53R54FR7)', false],
        ['DB7C60F93D030A703E499C196788A3F46DB3D103', 'iPhone Developer: Heena Bawa (JR982XV2B8)', false],
        ['EFF864A6997FDFCD977235CC5931A0982994553E', 'iPhone Developer: Yohan Pereira (QWH2VBNBKQ)', false],
        ['57D936AB95F4FD4D5C2F470875EDB6B78D48AACF', 'iPhone Developer: Kalpesh Doshi (P8ZG4TLUQC)', false]
      ]
      result = BrowserStack::OSUtils.get_cert_identities('app')
      expect(result).to eq(expected_result)
    end
  end

  context '#codesigning_identity_exists?' do
    let(:pp_uuid) { 'abc-uuid' }
    let(:pp_file) { "spec/tmp/#{pp_uuid}.mobileprovision" }
    before(:each) { File.write(pp_file, "deviceA") }
    after(:each) { FileUtils.rm_f(pp_file) }

    let(:certificates) do
      <<-CERT
          1) 0280BB1AD2903E17B51658B4C8C5A9C43CB1F2FC "iPhone Developer: Nick Hyland (9ZS959N5BN)"
          2) 8ED9FBC808714ADB200BB7F44B653F10AA1917A7 "iPhone Developer: Joe Bloggs (YLT8F35493)"
          3) EC92FF16E23BF4D81B938E906FFF1CFED8582939 "iPhone Developer: Akuna Matta (8RJ5T63PYC)"
            3 valid identities found
      CERT
    end

    it 'returns true when identity is listed in cert' do
      expect(BrowserStack::OSUtils).to receive(:retrieve_certificates).and_return(certificates)
      identity = "iPhone Developer: Nick Hyland (9ZS959N5BN)"
      expect(BrowserStack::OSUtils.codesigning_identity_exists?(identity)).to be true
    end

    it 'returns false when identity is not listed in cert' do
      expect(BrowserStack::OSUtils).to receive(:retrieve_certificates).and_return(certificates)
      identity = "iPhone Developer: Dick Whitman (9ZS959N5BN)"
      expect(BrowserStack::OSUtils.codesigning_identity_exists?(identity)).to be false
    end
  end

  context '#certificate_sha1_in_keychain?' do
    let(:pp_uuid) { 'abc-uuid' }
    let(:pp_file) { "spec/tmp/#{pp_uuid}.mobileprovision" }
    before(:each) { File.write(pp_file, "deviceA") }
    after(:each) { FileUtils.rm_f(pp_file) }

    let(:certificates) do
      <<-CERT
          1) 0280BB1AD2903E17B51658B4C8C5A9C43CB1F2FC "iPhone Developer: Nick Hyland (9ZS959N5BN)"
          2) 8ED9FBC808714ADB200BB7F44B653F10AA1917A7 "iPhone Developer: Joe Bloggs (YLT8F35493)"
          3) EC92FF16E23BF4D81B938E906FFF1CFED8582939 "iPhone Developer: Akuna Matta (8RJ5T63PYC)"
            3 valid identities found
      CERT
    end

    it 'returns true when cert sha1 is listed' do
      expect(BrowserStack::OSUtils).to receive(:retrieve_certificates).and_return(certificates)
      sha1 = '0280BB1AD2903E17B51658B4C8C5A9C43CB1F2FC'
      expect(BrowserStack::OSUtils.certificate_sha1_in_keychain?(sha1)).to be(true)
    end

    it 'returns false when cert sha1 is not listed' do
      expect(BrowserStack::OSUtils).to receive(:retrieve_certificates).and_return(certificates)
      sha1 = '0000000000000000000000000000000000000000'
      expect(BrowserStack::OSUtils.certificate_sha1_in_keychain?(sha1)).to be(false)
    end
  end

  describe '.get_cert_expiry' do
    let(:cert) do
      key = OpenSSL::PKey::RSA.new(1024)
      public_key = key.public_key
      subject = "/C=BE/O=Test/OU=Test/CN=Test"
      cert = OpenSSL::X509::Certificate.new
      cert.subject = cert.issuer = OpenSSL::X509::Name.parse(subject)
      cert.not_before = Time.mktime(2017, 6, 9, 14, 44)
      cert.not_after = Time.mktime(2018, 6, 9, 14, 44)
      cert.public_key = public_key
      cert.serial = 0x0
      cert.version = 2
      cert.sign key, OpenSSL::Digest.new('SHA1')
      cert.to_pem
    end

    it 'parses security cmd output and returns as valid ruby Time' do
      expect(BrowserStack::Configuration).to receive(:[]).with('user').and_return('app')
      cmd = "sudo su - app -c \"security find-certificate -c 'iPhone Developer: Heena Bawa (JR982XV2B8)' -p\""
      expect(BrowserStack::OSUtils).to receive(:execute).with(cmd, true).and_return(cert)
      result = BrowserStack::OSUtils.get_cert_expiry('iPhone Developer: Heena Bawa (JR982XV2B8)')
      expect(result.year).to eq(2018)
      expect(result.month).to eq(6)
      expect(result.day).to eq(9)
      expect(result.hour).to eq(14)
      expect(result.min).to eq(44)
    end
  end

  describe '.certificates_sha1' do
    let(:cert_name) { 'fake_apple_cert' }
    let(:keychain_path) { '/some/fake/path' }
    let(:sha1_1) { '06EC06599F4ED0027CC58956B4D3AC1255114F35' }
    let(:sha1_2) { 'FF6797793A3CD798DC5B2ABEF56F73EDC9F83A64' }
    let(:sha_cert1) { "SHA-1 hash: #{sha1_1}" }
    let(:sha_cert2) { "SHA-1 hash: #{sha1_2}" }

    it 'should call execute with required params' do
      expect(BrowserStack::OSUtils).to receive(:execute)
        .with("security find-certificate -a -Z -c \"#{cert_name}\" #{keychain_path} | grep ^SHA-1")
        .and_return([sha_cert1, sha_cert2].join("\n"))

      expect(BrowserStack::OSUtils.certificates_sha1(cert_name, keychain_path)).to eq([sha1_1, sha1_2])
    end

    it 'should return an empty array if no sha found' do
      expect(BrowserStack::OSUtils).to receive(:execute)
        .with("security find-certificate -a -Z -c \"#{cert_name}\" #{keychain_path} | grep ^SHA-1")
        .and_return("")

      expect(BrowserStack::OSUtils.certificates_sha1(cert_name, keychain_path)).to eq([])
    end
  end

  describe '.add_trusted_cert' do
    let(:cert_file) { '/tmp/AppleWWDRCAG3.cer' }
    let(:keychain_path) { '/some/fake/path' }

    it 'should call execute with required params' do
      expect(BrowserStack::OSUtils).to receive(:execute)
        .with("sudo security add-trusted-cert -d -r trustRoot -k #{keychain_path} #{cert_file}", true)
        .and_return(['', 0])

      expect(BrowserStack::OSUtils.add_trusted_cert(cert_file, keychain_path)).to eq('')
    end

    it 'should raise an exception if status code is non-zero' do
      expect(BrowserStack::OSUtils).to receive(:execute)
        .with("sudo security add-trusted-cert -d -r trustRoot -k #{keychain_path} #{cert_file}", true)
        .and_return(['', 1])

      expect { BrowserStack::OSUtils.add_trusted_cert(cert_file, keychain_path) }
        .to raise_error(StandardError, "Unable to add trusted cert")
    end
  end

  describe '.file_compare' do
    let(:file1) { '/tmp/file1' }
    let(:file2) { '/tmp/file2' }

    after { FileUtils.rm_rf(file1) }
    after { FileUtils.rm_rf(file2) }

    it 'returns false if both files are missing' do
      expect(BrowserStack::OSUtils.file_compare(file1, file2)).to be(false)
    end

    it 'returns false if first file is missing' do
      FileUtils.touch(file1)
      expect(BrowserStack::OSUtils.file_compare(file1, file2)).to be(false)
    end

    it 'returns false if second file is missing' do
      FileUtils.touch(file2)
      expect(BrowserStack::OSUtils.file_compare(file1, file2)).to be(false)
    end

    it 'returns false if contents differ' do
      File.write(file1, "hello world")
      File.write(file2, "goodbye world")
      expect(BrowserStack::OSUtils.file_compare(file1, file2)).to be(false)
    end

    it 'returns true if contents differ only by whitespace or newlines' do
      File.write(file1, "  \t\t hello \n world   ")
      File.write(file2, "hello world")
      expect(BrowserStack::OSUtils.file_compare(file1, file2)).to be(true)
    end

    it 'returns true if contents are the same' do
      File.write(file1, "hello world")
      File.write(file2, "hello world")
      expect(BrowserStack::OSUtils.file_compare(file1, file2)).to be(true)
    end
  end

  describe 'get_cpu_consumed' do
    it 'should get total consumed cpu' do
      allow(BrowserStack::OSUtils)
        .to receive(:execute)
        .with("top -l  2 | grep -E \"^CPU\" | tail -1 | awk '{ print $3 + $5 }'")
        .and_return("20.2\n")
      expect(BrowserStack::OSUtils.get_cpu_consumed).to eq(20.2)
    end
  end

  describe 'get_free_memory' do
    it 'should get total free memory' do
      allow(BrowserStack::OSUtils)
        .to receive(:execute)
        .with("memory_pressure | tail -1 | awk '{split($0,a,\":\"); print a[2]}' | sed 's/%//'")
        .and_return("80\n")
      expect(BrowserStack::OSUtils.get_free_memory).to eq(80)
    end
  end

  describe 'get_system_load' do
    it 'should get system load' do
      allow(BrowserStack::OSUtils)
        .to receive(:execute)
        .with("uptime | awk '{split($0,a,\"averages:\"); print a[2]}'")
        .and_return(" 2.35 2.77 2.50\n")
      expect(BrowserStack::OSUtils.get_system_load).to eq("2.35 2.77 2.50")
    end
  end

  describe 'get_system_resources' do
    it 'should get various system resources hash' do
      allow(BrowserStack::OSUtils).to receive(:get_cpu_consumed).and_return(20.2)
      allow(BrowserStack::OSUtils).to receive(:get_free_memory).and_return(80)
      allow(BrowserStack::OSUtils).to receive(:get_system_load).and_return("2.35 2.77 2.50")
      expect(BrowserStack::OSUtils.get_system_resources).to eq({ "cpu_used" => 20.2, "free_memory" => 80, "load" => "2.35 2.77 2.50" })
    end
  end

  describe 'ensure_path_exists' do
    it 'Should create a directory' do
      path = '/usr/local/.browserstack/config/installed_TestFlight/'
      FileUtils.rm_rf path
      expect(Dir.exists?(path)).to eq(false)
      expect(BrowserStack::OSUtils.ensure_path_exists(path)).to eq(path)
      expect(Dir.exists?(path)).to eq(true)
    end
  end

  describe 'process_elapsed_time' do
    it 'Should return correct time elapsed in seconds when days and time(hh:mm:ss) is present' do
      allow(BrowserStack::OSUtils).to receive(:execute).and_return('301-05:40:15')
      expect(BrowserStack::OSUtils.process_elapsed_time('clean', '123')).to eq(26026815)
    end

    it 'Should return correct time elapsed in seconds when time(hh:mm:ss) is present' do
      allow(BrowserStack::OSUtils).to receive(:execute).and_return('10:34:55')
      expect(BrowserStack::OSUtils.process_elapsed_time('clean', '123')).to eq(38095)
    end

    it 'Should return correct time elapsed in seconds when time(mm:ss) is present' do
      allow(BrowserStack::OSUtils).to receive(:execute).and_return('10:13')
      expect(BrowserStack::OSUtils.process_elapsed_time('clean', '123')).to eq(613)
    end

    it 'Should return correct time elapsed in seconds when time(ss) is present' do
      allow(BrowserStack::OSUtils).to receive(:execute).and_return('13')
      expect(BrowserStack::OSUtils.process_elapsed_time('clean', '123')).to eq(13)
    end

    it 'Should return correct time elapsed in seconds when time(mm:ss) is present with \n' do
      allow(BrowserStack::OSUtils).to receive(:execute).and_return('08:13\n')
      expect(BrowserStack::OSUtils.process_elapsed_time('clean', '123')).to eq(493)
    end
  end
end
