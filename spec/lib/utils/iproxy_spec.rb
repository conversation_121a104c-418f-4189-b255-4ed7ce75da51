require_relative '../../../lib/utils/iproxy'

describe Iproxy do
  describe '.version' do
    context 'when --version returns version' do
      it 'returns version number for 2.0.3' do
        allow(BrowserStack::OSUtils).to receive(:execute).with("#{IPROXY} --version", true).and_return(['iproxy 2.0.3', 0])
        expect(Iproxy.version).to eq('2.0.3')
      end
    end

    context 'when --version returns usage for 1.12' do
      it 'returns full output' do
        allow(BrowserStack::OSUtils).to receive(:execute).with("#{IPROXY} --version", true).and_return(['usage: iproxy LOCAL_TCP_PORT DEVICE_TCP_PORT [UDID]', 0])
        expect(Iproxy.version).to eq('usage: iproxy LOCAL_TCP_PORT DEVICE_TCP_PORT [UDID]')
      end
    end

    context 'when exit code is non zero' do
      it 'raises error' do
        allow(BrowserStack::OSUtils).to receive(:execute).with("#{IPROXY} --version", true).and_return(['-bash: iproxy: command not found', 2])
        expect { Iproxy.version }.to raise_error(/iproxy command failed/)
      end
    end
  end

  describe '.start' do
    let(:device_id) { "123456" }
    let(:local_port) { "8404" }
    let(:device_port) { "8001" }
    let(:process_options) { { process_name: "iproxy for device #{device_id} #{local_port}" } }

    context 'when using iproxy version 2.0.3' do
      it 'uses 2.0.3 command syntax' do
        iproxy_command = "#{IPROXY} #{local_port}:#{device_port} -u #{device_id}"
        expect(Iproxy).to receive(:version).and_return('2.0.3')
        expect(Utils).to receive(:fork_process).with(iproxy_command, process_options)

        Iproxy.start(device_id, local_port, device_port)
      end
    end

    context 'when using iproxy version 2.0.2' do
      it 'uses 2.0.2 command syntax' do
        iproxy_command = "#{IPROXY} #{local_port} #{device_port} -u #{device_id}"
        expect(Iproxy).to receive(:version).and_return('2.0.2')
        expect(Utils).to receive(:fork_process).with(iproxy_command, process_options)

        Iproxy.start(device_id, local_port, device_port)
      end
    end

    context 'when not version 2.0.3 or 2.0.2' do
      it 'uses old command syntax' do
        iproxy_command = "#{IPROXY} #{local_port} #{device_port} #{device_id}"
        expect(Iproxy).to receive(:version).and_return('usage: iproxy LOCAL_TCP_PORT DEVICE_TCP_PORT [UDID]')
        expect(Utils).to receive(:fork_process).with(iproxy_command, process_options)

        Iproxy.start(device_id, local_port, device_port)
      end
    end
  end

  describe "running?" do
    it 'should return true if running' do
      allow(BrowserStack::OSUtils).to receive(:grep_process_details).with('iproxy', '9580', 'device_id').and_return('found')
      result = Iproxy.running?('device_id', '9580')
      expect(result).to eq(true)
    end

    it 'should return false if running' do
      expect(BrowserStack::OSUtils).to receive(:grep_process_details).with('iproxy', '9580', 'device_id').and_return("")
      result = Iproxy.running?('device_id', '9580')
      expect(result).to eq(false)
    end
  end
end
