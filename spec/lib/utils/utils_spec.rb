require 'timecop'
require_relative '../../spec_helper'
require_relative '../../../lib/utils/utils'
require_relative '../../../lib/utils/ios_mdm_service_client'

class MockConfig
  def self.all
    { "eds" => { "hostname" => "config_host", "port_udp" => "config_port" }, "eds_api_key" => "config_key", "network_logs_send_to_eds" => true }
  end
end

describe Utils do
  context "send_to_eds" do
    before(:each) do
      allow(BrowserStack::Configuration).to receive(:new).and_return MockConfig
    end

    it 'should use params eds config if params has those keys in them' do
      expect(Bsdwh).to receive(:send).with(instance_of(String), "host", "port", "key").and_return(nil)
      Utils.send_to_eds({ edsHost: "host", edsPort: "port", edsKey: "key" }, "etype")
    end

    it 'should use eds config if params does not have those eds in them' do
      expect(Bsdwh).to receive(:send).with(instance_of(String), "config_host", "config_port", "config_key").and_return(nil)
      Utils.send_to_eds({}, "etype")
    end

    it 'should try sending event to eds with any params if params are recieved' do
      expect(Bsdwh).to receive(:send).with(any_args).and_return(nil)
      Utils.send_to_eds({ edsHost: "host", edsPort: "port", edsKey: "key" }, "etype")
    end
  end

  context "valid_live_url?" do
    # All these examples are either from
    # Customer issues or
    # logs
    valid_urls = [
      "www.facebook.com",
      "http://www.facebook.com",
      "https://www.facebook.com",
      "facebook.com",
      "http://facebook.com",
      "https://facebook.com",
      %{https://www.ashleymadison.com/landers/lonely_wives#?ac="></iframe><script>alert(document.domain)</script>},
      "www.demo-2dehands.qa-mp.so/q/voiture/#Language:fr-BE,nl-BE|searchInTitleAndDescription:false",
      "pa.outright.co.il/product/ברי-הברווז/",
      "beta.standardlifeadvice.co.uk/explore.html?ref=EWEWLE44343ddDsdsd#explore/spendingsummary|explore/essentialspendingnew.edit=true",
      "tmfgroup.local/dev/fluidlayout/gradient test/",
      "mutaresoftware.visualstudio.com/Git/_sprints/taskboard/Git Team/Git/2019/Q2/Sprint 106",
      %(cc-cloud.skoda-auto.com/esp/esp/es-es/5e3/68119#!cc-instance=ESP&cc-salesprogram=ESP&cc-culture=es-es&cc-model=5E3&cc-carline=68119&cc-equipment=&cc-motor=197364&cc-color=F6F6&cc-interior=HA&cc-packages=GW37W37,MLAC5K5,MMFA9S6&cc-view=step7&cc-configurationId=149428491&cc-viewstate={"packageItem":true,"lastConfigurationHash":"3_N5_HA_F6F6_GW37W37,MLAC5K5,MMFA9S6","accessoriesItems":[],"selectedAccessories":[]}),
      "sc9u2.sc/?sc_mode=preview&sc_itemid={F0618A82-9D52-403A-84F3-71A6195AD277}&sc_lang=en&sc_version=1&sc_site=UTC&sc_device={FE5D7FDF-89C0-4D99-9AA3-B5FBD009C9F3}&sc_ee_fb=true",
      "localhost:8080/index.html",
      "localhost:8080",
      "localhost/index.html",
      "***********/index.html",
      "127.0.0.1/index.html",
      "localhost",
      "***********",
      "127.0.0.1:4000",
      "**********",
      "************",
      "http://**********:9081/prweb/PRServlet/aSuQkZnLDRlmGUrlvcJdDwdVEDM8G8f9E4CYmX-OC8-Bzuw_nQePzg[[*/!b8bd5e629df93f47eaf5034b384eaaec!STANDARD?pzPostData=-583701679"
    ]

    invalid_urls = [
      "file:///C:/Users/<USER>/AppData/Local/Packages/Microsoft.MicrosoftEdge_8wekyb3d8bbwe/TempState/Downloads/form-23-02-07-2019 (9).pdf",
      "http://Berlin (Berlin) - DEU",
      "width: 100%;",
      "Search for a product...",
      ".ui-slider-horizontal .ui-slider-handle {top: -0.6em;border-radius: 100%;}.ui-slider-horizontal {height: 1px;background: #aaaaaa !important;}",
      %(Browser not supported
        C:\\Users\\<USER>\\~bs_stuff\\code\\server.rb:1850:in `get_url_from_history'
          C:\\Users\\<USER>\\~bs_stuff\\code\\server.rb:581:in `getCurrentBrowserUrl'
            C:/Ruby187/lib/ruby/1.8/timeout.rb:67:in `timeout')
    ]

    # These URLs will result in thread blocking
    timebombs = [
      "127.0.0.1/[[*/!@aaaaaaaaaaaaaaaaaaaaaaaaa!j?",
      "http://**********:9081/prweb/PRServlet/aSuQkZnLDRlmGUrlvcJdDwdVEDM8G8f9E4CYmX-OC8-Bzuw_nQePzg[[*/!@b8bd5e629df93f47eaf5034b384eaaec!STANDARD?pzPostData=-583701679",
      "127.0.0.1:4000/as/asd/asd[[*/!@aaaaaaaaaaaaaaaaaaaaaaaaa!j?pzPostData=-583701679"
    ]

    it "Allows good URLs" do
      valid_urls.each do |url|
        expect(Utils.valid_live_url?(url)).to eql(true)
      end
    end

    it "Does not allow bad URLs" do
      invalid_urls.each do |url|
        expect(Utils.valid_live_url?(url)).to eql(false)
      end
    end

    it "Does not allow URLs which are time consuming" do
      allow(Timeout).to receive(:timeout).and_raise(Timeout::Error)
      # Any url(valid/non_valid) should ideally work here as we are forcing exception
      expect(Utils.valid_live_url?(valid_urls.sample)).to eql(false)
    end

    it "Times outs on URLs which are time consuming" do
      expect { Timeout.timeout }.to raise_error
      expect(Utils.valid_live_url?(timebombs.sample)).to eql(false)
    end
  end

  context 'generate_app_live_url' do
    it 'should return prod app-live url if machine is a prod one' do
      allow(BrowserStack::Configuration).to receive_message_chain(:new, :all).and_return({ 'static_conf' => { 'env' => 'prod', 'rails_endpoint' => "https://terminals.browserstack.com" } })
      expect(Utils.generate_app_live_url).to eql("https://app-live.browserstack.com")
    end

    it 'should return staging app-live url if machine is not a prod one' do
      allow(BrowserStack::Configuration).to receive_message_chain(:new, :all).and_return({ 'static_conf' => { 'env' => 'staging', 'rails_endpoint' => "https://k8s-devdf.bsstag.com/" } })
      expect(Utils.generate_app_live_url).to eql("https://app-live-k8s-devdf.bsstag.com")
    end

    it 'should return staging app-live url if machine is not a prod one' do
      allow(BrowserStack::Configuration).to receive_message_chain(:new, :all).and_return({ 'static_conf' => { 'env' => 'staging', 'rails_endpoint' => "http://k8s-devdf.bsstag.com" } })
      expect(Utils.generate_app_live_url).to eql("https://app-live-k8s-devdf.bsstag.com")
    end

    it 'should return staging app-live url if machine is not a prod one' do
      allow(BrowserStack::Configuration).to receive_message_chain(:new, :all).and_return({ 'static_conf' => { 'env' => 'staging', 'rails_endpoint' => "http://callbacks-k8s-devdf.bsstag.com" } })
      expect(Utils.generate_app_live_url).to eql("https://app-live-k8s-devdf.bsstag.com")
    end

    it 'should return staging app-live url if machine is not a prod one' do
      allow(BrowserStack::Configuration).to receive_message_chain(:new, :all).and_return({ 'static_conf' => { 'env' => 'staging', 'rails_endpoint' => "https://callbacks-k8s-devdf.bsstag.com/" } })
      expect(Utils.generate_app_live_url).to eql("https://app-live-k8s-devdf.bsstag.com")
    end
  end

  context 'push_syslog_parse_results' do
    it 'should push zombie kind ios-syslog-parse-results with relevant results ' do
      instrumentation_data = {
        "DUMMY_DATA" => 3,
        "DUMMY_DATA2" => 1
      }
      fileMock = double('file')
      allow(File).to receive(:exists?).and_return true
      allow(File).to receive(:read).and_return "DUMMY_DATA\nDUMMY_DATA\nDUMMY_DATA\nDUMMY_DATA2"
      expect(BrowserStack::Zombie).to receive(:push_logs).with("ios-syslog-parse-results", "app_automate", { "device" => 'device_id', "session_id" => 'session_id', "data" => instrumentation_data })
      expect(FileUtils).to receive(:rm_f).with("dummy_results.log")

      Utils.push_syslog_parse_results("dummy_results.log", "session_id", 'device_id')
    end

    it 'should send to eds when testmanagerd process is killed by jetsam' do
      syslog_content = "Some prefix Process testmanagerd [1234] killed by jetsam reason highwater\nOther log lines..."

      allow(File).to receive(:exists?).and_return true
      allow(File).to receive(:read).and_return syslog_content
      allow(Time).to receive(:now).and_return(Time.at(1234567890))

      expected_eds_data = {
        secondary_diagnostic_reason: "Process killed by jetsam: highwater",
        hashed_id: "session_id",
        timestamp: 1234567890
      }

      expect(Utils).to receive(:send_to_eds).with(
        expected_eds_data,
        EdsConstants::APP_AUTOMATE_TEST_SESSIONS,
        true
      )
      expect(FileUtils).to receive(:rm_f).with("dummy_results.log")

      Utils.push_syslog_parse_results("dummy_results.log", "session_id", nil)
    end

    it 'should send to eds when WebDriverAgentRunner-Runner process is killed by jetsam' do
      syslog_content = "Some prefix Process WebDriverAgentRunner-Runner [5678] killed by jetsam reason per-process-limit\nOther log lines..."

      allow(File).to receive(:exists?).and_return true
      allow(File).to receive(:read).and_return syslog_content
      allow(Time).to receive(:now).and_return(Time.at(1234567890))

      expected_eds_data = {
        secondary_diagnostic_reason: "Process killed by jetsam: per-process-limit",
        hashed_id: "session_id",
        timestamp: 1234567890
      }

      expect(Utils).to receive(:send_to_eds).with(
        expected_eds_data,
        EdsConstants::APP_AUTOMATE_TEST_SESSIONS,
        true
      )
      expect(FileUtils).to receive(:rm_f).with("dummy_results.log")

      Utils.push_syslog_parse_results("dummy_results.log", "session_id", nil)
    end

    it 'should send to eds when app_ide_disconnected is detected' do
      syslog_content = "Some prefix app_ide_disconnected\nOther log lines..."

      allow(File).to receive(:exists?).and_return true
      allow(File).to receive(:read).and_return syslog_content
      allow(Time).to receive(:now).and_return(Time.at(1234567890))

      expected_eds_data = {
        secondary_diagnostic_reason: "Process killed by jetsam: disconnected",
        hashed_id: "session_id",
        timestamp: 1234567890
      }

      expect(Utils).to receive(:send_to_eds).with(
        expected_eds_data,
        EdsConstants::APP_AUTOMATE_TEST_SESSIONS,
        true
      )
      expect(FileUtils).to receive(:rm_f).with("dummy_results.log")

      Utils.push_syslog_parse_results("dummy_results.log", "session_id", nil)
    end
  end

  context "upload_file_to_s3" do
    let!(:session_id) { SecureRandom.hex(20) }
    let!(:s3_url) { "https://s3-eu-west-1.amazonaws.com/bs-video-logs-euw/#{session_id}/#{session_id}-har-logs.txt" }
    let!(:file_path) { "/tmp/#{session_id}/video-#{session_id}-har-logs.txt" }
    let!(:aws_response) { { etag: :gate } }

    it 'should upload a file to s3' do
      expect(File).to receive(:open).with(any_args).at_least(:once).and_return nil
      allow_any_instance_of(Aws::S3::Client).to receive(:put_object).with(any_args).and_return aws_response
      expect(Utils).to receive(:send_feature_usage_to_eds).with(any_args)
      results = Utils.upload_file_to_s3('aws_key', 'aws_secret', 'text/html', file_path, 'acl', s3_url, 'session_id', 'genre', 'region', 0)
      expect(results).to be_truthy
    end

    it 'should upload a file to s3 with metadata' do
      expect(File).to receive(:open).with(any_args).at_least(:once).and_return nil
      allow_any_instance_of(Aws::S3::Client).to receive(:put_object).with(any_args).and_return aws_response
      expect(Utils).to receive(:send_feature_usage_to_eds).with(any_args)
      results = Utils.upload_file_to_s3('aws_key', 'aws_secret', 'text/html', file_path, 'acl', s3_url, 'session_id', 'genre', 'region', 0, { num_crash_reports: "2" }, s3_metadata: { num_crash_reports: "2" })
      expect(results).to be_truthy
    end

    it "should send stop_req_timestamp to send_feature_usage_to_eds" do
      expect(File).to receive(:open).with(any_args).at_least(:once).and_return nil
      allow_any_instance_of(Aws::S3::Client).to receive(:put_object).with(any_args).and_return aws_response
      expect(Utils).to receive(:send_feature_usage_to_eds).with(anything, anything, anything, anything, anything, anything, hash_including(video_upload_delay: 3), anything, anything, anything, anything, anything, anything)
      allow(Time).to receive(:now).and_return(13)
      results = Utils.upload_file_to_s3('aws_key', 'aws_secret', 'text/html', file_path, 'acl', s3_url, 'session_id', 'genre', 'region', 0, { 'stop_req_timestamp' => "10" }, {}, 'STANDARD_IA')
    end
  end

  context "#is_wda_kill_flag_enabled?" do
    let(:session_id) { "1234" }

    it "should return true when custom params contains kill flag" do
      params = {
        "app_automate_custom_params" => {
          "xcuitest_wda_kill" => "true"
        },
        'automate_session_id' => session_id,
        'test_framework' => "xcuitest"
      }
      expect(Utils).to receive(:is_wda_kill_flag_enabled?).with(params).and_return true
      Utils.is_wda_kill_flag_enabled?(params)
    end
  end

  context "get_presigned_url" do
    let(:path) { "some random path" }
    let(:s3_config) { { "s3_access_keyId" => "aaa", "s3_secret_access_key" => "s3_secret_access_key", "region" => "us-east-2", "s3_bucket" => "s3_bucket" }  }
    let(:sample_s3_object) { 'sample_s3_object' }
    let(:sample_bucket) { 'sample_bucket' }
    let(:sample_object) { 'sample_object' }
    let(:sample_url) { 'sample_url.com' }

    before(:each) do
      allow(Aws::S3::Resource).to receive(:new).and_return(sample_s3_object)
      allow(sample_s3_object).to receive(:bucket).and_return(sample_bucket)
      allow(sample_bucket).to receive(:object).and_return(sample_object)
    end

    it('should generate presigned url of existing resource correctly') do
      expect(sample_object).to receive(:exists?).and_return(true)
      expect(sample_object).to receive(:presigned_url).and_return(sample_url)
      expect(Utils.get_presigned_url(path, s3_config, 1000)).to eq(sample_url)
    end

    it('should raise error if aws bucket object path does not exist') do
      expect(sample_object).to receive(:exists?).and_return(false)
      expect(sample_object).not_to receive(:presigned_url)
      expect { Utils.get_presigned_url(path, s3_config, 1000) }.to raise_error("Aws Bucket Object path does not exist")
    end
  end

  context "send_feature_usage_to_eds" do
    let!(:session_id) { SecureRandom.hex(20) }
    let!(:s3_url) { "https://s3-eu-west-1.amazonaws.com/bs-video-logs-euw/#{session_id}/#{session_id}-har-logs.txt" }
    let!(:file_path) { "/tmp/#{session_id}/video-#{session_id}-har-logs.txt" }

    it 'should feature report to eds for har logs' do
      allow(BrowserStack::Configuration).to receive(:new).and_return MockConfig
      expect(Bsdwh).to receive(:send).with(any_args).and_return(nil)
      Utils.send(:send_feature_usage_to_eds, file_path, s3_url, true, nil, session_id, 'automate', {})
    end
  end

  context '#with_lock' do
    after(:each) { FileUtils.rm_rf(['utils_spec.tmp', 'utils_spec.tmp.lock']) }

    it 'allows for concurrent processes to execute safely' do
      processes = (1..10).map do
        fork do
          Utils.with_lock('utils_spec.tmp') do
            sum = File.exist?('utils_spec.tmp') ? File.read('utils_spec.tmp').to_i : 0
            File.open('utils_spec.tmp', 'w+') { |f| f.write(sum + 1) }
          end
        end
      end
      processes.map { |pid| Process.waitpid(pid) }
      expect(File.read('utils_spec.tmp')).to eq('10')
    end
  end

  context '#send_general_feature_usage_data_to_eds' do
    it 'should return even if one of the required params is empty' do
      expect(Utils).to_not receive(:send_to_eds)
      Utils.send_general_feature_usage_data_to_eds("", "some_feature", false, "app_automate", "hello there")
    end

    it 'should call send_to_eds function in case of everything is correct and success is true' do
      expect(Utils).to receive(:send_to_eds)
      Utils.send_general_feature_usage_data_to_eds("some_session_id", "some_feature", true, "app_automate", "hello there")
    end

    it 'should call send_to_eds function in case of everything is correct and success is false' do
      expect(Utils).to receive(:send_to_eds)
      Utils.send_general_feature_usage_data_to_eds("some_session_id", "some_feature", false, "app_automate", "hello there")
    end

    it 'should send data to zombie in case of any failure while sending data for feature usage' do
      expect(Utils).to receive(:send_to_eds).and_raise("some_random_error")
      zombie_data = {
        "session_id" => "some_session_id",
        "data" => {
          "feature" => "some_feature",
          "status" => true
        }
      }
      expect(BrowserStack::Zombie).to receive(:push_logs).with("app-feature-usage-failed", "some_random_error", zombie_data)
      Utils.send_general_feature_usage_data_to_eds("some_session_id", "some_feature", true, "app_automate", "hello there")
    end
  end

  context '#is_md5_checksum_equal?' do
    it 'should return true if md5sum of both the files are same' do
      allow(Utils).to receive(:get_md5_checksum_for_file).with("file_path_1").and_return("abcd")
      allow(Utils).to receive(:get_md5_checksum_for_file).with("file_path_2").and_return("abcd")

      expect(Utils.is_md5_checksum_equal?("file_path_1", "file_path_2")).to eql(true)
    end

    it 'should return false if md5sum of both the files are different' do
      allow(Utils).to receive(:get_md5_checksum_for_file).with("file_path_1").and_return("abcd")
      allow(Utils).to receive(:get_md5_checksum_for_file).with("file_path_2").and_return("pqrs")

      expect(Utils.is_md5_checksum_equal?("file_path_1", "file_path_2")).to eql(false)
    end

    it 'should return false if one of the file does not exist' do
      allow(Utils).to receive(:get_md5_checksum_for_file).with("file_path_1").and_return("abcd")
      allow(Utils).to receive(:get_md5_checksum_for_file).with("file_path_2").and_raise("file not found")

      expect(Utils.is_md5_checksum_equal?("file_path_1", "file_path_2")).to eql(false)
    end
  end

  context '#get_md5_checksum_for_file' do
    it 'should return the md5sum of the file' do
      allow(File).to receive(:read).with("file_path").and_return("abcd")

      expect(Utils.get_md5_checksum_for_file("file_path")).to eql("e2fc714c4727ee9395f324cd2e7f331f")
    end

    it 'should raise exception if file does not exist' do
      allow(File).to receive(:read).with("file_path").and_raise("file not found")

      expect { Utils.get_md5_checksum_for_file("file_path") }.to raise_error("file not found")
    end
  end

  describe '#read_file_in_array' do
    let(:filename) { 'fake_file_name.txt' }
    let(:array) { [1, 2, 3] }
    it 'should read the file in an array' do
      expect(File).to receive_message_chain(:read, :strip, :split)
      Utils.read_file_in_array(filename)
    end

    it 'should write an array into a file' do
      expect(File).to receive(:open).with(filename, 'w')
      Utils.write_array_to_file!(filename, array)
    end
  end

  describe "substring_match?" do
    # m = modulename, c = classname, t = testname
    context 'when test of format [m/c/t] and filter_test of format [m/c/t]' do
      it 'should return false' do
        test = "m/c/t"
        filter_test = "m/c/t"
        module_name = "m"
        result = Utils.send(:substring_match?, test, filter_test, module_name)
        expect(result).to eq(false)
      end
    end

    context 'when test of format [m/c/t2] and filter_test of format [m/c/t]' do
      it 'should return true' do
        test = "m/c/t2"
        filter_test = "m/c/t"
        module_name = "m"
        result = Utils.send(:substring_match?, test, filter_test, module_name)
        expect(result).to eq(true)
      end
    end

    context 'when test of format [m/c/t] and filter_test of format [m]' do
      it 'should return false' do
        test = "m/c/t"
        filter_test = "m/c/t"
        module_name = "m"
        result = Utils.send(:substring_match?, test, filter_test, module_name)
        expect(result).to eq(false)
      end
    end

    context 'when test of format [m2/c/t] and filter_test of format [m]' do
      it 'should return true' do
        test = "m2/c/t"
        filter_test = "m/c/t"
        module_name = "m2"
        result = Utils.send(:substring_match?, test, filter_test, module_name)
        expect(result).to eq(true)
      end
    end

    context 'when test of format [m/c/t] and filter_test of format [m/c]' do
      it 'should return false' do
        test = "m/c/t"
        filter_test = "m/c"
        module_name = "m"
        result = Utils.send(:substring_match?, test, filter_test, module_name)
        expect(result).to eq(false)
      end
    end

    context 'when test of format [m/c/t] and filter_test of format [c]' do
      it 'should return true' do
        test = "m/c/t"
        filter_test = "c"
        module_name = "m"
        result = Utils.send(:substring_match?, test, filter_test, module_name)
        expect(result).to eq(true)
      end
    end

    context 'when test of format [m/c/t] and filter_test of format [c/t]' do
      it 'should return true' do
        test = "m/c/t"
        filter_test = "c/t"
        module_name = "m"
        result = Utils.send(:substring_match?, test, filter_test, module_name)
        expect(result).to eq(true)
      end
    end

    context 'when test of format [m/c/t2] and filter_test of format [c/t]' do
      it 'should return true' do
        test = "m/c/t2"
        filter_test = "c/t"
        module_name = "m"
        result = Utils.send(:substring_match?, test, filter_test, module_name)
        expect(result).to eq(true)
      end
    end
  end

  describe '#parse_plist_and_convert_to_json' do
    let(:response) { "{\"NSLocationAlwaysUsageDescriptionWithLHText\":\"Tapping “Allow” also turns on Location History for your Google account\"}" }
    it 'should execute command give json as response' do
      expect(BrowserStack::OSUtils).to receive(:safe_execute).with("plutil", ["-convert", "json", 'dummy_file_path', "-o", "-"]).and_return(response)

      result = Utils.parse_plist_and_convert_to_json("dummy_file_path")

      expect(result.class).to eql(Hash)
    end

    it 'should execute command give error if command failed' do
      expect(BrowserStack::OSUtils).to receive(:safe_execute).with("plutil", ["-convert", "json", 'dummy_file_path', "-o", "-"]).and_raise(OSUtilsError)

      expect { Utils.parse_plist_and_convert_to_json("dummy_file_path") }.to raise_error(OSUtilsError)
    end

    it 'should execute command give error if json is not parseable' do
      expect(BrowserStack::OSUtils).to receive(:safe_execute).with("plutil", ["-convert", "json", 'dummy_file_path', "-o", "-"]).and_return("Hi")

      expect { Utils.parse_plist_and_convert_to_json("dummy_file_path") }.to raise_error(JSON::ParserError)
    end
  end

  describe '#capture3' do
    it 'should capture stdout and stderr from process' do
      o, e, s = Utils.capture3(%(ruby -e 'STDOUT.sync=true; 1000.times { print "o"*1000; STDERR.print "e"*1000 }'))
      expect(o).to eq('o' * 1000000)
      expect(e).to eq('e' * 1000000)
      expect(s.success?).to be true
    end

    it 'should capture stdout and stderr from process and return error statue if command fails' do
      o, e, s = Utils.capture3(%(ruby -e 'STDOUT.sync=true; 1000.times { print "o"*1000; STDERR.print "e"*1000 }; exit 1'))
      expect(o).to eq('o' * 1000000)
      expect(e).to eq('e' * 1000000)
      expect(s.success?).to be false
    end
  end

  describe "get_session_id_from_test_id" do
    it "should return first 40 chars as session id" do
      test_id = "40cf96d3673099565f821a28924f5fa8dc268ba912345678"
      expect(Utils.get_session_id_from_test_id(test_id)).to eql("40cf96d3673099565f821a28924f5fa8dc268ba9")
    end
  end

  describe "push_stability_metrics" do
    it "pushes to eds as its the last test" do
      expect(Utils).to receive(:send_to_eds).and_return(true)

      Utils.push_stability_metrics("session_id", "test_id", "data", "instru", 5, 5)
    end

    it "pushed to zombie because of test case number mismatch" do
      expect(BrowserStack::Zombie).to receive(:push_logs).and_return(true)

      Utils.push_stability_metrics("session_id", "test_id", "data", "instru", 5, 6)
    end
  end

  describe "#get_working_interfaces" do
    let(:device) { "24234Device" }
    before do
      allow(IdeviceUtils).to receive(:device_version).and_return(Gem::Version.new('16.0'))
    end

    it "should return non-empty array of working interfaces" do
      expect(IdeviceUtils).to receive(:get_usb_location).and_return("14710000")
      expect(IdeviceUtils).to receive(:get_network_interfaces).and_return(["en11", "en20"])
      expect(IdeviceUtils).to receive(:is_network_interface_available?).and_return(true, false)
      result = Utils.get_working_interfaces(device)

      expect(result).to eq(["en11"])
    end

    it "should return empty array of working interfaces when none are available" do
      expect(IdeviceUtils).to receive(:get_usb_location).and_return("14710000")
      expect(IdeviceUtils).to receive(:get_network_interfaces).and_return(["en11", "en20"])
      expect(IdeviceUtils).to receive(:is_network_interface_available?).and_return(false, false)
      result = Utils.get_working_interfaces(device)

      expect(result).to eq([])
    end

    it "should return empty array of working interfaces when no network interface found" do
      expect(IdeviceUtils).to receive(:get_usb_location).and_return("14710000")
      expect(IdeviceUtils).to receive(:get_network_interfaces).and_return([])
      result = Utils.get_working_interfaces(device)

      expect(result).to eq([])
    end

    it "should return empty array of working interfaces when location_id is null" do
      expect(IdeviceUtils).to receive(:get_usb_location).and_return("")
      result = Utils.get_working_interfaces(device)

      expect(result).to eq([])
    end

    it "should return only interfaces with inet present if os is 17+" do
      allow(IdeviceUtils).to receive(:device_version).and_return(Gem::Version.new('17.0'))
      expect(IdeviceUtils).to receive(:get_usb_location).and_return("14710000")
      expect(IdeviceUtils).to receive(:get_network_interfaces).and_return(["en11", "en20"])
      expect(IdeviceUtils).to receive(:interface_has_inet?).with("en11").and_return true
      expect(IdeviceUtils).to receive(:interface_has_inet?).with("en20").and_return false
      expect(IdeviceUtils).to receive(:is_network_interface_available?).and_return(true, true)
      result = Utils.get_working_interfaces(device, check_inet = true)
      expect(result).to eq(['en11'])
    end
  end

  describe "#update_screenshot_instrumentation_with_lock" do
    let(:file) { "dummy" }

    after(:each) { FileUtils.rm_rf(["#{file}.lock"]) }
    it "should update screenshot instrumentation file for first screenshot" do
      expected_result = Utils.default_screenshot_instrumentation_data
      expected_result["total_screenshots"] = 1
      expected_result["capture_stage"] = 1

      expect(File).to receive(:read).and_return("")
      expect(Utils).to receive(:write_to_file).with(file, expected_result.to_json)

      Utils.update_screenshot_instrumentation_with_lock(file, nil, "capture", {})
    end

    it "should update screenshot instrumentation file for screenshot transition from capture to convert" do
      input_result =  Utils.default_screenshot_instrumentation_data
      input_result["total_screenshots"] = 1
      input_result["capture_stage"] = 1

      expected_result = Utils.default_screenshot_instrumentation_data
      expected_result["total_screenshots"] = 1
      expected_result["capture_stage"] = 0
      expected_result["convert_stage"] = 1
      expected_result["total_capture_time"] = 0.10
      expected_result["max_capture_time"] = 0.10
      expected_result = expected_result

      expect(File).to receive(:read).and_return(input_result.to_json)
      expect(Utils).to receive(:write_to_file).with(file, expected_result.to_json)

      Utils.update_screenshot_instrumentation_with_lock(file, "capture", "convert", { stage_time_taken: 0.10 })
    end

    it "should update screenshot instrumentation file for screenshot transition from capture to failed" do
      input_result =  Utils.default_screenshot_instrumentation_data
      input_result["total_screenshots"] = 1
      input_result["capture_stage"] = 1

      expected_result = Utils.default_screenshot_instrumentation_data
      expected_result["total_screenshots"] = 1
      expected_result["capture_stage"] = 0
      expected_result["capture_failed"] = 1
      expected_result["total_capture_time"] = 0.10
      expected_result["max_capture_time"] = 0.10
      expected_result = expected_result

      expect(File).to receive(:read).and_return(input_result.to_json)
      expect(Utils).to receive(:write_to_file).with(file, expected_result.to_json)

      Utils.update_screenshot_instrumentation_with_lock(file, "capture", "failed", { stage_time_taken: 0.10 })
    end

    it "should update screenshot instrumentation file for screenshot transition from convert to upload" do
      input_result =  Utils.default_screenshot_instrumentation_data
      input_result["total_screenshots"] = 1
      input_result["convert_stage"] = 1

      expected_result = Utils.default_screenshot_instrumentation_data
      expected_result["total_screenshots"] = 1
      expected_result["convert_stage"] = 0
      expected_result["upload_stage"] = 1
      expected_result["total_convert_time"] = 0.10
      expected_result["max_convert_time"] = 0.10
      expected_result["total_convert_queue_time"] = 0.10
      expected_result["max_convert_queue_time"] = 0.10
      expected_result = expected_result

      expect(File).to receive(:read).and_return(input_result.to_json)
      expect(Utils).to receive(:write_to_file).with(file, expected_result.to_json)

      Utils.update_screenshot_instrumentation_with_lock(file, "convert", "upload", { stage_time_taken: 0.10, queue_time: 0.10 })
    end

    it "should update screenshot instrumentation file for screenshot transition from convert to failed" do
      input_result =  Utils.default_screenshot_instrumentation_data
      input_result["total_screenshots"] = 1
      input_result["convert_stage"] = 1

      expected_result = Utils.default_screenshot_instrumentation_data
      expected_result["total_screenshots"] = 1
      expected_result["convert_stage"] = 0
      expected_result["convert_failed"] = 1
      expected_result["total_convert_time"] = 0.10
      expected_result["max_convert_time"] = 0.10
      expected_result["total_convert_queue_time"] = 0.10
      expected_result["max_convert_queue_time"] = 0.10
      expected_result = expected_result

      expect(File).to receive(:read).and_return(input_result.to_json)
      expect(Utils).to receive(:write_to_file).with(file, expected_result.to_json)

      Utils.update_screenshot_instrumentation_with_lock(file, "convert", "failed", { stage_time_taken: 0.10, queue_time: 0.10 })
    end

    it "should update screenshot instrumentation file for screenshot transition from upload to success" do
      input_result =  Utils.default_screenshot_instrumentation_data
      input_result["total_screenshots"] = 1
      input_result["upload_stage"] = 1

      expected_result = Utils.default_screenshot_instrumentation_data
      expected_result["total_screenshots"] = 1
      expected_result["upload_stage"] = 0
      expected_result["success_stage"] = 1
      expected_result["total_upload_time"] = 0.10
      expected_result["max_upload_time"] = 0.10
      expected_result["total_upload_queue_time"] = 0.10
      expected_result["max_upload_queue_time"] = 0.10
      expected_result["total_time"] = 0.10
      expected_result["max_time"] = 0.10
      expected_result = expected_result

      expect(File).to receive(:read).and_return(input_result.to_json)
      expect(Utils).to receive(:write_to_file).with(file, expected_result.to_json)

      Utils.update_screenshot_instrumentation_with_lock(file, "upload", "success", { stage_time_taken: 0.10, queue_time: 0.10, total_time: 0.10 })
    end

    it "should update screenshot instrumentation file for screenshot transition from upload to timeout" do
      input_result =  Utils.default_screenshot_instrumentation_data
      input_result["total_screenshots"] = 1
      input_result["upload_stage"] = 1

      expected_result = Utils.default_screenshot_instrumentation_data
      expected_result["total_screenshots"] = 1
      expected_result["upload_stage"] = 0
      expected_result["success_stage"] = 0
      expected_result["timeout_stage"] = 1
      expected_result["total_upload_time"] = 0.10
      expected_result["max_upload_time"] = 0.10
      expected_result["total_upload_queue_time"] = 0.10
      expected_result["max_upload_queue_time"] = 0.10
      expected_result["total_time"] = 121
      expected_result["max_time"] = 121
      expected_result = expected_result

      expect(File).to receive(:read).and_return(input_result.to_json)
      expect(Utils).to receive(:write_to_file).with(file, expected_result.to_json)

      Utils.update_screenshot_instrumentation_with_lock(file, "upload", "timeout", { stage_time_taken: 0.10, queue_time: 0.10, total_time: 121 })
    end

    it "should update screenshot instrumentation file for screenshot with appropriate black_screenshot_status" do
      input_result =  Utils.default_screenshot_instrumentation_data
      input_result["total_screenshots"] = 1
      input_result["upload_stage"] = 1

      expected_result = Utils.default_screenshot_instrumentation_data
      expected_result["total_screenshots"] = 1
      expected_result["upload_stage"] = 0
      expected_result["success_stage"] = 1
      expected_result["total_upload_time"] = 0.10
      expected_result["max_upload_time"] = 0.10
      expected_result["total_upload_queue_time"] = 0.10
      expected_result["max_upload_queue_time"] = 0.10
      expected_result["total_time"] = 0.10
      expected_result["max_time"] = 0.10
      expected_result["total_black_screenshots"] = 1
      intermediatte_result = expected_result.clone
      intermediatte_result["max_upload_queue_time"] = 0
      intermediatte_result["total_upload_queue_time"] = 0
      intermediatte_result["total_black_screenshots"] = 0

      expect(File).to receive(:read).and_return(input_result.to_json)
      expect(Utils).to receive(:write_to_file).with(file, expected_result.to_json)
      expect(ScreenshotUtil).to receive(:process_black_screenshot_instrumentation).with(intermediatte_result, "true") do |args|
        args["total_black_screenshots"] += 1
      end
      Utils.update_screenshot_instrumentation_with_lock(file, "upload", "success", { stage_time_taken: 0.10, queue_time: 0.10, total_time: 0.10 }, 2, "true")
    end

    it "should update screenshot instrumentation file for screenshot transition from upload to failed" do
      input_result =  Utils.default_screenshot_instrumentation_data
      input_result["total_screenshots"] = 1
      input_result["upload_stage"] = 1

      expected_result = Utils.default_screenshot_instrumentation_data
      expected_result["total_screenshots"] = 1
      expected_result["upload_stage"] = 0
      expected_result["upload_failed"] = 1
      expected_result["total_upload_time"] = 0.10
      expected_result["max_upload_time"] = 0.10
      expected_result["total_upload_queue_time"] = 0.10
      expected_result["max_upload_queue_time"] = 0.10
      expected_result = expected_result

      expect(File).to receive(:read).and_return(input_result.to_json)
      expect(Utils).to receive(:write_to_file).with(file, expected_result.to_json)

      Utils.update_screenshot_instrumentation_with_lock(file, "upload", "failed", { stage_time_taken: 0.10, queue_time: 0.10 })
    end

    it "should push kind to zombie if failed to acquire lock" do
      expect(Utils).to receive(:with_lock).and_raise(LockfileTimeoutError)
      expect(BrowserStack::Zombie).to receive(:push_logs).with("ss-instrumentation-failed", anything, { "session_id" => file })

      Utils.update_screenshot_instrumentation_with_lock(file, "upload", "failed", { stage_time_taken: 0.10, queue_time: 0.10 })
    end

    it "should push kind to zombie if standard error is raise" do
      expect(Utils).to receive(:with_lock).and_raise(StandardError)
      expect(BrowserStack::Zombie).to receive(:push_logs).with("ss-instrumentation-failed", anything, { "session_id" => file })

      Utils.update_screenshot_instrumentation_with_lock(file, "upload", "failed", { stage_time_taken: 0.10, queue_time: 0.10 })
    end
  end

  describe '#is_app_product_genre?' do
    it 'should return expected output' do
      expect(Utils.is_app_product_genre?('app_automate')).to eql(true)

      expect(Utils.is_app_product_genre?('app_live_testing')).to eql(true)

      expect(Utils.is_app_product_genre?('automate')).to eql(false)
    end
  end

  describe '#get_error_bucket' do
    it 'should return error bucket if any match is found' do
      error_message = "Error: undefined method 'get_cropped_screenshots' in WdaClient"
      expect(Utils.get_error_bucket(error_message)).to eq("wda_unknown_command_response")
    end

    it 'should return error bucket if any match is found' do
      error_message = "undefined method `[]' for nil:NilClass (NoMethodError)"
      expect(Utils.get_error_bucket(error_message)).to eq("wda_empty_response")
    end

    it 'should return unknown if no match is found' do
      error_message = "Exception Class: ZeroDivisionError"
      expect(Utils.get_error_bucket(error_message)).to eq("unknown")
    end
  end

  describe '#update_screenshot_states_with_lock' do
    let(:file) { "dummy" }

    after(:each) { FileUtils.rm_rf(["#{file}.lock"]) }
    it "should update screenshot instrumentation file for screenshot capture_failed_buckets in case of execution timeout" do
      input_result =  Utils.default_screenshot_instrumentation_data
      input_result["total_screenshots"] = 1
      input_result["capture_stage"] = 1

      expected_result = Utils.default_screenshot_instrumentation_data
      expected_result["total_screenshots"] = 1
      expected_result["capture_stage"] = 1
      expected_result["capture_failed_buckets"] = {
        "execution_timeout" => 1,
        "wda_empty_response" => 0,
        "wda_unknown_command_response" => 0,
        "unknown" => 0
      }
      expected_result = expected_result

      expect(File).to receive(:read).and_return(input_result.to_json)
      expect(Utils).to receive(:write_to_file).with(file, expected_result.to_json)

      Utils.update_screenshot_states_with_lock(file, "capture", "failed", "execution_timeout")
    end

    it "should update screenshot instrumentation file for screenshot capture_failed_buckets in case of empty wda response" do
      input_result =  Utils.default_screenshot_instrumentation_data
      input_result["total_screenshots"] = 1
      input_result["capture_stage"] = 1

      expected_result = Utils.default_screenshot_instrumentation_data
      expected_result["total_screenshots"] = 1
      expected_result["capture_stage"] = 1
      expected_result["capture_failed_buckets"] = {
        "execution_timeout" => 0,
        "wda_empty_response" => 1,
        "wda_unknown_command_response" => 0,
        "unknown" => 0
      }
      expected_result = expected_result

      expect(File).to receive(:read).and_return(input_result.to_json)
      expect(Utils).to receive(:write_to_file).with(file, expected_result.to_json)

      Utils.update_screenshot_states_with_lock(file, "capture", "failed", "wda_empty_response")
    end

    it "should update screenshot instrumentation file for screenshot capture_failed_buckets in case of wda unknown command response" do
      input_result =  Utils.default_screenshot_instrumentation_data
      input_result["total_screenshots"] = 1
      input_result["capture_stage"] = 1

      expected_result = Utils.default_screenshot_instrumentation_data
      expected_result["total_screenshots"] = 1
      expected_result["capture_stage"] = 1
      expected_result["capture_failed_buckets"] = {
        "execution_timeout" => 0,
        "wda_empty_response" => 0,
        "wda_unknown_command_response" => 1,
        "unknown" => 0
      }
      expected_result = expected_result

      expect(File).to receive(:read).and_return(input_result.to_json)
      expect(Utils).to receive(:write_to_file).with(file, expected_result.to_json)

      Utils.update_screenshot_states_with_lock(file, "capture", "failed", "wda_unknown_command_response")
    end

    it "should update screenshot instrumentation file for screenshot capture_failed_buckets in case of unknown bucket response" do
      input_result =  Utils.default_screenshot_instrumentation_data
      input_result["total_screenshots"] = 1
      input_result["capture_stage"] = 1

      expected_result = Utils.default_screenshot_instrumentation_data
      expected_result["total_screenshots"] = 1
      expected_result["capture_stage"] = 1
      expected_result["capture_failed_buckets"] = {
        "execution_timeout" => 0,
        "wda_empty_response" => 0,
        "wda_unknown_command_response" => 0,
        "unknown" => 1
      }
      expected_result = expected_result

      expect(File).to receive(:read).and_return(input_result.to_json)
      expect(Utils).to receive(:write_to_file).with(file, expected_result.to_json)

      Utils.update_screenshot_states_with_lock(file, "capture", "failed", "unknown")
    end
  end

  context "#enable_notifications" do
    let(:device_id) { 'dummy_id' }

    context 'success' do
      it 'should trigger change_notifications_profile_mdm when geoguard not present' do
        expect(IdeviceUtils).to receive(:check_app_with_bundle_id_exists).and_return(false)
        expect(Utils).to receive(:change_notifications_profile_mdm).with([], device_id, false).and_return({ "result" => "success" })

        expect { Utils.enable_notifications(device_id) }.to_not raise_error
      end

      it 'should trigger change_notifications_profile_mdm when geoguard present' do
        expect(IdeviceUtils).to receive(:check_app_with_bundle_id_exists).and_return(true)
        expect(Utils).to receive(:change_notifications_profile_mdm).with([GEOGUARD_BUNDLE_ID], device_id, true).and_return({ "result" => "success" })

        expect { Utils.enable_notifications(device_id) }.to_not raise_error
      end
    end

    context 'failure' do
      it 'should not raise error if profile is already uninstalled' do
        expect(IdeviceUtils).to receive(:check_app_with_bundle_id_exists).and_return(false)
        expect(Utils).to receive(:change_notifications_profile_mdm).with([], device_id, false).and_return({ "result" => "failed", "error" =>
          {
            "message" => "RuntimeError - Couldn't remove Notifications profile: The profile “com.browserstack.notifications.abc” is not installed."
          } })

        expect { Utils.enable_notifications(device_id) }.to_not raise_error
      end
      it 'should raise error' do
        expect(IdeviceUtils).to receive(:check_app_with_bundle_id_exists).and_return(false)
        expect(Utils).to receive(:change_notifications_profile_mdm).with([], device_id, false).and_return({ "result" => "failed", "error" => { "message" => "Failure" } })

        expect { Utils.enable_notifications(device_id) }.to raise_error(/Failure/)
      end
    end
  end

  context "#toggle_notifications_mdm" do
    let(:uuid) { '000820-427503425' }
    let(:start_time) { 100 }
    let(:failure_data) { { 'result' => "failed", 'time_taken' => 2, "error" => "some_xcui_reason", "error_message" => "some xcui message" } }
    let(:mdm_success_data) { { "result" => "success", "time_taken" => 0 } }
    let(:mdm_failure_data) { { "result" => "failed", "time_taken" => 0, "error" => "some_mdm_error" } }
    let(:mock_configuration_profiles_manager) { double("ConfigurationProfilesManager") }

    before do
      Timecop.freeze(Time.at(start_time))

      allow(ConfigurationProfilesManager).to receive(:new).and_return(mock_configuration_profiles_manager)
    end

    after do
      Timecop.return
    end

    context 'when new notifications flow is not enabled' do
      before do
        allow(Utils).to receive(:new_notifications_flow_enabled?).and_return(false)
      end

      it "should return success data when install and removal passed" do
        expect(mock_configuration_profiles_manager).to receive(:install_profile)
        expect(mock_configuration_profiles_manager).to receive(:remove_profile)
        expect(IdeviceUtils).to receive(:check_app_with_bundle_id_exists).and_return(false)

        result = Utils.toggle_notifications_mdm(["bundle_id"], uuid)
        expect(result).to eq(mdm_success_data)
      end

      it "should return failure data when install failed" do
        expect(mock_configuration_profiles_manager).to receive(:install_profile).and_raise(MdmApiFatalException, "Couldn't install Notifications profile")
        result = Utils.toggle_notifications_mdm(["bundle_id"], uuid)
        expect(result).to eq({
          "result" => "failed", "time_taken" => 0,
          "error" => { "reason" => "Toggle Notifications Failed", "message" => "Couldn't install Notifications profile" }
        })
      end

      it "should return failure data when removal failed" do
        expect(mock_configuration_profiles_manager).to receive(:install_profile)
        expect(IdeviceUtils).to receive(:check_app_with_bundle_id_exists).and_return(false)
        expect(mock_configuration_profiles_manager).to receive(:remove_profile).and_raise(MdmApiFatalException, "Couldn't remove Notifications profile")
        result = Utils.toggle_notifications_mdm(["bundle_id"], uuid)
        expect(result).to eq({
          "result" => "failed",
          "time_taken" => 0,
          "error" => { "reason" => "Toggle Notifications Failed", "message" => "Couldn't remove Notifications profile" }
        })
      end
    end

    context 'when new notifications flow is enabled' do
      before do
        allow(Utils).to receive(:new_notifications_flow_enabled?).and_return(true)
      end

      it "should return success data when install and removal passed" do
        expect(mock_configuration_profiles_manager).to receive(:install_profile)
        expect(mock_configuration_profiles_manager).to receive(:install_profile)
        result = Utils.toggle_notifications_mdm(["bundle_id"], uuid)
        expect(result).to eq(mdm_success_data)
      end

      it "should return failure data when install failed" do
        expect(mock_configuration_profiles_manager).to receive(:install_profile).and_raise(MdmApiFatalException, "Couldn't install Notifications profile")
        result = Utils.toggle_notifications_mdm(["bundle_id"], uuid)
        expect(result).to eq({
          "result" => "failed", "time_taken" => 0,
          "error" => { "reason" => "Toggle Notifications Failed", "message" => "Couldn't install Notifications profile" }
        })
      end
    end
  end

  context "#send_network_logs_to_eds" do
    before(:each) do
      @mock_config = {
        "eds" => { "hostname" => "config_host", "port_udp" => "config_port" },
        "eds_api_key" => "config_key",
        "network_logs_send_to_eds" => true,
        "static_conf" => { "sub_region" => "ap-southeast-2c" }
      }

      stub_const("REGEX_TO_SKIP_FOR_NETWORK_LOGS", { "StartsWith" => ["api."], "ExactMatch" => ["browserstack.com"] } )
    end

    it 'should use params eds config if params has those keys in them' do
      all_host_requests = { "play.googleapis.com:443": { "first_request_time" => "2022-12-22T06:35:11.549+05:30", "count" => 6 } }

      allow(BrowserStack::Configuration).to receive_message_chain(:new, :all).and_return(@mock_config)
      expect(Bsdwh).to receive(:send).with(instance_of(String), "host", "port", "key").and_return(nil)
      Utils.send_network_logs_to_eds("", "", all_host_requests, "etype", { edsHost: "host", edsPort: "port", edsKey: "key" })
    end

    it "creates and passes proper event json to bsdwh" do
      allow(BrowserStack::Configuration).to receive_message_chain(:new, :all).and_return(@mock_config)
      all_host_requests = { "play.googleapis.com:443": { "first_request_time" => "2022-12-22T06:35:11.549+05:30", "count" => 6 } }

      send_event_json = {
        event_type: "mobile_network_logs",
        data: { session_id: "abcd", product: "live_testing", url_accessed: "play.googleapis.com:443", first_request_time: "2022-12-22T06:35:11.549+05:30", requests_count: 6 }
      }

      expect(Bsdwh).to receive(:send).with(send_event_json.to_json, "config_host", "config_port", "config_key").and_return(nil)

      Utils.send_network_logs_to_eds("abcd", "live_testing", all_host_requests, "mobile_network_logs")
    end

    it "doesnt send to eds if config is set to false" do
      @mock_config["network_logs_send_to_eds"] = false
      allow(BrowserStack::Configuration).to receive_message_chain(:new, :all).and_return(@mock_config)
      all_host_requests = { "play.googleapis.com:443": { "first_request_time" => "2022-12-22T06:35:11.549+05:30", "count" => 6 } }

      expect(Bsdwh).not_to receive(:send)

      Utils.send_network_logs_to_eds("abcd", "live_testing", all_host_requests, "mobile_network_logs")
    end
  end

  context "#prepare_skip_urls_for_session_network_logs" do
    it "returns skip urls from constant" do
      stub_const("REGEX_TO_SKIP_FOR_NETWORK_LOGS", { "StartsWith" => ["api."], "ExactMatch" => ["browserstack.com"] } )

      result = Utils.prepare_skip_urls_for_session_network_logs

      expect(result).to eq(/^api[.]|browserstack[.]com/)
    end
  end

  context "#process_devtools_cache" do
    it "shouldn't write any file if product is not app-live" do
      params = {
        genre: "live_testing",
        'oldLive_session_id' => "oldLive_session_id12344",
        'app_live_session_id' => "app_live_session_id1234"
      }

      expect_any_instance_of(Utils).not_to receive(:write_to_file)
      Utils.process_devtools_cache(params, {})
    end

    it "should write al_session_session-id file but remove file shouldn't invoke if oldLive_session_id isn't present " do
      params = {
        genre: 'app_live_testing',
        'app_live_session_id' => 'app_live_session_id1234',
        'startElement' => 'network_config_change'
      }
      server_config = {
        'state_files_dir' => '/tmp'
      }

      expect(Utils).to receive(:write_to_file).with("/tmp/al_session_app_live_session_id1234", params.to_json)
      expect(FileUtils).not_to receive(:rm_rf)

      Utils.process_devtools_cache(params, server_config)
    end

    it "should write al_session_session-id file but remove file shouldn't invoke if it's not realunch flow " do
      params = {
        genre: 'app_live_testing',
        'oldLive_session_id' => 'oldLive_session_id12344',
        'app_live_session_id' => 'app_live_session_id1234',
        'startElement' => 'dummy'
      }
      server_config = {
        'state_files_dir' => '/tmp'
      }

      expect(Utils).to receive(:write_to_file).with("/tmp/al_session_app_live_session_id1234", params.to_json)
      expect(FileUtils).not_to receive(:rm_rf)
      Utils.process_devtools_cache(params, server_config)
    end

    it "should write the session file and also remove last session file" do
      params = {
        genre: 'app_live_testing',
        'oldLive_session_id' => 'oldLive_session_id12344',
        'app_live_session_id' => 'app_live_session_id1234',
        'startElement' => 'network_config_change'
      }
      server_config = {
        'state_files_dir' => '/tmp'
      }
      expect(Utils).to receive(:write_to_file).with("/tmp/al_session_app_live_session_id1234", params.to_json)
      expect(FileUtils).to receive(:rm_rf).with("/tmp/al_session_oldLive_session_id12344")
      Utils.process_devtools_cache(params, server_config)
    end
  end

  context "#zip_logs" do
    before(:each) do
      allow(DateTime).to receive(:now).and_return(Date.new(2023, 11, 19))
    end
    it "zips the log calculating time and size" do
      file_path = 'temp-file.txt'
      file_size_bytes = 2025000
      allow_any_instance_of(Object).to receive(:system).and_return(true)
      allow(File).to receive(:exist?).and_return(true)
      allow(File).to receive(:file?).and_return(true)
      allow(File).to receive_message_chain(:stat, :size).and_return(20200)

      expect(Utils.zip_logs(file_path, file_size_bytes, true)).to eq(['temp-file.txt.gz', 20200, 0])
    end

    it "does not set zipped file path if zip command returns false response" do
      file_path = 'temp-file.txt'
      file_size_bytes = 2025000
      allow_any_instance_of(Object).to receive(:system).and_return(false)
      allow(File).to receive(:exist?).and_return(true)
      allow(File).to receive(:file?).and_return(true)
      allow(File).to receive_message_chain(:stat, :size).and_return(20200)

      expect(Utils.zip_logs(file_path, file_size_bytes, true)).to eq(['temp-file.txt', -1, 0])
    end

    it "does not set zipped file path if file does not exist" do
      file_path = 'temp-file.txt'
      file_size_bytes = 2025000
      allow_any_instance_of(Object).to receive(:system).and_return(true)
      allow(File).to receive(:exist?).and_return(false)
      allow(File).to receive(:file?).and_return(true)
      allow(File).to receive_message_chain(:stat, :size).and_return(20200)

      expect(Utils.zip_logs(file_path, file_size_bytes, true)).to eq(['temp-file.txt', -1, 0])
    end

    it "returns unzipped file if StandardError exception raised" do
      file_path = 'temp-file.txt'
      file_size_bytes = 2025000
      allow_any_instance_of(Object).to receive(:system).and_raise(StandardError)
      allow(File).to receive(:exist?).and_return(false)
      allow(File).to receive(:file?).and_return(true)
      allow(File).to receive_message_chain(:stat, :size).and_return(20200)

      expect(Utils.zip_logs(file_path, file_size_bytes, true)).to eq(['temp-file.txt', -1, 0])
    end

    it "returns zipped file and size as -1 if StandardError exception raised when calculating size" do
      file_path = 'temp-file.txt'
      file_size_bytes = 2025000
      allow_any_instance_of(Object).to receive(:system).and_return(true)
      allow(File).to receive(:exist?).and_return(true)
      allow(File).to receive(:file?).and_return(true)
      allow(File).to receive_message_chain(:stat, :size).and_raise(StandardError)

      expect(Utils.zip_logs(file_path, file_size_bytes, true)).to eq(['temp-file.txt.gz', -1, 0])
    end
  end

  context "#delete_file" do
    it "prints the success log if delete operation passes" do
      file_path = 'temp-file.txt.gz'
      allow(File).to receive(:exist?).and_return(true)
      allow(File).to receive(:delete).and_return(true)
      expect(@mock_logger).to receive(:info).with("Deleted file with path temp-file.txt.gz")
      Utils.delete_file(file_path)
    end
  end

  context "#track_feature_usage" do
    let(:feature) { "aa_resign_app_false" }
    let(:state) { "attempt" }
    let(:params) do
      {
        :user_id => 123456,
        "automate_session_id" => "test_session_123",
        "genre" => "app-automate"
      }
    end
    let(:tags) do
      {
        "feature" => feature,
        "state" => state,
        "os" => "ios_njb"
      }
    end

    before do
      allow(BrowserStack).to receive_message_chain(:logger, :info)
      allow(BrowserStack).to receive_message_chain(:logger, :error)
      allow(BrowserStack::Zombie).to receive(:push_logs)
      stub_const("AA_FEATURE_USAGE", "feature_usage_metric")
    end

    it "sends feature usage event with correct parameters" do
      mock_hooter = instance_double(Hooter)
      expect(Hooter).to receive(:new).with('use').and_return(mock_hooter)
      expect(mock_hooter).to receive(:push_feature_usage).with(
        params[:user_id].to_s,
        params["genre"],
        AA_FEATURE_USAGE,
        tags
      )
      expect(BrowserStack).to receive_message_chain(:logger, :info).with("Feature usage event pushed successfully with tags: #{tags}")

      Utils.track_feature_usage(feature, params, state)
    end

    it "does nothing when feature is nil" do
      expect(Hooter).not_to receive(:new)

      Utils.track_feature_usage(nil, params, state)
    end

    it "does nothing when params is nil" do
      expect(Hooter).not_to receive(:new)

      Utils.track_feature_usage(feature, nil, state)
    end

    it "does nothing when user_id is missing" do
      params_without_user_id = params.dup
      params_without_user_id.delete(:user_id)

      expect(Hooter).not_to receive(:new)

      Utils.track_feature_usage(feature, params_without_user_id, state)
    end
  end
end
