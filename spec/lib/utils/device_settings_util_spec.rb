require_relative '../../spec_helper'
require_relative '../../../lib/utils/device_settings_util'
require_relative '../../../lib/utils/osutils'
require_relative '../../../config/constants'
require_relative '../../../server/device_manager'
require_relative '../../../lib/utils/utils'

describe BrowserStack::DeviceSettingsUtil do
  let(:device) { 'test_device' }
  let(:device_state) { DeviceState.new(device) }
  let(:wda_port) { 8084 }
  let(:wda_client) { WdaClient.new(wda_port) }
  let(:app_display_name) { 'sample_app' }
  let(:session_file_contents) do
    {
      'app_testing_bundle_id' => 'sample.app.com'
    }
  end

  let(:device_config) do
    {
      'webdriver_port' => wda_port,
      'device_version' => '16.5'
    }
  end

  let(:settings_config) do
    '{
      "location_services": {
          "global_setting_name": "Privacy & Security",
          "ON_DSL": {
              "Location Services": {
                  "type": "PSChildPaneSpecifier",
                  "value": {
                      "Location Services": {
                          "type": "PSToggleSwitchSpecifier",
                          "value": "ON"
                      }
                  }
              }
          },
          "OFF_DSL": {
              "Location Services": {
                  "type": "PSChildPaneSpecifier",
                  "value": {
                      "Location Services": {
                          "type": "PSToggleSwitchSpecifier",
                          "value": "OFF"
                      }
                  }
              }
          },
          "special_post_action_value_map": {
              "ON": null,
              "OFF": "postLocationTurnOff"
          }
      }
    }'
  end

  describe "#handle_device_setting" do
    it "should call update settings for location_services" do
      expect(DeviceManager).to receive(:device_configuration_check).with(device).and_return(device_config)
      expect(File).to receive(:exists?).with("#{CONFIG_PATH}/device_settings/ios_16.json").and_return(true)
      expect(File).to receive(:read).and_return(settings_config)
      expect(DeviceManager).to receive(:session_file_contents).with(device).and_return(session_file_contents)

      expected_params = {
        'automate_session_id' => 'session_id',
        'global_setting_name' => 'Privacy & Security',
        'special_post_action' => 'postLocationTurnOff',
        'value' => 'OFF',
        'app_display_name' => 'sample_app',
        'app_testing_bundle_id' => 'sample.app.com',
        'device_settings_DSL' => {
          'Location Services' => {
            'type' => 'PSChildPaneSpecifier',
            'value' => {
              'Location Services' => {
                'type' => 'PSToggleSwitchSpecifier',
                'value' => 'OFF'
              }
            }
          }
        }
      }
      expect(DeviceSettingsUtil).to receive(:update_settings).with('test_device', expected_params)
      DeviceSettingsUtil.handle_device_setting(device, 'location_services', 'OFF', app_display_name, 'session_id')
    end

    it "should throw error if settings config is not present" do
      expect(DeviceManager).to receive(:device_configuration_check).with(device).and_return(device_config)
      expect(DeviceManager).to receive(:session_file_contents).with(device).and_return(session_file_contents)
      expect(File).to receive(:exists?).with("#{CONFIG_PATH}/device_settings/ios_16.json").and_return(false)
      expect { DeviceSettingsUtil.handle_device_setting(device, 'location_services', 'OFF', app_display_name, 'session_id') }.to raise_error(DeviceSettingsError, "Device Settings JSON absent")
    end

    it "should throw error if setting name is not present in config" do
      expect(DeviceManager).to receive(:device_configuration_check).with(device).and_return(device_config)
      expect(DeviceManager).to receive(:session_file_contents).with(device).and_return(session_file_contents)
      expect(File).to receive(:exists?).with("#{CONFIG_PATH}/device_settings/ios_16.json").and_return(true)
      expect(File).to receive(:read).and_return("{}")
      expect { DeviceSettingsUtil.handle_device_setting(device, 'location_services', 'OFF', app_display_name, 'session_id') }.to raise_error(DeviceSettingsError, "Settings name absent in JSON")
    end
  end

  describe "#update_settings" do
    it "should call WDA correctly without special actions" do
      params = {
        'automate_session_id' => 'session_id',
        'global_setting_name' => 'Privacy & Security',
        'value' => 'ON',
        'app_display_name' => 'sample_app',
        'app_testing_bundle_id' => 'sample.app.com',
        'device_settings_DSL' => {
          'Location Services' => {
            'type' => 'PSChildPaneSpecifier',
            'value' => {
              'Location Services' => {
                'type' => 'PSToggleSwitchSpecifier',
                'value' => 'ON'
              }
            }
          }
        }
      }

      expected_request_body = {
        "session_id" => params['automate_session_id'],
        "global_setting_name" => params['global_setting_name'],
        "DSL" => params['device_settings_DSL']
      }

      expect(Utils).to receive(:enable_settings_app)
      expect(DeviceManager).to receive(:device_configuration_check).with(device).and_return(device_config)
      allow(WdaClient).to receive(:new).and_return(wda_client)
      response = { "value" => { "status" => "success" } }
      expect(wda_client).to receive(:make_request).with('POST', '/bs/updateSettings', expected_request_body).and_return(response)
      expect(DeviceState).to receive(:new).and_return(device_state)
      expect(device_state).to receive(:remove_device_location_off_file)
      expect(Utils).to receive(:disable_settings_app).and_return(true)
      expect(DeviceSettingsUtil).to receive(:foreground_user_app).with(device, params)
      DeviceSettingsUtil.update_settings(device, params)
    end

    it "should call WDA correctly with special actions" do
      params = {
        'automate_session_id' => 'session_id',
        'global_setting_name' => 'Privacy & Security',
        'value' => 'OFF',
        'app_display_name' => 'sample_app',
        'app_testing_bundle_id' => 'sample.app.com',
        'special_post_action' => 'postLocationTurnOff',
        'device_settings_DSL' => {
          'Location Services' => {
            'type' => 'PSChildPaneSpecifier',
            'value' => {
              'Location Services' => {
                'type' => 'PSToggleSwitchSpecifier',
                'value' => 'OFF'
              }
            }
          }
        }
      }

      expected_request_body = {
        "session_id" => params['automate_session_id'],
        "global_setting_name" => params['global_setting_name'],
        'special_post_action' => params['special_post_action'],
        "DSL" => params['device_settings_DSL']
      }

      expect(Utils).to receive(:enable_settings_app)
      expect(DeviceManager).to receive(:device_configuration_check).with(device).and_return(device_config)
      allow(WdaClient).to receive(:new).and_return(wda_client)
      response = { "value" => { "status" => "success" } }
      expect(wda_client).to receive(:make_request).with('POST', '/bs/updateSettings', expected_request_body).and_return(response)
      expect(DeviceState).to receive(:new).and_return(device_state)
      expect(device_state).to receive(:touch_device_location_off_file)
      expect(Utils).to receive(:disable_settings_app).and_return(true)
      expect(DeviceSettingsUtil).to receive(:foreground_user_app).with(device, params)
      DeviceSettingsUtil.update_settings(device, params)
    end

    it "should not touch file for off if WDA error" do
      params = {
        'automate_session_id' => 'session_id',
        'global_setting_name' => 'Privacy & Security',
        'value' => 'OFF',
        'app_display_name' => 'sample_app',
        'app_testing_bundle_id' => 'sample.app.com',
        'special_post_action' => 'postLocationTurnOff',
        'device_settings_DSL' => {
          'Location Services' => {
            'type' => 'PSChildPaneSpecifier',
            'value' => {
              'Location Services' => {
                'type' => 'PSToggleSwitchSpecifier',
                'value' => 'OFF'
              }
            }
          }
        }
      }

      expected_request_body = {
        "session_id" => params['automate_session_id'],
        "global_setting_name" => params['global_setting_name'],
        'special_post_action' => params['special_post_action'],
        "DSL" => params['device_settings_DSL']
      }

      expect(Utils).to receive(:enable_settings_app)
      expect(DeviceManager).to receive(:device_configuration_check).with(device).and_return(device_config)
      allow(WdaClient).to receive(:new).and_return(wda_client)
      response = { "value" => { "status" => "error", "error" => { "message" => "something unexpected" } } }
      expect(wda_client).to receive(:make_request).with('POST', '/bs/updateSettings', expected_request_body).and_return(response)
      expect(device_state).not_to receive(:touch_device_location_off_file)
      expect(Utils).to receive(:disable_settings_app).and_return(true)
      expect(DeviceSettingsUtil).to receive(:foreground_user_app).with(device, params)
      expect { DeviceSettingsUtil.update_settings(device, params) }.to raise_error(DeviceSettingsError, "something unexpected")
    end

    it "should not remove file for on if WDA error" do
      params = {
        'automate_session_id' => 'session_id',
        'global_setting_name' => 'Privacy & Security',
        'value' => 'ON',
        'app_display_name' => 'sample_app',
        'app_testing_bundle_id' => 'sample.app.com',
        'device_settings_DSL' => {
          'Location Services' => {
            'type' => 'PSChildPaneSpecifier',
            'value' => {
              'Location Services' => {
                'type' => 'PSToggleSwitchSpecifier',
                'value' => 'ON'
              }
            }
          }
        }
      }

      expected_request_body = {
        "session_id" => params['automate_session_id'],
        "global_setting_name" => params['global_setting_name'],
        "DSL" => params['device_settings_DSL']
      }

      expect(Utils).to receive(:enable_settings_app)
      expect(DeviceManager).to receive(:device_configuration_check).with(device).and_return(device_config)
      allow(WdaClient).to receive(:new).and_return(wda_client)
      response = { "value" => { "status" => "error", "error" => { "message" => "something unexpected" } } }
      expect(wda_client).to receive(:make_request).with('POST', '/bs/updateSettings', expected_request_body).and_return(response)
      expect(device_state).not_to receive(:remove_device_location_off_file)
      expect(Utils).to receive(:disable_settings_app).and_return(true)
      expect(DeviceSettingsUtil).to receive(:foreground_user_app).with(device, params)

      expect { DeviceSettingsUtil.update_settings(device, params) }.to raise_error(DeviceSettingsError, "something unexpected")
    end
  end
end