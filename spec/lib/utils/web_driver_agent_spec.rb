require_relative '../../spec_helper'
require_relative '../../../lib/utils/idevice_utils'
require_relative '../../../lib/utils/utils'
require_relative '../../../lib/utils/web_driver_agent'
require_relative '../../../lib/configuration'
require_relative '../../../server/iphone'

describe BrowserStack::WebDriverAgent do
  context "launch" do
    let(:device) { 'test_device' }
    let(:mini_ip) { 'localhost' }
    let(:session_id) { '19832e80ae1b423a4a552510069f2c771d99f1bb' }
    let(:genre) { "live_testing" }
    let(:params) do
      {
        "device" => device,
        "iproxyPort " => "8500",
        "genre" => genre,
        "component" => "server.rb",
        "device_browser" => "safari",
        "url" => "https://google.com",
        "live_session_id" => session_id,
        "version" => "iPad Mini 4-11.0"
      }
    end
    let(:wda_params) do
      {
        :allow_long_press => "false",
        "browser_bundle_id" => "com.apple.mobilesafari",
        :cls_servers => [{ "host" => "localhost", "port" => 1234, "type" => "CLS" }],
        "iproxyPort" => 200,
        "mini_ip" => "localhost",
        "set_rtcdata_request_count" => 1
      }
    end

    before(:each) do
      allow(File).to receive(:read).and_return(JSON.dump({ "iproxyPort": 200 }))
      allow_any_instance_of(BrowserStack::Configuration).to receive(:all).and_return({
        'cls_url' => 'localhost',
        'cls_port' => 1234
      })
      allow(BrowserStack::IPhone).to receive(:session_file).and_return('/tmp/location')
      allow(Utils).to receive(:write_to_file).and_return(nil)
    end

    context "live_testing" do
      let(:genre) { "live_testing" }

      it "should send log to eds for wda launch browser in fork" do
        allow_any_instance_of(BrowserStack::MethodInterceptor::WdaClientInterceptor).to receive(:set_rtc_data_and_start).and_return(nil)
        expect(Utils).to receive(:fork_code_block_for_device).with(device)

        expect(BrowserStack::WebDriverAgent).to receive(:send_launch_log_to_eds).and_return(nil)
        BrowserStack::WebDriverAgent.launch(device, mini_ip, params)
      end
    end

    context "app_live_testing" do
      let(:genre) { "app_live_testing" }

      it "should send log to eds for wda launch" do
        allow_any_instance_of(BrowserStack::MethodInterceptor::WdaClientInterceptor).to receive(:set_rtc_data_and_start).and_return(nil)

        expect(BrowserStack::WebDriverAgent).to receive(:send_launch_log_to_eds).and_return(nil)
        BrowserStack::WebDriverAgent.launch(device, mini_ip, params)
      end
    end
  end
end
