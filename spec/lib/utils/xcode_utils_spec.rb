require_relative '../../../lib/utils/xcode_utils'
require_relative '../../spec_helper'

describe BrowserStack::XcodeUtils do
  describe '.create_xcconfig_file' do
    # TODO: improve this spec - not quite sure what this method is supposed to do
    it 'creates xcconfig file' do
      temp_xcconfig_file = Tempfile.new(['abcd', 'xcconfig'], "/tmp")
      expect { BrowserStack::XcodeUtils.create_xcconfig_file({}, temp_xcconfig_file.path) }
        .not_to raise_error
      temp_xcconfig_file.close
    end
  end

  describe '.get_platform_version' do
    it 'runs xcodebuild shell command' do
      expect(BrowserStack::XcodeUtils).to receive(:`).with(/xcodebuild -showsdks/).and_return('')
      BrowserStack::XcodeUtils.get_platform_version
    end
  end
end
