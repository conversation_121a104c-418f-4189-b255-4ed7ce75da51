require_relative '../../../lib/utils/mobile_tampered_api'

describe BrowserStack::MobileTamperedAPI do
  subject { BrowserStack::MobileTamperedAPI }

  describe '.ban_last_user!' do
    before do
      # In case the test misfires, you don't want to call the API by accident
      allow(BrowserStack::HttpUtils).to receive(:get_response)
    end

    context 'in staging' do
      it "doesn't send a get request" do
        # host is debugstack.com in this file
        session_file = "#{__dir__}/../../fixtures/session_file_staging"
        expect(BrowserStack::HttpUtils).not_to receive(:get_response)
        subject.ban_last_user!(session_file)
      end
    end

    context 'in production' do
      it 'sends a get request with the expected parameters' do
        session_file = "#{__dir__}/../../fixtures/session_file_production"

        expected_url = 'https://live.browserstack.com/main/mobile_tampered'
        expected_url << '?'
        expected_url << 'auth=mobile_tampered_key_auth&'
        expected_url << 'user_id=6666666&'
        expected_url << 'instance_id=ABCDEFG&'
        expected_url << 'session_id=33333333333333333333333333333333&'
        expected_url << 'genre=live_testing'

        expect(BrowserStack::HttpUtils).to receive(:get_response).with(expected_url)

        subject.ban_last_user!(session_file)
      end
    end
  end
end
