require_relative '../../spec_helper'
require_relative '../../../lib/utils/image_injector'
require_relative '../../../lib/utils/osutils'
require_relative '../../../config/constants'
require_relative '../../../server/device_manager'

describe BrowserStack::ImageInjector do
  let(:device) { 'test_device' }

  context "inject_image" do
    let(:params) do
      {
        session_id: 'some_session_id',
        file_url: 'some_file_url',
        media_hashed_id: 'some_hashed_id',
        format: '.jpg',
        bundle_id: 'some_bundle_id'
      }
    end

    it 'should not download the image from s3 and dont push it in app if the product is not app automate' do
      params[:product] = 'automate'

      expect(BrowserStack::ImageInjector).to_not receive(:get_injection_media_dir)
      expect(FileUtils).to_not receive(:mkdir_p)
      expect(File).to_not receive(:exist?)
      expect(BrowserStack::HttpUtils).to_not receive(:download)
      expect(BrowserStack::ImageInjector).to_not receive(:convert_image_to_png)
      expect(BrowserStack::ImageInjector).to_not receive(:insert_media_in_app)

      expect { BrowserStack::ImageInjector.send(:inject_media, device, params) }.not_to raise_error
    end

    it 'should download the image from s3 and push it in app' do
      params[:product] = 'app_automate'

      expect(BrowserStack::ImageInjector).to receive(:get_injection_media_dir).and_return("/tmp/injection_media/test_device")
      expect(FileUtils).to receive(:mkdir_p).with("/tmp/injection_media/test_device")
      expect(File).to receive(:exist?).with("/tmp/injection_media/test_device/some_hashed_id.png").and_return(false)
      expect(BrowserStack::HttpUtils).to receive(:download).with('some_file_url', '/tmp/injection_media/test_device/some_hashed_id.jpg', { retry_count: 3, timeout: 20 }, "camera_injection_image")
      expect(BrowserStack::ImageInjector).to receive(:convert_image_to_png).with('/tmp/injection_media/test_device/some_hashed_id.jpg', '/tmp/injection_media/test_device/some_hashed_id.png')
      expect(File).to_not receive(:write)
      expect(BrowserStack::ImageInjector).to receive(:fetch_bundle_id_from_session_file).and_return(params[:bundle_id])
      expect(BrowserStack::ImageInjector).to receive(:insert_media_in_app).with('test_device', 'some_bundle_id', '/tmp/injection_media/test_device/some_hashed_id.png', false)

      expect { BrowserStack::ImageInjector.send(:inject_media, device, params) }.not_to raise_error
    end

    it 'should download the image from s3 and push it in video injection' do
      params[:product] = 'app_automate'
      params[:format] = '.mp4'

      expect(BrowserStack::ImageInjector).to receive(:get_injection_media_dir).and_return("/tmp/injection_media/test_device")
      expect(FileUtils).to receive(:mkdir_p).with("/tmp/injection_media/test_device")
      expect(File).to receive(:exist?).with("/tmp/injection_media/test_device/some_hashed_id.mp4").and_return(false)
      expect(BrowserStack::HttpUtils).to receive(:download).with('some_file_url', '/tmp/injection_media/test_device/some_hashed_id.mp4', { retry_count: 3, timeout: 20 }, "camera_injection_image")
      expect(File).to_not receive(:write)
      expect(BrowserStack::ImageInjector).to receive(:fetch_bundle_id_from_session_file).and_return(params[:bundle_id])
      expect(BrowserStack::ImageInjector).to receive(:insert_media_in_app).with('test_device', 'some_bundle_id', '/tmp/injection_media/test_device/some_hashed_id.mp4', true)

      expect { BrowserStack::ImageInjector.send(:inject_media, device, params) }.not_to raise_error
    end

    it 'should download the image from s3 and push it in app if product is app live' do
      params[:product] = 'app_live'

      expect(BrowserStack::ImageInjector).to receive(:get_injection_media_dir).and_return("/tmp/injection_media/test_device")
      expect(FileUtils).to receive(:mkdir_p).with("/tmp/injection_media/test_device")
      expect(File).to receive(:exist?).with("/tmp/injection_media/test_device/some_hashed_id.png").and_return(false)
      expect(BrowserStack::HttpUtils).to receive(:download).with('some_file_url', '/tmp/injection_media/test_device/some_hashed_id.jpg', { retry_count: 3, timeout: 20 }, "camera_injection_image")
      expect(BrowserStack::ImageInjector).to receive(:convert_image_to_png).with('/tmp/injection_media/test_device/some_hashed_id.jpg', '/tmp/injection_media/test_device/some_hashed_id.png')
      expect(File).to_not receive(:write)
      expect(BrowserStack::ImageInjector).to receive(:insert_media_in_app).with('test_device', 'some_bundle_id', '/tmp/injection_media/test_device/some_hashed_id.png', false)

      expect { BrowserStack::ImageInjector.send(:inject_media, device, params, 'some_bundle_id') }.not_to raise_error
    end

    it 'should raise error if error is raised while pushing image in app' do
      params[:product] = 'app_automate'

      expect(BrowserStack::ImageInjector).to receive(:get_injection_media_dir).and_return("/tmp/injection_media/test_device")
      expect(FileUtils).to receive(:mkdir_p).with("/tmp/injection_media/test_device")
      expect(File).to receive(:exist?).with("/tmp/injection_media/test_device/some_hashed_id.png").and_return(false)
      expect(BrowserStack::HttpUtils).to receive(:download).with('some_file_url', '/tmp/injection_media/test_device/some_hashed_id.jpg', { retry_count: 3, timeout: 20 }, "camera_injection_image")
      expect(BrowserStack::ImageInjector).to receive(:convert_image_to_png).with('/tmp/injection_media/test_device/some_hashed_id.jpg', '/tmp/injection_media/test_device/some_hashed_id.png')
      expect(File).to_not receive(:write)
      expect(BrowserStack::ImageInjector).to receive(:fetch_bundle_id_from_session_file).and_return(params[:bundle_id])
      expect(BrowserStack::ImageInjector).to receive(:insert_media_in_app).with('test_device', 'some_bundle_id', '/tmp/injection_media/test_device/some_hashed_id.png', false).and_raise("some_error")

      expect { BrowserStack::ImageInjector.send(:inject_media, device, params) }.to raise_error("some_error")
    end

    it 'should raise error if error is raised while converting the image' do
      params[:product] = 'app_automate'

      expect(BrowserStack::ImageInjector).to receive(:get_injection_media_dir).and_return("/tmp/injection_media/test_device")
      expect(FileUtils).to receive(:mkdir_p).with("/tmp/injection_media/test_device")
      expect(File).to receive(:exist?).with("/tmp/injection_media/test_device/some_hashed_id.png").and_return(false)
      expect(BrowserStack::HttpUtils).to receive(:download).with('some_file_url', '/tmp/injection_media/test_device/some_hashed_id.jpg', { retry_count: 3, timeout: 20 }, "camera_injection_image")
      expect(BrowserStack::ImageInjector).to receive(:convert_image_to_png).with('/tmp/injection_media/test_device/some_hashed_id.jpg', '/tmp/injection_media/test_device/some_hashed_id.png').and_raise("some_error")
      expect(FileUtils).to_not receive(:rm_rf)
      expect(BrowserStack::ImageInjector).to receive(:fetch_bundle_id_from_session_file).and_return(params[:bundle_id])
      expect(File).to_not receive(:write)
      expect(BrowserStack::ImageInjector).to_not receive(:insert_media_in_app)

      expect { BrowserStack::ImageInjector.send(:inject_media, device, params) }.to raise_error("some_error")
    end

    it 'should raise error if error is raised while downloading the image' do
      params[:product] = 'app_automate'

      expect(BrowserStack::ImageInjector).to receive(:get_injection_media_dir).and_return("/tmp/injection_media/test_device")
      expect(FileUtils).to receive(:mkdir_p).with("/tmp/injection_media/test_device")
      expect(File).to receive(:exist?).with("/tmp/injection_media/test_device/some_hashed_id.png").and_return(false)
      expect(BrowserStack::HttpUtils).to receive(:download).with('some_file_url', '/tmp/injection_media/test_device/some_hashed_id.jpg', { retry_count: 3, timeout: 20 }, "camera_injection_image").and_raise("some_error")
      expect(BrowserStack::ImageInjector).to_not receive(:convert_image_to_png)
      expect(FileUtils).to_not receive(:rm_rf)
      expect(BrowserStack::ImageInjector).to receive(:fetch_bundle_id_from_session_file).and_return(params[:bundle_id])
      expect(File).to_not receive(:write)
      expect(BrowserStack::ImageInjector).to_not receive(:insert_media_in_app)

      expect { BrowserStack::ImageInjector.send(:inject_media, device, params) }.to raise_error("some_error")
    end
  end

  context "insert_injection_image_in_app" do
    let(:bundle_id) { 'some_bundle_id' }
    let(:file_path) { '/tmp/injection_media/test_device/some_file_name.jpg' }

    it 'should push the image inside app' do
      expect(BrowserStack::ImageInjector).to receive(:get_delete_media_command).twice.and_return("some_command_to_delete")
      expect(BrowserStack::ImageInjector).to receive(:get_media_inject_command).and_return("some_command")
      expect(BrowserStack::OSUtils).to receive(:execute).with("some_command_to_delete", true, timeout: 30).exactly(2).and_return(["successfully deleted", 0])
      expect(BrowserStack::OSUtils).to receive(:execute).with("some_command", true, timeout: 30).and_return(["successfully injected", 0])

      BrowserStack::ImageInjector.send(:insert_media_in_app, device, bundle_id, file_path)
    end

    it 'should throw exception if status code is non zero while pushing image inside app' do
      expect(BrowserStack::ImageInjector).to receive(:get_delete_media_command).exactly(6).and_return("some_command_to_delete")
      expect(BrowserStack::ImageInjector).to receive(:get_media_inject_command).exactly(3).and_return("some_command")
      expect(BrowserStack::OSUtils).to receive(:execute).with("some_command_to_delete", true, timeout: 30).exactly(6).and_return(["successfully deleted", 0])
      expect(BrowserStack::OSUtils).to receive(:execute).with("some_command", true, timeout: 30).exactly(3).and_return(["successfully injected", 124])

      expect { BrowserStack::ImageInjector.send(:insert_media_in_app, device, bundle_id, file_path) }.to raise_error("Failed to push image to device test_device")
    end
  end

  context "get_injection_image_dir" do
    it "should return the injection image dir" do
      expect(BrowserStack::ImageInjector.send(:get_injection_media_dir, device)).to eql("/tmp/injection_media/test_device")
    end
  end

  context "convert_image_to_png" do
    let(:input_file_path_png) { 'input_file.png' }
    let(:input_file_path_jpg) { 'input_file.jpg' }
    let(:output_file_path) { 'output_file.png' }

    it "should raise error if trying to convert input png to png" do
      expect(File).to receive(:readable?).and_return(true)
      expect(BrowserStack::OSUtils).not_to receive(:execute)
      expect(BrowserStack::ImageInjector).not_to receive(:get_file_convert_command)

      expect { BrowserStack::ImageInjector.convert_image_to_png(input_file_path_png, output_file_path) }.to raise_error("Downloaded image file was not readable or trying to convert downloaded png")
    end

    it "should convert the image to png if extension is not png" do
      expect(File).to receive(:readable?).and_return(true)
      expect(FileUtils).not_to receive(:copy_file)
      expect(BrowserStack::ImageInjector).to receive(:get_file_convert_command).and_return("some_command")
      expect(BrowserStack::OSUtils).to receive(:execute).with("some_command", true, timeout: 15).and_return(["successfully converted", 0])

      expect { BrowserStack::ImageInjector.convert_image_to_png(input_file_path_jpg, output_file_path) }.not_to raise_error
    end

    it "should raise error if error happens while converting the image to png" do
      expect(File).to receive(:readable?).and_return(true)
      expect(FileUtils).not_to receive(:copy_file)
      expect(BrowserStack::ImageInjector).to receive(:get_file_convert_command).and_return("some_command")
      expect(BrowserStack::OSUtils).to receive(:execute).with("some_command", true, timeout: 15).and_return(["conversion failed", 124])

      expect { BrowserStack::ImageInjector.convert_image_to_png(input_file_path_jpg, output_file_path) }.to raise_error("Failed to convert image to png")
    end

    it "should raise error if input file is not readable" do
      expect(File).to receive(:readable?).and_return(false)
      expect(FileUtils).not_to receive(:copy_file)
      expect(BrowserStack::ImageInjector).not_to receive(:get_file_convert_command)
      expect(BrowserStack::OSUtils).not_to receive(:execute)

      expect { BrowserStack::ImageInjector.convert_image_to_png(input_file_path_jpg, output_file_path) }.to raise_error("Downloaded image file was not readable or trying to convert downloaded png")
    end
  end

  context 'cleanup' do
    it 'should delete the injection dir' do
      expect(BrowserStack::ImageInjector).to receive(:get_injection_media_dir)
      expect(FileUtils).to receive(:rm_rf)

      expect { BrowserStack::ImageInjector.send(:cleanup, device) }.not_to raise_error
    end
  end

  context 'get_image_inject_command' do
    it 'should return the command for image injection' do
      expect(File).to receive(:executable?).and_return(true)

      expect(BrowserStack::ImageInjector.send(:get_media_inject_command, device, 'some_bundle_id', 'some_file_path.png')).to eql("#{IOS_DEPLOY} --id test_device --bundle_id \"some_bundle_id\" --upload \"some_file_path.png\" --to \"Documents/injectionImage.png\" 2>&1")
    end

    it 'should return the command for video injection' do
      expect(File).to receive(:executable?).and_return(true)
      expect(BrowserStack::ImageInjector.send(:get_media_inject_command, device, 'some_bundle_id', 'some_file_path.mp4', true)).to eql("#{IOS_DEPLOY} --id test_device --bundle_id \"some_bundle_id\" --upload \"some_file_path.mp4\" --to \"Documents/browserstack/video/injectionVideo.mp4\" 2>&1")
    end

    it 'should raise error if ios-deploy not found or not executable' do
      expect(File).to receive(:executable?).and_return(false)

      expect { BrowserStack::ImageInjector.send(:get_media_inject_command, device, 'some_bundle_id', 'some_file_path.png') }.to raise_error("ios deploy v1.11 not found or is not executable")
    end
  end

  context 'get_file_convert_command' do
    it 'should return the command for image conversion to png' do
      expect(BrowserStack::ImageInjector.send(:get_file_convert_command, 'input_file.jpg', 'output_file.png')).to eql("mv \"input_file.jpg\" \"output_file.png\" 2>&1")
    end
  end

  context 'fetch_bundle_id_from_session_file' do
    let(:session_id) { "session_id" }
    let(:device) { "device_id" }

    it "should return bundle id when fetch_bundle_id_from_session_file is called" do
      expect(DeviceManager).to receive(:session_file).at_least(:once)
      expect(File).to receive(:exist?).and_return(true)
      expect(File).to receive(:read).at_least(:once).and_return({ "app_testing_bundle_id": "bundle_id", 'automate_session_id' => session_id }.to_json)

      expect(BrowserStack::ImageInjector.send(:fetch_bundle_id_from_session_file, device, session_id)).to eq("bundle_id")
    end

    it "should return empty bundle id when session_file is missing" do
      expect(DeviceManager).to receive(:session_file).at_least(:once)
      expect(File).to receive(:exist?).at_least(:once).and_return(false)

      expect(BrowserStack::ImageInjector.send(:fetch_bundle_id_from_session_file, device, session_id)).to eq("")
    end

    it "should return empty bundle id when session_id does not match with sesion_file is called" do
      expect(DeviceManager).to receive(:session_file).at_least(:once)
      expect(File).to receive(:exist?).at_least(:once).and_return(true)
      expect(File).to receive(:read).and_return({ "app_testing_bundle_id": "bundle_id", 'automate_session_id' => "session1" }.to_json)

      expect(BrowserStack::ImageInjector.send(:fetch_bundle_id_from_session_file, device, session_id)).to eq("")
    end
  end
end
