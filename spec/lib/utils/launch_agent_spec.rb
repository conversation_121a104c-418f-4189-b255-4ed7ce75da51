require_relative '../../../lib/utils/launch_agent'

RSpec.describe LaunchAgent do
  let(:fixtures_dir) { "#{Dir.getwd}/spec/fixtures/launch_agent" }
  let(:launch_daemons_dir) { "#{Dir.getwd}/spec/tmp/LaunchDaemons" }
  let(:launch_agents_dir) { "#{Dir.getwd}/spec/tmp/LaunchAgents" }
  before(:each) do
    FileUtils.mkdir_p(launch_daemons_dir)
    FileUtils.mkdir_p(launch_agents_dir)
    BrowserStack::CheckPlist.configure(
      "#{Dir.getwd}/templates",
      launch_daemons_dir,
      launch_agents_dir
    )
    allow(Utils).to receive(:write_as_root) do |filename, data|
      File.write(filename, data)
    end
    allow(BrowserStack::Configuration).to receive(:[]).with('logging_root').and_return('/var/log/browserstack')
    allow(BrowserStack::Configuration).to receive(:[]).with('user').and_return('app')
  end
  after(:each) do
    FileUtils.remove_entry(launch_agents_dir)
    FileUtils.remove_entry(launch_daemons_dir)
  end

  let(:expected_plist_data) { File.read("#{fixtures_dir}/expected_launch_agent.plist") }
  let(:launch_agent) do
    LaunchAgent.new(
      svc_name: 'example_program',
      cmd: ['run_command', 'arg1', 'arg2'],
      append_path_env: '/custom/path',
      prepend_path_env: '/custom/prepend/path',
      custom_config: { 'WorkingDirectory' => '/my/working/dir' },
      start_calendar_interval: { 'Minute' => 60 },
      keep_alive: false,
      session_create: true,
      set_lang_and_term: true,
      env_vars: { APPIUM_HOME: '/path/to/appium/home' }
    )
  end

  describe '#update' do
    it 'creates plist file for LaunchAgent and loads the service' do
      expect(BrowserStack::CheckPlist).to receive(:load_service).with('example_program', ServiceType.UserService, 'app', 0)
      expect(BrowserStack::OSUtils).to receive(:is_plist_loaded?).with('example_program', ServiceType.UserService).and_return(true)

      launch_agent.update

      expect(File.read("#{launch_agents_dir}/example_program.plist")).to eq(expected_plist_data)
    end
  end
end