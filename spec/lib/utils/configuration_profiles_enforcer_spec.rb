require_relative '../../../lib/utils/configuration_profiles_enforcer'
require 'spec_helper'

describe ConfigurationProfilesEnforcer do
  let(:device_id) { 'test_device' }
  let(:mock_data_report_helper) { double(DataReportHelper).as_null_object }
  let(:mock_configuration_profiles_manager) { double(ConfigurationProfilesManager).as_null_object }
  let(:mock_device_state) { double(DeviceState).as_null_object }
  let(:mock_server_config) { double(BrowserStack::Configuration) }

  let(:subject) { ConfigurationProfilesEnforcer.new(device_id, BrowserStack.logger) }

  before(:each) do
    allow(DataReportHelper).to receive(:new).and_return(mock_data_report_helper)
    allow(ConfigurationProfilesManager).to receive(:new).and_return(mock_configuration_profiles_manager)
    allow(DeviceState).to receive(:new).and_return(mock_device_state)
    allow(BrowserStack::Configuration).to receive(:new).and_return(mock_server_config)
    allow(mock_server_config).to receive(:all).and_return({})

    allow(File).to receive(:file?).and_return(true)
    allow(BrowserStack::IosMdmServiceClient).to receive(:check_device_on_mdm).and_return(true)
    allow(CustomMDMManager).to receive(:is_custom_mdm_device?).and_return(false)
  end

  describe '#check_and_enforce_configuration_profiles' do
    context 'when force install is false, profile check due is false' do
      it 'should return early' do
        expect(BrowserStack::IosMdmServiceClient).not_to receive(:check_device_on_mdm)
        expect(subject).not_to receive(:parse_profiles)
        expect(subject).not_to receive(:profiles_all_good?)
        expect(subject).not_to receive(:apply_profiles)
        expect(mock_device_state).not_to receive(:remove_force_install_mdm_profiles_file)
        expect(mock_device_state).not_to receive(:touch_configuration_profile_periodic_check_file)
        expect(subject).to receive(:check_and_report_data).with(hash_including({
          "force_install" => false,
          "profile_check_due" => false,
          "error" => nil
        }))

        subject.enforce_configuration_profiles(false, false)
      end
    end

    context 'when force install is false, profile check due is true' do
      context 'when profiles are all good' do
        before(:each) do
          allow(subject).to receive(:profiles_all_good?).and_return(true)
        end

        it 'should return early if there are no profiles to be installed or removed' do
          expect(subject).to receive(:parse_profiles).and_return({})
          expect(subject).not_to receive(:apply_profiles)
          expect(mock_device_state).not_to receive(:remove_force_install_mdm_profiles_file)
          expect(mock_device_state).to receive(:touch_configuration_profile_periodic_check_file)
          expect(subject).to receive(:check_and_report_data).with(hash_including({
            "force_install" => false,
            "profile_check_due" => true,
            "error" => nil
          }))

          subject.enforce_configuration_profiles(false, true)
        end

        it 'should raise error when error is raised' do
          expect(subject).to receive(:parse_profiles).and_raise("Something went wrong")
          expect(subject).not_to receive(:apply_profiles)
          expect(mock_device_state).not_to receive(:remove_force_install_mdm_profiles_file)
          expect(mock_device_state).not_to receive(:touch_configuration_profile_periodic_check_file)
          expect(subject).to receive(:check_and_report_data).with(hash_including({
            "force_install" => false,
            "profile_check_due" => true,
            "error" => "Something went wrong"
          }))

          expect { subject.enforce_configuration_profiles(false, true) }.to raise_error("Something went wrong")
        end
      end

      context 'when profiles are not good' do
        before(:each) do
          allow(subject).to receive(:profiles_all_good?).and_return(false, true)
        end

        it 'should install / remove pending profiles' do
          expect(subject).to receive(:parse_profiles).and_return({}, {})
          expect(subject).to receive(:apply_profiles).and_return(true)
          expect(mock_device_state).not_to receive(:remove_force_install_mdm_profiles_file)
          expect(mock_device_state).to receive(:touch_configuration_profile_periodic_check_file)
          expect(subject).to receive(:check_and_report_data).with(hash_including({
            "force_install" => false,
            "profile_check_due" => true,
            "error" => nil
          }))

          subject.enforce_configuration_profiles(false, true)
        end

        it 'should raise error when error is raised' do
          expect(subject).to receive(:parse_profiles).and_return({})
          expect(subject).to receive(:apply_profiles).and_raise("Something went wrong")
          expect(mock_device_state).not_to receive(:remove_force_install_mdm_profiles_file)
          expect(mock_device_state).not_to receive(:touch_configuration_profile_periodic_check_file)
          expect(subject).to receive(:check_and_report_data).with(hash_including({
            "force_install" => false,
            "profile_check_due" => true,
            "error" => "Something went wrong"
          }))

          expect { subject.enforce_configuration_profiles(false, true) }.to raise_error("Something went wrong")
        end
      end
    end

    context 'when force install is true, profile check due is true / false' do
      context 'when profiles are all good' do
        before(:each) do
          allow(subject).to receive(:profiles_all_good?).and_return(true)
        end

        it 'should re-install all profiles' do
          expect(subject).to receive(:parse_profiles).and_return({}, {})
          expect(subject).to receive(:apply_profiles).and_return(true)
          expect(mock_device_state).to receive(:remove_force_install_mdm_profiles_file)
          expect(mock_device_state).to receive(:touch_configuration_profile_periodic_check_file)
          expect(subject).to receive(:check_and_report_data).with(hash_including({
            "force_install" => true,
            "profile_check_due" => false,
            "error" => nil
          }))

          subject.enforce_configuration_profiles(true)
        end

        it 'should raise error when error is raised' do
          expect(subject).to receive(:parse_profiles).and_return({})
          expect(subject).to receive(:apply_profiles).and_raise("Something went wrong")
          expect(mock_device_state).not_to receive(:remove_force_install_mdm_profiles_file)
          expect(mock_device_state).not_to receive(:touch_configuration_profile_periodic_check_file)
          expect(subject).to receive(:check_and_report_data).with(hash_including({
            "force_install" => true,
            "profile_check_due" => false,
            "error" => "Something went wrong"
          }))

          expect { subject.enforce_configuration_profiles(true) }.to raise_error("Something went wrong")
        end
      end

      context 'when profiles are not good' do
        before(:each) do
          allow(subject).to receive(:profiles_all_good?).and_return(false, true)
        end

        it 'should remove pending profiles and re-install all profiles' do
          expect(subject).to receive(:parse_profiles).and_return({}, {})
          expect(subject).to receive(:apply_profiles).and_return(true)
          expect(mock_device_state).to receive(:remove_force_install_mdm_profiles_file)
          expect(mock_device_state).to receive(:touch_configuration_profile_periodic_check_file)
          expect(subject).to receive(:check_and_report_data).with(hash_including({
            "force_install" => true,
            "profile_check_due" => false,
            "error" => nil
          }))

          subject.enforce_configuration_profiles(true)
        end

        it 'should raise error when error is raised' do
          expect(subject).to receive(:parse_profiles).and_return({})
          expect(subject).to receive(:apply_profiles).and_raise("Something went wrong")
          expect(mock_device_state).not_to receive(:remove_force_install_mdm_profiles_file)
          expect(mock_device_state).not_to receive(:touch_configuration_profile_periodic_check_file)
          expect(subject).to receive(:check_and_report_data).with(hash_including({
            "force_install" => true,
            "profile_check_due" => false,
            "error" => "Something went wrong"
          }))

          expect { subject.enforce_configuration_profiles(true) }.to raise_error("Something went wrong")
        end
      end
    end
  end
end