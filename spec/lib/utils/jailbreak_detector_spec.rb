require_relative '../../../lib/utils/jailbreak_detector'

describe BrowserStack::JailbreakDetector do
  subject { BrowserStack::JailbreakDetector }

  describe '.check_jailbroken' do
    context 'when no jailbreaking apps are installed' do
      before do
        apps = [
          { bundle_id: "com.apple.unconspicuous", version: "1.0", display_name: "Unconspicuous" },
          { bundle_id: "org.coolstar.notelectra", version: "1", display_name: "Not Electra" },
          { bundle_id: "com.google.totallysafe", version: "25", display_name: "TotallySafe" }
        ]
        allow(IdeviceUtils).to receive(:list_apps).and_return(apps)
      end

      it 'returns nil' do
        expect(subject.check_jailbroken('c309b575245f737ae825c7a54be492bd24b4dca7')).to be_nil
      end
    end

    context 'when a jailbreaking app is installed' do
      before do
        apps = [
          { bundle_id: "com.apple.unconspicuous", version: "1.0", display_name: "Unconspicuous" },
          { bundle_id: "org.coolstar.electra", version: "1", display_name: "Electra" },
          { bundle_id: "com.google.totallysafe", version: "25", display_name: "TotallySafe" }
        ]
        allow(IdeviceUtils).to receive(:list_apps).and_return(apps)
      end

      it 'returns the name of the app' do
        expect(subject.check_jailbroken('c309b575245f737ae825c7a54be492bd24b4dca7'))
          .to be_eql('org.coolstar.electra')
      end
    end

    context 'when a jailbreaking app is installed but the case differs' do
      before do
        apps = [
          { bundle_id: "com.apple.unconspicuous", version: "1.0", display_name: "Unconspicuous" },
          { bundle_id: "org.CooLStAr.eleCtra1141", version: "1", display_name: "ElectrA" },
          { bundle_id: "com.google.totallysafe", version: "25", display_name: "TotallySafe" }
        ]
        allow(IdeviceUtils).to receive(:list_apps).and_return(apps)
      end

      it 'does case insensitive comparison and still returns the name of the app' do
        expect(subject.check_jailbroken('c309b575245f737ae825c7a54be492bd24b4dca7'))
          .to be_eql('org.coolstar.electra1141')
      end
    end

    context 'when a jailbreaking app is installed the whole bundle id is returned' do
      before do
        apps = [
          { bundle_id: "com.apple.unconspicuous", version: "1.0", display_name: "Unconspicuous" },
          { bundle_id: "org.coolstar.electra1141", version: "1", display_name: "Electra" },
          { bundle_id: "com.google.totallysafe", version: "25", display_name: "TotallySafe" }
        ]
        allow(IdeviceUtils).to receive(:list_apps).and_return(apps)
      end

      it 'still returns the name of the app' do
        expect(subject.check_jailbroken('c309b575245f737ae825c7a54be492bd24b4dca7'))
          .to be_eql('org.coolstar.electra1141')
      end
    end
  end
end
