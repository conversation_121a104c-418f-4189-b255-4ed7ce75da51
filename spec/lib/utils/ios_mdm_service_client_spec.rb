require_relative '../../spec_helper'
require_relative '../../../lib/utils/ios_mdm_service_client'

require 'time'

describe BrowserStack::IosMdmServiceClient do
  let(:device) { "device123456" }
  let(:serial) { "123456" }
  let(:http_response) { double("http_response") }
  context '.check_device_on_mdm(device)' do
    before do
      allow(BrowserStack::HttpUtils).to receive(:send_post).and_return(http_response)
      allow(http_response).to receive(:code).and_return(200)
    end

    it 'raises an error if device is not on mdm' do
      sample_mdm_response = "{ \"devices\":[] }"
      allow(http_response).to receive(:body).and_return(sample_mdm_response)
      allow(BrowserStack::IosMdmServiceClient).to receive(:device_config).and_return({ "device_serial" => serial })
      expect { BrowserStack::IosMdmServiceClient.check_device_on_mdm(device) }.to raise_error(MdmApiException, "Device not on mdm")
    end

    it 'raises an error if device is on mdm but enroll status is false' do
      sample_mdm_response = "{ \"devices\":[{ \"serial_number\": \"123456\",
                                              \"udid\": \"device123456\",
                                              \"enrollment_status\": false,
                                              \"last_seen\": \"2018-10-18T15:36:41.944238063Z\" }] }"
      allow(http_response).to receive(:body).and_return(sample_mdm_response)
      allow(BrowserStack::IosMdmServiceClient).to receive(:device_config).and_return({ "device_serial" => serial })
      expect { BrowserStack::IosMdmServiceClient.check_device_on_mdm(device) }.to raise_error(MdmApiException, "Device on mdm but enrollment_status is false, try re-mdm")
    end

    it 'raises Device not on MDM error if device is on mdm but serial does not match' do
      sample_mdm_response = "{ \"devices\":[{ \"serial_number\": \"123456\",
                                              \"udid\": \"device123456\",
                                              \"enrollment_status\": false,
                                              \"last_seen\": \"2018-10-18T15:36:41.944238063Z\" }] }"
      allow(http_response).to receive(:body).and_return(sample_mdm_response)
      allow(BrowserStack::IosMdmServiceClient).to receive(:device_config).and_return({ "device_serial" => "serial" })
      expect { BrowserStack::IosMdmServiceClient.check_device_on_mdm(device) }.to raise_error(MdmApiException, "Device not on mdm")
    end

    it 'should handle multiple entries for same device by matching both serial number and udid' do
      sample_mdm_response = "{ \"devices\":[{ \"serial_number\": \"123456\",
                                              \"udid\": \"\",
                                              \"enrollment_status\": false,
                                              \"last_seen\": \"2018-10-18T15:36:41.944238063Z\" },
                                            { \"serial_number\": \"123456\",
                                              \"udid\": \"device123456\",
                                              \"enrollment_status\": true,
                                              \"last_seen\": \"2018-10-18T15:36:41.944238063Z\" }] }"
      allow(http_response).to receive(:body).and_return(sample_mdm_response)
      allow(BrowserStack::IosMdmServiceClient).to receive(:device_config).and_return({ "device_serial" => serial })
      expect { BrowserStack::IosMdmServiceClient.check_device_on_mdm(device) }.to_not raise_error
    end

    it 'do not raise an error if device is on mdm' do
      sample_mdm_response = "{ \"devices\":[{ \"serial_number\": \"123456\",
                                              \"udid\": \"device123456\",
                                              \"enrollment_status\": true,
                                              \"last_seen\": \"2018-10-18T15:36:41.944238063Z\" }] }"
      allow(http_response).to receive(:body).and_return(sample_mdm_response)
      allow(BrowserStack::IosMdmServiceClient).to receive(:device_config).and_return({ "device_serial" => serial })
      expect { BrowserStack::IosMdmServiceClient.check_device_on_mdm(device) }.to_not raise_error
    end
  end

  describe '.mdm_last_seen' do
    before do
      allow(BrowserStack::HttpUtils).to receive(:send_post).and_return(http_response)
      allow(http_response).to receive(:code).and_return(200)
    end

    context 'when data from MDM has issues' do
      it 'raises an error if device is not on mdm' do
        sample_mdm_response = "{ \"devices\":[] }"
        allow(http_response).to receive(:body).and_return(sample_mdm_response)
        allow(BrowserStack::IosMdmServiceClient).to receive(:device_config).and_return({ "device_serial" => serial })
        expect { BrowserStack::IosMdmServiceClient.mdm_last_seen(device) }.to raise_error(MdmApiException, "Device not on mdm")
      end

      it 'raises an error if device is on mdm but last seen key is absent' do
        sample_mdm_response = "{ \"devices\":[{ \"serial_number\": \"123456\",
                                                \"udid\": \"device123456\",
                                                \"enrollment_status\": false }] }"
        allow(http_response).to receive(:body).and_return(sample_mdm_response)
        allow(BrowserStack::IosMdmServiceClient).to receive(:device_config).and_return({ "device_serial" => serial })
        expect { BrowserStack::IosMdmServiceClient.mdm_last_seen(device) }.to raise_error(MdmApiException, "Device on mdm but last_seen is empty")
      end

      it 'raises Device not on MDM error if device is on mdm but serial does not match' do
        sample_mdm_response = "{ \"devices\":[{ \"serial_number\": \"123456\",
                                                \"udid\": \"device123456\",
                                                \"enrollment_status\": false,
                                                \"last_seen\": \"2018-10-18T15:36:41.944238063Z\" }] }"
        allow(http_response).to receive(:body).and_return(sample_mdm_response)
        allow(BrowserStack::IosMdmServiceClient).to receive(:device_config).and_return({ "device_serial" => "serial" })
        expect { BrowserStack::IosMdmServiceClient.mdm_last_seen(device) }.to raise_error(MdmApiException, "Device not on mdm")
      end

      it 'should handle multiple entries for same device by matching both serial number and udid' do
        sample_mdm_response = "{ \"devices\":[{ \"serial_number\": \"123456\",
                                                \"udid\": \"\",
                                                \"enrollment_status\": false,
                                                \"last_seen\": \"2018-10-18T15:36:41.944238063Z\" },
                                              { \"serial_number\": \"123456\",
                                                \"udid\": \"device123456\",
                                                \"enrollment_status\": true,
                                                \"last_seen\": \"2018-10-18T15:36:41.944238063Z\" }] }"
        allow(http_response).to receive(:body).and_return(sample_mdm_response)
        allow(BrowserStack::IosMdmServiceClient).to receive(:device_config).and_return({ "device_serial" => serial })
        expect { BrowserStack::IosMdmServiceClient.mdm_last_seen(device) }.to_not raise_error
      end

      it 'do not raise an error if device is on mdm' do
        sample_mdm_response = "{ \"devices\":[{ \"serial_number\": \"123456\",
                                                \"udid\": \"device123456\",
                                                \"enrollment_status\": true,
                                                \"last_seen\": \"2018-10-18T15:36:41.944238063Z\" }] }"
        allow(http_response).to receive(:body).and_return(sample_mdm_response)
        allow(BrowserStack::IosMdmServiceClient).to receive(:device_config).and_return({ "device_serial" => serial })
        expect { BrowserStack::IosMdmServiceClient.mdm_last_seen(device) }.to_not raise_error
      end
    end

    context "when device was MDM'd" do
      it 'returns the last seen' do
        sample_mdm_response = "{ \"devices\":[{ \"serial_number\": \"123456\",
                                                \"udid\": \"device123456\",
                                                \"enrollment_status\": true,
                                                \"last_seen\": \"2018-10-18T15:36:41.944238063Z\" }] }"
        allow(http_response).to receive(:body).and_return(sample_mdm_response)
        allow(BrowserStack::IosMdmServiceClient).to receive(:device_config).and_return({ "device_serial" => serial })
        last_seen = BrowserStack::IosMdmServiceClient.mdm_last_seen(device)
        expect(last_seen).to eq("2018-10-18T15:36:41.944238063Z")
      end
    end
  end

  describe '.get_certificates' do
    it 'makes request with correct payload and parses response' do
      expect(BrowserStack::IosMdmServiceClient).to receive(:make_request).with({ "request_type" => "CertificateList", "udid" => device }, REDIS_CLIENT)
      expect(BrowserStack::IosMdmServiceClient).to receive(:parse_certificates)
      BrowserStack::IosMdmServiceClient.get_certificates(device)
    end

    it 'raises MdmApiException if request fails' do
      allow(BrowserStack::IosMdmServiceClient).to receive(:make_request).and_raise
      allow(BrowserStack::IosMdmServiceClient).to receive(:parse_certificates)
      expect { BrowserStack::IosMdmServiceClient.get_certificates(device) }.to raise_error(MdmApiException)
    end
  end

  describe '.parse_certificates' do
    let(:data) { Base64.decode64(fixture("device_certificate_data")) }
    let(:stringio_obj) { StringIO.new(data) }
    let(:response) do
      { "CertificateList" => [
         { "CommonName" => "MicroMDM Identity (%ComputerName%)", "Data" => stringio_obj, "IsIdentity" => true },
         { "CommonName" => "mitmproxy", "Data" => stringio_obj, "IsIdentity" => false },
         { "CommonName" => "mitmproxy", "Data" => stringio_obj, "IsIdentity" => false },
         { "Data" => stringio_obj, "IsIdentity" => false },
         { "CommonName" => "*.browserstack.com", "Data" => stringio_obj, "IsIdentity" => false }
         ],
        "CommandUUID" => "616a1f5e-03ce-44a2-a547-29687983e55a", "Status" => "Acknowledged", "UDID" => "f5a0fe980c2c82184135d3e794552f4edb19833e" }
    end

    it "returns array of certificate hashes" do
      parsed_response = BrowserStack::IosMdmServiceClient.parse_certificates(response)
      expect(parsed_response).to be_an(Array)
      expect(parsed_response[0]).to be_a(Hash)
      expect(parsed_response[0][:name]).to eq(response["CertificateList"][0]["CommonName"])
    end

    it "lists certificate as expired if expiry date has passed" do
      allow(Time).to receive(:now).and_return(Time.parse("2020-08-10"))
      parsed_response = BrowserStack::IosMdmServiceClient.parse_certificates(response)
      expect(parsed_response[0][:expired]).to be true
    end

    it "lists certificate as not expired if expiry date has not passed" do
      allow(Time).to receive(:now).and_return(Time.parse("2019-08-10"))
      parsed_response = BrowserStack::IosMdmServiceClient.parse_certificates(response)
      expect(parsed_response[0][:expired]).to be false
    end
  end

  describe '.expired_certificates' do
    let(:parsed_certs) do
      [
        { name: "mitmproxy", expired: true, issue_date: "2018-02-29 14:03:04 UTC", expiry_date: "2019-03-02 14:03:04 UTC" },
        { name: nil, expired: false, issue_date: "2017-06-06 10:29:48 UTC", expiry_date: "2022-06-06 10:29:48 UTC" },
        { name: "*.browserstack.com", expired: false, issue_date: "2019-07-09 07:36:11 UTC", expiry_date: "2021-07-18 16:05:38 UTC" },
        { name: "MicroMDM Identity (%ComputerName%)", expired: true, issue_date: "2018-08-11 07:42:32 UTC", expiry_date: "2019-08-11 07:42:32 UTC" },
        { name: "mitmproxy", expired: false, issue_date: "2018-05-05 09:51:34 UTC", expiry_date: "2021-05-06 09:51:34 UTC" }
      ]
    end

    it 'creates a list of expired certificates for a device' do
      allow(BrowserStack::IosMdmServiceClient).to receive(:get_certificates).and_return(parsed_certs)
      allow(Time).to receive(:now).and_return(Time.parse("2020-08-10"))
      expect(BrowserStack::IosMdmServiceClient.expired_certificates(device)).to eq(["mitmproxy", "MicroMDM Identity (%ComputerName%)"])
    end
  end

  describe '.mdm_re_enrollment' do
    let(:parsed_certs_not_expired) do
      [
        { name: "MicroMDM Identity (%ComputerName%)", expired: false, issue_date: "2021-05-11 07:42:32 UTC", expiry_date: "2022-05-11 07:42:32 UTC" },
        { name: "mitmproxy", expired: false, issue_date: "2018-05-05 09:51:34 UTC", expiry_date: "2021-05-06 09:51:34 UTC" }
      ]
    end
    let(:parsed_certs_expired) do
      [
        { name: "MicroMDM Identity (%ComputerName%)", expired: true, issue_date: "2021-05-06 07:42:32 UTC", expiry_date: "2022-05-06 07:42:32 UTC" },
        { name: "mitmproxy", expired: true, issue_date: "2018-05-05 09:51:34 UTC", expiry_date: "2021-05-06 09:51:34 UTC" }
      ]
    end

    before do
      allow(Time).to receive(:now).and_return(Time.parse("2022-05-07"))
    end

    it 're-enroll device if certificate is about to expire' do
      allow(BrowserStack::IosMdmServiceClient).to receive(:mdm_cert_expired?).and_return(true)
      expect(BrowserStack::IosMdmServiceClient).to receive(:run_mdm_ui_automation).and_return(nil)
      expect(BrowserStack::IosMdmServiceClient.mdm_re_enrollment(device)).to eq(true)
    end

    it 're-enroll device if state file is present' do
      expect(BrowserStack::IosMdmServiceClient).to receive(:run_mdm_ui_automation).and_return(nil)
      expect(BrowserStack::IosMdmServiceClient.mdm_re_enrollment(device, true)).to eq(true)
    end

    it 're-enroll if certificate has already expired' do
      allow(BrowserStack::IosMdmServiceClient).to receive(:mdm_cert_expired?).and_return(true)
      expect(BrowserStack::IosMdmServiceClient).to receive(:run_mdm_ui_automation).and_return(nil)
      expect(BrowserStack::IosMdmServiceClient.mdm_re_enrollment(device)).to eq(true)
    end
  end

  describe 'mdm commands' do
    it 'disables bluetooth' do
      BrowserStack::IosMdmServiceClient.configure
      allow(BrowserStack::IosMdmServiceClient).to receive(
        :make_request
      ).and_return({ success: true })
      allow_any_instance_of(DataReportHelper).to receive(
        :populate_data_hash
      ).and_return({ success: true })
      allow_any_instance_of(DataReportHelper).to receive(
        :report
      ).and_return(true)
      expect(BrowserStack::IosMdmServiceClient.disable_bluetooth(device)).to eq({ success: true })
    end

    it 'reports failures' do
      error_message = "Bad Response"
      BrowserStack::IosMdmServiceClient.configure
      allow(BrowserStack::IosMdmServiceClient).to receive(
        :make_request
      ).and_raise(error_message)
      allow_any_instance_of(DataReportHelper).to receive(
        :populate_data_hash
      ).and_return({ success: false })
      allow_any_instance_of(DataReportHelper).to receive(
        :report
      ).and_return(false)
      expect { BrowserStack::IosMdmServiceClient.disable_bluetooth(device) }.to raise_error(
        MdmApiFatalException, "Could not disable bluetooth: #{error_message}"
      )
    end
  end
end
