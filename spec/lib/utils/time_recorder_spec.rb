require_relative '../../spec_helper'
require_relative '../../../lib/utils/time_recorder'

class TestClass
  include BrowserStack::TimeRecorder
  time_methods :runs_no_error, :runs_error
  time_methods :runs_below_threshold, threshold: 10
  time_methods :runs_above_threshold, threshold: 1
  time_class_methods :runs_error, :runs_no_error
  around_method :runs_around do |original_method, _method_name|
    runs_before
    original_method.call
    runs_after
  end

  def runs_no_error
    "runs"
  end

  def runs_around(arg)
    42
  end

  def runs_error
    raise "Some error"
  end

  def runs_below_threshold
    sleep 1
  end

  def runs_above_threshold
    sleep 1
  end

  def self.runs_error
    raise "Class error"
  end

  def self.runs_no_error
    "class runs"
  end

  def runs_before
    "before"
  end

  def runs_after
    "after"
  end
end

RSpec::Matchers.define :hash_with_numeric_data do |expected|
  expected ||= {}
  match do |actual|
    actual.is_a?(Hash) && actual[:data].is_a?(Numeric) && actual.merge(expected) == actual
  end
end

describe BrowserStack::TimeRecorder do
  subject { TestClass.new }

  describe '#time_methods' do
    it 'records time taken for instance method to run and pushes it to Zombie' do
      expect(BrowserStack::Zombie).to receive(:push_logs)
        .with("timed-TestClass#runs_no_error", "", hash_with_numeric_data)
      subject.runs_no_error
    end

    it 'records time taken for instance method to run and pushes it to Zombie if time taken is greater than threshold' do
      expect(BrowserStack::Zombie).to receive(:push_logs)
        .with("timed-TestClass#runs_above_threshold", "", hash_with_numeric_data)
      subject.runs_above_threshold
    end

    it 'records time taken for instance method to run and does not push it to Zombie if time taken is less than threshold' do
      expect(BrowserStack::Zombie).not_to receive(:push_logs)
      subject.runs_below_threshold
    end

    it 'records time taken for instance method to run and pushes it to Zombie with error info if error occured' do
      expect(BrowserStack::Zombie).to receive(:push_logs)
        .with("timed-TestClass#runs_error", "RuntimeError - Some error", hash_with_numeric_data)
      expect { subject.runs_error }.to raise_error("Some error")
    end

    context 'instance responds to device_id method' do
      before(:each) do
        class TestClass
          time_methods :runs_no_error
          def device_id
            1
          end
        end
      end

      it 'will send device id in zombie message payload' do
        expect(BrowserStack::Zombie).to receive(:push_logs)
          .with("timed-TestClass#runs_no_error", "", hash_with_numeric_data)
        subject.runs_no_error
      end
    end
  end

  describe '#time_class_methods' do
    it 'records time taken for class method to run and pushes it to Zombie' do
      expect(BrowserStack::Zombie).to receive(:push_logs)
        .with("timed-TestClass#runs_no_error", "", hash_with_numeric_data)
      TestClass.runs_no_error
    end

    it 'records time taken for class method to run and pushes it to Zombie with error info if error occured' do
      expect(BrowserStack::Zombie).to receive(:push_logs)
        .with("timed-TestClass#runs_error", "RuntimeError - Class error", hash_with_numeric_data)
      expect { TestClass.runs_error }.to raise_error("Class error")
    end
  end

  describe 'record_time' do
    it 'records time taken for block to run and pushes it to Zombie' do
      expect(BrowserStack::Zombie).to receive(:push_logs)
        .with("timed-custom-test", "", hash_with_numeric_data)
      subject.record_time("custom-test") { 1 + 1 }
    end

    it 'records time taken for block to run and pushes it to Zombie with error info if error occured' do
      expect(BrowserStack::Zombie).to receive(:push_logs)
        .with("timed-custom-test", "RuntimeError - Some error", hash_with_numeric_data)
      expect { subject.record_time('custom-test') { raise "Some error" } }.to raise_error("Some error")
    end
  end

  describe 'around_method' do
    it 'runs around' do
      expect(subject).to receive(:runs_before)
      expect(subject).to receive(:runs_after)
      x = subject.runs_around("nick")
      expect(x).to eq(42)
    end
  end
end