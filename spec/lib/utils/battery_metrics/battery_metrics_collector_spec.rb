require 'spec_helper'
require 'tempfile'
require_relative '../../../../lib/utils/battery_metrics/battery_metrics_collector'

describe BatteryMetricsCollector do
  let(:device) { '1234' }
  let(:collector) { BatteryMetricsCollector.new(device) }

  describe '#run' do
    it 'collects and appends battery metrics' do
      expect(collector).to receive(:fetch_battery_metrics)
        .and_return(Tempfile.new)
      expect(collector).to receive(:append_current_metrics)

      collector.run
    end

    it 'handles errors gracefully' do
      expect(collector).to receive(:fetch_battery_metrics)
        .and_return(Tempfile.new)
      expect(collector).to receive(:append_current_metrics)
        .and_raise(StandardError, 'Something went wrong')

      expect { collector.run }.not_to raise_error
    end
  end

  describe '#append_current_metrics' do
    it 'writes the metrics to the accumulated metrics file' do
      metrics = { battery_temperature: 25.3, voltage: 3.7 }.to_json
      expect(File).to receive(:open).with(collector.instance_variable_get(:@accumulated_metrics_file), 'a')
                                    .and_yield(instance_double(File, puts: nil))

      collector.send(:append_current_metrics, metrics)
    end
  end

  describe '#fetch_battery_metrics' do
    it 'executes the idevicediagnostics command and returns a Tempfile' do
      expect(BrowserStack::OSUtils).to receive(:execute)
        .with("#{IDEVICEDIAGNOSTICS} -u #{device} ioregentry AppleSmartBattery", log_command: false)
        .and_return('some_metrics')

      temp_file = collector.send(:fetch_battery_metrics)
      expect(temp_file).to be_a(Tempfile)
    end

    it 'handles errors gracefully' do
      expect(BrowserStack::OSUtils).to receive(:execute)
        .and_raise(StandardError, 'Something went wrong')

      temp_file = collector.send(:fetch_battery_metrics)
      expect(temp_file).to be_a(Tempfile)
    end
  end
end