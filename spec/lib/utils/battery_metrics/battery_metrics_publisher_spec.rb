require 'spec_helper'
require_relative '../../../../lib/utils/battery_metrics/battery_metrics_publisher'

describe BatteryMetricsPublisher do
  let(:device) { 'my_device_id' }
  let(:session) { 'my_session_id' }
  let(:publisher) { BatteryMetricsPublisher.new(device, session) }

  describe '#start' do
    it 'updates the plist' do
      expect(publisher.plist).to receive(:update)

      publisher.start
    end

    it 'handles errors gracefully' do
      expect(publisher.plist).to receive(:update)
        .and_raise(StandardError, 'Something went wrong')

      expect { publisher.start }.not_to raise_error
    end
  end

  describe '#stop_and_push_metrics' do
    it 'stops the service, parses metrics, generates inference, and pushes metrics' do
      expect(publisher).to receive(:stop_service)
      expect(publisher).to receive(:parse_metrics)
      expect(publisher).to receive(:generate_inference)
      expect(publisher).to receive(:push_metrics)
      expect(publisher).to receive(:clean_metrics_file)

      publisher.stop_and_push_metrics
    end

    it 'handles errors gracefully' do
      expect(publisher).to receive(:stop_service)
        .and_raise(StandardError, 'Something went wrong')

      expect { publisher.stop_and_push_metrics }.not_to raise_error
    end
  end

  describe '#clean_metrics_file' do
    it 'truncates the accumulated metrics file' do
      expect(File).to receive(:truncate)
        .with(publisher.instance_variable_get(:@accumulated_metrics_file), 0)

      publisher.clean_metrics_file
    end
  end

  describe '#stop_service' do
    it 'unloads the service' do
      expect(BrowserStack::CheckPlist).to receive(:unload_service)
        .with(publisher.instance_variable_get(:@plist_name),
              publisher.instance_variable_get(:@service_type),
              publisher.instance_variable_get(:@user))

      publisher.send(:stop_service)
    end
  end

  describe '#parse_metrics' do
    it 'reads the accumulated metrics file and organizes the data' do
      allow(File).to receive(:foreach)
        .with(publisher.instance_variable_get(:@accumulated_metrics_file))
        .and_yield('{"battery_temperature": 25.3, "voltage": 3.7, "max_capacity": 100.0}')

      publisher.send(:parse_metrics)
      expect(publisher.instance_variable_get(:@data))
        .to eq(battery_temperature: [25.3], voltage: [3.7], max_capacity: [100.0])
    end
  end

  describe '#generate_inference' do
    it 'generates inference from the parsed metrics' do
      publisher.instance_variable_set(:@data, {
        battery_temperature: [25.3, 26.1],
        voltage: [3.7, 3.8],
        max_capacity: [100, 100]
      })

      publisher.send(:generate_inference)
      expect(publisher.instance_variable_get(:@inference)).to eq({
        battery_temperature: { avg: 25.700000000000003, end: 26.1, max: 26.1, min: 25.3, p50: 25.3, p90: 26.1, p99: 26.1, start: 25.3 },
        voltage: { avg: 3.75, end: 3.8, max: 3.8, min: 3.7, p50: 3.7, p90: 3.8, p99: 3.8, start: 3.7 },
        max_capacity: [100]
      })
    end
  end

  describe '#push_metrics' do
    it 'pushes the metrics inference to the Zombie logs' do
      publisher.instance_variable_set(:@inference, {
        battery_temperature: { avg: 25.700000000000003, end: 26.1, max: 26.1, min: 25.3, p50: 25.3, p90: 26.1, p99: 26.1, start: 25.3 },
        voltage: { avg: 3.75, end: 3.8, max: 3.8, min: 3.7, p50: 3.7, p90: 3.8, p99: 3.8, start: 3.7 },
        max_capacity: [100]
      })

      expect(BrowserStack::Zombie).to receive(:push_logs)
        .with('battery_metrics', '', {
          "device" => device,
          "session_id" => session,
          "data" => publisher.instance_variable_get(:@inference)
        })

      publisher.send(:push_metrics)
    end
  end
end