require_relative '../../../lib/utils/custom_mdm_manager'

describe CustomMDMManager do
  let(:device_id) { 'device123' }
  let(:logger) { double('Logger') }
  let(:device_state) { double('DeviceState') }
  let(:manager) { CustomMDMManager.new(device_id, logger) }
  let(:configuration_profiles_manager) { double('ConfigurationProfilesManager') }
  let(:ios_mdm_service_client) { double('IosMdmServiceClient') }
  let(:iphone) { double('IPhone') }

  before do
    allow(DeviceState).to receive(:new).with(device_id).and_return(device_state)
    allow(manager).to receive(:log)
    allow(ConfigurationProfilesManager).to receive(:new).and_return(configuration_profiles_manager)
    allow(PyMobileDevice::Profile).to receive(:remove_profiles)
    allow(IPhone).to receive(:new).with(anything, device_id).and_return(iphone)
    allow(iphone).to receive(:install_configuration_profiles)
  end

  describe 'manage_setup' do
    it 'removes the setup' do
      allow(device_state).to receive(:custom_mdm_remove_setup_file_present?).and_return(true)
      allow(device_state).to receive(:dedicated_device_file_present?).and_return(false)
      allow(device_state).to receive(:dedicated_cleanup_file_present?).and_return(true)
      expect(manager).to receive(:check_and_remove_setup)

      manager.manage_setup
    end

    it 'does not perform setup for non-dedicated device' do
      allow(device_state).to receive(:custom_mdm_remove_setup_file_present?).and_return(false)
      allow(device_state).to receive(:dedicated_device_file_present?).and_return(false)
      allow(device_state).to receive(:dedicated_cleanup_file_present?).and_return(false)
      allow(device_state).to receive(:custom_mdm_perform_setup_file_present?).and_return(true)

      expect(manager).not_to receive(:check_and_perform_setup)
      manager.manage_setup
    end

    it 'performs setup for dedicated device' do
      allow(device_state).to receive(:custom_mdm_remove_setup_file_present?).and_return(false)
      allow(device_state).to receive(:dedicated_device_file_present?).and_return(true)
      allow(device_state).to receive(:dedicated_cleanup_file_present?).and_return(true)
      allow(device_state).to receive(:custom_mdm_perform_setup_file_present?).and_return(true)

      expect(manager).to receive(:check_and_perform_setup)
      manager.manage_setup
    end
  end

  describe 'check_and_perform_setup' do
    before do
      allow(CustomMDMManager).to receive(:is_custom_mdm_device?).with(device_id).and_return(false)
      allow(device_state).to receive(:remove_custom_mdm_perform_setup_file)
      allow(device_state).to receive(:touch_custom_mdm_file)
      allow(device_state).to receive(:remove_custom_mdm_perform_setup_file)
      allow(configuration_profiles_manager).to receive(:install_profile)
    end

    it 'should not perform setup if already a custom mdm device' do
      allow(CustomMDMManager).to receive(:is_custom_mdm_device?).with(device_id).and_return(true)
      expect(device_state).to receive(:remove_custom_mdm_perform_setup_file)
      result = manager.check_and_perform_setup
      expect(result).to be(true)
    end

    it 'should remove profiles only if device does not use cfgutil' do
      allow(configuration_profiles_manager).to receive(:device_uses_cfgutil_managed_profiles?).and_return(false)

      # Expect the method to still remove profiles, but not install the restriction via cfgutil
      expect(device_state).to receive(:touch_custom_mdm_file)
      expect(device_state).to receive(:remove_custom_mdm_perform_setup_file)
      expect(configuration_profiles_manager).not_to receive(:install_profile).with(:restrictions, install_via: :cfgutil)

      result = manager.check_and_perform_setup
      expect(result).to be(true)
    end
  end

  describe 'check_and_remove_setup' do
    it 'should re-enroll to MDM & install configuration profiles' do
      expect(IosMdmServiceClient).to receive(:mdm_re_enrollment).with(device_id, true)

      # Expect that the IPhone install_configuration_profiles method is called
      expect(iphone).to receive(:install_configuration_profiles)

      # Expect that DeviceState's remove setup file methods are called
      expect(device_state).to receive(:remove_custom_mdm_file)
      expect(device_state).to receive(:remove_custom_mdm_remove_setup_file)
      manager.check_and_remove_setup
    end
  end

  describe 'is_custom_mdm_device?' do
    it 'should check for presence of state file' do
      allow(device_state).to receive(:custom_mdm_file_present?).and_return(true)
      result = CustomMDMManager.is_custom_mdm_device?(device_id)
      expect(result).to be(true)
    end
  end

  describe 'check references' do
    it 'ensures handling for CustomMDM when using IosMdmServiceClient' do
      # If IosMdmServiceClient is used, ensure that code handles the case where MDM is unavailable
      # ref - is_custom_mdm_device?
      # Increment the counter once the handling for CustomMDM is implemented

      references_count = 0
      Dir['./**/*.rb'].reject { |file| file =~ /_spec\.rb$/ }.each do |file|
        references_count += File.read(file).scan('IosMdmServiceClient').size
      end
      expect(references_count).to eq(77)
    end
  end
end
