require_relative '../../spec_helper'
require_relative '../../../lib/utils/idevice_file_utils'
require_relative '../../../lib/utils/osutils'

RSpec.describe IdeviceFileUtils do
  let(:device_id) { 'device_id' }
  let(:bundle_id) { 'bundle_id' }
  let(:folder_path) { 'folder_path' }
  let(:file_path) { 'file_path' }
  let(:filename) { 'filename' }
  let(:destination) { 'destination' }

  describe '.pull_folder' do
    it 'creates the destination folder and executes the correct command' do
      expected_command = "/usr/local/bin/gtimeout -s KILL 60 #{IOS_DEPLOY} --id #{device_id} --bundle_id #{bundle_id} --download='#{folder_path}' --to '#{destination}'"

      expect(FileUtils).to receive(:mkdir_p).with(destination)
      expect(BrowserStack::OSUtils).to receive(:execute).with(expected_command)

      IdeviceFileUtils.pull_folder(device_id, folder_path, bundle_id, destination)
    end
  end

  describe '.pull_all_photos' do
    it 'creates the destination folder and executes the correct command' do
      expected_command = "/usr/local/bin/gtimeout -s KILL 60 #{IOS_DEPLOY} --id #{device_id} -f -w/DCIM/ --to '#{destination}'"

      expect(FileUtils).to receive(:mkdir_p).with(destination)
      expect(BrowserStack::OSUtils).to receive(:execute).with(expected_command)

      IdeviceFileUtils.pull_all_photos(device_id, destination)
    end
  end

  describe '.remove_file' do
    it 'executes the correct command' do
      expected_command = "/usr/local/bin/gtimeout -s KILL 20 #{IOS_DEPLOY} --id #{device_id} --bundle_id #{bundle_id} --rm '#{file_path}'"

      expect(BrowserStack::OSUtils).to receive(:execute).with(expected_command)

      IdeviceFileUtils.remove_file(device_id, file_path, bundle_id)
    end
  end

  describe '.add_file' do
    it 'executes the correct command' do
      expected_command = "/usr/local/bin/gtimeout -s KILL 20 #{IOS_DEPLOY} --id #{device_id} --bundle_id #{bundle_id} --upload '#{filename}' --to '#{file_path}'"

      expect(BrowserStack::OSUtils).to receive(:execute).with(expected_command, false)

      IdeviceFileUtils.add_file(device_id, filename, file_path, bundle_id)
    end
  end

  describe '.truncate_folder' do
    it 'executes the correct command' do
      expected_command = "/usr/local/bin/gtimeout -s KILL 20 #{IOS_DEPLOY} --id #{device_id} --bundle_id #{bundle_id} --rmtree '#{folder_path}'"

      expect(BrowserStack::OSUtils).to receive(:execute).with(expected_command)

      IdeviceFileUtils.truncate_folder(device_id, folder_path, bundle_id)
    end
  end

  describe '.get_list_of_preloaded_media' do
    it 'executes the correct command and returns the expected result' do
      expected_command = "/usr/local/bin/gtimeout -s KILL 20 #{IOS_DEPLOY} --id #{device_id} -f -lDCIM"
      output = "DCIM/PHOTOS/IMG_001.jpg\nDCIM/VIDEOS/VID_001.mov\nDCIM/APPLE/IMG_002.jpg\nDCIM/APPLE/VID_002.mov\n"
      expected_result = ['DCIM/APPLE/IMG_002.jpg', 'DCIM/APPLE/VID_002.mov']
      expect(BrowserStack::OSUtils).to receive(:execute).with(expected_command).and_return(output)

      result = IdeviceFileUtils.get_list_of_preloaded_media(device_id)
      expect(result).to eq(expected_result)
    end
  end
end
