require 'rspec'
require_relative '../../../../lib/utils/idevice_ffi/developer_mode_util'

# Need to stub the idevice module classes because of import errors.

class LockdownClientMock
  def get_value(*args); end
end

class PropertyListServiceClientMock
  def send_plist(*args); end
end

describe 'DeveloperModeUtil' do
  let(:udid) { 'udid' }

  before do
    stub_const('Idevice::LockdownClient', LockdownClientMock)
    stub_const('Idevice::Idevice', Object)
  end

  context 'enabled?' do
    before(:each) do
      @lockdown = instance_double(Idevice::LockdownClient)
      @idevice = instance_double(Idevice::Idevice)
      @util = DeveloperModeUtil.new(udid)
      expect(@util).to receive(:_attach_idevice).and_return(@idevice)
      expect(Idevice::LockdownClient).to receive(:attach).with(idevice: @idevice).and_return(@lockdown)
    end

    it 'is enabled' do
      expect(@lockdown).to receive(:get_value).with(DeveloperModeUtil::DOMAIN, DeveloperModeUtil::KEY).and_return(true)
      expect(@util.enabled?).to be(true)
    end

    it 'is disabled' do
      expect(@lockdown).to receive(:get_value).with(DeveloperModeUtil::DOMAIN, DeveloperModeUtil::KEY).and_return(false)
      expect(@util.enabled?).to be(false)
    end
  end

  context '_send_plist' do
    before do
      stub_const('Idevice::PropertyListServiceClient', PropertyListServiceClientMock)
    end

    before(:each) do
      @idevice = instance_double(Idevice::Idevice)
      @pls = instance_double(Idevice::PropertyListServiceClient)
      @util = DeveloperModeUtil.new(udid)
      expect(@util).to receive(:_attach_idevice).and_return(@idevice)
      expect(Idevice::PropertyListServiceClient).to receive(:attach).with(idevice: @idevice).and_return(@pls)
    end

    context 'toggle' do
      it 'toggles the developer mode' do
        expect(@pls).to receive(:send_plist).with({ action: 1 })
        @util.toggle
      end
    end

    context 'enable' do
      it 'enables developer mode' do
        expect(@pls).to receive(:send_plist).with({ action: 2 })
        @util.enable
      end
    end
  end
end
