require 'spec_helper'
require 'json'

RSpec.describe PyMobileDevice::Developer do
  let(:bundle_id) { 'com.example.app' }
  let(:device) { 'device123' }
  let(:device_address_command) { '--udid device123' }
  let(:pid) { '12345' }
  let(:rsd_config) { { 'address' => '127.0.0.1', 'port' => '8081' } }

  before do
    allow(BrowserStack).to receive(:logger).and_return(double('logger').as_null_object)
  end

  describe '.get_process_id' do
    context 'when command executes successfully' do
      before do
        allow(BrowserStack::OSUtils).to receive(:execute)
          .with("#{described_class::CMD} process-id-for-bundle-id #{bundle_id} #{device_address_command}", true, timeout: 30, log_command: true).and_return([pid, 0])
      end

      it 'returns the process ID as integer' do
        expect(described_class.get_process_id(bundle_id, device_address_command)).to eq(12345)
      end
    end

    context 'when command fails' do
      before do
        allow(BrowserStack::OSUtils).to receive(:execute).and_return(['Error output', 1])
      end

      it 'raises an error' do
        expect { described_class.get_process_id(bundle_id, device_address_command) }.to raise_error(RuntimeError, /process-id-for-bundle-id command failed/)
      end
    end

    context 'when invalid PID is returned' do
      before do
        allow(BrowserStack::OSUtils).to receive(:execute).and_return(['invalid_pid', 0])
      end

      it 'raises an error' do
        expect { described_class.get_process_id(bundle_id, device_address_command) }.to raise_error(RuntimeError, /Invalid PID received/)
      end
    end
  end

  describe '.disable_memory_limit' do
    context 'when command fails' do
      before do
        allow(BrowserStack::OSUtils).to receive(:execute).and_return(['Error output', 1])
      end

      it 'raises an error' do
        expect { described_class.disable_memory_limit(pid, device_address_command) }.to raise_error(RuntimeError, /memlimitoff command failed/)
      end
    end
  end

  describe '.remove_memory_limit_for_bundle_id' do
    context 'when device version >= 17' do
      let(:device_config) { { 'device_version' => '17.0' } }

      before do
        allow(BrowserStack::DeviceConf).to receive(:[]).with(device).and_return(device_config)
        allow(described_class).to receive(:update_rsd_values)
        allow(described_class).to receive(:rsd_config).and_return(rsd_config)
        allow(described_class).to receive(:get_process_id).and_return(12345)
        allow(described_class).to receive(:disable_memory_limit)
      end

      it 'uses RSD configuration and removes memory limit' do
        expect(described_class).to receive(:update_rsd_values).with(device)
        expect(described_class).to receive(:rsd_config).with(device)

        expect(described_class.remove_memory_limit_for_bundle_id(bundle_id, device)).to be true
      end
    end

    context 'when device version < 17' do
      let(:device_config) { { 'device_version' => '16.0' } }

      before do
        allow(BrowserStack::DeviceConf).to receive(:[]).with(device).and_return(device_config)
        allow(described_class).to receive(:get_process_id).and_return(12345)
        allow(described_class).to receive(:disable_memory_limit)
      end

      it 'uses UDID and removes memory limit' do
        expect(described_class).not_to receive(:update_rsd_values)
        expect(described_class).not_to receive(:rsd_config)

        expect(described_class.remove_memory_limit_for_bundle_id(bundle_id, device)).to be true
      end
    end

    context 'when an error occurs' do
      let(:device_config) { { 'device_version' => '16.0' } }

      before do
        allow(BrowserStack::DeviceConf).to receive(:[]).with(device).and_return(device_config)
        allow(described_class).to receive(:get_process_id).and_raise(RuntimeError, 'Failed to get PID')
      end

      it 'logs error and re-raises' do
        expect(BrowserStack.logger).to receive(:error).with(/Failed to remove memory limit/)
        expect { described_class.remove_memory_limit_for_bundle_id(bundle_id, device) }.to raise_error(RuntimeError, /Failed to get PID/)
      end
    end
  end
end
