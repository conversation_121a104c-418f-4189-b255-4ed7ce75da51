require_relative '../../../lib/utils/configuration_profiles_manager'
require 'spec_helper'

describe ConfigurationProfilesManager do
  let(:device_id) { 'test_device' }
  let(:logger) { BrowserStack.logger }
  let(:mock_ios_device) { double(BrowserStack::IosDevice).as_null_object }
  let(:mock_data_report_helper) { double(DataReportHelper).as_null_object }
  let(:mock_device_config) { double(BrowserStack::DeviceConf).as_null_object }
  let(:mock_server_config) { double(Configuration).as_null_object }
  let(:mock_device_state) { double(DeviceState).as_null_object }
  let(:mock_configuration_profiles_generator) { double(ConfigurationProfilesGenerator).as_null_object }

  let(:subject) { ConfigurationProfilesManager.new(device_id, logger) }

  before(:each) do
    allow(BrowserStack::IosDevice).to receive(:new).and_return(mock_ios_device)
    allow(DataReportHelper).to receive(:new).and_return(mock_data_report_helper)
    allow(mock_ios_device).to receive(:device_config).and_return(mock_device_config)
    allow(mock_ios_device).to receive(:device_state).and_return(mock_device_state)
    allow(Configuration).to receive(:new).and_return(mock_server_config)
    allow(ConfigurationProfilesGenerator).to receive(:new).and_return(mock_configuration_profiles_generator)
  end

  describe '#mdm_managed_profiles' do
    context 'when there are no cfgutil managed profiles' do
      let(:sample_server_config) do
        {
          "mdm_profiles_required" => {
            "Proxy" => {
              type: "com.apple.proxy.http.global"
            },
            "Restrictions" => {
              type: "com.apple.applicationaccess",
              uuid: "95E5F163-06FD-4083-80FD-13CA0EE898A2"
            },
            "Setup Assistant" => {
              type: "com.apple.SetupAssistant.managed",
              uuid: "8876810A-8673-11ED-A1EB-0242AC120002"
            }
          },
          cfgutil_profiles_required: {}
        }
      end

      before(:each) do
        allow(mock_server_config).to receive(:all).and_return(sample_server_config)
        allow(subject).to receive(:device_uses_cfgutil_managed_profiles?).and_return(false)
      end

      it 'should return all mdm managed profiles' do
        expect(subject.mdm_managed_profiles).to eql({
          proxy: {
            type: "com.apple.proxy.http.global"
          },
          restrictions: {
            type: "com.apple.applicationaccess",
            uuid: "95E5F163-06FD-4083-80FD-13CA0EE898A2"
          },
          "setup assistant": {
            type: "com.apple.SetupAssistant.managed",
            uuid: "8876810A-8673-11ED-A1EB-0242AC120002"
          }
        })
      end
    end

    context 'when there are some cfgutil managed profiles, and device does not uses cfgutil managed profiles' do
      let(:sample_server_config) do
        {
          "mdm_profiles_required" => {
            "Proxy" => {
              type: "com.apple.proxy.http.global"
            },
            "Restrictions" => {
              type: "com.apple.applicationaccess",
              uuid: "95E5F163-06FD-4083-80FD-13CA0EE898A2"
            },
            "Setup Assistant" => {
              type: "com.apple.SetupAssistant.managed",
              uuid: "8876810A-8673-11ED-A1EB-0242AC120002"
            }
          },
          cfgutil_profiles_required: {
            restrictions: {
              prefix: "restrictions.",
              latest_version: 1
            }
          }
        }
      end

      before(:each) do
        allow(mock_server_config).to receive(:all).and_return(sample_server_config)
        allow(subject).to receive(:device_uses_cfgutil_managed_profiles?).and_return(false)
      end

      it 'should return all mdm managed profiles' do
        expect(subject.mdm_managed_profiles).to eql({
          proxy: {
            type: "com.apple.proxy.http.global"
          },
          restrictions: {
            type: "com.apple.applicationaccess",
            uuid: "95E5F163-06FD-4083-80FD-13CA0EE898A2"
          },
          "setup assistant": {
            type: "com.apple.SetupAssistant.managed",
            uuid: "8876810A-8673-11ED-A1EB-0242AC120002"
          }
        })
      end
    end

    context 'when there are some cfgutil managed profiles, and device does uses cfgutil managed profiles' do
      let(:sample_server_config) do
        {
          "mdm_profiles_required" => {
            "Proxy" => {
              type: "com.apple.proxy.http.global"
            },
            "Restrictions" => {
              type: "com.apple.applicationaccess",
              uuid: "95E5F163-06FD-4083-80FD-13CA0EE898A2"
            },
            "Setup Assistant" => {
              type: "com.apple.SetupAssistant.managed",
              uuid: "8876810A-8673-11ED-A1EB-0242AC120002"
            }
          },
          cfgutil_profiles_required: {
            restrictions: {
              prefix: "restrictions.",
              latest_version: 1
            }
          }
        }
      end

      before(:each) do
        allow(mock_server_config).to receive(:all).and_return(sample_server_config)
        allow(subject).to receive(:device_uses_cfgutil_managed_profiles?).and_return(true)
      end

      it 'should return all mdm managed profiles' do
        expect(subject.mdm_managed_profiles).to eql({
          proxy: {
            type: "com.apple.proxy.http.global"
          },
          "setup assistant": {
            type: "com.apple.SetupAssistant.managed",
            uuid: "8876810A-8673-11ED-A1EB-0242AC120002"
          }
        })
      end
    end
  end

  describe '#mdm_installed_profiles' do
    context 'when no error is raised' do
      let(:profiles_installed) do
        { 'CommandUUID' => 'd5d17635-5227-4a1a-badc-dbc946dd0a65',
          'ProfileList' =>
          [{ 'HasRemovalPasscode' => false,
             'IsEncrypted' => false,
             'IsManaged' => true,
             'PayloadContent' =>
            [{ 'PayloadDescription' => 'Global HTTP Proxy',
               'PayloadDisplayName' => 'Global HTTP Proxy',
              # Different to constants.yml
               'PayloadIdentifier' => 'com.apple.proxy.http.global.NOT_THE_SAME_UUID',
               'PayloadType' => 'com.apple.proxy.http.global',
               'PayloadVersion' => 1 }],
             'PayloadDisplayName' => 'Proxy',
             'PayloadIdentifier' => 'mdm.fde0564ca18ca2d7fca40a2ffaab1021f504de08',
             'PayloadOrganization' => 'BrowserStack',
             'PayloadRemovalDisallowed' => false,
             'PayloadUUID' => 'fde0564ca18ca2d7fca40a2ffaab1021f504de08',
             'PayloadVersion' => 1 }],
          'Status' => 'Acknowledged',
          'UDID' => 'fde0564ca18ca2d7fca40a2ffaab1021f504de08' }
      end

      before(:each) do
        allow(BrowserStack::IosMdmServiceClient).to receive(:get_installed_profiles).and_return(profiles_installed)
      end

      it 'should return mdm installed profiles' do
        expect(subject.mdm_installed_profiles).to eql(profiles_installed)
      end
    end

    context 'when error is raised' do
      before(:each) do
        allow(BrowserStack::IosMdmServiceClient).to receive(:get_installed_profiles).and_raise("Something went wrong")
      end

      it 'should raise error' do
        expect { subject.mdm_installed_profiles }.to raise_error("Something went wrong")
      end
    end
  end

  describe 'device_uses_cfgutil_managed_profiles?' do
    context 'when device is not whitelisted for new provisioning flow' do
      before(:each) do
        allow(mock_ios_device).to receive(:enable_new_provisioning_flow?).and_return(false)
      end

      it 'should return false' do
        expect(mock_data_report_helper).not_to receive(:report)
        expect(mock_ios_device).not_to receive(:supervision_identities_exist_and_cfgutil_installed?)
        expect(mock_ios_device).not_to receive(:supervision_identities_checked_file_present?)
        expect(subject.device_uses_cfgutil_managed_profiles?).to eql(false)
      end
    end

    context 'when device is whitelisted for provisioning flow, but cfgutil is not installed' do
      before(:each) do
        allow(mock_ios_device).to receive(:enable_new_provisioning_flow?).and_return(true)
        allow(mock_ios_device).to receive(:supervision_identities_exist_and_cfgutil_installed?).and_return(false)
      end

      context 'when should verify cfgutil' do
        it 'should return false' do
          expect(mock_data_report_helper).to receive(:report).with({
            "action" => "device_uses_cfgutil_managed_profiles",
            "error" => nil,
            "supervision_identities_exist_and_cfgutil_installed" => false,
            "supervision_identities_correct" => nil
          })
          expect(subject.device_uses_cfgutil_managed_profiles?(verify_cfgutil: true)).to eql(false)
        end

        context 'when error is raised' do
          before(:each) do
            allow(mock_ios_device).to receive(:supervision_identities_exist_and_cfgutil_installed?).and_raise("Something went wrong")
          end

          it 'should return false' do
            expect(mock_data_report_helper).to receive(:report).with({
              "action" => "device_uses_cfgutil_managed_profiles",
              "error" => "Something went wrong",
              "supervision_identities_exist_and_cfgutil_installed" => nil,
              "supervision_identities_correct" => nil
            })
            expect(subject.device_uses_cfgutil_managed_profiles?(verify_cfgutil: true)).to eql(false)
          end
        end
      end

      context 'when should not verify cfgutil' do
        it 'should return true' do
          expect(mock_data_report_helper).not_to receive(:report)
          expect(subject.device_uses_cfgutil_managed_profiles?).to eql(true)
        end
      end
    end

    context 'when device is whitelisted for provisioning flow, cfgutil is installed, but supervision identities are incorrect' do
      before(:each) do
        allow(mock_ios_device).to receive(:enable_new_provisioning_flow?).and_return(true)
        allow(mock_ios_device).to receive(:supervision_identities_exist_and_cfgutil_installed?).and_return(true)
        allow(mock_device_state).to receive(:supervision_identities_checked_file_present?).and_return(false)
        allow(mock_ios_device).to receive(:supervision_identities_correct?).and_return(false)
      end

      context 'when should verify cfgutil' do
        it 'should return false' do
          expect(mock_data_report_helper).to receive(:report).with({
            "action" => "device_uses_cfgutil_managed_profiles",
            "error" => nil,
            "supervision_identities_exist_and_cfgutil_installed" => true,
            "supervision_identities_correct" => false
          })
          expect(subject.device_uses_cfgutil_managed_profiles?(verify_cfgutil: true)).to eql(false)
        end

        context 'when error is raised' do
          before(:each) do
            allow(mock_ios_device).to receive(:supervision_identities_correct?).and_raise("Something went wrong")
          end

          it 'should return false' do
            expect(mock_data_report_helper).to receive(:report).with({
              "action" => "device_uses_cfgutil_managed_profiles",
              "error" => "Something went wrong",
              "supervision_identities_exist_and_cfgutil_installed" => true,
              "supervision_identities_correct" => nil
            })
            expect(subject.device_uses_cfgutil_managed_profiles?(verify_cfgutil: true)).to eql(false)
          end
        end
      end

      context 'when should not verify cfgutil' do
        it 'should return true' do
          expect(mock_data_report_helper).not_to receive(:report)
          expect(subject.device_uses_cfgutil_managed_profiles?).to eql(true)
        end
      end
    end

    context 'when device is whitelisted for provisioning flow, cfgutil is installed, and supervision identities are correct' do
      before(:each) do
        allow(mock_ios_device).to receive(:enable_new_provisioning_flow?).and_return(true)
        allow(mock_ios_device).to receive(:supervision_identities_exist_and_cfgutil_installed?).and_return(true)
        allow(mock_device_state).to receive(:supervision_identities_checked_file_present?).and_return(false)
        allow(mock_ios_device).to receive(:supervision_identities_correct?).and_return(true)
      end

      it 'should return true' do
        expect(subject.device_uses_cfgutil_managed_profiles?).to eql(true)
        expect(mock_data_report_helper).to_not receive(:report)
      end
    end
  end

  describe '#cfgutil_managed_profiles' do
    context 'when device does not use cfgutil managed profiles' do
      before(:each) do
        allow(subject).to receive(:device_uses_cfgutil_managed_profiles?).and_return(false)
      end

      it 'should return empty hash' do
        expect(subject.cfgutil_managed_profiles).to eql({})
      end
    end

    context 'when device uses cfgutil managed profiles' do
      let(:sample_server_config) do
        {
          "mdm_profiles_required" => {
            "Proxy" => {
              type: "com.apple.proxy.http.global"
            },
            "Restrictions" => {
              type: "com.apple.applicationaccess",
              uuid: "95E5F163-06FD-4083-80FD-13CA0EE898A2"
            },
            "Setup Assistant" => {
              type: "com.apple.SetupAssistant.managed",
              uuid: "8876810A-8673-11ED-A1EB-0242AC120002"
            }
          },
          cfgutil_profiles_required: {
            restrictions: {
              prefix: "restrictions.",
              latest_version: 1
            }
          }
        }
      end

      before(:each) do
        allow(subject).to receive(:device_uses_cfgutil_managed_profiles?).and_return(true)
        allow(mock_server_config).to receive(:all).and_return(sample_server_config)
      end

      it 'should return cfgutil required profiles' do
        expect(subject.cfgutil_managed_profiles).to eql(sample_server_config[:cfgutil_profiles_required])
      end
    end
  end

  describe '#cfgutil_installed_profiles' do
    context 'when no error is raised' do
      let(:profiles_installed) do
        [
          {
            "identifier": "mdm.test_device",
            "version": 1,
            "displayName": "Proxy",
            "uuid": "XYZ"
          },
          {
            "identifier": "com.github.micromdm.micromdm.enroll",
            "version": 1,
            "displayName": "Enrollment Profile",
            "uuid": "XYZ"
          }
        ]
      end

      before(:each) do
        allow(mock_ios_device).to receive(:installed_configuration_profiles).and_return(profiles_installed)
      end

      it 'should return cfgutil installed profiles' do
        expect(subject.cfgutil_installed_profiles).to eql(profiles_installed)
      end
    end

    context 'when error is raised' do
      before(:each) do
        allow(mock_ios_device).to receive(:installed_configuration_profiles).and_raise("Something went wrong")
      end

      it 'should raise error' do
        expect { subject.cfgutil_installed_profiles }.to raise_error("Something went wrong")
      end
    end
  end

  describe '#mitmproxy_certificate_profile_name' do
    context 'when profile_key is found' do
      it 'should return profile name' do
        expect(subject.mitmproxy_certificate_profile_name(:mitmproxy_root_certificate)).to eql("MitmProxy_Root_Certificate")
      end
    end

    context 'when profile key is not found' do
      it 'should return nil' do
        expect(subject.mitmproxy_certificate_profile_name(:some_random_key)).to eql(nil)
      end
    end
  end

  describe '#profile_identifier' do
    let(:sample_server_config) do
      {
        "mdm_profiles_required" => {
          "Proxy" => {
            type: "com.apple.proxy.http.global"
          },
          "Restrictions" => {
            type: "com.apple.applicationaccess",
            uuid: "95E5F163-06FD-4083-80FD-13CA0EE898A2"
          },
          "Setup Assistant" => {
            type: "com.apple.SetupAssistant.managed",
            uuid: "8876810A-8673-11ED-A1EB-0242AC120002"
          }
        },
        cfgutil_profiles_required: {
          restrictions: {
            prefix: "restrictions.",
            latest_version: 1
          }
        }
      }
    end

    before(:each) do
      allow(mock_server_config).to receive(:all).and_return(sample_server_config)
    end

    context 'when medium is cfgutil' do
      context 'when profile key is present' do
        it 'should return profile identifier' do
          expect(subject.profile_identifier(:restrictions, :cfgutil)).to eql("restrictions.#{device_id}")
        end
      end

      context 'when profile key is not present' do
        it 'should raise error' do
          expect { subject.profile_identifier(:some_random_key, :cfgutil) }.to raise_error(StandardError)
        end
      end
    end

    context 'when medium is mdm' do
      context 'when profile key is present' do
        it 'should return profile identifier' do
          expect(subject.profile_identifier(:restrictions, :mdm)).to eql("mdm.restrictions.#{device_id}")
        end
      end

      context 'when profile key is not present' do
        it 'should return nil' do
          expect(subject.profile_identifier(:some_random_key, :mdm)).to eql(nil)
        end
      end
    end
  end

  describe '#profile_uuid' do
    let(:sample_server_config) do
      {
        "mdm_profiles_required" => {
          "Proxy" => {
            type: "com.apple.proxy.http.global"
          },
          "Restrictions" => {
            type: "com.apple.applicationaccess",
            uuid: "95E5F163-06FD-4083-80FD-13CA0EE898A2"
          },
          "Setup Assistant" => {
            type: "com.apple.SetupAssistant.managed",
            uuid: "8876810A-8673-11ED-A1EB-0242AC120002"
          }
        },
        cfgutil_profiles_required: {
          restrictions: {
            prefix: "restrictions.",
            latest_version: 1
          }
        }
      }
    end

    before(:each) do
      allow(mock_server_config).to receive(:all).and_return(sample_server_config)
    end

    context 'when profile key is present' do
      it 'should return profile identifier' do
        expect(subject.profile_uuid(:restrictions)).to eql("restrictions.#{device_id}.1")
      end
    end

    context 'when profile key is not present' do
      it 'should raise error' do
        expect { subject.profile_uuid(:some_random_key) }.to raise_error(StandardError)
      end
    end
  end

  describe '#installed_profile_details' do
    let(:profile_details) do
      {
        "identifier": "mdm.test_device",
        "version": 1,
        "displayName": "Proxy",
        "uuid": "XYZ"
      }
    end

    context 'when device uses cfgutil managed profiles' do
      before(:each) do
        allow(subject).to receive(:device_uses_cfgutil_managed_profiles?).and_return(true)
        allow(subject).to receive(:profile_identifier).and_return("some_random_identifier")
        allow(mock_ios_device).to receive(:fetch_installed_profile).and_return(profile_details)
      end

      it 'should return installed profile details' do
        expect(subject.installed_profile_details("some_random_key")).to eql(profile_details)
      end
    end

    context 'when device does not use cfgutil managed profiles' do
      before(:each) do
        allow(subject).to receive(:device_uses_cfgutil_managed_profiles?).and_return(false)
      end

      it 'should return false' do
        expect(subject.installed_profile_details("some_random_key")).to eql(false)
      end
    end
  end

  describe '#profile_installed_via_cfgutil?' do
    let(:profile_details) do
      {
        "identifier": "mdm.test_device",
        "version": 1,
        "displayName": "Proxy",
        "uuid": "XYZ"
      }
    end

    context 'when device uses cfgutil managed profiles' do
      before(:each) do
        allow(subject).to receive(:device_uses_cfgutil_managed_profiles?).and_return(true)
        allow(subject).to receive(:profile_identifier).and_return("some_random_identifier")
      end

      context 'when profile is installed' do
        before(:each) do
          allow(mock_ios_device).to receive(:fetch_installed_profile).and_return(profile_details)
        end

        it 'should return true' do
          expect(subject.profile_installed_via_cfgutil?("some_random_key")).to eql(true)
        end
      end

      context 'when profile is not installed' do
        before(:each) do
          allow(mock_ios_device).to receive(:fetch_installed_profile).and_return(nil)
        end

        it 'should return false' do
          expect(subject.profile_installed_via_cfgutil?("some_random_key")).to eql(false)
        end
      end
    end

    context 'when device does not use cfgutil managed profiles' do
      before(:each) do
        allow(subject).to receive(:device_uses_cfgutil_managed_profiles?).and_return(false)
      end

      it 'should return false' do
        expect(subject.profile_installed_via_cfgutil?("some_random_key")).to eql(false)
      end
    end
  end

  describe '#remove_profile' do
    before(:each) do
      allow(subject).to receive(:profile_identifier).and_return("some_random_key")
    end

    context 'when remove_via is mdm' do
      context 'when identifier is nil' do
        it 'should remove profile with updated identifier' do
          expect(BrowserStack::IosMdmServiceClient).to receive(:remove_profile).with(device_id, "some_profile_key", "some_random_key").and_return(true)
          expect(mock_data_report_helper).to receive(:report).with(hash_including({
            "action" => "remove_profile",
            "profile_key" => "some_profile_key",
            "identifier" => "some_random_key",
            "remove_via" => :mdm,
            "error" => nil
          }))
          expect { subject.remove_profile("some_profile_key", nil, remove_via: :mdm) }.not_to raise_error
        end

        it 'should raise error when error is raised' do
          expect(BrowserStack::IosMdmServiceClient).to receive(:remove_profile).with(device_id, "some_profile_key", "some_random_key").and_raise("Something went wrong")
          expect(mock_data_report_helper).to receive(:report).with(hash_including({
            "action" => "remove_profile",
            "profile_key" => "some_profile_key",
            "identifier" => "some_random_key",
            "remove_via" => :mdm,
            "error" => "Something went wrong"
          }))
          expect { subject.remove_profile("some_profile_key", nil, remove_via: :mdm) }.to raise_error("Something went wrong")
        end
      end

      context 'when identifier is not nil' do
        it 'should remove profile with given identifier' do
          expect(BrowserStack::IosMdmServiceClient).to receive(:remove_profile).with(device_id, "some_profile_key", "some_random_key_1").and_return(true)
          expect(mock_data_report_helper).to receive(:report).with(hash_including({
            "action" => "remove_profile",
            "profile_key" => "some_profile_key",
            "identifier" => "some_random_key_1",
            "remove_via" => :mdm,
            "error" => nil
          }))
          expect { subject.remove_profile("some_profile_key", "some_random_key_1", remove_via: :mdm) }.not_to raise_error
        end

        it 'should raise error when error is raised' do
          expect(BrowserStack::IosMdmServiceClient).to receive(:remove_profile).with(device_id, "some_profile_key", "some_random_key").and_raise("Something went wrong")
          expect(mock_data_report_helper).to receive(:report).with(hash_including({
            "action" => "remove_profile",
            "profile_key" => "some_profile_key",
            "identifier" => "some_random_key",
            "remove_via" => :mdm,
            "error" => "Something went wrong"
          }))
          expect { subject.remove_profile("some_profile_key", nil, remove_via: :mdm) }.to raise_error("Something went wrong")
        end
      end
    end

    context 'when remove_via is cfgutil' do
      context 'when identifier is nil' do
        it 'should remove profile with updated identifier' do
          expect(mock_ios_device).to receive(:remove_profile).with("some_random_key").and_return(true)
          expect(mock_data_report_helper).to receive(:report).with(hash_including({
            "action" => "remove_profile",
            "profile_key" => "some_profile_key",
            "identifier" => "some_random_key",
            "remove_via" => :cfgutil,
            "error" => nil
          }))
          expect { subject.remove_profile("some_profile_key", nil, remove_via: :cfgutil) }.not_to raise_error
        end

        it 'should raise error when error is raised' do
          expect(mock_ios_device).to receive(:remove_profile).with("some_random_key").and_raise("Something went wrong")
          expect(mock_data_report_helper).to receive(:report).with(hash_including({
            "action" => "remove_profile",
            "profile_key" => "some_profile_key",
            "identifier" => "some_random_key",
            "remove_via" => :cfgutil,
            "error" => "Something went wrong"
          }))
          expect { subject.remove_profile("some_profile_key", nil, remove_via: :cfgutil) }.to raise_error("Something went wrong")
        end
      end

      context 'when identifier is not nil' do
        it 'should remove profile with given identifier' do
          expect(mock_ios_device).to receive(:remove_profile).with("some_random_key_1").and_return(true)
          expect(mock_data_report_helper).to receive(:report).with(hash_including({
            "action" => "remove_profile",
            "profile_key" => "some_profile_key",
            "identifier" => "some_random_key_1",
            "remove_via" => :cfgutil,
            "error" => nil
          }))
          expect { subject.remove_profile("some_profile_key", "some_random_key_1", remove_via: :cfgutil) }.not_to raise_error
        end

        it 'should raise error when error is raised' do
          expect(mock_ios_device).to receive(:remove_profile).with("some_random_key_1").and_raise("Something went wrong")
          expect(mock_data_report_helper).to receive(:report).with(hash_including({
            "action" => "remove_profile",
            "profile_key" => "some_profile_key",
            "identifier" => "some_random_key_1",
            "remove_via" => :cfgutil,
            "error" => "Something went wrong"
          }))
          expect { subject.remove_profile("some_profile_key", "some_random_key_1", remove_via: :cfgutil) }.to raise_error("Something went wrong")
        end
      end
    end

    context 'when remove_via is automatic' do
      context 'when identifier is nil' do
        context 'when profile is installed via cfgutil' do
          before(:each) do
            allow(subject).to receive(:profile_installed_via_cfgutil?).and_return(true)
          end

          it 'should remove profile with cfgutil and updated identifier' do
            expect(mock_ios_device).to receive(:remove_profile).with("some_random_key").and_return(true)
            expect(mock_data_report_helper).to receive(:report).with(hash_including({
              "action" => "remove_profile",
              "profile_key" => "some_profile_key",
              "identifier" => "some_random_key",
              "remove_via" => :cfgutil,
              "error" => nil
            }))
            expect { subject.remove_profile("some_profile_key", nil, remove_via: :automatic) }.not_to raise_error
          end

          it 'should raise error when error is raised' do
            expect(mock_ios_device).to receive(:remove_profile).with("some_random_key").and_raise("Something went wrong")
            expect(mock_data_report_helper).to receive(:report).with(hash_including({
              "action" => "remove_profile",
              "profile_key" => "some_profile_key",
              "identifier" => "some_random_key",
              "remove_via" => :cfgutil,
              "error" => "Something went wrong"
            }))
            expect { subject.remove_profile("some_profile_key", nil, remove_via: :cfgutil) }.to raise_error("Something went wrong")
          end
        end

        context 'when profile is not installed via cfgutil' do
          before(:each) do
            allow(subject).to receive(:profile_installed_via_cfgutil?).and_return(false)
          end

          it 'should remove profile with mdm and updated identifier' do
            expect(BrowserStack::IosMdmServiceClient).to receive(:remove_profile).with(device_id, "some_profile_key", "some_random_key").and_return(true)
            expect(mock_data_report_helper).to receive(:report).with(hash_including({
              "action" => "remove_profile",
              "profile_key" => "some_profile_key",
              "identifier" => "some_random_key",
              "remove_via" => :mdm,
              "error" => nil
            }))
            expect { subject.remove_profile("some_profile_key", nil, remove_via: :automatic) }.not_to raise_error
          end

          it 'should raise error when error is raised' do
            expect(BrowserStack::IosMdmServiceClient).to receive(:remove_profile).with(device_id, "some_profile_key", "some_random_key").and_raise("Something went wrong")
            expect(mock_data_report_helper).to receive(:report).with(hash_including({
              "action" => "remove_profile",
              "profile_key" => "some_profile_key",
              "identifier" => "some_random_key",
              "remove_via" => :mdm,
              "error" => "Something went wrong"
            }))
            expect { subject.remove_profile("some_profile_key", nil, remove_via: :mdm) }.to raise_error("Something went wrong")
          end
        end
      end

      context 'when identifier is not nil' do
        context 'when profile is installed via cfgutil' do
          before(:each) do
            allow(subject).to receive(:profile_installed_via_cfgutil?).and_return(true)
          end

          it 'should remove profile with cfgutil and updated identifier' do
            expect(mock_ios_device).to receive(:remove_profile).with("some_random_key_1").and_return(true)
            expect(mock_data_report_helper).to receive(:report).with(hash_including({
              "action" => "remove_profile",
              "profile_key" => "some_profile_key",
              "identifier" => "some_random_key_1",
              "remove_via" => :cfgutil,
              "error" => nil
            }))
            expect { subject.remove_profile("some_profile_key", "some_random_key_1", remove_via: :automatic) }.not_to raise_error
          end

          it 'should raise error when error is raised' do
            expect(mock_ios_device).to receive(:remove_profile).with("some_random_key_1").and_raise("Something went wrong")
            expect(mock_data_report_helper).to receive(:report).with(hash_including({
              "action" => "remove_profile",
              "profile_key" => "some_profile_key",
              "identifier" => "some_random_key_1",
              "remove_via" => :cfgutil,
              "error" => "Something went wrong"
            }))
            expect { subject.remove_profile("some_profile_key", "some_random_key_1", remove_via: :cfgutil) }.to raise_error("Something went wrong")
          end
        end

        context 'when profile is not installed via cfgutil' do
          before(:each) do
            allow(subject).to receive(:profile_installed_via_cfgutil?).and_return(false)
          end

          it 'should remove profile with mdm and given identifier' do
            expect(BrowserStack::IosMdmServiceClient).to receive(:remove_profile).with(device_id, "some_profile_key", "some_random_key_1").and_return(true)
            expect(mock_data_report_helper).to receive(:report).with(hash_including({
              "action" => "remove_profile",
              "profile_key" => "some_profile_key",
              "identifier" => "some_random_key_1",
              "remove_via" => :mdm,
              "error" => nil
            }))
            expect { subject.remove_profile("some_profile_key", "some_random_key_1", remove_via: :automatic) }.not_to raise_error
          end

          it 'should raise error when error is raised' do
            expect(BrowserStack::IosMdmServiceClient).to receive(:remove_profile).with(device_id, "some_profile_key", "some_random_key_1").and_raise("Something went wrong")
            expect(mock_data_report_helper).to receive(:report).with(hash_including({
              "action" => "remove_profile",
              "profile_key" => "some_profile_key",
              "identifier" => "some_random_key_1",
              "remove_via" => :mdm,
              "error" => "Something went wrong"
            }))
            expect { subject.remove_profile("some_profile_key", "some_random_key_1", remove_via: :mdm) }.to raise_error("Something went wrong")
          end
        end
      end
    end
  end

  describe '#install_profile' do
    before(:each) do
      allow(subject).to receive(:profile_identifier).and_return("some_random_key")
    end

    context 'when install_via is mdm' do
      context 'when identifier is nil' do
        it 'should install profile with updated identifier' do
          expect(subject).to receive(:install_proxy_profile).with(install_via: :mdm).and_return(true)
          expect(mock_data_report_helper).to receive(:report).with(hash_including({
            "action" => "install_profile",
            "profile_key" => :proxy,
            "install_via" => :mdm,
            "error" => nil
          }))
          expect { subject.install_profile(:proxy, install_via: :mdm) }.not_to raise_error
        end

        it 'should raise error when error is raised' do
          expect(subject).to receive(:install_proxy_profile).with(install_via: :mdm).and_raise("Something went wrong")
          expect(mock_data_report_helper).to receive(:report).with(hash_including({
            "action" => "install_profile",
            "profile_key" => :proxy,
            "install_via" => :mdm,
            "error" => "Something went wrong"
          }))
          expect { subject.install_profile(:proxy, install_via: :mdm) }.to raise_error("Something went wrong")
        end
      end

      context 'when identifier is not nil' do
        it 'should install profile with given identifier' do
          expect(subject).to receive(:install_proxy_profile).with(install_via: :mdm).and_return(true)
          expect(mock_data_report_helper).to receive(:report).with(hash_including({
            "action" => "install_profile",
            "profile_key" => :proxy,
            "install_via" => :mdm,
            "error" => nil
          }))
          expect { subject.install_profile(:proxy, install_via: :mdm) }.not_to raise_error
        end

        it 'should raise error when error is raised' do
          expect(subject).to receive(:install_proxy_profile).with(install_via: :mdm).and_raise("Something went wrong")
          expect(mock_data_report_helper).to receive(:report).with(hash_including({
            "action" => "install_profile",
            "profile_key" => :proxy,
            "install_via" => :mdm,
            "error" => "Something went wrong"
          }))
          expect { subject.install_profile(:proxy, install_via: :mdm) }.to raise_error("Something went wrong")
        end
      end
    end

    context 'when install_via is cfgutil' do
      context 'when identifier is nil' do
        it 'should install profile with updated identifier' do
          expect(subject).to receive(:install_proxy_profile).with(install_via: :cfgutil).and_return(true)
          expect(mock_data_report_helper).to receive(:report).with(hash_including({
            "action" => "install_profile",
            "profile_key" => :proxy,
            "install_via" => :cfgutil,
            "error" => nil
          }))
          expect { subject.install_profile(:proxy, install_via: :cfgutil) }.not_to raise_error
        end

        it 'should raise error when error is raised' do
          expect(subject).to receive(:install_proxy_profile).with(install_via: :cfgutil).and_raise("Something went wrong")
          expect(mock_data_report_helper).to receive(:report).with(hash_including({
            "action" => "install_profile",
            "profile_key" => :proxy,
            "install_via" => :cfgutil,
            "error" => "Something went wrong"
          }))
          expect { subject.install_profile(:proxy, install_via: :cfgutil) }.to raise_error("Something went wrong")
        end
      end

      context 'when identifier is not nil' do
        it 'should install profile with given identifier' do
          expect(subject).to receive(:install_proxy_profile).with(install_via: :cfgutil).and_return(true)
          expect(mock_data_report_helper).to receive(:report).with(hash_including({
            "action" => "install_profile",
            "profile_key" => :proxy,
            "install_via" => :cfgutil,
            "error" => nil
          }))
          expect { subject.install_profile(:proxy, install_via: :cfgutil) }.not_to raise_error
        end

        it 'should raise error when error is raised' do
          expect(subject).to receive(:install_proxy_profile).with(install_via: :cfgutil).and_raise("Something went wrong")
          expect(mock_data_report_helper).to receive(:report).with(hash_including({
            "action" => "install_profile",
            "profile_key" => :proxy,
            "install_via" => :cfgutil,
            "error" => "Something went wrong"
          }))
          expect { subject.install_profile(:proxy, install_via: :cfgutil) }.to raise_error("Something went wrong")
        end
      end
    end

    context 'when install_via is automatic' do
      context 'when identifier is nil' do
        context 'when profile is installed via cfgutil' do
          before(:each) do
            allow(subject).to receive(:profile_installed_via_cfgutil?).and_return(true)
          end

          it 'should install profile with cfgutil and updated identifier' do
            expect(subject).to receive(:install_proxy_profile).with(install_via: :cfgutil).and_return(true)
            expect(mock_data_report_helper).to receive(:report).with(hash_including({
              "action" => "install_profile",
              "profile_key" => :proxy,
              "install_via" => :cfgutil,
              "error" => nil
            }))
            expect { subject.install_profile(:proxy, install_via: :automatic) }.not_to raise_error
          end

          it 'should raise error when error is raised' do
            expect(subject).to receive(:install_proxy_profile).with(install_via: :cfgutil).and_raise("Something went wrong")
            expect(mock_data_report_helper).to receive(:report).with(hash_including({
              "action" => "install_profile",
              "profile_key" => :proxy,
              "install_via" => :cfgutil,
              "error" => "Something went wrong"
            }))
            expect { subject.install_profile(:proxy, install_via: :cfgutil) }.to raise_error("Something went wrong")
          end
        end

        context 'when profile is not installed via cfgutil' do
          before(:each) do
            allow(subject).to receive(:profile_installed_via_cfgutil?).and_return(false)
          end

          it 'should install profile with mdm and updated identifier' do
            expect(subject).to receive(:install_proxy_profile).with(install_via: :mdm).and_return(true)
            expect(mock_data_report_helper).to receive(:report).with(hash_including({
              "action" => "install_profile",
              "profile_key" => :proxy,
              "install_via" => :mdm,
              "error" => nil
            }))
            expect { subject.install_profile(:proxy, install_via: :automatic) }.not_to raise_error
          end

          it 'should raise error when error is raised' do
            expect(subject).to receive(:install_proxy_profile).with(install_via: :mdm).and_raise("Something went wrong")
            expect(mock_data_report_helper).to receive(:report).with(hash_including({
              "action" => "install_profile",
              "profile_key" => :proxy,
              "install_via" => :mdm,
              "error" => "Something went wrong"
            }))
            expect { subject.install_profile(:proxy, install_via: :mdm) }.to raise_error("Something went wrong")
          end
        end
      end

      context 'when identifier is not nil' do
        context 'when profile is installed via cfgutil' do
          before(:each) do
            allow(subject).to receive(:profile_installed_via_cfgutil?).and_return(true)
          end

          it 'should install profile with cfgutil and updated identifier' do
            expect(subject).to receive(:install_proxy_profile).with(install_via: :cfgutil).and_return(true)
            expect(mock_data_report_helper).to receive(:report).with(hash_including({
              "action" => "install_profile",
              "profile_key" => :proxy,
              "install_via" => :cfgutil,
              "error" => nil
            }))
            expect { subject.install_profile(:proxy, install_via: :automatic) }.not_to raise_error
          end

          it 'should raise error when error is raised' do
            expect(subject).to receive(:install_proxy_profile).with(install_via: :cfgutil).and_raise("Something went wrong")
            expect(mock_data_report_helper).to receive(:report).with(hash_including({
              "action" => "install_profile",
              "profile_key" => :proxy,
              "install_via" => :cfgutil,
              "error" => "Something went wrong"
            }))
            expect { subject.install_profile(:proxy, install_via: :cfgutil) }.to raise_error("Something went wrong")
          end
        end

        context 'when profile is not installed via cfgutil' do
          before(:each) do
            allow(subject).to receive(:profile_installed_via_cfgutil?).and_return(false)
          end

          it 'should install profile with mdm and given identifier' do
            expect(subject).to receive(:install_proxy_profile).with(install_via: :mdm).and_return(true)
            expect(mock_data_report_helper).to receive(:report).with(hash_including({
              "action" => "install_profile",
              "profile_key" => :proxy,
              "install_via" => :mdm,
              "error" => nil
            }))
            expect { subject.install_profile(:proxy, install_via: :automatic) }.not_to raise_error
          end

          it 'should raise error when error is raised' do
            expect(subject).to receive(:install_proxy_profile).with(install_via: :mdm).and_raise("Something went wrong")
            expect(mock_data_report_helper).to receive(:report).with(hash_including({
              "action" => "install_profile",
              "profile_key" => :proxy,
              "install_via" => :mdm,
              "error" => "Something went wrong"
            }))
            expect { subject.install_profile(:proxy, install_via: :mdm) }.to raise_error("Something went wrong")
          end
        end
      end
    end
  end
end