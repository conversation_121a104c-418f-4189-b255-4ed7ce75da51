require_relative '../../spec_helper'
require_relative '../../../lib/utils/utils'
require_relative '../../../lib/session/xctest_session'
require_relative '../../../lib/app_percy/app_percy_session'

describe BrowserStack::XCTestSession do
  let(:xctest_session) { BrowserStack::XCTestSession.new({}, {}) }
  let(:summary_file_v2) { './spec/fixtures/intermediate_summary_file_v2' }
  let(:summary_file) { './spec/fixtures/summary_file' }

  it 'creates a xctest_session object' do
    expect(xctest_session).to be_a BrowserStack::XCTestSession
  end

  describe "update_test_summary" do
    let(:start_time) { Time.now.to_i }
    let(:instrumentation) { "#{__dir__}/../../fixtures/fluttertest_instrumentation_success.txt" }

    before(:each) do
      xctest_session.instance_variable_set(:@instrumentation_file, instrumentation)
    end

    it 'should update test status and duration if test is not timedout' do
      duration = 1.23
      allow(xctest_session).to receive(:parse_xctest_output).and_return(["SUCCESS", duration])
      xctest_session.instance_variable_set("@summary_file_v2", summary_file_v2)
      xctest_session.instance_variable_set("@summary_file", summary_file)

      expected_summary_file_v2 = JSON.parse(File.read(summary_file_v2))
      expected_summary_file_v2['classes']['WebViewTest']['tests']['testSSLCert']["status"] = "passed"
      expected_summary_file_v2['classes']['WebViewTest']['tests']['testSSLCert']['duration'] = duration
      expected_summary_file_v2['classes']['WebViewTest']['tests']['testSSLCert']['start_time'] = Time.at(start_time.to_i)
      expected_summary_file_v2['classes']['WebViewTest']['tests']['testSSLCert']['crash_logs_count'] = 0
      expected_summary_file_v2['test_summary']['running'] = 0
      expected_summary_file_v2['test_summary']['passed'] = 1
      expected_summary_file_v2['classes']['WebViewTest']['tests_summary']['running'] = 0
      expected_summary_file_v2['classes']['WebViewTest']['tests_summary']['passed'] = 1

      expected_summary_file = JSON.parse(File.read(summary_file))
      expected_summary_file['test_status']['SUCCESS'] = 1
      expected_summary_file['test_status']['RUNNING'] = 0
      expected_summary_file["test_details"]["AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest"]['testSSLCert']['start_time'] = Time.at(start_time.to_i)
      expected_summary_file["test_details"]["AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest"]['testSSLCert']['status'] = "SUCCESS"
      expected_summary_file["test_details"]["AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest"]['testSSLCert']['duration'] = duration
      expected_summary_file["test_details"]["AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest"]['testSSLCert']['crash_logs_count'] = 0
      expected_summary_file["test_details"]["AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest"]['testSSLCert']['crash_log'] = ""

      expect(Utils).to receive(:write_to_file).once.with(summary_file, expected_summary_file.to_json).ordered.and_return(nil)
      expect(Utils).to receive(:write_to_file).once.with(summary_file_v2, expected_summary_file_v2.to_json).ordered.and_return(nil)

      xctest_session.send(:update_test_summary, "AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest/testSSLCert", start_time, "", "", "")
    end

    it 'should update test status and duration if test is timedout and killed by logmonitor' do
      duration = 18.173
      expect(xctest_session).to receive(:session_timedout?).and_return(false)
      expect(xctest_session).to receive(:parse_xctest_output).and_return(["SUCCESS", duration])
      xctest_session.instance_variable_set("@summary_file_v2", summary_file_v2)
      xctest_session.instance_variable_set("@summary_file", summary_file)

      expected_summary_file_v2 = JSON.parse(File.read(summary_file_v2))
      expected_summary_file_v2['classes']['WebViewTest']['tests']['testSSLCert']["status"] = "passed"
      expected_summary_file_v2['classes']['WebViewTest']['tests']['testSSLCert']['duration'] = duration
      expected_summary_file_v2['classes']['WebViewTest']['tests']['testSSLCert']['start_time'] = Time.at(start_time.to_i)
      expected_summary_file_v2['classes']['WebViewTest']['tests']['testSSLCert']['crash_logs_count'] = 0
      expected_summary_file_v2['test_summary']['running'] = 0
      expected_summary_file_v2['test_summary']['passed'] = 1
      expected_summary_file_v2['classes']['WebViewTest']['tests_summary']['running'] = 0
      expected_summary_file_v2['classes']['WebViewTest']['tests_summary']['passed'] = 1

      expected_summary_file = JSON.parse(File.read(summary_file))
      expected_summary_file['test_status']['SUCCESS'] = 1
      expected_summary_file['test_status']['RUNNING'] = 0
      expected_summary_file["test_details"]["AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest"]['testSSLCert']['start_time'] = Time.at(start_time.to_i)
      expected_summary_file["test_details"]["AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest"]['testSSLCert']['status'] = "SUCCESS"
      expected_summary_file["test_details"]["AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest"]['testSSLCert']['duration'] = duration
      expected_summary_file["test_details"]["AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest"]['testSSLCert']['crash_logs_count'] = 0
      expected_summary_file["test_details"]["AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest"]['testSSLCert']['crash_log'] = ""

      expect(Utils).to receive(:write_to_file).once.with(summary_file, expected_summary_file.to_json).ordered.and_return(nil)
      expect(Utils).to receive(:write_to_file).once.with(summary_file_v2, expected_summary_file_v2.to_json).ordered.and_return(nil)
      xctest_session.send(:update_test_summary, "AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest/testSSLCert", Time.at(start_time.to_i), duration, "", "TIMEDOUT")
    end

    it 'should not update duration if test is timedout and session is timedout as well' do
      allow(xctest_session).to receive(:parse_xctest_output).and_return(["TIMEDOUT", ""])
      expect(xctest_session).to receive(:session_timedout?).and_return(true)
      xctest_session.instance_variable_set("@summary_file_v2", summary_file_v2)
      xctest_session.instance_variable_set("@summary_file", summary_file)

      expected_summary_file_v2 = JSON.parse(File.read(summary_file_v2))
      expected_summary_file_v2['classes']['WebViewTest']['tests']['testSSLCert']["status"] = "timedout"
      expected_summary_file_v2['classes']['WebViewTest']['tests']['testSSLCert']['start_time'] = Time.at(start_time.to_i)
      expected_summary_file_v2['classes']['WebViewTest']['tests']['testSSLCert']['crash_logs_count'] = 0

      expected_summary_file_v2['test_summary']['running'] = 0
      expected_summary_file_v2['test_summary']['timedout'] = 1
      expected_summary_file_v2['classes']['WebViewTest']['tests_summary']['running'] = 0
      expected_summary_file_v2['classes']['WebViewTest']['tests_summary']['timedout'] = 1

      expected_summary_file = JSON.parse(File.read(summary_file))
      expected_summary_file['test_status']['TIMEDOUT'] = 1
      expected_summary_file['test_status']['RUNNING'] = 0
      expected_summary_file["test_details"]["AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest"]['testSSLCert']['status'] = "TIMEDOUT"

      expect(Utils).to receive(:write_to_file).once.with(summary_file, expected_summary_file.to_json).ordered.and_return(nil)
      expect(Utils).to receive(:write_to_file).once.with(summary_file_v2, expected_summary_file_v2.to_json).ordered.and_return(nil)

      xctest_session.send(:update_test_summary, "AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest/testSSLCert", start_time, "", "", "TIMEDOUT")
    end

    it 'should not update duration if test is errored' do
      allow(xctest_session).to receive(:parse_xctest_output).and_return(["ERROR", ""])
      xctest_session.instance_variable_set("@summary_file_v2", summary_file_v2)
      xctest_session.instance_variable_set("@summary_file", summary_file)

      expected_summary_file_v2 = JSON.parse(File.read(summary_file_v2))
      expected_summary_file_v2['classes']['WebViewTest']['tests']['testSSLCert']["status"] = "error"
      expected_summary_file_v2['classes']['WebViewTest']['tests']['testSSLCert']['start_time'] = Time.at(start_time.to_i)
      expected_summary_file_v2['classes']['WebViewTest']['tests']['testSSLCert']['crash_logs_count'] = 0
      expected_summary_file_v2['test_summary']['running'] = 0
      expected_summary_file_v2['test_summary']['error'] = 1
      expected_summary_file_v2['classes']['WebViewTest']['tests_summary']['running'] = 0
      expected_summary_file_v2['classes']['WebViewTest']['tests_summary']['error'] = 1

      expected_summary_file = JSON.parse(File.read(summary_file))
      expected_summary_file['test_status']['ERROR'] = 1
      expected_summary_file['test_status']['RUNNING'] = 0
      expected_summary_file["test_details"]["AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest"]['testSSLCert']['status'] = "ERROR"
      expect(Utils).to receive(:write_to_file).once.with(summary_file, expected_summary_file.to_json).ordered.and_return(nil)
      expect(Utils).to receive(:write_to_file).once.with(summary_file_v2, expected_summary_file_v2.to_json).ordered.and_return(nil)

      xctest_session.send(:update_test_summary, "AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest/testSSLCert", start_time, "", "", "ERROR")
    end

    context "update_test_summary for errored tests which inturn passed" do
      let(:start_time) { Time.now.to_i }

      it 'should update status to passed even if test is errored' do
        duration = 60.5
        xctest_session.instance_variable_set(:@instrumentation_file, "#{__dir__}/../../fixtures/xctestrun_instrumentation_success_with_device_conn_issue.txt")
        allow(xctest_session).to receive(:parse_xctest_output).and_return(["SUCCESS", duration])
        xctest_session.instance_variable_set("@summary_file_v2", summary_file_v2)
        xctest_session.instance_variable_set("@summary_file", summary_file)

        expected_summary_file_v2 = JSON.parse(File.read(summary_file_v2))
        expected_summary_file_v2['classes']['WebViewTest']['tests']['testSSLCert']["status"] = "passed"
        expected_summary_file_v2['classes']['WebViewTest']['tests']['testSSLCert']['start_time'] = Time.at(start_time.to_i)
        expected_summary_file_v2['classes']['WebViewTest']['tests']['testSSLCert']['duration'] = duration
        expected_summary_file_v2['classes']['WebViewTest']['tests']['testSSLCert']['crash_logs_count'] = 0

        expected_summary_file_v2['test_summary']['running'] = 0
        expected_summary_file_v2['test_summary']['passed'] = 1
        expected_summary_file_v2['classes']['WebViewTest']['tests_summary']['running'] = 0
        expected_summary_file_v2['classes']['WebViewTest']['tests_summary']['passed'] = 1

        expected_summary_file = JSON.parse(File.read(summary_file))
        expected_summary_file['test_status']['SUCCESS'] = 1
        expected_summary_file['test_status']['RUNNING'] = 0
        expected_summary_file["test_details"]["AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest"]['testSSLCert']['start_time'] = Time.at(start_time.to_i)
        expected_summary_file["test_details"]["AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest"]['testSSLCert']['status'] = "SUCCESS"
        expected_summary_file["test_details"]["AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest"]['testSSLCert']['duration'] = duration
        expected_summary_file["test_details"]["AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest"]['testSSLCert']['crash_logs_count'] = 0
        expected_summary_file["test_details"]["AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest"]['testSSLCert']['crash_log'] = ""

        expect(Utils).to receive(:write_to_file).once.with(summary_file, expected_summary_file.to_json).ordered.and_return(nil)
        expect(Utils).to receive(:write_to_file).once.with(summary_file_v2, expected_summary_file_v2.to_json).ordered.and_return(nil)

        xctest_session.send(:update_test_summary, "AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest/testSSLCert", start_time, duration, "", "ERROR")
      end

      it 'should update status to error even if test did not complete execution' do
        xctest_session.instance_variable_set(:@instrumentation_file, "#{__dir__}/../../fixtures/xctestrun_instrumentation_error_device_conn_issue.txt")
        expect(xctest_session).to_not receive(:parse_xctest_output)
        xctest_session.instance_variable_set("@summary_file_v2", summary_file_v2)
        xctest_session.instance_variable_set("@summary_file", summary_file)

        expected_summary_file_v2 = JSON.parse(File.read(summary_file_v2))
        expected_summary_file_v2['classes']['WebViewTest']['tests']['testSSLCert']["status"] = "error"
        expected_summary_file_v2['classes']['WebViewTest']['tests']['testSSLCert']['start_time'] = Time.at(start_time.to_i)
        expected_summary_file_v2['classes']['WebViewTest']['tests']['testSSLCert']['crash_logs_count'] = 0

        expected_summary_file_v2['test_summary']['running'] = 0
        expected_summary_file_v2['test_summary']['error'] = 1
        expected_summary_file_v2['classes']['WebViewTest']['tests_summary']['running'] = 0
        expected_summary_file_v2['classes']['WebViewTest']['tests_summary']['error'] = 1

        expected_summary_file = JSON.parse(File.read(summary_file))
        expected_summary_file['test_status']['ERROR'] = 1
        expected_summary_file['test_status']['RUNNING'] = 0
        expected_summary_file["test_details"]["AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest"]['testSSLCert']['status'] = "ERROR"

        expect(Utils).to receive(:write_to_file).once.with(summary_file, expected_summary_file.to_json).ordered.and_return(nil)
        expect(Utils).to receive(:write_to_file).once.with(summary_file_v2, expected_summary_file_v2.to_json).ordered.and_return(nil)

        xctest_session.send(:update_test_summary, "AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest/testSSLCert", start_time, "", "", "ERROR")
      end

      it 'should update status to failed even if test is errored' do
        duration = 60.5
        xctest_session.instance_variable_set(:@instrumentation_file, "#{__dir__}/../../fixtures/xctestrun_instrumentation_failure_with_device_conn_issue.txt")
        allow(xctest_session).to receive(:parse_xctest_output).and_return(["FAILED", duration])
        xctest_session.instance_variable_set("@summary_file_v2", summary_file_v2)
        xctest_session.instance_variable_set("@summary_file", summary_file)

        expected_summary_file_v2 = JSON.parse(File.read(summary_file_v2))
        expected_summary_file_v2['classes']['WebViewTest']['tests']['testSSLCert']["status"] = "failed"
        expected_summary_file_v2['classes']['WebViewTest']['tests']['testSSLCert']['start_time'] = Time.at(start_time.to_i)
        expected_summary_file_v2['classes']['WebViewTest']['tests']['testSSLCert']['duration'] = duration
        expected_summary_file_v2['classes']['WebViewTest']['tests']['testSSLCert']['crash_logs_count'] = 0

        expected_summary_file_v2['test_summary']['running'] = 0
        expected_summary_file_v2['test_summary']['failed'] = 1
        expected_summary_file_v2['classes']['WebViewTest']['tests_summary']['running'] = 0
        expected_summary_file_v2['classes']['WebViewTest']['tests_summary']['failed'] = 1

        expected_summary_file = JSON.parse(File.read(summary_file))
        expected_summary_file['test_status']['FAILED'] = 1
        expected_summary_file['test_status']['RUNNING'] = 0
        expected_summary_file["test_details"]["AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest"]['testSSLCert']['start_time'] = Time.at(start_time.to_i)
        expected_summary_file["test_details"]["AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest"]['testSSLCert']['status'] = "FAILED"
        expected_summary_file["test_details"]["AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest"]['testSSLCert']['duration'] = duration
        expected_summary_file["test_details"]["AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest"]['testSSLCert']['crash_logs_count'] = 0
        expected_summary_file["test_details"]["AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest"]['testSSLCert']['crash_log'] = ""

        expect(Utils).to receive(:write_to_file).once.with(summary_file, expected_summary_file.to_json).ordered.and_return(nil)
        expect(Utils).to receive(:write_to_file).once.with(summary_file_v2, expected_summary_file_v2.to_json).ordered.and_return(nil)

        xctest_session.send(:update_test_summary, "AWSDeviceFarmiOSReferenceAppUITestsSwift/WebViewTest/testSSLCert", start_time, duration, "", "ERROR")
      end
    end
  end

  describe "get_bstack_reason" do
    let(:test_name) { "test_name" }

    it 'should return empty string if test run was complete' do
      test_status = 0
      expect(File).to receive(:readlines).and_return([])
      output = xctest_session.send(:get_bstack_reason, test_status, test_name)
      expect(output).to be_eql("")
    end

    it 'should return TIMEDOUT if xcodebuild test run command was timedout' do
      test_status = 137
      output = xctest_session.send(:get_bstack_reason, test_status, test_name)
      expect(output).to be_eql("TIMEDOUT")
    end

    it 'should return ERROR if xcodebuild test run command was exited' do
      test_status = 70
      output = xctest_session.send(:get_bstack_reason, test_status, test_name)
      expect(output).to be_eql("ERROR")
    end
  end

  describe "initialize_logs_stability_metrics" do
    let(:session_id) { "1234" }
    let(:file_name) { "/tmp/#{session_id}_xctest_logs_stability" }

    it "should setup default params in logs stability file" do
      xctest_session.instance_variable_set(:@session_id, session_id)
      expect(FileUtils).to receive(:touch).with(file_name).and_return(true)
      expect(Utils).to receive(:write_to_file).with(file_name, anything).and_return(true)

      xctest_session.send(:initialize_logs_stability_metrics)
    end
  end

  describe "handle_test_for_logs_stability_metrics" do
    let(:logs_stability_file_path) { "./spec/fixtures/sessionid_xctest_logs_stability" }

    it "does not adjust json if file is not present" do
      xctest_session.instance_variable_set(:@logs_stability_file, "file_name")
      expect(File).to receive(:exists?).with("file_name").and_return(false)
      expect(File).not_to receive(:open)

      xctest_session.send(:handle_test_for_logs_stability_metrics, "testname", 1)
    end

    it "adjust uploaded count by x" do
      xctest_session.instance_variable_set(:@logs_stability_file, logs_stability_file_path)
      logs_stability_file_data = JSON.parse(File.read(logs_stability_file_path))
      logs_stability_file_data["instrumentationLogs"]["uploaded"] = logs_stability_file_data["instrumentationLogs"]["uploaded"] + 4
      logs_stability_file_data["networkLogs"]["uploaded"] = logs_stability_file_data["networkLogs"]["uploaded"] + 4
      logs_stability_file_data["deviceLogs"]["uploaded"] = logs_stability_file_data["deviceLogs"]["uploaded"] + 4

      expect(File).to receive(:exists?).with(logs_stability_file_path).and_return(true)
      expect_any_instance_of(File).to receive(:flock).and_return(true)
      expect_any_instance_of(File).to receive(:rewind).and_return(true)
      expect_any_instance_of(File).to receive(:write).with(logs_stability_file_data.to_json).and_return(true)
      expect_any_instance_of(File).to receive(:flush).and_return(true)
      expect_any_instance_of(File).to receive(:truncate).and_return(true)

      xctest_session.send(:handle_test_for_logs_stability_metrics, "testname", 4)
    end
  end

  describe "remove_newlines" do
    it 'should strip whitespace from each element in the array' do
      # Test with various whitespace characters
      input = [
        "test1\n",
        "test2 ",
        " test3",
        "\ttest4\t",
        "test5\r\n",
        "test6\n\r",
        "test7 with spaces "
      ]

      expected = [
        "test1",
        "test2",
        "test3",
        "test4",
        "test5",
        "test6",
        "test7 with spaces"
      ]

      result = xctest_session.send(:remove_newlines, input)
      expect(result).to eq(expected)
    end

    it 'should return nil when nil is passed' do
      expect(xctest_session.send(:remove_newlines, nil)).to eq(nil)
    end

    it 'should handle empty array' do
      expect(xctest_session.send(:remove_newlines, [])).to eq([])
    end

    it 'should handle array with empty strings' do
      input = ["", " ", "\n", "\t"]
      expected = ["", "", "", ""]

      result = xctest_session.send(:remove_newlines, input)
      expect(result).to eq(expected)
    end
  end

  describe "filter_testlist" do
    it 'should add the tests passed in only-testing as is in case of dynamic xcuitests for the given testsuite files' do
      tests1 = ["product_module_name/className1/tesName1", "product_module_name/className2/tesName2"]
      tests2 = ["other_product_module_name/className1/tesName1"]
      tests = tests1 + tests2
      test_params = {
        "only-testing" => tests,
        "is_dynamic_xcuitest" => "true"
      }.to_json
      xctest_session.instance_variable_set(:@testsuite_files, ["/tmp/93203920_xctest_product_module_name", "/tmp/93203920_xctest_other_product_module_name"])
      expect(File).to receive(:exists?).with("/tmp/93203920_xctest_product_module_name").once.and_return(true)
      expect(File).to receive(:exists?).with("/tmp/93203920_xctest_other_product_module_name").once.and_return(true)
      expect(xctest_session).to receive(:get_testlist).twice.and_return([""])
      expect(xctest_session).not_to receive(:handle_testsuite_parse_empty)
      expect(Utils).to receive(:write_to_file).with("/tmp/93203920_xctest_product_module_name", tests1.join("\n")).once
      expect(Utils).to receive(:write_to_file).with("/tmp/93203920_xctest_other_product_module_name", tests2.join("\n")).once
      xctest_session.send(:filter_testlist, test_params)
    end

    it 'should call handle_testsuite_parse_empty when only-testing filters all tests present' do
      tests = ["product_module_name/className1/testName1", "product_module_name/className2/testName2"]
      test_params = {
        "only-testing" => tests
      }.to_json
      xctest_session.instance_variable_set(:@testsuite_files, ["/tmp/93203920_xctest_product_module_name"])
      expect(File).to receive(:exists?).with("/tmp/93203920_xctest_product_module_name").once.and_return(true)
      expect(xctest_session).to receive(:get_testlist).once.and_return(["product_module_name/className1/testName3"])
      expect(xctest_session).to receive(:handle_testsuite_parse_empty)
      expect(Utils).to receive(:write_to_file).with("/tmp/93203920_xctest_product_module_name", [].join("\n")).once
      xctest_session.send(:filter_testlist, test_params)
    end

    it 'should call remove_newlines on only-testing test names' do
      tests = ["product_module_name/className1/testName1\n", "product_module_name/className2/testName2 "]
      expected_clean_tests = ["product_module_name/className1/testName1", "product_module_name/className2/testName2"]
      test_params = {
        "only-testing" => tests
      }.to_json
      xctest_session.instance_variable_set(:@testsuite_files, ["/tmp/93203920_xctest_product_module_name"])
      expect(File).to receive(:exists?).with("/tmp/93203920_xctest_product_module_name").once.and_return(true)
      expect(xctest_session).to receive(:get_testlist).once.and_return(["product_module_name/className1/testName1", "product_module_name/className2/testName2"])
      expect(xctest_session).to receive(:remove_newlines).with(tests).and_return(expected_clean_tests)
      expect(Utils).to receive(:write_to_file).with("/tmp/93203920_xctest_product_module_name", anything).once
      xctest_session.send(:filter_testlist, test_params)
    end
  end

  describe "handle_testsuite_parse_empty" do
    it 'should not update summary file v1 if not present' do
      xctest_session.instance_variable_set(:@summary_file, nil)
      xctest_session.instance_variable_set(:@testsuite_files, ["/tmp/xctest_BullsEye.log"])
      expect(BrowserStack::Zombie).to receive(:push_logs).and_return(true)
      expect(xctest_session).to receive(:send_error_reason_in_file).once.and_return(true)
      expect(xctest_session).to receive(:create_upload_request).once.and_return(nil)

      xctest_session.send(:handle_testsuite_parse_empty, "filename")
    end

    it 'should update summary file v1 if file is present' do
      summary_file = double("summary_file")
      xctest_session.instance_variable_set(:@summary_file, summary_file)
      xctest_session.instance_variable_set(:@testsuite_files, ["/tmp/xctest_BullsEye.log"])
      expect(File).to receive(:exists?).with(summary_file).and_return(true)
      expect(BrowserStack::Zombie).to receive(:push_logs).and_return(true)
      expect(xctest_session).to receive(:send_error_reason_in_file).twice.and_return(true)
      expect(xctest_session).to receive(:create_upload_request).once.and_return(nil)

      xctest_session.send(:handle_testsuite_parse_empty, "filename")
    end
  end

  describe "handle_testsuite_parse_failed" do
    it 'should not update summary file v1 if not present' do
      xctest_session.instance_variable_set(:@summary_file, nil)
      allow(BrowserStack::Zombie).to receive(:push_logs).and_return(true)
      expect(xctest_session).to receive(:send_error_reason_in_file).once.and_return(true)

      xctest_session.send(:handle_testsuite_parse_failed, StandardError.new("Something went wrong"))
    end

    it 'should update summary file v1 if file is present' do
      summary_file = double("summary_file")
      xctest_session.instance_variable_set(:@summary_file, summary_file)
      expect(File).to receive(:exists?).with(summary_file).and_return(true)
      allow(BrowserStack::Zombie).to receive(:push_logs).and_return(true)
      expect(xctest_session).to receive(:send_error_reason_in_file).twice.and_return(true)

      xctest_session.send(:handle_testsuite_parse_failed, StandardError.new("Something went wrong"))
    end
  end

  describe "parse_flutter_xctest_output" do
    it 'should give testname/status for sessions having atleast one test passed' do
      instrumentation = "#{__dir__}/../../fixtures/fluttertest_instrumentation_success.txt"
      xctest_session.instance_variable_set(:@instrumentation_file, instrumentation)
      parsed_data = xctest_session.send(:parse_flutter_xctest_output)
      expected_output = [["testGetTextLocalTest", "SUCCESS"], ["testGetOtherTests", "FAILED"]]
      expect(parsed_data).to eq(expected_output)
    end

    it 'should detect failed testname/status for sessions having at least one failed test case' do
      instrumentation = "#{__dir__}/../../fixtures/fluttertest_instrumentation_failure.txt"
      xctest_session.instance_variable_set(:@instrumentation_file, instrumentation)
      parsed_data = xctest_session.send(:parse_flutter_xctest_output)
      expected_output = [["testGetTextLocalTest", "FAILED"], ["testGetOtherTests", "FAILED"]]
      expect(parsed_data).to eq(expected_output)
    end

    it 'test name should be testInvocations with status skipped if running unit tests instead of ui tests' do
      instrumentation = "#{__dir__}/../../fixtures/fluttertest_instrumentation_skipped.txt"
      xctest_session.instance_variable_set(:@instrumentation_file, instrumentation)
      parsed_data = xctest_session.send(:parse_flutter_xctest_output)
      expected_output = []
      expect(parsed_data).to eq(expected_output)
    end
  end

  describe "update_status_for_xcretry" do
    let(:testname) { "testGameStyleSwitch001" }
    it 'should give passed as result for retry session with SUCCEEDED present in log' do
      instrumentation = "#{__dir__}/../../fixtures/xcuitest_instrumentation_xcretry_success.txt"
      xctest_session.instance_variable_set(:@instrumentation_file, instrumentation)
      status = xctest_session.send(:update_status_for_xcretry, testname, 'SUCCEEDED', 'passed')
      expect(status).to eq("SUCCESS")
    end

    it 'should give failed as result for retry session with FAILED present in log' do
      instrumentation = "#{__dir__}/../../fixtures/xcuitest_instrumentation_xcretry_failed.txt"
      xctest_session.instance_variable_set(:@instrumentation_file, instrumentation)
      status = xctest_session.send(:update_status_for_xcretry, testname, '', '')
      expect(status).to eq("FAILED")
    end

    it 'should give failed as result for retry session with all statuses present in log' do
      instrumentation = "#{__dir__}/../../fixtures/xcuitest_instrumentation_xcretry_all_status.txt"
      xctest_session.instance_variable_set(:@instrumentation_file, instrumentation)
      status = xctest_session.send(:update_status_for_xcretry, testname, 'SUCCEEDED', 'passed')
      expect(status).to eq("FAILED")
    end

    it 'should give passed as result for retry session with test case passed and no status in log' do
      instrumentation = "#{__dir__}/../../fixtures/xcuitest_instrumentation_xcretry_no_status_passed.txt"
      xctest_session.instance_variable_set(:@instrumentation_file, instrumentation)
      status = xctest_session.send(:update_status_for_xcretry, testname, '', 'passed')
      expect(status).to eq("SUCCESS")
    end

    it 'should give passed as result for retry session with test case failed and no status in log' do
      instrumentation = "#{__dir__}/../../fixtures/xcuitest_instrumentation_xcretry_no_status_failed.txt"
      xctest_session.instance_variable_set(:@instrumentation_file, instrumentation)
      status = xctest_session.send(:update_status_for_xcretry, testname, '', '')
      expect(status).to eq("FAILED")
    end
  end

  describe "#parse_instru_log" do
    let(:system_resources) { "system_resources" }
    let(:testname) { 'testGameStyleSwitch001' }

    before(:each) do
      allow(xctest_session).to receive(:get_md5).and_return("testid12")
      xctest_session.instance_variable_set(:@session_id, 'sessionid1234')
    end

    it 'should push logs for xctest duration value for failure case' do
      instrumentation = "#{__dir__}/../../fixtures/xcuitest_instrumentation_wrong_class_2.txt"
      expect(DeviceManager).to receive(:blocked_devices_count).and_return(2)
      expect(BrowserStack::OSUtils).to receive(:get_system_resources).and_return(system_resources)
      xctest_session.instance_variable_set(:@instrumentation_file, instrumentation)
      pager_data = {
        "session_id" => 'sessionid1234',
        "data" => {
          "testid" => 'sessionid1234testid12',
          "device_test_execution_time" => '0.001',
          "xcodebuild_process_time" => nil,
          "num_blocked_devices" => 2,
          "system_resources" => system_resources
        }
      }
      expect(BrowserStack::Zombie).to receive(:push_logs).with("xcodebuild-execution-stats", '', pager_data)
      xctest_session.send(:parse_instru_logs, "sample")
    end

    it 'should push logs for xctest duration value for success case' do
      instrumentation = "#{__dir__}/../../fixtures/xcuitest_instrumentation_success.txt"
      expect(DeviceManager).to receive(:blocked_devices_count).and_return(2)
      expect(BrowserStack::OSUtils).to receive(:get_system_resources).and_return(system_resources)
      xctest_session.instance_variable_set(:@test_names, Set.new)
      xctest_session.instance_variable_set(:@test_states, {})
      xctest_session.instance_variable_set(:@session_id, 'sessionid1234')
      xctest_session.instance_variable_set(:@instrumentation_file, instrumentation)
      pager_data = {
        "session_id" => 'sessionid1234',
        "data" => {
          "testid" => 'sessionid1234testid12',
          "device_test_execution_time" => '5.296',
          "xcodebuild_process_time" => nil,
          "num_blocked_devices" => 2,
          "system_resources" => system_resources
        }
      }
      expect(BrowserStack::Zombie).to receive(:push_logs).with("xcodebuild-execution-stats", '', pager_data)
      xctest_session.send(:parse_instru_logs, "sample")
    end

    it 'should push logs for test meta info parsing' do
      instrumentation = "#{__dir__}/../../fixtures/xcuitest_instrumentation_with_meta_info.txt"
      expect(DeviceManager).to receive(:blocked_devices_count).and_return(2)
      expect(BrowserStack::OSUtils).to receive(:get_system_resources).and_return(system_resources)
      xctest_session.instance_variable_set(:@session_id, 'sessionid1234')
      xctest_session.instance_variable_set(:@instrumentation_file, instrumentation)
      xctest_session.instance_variable_set(:@test_meta_info, {})
      expect(BrowserStack::Zombie).to receive(:push_logs).with("xcodebuild-execution-stats", "", anything)
      xctest_session.send(:parse_instru_logs, "sample")

      test_meta_info = xctest_session.instance_variable_get(:@test_meta_info)
      expect(test_meta_info).to eq({ "runner_exit_74" => 1, "unexpected_exit" => 1 })
    end

    it 'should not push logs for test issue when no issue occured' do
      instrumentation = "#{__dir__}/../../fixtures/xcuitest_instrumentation_success.txt"
      expect(DeviceManager).to receive(:blocked_devices_count).and_return(2)
      expect(BrowserStack::OSUtils).to receive(:get_system_resources).and_return(system_resources)
      xctest_session.instance_variable_set(:@session_id, 'sessionid1234')
      xctest_session.instance_variable_set(:@instrumentation_file, instrumentation)
      xctest_session.instance_variable_set(:@test_meta_info, {})
      expect(BrowserStack::Zombie).to receive(:push_logs).with("xcodebuild-execution-stats", "", anything)
      xctest_session.send(:parse_instru_logs, "sample")

      test_meta_info = xctest_session.instance_variable_get(:@test_meta_info)
      expect(test_meta_info).to eq({})
    end
  end

  describe "parse_xctest_output" do
    let(:system_resources) { "system_resources" }
    let(:testname) { 'testGameStyleSwitch001' }

    before(:each) do
      allow(xctest_session).to receive(:get_md5).and_return("testid12")
    end

    it 'should give skipped as result for sessions having 0 test executed' do
      instrumentation = "#{__dir__}/../../fixtures/xcuitest_instrumentation_wrong_class.txt"
      xctest_session.instance_variable_set(:@instrumentation_file, instrumentation)
      result, duration = xctest_session.send(:parse_xctest_output, "sample")
      expect(result).to eq("IGNORED")
    end

    it 'should give skipped as result for sessions having Test Case skipped logline' do
      instrumentation = "#{__dir__}/../../fixtures/xcuitest_instrumentation_skipped_test.txt"
      xctest_session.instance_variable_set(:@instrumentation_file, instrumentation)
      result, duration = xctest_session.send(:parse_xctest_output, "sampleTestName")
      expect(result).to eq("IGNORED")
    end

    it 'should give failed as result for sessions having 1 test failed and 1 test passed' do
      instrumentation = "#{__dir__}/../../fixtures/xcuitest_instrumentation_wrong_class_1.txt"
      xctest_session.instance_variable_set(:@instrumentation_file, instrumentation)
      result, duration = xctest_session.send(:parse_xctest_output, "sample")
      expect(result).to eq("FAILED")
    end

    it 'should give failed as result for sessions having 1 test failed' do
      instrumentation = "#{__dir__}/../../fixtures/xcuitest_instrumentation_wrong_class_2.txt"
      xctest_session.instance_variable_set(:@instrumentation_file, instrumentation)
      result, duration = xctest_session.send(:parse_xctest_output, "sample")
      expect(result).to eq("FAILED")
    end

    it 'should update status from failed to passed for success xctestrun instrumentation log' do
      instrumentation = "#{__dir__}/../../fixtures/xcuitest_instrumentation_xcretry_success.txt"
      params = {
        "retry_params" => {
          "relaunch_enabled" => "true"
        }
      }
      xctest_session.instance_variable_set(:@params, params)
      xctest_session.instance_variable_set(:@instrumentation_file, instrumentation)
      result, duration = xctest_session.send(:parse_xctest_output, testname)
      expect(result).to eq("SUCCESS")
    end

    it 'should update status from failed to failed for failed xctestrun instrumentation log' do
      instrumentation = "#{__dir__}/../../fixtures/xcuitest_instrumentation_xcretry_failed.txt"
      params = {
        "retry_params" => {
          "relaunch_enabled" => "true"
        }
      }
      xctest_session.instance_variable_set(:@params, params)
      xctest_session.instance_variable_set(:@instrumentation_file, instrumentation)
      result, duration = xctest_session.send(:parse_xctest_output, testname)
      expect(result).to eq("FAILED")
    end

    it 'should update status from failed to failed for all_status xctestrun instrumentation log' do
      instrumentation = "#{__dir__}/../../fixtures/xcuitest_instrumentation_xcretry_all_status.txt"
      params = {
        "retry_params" => {
          "relaunch_enabled" => "true"
        }
      }
      xctest_session.instance_variable_set(:@params, params)
      xctest_session.instance_variable_set(:@instrumentation_file, instrumentation)
      result, duration = xctest_session.send(:parse_xctest_output, testname)
      expect(result).to eq("FAILED")
    end

    it 'should update status from failed to passed for xctestrun instrumentation with no final status log' do
      instrumentation = "#{__dir__}/../../fixtures/xcuitest_instrumentation_xcretry_no_status_passed.txt"
      params = {
        "retry_params" => {
          "relaunch_enabled" => "true"
        }
      }
      xctest_session.instance_variable_set(:@params, params)
      xctest_session.instance_variable_set(:@instrumentation_file, instrumentation)
      result, duration = xctest_session.send(:parse_xctest_output, testname)
      expect(result).to eq("SUCCESS")
    end

    it 'should update status from failed to failed for xctestrun instrumentation with no final status log' do
      instrumentation = "#{__dir__}/../../fixtures/xcuitest_instrumentation_xcretry_no_status_failed.txt"
      params = {
        "retry_params" => {
          "relaunch_enabled" => "true"
        }
      }
      xctest_session.instance_variable_set(:@params, params)
      xctest_session.instance_variable_set(:@instrumentation_file, instrumentation)
      result, duration = xctest_session.send(:parse_xctest_output, testname)
      expect(result).to eq("FAILED")
    end

    it 'should give failed as result for sessions having 1 test executed with 1 failure' do
      instrumentation = "#{__dir__}/../../fixtures/xcuitest_instrumentation_wrong_class_3.txt"
      xctest_session.instance_variable_set(:@instrumentation_file, instrumentation)
      result, duration = xctest_session.send(:parse_xctest_output, "sample")
      expect(result).to eq("FAILED")
    end

    it 'should give success as result for sessions having all passed tests' do
      instrumentation = "#{__dir__}/../../fixtures/xcuitest_instrumentation_success.txt"
      xctest_session.instance_variable_set(:@instrumentation_file, instrumentation)
      result, duration = xctest_session.send(:parse_xctest_output, "sample")
      expect(result).to eq("SUCCESS")
    end

    it 'should give failed as result for sessions having failed tests' do
      instrumentation = "#{__dir__}/../../fixtures/xcuitest_instrumentation_failure.txt"
      xctest_session.instance_variable_set(:@instrumentation_file, instrumentation)
      result, duration = xctest_session.send(:parse_xctest_output, "sample")
      expect(result).to eq("FAILED")
    end
  end

  describe '#delete_xctest_runner_app_cache' do
    before(:each) do
      device = "00008030-001044C80EF9802E"
      xctest_session.instance_variable_set(:@device, device)
      xctest_session.instance_variable_set(:@device_config, { "device_version" => 17.0 })
      xctest_session.instance_variable_set(:@session_id, 'sessionid1234')
      xctest_session.instance_variable_set(:@params, { "test_suite_bundle_id" => "test_suite_bundle_id" } )
    end

    it 'deletes the cache and does not push logs to Zombie' do
      expect(BrowserStack::OSUtils).to receive(:execute).and_return(["", 0])
      expect(BrowserStack::Zombie).to_not receive(:push_logs)
      xctest_session.send(:delete_xctest_runner_app_cache)
    end

    it 'attempts to delete the cache twice and pushes logs to Zombie if both attempts fail' do
      expect(BrowserStack::OSUtils).to receive(:execute).twice.and_return(["", 1])
      expect(BrowserStack::Zombie).to receive(:push_logs).with("xctest-cleanup-files-failure", "xctest-cleanup-files-failure", { "session_id" => "sessionid1234", "exit_code" => 1 })
      xctest_session.send(:delete_xctest_runner_app_cache)
    end
  end

  describe "#generated_session_files" do
    it "should return the array of files and directories generated during the session" do
      device = "00008030-001044C80EF9802E"
      xctest_session.instance_variable_set(:@device, device)
      xctest_session.instance_variable_set(:@session_id, "sessionid")

      expected_paths = [
        "/tmp/00008030-001044C80EF9802E_xctestrun.xctestrun",
        "/tmp/00008030-001044C80EF9802E_product_module_name",
        "/tmp/00008030-001044C80EF9802E-xctest.pid",
        "/tmp/00008030-001044C80EF9802E-xctesttimeout.pid",
        "/tmp/00008030-001044C80EF9802E_xctest_summary.json",
        "/tmp/00008030-001044C80EF9802E_xctest_summary_v2.json",
        "/tmp/00008030-001044C80EF9802E_xcresult_bundle",
        "/tmp/00008030-001044C80EF9802E_result-bundle.xcresult.zip",
        "/tmp/custom_har_file_sessionid.har"
      ]
      expect(xctest_session.send(:generated_session_files)).to eq(expected_paths)
    end

    it "should return the array of files and directories generated during the session for ios17" do
      device = "00008030-001044C80EF9802E"
      xctest_session.instance_variable_set(:@device, device)
      xctest_session.instance_variable_set(:@device_config, { "device_version" => 17.0 })
      xctest_session.instance_variable_set(:@params, { "test_suite_bundle_id" => "test_suite_bundle_id" } )
      xctest_session.instance_variable_set(:@session_id, "sessionid")
      expected_paths = [
        "/tmp/00008030-001044C80EF9802E_xctestrun.xctestrun",
        "/tmp/00008030-001044C80EF9802E_product_module_name",
        "/tmp/00008030-001044C80EF9802E-xctest.pid",
        "/tmp/00008030-001044C80EF9802E-xctesttimeout.pid",
        "/tmp/00008030-001044C80EF9802E_xctest_summary.json",
        "/tmp/00008030-001044C80EF9802E_xctest_summary_v2.json",
        "/tmp/00008030-001044C80EF9802E_xcresult_bundle",
        "/tmp/00008030-001044C80EF9802E_result-bundle.xcresult.zip",
        "/tmp/custom_har_file_sessionid.har"
      ]
      expect(xctest_session.send(:generated_session_files)).to eq(expected_paths)
    end
  end

  describe "#ensure_session_stop" do
    it "should not execute ensure_session_stop if spawn xctest not running" do
      device = "00008030-001044C80EF9802E"
      xctest_session.instance_variable_set(:@device, device)
      expect(AppAutomateFrameworks::ProcessUtils).to receive(:get_process_id).and_return("BSTACK_NAN")
      expect(AppAutomateFrameworks::ProcessUtils).to receive(:process_running?).and_return(false)
      expect(AppAutomateFrameworks::ProcessUtils).to_not receive(:kill_pid)
      xctest_session.ensure_session_stop
    end

    it "should kill spawn xctest pid if spawn xctest running" do
      device = "00008030-001044C80EF9802E"
      xctest_session.instance_variable_set(:@device, device)

      xctest_manager = double("xctest_manager")
      allow(xctest_manager).to receive(:kill_idle_process)
      allow(xctest_manager).to receive(:stop)

      allow(AppAutomateFrameworks::ProcessUtils).to receive(:process_running?).and_return(true)
      expect(AppAutomateFrameworks::ProcessUtils).to receive(:kill_pid)
      expect(AppAutomateFrameworks::XCTestTimeoutManager).to receive(:new).and_return(xctest_manager)
      expect(xctest_manager).to receive(:kill_idle_process)
      expect(xctest_manager).to receive(:stop)

      app_percy = double("app_percy")
      allow(AppPercy::Session).to receive(:new).and_return(app_percy)
      allow(app_percy).to receive(:stop)
      expect(app_percy).to receive(:stop)
      expect(BrowserStack::DeviceLogger).to receive(:destroy)
      expect(MitmProxy).to receive(:stop_proxy)
      allow(Dir).to receive(:glob).with("/tmp/#{device}_xctest_*").and_return(['/tmp/file1', '/tmp/file2'])
      allow(Dir).to receive(:glob).with("/tmp/#{device}_xcuitest_instrumentation*").and_return(['/tmp/file1', '/tmp/file2'])
      expect(FileUtils).to receive(:rm_rf).exactly(6).times
      expect(xctest_session).to receive(:cleanup_files)
      xctest_session.ensure_session_stop
    end
  end

  describe "#xcode_retry_flags" do
    it "should append no flags" do
      xctest_session.instance_variable_set(:@params, {})
      flags = xctest_session.send(:xcode_retry_flags)

      expect(flags).to include("")
    end

    it "should append relaunch flags with YES" do
      params = {
        "retry_params" => {
          "relaunch_enabled" => "true"
        }
      }
      xctest_session.instance_variable_set(:@params, params)

      flags = xctest_session.send(:xcode_retry_flags)
      expect(flags).to include("-test-repetition-relaunch-enabled YES")
    end

    it "should append relaunch flags with NO" do
      params = {
        "retry_params" => {
          "relaunch_enabled" => "false"
        }
      }
      xctest_session.instance_variable_set(:@params, params)

      flags = xctest_session.send(:xcode_retry_flags)
      expect(flags).to include("-test-repetition-relaunch-enabled NO")
    end

    it "should append retry flags" do
      params = {
        "retry_params" => {
          "retry_on_failure" => "true"
        }
      }
      xctest_session.instance_variable_set(:@params, params)

      flags = xctest_session.send(:xcode_retry_flags)
      expect(flags).to include("-retry-tests-on-failure")
    end

    it "should not append retry flags" do
      params = {
        "retry_params" => {
          "retry_on_failure" => "false"
        }
      }
      xctest_session.instance_variable_set(:@params, params)

      flags = xctest_session.send(:xcode_retry_flags)
      expect(flags).to eq("")
    end

    it "should append test iteration flags" do
      params = {
        "retry_params" => {
          "test_iterations" => "4"
        }
      }
      xctest_session.instance_variable_set(:@params, params)

      flags = xctest_session.send(:xcode_retry_flags)
      expect(flags).to include("-test-iterations 4")
    end

    it "should append multiple flags" do
      params = {
        "retry_params" => {
          "test_iterations" => "4",
          "retry_on_failure" => "true",
          "relaunch_enabled" => "false"
        }
      }
      xctest_session.instance_variable_set(:@params, params)

      flags = xctest_session.send(:xcode_retry_flags)
      expect(flags).to include("-test-iterations 4")
      expect(flags).to include("-retry-tests-on-failure")
      expect(flags).to include("-test-repetition-relaunch-enabled NO")
    end
  end

  describe "#v2_video_enabled?" do
    it "should set true for valid v2 video params" do
      params = {
        'video' => true,
        'video_params_v2' => { 'v2' => "true" }.to_json
      }
      xctest_session.instance_variable_set(:@params, params)
      expect(xctest_session.send(:v2_video_enabled?)).to eq(true)
    end

    it "should set false for no video but valid v2 video params" do
      params = {
        'video' => false,
        'video_params_v2' => { 'v2' => "true" }.to_json
      }
      xctest_session.instance_variable_set(:@params, params)
      expect(xctest_session.send(:v2_video_enabled?)).to eq(false)
    end

    it "should set false for no v2 video params" do
      params = {
        'video' => false
      }
      xctest_session.instance_variable_set(:@params, params)
      expect(xctest_session.send(:v2_video_enabled?)).to eq(false)
    end
  end

  describe "#start_app_percy" do
    let(:device_id) { "00008030-001044C80EF9802E" }
    let(:device) do
      {
        'device_name' => 'iPhone XS',
        'port' => rand(1..10000)
      }
    end
    let(:params) do
      {
        "app_percy" => {
          'env' => {
            "PERCY_TOKEN" => "dummy_token"
          }
        },
        "device" => device_id
      }
    end
    let(:xctest_session) { BrowserStack::XCTestSession.new(params, device) }

    before(:each) do
      allow(DeviceManager).to receive(:device_configuration_check).with(device_id).and_return(device)
    end

    it "should not call start if app_percy_session is not initialized" do
      expect(xctest_session.start_app_percy).to eq(nil)
    end

    it "should call start if app_percy_session is initialized" do
      xctest_session.setup_config(nil)
      expect(xctest_session.app_percy_session).to receive(:start).with(params).and_return(true)
      expect(xctest_session.start_app_percy).to eq(true)
    end
  end

  describe "#stop_app_percy" do
    let(:device_id) { "00008030-001044C80EF9802E" }
    let(:device) do
      {
        'device_name' => 'iPhone XS',
        'port' => rand(1..10000)
      }
    end
    let(:params) do
      {
        "app_percy" => {
          'env' => {
            "PERCY_TOKEN" => "dummy_token"
          }
        },
        "device" => device_id,
        "automate_session_id" => "123"
      }
    end
    let(:xctest_session) { BrowserStack::XCTestSession.new(params, device) }

    before(:each) do
      allow(DeviceManager).to receive(:device_configuration_check).with(device_id).and_return(device)
    end

    it "should not call stop if app_percy_session is not initialized" do
      expect(xctest_session.stop_app_percy).to eq(nil)
    end

    it "should call stop if app_percy_session is initialized" do
      xctest_session.setup_config(nil)
      expect(xctest_session.app_percy_session).to receive(:stop).with(params['automate_session_id']).and_return(true)
      expect(xctest_session.stop_app_percy).to eq(true)
    end
  end

  describe "#get_s3_params" do
    it "should return the s3 params with default value if aws_params is not passed" do
      params = {
        "devicelogs_aws_bucket" => "DUMMY_AWS_BUCKET",
        "devicelogs_aws_region" => "DUMMY_AWS_REGION",
        "devicelogs_aws_key" => "DUMMY_AWS_KEY",
        "devicelogs_aws_secret" => "DUMMY_AWS_SECRET",
        "devicelogs_aws_storage_class" => "DUMMY_AWS_STORAGE_CLASS"
      }
      xctest_session.instance_variable_set(:@params, params)
      xctest_session.instance_variable_set(:@build_id, "12345678")
      xctest_session.instance_variable_set(:@session_id, "abcdefgh")

      expected_s3_params = {
        session_id: "abcdefgh",
        aws_key: "DUMMY_AWS_KEY",
        aws_secret: "DUMMY_AWS_SECRET",
        aws_bucket: "DUMMY_AWS_BUCKET",
        aws_region: "DUMMY_AWS_REGION",
        aws_storage_class: "DUMMY_AWS_STORAGE_CLASS",
        s3_url: "https://s3-DUMMY_AWS_REGION.amazonaws.com/DUMMY_AWS_BUCKET/12345678/abcdefgh/abcdefgh-device-logs",
        no_acl: true,
        is_json: false
      }

      expect(xctest_session.send(:get_s3_params, "device-logs")).to eq(expected_s3_params)
    end

    it "should return the s3 params with custom s3 params when aws_params is passed" do
      params = {
        "devicelogs_aws_bucket" => "DUMMY_AWS_BUCKET",
        "devicelogs_aws_region" => "DUMMY_AWS_REGION",
        "devicelogs_aws_key" => "DUMMY_AWS_KEY",
        "devicelogs_aws_secret" => "DUMMY_AWS_SECRET",
        "devicelogs_aws_storage_class" => "DUMMY_AWS_STORAGE_CLASS"
      }
      aws_params = {
        'bucket' => "xcresultbundle_aws_bucket",
        'region' => "xcresultbundle_aws_region",
        'key' => "xcresultbundle_aws_key",
        'secret' => "xcresultbundle_aws_secret",
        'storage_class' => "xcresultbundle_aws_storage_class"
      }
      xctest_session.instance_variable_set(:@params, params)
      xctest_session.instance_variable_set(:@build_id, "12345678")
      xctest_session.instance_variable_set(:@session_id, "abcdefgh")

      expected_s3_params = {
        session_id: "abcdefgh",
        aws_key: "xcresultbundle_aws_key",
        aws_secret: "xcresultbundle_aws_secret",
        aws_bucket: "xcresultbundle_aws_bucket",
        aws_region: "xcresultbundle_aws_region",
        aws_storage_class: "xcresultbundle_aws_storage_class",
        s3_url: "https://s3-xcresultbundle_aws_region.amazonaws.com/xcresultbundle_aws_bucket/12345678/abcdefgh/abcdefgh-result-bundle.xcresult.zip",
        no_acl: true,
        is_json: false
      }

      expect(xctest_session.send(:get_s3_params, "result-bundle.xcresult.zip", "", aws_params)).to eq(expected_s3_params)
    end
  end

  describe "#merge_result_bundles" do
    it "should merge the test level xcresult files into a single xcresult" do
      device = "00008030-001044C80EF9802E"
      xctest_session.instance_variable_set(:@result_bundle_directory, "/tmp/#{device}_xcresult_bundle")
      xctest_session.instance_variable_set(:@result_bundle_zip, "/tmp/#{device}_result-bundle.xcresult.zip")
      xcresult_count_command = "ls /tmp/#{device}_xcresult_bundle/*/* | egrep '.xcresult$'"
      merge_command = "gtimeout -s KILL 120 xcrun xcresulttool merge /tmp/#{device}_xcresult_bundle/*/*/*.xcresult --output-path /tmp/#{device}_xcresult_bundle/result-bundle.xcresult 2>&1"
      zip_command = "cd /tmp/#{device}_xcresult_bundle; gtimeout -s KILL 120 zip -rq /tmp/#{device}_result-bundle.xcresult.zip result-bundle.xcresult 2>&1"
      expect(BrowserStack::OSUtils).to receive(:execute).with(xcresult_count_command, true).and_return(["testAlert.xcresult\ntestText.xcresult\n", 0])
      expect(BrowserStack::OSUtils).to receive(:execute).with(merge_command, true).and_return(["[v3] Merged to: /tmp/00008030-001044C80EF9802E_xcresult_bundle/result-bundle.xcresult\n", 0])
      expect(BrowserStack::OSUtils).to receive(:execute).with(zip_command, true).and_return(["", 0])

      expect(xctest_session.send(:merge_result_bundles)).to eq(true)
    end

    it "should return false if no tests present" do
      device = "00008030-001044C80EF9802E"
      xctest_session.instance_variable_set(:@result_bundle_directory, "/tmp/#{device}_xcresult_bundle")
      xcresult_count_command = "ls /tmp/#{device}_xcresult_bundle/*/* | egrep '.xcresult$'"
      expect(BrowserStack::OSUtils).to receive(:execute).with(xcresult_count_command, true).and_return(["", 0])
      expect(xctest_session.send(:merge_result_bundles)).to eq(false)
    end

    it "should return false if zip command fails" do
      device = "00008030-001044C80EF9802E"
      xctest_session.instance_variable_set(:@result_bundle_directory, "/tmp/#{device}_xcresult_bundle")
      xctest_session.instance_variable_set(:@result_bundle_zip, "/tmp/#{device}_result-bundle.xcresult.zip")
      xcresult_count_command = "ls /tmp/#{device}_xcresult_bundle/*/* | egrep '.xcresult$'"
      merge_command = "gtimeout -s KILL 120 xcrun xcresulttool merge /tmp/#{device}_xcresult_bundle/*/*/*.xcresult --output-path /tmp/#{device}_xcresult_bundle/result-bundle.xcresult 2>&1"
      zip_command = "cd /tmp/#{device}_xcresult_bundle; gtimeout -s KILL 120 zip -rq /tmp/#{device}_result-bundle.xcresult.zip result-bundle.xcresult 2>&1"
      expect(BrowserStack::OSUtils).to receive(:execute).with(xcresult_count_command, true).and_return(["testAlert.xcresult\ntestText.xcresult\n", 0])
      expect(BrowserStack::OSUtils).to receive(:execute).with(merge_command, true).and_return(["[v3] Merged to: /tmp/00008030-001044C80EF9802E_xcresult_bundle/result-bundle.xcresult\n", 0])
      expect(BrowserStack::OSUtils).to receive(:execute).with(zip_command, true).and_return(["", 1])

      expect(xctest_session.send(:merge_result_bundles)).to eq(false)
    end

    it "should copy the .xcresult to @result_bundle_directory if single .xcresult file present" do
      device = "00008030-001044C80EF9802E"
      xctest_session.instance_variable_set(:@result_bundle_directory, "/tmp/#{device}_xcresult_bundle")
      xctest_session.instance_variable_set(:@result_bundle_zip, "/tmp/#{device}_result-bundle.xcresult.zip")
      xcresult_count_command = "ls /tmp/#{device}_xcresult_bundle/*/* | egrep '.xcresult$'"
      copy_command = "cp -R /tmp/#{device}_xcresult_bundle/*/*/*.xcresult /tmp/#{device}_xcresult_bundle/result-bundle.xcresult"
      zip_command = "cd /tmp/#{device}_xcresult_bundle; gtimeout -s KILL 120 zip -rq /tmp/#{device}_result-bundle.xcresult.zip result-bundle.xcresult 2>&1"
      expect(BrowserStack::OSUtils).to receive(:execute).with(xcresult_count_command, true).and_return(["testAlert.xcresult\n", 0])
      expect(BrowserStack::OSUtils).to receive(:execute).with(copy_command, true).and_return(["", 0])
      expect(BrowserStack::OSUtils).to receive(:execute).with(zip_command, true).and_return(["", 0])

      expect(xctest_session.send(:merge_result_bundles)).to eq(true)
    end
  end

  describe "#upload_result_bundle" do
    it "should create an upload request for uploading the result-bundle.xcresult.zip" do
      params = {
        "devicelogs_aws_bucket" => "DUMMY_AWS_BUCKET",
        "devicelogs_aws_region" => "DUMMY_AWS_REGION",
        "devicelogs_aws_key" => "DUMMY_AWS_KEY",
        "devicelogs_aws_secret" => "DUMMY_AWS_SECRET",
        "xcresultbundle_aws_bucket" => "XCRESULT_BUNDLE_AWS_BUCKET",
        "xcresultbundle_aws_region" => "XCRESULT_BUNDLE_AWS_REGION",
        "xcresultbundle_aws_key" => "XCRESULT_BUNDLE_AWS_KEY",
        "xcresultbundle_aws_secret" => "XCRESULT_BUNDLE_AWS_SECRET"
      }
      aws_params = {
        'bucket' => params["xcresultbundle_aws_bucket"],
        'region' => params["xcresultbundle_aws_region"],
        'key' => params["xcresultbundle_aws_key"],
        'secret' => params["xcresultbundle_aws_secret"]
      }
      s3_params = {
        session_id: "abcdefgh",
        aws_key: "xcresultbundle_aws_key",
        aws_secret: "xcresultbundle_aws_secret",
        aws_bucket: "xcresultbundle_aws_bucket",
        aws_region: "xcresultbundle_aws_region",
        s3_url: "https://s3-xcresultbundle_aws_region.amazonaws.com/xcresultbundle_aws_bucket/12345678/abcdefgh/abcdefgh-result-bundle.xcresult.zip",
        no_acl: true,
        is_json: false
      }
      device = "00008030-001044C80EF9802E"
      xctest_session.instance_variable_set(:@params, params)
      xctest_session.instance_variable_set(:@device, device)
      xctest_session.instance_variable_set(:@server_config, {})
      xctest_session.instance_variable_set(:@test_framework, "xcuitest")
      xctest_session.instance_variable_set(:@genre, "app_automate")
      xctest_session.instance_variable_set(:@result_bundle_directory, "/tmp/#{device}_xcresult_bundle")
      xctest_session.instance_variable_set(:@result_bundle_zip, "/tmp/#{device}_result-bundle.xcresult.zip")
      expect(xctest_session).to receive(:get_s3_params).with("result-bundle.xcresult.zip", "", aws_params).and_return(s3_params)
      expect(xctest_session).to receive(:merge_result_bundles).and_return(true)
      expect(Utils).to receive(:create_upload_request).with("/tmp/#{device}_result-bundle.xcresult.zip", "result-bundle", device, s3_params, {}, "xcuitest", "app_automate")

      xctest_session.send(:upload_result_bundle)
    end
  end

  describe "#mitm_restart" do
    let(:device) { "device_name" }
    let(:session_id) { "abcd1234" }
    let(:mitmproxy) { double("mitmproxy") }

    before(:each) do
      xctest_session.instance_variable_set(:@mitmproxy, mitmproxy)
      xctest_session.instance_variable_set(:@session_id, session_id)
      xctest_session.instance_variable_set(:@device, device)
      allow_any_instance_of(Object).to receive(:sleep)
    end

    it "restarts MitmProxy successfully" do
      expect(MitmProxy).to receive(:stop_proxy).with(device, session_id, 'xcuitest')
      expect(xctest_session).to receive(:capture_har_file).with('testname').and_return([1234, 5678])
      expect(mitmproxy).to receive(:boot).with(1234, 5678)
      retry_count = xctest_session.send(:mitm_restart, 'testname', 0, 0, 0)

      expect(retry_count).to eq(0)
    end

    it "raises an error" do
      expect(MitmProxy).to receive(:stop_proxy).with(device, session_id, 'xcuitest').exactly(5).times
      expect(xctest_session).to receive(:capture_har_file).with('testname').and_return([1234, 5678])
      expect(mitmproxy).to receive(:boot).with(1234, 5678).exactly(5).times.and_raise('Mitm Boot Failed')
      expect { xctest_session.send(:mitm_restart, 'testname', 0, 0, 0) }.to raise_error
    end

    it "retry n-1 times" do
      expect(MitmProxy).to receive(:stop_proxy).with(device, session_id, 'xcuitest').exactly(5).times
      expect(xctest_session).to receive(:capture_har_file).exactly(1).times.with('testname').and_return([1234, 5678])
      expect(mitmproxy).to receive(:boot).with(1234, 5678).exactly(4).times.ordered.and_raise('Mitm Boot Failed')
      expect(mitmproxy).to receive(:boot).with(1234, 5678).ordered.and_return(true)

      retry_count = xctest_session.send(:mitm_restart, 'testname', 0, 0, 0)

      expect(retry_count).to eq(4)
    end

    it "should not capture_har_file more than once" do
      expect(MitmProxy).to receive(:stop_proxy).with(device, session_id, 'xcuitest').ordered.and_raise('Mitm Stop Proxy Failed')
      expect(MitmProxy).to receive(:stop_proxy).with(device, session_id, 'xcuitest').ordered.and_return(true)
      expect(xctest_session).to receive(:capture_har_file).exactly(1).times.with('testname').and_return([1234, 5678])
      expect(mitmproxy).to receive(:boot).with(1234, 5678)

      retry_count = xctest_session.send(:mitm_restart, 'testname', 0, 0, 0)

      expect(retry_count).to eq(1)
    end
  end

  describe "#run_xctest" do
    let(:test_name) { "ProductName/ClassName/TestName" }
    let(:test_num) { 1 }

    before(:each) do
      expect(xctest_session).to receive(:start_timeout_manager).at_least(:once).and_return(true)
      expect(xctest_session).to receive(:stop_timeout_manager).at_least(:once).and_return(true)
      expect(xctest_session).to receive(:get_test_expire_time).at_least(:once).and_return(7100)
      expect(xctest_session).to receive(:generate_xctestrun_xml).at_least(:once).and_return(true)
      xctest_session.instance_variable_set(:@test_meta_info, { "retries" => 0, "retry_time" => 0 })
      allow_any_instance_of(Object).to receive(:sleep).and_return(0)
    end

    it "should execute xcodebuild command" do
      expect(BrowserStack::OSUtils).to receive(:execute).and_return(["", 0])
      expect(xctest_session).to receive(:should_retry_test?).and_return(false)
      status = xctest_session.send(:run_xctest, test_name, test_num, 0)

      expect(status).to eq(0)
    end

    it "should execute xcodebuild command with enableResultBundle true" do
      xctest_session.instance_variable_set(:@params, { 'enableResultBundle' => "true" })
      expect(BrowserStack::OSUtils).to receive(:execute).and_return(["", 0])
      expect(xctest_session).to receive(:should_retry_test?).and_return(false)
      status = xctest_session.send(:run_xctest, test_name, test_num, 0)

      expect(status).to eq(0)
    end

    it "should execute xcodebuild command by removing xcresult bundle incase of bs retries" do
      xctest_session.instance_variable_set(:@params, { 'enableResultBundle' => "true" })
      expect(BrowserStack::OSUtils).to receive(:execute).and_return(["", 70], ["", 0])
      expect(xctest_session).to receive(:should_retry_test?).and_return(true, false)
      expect(FileUtils).to receive(:rm_rf).twice.and_return(true)
      status = xctest_session.send(:run_xctest, test_name, test_num, 0)

      expect(status).to eq(0)
    end

    it "should retry xcodebuild command" do
      expect(BrowserStack::OSUtils).to receive(:execute).exactly(2).times.and_return(["", 0], ["", 0])
      expect(xctest_session).to receive(:should_retry_test?).and_return(true, false)
      status = xctest_session.send(:run_xctest, test_name, test_num)

      expect(status).to eq(0)
    end
  end

  describe "#should_retry_test?" do
    let(:test_name) { "ProductName/ClassName/TestName" }
    let(:error_log) { "No installed application found for launch session" }
    let(:test_started_log) { "Test Case '-[UISmokeTests.CoreReddit testNSFWRoadblock]' started" }
    let(:instru_file) { double("instrumentation_file") }

    it "should return true if instru_logs contains app not installed logs and no test has started" do
      xctest_session.instance_variable_set(:@device_os_version, 16.2)
      expect(xctest_session).to receive(:session_timedout?).and_return(false)
      expect(File).to receive(:open).and_return(instru_file)
      expect(instru_file).to receive(:seek).and_return(true)
      expect(instru_file).to receive(:readlines).and_return(["gibberish", error_log])
      expect(BrowserStack::Zombie).to receive(:push_logs).and_return(true)

      expect(xctest_session.send(:should_retry_test?, test_name, 0)).to eq(true)
    end

    it "should return false if instru_logs contains app not installed logs but a test has started" do
      xctest_session.instance_variable_set(:@device_os_version, 16.2)
      expect(xctest_session).to receive(:session_timedout?).and_return(false)
      expect(File).to receive(:open).and_return(instru_file)
      expect(instru_file).to receive(:seek).and_return(true)
      expect(instru_file).to receive(:readlines).and_return(["gibberish", error_log, test_started_log])
      expect(BrowserStack::Zombie).to_not receive(:push_logs)

      expect(xctest_session.send(:should_retry_test?, test_name, 0)).to eq(false)
    end

    it "should return false if instru_logs does not contain error logs" do
      xctest_session.instance_variable_set(:@device_os_version, 16.2)

      expect(xctest_session).to receive(:session_timedout?).and_return(false)
      expect(File).to receive(:open).and_return(instru_file)
      expect(instru_file).to receive(:seek).and_return(true)
      expect(instru_file).to receive(:readlines).and_return(["gibberish", "gibberish2"])
      expect(BrowserStack::Zombie).to_not receive(:push_logs)
      expect(xctest_session.send(:should_retry_test?, test_name, 0)).to eq(false)
    end

    it "should return false if session is timedout" do
      xctest_session.instance_variable_set(:@device_os_version, 16.2)
      expect(xctest_session).to receive(:session_timedout?).and_return(true)
      expect(File).to_not receive(:readlines)
      expect(BrowserStack::Zombie).to_not receive(:push_logs)

      expect(xctest_session.send(:should_retry_test?, test_name, 0)).to eq(false)
    end

    it "should return false if ios version is invalid" do
      xctest_session.instance_variable_set(:@device_os_version, 15.2)
      expect(xctest_session).to receive(:session_timedout?).and_return(true)
      expect(File).to_not receive(:readlines)
      expect(BrowserStack::Zombie).to_not receive(:push_logs)

      expect(xctest_session.send(:should_retry_test?, test_name, 0)).to eq(false)
    end

    it "should return true if retry limit is exhausted" do
      xctest_session.instance_variable_set(:@device_os_version, 16.2)
      expect(xctest_session).to_not receive(:session_timedout?)
      expect(File).to_not receive(:readlines)
      expect(BrowserStack::Zombie).to_not receive(:push_logs)

      expect(xctest_session.send(:should_retry_test?, test_name, 6)).to eq(false)
    end

    it "should return true if no tests were started and xctestrun_cmd_exit_status is timeout" do
      xctest_session.instance_variable_set(:@device_os_version, 16.2)
      expect(xctest_session).to receive(:session_timedout?).and_return(false)
      expect(File).to receive(:open).and_return(instru_file)
      expect(instru_file).to receive(:seek).and_return(true)
      expect(instru_file).to receive(:readlines).and_return(["gibberish", "gibberish2"])
      expect(BrowserStack::Zombie).to receive(:push_logs)
      expect(xctest_session.send(:should_retry_test?, test_name, 0, 137)).to eq(true)
    end

    it "should return false if tests were started and xctestrun_cmd_exit_status is timeout" do
      xctest_session.instance_variable_set(:@device_os_version, 16.2)
      expect(xctest_session).to receive(:session_timedout?).and_return(false)
      expect(File).to receive(:open).and_return(instru_file)
      expect(instru_file).to receive(:seek).and_return(true)
      expect(instru_file).to receive(:readlines).and_return(["gibberish", "gibberish2", test_started_log])
      expect(BrowserStack::Zombie).not_to receive(:push_logs)
      expect(xctest_session.send(:should_retry_test?, test_name, 0, 137)).to eq(false)
    end

    it "should return false if no tests were started and xctestrun_cmd_exit_status is not timeout" do
      xctest_session.instance_variable_set(:@device_os_version, 16.2)
      expect(File).to receive(:open).and_return(instru_file)
      expect(instru_file).to receive(:seek).and_return(true)
      expect(instru_file).to receive(:readlines).and_return(["gibberish", "gibberish2"])
      expect(BrowserStack::Zombie).not_to receive(:push_logs)
      expect(xctest_session.send(:should_retry_test?, test_name, 0, 123)).to eq(false)
    end
  end

  describe "generate_xctestrun_xml" do
    it 'should read product_module_name from params if present in case of xcuitest' do
      params = {
        'product_module_name' => 'product_module_name',
        'app_details_bundle_id' => 'app_details_bundle_id'
      }
      product_module_names = {
        'xctest': 'xctest'
      }
      xctest_xmlfile = "./spec/fixtures/xctest_xmlfile_product_module_name"
      test_framework = "xcuitest"
      xctest_session.instance_variable_set(:@params, params)
      xctest_session.instance_variable_set(:@product_module_names, product_module_names)
      xctest_session.instance_variable_set(:@xctest_xmlfile, xctest_xmlfile)
      xctest_session.instance_variable_set(:@device_os_version, 16.0)
      xctestrun_contents = File.read(xctest_xmlfile)
      expect(Utils).to receive(:write_to_file).once.with(xctest_xmlfile, anything)
      xctest_session.send(:generate_xctestrun_xml, test_framework, "xctest")
    end

    it 'should read product_module_name from params if present in case of xcuitest' do
      params = {
        'enableCameraImageInjection' => true,
        'product_module_name' => 'product_module_name',
        'app_details_bundle_id' => 'app_details_bundle_id'
      }
      product_module_names = {
        'xctest': 'xctest'
      }
      xctest_xmlfile = "./spec/fixtures/xctest_xmlfile_product_module_name"
      original_xctestrun_contents = File.read(xctest_xmlfile)
      test_framework = "xcuitest"
      xctest_session.instance_variable_set(:@params, params)
      xctest_session.instance_variable_set(:@product_module_names, product_module_names)
      xctest_session.instance_variable_set(:@xctest_xmlfile, xctest_xmlfile)
      xctest_session.instance_variable_set(:@device_os_version, 16.0)
      xctest_session.send(:generate_xctestrun_xml, test_framework, "xctest")
      xctestrun_contents = File.read(xctest_xmlfile)
      # Restore the original content
      File.write(xctest_xmlfile, original_xctestrun_contents)

      expect(xctestrun_contents).to include("<key>SENSOR_MOCKER_HOST</key>")
      expect(xctestrun_contents).to include("<string>http://#{SENSOR_MOCKER_HOST}</string>")
      expect(xctestrun_contents).to include("<key>IMAGE_INJECTION_PATH</key>")
      expect(xctestrun_contents).to include("xcuitest/inject-media")
    end
  end

  describe "#handle_test_execution_error" do
    let(:session_id) { "1234" }

    before(:each) do
      params = {
        'automate_session_id' => session_id,
        'test_framework' => "xcuitest"
      }
      xctest_session.instance_variable_set(:@params, params)
      xctest_session.instance_variable_set(:@stability_reasons, [])
    end

    it "Should log any exception and push data to pager" do
      ex = Exception.new("No space left on device")
      ex.set_backtrace(caller)
      expect(BrowserStack::Zombie).to receive(:push_logs).and_return(true)
      expect(xctest_session).to receive(:track_stability_reason_in_eds).and_return(true)

      xctest_session.send(:handle_test_execution_error, ex)
    end

    it "Should rescue any exception while handling test execution error" do
      ex = Exception.new("Failed to execute method")
      ex.set_backtrace(caller)
      expect { BrowserStack::Zombie.to receive(:push_logs).and_raise_error('servname not found') }

      expect { xctest_session.send(:handle_test_execution_error, ex) }.to_not raise_error
    end
  end

  describe "#setup_proxies" do
    let(:session_id) { "1234" }
    let(:mitmproxy) { double("mitmproxy") }

    before(:each) do
      params = {
        'automate_session_id' => session_id,
        'test_framework' => "xcuitest"
      }
      xctest_session.instance_variable_set(:@params, params)
    end

    it "should setup proxy in first try" do
      expect(PrivoxyManager).to receive(:change_port).once.and_return(true)
      expect(MitmProxy).to receive(:new).once.and_return(mitmproxy)
      expect(mitmproxy).to receive(:boot).once.and_return(true)

      xctest_session.send(:setup_proxies)
    end

    it "should exhaust retries and raise exceptions" do
      allow_any_instance_of(Object).to receive(:sleep).and_return(0)
      expect(PrivoxyManager).to receive(:change_port).exactly(3).times.and_return(true)
      expect(MitmProxy).to receive(:new).exactly(3).times.and_return(mitmproxy)
      expect(mitmproxy).to receive(:boot).exactly(3).times.and_raise('Failed to launch privoxy')

      expect { xctest_session.send(:setup_proxies) }.to raise_error
    end

    it "should retry and exit on success" do
      allow_any_instance_of(Object).to receive(:sleep).and_return(0)
      expect(PrivoxyManager).to receive(:change_port).exactly(2).times.and_return(true)
      expect(MitmProxy).to receive(:new).exactly(2).times.and_return(mitmproxy)
      expect(mitmproxy).to receive(:boot).ordered.and_raise('Failed to launch privoxy')
      expect(mitmproxy).to receive(:boot).ordered.and_return(true)

      xctest_session.send(:setup_proxies)
    end
  end

  describe "#track_stability_reason_in_eds" do
    let(:session_id) { "1234" }

    before(:each) do
      params = {
        'automate_session_id' => session_id,
        'test_framework' => "xcuitest"
      }
      xctest_session.instance_variable_set(:@params, params)
    end

    it "should send test stability reasons to eds" do
      expect(Utils).to receive(:send_to_eds).with({
        hashed_id: session_id,
        secondary_diagnostic_reason: "machine-storage-issue",
        product: { stability: { reason: "error-platform-issues" } },
        timestamp: anything
      }, EdsConstants::APP_AUTOMATE_TEST_SESSIONS, true).and_return(true)

      expect(xctest_session).to receive(:send_error_reason_in_file).and_return(true)

      xctest_session.send(:track_stability_reason_in_eds, "error-platform-issues", "machine-storage-issue")
    end

    it "should rescue any exceptions raised" do
      expect { Utils.to receive(:send_to_eds).and_raise_error('servname not found') }

      xctest_session.send(:track_stability_reason_in_eds, "error-platform-issues", "machine-storage-issue")
    end
  end

  describe "#ensure_wda_not_running" do
    let(:session_id) { "1234" }

    before(:each) do
      params = {
        'automate_session_id' => session_id,
        'test_framework' => "xcuitest"
      }
      xctest_session.instance_variable_set(:@params, params)
    end

    it "should kill wda when its running before hand" do
      xctest_session.instance_variable_set("@device_os_version", 18)
      expect(BrowserStack::OSUtils).to receive(:wda_xcodebuild_running?).and_return(true)
      expect(BrowserStack::OSUtils).to receive(:kill_wda_xcodebuild).and_return(true)

      xctest_session.ensure_wda_not_running
    end

    it "should not kill wda when its not running before hand" do
      xctest_session.instance_variable_set("@device_os_version", 18)
      expect(BrowserStack::OSUtils).to receive(:wda_xcodebuild_running?).and_return(false)
      expect(BrowserStack::OSUtils).not_to receive(:kill_wda_xcodebuild)

      xctest_session.ensure_wda_not_running
    end

    it "should rescue any exception while killing wda" do
      xctest_session.instance_variable_set("@device_os_version", 18)
      expect(BrowserStack::OSUtils).to receive(:wda_xcodebuild_running?).and_raise(StandardError.new("Something went wrong"))
      expect(BrowserStack::Zombie).to receive(:push_logs).and_return(true)
      expect(BrowserStack::OSUtils).not_to receive(:kill_wda_xcodebuild)

      xctest_session.ensure_wda_not_running
    end
  end

  describe "#push_feature_usage_to_eds" do
    let(:session_id) { "1234" }

    before(:each) do
      params = {
        'automate_session_id' => session_id,
        'test_framework' => "xcuitest"
      }
      xctest_session.instance_variable_set(:@params, params)
    end

    it "should send test stability reasons to eds" do
      xctest_session.instance_variable_set(:@test_meta_info, { "retries" => 1 })
      expect(Utils).to receive(:send_to_eds).with({
        hashed_id: session_id,
        feature_usage: { "tests_stability" => { "retries" => 1 } },
        timestamp: anything
      }, EdsConstants::APP_AUTOMATE_TEST_SESSIONS, true).and_return(true)

      xctest_session.send(:push_feature_usage_to_eds)
    end

    it "should not send test stability reasons to eds if feature_usage is {}" do
      xctest_session.instance_variable_set(:@test_meta_info, {})
      expect(Utils).to_not receive(:send_to_eds)

      xctest_session.send(:push_feature_usage_to_eds)
    end

    it "should rescue any exceptions raised" do
      expect { Utils.to receive(:send_to_eds).and_raise_error('servname not found') }

      xctest_session.send(:push_feature_usage_to_eds)
    end
  end

  describe "track_stability" do
    let(:device) { "00008030-001044C80EF9802E" }
    let(:callback_file) { "/tmp/#{device}_xctest_callback" }
    let(:system_level_pattern_key) { "runner_failure" }

    before(:each) do
      xctest_session.instance_variable_set(:@device, device)
      xctest_session.instance_variable_set(:@callback_file, callback_file)
    end

    it "should do nothing if test_meta_info is empty" do
      xctest_session.instance_variable_set(:@test_meta_info, {})
      expect(File).not_to receive(:read)
      xctest_session.track_stability
    end

    it "should do nothing if test_meta_info is nil" do
      xctest_session.instance_variable_set(:@test_meta_info, nil)
      expect(File).not_to receive(:read)
      xctest_session.track_stability
    end

    it "should do nothing if no system level patterns match" do
      xctest_session.instance_variable_set(:@test_meta_info, { "non_matching_key" => 1 })
      expect(File).not_to receive(:read)
      xctest_session.track_stability
    end

    context "when system level pattern matches" do
      before(:each) do
        xctest_session.instance_variable_set(:@test_meta_info, { system_level_pattern_key => 1 })
      end

      it "should track stability with nil reason when error count is 0" do
        session_data = {
          "test_status" => {
            "error" => 0
          }
        }
        expect(File).to receive(:read).with(callback_file).and_return(session_data.to_json)
        expect(xctest_session).to receive(:track_stability_reason_in_eds).with(nil, "xcodebuild-device-issue")
        xctest_session.track_stability
      end

      it "should track stability with error-platform-issues when error count > 0" do
        session_data = {
          "test_status" => {
            "error" => 1
          }
        }
        expect(File).to receive(:read).with(callback_file).and_return(session_data.to_json)
        expect(xctest_session).to receive(:track_stability_reason_in_eds).with("error-platform-issues", "xcodebuild-device-issue")
        xctest_session.track_stability
      end
    end
  end
end
