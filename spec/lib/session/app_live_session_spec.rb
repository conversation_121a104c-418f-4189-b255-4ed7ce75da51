require_relative '../../../server/iphone'
require_relative '../../../lib/utils/utils'
require_relative '../../../lib/utils/web_driver_agent'
require_relative '../../../server/device_manager'
require_relative '../../../lib/mitm_proxy'
require_relative '../../../lib/session/app_live_session'

describe BrowserStack::AppLiveSession do
  let(:live_session) { BrowserStack::AppLiveSession.new({}, {}) }

  before :each do
    video_rec_manager = instance_double(VideoRecManager)
    expect(VideoRecManager).to receive(:new).with(nil, {}).and_return(video_rec_manager)
    allow(DeviceManager).to receive(:is_first_cleanup?).and_return(false)
  end

  it 'creates a AppLiveSession object' do
    expect(live_session).to be_a BrowserStack::AppLiveSession
  end

  describe "enable_network_logs" do
    it "should setup mitm proxy for applive if not already running" do
      allow(<PERSON>ceManager).to receive(:device_configuration_check).and_return("test_device")
      allow(MitmProxy).to receive(:running?).and_return(false)
      expect(<PERSON>ceManager).to receive(:setup_mitm_proxy).with(anything, anything, anything).and_return(true)
      live_session.instance_variable_set("@params", { "networkLogs" => "true", "genre" => "app_live_testing" })
      enabled = live_session.send(:enable_network_logs)
      expect(enabled).to be true
    end

    it "should disable mitm proxy for applive if already running" do
      allow(DeviceManager).to receive(:device_configuration_check).and_return("test_device")
      allow(MitmProxy).to receive(:running?).and_return(true)
      expect(MitmProxy).to receive(:stop_proxy).with(anything)
      expect(PrivoxyManager).to receive(:reset_proxy).with(anything, anything, anything)
      live_session.instance_variable_set("@params", { "networkLogs" => "false", "genre" => "app_live_testing" })
      enabled = live_session.send(:enable_network_logs)
      expect(enabled).to be true
    end

    it "should reset privoxy if setting up mitm throws an exception" do
      allow(DeviceManager).to receive(:device_configuration_check).and_return("test_device")
      allow(MitmProxy).to receive(:running?).and_return(false)
      expect(DeviceManager).to receive(:setup_mitm_proxy).with(anything, anything, anything) do
        raise("timeout")
      end
      live_session.instance_variable_set("@params", { "networkLogs" => "true", "genre" => "app_live_testing" })
      expect(MitmProxy).to receive(:stop_proxy).with(anything)
      expect(PrivoxyManager).to receive(:reset_proxy).with(anything, anything)
      enabled = live_session.send(:enable_network_logs)
      expect(enabled).to be false
    end
  end

  describe "set_default_timezone" do
    let(:iphone_class) { BrowserStack::IPhone }

    it 'should set default timezone when timezone present' do
      live_session.instance_variable_set("@device", "test_device_id")
      live_session.instance_variable_set("@params", { "timezone" => "Kolkata", "genre" => "app_live_testing", "app_live_session_id" => "12345" })
      expect(BrowserStack::IPhone).to receive(:change_time_zone).once
      live_session.send(:set_default_timezone)
    end
    it 'should not set default timezone when timezone is empty' do
      live_session.instance_variable_set("@device", "test_device_id")
      live_session.instance_variable_set("@params", { "timezone" => "", "genre" => "app_live_testing", "app_live_session_id" => "12345" })
      iphone_class.any_instance.stub(:change_time_zone)
      expect(iphone_class).not_to receive(:change_time_zone)
      live_session.send(:set_default_timezone)
    end
    it 'should not set default timezone when timezone is UTC' do
      live_session.instance_variable_set("@device", "test_device_id")
      live_session.instance_variable_set("@params", { "timezone" => "UTC", "genre" => "app_live_testing", "app_live_session_id" => "12345" })
      iphone_class.any_instance.stub(:change_time_zone)
      expect(iphone_class).not_to receive(:change_time_zone)
      live_session.send(:set_default_timezone)
    end
  end

  describe "install_automation_and_launch" do
    it "should raise error when install fails" do
      live_session.instance_variable_set("@device", "test_device_id")
      allow(OSUtils).to receive(:unarchive).and_return(0)
      allow(Utils).to receive(:notify_pusher)
      allow(Utils).to receive(:add_app_to_cls_tracking_file)
      allow(Dir).to receive(:glob).and_return(["some_path"])
      allow(Shellwords).to receive(:escape).and_return("test_bundle_id")
      allow(DeviceManager).to receive(:device_configuration_check).and_return({ 'device_version' => "11" })
      # should fail
      allow(OSUtils).to receive(:execute).with("#{IOS_DEPLOY} --id test_device_id --bundle test_bundle_id 2>&1", true).and_return(["", 1])
      allow(Utils).to receive(:mark_event_end)
      allow(Utils).to receive(:mark_event_start)
      allow(Utils).to receive(:send_to_eds)
      allow(Utils).to receive(:write_to_file_with_lock)
      live_session.instance_variable_set("@params", { "networkLogs" => "true", "genre" => "app_live_testing" })
      expect { live_session.send(:install_automate_and_launch_app) }.to raise_error(BrowserStack::AppLiveSession::AppInstallFailureException, "Failed to install IPA")
    end

    it "should raise error and call new automation when install succeeds but automation fails for ios > 10" do
      live_session.instance_variable_set("@device", "test_device_id")
      allow(OSUtils).to receive(:unarchive).and_return(0)
      allow(Utils).to receive(:notify_pusher)
      allow(Utils).to receive(:add_app_to_cls_tracking_file)
      allow(Dir).to receive(:glob).and_return(["some_path"])
      allow(Shellwords).to receive(:escape).and_return("test_bundle_id")
      # should fail
      allow(OSUtils).to receive(:execute).with("#{IOS_DEPLOY} --id test_device_id --bundle test_bundle_id 2>&1", true).and_return(["", 0])
      allow(Utils).to receive(:mark_event_end)
      allow(Utils).to receive(:mark_event_start)
      allow(Utils).to receive(:send_to_eds)
      allow(Utils).to receive(:write_to_file_with_lock)
      allow(BrowserStackAppHelper).to receive(:check_and_install_browserstack_test_suite).and_return(true)
      allow(DeviceManager).to receive(:device_configuration_check).and_return({ 'device_version' => "11" })
      allow(live_session.idevice).to receive(:get_device_details_for_app_trust).and_return({})
      allow(live_session.idevice).to receive(:trust_client_enterprise_app_via_browserstack_app).and_return(false)
      live_session.instance_variable_set("@params", { "networkLogs" => "true", "genre" => "app_live_testing" })
      allow(live_session.idevice).to receive(:press_home)

      expect { live_session.send(:install_automate_and_launch_app) }.to raise_error(BrowserStack::AppLiveSession::AppInstallFailureException, "app trust returned false after possible retries")
    end

    it "should raise error and call old automation flow when install succeeds but automation fails for ios <= 10" do
      live_session.instance_variable_set("@device", "test_device_id")
      allow(OSUtils).to receive(:unarchive).and_return(0)
      allow(Utils).to receive(:notify_pusher)
      allow(Utils).to receive(:add_app_to_cls_tracking_file)
      allow(Dir).to receive(:glob).and_return(["some_path"])
      allow(Shellwords).to receive(:escape).and_return("test_bundle_id")
      # should fail
      allow(OSUtils).to receive(:execute).with("#{IOS_DEPLOY} --id test_device_id --bundle test_bundle_id 2>&1", true).and_return(["", 0])
      allow(Utils).to receive(:mark_event_end)
      allow(Utils).to receive(:mark_event_start)
      allow(Utils).to receive(:send_to_eds)
      allow(Utils).to receive(:write_to_file_with_lock)
      allow(DeviceManager).to receive(:device_configuration_check).and_return({ 'device_version' => "10" })
      allow(live_session.idevice).to receive(:get_device_details_for_app_trust).and_return({})
      allow(live_session.idevice).to receive(:trust_client_enterprise_app).and_return(false)
      live_session.instance_variable_set("@params", { "networkLogs" => "true", "genre" => "app_live_testing" })
      allow(live_session.idevice).to receive(:press_home)
      expect { live_session.send(:install_automate_and_launch_app) }.to raise_error(BrowserStack::AppLiveSession::AppInstallFailureException, "app trust returned false after possible retries")
    end
  end

  describe "stop" do
    it "should call the push_privoxy_logs for stop call" do
      expect(PrivoxyPushRepeater).to receive(:push_privoxy_logs).at_least(:once)
      live_session.stop
    end
  end

  describe "restart" do
    it "should call the execute_restart_action if no exception is raised" do
      live_session.instance_variable_set("@params", { "timezone" => "UTC", "genre" => "app_live_testing", "app_live_session_id" => "12345" , "actions" => "proxy-setting" , "session_restart_events" => {} })
      expect(live_session).to receive(:execute_restart_action)
      expect(BrowserStack.logger).not_to receive(:error)
      live_session.send(:restart)
    end

    it "should call the error msg in case of any exception is raised" do
      # Don't pass actions param in params so action.split will throw exception
      live_session.instance_variable_set("@params", { "timezone" => "UTC", "genre" => "app_live_testing", "app_live_session_id" => "12345" , "session_restart_events" => {} })
      expect(live_session).not_to receive(:execute_restart_action)
      expect(BrowserStack.logger).to receive(:error)
      live_session.send(:restart)
    end
  end

  describe "#start - async flow" do
    before(:each) do
      allow_any_instance_of(BrowserStack::AppLiveSession).to receive(:create_temp_app_folder).and_return nil
      allow_any_instance_of(BrowserStack::AppLiveSession).to receive(:enable_network_logs).and_return nil
      allow_any_instance_of(BrowserStack::AppLiveSession).to receive(:download_app).and_return nil
      allow_any_instance_of(BrowserStack::AppLiveSession).to receive(:add_bundle_id).and_return nil
      allow_any_instance_of(BrowserStack::AppLiveSession).to receive(:install_and_launch_app).and_return nil
      allow(Utils).to receive(:send_to_eds).and_return nil
    end

    it "presetup/download/install/launch should happen sequentially when async flag is not enabled" do
      live_session.instance_variable_set("@params", { "timezone" => "Kolkata", "genre" => "app_live_testing", "app_live_session_id" => "12345", "enable_async_app_launch_flow" => false })
      allow(DeviceManager).to receive(:device_configuration_check).and_return("test_device")

      expect_any_instance_of(BrowserStack::AppLiveSession).to receive(:async_app_launch_setup_enabled?).and_call_original
      expect(IO).to_not receive(:pipe)
      expect_any_instance_of(BrowserStack::LiveSession).to receive(:start).and_return nil

      expect(Utils).to receive(:fork_code_block_for_device).and_call_original

      expect_any_instance_of(Thread).to_not receive(:join)
      expect(Utils).to_not receive(:fork_code_block_for_device)

      live_session.send(:start)
    end

    context "presetup & download/install should happen in parallel when async flag is enabled" do
      xit "should launch the app if download/install app is successful" do
        live_session.instance_variable_set("@params", { "timezone" => "Kolkata", "genre" => "app_live_testing", "app_live_session_id" => "12345", "enable_async_app_launch_flow" => true })
        reader, writer = IO.pipe
        allow(DeviceManager).to receive(:device_configuration_check).and_return("test_device")

        expect_any_instance_of(BrowserStack::AppLiveSession).to receive(:async_app_launch_setup_enabled?).and_call_original
        expect(IO).to receive(:pipe).and_return([reader, writer])
        expect_any_instance_of(BrowserStack::LiveSession).to receive(:start).and_return nil

        expect(Utils).to receive(:fork_code_block_for_device).and_call_original

        expect_any_instance_of(Thread).to receive(:join)
        expect(Utils).to receive(:fork_code_block_for_device).and_yield
        expect(writer).to receive(:close)
        expect(reader).to receive_message_chain(:gets).and_return("download_and_install_success\n")
        expect_any_instance_of(BrowserStack::AppLiveSession).to receive(:launch_app_helper)
        expect(reader).to receive(:close)

        live_session.send(:start)
      end
    end

    xit "should not launch the app if download/install app failed" do
      live_session.instance_variable_set("@params", { "timezone" => "Kolkata", "genre" => "app_live_testing", "app_live_session_id" => "12345", "enable_async_app_launch_flow" => true })
      reader, writer = IO.pipe
      allow(DeviceManager).to receive(:device_configuration_check).and_return("test_device")

      expect_any_instance_of(BrowserStack::AppLiveSession).to receive(:async_app_launch_setup_enabled?).and_call_original
      expect(IO).to receive(:pipe).and_return([reader, writer])
      expect_any_instance_of(BrowserStack::LiveSession).to receive(:start).and_return nil

      expect(Utils).to receive(:fork_code_block_for_device).and_call_original

      expect_any_instance_of(Thread).to receive(:join)
      expect(Utils).to receive(:fork_code_block_for_device).and_yield
      expect(writer).to receive(:close)
      expect(reader).to receive_message_chain(:gets).and_return("download_and_install_fail\n")
      expect_any_instance_of(BrowserStack::AppLiveSession).to_not receive(:launch_app_helper)
      expect(reader).to receive(:close)

      live_session.send(:start)
    end
  end
end
