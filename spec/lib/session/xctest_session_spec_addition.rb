  describe "remove_newlines" do
    it 'should strip whitespace from each element in the array' do
      arr = ["test1 \n", " test2", "test3\n "]
      result = xctest_session.send(:remove_newlines, arr)
      expect(result).to eq(["test1", "test2", "test3"])
    end

    it 'should return an empty array when nil is passed' do
      result = xctest_session.send(:remove_newlines, nil)
      expect(result).to eq([])
    end

    it 'should handle an empty array' do
      result = xctest_session.send(:remove_newlines, [])
      expect(result).to eq([])
    end
  end
