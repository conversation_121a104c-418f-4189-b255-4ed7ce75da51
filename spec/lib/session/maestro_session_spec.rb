require_relative '../../spec_helper'
require_relative '../../../lib/utils/utils'
require_relative '../../../lib/session/maestro_session'

describe BrowserStack::MaestroSession do
  let(:params) do
    {
      'genre' => 'app_automate',
      'device' => 'iPhone XS',
      'automate_session_id' => 'session123',
      'video_aws_bucket' => 'bs-stag',
      'video_aws_region' => 'us-east-1',
      'rails_host' => 'example.com',
      'video_file' => 'video123'
    }
  end

  let(:device_config) do
    {
      'device_version' => '14.0'
    }
  end

  let(:device) { 'iPhone XS' }

  let(:maestro_session) { BrowserStack::MaestroSession.new(params, device_config) }

  it 'creates a maestro_session object' do
    expect(maestro_session).to be_a BrowserStack::MaestroSession
  end

  describe '#start' do
    context 'when no errors occur' do
      it 'executes the start process in order' do
        video_rec_manager = instance_double(VideoRecManager)
        allow(VideoRecManager).to receive(:new).and_return(video_rec_manager)
        allow(DataReportHelper).to receive(:new).and_return(anything)

        expect(maestro_session).to receive(:log_start).with("start").ordered
        # super start
        expect(DeviceManager).to receive(:device_configuration_check)
        expect(PrivoxyManager).to receive(:write_privoxy_log_head)
        expect(DeviceLogger).to receive(:initialize)
        expect(Utils).to receive(:monitor_device_logger_metric)
        expect(Utils).to receive(:write_to_file_with_lock)
        # super end
        expect(maestro_session).to receive(:save_session_params).ordered
        expect(maestro_session).to receive(:setup_params).ordered
        expect(maestro_session).to receive(:collect_maestro_flow_files).ordered
        expect(maestro_session).to receive(:setup_summary).ordered
        expect(maestro_session).to receive(:run_maestro_session).ordered
        expect(maestro_session).to receive(:log_finish).with("start").ordered
        expect(maestro_session).to receive(:stop).ordered

        maestro_session.send(:start)
      end

      it "should handle_test_execution_error in case of exception" do
        video_rec_manager = instance_double(VideoRecManager)
        allow(VideoRecManager).to receive(:new).and_return(video_rec_manager)
        allow(DataReportHelper).to receive(:new).and_return(anything)

        expect(maestro_session).to receive(:log_start).with("start").ordered
        expect(maestro_session).to receive(:handle_test_execution_error).ordered
        expect(maestro_session).to receive(:stop).ordered

        maestro_session.send(:start)
      end
    end
  end

  describe "#save_session_params_v1" do
    let(:params) do
      {
        "build_id" => "b123",
        "automate_session_id" => "s456",
        "user_device_name" => "iPhone 13",
        "idle_timeout" => 120
      }
    end

    let(:summary_file_path) { "/tmp/fake_summary_file.json" }

    before do
      maestro_session.instance_variable_set(:@params, params)
      allow(maestro_session).to receive(:log_start)
      allow(maestro_session).to receive(:log_finish)
      allow(maestro_session).to receive(:maestro_summary_file_path).and_return(summary_file_path)
      allow(Utils).to receive(:write_to_file)
    end

    it "writes summary data to file and logs start/finish" do
      expect(maestro_session).to receive(:log_start).with("save_session_params_v1")
      expect(maestro_session).to receive(:log_finish).with("save_session_params_v1")

      expect(Utils).to receive(:write_to_file) do |path, content|
        expect(path).to eq(summary_file_path)
        json = JSON.parse(content)
        expect(json["build_id"]).to eq("b123")
        expect(json["session_id"]).to eq("s456")
        expect(json["device"]).to eq("iPhone 13")
        expect(json["idle_timeout"]).to eq(120)
        expect(json["duration"]).to eq("")
        expect(json["error_reason"]).to eq("")
        expect(Time.parse(json["start_time"])).to be_within(5).of(Time.now.utc)
      end

      maestro_session.save_session_params_v1
      expect(maestro_session.instance_variable_get(:@summary_file)).to eq(summary_file_path)
    end
  end

  describe "#save_session_params_v2" do
    let(:params) do
      {
        "build_id" => "build123",
        "automate_session_id" => "session456",
        "timeout_sidekiq_worker_hashed_id" => "hashed789",
        "auth_key" => "secret_auth",
        "rails_host" => "rails.local"
      }
    end

    let(:device) { "device_abc" }
    let(:callback_file_path) { "/tmp/#{device}_maestro_callback" }

    before do
      maestro_session.instance_variable_set(:@params, params)
      maestro_session.instance_variable_set(:@device, device)
      allow(maestro_session).to receive(:log_start)
      allow(maestro_session).to receive(:log_finish)
      allow(Utils).to receive(:write_to_file)
    end

    it "writes callback data to file and logs start/finish" do
      expect(maestro_session).to receive(:log_start).with("save_session_params_v2")
      expect(maestro_session).to receive(:log_finish).with("save_session_params_v2")

      expect(Utils).to receive(:write_to_file) do |path, content|
        expect(path).to eq(callback_file_path)
        json = JSON.parse(content)
        expect(json["build_id"]).to eq("build123")
        expect(json["session_id"]).to eq("session456")
        expect(json["timeout_sidekiq_worker_hashed_id"]).to eq("hashed789")
        expect(json["auth_key"]).to eq("secret_auth")
        expect(json["rails_callback"]).to eq("http://rails.local/app-automate/session_done")
        expect(json["device"]).to eq(device)
        expect(json["error_reason"]).to eq("")
      end

      maestro_session.save_session_params_v2
      expect(maestro_session.instance_variable_get(:@callback_file)).to eq(callback_file_path)
    end
  end

  describe "#stop" do
    let(:device) { "iPhone-XYZ" }
    let(:automate_session_id) { "session-123" }
    let(:params) do
      {
        "automate_session_id" => automate_session_id,
        "video" => true
      }
    end
    let(:proxy_log_file_path) { "/tmp/mitm_log.txt" }
    let(:mitmproxy) { double("MitmProxy", proxy_log_file: proxy_log_file_path) }

    before do
      maestro_session.instance_variable_set(:@device, device)
      maestro_session.instance_variable_set(:@params, params)
      maestro_session.instance_variable_set(:@mitmproxy, mitmproxy)

      allow(maestro_session).to receive(:log_start)
      allow(maestro_session).to receive(:log_finish)
      allow(maestro_session).to receive(:stop_maestro_ui_runner)
      allow(maestro_session).to receive(:stop_iproxy_forwarding)
      allow(maestro_session).to receive(:push_feature_usage_to_eds)
      allow(maestro_session).to receive(:upload_summary)
      allow(maestro_session).to receive(:cleanup_files)
      allow(maestro_session).to receive(:inform_rails)
      allow(maestro_session).to receive(:super)
      allow(maestro_session).to receive(:video_enabled?).and_return(true)
      allow(maestro_session).to receive(:boot_mitmproxy?).and_return(true)

      allow(BrowserStack::DeviceLogger).to receive(:destroy)
      allow(BrowserStack::OSUtils).to receive(:execute).and_return(["mitm error log output"])
      allow(Utils).to receive(:write_to_file)
      allow(MitmProxy).to receive(:stop_proxy)
      allow(BrowserStack).to receive_message_chain(:logger, :info)
      allow(BrowserStack).to receive_message_chain(:logger, :error)
      allow(BrowserStack::Zombie).to receive(:push_logs)
    end

    it "calls all necessary cleanup and logging methods" do
      expect(maestro_session).to receive(:log_start).with("stop")
      expect(maestro_session).to receive(:log_finish).with("stop")
      expect(BrowserStack::DeviceLogger).to receive(:destroy).with(device)
      expect(maestro_session).to receive(:stop_maestro_ui_runner)
      expect(maestro_session).to receive(:stop_iproxy_forwarding)
      expect(maestro_session).to receive(:push_feature_usage_to_eds)
      expect(maestro_session).to receive(:upload_summary)
      expect(Utils).to receive(:write_to_file).with("/tmp/video_params_#{device}", params.to_json)
      expect(MitmProxy).to receive(:stop_proxy).with(device, automate_session_id, "maestro")
      expect(BrowserStack::OSUtils).to receive(:execute).with(/tail -100 .*mitm_log\.txt/, true, timeout: 3)
      expect(BrowserStack::Zombie).to receive(:push_logs).with(
        "xcuitest-mitm-exception",
        "Exception in mitm proxy logfile",
        hash_including("session_id" => automate_session_id, "data" => "mitm error log output")
      )
      expect(maestro_session).to receive(:cleanup_files)
      expect(maestro_session).to receive(:inform_rails)

      maestro_session.stop
    end

    context "when boot_mitmproxy? is false" do
      before { allow(maestro_session).to receive(:boot_mitmproxy?).and_return(false) }

      it "skips mitmproxy-related logic" do
        expect(MitmProxy).not_to receive(:stop_proxy)
        expect(BrowserStack::OSUtils).to receive(:execute)

        maestro_session.stop
      end
    end

    context "when an exception is raised" do
      before do
        allow(maestro_session).to receive(:stop_maestro_ui_runner).and_raise(StandardError.new("fail during stop"))
      end

      it "logs the error and still calls cleanup and inform_rails" do
        expect(BrowserStack.logger).to receive(:error).with(/Error in Maestro Stop: fail during stop/)
        expect(maestro_session).to receive(:cleanup_files)
        expect(maestro_session).to receive(:inform_rails)

        maestro_session.stop
      end
    end
  end

  describe "#ensure_session_stop" do
    let(:device) { "iPhone-XYZ" }
    let(:automate_session_id) { "session-123" }
    let(:params) { { "automate_session_id" => automate_session_id } }
    let(:pid_file_path) { "/tmp/spawn_xctest_pid" }
    let(:pid) { 1234 }
    let(:logger) { double("Logger", info: nil, error: nil, params: {}) }
    let(:timeout_manager_instance) { double("MaestroTimeoutManager", kill_idle_process: nil, stop: nil) }

    before do
      maestro_session.instance_variable_set(:@device, device)
      maestro_session.instance_variable_set(:@params, params)

      allow(maestro_session).to receive(:spawn_xctest_pid_file_path).and_return(pid_file_path)

      stub_const("AppAutomateFrameworks::ProcessUtils", Class.new)
      allow(AppAutomateFrameworks::ProcessUtils).to receive(:get_process_id).with(pid_file_path).and_return(pid)
      allow(AppAutomateFrameworks::ProcessUtils).to receive(:process_running?).with(pid).and_return(true)
      allow(AppAutomateFrameworks::ProcessUtils).to receive(:kill_pid).with(pid)

      allow(FileUtils).to receive(:rm_rf)
      allow(BrowserStack).to receive(:logger).and_return(logger)
      allow(BrowserStack::DeviceLogger).to receive(:destroy)
      allow(maestro_session).to receive(:stop_maestro_ui_runner)
      allow(maestro_session).to receive(:stop_iproxy_forwarding)
      allow(maestro_session).to receive(:ensure_session_files_cleanup)
      allow(MitmProxy).to receive(:stop_proxy)
      allow(BrowserStack::Zombie).to receive(:push_logs)

      stub_const("AppAutomateFrameworks::MaestroTimeoutManager", Class.new)
      allow(AppAutomateFrameworks::MaestroTimeoutManager).to receive(:new).and_return(timeout_manager_instance)
    end

    context "when process is running and MaestroTimeoutManager is defined" do
      it "kills the process, stops timeout manager and cleans up resources" do
        expect(AppAutomateFrameworks::ProcessUtils).to receive(:kill_pid).with(pid)
        expect(timeout_manager_instance).to receive(:kill_idle_process)
        expect(timeout_manager_instance).to receive(:stop)
        expect(MitmProxy).to receive(:stop_proxy).with(device, automate_session_id, "maestro")
        expect(BrowserStack::Zombie).to receive(:push_logs).with(
          "cleanup-forcefully-stopping-session",
          "Invalid cleanup request received while session is running",
          hash_including("session_id" => automate_session_id)
        )

        maestro_session.ensure_session_stop
      end
    end

    context "when process is not running" do
      before do
        allow(AppAutomateFrameworks::ProcessUtils).to receive(:process_running?).and_return(false)
      end

      it "removes the pid file and exits early" do
        expect(FileUtils).to receive(:rm_rf).with(pid_file_path)
        expect(AppAutomateFrameworks::ProcessUtils).not_to receive(:kill_pid)
        expect(maestro_session).not_to receive(:stop_maestro_ui_runner)

        maestro_session.ensure_session_stop
      end
    end

    context "when MaestroTimeoutManager is not defined" do
      before do
        hide_const("AppAutomateFrameworks::MaestroTimeoutManager")
      end

      it "skips timeout manager logic and logs a message" do
        expect(logger).to receive(:info).with(/MaestroTimeoutManager is not available/)
        expect(maestro_session).to receive(:stop_maestro_ui_runner)
        expect(maestro_session).to receive(:stop_iproxy_forwarding)

        maestro_session.ensure_session_stop
      end
    end

    context "when an exception occurs" do
      before do
        allow(AppAutomateFrameworks::ProcessUtils).to receive(:kill_pid).and_raise(StandardError.new("kill failed"))
      end

      it "logs the error and re-raises" do
        expect(logger).to receive(:error).with(/Error in Maestro ensure_session_stop kill failed/)
        expect { maestro_session.ensure_session_stop }.to raise_error(StandardError, "kill failed")
      end
    end
  end

  describe "#force_stop" do
    let(:device) { "iPhone-13-Pro" }
    let(:logger) { double("Logger", info: nil, error: nil, params: {}) }
    let(:device_state) { double("DeviceState", touch_maestro_session_timedout_file: nil) }
    let(:timeout_manager_instance) { double("MaestroTimeoutManager", kill_idle_process: nil) }

    before do
      maestro_session.instance_variable_set(:@device, device)
      maestro_session.instance_variable_set(:@device_state, device_state)

      allow(BrowserStack).to receive(:logger).and_return(logger)
      allow(maestro_session).to receive(:log_start).with("force_stop")
      allow(maestro_session).to receive(:log_finish).with("force_stop")
    end

    context "when MaestroTimeoutManager is defined" do
      before do
        stub_const("AppAutomateFrameworks::MaestroTimeoutManager", Class.new)
        allow(AppAutomateFrameworks::MaestroTimeoutManager).to receive(:new).and_return(timeout_manager_instance)
      end

      it "touches timeout file, kills idle process, and logs actions" do
        expect(device_state).to receive(:touch_maestro_session_timedout_file)
        expect(AppAutomateFrameworks::MaestroTimeoutManager).to receive(:new)
          .with(device, nil, logger: logger, framework: "maestro", test_id: nil, logger_params: logger.params, script_identifier: ["spawn_xctest.rb"]).and_return(timeout_manager_instance)
        expect(timeout_manager_instance).to receive(:kill_idle_process)
        expect(logger).to receive(:info).with("force_stop : created timeout file for device: #{device}")
        expect(logger).to receive(:info).with("Killed running session for device: #{device}")
        expect(maestro_session).to receive(:log_finish).with("force_stop")

        maestro_session.force_stop
      end
    end

    context "when MaestroTimeoutManager is not defined" do
      before do
        hide_const("AppAutomateFrameworks::MaestroTimeoutManager")
      end

      it "logs a fallback message and completes execution" do
        expect(device_state).to receive(:touch_maestro_session_timedout_file)
        expect(logger).to receive(:info).with("MaestroTimeoutManager is not available, skipping execution.")
        expect(logger).to receive(:info).with("Killed running session for device: #{device}")
        expect(maestro_session).to receive(:log_finish).with("force_stop")

        maestro_session.force_stop
      end
    end
  end

  describe "#collect_maestro_flow_files" do
    let(:session_id) { "abc123" }
    let(:device) { "iPhone-13-Pro" }
    let(:java_home) { "/path/to/java" }
    let(:maestro_cli_path) { "/path/to/maestro" }
    let(:flow_path) { "tests/sample.yaml" }
    let(:input_paths) { [flow_path] }
    let(:dry_run_path) { "/tmp/#{session_id}/" }
    let(:dry_run_content_path) { "#{dry_run_path}execution_plan.json" }
    let(:logger) { double("Logger", info: nil, error: nil) }
    let(:flow_file_content) { [{ "name" => "Test Flow 1" }] }

    before do
      maestro_session.instance_variable_set(:@params, { 'execute' => input_paths, 'automate_session_id' => session_id })
      maestro_session.instance_variable_set(:@downloaded_test_app_path, "")
      maestro_session.instance_variable_set(:@java_home, java_home)
      maestro_session.instance_variable_set(:@maestro_cli_path, maestro_cli_path)
      maestro_session.instance_variable_set(:@session_id, session_id)

      allow(maestro_session).to receive(:log_start)
      allow(maestro_session).to receive(:log_finish)
      allow(maestro_session).to receive(:combine_paths).and_return(input_paths)
      allow(BrowserStack).to receive(:logger).and_return(logger)
    end

    context "when flow files are found and valid" do
      before do
        allow(BrowserStack::OSUtils).to receive(:execute).and_return(["execution log", 0])
        allow(File).to receive(:exist?).with(dry_run_content_path).and_return(true)
        allow(File).to receive(:read).with(dry_run_content_path).and_return(flow_file_content.to_json)
      end

      it "parses the flow files and logs success" do
        expect(logger).to receive(:info).with(/Collected Maestro flow files/)
        maestro_session.collect_maestro_flow_files
        expect(maestro_session.instance_variable_get(:@flow_files)).to eq(flow_file_content)
      end
    end

    context "when execution_plan.json does not exist" do
      before do
        allow(BrowserStack::OSUtils).to receive(:execute).and_return(["execution log", 0])
        allow(File).to receive(:exist?).with(dry_run_content_path).and_return(false)
      end

      it "logs error and raises 'No Maestro flow files found'" do
        expect(logger).to receive(:error).with(/No Maestro flow files found/)
        expect(maestro_session).to receive(:handle_flow_file_parse_failed).with(instance_of(RuntimeError))
        maestro_session.collect_maestro_flow_files
      end
    end

    context "when JSON is invalid or not an array" do
      before do
        allow(BrowserStack::OSUtils).to receive(:execute).and_return(["log", 0])
        allow(File).to receive(:exist?).with(dry_run_content_path).and_return(true)
        allow(File).to receive(:read).with(dry_run_content_path).and_return({ invalid: "json" }.to_json)
      end

      it "logs error and clears @flow_files" do
        expect(logger).to receive(:error).with(/flow files is not an array/)
        expect(logger).to receive(:error).with(/No Maestro flow files found/)
        expect(maestro_session).to receive(:handle_flow_file_parse_failed).with(instance_of(RuntimeError))
        maestro_session.collect_maestro_flow_files
        expect(maestro_session.instance_variable_get(:@flow_files)).to eq([])
      end
    end

    context "when dry run command raises an exception" do
      before do
        allow(BrowserStack::OSUtils).to receive(:execute).and_raise(StandardError.new("Command failed"))
      end

      it "handles and logs the exception" do
        expect(maestro_session).to receive(:handle_flow_file_parse_failed).with(instance_of(StandardError))
        maestro_session.collect_maestro_flow_files
      end
    end
  end

  describe "#combine_paths" do
    let(:logger) { double("Logger", info: nil) }

    before do
      allow(BrowserStack).to receive(:logger).and_return(logger)
    end

    it "returns base path when input_paths is nil" do
      result = maestro_session.combine_paths("/test/path", nil)
      expect(result).to eq(["/test/path"])
    end

    it "returns base path when input_paths is empty array" do
      result = maestro_session.combine_paths("/test/path", [])
      expect(result).to eq(["/test/path"])
    end

    it "returns base path when input_paths is empty string" do
      result = maestro_session.combine_paths("/test/path", "")
      expect(result).to eq(["/test/path"])
    end

    it "adds trailing slash if not present in base path and joins properly" do
      result = maestro_session.combine_paths("/test/path", ["flow1.yaml", "flow2.yaml"])
      expect(result).to eq(["/test/path/flow1.yaml", "/test/path/flow2.yaml"])
    end

    it "does not duplicate slash if base path ends with one" do
      result = maestro_session.combine_paths("/test/path/", ["flow1.yaml"])
      expect(result).to eq(["/test/path/flow1.yaml"])
    end

    it "removes leading slash from relative paths before joining" do
      result = maestro_session.combine_paths("/test/path", ["/flow1.yaml", "/flow2.yaml"])
      expect(result).to eq(["/test/path/flow1.yaml", "/test/path/flow2.yaml"])
    end

    it "logs the input parameters" do
      expect(logger).to receive(:info).with("combine_paths: /root/flows [\"flow1.yaml\"]")
      maestro_session.combine_paths("/root/flows", ["flow1.yaml"])
    end
  end

  describe "#setup_summary_v1" do
    let(:test_hash) { { "some_test" => { status: "QUEUED" } } }
    let(:summary_data) { { "build_id" => "b123", "session_id" => "s456" } }
    let(:expected_summary) do
      summary_data.merge(
        {
          "test_count" => 1,
          "test_details" => test_hash,
          "test_status" => {
            "SUCCESS" => 0,
            "FAILED" => 0,
            "IGNORED" => 0,
            "TIMEDOUT" => 0,
            "ERROR" => 0,
            "RUNNING" => 0,
            "QUEUED" => 1
          }
        }
      )
    end

    before do
      maestro_session.instance_variable_set(:@summary_file, "/tmp/summary_v1.json")
      maestro_session.instance_variable_set(:@total_test_count, 1)

      allow(maestro_session).to receive(:log_start)
      allow(maestro_session).to receive(:log_finish)
      allow(maestro_session).to receive(:test_hash_v1).and_return(test_hash)
      allow(maestro_session).to receive(:total_number_of_tests)
      allow(maestro_session).to receive(:get_summary_data).with("/tmp/summary_v1.json").and_return(summary_data)
      allow(Utils).to receive(:write_to_file)
    end

    it "writes expected summary data to file" do
      expect(Utils).to receive(:write_to_file).with("/tmp/summary_v1.json", expected_summary.to_json)
      maestro_session.setup_summary_v1
    end
  end

  describe "#setup_summary_v2" do
    let(:test_hash_v2) { { "SomeClass" => [{ name: "testLogin", status: "QUEUED" }] } }
    let(:test_summary) do
      {
        "SUCCESS" => 0,
        "FAILED" => 0,
        "IGNORED" => 0,
        "TIMEDOUT" => 0,
        "ERROR" => 0,
        "RUNNING" => 0,
        "QUEUED" => 1
      }
    end

    before do
      maestro_session.instance_variable_set(:@params, {
        "build_id" => "build_id_123",
        "automate_session_id" => "session_123"
      })
      maestro_session.instance_variable_set(:@total_test_count, 1)
      maestro_session.instance_variable_set(:@summary_file_v2, "/tmp/summary_v2.json")

      allow(maestro_session).to receive(:log_start)
      allow(maestro_session).to receive(:log_finish)
      allow(maestro_session).to receive(:summary_path_v2).and_return("/tmp/summary_v2.json")
      allow(maestro_session).to receive(:test_hash_v2).and_return(test_hash_v2)
      allow(maestro_session).to receive(:test_states).with(1).and_return(test_summary)
      allow(Utils).to receive(:write_to_file)
    end

    it "writes expected summary v2 data to file" do
      expected_data = {
        build_id: "build_id_123",
        session_id: "session_123",
        test_summary: test_summary,
        classes: test_hash_v2
      }

      expect(Utils).to receive(:write_to_file).with("/tmp/summary_v2.json", expected_data.to_json)
      maestro_session.setup_summary_v2
    end
  end

  describe "#generate_xctestrun_xml" do
    let(:ppuid_file) { "some-ppuid" }
    let(:signed_app_path) { "/signed/path/to/app.app" }
    let(:xctest_xmlfile_path) { "/tmp/fake_xctest.xml" }
    let(:erb_template_path) { "/fake/path/maestro_xctestrun.erb" }
    let(:erb_content) { "generated xml content" }

    before do
      maestro_session.instance_variable_set(:@device, "device123")
      maestro_session.instance_variable_set(:@device_os_version, "17.2")
      maestro_session.instance_variable_set(:@xctest_xmlfile, xctest_xmlfile_path)
      maestro_session.instance_variable_set(:@server_config, { "signed_app_dir" => "/signed/app" })

      allow(maestro_session).to receive(:log_start)
      allow(maestro_session).to receive(:log_finish)

      mock_ppuid_file = double(ppuid: ppuid_file)
      allow(PpuidFile).to receive(:new).with("device123").and_return(mock_ppuid_file)

      mock_runner = double("MaestroUIRunner")
      allow(mock_runner).to receive(:update_app_version_using_ios_version)
      allow(mock_runner).to receive(:signed_app_path).and_return(signed_app_path)
      allow(MaestroUIRunner).to receive(:new).and_return(mock_runner)

      erb_file = double("ERBFile", read: "template content")
      allow(File).to receive(:open).with(erb_template_path).and_return(erb_file)

      fake_erb = double("ERB", result: erb_content)
      allow(ERB).to receive(:new).and_return(fake_erb)

      allow(Utils).to receive(:write_to_file)
      allow(FileUtils).to receive(:rm_rf)
    end

    it "generates xml from erb and writes to file" do
      expect(Utils).to receive(:write_to_file).with(xctest_xmlfile_path, erb_content)
      expect(FileUtils).to receive(:rm_rf).with("/tmp/device123_test")
      maestro_session.generate_xctestrun_xml(erb_template_path)
    end
  end

  describe "#start_maestro_ui_runner" do
    let(:pid_file_path) { "/tmp/device123_maestro_ui.pid" }

    before do
      maestro_session.instance_variable_set(:@device, "device123")
      maestro_session.instance_variable_set(:@xctest_xmlfile, "/tmp/test.xml")
      maestro_session.instance_variable_set(:@params, { "session_time" => "120" })

      allow(maestro_session).to receive(:generate_xctestrun_xml)
      allow(maestro_session).to receive(:log_start)
      allow(maestro_session).to receive(:log_finish)
      allow(maestro_session).to receive(:maestro_ui_pid_file_path).and_return(pid_file_path)
      allow(BrowserStack::OSUtils).to receive(:execute)

      allow(File).to receive(:read).with(pid_file_path).and_return("4567")
      allow(BrowserStack.logger).to receive(:info)
    end

    it "generates xml, runs xcodebuild, and logs PID" do
      expect(maestro_session).to receive(:generate_xctestrun_xml)
      expect(BrowserStack::OSUtils).to receive(:execute).with(
        a_string_including("xcodebuild"),
        true
      )
      expect(BrowserStack.logger).to receive(:info).with("Started Maestro UI Runner with PID: 4567")
      maestro_session.start_maestro_ui_runner
    end
  end

  describe "#stop_maestro_ui_runner" do
    let(:pid_file_path) { "/tmp/device123_maestro_ui.pid" }

    before do
      maestro_session.instance_variable_set(:@device, "device123")
      maestro_session.instance_variable_set(:@params, { "automate_session_id" => "sess_123" })

      allow(maestro_session).to receive(:maestro_ui_pid_file_path).and_return(pid_file_path)
      allow(File).to receive(:exist?).with(pid_file_path).and_return(true)
      allow(File).to receive(:read).with(pid_file_path).and_return("9999")
      allow(AppAutomateFrameworks::ProcessUtils).to receive(:process_running?).with(9999).and_return(true)
      allow(BrowserStack::OSUtils).to receive(:kill_pid).with(9999)
      allow(FileUtils).to receive(:rm_rf)
      allow(BrowserStack.logger).to receive(:info)
    end

    it "kills process if running and removes pid file" do
      expect(AppAutomateFrameworks::ProcessUtils).to receive(:get_process_id).and_return("9999")
      expect(AppAutomateFrameworks::ProcessUtils).to receive(:process_running?).and_return(true)
      expect(BrowserStack::OSUtils).to receive(:kill_pid).with(9999)
      expect(FileUtils).to receive(:rm_rf).with(pid_file_path)
      maestro_session.stop_maestro_ui_runner
    end

    context "when PID file does not exist" do
      it "does nothing" do
        allow(File).to receive(:exist?).with(pid_file_path).and_return(false)
        expect(FileUtils).not_to receive(:rm_rf)
        maestro_session.stop_maestro_ui_runner
      end
    end
  end

  describe "#run_maestro_flow" do
    let(:flow_file) { "/tmp/sample_flow.yaml" }
    let(:classname) { "SampleClass" }
    let(:testname) { "SampleClass" }
    let(:test_num) { 0 }
    let(:retry_count) { 0 }
    let(:session_id) { "abc123" }
    let(:debug_output) { "/tmp/#{session_id}/#{device}_maestro_debug_#{classname}_#{testname}_#{test_num}" }
    let(:instru_logs_path) { "#{debug_output}/instrulogs.xml" }
    let(:maestro_cmd) { instance_double(String) }
    let(:output) { "Command output" }

    before do
      maestro_session.instance_variable_set(:@device, device)
      maestro_session.instance_variable_set(:@session_id, session_id)
      maestro_session.instance_variable_set(:@params, { "wda_port" => 8100 })
      maestro_session.instance_variable_set(:@maestro_cli_path, "/path/to/maestro")
      maestro_session.instance_variable_set(:@java_home, "/path/to/java")

      allow(maestro_session).to receive(:log_start)
      allow(maestro_session).to receive(:log_finish)
      allow(maestro_session).to receive(:extract_classname).with(flow_file).and_return(classname)
      allow(maestro_session).to receive(:test_expire_time).and_return(60)
      allow(maestro_session).to receive(:start_timeout_manager)
      allow(maestro_session).to receive(:stop_timeout_manager)
    end

    context "when command executes successfully and instru logs exist" do
      before do
        allow(BrowserStack::OSUtils).to receive(:execute).and_return([output, 0])
        allow(File).to receive(:exist?).with(instru_logs_path).and_return(true)
        allow(maestro_session).to receive(:parse_instru_logs).with(instru_logs_path, classname, testname).and_return(nil)
      end

      it "returns SUCCESS and command output" do
        status, duration, result = maestro_session.run_maestro_flow(flow_file)
        expect(status).to eq("SUCCESS")
        expect(result).to eq(output)
        expect(duration).to be_a(Float)
      end
    end

    context "when command times out and instru logs do not exist" do
      before do
        allow(BrowserStack::OSUtils).to receive(:execute).and_return([output, described_class::TIMEDOUT_EXIT_CODE])
        allow(File).to receive(:exist?).with(instru_logs_path).and_return(false)
      end

      it "returns TIMEDOUT and command output" do
        status, duration, result = maestro_session.run_maestro_flow(flow_file)
        expect(status).to eq("TIMEDOUT")
        expect(result).to eq(output)
      end
    end

    context "when command fails with an unknown error" do
      before do
        allow(BrowserStack::OSUtils).to receive(:execute).and_return([output, 1])
        allow(File).to receive(:exist?).with(instru_logs_path).and_return(false)
      end

      it "returns FAILED and command output" do
        status, duration, result = maestro_session.run_maestro_flow(flow_file)
        expect(status).to eq("FAILED")
        expect(result).to eq(output)
      end
    end
  end

  describe "#parse_instru_logs" do
    let(:instru_logs_path) { "/tmp/fake_instrulogs.xml" }
    let(:expected_classname) { "SampleTestClass" }
    let(:expected_testname) { "SampleTestClass" }
    let(:xml_content) { "" }

    before do
      # Allow File.read to behave normally unless overridden
      allow(File).to receive(:read).and_call_original

      # Stub only the XML log path used in the test
      allow(File).to receive(:read).with(instru_logs_path).and_return(xml_content)
    end

    context "when test is successful" do
      let(:xml_content) do
        <<-XML
          <testsuite>
            <testcase classname="#{expected_classname}" name="#{expected_testname}" time="1.23" />
          </testsuite>
        XML
      end

      it "returns SUCCESS" do
        result = maestro_session.parse_instru_logs(instru_logs_path, expected_classname, expected_testname)
        expect(result).to eq("SUCCESS")
      end
    end

    context "when test fails with matching error pattern" do
      let(:xml_content) do
        <<-XML
          <testsuite>
            <testcase classname="#{expected_classname}" name="#{expected_testname}">
              <failure>java.lang.NullPointerException</failure>
            </testcase>
          </testsuite>
        XML
      end

      before do
        stub_const("#{maestro_session.class}::ERROR_REGEX_PATTERNS", [/NullPointerException/])
      end

      it "returns ERROR" do
        result = maestro_session.parse_instru_logs(instru_logs_path, expected_classname, expected_testname)
        expect(result).to eq("ERROR")
      end
    end

    context "when test fails but doesn't match error pattern" do
      let(:xml_content) do
        <<-XML
          <testsuite>
            <testcase classname="#{expected_classname}" name="#{expected_testname}">
              <failure>Some unexpected failure</failure>
            </testcase>
          </testsuite>
        XML
      end

      before do
        stub_const("#{maestro_session.class}::ERROR_REGEX_PATTERNS", [/TimeoutError/])
      end

      it "returns FAILED" do
        result = maestro_session.parse_instru_logs(instru_logs_path, expected_classname, expected_testname)
        expect(result).to eq("FAILED")
      end
    end

    context "when testcase has no failure or time" do
      let(:xml_content) do
        <<-XML
          <testsuite>
            <testcase classname="#{expected_classname}" name="#{expected_testname}" />
          </testsuite>
        XML
      end

      it "returns nil" do
        result = maestro_session.parse_instru_logs(instru_logs_path, expected_classname, expected_testname)
        expect(result).to be_nil
      end
    end

    context "when testsuite is missing" do
      let(:xml_content) { "<root></root>" }

      it "returns nil" do
        result = maestro_session.parse_instru_logs(instru_logs_path, expected_classname, expected_testname)
        expect(result).to be_nil
      end
    end

    context "when XML is invalid or raises exception" do
      before do
        allow(File).to receive(:read).with(instru_logs_path).and_raise(StandardError.new("file read error"))
      end

      it "logs error and returns nil" do
        expect(BrowserStack.logger).to receive(:error).with(/Error parsing instrulogs/)
        result = maestro_session.parse_instru_logs(instru_logs_path, expected_classname, expected_testname)
        expect(result).to be_nil
      end
    end
  end

  describe '#run_maestro_session' do
    let(:session) { described_class.new(params, device) }
    let(:flow_file) { '/path/to/test_flow.yaml' }
    let(:summary_file_v2_data) do
      {
        'test_summary' => {
          'failed' => 1,
          'passed' => 2,
          'skipped' => 0,
          'timedout' => 0,
          'total' => 3
        }
      }
    end

    before do
      allow(session).to receive(:log_start)
      allow(session).to receive(:log_finish)
      allow(session).to receive(:setup_maestro_session)
      allow(session).to receive(:session_timedout?).and_return(false)
      allow(session).to receive(:extract_classname).and_return('ExampleClass')
      allow(session).to receive(:get_md5).and_return('md5hash')
      allow(session).to receive(:update_test_status_to_running)
      allow(session).to receive(:notify_test_name_info_to_pusher)
      allow(session).to receive(:get_video_duration).and_return(0)
      allow(session).to receive(:video_enabled?).and_return(true)
      allow(session).to receive(:run_maestro_flow).and_return(['SUCCESS', 10.0, 'some output'])
      allow(session).to receive(:update_test_summary)
      allow(session).to receive(:upload_logfiles)
      allow(session).to receive(:upload_maestro_debug_files)
      allow(session).to receive(:update_timeout_tests)
      allow(session).to receive(:update_session_duration)

      session.instance_variable_set(:@flow_files, [flow_file])
      session.instance_variable_set(:@video_s3url, 'http://example.com/video.mp4')
      session.instance_variable_set(:@session_id, '123456')
      session.instance_variable_set(:@summary_file_v2, '/tmp/summary_v2.json')
      session.instance_variable_set(:@callback_file, '/tmp/callback.json')

      File.write('/tmp/summary_v2.json', summary_file_v2_data.to_json)
      File.write('/tmp/callback.json', { "key" => "value" }.to_json)

      allow(File).to receive(:read).with('/tmp/summary_v2.json').and_return(summary_file_v2_data.to_json)
      allow(File).to receive(:read).with('/tmp/callback.json').and_return({ "key" => "value" }.to_json)
      allow(Utils).to receive(:write_to_file)
      allow(BrowserStack::Zombie).to receive(:push_logs)
    end

    after do
      FileUtils.rm_f('/tmp/summary_v2.json')
      FileUtils.rm_f('/tmp/callback.json')
    end

    it 'runs the session, executes the flow, and updates logs and summary' do
      expect(session).to receive(:setup_maestro_session)
      expect(session).to receive(:run_maestro_flow).and_return(['SUCCESS', 10.0, 'output'])
      expect(BrowserStack::Zombie).to receive(:push_logs).with(
        "app_automation_session_stats", '',
        hash_including(
          "sessionid" => "123456",
          "test_failed" => 1,
          "test_success" => 2,
          "test_ignored" => 0,
          "test_timedout" => 0
        )
      )
      session.run_maestro_session
    end
  end

  describe '#confirm_ui_runner_running' do
    let(:session) { described_class.new(params, device) }
    let(:url) { "http://127.0.0.1:10800/status" }
    let(:wda_port) { 8100 }

    before do
      stub_request(:get, url)
        .to_return(status: 200, body: { status: 'ok' }.to_json)

      # Stub PID check
      allow(session).to receive(:confirm_maestro_ui_runner_pid_running).and_return(true)
      session.instance_variable_set(:@params, { 'wda_port' => wda_port })

      # Stub sleep to avoid actual delays
      allow(session).to receive(:sleep)
    end

    it 'returns true and logs success' do
      expect(session.confirm_ui_runner_running).to be true
      expect(BrowserStack.logger).to have_received(:info).with(/Checking if Maestro UI Runner is running/)
      expect(BrowserStack.logger).to have_received(:info).with(/Status response:/)
      expect(BrowserStack.logger).to have_received(:info).with(/status: true pid_status: true/)
    end

    context 'when the Maestro UI Runner responds with an invalid status' do
      before do
        # Stub HTTP request with invalid status
        stub_request(:get, url)
          .to_return(status: 200, body: { status: 'error' }.to_json)

        # Stub PID check
        allow(session).to receive(:confirm_maestro_ui_runner_pid_running).and_return(false)
      end

      it 'retries up to max_attempts and returns false' do
        expect(session.confirm_ui_runner_running).to be false
        expect(a_request(:get, url)).to have_been_made.times(12)
        expect(BrowserStack.logger).to have_received(:error).with(/Maestro UI Runner is not running/)
        expect(session).to have_received(:sleep).with(2).exactly(11).times
      end
    end

    context 'when the HTTP request fails' do
      before do
        # Stub HTTP request to raise an error
        stub_request(:get, url).to_raise(StandardError.new('Connection refused'))

        # Stub PID check
        allow(session).to receive(:confirm_maestro_ui_runner_pid_running).and_return(false)
      end

      it 'retries up to max_attempts and returns false' do
        expect(session.confirm_ui_runner_running).to be false
        expect(a_request(:get, url)).to have_been_made.times(12)
        expect(session).to have_received(:sleep).with(2).exactly(11).times
      end
    end

    context 'when the response body is empty' do
      before do
        # Stub HTTP request with empty body
        stub_request(:get, url)
          .to_return(status: 200, body: {}.to_json)

        # Stub PID check
        allow(session).to receive(:confirm_maestro_ui_runner_pid_running).and_return(false)
      end

      it 'retries up to max_attempts and returns false' do
        expect(session.confirm_ui_runner_running).to be false
        expect(a_request(:get, url)).to have_been_made.times(12)
        expect(BrowserStack.logger).to have_received(:error).with(/Maestro UI Runner is not running/)
        expect(session).to have_received(:sleep).with(2).exactly(11).times
      end
    end

    context 'when the response is not successful' do
      before do
        # Stub HTTP request with non-success status
        stub_request(:get, url)
          .to_return(status: 500, body: '')

        # Stub PID check
        allow(session).to receive(:confirm_maestro_ui_runner_pid_running).and_return(false)
      end

      it 'retries up to max_attempts and returns false' do
        expect(session.confirm_ui_runner_running).to be false
        expect(a_request(:get, url)).to have_been_made.times(12)
        expect(BrowserStack.logger).to have_received(:error).with(/Maestro UI Runner is not running/)
        expect(session).to have_received(:sleep).with(2).exactly(11).times
      end
    end
  end
end
