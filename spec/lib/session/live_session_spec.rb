require_relative '../../../server/iphone'
require_relative '../../../lib/utils/utils'
require_relative '../../../lib/utils/web_driver_agent'

describe BrowserStack::LiveSession do
  let(:params) { { 'loader_trigger' => 'switcher', 'device_browser' => 'safari', 'session_start_events' => {} } }
  let(:live_session) { BrowserStack::LiveSession.new(params, {}) }

  before :each do
    #stub the parent class
    video_rec_manager = instance_double(VideoRecManager)
    expect(VideoRecManager).to receive(:new).with(nil, params).and_return(video_rec_manager)
  end

  it 'creates a LiveSession object' do
    expect(live_session).to be_a BrowserStack::LiveSession
  end

  describe "start" do
    before :each do
      #stub the parent class
      allow_any_instance_of(BrowserStack::Session).to receive(:start).and_return(nil)

      allow(BrowserStack::Session).to receive(:setup_local).and_return(nil)
      allow(live_session).to receive(:start_webdriver_agent).and_return(nil)
      allow(live_session).to receive(:start_device_logger).and_return(nil)
      allow(live_session).to receive(:set_geolocation).and_return(nil)
    end

    describe "iproxy" do
    end

    describe "start_webdriver_agent" do
      before :each do
        allow(BrowserStack::WebDriverAgent).to receive(:launch)
        allow(live_session).to receive(:start_webdriver_agent).and_call_original
      end
    end
  end

  describe "restart_iwdp_on_switch_browser" do
    let(:device) { '000820-427503425' }
    let(:idevice) { instance_double(BrowserStack::IPhone) }

    context "browser switch and same device session" do
      it "should restart webkit_proxy" do
        live_session.instance_variable_set(:@is_update_session, true)
        allow(BrowserStack::IPhone).to receive(:new).and_return(idevice)

        expect(idevice).to receive(:restart_webkit_proxy).once.ordered
        live_session.send(:restart_iwdp_on_switch_browser)
      end
    end
  end

  describe "kill_browser_during_switch" do
    let(:device) { '000820-427503425' }
    let(:idevice) { instance_double(BrowserStack::IPhone) }

    before(:each) do
      live_session.instance_variable_set(:@is_update_session, true)
      live_session.instance_variable_set(:@device, device)
      allow(BrowserStack::IPhone).to receive(:new).and_return(idevice)
    end

    context "browser switch and same device session" do
      it "should kill the browser and relaunch iwdp for Safari browser" do
        live_session.instance_variable_set(:@is_update_session, true)

        expect_any_instance_of(BrowserStack::Chrome).to receive(:cleanup).and_return(nil)
        expect(idevice).to receive(:restart_webkit_proxy).once
        live_session.send(:kill_browser_during_switch)
      end

      it "shouldn't do anything for Chrome browser" do
        params["device_browser"] = "chrome"

        expect(idevice).not_to receive(:restart_webkit_proxy)
        live_session.send(:kill_browser_during_switch)
      end
    end
  end

  describe "start_device_logger" do
    let(:update_session?) { false }
    let(:device) { '000820-427503425' }

    context "normal flow without sim params" do
      it "success" do
        expect(BrowserStack::DeviceLogger).to receive(:start)
        live_session.send(:start_device_logger, false)
      end
    end

    context "flow with enable_sim_live true and force_setup_when_sim false" do
      let(:params) { { 'enable_sim_live' => true, 'session_start_events' => {} } }

      it "success" do
        expect(BrowserStack::DeviceLogger).not_to receive(:start)
        live_session.send(:start_device_logger, false)
      end
    end

    context "flow with enable_sim_live true and force_setup_when_sim true" do
      let(:params) { { 'enable_sim_live' => true, 'session_start_events' => {} } }

      it "success" do
        expect(BrowserStack::DeviceLogger).to receive(:start)
        live_session.send(:start_device_logger, true)
      end
    end
  end

  describe "start_session_save_polling" do
    let(:device) { '000820-427503425' }
    let(:debugger_port) { '9222' }
    let(:local_file_path) { "/usr/local/.browserstack/state_files/#{device}_cookie_data_from_s3.json" }
    let(:pre_signed_url) { 'https://example.com/presigned-url' }

    before :each do
      live_session.instance_variable_set(:@device, device)
      live_session.instance_variable_set(:@params, {
        'additional_action' => 'enable_cookie_restore',
        'pre_signed_url' => pre_signed_url,
        'live_session_id' => 'test-session-123',
        'genre' => 'ios',
        :pre_signed_url => pre_signed_url,
        :live_session_id => 'test-session-123',
        :genre => 'ios'
      })

      allow(DeviceManager).to receive(:device_configuration_check)
        .and_return({ 'debugger_port' => debugger_port })
      allow(BrowserStack::HttpUtils).to receive(:download_from_s3_with_presigned_url)
        .and_return(true)
      allow(SessionSavePolling).to receive(:running?).and_return(false)
      allow(SessionSavePolling).to receive(:new).and_return(
        instance_double(SessionSavePolling, start: nil)
      )
      allow(SessionSavePolling).to receive(:start_file).and_return('/tmp/session_file')
      allow(File).to receive(:open)
    end

    it 'downloads cookies from S3' do
      expect(BrowserStack::HttpUtils).to receive(:download_from_s3_with_presigned_url)
        .with(pre_signed_url, local_file_path)
      live_session.send(:start_session_save_polling)
    end

    context 'when polling is already running' do
      before do
        allow(SessionSavePolling).to receive(:running?).and_return(true)
      end

      it 'updates the existing session ID' do
        expect(File).to receive(:open)
        live_session.send(:start_session_save_polling)
      end
    end

    context 'when additional_action is not enable_cookie_restore' do
      it 'returns early' do
        live_session.instance_variable_set(:@params, { 'additional_action' => 'something_else' })
        expect(BrowserStack::HttpUtils).not_to receive(:download_from_s3_with_presigned_url)
        live_session.send(:start_session_save_polling)
      end
    end
  end

  describe 'execute_restart_action' do
    context 'action_name=proxy-setting' do
      let(:action_name) { 'proxy-setting' }
      let(:mock_wda_client) { double(WdaClient).as_null_object }
      it 'should run the action' do
        expect(live_session).to receive(:start_mitm_proxy).once
        allow_any_instance_of(BrowserStack::Chrome).to receive(:cleanup).and_return(nil)
        allow(mock_wda_client).to receive(:kill_apps)
        allow(WdaClient).to receive(:new).and_return(mock_wda_client)
        allow(DeviceManager).to receive(:device_configuration_check).and_return({ 'debugger_port' => "10", 'webdriver_port' => '90', 'device_version' => '12' })
        live_session.send(:execute_restart_action, action_name)
      end
    end
  end
end
