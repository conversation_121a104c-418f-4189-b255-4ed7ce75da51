require_relative '../spec_helper'
require_relative '../../lib/mitm_proxy'
require_relative '../../lib/utils/utils'

describe MitmProxy do
  let(:mitm_proxy) do
    MitmProxy.new("test_device", {}, {
      "logging_root" => "/tmp", "mobile_root" => "/tmp", "mitmdump_dir" => "/tmp"
    })
  end
  it "creates a MitmProxy object" do
    allow(Dir).to receive("exist?").and_return(true)
    allow(IdeviceUtils).to receive(:device_version).and_return(Gem::Version.new("10"))
    allow(BrowserStack::OSUtils).to receive(:macos_version).and_return('10')
    expect(mitm_proxy).to be_a MitmProxy
  end

  context "boot" do
    before(:each) do
      allow_any_instance_of(Object)
        .to receive(:set_details_for_instrumentation)
        .and_return(true)
    end

    it "should boot with app-live launch command if genre is app-live" do
      allow(IdeviceUtils).to receive(:device_version).and_return(Gem::Version.new("10"))
      allow(BrowserStack::OSUtils).to receive(:macos_version).and_return('10')
      mitm_proxy.instance_variable_set("@genre", "app_live_testing")
      cmd = '/usr/local/bin/mitmdump --ignore-hosts ".*.apple.com.*|.*updates-http.cdn-apple.com.*|.*.icloud.com.*|.*mobile-.*.browserstack.com.*|.*mobile-.*.bsstag.com.*|.*sensormockerdata*.browserstack.com.*|.*.mzstatic.com.*"'
      cmd += ' --set block_global=false --ssl-insecure --listen-port 8080 --set confdir=/Users/<USER>/.mitmproxy --mode upstream:https://localhost:8081 --set device=test_device --set port= --set hardump=/tmp/har_file_.har --set bs_allow_hosts=\'.*\' -s /usr/local/.browserstack/mobile-common/app_live_har_dump/app_live_har_dump.py >> /tmp/mitmproxy_test_device.log 2>&1'
      allow(mitm_proxy).to receive(:`).and_return("Bad Request")
      expect(mitm_proxy).to receive(:fork)
      mitm_proxy.boot(8080, 8081)
    end

    it "should boot with automate launch command if genre is automate" do
      allow(IdeviceUtils).to receive(:device_version).and_return(Gem::Version.new("10"))
      allow(BrowserStack::OSUtils).to receive(:macos_version).and_return('10')
      mitm_proxy.instance_variable_set("@genre", "automate")
      cmd = '/usr/local/bin/mitmdump --ignore-hosts ".*.apple.com.*|.*updates-http.cdn-apple.com.*|.*.icloud.com.*|.*mobile-.*.browserstack.com.*|.*mobile-.*.bsstag.com.*|.*sensormockerdata*.browserstack.com.*|.*.mzstatic.com.*" --set block_global=false --ssl-insecure --listen-port 8080 --set confdir=/Users/<USER>/.mitmproxy --mode upstream:https://localhost:8081 >> /tmp/mitmproxy_test_device.log 2>&1'
      allow(mitm_proxy).to receive(:`).and_return("Bad Request")
      expect(mitm_proxy).to receive(:fork)
      mitm_proxy.boot(8080, 8081)
    end

    it "should boot MITM 5 for ios 13 and above" do
      allow(BrowserStack::OSUtils).to receive(:macos_version).and_return('10')
      expect(IdeviceUtils).to receive(:device_version).with(any_args).at_least(:once).and_return Gem::Version.new(13.0)
      mitm_proxy.instance_variable_set("@genre", "automate")
      cmd = '/Users/<USER>/.local/bin/mitmdump --ignore-hosts ".*.apple.com.*|.*updates-http.cdn-apple.com.*|.*.icloud.com.*|.*mobile-.*.browserstack.com.*|.*mobile-.*.bsstag.com.*|.*sensormockerdata*.browserstack.com.*|.*.mzstatic.com.*" --set block_global=false --ssl-insecure --listen-port 8080 --set confdir=/Users/<USER>/.mitmproxy_5 --mode upstream:https://localhost:8081 >> /tmp/mitmproxy_test_device.log 2>&1'
      allow(mitm_proxy).to receive(:`).and_return("Bad Request")
      expect(mitm_proxy).to receive(:fork)
      mitm_proxy.boot(8080, 8081)
    end

    it "should boot MITM 5 for ios 15 and above using big sur" do
      allow(BrowserStack::OSUtils).to receive(:macos_version).and_return('11')
      expect(IdeviceUtils).to receive(:device_version).with(any_args).at_least(:once).and_return Gem::Version.new(13.0)
      mitm_proxy.instance_variable_set("@genre", "automate")
      cmd = '/usr/local/bin/mitmdump --ignore-hosts ".*.apple.com.*|.*updates-http.cdn-apple.com.*|.*.icloud.com.*|.*mobile-.*.browserstack.com.*|.*mobile-.*.bsstag.com.*|.*sensormockerdata*.browserstack.com.*|.*.mzstatic.com.*" --set block_global=false --ssl-insecure --listen-port 8080 --set confdir=/Users/<USER>/.mitmproxy_5 --mode upstream:https://localhost:8081 >> /tmp/mitmproxy_test_device.log 2>&1'
      allow(mitm_proxy).to receive(:`).and_return("Bad Request")
      expect(mitm_proxy).to receive(:fork)
      mitm_proxy.boot(8080, 8081)
    end
  end

  context "get_convert_cmd" do
    it "should return mitmdump command with captureContent false when param not passed" do
      allow(Dir).to receive("exist?").and_return(true)
      allow(IdeviceUtils).to receive(:device_version).and_return(Gem::Version.new("10"))
      allow(BrowserStack::OSUtils).to receive(:macos_version).and_return('10')
      mitm_proxy.instance_variable_set("@harfile", "/tmp/har_file.har")
      cmd = mitm_proxy.send(:get_convert_cmd, "input_file_name")
      expected_cmd = "gtimeout 200 /usr/local/bin/mitmdump -n -r"\
                      " input_file_name -s \"/tmp/scripts/har_dump.py\""\
                      " --set hardump=\"/tmp/har_file.har\""\
                      " --set captureContent=false  "\
                      ">> /tmp/mitmproxy_flow_to_har_test_device.log 2>&1"
      expect(cmd).to eql(expected_cmd)
    end

    it "should return mitmdump command with captureContent true when param set as true" do
      allow(Dir).to receive("exist?").and_return(true)
      allow(IdeviceUtils).to receive(:device_version).and_return(Gem::Version.new("10"))
      allow(BrowserStack::OSUtils).to receive(:macos_version).and_return('10')
      mitm_proxy_with_params = MitmProxy.new("test_device", {
        'networkLogsCaptureContent' => true
      }, {
        "logging_root" => "/tmp", "mobile_root" => "/tmp", "mitmdump_dir" => "/tmp"
      })
      mitm_proxy_with_params.instance_variable_set("@harfile", "/tmp/har_file.har")
      cmd = mitm_proxy_with_params.send(:get_convert_cmd, "input_file_name")
      expected_cmd = "gtimeout 200 /usr/local/bin/mitmdump -n -r"\
                      " input_file_name -s \"/tmp/scripts/har_dump.py\""\
                      " --set hardump=\"/tmp/har_file.har\""\
                      " --set captureContent=true  "\
                      ">> /tmp/mitmproxy_flow_to_har_test_device.log 2>&1"
      expect(cmd).to eql(expected_cmd)
    end

    it "should return mitmdump command with captureContent false when param set as false" do
      allow(Dir).to receive("exist?").and_return(true)
      allow(IdeviceUtils).to receive(:device_version).and_return(Gem::Version.new("10"))
      allow(BrowserStack::OSUtils).to receive(:macos_version).and_return('10')
      mitm_proxy_with_params = MitmProxy.new("test_device", {
        networkLogsCaptureContent: false
      }, {
        "logging_root" => "/tmp", "mobile_root" => "/tmp", "mitmdump_dir" => "/tmp"
      })
      mitm_proxy_with_params.instance_variable_set("@harfile", "/tmp/har_file.har")
      cmd = mitm_proxy_with_params.send(:get_convert_cmd, "input_file_name")
      expected_cmd = "gtimeout 200 /usr/local/bin/mitmdump -n -r"\
                      " input_file_name -s \"/tmp/scripts/har_dump.py\""\
                      " --set hardump=\"/tmp/har_file.har\""\
                      " --set captureContent=false  "\
                      ">> /tmp/mitmproxy_flow_to_har_test_device.log 2>&1"
      expect(cmd).to eql(expected_cmd)
    end
  end

  context 'get_launch_cmd' do
    before do
      allow(IdeviceUtils).to receive(:device_version).and_return(Gem::Version.new("10"))
    end

    before(:each) do
      @mitm = MitmProxy.new(
        "test_device",
        {},
        {
          "logging_root" => "/tmp", "mobile_root" => "/tmp", "mitmdump_dir" => "/tmp"
        }
      )
    end

    it 'no genre' do
      expected_cmd =  '/usr/local/bin/mitmdump ' \
                      '--ignore-hosts ".*.apple.com.*|.*updates-http.cdn-apple.com.*|.*.icloud.com.*|' \
                      '.*mobile-.*.browserstack.com.*|.*mobile-.*.bsstag.com.*|.*sensormockerdata*.browserstack.com.*|.*.mzstatic.com.*" ' \
                      '--set block_global=false ' \
                      '--ssl-insecure ' \
                      '--listen-port 10000 ' \
                      '--set confdir=/Users/<USER>/.mitmproxy ' \
                      '--mode upstream:https://localhost:11000 ' \
                      '>> /tmp/mitmproxy_test_device.log ' \
                      '2>&1'
      cmd = @mitm.send(:get_launch_cmd, 10000, 11000)
      expect(cmd).to eq(expected_cmd)
    end

    it 'custom headers' do
      expected_cmd = '/usr/local/bin/mitmdump ' \
                     '--ignore-hosts ".*.apple.com.*|.*updates-http.cdn-apple.com.*|.*.icloud.com.*|' \
                     '.*mobile-.*.browserstack.com.*|.*mobile-.*.bsstag.com.*|.*sensormockerdata*.browserstack.com.*|.*.mzstatic.com.*" ' \
                     '--set block_global=false ' \
                     '--ssl-insecure ' \
                     '--listen-port 10000 ' \
                     '--set confdir=/Users/<USER>/.mitmproxy ' \
                     '--mode upstream:https://localhost:11000 ' \
                     '--setheader /~q/my/"header" ' \
                     '>> /tmp/mitmproxy_test_device.log ' \
                     '2>&1'
      mitm = MitmProxy.new(
        "test_device",
        { "custom_headers" => { my: 'header' }.to_json },
        {
          "logging_root" => "/tmp", "mobile_root" => "/tmp", "mitmdump_dir" => "/tmp"
        }
      )
      cmd = mitm.send(:get_launch_cmd, 10000, 11000)
      expect(cmd).to eq(expected_cmd)
      mitm.instance_variable_set('@binary_path', 'mitmdump')
      expected_cmd.gsub!('/usr/local/bin/mitmdump', 'mitmdump')
      expected_cmd.gsub!('--setheader', '--modify-header')
      cmd = mitm.send(:get_launch_cmd, 10000, 11000)
      expect(cmd).to eq(expected_cmd)
    end

    it 'camera injection' do
      expected_cmd = '/usr/local/bin/mitmdump ' \
                     '--ignore-hosts ".*.apple.com.*|.*updates-http.cdn-apple.com.*|.*.icloud.com.*|' \
                     '.*mobile-.*.browserstack.com.*|.*mobile-.*.bsstag.com.*|.*sensormockerdata*.browserstack.com.*|.*.mzstatic.com.*" ' \
                     '--set block_global=false ' \
                     '--ssl-insecure ' \
                     '--listen-port 10000 ' \
                     '--set confdir=/Users/<USER>/.mitmproxy ' \
                     '--mode upstream:https://localhost:11000 ' \
                     '-s /usr/local/.browserstack/realmobile/scripts/camera_injection/camera_injection_script.py ' \
                     '>> /tmp/mitmproxy_test_device.log ' \
                     '2>&1'
      mitm = MitmProxy.new(
        "test_device",
        { "cameraInjection" => true },
        {
          "logging_root" => "/tmp", "mobile_root" => "/tmp", "mitmdump_dir" => "/tmp"
        }
      )
      camera_injection_helper = double
      expect(CameraInjectionHelper).to receive(:new).and_return(camera_injection_helper)
      expect(camera_injection_helper).to receive(:update_camera_url)
      cmd = mitm.send(:get_launch_cmd, 10000, 11000)
      expect(cmd).to eq(expected_cmd)
      mitm.instance_variable_set('@binary_path', 'mitmdump')
      expected_cmd.gsub!('/usr/local/bin/mitmdump', 'mitmdump')
      expect(CameraInjectionHelper).to receive(:new).and_return(camera_injection_helper)
      expect(camera_injection_helper).to receive(:update_camera_url)
      cmd = mitm.send(:get_launch_cmd, 10000, 11000)
      expect(cmd).to eq(expected_cmd)
    end

    it 'app_live_testing' do
      expected_cmd = '/usr/local/bin/mitmdump ' \
      '--ignore-hosts ".*.apple.com.*|.*updates-http.cdn-apple.com.*|.*.icloud.com.*' \
      '|.*mobile-.*.browserstack.com.*|.*mobile-.*.bsstag.com.*|.*sensormockerdata*.browserstack.com.*|.*.mzstatic.com.*" ' \
      '--set block_global=false ' \
      '--ssl-insecure ' \
      '--listen-port 10000 ' \
      '--set confdir=/Users/<USER>/.mitmproxy ' \
      '--mode upstream:https://localhost:11000 ' \
      '--set device=test_device ' \
      '--set port=9000 ' \
      '--set hardump=/tmp/har_file_.har ' \
      '--set bs_allow_hosts=\'.*\' ' \
      '-s /usr/local/.browserstack/mobile-common/app_live_har_dump/app_live_har_dump.py ' \
      '>> /tmp/mitmproxy_test_device.log 2>&1'
      @mitm.instance_variable_set("@genre", "app_live_testing")
      @mitm.instance_variable_set('@network_logs_port', 9000)
      cmd = @mitm.send(:get_launch_cmd, 10000, 11000)
      expect(cmd).to eq(expected_cmd)
    end

    it 'capture_network_logs' do
      expected_cmd = '/usr/local/bin/mitmdump ' \
      '--ignore-hosts ".*.apple.com.*|.*updates-http.cdn-apple.com.*|.*.icloud.com.*|' \
      '.*mobile-.*.browserstack.com.*|.*mobile-.*.bsstag.com.*|.*sensormockerdata*.browserstack.com.*|.*.mzstatic.com.*" ' \
      '--set block_global=false ' \
      '--ssl-insecure ' \
      '--listen-port 10000 ' \
      '--set confdir=/Users/<USER>/.mitmproxy ' \
      '--mode upstream:https://localhost:11000 ' \
      '--save-stream-file /tmp/dumpfile_ ' \
      '>> /tmp/mitmproxy_test_device.log 2>&1'
      @mitm.instance_variable_set("@capture_network_logs", true)
      @mitm.instance_variable_set("@exclude_host_regex", "www.google.*")
      @mitm.instance_variable_set("@include_host_regex", "www.facebook.*")
      cmd = @mitm.send(:get_launch_cmd, 10000, 11000)
      expect(cmd).to eq(expected_cmd)
    end

    it 'handles host regex and custom headers' do
      expected_cmd = '/usr/local/bin/mitmdump ' \
      '--ignore-hosts ".*.apple.com.*|.*updates-http.cdn-apple.com.*|.*.icloud.com.*|' \
      '.*mobile-.*.browserstack.com.*|.*mobile-.*.bsstag.com.*|.*sensormockerdata*.browserstack.com.*|.*.mzstatic.com.*" ' \
      '--set block_global=false ' \
      '--ssl-insecure ' \
      '--listen-port 10000 ' \
      '--set confdir=/Users/<USER>/.mitmproxy ' \
      '--mode upstream:https://localhost:11000 ' \
      '--save-stream-file /tmp/dumpfile_ ' \
      '>> /tmp/mitmproxy_test_device.log 2>&1'
      @mitm.instance_variable_set("@capture_network_logs", true)
      @mitm.instance_variable_set("@exclude_host_regex", "www.google.*")
      @mitm.instance_variable_set("@include_host_regex", "www.facebook.*")
      @mitm.instance_variable_set("@proxy_exclude_hosts", "www.bsstag.*")
      cmd = @mitm.send(:get_launch_cmd, 10000, 11000)
      expect(cmd).to eq(expected_cmd)
    end
  end

  describe 'set_details_for_instrumentation' do
    before do
      allow(IdeviceUtils).to receive(:device_version).and_return(Gem::Version.new("10"))
    end
    before(:each) do
      @mitm = MitmProxy.new("iphone14", { genre: "live_testing" }, {
        "logging_root" => "/tmp", "mobile_root" => "/tmp", "mitmdump_dir" => "/tmp"
      })
    end

    it 'writes device and genre details to mitm log file' do
      allow(File).to receive(:new).and_return(double('mitm_log_file_info', puts: nil, close: nil))
      allow_any_instance_of(Object).to receive(:`).and_return('100')
      @mitm.set_details_for_instrumentation
      expect(File).to have_received(:new).with("/tmp/mitm_log_file_info_iphone14.txt", 'w')
    end
  end

  describe 'send_script_injection_status' do
    before do
      allow(IdeviceUtils).to receive(:device_version).and_return(Gem::Version.new("10"))
    end
    let(:session) { 'session_123' }
    let(:device) { 'iPhone14' }
    let(:mitmdump_log_file) { '/var/log/browserstack/mitmproxy_iPhone14.log' }
    let(:mitmdump_log_file_path) { "/tmp/mitm_log_file_info_iPhone14.txt" }
    let(:zombie_params_for_success) do
      [
        'mitm_injection_status',
        '',
        {
          "data" => {
            "team" => "live",
            "success_count" => 2,
            "failure_count" => 0
          },
          "session_id" => "session_123"
        }
      ]
    end

    it 'sends script injection status for live_testing genre' do
      allow(File).to receive(:exists?).and_return(true)
      allow(File).to receive(:read).and_return("live_testing 100\n")
      allow(BrowserStack::Zombie).to receive(:push_logs)
        .with("mitm_injection_status", "", *zombie_params_for_success)
      allow_any_instance_of(Object).to receive(:`)
        .and_return(0)
      MitmProxy.send_script_injection_status(device, session)
    end
  end
end
