require_relative "../lib/utils/telephony"

describe 'telephony' do
  before do
    @tel = Telephony.new("http://telephony.bsstag.com", "app", "abcd")
  end

  context "when no device or imsi is empty" do
    it "should return an error message" do
      device = imsi = ""
      expect(@tel.push_imsi(device, imsi)).to be_eql("device or imsi can't be empty")
    end
  end

  context "when device and imsi are correctly passed" do
    before do
      allow(BrowserStack::HttpUtils).to receive(:send_post).and_return("ok")
    end
    it "should send a post request to telephony service" do
      expect { @tel.push_imsi("device-id", "imsi") }.to_not raise_error
    end
  end
end
