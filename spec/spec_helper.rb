require 'rack/test'
require 'rspec'
require 'simplecov'
require 'webmock/rspec'

WebMock.allow_net_connect!

require_relative 'helpers/mock_logger'
require_relative '../lib/utils/hooter'
require_relative '../lib/utils/zombie'

ENV['RACK_ENV'] = 'test'

SimpleCov.start

module RSpecMixin
  include Rack::Test::Methods

  def app
    # Because of the logic that has been put in the `configure` block for the Sinatra app, a
    # *lot* of code is executed when server.rb is loaded. Therefore we need to mock Zombie before
    # loading it:
    allow(BrowserStack::Zombie).to receive(:configure)
    require_relative '../server/server'
    BrowserStack::PlatformServer
  end

  def mock_hooter_instance
    mock_hooter = instance_double('Hooter')
    allow(Hooter).to receive(:new) { mock_hooter }
    mock_hooter
  end

  def mock_returned_url_in_debugger_json(url)
    mock_http = instance_double('Net::HTTP')
    allow(mock_http).to receive(:use_ssl=) { true }
    allow(mock_http).to receive(:read_timeout=)
    allow(Net::HTTP).to receive(:new) { mock_http }
    mock_response = double
    allow(mock_response).to receive(:body) { "[{\"url\": \"#{url}\"}]" }
    allow(mock_http).to receive(:get).with('/json?debug_port=12345') { [mock_response, nil] }
    allow(mock_http).to receive(:verify_mode=) { OpenSSL::SSL::VERIFY_NONE }
  end
end

def fixture(filename)
  path = "#{__dir__}/fixtures/#{filename}"
  raise "Can't find fixture at #{path}" unless File.exist?(path)

  File.read(path)
end

RSpec.configure do |c|
  c.include RSpecMixin

  c.filter_run focus: true
  c.run_all_when_everything_filtered = true

  # To use --only-failures to run only the tests that failed last time
  # Refer https://relishapp.com/rspec/rspec-core/docs/command-line/only-failures
  c.example_status_persistence_file_path = "failed_rspec_persistence_file.txt"

  c.before(:each) do
    # Reverting to using normal mock logger, since rspec fails to pass *args to MockLogger
    # when sending symbol messages. For example, the following causes an ArgumentError:
    # BrowserStack.logger.send(level.to_sym, 'hello world', { subcomponent: 'MyClass' })
    @mock_logger = double('logger')
    allow(@mock_logger).to receive(:debug)
    allow(@mock_logger).to receive(:info)
    allow(@mock_logger).to receive(:warn)
    allow(@mock_logger).to receive(:error)
    allow(@mock_logger).to receive(:fatal)
    allow(@mock_logger).to receive(:unknown)
    allow(@mock_logger).to receive(:params).and_return({})
    allow(BrowserStack).to receive(:logger).and_return(@mock_logger)

    FileUtils.rm_r(Dir.glob("spec/tmp/*"))
  end
end
